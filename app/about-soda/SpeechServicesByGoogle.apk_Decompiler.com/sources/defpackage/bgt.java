package defpackage;

/* renamed from: bgt  reason: default package */
/* compiled from: PG */
public final class bgt {
    public final String a;
    public final int b;

    public bgt(String str, int i) {
        jnu.e(str, "workSpecId");
        this.a = str;
        this.b = i;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof bgt)) {
            return false;
        }
        bgt bgt = (bgt) obj;
        if (jnu.i(this.a, bgt.a) && this.b == bgt.b) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (this.a.hashCode() * 31) + this.b;
    }

    public final String toString() {
        return "WorkGenerationalId(workSpecId=" + this.a + ", generation=" + this.b + ')';
    }
}
