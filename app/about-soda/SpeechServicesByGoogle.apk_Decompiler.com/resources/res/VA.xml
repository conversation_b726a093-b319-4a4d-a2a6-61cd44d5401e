<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt">
    <item android:id="@android:id/background">
        <shape>
            <corners android:radius="8dp"/>
            <solid android:color="@color/settingslib_materialColorSurfaceVariant"/>
        </shape>
    </item>
    <item android:id="@android:id/progress">
        <scale android:scaleWidth="100%" android:useIntrinsicSizeAsMinimum="true">
            <shape>
                <corners android:radius="8dp"/>
                <solid android:color="?android:attr/textColorPrimary"/>
                <size android:width="8dp"/>
            </shape>
        </scale>
    </item>
</layer-list>
