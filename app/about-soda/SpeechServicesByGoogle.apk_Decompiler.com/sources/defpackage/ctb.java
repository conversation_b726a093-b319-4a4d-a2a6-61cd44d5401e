package defpackage;

/* renamed from: ctb  reason: default package */
/* compiled from: PG */
public final class ctb extends htq implements hvb {
    public static final ctb h;
    private static volatile hvh i;
    public int a;
    public ctg b;
    public long c;
    public String d = "";
    public int e;
    public long f;
    public long g;

    static {
        ctb ctb = new ctb();
        h = ctb;
        htq.z(ctb.class, ctb);
    }

    private ctb() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i2, Object obj) {
        int i3 = i2 - 1;
        if (i3 == 0) {
            return (byte) 1;
        }
        if (i3 == 2) {
            return new hvl(h, "\u0001\u0006\u0000\u0001\u0001\u0006\u0006\u0000\u0000\u0000\u0001ဉ\u0000\u0002ဂ\u0001\u0003င\u0003\u0004ဂ\u0004\u0005ဂ\u0005\u0006ဈ\u0002", new Object[]{"a", "b", "c", "e", "f", "g", "d"});
        } else if (i3 == 3) {
            return new ctb();
        } else {
            if (i3 == 4) {
                return new htk((htq) h);
            }
            if (i3 == 5) {
                return h;
            }
            if (i3 != 6) {
                return null;
            }
            hvh hvh = i;
            if (hvh == null) {
                synchronized (ctb.class) {
                    hvh = i;
                    if (hvh == null) {
                        hvh = new htl(h);
                        i = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
