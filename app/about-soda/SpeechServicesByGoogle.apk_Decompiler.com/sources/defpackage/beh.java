package defpackage;

import android.content.Context;
import android.os.PowerManager;
import java.util.Objects;
import java.util.concurrent.CancellationException;
import java.util.concurrent.Executor;

/* renamed from: beh  reason: default package */
/* compiled from: PG */
public final class beh implements bey, bir {
    public final Context a;
    public final int b;
    public final bgt c;
    public final bem d;
    public int e = 0;
    public final Executor f;
    public final Executor g;
    public PowerManager.WakeLock h;
    public boolean i = false;
    public final jqp j;
    public volatile jrz k;
    public final byw l;
    public final byw m;
    private final Object n = new Object();

    static {
        bbk.b("DelayMetCommandHandler");
    }

    /* JADX WARNING: type inference failed for: r3v1, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v2, types: [java.util.concurrent.Executor, java.lang.Object] */
    public beh(Context context, int i2, bem bem, byw byw) {
        this.a = context;
        this.b = i2;
        this.d = bem;
        this.c = (bgt) byw.a;
        this.m = byw;
        alx alx = bem.e.j;
        cyw cyw = bem.j;
        this.f = cyw.a;
        this.g = cyw.c;
        this.j = (jqp) cyw.d;
        this.l = new byw(alx, (byte[]) null);
    }

    public final void a() {
        synchronized (this.n) {
            if (this.k != null) {
                this.k.r((CancellationException) null);
            }
            this.d.c.a(this.c);
            PowerManager.WakeLock wakeLock = this.h;
            if (wakeLock != null && wakeLock.isHeld()) {
                bbk.a();
                Objects.toString(this.h);
                Objects.toString(this.c);
                this.h.release();
            }
        }
    }

    public final void b(bgt bgt) {
        bbk.a();
        Objects.toString(bgt);
        bgt.toString();
        this.f.execute(new alr(this, 14));
    }

    public final void e(bhe bhe, wf wfVar) {
        if (wfVar instanceof beq) {
            this.f.execute(new alr(this, 15));
        } else {
            this.f.execute(new alr(this, 14));
        }
    }
}
