package defpackage;

/* renamed from: cqe  reason: default package */
/* compiled from: PG */
public final class cqe {
    public static final int a(long j) {
        return (((int) (j >> 43)) & 63) - 21;
    }

    public static long b(int i, long j) {
        return cqx.z(false, false, false, -21, -21, i, j);
    }

    public static final long c(long j) {
        return j & 8796093022207L;
    }

    public final boolean equals(Object obj) {
        throw null;
    }

    public final int hashCode() {
        throw null;
    }

    public final String toString() {
        throw null;
    }
}
