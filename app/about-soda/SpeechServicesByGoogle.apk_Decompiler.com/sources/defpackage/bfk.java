package defpackage;

import android.os.Build;

/* renamed from: bfk  reason: default package */
/* compiled from: PG */
public final class bfk extends bfd {
    public bfk(bft bft) {
        super(bft);
    }

    public final boolean b(bhe bhe) {
        jnu.e(bhe, "workSpec");
        bbl bbl = bhe.k.b;
        if (bbl == bbl.UNMETERED) {
            return true;
        }
        if (Build.VERSION.SDK_INT < 30) {
            return false;
        }
        if (bbl == bbl.TEMPORARILY_UNMETERED) {
            return true;
        }
        return false;
    }

    public final int d() {
        return 7;
    }

    public final /* bridge */ /* synthetic */ boolean e(Object obj) {
        bex bex = (bex) obj;
        jnu.e(bex, "value");
        if (!bex.a || bex.c) {
            return true;
        }
        return false;
    }
}
