package com.google.android.libraries.assistant.soda.s3client;

import android.util.Log;

/**
 * SODA传输工厂（s3client包版本）
 * 
 * 这是为了匹配native层期望的类路径而创建的版本
 */
public class SodaTransportFactory {
    private static final String TAG = "SodaTransportFactory_S3";
    
    private boolean isInitialized = false;
    
    /**
     * 构造函数
     */
    public SodaTransportFactory() {
        Log.d(TAG, "SodaTransportFactory (s3client) created");
        this.isInitialized = true;
    }
    
    /**
     * 初始化传输工厂
     * @return 是否初始化成功
     */
    public boolean initialize() {
        Log.d(TAG, "Initializing transport factory");
        this.isInitialized = true;
        return true;
    }
    
    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        Log.d(TAG, "SodaTransportFactory cleanup");
        this.isInitialized = false;
    }
}
