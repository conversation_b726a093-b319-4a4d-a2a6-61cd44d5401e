package defpackage;

import j$.util.DesugarArrays;
import j$.util.function.IntPredicate$CC;
import java.util.function.IntPredicate;

/* renamed from: boh  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class boh implements IntPredicate {
    public final /* synthetic */ boi a;

    public /* synthetic */ boh(boi boi) {
        this.a = boi;
    }

    public final /* synthetic */ IntPredicate and(IntPredicate intPredicate) {
        return IntPredicate$CC.$default$and(this, intPredicate);
    }

    public final /* synthetic */ IntPredicate negate() {
        return IntPredicate$CC.$default$negate(this);
    }

    public final /* synthetic */ IntPredicate or(IntPredicate intPredicate) {
        return IntPredicate$CC.$default$or(this, intPredicate);
    }

    public final boolean test(int i) {
        return DesugarArrays.stream(this.a.a).noneMatch(new bog(i));
    }
}
