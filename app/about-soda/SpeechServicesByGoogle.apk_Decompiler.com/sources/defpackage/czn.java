package defpackage;

/* renamed from: czn  reason: default package */
/* compiled from: PG */
public final class czn implements cag {
    private final cai a = cai.a(92469333, ihj.LOGGER_DEFERRING_PROVIDER);
    private final /* synthetic */ int b;

    public czn(int i, byte[] bArr) {
        this.b = i;
    }

    public final cai a() {
        if (this.b != 0) {
            return this.a;
        }
        return this.a;
    }

    public czn(int i) {
        this.b = i;
    }
}
