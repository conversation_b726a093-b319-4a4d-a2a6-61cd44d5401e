package defpackage;

/* renamed from: bhz  reason: default package */
/* compiled from: PG */
final class bhz extends aub {
    public bhz(aus aus) {
        super(aus);
    }

    /* access modifiers changed from: protected */
    public final String a() {
        return "INSERT OR IGNORE INTO `WorkTag` (`tag`,`work_spec_id`) VALUES (?,?)";
    }

    /* access modifiers changed from: protected */
    public final /* bridge */ /* synthetic */ void c(axc axc, Object obj) {
        bvj bvj = (bvj) obj;
        axc.g(1, (String) bvj.b);
        axc.g(2, (String) bvj.a);
    }
}
