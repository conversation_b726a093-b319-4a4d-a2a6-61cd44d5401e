package defpackage;

import java.util.Objects;
import java.util.concurrent.CancellationException;

/* renamed from: jtl  reason: default package */
/* compiled from: PG */
public class jtl implements jtp {
    public final jna a = null;
    public final jpr b;
    public final jpr c;
    public final jps d;
    public final jps e;
    private final int f;
    private final jpr g;
    private final jpr h;
    private final jps i;
    private final jps j;
    private final jps k;

    public jtl(int i2) {
        this.f = i2;
        if (i2 >= 0) {
            long j2 = 0;
            this.b = new jpr(0, jpt.a);
            this.c = new jpr(0, jpt.a);
            int i3 = jtn.a;
            if (i2 != 0) {
                if (i2 != Integer.MAX_VALUE) {
                    j2 = (long) i2;
                } else {
                    j2 = Long.MAX_VALUE;
                }
            }
            this.g = new jpr(j2, jpt.a);
            this.h = new jpr(C(), jpt.a);
            jwn jwn = new jwn(0, (jwn) null, this, 3);
            this.d = new jps(jwn, jpt.a);
            this.e = new jps(jwn, jpt.a);
            if (L()) {
                jwn = jtn.s;
                jnu.c(jwn, "null cannot be cast to non-null type kotlinx.coroutines.channels.ChannelSegment<E of kotlinx.coroutines.channels.BufferedChannel>");
            }
            jpt jpt = jpt.a;
            this.i = new jps(jwn, jpt);
            this.j = new jps(jtn.r, jpt);
            this.k = new jps((Object) null, jpt);
            return;
        }
        throw new IllegalArgumentException(a.am(i2, "Invalid channel capacity: ", ", should be >=0"));
    }

    public static final void B(jta jta, jwn jwn, int i2) {
        jta.y(jwn, i2);
    }

    private final long C() {
        return this.g.b;
    }

    /* JADX WARNING: Removed duplicated region for block: B:100:0x000d A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:43:0x00b6  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    private final void D() {
        /*
            r15 = this;
            boolean r0 = r15.L()
            if (r0 == 0) goto L_0x0007
            return
        L_0x0007:
            jps r0 = r15.i
            java.lang.Object r0 = r0.a
            jwn r0 = (defpackage.jwn) r0
        L_0x000d:
            jpr r1 = r15.g
            long r1 = r1.b()
            int r3 = defpackage.jtn.a
            long r3 = (long) r3
            long r3 = r1 / r3
            long r5 = r15.b()
            int r5 = (r5 > r1 ? 1 : (r5 == r1 ? 0 : -1))
            if (r5 > 0) goto L_0x0033
            long r1 = r0.b
            int r1 = (r1 > r3 ? 1 : (r1 == r3 ? 0 : -1))
            if (r1 >= 0) goto L_0x002f
            jwn r1 = r0.b()
            if (r1 == 0) goto L_0x002f
            r15.O(r3, r0)
        L_0x002f:
            r15.E(1)
            return
        L_0x0033:
            long r5 = r0.b
            int r5 = (r5 > r3 ? 1 : (r5 == r3 ? 0 : -1))
            r6 = 0
            if (r5 == 0) goto L_0x00b7
            jps r5 = r15.i
            jtm r7 = defpackage.jtm.a
        L_0x003e:
            java.lang.Object r8 = defpackage.jwm.a(r0, r3, r7)
            boolean r9 = defpackage.jxj.a(r8)
            if (r9 != 0) goto L_0x0079
            jwn r9 = defpackage.jxj.b(r8)
        L_0x004c:
            java.lang.Object r10 = r5.a
            jwn r10 = (defpackage.jwn) r10
            long r11 = r10.b
            long r13 = r9.b
            int r11 = (r11 > r13 ? 1 : (r11 == r13 ? 0 : -1))
            if (r11 < 0) goto L_0x0059
            goto L_0x0079
        L_0x0059:
            boolean r11 = r9.j()
            if (r11 == 0) goto L_0x003e
            boolean r11 = r5.d(r10, r9)
            if (r11 == 0) goto L_0x006f
            boolean r5 = r10.h()
            if (r5 == 0) goto L_0x0079
            r10.e()
            goto L_0x0079
        L_0x006f:
            boolean r10 = r9.h()
            if (r10 == 0) goto L_0x004c
            r9.e()
            goto L_0x004c
        L_0x0079:
            boolean r5 = defpackage.jxj.a(r8)
            if (r5 == 0) goto L_0x008a
            r15.q()
            r15.O(r3, r0)
            r15.E(1)
        L_0x0088:
            r5 = r6
            goto L_0x00b4
        L_0x008a:
            jwn r5 = defpackage.jxj.b(r8)
            long r7 = r5.b
            int r3 = (r7 > r3 ? 1 : (r7 == r3 ? 0 : -1))
            if (r3 <= 0) goto L_0x00b2
            jpr r3 = r15.g
            r9 = 1
            long r9 = r9 + r1
            int r4 = defpackage.jtn.a
            long r11 = (long) r4
            long r7 = r7 * r11
            boolean r3 = r3.c(r9, r7)
            if (r3 == 0) goto L_0x00ae
            long r3 = r5.b
            int r5 = defpackage.jtn.a
            long r7 = (long) r5
            long r3 = r3 * r7
            long r3 = r3 - r1
            r15.E(r3)
            goto L_0x0088
        L_0x00ae:
            r15.E(1)
            goto L_0x0088
        L_0x00b2:
            boolean r3 = defpackage.jqv.a
        L_0x00b4:
            if (r5 == 0) goto L_0x000d
            r0 = r5
        L_0x00b7:
            int r3 = defpackage.jtn.a
            long r3 = (long) r3
            long r3 = r1 % r3
            int r3 = (int) r3
            java.lang.Object r4 = r0.m(r3)
            boolean r5 = r4 instanceof defpackage.jta
            r7 = 0
            if (r5 != 0) goto L_0x00c7
            goto L_0x00ed
        L_0x00c7:
            jpr r5 = r15.c
            long r8 = r5.b
            int r5 = (r1 > r8 ? 1 : (r1 == r8 ? 0 : -1))
            if (r5 < 0) goto L_0x00ed
            jxl r5 = defpackage.jtn.f
            boolean r5 = r0.t(r3, r4, r5)
            if (r5 == 0) goto L_0x00ed
            boolean r1 = Q(r4)
            if (r1 == 0) goto L_0x00e4
            jxl r1 = defpackage.jtn.c
            r0.s(r3, r1)
            goto L_0x016c
        L_0x00e4:
            jxl r1 = defpackage.jtn.i
            r0.s(r3, r1)
            r0.q(r3, r7)
            goto L_0x012d
        L_0x00ed:
            java.lang.Object r4 = r0.m(r3)
            boolean r5 = r4 instanceof defpackage.jta
            if (r5 == 0) goto L_0x0129
            jpr r5 = r15.c
            long r8 = r5.b
            int r5 = (r1 > r8 ? 1 : (r1 == r8 ? 0 : -1))
            if (r5 >= 0) goto L_0x010c
            juc r5 = new juc
            r8 = r4
            jta r8 = (defpackage.jta) r8
            r5.<init>(r8)
            boolean r4 = r0.t(r3, r4, r5)
            if (r4 == 0) goto L_0x00ed
            goto L_0x016c
        L_0x010c:
            jxl r5 = defpackage.jtn.f
            boolean r5 = r0.t(r3, r4, r5)
            if (r5 == 0) goto L_0x00ed
            boolean r1 = Q(r4)
            if (r1 == 0) goto L_0x0120
            jxl r1 = defpackage.jtn.c
            r0.s(r3, r1)
            goto L_0x016c
        L_0x0120:
            jxl r1 = defpackage.jtn.i
            r0.s(r3, r1)
            r0.q(r3, r7)
            goto L_0x012d
        L_0x0129:
            jxl r5 = defpackage.jtn.i
            if (r4 != r5) goto L_0x0132
        L_0x012d:
            r15.E(1)
            goto L_0x000d
        L_0x0132:
            if (r4 != 0) goto L_0x013d
            jxl r4 = defpackage.jtn.d
            boolean r4 = r0.t(r3, r6, r4)
            if (r4 == 0) goto L_0x00ed
            goto L_0x016c
        L_0x013d:
            jxl r5 = defpackage.jtn.c
            if (r4 != r5) goto L_0x0142
            goto L_0x016c
        L_0x0142:
            jxl r5 = defpackage.jtn.g
            if (r4 == r5) goto L_0x016c
            jxl r5 = defpackage.jtn.h
            if (r4 == r5) goto L_0x016c
            jxl r5 = defpackage.jtn.j
            if (r4 != r5) goto L_0x014f
            goto L_0x016c
        L_0x014f:
            jxl r5 = defpackage.jtn.k
            if (r4 != r5) goto L_0x0154
            goto L_0x016c
        L_0x0154:
            jxl r5 = defpackage.jtn.e
            if (r4 != r5) goto L_0x0159
            goto L_0x00ed
        L_0x0159:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            java.util.Objects.toString(r4)
            java.lang.String r1 = r4.toString()
            java.lang.String r2 = "Unexpected cell state: "
            java.lang.String r1 = r2.concat(r1)
            r0.<init>(r1)
            throw r0
        L_0x016c:
            r15.E(1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.jtl.D():void");
    }

    /* access modifiers changed from: private */
    public final void E(long j2) {
        if ((this.h.a(j2) & 4611686018427387904L) != 0) {
            do {
            } while ((this.h.b & 4611686018427387904L) != 0);
        }
    }

    private final void F(jta jta) {
        H(jta, true);
    }

    private final void G(jta jta) {
        H(jta, false);
    }

    private final void H(jta jta, boolean z) {
        Throwable th;
        if (jta instanceof jti) {
            jti jti = (jti) jta;
            throw null;
        } else if (jta instanceof jqb) {
            jlr jlr = (jlr) jta;
            if (z) {
                th = i();
            } else {
                th = j();
            }
            jlr.bE(jji.b(th));
        } else if (jta instanceof jtz) {
            ((jtz) jta).a.bE(new jtt(new jtr(h())));
        } else if (jta instanceof jth) {
            jth jth = (jth) jta;
            jqb jqb = jth.b;
            jnu.b(jqb);
            jth.b = null;
            jth.a = jtn.k;
            Throwable h2 = jth.c.h();
            if (h2 == null) {
                jqb.bE(false);
                return;
            }
            if (jqv.b && (jqb instanceof jmf)) {
                h2 = jxk.a(h2, jqb);
            }
            jqb.bE(jji.b(h2));
        } else if (jta instanceof jyc) {
            jyc jyc = (jyc) jta;
            int i2 = jtn.a;
            throw null;
        } else {
            Objects.toString(jta);
            throw new IllegalStateException("Unexpected waiter: ".concat(String.valueOf(jta)));
        }
    }

    private final boolean I(long j2) {
        if (j2 < C() || j2 < a() + ((long) this.f)) {
            return true;
        }
        return false;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:46:0x009d, code lost:
        r9 = r9.c();
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    private final boolean J(long r9, boolean r11) {
        /*
            r8 = this;
            r0 = 60
            long r0 = r9 >> r0
            int r0 = (int) r0
            r1 = 0
            if (r0 == 0) goto L_0x0158
            r2 = 1
            if (r0 == r2) goto L_0x0158
            r3 = 2
            r4 = 1152921504606846975(0xfffffffffffffff, double:1.2882297539194265E-231)
            if (r0 == r3) goto L_0x00d0
            r11 = 3
            if (r0 != r11) goto L_0x00c4
            long r9 = r9 & r4
            jwn r9 = r8.N(r9)
            r10 = 0
        L_0x001c:
            int r11 = defpackage.jtn.a
        L_0x001e:
            int r11 = r11 + -1
            if (r11 < 0) goto L_0x009d
            long r0 = r9.b
            int r3 = defpackage.jtn.a
            long r3 = (long) r3
            long r0 = r0 * r3
        L_0x0028:
            java.lang.Object r3 = r9.m(r11)
            jxl r4 = defpackage.jtn.h
            if (r3 == r4) goto L_0x00a3
            long r4 = (long) r11
            long r4 = r4 + r0
            jxl r6 = defpackage.jtn.c
            if (r3 != r6) goto L_0x004d
            long r6 = r8.a()
            int r4 = (r4 > r6 ? 1 : (r4 == r6 ? 0 : -1))
            if (r4 < 0) goto L_0x00a3
            jxl r4 = defpackage.jtn.k
            boolean r3 = r9.t(r11, r3, r4)
            if (r3 == 0) goto L_0x0028
            r9.p(r11)
            r9.g()
            goto L_0x001e
        L_0x004d:
            jxl r6 = defpackage.jtn.d
            if (r3 == r6) goto L_0x0091
            if (r3 != 0) goto L_0x0054
            goto L_0x0091
        L_0x0054:
            boolean r6 = r3 instanceof defpackage.jta
            if (r6 != 0) goto L_0x0069
            boolean r6 = r3 instanceof defpackage.juc
            if (r6 == 0) goto L_0x005d
            goto L_0x0069
        L_0x005d:
            jxl r4 = defpackage.jtn.f
            if (r3 == r4) goto L_0x00a3
            jxl r5 = defpackage.jtn.e
            if (r3 != r5) goto L_0x0066
            goto L_0x00a3
        L_0x0066:
            if (r3 == r4) goto L_0x0028
            goto L_0x001e
        L_0x0069:
            long r6 = r8.a()
            int r4 = (r4 > r6 ? 1 : (r4 == r6 ? 0 : -1))
            if (r4 < 0) goto L_0x00a3
            boolean r4 = r3 instanceof defpackage.juc
            if (r4 == 0) goto L_0x007b
            r4 = r3
            juc r4 = (defpackage.juc) r4
            jta r4 = r4.a
            goto L_0x007e
        L_0x007b:
            r4 = r3
            jta r4 = (defpackage.jta) r4
        L_0x007e:
            jxl r5 = defpackage.jtn.k
            boolean r3 = r9.t(r11, r3, r5)
            if (r3 == 0) goto L_0x0028
            java.lang.Object r10 = defpackage.jwx.a(r10, r4)
            r9.p(r11)
            r9.g()
            goto L_0x001e
        L_0x0091:
            jxl r4 = defpackage.jtn.k
            boolean r3 = r9.t(r11, r3, r4)
            if (r3 == 0) goto L_0x0028
            r9.g()
            goto L_0x001e
        L_0x009d:
            jwn r9 = r9.c()
            if (r9 != 0) goto L_0x001c
        L_0x00a3:
            if (r10 == 0) goto L_0x00c3
            boolean r9 = r10 instanceof java.util.ArrayList
            if (r9 != 0) goto L_0x00af
            jta r10 = (defpackage.jta) r10
            r8.G(r10)
            goto L_0x00c3
        L_0x00af:
            java.util.ArrayList r10 = (java.util.ArrayList) r10
            int r9 = r10.size()
        L_0x00b5:
            int r9 = r9 + -1
            if (r9 < 0) goto L_0x00c3
            java.lang.Object r11 = r10.get(r9)
            jta r11 = (defpackage.jta) r11
            r8.G(r11)
            goto L_0x00b5
        L_0x00c3:
            return r2
        L_0x00c4:
            java.lang.String r9 = "unexpected close status: "
            java.lang.String r9 = defpackage.a.ak(r0, r9)
            java.lang.IllegalStateException r10 = new java.lang.IllegalStateException
            r10.<init>(r9)
            throw r10
        L_0x00d0:
            long r9 = r9 & r4
            r8.N(r9)
            if (r11 == 0) goto L_0x0156
        L_0x00d6:
            jps r9 = r8.e
            java.lang.Object r9 = r9.a
            jwn r9 = (defpackage.jwn) r9
            long r10 = r8.a()
            long r3 = r8.b()
            int r0 = (r3 > r10 ? 1 : (r3 == r10 ? 0 : -1))
            if (r0 > 0) goto L_0x00e9
            goto L_0x0106
        L_0x00e9:
            int r0 = defpackage.jtn.a
            long r3 = (long) r0
            long r3 = r10 / r3
            long r5 = r9.b
            int r0 = (r5 > r3 ? 1 : (r5 == r3 ? 0 : -1))
            if (r0 == 0) goto L_0x0107
            jwn r9 = r8.y(r3, r9)
            if (r9 != 0) goto L_0x0107
            jps r9 = r8.e
            java.lang.Object r9 = r9.a
            jwn r9 = (defpackage.jwn) r9
            long r9 = r9.b
            int r9 = (r9 > r3 ? 1 : (r9 == r3 ? 0 : -1))
            if (r9 >= 0) goto L_0x00d6
        L_0x0106:
            return r2
        L_0x0107:
            r9.d()
            int r0 = defpackage.jtn.a
            long r3 = (long) r0
            long r3 = r10 % r3
            int r0 = (int) r3
        L_0x0110:
            java.lang.Object r3 = r9.m(r0)
            if (r3 == 0) goto L_0x0142
            jxl r4 = defpackage.jtn.d
            if (r3 != r4) goto L_0x011b
            goto L_0x0142
        L_0x011b:
            jxl r9 = defpackage.jtn.c
            if (r3 != r9) goto L_0x0120
            goto L_0x0158
        L_0x0120:
            jxl r9 = defpackage.jtn.i
            if (r3 == r9) goto L_0x014d
            jxl r9 = defpackage.jtn.k
            if (r3 == r9) goto L_0x014d
            jxl r9 = defpackage.jtn.h
            if (r3 == r9) goto L_0x014d
            jxl r9 = defpackage.jtn.g
            if (r3 == r9) goto L_0x014d
            jxl r9 = defpackage.jtn.f
            if (r3 != r9) goto L_0x0135
            goto L_0x0158
        L_0x0135:
            jxl r9 = defpackage.jtn.e
            if (r3 == r9) goto L_0x014d
            long r3 = r8.a()
            int r9 = (r10 > r3 ? 1 : (r10 == r3 ? 0 : -1))
            if (r9 != 0) goto L_0x014d
            goto L_0x0158
        L_0x0142:
            jxl r4 = defpackage.jtn.g
            boolean r3 = r9.t(r0, r3, r4)
            if (r3 == 0) goto L_0x0110
            r8.D()
        L_0x014d:
            jpr r9 = r8.c
            r3 = 1
            long r3 = r3 + r10
            r9.c(r10, r3)
            goto L_0x00d6
        L_0x0156:
            r1 = r2
        L_0x0158:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.jtl.J(long, boolean):boolean");
    }

    private final boolean K(long j2) {
        return J(j2, true);
    }

    private final boolean L() {
        long C = C();
        if (C == 0 || C == Long.MAX_VALUE) {
            return true;
        }
        return false;
    }

    private final int M(jwn jwn, int i2, Object obj, long j2, Object obj2, boolean z) {
        while (true) {
            Object m = jwn.m(i2);
            if (m == null) {
                if (I(j2)) {
                    if (!z) {
                        if (jwn.t(i2, (Object) null, jtn.c)) {
                            return 1;
                        }
                    }
                } else if (!z) {
                    if (obj2 == null) {
                        return 3;
                    }
                    if (jwn.t(i2, (Object) null, obj2)) {
                        return 2;
                    }
                }
                if (jwn.t(i2, (Object) null, jtn.i)) {
                    jwn.q(i2, false);
                    return 4;
                }
            } else if (m == jtn.d) {
                if (jwn.t(i2, m, jtn.c)) {
                    return 1;
                }
            } else if (m == jtn.j) {
                jwn.p(i2);
                return 5;
            } else if (m == jtn.g) {
                jwn.p(i2);
                return 5;
            } else if (m == jtn.k) {
                jwn.p(i2);
                q();
                return 4;
            } else {
                boolean z2 = jqv.a;
                jwn.p(i2);
                if (m instanceof juc) {
                    m = ((juc) m).a;
                }
                if (T(m, obj)) {
                    jwn.s(i2, jtn.h);
                    return 0;
                } else if (jwn.k(i2, jtn.j) == jtn.j) {
                    return 5;
                } else {
                    jwn.q(i2, true);
                    return 5;
                }
            }
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:30:0x007b, code lost:
        r1 = r1.c();
     */
    /* JADX WARNING: Code restructure failed: missing block: B:60:0x00e2, code lost:
        r1 = r1.c();
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    private final defpackage.jwn N(long r13) {
        /*
            r12 = this;
            jps r0 = r12.i
            java.lang.Object r0 = r0.a
            jps r1 = r12.d
            java.lang.Object r1 = r1.a
            jwn r1 = (defpackage.jwn) r1
            long r2 = r1.b
            r4 = r0
            jwn r4 = (defpackage.jwn) r4
            long r4 = r4.b
            int r2 = (r2 > r4 ? 1 : (r2 == r4 ? 0 : -1))
            if (r2 <= 0) goto L_0x0016
            r0 = r1
        L_0x0016:
            jps r1 = r12.e
            java.lang.Object r1 = r1.a
            jwn r1 = (defpackage.jwn) r1
            long r2 = r1.b
            r4 = r0
            jwn r4 = (defpackage.jwn) r4
            long r4 = r4.b
            int r2 = (r2 > r4 ? 1 : (r2 == r4 ? 0 : -1))
            if (r2 <= 0) goto L_0x0028
            r0 = r1
        L_0x0028:
            jwn r0 = (defpackage.jwn) r0
        L_0x002a:
            java.lang.Object r1 = r0.a()
            jxl r2 = defpackage.jwm.a
            r3 = 0
            if (r1 != r2) goto L_0x0034
            goto L_0x0040
        L_0x0034:
            jwn r1 = (defpackage.jwn) r1
            if (r1 != 0) goto L_0x0108
            jps r1 = r0.a
            boolean r1 = r1.d(r3, r2)
            if (r1 == 0) goto L_0x002a
        L_0x0040:
            boolean r1 = r12.t()
            if (r1 == 0) goto L_0x0089
            r1 = r0
        L_0x0047:
            int r2 = defpackage.jtn.a
        L_0x0049:
            int r2 = r2 + -1
            r4 = -1
            if (r2 < 0) goto L_0x007b
            long r6 = r1.b
            int r8 = defpackage.jtn.a
            long r8 = (long) r8
            long r6 = r6 * r8
            long r8 = r12.a()
            long r10 = (long) r2
            long r6 = r6 + r10
            int r8 = (r6 > r8 ? 1 : (r6 == r8 ? 0 : -1))
            if (r8 < 0) goto L_0x0081
        L_0x005f:
            java.lang.Object r8 = r1.m(r2)
            if (r8 == 0) goto L_0x006f
            jxl r9 = defpackage.jtn.d
            if (r8 != r9) goto L_0x006a
            goto L_0x006f
        L_0x006a:
            jxl r9 = defpackage.jtn.c
            if (r8 != r9) goto L_0x007a
            goto L_0x0082
        L_0x006f:
            jxl r9 = defpackage.jtn.k
            boolean r8 = r1.t(r2, r8, r9)
            if (r8 == 0) goto L_0x005f
            r1.g()
        L_0x007a:
            goto L_0x0049
        L_0x007b:
            jwn r1 = r1.c()
            if (r1 != 0) goto L_0x0047
        L_0x0081:
            r6 = r4
        L_0x0082:
            int r1 = (r6 > r4 ? 1 : (r6 == r4 ? 0 : -1))
            if (r1 == 0) goto L_0x0089
            r12.k(r6)
        L_0x0089:
            r1 = r0
        L_0x008a:
            if (r1 == 0) goto L_0x00e7
            int r2 = defpackage.jtn.a
        L_0x008e:
            int r2 = r2 + -1
            if (r2 < 0) goto L_0x00e2
            long r4 = r1.b
            int r6 = defpackage.jtn.a
            long r6 = (long) r6
            long r8 = (long) r2
            long r4 = r4 * r6
            long r4 = r4 + r8
            int r4 = (r4 > r13 ? 1 : (r4 == r13 ? 0 : -1))
            if (r4 < 0) goto L_0x00e7
        L_0x009e:
            java.lang.Object r4 = r1.m(r2)
            if (r4 == 0) goto L_0x00d6
            jxl r5 = defpackage.jtn.d
            if (r4 != r5) goto L_0x00a9
            goto L_0x00d6
        L_0x00a9:
            boolean r5 = r4 instanceof defpackage.juc
            r6 = 1
            if (r5 == 0) goto L_0x00c2
            jxl r5 = defpackage.jtn.k
            boolean r5 = r1.t(r2, r4, r5)
            if (r5 == 0) goto L_0x009e
            juc r4 = (defpackage.juc) r4
            jta r4 = r4.a
            java.lang.Object r3 = defpackage.jwx.a(r3, r4)
            r1.q(r2, r6)
            goto L_0x00e1
        L_0x00c2:
            boolean r5 = r4 instanceof defpackage.jta
            if (r5 == 0) goto L_0x00e1
            jxl r5 = defpackage.jtn.k
            boolean r5 = r1.t(r2, r4, r5)
            if (r5 == 0) goto L_0x009e
            java.lang.Object r3 = defpackage.jwx.a(r3, r4)
            r1.q(r2, r6)
            goto L_0x00e1
        L_0x00d6:
            jxl r5 = defpackage.jtn.k
            boolean r4 = r1.t(r2, r4, r5)
            if (r4 == 0) goto L_0x009e
            r1.g()
        L_0x00e1:
            goto L_0x008e
        L_0x00e2:
            jwn r1 = r1.c()
            goto L_0x008a
        L_0x00e7:
            if (r3 == 0) goto L_0x0107
            boolean r13 = r3 instanceof java.util.ArrayList
            if (r13 != 0) goto L_0x00f3
            jta r3 = (defpackage.jta) r3
            r12.F(r3)
            goto L_0x0107
        L_0x00f3:
            java.util.ArrayList r3 = (java.util.ArrayList) r3
            int r13 = r3.size()
        L_0x00f9:
            int r13 = r13 + -1
            if (r13 < 0) goto L_0x0107
            java.lang.Object r14 = r3.get(r13)
            jta r14 = (defpackage.jta) r14
            r12.F(r14)
            goto L_0x00f9
        L_0x0107:
            return r0
        L_0x0108:
            r0 = r1
            goto L_0x002a
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.jtl.N(long):jwn");
    }

    private final void O(long j2, jwn jwn) {
        jwn b2;
        jwn b3;
        while (jwn.b < j2 && (b3 = jwn.b()) != null) {
            jwn = b3;
        }
        while (true) {
            if (!jwn.i() || (b2 = jwn.b()) == null) {
                jps jps = this.i;
                while (true) {
                    jwn jwn2 = (jwn) jps.a;
                    if (jwn2.b < jwn.b) {
                        if (!jwn.j()) {
                            continue;
                            break;
                        } else if (jps.d(jwn2, jwn)) {
                            if (jwn2.h()) {
                                jwn2.e();
                                return;
                            }
                            return;
                        } else if (jwn.h()) {
                            jwn.e();
                        }
                    } else {
                        return;
                    }
                }
            } else {
                jwn = b2;
            }
        }
    }

    private static final void P(jta jta, jwn jwn, int i2) {
        jta.y(jwn, i2 + jtn.a);
    }

    private static final boolean Q(Object obj) {
        if (obj instanceof jqb) {
            jnu.c(obj, "null cannot be cast to non-null type kotlinx.coroutines.CancellableContinuation<kotlin.Unit>");
            return jtn.c((jqb) obj, jkd.a);
        } else if (obj instanceof jyc) {
            jnu.c(obj, "null cannot be cast to non-null type kotlinx.coroutines.selects.SelectImplementation<*>");
            jyc jyc = (jyc) obj;
            throw null;
        } else if (obj instanceof jti) {
            jti jti = (jti) obj;
            throw null;
        } else {
            Objects.toString(obj);
            throw new IllegalStateException("Unexpected waiter: ".concat(String.valueOf(obj)));
        }
    }

    private final Object R(jlr jlr) {
        jqb jqb = new jqb(jji.av(jlr), 1);
        jqb.s();
        Throwable j2 = j();
        if (jqv.b) {
            j2 = jxk.a(j2, jqb);
        }
        jqb.bE(jji.b(j2));
        Object a2 = jqb.a();
        if (a2 == jlx.COROUTINE_SUSPENDED) {
            jji.as(jlr);
        }
        if (a2 == jlx.COROUTINE_SUSPENDED) {
            return a2;
        }
        return jkd.a;
    }

    private final void S(jqb jqb) {
        Throwable j2 = j();
        if (jqv.b) {
            j2 = jxk.a(j2, jqb);
        }
        jqb.bE(jji.b(j2));
    }

    private static final boolean T(Object obj, Object obj2) {
        if (obj instanceof jyc) {
            jyc jyc = (jyc) obj;
            throw null;
        } else if (obj instanceof jtz) {
            jnu.c(obj, "null cannot be cast to non-null type kotlinx.coroutines.channels.ReceiveCatching<E of kotlinx.coroutines.channels.BufferedChannel>");
            return jtn.c(((jtz) obj).a, new jtt(obj2));
        } else if (obj instanceof jth) {
            jnu.c(obj, "null cannot be cast to non-null type kotlinx.coroutines.channels.BufferedChannel.BufferedChannelIterator<E of kotlinx.coroutines.channels.BufferedChannel>");
            jth jth = (jth) obj;
            jqb jqb = jth.b;
            jnu.b(jqb);
            jth.b = null;
            jth.a = obj2;
            jtl jtl = jth.c;
            return jtn.c(jqb, true);
        } else if (obj instanceof jqb) {
            jnu.c(obj, "null cannot be cast to non-null type kotlinx.coroutines.CancellableContinuation<E of kotlinx.coroutines.channels.BufferedChannel>");
            return jtn.c((jqb) obj, obj2);
        } else {
            Objects.toString(obj);
            throw new IllegalStateException("Unexpected receiver type: ".concat(String.valueOf(obj)));
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:12:0x0035  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0022  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    static /* synthetic */ java.lang.Object d(defpackage.jtl r13, defpackage.jlr r14) {
        /*
            boolean r0 = r14 instanceof defpackage.jtj
            if (r0 == 0) goto L_0x0013
            r0 = r14
            jtj r0 = (defpackage.jtj) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            jtj r0 = new jtj
            r0.<init>(r13, r14)
        L_0x0018:
            r6 = r0
            java.lang.Object r14 = r6.a
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r1 = r6.c
            r2 = 1
            if (r1 == 0) goto L_0x0035
            if (r1 != r2) goto L_0x002d
            defpackage.jji.c(r14)
            jtt r14 = (defpackage.jtt) r14
            java.lang.Object r13 = r14.b
            goto L_0x009d
        L_0x002d:
            java.lang.IllegalStateException r13 = new java.lang.IllegalStateException
            java.lang.String r14 = "call to 'resume' before 'invoke' with coroutine"
            r13.<init>(r14)
            throw r13
        L_0x0035:
            defpackage.jji.c(r14)
            jps r14 = r13.e
            java.lang.Object r14 = r14.a
            jwn r14 = (defpackage.jwn) r14
        L_0x003e:
            boolean r1 = r13.p()
            if (r1 == 0) goto L_0x004f
            java.lang.Throwable r13 = r13.h()
            jtr r14 = new jtr
            r14.<init>(r13)
            r13 = r14
            goto L_0x009d
        L_0x004f:
            jpr r1 = r13.c
            long r4 = r1.b()
            int r1 = defpackage.jtn.a
            long r7 = (long) r1
            long r7 = r4 / r7
            int r1 = defpackage.jtn.a
            long r9 = (long) r1
            long r9 = r4 % r9
            int r3 = (int) r9
            long r9 = r14.b
            int r1 = (r9 > r7 ? 1 : (r9 == r7 ? 0 : -1))
            if (r1 == 0) goto L_0x006d
            jwn r1 = r13.y(r7, r14)
            if (r1 == 0) goto L_0x003e
            r14 = r1
        L_0x006d:
            r12 = 0
            r7 = r13
            r8 = r14
            r9 = r3
            r10 = r4
            java.lang.Object r1 = r7.x(r8, r9, r10, r12)
            jxl r7 = defpackage.jtn.l
            if (r1 == r7) goto L_0x009e
            jxl r7 = defpackage.jtn.n
            if (r1 != r7) goto L_0x008a
            long r7 = r13.b()
            int r1 = (r4 > r7 ? 1 : (r4 == r7 ? 0 : -1))
            if (r1 >= 0) goto L_0x003e
            r14.d()
            goto L_0x003e
        L_0x008a:
            jxl r7 = defpackage.jtn.m
            if (r1 != r7) goto L_0x0099
            r6.c = r2
            r1 = r13
            r2 = r14
            java.lang.Object r13 = r1.w(r2, r3, r4, r6)
            if (r13 != r0) goto L_0x009d
            return r0
        L_0x0099:
            r14.d()
            r13 = r1
        L_0x009d:
            return r13
        L_0x009e:
            java.lang.IllegalStateException r13 = new java.lang.IllegalStateException
            java.lang.String r14 = "unexpected"
            r13.<init>(r14)
            throw r13
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.jtl.d(jtl, jlr):java.lang.Object");
    }

    public final long a() {
        return this.c.b;
    }

    public final long b() {
        return this.b.b & 1152921504606846975L;
    }

    public final Object c(jlr jlr) {
        return d(this, jlr);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:43:0x00d5, code lost:
        r7 = r18;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:45:?, code lost:
        S(r7);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:46:0x00da, code lost:
        r1 = r7;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:47:0x00dd, code lost:
        r0 = th;
     */
    /* JADX WARNING: Removed duplicated region for block: B:88:0x017d  */
    /* JADX WARNING: Removed duplicated region for block: B:91:0x0184  */
    /* JADX WARNING: Removed duplicated region for block: B:93:0x0188  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public java.lang.Object e(java.lang.Object r22, defpackage.jlr r23) {
        /*
            r21 = this;
            r9 = r21
            r0 = r23
            jps r1 = r9.d
            java.lang.Object r1 = r1.a
            jwn r1 = (defpackage.jwn) r1
        L_0x000a:
            jpr r2 = r9.b
            long r2 = r2.b()
            r10 = 1152921504606846975(0xfffffffffffffff, double:1.2882297539194265E-231)
            long r12 = r2 & r10
            boolean r14 = r9.s(r2)
            int r2 = defpackage.jtn.a
            long r2 = (long) r2
            long r2 = r12 / r2
            int r4 = defpackage.jtn.a
            long r4 = (long) r4
            long r4 = r12 % r4
            int r15 = (int) r4
            long r4 = r1.b
            int r4 = (r4 > r2 ? 1 : (r4 == r2 ? 0 : -1))
            if (r4 == 0) goto L_0x0040
            jwn r2 = r9.z(r2, r1)
            if (r2 != 0) goto L_0x003e
            if (r14 == 0) goto L_0x000a
            java.lang.Object r0 = r9.R(r0)
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            if (r0 != r1) goto L_0x01ab
            goto L_0x01a2
        L_0x003e:
            r8 = r2
            goto L_0x0041
        L_0x0040:
            r8 = r1
        L_0x0041:
            r7 = 0
            r1 = r21
            r2 = r8
            r3 = r15
            r4 = r22
            r5 = r12
            r16 = r8
            r8 = r14
            int r1 = r1.v(r2, r3, r4, r5, r7, r8)
            if (r1 == 0) goto L_0x01a6
            r8 = 1
            if (r1 == r8) goto L_0x01ab
            r7 = 2
            if (r1 == r7) goto L_0x0193
            r14 = 3
            r5 = 4
            if (r1 == r14) goto L_0x0079
            if (r1 == r5) goto L_0x0064
            r16.d()
            r1 = r16
            goto L_0x000a
        L_0x0064:
            long r1 = r21.a()
            int r1 = (r12 > r1 ? 1 : (r12 == r1 ? 0 : -1))
            if (r1 >= 0) goto L_0x006f
            r16.d()
        L_0x006f:
            java.lang.Object r0 = r9.R(r0)
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            if (r0 != r1) goto L_0x01ab
            goto L_0x01a2
        L_0x0079:
            jlr r1 = defpackage.jji.av(r23)
            jqb r6 = defpackage.jqw.l(r1)
            r17 = 0
            r1 = r21
            r2 = r16
            r3 = r15
            r4 = r22
            r14 = r5
            r18 = r6
            r5 = r12
            r10 = r7
            r7 = r18
            r11 = r8
            r8 = r17
            int r1 = r1.v(r2, r3, r4, r5, r7, r8)     // Catch:{ all -> 0x018c }
            if (r1 == 0) goto L_0x016b
            if (r1 == r11) goto L_0x0166
            if (r1 == r10) goto L_0x015e
            if (r1 == r14) goto L_0x0150
            r2 = 5
            java.lang.String r12 = "unexpected"
            if (r1 != r2) goto L_0x0148
            r16.d()     // Catch:{ all -> 0x018c }
            jps r1 = r9.d     // Catch:{ all -> 0x018c }
            java.lang.Object r1 = r1.a     // Catch:{ all -> 0x018c }
            jwn r1 = (defpackage.jwn) r1     // Catch:{ all -> 0x018c }
        L_0x00ae:
            jpr r2 = r9.b     // Catch:{ all -> 0x018c }
            long r2 = r2.b()     // Catch:{ all -> 0x018c }
            r15 = 1152921504606846975(0xfffffffffffffff, double:1.2882297539194265E-231)
            long r19 = r2 & r15
            boolean r13 = r9.s(r2)     // Catch:{ all -> 0x018c }
            int r2 = defpackage.jtn.a     // Catch:{ all -> 0x018c }
            long r2 = (long) r2     // Catch:{ all -> 0x018c }
            long r4 = r19 / r2
            long r2 = r19 % r2
            int r8 = (int) r2     // Catch:{ all -> 0x018c }
            long r2 = r1.b     // Catch:{ all -> 0x018c }
            int r2 = (r2 > r4 ? 1 : (r2 == r4 ? 0 : -1))
            if (r2 == 0) goto L_0x00e9
            jwn r2 = r9.z(r4, r1)     // Catch:{ all -> 0x00e3 }
            if (r2 != 0) goto L_0x00df
            if (r13 == 0) goto L_0x00ae
            r7 = r18
            r9.S(r7)     // Catch:{ all -> 0x00dd }
            r1 = r7
            goto L_0x0175
        L_0x00dd:
            r0 = move-exception
            goto L_0x00e6
        L_0x00df:
            r7 = r18
            r5 = r2
            goto L_0x00ec
        L_0x00e3:
            r0 = move-exception
            r7 = r18
        L_0x00e6:
            r1 = r7
            goto L_0x018f
        L_0x00e9:
            r7 = r18
            r5 = r1
        L_0x00ec:
            r1 = r21
            r2 = r5
            r3 = r8
            r4 = r22
            r17 = r5
            r5 = r19
            r18 = r7
            r15 = r8
            r8 = r13
            int r1 = r1.v(r2, r3, r4, r5, r7, r8)     // Catch:{ all -> 0x018c }
            if (r1 == 0) goto L_0x013e
            if (r1 == r11) goto L_0x0136
            if (r1 == r10) goto L_0x0128
            r2 = 3
            if (r1 == r2) goto L_0x0120
            if (r1 == r14) goto L_0x010f
            r17.d()     // Catch:{ all -> 0x018c }
            r1 = r17
            goto L_0x00ae
        L_0x010f:
            long r1 = r21.a()     // Catch:{ all -> 0x018c }
            int r1 = (r19 > r1 ? 1 : (r19 == r1 ? 0 : -1))
            if (r1 >= 0) goto L_0x011a
            r17.d()     // Catch:{ all -> 0x018c }
        L_0x011a:
            r1 = r18
        L_0x011c:
            r9.S(r1)     // Catch:{ all -> 0x018a }
            goto L_0x0175
        L_0x0120:
            r1 = r18
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException     // Catch:{ all -> 0x018a }
            r0.<init>(r12)     // Catch:{ all -> 0x018a }
            throw r0     // Catch:{ all -> 0x018a }
        L_0x0128:
            r1 = r18
            if (r13 == 0) goto L_0x0130
            r17.g()     // Catch:{ all -> 0x018a }
            goto L_0x011c
        L_0x0130:
            r2 = r17
            P(r1, r2, r15)     // Catch:{ all -> 0x018a }
            goto L_0x0175
        L_0x0136:
            r1 = r18
            jkd r2 = defpackage.jkd.a     // Catch:{ all -> 0x018a }
        L_0x013a:
            r1.bE(r2)     // Catch:{ all -> 0x018a }
            goto L_0x0175
        L_0x013e:
            r2 = r17
            r1 = r18
            r2.d()     // Catch:{ all -> 0x018a }
            jkd r2 = defpackage.jkd.a     // Catch:{ all -> 0x018a }
            goto L_0x013a
        L_0x0148:
            r1 = r18
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException     // Catch:{ all -> 0x018a }
            r0.<init>(r12)     // Catch:{ all -> 0x018a }
            throw r0     // Catch:{ all -> 0x018a }
        L_0x0150:
            r1 = r18
            long r2 = r21.a()     // Catch:{ all -> 0x018a }
            int r2 = (r12 > r2 ? 1 : (r12 == r2 ? 0 : -1))
            if (r2 >= 0) goto L_0x011c
            r16.d()     // Catch:{ all -> 0x018a }
            goto L_0x011c
        L_0x015e:
            r2 = r16
            r1 = r18
            P(r1, r2, r15)     // Catch:{ all -> 0x018a }
            goto L_0x0175
        L_0x0166:
            r1 = r18
            jkd r2 = defpackage.jkd.a     // Catch:{ all -> 0x018a }
            goto L_0x013a
        L_0x016b:
            r2 = r16
            r1 = r18
            r2.d()     // Catch:{ all -> 0x018a }
            jkd r2 = defpackage.jkd.a     // Catch:{ all -> 0x018a }
            goto L_0x013a
        L_0x0175:
            java.lang.Object r1 = r1.a()
            jlx r2 = defpackage.jlx.COROUTINE_SUSPENDED
            if (r1 != r2) goto L_0x0180
            defpackage.jji.as(r23)
        L_0x0180:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            if (r1 == r0) goto L_0x0186
            jkd r1 = defpackage.jkd.a
        L_0x0186:
            if (r1 != r0) goto L_0x01ab
            r0 = r1
            goto L_0x01a2
        L_0x018a:
            r0 = move-exception
            goto L_0x018f
        L_0x018c:
            r0 = move-exception
            r1 = r18
        L_0x018f:
            r1.v()
            throw r0
        L_0x0193:
            r2 = r16
            if (r14 == 0) goto L_0x01a3
            r2.g()
            java.lang.Object r0 = r9.R(r0)
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            if (r0 != r1) goto L_0x01ab
        L_0x01a2:
            return r0
        L_0x01a3:
            boolean r0 = defpackage.jqv.a
            goto L_0x01ab
        L_0x01a6:
            r2 = r16
            r2.d()
        L_0x01ab:
            jkd r0 = defpackage.jkd.a
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.jtl.e(java.lang.Object, jlr):java.lang.Object");
    }

    public final Object f() {
        long j2 = this.c.b;
        long j3 = this.b.b;
        if (K(j3)) {
            return new jtr(h());
        }
        if (j2 >= (j3 & 1152921504606846975L)) {
            return jtt.a;
        }
        jps jps = this.e;
        jxl jxl = jtn.j;
        jwn jwn = (jwn) jps.a;
        while (!p()) {
            long b2 = this.c.b();
            long j4 = (long) jtn.a;
            long j5 = b2 / j4;
            int i2 = (int) (b2 % j4);
            if (jwn.b != j5) {
                jwn y = y(j5, jwn);
                if (y != null) {
                    jwn = y;
                } else {
                    continue;
                }
            }
            Object x = x(jwn, i2, b2, jxl);
            if (x == jtn.l) {
                m(b2);
                jwn.g();
                return jtt.a;
            } else if (x == jtn.n) {
                if (b2 < b()) {
                    jwn.d();
                }
            } else if (x != jtn.m) {
                jwn.d();
                return x;
            } else {
                throw new IllegalStateException("unexpected");
            }
        }
        return new jtr(h());
    }

    public Object g(Object obj) {
        jwn jwn;
        long j2 = this.b.b;
        if (!s(j2) && !I(j2 & 1152921504606846975L)) {
            return jtt.a;
        }
        jps jps = this.d;
        jxl jxl = jtn.i;
        jwn jwn2 = (jwn) jps.a;
        while (true) {
            long b2 = this.b.b();
            long j3 = b2 & 1152921504606846975L;
            boolean s = s(b2);
            long j4 = (long) jtn.a;
            long j5 = j3 / j4;
            int i2 = (int) (j3 % j4);
            if (jwn2.b != j5) {
                jwn z = z(j5, jwn2);
                if (z != null) {
                    jwn = z;
                } else if (s) {
                    return new jtr(j());
                }
            } else {
                jwn = jwn2;
            }
            int v = v(jwn, i2, obj, j3, jxl, s);
            if (v == 0) {
                jwn.d();
                return jkd.a;
            } else if (v == 1) {
                return jkd.a;
            } else {
                if (v != 2) {
                    if (v == 3) {
                        throw new IllegalStateException("unexpected");
                    } else if (v != 4) {
                        jwn.d();
                        jwn2 = jwn;
                    } else {
                        if (j3 < a()) {
                            jwn.d();
                        }
                        return new jtr(j());
                    }
                } else if (s) {
                    jwn.g();
                    return new jtr(j());
                } else {
                    jwn.g();
                    return jtt.a;
                }
            }
        }
    }

    /* access modifiers changed from: protected */
    public final Throwable h() {
        return (Throwable) this.j.a;
    }

    public final Throwable i() {
        Throwable h2 = h();
        if (h2 == null) {
            return new jtu();
        }
        return h2;
    }

    /* access modifiers changed from: protected */
    public final Throwable j() {
        Throwable h2 = h();
        if (h2 == null) {
            return new jtv();
        }
        return h2;
    }

    /* access modifiers changed from: protected */
    public final void k(long j2) {
        boolean z = jqv.a;
        jwn jwn = (jwn) this.e.a;
        while (true) {
            jpr jpr = this.c;
            int i2 = this.f;
            long j3 = jpr.b;
            if (j2 >= Math.max(((long) i2) + j3, C())) {
                if (this.c.c(j3, 1 + j3)) {
                    long j4 = j3 / ((long) jtn.a);
                    int i3 = (int) (j3 % ((long) jtn.a));
                    if (jwn.b != j4) {
                        jwn y = y(j4, jwn);
                        if (y != null) {
                            jwn = y;
                        }
                    }
                    if (x(jwn, i3, j3, (Object) null) != jtn.n) {
                        jwn.d();
                    } else if (j3 < b()) {
                        jwn.d();
                    }
                }
            } else {
                return;
            }
        }
    }

    public final void l(jna jna) {
        if (!this.k.d((Object) null, jna)) {
            jps jps = this.k;
            do {
                Object obj = jps.a;
                if (obj != jtn.p) {
                    if (obj == jtn.q) {
                        throw new IllegalStateException("Another handler was already registered and successfully invoked");
                    }
                    Objects.toString(obj);
                    throw new IllegalStateException("Another handler is already registered: ".concat(String.valueOf(obj)));
                }
            } while (!this.k.d(jtn.p, jtn.q));
            jna.a(h());
        }
    }

    public final void m(long j2) {
        long j3;
        long j4;
        if (!L()) {
            do {
            } while (C() <= j2);
            int i2 = jtn.b;
            int i3 = 0;
            while (i3 < i2) {
                long C = C();
                if (C != (4611686018427387903L & this.h.b) || C != C()) {
                    i3++;
                } else {
                    return;
                }
            }
            jpr jpr = this.h;
            do {
                j3 = jpr.b;
            } while (!jpr.c(j3, jtn.a(j3 & 4611686018427387903L, true)));
            while (true) {
                long C2 = C();
                long j5 = this.h.b;
                long j6 = j5 & 4611686018427387903L;
                long j7 = 4611686018427387904L & j5;
                if (C2 == j6 && C2 == C()) {
                    break;
                } else if (j7 == 0) {
                    this.h.c(j5, jtn.a(j6, true));
                }
            }
            jpr jpr2 = this.h;
            do {
                j4 = jpr2.b;
            } while (!jpr2.c(j4, jtn.a(j4 & 4611686018427387903L, false)));
        }
    }

    public final boolean n(Throwable th) {
        return o(th, false);
    }

    /* access modifiers changed from: protected */
    public final boolean o(Throwable th, boolean z) {
        Object obj;
        jxl jxl;
        long j2;
        long j3;
        long j4;
        long j5;
        if (z) {
            jpr jpr = this.b;
            do {
                j5 = jpr.b;
                if (((int) (j5 >> 60)) != 0) {
                    break;
                }
            } while (jpr.c(j5, jtn.b(j5 & 1152921504606846975L, 1)));
        }
        boolean d2 = this.j.d(jtn.r, th);
        if (z) {
            jpr jpr2 = this.b;
            do {
                j4 = jpr2.b;
            } while (!jpr2.c(j4, jtn.b(j4 & 1152921504606846975L, 3)));
        } else {
            jpr jpr3 = this.b;
            do {
                j2 = jpr3.b;
                int i2 = (int) (j2 >> 60);
                if (i2 == 0) {
                    j3 = jtn.b(j2 & 1152921504606846975L, 2);
                } else if (i2 != 1) {
                    break;
                } else {
                    j3 = jtn.b(j2 & 1152921504606846975L, 3);
                }
            } while (!jpr3.c(j2, j3));
        }
        q();
        if (d2) {
            jps jps = this.k;
            do {
                obj = jps.a;
                if (obj == null) {
                    jxl = jtn.p;
                } else {
                    jxl = jtn.q;
                }
            } while (!jps.d(obj, jxl));
            if (obj != null) {
                job.c(obj, 1);
                ((jna) obj).a(h());
                return true;
            }
        }
        return d2;
    }

    public final boolean p() {
        return K(this.b.b);
    }

    public final boolean q() {
        return s(this.b.b);
    }

    public final void r(CancellationException cancellationException) {
        if (cancellationException == null) {
            cancellationException = new CancellationException("Channel was cancelled");
        }
        o(cancellationException, true);
    }

    public final boolean s(long j2) {
        return J(j2, false);
    }

    /* access modifiers changed from: protected */
    public boolean t() {
        return false;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:82:0x019a, code lost:
        r4 = r4.b();
     */
    /* JADX WARNING: Code restructure failed: missing block: B:83:0x019e, code lost:
        if (r4 != null) goto L_0x01d3;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.String toString() {
        /*
            r16 = this;
            r0 = r16
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            r1.<init>()
            jpr r2 = r0.b
            long r2 = r2.b
            r4 = 60
            long r2 = r2 >> r4
            int r2 = (int) r2
            r3 = 3
            r4 = 2
            if (r2 == r4) goto L_0x001c
            if (r2 == r3) goto L_0x0016
            goto L_0x0021
        L_0x0016:
            java.lang.String r2 = "cancelled,"
            r1.append(r2)
            goto L_0x0021
        L_0x001c:
            java.lang.String r2 = "closed,"
            r1.append(r2)
        L_0x0021:
            int r2 = r0.f
            java.lang.StringBuilder r5 = new java.lang.StringBuilder
            java.lang.String r6 = "capacity="
            r5.<init>(r6)
            r5.append(r2)
            java.lang.String r2 = ","
            r5.append(r2)
            java.lang.String r5 = r5.toString()
            r1.append(r5)
            java.lang.String r5 = "data=["
            r1.append(r5)
            jps r5 = r0.e
            jwn[] r3 = new defpackage.jwn[r3]
            java.lang.Object r5 = r5.a
            jwn r5 = (defpackage.jwn) r5
            r6 = 0
            r3[r6] = r5
            jps r5 = r0.d
            java.lang.Object r5 = r5.a
            jwn r5 = (defpackage.jwn) r5
            r7 = 1
            r3[r7] = r5
            jps r5 = r0.i
            java.lang.Object r5 = r5.a
            jwn r5 = (defpackage.jwn) r5
            r3[r4] = r5
            java.util.List r3 = defpackage.jji.o(r3)
            java.util.ArrayList r4 = new java.util.ArrayList
            r4.<init>()
            java.util.Iterator r3 = r3.iterator()
        L_0x0067:
            boolean r5 = r3.hasNext()
            if (r5 == 0) goto L_0x007c
            java.lang.Object r5 = r3.next()
            r7 = r5
            jwn r7 = (defpackage.jwn) r7
            jwn r8 = defpackage.jtn.s
            if (r7 == r8) goto L_0x0067
            r4.add(r5)
            goto L_0x0067
        L_0x007c:
            java.util.Iterator r3 = r4.iterator()
            boolean r4 = r3.hasNext()
            if (r4 == 0) goto L_0x01d6
            java.lang.Object r4 = r3.next()
            boolean r5 = r3.hasNext()
            if (r5 == 0) goto L_0x00ac
            r5 = r4
            jwn r5 = (defpackage.jwn) r5
            long r7 = r5.b
        L_0x0095:
            java.lang.Object r5 = r3.next()
            r9 = r5
            jwn r9 = (defpackage.jwn) r9
            long r9 = r9.b
            int r11 = (r7 > r9 ? 1 : (r7 == r9 ? 0 : -1))
            if (r11 <= 0) goto L_0x00a3
            r7 = r9
        L_0x00a3:
            if (r11 <= 0) goto L_0x00a6
            r4 = r5
        L_0x00a6:
            boolean r5 = r3.hasNext()
            if (r5 != 0) goto L_0x0095
        L_0x00ac:
            jwn r4 = (defpackage.jwn) r4
            long r7 = r16.a()
            long r9 = r16.b()
        L_0x00b6:
            int r3 = defpackage.jtn.a
            r5 = r6
        L_0x00b9:
            if (r5 >= r3) goto L_0x019a
            long r11 = r4.b
            int r13 = defpackage.jtn.a
            long r13 = (long) r13
            long r11 = r11 * r13
            long r13 = (long) r5
            long r11 = r11 + r13
            int r13 = (r11 > r9 ? 1 : (r11 == r9 ? 0 : -1))
            if (r13 < 0) goto L_0x00cb
            int r14 = (r11 > r7 ? 1 : (r11 == r7 ? 0 : -1))
            if (r14 >= 0) goto L_0x01a0
        L_0x00cb:
            java.lang.Object r14 = r4.m(r5)
            java.lang.Object r15 = r4.l(r5)
            boolean r6 = r14 instanceof defpackage.jqb
            if (r6 == 0) goto L_0x00ed
            int r6 = (r11 > r7 ? 1 : (r11 == r7 ? 0 : -1))
            if (r6 >= 0) goto L_0x00e1
            if (r13 < 0) goto L_0x00e1
            java.lang.String r6 = "receive"
            goto L_0x016b
        L_0x00e1:
            if (r13 >= 0) goto L_0x00e9
            if (r6 < 0) goto L_0x00e9
            java.lang.String r6 = "send"
            goto L_0x016b
        L_0x00e9:
            java.lang.String r6 = "cont"
            goto L_0x016b
        L_0x00ed:
            boolean r6 = r14 instanceof defpackage.jyc
            if (r6 == 0) goto L_0x0106
            int r6 = (r11 > r7 ? 1 : (r11 == r7 ? 0 : -1))
            if (r6 >= 0) goto L_0x00fb
            if (r13 < 0) goto L_0x00fb
            java.lang.String r6 = "onReceive"
            goto L_0x016b
        L_0x00fb:
            if (r13 >= 0) goto L_0x0103
            if (r6 < 0) goto L_0x0103
            java.lang.String r6 = "onSend"
            goto L_0x016b
        L_0x0103:
            java.lang.String r6 = "select"
            goto L_0x016b
        L_0x0106:
            boolean r6 = r14 instanceof defpackage.jtz
            if (r6 == 0) goto L_0x010d
            java.lang.String r6 = "receiveCatching"
            goto L_0x016b
        L_0x010d:
            boolean r6 = r14 instanceof defpackage.jti
            if (r6 == 0) goto L_0x0114
            java.lang.String r6 = "sendBroadcast"
            goto L_0x016b
        L_0x0114:
            boolean r6 = r14 instanceof defpackage.juc
            if (r6 == 0) goto L_0x0121
            java.lang.String r6 = "EB("
            java.lang.String r11 = ")"
            java.lang.String r6 = defpackage.a.ao(r14, r6, r11)
            goto L_0x016b
        L_0x0121:
            jxl r6 = defpackage.jtn.e
            boolean r6 = defpackage.jnu.i(r14, r6)
            if (r6 != 0) goto L_0x0169
            jxl r6 = defpackage.jtn.f
            boolean r6 = defpackage.jnu.i(r14, r6)
            if (r6 == 0) goto L_0x0132
            goto L_0x0169
        L_0x0132:
            if (r14 == 0) goto L_0x0195
            jxl r6 = defpackage.jtn.d
            boolean r6 = defpackage.jnu.i(r14, r6)
            if (r6 != 0) goto L_0x0195
            jxl r6 = defpackage.jtn.h
            boolean r6 = defpackage.jnu.i(r14, r6)
            if (r6 != 0) goto L_0x0195
            jxl r6 = defpackage.jtn.g
            boolean r6 = defpackage.jnu.i(r14, r6)
            if (r6 != 0) goto L_0x0195
            jxl r6 = defpackage.jtn.j
            boolean r6 = defpackage.jnu.i(r14, r6)
            if (r6 != 0) goto L_0x0195
            jxl r6 = defpackage.jtn.i
            boolean r6 = defpackage.jnu.i(r14, r6)
            if (r6 != 0) goto L_0x0195
            jxl r6 = defpackage.jtn.k
            boolean r6 = defpackage.jnu.i(r14, r6)
            if (r6 != 0) goto L_0x0195
            java.lang.String r6 = r14.toString()
            goto L_0x016b
        L_0x0169:
            java.lang.String r6 = "resuming_sender"
        L_0x016b:
            if (r15 == 0) goto L_0x018a
            java.lang.StringBuilder r11 = new java.lang.StringBuilder
            java.lang.String r12 = "("
            r11.<init>(r12)
            r11.append(r6)
            r11.append(r2)
            r11.append(r15)
            java.lang.String r6 = "),"
            r11.append(r6)
            java.lang.String r6 = r11.toString()
            r1.append(r6)
            goto L_0x0195
        L_0x018a:
            java.lang.String r6 = java.lang.String.valueOf(r6)
            java.lang.String r6 = r6.concat(r2)
            r1.append(r6)
        L_0x0195:
            int r5 = r5 + 1
            r6 = 0
            goto L_0x00b9
        L_0x019a:
            jwn r4 = r4.b()
            if (r4 != 0) goto L_0x01d3
        L_0x01a0:
            int r2 = r1.length()
            if (r2 == 0) goto L_0x01cb
            int r2 = defpackage.job.i(r1)
            char r2 = r1.charAt(r2)
            r3 = 44
            if (r2 != r3) goto L_0x01c1
            int r2 = r1.length()
            int r2 = r2 + -1
            java.lang.StringBuilder r2 = r1.deleteCharAt(r2)
            java.lang.String r3 = "deleteCharAt(...)"
            defpackage.jnu.d(r2, r3)
        L_0x01c1:
            java.lang.String r2 = "]"
            r1.append(r2)
            java.lang.String r1 = r1.toString()
            return r1
        L_0x01cb:
            java.util.NoSuchElementException r1 = new java.util.NoSuchElementException
            java.lang.String r2 = "Char sequence is empty."
            r1.<init>(r2)
            throw r1
        L_0x01d3:
            r6 = 0
            goto L_0x00b6
        L_0x01d6:
            java.util.NoSuchElementException r1 = new java.util.NoSuchElementException
            r1.<init>()
            throw r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.jtl.toString():java.lang.String");
    }

    public final jth u() {
        return new jth(this);
    }

    public final int v(jwn jwn, int i2, Object obj, long j2, Object obj2, boolean z) {
        jwn.r(i2, obj);
        if (z) {
            return M(jwn, i2, obj, j2, obj2, true);
        }
        Object m = jwn.m(i2);
        if (m == null) {
            if (I(j2)) {
                if (jwn.t(i2, (Object) null, jtn.c)) {
                    return 1;
                }
            } else if (obj2 == null) {
                return 3;
            } else {
                if (jwn.t(i2, (Object) null, obj2)) {
                    return 2;
                }
            }
        } else if (m instanceof jta) {
            jwn.p(i2);
            if (T(m, obj)) {
                jwn.s(i2, jtn.h);
                return 0;
            } else if (jwn.k(i2, jtn.j) == jtn.j) {
                return 5;
            } else {
                jwn.q(i2, true);
                return 5;
            }
        }
        return M(jwn, i2, obj, j2, obj2, false);
    }

    /* JADX WARNING: Removed duplicated region for block: B:12:0x0030  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object w(defpackage.jwn r10, int r11, long r12, defpackage.jlr r14) {
        /*
            r9 = this;
            boolean r0 = r14 instanceof defpackage.jtk
            if (r0 == 0) goto L_0x0013
            r0 = r14
            jtk r0 = (defpackage.jtk) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            jtk r0 = new jtk
            r0.<init>(r9, r14)
        L_0x0018:
            java.lang.Object r14 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            r3 = 1
            if (r2 == 0) goto L_0x0030
            if (r2 != r3) goto L_0x0028
            defpackage.jji.c(r14)
            goto L_0x00ec
        L_0x0028:
            java.lang.IllegalStateException r10 = new java.lang.IllegalStateException
            java.lang.String r11 = "call to 'resume' before 'invoke' with coroutine"
            r10.<init>(r11)
            throw r10
        L_0x0030:
            defpackage.jji.c(r14)
            r0.c = r3
            jlr r14 = defpackage.jji.av(r0)
            jqb r14 = defpackage.jqw.l(r14)
            jtz r8 = new jtz     // Catch:{ all -> 0x00f1 }
            r8.<init>(r14)     // Catch:{ all -> 0x00f1 }
            r2 = r9
            r3 = r10
            r4 = r11
            r5 = r12
            r7 = r8
            java.lang.Object r2 = r2.x(r3, r4, r5, r7)     // Catch:{ all -> 0x00f1 }
            jxl r3 = defpackage.jtn.l     // Catch:{ all -> 0x00f1 }
            if (r2 != r3) goto L_0x0054
            B(r8, r10, r11)     // Catch:{ all -> 0x00f1 }
            goto L_0x00de
        L_0x0054:
            jxl r11 = defpackage.jtn.n     // Catch:{ all -> 0x00f1 }
            if (r2 != r11) goto L_0x00d5
            long r2 = r9.b()     // Catch:{ all -> 0x00f1 }
            int r11 = (r12 > r2 ? 1 : (r12 == r2 ? 0 : -1))
            if (r11 >= 0) goto L_0x0063
            r10.d()     // Catch:{ all -> 0x00f1 }
        L_0x0063:
            jps r10 = r9.e     // Catch:{ all -> 0x00f1 }
            java.lang.Object r10 = r10.a     // Catch:{ all -> 0x00f1 }
            jwn r10 = (defpackage.jwn) r10     // Catch:{ all -> 0x00f1 }
        L_0x0069:
            boolean r11 = r9.p()     // Catch:{ all -> 0x00f1 }
            if (r11 == 0) goto L_0x0081
            java.lang.Throwable r10 = r9.h()     // Catch:{ all -> 0x00f1 }
            jtr r11 = new jtr     // Catch:{ all -> 0x00f1 }
            r11.<init>(r10)     // Catch:{ all -> 0x00f1 }
            jtt r10 = new jtt     // Catch:{ all -> 0x00f1 }
            r10.<init>(r11)     // Catch:{ all -> 0x00f1 }
            r14.bE(r10)     // Catch:{ all -> 0x00f1 }
            goto L_0x00de
        L_0x0081:
            jpr r11 = r9.c     // Catch:{ all -> 0x00f1 }
            long r11 = r11.b()     // Catch:{ all -> 0x00f1 }
            int r13 = defpackage.jtn.a     // Catch:{ all -> 0x00f1 }
            long r2 = (long) r13     // Catch:{ all -> 0x00f1 }
            long r4 = r11 / r2
            long r2 = r11 % r2
            int r13 = (int) r2     // Catch:{ all -> 0x00f1 }
            long r2 = r10.b     // Catch:{ all -> 0x00f1 }
            int r2 = (r2 > r4 ? 1 : (r2 == r4 ? 0 : -1))
            if (r2 == 0) goto L_0x009c
            jwn r2 = r9.y(r4, r10)     // Catch:{ all -> 0x00f1 }
            if (r2 == 0) goto L_0x0069
            r10 = r2
        L_0x009c:
            r2 = r9
            r3 = r10
            r4 = r13
            r5 = r11
            r7 = r8
            java.lang.Object r2 = r2.x(r3, r4, r5, r7)     // Catch:{ all -> 0x00f1 }
            jxl r3 = defpackage.jtn.l     // Catch:{ all -> 0x00f1 }
            if (r2 != r3) goto L_0x00ad
            B(r8, r10, r13)     // Catch:{ all -> 0x00f1 }
            goto L_0x00de
        L_0x00ad:
            jxl r13 = defpackage.jtn.n     // Catch:{ all -> 0x00f1 }
            if (r2 != r13) goto L_0x00bd
            long r2 = r9.b()     // Catch:{ all -> 0x00f1 }
            int r11 = (r11 > r2 ? 1 : (r11 == r2 ? 0 : -1))
            if (r11 >= 0) goto L_0x0069
            r10.d()     // Catch:{ all -> 0x00f1 }
            goto L_0x0069
        L_0x00bd:
            jxl r11 = defpackage.jtn.m     // Catch:{ all -> 0x00f1 }
            if (r2 == r11) goto L_0x00cd
            r10.d()     // Catch:{ all -> 0x00f1 }
            jtt r10 = new jtt     // Catch:{ all -> 0x00f1 }
            r10.<init>(r2)     // Catch:{ all -> 0x00f1 }
        L_0x00c9:
            r14.B(r10)     // Catch:{ all -> 0x00f1 }
            goto L_0x00de
        L_0x00cd:
            java.lang.IllegalStateException r10 = new java.lang.IllegalStateException     // Catch:{ all -> 0x00f1 }
            java.lang.String r11 = "unexpected"
            r10.<init>(r11)     // Catch:{ all -> 0x00f1 }
            throw r10     // Catch:{ all -> 0x00f1 }
        L_0x00d5:
            r10.d()     // Catch:{ all -> 0x00f1 }
            jtt r10 = new jtt     // Catch:{ all -> 0x00f1 }
            r10.<init>(r2)     // Catch:{ all -> 0x00f1 }
            goto L_0x00c9
        L_0x00de:
            java.lang.Object r14 = r14.a()
            jlx r10 = defpackage.jlx.COROUTINE_SUSPENDED
            if (r14 != r10) goto L_0x00e9
            defpackage.jji.as(r0)
        L_0x00e9:
            if (r14 != r1) goto L_0x00ec
            return r1
        L_0x00ec:
            jtt r14 = (defpackage.jtt) r14
            java.lang.Object r10 = r14.b
            return r10
        L_0x00f1:
            r10 = move-exception
            r14.v()
            throw r10
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.jtl.w(jwn, int, long, jlr):java.lang.Object");
    }

    public final Object x(jwn jwn, int i2, long j2, Object obj) {
        Object m = jwn.m(i2);
        if (m == null) {
            if (j2 >= (this.b.b & 1152921504606846975L)) {
                if (obj == null) {
                    return jtn.m;
                }
                if (jwn.t(i2, (Object) null, obj)) {
                    D();
                    return jtn.l;
                }
            }
        } else if (m == jtn.c && jwn.t(i2, m, jtn.h)) {
            D();
            return jwn.n(i2);
        }
        while (true) {
            Object m2 = jwn.m(i2);
            if (m2 == null || m2 == jtn.d) {
                if (j2 < (this.b.b & 1152921504606846975L)) {
                    if (jwn.t(i2, m2, jtn.g)) {
                        D();
                        return jtn.n;
                    }
                } else if (obj == null) {
                    return jtn.m;
                } else {
                    if (jwn.t(i2, m2, obj)) {
                        D();
                        return jtn.l;
                    }
                }
            } else if (m2 == jtn.c) {
                if (jwn.t(i2, m2, jtn.h)) {
                    D();
                    return jwn.n(i2);
                }
            } else if (!(m2 == jtn.i || m2 == jtn.g)) {
                if (m2 == jtn.k) {
                    D();
                    return jtn.n;
                } else if (m2 != jtn.f && jwn.t(i2, m2, jtn.e)) {
                    boolean z = m2 instanceof juc;
                    if (z) {
                        m2 = ((juc) m2).a;
                    }
                    if (Q(m2)) {
                        jwn.s(i2, jtn.h);
                        D();
                        return jwn.n(i2);
                    }
                    jwn.s(i2, jtn.i);
                    jwn.q(i2, false);
                    if (z) {
                        D();
                    }
                    return jtn.n;
                }
            }
        }
        return jtn.n;
    }

    public final jwn y(long j2, jwn jwn) {
        Object a2;
        long j3;
        long j4;
        jtm jtm = jtm.a;
        loop0:
        while (true) {
            a2 = jwm.a(jwn, j2, jtm);
            if (jxj.a(a2)) {
                break;
            }
            jwn b2 = jxj.b(a2);
            while (true) {
                jps jps = this.e;
                jwn jwn2 = (jwn) jps.a;
                if (jwn2.b >= b2.b) {
                    break loop0;
                } else if (b2.j()) {
                    if (jps.d(jwn2, b2)) {
                        if (jwn2.h()) {
                            jwn2.e();
                        }
                    } else if (b2.h()) {
                        b2.e();
                    }
                }
            }
        }
        if (jxj.a(a2)) {
            q();
            if (jwn.b * ((long) jtn.a) >= b()) {
                return null;
            }
            jwn.d();
            return null;
        }
        jwn b3 = jxj.b(a2);
        if (!L() && j2 <= C() / ((long) jtn.a)) {
            jps jps2 = this.i;
            while (true) {
                jwn jwn3 = (jwn) jps2.a;
                if (jwn3.b >= b3.b || !b3.j()) {
                    break;
                } else if (jps2.d(jwn3, b3)) {
                    if (jwn3.h()) {
                        jwn3.e();
                    }
                } else if (b3.h()) {
                    b3.e();
                }
            }
        }
        long j5 = b3.b;
        if (j5 > j2) {
            long j6 = (long) jtn.a;
            jpr jpr = this.c;
            do {
                j3 = j5 * j6;
                j4 = jpr.b;
                if (j4 >= j3 || this.c.c(j4, j3)) {
                }
                j3 = j5 * j6;
                j4 = jpr.b;
                break;
            } while (this.c.c(j4, j3));
            if (b3.b * ((long) jtn.a) >= b()) {
                return null;
            }
            b3.d();
            return null;
        }
        boolean z = jqv.a;
        return b3;
    }

    public final jwn z(long j2, jwn jwn) {
        Object a2;
        long j3;
        long j4;
        jtm jtm = jtm.a;
        loop0:
        while (true) {
            a2 = jwm.a(jwn, j2, jtm);
            if (jxj.a(a2)) {
                break;
            }
            jwn b2 = jxj.b(a2);
            while (true) {
                jps jps = this.d;
                jwn jwn2 = (jwn) jps.a;
                if (jwn2.b >= b2.b) {
                    break loop0;
                } else if (b2.j()) {
                    if (jps.d(jwn2, b2)) {
                        if (jwn2.h()) {
                            jwn2.e();
                        }
                    } else if (b2.h()) {
                        b2.e();
                    }
                }
            }
        }
        if (jxj.a(a2)) {
            q();
            if (jwn.b * ((long) jtn.a) >= a()) {
                return null;
            }
            jwn.d();
            return null;
        }
        jwn b3 = jxj.b(a2);
        long j5 = b3.b;
        if (j5 > j2) {
            long j6 = (long) jtn.a;
            jpr jpr = this.b;
            do {
                j3 = jpr.b;
                j4 = 1152921504606846975L & j3;
                if (j4 >= j5 * j6 || this.b.c(j3, jtn.b(j4, (int) (j3 >> 60)))) {
                }
                j3 = jpr.b;
                j4 = 1152921504606846975L & j3;
                break;
            } while (this.b.c(j3, jtn.b(j4, (int) (j3 >> 60))));
            if (b3.b * ((long) jtn.a) >= a()) {
                return null;
            }
            b3.d();
            return null;
        }
        boolean z = jqv.a;
        return b3;
    }
}
