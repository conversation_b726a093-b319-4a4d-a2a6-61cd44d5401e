package defpackage;

import java.util.Collection;
import java.util.Iterator;
import java.util.List;

/* renamed from: eqk  reason: default package */
/* compiled from: PG */
final class eqk extends jmi implements jna {
    final /* synthetic */ eql a;
    final /* synthetic */ eqp b;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eqk(eql eql, eqp eqp, jlr jlr) {
        super(1, jlr);
        this.a = eql;
        this.b = eqp;
    }

    public final /* bridge */ /* synthetic */ Object a(Object obj) {
        return new eqk(this.a, this.b, (jlr) obj).bk(jkd.a);
    }

    public final Object bk(Object obj) {
        jji.c(obj);
        eqp eqp = this.b;
        epq b2 = epq.b(eqp.d);
        if (b2 == null) {
            b2 = epq.REQUEST_UNKNOWN;
        }
        if (b2 != epq.REQUEST_OPEN_PENDING) {
            ((hby) eql.a.f().h(hdg.a, "ALT.MicStateReporter").j("com/google/android/libraries/search/audio/microphone/state/impl/MicStateReporterImpl", "applyToPartialUpcomingUpdates", 392, "MicStateReporterImpl.kt")).u("#audio# skip upcoming update(id(%s)), not supported", eqp.c);
        } else {
            eql eql = this.a;
            List list = eql.b;
            if (!(list instanceof Collection) || !list.isEmpty()) {
                Iterator it = list.iterator();
                while (true) {
                    if (it.hasNext()) {
                        if (jnu.i(((eqp) it.next()).c, eqp.c)) {
                            ((hby) eql.a.f().h(hdg.a, "ALT.MicStateReporter").j("com/google/android/libraries/search/audio/microphone/state/impl/MicStateReporterImpl", "applyToPartialUpcomingUpdates", 398, "MicStateReporterImpl.kt")).u("#audio# skip upcoming update(id(%s)), already exists", eqp.c);
                            break;
                        }
                    } else {
                        break;
                    }
                }
            }
            eql.b = jji.x(eql.b, eqp);
            eql eql2 = this.a;
            eqa b3 = eql2.b();
            htk l = eqb.b.l();
            jnu.d(l, "newBuilder(...)");
            eql2.c(b3, jnu.e(l, "builder").N());
        }
        return jkd.a;
    }
}
