package defpackage;

/* renamed from: eqh  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class eqh implements hcb {
    public final /* synthetic */ htq a;
    private final /* synthetic */ int b;

    public /* synthetic */ eqh(htq htq, int i) {
        this.b = i;
        this.a = htq;
    }

    public final Object a() {
        eah eah;
        eah eah2;
        eag eag;
        eak eak;
        eak eak2;
        eaj eaj;
        String str;
        eak eak3;
        eak eak4;
        eaj eaj2;
        int i = this.b;
        if (i == 0) {
            htq htq = this.a;
            jnu.e(htq, "$partialUpdate");
            StringBuilder sb = new StringBuilder("[");
            eqq eqq = (eqq) htq;
            ept b2 = ept.b(eqq.d);
            if (b2 == null) {
                b2 = ept.CLIENT_TYPE_UNKNOWN;
            }
            sb.append("type=".concat(String.valueOf(b2.name())));
            sb.append(",updateId=".concat(String.valueOf(eqq.e)));
            if ((eqq.a & 4) != 0) {
                int i2 = eqq.f;
                sb.append(",recordId=" + i2);
            }
            if (eqq.b == 2) {
                eak eak5 = (eak) eqq.c;
                if (eak5.a == 1) {
                    eal b3 = eal.b(((Integer) eak5.b).intValue());
                    if (b3 == null) {
                        b3 = eal.UNKNOWN_OPENING_SUCCESS;
                    }
                    sb.append(",open=".concat(String.valueOf(b3.name())));
                }
                int i3 = eqq.b;
                if (i3 == 2) {
                    eak = (eak) eqq.c;
                } else {
                    eak = eak.c;
                }
                if (eak.a == 2) {
                    if (i3 == 2) {
                        eak2 = (eak) eqq.c;
                    } else {
                        eak2 = eak.c;
                    }
                    if (eak2.a == 2) {
                        eaj = eaj.b(((Integer) eak2.b).intValue());
                        if (eaj == null) {
                            eaj = eaj.UNKNOWN_OPENING_FAILURE;
                        }
                    } else {
                        eaj = eaj.UNKNOWN_OPENING_FAILURE;
                    }
                    sb.append(",open=".concat(String.valueOf(eaj.name())));
                }
            }
            if (eqq.b == 3) {
                eah eah3 = (eah) eqq.c;
                if (eah3.a == 1) {
                    eai b4 = eai.b(((Integer) eah3.b).intValue());
                    if (b4 == null) {
                        b4 = eai.UNKNOWN_CLOSING_SUCCESS;
                    }
                    sb.append(",close=".concat(String.valueOf(b4.name())));
                }
                int i4 = eqq.b;
                if (i4 == 3) {
                    eah = (eah) eqq.c;
                } else {
                    eah = eah.c;
                }
                if (eah.a == 2) {
                    if (i4 == 3) {
                        eah2 = (eah) eqq.c;
                    } else {
                        eah2 = eah.c;
                    }
                    if (eah2.a == 2) {
                        eag = eag.b(((Integer) eah2.b).intValue());
                        if (eag == null) {
                            eag = eag.UNKNOWN_CLOSING_FAILURE;
                        }
                    } else {
                        eag = eag.UNKNOWN_CLOSING_FAILURE;
                    }
                    sb.append(",close=".concat(String.valueOf(eag.name())));
                }
            }
            sb.append("]");
            return sb.toString();
        } else if (i == 1) {
            htq htq2 = this.a;
            jnu.e(htq2, "$update");
            StringBuilder sb2 = new StringBuilder("[");
            int i5 = 0;
            for (epr epr : ((eqa) htq2).a) {
                int i6 = i5 + 1;
                if (i5 > 0) {
                    str = ",{";
                } else {
                    str = "{";
                }
                sb2.append(str);
                ept b5 = ept.b(epr.d);
                if (b5 == null) {
                    b5 = ept.CLIENT_TYPE_UNKNOWN;
                }
                sb2.append("type=".concat(String.valueOf(b5.name())));
                if ((epr.a & 2) != 0) {
                    int i7 = epr.e;
                    sb2.append(",recordId=" + i7);
                }
                if (epr.b == 2) {
                    eak eak6 = (eak) epr.c;
                    if (eak6.a == 1) {
                        eal b6 = eal.b(((Integer) eak6.b).intValue());
                        if (b6 == null) {
                            b6 = eal.UNKNOWN_OPENING_SUCCESS;
                        }
                        sb2.append(",open=".concat(String.valueOf(b6.name())));
                    }
                    int i8 = epr.b;
                    if (i8 == 2) {
                        eak3 = (eak) epr.c;
                    } else {
                        eak3 = eak.c;
                    }
                    if (eak3.a == 2) {
                        if (i8 == 2) {
                            eak4 = (eak) epr.c;
                        } else {
                            eak4 = eak.c;
                        }
                        if (eak4.a == 2) {
                            eaj2 = eaj.b(((Integer) eak4.b).intValue());
                            if (eaj2 == null) {
                                eaj2 = eaj.UNKNOWN_OPENING_FAILURE;
                            }
                        } else {
                            eaj2 = eaj.UNKNOWN_OPENING_FAILURE;
                        }
                        sb2.append(",open=".concat(String.valueOf(eaj2.name())));
                    }
                }
                if (epr.b == 3) {
                    epq b7 = epq.b(((Integer) epr.c).intValue());
                    if (b7 == null) {
                        b7 = epq.REQUEST_UNKNOWN;
                    }
                    sb2.append(",request=".concat(String.valueOf(b7.name())));
                }
                sb2.append("}");
                i5 = i6;
            }
            sb2.append("]");
            return sb2.toString();
        } else if (i == 2) {
            htq htq3 = this.a;
            jnu.e(htq3, "$partialRequestUpdate");
            eqp eqp = (eqp) htq3;
            ept b8 = ept.b(eqp.b);
            if (b8 == null) {
                b8 = ept.CLIENT_TYPE_UNKNOWN;
            }
            String name = b8.name();
            String str2 = eqp.c;
            epq b9 = epq.b(eqp.d);
            if (b9 == null) {
                b9 = epq.REQUEST_UNKNOWN;
            }
            String name2 = b9.name();
            return "[type=" + name + ",updateId=" + str2 + ",request=" + name2 + "]";
        } else if (i == 3) {
            return esx.q((eao) this.a);
        } else {
            if (i != 4) {
                return esx.q((eao) this.a);
            }
            return esx.q((eao) this.a);
        }
    }
}
