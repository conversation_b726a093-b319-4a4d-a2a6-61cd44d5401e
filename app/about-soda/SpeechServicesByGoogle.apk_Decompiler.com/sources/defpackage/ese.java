package defpackage;

/* renamed from: ese  reason: default package */
/* compiled from: PG */
public final class ese implements erq {
    private static final hca j = hca.m("com/google/android/libraries/search/audio/routing/impl/RefAudioRoute");
    public final ekt a;
    public final ekt b;
    public final erq c;
    public final ejn d;
    public final boolean e;
    public final hme f;
    public final jqh g;
    public final eks h;
    public final eoz i;
    private final jqs k;

    public ese(erq erq, jqs jqs, eks eks) {
        this.k = jqs;
        this.h = eks;
        this.i = don.k(jqs);
        jqh jqh = new jqh();
        this.g = jqh;
        ekt c2 = eks.c("ref-route@");
        this.a = c2;
        ekt d2 = erq.d();
        this.b = d2 == null ? c2 : d2;
        erq e2 = erq.e();
        this.c = e2;
        this.d = new ejn(jqw.w(jqh), e2.a().b, e2.a().c, grh.g(c2));
        this.e = jnu.i(erq, e2);
        this.f = e2.a().a;
        job.S(jqs, (jlv) null, (jqt) null, new edc(this, (jlr) null, 18, (byte[]) null), 3);
    }

    public final ejn a() {
        return this.d;
    }

    public final hme b() {
        return this.i.o(new esd(this, (jlr) null));
    }

    public final hme c(dzx dzx) {
        jnu.e(dzx, "disconnectReason");
        return this.i.o(new egv(this, dzx, (jlr) null, 5));
    }

    public final ekt d() {
        return this.b;
    }

    public final erq e() {
        return this.c;
    }

    /* JADX WARNING: Removed duplicated region for block: B:12:0x0033  */
    /* JADX WARNING: Removed duplicated region for block: B:17:0x0054  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object f(defpackage.jlr r6) {
        /*
            r5 = this;
            boolean r0 = r6 instanceof defpackage.esc
            if (r0 == 0) goto L_0x0013
            r0 = r6
            esc r0 = (defpackage.esc) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            esc r0 = new esc
            r0.<init>(r5, r6)
        L_0x0018:
            java.lang.Object r6 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            r3 = 1
            if (r2 == 0) goto L_0x0033
            if (r2 != r3) goto L_0x002b
            jqh r1 = r0.e
            ese r0 = r0.d
            defpackage.jji.c(r6)
            goto L_0x0049
        L_0x002b:
            java.lang.IllegalStateException r6 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r6.<init>(r0)
            throw r6
        L_0x0033:
            defpackage.jji.c(r6)
            jqh r6 = r5.g
            hme r2 = r5.f
            r0.d = r5
            r0.e = r6
            r0.c = r3
            java.lang.Object r0 = defpackage.jqw.x(r2, r0)
            if (r0 == r1) goto L_0x0085
            r1 = r6
            r6 = r0
            r0 = r5
        L_0x0049:
            java.lang.String r2 = "await(...)"
            defpackage.jnu.d(r6, r2)
            boolean r6 = r1.O(r6)
            if (r6 == 0) goto L_0x0082
            hca r6 = j
            hco r6 = r6.f()
            hcr r1 = defpackage.hdg.a
            java.lang.String r2 = "ALT.RefRoute"
            hco r6 = r6.h(r1, r2)
            java.lang.String r1 = "completeDisconnectionAfterOrigin"
            r2 = 142(0x8e, float:1.99E-43)
            java.lang.String r3 = "com/google/android/libraries/search/audio/routing/impl/RefAudioRoute"
            java.lang.String r4 = "RefAudioRoute.kt"
            hco r6 = r6.j(r3, r1, r2, r4)
            hby r6 = (defpackage.hby) r6
            ekt r1 = r0.a
            java.lang.String r1 = r1.b
            java.lang.String r2 = "#audio# route(%s) origin disconnected"
            r6.u(r2, r1)
            eks r6 = r0.h
            ekt r1 = r0.b
            ekt r0 = r0.a
            r6.b(r1, r0)
        L_0x0082:
            jkd r6 = defpackage.jkd.a
            return r6
        L_0x0085:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.ese.f(jlr):java.lang.Object");
    }
}
