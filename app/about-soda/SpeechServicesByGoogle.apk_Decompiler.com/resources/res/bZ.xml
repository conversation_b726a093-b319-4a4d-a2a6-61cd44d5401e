<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt">
    <com.google.android.material.appbar.AppBarLayout android:theme="@style/Theme.CollapsingToolbar.Settings" android:id="@+id/app_bar" android:background="@android:color/transparent" android:fitsSystemWindows="true" android:layout_width="match_parent" android:layout_height="wrap_content" android:outlineSpotShadowColor="@android:color/transparent" android:outlineAmbientShadowColor="@android:color/transparent">
        <com.google.android.material.appbar.CollapsingToolbarLayout android:id="@+id/collapsing_toolbar" android:layout_width="match_parent" android:layout_height="@dimen/settingslib_toolbar_layout_height" app:layout_scrollFlags="13" app:toolbarId="@+id/action_bar" style="@style/CollapsingToolbarLayoutStyle.SettingsLib">
            <Toolbar android:theme="?android:attr/actionBarTheme" android:id="@+id/action_bar" android:layout_width="match_parent" android:layout_height="?attr/actionBarSize" android:transitionName="shared_element_view" app:layout_collapseMode="1"/>
        </com.google.android.material.appbar.CollapsingToolbarLayout>
    </com.google.android.material.appbar.AppBarLayout>
    <FrameLayout android:id="@+id/content_frame" android:layout_width="match_parent" android:layout_height="wrap_content" app:layout_behavior="@string/appbar_scrolling_view_behavior"/>
</merge>
