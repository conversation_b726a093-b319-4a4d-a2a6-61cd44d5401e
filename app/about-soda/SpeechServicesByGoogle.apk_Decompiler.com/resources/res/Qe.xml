<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:layout_width="match_parent" android:layout_height="match_parent">
    <FrameLayout android:id="@android:id/action_menu_presenter" android:layout_width="match_parent" android:layout_height="match_parent">
        <com.android.car.ui.FocusArea android:id="@+id/car_ui_focus_area" android:layout_width="match_parent" android:layout_height="match_parent">
            <com.android.car.ui.recyclerview.CarUiRecyclerView android:id="@+id/recycler_view" android:tag="carUiPreferenceRecyclerView" android:background="@drawable/car_ui_activity_background" android:layout_width="match_parent" android:layout_height="match_parent" app:enableDivider="true"/>
        </com.android.car.ui.FocusArea>
    </FrameLayout>
</FrameLayout>
