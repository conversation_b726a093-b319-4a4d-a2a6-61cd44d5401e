package defpackage;

/* renamed from: crl  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class crl implements cko {
    public final /* synthetic */ crm a;

    public /* synthetic */ crl(crm crm) {
        this.a = crm;
    }

    public final void a(cks cks) {
        boolean z = ((ckw) cks).c;
        crm crm = this.a;
        if (z) {
            crm.cancel(false);
        } else if (cks.c()) {
            crm.m(cks.b());
        } else {
            Exception a2 = cks.a();
            if (a2 != null) {
                crm.n(a2);
                return;
            }
            throw new IllegalStateException();
        }
    }
}
