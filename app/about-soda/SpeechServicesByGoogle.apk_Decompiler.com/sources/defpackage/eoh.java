package defpackage;

/* renamed from: eoh  reason: default package */
/* compiled from: PG */
public final class eoh extends jnv implements jna {
    final /* synthetic */ int a;
    private final /* synthetic */ int b;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eoh(int i, int i2) {
        super(1);
        this.b = i2;
        this.a = i;
    }

    public final /* synthetic */ Object a(Object obj) {
        boolean z = true;
        switch (this.b) {
            case 0:
                eod eod = (eod) obj;
                jnu.e(eod, "it");
                if (eod.d == this.a) {
                    z = false;
                }
                return Boolean.valueOf(z);
            case 1:
                ejg ejg = (ejg) obj;
                jnu.e(ejg, "it");
                if (ejg.b == this.a) {
                    z = false;
                }
                return Boolean.valueOf(z);
            case 2:
                eoo eoo = (eoo) obj;
                jnu.e(eoo, "it");
                if (eoo.c == this.a) {
                    z = false;
                }
                return Boolean.valueOf(z);
            case 3:
                epc epc = (epc) obj;
                jnu.e(epc, "it");
                if (epc.c.a == this.a) {
                    z = false;
                }
                return Boolean.valueOf(z);
            case 4:
                epi epi = (epi) obj;
                jnu.e(epi, "it");
                if (epi.c.a == this.a) {
                    z = false;
                }
                return Boolean.valueOf(z);
            case 5:
                epi epi2 = (epi) obj;
                jnu.e(epi2, "it");
                if (epi2.c.a == this.a) {
                    z = false;
                }
                return Boolean.valueOf(z);
            case 6:
                esi esi = (esi) obj;
                jnu.e(esi, "it");
                if (esi.b == this.a) {
                    z = false;
                }
                return Boolean.valueOf(z);
            default:
                esp esp = (esp) obj;
                jnu.e(esp, "it");
                if (esp.b == this.a) {
                    z = false;
                }
                return Boolean.valueOf(z);
        }
    }
}
