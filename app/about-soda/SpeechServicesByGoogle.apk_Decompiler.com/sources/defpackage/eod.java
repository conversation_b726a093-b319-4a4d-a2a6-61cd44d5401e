package defpackage;

/* renamed from: eod  reason: default package */
/* compiled from: PG */
public final class eod implements eoc {
    public final dyy a;
    public final eoa b;
    public final eow c;
    public final int d;
    public final jrz e;
    private final dze f;
    private final boolean g;

    public eod(dyy dyy, eoa eoa, eow eow, int i, dze dze, boolean z, jrz jrz) {
        jnu.e(dyy, "session");
        jnu.e(eoa, "client");
        jnu.e(eow, "route");
        jnu.e(dze, "params");
        this.a = dyy;
        this.b = eoa;
        this.c = eow;
        this.d = i;
        this.f = dze;
        this.g = z;
        this.e = jrz;
    }

    public final int a() {
        return this.d;
    }

    public final dyy b() {
        return this.a;
    }

    public final dze c() {
        return this.f;
    }

    public final eoa d() {
        return this.b;
    }

    public final eow e() {
        return this.c;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof eod)) {
            return false;
        }
        eod eod = (eod) obj;
        if (jnu.i(this.a, eod.a) && jnu.i(this.b, eod.b) && jnu.i(this.c, eod.c) && this.d == eod.d && jnu.i(this.f, eod.f) && this.g == eod.g && jnu.i(this.e, eod.e)) {
            return true;
        }
        return false;
    }

    public final /* synthetic */ hme f() {
        return cqx.U(this);
    }

    public final /* synthetic */ Object g(jlr jlr) {
        return cqx.V(this, jlr);
    }

    public final int hashCode() {
        int i;
        int hashCode = (((this.a.hashCode() * 31) + this.b.hashCode()) * 31) + this.c.hashCode();
        dze dze = this.f;
        if (dze.B()) {
            i = dze.i();
        } else {
            int i2 = dze.memoizedHashCode;
            if (i2 == 0) {
                i2 = dze.i();
                dze.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (((((((hashCode * 31) + this.d) * 31) + i) * 31) + a.f(this.g)) * 31) + this.e.hashCode();
    }

    public final String toString() {
        return "AudioSessionDataInternal(session=" + this.a + ", client=" + this.b + ", route=" + this.c + ", sessionToken=" + this.d + ", params=" + this.f + ", isInactive=" + this.g + ", routeDisconnectJob=" + this.e + ")";
    }
}
