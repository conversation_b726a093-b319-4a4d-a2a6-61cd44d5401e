package defpackage;

/* renamed from: bcd  reason: default package */
/* compiled from: PG */
public final class bcd extends asy {
    public final void b(awl awl) {
        awl.d();
        try {
            awl.g("DELETE FROM workspec WHERE state IN (2, 3, 5) AND (last_enqueue_time + minimum_retention_duration) < " + (System.currentTimeMillis() - bda.a) + " AND (SELECT COUNT(*)=0 FROM dependency WHERE     prerequisite_id=id AND     work_spec_id NOT IN         (SELECT id FROM workspec WHERE state IN (2, 3, 5)))");
            awl.h();
        } finally {
            awl.f();
        }
    }
}
