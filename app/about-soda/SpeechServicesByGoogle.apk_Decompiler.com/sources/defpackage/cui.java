package defpackage;

import java.util.concurrent.Executor;

/* renamed from: cui  reason: default package */
/* compiled from: PG */
public final class cui {
    public byte a;
    public Object b;
    public Object c;
    public Object d;

    public cui() {
        throw null;
    }

    public final cuj a() {
        Object obj;
        if (this.a != 1 || (obj = this.b) == null) {
            StringBuilder sb = new StringBuilder();
            if (this.b == null) {
                sb.append(" groupName");
            }
            if (this.a == 0) {
                sb.append(" pendingOnly");
            }
            throw new IllegalStateException("Missing required properties:".concat(sb.toString()));
        }
        return new cuj((String) obj, (grh) this.c, (grh) this.d);
    }

    public final void b(String str) {
        if (str != null) {
            this.b = str;
            return;
        }
        throw new NullPointerException("Null groupName");
    }

    public final void c(Executor executor) {
        if (executor != null) {
            this.d = executor;
            return;
        }
        throw new NullPointerException("Null callbackExecutor");
    }

    public cui(byte[] bArr) {
        gqd gqd = gqd.a;
        this.c = gqd;
        this.d = gqd;
    }

    public cui(char[] cArr) {
    }
}
