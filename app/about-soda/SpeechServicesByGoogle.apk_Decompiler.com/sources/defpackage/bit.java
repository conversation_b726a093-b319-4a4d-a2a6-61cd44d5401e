package defpackage;

import android.os.Handler;
import java.util.concurrent.Executor;

/* renamed from: bit  reason: default package */
/* compiled from: PG */
public final class bit implements Executor {
    final /* synthetic */ cyw a;

    public bit(cyw cyw) {
        this.a = cyw;
    }

    public final void execute(Runnable runnable) {
        ((<PERSON><PERSON>) this.a.b).post(runnable);
    }
}
