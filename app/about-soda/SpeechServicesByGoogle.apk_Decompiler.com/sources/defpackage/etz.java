package defpackage;

/* renamed from: etz  reason: default package */
/* compiled from: PG */
final class etz extends jmi implements jne {
    Object a;
    int b;
    final /* synthetic */ eua c;
    private /* synthetic */ Object d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public etz(eua eua, jlr jlr) {
        super(2, jlr);
        this.c = eua;
    }

    public final /* bridge */ /* synthetic */ Object b(Object obj, Object obj2) {
        return ((etz) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v3, resolved type: ebg} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v6, resolved type: ebg} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v11, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v13, resolved type: ebg} */
    /* JADX WARNING: type inference failed for: r1v9, types: [jlr, jlv, java.lang.Object, jqt] */
    /* JADX WARNING: type inference failed for: r1v12 */
    /* JADX WARNING: type inference failed for: r1v13 */
    /* JADX WARNING: type inference failed for: r1v14 */
    /* JADX WARNING: Code restructure failed: missing block: B:13:0x0098, code lost:
        if (r8 != r1) goto L_0x009a;
     */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:21:0x00b6  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r22) {
        /*
            r21 = this;
            r0 = r21
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.b
            java.lang.String r3 = "invokeSuspend"
            java.lang.String r4 = "com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender$startInternal$1"
            java.lang.String r5 = "StreamListeningSessionResponseSender.kt"
            java.lang.String r6 = "ALT.GrpcARCRespSender"
            r7 = 2
            r8 = 1
            java.lang.String r9 = "newBuilder(...)"
            if (r2 == 0) goto L_0x0036
            if (r2 == r8) goto L_0x002c
            if (r2 == r7) goto L_0x001e
            defpackage.jji.c(r22)
            r2 = r3
            goto L_0x026f
        L_0x001e:
            java.lang.Object r2 = r0.a
            java.lang.Object r7 = r0.d
            jqs r7 = (defpackage.jqs) r7
            defpackage.jji.c(r22)
            r8 = r7
            r7 = r22
            goto L_0x00b1
        L_0x002c:
            java.lang.Object r2 = r0.d
            jqs r2 = (defpackage.jqs) r2
            defpackage.jji.c(r22)
            r8 = r22
            goto L_0x009a
        L_0x0036:
            defpackage.jji.c(r22)
            java.lang.Object r2 = r0.d
            jqs r2 = (defpackage.jqs) r2
            eua r10 = r0.c
            etf r11 = defpackage.etf.c
            htk r11 = r11.l()
            defpackage.jnu.d(r11, r9)
            dlv r11 = defpackage.jnu.e(r11, "builder")
            htc r12 = defpackage.htc.a
            htk r12 = r12.l()
            defpackage.jnu.d(r12, r9)
            cxi r12 = defpackage.jnu.e(r12, "builder")
            htc r12 = r12.r()
            java.lang.String r13 = "value"
            defpackage.jnu.e(r12, r13)
            java.lang.Object r13 = r11.a
            htk r13 = (defpackage.htk) r13
            htq r14 = r13.b
            boolean r14 = r14.B()
            if (r14 != 0) goto L_0x0071
            r13.u()
        L_0x0071:
            htq r13 = r13.b
            etf r13 = (defpackage.etf) r13
            r12.getClass()
            r13.b = r12
            r12 = 9
            r13.a = r12
            etf r11 = r11.n()
            java.lang.Object r10 = r10.f(r11)
            java.lang.Throwable r10 = defpackage.jju.a(r10)
            if (r10 == 0) goto L_0x008e
            goto L_0x0290
        L_0x008e:
            eua r10 = r0.c
            r0.d = r2
            r0.b = r8
            java.lang.Object r8 = r10.b(r0)
            if (r8 == r1) goto L_0x0293
        L_0x009a:
            ebg r8 = (defpackage.ebg) r8
            if (r8 == 0) goto L_0x0290
            eua r10 = r0.c
            r0.d = r2
            r0.a = r8
            r0.b = r7
            java.lang.Object r7 = r10.a(r0)
            if (r7 == r1) goto L_0x0293
            r20 = r8
            r8 = r2
            r2 = r20
        L_0x00b1:
            r13 = r7
            dyt r13 = (defpackage.dyt) r13
            if (r13 == 0) goto L_0x0290
            eua r7 = r0.c
            etf r10 = defpackage.etf.c
            htk r10 = r10.l()
            defpackage.jnu.d(r10, r9)
            dlv r10 = defpackage.jnu.e(r10, "builder")
            etc r11 = defpackage.etc.f
            htk r11 = r11.l()
            defpackage.jnu.d(r11, r9)
            bzl r11 = defpackage.jnu.e(r11, "builder")
            java.lang.Object r12 = r2.f()
            jna r14 = r7.b
            java.lang.Object r12 = r14.a(r12)
            dzh r12 = (defpackage.dzh) r12
            r11.L(r12)
            java.lang.Object r12 = r11.a
            htk r12 = (defpackage.htk) r12
            htq r14 = r12.b
            boolean r14 = r14.B()
            if (r14 != 0) goto L_0x00f0
            r12.u()
        L_0x00f0:
            htq r12 = r12.b
            etc r12 = (defpackage.etc) r12
            r12.d = r13
            int r14 = r12.a
            r14 = r14 | 4
            r12.a = r14
            ebw r12 = defpackage.ebw.c
            htk r12 = r12.l()
            defpackage.jnu.d(r12, r9)
            bzl r12 = defpackage.jnu.e(r12, "builder")
            grh r14 = r7.c
            grm r14 = (defpackage.grm) r14
            java.lang.Object r14 = r14.a
            java.lang.Number r14 = (java.lang.Number) r14
            int r14 = r14.intValue()
            r12.y(r14)
            ebw r12 = r12.x()
            r11.K(r12)
            grh r12 = r2.d()
            boolean r12 = r12.f()
            java.lang.Object r14 = r11.a
            htk r14 = (defpackage.htk) r14
            htq r15 = r14.b
            boolean r15 = r15.B()
            if (r15 != 0) goto L_0x0136
            r14.u()
        L_0x0136:
            htq r14 = r14.b
            etc r14 = (defpackage.etc) r14
            int r15 = r14.a
            r15 = r15 | 8
            r14.a = r15
            r14.e = r12
            etc r11 = r11.J()
            r10.r(r11)
            etf r10 = r10.n()
            java.lang.Object r7 = r7.f(r10)
            java.lang.Throwable r7 = defpackage.jju.a(r7)
            if (r7 != 0) goto L_0x0290
            java.util.ArrayList r7 = new java.util.ArrayList
            r7.<init>()
            grh r10 = r2.a()
            boolean r10 = r10.f()
            r15 = 3
            r14 = 0
            if (r10 == 0) goto L_0x018b
            eua r11 = r0.c
            eux r12 = new eux
            r16 = 0
            r17 = 1
            r10 = r12
            r18 = r12
            r12 = r2
            r19 = r1
            r1 = r14
            r14 = r16
            r16 = r9
            r9 = r15
            r15 = r17
            r10.<init>((defpackage.eua) r11, (defpackage.ebg) r12, (defpackage.dyt) r13, (defpackage.jlr) r14, (int) r15)
            r10 = r18
            jrz r10 = defpackage.job.S(r8, r1, r1, r10, r9)
            r7.add(r10)
            goto L_0x01aa
        L_0x018b:
            r19 = r1
            r16 = r9
            r1 = r14
            r9 = r15
            grh r10 = r2.b()
            boolean r10 = r10.f()
            if (r10 == 0) goto L_0x01aa
            eua r10 = r0.c
            egv r11 = new egv
            r12 = 6
            r11.<init>((defpackage.eua) r10, (defpackage.ebg) r2, (defpackage.jlr) r1, (int) r12)
            jrz r10 = defpackage.job.S(r8, r1, r1, r11, r9)
            r7.add(r10)
        L_0x01aa:
            grh r10 = r2.d()
            boolean r10 = r10.f()
            if (r10 == 0) goto L_0x01ce
            eua r11 = r0.c
            egv r15 = new egv
            r14 = 7
            r17 = 0
            r13 = 0
            r10 = r15
            r12 = r2
            r18 = r3
            r3 = r15
            r15 = r17
            r10.<init>((defpackage.eua) r11, (defpackage.ebg) r12, (defpackage.jlr) r13, (int) r14, (byte[]) r15)
            jrz r3 = defpackage.job.S(r8, r1, r1, r3, r9)
            r7.add(r3)
            goto L_0x01d0
        L_0x01ce:
            r18 = r3
        L_0x01d0:
            grh r3 = r2.c()
            boolean r3 = r3.f()
            if (r3 == 0) goto L_0x01f1
            eua r11 = r0.c
            egv r3 = new egv
            r14 = 8
            r15 = 0
            r13 = 0
            r10 = r3
            r12 = r2
            r10.<init>((defpackage.eua) r11, (defpackage.ebg) r12, (defpackage.jlr) r13, (int) r14, (char[]) r15)
            jrz r3 = defpackage.job.S(r8, r1, r1, r3, r9)
            r7.add(r3)
            r15 = r18
            goto L_0x0238
        L_0x01f1:
            hca r3 = defpackage.eua.a
            hco r3 = r3.f()
            hcr r10 = defpackage.hdg.a
            hco r3 = r3.h(r10, r6)
            r10 = 140(0x8c, float:1.96E-43)
            r15 = r18
            hco r3 = r3.j(r4, r15, r10, r5)
            hby r3 = (defpackage.hby) r3
            java.lang.String r10 = "#audio# No first byte read result, send empty"
            r3.r(r10)
            eua r3 = r0.c
            etf r10 = defpackage.etf.c
            htk r10 = r10.l()
            r11 = r16
            defpackage.jnu.d(r10, r11)
            dlv r10 = defpackage.jnu.e(r10, "builder")
            dyv r12 = defpackage.dyv.c
            htk r12 = r12.l()
            defpackage.jnu.d(r12, r11)
            bzj r11 = defpackage.jnu.e(r12, "builder")
            dyv r11 = r11.A()
            r10.q(r11)
            etf r10 = r10.n()
            r3.f(r10)
        L_0x0238:
            eua r11 = r0.c
            egv r3 = new egv
            r14 = 9
            r16 = 0
            r13 = 0
            r10 = r3
            r12 = r2
            r2 = r15
            r15 = r16
            r10.<init>((defpackage.eua) r11, (defpackage.ebg) r12, (defpackage.jlr) r13, (int) r14, (short[]) r15)
            jrz r3 = defpackage.job.S(r8, r1, r1, r3, r9)
            r7.add(r3)
            eua r3 = r0.c
            edc r10 = new edc
            r11 = 20
            r10.<init>((defpackage.eua) r3, (defpackage.jlr) r1, (int) r11)
            jrz r3 = defpackage.job.S(r8, r1, r1, r10, r9)
            r7.add(r3)
            r0.d = r1
            r0.a = r1
            r0.b = r9
            java.lang.Object r1 = defpackage.job.T(r7, r0)
            r3 = r19
            if (r1 != r3) goto L_0x026f
            goto L_0x0294
        L_0x026f:
            hca r1 = defpackage.eua.a
            hco r1 = r1.f()
            hcr r3 = defpackage.hdg.a
            hco r1 = r1.h(r3, r6)
            r3 = 149(0x95, float:2.09E-43)
            hco r1 = r1.j(r4, r2, r3, r5)
            hby r1 = (defpackage.hby) r1
            java.lang.String r2 = "#audio# Completing sending data to the remote client"
            r1.r(r2)
            eua r1 = r0.c
            r1.h()
            jkd r1 = defpackage.jkd.a
            return r1
        L_0x0290:
            jkd r1 = defpackage.jkd.a
            return r1
        L_0x0293:
            r3 = r1
        L_0x0294:
            return r3
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.etz.bk(java.lang.Object):java.lang.Object");
    }

    public final jlr c(Object obj, jlr jlr) {
        etz etz = new etz(this.c, jlr);
        etz.d = obj;
        return etz;
    }
}
