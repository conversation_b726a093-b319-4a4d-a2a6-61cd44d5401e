package defpackage;

/* renamed from: bcl  reason: default package */
/* compiled from: PG */
public final class bcl extends avu {
    public static final bcl c = new bcl();

    private bcl() {
        super(4, 5);
    }

    public final void a(awl awl) {
        awl.g("ALTER TABLE workspec ADD COLUMN `trigger_content_update_delay` INTEGER NOT NULL DEFAULT -1");
        awl.g("ALTER TABLE workspec ADD COLUMN `trigger_max_content_delay` INTEGER NOT NULL DEFAULT -1");
    }
}
