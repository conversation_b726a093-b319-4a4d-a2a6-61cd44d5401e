package defpackage;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.widget.ImageView;
import java.util.function.Consumer;

/* renamed from: bmf  reason: default package */
/* compiled from: PG */
public final class bmf extends ImageView {
    public Consumer a;

    public bmf(Context context) {
        super(context);
    }

    public final void setImageDrawable(Drawable drawable) {
        super.setImageDrawable(drawable);
        Consumer consumer = this.a;
        if (consumer != null) {
            consumer.accept(drawable);
        }
    }
}
