package defpackage;

/* renamed from: cuv  reason: default package */
/* compiled from: PG */
public final class cuv {
    public final String a;

    public cuv() {
        throw null;
    }

    public static cuv a(String str) {
        int i = hgk.a;
        hgi a2 = hgj.a.a();
        a2.e(str);
        return new cuv(a2.n().toString());
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (!(obj instanceof cuv) || !this.a.equals(((cuv) obj).a)) {
            return false;
        }
        return true;
    }

    public final int hashCode() {
        a.I(1);
        return this.a.hashCode() ^ -722379962;
    }

    public final String toString() {
        return this.a;
    }

    public cuv(String str) {
        this.a = str;
    }
}
