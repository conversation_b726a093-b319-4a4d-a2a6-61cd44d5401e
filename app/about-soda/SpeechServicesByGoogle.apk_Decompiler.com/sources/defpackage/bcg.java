package defpackage;

/* renamed from: bcg  reason: default package */
/* compiled from: PG */
public final class bcg extends avu {
    public static final bcg c = new bcg();

    private bcg() {
        super(12, 13);
    }

    public final void a(awl awl) {
        awl.g("UPDATE workspec SET required_network_type = 0 WHERE required_network_type IS NULL ");
        awl.g("UPDATE workspec SET content_uri_triggers = x'' WHERE content_uri_triggers is NULL");
    }
}
