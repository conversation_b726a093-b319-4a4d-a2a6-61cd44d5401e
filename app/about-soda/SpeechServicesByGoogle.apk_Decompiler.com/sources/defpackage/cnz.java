package defpackage;

import android.os.StrictMode;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadFactory;

/* renamed from: cnz  reason: default package */
/* compiled from: PG */
public final class cnz implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;
    private final jjk e;
    private final jjk f;
    private final jjk g;
    private final jjk h;
    private final /* synthetic */ int i;

    public cnz(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, int i2) {
        this.i = i2;
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
        this.e = jjk5;
        this.f = jjk6;
        this.g = jjk7;
        this.h = jjk8;
    }

    public final /* synthetic */ Object b() {
        switch (this.i) {
            case 0:
                int intValue = ((gbg) this.c).a().intValue();
                cpk a2 = ((cps) this.d).b();
                cpp a3 = ((cpq) this.h).b();
                cqn cqn = new cqn("BG", intValue, ((Boolean) ((grh) ((iiv) this.f).a).d(false)).booleanValue());
                cqp J2 = cqx.J((bzl) this.g.b(), cqn);
                String str = cqn.a;
                StrictMode.ThreadPolicy.Builder penaltyLog = new StrictMode.ThreadPolicy.Builder().detectNetwork().penaltyLog();
                penaltyLog.detectResourceMismatches();
                StrictMode.ThreadPolicy.Builder unused = penaltyLog.detectUnbufferedIo();
                return new coi(hfc.E(coo.a((grh) ((iiv) this.e).a, cqx.C(cqn, a2, cqx.D(str, new cnt(new cnu((ThreadFactory) this.a.b(), 2), penaltyLog.build())), J2, a3))), (hmi) this.b.b());
            case 1:
                ((gbu) this.c).b();
                return new byr((hmh) this.h.b(), (exq) this.b.b(), (exx) this.a.b(), (exp) this.f.b(), (bmu) this.d.b(), ((fcv) this.g).b(), (dwj) this.e.b());
            case 2:
                jjk jjk = this.h;
                jjk jjk2 = this.a;
                jjk jjk3 = this.f;
                dni a4 = ((dnj) this.b).b();
                jjk jjk4 = this.c;
                return new dnq(((dme) this.d).b(), ((iim) jjk2).a(), (Executor) jjk.b(), a4, this.g, iit.c(jjk3), this.e, jjk4);
            case 3:
                jjk jjk5 = this.a;
                jjk jjk6 = this.e;
                jjk jjk7 = this.d;
                jjk jjk8 = this.g;
                jjk jjk9 = this.f;
                return new dqg((dkq) this.b.b(), this.h, this.c, jjk9, jjk8, jjk7, jjk6, iit.c(jjk5));
            case 4:
                jjk jjk10 = this.c;
                jjk jjk11 = this.f;
                jjk jjk12 = this.a;
                jjk jjk13 = this.e;
                jjk jjk14 = this.b;
                jjk jjk15 = this.h;
                return new eix(this.d, this.g, jjk15, jjk14, jjk13, jjk12, jjk11, jjk10, (byte[]) null);
            case 5:
                ((gbb) this.e).b();
                jjk jjk16 = this.b;
                ((eqo) this.d).b();
                return new eql((dku) this.c.b(), (eoz) this.g.b(), ((iim) this.f).a(), (Executor) jjk16.b(), (jqs) this.a.b(), (jlv) this.h.b());
            case 6:
                bzj bzj = (bzj) ((iiv) this.a).a;
                gdh gdh = (gdh) ((iiv) this.f).a;
                gmg gmg = (gmg) this.d.b();
                hmh hmh = (hmh) this.e.b();
                gnk gnk = (gnk) this.g.b();
                Map map = ((iir) this.c).a;
                grh grh = (grh) ((iiv) this.h).a;
                bzj a5 = ((gdm) this.b).b();
                jnu.e(bzj, "factory");
                jnu.e(gdh, "deviceCommitterFactory");
                jnu.e(gmg, "pathFactory");
                jnu.e(hmh, "blockingExecutor");
                jnu.e(gnk, "updater");
                jnu.e(map, "defaultExperimentFlagValues");
                jnu.e(grh, "flagRecorder");
                gcp gcp = gcp.DEVICE;
                exv exv = new exv(a5, gmg, 3);
                return bzj.U(gcp, exv, hmh, map, gdk.a, new gbj((Object) gdh, 4, (float[]) null), new fpn(gnk, 15), auj.f, (gdo) grh.e());
            default:
                gdh gdh2 = (gdh) ((iiv) this.c).a;
                bzj bzj2 = (bzj) ((iiv) this.b).a;
                gmg gmg2 = (gmg) this.h.b();
                Map map2 = (Map) ((iiv) this.e).a;
                Executor executor = (Executor) this.d.b();
                gnk gnk2 = (gnk) this.g.b();
                bzj a6 = ((gdm) this.f).b();
                grh grh2 = (grh) ((iiv) this.a).a;
                jnu.e(gdh2, "deviceCommitterFactory");
                jnu.e(bzj2, "factory");
                jnu.e(gmg2, "pathFactory");
                jnu.e(map2, "defaultFlagValues");
                jnu.e(executor, "uiExecutor");
                jnu.e(gnk2, "updater");
                jnu.e(grh2, "flagRecorder");
                gcp gcp2 = gcp.UI_DEVICE;
                exv exv2 = new exv(a6, gmg2, 5);
                gew gew = gew.a;
                gbj gbj = new gbj((Object) gdh2, 6, (float[]) null);
                fpn fpn = new fpn(gnk2, 18);
                return bzj2.U(gcp2, exv2, executor, map2, gew, gbj, fpn, auj.g, (gdo) grh2.e());
        }
    }

    public cnz(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, int i2, byte[] bArr) {
        this.i = i2;
        this.c = jjk;
        this.h = jjk2;
        this.b = jjk3;
        this.a = jjk4;
        this.f = jjk5;
        this.d = jjk6;
        this.g = jjk7;
        this.e = jjk8;
    }

    public cnz(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, int i2, char[] cArr) {
        this.i = i2;
        this.d = jjk;
        this.a = jjk2;
        this.h = jjk3;
        this.b = jjk4;
        this.g = jjk5;
        this.f = jjk6;
        this.e = jjk7;
        this.c = jjk8;
    }

    public cnz(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, int i2, float[] fArr) {
        this.i = i2;
        this.a = jjk;
        this.f = jjk2;
        this.d = jjk3;
        this.e = jjk4;
        this.g = jjk5;
        this.c = jjk6;
        this.h = jjk7;
        this.b = jjk8;
    }

    public cnz(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, int i2, int[] iArr) {
        this.i = i2;
        this.d = jjk;
        this.g = jjk2;
        this.h = jjk3;
        this.b = jjk4;
        this.e = jjk5;
        this.a = jjk6;
        this.f = jjk7;
        this.c = jjk8;
    }

    public cnz(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, int i2, short[] sArr) {
        this.i = i2;
        this.b = jjk;
        this.h = jjk2;
        this.c = jjk3;
        this.f = jjk4;
        this.g = jjk5;
        this.d = jjk6;
        this.e = jjk7;
        this.a = jjk8;
    }

    public cnz(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, int i2, boolean[] zArr) {
        this.i = i2;
        this.e = jjk;
        this.c = jjk2;
        this.g = jjk3;
        this.f = jjk4;
        this.b = jjk5;
        this.a = jjk6;
        this.h = jjk7;
        this.d = jjk8;
    }

    public cnz(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, int i2, byte[][] bArr) {
        this.i = i2;
        this.c = jjk;
        this.b = jjk2;
        this.h = jjk3;
        this.e = jjk4;
        this.d = jjk5;
        this.g = jjk6;
        this.f = jjk7;
        this.a = jjk8;
    }
}
