package defpackage;

/* renamed from: eqp  reason: default package */
/* compiled from: PG */
public final class eqp extends htq implements hvb {
    public static final eqp e;
    private static volatile hvh f;
    public int a;
    public int b;
    public String c = "";
    public int d;

    static {
        eqp eqp = new eqp();
        e = eqp;
        htq.z(eqp.class, eqp);
    }

    private eqp() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(e, "\u0004\u0003\u0000\u0001\u0001\u0003\u0003\u0000\u0000\u0000\u0001᠌\u0000\u0002ဈ\u0001\u0003᠌\u0002", new Object[]{"a", "b", ebb.k, "c", "d", ebb.j});
        } else if (i2 == 3) {
            return new eqp();
        } else {
            if (i2 == 4) {
                return new htk((htq) e);
            }
            if (i2 == 5) {
                return e;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = f;
            if (hvh == null) {
                synchronized (eqp.class) {
                    hvh = f;
                    if (hvh == null) {
                        hvh = new htl(e);
                        f = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
