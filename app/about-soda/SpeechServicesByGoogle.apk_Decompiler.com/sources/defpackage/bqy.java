package defpackage;

/* renamed from: bqy  reason: default package */
/* compiled from: PG */
public final class bqy implements ggg, ihw, iib, iio {
    private iiz a;
    private final brc b;
    private final bqy c;

    public bqy() {
        throw null;
    }

    public final ihv a() {
        return (ihv) this.a.b();
    }

    public final bqq b() {
        return new bqq(this.b, this.c);
    }

    public bqy(brc brc) {
        this.c = this;
        this.b = brc;
        this.a = iit.d(iid.a);
    }
}
