package defpackage;

import android.net.ConnectivityManager;
import android.util.Base64;
import android.util.Log;
import androidx.work.impl.WorkDatabase;

/* renamed from: bes  reason: default package */
/* compiled from: PG */
public final class bes extends jnv implements jmp {
    final /* synthetic */ Object a;
    final /* synthetic */ Object b;
    private final /* synthetic */ int c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bes(Object obj, Object obj2, int i) {
        super(0);
        this.c = i;
        this.a = obj;
        this.b = obj2;
    }

    public final /* synthetic */ Object a() {
        hsq hsq;
        switch (this.c) {
            case 0:
                bbk.a();
                long j = bfc.a;
                ((bew) this.a).a.unregisterNetworkCallback((ConnectivityManager.NetworkCallback) this.b);
                return jkd.a;
            case 1:
                am amVar = (am) this.b;
                Object obj = amVar.g;
                jnu.b(obj);
                amVar.d.u(obj, new aj(this.b, this.a, 4, (byte[]) null));
                return jkd.a;
            case 2:
                bft bft = ((bfd) this.a).a;
                Object obj2 = this.b;
                synchronized (bft.b) {
                    if (bft.c.remove(obj2) && bft.c.isEmpty()) {
                        bft.e();
                    }
                }
                return jkd.a;
            case 3:
                WorkDatabase workDatabase = ((bdm) this.b).d;
                jnu.d(workDatabase, "workManagerImpl.workDatabase");
                workDatabase.n(new aku(this.b, this.a, 10));
                xm.i((bdm) this.b);
                return jkd.a;
            case 4:
                xm.h((String) this.a, (bdm) this.b);
                xm.i((bdm) this.b);
                return jkd.a;
            case 5:
                Object obj3 = this.b;
                Object obj4 = this.a;
                ((ecu) obj4).e.execute(gof.h(new dpz(obj4, obj3, 7, (byte[]) null)));
                return jkd.a;
            case 6:
                return new emk((dze) this.b, ((gbb) ((dku) this.a).a).b());
            case 7:
                eng eng = (eng) this.b;
                dyt dyt = eng.f;
                if ((dyt.a & 512) == 0) {
                    return null;
                }
                return ((bzj) this.a).P(eng.b, dyt);
            case 8:
                eno eno = (eno) this.b;
                dyt dyt2 = eno.e;
                if ((dyt2.a & 512) == 0) {
                    return null;
                }
                return ((bzj) this.a).P(eno.b, dyt2);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                hva r = ((gcx) this.b).b().aY().e(Base64.decode((String) this.a, 3)).r();
                jnu.d(r, "build(...)");
                return r;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                try {
                    hva hva = ((gcx) this.a).d;
                    jnu.b(hva);
                    huz aY = hva.aY();
                    Object obj5 = this.b;
                    if (((gcs) obj5).b == 6) {
                        hsq = (hsq) ((gcs) obj5).c;
                    } else {
                        hsq = hsq.b;
                    }
                    aY.m(hsq);
                    return aY.r();
                } catch (hui e) {
                    Log.e("MendelPackageState", "Failed to parse flag", e);
                    return ((gcx) this.a).b();
                } catch (RuntimeException e2) {
                    Log.e("MendelPackageState", "Failed to parse flag", e2);
                    return ((gcx) this.a).b();
                }
            default:
                try {
                    hva hva2 = ((gcx) this.a).d;
                    jnu.b(hva2);
                    huz aY2 = hva2.aY();
                    jnu.d(aY2, "newBuilderForType(...)");
                    Object a2 = ((due) this.b).a();
                    if (a2 instanceof hsq) {
                        Object a3 = ((due) this.b).a();
                        jnu.c(a3, "null cannot be cast to non-null type com.google.protobuf.ByteString");
                        aY2.m((hsq) a3);
                    } else if (a2 instanceof byte[]) {
                        Object a4 = ((due) this.b).a();
                        jnu.c(a4, "null cannot be cast to non-null type kotlin.ByteArray");
                        aY2.e((byte[]) a4);
                    } else {
                        Class<?> cls = ((due) this.b).a().getClass();
                        int i = joa.a;
                        jnq jnq = new jnq(cls);
                        throw new IllegalStateException("Invalid type " + jnq);
                    }
                    return aY2.r();
                } catch (hui e3) {
                    Log.e("MendelPackageState", "Failed to parse flag", e3);
                    return ((gcx) this.a).b();
                } catch (RuntimeException e4) {
                    Log.e("MendelPackageState", "Failed to parse flag", e4);
                    return ((gcx) this.a).b();
                }
        }
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bes(Object obj, Object obj2, int i, byte[] bArr) {
        super(0);
        this.c = i;
        this.b = obj;
        this.a = obj2;
    }
}
