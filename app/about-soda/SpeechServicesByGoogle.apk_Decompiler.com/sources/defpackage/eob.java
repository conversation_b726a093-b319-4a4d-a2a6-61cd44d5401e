package defpackage;

/* renamed from: eob  reason: default package */
/* compiled from: PG */
public final class eob extends jme {
    public /* synthetic */ Object a;
    final /* synthetic */ eoc b;
    public int c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eob(eoc eoc, jlr jlr) {
        super(jlr);
        this.b = eoc;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.c |= Integer.MIN_VALUE;
        return cqx.V(this.b, this);
    }
}
