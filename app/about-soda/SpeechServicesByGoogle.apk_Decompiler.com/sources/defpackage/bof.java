package defpackage;

import com.android.car.ui.uxr.DrawableStateButton;
import com.android.car.ui.uxr.DrawableStateConstraintLayout;
import com.android.car.ui.uxr.DrawableStateFrameLayout;
import com.android.car.ui.uxr.DrawableStateImageView;
import com.android.car.ui.uxr.DrawableStateLinearLayout;
import com.android.car.ui.uxr.DrawableStateRelativeLayout;
import com.android.car.ui.uxr.DrawableStateSwitch;
import com.android.car.ui.uxr.DrawableStateTextView;
import j$.util.Collection;
import j$.util.function.Function$CC;
import j$.util.stream.IntStream;
import j$.util.stream.Stream;
import java.util.Locale;
import java.util.Map;
import java.util.function.Function;

/* renamed from: bof  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bof implements Function {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ bof(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    public final /* synthetic */ Function andThen(Function function) {
        switch (this.b) {
            case 0:
                return Function$CC.$default$andThen(this, function);
            case 1:
                return Function$CC.$default$andThen(this, function);
            case 2:
                return Function$CC.$default$andThen(this, function);
            case 3:
                return Function$CC.$default$andThen(this, function);
            case 4:
                return Function$CC.$default$andThen(this, function);
            case 5:
                return Function$CC.$default$andThen(this, function);
            case 6:
                return Function$CC.$default$andThen(this, function);
            case 7:
                return Function$CC.$default$andThen(this, function);
            case 8:
                return Function$CC.$default$andThen(this, function);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return Function$CC.$default$andThen(this, function);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return Function$CC.$default$andThen(this, function);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return Function$CC.$default$andThen(this, function);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return Function$CC.$default$andThen(this, function);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return Function$CC.$default$andThen(this, function);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return Function$CC.$default$andThen(this, function);
            case 15:
                return Function$CC.$default$andThen(this, function);
            case 16:
                return Function$CC.$default$andThen(this, function);
            case 17:
                return Function$CC.$default$andThen(this, function);
            case 18:
                return Function$CC.$default$andThen(this, function);
            case 19:
                return Function$CC.$default$andThen(this, function);
            default:
                return Function$CC.$default$andThen(this, function);
        }
    }

    /* JADX WARNING: type inference failed for: r0v24, types: [exi, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v25, types: [exi, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v26, types: [exi, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v41, types: [java.lang.Object, cto] */
    public final Object apply(Object obj) {
        switch (this.b) {
            case 0:
                return ((DrawableStateConstraintLayout) this.a).d((Integer) obj);
            case 1:
                return ((DrawableStateButton) this.a).b((Integer) obj);
            case 2:
                return ((DrawableStateFrameLayout) this.a).b((Integer) obj);
            case 3:
                return ((DrawableStateImageView) this.a).b((Integer) obj);
            case 4:
                return ((DrawableStateLinearLayout) this.a).b((Integer) obj);
            case 5:
                return ((DrawableStateRelativeLayout) this.a).b((Integer) obj);
            case 6:
                return ((DrawableStateSwitch) this.a).b((Integer) obj);
            case 7:
                return ((DrawableStateTextView) this.a).b((Integer) obj);
            case 8:
                String str = (String) obj;
                return IntStream.CC.range(0, ((Map) ((cmg) this.a).i.get(str)).size()).mapToObj(new cmd(str));
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return this.a.h((exo) obj);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return this.a.b((exo) obj);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return this.a.e((exo) obj);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return ezh.b((crw) obj, ((ezg) this.a).c);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return ezh.b((crw) obj, ((ezg) this.a).c);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                exo exo = (exo) obj;
                Locale locale = exo.a;
                hdf hdf = ezl.a;
                String a2 = ezh.a(locale.toLanguageTag(), exo.b);
                ((hdc) ((hdc) ezl.a.f()).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackPopulator", "scheduleLanguagePackDeletion", 327, "ZipfileLanguagePackPopulator.java")).u("MDD.removeFileGroup(%s)", a2);
                cui a3 = cuj.a();
                a3.b(a2);
                return this.a.g(a3.a());
            case 15:
                return ((ezo) this.a).o((exo) obj);
            case 16:
                ffg ffg = new ffg((ffh) obj);
                ffg.e = ((fck) this.a).a;
                return ffg.a();
            case 17:
                ffg ffg2 = new ffg((ffh) obj);
                ffg2.a = Locale.forLanguageTag((String) this.a).getDisplayName();
                return ffg2.a();
            case 18:
                ffg ffg3 = new ffg((ffh) obj);
                ffg3.f = this.a;
                return ffg3.a();
            case 19:
                ffg ffg4 = new ffg((ffh) obj);
                ffg4.b = this.a;
                return ffg4.a();
            default:
                exo exo2 = (exo) obj;
                htk l = fga.e.l();
                String languageTag = exo2.a.toLanguageTag();
                if (!l.b.B()) {
                    l.u();
                }
                htq htq = l.b;
                fga fga = (fga) htq;
                languageTag.getClass();
                fga.a |= 1;
                fga.b = languageTag;
                int i = exo2.b;
                if (!htq.B()) {
                    l.u();
                }
                Object obj2 = this.a;
                fga fga2 = (fga) l.b;
                fga2.a |= 2;
                fga2.c = i;
                gxv gxv = exo2.d;
                iai b2 = iai.b(((ffz) obj2).a);
                if (b2 == null) {
                    b2 = iai.UNKNOWN;
                }
                Stream filter = Collection.EL.stream((gyo) gxv.getOrDefault(b2, hau.a)).filter(new eyn(obj2, 13));
                int i2 = gxq.d;
                gxq<hxr> gxq = (gxq) filter.collect(gvx.a);
                if (!l.b.B()) {
                    l.u();
                }
                fga fga3 = (fga) l.b;
                htw htw = fga3.d;
                if (!htw.c()) {
                    fga3.d = htq.q(htw);
                }
                for (hxr hxr : gxq) {
                    fga3.d.g(hxr.r);
                }
                return (fga) l.r();
        }
    }

    public final /* synthetic */ Function compose(Function function) {
        switch (this.b) {
            case 0:
                return Function$CC.$default$compose(this, function);
            case 1:
                return Function$CC.$default$compose(this, function);
            case 2:
                return Function$CC.$default$compose(this, function);
            case 3:
                return Function$CC.$default$compose(this, function);
            case 4:
                return Function$CC.$default$compose(this, function);
            case 5:
                return Function$CC.$default$compose(this, function);
            case 6:
                return Function$CC.$default$compose(this, function);
            case 7:
                return Function$CC.$default$compose(this, function);
            case 8:
                return Function$CC.$default$compose(this, function);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return Function$CC.$default$compose(this, function);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return Function$CC.$default$compose(this, function);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return Function$CC.$default$compose(this, function);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return Function$CC.$default$compose(this, function);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return Function$CC.$default$compose(this, function);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return Function$CC.$default$compose(this, function);
            case 15:
                return Function$CC.$default$compose(this, function);
            case 16:
                return Function$CC.$default$compose(this, function);
            case 17:
                return Function$CC.$default$compose(this, function);
            case 18:
                return Function$CC.$default$compose(this, function);
            case 19:
                return Function$CC.$default$compose(this, function);
            default:
                return Function$CC.$default$compose(this, function);
        }
    }
}
