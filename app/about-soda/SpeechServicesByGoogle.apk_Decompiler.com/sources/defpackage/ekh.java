package defpackage;

/* renamed from: ekh  reason: default package */
/* compiled from: PG */
public final class ekh implements ebi {
    final /* synthetic */ dzx a;

    public ekh(dzx dzx) {
        this.a = dzx;
    }

    public final hme a() {
        htk l = dzm.c.l();
        if (!l.b.B()) {
            l.u();
        }
        dzx dzx = this.a;
        dzm dzm = (dzm) l.b;
        dzm.b = dzx.H;
        dzm.a |= 1;
        return hfc.K((dzm) l.r());
    }
}
