package defpackage;

import android.os.Process;
import android.os.StrictMode;
import j$.util.concurrent.ConcurrentHashMap;
import java.io.File;
import java.util.Locale;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/* renamed from: cqq  reason: default package */
/* compiled from: PG */
public final class cqq implements cqp {
    private final Map b = new ConcurrentHashMap();
    private final cqn c;
    private final AtomicInteger d;
    private final AtomicInteger e;
    private volatile cql f;

    public cqq(cqn cqn) {
        new ConcurrentHashMap();
        this.d = new AtomicInteger();
        this.e = new AtomicInteger();
        this.c = cqn;
        this.f = cql.a;
    }

    public final void a() {
        this.e.getAndIncrement();
    }

    public final void b() {
        this.d.getAndIncrement();
    }

    /* JADX INFO: finally extract failed */
    public final void c(long j) {
        cqm cqm = (cqm) this.b.remove(Long.valueOf(j));
        if (cqm != null) {
            int i = cqm.a;
            StrictMode.ThreadPolicy allowThreadDiskReads = StrictMode.allowThreadDiskReads();
            try {
                cql a = cqk.a(new File(String.format(Locale.US, "/proc/self/task/%d/schedstat", new Object[]{Integer.valueOf(i)})));
                StrictMode.setThreadPolicy(allowThreadDiskReads);
                if (a != cql.a) {
                    cql cql = this.f;
                    this.f = new cql(cql.b + a.b, cql.c + a.c, cql.d + a.d);
                }
            } catch (Throwable th) {
                StrictMode.setThreadPolicy(allowThreadDiskReads);
                throw th;
            }
        }
    }

    public final void d(long j) {
        Map map = this.b;
        Long valueOf = Long.valueOf(j);
        if (!map.containsKey(valueOf)) {
            long j2 = j;
            this.b.put(valueOf, new cqm(Process.myTid(), j2, Thread.currentThread().getName(), this.c.a));
        }
    }
}
