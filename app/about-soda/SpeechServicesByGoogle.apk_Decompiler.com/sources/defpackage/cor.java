package defpackage;

import java.util.concurrent.Delayed;
import java.util.concurrent.RunnableScheduledFuture;
import java.util.concurrent.TimeUnit;

/* renamed from: cor  reason: default package */
/* compiled from: PG */
final class cor implements RunnableScheduledFuture {
    private final RunnableScheduledFuture a;

    public cor(RunnableScheduledFuture runnableScheduledFuture) {
        this.a = runnableScheduledFuture;
    }

    public final boolean cancel(boolean z) {
        return this.a.cancel(z);
    }

    public final /* bridge */ /* synthetic */ int compareTo(Object obj) {
        Delayed delayed = (Delayed) obj;
        if (this == delayed) {
            return 0;
        }
        if (delayed instanceof cor) {
            return this.a.compareTo(((cor) delayed).a);
        }
        return Long.compare(getDelay(TimeUnit.NANOSECONDS), delayed.getDelay(TimeUnit.NANOSECONDS));
    }

    public final Object get() {
        return this.a.get();
    }

    public final long getDelay(TimeUnit timeUnit) {
        long delay = this.a.getDelay(timeUnit);
        if (TimeUnit.NANOSECONDS.convert(delay, timeUnit) > 2147483647999999999L) {
            return timeUnit.convert(2147483647999999999L, TimeUnit.NANOSECONDS);
        }
        return delay;
    }

    public final boolean isCancelled() {
        return this.a.isCancelled();
    }

    public final boolean isDone() {
        return this.a.isDone();
    }

    public final boolean isPeriodic() {
        return this.a.isPeriodic();
    }

    public final void run() {
        this.a.run();
    }

    public final Object get(long j, TimeUnit timeUnit) {
        return cos.a(this.a, j, timeUnit);
    }
}
