package defpackage;

import android.content.Context;
import android.net.Uri;
import android.os.Build;
import android.os.Process;
import android.os.StrictMode;
import android.os.SystemClock;
import android.util.Base64;
import j$.time.Duration;
import j$.time.Instant;
import j$.time.temporal.ChronoUnit;
import java.io.File;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;

/* renamed from: cqx  reason: default package */
/* compiled from: PG */
public class cqx {
    public static volatile cqx c;

    public cqx() {
    }

    public static StrictMode.ThreadPolicy A() {
        return new StrictMode.ThreadPolicy.Builder().detectAll().penaltyLog().build();
    }

    public static hmi B(hmi hmi) {
        return new coi(new cot(new hmq(hmi), hmi), hmi);
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v1, resolved type: java.util.concurrent.ThreadFactory} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v3, resolved type: cqr} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v4, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v7, resolved type: java.util.concurrent.ThreadFactory} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v9, resolved type: java.util.concurrent.ThreadFactory} */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static java.util.concurrent.ExecutorService C(defpackage.cqn r16, defpackage.cpk r17, java.util.concurrent.ThreadFactory r18, defpackage.cqp r19, defpackage.cpp r20) {
        /*
            r0 = r16
            r1 = r17
            r2 = r19
            grh r3 = r1.a
            boolean r3 = r3.f()
            if (r3 != 0) goto L_0x0013
            gqd r3 = defpackage.gqd.a
            r4 = r18
            goto L_0x001e
        L_0x0013:
            cpj r3 = new cpj
            r4 = r18
            r3.<init>(r4)
            grh r3 = defpackage.grh.h(r3)
        L_0x001e:
            boolean r5 = r3.f()
            if (r5 == 0) goto L_0x0028
            java.lang.Object r4 = r3.b()
        L_0x0028:
            boolean r5 = r0.c
            if (r5 == 0) goto L_0x0032
            cqr r5 = new cqr
            r5.<init>(r4, r2)
            r4 = r5
        L_0x0032:
            int r0 = r0.b
            cmp r5 = new cmp
            r6 = 3
            r5.<init>(r2, r6)
            cmp r6 = new cmp
            r7 = 4
            r6.<init>(r2, r7)
            hhl r14 = new hhl
            r14.<init>(r0, r4, r5, r6)
            boolean r0 = r3.f()
            if (r0 == 0) goto L_0x0071
            java.lang.Object r0 = r3.b()
            j$.util.Objects.requireNonNull(r14)
            androidx.wear.ambient.AmbientModeSupport$AmbientController r15 = new androidx.wear.ambient.AmbientModeSupport$AmbientController
            r2 = 0
            r15.<init>(r14, r2)
            grh r2 = r1.a
            cph r3 = new cph
            java.lang.Object r2 = r2.b()
            r9 = r2
            cpl r9 = (defpackage.cpl) r9
            boolean r11 = r1.b
            hmi r12 = r1.c
            r13 = r0
            cpj r13 = (defpackage.cpj) r13
            r8 = r3
            r10 = r20
            r8.<init>(r9, r10, r11, r12, r13, r14, r15)
            return r3
        L_0x0071:
            return r14
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cqx.C(cqn, cpk, java.util.concurrent.ThreadFactory, cqp, cpp):java.util.concurrent.ExecutorService");
    }

    public static ThreadFactory D(String str, ThreadFactory threadFactory) {
        iqe iqe = new iqe((char[]) null);
        iqe.g();
        iqe.h(str.concat(" Thread #%d"));
        iqe.c = threadFactory;
        return iqe.i(iqe);
    }

    public static /* synthetic */ hmi E(jjk jjk, grh grh) {
        if (grh.f()) {
            return (hmi) grh.b();
        }
        return (hmi) jjk.b();
    }

    public static long F() {
        if (cnq.a) {
            return SystemClock.elapsedRealtimeNanos();
        }
        return SystemClock.elapsedRealtime() * 1000000;
    }

    public static Instant G() {
        return Instant.now().truncatedTo(ChronoUnit.MILLIS);
    }

    public static Duration H() {
        return Duration.ofMillis(SystemClock.uptimeMillis());
    }

    public static cks I(Object obj) {
        ckw ckw = new ckw();
        ckw.i(obj);
        return ckw;
    }

    public static cqp J(bzl bzl, cqn cqn) {
        if (cqn.c) {
            return bzl.c(cqn);
        }
        return cqp.a;
    }

    public static void K(Context context, grh grh, csx csx, kjd kjd) {
        Uri i = i(context, grh, csx);
        if (kjd.j(i)) {
            Cfor forR = new Cfor();
            forR.a = true;
            Void voidR = (Void) kjd.e(i, forR);
        }
    }

    public static eow L(esh esh) {
        return new eow(esh.a(), esh.d(), esh.g());
    }

    public static boolean M(Context context) {
        if (context.getPackageManager().checkPermission("android.permission.CAPTURE_AUDIO_HOTWORD", context.getPackageName()) == 0) {
            return true;
        }
        return false;
    }

    public static boolean N(Context context) {
        if (Build.VERSION.SDK_INT < 31) {
            return O(context, "android.permission.BLUETOOTH");
        }
        if (!O(context, "android.permission.BLUETOOTH_SCAN") || !O(context, "android.permission.BLUETOOTH_CONNECT")) {
            return false;
        }
        return true;
    }

    public static boolean O(Context context, String str) {
        try {
            if (context.checkPermission(str, Process.myPid(), Process.myUid()) == 0) {
                return true;
            }
            return false;
        } catch (RuntimeException unused) {
        }
    }

    public static jrz P(eov eov, jqs jqs, jna jna) {
        jnu.e(jqs, "scope");
        return job.S(jqs, (jlv) null, (jqt) null, new eot(eov, jna, (jlr) null, 1), 3);
    }

    public static void Q(eou eou, jqs jqs, jna jna) {
        jnu.e(jqs, "scope");
        job.S(jqs, (jlv) null, (jqt) null, new eot(jna, eou, (jlr) null, 0), 3);
    }

    public static String R(ebn ebn) {
        ehg ehg = ebn.f;
        if (ehg == null) {
            ehg = ehg.c;
        }
        jnu.d(ehg, "getClientInfo(...)");
        return fbi.s(ehg);
    }

    public static hme S(eon eon) {
        return eon.d().b.a;
    }

    /* JADX WARNING: Removed duplicated region for block: B:12:0x002f  */
    /* JADX WARNING: Removed duplicated region for block: B:16:0x004e  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static /* synthetic */ java.lang.Object T(defpackage.eon r4, defpackage.jlr r5) {
        /*
            boolean r0 = r5 instanceof defpackage.eom
            if (r0 == 0) goto L_0x0013
            r0 = r5
            eom r0 = (defpackage.eom) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            eom r0 = new eom
            r0.<init>(r4, r5)
        L_0x0018:
            java.lang.Object r5 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            r3 = 1
            if (r2 == 0) goto L_0x002f
            if (r2 != r3) goto L_0x0027
            defpackage.jji.c(r5)
            goto L_0x0042
        L_0x0027:
            java.lang.IllegalStateException r4 = new java.lang.IllegalStateException
            java.lang.String r5 = "call to 'resume' before 'invoke' with coroutine"
            r4.<init>(r5)
            throw r4
        L_0x002f:
            defpackage.jji.c(r5)
            ebl r4 = r4.b()
            hme r4 = r4.c()
            r0.c = r3
            java.lang.Object r5 = defpackage.jqw.x(r4, r0)
            if (r5 == r1) goto L_0x0056
        L_0x0042:
            ebj r5 = (defpackage.ebj) r5
            java.lang.Object r4 = r5.f()
            ebo r4 = (defpackage.ebo) r4
            eak r4 = r4.b
            if (r4 != 0) goto L_0x0050
            eak r4 = defpackage.eak.c
        L_0x0050:
            java.lang.String r5 = "getAudioSourceOpeningStatus(...)"
            defpackage.jnu.d(r4, r5)
            return r4
        L_0x0056:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cqx.T(eon, jlr):java.lang.Object");
    }

    public static hme U(eoc eoc) {
        return eoc.e().b.a;
    }

    /* JADX WARNING: Removed duplicated region for block: B:12:0x002f  */
    /* JADX WARNING: Removed duplicated region for block: B:16:0x0053  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static /* synthetic */ java.lang.Object V(defpackage.eoc r4, defpackage.jlr r5) {
        /*
            boolean r0 = r5 instanceof defpackage.eob
            if (r0 == 0) goto L_0x0013
            r0 = r5
            eob r0 = (defpackage.eob) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            eob r0 = new eob
            r0.<init>(r4, r5)
        L_0x0018:
            java.lang.Object r5 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            r3 = 1
            if (r2 == 0) goto L_0x002f
            if (r2 != r3) goto L_0x0027
            defpackage.jji.c(r5)
            goto L_0x0047
        L_0x0027:
            java.lang.IllegalStateException r4 = new java.lang.IllegalStateException
            java.lang.String r5 = "call to 'resume' before 'invoke' with coroutine"
            r4.<init>(r5)
            throw r4
        L_0x002f:
            defpackage.jji.c(r5)
            dyy r4 = r4.b()
            hme r4 = r4.c()
            java.lang.String r5 = "getStartListeningResult(...)"
            defpackage.jnu.d(r4, r5)
            r0.c = r3
            java.lang.Object r5 = defpackage.jqw.x(r4, r0)
            if (r5 == r1) goto L_0x005b
        L_0x0047:
            dyw r5 = (defpackage.dyw) r5
            java.lang.Object r4 = r5.f()
            dzh r4 = (defpackage.dzh) r4
            eak r4 = r4.b
            if (r4 != 0) goto L_0x0055
            eak r4 = defpackage.eak.c
        L_0x0055:
            java.lang.String r5 = "getAudioSourceOpeningStatus(...)"
            defpackage.jnu.d(r4, r5)
            return r4
        L_0x005b:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cqx.V(eoc, jlr):java.lang.Object");
    }

    public static hme a(cks cks) {
        crm crm = new crm(cks, (Runnable) null);
        cks.d(hld.a, new crl(crm));
        return crm;
    }

    public static boolean b(ihf ihf) {
        try {
            fpa.a(ihf);
            return true;
        } catch (IllegalArgumentException e) {
            cyh.q(e, "Invalid transform specification");
            return false;
        }
    }

    public static long c() {
        return G().toEpochMilli();
    }

    public static ctg d(String str) {
        try {
            return (ctg) cqh.C(str, (hvh) ctg.g.C(7));
        } catch (hui | NullPointerException e) {
            throw new czf("Failed to deserialize key:".concat(String.valueOf(str)), e);
        }
    }

    public static File e(Context context, grh grh) {
        String str = "gms_icing_mdd_garbage_file";
        if (grh != null && grh.f()) {
            str = str.concat((String) grh.b());
        }
        return new File(context.getFilesDir(), str);
    }

    public static String f(ctg ctg) {
        return Base64.encodeToString(ctg.g(), 3);
    }

    public static long g(csx csx) {
        if (csx.k == 0) {
            return Long.MAX_VALUE;
        }
        return TimeUnit.SECONDS.toMillis(csx.k);
    }

    public static Uri h(Uri uri, csv csv) {
        Uri.Builder buildUpon = uri.buildUpon();
        if (csv.o.isEmpty()) {
            String str = csv.c;
            buildUpon.appendPath(str.substring(str.lastIndexOf("/") + 1));
        } else {
            for (String str2 : csv.o.split("/", -1)) {
                if (!str2.isEmpty()) {
                    buildUpon.appendPath(str2);
                }
            }
        }
        return buildUpon.build();
    }

    public static Uri i(Context context, grh grh, csx csx) {
        String str;
        if (!csx.v.isEmpty()) {
            str = csx.v;
        } else {
            str = csx.c;
        }
        int x = a.x(csx.i);
        if (x == 0) {
            x = 1;
        }
        return s(context, grh).buildUpon().appendPath(v(x)).build().buildUpon().appendPath(str).build();
    }

    public static csx j(csx csx, long j) {
        csw csw = csx.b;
        if (csw == null) {
            csw = csw.i;
        }
        htk htk = (htk) csw.C(5);
        htk.x(csw);
        if (!htk.b.B()) {
            htk.u();
        }
        csw csw2 = (csw) htk.b;
        csw2.a |= 1;
        csw2.b = j;
        csw csw3 = (csw) htk.r();
        htk htk2 = (htk) csx.C(5);
        htk2.x(csx);
        if (!htk2.b.B()) {
            htk2.u();
        }
        csx csx2 = (csx) htk2.b;
        csw3.getClass();
        csx2.b = csw3;
        csx2.a |= 1;
        return (csx) htk2.r();
    }

    public static String k(csv csv) {
        if (l(csv)) {
            return csv.h;
        }
        return csv.f;
    }

    public static boolean l(csv csv) {
        if ((csv.a & 32) == 0) {
            return false;
        }
        ihf ihf = csv.g;
        if (ihf == null) {
            ihf = ihf.b;
        }
        for (ihe ihe : ihf.a) {
            if (ihe.a == 4) {
                return true;
            }
        }
        return false;
    }

    public static boolean m(String str, gyo gyo) {
        boolean z;
        if (str.isEmpty()) {
            return false;
        }
        int indexOf = str.indexOf(58);
        if (indexOf >= 0) {
            z = true;
        } else {
            z = false;
        }
        fvf.aJ(z, "Invalid url: %s", str);
        String substring = str.substring(0, indexOf);
        hbp k = gyo.iterator();
        while (k.hasNext()) {
            if (ftd.v(substring, (String) k.next())) {
                return true;
            }
        }
        return false;
    }

    public static boolean n(csv csv) {
        return m(csv.c, new hbi("inlinefile"));
    }

    public static boolean o(csx csx) {
        if (!csx.m) {
            return false;
        }
        for (csv csv : csx.n) {
            int A = a.A(csv.l);
            if (A != 0 && A == 2) {
                return false;
            }
        }
        return true;
    }

    public static boolean p(csv csv) {
        return m(csv.c, gyo.p("file", "asset"));
    }

    public static boolean q(long j) {
        if (j <= c()) {
            return true;
        }
        return false;
    }

    public static Uri r(Context context, grh grh) {
        String str;
        fnr fnr = new fnr(context);
        if (grh == null || !grh.f()) {
            str = "datadownload";
        } else {
            str = (String) grh.b();
        }
        fnr.d(str);
        if (grh != null && grh.f()) {
            fnr.e("datadownload");
        }
        return fnr.a();
    }

    public static Uri s(Context context, grh grh) {
        return r(context, grh).buildUpon().appendPath("links").build();
    }

    public static Uri t(Context context, String str) {
        gry gry = fnt.a;
        return ftc.K(str, context.getPackageName(), 0);
    }

    public static Uri u(Context context, int i, String str, String str2, cuk cuk, grh grh, boolean z) {
        if (z) {
            try {
                return t(context, str2);
            } catch (Exception e) {
                cyh.j(e, "%s: Unable to create mobstore uri for file %s.", "DirectoryUtil", str);
                cuk.a();
                return null;
            }
        } else {
            return r(context, grh).buildUpon().appendPath(v(i)).build().buildUpon().appendPath(str).build();
        }
    }

    public static String v(int i) {
        int i2 = i - 1;
        if (i2 == 0) {
            return "public";
        }
        if (i2 != 1) {
            return "public_3p";
        }
        return "private";
    }

    public static cxz w(cxm cxm, iiz iiz, iiz iiz2, iiz iiz3) {
        cvz cvz = (cvz) iiz3.b();
        Object obj = cxm.h;
        hzz.u(obj);
        Executor executor = (Executor) iiz2.b();
        cqh cqh = (cqh) iiz.b();
        grh grh = (grh) obj;
        if (!grh.f()) {
            return new cya();
        }
        Object obj2 = cxm.j;
        return new cyd((csm) grh.b(), cvz, executor);
    }

    public static cvy x(cxi cxi, cxm cxm, iiz iiz, iiz iiz2, iiz iiz3, iiz iiz4, iiz iiz5, iiz iiz6, iiz iiz7, iiz iiz8, iiz iiz9, iiz iiz10, iiz iiz11, iiz iiz12, iiz iiz13, iiz iiz14, iiz iiz15, iiz iiz16, iiz iiz17) {
        Context c2 = iim.c(cxi);
        dbw y = y(cxi, cxm, iiz, iiz2, iiz3, iiz4, iiz5, iiz6, iiz7, iiz8, iiz9, iiz10, iiz12, iiz13, iiz14);
        cxz w = w(cxm, iiz4, iiz5, iiz14);
        cqh cqh = (cqh) iiz4.b();
        return new cvy(c2, (cyk) iiz.b(), (cuk) iiz2.b(), (cvz) iiz14.b(), y, (cqx) iiz11.b(), (grh) iiz15.b(), (Executor) iiz5.b(), (grh) iiz3.b(), (kjd) iiz6.b(), (grh) iiz16.b(), w, (Executor) iiz17.b());
    }

    public static dbw y(cxi cxi, cxm cxm, iiz iiz, iiz iiz2, iiz iiz3, iiz iiz4, iiz iiz5, iiz iiz6, iiz iiz7, iiz iiz8, iiz iiz9, iiz iiz10, iiz iiz11, iiz iiz12, iiz iiz13) {
        Context c2 = iim.c(cxi);
        cqh cqh = (cqh) iiz4.b();
        return new dbw(c2, (cuk) iiz2.b(), (cxa) iiz7.b(), (kjd) iiz6.b(), new cxx(iim.c(cxi), (gsb) iiz8.b(), (kjd) iiz6.b(), (czs) iiz9.b(), (grh) iiz10.b(), (cyi) iiz11.b(), (Executor) iiz5.b()), (grh) iiz12.b(), (grh) iiz10.b(), (cyk) iiz.b(), (cqh) iiz4.b(), (cvz) iiz13.b(), (grh) iiz3.b(), (Executor) iiz5.b(), (ftc) ((grh) cxm.i).d(new ftc((byte[]) null)));
    }

    public static long z(boolean z, boolean z2, boolean z3, int i, int i2, int i3, long j) {
        long j2;
        long j3;
        long j4 = 0;
        if (true != z) {
            j2 = 0;
        } else {
            j2 = 1;
        }
        if (true != z2) {
            j3 = 0;
        } else {
            j3 = 1;
        }
        long j5 = j2 + j2;
        if (true == z3) {
            j4 = 1;
        }
        long j6 = j3 | j5;
        return (((((((((j6 + j6) | j4) << 6) | ((long) ((i + 21) & 63))) << 6) | ((long) (i2 + 21))) << 6) | ((long) ((i3 + 21) & 63))) << 43) | (8796093022207L & j);
    }

    public cqx(char[] cArr) {
    }
}
