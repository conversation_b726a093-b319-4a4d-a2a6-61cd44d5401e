package defpackage;

/* renamed from: crw  reason: default package */
/* compiled from: PG */
public final class crw extends htq implements hvb {
    public static final crw n;
    private static volatile hvh o;
    public int a;
    public String b = "";
    public String c = "";
    public String d = "";
    public int e;
    public int f;
    public huf g;
    public long h;
    public String i;
    public huf j;
    public hse k;
    public hse l;
    public String m;

    static {
        crw crw = new crw();
        n = crw;
        htq.z(crw.class, crw);
    }

    private crw() {
        hvk hvk = hvk.a;
        this.g = hvk;
        this.i = "";
        this.j = hvk;
        this.m = "";
    }

    /* access modifiers changed from: protected */
    public final Object a(int i2, Object obj) {
        int i3 = i2 - 1;
        if (i3 == 0) {
            return (byte) 1;
        }
        if (i3 == 2) {
            return new hvl(n, "\u0001\f\u0000\u0001\u0001\u000f\f\u0000\u0002\u0000\u0001ဈ\u0000\u0002\u001b\u0003ဈ\u0001\u0004င\u0003\u0005᠌\u0004\u0006ဈ\u0002\bဂ\u0005\n\u001a\u000bဉ\u0007\fဈ\u0006\rဉ\b\u000fဈ\n", new Object[]{"a", "b", "g", crv.class, "c", "e", "f", bqk.i, "d", "h", "j", "k", "i", "l", "m"});
        } else if (i3 == 3) {
            return new crw();
        } else {
            if (i3 == 4) {
                return new htk((htq) n);
            }
            if (i3 == 5) {
                return n;
            }
            if (i3 != 6) {
                return null;
            }
            hvh hvh = o;
            if (hvh == null) {
                synchronized (crw.class) {
                    hvh = o;
                    if (hvh == null) {
                        hvh = new htl(n);
                        o = hvh;
                    }
                }
            }
            return hvh;
        }
    }

    public final void b() {
        huf huf = this.g;
        if (!huf.c()) {
            this.g = htq.s(huf);
        }
    }
}
