package defpackage;

/* renamed from: csd  reason: default package */
/* compiled from: PG */
public enum csd implements hts {
    DOWNLOAD_ONLY_ON_WIFI(0),
    DOWNLOAD_ON_ANY_NETWORK(1),
    DOWNLOAD_FIRST_ON_WIFI_THEN_ON_ANY_NETWORK(2);
    
    public final int d;

    private csd(int i) {
        this.d = i;
    }

    public static csd b(int i) {
        if (i == 0) {
            return DOWNLOAD_ONLY_ON_WIFI;
        }
        if (i == 1) {
            return DOWNLOAD_ON_ANY_NETWORK;
        }
        if (i != 2) {
            return null;
        }
        return DOWNLOAD_FIRST_ON_WIFI_THEN_ON_ANY_NETWORK;
    }

    public final int a() {
        return this.d;
    }

    public final String toString() {
        return Integer.toString(this.d);
    }
}
