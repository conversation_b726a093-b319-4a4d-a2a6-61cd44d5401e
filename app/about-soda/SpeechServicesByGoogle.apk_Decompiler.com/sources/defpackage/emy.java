package defpackage;

/* renamed from: emy  reason: default package */
/* compiled from: PG */
final class emy extends jmi implements jne {
    Object a;
    Object b;
    Object c;
    Object d;
    Object e;
    int f;
    final /* synthetic */ emz g;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public emy(emz emz, jlr jlr) {
        super(2, jlr);
        this.g = emz;
    }

    public final /* bridge */ /* synthetic */ Object b(Object obj, Object obj2) {
        return ((emy) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX WARNING: Removed duplicated region for block: B:18:0x00bc  */
    /* JADX WARNING: Removed duplicated region for block: B:21:0x0101 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:22:0x0102 A[PHI: r13 
      PHI: (r13v1 java.lang.Object) = (r13v11 java.lang.Object), (r13v0 java.lang.Object) binds: [B:20:0x00ff, B:4:0x000f] A[DONT_GENERATE, DONT_INLINE], RETURN] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r13) {
        /*
            r12 = this;
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r1 = r12.f
            java.lang.String r2 = "value"
            r3 = 2
            r4 = 1
            r5 = 0
            if (r1 == 0) goto L_0x0038
            if (r1 == r4) goto L_0x0023
            if (r1 == r3) goto L_0x0014
            defpackage.jji.c(r13)
            goto L_0x0102
        L_0x0014:
            java.lang.Object r1 = r12.c
            esu r1 = (defpackage.esu) r1
            java.lang.Object r4 = r12.b
            java.lang.Object r6 = r12.a
            dlv r6 = (defpackage.dlv) r6
            defpackage.jji.c(r13)
            goto L_0x00a9
        L_0x0023:
            java.lang.Object r1 = r12.e
            java.lang.Object r6 = r12.d
            java.lang.Object r7 = r12.c
            dlv r7 = (defpackage.dlv) r7
            java.lang.Object r8 = r12.b
            java.lang.Object r9 = r12.a
            emz r9 = (defpackage.emz) r9
            defpackage.jji.c(r13)
            r11 = r7
            r7 = r6
            r6 = r11
            goto L_0x006d
        L_0x0038:
            defpackage.jji.c(r13)
            emz r9 = r12.g
            etd r13 = defpackage.etd.d
            htk r13 = r13.l()
            java.lang.String r1 = "newBuilder(...)"
            defpackage.jnu.d(r13, r1)
            java.lang.String r1 = "builder"
            defpackage.jnu.e(r13, r1)
            dlv r6 = new dlv
            r6.<init>((java.lang.Object) r13)
            r12.a = r9
            r12.b = r6
            r12.c = r6
            r12.d = r6
            esu r13 = r9.d
            r12.e = r13
            r12.f = r4
            hme r1 = r9.a
            java.lang.Object r1 = defpackage.jqw.x(r1, r12)
            if (r1 == r0) goto L_0x0103
            r7 = r6
            r8 = r7
            r11 = r1
            r1 = r13
            r13 = r11
        L_0x006d:
            ebf r13 = (defpackage.ebf) r13
            defpackage.jnu.e(r13, r2)
            dlv r7 = (defpackage.dlv) r7
            java.lang.Object r7 = r7.a
            htk r7 = (defpackage.htk) r7
            htq r10 = r7.b
            boolean r10 = r10.B()
            if (r10 != 0) goto L_0x0083
            r7.u()
        L_0x0083:
            htq r7 = r7.b
            etd r7 = (defpackage.etd) r7
            etd r10 = defpackage.etd.d
            r13.getClass()
            r7.b = r13
            int r13 = r7.a
            r13 = r13 | r4
            r7.a = r13
            hme r13 = r9.b
            r12.a = r8
            r12.b = r6
            r12.c = r1
            r12.d = r5
            r12.e = r5
            r12.f = r3
            java.lang.Object r13 = defpackage.jqw.x(r13, r12)
            if (r13 == r0) goto L_0x0103
            r4 = r6
            r6 = r8
        L_0x00a9:
            ebw r13 = (defpackage.ebw) r13
            defpackage.jnu.e(r13, r2)
            dlv r4 = (defpackage.dlv) r4
            java.lang.Object r2 = r4.a
            htk r2 = (defpackage.htk) r2
            htq r4 = r2.b
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x00bf
            r2.u()
        L_0x00bf:
            htq r2 = r2.b
            etd r2 = (defpackage.etd) r2
            etd r4 = defpackage.etd.d
            r13.getClass()
            r2.c = r13
            int r13 = r2.a
            r13 = r13 | r3
            r2.a = r13
            dlv r6 = (defpackage.dlv) r6
            java.lang.Object r13 = r6.a
            htk r13 = (defpackage.htk) r13
            htq r13 = r13.r()
            java.lang.String r2 = "build(...)"
            defpackage.jnu.d(r13, r2)
            etd r13 = (defpackage.etd) r13
            jie r1 = (defpackage.jie) r1
            iow r2 = r1.a
            iov r1 = r1.b
            isa r3 = defpackage.esx.i()
            ioy r1 = r2.a(r3, r1)
            hme r13 = defpackage.jin.a(r1, r13)
            r12.a = r5
            r12.b = r5
            r12.c = r5
            r1 = 3
            r12.f = r1
            java.lang.Object r13 = defpackage.jqw.x(r13, r12)
            if (r13 != r0) goto L_0x0102
            return r0
        L_0x0102:
            return r13
        L_0x0103:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.emy.bk(java.lang.Object):java.lang.Object");
    }

    public final jlr c(Object obj, jlr jlr) {
        return new emy(this.g, jlr);
    }
}
