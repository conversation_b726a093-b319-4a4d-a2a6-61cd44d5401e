package defpackage;

/* renamed from: esn  reason: default package */
/* compiled from: PG */
public final class esn {
    public final eaj a;
    public final eag b;
    public final dzx c;
    public final eaf d;
    public final String e;

    public esn(eaj eaj, eag eag, dzx dzx, eaf eaf, String str) {
        jnu.e(eaj, "openingFailure");
        jnu.e(eag, "closingFailure");
        jnu.e(dzx, "disconnectReason");
        jnu.e(eaf, "updateRoutingStatus");
        this.a = eaj;
        this.b = eag;
        this.c = dzx;
        this.d = eaf;
        this.e = str;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof esn)) {
            return false;
        }
        esn esn = (esn) obj;
        if (this.a == esn.a && this.b == esn.b && this.c == esn.c && this.d == esn.d && jnu.i(this.e, esn.e)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (((((((this.a.hashCode() * 31) + this.b.hashCode()) * 31) + this.c.hashCode()) * 31) + this.d.hashCode()) * 31) + this.e.hashCode();
    }

    public final String toString() {
        return "FailedRouteStatuses(openingFailure=" + this.a + ", closingFailure=" + this.b + ", disconnectReason=" + this.c + ", updateRoutingStatus=" + this.d + ", message=" + this.e + ")";
    }
}
