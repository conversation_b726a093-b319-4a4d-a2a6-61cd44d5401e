package defpackage;

import androidx.wear.ambient.AmbientModeSupport;
import java.util.List;
import java.util.concurrent.AbstractExecutorService;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/* renamed from: cph  reason: default package */
/* compiled from: PG */
public final class cph extends AbstractExecutorService {
    public final cpj a;
    public final cpl b;
    public final cpp c;
    public final boolean d;
    public final hmi e;
    private final AtomicInteger f = new AtomicInteger(1000);
    private final ExecutorService g;
    private final AmbientModeSupport.AmbientController h;

    public cph(cpl cpl, cpp cpp, boolean z, hmi hmi, cpj cpj, ExecutorService executorService, AmbientModeSupport.AmbientController ambientController) {
        this.a = cpj;
        this.g = executorService;
        this.h = ambientController;
        this.b = cpl;
        this.c = cpp;
        this.d = z;
        this.e = hmi;
    }

    public final boolean awaitTermination(long j, TimeUnit timeUnit) {
        return this.g.awaitTermination(j, timeUnit);
    }

    public final void execute(Runnable runnable) {
        this.c.c();
        if (cpk.b()) {
            this.g.execute(new cpg(this, runnable));
        } else {
            this.g.execute(runnable);
        }
        this.c.a();
        if (cpk.b()) {
            AtomicInteger atomicInteger = ((hhl) this.h.a).d;
            cpp cpp = this.c;
            int i = atomicInteger.get();
            cpp.b();
            if (i >= 1000) {
                while (true) {
                    int i2 = this.f.get();
                    if (i >= i2) {
                        if (this.f.compareAndSet(i2, i2 + i2)) {
                            this.c.b();
                            cpk.a(this.b, this.a.a(), new cpm(a.am(i, "Queue size of ", " exceeds starvation threshold of 1000")));
                        }
                    } else {
                        return;
                    }
                }
            }
        }
    }

    public final boolean isShutdown() {
        return this.g.isShutdown();
    }

    public final boolean isTerminated() {
        return this.g.isTerminated();
    }

    public final void shutdown() {
        this.g.shutdown();
    }

    public final List shutdownNow() {
        return this.g.shutdownNow();
    }

    public final String toString() {
        String obj = this.g.toString();
        return "Monitoring[" + obj + "]";
    }
}
