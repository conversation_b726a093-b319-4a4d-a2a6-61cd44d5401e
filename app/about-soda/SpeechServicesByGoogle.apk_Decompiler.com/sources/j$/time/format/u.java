package j$.time.format;

import j$.time.ZoneId;
import j$.time.chrono.C0003b;
import j$.time.chrono.n;
import j$.time.temporal.a;
import j$.time.temporal.l;
import j$.time.temporal.m;
import j$.time.temporal.p;
import j$.time.temporal.r;
import j$.util.Objects;
import java.util.Locale;

final class u {
    private m a;
    private DateTimeFormatter b;
    private int c;

    u(m mVar, DateTimeFormatter dateTimeFormatter) {
        n nVar;
        n b2 = dateTimeFormatter.b();
        if (b2 != null) {
            n nVar2 = (n) mVar.F(l.e());
            ZoneId zoneId = (ZoneId) mVar.F(l.k());
            C0003b bVar = null;
            b2 = Objects.equals(b2, nVar2) ? null : b2;
            Objects.equals((Object) null, zoneId);
            if (b2 != null) {
                if (b2 != null) {
                    nVar = b2;
                } else {
                    nVar = nVar2;
                }
                if (b2 != null) {
                    if (mVar.e(a.EPOCH_DAY)) {
                        bVar = nVar.r(mVar);
                    } else if (!(b2 == j$.time.chrono.u.d && nVar2 == null)) {
                        a[] values = a.values();
                        int length = values.length;
                        int i = 0;
                        while (i < length) {
                            a aVar = values[i];
                            if (!aVar.B() || !mVar.e(aVar)) {
                                i++;
                            } else {
                                String valueOf = String.valueOf(b2);
                                String valueOf2 = String.valueOf(mVar);
                                throw new RuntimeException("Unable to apply override chronology '" + valueOf + "' because the temporal object being formatted contains date fields but does not represent a whole date: " + valueOf2);
                            }
                        }
                    }
                }
                mVar = new t(bVar, mVar, nVar, zoneId);
            }
        }
        this.a = mVar;
        this.b = dateTimeFormatter;
    }

    /* access modifiers changed from: package-private */
    public final void a() {
        this.c--;
    }

    /* access modifiers changed from: package-private */
    public final y b() {
        return this.b.c();
    }

    /* access modifiers changed from: package-private */
    public final Locale c() {
        return this.b.d();
    }

    /* access modifiers changed from: package-private */
    public final m d() {
        return this.a;
    }

    /* access modifiers changed from: package-private */
    public final Long e(p pVar) {
        int i = this.c;
        m mVar = this.a;
        if (i <= 0 || mVar.e(pVar)) {
            return Long.valueOf(mVar.B(pVar));
        }
        return null;
    }

    /* access modifiers changed from: package-private */
    public final Object f(r rVar) {
        m mVar = this.a;
        Object F = mVar.F(rVar);
        if (F != null || this.c != 0) {
            return F;
        }
        String valueOf = String.valueOf(rVar);
        String valueOf2 = String.valueOf(mVar);
        throw new RuntimeException("Unable to extract " + valueOf + " from temporal " + valueOf2);
    }

    /* access modifiers changed from: package-private */
    public final void g() {
        this.c++;
    }

    public final String toString() {
        return this.a.toString();
    }
}
