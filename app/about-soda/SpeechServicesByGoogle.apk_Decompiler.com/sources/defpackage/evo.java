package defpackage;

import android.content.Context;
import java.io.File;

/* renamed from: evo  reason: default package */
/* compiled from: PG */
public final class evo {
    private static final hca a = hca.m("com/google/android/libraries/search/integrations/storage/FileUtilsKt");

    public static final File a(Context context) {
        File cacheDir = context.getCacheDir();
        jnu.d(cacheDir, "getCacheDir(...)");
        File file = new File(cacheDir, "audio_library/recordings");
        if (!file.exists() && !file.mkdirs() && !file.exists()) {
            ((hby) a.g().h(hdg.a, "FileUtils").j("com/google/android/libraries/search/integrations/storage/FileUtilsKt", "createDirIfNotExists", 133, "FileUtils.kt")).r("Directory not created");
        }
        return file;
    }
}
