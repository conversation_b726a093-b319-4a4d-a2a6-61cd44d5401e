package defpackage;

/* renamed from: cqm  reason: default package */
/* compiled from: PG */
public final class cqm {
    public final int a;
    private final long b;
    private final String c;
    private final String d;

    public cqm() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof cqm) {
            cqm cqm = (cqm) obj;
            if (this.a != cqm.a || this.b != cqm.b || !this.c.equals(cqm.c) || !this.d.equals(cqm.d)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        long j = this.b;
        int i = (int) (j ^ (j >>> 32));
        return ((((i ^ ((this.a ^ 1000003) * 1000003)) * 1000003) ^ this.c.hashCode()) * 1000003) ^ this.d.hashCode();
    }

    public final String toString() {
        return "ThreadIdentifier{tid=" + this.a + ", id=" + this.b + ", name=" + this.c + ", threadPoolName=" + this.d + "}";
    }

    public cqm(int i, long j, String str, String str2) {
        this.a = i;
        this.b = j;
        if (str != null) {
            this.c = str;
            this.d = str2;
            return;
        }
        throw new NullPointerException("Null name");
    }
}
