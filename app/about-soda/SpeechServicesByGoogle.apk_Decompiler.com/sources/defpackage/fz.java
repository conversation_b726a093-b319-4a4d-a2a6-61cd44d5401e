package defpackage;

import android.content.Context;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.view.ActionProvider;
import android.view.CollapsibleActionView;
import android.view.ContextMenu;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import java.lang.reflect.Method;

/* renamed from: fz  reason: default package */
/* compiled from: PG */
public final class fz extends fj implements MenuItem {
    public final ts c;
    public Method d;

    public fz(Context context, ts tsVar) {
        super(context);
        if (tsVar != null) {
            this.c = tsVar;
            return;
        }
        throw new IllegalArgumentException("Wrapped Object can not be null.");
    }

    public final boolean collapseActionView() {
        return this.c.collapseActionView();
    }

    public final boolean expandActionView() {
        return this.c.expandActionView();
    }

    public final ActionProvider getActionProvider() {
        a c2 = this.c.c();
        if (!(c2 instanceof fv)) {
            return null;
        }
        int i = fv.b;
        return ((fv) c2).a;
    }

    public final View getActionView() {
        View actionView = this.c.getActionView();
        if (actionView instanceof fw) {
            return (View) ((fw) actionView).a;
        }
        return actionView;
    }

    public final int getAlphabeticModifiers() {
        return this.c.getAlphabeticModifiers();
    }

    public final char getAlphabeticShortcut() {
        return this.c.getAlphabeticShortcut();
    }

    public final CharSequence getContentDescription() {
        return this.c.getContentDescription();
    }

    public final int getGroupId() {
        return this.c.getGroupId();
    }

    public final Drawable getIcon() {
        return this.c.getIcon();
    }

    public final ColorStateList getIconTintList() {
        return this.c.getIconTintList();
    }

    public final PorterDuff.Mode getIconTintMode() {
        return this.c.getIconTintMode();
    }

    public final Intent getIntent() {
        return this.c.getIntent();
    }

    public final int getItemId() {
        return this.c.getItemId();
    }

    public final ContextMenu.ContextMenuInfo getMenuInfo() {
        return null;
    }

    public final int getNumericModifiers() {
        return this.c.getNumericModifiers();
    }

    public final char getNumericShortcut() {
        return this.c.getNumericShortcut();
    }

    public final int getOrder() {
        return this.c.getOrder();
    }

    public final SubMenu getSubMenu() {
        return this.c.getSubMenu();
    }

    public final CharSequence getTitle() {
        return this.c.getTitle();
    }

    public final CharSequence getTitleCondensed() {
        return this.c.getTitleCondensed();
    }

    public final CharSequence getTooltipText() {
        return this.c.getTooltipText();
    }

    public final boolean hasSubMenu() {
        return this.c.hasSubMenu();
    }

    public final boolean isActionViewExpanded() {
        return this.c.isActionViewExpanded();
    }

    public final boolean isCheckable() {
        return this.c.isCheckable();
    }

    public final boolean isChecked() {
        return this.c.isChecked();
    }

    public final boolean isEnabled() {
        return this.c.isEnabled();
    }

    public final boolean isVisible() {
        return this.c.isVisible();
    }

    public final MenuItem setActionProvider(ActionProvider actionProvider) {
        fv fvVar = new fv(actionProvider);
        if (actionProvider == null) {
            fvVar = null;
        }
        this.c.d(fvVar);
        return this;
    }

    public final MenuItem setActionView(int i) {
        this.c.setActionView(i);
        View actionView = this.c.getActionView();
        if (actionView instanceof CollapsibleActionView) {
            this.c.setActionView((View) new fw(actionView));
        }
        return this;
    }

    public final MenuItem setAlphabeticShortcut(char c2) {
        this.c.setAlphabeticShortcut(c2);
        return this;
    }

    public final MenuItem setCheckable(boolean z) {
        this.c.setCheckable(z);
        return this;
    }

    public final MenuItem setChecked(boolean z) {
        this.c.setChecked(z);
        return this;
    }

    public final MenuItem setContentDescription(CharSequence charSequence) {
        this.c.a(charSequence);
        return this;
    }

    public final MenuItem setEnabled(boolean z) {
        this.c.setEnabled(z);
        return this;
    }

    public final MenuItem setIcon(int i) {
        this.c.setIcon(i);
        return this;
    }

    public final MenuItem setIconTintList(ColorStateList colorStateList) {
        this.c.setIconTintList(colorStateList);
        return this;
    }

    public final MenuItem setIconTintMode(PorterDuff.Mode mode) {
        this.c.setIconTintMode(mode);
        return this;
    }

    public final MenuItem setIntent(Intent intent) {
        this.c.setIntent(intent);
        return this;
    }

    public final MenuItem setNumericShortcut(char c2) {
        this.c.setNumericShortcut(c2);
        return this;
    }

    public final MenuItem setOnActionExpandListener(MenuItem.OnActionExpandListener onActionExpandListener) {
        fx fxVar;
        if (onActionExpandListener != null) {
            fxVar = new fx(this, onActionExpandListener);
        } else {
            fxVar = null;
        }
        this.c.setOnActionExpandListener(fxVar);
        return this;
    }

    public final MenuItem setOnMenuItemClickListener(MenuItem.OnMenuItemClickListener onMenuItemClickListener) {
        fy fyVar;
        if (onMenuItemClickListener != null) {
            fyVar = new fy(this, onMenuItemClickListener);
        } else {
            fyVar = null;
        }
        this.c.setOnMenuItemClickListener(fyVar);
        return this;
    }

    public final MenuItem setShortcut(char c2, char c3) {
        this.c.setShortcut(c2, c3);
        return this;
    }

    public final void setShowAsAction(int i) {
        this.c.setShowAsAction(i);
    }

    public final MenuItem setShowAsActionFlags(int i) {
        this.c.setShowAsActionFlags(i);
        return this;
    }

    public final MenuItem setTitle(int i) {
        this.c.setTitle(i);
        return this;
    }

    public final MenuItem setTitleCondensed(CharSequence charSequence) {
        this.c.setTitleCondensed(charSequence);
        return this;
    }

    public final MenuItem setTooltipText(CharSequence charSequence) {
        this.c.b(charSequence);
        return this;
    }

    public final MenuItem setVisible(boolean z) {
        ts tsVar = this.c;
        tsVar.setVisible(z);
        return tsVar;
    }

    public final MenuItem setAlphabeticShortcut(char c2, int i) {
        this.c.setAlphabeticShortcut(c2, i);
        return this;
    }

    public final MenuItem setIcon(Drawable drawable) {
        this.c.setIcon(drawable);
        return this;
    }

    public final MenuItem setNumericShortcut(char c2, int i) {
        this.c.setNumericShortcut(c2, i);
        return this;
    }

    public final MenuItem setShortcut(char c2, char c3, int i, int i2) {
        this.c.setShortcut(c2, c3, i, i2);
        return this;
    }

    public final MenuItem setTitle(CharSequence charSequence) {
        this.c.setTitle(charSequence);
        return this;
    }

    public final MenuItem setActionView(View view) {
        if (view instanceof CollapsibleActionView) {
            view = new fw(view);
        }
        this.c.setActionView(view);
        return this;
    }
}
