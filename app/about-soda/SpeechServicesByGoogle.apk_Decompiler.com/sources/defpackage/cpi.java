package defpackage;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/* renamed from: cpi  reason: default package */
/* compiled from: PG */
public final class cpi implements ThreadFactory {
    private final cpj a;
    private final cpl b;
    private final AtomicInteger c = new AtomicInteger(1000);

    public cpi(cpl cpl, ThreadFactory threadFactory) {
        this.b = cpl;
        this.a = new cpj(threadFactory);
    }

    public final Thread newThread(Runnable runnable) {
        int size;
        Thread newThread = this.a.newThread(runnable);
        if (cpk.b()) {
            cpj cpj = this.a;
            synchronized (cpj.a) {
                size = cpj.a.size();
            }
            if (size >= 1000) {
                while (true) {
                    int i = this.c.get();
                    if (size < i) {
                        break;
                    } else if (this.c.compareAndSet(i, i + i)) {
                        cpk.a(this.b, this.a.a(), new cpo(a.am(size, "Number of blocking threads ", " exceeds starvation threshold of 1000")));
                    }
                }
            }
        }
        return newThread;
    }
}
