package defpackage;

import android.net.Uri;
import java.io.IOException;
import java.io.InputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

/* renamed from: cxt  reason: default package */
/* compiled from: PG */
public final class cxt {
    private static final char[] a = {'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'a', 'b', 'c', 'd', 'e', 'f'};

    public static String a(byte[] bArr) {
        int length = bArr.length;
        char[] cArr = new char[(length + length)];
        int i = 0;
        for (byte b : bArr) {
            char[] cArr2 = a;
            cArr[i] = cArr2[(b & 255) >>> 4];
            cArr[i + 1] = cArr2[b & 15];
            i += 2;
        }
        return new String(cArr);
    }

    public static MessageDigest b() {
        try {
            MessageDigest instance = MessageDigest.getInstance("SHA1");
            if (instance != null) {
                return instance;
            }
            return null;
        } catch (NoSuchAlgorithmException unused) {
            return null;
        }
    }

    public static void c(kjd kjd, csv csv, Uri uri, String str) {
        long j;
        try {
            if (kjd.j(uri)) {
                int A = a.A(csv.e);
                if (A != 0) {
                    if (A == 2) {
                        return;
                    }
                }
                if (!d(kjd, uri, str)) {
                    try {
                        j = kjd.c(uri);
                    } catch (IOException unused) {
                        j = -1;
                    }
                    cyh.i("%s: Downloaded file at uri = %s, checksum = %s, size = %s verification failed", "FileValidator", uri, str, Long.valueOf(j));
                    kml a2 = csi.a();
                    a2.b = csh.DOWNLOADED_FILE_CHECKSUM_MISMATCH_ERROR;
                    throw a2.a();
                }
                return;
            }
            cyh.i("%s: Downloaded file %s is not present at %s", "FileValidator", cqx.k(csv), uri);
            kml a3 = csi.a();
            a3.b = csh.DOWNLOADED_FILE_NOT_FOUND_ERROR;
            throw a3.a();
        } catch (IOException e) {
            cyh.j(e, "%s: Failed to validate download file %s", "FileValidator", cqx.k(csv));
            kml a4 = csi.a();
            a4.b = csh.UNABLE_TO_VALIDATE_DOWNLOAD_FILE_ERROR;
            a4.d = e;
            throw a4.a();
        }
    }

    public static boolean d(kjd kjd, Uri uri, String str) {
        InputStream inputStream;
        String str2;
        String str3 = "";
        try {
            inputStream = (InputStream) kjd.e(uri, new foq());
            MessageDigest b = b();
            if (b == null) {
                str2 = str3;
            } else {
                byte[] bArr = new byte[8192];
                for (int read = inputStream.read(bArr); read != -1; read = inputStream.read(bArr)) {
                    b.update(bArr, 0, read);
                }
                str2 = a(b.digest());
            }
            if (inputStream != null) {
                inputStream.close();
            }
            str3 = str2;
        } catch (IOException unused) {
            cyh.h("%s: Failed to open file, uri = %s", "FileValidator", uri);
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        return str3.equals(str);
        throw th;
    }
}
