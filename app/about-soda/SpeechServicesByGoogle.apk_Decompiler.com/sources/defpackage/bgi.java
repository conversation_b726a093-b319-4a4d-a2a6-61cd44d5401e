package defpackage;

/* renamed from: bgi  reason: default package */
/* compiled from: PG */
final class bgi extends aub {
    public bgi(aus aus) {
        super(aus);
    }

    /* access modifiers changed from: protected */
    public final String a() {
        return "INSERT OR REPLACE INTO `Preference` (`key`,`long_value`) VALUES (?,?)";
    }

    /* access modifiers changed from: protected */
    public final /* bridge */ /* synthetic */ void c(axc axc, Object obj) {
        bgg bgg = (bgg) obj;
        axc.g(1, bgg.a);
        axc.e(2, bgg.b.longValue());
    }
}
