package defpackage;

/* renamed from: eti  reason: default package */
/* compiled from: PG */
public final class eti extends htq implements hvb {
    public static final eti c;
    private static volatile hvh e;
    public ebf a;
    public dzn b;
    private int d;

    static {
        eti eti = new eti();
        c = eti;
        htq.z(eti.class, eti);
    }

    private eti() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(c, "\u0004\u0002\u0000\u0001\u0001\u0002\u0002\u0000\u0000\u0000\u0001ဉ\u0000\u0002ဉ\u0001", new Object[]{"d", "a", "b"});
        } else if (i2 == 3) {
            return new eti();
        } else {
            if (i2 == 4) {
                return new htk((htq) c);
            }
            if (i2 == 5) {
                return c;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = e;
            if (hvh == null) {
                synchronized (eti.class) {
                    hvh = e;
                    if (hvh == null) {
                        hvh = new htl(c);
                        e = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
