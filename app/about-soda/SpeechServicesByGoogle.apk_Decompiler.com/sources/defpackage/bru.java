package defpackage;

import java.util.Locale;
import java.util.MissingResourceException;

/* renamed from: bru  reason: default package */
/* compiled from: PG */
public final class bru extends brw {
    public bru(Locale locale) {
        super(locale);
    }

    public static grh b(Locale locale) {
        try {
            return grh.h(new bru(new Locale(locale.getISO3Language(), locale.getISO3Country())));
        } catch (MissingResourceException unused) {
            return gqd.a;
        }
    }

    /* access modifiers changed from: protected */
    public final void a() {
        if (this.b.getLanguage().length() == 3) {
            String country = this.b.getCountry();
            if (country.length() != 3 && country.length() != 0) {
                throw new IllegalArgumentException("Expected an iso3 country code but got:".concat(String.valueOf(this.b.getCountry())));
            }
            return;
        }
        throw new IllegalArgumentException("Expected an iso3 language code but got:".concat(String.valueOf(this.b.getLanguage())));
    }
}
