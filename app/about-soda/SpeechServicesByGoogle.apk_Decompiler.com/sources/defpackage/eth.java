package defpackage;

/* renamed from: eth  reason: default package */
/* compiled from: PG */
public final class eth extends htq implements hvb {
    public static final eth b;
    private static volatile hvh d;
    public eab a;
    private int c;

    static {
        eth eth = new eth();
        b = eth;
        htq.z(eth.class, eth);
    }

    private eth() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(b, "\u0004\u0001\u0000\u0001\u0003\u0003\u0001\u0000\u0000\u0000\u0003ဉ\u0000", new Object[]{"c", "a"});
        } else if (i2 == 3) {
            return new eth();
        } else {
            if (i2 == 4) {
                return new htk((htq) b);
            }
            if (i2 == 5) {
                return b;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = d;
            if (hvh == null) {
                synchronized (eth.class) {
                    hvh = d;
                    if (hvh == null) {
                        hvh = new htl(b);
                        d = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
