package defpackage;

import android.text.TextUtils;
import androidx.wear.ambient.AmbientLifecycleObserverKt;
import androidx.wear.ambient.AmbientMode;
import androidx.wear.ambient.AmbientModeSupport;
import java.util.ArrayList;
import java.util.List;

/* renamed from: bcy  reason: default package */
/* compiled from: PG */
public final class bcy extends AmbientModeSupport.AmbientCallback {
    private static final String g = bbk.b("WorkContinuationImpl");
    public final bdm a;
    public final String b;
    public final List c;
    public final List d;
    public boolean e;
    public final int f;
    private final List h;
    private bbr i;

    public bcy(bdm bdm, String str, int i2, List list) {
        this(bdm, str, i2, list, (byte[]) null);
    }

    /* JADX WARNING: type inference failed for: r0v5, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final bbr e() {
        String str;
        if (!this.e) {
            bdm bdm = this.a;
            AmbientLifecycleObserverKt ambientLifecycleObserverKt = bdm.c.p;
            if (this.f != 1) {
                str = "KEEP";
            } else {
                str = "REPLACE";
            }
            this.i = AmbientMode.AmbientCallback.b(ambientLifecycleObserverKt, "EnqueueRunnable_".concat(str), bdm.k.a, new bdl(this, 1));
        } else {
            bbk a2 = bbk.a();
            String str2 = g;
            a2.f(str2, "Already enqueued work ids (" + TextUtils.join(", ", this.d) + ")");
        }
        return this.i;
    }

    public bcy(bdm bdm, String str, int i2, List list, byte[] bArr) {
        super((byte[]) null);
        this.a = bdm;
        this.b = str;
        this.f = i2;
        this.c = list;
        this.d = new ArrayList(list.size());
        this.h = new ArrayList();
        int i3 = 0;
        while (i3 < list.size()) {
            if (i2 != 1 || ((bhe) ((bmu) list.get(i3)).b).v == Long.MAX_VALUE) {
                String a2 = ((bmu) list.get(i3)).a();
                this.d.add(a2);
                this.h.add(a2);
                i3++;
            } else {
                throw new IllegalArgumentException("Next Schedule Time Override must be used with ExistingPeriodicWorkPolicyUPDATE (preferably) or KEEP");
            }
        }
    }
}
