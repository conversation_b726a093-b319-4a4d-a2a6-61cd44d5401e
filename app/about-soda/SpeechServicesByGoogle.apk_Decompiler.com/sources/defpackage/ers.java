package defpackage;

/* renamed from: ers  reason: default package */
/* compiled from: PG */
final class ers extends jme {
    /* synthetic */ Object a;
    final /* synthetic */ eru b;
    int c;
    eru d;
    dzx e;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public ers(eru eru, jlr jlr) {
        super(jlr);
        this.b = eru;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.c |= Integer.MIN_VALUE;
        return this.b.g((dzx) null, this);
    }
}
