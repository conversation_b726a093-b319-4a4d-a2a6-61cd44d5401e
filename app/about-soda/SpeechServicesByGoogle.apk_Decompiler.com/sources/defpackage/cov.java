package defpackage;

import java.util.concurrent.Callable;

/* renamed from: cov  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cov implements Runnable {
    public final /* synthetic */ hmr a;
    public final /* synthetic */ Callable b;

    public /* synthetic */ cov(hmr hmr, Callable callable) {
        this.a = hmr;
        this.b = callable;
    }

    public final void run() {
        int i = coy.b;
        hmr hmr = this.a;
        try {
            hmr.m(this.b.call());
        } catch (Exception e) {
            hmr.n(e);
            throw new RuntimeException(e);
        }
    }
}
