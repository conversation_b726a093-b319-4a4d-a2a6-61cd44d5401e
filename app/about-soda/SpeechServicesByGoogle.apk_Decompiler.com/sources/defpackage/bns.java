package defpackage;

import android.app.AlertDialog;
import android.util.Log;

/* renamed from: bns  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bns implements bmk {
    public final /* synthetic */ bnu a;

    public /* synthetic */ bns(bnu bnu) {
        this.a = bnu;
    }

    public final void a(bml bml) {
        AlertDialog alertDialog = this.a.i;
        if (alertDialog != null) {
            alertDialog.show();
        } else if (Log.isLoggable("CarUiToolbarController", 6)) {
            Log.e("CarUiToolbarController", "Overflow dialog was null when trying to show it!");
        }
    }
}
