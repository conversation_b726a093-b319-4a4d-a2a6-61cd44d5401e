package defpackage;

/* renamed from: eru  reason: default package */
/* compiled from: PG */
public final class eru implements erq {
    private static final hca a = hca.m("com/google/android/libraries/search/audio/routing/impl/AudioAdapterAudioRoute");
    private final ecg b;
    private final ejn c;
    private final ekt d;
    private final jqh e;
    private final eoz f;
    private final bzl g;

    public eru(dzq dzq, ecg ecg, grh grh, bzl bzl, jqs jqs) {
        this.b = ecg;
        this.g = bzl;
        this.f = don.k(jqs);
        jqh jqh = new jqh();
        this.e = jqh;
        this.c = new ejn(jqw.w(jqh), dzq, grh, gqd.a);
        this.d = doe.o(ecg);
    }

    public final ejn a() {
        return this.c;
    }

    public final hme b() {
        return this.f.o(new edc(this, (jlr) null, 16));
    }

    public final hme c(dzx dzx) {
        jnu.e(dzx, "disconnectReason");
        return this.f.o(new egv(this, dzx, (jlr) null, 4));
    }

    public final ekt d() {
        return this.d;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:23:0x00a4, code lost:
        if (h(r11, r0) == r1) goto L_0x0110;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:32:0x00e0, code lost:
        if (r2.h(r11, r0) != r1) goto L_0x00e2;
     */
    /* JADX WARNING: Removed duplicated region for block: B:16:0x0043  */
    /* JADX WARNING: Removed duplicated region for block: B:31:0x00d5  */
    /* JADX WARNING: Removed duplicated region for block: B:35:0x00f9  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0025  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object f(defpackage.jlr r11) {
        /*
            r10 = this;
            boolean r0 = r11 instanceof defpackage.err
            if (r0 == 0) goto L_0x0013
            r0 = r11
            err r0 = (defpackage.err) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            err r0 = new err
            r0.<init>(r10, r11)
        L_0x0018:
            java.lang.Object r11 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            r3 = 3
            r4 = 2
            r5 = 1
            java.lang.String r6 = "newBuilder(...)"
            if (r2 == 0) goto L_0x0043
            if (r2 == r5) goto L_0x003f
            if (r2 == r4) goto L_0x0038
            if (r2 != r3) goto L_0x0030
            defpackage.jji.c(r11)
            goto L_0x00e2
        L_0x0030:
            java.lang.IllegalStateException r11 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r11.<init>(r0)
            throw r11
        L_0x0038:
            eru r2 = r0.d
            defpackage.jji.c(r11)
            goto L_0x00cf
        L_0x003f:
            defpackage.jji.c(r11)
            goto L_0x00a7
        L_0x0043:
            defpackage.jji.c(r11)
            hca r11 = a
            hco r11 = r11.f()
            hcr r2 = defpackage.hdg.a
            java.lang.String r7 = "ALT.AdapterRoute"
            hco r11 = r11.h(r2, r7)
            java.lang.String r2 = "connectInternal"
            r7 = 63
            java.lang.String r8 = "com/google/android/libraries/search/audio/routing/impl/AudioAdapterAudioRoute"
            java.lang.String r9 = "AudioAdapterAudioRoute.kt"
            hco r11 = r11.j(r8, r2, r7, r9)
            hby r11 = (defpackage.hby) r11
            ecg r2 = r10.b
            java.lang.String r7 = "#audio# connect to adapter(%s)"
            java.lang.String r2 = r2.name()
            r11.u(r7, r2)
            jqh r11 = r10.e
            boolean r11 = r11.bI()
            if (r11 == 0) goto L_0x008c
            dzo r11 = defpackage.dzo.c
            htk r11 = r11.l()
            defpackage.jnu.d(r11, r6)
            dku r11 = defpackage.jnu.e(r11, "builder")
            eaf r0 = defpackage.eaf.FAILED_ROUTING_DUE_TO_DISCONNECT_ALREADY_CALLED
            r11.i(r0)
            dzo r11 = r11.h()
            return r11
        L_0x008c:
            bzl r11 = r10.g
            ecg r2 = r10.b
            grh r11 = r11.p(r2)
            java.lang.Object r11 = r11.e()
            eck r11 = (defpackage.eck) r11
            if (r11 != 0) goto L_0x00be
            dzx r11 = defpackage.dzx.DISCONNECT_REASON_AUDIO_ADAPTER_NOT_REGISTERED
            r0.c = r5
            java.lang.Object r11 = r10.h(r11, r0)
            if (r11 != r1) goto L_0x00a7
            goto L_0x0110
        L_0x00a7:
            dzo r11 = defpackage.dzo.c
            htk r11 = r11.l()
            defpackage.jnu.d(r11, r6)
            dku r11 = defpackage.jnu.e(r11, "builder")
            eaf r0 = defpackage.eaf.FAILED_ROUTING_DUE_TO_AUDIO_ADAPTER_NOT_REGISTERED
            r11.i(r0)
            dzo r11 = r11.h()
            return r11
        L_0x00be:
            ece r11 = defpackage.ece.AUDIO_ADAPTER_CONNECT_STATUS_SUCCESS
            hme r11 = defpackage.hfc.K(r11)
            r0.d = r10
            r0.c = r4
            java.lang.Object r11 = defpackage.jqw.x(r11, r0)
            if (r11 == r1) goto L_0x0110
            r2 = r10
        L_0x00cf:
            ece r11 = (defpackage.ece) r11
            ece r4 = defpackage.ece.AUDIO_ADAPTER_CONNECT_STATUS_SUCCESS
            if (r11 == r4) goto L_0x00f9
            dzx r11 = defpackage.dzx.DISCONNECT_REASON_FAILED_CONNECT_TO_AUDIO_ADAPTER
            r4 = 0
            r0.d = r4
            r0.c = r3
            java.lang.Object r11 = r2.h(r11, r0)
            if (r11 == r1) goto L_0x0110
        L_0x00e2:
            dzo r11 = defpackage.dzo.c
            htk r11 = r11.l()
            defpackage.jnu.d(r11, r6)
            dku r11 = defpackage.jnu.e(r11, "builder")
            eaf r0 = defpackage.eaf.FAILED_ROUTING_DUE_TO_CANNOT_CONNECT_TO_AUDIO_ADAPTER
            r11.i(r0)
            dzo r11 = r11.h()
            return r11
        L_0x00f9:
            dzo r11 = defpackage.dzo.c
            htk r11 = r11.l()
            defpackage.jnu.d(r11, r6)
            dku r11 = defpackage.jnu.e(r11, "builder")
            eaf r0 = defpackage.eaf.UPDATED
            r11.i(r0)
            dzo r11 = r11.h()
            return r11
        L_0x0110:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eru.f(jlr):java.lang.Object");
    }

    /* JADX WARNING: Removed duplicated region for block: B:12:0x003d  */
    /* JADX WARNING: Removed duplicated region for block: B:19:0x008c  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x002b  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object g(defpackage.dzx r13, defpackage.jlr r14) {
        /*
            r12 = this;
            boolean r0 = r14 instanceof defpackage.ers
            if (r0 == 0) goto L_0x0013
            r0 = r14
            ers r0 = (defpackage.ers) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            ers r0 = new ers
            r0.<init>(r12, r14)
        L_0x0018:
            java.lang.Object r14 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            java.lang.String r3 = "newBuilder(...)"
            r4 = 1
            java.lang.String r5 = "disconnectInternal"
            java.lang.String r6 = "com/google/android/libraries/search/audio/routing/impl/AudioAdapterAudioRoute"
            java.lang.String r7 = "AudioAdapterAudioRoute.kt"
            java.lang.String r8 = "ALT.AdapterRoute"
            if (r2 == 0) goto L_0x003d
            if (r2 != r4) goto L_0x0035
            dzx r13 = r0.e
            eru r0 = r0.d
            defpackage.jji.c(r14)
            goto L_0x0086
        L_0x0035:
            java.lang.IllegalStateException r13 = new java.lang.IllegalStateException
            java.lang.String r14 = "call to 'resume' before 'invoke' with coroutine"
            r13.<init>(r14)
            throw r13
        L_0x003d:
            defpackage.jji.c(r14)
            hca r14 = a
            hco r2 = r14.f()
            hcr r9 = defpackage.hdg.a
            hco r2 = r2.h(r9, r8)
            r9 = 104(0x68, float:1.46E-43)
            hco r2 = r2.j(r6, r5, r9, r7)
            hby r2 = (defpackage.hby) r2
            java.lang.String r9 = r13.name()
            ecg r10 = r12.b
            java.lang.String r11 = "#audio# disconnect(%s) adapter(%s)"
            java.lang.String r10 = r10.name()
            r2.C(r11, r9, r10)
            bzl r2 = r12.g
            ecg r9 = r12.b
            grh r2 = r2.p(r9)
            java.lang.Object r2 = r2.e()
            eck r2 = (defpackage.eck) r2
            if (r2 == 0) goto L_0x00c5
            ecf r14 = defpackage.ecf.AUDIO_ADAPTER_DISCONNECT_STATUS_SUCCESS
            hme r14 = defpackage.hfc.K(r14)
            r0.d = r12
            r0.e = r13
            r0.c = r4
            java.lang.Object r14 = defpackage.jqw.x(r14, r0)
            if (r14 == r1) goto L_0x00c4
            r0 = r12
        L_0x0086:
            ecf r14 = (defpackage.ecf) r14
            ecf r1 = defpackage.ecf.AUDIO_ADAPTER_DISCONNECT_STATUS_SUCCESS
            if (r14 == r1) goto L_0x00af
            hca r1 = a
            hco r1 = r1.f()
            hcr r2 = defpackage.hdg.a
            hco r1 = r1.h(r2, r8)
            r2 = 117(0x75, float:1.64E-43)
            hco r1 = r1.j(r6, r5, r2, r7)
            hby r1 = (defpackage.hby) r1
            ecg r0 = r0.b
            java.lang.String r0 = r0.name()
            java.lang.String r14 = r14.name()
            java.lang.String r2 = "#audio# adapter(%s) disconnected unsuccessfully(%s)"
            r1.C(r2, r0, r14)
        L_0x00af:
            dzm r14 = defpackage.dzm.c
            htk r14 = r14.l()
            defpackage.jnu.d(r14, r3)
            dku r14 = defpackage.jnu.e(r14, "builder")
            r14.m(r13)
            dzm r13 = r14.l()
            return r13
        L_0x00c4:
            return r1
        L_0x00c5:
            hco r14 = r14.f()
            hcr r0 = defpackage.hdg.a
            hco r14 = r14.h(r0, r8)
            r0 = 110(0x6e, float:1.54E-43)
            hco r14 = r14.j(r6, r5, r0, r7)
            hby r14 = (defpackage.hby) r14
            ecg r0 = r12.b
            java.lang.String r1 = "#audio# no adapter(%s) found for disconnection"
            java.lang.String r0 = r0.name()
            r14.u(r1, r0)
            dzm r14 = defpackage.dzm.c
            htk r14 = r14.l()
            defpackage.jnu.d(r14, r3)
            dku r14 = defpackage.jnu.e(r14, "builder")
            r14.m(r13)
            dzm r13 = r14.l()
            return r13
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eru.g(dzx, jlr):java.lang.Object");
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x003c  */
    /* JADX WARNING: Removed duplicated region for block: B:26:0x0075 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0023  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object h(defpackage.dzx r7, defpackage.jlr r8) {
        /*
            r6 = this;
            boolean r0 = r8 instanceof defpackage.ert
            if (r0 == 0) goto L_0x0013
            r0 = r8
            ert r0 = (defpackage.ert) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            ert r0 = new ert
            r0.<init>(r6, r8)
        L_0x0018:
            java.lang.Object r8 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            r3 = 3
            r4 = 2
            r5 = 1
            if (r2 == 0) goto L_0x003c
            if (r2 == r5) goto L_0x0038
            if (r2 == r4) goto L_0x0032
            if (r2 != r3) goto L_0x002a
            goto L_0x0038
        L_0x002a:
            java.lang.IllegalStateException r7 = new java.lang.IllegalStateException
            java.lang.String r8 = "call to 'resume' before 'invoke' with coroutine"
            r7.<init>(r8)
            throw r7
        L_0x0032:
            eru r7 = r0.d
            defpackage.jji.c(r8)
            goto L_0x005f
        L_0x0038:
            defpackage.jji.c(r8)
            goto L_0x0074
        L_0x003c:
            defpackage.jji.c(r8)
            jqh r8 = r6.e
            boolean r8 = r8.bI()
            if (r8 == 0) goto L_0x0053
            jqh r7 = r6.e
            r0.c = r5
            java.lang.Object r7 = r7.y(r0)
            if (r7 != r1) goto L_0x0052
            goto L_0x0073
        L_0x0052:
            return r7
        L_0x0053:
            r0.d = r6
            r0.c = r4
            java.lang.Object r8 = r6.g(r7, r0)
            if (r8 != r1) goto L_0x005e
            goto L_0x0073
        L_0x005e:
            r7 = r6
        L_0x005f:
            dzm r8 = (defpackage.dzm) r8
            jqh r2 = r7.e
            r2.O(r8)
            jqh r7 = r7.e
            r8 = 0
            r0.d = r8
            r0.c = r3
            java.lang.Object r7 = r7.y(r0)
            if (r7 != r1) goto L_0x0075
        L_0x0073:
            r8 = r1
        L_0x0074:
            return r8
        L_0x0075:
            return r7
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eru.h(dzx, jlr):java.lang.Object");
    }

    public final /* synthetic */ erq e() {
        return this;
    }
}
