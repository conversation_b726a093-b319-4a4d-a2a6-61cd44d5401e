package defpackage;

import java.util.Map;
import java.util.concurrent.Executor;

/* renamed from: eok  reason: default package */
/* compiled from: PG */
public final class eok implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;
    private final jjk e;
    private final jjk f;
    private final jjk g;
    private final jjk h;
    private final jjk i;
    private final /* synthetic */ int j;

    public eok(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, jjk jjk9, int i2) {
        this.j = i2;
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
        this.e = jjk5;
        this.f = jjk6;
        this.g = jjk7;
        this.h = jjk8;
        this.i = jjk9;
    }

    public final /* synthetic */ Object b() {
        int i2 = this.j;
        if (i2 == 0) {
            jjk jjk = this.f;
            return new eoj((dvx) this.a.b(), (elm) this.b.b(), (emr) this.c.b(), ((eme) this.d).b(), ((eox) this.e).b(), (eoz) jjk.b(), ((epo) this.g).b(), (jqs) this.h.b(), ((ijj) this.i).a().booleanValue());
        } else if (i2 == 1) {
            jjk jjk2 = this.h;
            jjk jjk3 = this.a;
            jjk jjk4 = this.c;
            jjk jjk5 = this.b;
            return new dpt(((dme) this.e).b(), ((iim) jjk3).a(), (dkq) jjk2.b(), (hmi) this.i.b(), iit.c(this.d), iit.c(jjk5), this.f, (Executor) jjk4.b(), (cxj) this.g.b());
        } else if (i2 == 2) {
            jjk jjk6 = this.f;
            return new eor((dvx) this.a.b(), (elm) this.b.b(), (emr) this.c.b(), ((eme) this.d).b(), ((eox) this.e).b(), (eoz) jjk6.b(), ((epo) this.g).b(), (jqs) this.h.b(), ((ijj) this.i).a().booleanValue());
        } else if (i2 == 3) {
            jjk jjk7 = this.f;
            jjk jjk8 = this.h;
            jjk jjk9 = this.g;
            jjk jjk10 = this.d;
            bzj a2 = ((esb) this.i).b();
            dsy a3 = ((erv) jjk10).b();
            return new esr((dvx) this.b.b(), ((eme) this.c).b(), (eoz) jjk7.b(), a2, a3, ((epa) jjk9).b(), (Executor) jjk8.b(), (hll) this.a.b(), ((esf) this.e).b());
        } else if (i2 != 4) {
            jjk jjk11 = this.d;
            Object b2 = this.c.b();
            cqx cqx = (cqx) jjk11.b();
            glm glm = glm.a;
            hzz.u(glm);
            return new gnw((gly) b2, (hmi) this.a.b(), this.h, (grh) ((iiv) this.g).a, glm, (Map) ((iiv) this.i).a, (Map) ((iiv) this.e).a, (grh) ((iiv) this.b).a, (grh) ((iiv) this.f).a);
        } else {
            jjk jjk12 = this.b;
            jjk jjk13 = this.a;
            Object b3 = this.f.b();
            return new fjr((alx) this.h.b(), ((fcn) this.c).b(), ((gbu) jjk13).b(), (bmu) jjk12.b(), (gnk) b3, new ffu(), (hmi) this.i.b(), (fps) this.e.b(), ((fkk) this.g).b(), (cqx) this.d.b());
        }
    }

    public eok(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, jjk jjk9, int i2, byte[] bArr) {
        this.j = i2;
        this.e = jjk;
        this.a = jjk2;
        this.h = jjk3;
        this.i = jjk4;
        this.d = jjk5;
        this.b = jjk6;
        this.f = jjk7;
        this.c = jjk8;
        this.g = jjk9;
    }

    public eok(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, jjk jjk9, int i2, int[] iArr) {
        this.j = i2;
        this.h = jjk;
        this.c = jjk2;
        this.a = jjk3;
        this.b = jjk4;
        this.f = jjk5;
        this.i = jjk6;
        this.e = jjk7;
        this.g = jjk8;
        this.d = jjk9;
    }

    public eok(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, jjk jjk9, int i2, short[] sArr) {
        this.j = i2;
        this.b = jjk;
        this.c = jjk2;
        this.f = jjk3;
        this.i = jjk4;
        this.d = jjk5;
        this.g = jjk6;
        this.h = jjk7;
        this.a = jjk8;
        this.e = jjk9;
    }

    public eok(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, jjk jjk9, int i2, boolean[] zArr) {
        this.j = i2;
        this.c = jjk;
        this.d = jjk2;
        this.a = jjk3;
        this.h = jjk4;
        this.g = jjk5;
        this.i = jjk6;
        this.e = jjk7;
        this.b = jjk8;
        this.f = jjk9;
    }
}
