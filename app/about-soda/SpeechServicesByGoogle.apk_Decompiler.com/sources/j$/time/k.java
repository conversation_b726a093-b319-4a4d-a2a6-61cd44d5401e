package j$.time;

import j$.time.chrono.C0010i;
import j$.time.temporal.ChronoUnit;
import j$.time.temporal.TemporalUnit;
import j$.time.temporal.a;
import j$.time.temporal.l;
import j$.time.temporal.m;
import j$.time.temporal.n;
import j$.time.temporal.p;
import j$.time.temporal.r;
import j$.time.temporal.t;
import j$.util.Objects;
import java.io.DataOutput;
import java.io.InvalidObjectException;
import java.io.ObjectInput;
import java.io.ObjectInputStream;
import java.io.Serializable;

public final class k implements j$.time.temporal.k, n, Comparable, Serializable {
    public static final k e;
    public static final k f = new k(23, 59, 59, 999999999);
    public static final k g;
    private static final k[] h = new k[24];
    private static final long serialVersionUID = 6414437269572265201L;
    private final byte a;
    private final byte b;
    private final byte c;
    private final int d;

    static {
        int i = 0;
        while (true) {
            k[] kVarArr = h;
            if (i < kVarArr.length) {
                kVarArr[i] = new k(i, 0, 0, 0);
                i++;
            } else {
                k kVar = kVarArr[0];
                g = kVar;
                k kVar2 = kVarArr[12];
                e = kVar;
                return;
            }
        }
    }

    private k(int i, int i2, int i3, int i4) {
        this.a = (byte) i;
        this.b = (byte) i2;
        this.c = (byte) i3;
        this.d = i4;
    }

    private static k J(int i, int i2, int i3, int i4) {
        if ((i2 | i3 | i4) == 0) {
            return h[i];
        }
        return new k(i, i2, i3, i4);
    }

    public static k K(m mVar) {
        Objects.requireNonNull(mVar, "temporal");
        k kVar = (k) mVar.F(l.g());
        if (kVar != null) {
            return kVar;
        }
        String valueOf = String.valueOf(mVar);
        String name = mVar.getClass().getName();
        throw new RuntimeException("Unable to obtain LocalTime from TemporalAccessor: " + valueOf + " of type " + name);
    }

    private int L(p pVar) {
        int i = j.a[((a) pVar).ordinal()];
        byte b2 = this.b;
        int i2 = this.d;
        byte b3 = this.a;
        switch (i) {
            case 1:
                return i2;
            case 2:
                throw new RuntimeException("Invalid field 'NanoOfDay' for get() method, use getLong() instead");
            case 3:
                return i2 / 1000;
            case 4:
                throw new RuntimeException("Invalid field 'MicroOfDay' for get() method, use getLong() instead");
            case 5:
                return i2 / 1000000;
            case 6:
                return (int) (Y() / 1000000);
            case 7:
                return this.c;
            case 8:
                return Z();
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER:
                return b2;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER:
                return (b3 * 60) + b2;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER:
                return b3 % 12;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER:
                int i3 = b3 % 12;
                if (i3 % 12 == 0) {
                    return 12;
                }
                return i3;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER:
                return b3;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER:
                if (b3 == 0) {
                    return 24;
                }
                return b3;
            case 15:
                return b3 / 12;
            default:
                throw new RuntimeException("Unsupported field: ".concat(String.valueOf(pVar)));
        }
    }

    public static k P(int i) {
        a.HOUR_OF_DAY.I((long) i);
        return h[i];
    }

    public static k Q(long j) {
        a.NANO_OF_DAY.I(j);
        int i = (int) (j / 3600000000000L);
        long j2 = j - (((long) i) * 3600000000000L);
        int i2 = (int) (j2 / 60000000000L);
        long j3 = j2 - (((long) i2) * 60000000000L);
        int i3 = (int) (j3 / 1000000000);
        return J(i, i2, i3, (int) (j3 - (((long) i3) * 1000000000)));
    }

    public static k R(long j) {
        a.SECOND_OF_DAY.I(j);
        int i = (int) (j / 3600);
        long j2 = j - ((long) (i * 3600));
        int i2 = (int) (j2 / 60);
        return J(i, i2, (int) (j2 - ((long) (i2 * 60))), 0);
    }

    static k X(ObjectInput objectInput) {
        byte b2;
        int i;
        int readByte = objectInput.readByte();
        byte b3 = 0;
        if (readByte < 0) {
            readByte = ~readByte;
            b2 = 0;
        } else {
            byte readByte2 = objectInput.readByte();
            if (readByte2 < 0) {
                int i2 = ~readByte2;
                i = 0;
                b3 = i2;
                b2 = 0;
            } else {
                byte readByte3 = objectInput.readByte();
                if (readByte3 < 0) {
                    b2 = ~readByte3;
                    b3 = readByte2;
                } else {
                    int readInt = objectInput.readInt();
                    b2 = readByte3;
                    byte b4 = readByte2;
                    i = readInt;
                    b3 = b4;
                }
            }
            a.HOUR_OF_DAY.I((long) readByte);
            a.MINUTE_OF_HOUR.I((long) b3);
            a.SECOND_OF_MINUTE.I((long) b2);
            a.NANO_OF_SECOND.I((long) i);
            return J(readByte, b3, b2, i);
        }
        i = 0;
        a.HOUR_OF_DAY.I((long) readByte);
        a.MINUTE_OF_HOUR.I((long) b3);
        a.SECOND_OF_MINUTE.I((long) b2);
        a.NANO_OF_SECOND.I((long) i);
        return J(readByte, b3, b2, i);
    }

    private void readObject(ObjectInputStream objectInputStream) {
        throw new InvalidObjectException("Deserialization via serialization delegate");
    }

    private Object writeReplace() {
        return new s((byte) 4, this);
    }

    public final long B(p pVar) {
        if (!(pVar instanceof a)) {
            return pVar.g(this);
        }
        if (pVar == a.NANO_OF_DAY) {
            return Y();
        }
        if (pVar == a.MICRO_OF_DAY) {
            return Y() / 1000;
        }
        return (long) L(pVar);
    }

    public final Object F(r rVar) {
        if (rVar == l.e() || rVar == l.k() || rVar == l.j() || rVar == l.h()) {
            return null;
        }
        if (rVar == l.g()) {
            return this;
        }
        if (rVar == l.f()) {
            return null;
        }
        if (rVar == l.i()) {
            return ChronoUnit.NANOS;
        }
        return rVar.a(this);
    }

    /* renamed from: I */
    public final int compareTo(k kVar) {
        int compare = Integer.compare(this.a, kVar.a);
        if (compare != 0) {
            return compare;
        }
        int compare2 = Integer.compare(this.b, kVar.b);
        if (compare2 != 0) {
            return compare2;
        }
        int compare3 = Integer.compare(this.c, kVar.c);
        if (compare3 == 0) {
            return Integer.compare(this.d, kVar.d);
        }
        return compare3;
    }

    public final int M() {
        return this.a;
    }

    public final int N() {
        return this.d;
    }

    public final int O() {
        return this.c;
    }

    /* renamed from: S */
    public final k f(long j, TemporalUnit temporalUnit) {
        if (!(temporalUnit instanceof ChronoUnit)) {
            return (k) temporalUnit.m(this, j);
        }
        switch (j.b[((ChronoUnit) temporalUnit).ordinal()]) {
            case 1:
                return V(j);
            case 2:
                return V((j % 86400000000L) * 1000);
            case 3:
                return V((j % 86400000) * 1000000);
            case 4:
                return W(j);
            case 5:
                return U(j);
            case 6:
                return T(j);
            case 7:
                return T((j % 2) * 12);
            default:
                throw new RuntimeException("Unsupported unit: ".concat(String.valueOf(temporalUnit)));
        }
    }

    public final k T(long j) {
        if (j == 0) {
            return this;
        }
        return J(((((int) (j % 24)) + this.a) + 24) % 24, this.b, this.c, this.d);
    }

    public final k U(long j) {
        if (j == 0) {
            return this;
        }
        int i = (this.a * 60) + this.b;
        int i2 = ((((int) (j % 1440)) + i) + 1440) % 1440;
        if (i == i2) {
            return this;
        }
        return J(i2 / 60, i2 % 60, this.c, this.d);
    }

    public final k V(long j) {
        if (j == 0) {
            return this;
        }
        long Y = Y();
        long j2 = (((j % 86400000000000L) + Y) + 86400000000000L) % 86400000000000L;
        if (Y == j2) {
            return this;
        }
        return J((int) (j2 / 3600000000000L), (int) ((j2 / 60000000000L) % 60), (int) ((j2 / 1000000000) % 60), (int) (j2 % 1000000000));
    }

    public final k W(long j) {
        if (j == 0) {
            return this;
        }
        int i = (this.b * 60) + (this.a * 3600) + this.c;
        int i2 = ((((int) (j % 86400)) + i) + 86400) % 86400;
        if (i == i2) {
            return this;
        }
        return J(i2 / 3600, (i2 / 60) % 60, i2 % 60, this.d);
    }

    public final long Y() {
        return (((long) this.c) * 1000000000) + (((long) this.b) * 60000000000L) + (((long) this.a) * 3600000000000L) + ((long) this.d);
    }

    public final int Z() {
        return (this.b * 60) + (this.a * 3600) + this.c;
    }

    /* renamed from: a0 */
    public final k d(long j, p pVar) {
        if (!(pVar instanceof a)) {
            return (k) pVar.s(this, j);
        }
        a aVar = (a) pVar;
        aVar.I(j);
        int i = j.a[aVar.ordinal()];
        byte b2 = this.b;
        byte b3 = this.c;
        int i2 = this.d;
        byte b4 = this.a;
        switch (i) {
            case 1:
                return b0((int) j);
            case 2:
                return Q(j);
            case 3:
                return b0(((int) j) * 1000);
            case 4:
                return Q(j * 1000);
            case 5:
                return b0(((int) j) * 1000000);
            case 6:
                return Q(j * 1000000);
            case 7:
                int i3 = (int) j;
                if (b3 == i3) {
                    return this;
                }
                a.SECOND_OF_MINUTE.I((long) i3);
                return J(b4, b2, i3, i2);
            case 8:
                return W(j - ((long) Z()));
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER:
                int i4 = (int) j;
                if (b2 == i4) {
                    return this;
                }
                a.MINUTE_OF_HOUR.I((long) i4);
                return J(b4, i4, b3, i2);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER:
                return U(j - ((long) ((b4 * 60) + b2)));
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER:
                return T(j - ((long) (b4 % 12)));
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER:
                if (j == 12) {
                    j = 0;
                }
                return T(j - ((long) (b4 % 12)));
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER:
                int i5 = (int) j;
                if (b4 == i5) {
                    return this;
                }
                a.HOUR_OF_DAY.I((long) i5);
                return J(i5, b2, b3, i2);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER:
                if (j == 24) {
                    j = 0;
                }
                int i6 = (int) j;
                if (b4 == i6) {
                    return this;
                }
                a.HOUR_OF_DAY.I((long) i6);
                return J(i6, b2, b3, i2);
            case 15:
                return T((j - ((long) (b4 / 12))) * 12);
            default:
                throw new RuntimeException("Unsupported field: ".concat(String.valueOf(pVar)));
        }
    }

    public final k b0(int i) {
        if (this.d == i) {
            return this;
        }
        a.NANO_OF_SECOND.I((long) i);
        return J(this.a, this.b, this.c, i);
    }

    /* access modifiers changed from: package-private */
    public final void c0(DataOutput dataOutput) {
        byte b2 = this.c;
        byte b3 = this.a;
        byte b4 = this.b;
        int i = this.d;
        if (i != 0) {
            dataOutput.writeByte(b3);
            dataOutput.writeByte(b4);
            dataOutput.writeByte(b2);
            dataOutput.writeInt(i);
        } else if (b2 != 0) {
            dataOutput.writeByte(b3);
            dataOutput.writeByte(b4);
            dataOutput.writeByte(~b2);
        } else if (b4 == 0) {
            dataOutput.writeByte(~b3);
        } else {
            dataOutput.writeByte(b3);
            dataOutput.writeByte(~b4);
        }
    }

    public final boolean e(p pVar) {
        if (pVar instanceof a) {
            return ((a) pVar).J();
        }
        if (pVar == null || !pVar.p(this)) {
            return false;
        }
        return true;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:5:0x0009, code lost:
        r5 = (j$.time.k) r5;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean equals(java.lang.Object r5) {
        /*
            r4 = this;
            r0 = 1
            if (r4 != r5) goto L_0x0004
            return r0
        L_0x0004:
            boolean r1 = r5 instanceof j$.time.k
            r2 = 0
            if (r1 == 0) goto L_0x0026
            j$.time.k r5 = (j$.time.k) r5
            byte r1 = r5.a
            byte r3 = r4.a
            if (r3 != r1) goto L_0x0024
            byte r1 = r4.b
            byte r3 = r5.b
            if (r1 != r3) goto L_0x0024
            byte r1 = r4.c
            byte r3 = r5.c
            if (r1 != r3) goto L_0x0024
            int r1 = r4.d
            int r5 = r5.d
            if (r1 != r5) goto L_0x0024
            goto L_0x0025
        L_0x0024:
            r0 = 0
        L_0x0025:
            return r0
        L_0x0026:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: j$.time.k.equals(java.lang.Object):boolean");
    }

    public final j$.time.temporal.k g(j$.time.temporal.k kVar) {
        return kVar.d(Y(), a.NANO_OF_DAY);
    }

    public final int hashCode() {
        long Y = Y();
        return (int) (Y ^ (Y >>> 32));
    }

    public final j$.time.temporal.k m(long j, ChronoUnit chronoUnit) {
        if (j == Long.MIN_VALUE) {
            return f(Long.MAX_VALUE, chronoUnit).f(1, chronoUnit);
        }
        return f(-j, chronoUnit);
    }

    public final int p(p pVar) {
        if (pVar instanceof a) {
            return L(pVar);
        }
        return l.a(this, pVar);
    }

    public final j$.time.temporal.k s(g gVar) {
        return (k) C0010i.a(gVar, this);
    }

    public final t t(p pVar) {
        return l.d(this, pVar);
    }

    public final String toString() {
        String str;
        String str2;
        StringBuilder sb = new StringBuilder(18);
        byte b2 = this.a;
        if (b2 < 10) {
            str = "0";
        } else {
            str = "";
        }
        sb.append(str);
        sb.append(b2);
        String str3 = ":";
        byte b3 = this.b;
        if (b3 < 10) {
            str2 = ":0";
        } else {
            str2 = str3;
        }
        sb.append(str2);
        sb.append(b3);
        byte b4 = this.c;
        int i = this.d;
        if (b4 > 0 || i > 0) {
            if (b4 < 10) {
                str3 = ":0";
            }
            sb.append(str3);
            sb.append(b4);
            if (i > 0) {
                sb.append('.');
                if (i % 1000000 == 0) {
                    sb.append(Integer.toString((i / 1000000) + 1000).substring(1));
                } else if (i % 1000 == 0) {
                    sb.append(Integer.toString((i / 1000) + 1000000).substring(1));
                } else {
                    sb.append(Integer.toString(i + 1000000000).substring(1));
                }
            }
        }
        return sb.toString();
    }
}
