package defpackage;

import android.content.Context;
import android.telephony.TelephonyManager;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;

/* renamed from: eql  reason: default package */
/* compiled from: PG */
public final class eql implements eqr {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/microphone/state/impl/MicStateReporterImpl");
    public List b;
    public List c;
    private final Context d;
    private final Executor e;
    private final jqs f;
    private final Map g = new LinkedHashMap();
    private final Set h = new LinkedHashSet();
    private final List i;
    private final eoz j;
    private final bzl k = new bzl((short[]) null);

    static {
        jji.o(1999, 4, 3, 2);
    }

    public eql(dku dku, eoz eoz, Context context, Executor executor, jqs jqs, jlv jlv) {
        jnu.e(dku, "audioSessionIdStore");
        jnu.e(eoz, "tokenGenerator");
        jnu.e(executor, "lightweightExecutor");
        jnu.e(jqs, "lightweightScope");
        jnu.e(jlv, "mainContext");
        this.j = eoz;
        this.d = context;
        this.e = executor;
        this.f = jqs;
        TelephonyManager telephonyManager = (TelephonyManager) context.getSystemService("phone");
        jkq jkq = jkq.a;
        this.b = jkq;
        this.c = jkq;
        this.i = jkq;
    }

    private final void f(bzl bzl, String str, jna jna) {
        Executor executor = this.e;
        jnu.e(executor, "executor");
        jqs jqs = this.f;
        jnu.e(jqs, "coroutineScope");
        fyz.c(bzl.D(new ejj(jqs, jna, 0), executor), "%s", str);
    }

    public final long a() {
        return this.j.n();
    }

    public final eqa b() {
        eak eak;
        ArrayList arrayList = new ArrayList();
        for (eqp eqp : this.b) {
            htk l = epr.f.l();
            jnu.d(l, "newBuilder(...)");
            byw ae = jnu.e(l, "builder");
            ept b2 = ept.b(eqp.b);
            if (b2 == null) {
                b2 = ept.CLIENT_TYPE_UNKNOWN;
            }
            ae.R(b2);
            epq b3 = epq.b(eqp.d);
            if (b3 == null) {
                b3 = epq.REQUEST_UNKNOWN;
            }
            jnu.e(b3, "value");
            htk htk = (htk) ae.a;
            if (!htk.b.B()) {
                htk.u();
            }
            epr epr = (epr) htk.b;
            epr.c = Integer.valueOf(b3.c);
            epr.b = 3;
            arrayList.add(ae.Q());
        }
        for (eqq eqq : this.c) {
            htk l2 = epr.f.l();
            jnu.d(l2, "newBuilder(...)");
            byw ae2 = jnu.e(l2, "builder");
            ept b4 = ept.b(eqq.d);
            if (b4 == null) {
                b4 = ept.CLIENT_TYPE_UNKNOWN;
            }
            ae2.R(b4);
            if ((eqq.a & 4) != 0) {
                int i2 = eqq.f;
                htk htk2 = (htk) ae2.a;
                if (!htk2.b.B()) {
                    htk2.u();
                }
                epr epr2 = (epr) htk2.b;
                epr2.a |= 2;
                epr2.e = i2;
            }
            if (eqq.b == 2) {
                eak = (eak) eqq.c;
            } else {
                eak = eak.c;
            }
            jnu.e(eak, "value");
            htk htk3 = (htk) ae2.a;
            if (!htk3.b.B()) {
                htk3.u();
            }
            epr epr3 = (epr) htk3.b;
            eak.getClass();
            epr3.c = eak;
            epr3.b = 2;
            arrayList.add(ae2.Q());
        }
        jji.J(arrayList, this.i);
        htk l3 = eqa.b.l();
        jnu.d(l3, "newBuilder(...)");
        jnu.e(l3, "builder");
        List unmodifiableList = Collections.unmodifiableList(((eqa) l3.b).a);
        jnu.d(unmodifiableList, "getUpdatesList(...)");
        new hwm(unmodifiableList);
        if (!l3.b.B()) {
            l3.u();
        }
        eqa eqa = (eqa) l3.b;
        huf huf = eqa.a;
        if (!huf.c()) {
            eqa.a = htq.s(huf);
        }
        hrz.g(arrayList, eqa.a);
        htq o = l3.r();
        jnu.d(o, "build(...)");
        return (eqa) o;
    }

    public final void c(eqa eqa, eqb eqb) {
        ((hby) a.c().h(hdg.a, "ALT.MicStateReporter").j("com/google/android/libraries/search/audio/microphone/state/impl/MicStateReporterImpl", "notifyListeners", 500, "MicStateReporterImpl.kt")).C("#audio# notify listeners with(update(%s), delta(%s))", new eqh(eqa, 1), new eqg(eqb));
        for (epz epz : this.h) {
            Map map = this.g;
            Object obj = map.get(epz);
            if (obj == null) {
                obj = new bzl((short[]) null);
                map.put(epz, obj);
            }
            f((bzl) obj, "failed to notify mic state listener", new eqi(epz, eqa, eqb, (jlr) null));
        }
    }

    public final void d(eqp eqp) {
        jnu.e(eqp, "partialRequestUpdate");
        hca hca = a;
        ((hby) hca.c().h(hdg.a, "ALT.MicStateReporter").j("com/google/android/libraries/search/audio/microphone/state/impl/MicStateReporterImpl", "updateState", 177, "MicStateReporterImpl.kt")).u("#audio# update state partially with upcoming update(%s)", new eqh(eqp, 2));
        if ((eqp.a & 2) != 0) {
            bzl bzl = this.k;
            String str = eqp.c;
            f(bzl, "partial upcoming update(" + str + ") failed", new eqk(this, eqp, (jlr) null));
            return;
        }
        ((hby) hca.h().h(hdg.a, "ALT.MicStateReporter").j("com/google/android/libraries/search/audio/microphone/state/impl/MicStateReporterImpl", "updateState", 182, "MicStateReporterImpl.kt")).r("#audio# skip partial upcoming update as no updateId present");
    }

    public final void e(eqq eqq) {
        jnu.e(eqq, "partialUpdate");
        hca hca = a;
        ((hby) hca.c().h(hdg.a, "ALT.MicStateReporter").j("com/google/android/libraries/search/audio/microphone/state/impl/MicStateReporterImpl", "updateState", 158, "MicStateReporterImpl.kt")).u("#audio# update state partially with(%s)", new eqh(eqq, 0));
        if ((eqq.a & 2) != 0) {
            bzl bzl = this.k;
            String str = eqq.e;
            f(bzl, "partial update(" + str + ") failed", new eqj(this, eqq, (jlr) null));
            return;
        }
        ((hby) hca.h().h(hdg.a, "ALT.MicStateReporter").j("com/google/android/libraries/search/audio/microphone/state/impl/MicStateReporterImpl", "updateState", 163, "MicStateReporterImpl.kt")).r("#audio# skip partial update as no updateId present");
    }
}
