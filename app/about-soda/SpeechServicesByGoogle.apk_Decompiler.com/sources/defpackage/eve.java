package defpackage;

import java.util.Set;

/* renamed from: eve  reason: default package */
/* compiled from: PG */
public final class eve {
    public static final Set a = jji.aM(1, 16, 12);

    public static final dyt a(dyt dyt, dyt dyt2) {
        jnu.e(dyt, "<this>");
        jnu.e(dyt2, "sourceParams");
        htk htk = (htk) dyt2.C(5);
        htk.x(dyt2);
        jnu.d(htk, "toBuilder(...)");
        cxi f = jnu.e(htk, "builder");
        if ((dyt.a & 128) != 0) {
            long j = dyt.h;
            htk htk2 = (htk) f.a;
            if (!htk2.b.B()) {
                htk2.u();
            }
            dyt dyt3 = (dyt) htk2.b;
            dyt3.a |= 128;
            dyt3.h = j;
        }
        return f.h();
    }
}
