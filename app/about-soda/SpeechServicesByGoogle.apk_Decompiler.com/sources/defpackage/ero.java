package defpackage;

import java.util.Map;

/* renamed from: ero  reason: default package */
/* compiled from: PG */
public final class ero {
    private static final hca d = hca.m("com/google/android/libraries/search/audio/policies/controller/impl/SourcePolicyControllerImpl");
    public final boolean a;
    public final boolean b;
    public final efl c;
    private final Map e;
    private final cyw f;
    private final cqx g;

    public ero(Map map, cqx cqx, efl efl, cyw cyw, boolean z, boolean z2) {
        jnu.e(map, "sourcePolicyMap");
        jnu.e(cqx, "defaultPolicy");
        jnu.e(efl, "audioSourceAvailability");
        this.e = map;
        this.g = cqx;
        this.c = efl;
        this.f = cyw;
        this.a = z;
        this.b = z2;
    }

    public final void a(dzc dzc, boolean z) {
        ehg ehg = dzc.b;
        if (ehg == null) {
            ehg = ehg.c;
        }
        int i = ehf.a(ehg.a).K;
        dzb b2 = dzb.b(dzc.c);
        if (b2 == null) {
            b2 = dzb.DEFAULT;
        }
        Map map = this.e;
        jnu.d(b2, "getIntent(...)");
        Object obj = map.get(new ern(i, b2));
        if (obj == null) {
            if (z) {
                cyw cyw = this.f;
                ehg ehg2 = dzc.b;
                if (ehg2 == null) {
                    ehg2 = ehg.c;
                }
                jnu.d(ehg2, "getClientInfo(...)");
                dzb b3 = dzb.b(dzc.c);
                if (b3 == null) {
                    b3 = dzb.DEFAULT;
                }
                jnu.d(b3, "getIntent(...)");
                jnu.e(ehg2, "clientInfo");
                jnu.e(b3, "intent");
                ((fqy) ((emi) ((emd) cyw.d).b.b()).n.a()).b(b3.name(), ehf.a(ehg2.a).name());
                hby hby = (hby) d.h().h(hdg.a, "ALT.SourcePolicyCtlr").j("com/google/android/libraries/search/audio/policies/controller/impl/SourcePolicyControllerImpl", "getPolicy", 75, "SourcePolicyControllerImpl.kt");
                ehg ehg3 = dzc.b;
                if (ehg3 == null) {
                    ehg3 = ehg.c;
                }
                String name = ehf.a(ehg3.a).name();
                dzb b4 = dzb.b(dzc.c);
                if (b4 == null) {
                    b4 = dzb.DEFAULT;
                }
                hby.C("#audio# Audio source policy missing for client(%s) intent(%s)", name, b4.name());
            }
            obj = this.g;
        }
        cqx cqx = (cqx) obj;
    }
}
