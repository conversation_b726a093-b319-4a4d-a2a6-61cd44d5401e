package defpackage;

import androidx.wear.ambient.AmbientMode;

/* renamed from: enn  reason: default package */
/* compiled from: PG */
public final class enn extends jmi implements jne {
    final /* synthetic */ Object a;
    final /* synthetic */ Object b;
    private /* synthetic */ Object c;
    private final /* synthetic */ int d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public enn(egu egu, AmbientMode.AmbientController ambientController, jlr jlr, int i) {
        super(2, jlr);
        this.d = i;
        this.a = egu;
        this.b = ambientController;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        if (this.d != 0) {
            return ((enn) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
        return ((enn) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX WARNING: type inference failed for: r8v3, types: [enk, java.lang.Object] */
    public final Object bk(Object obj) {
        Object obj2;
        Object obj3;
        Object obj4;
        eek eek = null;
        if (this.d != 0) {
            jji.c(obj);
            jqs jqs = (jqs) this.c;
            egu egu = (egu) this.a;
            egy egy = egu.t;
            if (egy != null) {
                egu.l((AmbientMode.AmbientController) this.b, egy);
            } else {
                Object obj5 = this.b;
                if (!egu.p.get()) {
                    egu.l((AmbientMode.AmbientController) obj5, (egy) null);
                }
            }
            Object obj6 = this.a;
            ((egu) obj6).g.add(this.b);
            return jkd.a;
        }
        jji.c(obj);
        jqs jqs2 = (jqs) this.c;
        ? r8 = this.b;
        Object obj7 = this.a;
        try {
            ((hby) eno.a.f().h(hdg.a, "ALT.SrcAccessor").j("com/google/android/libraries/search/audio/microphone/impl/audiosourcedataaccessor/AudioSourceDataAccessorImpl", "startListeningInternal", 127, "AudioSourceDataAccessorImpl.kt")).C("#audio# opening audio source(%s), offset(%s)", ((eno) obj7).h, r8);
            eqw g = ((eno) obj7).g();
            if (g != null) {
                g.b();
            }
            jju jju = ((eno) obj7).g;
            if (jju != null) {
                obj4 = jju.a;
            } else {
                obj4 = ((eno) obj7).c.c();
            }
        } catch (Throwable th) {
            obj2 = jji.b(th);
        }
        ((eno) obj7).g = new jju(obj4);
        jju jju2 = ((eno) obj7).g;
        if (jju2 != null) {
            Object obj8 = jju2.a;
            jji.c(obj8);
            eek = (eek) ((grh) obj8).e();
        }
        if (eek != null) {
            ((eno) obj7).d.a(eek);
            obj2 = ((eno) obj7).c(eal.OPENED, r8);
        } else {
            obj2 = ejw.a(eaj.FAILED_TO_OPEN_AUDIO_SOURCE);
        }
        Object obj9 = this.a;
        if (jju.b(obj2)) {
            obj3 = ((dzh) ((dyw) obj2).f()).b;
            if (obj3 == null) {
                obj3 = eak.c;
            }
        } else {
            obj3 = obj2;
        }
        jqw.k(((eno) obj9).i, obj3);
        jji.c(obj2);
        return obj2;
    }

    /* JADX WARNING: type inference failed for: r1v0, types: [enk, java.lang.Object] */
    public final jlr c(Object obj, jlr jlr) {
        if (this.d != 0) {
            enn enn = new enn((egu) this.a, (AmbientMode.AmbientController) this.b, jlr, 1);
            enn.c = obj;
            return enn;
        }
        enn enn2 = new enn((eno) this.a, (enk) this.b, jlr, 0);
        enn2.c = obj;
        return enn2;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public enn(eno eno, enk enk, jlr jlr, int i) {
        super(2, jlr);
        this.d = i;
        this.a = eno;
        this.b = enk;
    }
}
