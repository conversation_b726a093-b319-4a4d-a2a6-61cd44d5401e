package defpackage;

/* renamed from: eqn  reason: default package */
/* compiled from: PG */
public final class eqn implements iiu {
    private final jjk a;
    private final jjk b;

    public eqn(jjk jjk, jjk jjk2) {
        this.a = jjk;
        this.b = jjk2;
    }

    /* renamed from: a */
    public final eqr b() {
        grh grh = (grh) ((iiv) this.a).a;
        eql eql = (eql) this.b.b();
        jnu.e(grh, "micStatePartialUpdaterOverride");
        jnu.e(eql, "defaultMicStatePartialUpdater");
        return (eqr) grh.d(eql);
    }
}
