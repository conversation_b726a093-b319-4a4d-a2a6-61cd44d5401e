package com.google.android.apps.googlespeechoffline.speech;

import com.google.android.libraries.assistant.soda.Soda;
import com.google.speech.recognizer.ResourceManager;

import java.io.Closeable;
import java.io.IOException;

/**
 * 自定义SODA实现，继承自原始Soda类
 * 参考CustomRecognizer对AbstractRecognizer的实现模式
 */
public class CustomSoda extends Soda implements Closeable {

    public final ResourceManager mResourceManager = new CustomResourceManager();

    static {
        System.loadLibrary("google_speech_jni");
        System.loadLibrary("inject");
    }


    static public CustomSoda create(String model)
    {

        CustomSoda recognizer = new CustomSoda();
        recognizer.init(model,recognizer);
        return recognizer;
    }

    @Override
    public void close() throws IOException {

    }
}