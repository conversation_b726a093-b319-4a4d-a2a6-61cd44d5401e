package defpackage;

/* renamed from: esi  reason: default package */
/* compiled from: PG */
public final class esi implements esh {
    public final erp a;
    public final int b;
    public final esg c;
    public final ejn d;
    private final dyz e;
    private final dzn f;
    private final boolean g;

    public /* synthetic */ esi(erp erp, dyz dyz, int i, esg esg, dzn dzn) {
        boolean z;
        ejn a2 = erp.a();
        jnu.e(dzn, "params");
        this.a = erp;
        this.e = dyz;
        this.b = i;
        this.c = esg;
        this.f = dzn;
        if (i == -1) {
            z = true;
        } else {
            z = false;
        }
        this.g = z;
        this.d = a2;
    }

    public final int a() {
        return this.b;
    }

    public final dyz b() {
        return this.e;
    }

    public final dzn c() {
        return this.f;
    }

    public final ejn d() {
        return this.d;
    }

    public final /* synthetic */ eow e() {
        return cqx.L(this);
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof esi)) {
            return false;
        }
        esi esi = (esi) obj;
        if (jnu.i(this.a, esi.a) && jnu.i(this.e, esi.e) && this.b == esi.b && jnu.i(this.c, esi.c) && jnu.i(this.f, esi.f) && this.g == esi.g && jnu.i(this.d, esi.d)) {
            return true;
        }
        return false;
    }

    public final esg f() {
        return this.c;
    }

    public final boolean g() {
        return this.g;
    }

    public final int hashCode() {
        int i;
        int hashCode = (((((this.a.hashCode() * 31) + this.e.hashCode()) * 31) + this.b) * 31) + this.c.hashCode();
        dzn dzn = this.f;
        if (dzn.B()) {
            i = dzn.i();
        } else {
            int i2 = dzn.memoizedHashCode;
            if (i2 == 0) {
                i2 = dzn.i();
                dzn.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (((((hashCode * 31) + i) * 31) + a.f(this.g)) * 31) + this.d.hashCode();
    }

    public final String toString() {
        return "AudioRouteSessionDataInternal(audioRoute=" + this.a + ", session=" + this.e + ", routeToken=" + this.b + ", client=" + this.c + ", params=" + this.f + ", isInactive=" + this.g + ", routeData=" + this.d + ")";
    }
}
