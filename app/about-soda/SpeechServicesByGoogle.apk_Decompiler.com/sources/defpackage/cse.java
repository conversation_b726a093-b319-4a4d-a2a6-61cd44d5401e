package defpackage;

/* renamed from: cse  reason: default package */
/* compiled from: PG */
public enum cse implements hts {
    BLOCK_DOWNLOAD_IN_LOW_STORAGE(0),
    BLOCK_DOWNLOAD_LOWER_THRESHOLD(1),
    EXTREMELY_LOW_THRESHOLD(2);
    
    public final int d;

    private cse(int i) {
        this.d = i;
    }

    public final int a() {
        return this.d;
    }

    public final String toString() {
        return Integer.toString(this.d);
    }
}
