package defpackage;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CancellationException;

/* renamed from: eoj  reason: default package */
/* compiled from: PG */
public final class eoj {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/microphone/registry/AudioSessionsRegistry");
    public final cyw b;
    private final elm c;
    private final emr d;
    private final jqs e;
    private final boolean f;
    private final List g = new ArrayList();
    private final eoz h;
    private final dsy i;
    private final cyw j;

    public eoj(dvx dvx, elm elm, emr emr, cyw cyw, cyw cyw2, eoz eoz, dsy dsy, jqs jqs, boolean z) {
        jnu.e(dvx, "audioSessionAssembler");
        jnu.e(elm, "micUpdateReporter");
        jnu.e(emr, "audioSessionToMicStateUpdater");
        jnu.e(eoz, "tokenGenerator");
        jnu.e(jqs, "lightweightScope");
        this.c = elm;
        this.d = emr;
        this.b = cyw;
        this.j = cyw2;
        this.h = eoz;
        this.i = dsy;
        this.e = jqs;
        this.f = z;
    }

    /* JADX WARNING: type inference failed for: r3v2, types: [elp, java.lang.Object] */
    private final void j(eoc eoc) {
        eof eof = (eof) eoc;
        ehg ehg = eof.b.b.b;
        if (ehg == null) {
            ehg = ehg.c;
        }
        ehg ehg2 = ehg;
        cyw cyw = this.b;
        jnu.d(ehg2, "getClientInfo(...)");
        dyy dyy = eof.a;
        int i2 = eof.d;
        long j2 = (long) i2;
        cyw.a.l(ehg2, dyy, j2, eof.e, eof.c.b);
        dyy dyy2 = eof.a;
        eoa eoa = eof.b;
        int i3 = eof.d;
        int i4 = eoa.a;
        ehg ehg3 = eoa.b.b;
        if (ehg3 == null) {
            ehg3 = ehg.c;
        }
        eug eug = (eug) cyw.b;
        int i5 = i4;
        long j3 = (long) i3;
        ehg ehg4 = ehg3;
        eug eug2 = eug;
        hfc.T(dyy2.c(), gof.g(new dae(eug, i5, j3, ehg4, 3)), eug2.b);
        hfc.T(((dyx) dyy2.d()).a(), gof.g(new dae(eug2, i5, j3, ehg4, 2)), eug2.b);
        Object obj = cyw.d;
        dyy dyy3 = eof.a;
        ehg ehg5 = eof.b.b.b;
        if (ehg5 == null) {
            ehg5 = ehg.c;
        }
        emd emd = (emd) obj;
        emd.d(dyy3, ehg5, new boa(19), eaj.FAILED_OPENING_ERROR_RETRIEVING_STATUS);
        emd.e(dyy3, ehg5, new boa(20), eag.FAILED_CLOSING_ERROR_RETRIEVING_STATUS);
        dyy dyy4 = eof.a;
        hme c2 = dyy4.c();
        gqx b2 = gof.b(new dun(8));
        elm elm = this.c;
        elm.a(2, hke.f(c2, b2, elm.a), hke.f(((dyx) dyy4.d()).a(), gof.b(new dun(9)), elm.a));
        if (eof.f) {
            this.d.h(eof.a, -1, dom.m(eel.SOURCE_EMPTY));
        }
    }

    private static final eof k(eaj eaj, eag eag) {
        return new eof(new ejz(eaj, eag), (eoa) null, (eow) null, 0, (dze) null, true, 30);
    }

    /* JADX WARNING: type inference failed for: r10v6, types: [elp, java.lang.Object] */
    public final synchronized dyx a(eoa eoa, int i2, eam eam, boolean z) {
        Object obj;
        jnu.e(eoa, "client");
        jnu.e(eam, "reason");
        eoi eoi = new eoi(i2, eam, eoa, this);
        if (eoa.c) {
            eoi.a();
            return new ekd(eag.FAILED_CLOSING_DUE_TO_INACTIVE_CLIENT, eam);
        }
        Iterator it = this.g.iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            eod eod = (eod) obj;
            if (eod.b.a == eoa.a && eod.d == i2) {
                break;
            }
        }
        eod eod2 = (eod) obj;
        if (eod2 == null) {
            if (!z) {
                eoi.a();
            }
            return new ekd(eag.FAILED_CLOSING_DUE_TO_INACTIVE_SESSION, eam);
        }
        jji.N(this.g, new eoh(i2, 0));
        ((hby) a.f().h(hdg.a, "ALT.AudioSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/AudioSessionsRegistry", "markStopped", 292, "AudioSessionsRegistry.kt")).G("#audio# stopping(%s) audio session(%d) for %s", eam.name(), Integer.valueOf(eod2.d), eod2.b.a());
        cyw cyw = this.b;
        jnu.e(eam, "reason");
        ehg ehg = eod2.b.b.b;
        if (ehg == null) {
            ehg = ehg.c;
        }
        ? r10 = cyw.a;
        jnu.d(ehg, "getClientInfo(...)");
        r10.t(ehg, (long) eod2.d);
        Object obj2 = cyw.b;
        htk l = eav.n.l();
        jnu.d(l, "newBuilder(...)");
        byw D = jnu.e(l, "builder");
        D.o(eay.AUDIO_REQUEST_STOP_LISTENING);
        D.q((long) eod2.b.a);
        D.s((long) eod2.d);
        D.t(eam);
        ehg ehg2 = eod2.b.b.b;
        if (ehg2 == null) {
            ehg2 = ehg.c;
        }
        D.p(ehg2);
        ((eug) obj2).d(D.l());
        eod2.e.r((CancellationException) null);
        return this.i.e(eod2.d, eam);
    }

    public final synchronized eoc b(int i2) {
        Object obj;
        ((hby) a.e().h(hdg.a, "ALT.AudioSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/AudioSessionsRegistry", "findAudioSession$java_com_google_android_libraries_search_audio_microphone_registry_audio_sessions_registry", 335, "AudioSessionsRegistry.kt")).s("#audio# find audio session(%d)", i2);
        Iterator it = this.g.iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            if (((eod) obj).d == i2) {
                break;
            }
        }
        eod eod = (eod) obj;
        if (eod != null) {
            return eod;
        }
        ((hby) a.e().h(hdg.a, "ALT.AudioSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/AudioSessionsRegistry", "findAudioSession$java_com_google_android_libraries_search_audio_microphone_registry_audio_sessions_registry", 338, "AudioSessionsRegistry.kt")).s("#audio# no audio session(%d) found, inactive", i2);
        return eof.h(k(eaj.FAILED_OPENING_DUE_TO_INACTIVE_SESSION, eag.FAILED_CLOSING_DUE_TO_INACTIVE_SESSION), (eoa) null, (eow) null, i2, (dze) null, 55);
    }

    public final synchronized eoc c(eoa eoa, int i2) {
        if (eoa.c) {
            ((hby) a.e().h(hdg.a, "ALT.AudioSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/AudioSessionsRegistry", "findAudioSession$java_com_google_android_libraries_search_audio_microphone_registry_audio_sessions_registry", 315, "AudioSessionsRegistry.kt")).x("#audio# no audio session(%d) found for %s, inactive", i2, eoa.a());
            return eof.h(k(eaj.FAILED_OPENING_DUE_TO_INACTIVE_CLIENT, eag.FAILED_CLOSING_DUE_TO_INACTIVE_CLIENT), eoa, (eow) null, i2, (dze) null, 53);
        }
        return b(i2);
    }

    public final synchronized List d() {
        ArrayList arrayList;
        List<eoc> list = this.g;
        arrayList = new ArrayList(jji.K(list));
        for (eoc eoc : list) {
            ehg ehg = eoc.d().b.b;
            if (ehg == null) {
                ehg = ehg.c;
            }
            jnu.d(ehg, "getClientInfo(...)");
            arrayList.add(new eos(ehg, eoc.a(), new eoe(eoc, 0), eoc.c()));
        }
        return arrayList;
    }

    public final synchronized void e(eoa eoa, eam eam) {
        jnu.e(eam, "reason");
        ArrayList<eod> arrayList = new ArrayList<>();
        for (Object next : this.g) {
            if (((eod) next).b.a == eoa.a) {
                arrayList.add(next);
            }
        }
        if (arrayList.isEmpty()) {
            ((hby) a.e().h(hdg.a, "ALT.AudioSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/AudioSessionsRegistry", "stopClientAudioSessions$java_com_google_android_libraries_search_audio_microphone_registry_audio_sessions_registry", 222, "AudioSessionsRegistry.kt")).C("#audio# no sessions left to stop(%s) for %s", eam.name(), eoa.a());
            return;
        }
        for (eod eod : arrayList) {
            dyx unused = a(eod.b, eod.d, eam, false);
        }
    }

    public final synchronized void f(eow eow, eam eam) {
        jnu.e(eam, "reason");
        ArrayList<eod> arrayList = new ArrayList<>();
        for (Object next : this.g) {
            if (((eod) next).c.a == eow.a) {
                arrayList.add(next);
            }
        }
        if (arrayList.isEmpty()) {
            ((hby) a.e().h(hdg.a, "ALT.AudioSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/AudioSessionsRegistry", "stopRouteAudioSessions$java_com_google_android_libraries_search_audio_microphone_registry_audio_sessions_registry", 238, "AudioSessionsRegistry.kt")).C("#audio# no sessions left to stop(%s) for %s", eam.name(), eow.b());
            return;
        }
        for (eod eod : arrayList) {
            dyx unused = a(eod.b, eod.d, eam, false);
        }
    }

    public final synchronized boolean g(dzb... dzbArr) {
        boolean z;
        jnu.e(dzbArr, "intents");
        List list = this.g;
        z = false;
        if (!list.isEmpty()) {
            Iterator it = list.iterator();
            while (true) {
                if (!it.hasNext()) {
                    break;
                }
                eod eod = (eod) it.next();
                dzb b2 = dzb.b(eod.b.b.c);
                if (b2 == null) {
                    b2 = dzb.DEFAULT;
                }
                if (jji.ac(dzbArr, b2) && !((dyx) eod.a.d()).a().isDone()) {
                    z = true;
                    break;
                }
            }
        }
        return z;
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r16v0, resolved type: eod} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r16v1, resolved type: eod} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v28, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v30, resolved type: eod} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r16v4, resolved type: eod} */
    /* JADX WARNING: type inference failed for: r7v1, types: [elp, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v26, types: [java.lang.Object, java.lang.Iterable] */
    /* JADX WARNING: type inference failed for: r4v27, types: [jne, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v72, types: [java.lang.Object, epv] */
    /* JADX WARNING: type inference failed for: r2v74, types: [java.lang.Object, epv] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:116:0x0308  */
    /* JADX WARNING: Removed duplicated region for block: B:149:0x030b A[SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final synchronized defpackage.eoc i(defpackage.eoa r21, defpackage.eow r22, defpackage.dze r23, defpackage.eoz r24) {
        /*
            r20 = this;
            r1 = r20
            r0 = r21
            r8 = r22
            r9 = r23
            r10 = r24
            monitor-enter(r20)
            java.lang.String r2 = "params"
            defpackage.jnu.e(r9, r2)     // Catch:{ all -> 0x0415 }
            eoz r2 = r1.h     // Catch:{ all -> 0x0415 }
            int r11 = r2.m()     // Catch:{ all -> 0x0415 }
            hca r12 = a     // Catch:{ all -> 0x0415 }
            hco r2 = r12.f()     // Catch:{ all -> 0x0415 }
            hcr r3 = defpackage.hdg.a     // Catch:{ all -> 0x0415 }
            java.lang.String r4 = "ALT.AudioSessionsRegy"
            hco r2 = r2.h(r3, r4)     // Catch:{ all -> 0x0415 }
            java.lang.String r3 = "AudioSessionsRegistry.kt"
            java.lang.String r4 = "com/google/android/libraries/search/audio/microphone/registry/AudioSessionsRegistry"
            java.lang.String r5 = "startAudioSession$java_com_google_android_libraries_search_audio_microphone_registry_audio_sessions_registry"
            r6 = 82
            hco r2 = r2.j(r4, r5, r6, r3)     // Catch:{ all -> 0x0415 }
            hby r2 = (defpackage.hby) r2     // Catch:{ all -> 0x0415 }
            java.lang.String r3 = r21.a()     // Catch:{ all -> 0x0415 }
            java.lang.String r4 = "#audio# starting audio session(%d) for %s"
            r2.x(r4, r11, r3)     // Catch:{ all -> 0x0415 }
            java.lang.String r2 = "params"
            defpackage.jnu.e(r9, r2)     // Catch:{ all -> 0x0415 }
            dzc r2 = r0.b     // Catch:{ all -> 0x0415 }
            ehg r2 = r2.b     // Catch:{ all -> 0x0415 }
            if (r2 != 0) goto L_0x0048
            ehg r2 = defpackage.ehg.c     // Catch:{ all -> 0x0415 }
        L_0x0048:
            cyw r3 = r1.b     // Catch:{ all -> 0x0415 }
            java.lang.String r4 = "getClientInfo(...)"
            defpackage.jnu.d(r2, r4)     // Catch:{ all -> 0x0415 }
            long r4 = (long) r11     // Catch:{ all -> 0x0415 }
            long r6 = defpackage.cqx.F()     // Catch:{ all -> 0x0415 }
            j$.time.Duration r6 = j$.time.Duration.ofNanos(r6)     // Catch:{ all -> 0x0415 }
            java.lang.String r7 = "ofNanos(...)"
            defpackage.jnu.d(r6, r7)     // Catch:{ all -> 0x0415 }
            java.lang.Object r7 = r3.a     // Catch:{ all -> 0x0415 }
            r7.s(r2, r4, r6)     // Catch:{ all -> 0x0415 }
            eav r2 = defpackage.eav.n     // Catch:{ all -> 0x0415 }
            htk r2 = r2.l()     // Catch:{ all -> 0x0415 }
            java.lang.String r6 = "newBuilder(...)"
            defpackage.jnu.d(r2, r6)     // Catch:{ all -> 0x0415 }
            byw r2 = defpackage.jnu.e(r2, "builder")     // Catch:{ all -> 0x0415 }
            eay r6 = defpackage.eay.AUDIO_REQUEST_START_LISTENING     // Catch:{ all -> 0x0415 }
            r2.o(r6)     // Catch:{ all -> 0x0415 }
            java.lang.String r6 = "value"
            defpackage.jnu.e(r9, r6)     // Catch:{ all -> 0x0415 }
            java.lang.Object r6 = r2.a     // Catch:{ all -> 0x0415 }
            r7 = r6
            htk r7 = (defpackage.htk) r7     // Catch:{ all -> 0x0415 }
            htq r7 = r7.b     // Catch:{ all -> 0x0415 }
            boolean r7 = r7.B()     // Catch:{ all -> 0x0415 }
            if (r7 != 0) goto L_0x008e
            r7 = r6
            htk r7 = (defpackage.htk) r7     // Catch:{ all -> 0x0415 }
            r7.u()     // Catch:{ all -> 0x0415 }
        L_0x008e:
            htk r6 = (defpackage.htk) r6     // Catch:{ all -> 0x0415 }
            htq r6 = r6.b     // Catch:{ all -> 0x0415 }
            eav r6 = (defpackage.eav) r6     // Catch:{ all -> 0x0415 }
            r23.getClass()     // Catch:{ all -> 0x0415 }
            r6.c = r9     // Catch:{ all -> 0x0415 }
            r7 = 201(0xc9, float:2.82E-43)
            r6.b = r7     // Catch:{ all -> 0x0415 }
            int r6 = r0.a     // Catch:{ all -> 0x0415 }
            long r6 = (long) r6     // Catch:{ all -> 0x0415 }
            r2.q(r6)     // Catch:{ all -> 0x0415 }
            dzc r6 = r0.b     // Catch:{ all -> 0x0415 }
            ehg r6 = r6.b     // Catch:{ all -> 0x0415 }
            if (r6 != 0) goto L_0x00ab
            ehg r6 = defpackage.ehg.c     // Catch:{ all -> 0x0415 }
        L_0x00ab:
            java.lang.Object r3 = r3.b     // Catch:{ all -> 0x0415 }
            r2.p(r6)     // Catch:{ all -> 0x0415 }
            int r6 = r8.a     // Catch:{ all -> 0x0415 }
            long r6 = (long) r6     // Catch:{ all -> 0x0415 }
            r2.r(r6)     // Catch:{ all -> 0x0415 }
            r2.s(r4)     // Catch:{ all -> 0x0415 }
            eav r2 = r2.l()     // Catch:{ all -> 0x0415 }
            eug r3 = (defpackage.eug) r3     // Catch:{ all -> 0x0415 }
            r3.d(r2)     // Catch:{ all -> 0x0415 }
            boolean r2 = r0.c     // Catch:{ all -> 0x0415 }
            r13 = 2
            r14 = 0
            r15 = 1
            r16 = 0
            if (r2 == 0) goto L_0x00d7
            eol r2 = new eol     // Catch:{ all -> 0x0415 }
            eaj r3 = defpackage.eaj.FAILED_OPENING_DUE_TO_INACTIVE_CLIENT     // Catch:{ all -> 0x0415 }
            eag r4 = defpackage.eag.FAILED_CLOSING_DUE_TO_INACTIVE_CLIENT     // Catch:{ all -> 0x0415 }
            java.lang.String r5 = "the associated audio client is already inactive"
            r2.<init>(r3, r4, r5)     // Catch:{ all -> 0x0415 }
            goto L_0x0115
        L_0x00d7:
            boolean r2 = r8.c     // Catch:{ all -> 0x0415 }
            if (r2 == 0) goto L_0x00e7
            eol r2 = new eol     // Catch:{ all -> 0x0415 }
            eaj r3 = defpackage.eaj.FAILED_OPENING_DUE_TO_INACTIVE_AUDIO_ROUTE_SESSION     // Catch:{ all -> 0x0415 }
            eag r4 = defpackage.eag.FAILED_CLOSING_DUE_TO_INACTIVE_AUDIO_ROUTE_SESSION     // Catch:{ all -> 0x0415 }
            java.lang.String r5 = "the associated audio route is already inactive"
            r2.<init>(r3, r4, r5)     // Catch:{ all -> 0x0415 }
            goto L_0x0115
        L_0x00e7:
            dzc r2 = r0.b     // Catch:{ all -> 0x0415 }
            int r2 = r2.c     // Catch:{ all -> 0x0415 }
            dzb r2 = defpackage.dzb.b(r2)     // Catch:{ all -> 0x0415 }
            if (r2 != 0) goto L_0x00f3
            dzb r2 = defpackage.dzb.DEFAULT     // Catch:{ all -> 0x0415 }
        L_0x00f3:
            dzb r3 = defpackage.dzb.AMBIENT     // Catch:{ all -> 0x0415 }
            if (r2 != r3) goto L_0x0113
            dzb[] r2 = new defpackage.dzb[r13]     // Catch:{ all -> 0x0415 }
            dzb r3 = defpackage.dzb.DEFAULT     // Catch:{ all -> 0x0415 }
            r2[r14] = r3     // Catch:{ all -> 0x0415 }
            dzb r3 = defpackage.dzb.CONVERSATIONAL     // Catch:{ all -> 0x0415 }
            r2[r15] = r3     // Catch:{ all -> 0x0415 }
            boolean r2 = r1.g(r2)     // Catch:{ all -> 0x0415 }
            if (r2 == 0) goto L_0x0113
            eol r2 = new eol     // Catch:{ all -> 0x0415 }
            eaj r3 = defpackage.eaj.FAILED_OPENING_CONCURRENCY_CONFLICT     // Catch:{ all -> 0x0415 }
            eag r4 = defpackage.eag.FAILED_CLOSING_CONCURRENCY_CONFLICT     // Catch:{ all -> 0x0415 }
            java.lang.String r5 = "ambient session cannot start due to a conflict with another active session"
            r2.<init>(r3, r4, r5)     // Catch:{ all -> 0x0415 }
            goto L_0x0115
        L_0x0113:
            r2 = r16
        L_0x0115:
            if (r2 != 0) goto L_0x01e5
            cyw r2 = r1.j     // Catch:{ all -> 0x0415 }
            java.lang.String r3 = "params"
            defpackage.jnu.e(r9, r3)     // Catch:{ all -> 0x0415 }
            eol r3 = r2.j()     // Catch:{ all -> 0x0415 }
            if (r3 != 0) goto L_0x0140
            boolean r3 = r9.i     // Catch:{ all -> 0x0415 }
            if (r3 == 0) goto L_0x013e
            java.lang.Object r3 = r2.d     // Catch:{ all -> 0x0415 }
            emt r3 = (defpackage.emt) r3     // Catch:{ all -> 0x0415 }
            boolean r3 = r3.c()     // Catch:{ all -> 0x0415 }
            if (r3 != 0) goto L_0x013e
            eol r3 = new eol     // Catch:{ all -> 0x0415 }
            eaj r4 = defpackage.eaj.FAILED_OPENING_OP_NOT_ALLOWED     // Catch:{ all -> 0x0415 }
            eag r5 = defpackage.eag.FAILED_CLOSING_OP_NOT_ALLOWED     // Catch:{ all -> 0x0415 }
            java.lang.String r6 = "record audio OP not allowed"
            r3.<init>(r4, r5, r6)     // Catch:{ all -> 0x0415 }
            goto L_0x0140
        L_0x013e:
            r3 = r16
        L_0x0140:
            if (r3 != 0) goto L_0x015c
            dyt r3 = r9.f     // Catch:{ all -> 0x0415 }
            if (r3 != 0) goto L_0x0148
            dyt r3 = defpackage.dyt.l     // Catch:{ all -> 0x0415 }
        L_0x0148:
            int r3 = r3.b     // Catch:{ all -> 0x0415 }
            r4 = 1999(0x7cf, float:2.801E-42)
            if (r3 == r4) goto L_0x0151
            r3 = r16
            goto L_0x015c
        L_0x0151:
            eol r3 = new eol     // Catch:{ all -> 0x0415 }
            eaj r4 = defpackage.eaj.FAILED_OPENING_NOT_ALLOWED_HOTWORD_SOURCE_USAGE     // Catch:{ all -> 0x0415 }
            eag r5 = defpackage.eag.FAILED_CLOSING_NOT_ALLOWED_HOTWORD_SOURCE_USAGE     // Catch:{ all -> 0x0415 }
            java.lang.String r6 = "using hotword audio source is not allowed"
            r3.<init>(r4, r5, r6)     // Catch:{ all -> 0x0415 }
        L_0x015c:
            if (r3 != 0) goto L_0x01b4
            int r3 = r9.j     // Catch:{ all -> 0x0415 }
            ebt r3 = defpackage.ebt.b(r3)     // Catch:{ all -> 0x0415 }
            if (r3 != 0) goto L_0x0168
            ebt r3 = defpackage.ebt.BEHAVIOR_NO_FAIL     // Catch:{ all -> 0x0415 }
        L_0x0168:
            java.lang.String r4 = "getMicOccupiedBehavior(...)"
            defpackage.jnu.d(r3, r4)     // Catch:{ all -> 0x0415 }
            int r3 = r3.ordinal()     // Catch:{ all -> 0x0415 }
            if (r3 == 0) goto L_0x01b1
            if (r3 == r15) goto L_0x0186
            if (r3 != r13) goto L_0x0180
            java.lang.Object r2 = r2.c     // Catch:{ all -> 0x0415 }
            epu r3 = defpackage.epu.MIC_OCCUPATION_ANY     // Catch:{ all -> 0x0415 }
            boolean r2 = r2.a(r3)     // Catch:{ all -> 0x0415 }
            goto L_0x018e
        L_0x0180:
            jjq r0 = new jjq     // Catch:{ all -> 0x0415 }
            r0.<init>()     // Catch:{ all -> 0x0415 }
            throw r0     // Catch:{ all -> 0x0415 }
        L_0x0186:
            java.lang.Object r2 = r2.c     // Catch:{ all -> 0x0415 }
            epu r3 = defpackage.epu.MIC_OCCUPATION_PRIVACY_SENSITIVE     // Catch:{ all -> 0x0415 }
            boolean r2 = r2.a(r3)     // Catch:{ all -> 0x0415 }
        L_0x018e:
            if (r2 == 0) goto L_0x01b1
            eol r2 = new eol     // Catch:{ all -> 0x0415 }
            eaj r3 = defpackage.eaj.FAILED_OPENING_MIC_OCCUPIED     // Catch:{ all -> 0x0415 }
            eag r4 = defpackage.eag.FAILED_CLOSING_MIC_OCCUPIED     // Catch:{ all -> 0x0415 }
            int r5 = r9.j     // Catch:{ all -> 0x0415 }
            ebt r5 = defpackage.ebt.b(r5)     // Catch:{ all -> 0x0415 }
            if (r5 != 0) goto L_0x01a0
            ebt r5 = defpackage.ebt.BEHAVIOR_NO_FAIL     // Catch:{ all -> 0x0415 }
        L_0x01a0:
            java.lang.String r6 = "mic occupied per "
            java.util.Objects.toString(r5)     // Catch:{ all -> 0x0415 }
            java.lang.String r5 = java.lang.String.valueOf(r5)     // Catch:{ all -> 0x0415 }
            java.lang.String r5 = r6.concat(r5)     // Catch:{ all -> 0x0415 }
            r2.<init>(r3, r4, r5)     // Catch:{ all -> 0x0415 }
            goto L_0x01b5
        L_0x01b1:
            r2 = r16
            goto L_0x01b5
        L_0x01b4:
            r2 = r3
        L_0x01b5:
            if (r2 != 0) goto L_0x01e5
            int r2 = r9.b     // Catch:{ all -> 0x0415 }
            r3 = 17
            if (r2 != r3) goto L_0x01e3
            java.lang.Object r2 = r9.c     // Catch:{ all -> 0x0415 }
            java.lang.Boolean r2 = (java.lang.Boolean) r2     // Catch:{ all -> 0x0415 }
            boolean r2 = r2.booleanValue()     // Catch:{ all -> 0x0415 }
            if (r2 == 0) goto L_0x01e3
            dyt r2 = r9.f     // Catch:{ all -> 0x0415 }
            if (r2 != 0) goto L_0x01cd
            dyt r2 = defpackage.dyt.l     // Catch:{ all -> 0x0415 }
        L_0x01cd:
            dyr r2 = r2.g     // Catch:{ all -> 0x0415 }
            if (r2 != 0) goto L_0x01d3
            dyr r2 = defpackage.dyr.d     // Catch:{ all -> 0x0415 }
        L_0x01d3:
            boolean r2 = r2.c     // Catch:{ all -> 0x0415 }
            if (r2 == 0) goto L_0x01e3
            eol r2 = new eol     // Catch:{ all -> 0x0415 }
            eaj r3 = defpackage.eaj.FAILED_OPENING_VOICE_DSP_HOTWORD_MIC_AEC_IS_SET     // Catch:{ all -> 0x0415 }
            eag r4 = defpackage.eag.FAILED_CLOSING_VOICE_DSP_HOTWORD_MIC_AEC_IS_SET     // Catch:{ all -> 0x0415 }
            java.lang.String r5 = "setting acoustic_echo_cancellation_enabled when enable_voice_dsp_hotword_mic is set, not allowed"
            r2.<init>(r3, r4, r5)     // Catch:{ all -> 0x0415 }
            goto L_0x01e5
        L_0x01e3:
            r2 = r16
        L_0x01e5:
            if (r2 != 0) goto L_0x0209
            hco r2 = r12.f()     // Catch:{ all -> 0x0415 }
            hcr r3 = defpackage.hdg.a     // Catch:{ all -> 0x0415 }
            java.lang.String r4 = "ALT.AudioSessionsRegy"
            hco r2 = r2.h(r3, r4)     // Catch:{ all -> 0x0415 }
            java.lang.String r3 = "AudioSessionsRegistry.kt"
            java.lang.String r4 = "com/google/android/libraries/search/audio/microphone/registry/AudioSessionsRegistry"
            java.lang.String r5 = "checkAudioSessionStartConditions"
            r6 = 154(0x9a, float:2.16E-43)
            hco r2 = r2.j(r4, r5, r6, r3)     // Catch:{ all -> 0x0415 }
            hby r2 = (defpackage.hby) r2     // Catch:{ all -> 0x0415 }
            java.lang.String r3 = "#audio# audio session(%d) OK to start"
            r2.s(r3, r11)     // Catch:{ all -> 0x0415 }
            r2 = r16
            goto L_0x024a
        L_0x0209:
            hco r3 = r12.h()     // Catch:{ all -> 0x0415 }
            hcr r4 = defpackage.hdg.a     // Catch:{ all -> 0x0415 }
            java.lang.String r5 = "ALT.AudioSessionsRegy"
            hco r3 = r3.h(r4, r5)     // Catch:{ all -> 0x0415 }
            java.lang.String r4 = "AudioSessionsRegistry.kt"
            java.lang.String r5 = "com/google/android/libraries/search/audio/microphone/registry/AudioSessionsRegistry"
            java.lang.String r6 = "checkAudioSessionStartConditions"
            r7 = 160(0xa0, float:2.24E-43)
            hco r3 = r3.j(r5, r6, r7, r4)     // Catch:{ all -> 0x0415 }
            hby r3 = (defpackage.hby) r3     // Catch:{ all -> 0x0415 }
            java.lang.Integer r4 = java.lang.Integer.valueOf(r11)     // Catch:{ all -> 0x0415 }
            java.lang.String r5 = r21.a()     // Catch:{ all -> 0x0415 }
            java.lang.String r6 = r2.c     // Catch:{ all -> 0x0415 }
            java.lang.String r7 = "#audio# audio session(%d) start conditions failed for %s: %s"
            r3.G(r7, r4, r5, r6)     // Catch:{ all -> 0x0415 }
            eag r3 = r2.b     // Catch:{ all -> 0x0415 }
            eaj r2 = r2.a     // Catch:{ all -> 0x0415 }
            eof r2 = k(r2, r3)     // Catch:{ all -> 0x0415 }
            r7 = 33
            r3 = r21
            r4 = r22
            r5 = r11
            r6 = r23
            eof r2 = defpackage.eof.h(r2, r3, r4, r5, r6, r7)     // Catch:{ all -> 0x0415 }
            r1.j(r2)     // Catch:{ all -> 0x0415 }
        L_0x024a:
            if (r2 != 0) goto L_0x0413
            hco r2 = r12.f()     // Catch:{ all -> 0x0415 }
            hcr r3 = defpackage.hdg.a     // Catch:{ all -> 0x0415 }
            java.lang.String r4 = "ALT.AudioSessionsRegy"
            hco r2 = r2.h(r3, r4)     // Catch:{ all -> 0x0415 }
            java.lang.String r3 = "AudioSessionsRegistry.kt"
            java.lang.String r4 = "com/google/android/libraries/search/audio/microphone/registry/AudioSessionsRegistry"
            java.lang.String r5 = "enforceConcurrencyStateOnNewAudioSession"
            r6 = 189(0xbd, float:2.65E-43)
            hco r2 = r2.j(r4, r5, r6, r3)     // Catch:{ all -> 0x0415 }
            hby r2 = (defpackage.hby) r2     // Catch:{ all -> 0x0415 }
            java.lang.String r3 = r21.a()     // Catch:{ all -> 0x0415 }
            java.lang.String r4 = "#audio# enforcing concurrency state on new audio session of %s"
            r2.u(r4, r3)     // Catch:{ all -> 0x0415 }
            java.util.List r2 = r1.g     // Catch:{ all -> 0x0415 }
            java.util.Iterator r2 = r2.iterator()     // Catch:{ all -> 0x0415 }
        L_0x0275:
            boolean r3 = r2.hasNext()     // Catch:{ all -> 0x0415 }
            if (r3 == 0) goto L_0x028c
            java.lang.Object r3 = r2.next()     // Catch:{ all -> 0x0415 }
            r4 = r3
            eod r4 = (defpackage.eod) r4     // Catch:{ all -> 0x0415 }
            eoa r4 = r4.b     // Catch:{ all -> 0x0415 }
            int r4 = r4.a     // Catch:{ all -> 0x0415 }
            int r5 = r0.a     // Catch:{ all -> 0x0415 }
            if (r4 != r5) goto L_0x0275
            r16 = r3
        L_0x028c:
            r2 = r16
            eod r2 = (defpackage.eod) r2     // Catch:{ all -> 0x0415 }
            if (r2 == 0) goto L_0x029b
            int r3 = r2.d     // Catch:{ all -> 0x0415 }
            eoa r2 = r2.b     // Catch:{ all -> 0x0415 }
            eam r4 = defpackage.eam.NEW_AUDIO_REQUEST_CLIENT_LISTENING     // Catch:{ all -> 0x0415 }
            defpackage.dyx unused = r1.a(r2, r3, r4, false)     // Catch:{ all -> 0x0415 }
        L_0x029b:
            java.lang.Object r2 = r10.a     // Catch:{ all -> 0x0415 }
            java.util.ArrayList r3 = new java.util.ArrayList     // Catch:{ all -> 0x0415 }
            r3.<init>()     // Catch:{ all -> 0x0415 }
            java.util.Iterator r2 = r2.iterator()     // Catch:{ all -> 0x0415 }
        L_0x02a6:
            boolean r4 = r2.hasNext()     // Catch:{ all -> 0x0415 }
            if (r4 == 0) goto L_0x030d
            java.lang.Object r4 = r2.next()     // Catch:{ all -> 0x0415 }
            r5 = r4
            eos r5 = (defpackage.eos) r5     // Catch:{ all -> 0x0415 }
            dzc r6 = r0.b     // Catch:{ all -> 0x0415 }
            ehg r6 = r6.b     // Catch:{ all -> 0x0415 }
            if (r6 != 0) goto L_0x02bb
            ehg r6 = defpackage.ehg.c     // Catch:{ all -> 0x0415 }
        L_0x02bb:
            java.lang.String r7 = "getClientInfo(...)"
            defpackage.jnu.d(r6, r7)     // Catch:{ all -> 0x0415 }
            ejn r7 = r8.b     // Catch:{ all -> 0x0415 }
            int r12 = r5.b     // Catch:{ all -> 0x0415 }
            hca r16 = defpackage.evd.a     // Catch:{ all -> 0x0415 }
            java.lang.String r14 = "audioRouteData"
            defpackage.jnu.e(r7, r14)     // Catch:{ all -> 0x0415 }
            java.lang.String r14 = "params"
            defpackage.jnu.e(r9, r14)     // Catch:{ all -> 0x0415 }
            ebh r7 = defpackage.evd.b(r7, r9)     // Catch:{ all -> 0x0415 }
            if (r7 == 0) goto L_0x02dc
            int r7 = r7.a     // Catch:{ all -> 0x0415 }
            if (r7 != r12) goto L_0x02dc
        L_0x02da:
            r5 = r15
            goto L_0x0306
        L_0x02dc:
            ejn r7 = r8.b     // Catch:{ all -> 0x0415 }
            dzq r7 = r7.b     // Catch:{ all -> 0x0415 }
            int r7 = r7.a     // Catch:{ all -> 0x0415 }
            dzp r7 = defpackage.dzp.a(r7)     // Catch:{ all -> 0x0415 }
            dzp r12 = defpackage.dzp.SODA_ROUTE     // Catch:{ all -> 0x0415 }
            if (r7 != r12) goto L_0x02eb
            goto L_0x02da
        L_0x02eb:
            java.lang.Object r5 = r5.d     // Catch:{ all -> 0x0415 }
            ebn r5 = (defpackage.ebn) r5     // Catch:{ all -> 0x0415 }
            int r5 = r5.g     // Catch:{ all -> 0x0415 }
            int r5 = defpackage.a.A(r5)     // Catch:{ all -> 0x0415 }
            if (r5 != 0) goto L_0x02f9
        L_0x02f7:
            r5 = 0
            goto L_0x0306
        L_0x02f9:
            if (r5 != r13) goto L_0x02f7
            int r5 = r6.a     // Catch:{ all -> 0x0415 }
            r6 = 34
            if (r5 != r6) goto L_0x02da
            boolean r5 = r1.f     // Catch:{ all -> 0x0415 }
            if (r5 == 0) goto L_0x02da
            goto L_0x02f7
        L_0x0306:
            if (r5 != 0) goto L_0x030b
            r3.add(r4)     // Catch:{ all -> 0x0415 }
        L_0x030b:
            r14 = 0
            goto L_0x02a6
        L_0x030d:
            java.util.Iterator r2 = r3.iterator()     // Catch:{ all -> 0x0415 }
        L_0x0311:
            boolean r3 = r2.hasNext()     // Catch:{ all -> 0x0415 }
            if (r3 == 0) goto L_0x0350
            java.lang.Object r3 = r2.next()     // Catch:{ all -> 0x0415 }
            eos r3 = (defpackage.eos) r3     // Catch:{ all -> 0x0415 }
            hca r4 = a     // Catch:{ all -> 0x0415 }
            hco r4 = r4.f()     // Catch:{ all -> 0x0415 }
            hcr r5 = defpackage.hdg.a     // Catch:{ all -> 0x0415 }
            java.lang.String r6 = "ALT.AudioSessionsRegy"
            hco r4 = r4.h(r5, r6)     // Catch:{ all -> 0x0415 }
            java.lang.String r5 = "AudioSessionsRegistry.kt"
            java.lang.String r6 = "com/google/android/libraries/search/audio/microphone/registry/AudioSessionsRegistry"
            java.lang.String r7 = "enforceConcurrencyStateOnNewAudioSession"
            r12 = 204(0xcc, float:2.86E-43)
            hco r4 = r4.j(r6, r7, r12, r5)     // Catch:{ all -> 0x0415 }
            hby r4 = (defpackage.hby) r4     // Catch:{ all -> 0x0415 }
            int r5 = r3.b     // Catch:{ all -> 0x0415 }
            java.lang.String r6 = "#audio# hotword session(%s) has insufficient concurrent mode, stopping"
            r4.s(r6, r5)     // Catch:{ all -> 0x0415 }
            java.lang.Object r4 = r10.b     // Catch:{ all -> 0x0415 }
            if (r4 == 0) goto L_0x0311
            int r3 = r3.b     // Catch:{ all -> 0x0415 }
            java.lang.Integer r3 = java.lang.Integer.valueOf(r3)     // Catch:{ all -> 0x0415 }
            eam r5 = defpackage.eam.NEW_AUDIO_REQUEST_CLIENT_LISTENING     // Catch:{ all -> 0x0415 }
            r4.b(r3, r5)     // Catch:{ all -> 0x0415 }
            goto L_0x0311
        L_0x0350:
            emr r2 = r1.d     // Catch:{ all -> 0x0415 }
            r3 = -1
            if (r11 != r3) goto L_0x0377
            hca r2 = defpackage.emr.a     // Catch:{ all -> 0x0415 }
            hco r2 = r2.h()     // Catch:{ all -> 0x0415 }
            hcr r3 = defpackage.hdg.a     // Catch:{ all -> 0x0415 }
            java.lang.String r4 = "ALT.SessionMicUpdater"
            hco r2 = r2.h(r3, r4)     // Catch:{ all -> 0x0415 }
            java.lang.String r3 = "AudioSessionToMicStateUpdater.kt"
            java.lang.String r4 = "com/google/android/libraries/search/audio/microphone/AudioSessionToMicStateUpdater"
            java.lang.String r5 = "reportUpcomingAudioRequestSession"
            r6 = 75
            hco r2 = r2.j(r4, r5, r6, r3)     // Catch:{ all -> 0x0415 }
            hby r2 = (defpackage.hby) r2     // Catch:{ all -> 0x0415 }
            java.lang.String r3 = "#audio# unexpected inactive token, skip reporting"
            r2.r(r3)     // Catch:{ all -> 0x0415 }
            goto L_0x03a0
        L_0x0377:
            eqr r3 = r2.b     // Catch:{ all -> 0x0415 }
            eqp r4 = defpackage.eqp.e     // Catch:{ all -> 0x0415 }
            htk r4 = r4.l()     // Catch:{ all -> 0x0415 }
            java.lang.String r5 = "newBuilder(...)"
            defpackage.jnu.d(r4, r5)     // Catch:{ all -> 0x0415 }
            bzj r4 = defpackage.jnu.e(r4, "builder")     // Catch:{ all -> 0x0415 }
            ept r5 = defpackage.ept.CLIENT_TYPE_AUDIO_REQUEST     // Catch:{ all -> 0x0415 }
            r4.R(r5)     // Catch:{ all -> 0x0415 }
            java.lang.String r2 = r2.f(r11)     // Catch:{ all -> 0x0415 }
            r4.T(r2)     // Catch:{ all -> 0x0415 }
            epq r2 = defpackage.epq.REQUEST_OPEN_PENDING     // Catch:{ all -> 0x0415 }
            r4.S(r2)     // Catch:{ all -> 0x0415 }
            eqp r2 = r4.Q()     // Catch:{ all -> 0x0415 }
            r3.d(r2)     // Catch:{ all -> 0x0415 }
        L_0x03a0:
            dsy r2 = r1.i     // Catch:{ all -> 0x0415 }
            int r3 = r0.a     // Catch:{ all -> 0x0415 }
            dzc r4 = r0.b     // Catch:{ all -> 0x0415 }
            epb r5 = new epb     // Catch:{ all -> 0x0415 }
            ehg r4 = r4.b     // Catch:{ all -> 0x0415 }
            if (r4 != 0) goto L_0x03ae
            ehg r4 = defpackage.ehg.c     // Catch:{ all -> 0x0415 }
        L_0x03ae:
            java.lang.String r6 = "getClientInfo(...)"
            defpackage.jnu.d(r4, r6)     // Catch:{ all -> 0x0415 }
            r5.<init>(r3, r4)     // Catch:{ all -> 0x0415 }
            epp r3 = r22.a()     // Catch:{ all -> 0x0415 }
            epg r4 = new epg     // Catch:{ all -> 0x0415 }
            r4.<init>(r11, r9)     // Catch:{ all -> 0x0415 }
            dyy r3 = r2.f(r5, r3, r4)     // Catch:{ all -> 0x0415 }
            eof r10 = new eof     // Catch:{ all -> 0x0415 }
            r12 = 0
            r13 = 32
            r2 = r10
            r4 = r21
            r5 = r22
            r6 = r11
            r7 = r23
            r8 = r12
            r9 = r13
            r2.<init>(r3, r4, r5, r6, r7, r8, r9)     // Catch:{ all -> 0x0415 }
            r1.j(r10)     // Catch:{ all -> 0x0415 }
            boolean r2 = r10.f     // Catch:{ all -> 0x0415 }
            if (r2 == 0) goto L_0x03de
            monitor-exit(r20)
            return r10
        L_0x03de:
            jqs r2 = r1.e     // Catch:{ all -> 0x0415 }
            eog r3 = new eog     // Catch:{ all -> 0x0415 }
            r3.<init>(r1, r0, r11, r15)     // Catch:{ all -> 0x0415 }
            defpackage.cqx.Q(r10, r2, r3)     // Catch:{ all -> 0x0415 }
            jqs r2 = r1.e     // Catch:{ all -> 0x0415 }
            eog r3 = new eog     // Catch:{ all -> 0x0415 }
            r4 = 0
            r3.<init>(r1, r0, r11, r4)     // Catch:{ all -> 0x0415 }
            jrz r19 = defpackage.cqx.P(r10, r2, r3)     // Catch:{ all -> 0x0415 }
            java.util.List r0 = r1.g     // Catch:{ all -> 0x0415 }
            dyy r13 = r10.a     // Catch:{ all -> 0x0415 }
            eoa r14 = r10.b     // Catch:{ all -> 0x0415 }
            eow r15 = r10.c     // Catch:{ all -> 0x0415 }
            int r2 = r10.d     // Catch:{ all -> 0x0415 }
            dze r3 = r10.e     // Catch:{ all -> 0x0415 }
            boolean r4 = r10.f     // Catch:{ all -> 0x0415 }
            eod r5 = new eod     // Catch:{ all -> 0x0415 }
            r12 = r5
            r16 = r2
            r17 = r3
            r18 = r4
            r12.<init>(r13, r14, r15, r16, r17, r18, r19)     // Catch:{ all -> 0x0415 }
            r0.add(r5)     // Catch:{ all -> 0x0415 }
            monitor-exit(r20)
            return r10
        L_0x0413:
            monitor-exit(r20)
            return r2
        L_0x0415:
            r0 = move-exception
            monitor-exit(r20)     // Catch:{ all -> 0x0415 }
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eoj.i(eoa, eow, dze, eoz):eoc");
    }
}
