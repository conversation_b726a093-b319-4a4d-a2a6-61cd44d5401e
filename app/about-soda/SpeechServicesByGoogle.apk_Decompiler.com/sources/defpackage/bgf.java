package defpackage;

import android.database.Cursor;
import java.util.ArrayList;
import java.util.List;

/* renamed from: bgf  reason: default package */
/* compiled from: PG */
public final class bgf implements bgd {
    public final aus a;
    public final aub b;

    public bgf(aus aus) {
        this.a = aus;
        this.b = new bge(aus);
    }

    public final List a(String str) {
        auu a2 = auu.a("SELECT work_spec_id FROM dependency WHERE prerequisite_id=?", 1);
        a2.g(1, str);
        this.a.k();
        Cursor f = vy.f(this.a, a2, false);
        try {
            ArrayList arrayList = new ArrayList(f.getCount());
            while (f.moveToNext()) {
                arrayList.add(f.getString(0));
            }
            return arrayList;
        } finally {
            f.close();
            a2.j();
        }
    }
}
