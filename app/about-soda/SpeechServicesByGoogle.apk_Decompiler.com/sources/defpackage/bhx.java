package defpackage;

import android.database.Cursor;
import androidx.wear.ambient.SharedLibraryVersion;
import java.util.ArrayList;
import java.util.List;

/* renamed from: bhx  reason: default package */
/* compiled from: PG */
public final class bhx implements bhf {
    public final aus a;
    public final aub b;
    public final aua c;
    public final auv d;
    public final auv e;
    public final auv f;
    public final auv g;
    public final auv h;
    private final auv i;
    private final auv j;
    private final auv k;
    private final auv l;
    private final auv m;
    private final auv n;
    private final auv o;

    public bhx(aus aus) {
        this.a = aus;
        this.b = new bho(aus);
        this.c = new aua(aus);
        this.i = new bhq(aus);
        this.j = new bhr(aus);
        this.d = new bhs(aus);
        this.e = new bht(aus);
        this.k = new bhu(aus);
        this.l = new bhv(aus);
        this.f = new bhw(aus);
        this.g = new bhg(aus);
        new bhh(aus);
        this.m = new bhi(aus);
        this.n = new bhj(aus);
        this.h = new bhk(aus);
        new bhl(aus);
        new bhm(aus);
        this.o = new bhn(aus);
    }

    public final bbx a(String str) {
        Integer num;
        auu a2 = auu.a("SELECT state FROM workspec WHERE id=?", 1);
        a2.g(1, str);
        this.a.k();
        Cursor f2 = vy.f(this.a, a2, false);
        try {
            bbx bbx = null;
            if (f2.moveToFirst()) {
                if (f2.isNull(0)) {
                    num = null;
                } else {
                    num = Integer.valueOf(f2.getInt(0));
                }
                if (num != null) {
                    bbx = xm.q(num.intValue());
                }
            }
            return bbx;
        } finally {
            f2.close();
            a2.j();
        }
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r35v0, resolved type: bhe} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r35v1, resolved type: bhe} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r35v3, resolved type: bhe} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r68v0, resolved type: java.lang.String} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r35v4, resolved type: bhe} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r36v1, resolved type: bhe} */
    /* JADX WARNING: type inference failed for: r35v2, types: [java.lang.String] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.bhe b(java.lang.String r82) {
        /*
            r81 = this;
            r1 = r81
            java.lang.String r0 = "SELECT * FROM workspec WHERE id=?"
            r2 = 1
            auu r3 = defpackage.auu.a(r0, r2)
            r0 = r82
            r3.g(r2, r0)
            aus r0 = r1.a
            r0.k()
            aus r0 = r1.a
            r4 = 0
            android.database.Cursor r5 = defpackage.vy.f(r0, r3, r4)
            java.lang.String r0 = "id"
            int r0 = defpackage.vy.h(r5, r0)     // Catch:{ all -> 0x0216 }
            java.lang.String r6 = "state"
            int r6 = defpackage.vy.h(r5, r6)     // Catch:{ all -> 0x0216 }
            java.lang.String r7 = "worker_class_name"
            int r7 = defpackage.vy.h(r5, r7)     // Catch:{ all -> 0x0216 }
            java.lang.String r8 = "input_merger_class_name"
            int r8 = defpackage.vy.h(r5, r8)     // Catch:{ all -> 0x0216 }
            java.lang.String r9 = "input"
            int r9 = defpackage.vy.h(r5, r9)     // Catch:{ all -> 0x0216 }
            java.lang.String r10 = "output"
            int r10 = defpackage.vy.h(r5, r10)     // Catch:{ all -> 0x0216 }
            java.lang.String r11 = "initial_delay"
            int r11 = defpackage.vy.h(r5, r11)     // Catch:{ all -> 0x0216 }
            java.lang.String r12 = "interval_duration"
            int r12 = defpackage.vy.h(r5, r12)     // Catch:{ all -> 0x0216 }
            java.lang.String r13 = "flex_duration"
            int r13 = defpackage.vy.h(r5, r13)     // Catch:{ all -> 0x0216 }
            java.lang.String r14 = "run_attempt_count"
            int r14 = defpackage.vy.h(r5, r14)     // Catch:{ all -> 0x0216 }
            java.lang.String r15 = "backoff_policy"
            int r15 = defpackage.vy.h(r5, r15)     // Catch:{ all -> 0x0216 }
            java.lang.String r2 = "backoff_delay_duration"
            int r2 = defpackage.vy.h(r5, r2)     // Catch:{ all -> 0x0216 }
            java.lang.String r4 = "last_enqueue_time"
            int r4 = defpackage.vy.h(r5, r4)     // Catch:{ all -> 0x0216 }
            java.lang.String r1 = "minimum_retention_duration"
            int r1 = defpackage.vy.h(r5, r1)     // Catch:{ all -> 0x0216 }
            r16 = r3
            java.lang.String r3 = "schedule_requested_at"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r17 = r3
            java.lang.String r3 = "run_in_foreground"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r18 = r3
            java.lang.String r3 = "out_of_quota_policy"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r19 = r3
            java.lang.String r3 = "period_count"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r20 = r3
            java.lang.String r3 = "generation"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r21 = r3
            java.lang.String r3 = "next_schedule_time_override"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r22 = r3
            java.lang.String r3 = "next_schedule_time_override_generation"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r23 = r3
            java.lang.String r3 = "stop_reason"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r24 = r3
            java.lang.String r3 = "trace_tag"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r25 = r3
            java.lang.String r3 = "required_network_type"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r26 = r3
            java.lang.String r3 = "required_network_request"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r27 = r3
            java.lang.String r3 = "requires_charging"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r28 = r3
            java.lang.String r3 = "requires_device_idle"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r29 = r3
            java.lang.String r3 = "requires_battery_not_low"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r30 = r3
            java.lang.String r3 = "requires_storage_not_low"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r31 = r3
            java.lang.String r3 = "trigger_content_update_delay"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r32 = r3
            java.lang.String r3 = "trigger_max_content_delay"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            r33 = r3
            java.lang.String r3 = "content_uri_triggers"
            int r3 = defpackage.vy.h(r5, r3)     // Catch:{ all -> 0x0214 }
            boolean r34 = r5.moveToFirst()     // Catch:{ all -> 0x0214 }
            r35 = 0
            if (r34 == 0) goto L_0x020d
            java.lang.String r37 = r5.getString(r0)     // Catch:{ all -> 0x0214 }
            int r0 = r5.getInt(r6)     // Catch:{ all -> 0x0214 }
            bbx r38 = defpackage.xm.q(r0)     // Catch:{ all -> 0x0214 }
            java.lang.String r39 = r5.getString(r7)     // Catch:{ all -> 0x0214 }
            java.lang.String r40 = r5.getString(r8)     // Catch:{ all -> 0x0214 }
            byte[] r0 = r5.getBlob(r9)     // Catch:{ all -> 0x0214 }
            bat r41 = defpackage.bat.a(r0)     // Catch:{ all -> 0x0214 }
            byte[] r0 = r5.getBlob(r10)     // Catch:{ all -> 0x0214 }
            bat r42 = defpackage.bat.a(r0)     // Catch:{ all -> 0x0214 }
            long r43 = r5.getLong(r11)     // Catch:{ all -> 0x0214 }
            long r45 = r5.getLong(r12)     // Catch:{ all -> 0x0214 }
            long r47 = r5.getLong(r13)     // Catch:{ all -> 0x0214 }
            int r50 = r5.getInt(r14)     // Catch:{ all -> 0x0214 }
            int r0 = r5.getInt(r15)     // Catch:{ all -> 0x0214 }
            bak r51 = defpackage.xm.n(r0)     // Catch:{ all -> 0x0214 }
            long r52 = r5.getLong(r2)     // Catch:{ all -> 0x0214 }
            long r54 = r5.getLong(r4)     // Catch:{ all -> 0x0214 }
            long r56 = r5.getLong(r1)     // Catch:{ all -> 0x0214 }
            r0 = r17
            long r58 = r5.getLong(r0)     // Catch:{ all -> 0x0214 }
            r0 = r18
            int r0 = r5.getInt(r0)     // Catch:{ all -> 0x0214 }
            if (r0 == 0) goto L_0x0161
            r0 = r19
            r60 = 1
            goto L_0x0165
        L_0x0161:
            r0 = r19
            r60 = 0
        L_0x0165:
            int r0 = r5.getInt(r0)     // Catch:{ all -> 0x0214 }
            bbt r61 = defpackage.xm.p(r0)     // Catch:{ all -> 0x0214 }
            r0 = r20
            int r62 = r5.getInt(r0)     // Catch:{ all -> 0x0214 }
            r0 = r21
            int r63 = r5.getInt(r0)     // Catch:{ all -> 0x0214 }
            r0 = r22
            long r64 = r5.getLong(r0)     // Catch:{ all -> 0x0214 }
            r0 = r23
            int r66 = r5.getInt(r0)     // Catch:{ all -> 0x0214 }
            r0 = r24
            int r67 = r5.getInt(r0)     // Catch:{ all -> 0x0214 }
            r0 = r25
            boolean r1 = r5.isNull(r0)     // Catch:{ all -> 0x0214 }
            if (r1 == 0) goto L_0x0198
        L_0x0193:
            r0 = r26
            r68 = r35
            goto L_0x019d
        L_0x0198:
            java.lang.String r35 = r5.getString(r0)     // Catch:{ all -> 0x0214 }
            goto L_0x0193
        L_0x019d:
            int r0 = r5.getInt(r0)     // Catch:{ all -> 0x0214 }
            bbl r71 = defpackage.xm.o(r0)     // Catch:{ all -> 0x0214 }
            r0 = r27
            byte[] r0 = r5.getBlob(r0)     // Catch:{ all -> 0x0214 }
            bie r70 = defpackage.xm.r(r0)     // Catch:{ all -> 0x0214 }
            r0 = r28
            int r0 = r5.getInt(r0)     // Catch:{ all -> 0x0214 }
            if (r0 == 0) goto L_0x01bc
            r0 = r29
            r72 = 1
            goto L_0x01c0
        L_0x01bc:
            r0 = r29
            r72 = 0
        L_0x01c0:
            int r0 = r5.getInt(r0)     // Catch:{ all -> 0x0214 }
            if (r0 == 0) goto L_0x01cb
            r0 = r30
            r73 = 1
            goto L_0x01cf
        L_0x01cb:
            r0 = r30
            r73 = 0
        L_0x01cf:
            int r0 = r5.getInt(r0)     // Catch:{ all -> 0x0214 }
            if (r0 == 0) goto L_0x01da
            r0 = r31
            r74 = 1
            goto L_0x01de
        L_0x01da:
            r0 = r31
            r74 = 0
        L_0x01de:
            int r0 = r5.getInt(r0)     // Catch:{ all -> 0x0214 }
            if (r0 == 0) goto L_0x01e9
            r0 = r32
            r75 = 1
            goto L_0x01ed
        L_0x01e9:
            r0 = r32
            r75 = 0
        L_0x01ed:
            long r76 = r5.getLong(r0)     // Catch:{ all -> 0x0214 }
            r0 = r33
            long r78 = r5.getLong(r0)     // Catch:{ all -> 0x0214 }
            byte[] r0 = r5.getBlob(r3)     // Catch:{ all -> 0x0214 }
            java.util.Set r80 = defpackage.xm.s(r0)     // Catch:{ all -> 0x0214 }
            baq r49 = new baq     // Catch:{ all -> 0x0214 }
            r69 = r49
            r69.<init>(r70, r71, r72, r73, r74, r75, r76, r78, r80)     // Catch:{ all -> 0x0214 }
            bhe r35 = new bhe     // Catch:{ all -> 0x0214 }
            r36 = r35
            r36.<init>((java.lang.String) r37, (defpackage.bbx) r38, (java.lang.String) r39, (java.lang.String) r40, (defpackage.bat) r41, (defpackage.bat) r42, (long) r43, (long) r45, (long) r47, (defpackage.baq) r49, (int) r50, (defpackage.bak) r51, (long) r52, (long) r54, (long) r56, (long) r58, (boolean) r60, (defpackage.bbt) r61, (int) r62, (int) r63, (long) r64, (int) r66, (int) r67, (java.lang.String) r68)     // Catch:{ all -> 0x0214 }
        L_0x020d:
            r5.close()
            r16.j()
            return r35
        L_0x0214:
            r0 = move-exception
            goto L_0x0219
        L_0x0216:
            r0 = move-exception
            r16 = r3
        L_0x0219:
            r5.close()
            r16.j()
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bhx.b(java.lang.String):bhe");
    }

    public final List c() {
        auu auu;
        boolean z;
        int i2;
        String string;
        boolean z2;
        int i3;
        boolean z3;
        int i4;
        boolean z4;
        int i5;
        boolean z5;
        int i6;
        aus aus = this.a;
        auu a2 = auu.a("SELECT * FROM workspec WHERE state=1", 0);
        aus.k();
        Cursor f2 = vy.f(this.a, a2, false);
        try {
            int h2 = vy.h(f2, "id");
            int h3 = vy.h(f2, "state");
            int h4 = vy.h(f2, "worker_class_name");
            int h5 = vy.h(f2, "input_merger_class_name");
            int h6 = vy.h(f2, "input");
            int h7 = vy.h(f2, "output");
            int h8 = vy.h(f2, "initial_delay");
            int h9 = vy.h(f2, "interval_duration");
            int h10 = vy.h(f2, "flex_duration");
            int h11 = vy.h(f2, "run_attempt_count");
            int h12 = vy.h(f2, "backoff_policy");
            int h13 = vy.h(f2, "backoff_delay_duration");
            int h14 = vy.h(f2, "last_enqueue_time");
            int h15 = vy.h(f2, "minimum_retention_duration");
            auu = a2;
            try {
                int h16 = vy.h(f2, "schedule_requested_at");
                int h17 = vy.h(f2, "run_in_foreground");
                int h18 = vy.h(f2, "out_of_quota_policy");
                int h19 = vy.h(f2, "period_count");
                int h20 = vy.h(f2, "generation");
                int h21 = vy.h(f2, "next_schedule_time_override");
                int h22 = vy.h(f2, "next_schedule_time_override_generation");
                int h23 = vy.h(f2, "stop_reason");
                int h24 = vy.h(f2, "trace_tag");
                int h25 = vy.h(f2, "required_network_type");
                int h26 = vy.h(f2, "required_network_request");
                int h27 = vy.h(f2, "requires_charging");
                int h28 = vy.h(f2, "requires_device_idle");
                int h29 = vy.h(f2, "requires_battery_not_low");
                int h30 = vy.h(f2, "requires_storage_not_low");
                int h31 = vy.h(f2, "trigger_content_update_delay");
                int h32 = vy.h(f2, "trigger_max_content_delay");
                int h33 = vy.h(f2, "content_uri_triggers");
                int i7 = h15;
                ArrayList arrayList = new ArrayList(f2.getCount());
                while (f2.moveToNext()) {
                    String string2 = f2.getString(h2);
                    bbx q = xm.q(f2.getInt(h3));
                    String string3 = f2.getString(h4);
                    String string4 = f2.getString(h5);
                    bat a3 = bat.a(f2.getBlob(h6));
                    bat a4 = bat.a(f2.getBlob(h7));
                    long j2 = f2.getLong(h8);
                    long j3 = f2.getLong(h9);
                    long j4 = f2.getLong(h10);
                    int i8 = f2.getInt(h11);
                    bak n2 = xm.n(f2.getInt(h12));
                    long j5 = f2.getLong(h13);
                    long j6 = f2.getLong(h14);
                    int i9 = i7;
                    long j7 = f2.getLong(i9);
                    int i10 = h2;
                    int i11 = h16;
                    long j8 = f2.getLong(i11);
                    h16 = i11;
                    int i12 = h17;
                    if (f2.getInt(i12) != 0) {
                        h17 = i12;
                        i2 = h18;
                        z = true;
                    } else {
                        h17 = i12;
                        i2 = h18;
                        z = false;
                    }
                    bbt p = xm.p(f2.getInt(i2));
                    h18 = i2;
                    int i13 = h19;
                    int i14 = f2.getInt(i13);
                    h19 = i13;
                    int i15 = h20;
                    int i16 = f2.getInt(i15);
                    h20 = i15;
                    int i17 = h21;
                    long j9 = f2.getLong(i17);
                    h21 = i17;
                    int i18 = h22;
                    int i19 = f2.getInt(i18);
                    h22 = i18;
                    int i20 = h23;
                    int i21 = f2.getInt(i20);
                    h23 = i20;
                    int i22 = h24;
                    if (f2.isNull(i22)) {
                        string = null;
                    } else {
                        string = f2.getString(i22);
                    }
                    String str = string;
                    h24 = i22;
                    int i23 = h25;
                    bbl o2 = xm.o(f2.getInt(i23));
                    h25 = i23;
                    int i24 = h26;
                    bie r = xm.r(f2.getBlob(i24));
                    h26 = i24;
                    int i25 = h27;
                    if (f2.getInt(i25) != 0) {
                        h27 = i25;
                        i3 = h28;
                        z2 = true;
                    } else {
                        h27 = i25;
                        i3 = h28;
                        z2 = false;
                    }
                    if (f2.getInt(i3) != 0) {
                        h28 = i3;
                        i4 = h29;
                        z3 = true;
                    } else {
                        h28 = i3;
                        i4 = h29;
                        z3 = false;
                    }
                    if (f2.getInt(i4) != 0) {
                        h29 = i4;
                        i5 = h30;
                        z4 = true;
                    } else {
                        h29 = i4;
                        i5 = h30;
                        z4 = false;
                    }
                    if (f2.getInt(i5) != 0) {
                        h30 = i5;
                        i6 = h31;
                        z5 = true;
                    } else {
                        h30 = i5;
                        i6 = h31;
                        z5 = false;
                    }
                    long j10 = f2.getLong(i6);
                    h31 = i6;
                    int i26 = h32;
                    long j11 = f2.getLong(i26);
                    h32 = i26;
                    int i27 = h33;
                    h33 = i27;
                    arrayList.add(new bhe(string2, q, string3, string4, a3, a4, j2, j3, j4, new baq(r, o2, z2, z3, z4, z5, j10, j11, xm.s(f2.getBlob(i27))), i8, n2, j5, j6, j7, j8, z, p, i14, i16, j9, i19, i21, str));
                    h2 = i10;
                    i7 = i9;
                }
                f2.close();
                auu.j();
                return arrayList;
            } catch (Throwable th) {
                th = th;
                f2.close();
                auu.j();
                throw th;
            }
        } catch (Throwable th2) {
            th = th2;
            auu = a2;
            f2.close();
            auu.j();
            throw th;
        }
    }

    public final List d() {
        auu auu;
        boolean z;
        int i2;
        String string;
        boolean z2;
        int i3;
        boolean z3;
        int i4;
        boolean z4;
        int i5;
        boolean z5;
        int i6;
        aus aus = this.a;
        auu a2 = auu.a("SELECT * FROM workspec WHERE state=0 AND schedule_requested_at<>-1", 0);
        aus.k();
        Cursor f2 = vy.f(this.a, a2, false);
        try {
            int h2 = vy.h(f2, "id");
            int h3 = vy.h(f2, "state");
            int h4 = vy.h(f2, "worker_class_name");
            int h5 = vy.h(f2, "input_merger_class_name");
            int h6 = vy.h(f2, "input");
            int h7 = vy.h(f2, "output");
            int h8 = vy.h(f2, "initial_delay");
            int h9 = vy.h(f2, "interval_duration");
            int h10 = vy.h(f2, "flex_duration");
            int h11 = vy.h(f2, "run_attempt_count");
            int h12 = vy.h(f2, "backoff_policy");
            int h13 = vy.h(f2, "backoff_delay_duration");
            int h14 = vy.h(f2, "last_enqueue_time");
            int h15 = vy.h(f2, "minimum_retention_duration");
            auu = a2;
            try {
                int h16 = vy.h(f2, "schedule_requested_at");
                int h17 = vy.h(f2, "run_in_foreground");
                int h18 = vy.h(f2, "out_of_quota_policy");
                int h19 = vy.h(f2, "period_count");
                int h20 = vy.h(f2, "generation");
                int h21 = vy.h(f2, "next_schedule_time_override");
                int h22 = vy.h(f2, "next_schedule_time_override_generation");
                int h23 = vy.h(f2, "stop_reason");
                int h24 = vy.h(f2, "trace_tag");
                int h25 = vy.h(f2, "required_network_type");
                int h26 = vy.h(f2, "required_network_request");
                int h27 = vy.h(f2, "requires_charging");
                int h28 = vy.h(f2, "requires_device_idle");
                int h29 = vy.h(f2, "requires_battery_not_low");
                int h30 = vy.h(f2, "requires_storage_not_low");
                int h31 = vy.h(f2, "trigger_content_update_delay");
                int h32 = vy.h(f2, "trigger_max_content_delay");
                int h33 = vy.h(f2, "content_uri_triggers");
                int i7 = h15;
                ArrayList arrayList = new ArrayList(f2.getCount());
                while (f2.moveToNext()) {
                    String string2 = f2.getString(h2);
                    bbx q = xm.q(f2.getInt(h3));
                    String string3 = f2.getString(h4);
                    String string4 = f2.getString(h5);
                    bat a3 = bat.a(f2.getBlob(h6));
                    bat a4 = bat.a(f2.getBlob(h7));
                    long j2 = f2.getLong(h8);
                    long j3 = f2.getLong(h9);
                    long j4 = f2.getLong(h10);
                    int i8 = f2.getInt(h11);
                    bak n2 = xm.n(f2.getInt(h12));
                    long j5 = f2.getLong(h13);
                    long j6 = f2.getLong(h14);
                    int i9 = i7;
                    long j7 = f2.getLong(i9);
                    int i10 = h2;
                    int i11 = h16;
                    long j8 = f2.getLong(i11);
                    h16 = i11;
                    int i12 = h17;
                    if (f2.getInt(i12) != 0) {
                        h17 = i12;
                        i2 = h18;
                        z = true;
                    } else {
                        h17 = i12;
                        i2 = h18;
                        z = false;
                    }
                    bbt p = xm.p(f2.getInt(i2));
                    h18 = i2;
                    int i13 = h19;
                    int i14 = f2.getInt(i13);
                    h19 = i13;
                    int i15 = h20;
                    int i16 = f2.getInt(i15);
                    h20 = i15;
                    int i17 = h21;
                    long j9 = f2.getLong(i17);
                    h21 = i17;
                    int i18 = h22;
                    int i19 = f2.getInt(i18);
                    h22 = i18;
                    int i20 = h23;
                    int i21 = f2.getInt(i20);
                    h23 = i20;
                    int i22 = h24;
                    if (f2.isNull(i22)) {
                        string = null;
                    } else {
                        string = f2.getString(i22);
                    }
                    String str = string;
                    h24 = i22;
                    int i23 = h25;
                    bbl o2 = xm.o(f2.getInt(i23));
                    h25 = i23;
                    int i24 = h26;
                    bie r = xm.r(f2.getBlob(i24));
                    h26 = i24;
                    int i25 = h27;
                    if (f2.getInt(i25) != 0) {
                        h27 = i25;
                        i3 = h28;
                        z2 = true;
                    } else {
                        h27 = i25;
                        i3 = h28;
                        z2 = false;
                    }
                    if (f2.getInt(i3) != 0) {
                        h28 = i3;
                        i4 = h29;
                        z3 = true;
                    } else {
                        h28 = i3;
                        i4 = h29;
                        z3 = false;
                    }
                    if (f2.getInt(i4) != 0) {
                        h29 = i4;
                        i5 = h30;
                        z4 = true;
                    } else {
                        h29 = i4;
                        i5 = h30;
                        z4 = false;
                    }
                    if (f2.getInt(i5) != 0) {
                        h30 = i5;
                        i6 = h31;
                        z5 = true;
                    } else {
                        h30 = i5;
                        i6 = h31;
                        z5 = false;
                    }
                    long j10 = f2.getLong(i6);
                    h31 = i6;
                    int i26 = h32;
                    long j11 = f2.getLong(i26);
                    h32 = i26;
                    int i27 = h33;
                    h33 = i27;
                    arrayList.add(new bhe(string2, q, string3, string4, a3, a4, j2, j3, j4, new baq(r, o2, z2, z3, z4, z5, j10, j11, xm.s(f2.getBlob(i27))), i8, n2, j5, j6, j7, j8, z, p, i14, i16, j9, i19, i21, str));
                    h2 = i10;
                    i7 = i9;
                }
                f2.close();
                auu.j();
                return arrayList;
            } catch (Throwable th) {
                th = th;
                f2.close();
                auu.j();
                throw th;
            }
        } catch (Throwable th2) {
            th = th2;
            auu = a2;
            f2.close();
            auu.j();
            throw th;
        }
    }

    public final List e(String str) {
        auu a2 = auu.a("SELECT id, state FROM workspec WHERE id IN (SELECT work_spec_id FROM workname WHERE name=?)", 1);
        a2.g(1, str);
        this.a.k();
        Cursor f2 = vy.f(this.a, a2, false);
        try {
            ArrayList arrayList = new ArrayList(f2.getCount());
            while (f2.moveToNext()) {
                arrayList.add(new bhc(f2.getString(0), xm.q(f2.getInt(1))));
            }
            return arrayList;
        } finally {
            f2.close();
            a2.j();
        }
    }

    public final void f(String str) {
        this.a.k();
        axc d2 = this.i.d();
        d2.g(1, str);
        try {
            this.a.l();
            d2.a();
            this.a.o();
            this.a.m();
            this.i.f(d2);
        } catch (Throwable th) {
            this.i.f(d2);
            throw th;
        }
    }

    public final void g(String str, int i2) {
        this.a.k();
        axc d2 = this.m.d();
        d2.g(1, str);
        d2.e(2, (long) i2);
        try {
            this.a.l();
            d2.a();
            this.a.o();
            this.a.m();
            this.m.f(d2);
        } catch (Throwable th) {
            this.m.f(d2);
            throw th;
        }
    }

    public final void h(String str, long j2) {
        this.a.k();
        axc d2 = this.l.d();
        d2.e(1, j2);
        d2.g(2, str);
        try {
            this.a.l();
            d2.a();
            this.a.o();
            this.a.m();
            this.l.f(d2);
        } catch (Throwable th) {
            this.l.f(d2);
            throw th;
        }
    }

    public final void i(String str, bat bat) {
        this.a.k();
        axc d2 = this.k.d();
        d2.c(1, SharedLibraryVersion.a(bat));
        d2.g(2, str);
        try {
            this.a.l();
            d2.a();
            this.a.o();
            this.a.m();
            this.k.f(d2);
        } catch (Throwable th) {
            this.k.f(d2);
            throw th;
        }
    }

    public final void j(String str, int i2) {
        this.a.k();
        long j2 = (long) i2;
        axc d2 = this.o.d();
        d2.e(1, j2);
        d2.g(2, str);
        try {
            this.a.l();
            d2.a();
            this.a.o();
            this.a.m();
            this.o.f(d2);
        } catch (Throwable th) {
            this.o.f(d2);
            throw th;
        }
    }

    public final List k() {
        auu auu;
        boolean z;
        int i2;
        String string;
        boolean z2;
        int i3;
        boolean z3;
        int i4;
        boolean z4;
        int i5;
        boolean z5;
        int i6;
        auu a2 = auu.a("SELECT * FROM workspec WHERE state=0 ORDER BY last_enqueue_time LIMIT ?", 1);
        a2.e(1, 200);
        this.a.k();
        Cursor f2 = vy.f(this.a, a2, false);
        try {
            int h2 = vy.h(f2, "id");
            int h3 = vy.h(f2, "state");
            int h4 = vy.h(f2, "worker_class_name");
            int h5 = vy.h(f2, "input_merger_class_name");
            int h6 = vy.h(f2, "input");
            int h7 = vy.h(f2, "output");
            int h8 = vy.h(f2, "initial_delay");
            int h9 = vy.h(f2, "interval_duration");
            int h10 = vy.h(f2, "flex_duration");
            int h11 = vy.h(f2, "run_attempt_count");
            int h12 = vy.h(f2, "backoff_policy");
            int h13 = vy.h(f2, "backoff_delay_duration");
            int h14 = vy.h(f2, "last_enqueue_time");
            int h15 = vy.h(f2, "minimum_retention_duration");
            auu = a2;
            try {
                int h16 = vy.h(f2, "schedule_requested_at");
                int h17 = vy.h(f2, "run_in_foreground");
                int h18 = vy.h(f2, "out_of_quota_policy");
                int h19 = vy.h(f2, "period_count");
                int h20 = vy.h(f2, "generation");
                int h21 = vy.h(f2, "next_schedule_time_override");
                int h22 = vy.h(f2, "next_schedule_time_override_generation");
                int h23 = vy.h(f2, "stop_reason");
                int h24 = vy.h(f2, "trace_tag");
                int h25 = vy.h(f2, "required_network_type");
                int h26 = vy.h(f2, "required_network_request");
                int h27 = vy.h(f2, "requires_charging");
                int h28 = vy.h(f2, "requires_device_idle");
                int h29 = vy.h(f2, "requires_battery_not_low");
                int h30 = vy.h(f2, "requires_storage_not_low");
                int h31 = vy.h(f2, "trigger_content_update_delay");
                int h32 = vy.h(f2, "trigger_max_content_delay");
                int h33 = vy.h(f2, "content_uri_triggers");
                int i7 = h15;
                ArrayList arrayList = new ArrayList(f2.getCount());
                while (f2.moveToNext()) {
                    String string2 = f2.getString(h2);
                    bbx q = xm.q(f2.getInt(h3));
                    String string3 = f2.getString(h4);
                    String string4 = f2.getString(h5);
                    bat a3 = bat.a(f2.getBlob(h6));
                    bat a4 = bat.a(f2.getBlob(h7));
                    long j2 = f2.getLong(h8);
                    long j3 = f2.getLong(h9);
                    long j4 = f2.getLong(h10);
                    int i8 = f2.getInt(h11);
                    bak n2 = xm.n(f2.getInt(h12));
                    long j5 = f2.getLong(h13);
                    long j6 = f2.getLong(h14);
                    int i9 = i7;
                    long j7 = f2.getLong(i9);
                    int i10 = h2;
                    int i11 = h16;
                    long j8 = f2.getLong(i11);
                    h16 = i11;
                    int i12 = h17;
                    if (f2.getInt(i12) != 0) {
                        h17 = i12;
                        i2 = h18;
                        z = true;
                    } else {
                        h17 = i12;
                        i2 = h18;
                        z = false;
                    }
                    bbt p = xm.p(f2.getInt(i2));
                    h18 = i2;
                    int i13 = h19;
                    int i14 = f2.getInt(i13);
                    h19 = i13;
                    int i15 = h20;
                    int i16 = f2.getInt(i15);
                    h20 = i15;
                    int i17 = h21;
                    long j9 = f2.getLong(i17);
                    h21 = i17;
                    int i18 = h22;
                    int i19 = f2.getInt(i18);
                    h22 = i18;
                    int i20 = h23;
                    int i21 = f2.getInt(i20);
                    h23 = i20;
                    int i22 = h24;
                    if (f2.isNull(i22)) {
                        string = null;
                    } else {
                        string = f2.getString(i22);
                    }
                    String str = string;
                    h24 = i22;
                    int i23 = h25;
                    bbl o2 = xm.o(f2.getInt(i23));
                    h25 = i23;
                    int i24 = h26;
                    bie r = xm.r(f2.getBlob(i24));
                    h26 = i24;
                    int i25 = h27;
                    if (f2.getInt(i25) != 0) {
                        h27 = i25;
                        i3 = h28;
                        z2 = true;
                    } else {
                        h27 = i25;
                        i3 = h28;
                        z2 = false;
                    }
                    if (f2.getInt(i3) != 0) {
                        h28 = i3;
                        i4 = h29;
                        z3 = true;
                    } else {
                        h28 = i3;
                        i4 = h29;
                        z3 = false;
                    }
                    if (f2.getInt(i4) != 0) {
                        h29 = i4;
                        i5 = h30;
                        z4 = true;
                    } else {
                        h29 = i4;
                        i5 = h30;
                        z4 = false;
                    }
                    if (f2.getInt(i5) != 0) {
                        h30 = i5;
                        i6 = h31;
                        z5 = true;
                    } else {
                        h30 = i5;
                        i6 = h31;
                        z5 = false;
                    }
                    long j10 = f2.getLong(i6);
                    h31 = i6;
                    int i26 = h32;
                    long j11 = f2.getLong(i26);
                    h32 = i26;
                    int i27 = h33;
                    h33 = i27;
                    arrayList.add(new bhe(string2, q, string3, string4, a3, a4, j2, j3, j4, new baq(r, o2, z2, z3, z4, z5, j10, j11, xm.s(f2.getBlob(i27))), i8, n2, j5, j6, j7, j8, z, p, i14, i16, j9, i19, i21, str));
                    h2 = i10;
                    i7 = i9;
                }
                f2.close();
                auu.j();
                return arrayList;
            } catch (Throwable th) {
                th = th;
                f2.close();
                auu.j();
                throw th;
            }
        } catch (Throwable th2) {
            th = th2;
            auu = a2;
            f2.close();
            auu.j();
            throw th;
        }
    }

    public final void l(String str, long j2) {
        this.a.k();
        axc d2 = this.n.d();
        d2.e(1, j2);
        d2.g(2, str);
        try {
            this.a.l();
            d2.a();
            this.a.o();
            this.a.m();
            this.n.f(d2);
        } catch (Throwable th) {
            this.n.f(d2);
            throw th;
        }
    }

    public final void m(bbx bbx, String str) {
        this.a.k();
        axc d2 = this.j.d();
        d2.e(1, (long) xm.m(bbx));
        d2.g(2, str);
        try {
            this.a.l();
            d2.a();
            this.a.o();
            this.a.m();
            this.j.f(d2);
        } catch (Throwable th) {
            this.j.f(d2);
            throw th;
        }
    }
}
