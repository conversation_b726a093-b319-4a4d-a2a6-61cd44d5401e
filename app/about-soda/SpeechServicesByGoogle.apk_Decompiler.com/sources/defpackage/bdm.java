package defpackage;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.os.Trace;
import androidx.wear.ambient.AmbientLifecycleObserverKt;
import androidx.wear.ambient.AmbientMode;
import androidx.work.impl.WorkDatabase;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

/* renamed from: bdm  reason: default package */
/* compiled from: PG */
public final class bdm extends bbz {
    public static final Object a = new Object();
    private static bdm m = null;
    private static bdm n = null;
    public Context b;
    public bam c;
    public WorkDatabase d;
    public List e;
    public bcp f;
    public boolean g = false;
    public BroadcastReceiver.PendingResult h;
    public volatile bjg i;
    public final alx j;
    public cyw k;
    public byw l;
    private final jqs o;

    static {
        bbk.b("WorkManagerImpl");
    }

    /* JADX WARNING: type inference failed for: r10v2, types: [jlv, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r7v1, types: [java.util.concurrent.Executor, java.lang.Object] */
    public bdm(Context context, bam bam, cyw cyw, WorkDatabase workDatabase, List list, bcp bcp, alx alx) {
        Context applicationContext = context.getApplicationContext();
        if (!ag$$ExternalSyntheticApiModelOutline1.m(applicationContext)) {
            bbk bbk = new bbk(bam.j);
            synchronized (bbk.a) {
                if (bbk.b == null) {
                    bbk.b = bbk;
                }
            }
            this.b = applicationContext;
            this.k = cyw;
            this.d = workDatabase;
            this.f = bcp;
            this.j = alx;
            this.c = bam;
            this.e = list;
            jnu.e(cyw, "taskExecutor");
            ? r10 = cyw.d;
            jnu.d(r10, "taskExecutor.taskCoroutineDispatcher");
            jqs e2 = jqw.e(r10);
            this.o = e2;
            WorkDatabase workDatabase2 = this.d;
            this.l = new byw((Object) workDatabase2, (byte[]) null);
            this.f.a(new bcs(cyw.a, list, bam, workDatabase2));
            this.k.a(new bid(applicationContext, this));
            Context context2 = this.b;
            jnu.e(context2, "appContext");
            jnu.e(bam, "configuration");
            jnu.e(workDatabase, "db");
            if (big.a(context2, bam)) {
                bhf A = workDatabase.A();
                bhx bhx = (bhx) A;
                job.S(e2, (jlv) null, (jqt) null, new jur((juo) new juy(jut.a(job.Y(new juy(new jue(new avl(bhx.a, new String[]{"workspec"}, new mz(new bhp(bhx, auu.a("SELECT COUNT(*) > 0 FROM workspec WHERE state NOT IN (2, 3, 5) LIMIT 1", 0)), 4), (jlr) null)), new bcv((jlr) null), 0), -1)), new bcw(context2, (jlr) null), 2), (jlr) null, 0), 3);
                return;
            }
            return;
        }
        throw new IllegalStateException("Cannot initialize WorkManager in direct boot mode");
    }

    /* JADX WARNING: type inference failed for: r8v1, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v27, types: [java.util.Map, java.lang.Object] */
    /*  JADX ERROR: IndexOutOfBoundsException in pass: RegionMakerVisitor
        java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
        	at java.util.ArrayList.rangeCheck(ArrayList.java:659)
        	at java.util.ArrayList.get(ArrayList.java:435)
        	at jadx.core.dex.nodes.InsnNode.getArg(InsnNode.java:101)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:611)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.processMonitorEnter(RegionMaker.java:561)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverse(RegionMaker.java:133)
        	at jadx.core.dex.visitors.regions.RegionMaker.makeRegion(RegionMaker.java:86)
        	at jadx.core.dex.visitors.regions.RegionMaker.processMonitorEnter(RegionMaker.java:598)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverse(RegionMaker.java:133)
        	at jadx.core.dex.visitors.regions.RegionMaker.makeRegion(RegionMaker.java:86)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:49)
        */
    public static defpackage.bdm i(android.content.Context r34) {
        /*
            java.lang.Object r1 = a
            monitor-enter(r1)
            monitor-enter(r1)     // Catch:{ all -> 0x0690 }
            bdm r2 = m     // Catch:{ all -> 0x068c }
            if (r2 == 0) goto L_0x000a
            monitor-exit(r1)     // Catch:{ all -> 0x068c }
            goto L_0x000d
        L_0x000a:
            bdm r2 = n     // Catch:{ all -> 0x068c }
            monitor-exit(r1)     // Catch:{ all -> 0x068c }
        L_0x000d:
            if (r2 != 0) goto L_0x068a
            android.content.Context r2 = r34.getApplicationContext()     // Catch:{ all -> 0x0690 }
            boolean r3 = r2 instanceof com.google.android.apps.speech.tts.googletts.GoogleTTSRoot_Application     // Catch:{ all -> 0x0690 }
            if (r3 == 0) goto L_0x0682
            r3 = r2
            com.google.android.apps.speech.tts.googletts.GoogleTTSRoot_Application r3 = (com.google.android.apps.speech.tts.googletts.GoogleTTSRoot_Application) r3     // Catch:{ all -> 0x0690 }
            java.lang.Class<gae> r4 = defpackage.gae.class
            java.lang.Object r4 = defpackage.hzz.d(r3, r4)     // Catch:{ all -> 0x0690 }
            gae r4 = (defpackage.gae) r4     // Catch:{ all -> 0x0690 }
            bzl r4 = r4.R()     // Catch:{ all -> 0x0690 }
            java.lang.Object r4 = r4.a     // Catch:{ all -> 0x0690 }
            goq r5 = defpackage.goq.a     // Catch:{ all -> 0x0690 }
            boolean r5 = defpackage.ftd.W(r5)     // Catch:{ all -> 0x0690 }
            r6 = 5
            if (r5 != 0) goto L_0x003a
            java.lang.String r5 = "getWorkManagerConfiguration"
            gnk r4 = (defpackage.gnk) r4     // Catch:{ all -> 0x0690 }
            gmd r4 = r4.d(r5)     // Catch:{ all -> 0x0690 }
            goto L_0x003f
        L_0x003a:
            gle r4 = new gle     // Catch:{ all -> 0x0690 }
            r4.<init>(r6)     // Catch:{ all -> 0x0690 }
        L_0x003f:
            java.lang.Class<gae> r5 = defpackage.gae.class
            java.lang.Object r3 = defpackage.hzz.d(r3, r5)     // Catch:{ all -> 0x0676 }
            gae r3 = (defpackage.gae) r3     // Catch:{ all -> 0x0676 }
            bam r3 = r3.h()     // Catch:{ all -> 0x0676 }
            r4.close()     // Catch:{ all -> 0x0690 }
            monitor-enter(r1)     // Catch:{ all -> 0x0690 }
            bdm r4 = m     // Catch:{ all -> 0x0672 }
            if (r4 == 0) goto L_0x0060
            bdm r5 = n     // Catch:{ all -> 0x0672 }
            if (r5 != 0) goto L_0x0058
            goto L_0x0060
        L_0x0058:
            java.lang.IllegalStateException r2 = new java.lang.IllegalStateException     // Catch:{ all -> 0x0672 }
            java.lang.String r3 = "WorkManager is already initialized.  Did you try to initialize it manually without disabling WorkManagerInitializer? See WorkManager#initialize(Context, Configuration) or the class level Javadoc for more information."
            r2.<init>(r3)     // Catch:{ all -> 0x0672 }
            throw r2     // Catch:{ all -> 0x0672 }
        L_0x0060:
            if (r4 != 0) goto L_0x066a
            android.content.Context r4 = r2.getApplicationContext()     // Catch:{ all -> 0x0672 }
            bdm r5 = n     // Catch:{ all -> 0x0672 }
            if (r5 != 0) goto L_0x0663
            java.lang.String r5 = "context"
            defpackage.jnu.e(r4, r5)     // Catch:{ all -> 0x0672 }
            java.lang.String r5 = "configuration"
            defpackage.jnu.e(r3, r5)     // Catch:{ all -> 0x0672 }
            cyw r5 = new cyw     // Catch:{ all -> 0x0672 }
            java.util.concurrent.Executor r7 = r3.c     // Catch:{ all -> 0x0672 }
            r5.<init>(r7)     // Catch:{ all -> 0x0672 }
            android.content.Context r7 = r4.getApplicationContext()     // Catch:{ all -> 0x0672 }
            java.lang.String r8 = "context.applicationContext"
            defpackage.jnu.d(r7, r8)     // Catch:{ all -> 0x0672 }
            java.lang.Object r8 = r5.a     // Catch:{ all -> 0x0672 }
            java.lang.String r9 = "workTaskExecutor.serialTaskExecutor"
            defpackage.jnu.d(r8, r9)     // Catch:{ all -> 0x0672 }
            androidx.wear.ambient.AmbientModeSupport$AmbientCallback r9 = r3.q     // Catch:{ all -> 0x0672 }
            android.content.res.Resources r9 = r4.getResources()     // Catch:{ all -> 0x0672 }
            r10 = 2131034162(0x7f050032, float:1.7678834E38)
            boolean r9 = r9.getBoolean(r10)     // Catch:{ all -> 0x0672 }
            java.lang.String r10 = "context"
            defpackage.jnu.e(r7, r10)     // Catch:{ all -> 0x0672 }
            java.lang.String r10 = "queryExecutor"
            defpackage.jnu.e(r8, r10)     // Catch:{ all -> 0x0672 }
            r10 = 0
            r14 = 1
            if (r9 == 0) goto L_0x00b5
            java.lang.Class<androidx.work.impl.WorkDatabase> r9 = androidx.work.impl.WorkDatabase.class
            java.lang.String r11 = "context"
            defpackage.jnu.e(r7, r11)     // Catch:{ all -> 0x0672 }
            auq r11 = new auq     // Catch:{ all -> 0x0672 }
            r11.<init>(r7, r9, r10)     // Catch:{ all -> 0x0672 }
            r11.i = r14     // Catch:{ all -> 0x0672 }
            goto L_0x00d8
        L_0x00b5:
            java.lang.Class<androidx.work.impl.WorkDatabase> r9 = androidx.work.impl.WorkDatabase.class
            java.lang.String r11 = "context"
            java.lang.String r12 = "androidx.work.workdb"
            defpackage.jnu.e(r7, r11)     // Catch:{ all -> 0x0672 }
            boolean r11 = defpackage.job.p(r12)     // Catch:{ all -> 0x0672 }
            if (r11 != 0) goto L_0x065b
            java.lang.String r11 = ":memory:"
            boolean r11 = defpackage.jnu.i(r12, r11)     // Catch:{ all -> 0x0672 }
            if (r11 != 0) goto L_0x0653
            auq r11 = new auq     // Catch:{ all -> 0x0672 }
            r11.<init>(r7, r9, r12)     // Catch:{ all -> 0x0672 }
            bcz r9 = new bcz     // Catch:{ all -> 0x0672 }
            r9.<init>(r7)     // Catch:{ all -> 0x0672 }
            r11.h = r9     // Catch:{ all -> 0x0672 }
        L_0x00d8:
            java.lang.String r9 = "executor"
            defpackage.jnu.e(r8, r9)     // Catch:{ all -> 0x0672 }
            r11.f = r8     // Catch:{ all -> 0x0672 }
            bcd r8 = new bcd     // Catch:{ all -> 0x0672 }
            r8.<init>()     // Catch:{ all -> 0x0672 }
            java.util.List r9 = r11.d     // Catch:{ all -> 0x0672 }
            r9.add(r8)     // Catch:{ all -> 0x0672 }
            avu[] r8 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bcj r9 = defpackage.bcj.c     // Catch:{ all -> 0x0672 }
            r12 = 0
            r8[r12] = r9     // Catch:{ all -> 0x0672 }
            r11.a(r8)     // Catch:{ all -> 0x0672 }
            avu[] r8 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bcq r9 = new bcq     // Catch:{ all -> 0x0672 }
            r13 = 3
            r15 = 2
            r9.<init>(r7, r15, r13)     // Catch:{ all -> 0x0672 }
            r8[r12] = r9     // Catch:{ all -> 0x0672 }
            r11.a(r8)     // Catch:{ all -> 0x0672 }
            avu[] r8 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bck r9 = defpackage.bck.c     // Catch:{ all -> 0x0672 }
            r8[r12] = r9     // Catch:{ all -> 0x0672 }
            r11.a(r8)     // Catch:{ all -> 0x0672 }
            avu[] r8 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bcl r9 = defpackage.bcl.c     // Catch:{ all -> 0x0672 }
            r8[r12] = r9     // Catch:{ all -> 0x0672 }
            r11.a(r8)     // Catch:{ all -> 0x0672 }
            avu[] r8 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bcq r9 = new bcq     // Catch:{ all -> 0x0672 }
            r13 = 6
            r9.<init>(r7, r6, r13)     // Catch:{ all -> 0x0672 }
            r8[r12] = r9     // Catch:{ all -> 0x0672 }
            r11.a(r8)     // Catch:{ all -> 0x0672 }
            avu[] r6 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bcm r8 = defpackage.bcm.c     // Catch:{ all -> 0x0672 }
            r6[r12] = r8     // Catch:{ all -> 0x0672 }
            r11.a(r6)     // Catch:{ all -> 0x0672 }
            avu[] r6 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bcn r8 = defpackage.bcn.c     // Catch:{ all -> 0x0672 }
            r6[r12] = r8     // Catch:{ all -> 0x0672 }
            r11.a(r6)     // Catch:{ all -> 0x0672 }
            avu[] r6 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bco r8 = defpackage.bco.c     // Catch:{ all -> 0x0672 }
            r6[r12] = r8     // Catch:{ all -> 0x0672 }
            r11.a(r6)     // Catch:{ all -> 0x0672 }
            avu[] r6 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bdn r8 = new bdn     // Catch:{ all -> 0x0672 }
            r8.<init>(r7)     // Catch:{ all -> 0x0672 }
            r6[r12] = r8     // Catch:{ all -> 0x0672 }
            r11.a(r6)     // Catch:{ all -> 0x0672 }
            avu[] r6 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bcq r8 = new bcq     // Catch:{ all -> 0x0672 }
            r9 = 10
            r15 = 11
            r8.<init>(r7, r9, r15)     // Catch:{ all -> 0x0672 }
            r6[r12] = r8     // Catch:{ all -> 0x0672 }
            r11.a(r6)     // Catch:{ all -> 0x0672 }
            avu[] r6 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bcf r8 = defpackage.bcf.c     // Catch:{ all -> 0x0672 }
            r6[r12] = r8     // Catch:{ all -> 0x0672 }
            r11.a(r6)     // Catch:{ all -> 0x0672 }
            avu[] r6 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bcg r8 = defpackage.bcg.c     // Catch:{ all -> 0x0672 }
            r6[r12] = r8     // Catch:{ all -> 0x0672 }
            r11.a(r6)     // Catch:{ all -> 0x0672 }
            avu[] r6 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bch r8 = defpackage.bch.c     // Catch:{ all -> 0x0672 }
            r6[r12] = r8     // Catch:{ all -> 0x0672 }
            r11.a(r6)     // Catch:{ all -> 0x0672 }
            avu[] r6 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bci r8 = defpackage.bci.c     // Catch:{ all -> 0x0672 }
            r6[r12] = r8     // Catch:{ all -> 0x0672 }
            r11.a(r6)     // Catch:{ all -> 0x0672 }
            avu[] r6 = new defpackage.avu[r14]     // Catch:{ all -> 0x0672 }
            bcq r8 = new bcq     // Catch:{ all -> 0x0672 }
            r9 = 21
            r15 = 22
            r8.<init>(r7, r9, r15)     // Catch:{ all -> 0x0672 }
            r6[r12] = r8     // Catch:{ all -> 0x0672 }
            r11.a(r6)     // Catch:{ all -> 0x0672 }
            r11.n = r12     // Catch:{ all -> 0x0672 }
            r11.o = r14     // Catch:{ all -> 0x0672 }
            java.util.concurrent.Executor r6 = r11.f     // Catch:{ all -> 0x0672 }
            if (r6 != 0) goto L_0x01a0
            java.util.concurrent.Executor r7 = r11.g     // Catch:{ all -> 0x0672 }
            if (r7 != 0) goto L_0x01a0
            java.util.concurrent.Executor r6 = defpackage.nz.a     // Catch:{ all -> 0x0672 }
            r11.g = r6     // Catch:{ all -> 0x0672 }
            java.util.concurrent.Executor r6 = r11.g     // Catch:{ all -> 0x0672 }
            r11.f = r6     // Catch:{ all -> 0x0672 }
            goto L_0x01af
        L_0x01a0:
            if (r6 == 0) goto L_0x01a9
            java.util.concurrent.Executor r7 = r11.g     // Catch:{ all -> 0x0672 }
            if (r7 != 0) goto L_0x01a9
            r11.g = r6     // Catch:{ all -> 0x0672 }
            goto L_0x01af
        L_0x01a9:
            if (r6 != 0) goto L_0x01af
            java.util.concurrent.Executor r6 = r11.g     // Catch:{ all -> 0x0672 }
            r11.f = r6     // Catch:{ all -> 0x0672 }
        L_0x01af:
            java.util.Set r6 = r11.l     // Catch:{ all -> 0x0672 }
            java.util.Set r7 = r11.k     // Catch:{ all -> 0x0672 }
            boolean r8 = r6.isEmpty()     // Catch:{ all -> 0x0672 }
            if (r8 != 0) goto L_0x01e4
            java.util.Iterator r6 = r6.iterator()     // Catch:{ all -> 0x0672 }
        L_0x01bd:
            boolean r8 = r6.hasNext()     // Catch:{ all -> 0x0672 }
            if (r8 == 0) goto L_0x01e4
            java.lang.Object r8 = r6.next()     // Catch:{ all -> 0x0672 }
            java.lang.Number r8 = (java.lang.Number) r8     // Catch:{ all -> 0x0672 }
            int r8 = r8.intValue()     // Catch:{ all -> 0x0672 }
            java.lang.Integer r9 = java.lang.Integer.valueOf(r8)     // Catch:{ all -> 0x0672 }
            boolean r9 = r7.contains(r9)     // Catch:{ all -> 0x0672 }
            if (r9 != 0) goto L_0x01d8
            goto L_0x01bd
        L_0x01d8:
            java.lang.String r2 = "Inconsistency detected. A Migration was supplied to addMigration() that has a start or end version equal to a start version supplied to fallbackToDestructiveMigrationFrom(). Start version is: "
            java.lang.String r2 = defpackage.a.ak(r8, r2)     // Catch:{ all -> 0x0672 }
            java.lang.IllegalArgumentException r3 = new java.lang.IllegalArgumentException     // Catch:{ all -> 0x0672 }
            r3.<init>(r2)     // Catch:{ all -> 0x0672 }
            throw r3     // Catch:{ all -> 0x0672 }
        L_0x01e4:
            awo r6 = r11.h     // Catch:{ all -> 0x0672 }
            if (r6 != 0) goto L_0x01ed
            axa r6 = new axa     // Catch:{ all -> 0x0672 }
            r6.<init>()     // Catch:{ all -> 0x0672 }
        L_0x01ed:
            r18 = r6
            atz r6 = new atz     // Catch:{ all -> 0x0672 }
            android.content.Context r7 = r11.b     // Catch:{ all -> 0x0672 }
            java.lang.String r8 = r11.c     // Catch:{ all -> 0x0672 }
            byw r9 = r11.p     // Catch:{ all -> 0x0672 }
            java.util.List r15 = r11.d     // Catch:{ all -> 0x0672 }
            boolean r12 = r11.i     // Catch:{ all -> 0x0672 }
            aur r14 = r11.j     // Catch:{ all -> 0x0672 }
            java.lang.String r13 = "context"
            defpackage.jnu.e(r7, r13)     // Catch:{ all -> 0x0672 }
            aur r13 = defpackage.aur.AUTOMATIC     // Catch:{ all -> 0x0672 }
            if (r14 != r13) goto L_0x0224
            java.lang.String r13 = "activity"
            java.lang.Object r13 = r7.getSystemService(r13)     // Catch:{ all -> 0x0672 }
            boolean r14 = r13 instanceof android.app.ActivityManager     // Catch:{ all -> 0x0672 }
            if (r14 == 0) goto L_0x0213
            android.app.ActivityManager r13 = (android.app.ActivityManager) r13     // Catch:{ all -> 0x0672 }
            goto L_0x0214
        L_0x0213:
            r13 = r10
        L_0x0214:
            if (r13 == 0) goto L_0x021f
            boolean r13 = r13.isLowRamDevice()     // Catch:{ all -> 0x0672 }
            if (r13 != 0) goto L_0x021f
            aur r13 = defpackage.aur.WRITE_AHEAD_LOGGING     // Catch:{ all -> 0x0672 }
            goto L_0x0221
        L_0x021f:
            aur r13 = defpackage.aur.TRUNCATE     // Catch:{ all -> 0x0672 }
        L_0x0221:
            r22 = r13
            goto L_0x0226
        L_0x0224:
            r22 = r14
        L_0x0226:
            java.util.concurrent.Executor r13 = r11.f     // Catch:{ all -> 0x0672 }
            if (r13 == 0) goto L_0x064b
            java.util.concurrent.Executor r14 = r11.g     // Catch:{ all -> 0x0672 }
            if (r14 != 0) goto L_0x0236
            java.lang.IllegalArgumentException r2 = new java.lang.IllegalArgumentException     // Catch:{ all -> 0x0672 }
            java.lang.String r3 = "Required value was null."
            r2.<init>(r3)     // Catch:{ all -> 0x0672 }
            throw r2     // Catch:{ all -> 0x0672 }
        L_0x0236:
            boolean r10 = r11.n     // Catch:{ all -> 0x0672 }
            r30 = r2
            boolean r2 = r11.o     // Catch:{ all -> 0x0672 }
            r31 = r3
            java.util.Set r3 = r11.k     // Catch:{ all -> 0x0672 }
            r32 = r5
            java.util.List r5 = r11.e     // Catch:{ all -> 0x0672 }
            r33 = r4
            java.util.List r4 = r11.m     // Catch:{ all -> 0x0672 }
            r20 = r15
            r15 = r6
            r16 = r7
            r17 = r8
            r19 = r9
            r21 = r12
            r23 = r13
            r24 = r14
            r25 = r10
            r26 = r2
            r27 = r3
            r28 = r5
            r29 = r4
            r15.<init>(r16, r17, r18, r19, r20, r21, r22, r23, r24, r25, r26, r27, r28, r29)     // Catch:{ all -> 0x0672 }
            joq r2 = r11.a     // Catch:{ all -> 0x0672 }
            java.lang.Class r2 = defpackage.jnu.v(r2)     // Catch:{ all -> 0x0672 }
            java.lang.Object r2 = defpackage.wa.y(r2)     // Catch:{ all -> 0x0672 }
            aus r2 = (defpackage.aus) r2     // Catch:{ all -> 0x0672 }
            jjr r3 = new jjr     // Catch:{ jjr -> 0x0277 }
            r4 = 0
            r3.<init>(r4)     // Catch:{ jjr -> 0x0277 }
            throw r3     // Catch:{ jjr -> 0x0277 }
        L_0x0277:
            androidx.wear.ambient.AmbientDelegate r3 = new androidx.wear.ambient.AmbientDelegate     // Catch:{ all -> 0x0672 }
            mz r4 = new mz     // Catch:{ all -> 0x0672 }
            r5 = 6
            r4.<init>(r2, r5)     // Catch:{ all -> 0x0672 }
            r3.<init>((defpackage.atz) r6, (defpackage.jna) r4)     // Catch:{ all -> 0x0672 }
            r2.h = r3     // Catch:{ all -> 0x0672 }
            auk r3 = r2.a()     // Catch:{ all -> 0x0672 }
            r2.e = r3     // Catch:{ all -> 0x0672 }
            if (r2 != 0) goto L_0x0291
            java.lang.String r3 = "<this>"
            defpackage.jnu.g(r3)     // Catch:{ all -> 0x0672 }
        L_0x0291:
            java.util.LinkedHashMap r3 = new java.util.LinkedHashMap     // Catch:{ all -> 0x0672 }
            r3.<init>()     // Catch:{ all -> 0x0672 }
            java.util.Set r4 = r2.h()     // Catch:{ all -> 0x0672 }
            java.util.ArrayList r5 = new java.util.ArrayList     // Catch:{ all -> 0x0672 }
            int r7 = defpackage.jji.K(r4)     // Catch:{ all -> 0x0672 }
            r5.<init>(r7)     // Catch:{ all -> 0x0672 }
            java.util.Iterator r4 = r4.iterator()     // Catch:{ all -> 0x0672 }
        L_0x02a7:
            boolean r7 = r4.hasNext()     // Catch:{ all -> 0x0672 }
            if (r7 == 0) goto L_0x02bb
            java.lang.Object r7 = r4.next()     // Catch:{ all -> 0x0672 }
            java.lang.Class r7 = (java.lang.Class) r7     // Catch:{ all -> 0x0672 }
            joq r7 = defpackage.jnu.x(r7)     // Catch:{ all -> 0x0672 }
            r5.add(r7)     // Catch:{ all -> 0x0672 }
            goto L_0x02a7
        L_0x02bb:
            java.util.Set r4 = defpackage.jji.G(r5)     // Catch:{ all -> 0x0672 }
            int r5 = r4.size()     // Catch:{ all -> 0x0672 }
            boolean[] r7 = new boolean[r5]     // Catch:{ all -> 0x0672 }
            java.util.Iterator r4 = r4.iterator()     // Catch:{ all -> 0x0672 }
        L_0x02c9:
            boolean r8 = r4.hasNext()     // Catch:{ all -> 0x0672 }
            r9 = -1
            if (r8 == 0) goto L_0x032e
            java.lang.Object r8 = r4.next()     // Catch:{ all -> 0x0672 }
            joq r8 = (defpackage.joq) r8     // Catch:{ all -> 0x0672 }
            java.util.List r10 = r6.m     // Catch:{ all -> 0x0672 }
            int r10 = r10.size()     // Catch:{ all -> 0x0672 }
            int r10 = r10 + r9
            if (r10 < 0) goto L_0x0302
        L_0x02df:
            int r11 = r10 + -1
            java.util.List r12 = r6.m     // Catch:{ all -> 0x0672 }
            java.lang.Object r12 = r12.get(r10)     // Catch:{ all -> 0x0672 }
            java.lang.Class r12 = r12.getClass()     // Catch:{ all -> 0x0672 }
            int r13 = defpackage.joa.a     // Catch:{ all -> 0x0672 }
            jnq r13 = new jnq     // Catch:{ all -> 0x0672 }
            r13.<init>(r12)     // Catch:{ all -> 0x0672 }
            boolean r12 = defpackage.wa.x(r8, r13)     // Catch:{ all -> 0x0672 }
            if (r12 == 0) goto L_0x02fd
            r12 = 1
            r7[r10] = r12     // Catch:{ all -> 0x0672 }
            r9 = r10
            goto L_0x0302
        L_0x02fd:
            if (r11 >= 0) goto L_0x0300
            goto L_0x0302
        L_0x0300:
            r10 = r11
            goto L_0x02df
        L_0x0302:
            if (r9 < 0) goto L_0x030e
            java.util.List r10 = r6.m     // Catch:{ all -> 0x0672 }
            java.lang.Object r9 = r10.get(r9)     // Catch:{ all -> 0x0672 }
            r3.put(r8, r9)     // Catch:{ all -> 0x0672 }
            goto L_0x02c9
        L_0x030e:
            java.lang.StringBuilder r2 = new java.lang.StringBuilder     // Catch:{ all -> 0x0672 }
            r2.<init>()     // Catch:{ all -> 0x0672 }
            java.lang.String r3 = "A required auto migration spec ("
            r2.append(r3)     // Catch:{ all -> 0x0672 }
            java.lang.String r3 = r8.b()     // Catch:{ all -> 0x0672 }
            r2.append(r3)     // Catch:{ all -> 0x0672 }
            java.lang.String r3 = ") is missing in the database configuration."
            r2.append(r3)     // Catch:{ all -> 0x0672 }
            java.lang.String r2 = r2.toString()     // Catch:{ all -> 0x0672 }
            java.lang.IllegalArgumentException r3 = new java.lang.IllegalArgumentException     // Catch:{ all -> 0x0672 }
            r3.<init>(r2)     // Catch:{ all -> 0x0672 }
            throw r3     // Catch:{ all -> 0x0672 }
        L_0x032e:
            java.util.List r4 = r6.m     // Catch:{ all -> 0x0672 }
            int r4 = r4.size()     // Catch:{ all -> 0x0672 }
            int r4 = r4 + r9
            if (r4 >= 0) goto L_0x0338
            goto L_0x0342
        L_0x0338:
            int r8 = r4 + -1
            if (r4 >= r5) goto L_0x0643
            boolean r4 = r7[r4]     // Catch:{ all -> 0x0672 }
            if (r4 == 0) goto L_0x0643
            if (r8 >= 0) goto L_0x0640
        L_0x0342:
            java.util.LinkedHashMap r4 = new java.util.LinkedHashMap     // Catch:{ all -> 0x0672 }
            int r5 = r3.size()     // Catch:{ all -> 0x0672 }
            int r5 = defpackage.jji.e(r5)     // Catch:{ all -> 0x0672 }
            r4.<init>(r5)     // Catch:{ all -> 0x0672 }
            java.util.Set r3 = r3.entrySet()     // Catch:{ all -> 0x0672 }
            java.util.Iterator r3 = r3.iterator()     // Catch:{ all -> 0x0672 }
        L_0x0357:
            boolean r5 = r3.hasNext()     // Catch:{ all -> 0x0672 }
            if (r5 == 0) goto L_0x0375
            java.lang.Object r5 = r3.next()     // Catch:{ all -> 0x0672 }
            java.util.Map$Entry r5 = (java.util.Map.Entry) r5     // Catch:{ all -> 0x0672 }
            java.lang.Object r7 = r5.getKey()     // Catch:{ all -> 0x0672 }
            joq r7 = (defpackage.joq) r7     // Catch:{ all -> 0x0672 }
            java.lang.Class r7 = defpackage.jnu.v(r7)     // Catch:{ all -> 0x0672 }
            java.lang.Object r5 = r5.getValue()     // Catch:{ all -> 0x0672 }
            r4.put(r7, r5)     // Catch:{ all -> 0x0672 }
            goto L_0x0357
        L_0x0375:
            java.util.List r3 = r2.s()     // Catch:{ all -> 0x0672 }
            java.util.Iterator r3 = r3.iterator()     // Catch:{ all -> 0x0672 }
        L_0x037d:
            boolean r4 = r3.hasNext()     // Catch:{ all -> 0x0672 }
            if (r4 == 0) goto L_0x03b5
            java.lang.Object r4 = r3.next()     // Catch:{ all -> 0x0672 }
            avu r4 = (defpackage.avu) r4     // Catch:{ all -> 0x0672 }
            byw r5 = r6.n     // Catch:{ all -> 0x0672 }
            int r7 = r4.a     // Catch:{ all -> 0x0672 }
            int r8 = r4.b     // Catch:{ all -> 0x0672 }
            java.lang.Object r5 = r5.a     // Catch:{ all -> 0x0672 }
            java.lang.Integer r7 = java.lang.Integer.valueOf(r7)     // Catch:{ all -> 0x0672 }
            boolean r10 = r5.containsKey(r7)     // Catch:{ all -> 0x0672 }
            if (r10 == 0) goto L_0x03af
            java.lang.Object r5 = r5.get(r7)     // Catch:{ all -> 0x0672 }
            java.util.Map r5 = (java.util.Map) r5     // Catch:{ all -> 0x0672 }
            if (r5 != 0) goto L_0x03a5
            jkr r5 = defpackage.jkr.a     // Catch:{ all -> 0x0672 }
        L_0x03a5:
            java.lang.Integer r7 = java.lang.Integer.valueOf(r8)     // Catch:{ all -> 0x0672 }
            boolean r5 = r5.containsKey(r7)     // Catch:{ all -> 0x0672 }
            if (r5 != 0) goto L_0x037d
        L_0x03af:
            byw r5 = r6.n     // Catch:{ all -> 0x0672 }
            r5.X(r4)     // Catch:{ all -> 0x0672 }
            goto L_0x037d
        L_0x03b5:
            if (r2 != 0) goto L_0x03bc
            java.lang.String r3 = "<this>"
            defpackage.jnu.g(r3)     // Catch:{ all -> 0x0672 }
        L_0x03bc:
            java.util.Map r3 = r2.g()     // Catch:{ all -> 0x0672 }
            java.util.Set r3 = r3.entrySet()     // Catch:{ all -> 0x0672 }
            int r4 = defpackage.jji.K(r3)     // Catch:{ all -> 0x0672 }
            int r4 = defpackage.jji.e(r4)     // Catch:{ all -> 0x0672 }
            java.util.LinkedHashMap r5 = new java.util.LinkedHashMap     // Catch:{ all -> 0x0672 }
            r7 = 16
            int r4 = defpackage.jnu.o(r4, r7)     // Catch:{ all -> 0x0672 }
            r5.<init>(r4)     // Catch:{ all -> 0x0672 }
            java.util.Iterator r3 = r3.iterator()     // Catch:{ all -> 0x0672 }
        L_0x03db:
            boolean r4 = r3.hasNext()     // Catch:{ all -> 0x0672 }
            if (r4 == 0) goto L_0x0425
            java.lang.Object r4 = r3.next()     // Catch:{ all -> 0x0672 }
            java.util.Map$Entry r4 = (java.util.Map.Entry) r4     // Catch:{ all -> 0x0672 }
            java.lang.Object r7 = r4.getKey()     // Catch:{ all -> 0x0672 }
            java.lang.Class r7 = (java.lang.Class) r7     // Catch:{ all -> 0x0672 }
            java.lang.Object r4 = r4.getValue()     // Catch:{ all -> 0x0672 }
            java.util.List r4 = (java.util.List) r4     // Catch:{ all -> 0x0672 }
            joq r7 = defpackage.jnu.x(r7)     // Catch:{ all -> 0x0672 }
            java.util.ArrayList r8 = new java.util.ArrayList     // Catch:{ all -> 0x0672 }
            int r10 = defpackage.jji.K(r4)     // Catch:{ all -> 0x0672 }
            r8.<init>(r10)     // Catch:{ all -> 0x0672 }
            java.util.Iterator r4 = r4.iterator()     // Catch:{ all -> 0x0672 }
        L_0x0404:
            boolean r10 = r4.hasNext()     // Catch:{ all -> 0x0672 }
            if (r10 == 0) goto L_0x0418
            java.lang.Object r10 = r4.next()     // Catch:{ all -> 0x0672 }
            java.lang.Class r10 = (java.lang.Class) r10     // Catch:{ all -> 0x0672 }
            joq r10 = defpackage.jnu.x(r10)     // Catch:{ all -> 0x0672 }
            r8.add(r10)     // Catch:{ all -> 0x0672 }
            goto L_0x0404
        L_0x0418:
            jjs r4 = new jjs     // Catch:{ all -> 0x0672 }
            r4.<init>(r7, r8)     // Catch:{ all -> 0x0672 }
            java.lang.Object r7 = r4.a     // Catch:{ all -> 0x0672 }
            java.lang.Object r4 = r4.b     // Catch:{ all -> 0x0672 }
            r5.put(r7, r4)     // Catch:{ all -> 0x0672 }
            goto L_0x03db
        L_0x0425:
            int r3 = r5.size()     // Catch:{ all -> 0x0672 }
            boolean[] r3 = new boolean[r3]     // Catch:{ all -> 0x0672 }
            java.util.Set r4 = r5.entrySet()     // Catch:{ all -> 0x0672 }
            java.util.Iterator r4 = r4.iterator()     // Catch:{ all -> 0x0672 }
        L_0x0433:
            boolean r5 = r4.hasNext()     // Catch:{ all -> 0x0672 }
            if (r5 == 0) goto L_0x04cb
            java.lang.Object r5 = r4.next()     // Catch:{ all -> 0x0672 }
            java.util.Map$Entry r5 = (java.util.Map.Entry) r5     // Catch:{ all -> 0x0672 }
            java.lang.Object r7 = r5.getKey()     // Catch:{ all -> 0x0672 }
            joq r7 = (defpackage.joq) r7     // Catch:{ all -> 0x0672 }
            java.lang.Object r5 = r5.getValue()     // Catch:{ all -> 0x0672 }
            java.util.List r5 = (java.util.List) r5     // Catch:{ all -> 0x0672 }
            java.util.Iterator r5 = r5.iterator()     // Catch:{ all -> 0x0672 }
        L_0x044f:
            boolean r8 = r5.hasNext()     // Catch:{ all -> 0x0672 }
            if (r8 == 0) goto L_0x0433
            java.lang.Object r8 = r5.next()     // Catch:{ all -> 0x0672 }
            joq r8 = (defpackage.joq) r8     // Catch:{ all -> 0x0672 }
            java.util.List r10 = r6.l     // Catch:{ all -> 0x0672 }
            int r10 = r10.size()     // Catch:{ all -> 0x0672 }
            int r10 = r10 + r9
            if (r10 < 0) goto L_0x0486
        L_0x0464:
            int r11 = r10 + -1
            java.util.List r12 = r6.l     // Catch:{ all -> 0x0672 }
            java.lang.Object r12 = r12.get(r10)     // Catch:{ all -> 0x0672 }
            java.lang.Class r12 = r12.getClass()     // Catch:{ all -> 0x0672 }
            int r13 = defpackage.joa.a     // Catch:{ all -> 0x0672 }
            jnq r13 = new jnq     // Catch:{ all -> 0x0672 }
            r13.<init>(r12)     // Catch:{ all -> 0x0672 }
            boolean r12 = defpackage.wa.x(r8, r13)     // Catch:{ all -> 0x0672 }
            if (r12 == 0) goto L_0x0481
            r12 = 1
            r3[r10] = r12     // Catch:{ all -> 0x0672 }
            goto L_0x0487
        L_0x0481:
            if (r11 >= 0) goto L_0x0484
            goto L_0x0486
        L_0x0484:
            r10 = r11
            goto L_0x0464
        L_0x0486:
            r10 = r9
        L_0x0487:
            if (r10 < 0) goto L_0x049f
            java.util.List r11 = r6.l     // Catch:{ all -> 0x0672 }
            java.lang.Object r10 = r11.get(r10)     // Catch:{ all -> 0x0672 }
            java.lang.String r11 = "kclass"
            defpackage.jnu.e(r8, r11)     // Catch:{ all -> 0x0672 }
            java.lang.String r11 = "converter"
            defpackage.jnu.e(r10, r11)     // Catch:{ all -> 0x0672 }
            java.util.Map r11 = r2.g     // Catch:{ all -> 0x0672 }
            r11.put(r8, r10)     // Catch:{ all -> 0x0672 }
            goto L_0x044f
        L_0x049f:
            java.lang.StringBuilder r2 = new java.lang.StringBuilder     // Catch:{ all -> 0x0672 }
            r2.<init>()     // Catch:{ all -> 0x0672 }
            java.lang.String r3 = "A required type converter ("
            r2.append(r3)     // Catch:{ all -> 0x0672 }
            java.lang.String r3 = r8.b()     // Catch:{ all -> 0x0672 }
            r2.append(r3)     // Catch:{ all -> 0x0672 }
            java.lang.String r3 = ") for "
            r2.append(r3)     // Catch:{ all -> 0x0672 }
            java.lang.String r3 = r7.b()     // Catch:{ all -> 0x0672 }
            r2.append(r3)     // Catch:{ all -> 0x0672 }
            java.lang.String r3 = " is missing in the database configuration."
            r2.append(r3)     // Catch:{ all -> 0x0672 }
            java.lang.String r2 = r2.toString()     // Catch:{ all -> 0x0672 }
            java.lang.IllegalArgumentException r3 = new java.lang.IllegalArgumentException     // Catch:{ all -> 0x0672 }
            r3.<init>(r2)     // Catch:{ all -> 0x0672 }
            throw r3     // Catch:{ all -> 0x0672 }
        L_0x04cb:
            java.util.List r4 = r6.l     // Catch:{ all -> 0x0672 }
            int r4 = r4.size()     // Catch:{ all -> 0x0672 }
            int r4 = r4 + r9
            if (r4 < 0) goto L_0x0501
        L_0x04d4:
            int r5 = r4 + -1
            boolean r7 = r3[r4]     // Catch:{ all -> 0x0672 }
            if (r7 == 0) goto L_0x04df
            if (r5 >= 0) goto L_0x04dd
            goto L_0x0501
        L_0x04dd:
            r4 = r5
            goto L_0x04d4
        L_0x04df:
            java.util.List r2 = r6.l     // Catch:{ all -> 0x0672 }
            java.lang.Object r2 = r2.get(r4)     // Catch:{ all -> 0x0672 }
            java.lang.IllegalArgumentException r3 = new java.lang.IllegalArgumentException     // Catch:{ all -> 0x0672 }
            java.lang.StringBuilder r4 = new java.lang.StringBuilder     // Catch:{ all -> 0x0672 }
            r4.<init>()     // Catch:{ all -> 0x0672 }
            java.lang.String r5 = "Unexpected type converter "
            r4.append(r5)     // Catch:{ all -> 0x0672 }
            r4.append(r2)     // Catch:{ all -> 0x0672 }
            java.lang.String r2 = ". Annotate TypeConverter class with @ProvidedTypeConverter annotation or remove this converter from the builder."
            r4.append(r2)     // Catch:{ all -> 0x0672 }
            java.lang.String r2 = r4.toString()     // Catch:{ all -> 0x0672 }
            r3.<init>(r2)     // Catch:{ all -> 0x0672 }
            throw r3     // Catch:{ all -> 0x0672 }
        L_0x0501:
            androidx.wear.ambient.AmbientDelegate r3 = r2.h     // Catch:{ all -> 0x0672 }
            if (r3 != 0) goto L_0x050b
            java.lang.String r3 = "connectionManager"
            defpackage.jnu.h(r3)     // Catch:{ all -> 0x0672 }
            r3 = 0
        L_0x050b:
            awp r3 = r3.B()     // Catch:{ all -> 0x0672 }
            java.lang.Class<avw> r4 = defpackage.avw.class
            java.lang.Object r3 = r2.f(r4, r3)     // Catch:{ all -> 0x0672 }
            avw r3 = (defpackage.avw) r3     // Catch:{ all -> 0x0672 }
            if (r3 != 0) goto L_0x063e
            androidx.wear.ambient.AmbientDelegate r3 = r2.h     // Catch:{ all -> 0x0672 }
            if (r3 != 0) goto L_0x0523
            java.lang.String r3 = "connectionManager"
            defpackage.jnu.h(r3)     // Catch:{ all -> 0x0672 }
            r3 = 0
        L_0x0523:
            awp r3 = r3.B()     // Catch:{ all -> 0x0672 }
            java.lang.Class<avv> r4 = defpackage.avv.class
            java.lang.Object r3 = r2.f(r4, r3)     // Catch:{ all -> 0x0672 }
            avv r3 = (defpackage.avv) r3     // Catch:{ all -> 0x0672 }
            if (r3 != 0) goto L_0x063c
            java.util.concurrent.Executor r3 = r6.g     // Catch:{ all -> 0x0672 }
            r2.c = r3     // Catch:{ all -> 0x0672 }
            bih r3 = new bih     // Catch:{ all -> 0x0672 }
            java.util.concurrent.Executor r4 = r6.h     // Catch:{ all -> 0x0672 }
            r5 = 0
            r7 = 1
            r3.<init>(r4, r7, r5)     // Catch:{ all -> 0x0672 }
            r2.d = r3     // Catch:{ all -> 0x0672 }
            java.util.concurrent.Executor r4 = r2.c     // Catch:{ all -> 0x0672 }
            if (r4 != 0) goto L_0x054a
            java.lang.String r3 = "internalQueryExecutor"
            defpackage.jnu.h(r3)     // Catch:{ all -> 0x0672 }
            r4 = 0
        L_0x054a:
            jqp r3 = defpackage.jnu.I(r4)     // Catch:{ all -> 0x0672 }
            jsr r4 = new jsr     // Catch:{ all -> 0x0672 }
            r4.<init>()     // Catch:{ all -> 0x0672 }
            jlv r3 = r3.plus(r4)     // Catch:{ all -> 0x0672 }
            jqs r3 = defpackage.jqw.e(r3)     // Catch:{ all -> 0x0672 }
            r2.b = r3     // Catch:{ all -> 0x0672 }
            jqs r4 = r2.b     // Catch:{ all -> 0x0672 }
            if (r4 != 0) goto L_0x0567
            java.lang.String r3 = "coroutineScope"
            defpackage.jnu.h(r3)     // Catch:{ all -> 0x0672 }
            r4 = 0
        L_0x0567:
            jwo r4 = (defpackage.jwo) r4     // Catch:{ all -> 0x0672 }
            jlv r3 = r4.a     // Catch:{ all -> 0x0672 }
            java.util.concurrent.Executor r4 = r2.d     // Catch:{ all -> 0x0672 }
            if (r4 != 0) goto L_0x0576
            java.lang.String r4 = "internalTransactionExecutor"
            defpackage.jnu.h(r4)     // Catch:{ all -> 0x0672 }
            r10 = 0
            goto L_0x0577
        L_0x0576:
            r10 = r4
        L_0x0577:
            jqp r4 = defpackage.jnu.I(r10)     // Catch:{ all -> 0x0672 }
            r3.plus(r4)     // Catch:{ all -> 0x0672 }
            boolean r3 = r6.e     // Catch:{ all -> 0x0672 }
            r2.f = r3     // Catch:{ all -> 0x0672 }
            androidx.work.impl.WorkDatabase r2 = (androidx.work.impl.WorkDatabase) r2     // Catch:{ all -> 0x0672 }
            alx r14 = new alx     // Catch:{ all -> 0x0672 }
            android.content.Context r4 = r33.getApplicationContext()     // Catch:{ all -> 0x0672 }
            java.lang.String r3 = "context.applicationContext"
            defpackage.jnu.d(r4, r3)     // Catch:{ all -> 0x0672 }
            bfm r5 = new bfm     // Catch:{ all -> 0x0672 }
            android.content.Context r3 = r4.getApplicationContext()     // Catch:{ all -> 0x0672 }
            java.lang.String r6 = "context.applicationContext"
            defpackage.jnu.d(r3, r6)     // Catch:{ all -> 0x0672 }
            r15 = r32
            r5.<init>(r3, r15)     // Catch:{ all -> 0x0672 }
            bfo r6 = new bfo     // Catch:{ all -> 0x0672 }
            android.content.Context r3 = r4.getApplicationContext()     // Catch:{ all -> 0x0672 }
            java.lang.String r7 = "context.applicationContext"
            defpackage.jnu.d(r3, r7)     // Catch:{ all -> 0x0672 }
            r6.<init>(r3, r15)     // Catch:{ all -> 0x0672 }
            android.content.Context r3 = r4.getApplicationContext()     // Catch:{ all -> 0x0672 }
            java.lang.String r7 = "context.applicationContext"
            defpackage.jnu.d(r3, r7)     // Catch:{ all -> 0x0672 }
            java.lang.String r7 = "context"
            defpackage.jnu.e(r3, r7)     // Catch:{ all -> 0x0672 }
            bfw r7 = new bfw     // Catch:{ all -> 0x0672 }
            r7.<init>(r3, r15)     // Catch:{ all -> 0x0672 }
            bfy r8 = new bfy     // Catch:{ all -> 0x0672 }
            android.content.Context r3 = r4.getApplicationContext()     // Catch:{ all -> 0x0672 }
            java.lang.String r9 = "context.applicationContext"
            defpackage.jnu.d(r3, r9)     // Catch:{ all -> 0x0672 }
            r8.<init>(r3, r15)     // Catch:{ all -> 0x0672 }
            r3 = r14
            r3.<init>((android.content.Context) r4, (defpackage.bft) r5, (defpackage.bfo) r6, (defpackage.bft) r7, (defpackage.bft) r8)     // Catch:{ all -> 0x0672 }
            bcp r3 = new bcp     // Catch:{ all -> 0x0672 }
            android.content.Context r4 = r33.getApplicationContext()     // Catch:{ all -> 0x0672 }
            r5 = r31
            r3.<init>(r4, r5, r15, r2)     // Catch:{ all -> 0x0672 }
            java.lang.String r4 = "context"
            r6 = r33
            defpackage.jnu.e(r6, r4)     // Catch:{ all -> 0x0672 }
            java.lang.String r4 = "configuration"
            defpackage.jnu.e(r5, r4)     // Catch:{ all -> 0x0672 }
            java.lang.String r4 = "workDatabase"
            defpackage.jnu.e(r2, r4)     // Catch:{ all -> 0x0672 }
            java.lang.String r4 = "p0"
            defpackage.jnu.e(r6, r4)     // Catch:{ all -> 0x0672 }
            java.lang.String r4 = "p1"
            defpackage.jnu.e(r5, r4)     // Catch:{ all -> 0x0672 }
            java.lang.String r4 = "p3"
            defpackage.jnu.e(r2, r4)     // Catch:{ all -> 0x0672 }
            r4 = 2
            bcr[] r4 = new defpackage.bcr[r4]     // Catch:{ all -> 0x0672 }
            int r7 = defpackage.bct.a     // Catch:{ all -> 0x0672 }
            bep r7 = new bep     // Catch:{ all -> 0x0672 }
            r7.<init>(r6, r2, r5)     // Catch:{ all -> 0x0672 }
            java.lang.Class<androidx.work.impl.background.systemjob.SystemJobService> r8 = androidx.work.impl.background.systemjob.SystemJobService.class
            r9 = 1
            defpackage.bif.a(r6, r8, r9)     // Catch:{ all -> 0x0672 }
            defpackage.bbk.a()     // Catch:{ all -> 0x0672 }
            r10 = 0
            r4[r10] = r7     // Catch:{ all -> 0x0672 }
            beb r16 = new beb     // Catch:{ all -> 0x0672 }
            bxq r12 = new bxq     // Catch:{ all -> 0x0672 }
            r12.<init>((defpackage.bcp) r3, (defpackage.cyw) r15)     // Catch:{ all -> 0x0672 }
            r7 = r16
            r8 = r6
            r9 = r5
            r10 = r14
            r11 = r3
            r13 = r15
            r7.<init>(r8, r9, r10, r11, r12, r13)     // Catch:{ all -> 0x0672 }
            r11 = 1
            r4[r11] = r16     // Catch:{ all -> 0x0672 }
            java.util.List r12 = defpackage.jji.o(r4)     // Catch:{ all -> 0x0672 }
            bdm r4 = new bdm     // Catch:{ all -> 0x0672 }
            android.content.Context r8 = r6.getApplicationContext()     // Catch:{ all -> 0x0672 }
            r7 = r4
            r9 = r5
            r10 = r15
            r11 = r2
            r13 = r3
            r7.<init>(r8, r9, r10, r11, r12, r13, r14)     // Catch:{ all -> 0x0672 }
            n = r4     // Catch:{ all -> 0x0672 }
            goto L_0x0665
        L_0x063c:
            r2 = 0
            throw r2     // Catch:{ all -> 0x0672 }
        L_0x063e:
            r12 = 0
            throw r12     // Catch:{ all -> 0x0672 }
        L_0x0640:
            r4 = r8
            goto L_0x0338
        L_0x0643:
            java.lang.IllegalArgumentException r2 = new java.lang.IllegalArgumentException     // Catch:{ all -> 0x0672 }
            java.lang.String r3 = "Unexpected auto migration specs found. Annotate AutoMigrationSpec implementation with @ProvidedAutoMigrationSpec annotation or remove this spec from the builder."
            r2.<init>(r3)     // Catch:{ all -> 0x0672 }
            throw r2     // Catch:{ all -> 0x0672 }
        L_0x064b:
            java.lang.IllegalArgumentException r2 = new java.lang.IllegalArgumentException     // Catch:{ all -> 0x0672 }
            java.lang.String r3 = "Required value was null."
            r2.<init>(r3)     // Catch:{ all -> 0x0672 }
            throw r2     // Catch:{ all -> 0x0672 }
        L_0x0653:
            java.lang.String r2 = "Cannot build a database with the special name ':memory:'. If you are trying to create an in memory database, use Room.inMemoryDatabaseBuilder"
            java.lang.IllegalArgumentException r3 = new java.lang.IllegalArgumentException     // Catch:{ all -> 0x0672 }
            r3.<init>(r2)     // Catch:{ all -> 0x0672 }
            throw r3     // Catch:{ all -> 0x0672 }
        L_0x065b:
            java.lang.String r2 = "Cannot build a database with null or empty name. If you are trying to create an in memory database, use Room.inMemoryDatabaseBuilder"
            java.lang.IllegalArgumentException r3 = new java.lang.IllegalArgumentException     // Catch:{ all -> 0x0672 }
            r3.<init>(r2)     // Catch:{ all -> 0x0672 }
            throw r3     // Catch:{ all -> 0x0672 }
        L_0x0663:
            r30 = r2
        L_0x0665:
            bdm r2 = n     // Catch:{ all -> 0x0672 }
            m = r2     // Catch:{ all -> 0x0672 }
            goto L_0x066c
        L_0x066a:
            r30 = r2
        L_0x066c:
            monitor-exit(r1)     // Catch:{ all -> 0x0672 }
            bdm r2 = i(r30)     // Catch:{ all -> 0x0690 }
            goto L_0x068a
        L_0x0672:
            r0 = move-exception
            r2 = r0
            monitor-exit(r1)     // Catch:{ all -> 0x0672 }
            throw r2     // Catch:{ all -> 0x0690 }
        L_0x0676:
            r0 = move-exception
            r2 = r0
            r4.close()     // Catch:{ all -> 0x067c }
            goto L_0x0681
        L_0x067c:
            r0 = move-exception
            r3 = r0
            r2.addSuppressed(r3)     // Catch:{ all -> 0x0690 }
        L_0x0681:
            throw r2     // Catch:{ all -> 0x0690 }
        L_0x0682:
            java.lang.IllegalStateException r2 = new java.lang.IllegalStateException     // Catch:{ all -> 0x0690 }
            java.lang.String r3 = "WorkManager is not initialized properly.  You have explicitly disabled WorkManagerInitializer in your manifest, have not manually called WorkManager#initialize at this point, and your Application does not implement Configuration.Provider."
            r2.<init>(r3)     // Catch:{ all -> 0x0690 }
            throw r2     // Catch:{ all -> 0x0690 }
        L_0x068a:
            monitor-exit(r1)     // Catch:{ all -> 0x0690 }
            return r2
        L_0x068c:
            r0 = move-exception
            r2 = r0
            monitor-exit(r1)     // Catch:{ all -> 0x068c }
            throw r2     // Catch:{ all -> 0x0690 }
        L_0x0690:
            r0 = move-exception
            r2 = r0
            monitor-exit(r1)     // Catch:{ all -> 0x0690 }
            throw r2
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bdm.i(android.content.Context):bdm");
    }

    /* JADX WARNING: type inference failed for: r1v1, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final bbr a(String str) {
        AmbientLifecycleObserverKt ambientLifecycleObserverKt = this.c.p;
        ? r1 = this.k.a;
        jnu.d(r1, "workManagerImpl.workTask…ecutor.serialTaskExecutor");
        return AmbientMode.AmbientCallback.b(ambientLifecycleObserverKt, "CancelWorkByName_".concat(str), r1, new bes(str, this, 4));
    }

    public final bbr b(UUID uuid) {
        return xm.f(uuid, this);
    }

    public final bbr c(List list) {
        if (!list.isEmpty()) {
            return new bcy(this, (String) null, 2, list, (byte[]) null).e();
        }
        throw new IllegalArgumentException("enqueue needs at least one WorkRequest.");
    }

    public final bbr e(String str, int i2, List list) {
        return new bcy(this, str, i2, list).e();
    }

    /* JADX WARNING: type inference failed for: r0v2, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final bbr g(String str, int i2, bmu bmu) {
        if (i2 != 3) {
            List singletonList = Collections.singletonList(bmu);
            int i3 = 2;
            if (i2 != 2) {
                i3 = 1;
            }
            return new bcy(this, str, i3, singletonList).e();
        }
        AmbientLifecycleObserverKt ambientLifecycleObserverKt = this.c.p;
        ? r0 = this.k.a;
        jnu.d(r0, "workTaskExecutor.serialTaskExecutor");
        return AmbientMode.AmbientCallback.b(ambientLifecycleObserverKt, "enqueueUniquePeriodic_".concat(str), r0, new bdq(this, str, bmu, 2, (byte[]) null));
    }

    /* JADX WARNING: type inference failed for: r4v1, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme h(byw byw) {
        WorkDatabase workDatabase = this.d;
        cyw cyw = this.k;
        jnu.e(workDatabase, "<this>");
        jnu.e(cyw, "executor");
        bii bii = new bii(byw);
        ? r4 = cyw.a;
        jnu.d(r4, "executor.serialTaskExecutor");
        return AmbientLifecycleObserverKt.a(r4, "loadStatusFuture", new bij(bii, workDatabase));
    }

    public final void j() {
        synchronized (a) {
            this.g = true;
            BroadcastReceiver.PendingResult pendingResult = this.h;
            if (pendingResult != null) {
                pendingResult.finish();
                this.h = null;
            }
        }
    }

    public final void k() {
        AmbientLifecycleObserverKt ambientLifecycleObserverKt = this.c.p;
        bdl bdl = new bdl(this, 0);
        boolean r = wd.r();
        if (r) {
            try {
                wd.p("ReschedulingWork");
            } catch (Throwable th) {
                if (r) {
                    Trace.endSection();
                }
                throw th;
            }
        }
        bdl.a();
        if (r) {
            Trace.endSection();
        }
    }
}
