package defpackage;

import android.content.res.ColorStateList;
import android.content.res.Resources;
import android.graphics.PorterDuff;
import android.graphics.Rect;
import android.os.Build;
import android.text.Editable;
import android.text.Selection;
import android.view.InputDevice;
import android.view.KeyEvent;
import android.view.View;
import android.view.WindowInsets;
import com.google.android.tts.R;
import java.util.HashMap;
import java.util.Iterator;

/* renamed from: wa  reason: default package */
/* compiled from: PG */
public final class wa {
    private static boolean A(int i, int i2) {
        if (i == -1 || i2 == -1 || i != i2) {
            return true;
        }
        return false;
    }

    public static float a(View view) {
        return view.getElevation();
    }

    public static float b(View view) {
        return view.getZ();
    }

    public static ColorStateList c(View view) {
        return view.getBackgroundTintList();
    }

    public static PorterDuff.Mode d(View view) {
        return view.getBackgroundTintMode();
    }

    public static xn e(View view, xn xnVar, Rect rect) {
        WindowInsets e = xnVar.e();
        if (e != null) {
            return xn.n(view.computeSystemWindowInsets(e, rect), view);
        }
        rect.setEmpty();
        return xnVar;
    }

    public static String f(View view) {
        return view.getTransitionName();
    }

    static void g(WindowInsets windowInsets, View view) {
        View.OnApplyWindowInsetsListener onApplyWindowInsetsListener = (View.OnApplyWindowInsetsListener) view.getTag(R.id.tag_window_insets_animation_callback);
        if (onApplyWindowInsetsListener != null) {
            onApplyWindowInsetsListener.onApplyWindowInsets(view, windowInsets);
        }
    }

    public static void h(View view, ColorStateList colorStateList) {
        view.setBackgroundTintList(colorStateList);
    }

    public static void i(View view, PorterDuff.Mode mode) {
        view.setBackgroundTintMode(mode);
    }

    public static void j(View view, float f) {
        view.setElevation(f);
    }

    public static void k(View view, vk vkVar) {
        if (Build.VERSION.SDK_INT < 30) {
            view.setTag(R.id.tag_on_apply_window_listener, vkVar);
        }
        if (vkVar == null) {
            view.setOnApplyWindowInsetsListener((View.OnApplyWindowInsetsListener) view.getTag(R.id.tag_window_insets_animation_callback));
        } else {
            view.setOnApplyWindowInsetsListener(new vz(view, vkVar));
        }
    }

    public static void l(View view, String str) {
        view.setTransitionName(str);
    }

    static void m(View view) {
        view.stopNestedScroll();
    }

    public static boolean n(View view) {
        return view.isNestedScrollingEnabled();
    }

    public static int o(Resources resources, int i, uq uqVar, int i2) {
        int dimensionPixelSize;
        if (i == -1) {
            return ((Integer) uqVar.a()).intValue();
        }
        if (i == 0 || (dimensionPixelSize = resources.getDimensionPixelSize(i)) < 0) {
            return i2;
        }
        return dimensionPixelSize;
    }

    public static int p(Resources resources, String str, String str2) {
        return resources.getIdentifier(str, str2, "android");
    }

    public static boolean q(int i, int i2, int i3) {
        InputDevice device = InputDevice.getDevice(i);
        if (device == null || device.getMotionRange(i2, i3) == null) {
            return false;
        }
        return true;
    }

    public static boolean r(Editable editable, KeyEvent keyEvent, boolean z) {
        zl[] zlVarArr;
        int length;
        if (KeyEvent.metaStateHasNoModifiers(keyEvent.getMetaState())) {
            int selectionStart = Selection.getSelectionStart(editable);
            int selectionEnd = Selection.getSelectionEnd(editable);
            if (!A(selectionStart, selectionEnd) && (zlVarArr = (zl[]) editable.getSpans(selectionStart, selectionEnd, zl.class)) != null && (length = zlVarArr.length) > 0) {
                int i = 0;
                while (i < length) {
                    zl zlVar = zlVarArr[i];
                    int spanStart = editable.getSpanStart(zlVar);
                    int spanEnd = editable.getSpanEnd(zlVar);
                    if (z) {
                        if (spanStart == selectionStart) {
                            editable.delete(spanStart, spanEnd);
                            return true;
                        }
                    } else if (spanEnd == selectionStart) {
                        editable.delete(spanStart, spanEnd);
                        return true;
                    }
                    if (selectionStart <= spanStart || selectionStart >= spanEnd) {
                        i++;
                    } else {
                        editable.delete(spanStart, spanEnd);
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:21:0x0041, code lost:
        if (java.lang.Character.isHighSurrogate(r5) != false) goto L_0x0044;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:37:0x006f, code lost:
        if (r11 != false) goto L_0x009c;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:41:0x007c, code lost:
        if (java.lang.Character.isLowSurrogate(r5) != false) goto L_0x007f;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:52:0x009f, code lost:
        if (r10 != -1) goto L_0x00b0;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static boolean s(android.view.inputmethod.InputConnection r7, android.text.Editable r8, int r9, int r10, boolean r11) {
        /*
            r0 = 0
            if (r8 == 0) goto L_0x00eb
            if (r9 < 0) goto L_0x00eb
            if (r10 >= 0) goto L_0x0009
            goto L_0x00eb
        L_0x0009:
            int r1 = android.text.Selection.getSelectionStart(r8)
            int r2 = android.text.Selection.getSelectionEnd(r8)
            boolean r3 = A(r1, r2)
            if (r3 != 0) goto L_0x00eb
            r3 = 1
            if (r11 == 0) goto L_0x00a2
            int r9 = java.lang.Math.max(r9, r0)
            int r11 = r8.length()
            r4 = -1
            if (r1 < 0) goto L_0x0059
            if (r11 >= r1) goto L_0x0028
            goto L_0x0059
        L_0x0028:
            if (r9 >= 0) goto L_0x002b
            goto L_0x0059
        L_0x002b:
            r11 = r0
        L_0x002c:
            if (r9 == 0) goto L_0x005a
            int r1 = r1 + -1
            if (r1 >= 0) goto L_0x0037
            if (r11 == 0) goto L_0x0035
            goto L_0x0059
        L_0x0035:
            r1 = r0
            goto L_0x005a
        L_0x0037:
            char r5 = r8.charAt(r1)
            if (r11 == 0) goto L_0x0047
            boolean r11 = java.lang.Character.isHighSurrogate(r5)
            if (r11 != 0) goto L_0x0044
            goto L_0x0059
        L_0x0044:
            int r9 = r9 + -1
            goto L_0x002b
        L_0x0047:
            boolean r6 = java.lang.Character.isSurrogate(r5)
            if (r6 != 0) goto L_0x0050
            int r9 = r9 + -1
            goto L_0x002c
        L_0x0050:
            boolean r11 = java.lang.Character.isHighSurrogate(r5)
            if (r11 == 0) goto L_0x0057
            goto L_0x0059
        L_0x0057:
            r11 = r3
            goto L_0x002c
        L_0x0059:
            r1 = r4
        L_0x005a:
            int r9 = java.lang.Math.max(r10, r0)
            int r10 = r8.length()
            if (r2 < 0) goto L_0x009c
            if (r10 >= r2) goto L_0x0067
            goto L_0x009c
        L_0x0067:
            if (r9 >= 0) goto L_0x006a
            goto L_0x009c
        L_0x006a:
            r11 = r0
        L_0x006b:
            if (r9 == 0) goto L_0x009a
            if (r2 < r10) goto L_0x0072
            if (r11 == 0) goto L_0x009d
            goto L_0x009c
        L_0x0072:
            char r5 = r8.charAt(r2)
            if (r11 == 0) goto L_0x0084
            boolean r11 = java.lang.Character.isLowSurrogate(r5)
            if (r11 != 0) goto L_0x007f
            goto L_0x009c
        L_0x007f:
            int r2 = r2 + 1
            int r9 = r9 + -1
            goto L_0x006a
        L_0x0084:
            boolean r6 = java.lang.Character.isSurrogate(r5)
            if (r6 != 0) goto L_0x008f
            int r2 = r2 + 1
            int r9 = r9 + -1
            goto L_0x006b
        L_0x008f:
            boolean r11 = java.lang.Character.isLowSurrogate(r5)
            if (r11 == 0) goto L_0x0096
            goto L_0x009c
        L_0x0096:
            int r2 = r2 + 1
            r11 = r3
            goto L_0x006b
        L_0x009a:
            r10 = r2
            goto L_0x009d
        L_0x009c:
            r10 = r4
        L_0x009d:
            if (r1 == r4) goto L_0x00eb
            if (r10 == r4) goto L_0x00eb
            goto L_0x00b0
        L_0x00a2:
            int r1 = r1 - r9
            int r1 = java.lang.Math.max(r1, r0)
            int r2 = r2 + r10
            int r9 = r8.length()
            int r10 = java.lang.Math.min(r2, r9)
        L_0x00b0:
            java.lang.Class<zl> r9 = defpackage.zl.class
            java.lang.Object[] r9 = r8.getSpans(r1, r10, r9)
            zl[] r9 = (defpackage.zl[]) r9
            if (r9 == 0) goto L_0x00eb
            int r11 = r9.length
            if (r11 <= 0) goto L_0x00eb
            r2 = r0
        L_0x00be:
            if (r2 >= r11) goto L_0x00d5
            r4 = r9[r2]
            int r5 = r8.getSpanStart(r4)
            int r4 = r8.getSpanEnd(r4)
            int r1 = java.lang.Math.min(r5, r1)
            int r10 = java.lang.Math.max(r4, r10)
            int r2 = r2 + 1
            goto L_0x00be
        L_0x00d5:
            int r9 = java.lang.Math.max(r1, r0)
            int r11 = r8.length()
            int r10 = java.lang.Math.min(r10, r11)
            r7.beginBatchEdit()
            r8.delete(r9, r10)
            r7.endBatchEdit()
            return r3
        L_0x00eb:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.wa.s(android.view.inputmethod.InputConnection, android.text.Editable, int, int, boolean):boolean");
    }

    public static final void t(View view) {
        jnu.e(view, "<this>");
        Iterator a = new wn(new jmh(view, (jlr) null), 3).a();
        while (a.hasNext()) {
            z((View) a.next()).ad();
        }
    }

    public static final int u(awj awj, String str) {
        int v = v(awj, str);
        if (v >= 0) {
            return v;
        }
        int v2 = v(awj, "`" + str + '`');
        if (v2 >= 0) {
            return v2;
        }
        return -1;
    }

    public static final int v(awj awj, String str) {
        int a = awj.a();
        for (int i = 0; i < a; i++) {
            if (jnu.i(str, awj.c(i))) {
                return i;
            }
        }
        return -1;
    }

    public static final void w(HashMap hashMap, jna jna) {
        int i;
        jnu.e(hashMap, "map");
        HashMap hashMap2 = new HashMap(999);
        loop0:
        while (true) {
            i = 0;
            for (Object next : hashMap.keySet()) {
                jnu.d(next, "key");
                hashMap2.put(next, hashMap.get(next));
                i++;
                if (i == 999) {
                    jna.a(hashMap2);
                    hashMap2.clear();
                }
            }
            break loop0;
        }
        if (i > 0) {
            jna.a(hashMap2);
        }
    }

    public static final boolean x(joq joq, joq joq2) {
        jnu.e(joq, "<this>");
        return jnu.v(joq).isAssignableFrom(jnu.v(joq2));
    }

    public static final Object y(Class cls) {
        String str;
        String str2;
        jnu.e(cls, "klass");
        Package packageR = cls.getPackage();
        if (packageR != null) {
            str = packageR.getName();
        } else {
            str = null;
        }
        String canonicalName = cls.getCanonicalName();
        jnu.b(canonicalName);
        if (str == null) {
            str = "";
        }
        if (str.length() != 0) {
            canonicalName = canonicalName.substring(str.length() + 1);
            jnu.d(canonicalName, "this as java.lang.String).substring(startIndex)");
        }
        String concat = String.valueOf(job.F(canonicalName, '.', '_')).concat("_Impl");
        try {
            if (str.length() == 0) {
                str2 = concat;
            } else {
                str2 = str + '.' + concat;
            }
            Class<?> cls2 = Class.forName(str2, true, cls.getClassLoader());
            jnu.c(cls2, "null cannot be cast to non-null type java.lang.Class<T of androidx.room.util.KClassUtil.findAndInstantiateDatabaseImpl>");
            return cls2.getDeclaredConstructor((Class[]) null).newInstance((Object[]) null);
        } catch (ClassNotFoundException e) {
            throw new RuntimeException("Cannot find implementation for " + cls.getCanonicalName() + ". " + concat + " does not exist. Is Room annotation processor correctly configured?", e);
        } catch (IllegalAccessException e2) {
            throw new RuntimeException("Cannot access the constructor ".concat(String.valueOf(cls.getCanonicalName())), e2);
        } catch (InstantiationException e3) {
            throw new RuntimeException("Failed to create an instance of ".concat(String.valueOf(cls.getCanonicalName())), e3);
        }
    }

    public static final byw z(View view) {
        byw byw = (byw) view.getTag(R.id.pooling_container_listener_holder_tag);
        if (byw != null) {
            return byw;
        }
        byw byw2 = new byw((byte[]) null, (char[]) null, (byte[]) null);
        view.setTag(R.id.pooling_container_listener_holder_tag, byw2);
        return byw2;
    }
}
