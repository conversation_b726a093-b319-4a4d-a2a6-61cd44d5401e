package defpackage;

import java.util.LinkedHashSet;
import java.util.Set;

/* renamed from: bao  reason: default package */
/* compiled from: PG */
public final class bao {
    public boolean a;
    public boolean b;
    private bie c = new bie((byte[]) null);
    private bbl d = bbl.NOT_REQUIRED;
    private final Set e = new LinkedHashSet();

    public final baq a() {
        return new baq(this.c, this.d, this.a, this.b, false, false, -1, -1, jji.G(this.e));
    }

    public final void b(bbl bbl) {
        jnu.e(bbl, "networkType");
        this.d = bbl;
        this.c = new bie((byte[]) null);
    }
}
