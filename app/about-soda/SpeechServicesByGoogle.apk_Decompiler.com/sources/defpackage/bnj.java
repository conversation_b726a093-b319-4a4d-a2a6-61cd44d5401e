package defpackage;

import android.content.Context;
import android.widget.TextView;
import java.util.function.Consumer;

/* renamed from: bnj  reason: default package */
/* compiled from: PG */
public final class bnj extends TextView {
    public Consumer a;

    public bnj(Context context) {
        super(context);
    }

    public final void setText(CharSequence charSequence, TextView.BufferType bufferType) {
        super.setText(charSequence, bufferType);
        Consumer consumer = this.a;
        if (consumer != null) {
            consumer.accept(charSequence);
        }
    }
}
