package defpackage;

import java.io.IOException;

/* renamed from: kdc  reason: default package */
/* compiled from: PG */
public final class kdc {
    public final kbj a;
    public final kdg b;
    public kdm c;
    public kdn d;
    public int e;
    public int f;
    public int g;
    public kcp h;
    private final kdk i;

    public kdc(kdk kdk, kbj kbj, kdg kdg) {
        this.i = kdk;
        this.a = kbj;
        this.b = kdg;
    }

    /* JADX WARNING: type inference failed for: r6v29, types: [java.lang.Object[], java.lang.Object] */
    /* JADX WARNING: type inference failed for: r9v65, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: Code restructure failed: missing block: B:105:0x0235, code lost:
        throw new java.net.SocketException("No route to " + r15 + ":" + r9 + "; port is out of range");
     */
    /* JADX WARNING: Code restructure failed: missing block: B:171:0x043f, code lost:
        throw new java.io.IOException("TLS tunnel buffered too many bytes!");
     */
    /* JADX WARNING: Code restructure failed: missing block: B:345:0x07db, code lost:
        if (r7.a.a() == false) goto L_0x07ef;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:347:0x07df, code lost:
        if (r7.b == null) goto L_0x07e2;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:350:0x07ee, code lost:
        throw new defpackage.kdl(new java.net.ProtocolException("Too many tunnel connections attempted: 21"));
     */
    /* JADX WARNING: Code restructure failed: missing block: B:351:0x07ef, code lost:
        r7.m = java.lang.System.nanoTime();
     */
    /* JADX WARNING: Code restructure failed: missing block: B:353:?, code lost:
        r1.b.m = null;
        r1.b.a.y.C(r7.a);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:354:0x0812, code lost:
        if (r1.i.a(r1.a, r1.b, r24, true) == false) goto L_0x082a;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:355:0x0814, code lost:
        r9 = r1.b.h;
        defpackage.jnu.b(r9);
        r1.h = r26;
        defpackage.kcs.r(r7.a());
        defpackage.kby.c(r9);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:356:0x082a, code lost:
        monitor-enter(r7);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:358:?, code lost:
        r2 = r1.i;
        r3 = defpackage.kcs.a;
        ((j$.util.concurrent.ConcurrentLinkedQueue) r2.d).add(r7);
        ((defpackage.kcw) r2.b).f((defpackage.kcu) r2.c);
        r1.b.e(r7);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:360:?, code lost:
        monitor-exit(r7);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:361:0x0847, code lost:
        defpackage.kby.c(r7);
        r9 = r7;
     */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: No exception handlers in catch block: Catch:{  } */
    /* JADX WARNING: Removed duplicated region for block: B:104:0x0212 A[Catch:{ all -> 0x0ac0, all -> 0x0916, all -> 0x08dc, NullPointerException -> 0x01fd, all -> 0x0062, kdl -> 0x0ae9, IOException -> 0x0ade }, EDGE_INSN: B:520:0x0212->B:104:0x0212 ?: BREAK  
    EDGE_INSN: B:521:0x0212->B:104:0x0212 ?: BREAK  ] */
    /* JADX WARNING: Removed duplicated region for block: B:122:0x02ae A[Catch:{ all -> 0x0ac0, all -> 0x0916, all -> 0x08dc, NullPointerException -> 0x01fd, all -> 0x0062, kdl -> 0x0ae9, IOException -> 0x0ade }] */
    /* JADX WARNING: Removed duplicated region for block: B:370:0x0878 A[Catch:{ all -> 0x0ac0, all -> 0x0916, all -> 0x08dc, NullPointerException -> 0x01fd, all -> 0x0062, kdl -> 0x0ae9, IOException -> 0x0ade }] */
    /* JADX WARNING: Removed duplicated region for block: B:371:0x0879 A[Catch:{ all -> 0x0ac0, all -> 0x0916, all -> 0x08dc, NullPointerException -> 0x01fd, all -> 0x0062, kdl -> 0x0ae9, IOException -> 0x0ade }] */
    /* JADX WARNING: Removed duplicated region for block: B:387:0x08ab A[Catch:{ all -> 0x0ac0, all -> 0x0916, all -> 0x08dc, NullPointerException -> 0x01fd, all -> 0x0062, kdl -> 0x0ae9, IOException -> 0x0ade }] */
    /* JADX WARNING: Removed duplicated region for block: B:388:0x08b5 A[Catch:{ all -> 0x0ac0, all -> 0x0916, all -> 0x08dc, NullPointerException -> 0x01fd, all -> 0x0062, kdl -> 0x0ae9, IOException -> 0x0ade }] */
    /* JADX WARNING: Removed duplicated region for block: B:403:0x08ff A[Catch:{ all -> 0x0ac0, all -> 0x0916, all -> 0x08dc, NullPointerException -> 0x01fd, all -> 0x0062, kdl -> 0x0ae9, IOException -> 0x0ade }] */
    /* JADX WARNING: Removed duplicated region for block: B:447:0x0a03 A[SYNTHETIC, Splitter:B:447:0x0a03] */
    /* JADX WARNING: Removed duplicated region for block: B:452:0x0a0e A[Catch:{ IOException -> 0x0a09 }] */
    /* JADX WARNING: Removed duplicated region for block: B:464:0x0a3f A[Catch:{ all -> 0x0ac0, all -> 0x0916, all -> 0x08dc, NullPointerException -> 0x01fd, all -> 0x0062, kdl -> 0x0ae9, IOException -> 0x0ade }] */
    /* JADX WARNING: Removed duplicated region for block: B:467:0x0a46 A[Catch:{ all -> 0x0ac0, all -> 0x0916, all -> 0x08dc, NullPointerException -> 0x01fd, all -> 0x0062, kdl -> 0x0ae9, IOException -> 0x0ade }] */
    /* JADX WARNING: Removed duplicated region for block: B:470:0x0a6d A[Catch:{ all -> 0x0ac0, all -> 0x0916, all -> 0x08dc, NullPointerException -> 0x01fd, all -> 0x0062, kdl -> 0x0ae9, IOException -> 0x0ade }] */
    /* JADX WARNING: Removed duplicated region for block: B:471:0x0a73 A[Catch:{ all -> 0x0ac0, all -> 0x0916, all -> 0x08dc, NullPointerException -> 0x01fd, all -> 0x0062, kdl -> 0x0ae9, IOException -> 0x0ade }] */
    /* JADX WARNING: Removed duplicated region for block: B:473:0x0a7e A[ADDED_TO_REGION, Catch:{ all -> 0x0ac0, all -> 0x0916, all -> 0x08dc, NullPointerException -> 0x01fd, all -> 0x0062, kdl -> 0x0ae9, IOException -> 0x0ade }] */
    /* JADX WARNING: Removed duplicated region for block: B:519:0x0900 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:523:0x0a0c A[EDGE_INSN: B:523:0x0a0c->B:451:0x0a0c ?: BREAK  , SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:526:0x0aaa A[ADDED_TO_REGION, EDGE_INSN: B:526:0x0aaa->B:487:0x0aaa ?: BREAK  , SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:533:0x0ab8 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:71:0x0145 A[Catch:{ all -> 0x0ac0, all -> 0x0916, all -> 0x08dc, NullPointerException -> 0x01fd, all -> 0x0062, kdl -> 0x0ae9, IOException -> 0x0ade }] */
    /* JADX WARNING: Unknown variable types count: 1 */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.kds a(defpackage.kcg r34, defpackage.kdu r35) {
        /*
            r33 = this;
            r1 = r33
            r2 = r34
            r3 = r35
            int r4 = r3.c     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            int r5 = r3.d     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            int r6 = r3.e     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            boolean r7 = r2.f     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kcj r8 = r3.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r8 = r8.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r9 = "GET"
            boolean r8 = defpackage.jnu.i(r8, r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x0018:
            kdg r9 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            boolean r9 = r9.l     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r9 != 0) goto L_0x0ad6
            kdg r9 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdi r9 = r9.h     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r10 = 0
            if (r9 == 0) goto L_0x0066
            monitor-enter(r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            boolean r11 = r9.i     // Catch:{ all -> 0x0062 }
            if (r11 != 0) goto L_0x0039
            kcp r11 = r9.a     // Catch:{ all -> 0x0062 }
            kbj r11 = r11.a     // Catch:{ all -> 0x0062 }
            kcd r11 = r11.i     // Catch:{ all -> 0x0062 }
            boolean r11 = r1.c(r11)     // Catch:{ all -> 0x0062 }
            if (r11 != 0) goto L_0x0037
            goto L_0x0039
        L_0x0037:
            r11 = r10
            goto L_0x003f
        L_0x0039:
            kdg r11 = r1.b     // Catch:{ all -> 0x0062 }
            java.net.Socket r11 = r11.c()     // Catch:{ all -> 0x0062 }
        L_0x003f:
            monitor-exit(r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdg r12 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdi r12 = r12.h     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r12 == 0) goto L_0x005c
            if (r11 != 0) goto L_0x0054
        L_0x0048:
            r20 = r4
            r21 = r5
            r22 = r6
            r16 = r7
        L_0x0050:
            r23 = r8
            goto L_0x084b
        L_0x0054:
            java.lang.String r2 = "Check failed."
            java.lang.IllegalStateException r3 = new java.lang.IllegalStateException     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r3.<init>(r2)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r3     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x005c:
            if (r11 == 0) goto L_0x0066
            defpackage.kcs.r(r11)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            goto L_0x0066
        L_0x0062:
            r0 = move-exception
            r2 = r0
            monitor-exit(r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r2     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x0066:
            r9 = 0
            r1.e = r9     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r1.f = r9     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r1.g = r9     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdk r11 = r1.i     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kbj r12 = r1.a     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdg r13 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            boolean r11 = r11.a(r12, r13, r10, r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r11 == 0) goto L_0x0084
            kdg r9 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdi r9 = r9.h     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.jnu.b(r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.kby.c(r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            goto L_0x0048
        L_0x0084:
            kcp r11 = r1.h     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r11 == 0) goto L_0x008f
            r1.h = r10     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x008a:
            r16 = r7
            r9 = r10
            goto L_0x02a1
        L_0x008f:
            kdm r11 = r1.c     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r11 == 0) goto L_0x00a3
            boolean r11 = r11.b()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r11 == 0) goto L_0x00a3
            kdm r11 = r1.c     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.jnu.b(r11)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kcp r11 = r11.a()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            goto L_0x008a
        L_0x00a3:
            kdn r11 = r1.d     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r11 != 0) goto L_0x00b6
            kdn r11 = new kdn     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kbj r12 = r1.a     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdg r13 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kcg r13 = r13.a     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            dlv r13 = r13.y     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r11.<init>(r12, r13)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r1.d = r11     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x00b6:
            boolean r12 = r11.a()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r12 == 0) goto L_0x0ad0
            java.util.ArrayList r12 = new java.util.ArrayList     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r12.<init>()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x00c1:
            boolean r13 = r11.b()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r13 == 0) goto L_0x025d
            boolean r13 = r11.b()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r13 == 0) goto L_0x0236
            java.util.List r13 = r11.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            int r14 = r11.c     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            int r15 = r14 + 1
            r11.c = r15     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.Object r13 = r13.get(r14)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.net.Proxy r13 = (java.net.Proxy) r13     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.util.ArrayList r14 = new java.util.ArrayList     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r14.<init>()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r11.d = r14     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.net.Proxy$Type r15 = r13.type()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.net.Proxy$Type r10 = java.net.Proxy.Type.DIRECT     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r15 == r10) goto L_0x0137
            java.net.Proxy$Type r10 = r13.type()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.net.Proxy$Type r15 = java.net.Proxy.Type.SOCKS     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r10 != r15) goto L_0x00f3
            goto L_0x0137
        L_0x00f3:
            java.net.SocketAddress r10 = r13.address()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            boolean r15 = r10 instanceof java.net.InetSocketAddress     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r15 == 0) goto L_0x0120
            java.net.InetSocketAddress r10 = (java.net.InetSocketAddress) r10     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r15 = "<this>"
            defpackage.jnu.e(r10, r15)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.net.InetAddress r15 = r10.getAddress()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r15 != 0) goto L_0x0112
            java.lang.String r15 = r10.getHostName()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r9 = "getHostName(...)"
            defpackage.jnu.d(r15, r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            goto L_0x011b
        L_0x0112:
            java.lang.String r15 = r15.getHostAddress()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r9 = "getHostAddress(...)"
            defpackage.jnu.d(r15, r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x011b:
            int r9 = r10.getPort()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            goto L_0x013f
        L_0x0120:
            java.lang.Class r2 = r10.getClass()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r3 = "Proxy.address() is not an InetSocketAddress: "
            java.util.Objects.toString(r2)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r2 = java.lang.String.valueOf(r2)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r2 = r3.concat(r2)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.IllegalArgumentException r3 = new java.lang.IllegalArgumentException     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r3.<init>(r2)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r3     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x0137:
            kbj r9 = r11.a     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kcd r9 = r9.i     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r15 = r9.c     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            int r9 = r9.d     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x013f:
            if (r9 <= 0) goto L_0x0212
            r10 = 65536(0x10000, float:9.18355E-41)
            if (r9 >= r10) goto L_0x0212
            java.net.Proxy$Type r10 = r13.type()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r16 = r7
            java.net.Proxy$Type r7 = java.net.Proxy.Type.SOCKS     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r10 != r7) goto L_0x0157
            java.net.InetSocketAddress r7 = java.net.InetSocketAddress.createUnresolved(r15, r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r14.add(r7)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            goto L_0x01a6
        L_0x0157:
            boolean r7 = defpackage.kcs.s(r15)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r7 == 0) goto L_0x0166
            java.net.InetAddress r7 = java.net.InetAddress.getByName(r15)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.util.List r7 = defpackage.jji.m(r7)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            goto L_0x018d
        L_0x0166:
            java.lang.String r7 = "domainName"
            defpackage.jnu.e(r15, r7)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r7 = "hostname"
            defpackage.jnu.e(r15, r7)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.net.InetAddress[] r7 = java.net.InetAddress.getAllByName(r15)     // Catch:{ NullPointerException -> 0x01fd }
            java.lang.String r10 = "getAllByName(...)"
            defpackage.jnu.d(r7, r10)     // Catch:{ NullPointerException -> 0x01fd }
            java.util.List r7 = defpackage.jji.aa(r7)     // Catch:{ NullPointerException -> 0x01fd }
            boolean r10 = r7.isEmpty()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r10 != 0) goto L_0x01df
            java.lang.String r10 = "domainName"
            defpackage.jnu.e(r15, r10)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r10 = "inetAddressList"
            defpackage.jnu.e(r7, r10)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x018d:
            java.util.Iterator r7 = r7.iterator()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x0191:
            boolean r10 = r7.hasNext()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r10 == 0) goto L_0x01a6
            java.lang.Object r10 = r7.next()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.net.InetAddress r10 = (java.net.InetAddress) r10     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.net.InetSocketAddress r15 = new java.net.InetSocketAddress     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r15.<init>(r10, r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r14.add(r15)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            goto L_0x0191
        L_0x01a6:
            java.util.List r7 = r11.d     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.util.Iterator r7 = r7.iterator()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x01ac:
            boolean r9 = r7.hasNext()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r9 == 0) goto L_0x01d1
            java.lang.Object r9 = r7.next()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.net.InetSocketAddress r9 = (java.net.InetSocketAddress) r9     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kcp r10 = new kcp     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kbj r14 = r11.a     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r10.<init>(r14, r13, r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            dlv r9 = r11.f     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            boolean r9 = r9.E(r10)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r9 == 0) goto L_0x01cd
            java.util.List r9 = r11.e     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r9.add(r10)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            goto L_0x01ac
        L_0x01cd:
            r12.add(r10)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            goto L_0x01ac
        L_0x01d1:
            boolean r7 = r12.isEmpty()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r7 != 0) goto L_0x01d9
            goto L_0x025f
        L_0x01d9:
            r7 = r16
            r9 = 0
            r10 = 0
            goto L_0x00c1
        L_0x01df:
            java.net.UnknownHostException r2 = new java.net.UnknownHostException     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kbj r3 = r11.a     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kbx r3 = r3.a     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.StringBuilder r4 = new java.lang.StringBuilder     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r4.<init>()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r4.append(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r3 = " returned no addresses for "
            r4.append(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r4.append(r15)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r3 = r4.toString()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r2.<init>(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r2     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x01fd:
            r0 = move-exception
            r2 = r0
            java.lang.String r3 = java.lang.String.valueOf(r15)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r4 = "Broken system behaviour for dns lookup of "
            java.lang.String r3 = r4.concat(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.net.UnknownHostException r4 = new java.net.UnknownHostException     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r4.<init>(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r4.initCause(r2)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r4     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x0212:
            java.net.SocketException r2 = new java.net.SocketException     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.StringBuilder r3 = new java.lang.StringBuilder     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r3.<init>()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r4 = "No route to "
            r3.append(r4)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r3.append(r15)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r4 = ":"
            r3.append(r4)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r3.append(r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r4 = "; port is out of range"
            r3.append(r4)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r3 = r3.toString()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r2.<init>(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r2     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x0236:
            java.net.SocketException r2 = new java.net.SocketException     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kbj r3 = r11.a     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kcd r3 = r3.i     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r3 = r3.c     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.util.List r4 = r11.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.StringBuilder r5 = new java.lang.StringBuilder     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r5.<init>()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r6 = "No route to "
            r5.append(r6)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r5.append(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r3 = "; exhausted proxy configurations: "
            r5.append(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r5.append(r4)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r3 = r5.toString()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r2.<init>(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r2     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x025d:
            r16 = r7
        L_0x025f:
            boolean r7 = r12.isEmpty()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r7 == 0) goto L_0x026f
            java.util.List r7 = r11.e     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.jji.J(r12, r7)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.util.List r7 = r11.e     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r7.clear()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x026f:
            kdm r7 = new kdm     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r7.<init>((java.util.List) r12)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r1.c = r7     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.Object r9 = r7.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdg r10 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            boolean r10 = r10.l     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r10 != 0) goto L_0x0ac8
            kdk r10 = r1.i     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kbj r11 = r1.a     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdg r12 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r13 = 0
            boolean r10 = r10.a(r11, r12, r9, r13)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r10 == 0) goto L_0x029d
            kdg r7 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdi r9 = r7.h     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.jnu.b(r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.kby.c(r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r20 = r4
            r21 = r5
            r22 = r6
            goto L_0x0050
        L_0x029d:
            kcp r11 = r7.a()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x02a1:
            kdi r7 = new kdi     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r7.<init>(r11)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdg r10 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r10.m = r7     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kch r10 = r7.e     // Catch:{ all -> 0x0ac0 }
            if (r10 != 0) goto L_0x0ab8
            kcp r10 = r7.a     // Catch:{ all -> 0x0ac0 }
            kbj r10 = r10.a     // Catch:{ all -> 0x0ac0 }
            java.util.List r10 = r10.k     // Catch:{ all -> 0x0ac0 }
            java.lang.String r12 = "connectionSpecs"
            defpackage.jnu.e(r10, r12)     // Catch:{ all -> 0x0ac0 }
            kcp r12 = r7.a     // Catch:{ all -> 0x0ac0 }
            kbj r12 = r12.a     // Catch:{ all -> 0x0ac0 }
            javax.net.ssl.SSLSocketFactory r13 = r12.c     // Catch:{ all -> 0x0ac0 }
            if (r13 != 0) goto L_0x02fa
            kbr r12 = defpackage.kbr.b     // Catch:{ all -> 0x0ac0 }
            boolean r12 = r10.contains(r12)     // Catch:{ all -> 0x0ac0 }
            if (r12 == 0) goto L_0x02ed
            kcp r12 = r7.a     // Catch:{ all -> 0x0ac0 }
            kbj r12 = r12.a     // Catch:{ all -> 0x0ac0 }
            kcd r12 = r12.i     // Catch:{ all -> 0x0ac0 }
            kfw r13 = defpackage.kfw.b     // Catch:{ all -> 0x0ac0 }
            java.lang.String r12 = r12.c     // Catch:{ all -> 0x0ac0 }
            boolean r13 = r13.e(r12)     // Catch:{ all -> 0x0ac0 }
            if (r13 == 0) goto L_0x02da
            goto L_0x0304
        L_0x02da:
            kdl r2 = new kdl     // Catch:{ all -> 0x0ac0 }
            java.net.UnknownServiceException r3 = new java.net.UnknownServiceException     // Catch:{ all -> 0x0ac0 }
            java.lang.String r4 = "CLEARTEXT communication to "
            java.lang.String r5 = " not permitted by network security policy"
            java.lang.String r4 = defpackage.a.ap(r12, r4, r5)     // Catch:{ all -> 0x0ac0 }
            r3.<init>(r4)     // Catch:{ all -> 0x0ac0 }
            r2.<init>(r3)     // Catch:{ all -> 0x0ac0 }
            throw r2     // Catch:{ all -> 0x0ac0 }
        L_0x02ed:
            java.lang.String r2 = "CLEARTEXT communication not enabled for client"
            kdl r3 = new kdl     // Catch:{ all -> 0x0ac0 }
            java.net.UnknownServiceException r4 = new java.net.UnknownServiceException     // Catch:{ all -> 0x0ac0 }
            r4.<init>(r2)     // Catch:{ all -> 0x0ac0 }
            r3.<init>(r4)     // Catch:{ all -> 0x0ac0 }
            throw r3     // Catch:{ all -> 0x0ac0 }
        L_0x02fa:
            java.util.List r12 = r12.j     // Catch:{ all -> 0x0ac0 }
            kch r13 = defpackage.kch.H2_PRIOR_KNOWLEDGE     // Catch:{ all -> 0x0ac0 }
            boolean r12 = r12.contains(r13)     // Catch:{ all -> 0x0ac0 }
            if (r12 != 0) goto L_0x0aab
        L_0x0304:
            r13 = 0
            r14 = 0
            r15 = 0
            r17 = 0
        L_0x0309:
            kcp r12 = r7.a     // Catch:{ IOException -> 0x0a28 }
            boolean r12 = r12.a()     // Catch:{ IOException -> 0x0a28 }
            if (r12 == 0) goto L_0x0471
            kci r12 = new kci     // Catch:{ IOException -> 0x0456 }
            r12.<init>()     // Catch:{ IOException -> 0x0456 }
            r18 = r14
            kcp r14 = r7.a     // Catch:{ IOException -> 0x044c }
            kbj r14 = r14.a     // Catch:{ IOException -> 0x044c }
            kcd r14 = r14.i     // Catch:{ IOException -> 0x044c }
            r12.a = r14     // Catch:{ IOException -> 0x044c }
            java.lang.String r14 = "CONNECT"
            r19 = r15
            r15 = 0
            r12.c(r14, r15)     // Catch:{ IOException -> 0x0442 }
            java.lang.String r14 = "Host"
            kcp r15 = r7.a     // Catch:{ IOException -> 0x0442 }
            kbj r15 = r15.a     // Catch:{ IOException -> 0x0442 }
            kcd r15 = r15.i     // Catch:{ IOException -> 0x0442 }
            r2 = 1
            java.lang.String r15 = defpackage.kcs.l(r15, r2)     // Catch:{ IOException -> 0x0442 }
            r12.b(r14, r15)     // Catch:{ IOException -> 0x0442 }
            java.lang.String r2 = "Proxy-Connection"
            java.lang.String r14 = "Keep-Alive"
            r12.b(r2, r14)     // Catch:{ IOException -> 0x0442 }
            java.lang.String r2 = "User-Agent"
            java.lang.String r14 = "okhttp/4.12.0"
            r12.b(r2, r14)     // Catch:{ IOException -> 0x0442 }
            kcj r2 = r12.a()     // Catch:{ IOException -> 0x0442 }
            kcl r12 = new kcl     // Catch:{ IOException -> 0x0442 }
            r12.<init>()     // Catch:{ IOException -> 0x0442 }
            r12.f(r2)     // Catch:{ IOException -> 0x0442 }
            kch r14 = defpackage.kch.HTTP_1_1     // Catch:{ IOException -> 0x0442 }
            r12.e(r14)     // Catch:{ IOException -> 0x0442 }
            r14 = 407(0x197, float:5.7E-43)
            r12.a = r14     // Catch:{ IOException -> 0x0442 }
            java.lang.String r15 = "Preemptive Authenticate"
            r12.d(r15)     // Catch:{ IOException -> 0x0442 }
            kco r15 = defpackage.kcs.c     // Catch:{ IOException -> 0x0442 }
            r12.c = r15     // Catch:{ IOException -> 0x0442 }
            r14 = -1
            r12.g = r14     // Catch:{ IOException -> 0x0442 }
            r12.h = r14     // Catch:{ IOException -> 0x0442 }
            java.lang.String r14 = "Proxy-Authenticate"
            java.lang.String r15 = "OkHttp-Preemptive"
            dlv r3 = r12.j     // Catch:{ IOException -> 0x0442 }
            r3.J(r14, r15)     // Catch:{ IOException -> 0x0442 }
            r12.a()     // Catch:{ IOException -> 0x0442 }
            kcp r3 = r7.a     // Catch:{ IOException -> 0x0442 }
            kbj r3 = r3.a     // Catch:{ IOException -> 0x0442 }
            kcd r3 = r2.a     // Catch:{ IOException -> 0x0442 }
            r7.j(r4, r5)     // Catch:{ IOException -> 0x0442 }
            r12 = 1
            java.lang.String r3 = defpackage.kcs.l(r3, r12)     // Catch:{ IOException -> 0x0442 }
            java.lang.StringBuilder r12 = new java.lang.StringBuilder     // Catch:{ IOException -> 0x0442 }
            r12.<init>()     // Catch:{ IOException -> 0x0442 }
            java.lang.String r14 = "CONNECT "
            r12.append(r14)     // Catch:{ IOException -> 0x0442 }
            r12.append(r3)     // Catch:{ IOException -> 0x0442 }
            java.lang.String r3 = " HTTP/1.1"
            r12.append(r3)     // Catch:{ IOException -> 0x0442 }
            java.lang.String r3 = r12.toString()     // Catch:{ IOException -> 0x0442 }
            khf r12 = r7.g     // Catch:{ IOException -> 0x0442 }
            defpackage.jnu.b(r12)     // Catch:{ IOException -> 0x0442 }
            khe r14 = r7.h     // Catch:{ IOException -> 0x0442 }
            defpackage.jnu.b(r14)     // Catch:{ IOException -> 0x0442 }
            kef r15 = new kef     // Catch:{ IOException -> 0x0442 }
            r23 = r8
            r8 = 0
            r15.<init>(r8, r7, r12, r14)     // Catch:{ IOException -> 0x0440 }
            kid r8 = r12.a()     // Catch:{ IOException -> 0x0440 }
            r24 = r9
            r25 = r10
            long r9 = (long) r5
            r26 = r11
            java.util.concurrent.TimeUnit r11 = java.util.concurrent.TimeUnit.MILLISECONDS     // Catch:{ IOException -> 0x04b5 }
            r8.n(r9, r11)     // Catch:{ IOException -> 0x04b5 }
            kid r8 = r14.a()     // Catch:{ IOException -> 0x04b5 }
            long r9 = (long) r6     // Catch:{ IOException -> 0x04b5 }
            java.util.concurrent.TimeUnit r11 = java.util.concurrent.TimeUnit.MILLISECONDS     // Catch:{ IOException -> 0x04b5 }
            r8.n(r9, r11)     // Catch:{ IOException -> 0x04b5 }
            kcb r8 = r2.c     // Catch:{ IOException -> 0x04b5 }
            r15.k(r8, r3)     // Catch:{ IOException -> 0x04b5 }
            r15.g()     // Catch:{ IOException -> 0x04b5 }
            r3 = 0
            kcl r8 = r15.b(r3)     // Catch:{ IOException -> 0x04b5 }
            defpackage.jnu.b(r8)     // Catch:{ IOException -> 0x04b5 }
            r8.f(r2)     // Catch:{ IOException -> 0x04b5 }
            kcm r2 = r8.a()     // Catch:{ IOException -> 0x04b5 }
            long r8 = defpackage.kcs.i(r2)     // Catch:{ IOException -> 0x04b5 }
            r10 = -1
            int r3 = (r8 > r10 ? 1 : (r8 == r10 ? 0 : -1))
            if (r3 == 0) goto L_0x03f6
            kib r3 = r15.j(r8)     // Catch:{ IOException -> 0x04b5 }
            java.util.concurrent.TimeUnit r8 = java.util.concurrent.TimeUnit.MILLISECONDS     // Catch:{ IOException -> 0x04b5 }
            r9 = 2147483647(0x7fffffff, float:NaN)
            defpackage.kcs.x(r3, r9, r8)     // Catch:{ IOException -> 0x04b5 }
            r3.close()     // Catch:{ IOException -> 0x04b5 }
        L_0x03f6:
            int r2 = r2.d     // Catch:{ IOException -> 0x04b5 }
            r3 = 200(0xc8, float:2.8E-43)
            if (r2 == r3) goto L_0x0418
            r3 = 407(0x197, float:5.7E-43)
            if (r2 == r3) goto L_0x040c
            java.io.IOException r3 = new java.io.IOException     // Catch:{ IOException -> 0x04b5 }
            java.lang.String r8 = "Unexpected response code for CONNECT: "
            java.lang.String r2 = defpackage.a.ak(r2, r8)     // Catch:{ IOException -> 0x04b5 }
            r3.<init>(r2)     // Catch:{ IOException -> 0x04b5 }
            throw r3     // Catch:{ IOException -> 0x04b5 }
        L_0x040c:
            kcp r2 = r7.a     // Catch:{ IOException -> 0x04b5 }
            kbj r2 = r2.a     // Catch:{ IOException -> 0x04b5 }
            java.io.IOException r2 = new java.io.IOException     // Catch:{ IOException -> 0x04b5 }
            java.lang.String r3 = "Failed to authenticate with proxy"
            r2.<init>(r3)     // Catch:{ IOException -> 0x04b5 }
            throw r2     // Catch:{ IOException -> 0x04b5 }
        L_0x0418:
            khv r12 = (defpackage.khv) r12     // Catch:{ IOException -> 0x04b5 }
            khd r2 = r12.b     // Catch:{ IOException -> 0x04b5 }
            boolean r2 = r2.y()     // Catch:{ IOException -> 0x04b5 }
            if (r2 == 0) goto L_0x0438
            kht r14 = (defpackage.kht) r14     // Catch:{ IOException -> 0x04b5 }
            khd r2 = r14.b     // Catch:{ IOException -> 0x04b5 }
            boolean r2 = r2.y()     // Catch:{ IOException -> 0x04b5 }
            if (r2 == 0) goto L_0x0438
            java.net.Socket r2 = r7.b     // Catch:{ IOException -> 0x04b5 }
            if (r2 != 0) goto L_0x0480
            r20 = r4
            r21 = r5
            r22 = r6
            goto L_0x07d5
        L_0x0438:
            java.io.IOException r2 = new java.io.IOException     // Catch:{ IOException -> 0x04b5 }
            java.lang.String r3 = "TLS tunnel buffered too many bytes!"
            r2.<init>(r3)     // Catch:{ IOException -> 0x04b5 }
            throw r2     // Catch:{ IOException -> 0x04b5 }
        L_0x0440:
            r0 = move-exception
            goto L_0x0445
        L_0x0442:
            r0 = move-exception
            r23 = r8
        L_0x0445:
            r24 = r9
            r25 = r10
            r26 = r11
            goto L_0x0463
        L_0x044c:
            r0 = move-exception
            r23 = r8
            r24 = r9
            r25 = r10
            r26 = r11
            goto L_0x0461
        L_0x0456:
            r0 = move-exception
            r23 = r8
            r24 = r9
            r25 = r10
            r26 = r11
            r18 = r14
        L_0x0461:
            r19 = r15
        L_0x0463:
            r8 = r34
            r15 = r35
            r2 = r0
            r20 = r4
            r21 = r5
            r22 = r6
        L_0x046e:
            r6 = 0
            goto L_0x0a39
        L_0x0471:
            r23 = r8
            r24 = r9
            r25 = r10
            r26 = r11
            r18 = r14
            r19 = r15
            r7.j(r4, r5)     // Catch:{ IOException -> 0x0a15 }
        L_0x0480:
            kcp r2 = r7.a     // Catch:{ IOException -> 0x0a15 }
            kbj r2 = r2.a     // Catch:{ IOException -> 0x0a15 }
            javax.net.ssl.SSLSocketFactory r3 = r2.c     // Catch:{ IOException -> 0x0a15 }
            if (r3 != 0) goto L_0x04c8
            java.util.List r2 = r2.j     // Catch:{ IOException -> 0x04b5 }
            kch r3 = defpackage.kch.H2_PRIOR_KNOWLEDGE     // Catch:{ IOException -> 0x04b5 }
            boolean r2 = r2.contains(r3)     // Catch:{ IOException -> 0x04b5 }
            if (r2 == 0) goto L_0x049e
            java.net.Socket r2 = r7.b     // Catch:{ IOException -> 0x04b5 }
            r7.c = r2     // Catch:{ IOException -> 0x04b5 }
            kch r2 = defpackage.kch.H2_PRIOR_KNOWLEDGE     // Catch:{ IOException -> 0x04b5 }
            r7.e = r2     // Catch:{ IOException -> 0x04b5 }
            r7.m()     // Catch:{ IOException -> 0x04b5 }
            goto L_0x04a6
        L_0x049e:
            java.net.Socket r2 = r7.b     // Catch:{ IOException -> 0x04b5 }
            r7.c = r2     // Catch:{ IOException -> 0x04b5 }
            kch r2 = defpackage.kch.HTTP_1_1     // Catch:{ IOException -> 0x04b5 }
            r7.e = r2     // Catch:{ IOException -> 0x04b5 }
        L_0x04a6:
            r20 = r4
            r21 = r5
            r22 = r6
            r12 = r17
            r14 = r18
            r10 = r25
            r6 = 0
            goto L_0x07c5
        L_0x04b5:
            r0 = move-exception
            r8 = r34
            r15 = r35
            r2 = r0
            r20 = r4
            r21 = r5
            r22 = r6
            r9 = r24
            r10 = r25
            r11 = r26
            goto L_0x046e
        L_0x04c8:
            java.net.Socket r8 = r7.b     // Catch:{ all -> 0x09e9 }
            kcd r9 = r2.i     // Catch:{ all -> 0x09e9 }
            java.lang.String r10 = r9.c     // Catch:{ all -> 0x09e9 }
            int r9 = r9.d     // Catch:{ all -> 0x09e9 }
            r11 = 1
            java.net.Socket r3 = r3.createSocket(r8, r10, r9, r11)     // Catch:{ all -> 0x09e9 }
            java.lang.String r8 = "null cannot be cast to non-null type javax.net.ssl.SSLSocket"
            defpackage.jnu.c(r3, r8)     // Catch:{ all -> 0x09e9 }
            javax.net.ssl.SSLSocket r3 = (javax.net.ssl.SSLSocket) r3     // Catch:{ all -> 0x09e9 }
            java.lang.String r8 = "sslSocket"
            defpackage.jnu.e(r3, r8)     // Catch:{ all -> 0x09d1 }
            int r8 = r25.size()     // Catch:{ all -> 0x09d1 }
            r9 = r17
        L_0x04e7:
            if (r9 >= r8) goto L_0x0512
            r10 = r25
            java.lang.Object r11 = r10.get(r9)     // Catch:{ all -> 0x04ff }
            kbr r11 = (defpackage.kbr) r11     // Catch:{ all -> 0x04ff }
            boolean r12 = r11.c(r3)     // Catch:{ all -> 0x04ff }
            if (r12 == 0) goto L_0x04fa
            int r12 = r9 + 1
            goto L_0x0517
        L_0x04fa:
            int r9 = r9 + 1
            r25 = r10
            goto L_0x04e7
        L_0x04ff:
            r0 = move-exception
            r8 = r34
            r15 = r35
            r2 = r0
            r20 = r4
            r21 = r5
            r22 = r6
            r9 = r24
            r11 = r26
            r6 = 0
            goto L_0x09e4
        L_0x0512:
            r10 = r25
            r12 = r17
            r11 = 0
        L_0x0517:
            if (r11 == 0) goto L_0x0982
            int r8 = r10.size()     // Catch:{ all -> 0x096e }
            r9 = r12
        L_0x051e:
            if (r9 >= r8) goto L_0x0546
            java.lang.Object r14 = r10.get(r9)     // Catch:{ all -> 0x0531 }
            kbr r14 = (defpackage.kbr) r14     // Catch:{ all -> 0x0531 }
            boolean r14 = r14.c(r3)     // Catch:{ all -> 0x0531 }
            if (r14 == 0) goto L_0x052e
            r8 = 1
            goto L_0x0547
        L_0x052e:
            int r9 = r9 + 1
            goto L_0x051e
        L_0x0531:
            r0 = move-exception
            r8 = r34
            r15 = r35
            r2 = r0
            r20 = r4
            r21 = r5
            r22 = r6
            r25 = r12
            r9 = r24
            r11 = r26
            r6 = 0
            goto L_0x09cc
        L_0x0546:
            r8 = 0
        L_0x0547:
            java.lang.String r9 = "sslSocket"
            defpackage.jnu.e(r3, r9)     // Catch:{ all -> 0x0956 }
            java.lang.String[] r9 = r11.e     // Catch:{ all -> 0x0956 }
            if (r9 == 0) goto L_0x0579
            java.lang.String[] r9 = r3.getEnabledCipherSuites()     // Catch:{ all -> 0x0562 }
            java.lang.String r14 = "getEnabledCipherSuites(...)"
            defpackage.jnu.d(r9, r14)     // Catch:{ all -> 0x0562 }
            java.lang.String[] r14 = r11.e     // Catch:{ all -> 0x0562 }
            java.util.Comparator r15 = defpackage.kbp.a     // Catch:{ all -> 0x0562 }
            java.lang.String[] r9 = defpackage.kcs.y(r9, r14, r15)     // Catch:{ all -> 0x0562 }
            goto L_0x057d
        L_0x0562:
            r0 = move-exception
            r15 = r35
            r2 = r0
            r20 = r4
        L_0x0568:
            r21 = r5
            r22 = r6
            r17 = r8
            r25 = r12
            r9 = r24
            r11 = r26
            r6 = 0
            r8 = r34
            goto L_0x096b
        L_0x0579:
            java.lang.String[] r9 = r3.getEnabledCipherSuites()     // Catch:{ all -> 0x0956 }
        L_0x057d:
            java.lang.String[] r14 = r11.f     // Catch:{ all -> 0x0956 }
            if (r14 == 0) goto L_0x059e
            java.lang.String[] r14 = r3.getEnabledProtocols()     // Catch:{ all -> 0x0597 }
            java.lang.String r15 = "getEnabledProtocols(...)"
            defpackage.jnu.d(r14, r15)     // Catch:{ all -> 0x0597 }
            java.lang.String[] r15 = r11.f     // Catch:{ all -> 0x0597 }
            r20 = r4
            jll r4 = defpackage.jll.b     // Catch:{ all -> 0x0595 }
            java.lang.String[] r4 = defpackage.kcs.y(r14, r15, r4)     // Catch:{ all -> 0x0595 }
            goto L_0x05a4
        L_0x0595:
            r0 = move-exception
            goto L_0x059a
        L_0x0597:
            r0 = move-exception
            r20 = r4
        L_0x059a:
            r15 = r35
            r2 = r0
            goto L_0x0568
        L_0x059e:
            r20 = r4
            java.lang.String[] r4 = r3.getEnabledProtocols()     // Catch:{ all -> 0x0952 }
        L_0x05a4:
            java.lang.String[] r14 = r3.getSupportedCipherSuites()     // Catch:{ all -> 0x0952 }
            defpackage.jnu.b(r14)     // Catch:{ all -> 0x0952 }
            java.util.Comparator r15 = defpackage.kbp.a     // Catch:{ all -> 0x0952 }
            r21 = r5
            java.lang.String r5 = "<this>"
            r22 = r6
            java.lang.String r6 = "TLS_FALLBACK_SCSV"
            defpackage.jnu.e(r14, r5)     // Catch:{ all -> 0x094a }
            int r5 = r14.length     // Catch:{ all -> 0x094a }
            r17 = r8
            r25 = r12
            r8 = 0
        L_0x05be:
            if (r8 >= r5) goto L_0x05d9
            r12 = r14[r8]     // Catch:{ all -> 0x05cc }
            int r12 = r15.compare(r12, r6)     // Catch:{ all -> 0x05cc }
            if (r12 != 0) goto L_0x05c9
            goto L_0x05da
        L_0x05c9:
            int r8 = r8 + 1
            goto L_0x05be
        L_0x05cc:
            r0 = move-exception
            r8 = r34
            r15 = r35
            r2 = r0
            r9 = r24
            r11 = r26
            r6 = 0
            goto L_0x096b
        L_0x05d9:
            r8 = -1
        L_0x05da:
            if (r13 == 0) goto L_0x0608
            r5 = -1
            if (r8 == r5) goto L_0x0608
            defpackage.jnu.b(r9)     // Catch:{ all -> 0x05cc }
            r5 = r14[r8]     // Catch:{ all -> 0x05cc }
            java.lang.String r6 = "get(...)"
            defpackage.jnu.d(r5, r6)     // Catch:{ all -> 0x05cc }
            java.lang.String r6 = "<this>"
            defpackage.jnu.e(r9, r6)     // Catch:{ all -> 0x05cc }
            java.lang.String r6 = "value"
            defpackage.jnu.e(r5, r6)     // Catch:{ all -> 0x05cc }
            int r6 = r9.length     // Catch:{ all -> 0x05cc }
            r8 = 1
            int r6 = r6 + r8
            java.lang.Object[] r6 = java.util.Arrays.copyOf(r9, r6)     // Catch:{ all -> 0x05cc }
            java.lang.String r8 = "copyOf(...)"
            defpackage.jnu.d(r6, r8)     // Catch:{ all -> 0x05cc }
            r9 = r6
            java.lang.String[] r9 = (java.lang.String[]) r9     // Catch:{ all -> 0x05cc }
            int r6 = defpackage.jji.X(r9)     // Catch:{ all -> 0x05cc }
            r9[r6] = r5     // Catch:{ all -> 0x05cc }
        L_0x0608:
            kbq r5 = new kbq     // Catch:{ all -> 0x093f }
            r5.<init>((defpackage.kbr) r11)     // Catch:{ all -> 0x093f }
            int r6 = r9.length     // Catch:{ all -> 0x093f }
            java.lang.Object[] r6 = java.util.Arrays.copyOf(r9, r6)     // Catch:{ all -> 0x093f }
            java.lang.String[] r6 = (java.lang.String[]) r6     // Catch:{ all -> 0x093f }
            r5.b(r6)     // Catch:{ all -> 0x093f }
            int r6 = r4.length     // Catch:{ all -> 0x093f }
            java.lang.Object[] r4 = java.util.Arrays.copyOf(r4, r6)     // Catch:{ all -> 0x093f }
            java.lang.String[] r4 = (java.lang.String[]) r4     // Catch:{ all -> 0x093f }
            r5.d(r4)     // Catch:{ all -> 0x093f }
            kbr r4 = r5.a()     // Catch:{ all -> 0x093f }
            java.util.List r5 = r4.b()     // Catch:{ all -> 0x093f }
            if (r5 == 0) goto L_0x0630
            java.lang.String[] r5 = r4.f     // Catch:{ all -> 0x05cc }
            r3.setEnabledProtocols(r5)     // Catch:{ all -> 0x05cc }
        L_0x0630:
            java.util.List r5 = r4.a()     // Catch:{ all -> 0x093f }
            if (r5 == 0) goto L_0x063b
            java.lang.String[] r4 = r4.e     // Catch:{ all -> 0x05cc }
            r3.setEnabledCipherSuites(r4)     // Catch:{ all -> 0x05cc }
        L_0x063b:
            boolean r4 = r11.d     // Catch:{ all -> 0x093f }
            if (r4 == 0) goto L_0x064a
            kfw r4 = defpackage.kfw.b     // Catch:{ all -> 0x05cc }
            kcd r5 = r2.i     // Catch:{ all -> 0x05cc }
            java.lang.String r5 = r5.c     // Catch:{ all -> 0x05cc }
            java.util.List r6 = r2.j     // Catch:{ all -> 0x05cc }
            r4.d(r3, r5, r6)     // Catch:{ all -> 0x05cc }
        L_0x064a:
            r3.startHandshake()     // Catch:{ all -> 0x093f }
            javax.net.ssl.SSLSession r4 = r3.getSession()     // Catch:{ all -> 0x093f }
            defpackage.jnu.b(r4)     // Catch:{ all -> 0x093f }
            kca r5 = defpackage.jnu.U(r4)     // Catch:{ all -> 0x093f }
            javax.net.ssl.HostnameVerifier r6 = r2.d     // Catch:{ all -> 0x093f }
            defpackage.jnu.b(r6)     // Catch:{ all -> 0x093f }
            kcd r8 = r2.i     // Catch:{ all -> 0x093f }
            java.lang.String r8 = r8.c     // Catch:{ all -> 0x093f }
            boolean r4 = r6.verify(r8, r4)     // Catch:{ all -> 0x093f }
            if (r4 != 0) goto L_0x06fc
            java.util.List r4 = r5.a()     // Catch:{ all -> 0x06ef }
            boolean r5 = r4.isEmpty()     // Catch:{ all -> 0x06ef }
            if (r5 != 0) goto L_0x06da
            r6 = 0
            java.lang.Object r4 = r4.get(r6)     // Catch:{ all -> 0x06ed }
            java.lang.String r5 = "null cannot be cast to non-null type java.security.cert.X509Certificate"
            defpackage.jnu.c(r4, r5)     // Catch:{ all -> 0x06ed }
            java.security.cert.X509Certificate r4 = (java.security.cert.X509Certificate) r4     // Catch:{ all -> 0x06ed }
            javax.net.ssl.SSLPeerUnverifiedException r5 = new javax.net.ssl.SSLPeerUnverifiedException     // Catch:{ all -> 0x06ed }
            kcd r2 = r2.i     // Catch:{ all -> 0x06ed }
            java.lang.String r2 = r2.c     // Catch:{ all -> 0x06ed }
            kbo r8 = defpackage.kbo.a     // Catch:{ all -> 0x06ed }
            java.lang.String r8 = defpackage.jnu.Y(r4)     // Catch:{ all -> 0x06ed }
            java.security.Principal r9 = r4.getSubjectDN()     // Catch:{ all -> 0x06ed }
            java.lang.String r9 = r9.getName()     // Catch:{ all -> 0x06ed }
            java.lang.String r11 = "certificate"
            defpackage.jnu.e(r4, r11)     // Catch:{ all -> 0x06ed }
            r11 = 7
            java.util.List r11 = defpackage.kgk.a(r4, r11)     // Catch:{ all -> 0x06ed }
            r12 = 2
            java.util.List r4 = defpackage.kgk.a(r4, r12)     // Catch:{ all -> 0x06ed }
            java.util.List r4 = defpackage.jji.w(r11, r4)     // Catch:{ all -> 0x06ed }
            java.lang.StringBuilder r11 = new java.lang.StringBuilder     // Catch:{ all -> 0x06ed }
            r11.<init>()     // Catch:{ all -> 0x06ed }
            java.lang.String r12 = "\n              |Hostname "
            r11.append(r12)     // Catch:{ all -> 0x06ed }
            r11.append(r2)     // Catch:{ all -> 0x06ed }
            java.lang.String r2 = " not verified:\n              |    certificate: "
            r11.append(r2)     // Catch:{ all -> 0x06ed }
            r11.append(r8)     // Catch:{ all -> 0x06ed }
            java.lang.String r2 = "\n              |    DN: "
            r11.append(r2)     // Catch:{ all -> 0x06ed }
            r11.append(r9)     // Catch:{ all -> 0x06ed }
            java.lang.String r2 = "\n              |    subjectAltNames: "
            r11.append(r2)     // Catch:{ all -> 0x06ed }
            r11.append(r4)     // Catch:{ all -> 0x06ed }
            java.lang.String r2 = "\n              "
            r11.append(r2)     // Catch:{ all -> 0x06ed }
            java.lang.String r2 = r11.toString()     // Catch:{ all -> 0x06ed }
            java.lang.String r2 = defpackage.job.M(r2)     // Catch:{ all -> 0x06ed }
            r5.<init>(r2)     // Catch:{ all -> 0x06ed }
            throw r5     // Catch:{ all -> 0x06ed }
        L_0x06da:
            r6 = 0
            javax.net.ssl.SSLPeerUnverifiedException r4 = new javax.net.ssl.SSLPeerUnverifiedException     // Catch:{ all -> 0x06ed }
            kcd r2 = r2.i     // Catch:{ all -> 0x06ed }
            java.lang.String r2 = r2.c     // Catch:{ all -> 0x06ed }
            java.lang.String r5 = "Hostname "
            java.lang.String r8 = " not verified (no certificates)"
            java.lang.String r2 = defpackage.a.ap(r2, r5, r8)     // Catch:{ all -> 0x06ed }
            r4.<init>(r2)     // Catch:{ all -> 0x06ed }
            throw r4     // Catch:{ all -> 0x06ed }
        L_0x06ed:
            r0 = move-exception
            goto L_0x06f1
        L_0x06ef:
            r0 = move-exception
            r6 = 0
        L_0x06f1:
            r8 = r34
            r15 = r35
            r2 = r0
            r9 = r24
            r11 = r26
            goto L_0x096b
        L_0x06fc:
            r6 = 0
            kbo r4 = r2.e     // Catch:{ all -> 0x0935 }
            defpackage.jnu.b(r4)     // Catch:{ all -> 0x0935 }
            kca r8 = new kca     // Catch:{ all -> 0x0935 }
            kcq r9 = r5.a     // Catch:{ all -> 0x0935 }
            kbp r12 = r5.b     // Catch:{ all -> 0x0935 }
            java.util.List r13 = r5.c     // Catch:{ all -> 0x0935 }
            bdq r14 = new bdq     // Catch:{ all -> 0x0935 }
            r31 = 4
            r32 = 0
            r27 = r14
            r28 = r4
            r29 = r5
            r30 = r2
            r27.<init>(r28, r29, r30, r31, r32)     // Catch:{ all -> 0x0935 }
            r8.<init>(r9, r12, r13, r14)     // Catch:{ all -> 0x0935 }
            r7.d = r8     // Catch:{ all -> 0x0935 }
            r4.b()     // Catch:{ all -> 0x0935 }
            boolean r2 = r11.d     // Catch:{ all -> 0x0935 }
            if (r2 == 0) goto L_0x072e
            kfw r2 = defpackage.kfw.b     // Catch:{ all -> 0x06ed }
            java.lang.String r2 = r2.a(r3)     // Catch:{ all -> 0x06ed }
            goto L_0x072f
        L_0x072e:
            r2 = 0
        L_0x072f:
            r7.c = r3     // Catch:{ all -> 0x0935 }
            kib r4 = defpackage.khn.d(r3)     // Catch:{ all -> 0x0935 }
            khv r5 = new khv     // Catch:{ all -> 0x0935 }
            r5.<init>(r4)     // Catch:{ all -> 0x0935 }
            r7.g = r5     // Catch:{ all -> 0x0935 }
            khz r4 = defpackage.khn.b(r3)     // Catch:{ all -> 0x0935 }
            khe r4 = defpackage.job.ae(r4)     // Catch:{ all -> 0x0935 }
            r7.h = r4     // Catch:{ all -> 0x0935 }
            if (r2 == 0) goto L_0x07a2
            kch r4 = defpackage.kch.HTTP_1_0     // Catch:{ all -> 0x06ed }
            java.lang.String r4 = r4.g     // Catch:{ all -> 0x06ed }
            boolean r4 = defpackage.jnu.i(r2, r4)     // Catch:{ all -> 0x06ed }
            if (r4 == 0) goto L_0x0755
            kch r2 = defpackage.kch.HTTP_1_0     // Catch:{ all -> 0x06ed }
            goto L_0x07a4
        L_0x0755:
            kch r4 = defpackage.kch.HTTP_1_1     // Catch:{ all -> 0x06ed }
            java.lang.String r4 = r4.g     // Catch:{ all -> 0x06ed }
            boolean r4 = defpackage.jnu.i(r2, r4)     // Catch:{ all -> 0x06ed }
            if (r4 == 0) goto L_0x0762
            kch r2 = defpackage.kch.HTTP_1_1     // Catch:{ all -> 0x06ed }
            goto L_0x07a4
        L_0x0762:
            kch r4 = defpackage.kch.H2_PRIOR_KNOWLEDGE     // Catch:{ all -> 0x06ed }
            java.lang.String r4 = r4.g     // Catch:{ all -> 0x06ed }
            boolean r4 = defpackage.jnu.i(r2, r4)     // Catch:{ all -> 0x06ed }
            if (r4 == 0) goto L_0x076f
            kch r2 = defpackage.kch.H2_PRIOR_KNOWLEDGE     // Catch:{ all -> 0x06ed }
            goto L_0x07a4
        L_0x076f:
            kch r4 = defpackage.kch.HTTP_2     // Catch:{ all -> 0x06ed }
            java.lang.String r4 = r4.g     // Catch:{ all -> 0x06ed }
            boolean r4 = defpackage.jnu.i(r2, r4)     // Catch:{ all -> 0x06ed }
            if (r4 == 0) goto L_0x077c
            kch r2 = defpackage.kch.HTTP_2     // Catch:{ all -> 0x06ed }
            goto L_0x07a4
        L_0x077c:
            kch r4 = defpackage.kch.SPDY_3     // Catch:{ all -> 0x06ed }
            java.lang.String r4 = r4.g     // Catch:{ all -> 0x06ed }
            boolean r4 = defpackage.jnu.i(r2, r4)     // Catch:{ all -> 0x06ed }
            if (r4 == 0) goto L_0x0789
            kch r2 = defpackage.kch.SPDY_3     // Catch:{ all -> 0x06ed }
            goto L_0x07a4
        L_0x0789:
            kch r4 = defpackage.kch.QUIC     // Catch:{ all -> 0x06ed }
            java.lang.String r4 = r4.g     // Catch:{ all -> 0x06ed }
            boolean r4 = defpackage.jnu.i(r2, r4)     // Catch:{ all -> 0x06ed }
            if (r4 == 0) goto L_0x0796
            kch r2 = defpackage.kch.QUIC     // Catch:{ all -> 0x06ed }
            goto L_0x07a4
        L_0x0796:
            java.io.IOException r4 = new java.io.IOException     // Catch:{ all -> 0x06ed }
            java.lang.String r5 = "Unexpected protocol: "
            java.lang.String r2 = r5.concat(r2)     // Catch:{ all -> 0x06ed }
            r4.<init>(r2)     // Catch:{ all -> 0x06ed }
            throw r4     // Catch:{ all -> 0x06ed }
        L_0x07a2:
            kch r2 = defpackage.kch.HTTP_1_1     // Catch:{ all -> 0x0935 }
        L_0x07a4:
            r7.e = r2     // Catch:{ all -> 0x0935 }
            kfw r2 = defpackage.kfw.b     // Catch:{ IOException -> 0x0925 }
            r2.j(r3)     // Catch:{ IOException -> 0x0925 }
            kch r2 = r7.e     // Catch:{ IOException -> 0x0925 }
            kch r3 = defpackage.kch.HTTP_2     // Catch:{ IOException -> 0x0925 }
            if (r2 != r3) goto L_0x07c1
            r7.m()     // Catch:{ IOException -> 0x07b5 }
            goto L_0x07c1
        L_0x07b5:
            r0 = move-exception
            r8 = r34
            r15 = r35
            r2 = r0
            r9 = r24
            r11 = r26
            goto L_0x092f
        L_0x07c1:
            r14 = r17
            r12 = r25
        L_0x07c5:
            kcp r2 = r7.a     // Catch:{ IOException -> 0x091a }
            java.net.InetSocketAddress r3 = r2.c     // Catch:{ IOException -> 0x091a }
            java.net.Proxy r2 = r2.b     // Catch:{ IOException -> 0x091a }
            java.lang.String r4 = "inetSocketAddress"
            defpackage.jnu.e(r3, r4)     // Catch:{ IOException -> 0x091a }
            java.lang.String r3 = "proxy"
            defpackage.jnu.e(r2, r3)     // Catch:{ IOException -> 0x091a }
        L_0x07d5:
            kcp r2 = r7.a     // Catch:{ all -> 0x0ac0 }
            boolean r2 = r2.a()     // Catch:{ all -> 0x0ac0 }
            if (r2 == 0) goto L_0x07ef
            java.net.Socket r2 = r7.b     // Catch:{ all -> 0x0ac0 }
            if (r2 == 0) goto L_0x07e2
            goto L_0x07ef
        L_0x07e2:
            java.lang.String r2 = "Too many tunnel connections attempted: 21"
            kdl r3 = new kdl     // Catch:{ all -> 0x0ac0 }
            java.net.ProtocolException r4 = new java.net.ProtocolException     // Catch:{ all -> 0x0ac0 }
            r4.<init>(r2)     // Catch:{ all -> 0x0ac0 }
            r3.<init>(r4)     // Catch:{ all -> 0x0ac0 }
            throw r3     // Catch:{ all -> 0x0ac0 }
        L_0x07ef:
            long r2 = java.lang.System.nanoTime()     // Catch:{ all -> 0x0ac0 }
            r7.m = r2     // Catch:{ all -> 0x0ac0 }
            kdg r2 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r3 = 0
            r2.m = r3     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdg r2 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kcg r2 = r2.a     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            dlv r2 = r2.y     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kcp r3 = r7.a     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r2.C(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdk r2 = r1.i     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kbj r3 = r1.a     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdg r4 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r9 = r24
            r5 = 1
            boolean r2 = r2.a(r3, r4, r9, r5)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r2 == 0) goto L_0x082a
            kdg r2 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdi r9 = r2.h     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.jnu.b(r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r11 = r26
            r1.h = r11     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.net.Socket r2 = r7.a()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.kcs.r(r2)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.kby.c(r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            goto L_0x084b
        L_0x082a:
            monitor-enter(r7)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kdk r2 = r1.i     // Catch:{ all -> 0x0916 }
            byte[] r3 = defpackage.kcs.a     // Catch:{ all -> 0x0916 }
            java.lang.Object r3 = r2.d     // Catch:{ all -> 0x0916 }
            j$.util.concurrent.ConcurrentLinkedQueue r3 = (j$.util.concurrent.ConcurrentLinkedQueue) r3     // Catch:{ all -> 0x0916 }
            r3.add(r7)     // Catch:{ all -> 0x0916 }
            java.lang.Object r3 = r2.b     // Catch:{ all -> 0x0916 }
            java.lang.Object r2 = r2.c     // Catch:{ all -> 0x0916 }
            kcu r2 = (defpackage.kcu) r2     // Catch:{ all -> 0x0916 }
            kcw r3 = (defpackage.kcw) r3     // Catch:{ all -> 0x0916 }
            r3.f(r2)     // Catch:{ all -> 0x0916 }
            kdg r2 = r1.b     // Catch:{ all -> 0x0916 }
            r2.e(r7)     // Catch:{ all -> 0x0916 }
            monitor-exit(r7)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.kby.c(r7)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r9 = r7
        L_0x084b:
            byte[] r2 = defpackage.kcs.a     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            long r2 = java.lang.System.nanoTime()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.net.Socket r4 = r9.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.jnu.b(r4)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.net.Socket r5 = r9.c     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.jnu.b(r5)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            khf r6 = r9.g     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.jnu.b(r6)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            boolean r4 = r4.isClosed()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r4 != 0) goto L_0x08e0
            boolean r4 = r5.isClosed()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r4 != 0) goto L_0x08e0
            boolean r4 = r5.isInputShutdown()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r4 != 0) goto L_0x08e0
            boolean r4 = r5.isOutputShutdown()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r4 == 0) goto L_0x0879
            goto L_0x08e0
        L_0x0879:
            kfc r4 = r9.f     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r4 == 0) goto L_0x0882
            boolean r2 = r4.m(r2)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            goto L_0x0896
        L_0x0882:
            monitor-enter(r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            long r7 = r9.m     // Catch:{ all -> 0x08dc }
            long r2 = r2 - r7
            monitor-exit(r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r7 = 10000000000(0x2540be400, double:4.9406564584E-314)
            int r2 = (r2 > r7 ? 1 : (r2 == r7 ? 0 : -1))
            if (r2 < 0) goto L_0x0898
            if (r23 != 0) goto L_0x0898
            boolean r2 = defpackage.kcs.v(r5, r6)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x0896:
            if (r2 == 0) goto L_0x08e0
        L_0x0898:
            java.net.Socket r2 = r9.c     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.jnu.b(r2)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            khf r3 = r9.g     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.jnu.b(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            khe r4 = r9.h     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            defpackage.jnu.b(r4)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kfc r5 = r9.f     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r5 == 0) goto L_0x08b5
            kfd r2 = new kfd     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r8 = r34
            r15 = r35
            r2.<init>(r8, r9, r15, r5)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            goto L_0x08db
        L_0x08b5:
            r8 = r34
            r15 = r35
            int r5 = r15.d     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r2.setSoTimeout(r5)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kid r2 = r3.a()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            int r5 = r15.d     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            long r5 = (long) r5     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.util.concurrent.TimeUnit r7 = java.util.concurrent.TimeUnit.MILLISECONDS     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r2.n(r5, r7)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kid r2 = r4.a()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            int r5 = r15.e     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            long r5 = (long) r5     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.util.concurrent.TimeUnit r7 = java.util.concurrent.TimeUnit.MILLISECONDS     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r2.n(r5, r7)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kef r2 = new kef     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r2.<init>(r8, r9, r3, r4)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x08db:
            return r2
        L_0x08dc:
            r0 = move-exception
            r2 = r0
            monitor-exit(r9)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r2     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x08e0:
            r8 = r34
            r15 = r35
            r9.e()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            kcp r2 = r1.h     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r2 != 0) goto L_0x0908
            kdm r2 = r1.c     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r2 == 0) goto L_0x0908
            boolean r2 = r2.b()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r2 != 0) goto L_0x0908
            kdn r2 = r1.d     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r2 == 0) goto L_0x0908
            boolean r2 = r2.a()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            if (r2 == 0) goto L_0x0900
            goto L_0x0908
        L_0x0900:
            java.io.IOException r2 = new java.io.IOException     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r3 = "exhausted all routes"
            r2.<init>(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r2     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x0908:
            r2 = r8
            r3 = r15
            r7 = r16
            r4 = r20
            r5 = r21
            r6 = r22
            r8 = r23
            goto L_0x0018
        L_0x0916:
            r0 = move-exception
            r2 = r0
            monitor-exit(r7)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r2     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x091a:
            r0 = move-exception
            r8 = r34
            r15 = r35
            r9 = r24
            r11 = r26
            goto L_0x0a0a
        L_0x0925:
            r0 = move-exception
            r8 = r34
            r15 = r35
            r9 = r24
            r11 = r26
            r2 = r0
        L_0x092f:
            r14 = r17
            r17 = r25
            goto L_0x0a3b
        L_0x0935:
            r0 = move-exception
            r8 = r34
            r15 = r35
            r9 = r24
            r11 = r26
            goto L_0x096a
        L_0x093f:
            r0 = move-exception
            r8 = r34
            r15 = r35
            r9 = r24
            r11 = r26
            r6 = 0
            goto L_0x096a
        L_0x094a:
            r0 = move-exception
            r15 = r35
            goto L_0x095f
        L_0x094e:
            r0 = move-exception
            r15 = r35
            goto L_0x095d
        L_0x0952:
            r0 = move-exception
            r15 = r35
            goto L_0x095b
        L_0x0956:
            r0 = move-exception
            r15 = r35
            r20 = r4
        L_0x095b:
            r21 = r5
        L_0x095d:
            r22 = r6
        L_0x095f:
            r17 = r8
            r25 = r12
            r9 = r24
            r11 = r26
            r6 = 0
            r8 = r34
        L_0x096a:
            r2 = r0
        L_0x096b:
            r14 = r17
            goto L_0x09ce
        L_0x096e:
            r0 = move-exception
            r8 = r34
            r15 = r35
            r20 = r4
            r21 = r5
            r22 = r6
            r25 = r12
            r9 = r24
            r11 = r26
            r6 = 0
        L_0x0980:
            r2 = r0
            goto L_0x09cc
        L_0x0982:
            r8 = r34
            r15 = r35
            r20 = r4
            r21 = r5
            r22 = r6
            r25 = r12
            r9 = r24
            r11 = r26
            r6 = 0
            java.net.UnknownServiceException r2 = new java.net.UnknownServiceException     // Catch:{ all -> 0x09ca }
            java.lang.String[] r4 = r3.getEnabledProtocols()     // Catch:{ all -> 0x09ca }
            defpackage.jnu.b(r4)     // Catch:{ all -> 0x09ca }
            java.lang.String r4 = java.util.Arrays.toString(r4)     // Catch:{ all -> 0x09ca }
            java.lang.String r5 = "toString(...)"
            defpackage.jnu.d(r4, r5)     // Catch:{ all -> 0x09ca }
            java.lang.StringBuilder r5 = new java.lang.StringBuilder     // Catch:{ all -> 0x09ca }
            r5.<init>()     // Catch:{ all -> 0x09ca }
            java.lang.String r12 = "Unable to find acceptable protocols. isFallback="
            r5.append(r12)     // Catch:{ all -> 0x09ca }
            r5.append(r13)     // Catch:{ all -> 0x09ca }
            java.lang.String r12 = ", modes="
            r5.append(r12)     // Catch:{ all -> 0x09ca }
            r5.append(r10)     // Catch:{ all -> 0x09ca }
            java.lang.String r12 = ", supported protocols="
            r5.append(r12)     // Catch:{ all -> 0x09ca }
            r5.append(r4)     // Catch:{ all -> 0x09ca }
            java.lang.String r4 = r5.toString()     // Catch:{ all -> 0x09ca }
            r2.<init>(r4)     // Catch:{ all -> 0x09ca }
            throw r2     // Catch:{ all -> 0x09ca }
        L_0x09ca:
            r0 = move-exception
            goto L_0x0980
        L_0x09cc:
            r14 = r18
        L_0x09ce:
            r12 = r25
            goto L_0x0a01
        L_0x09d1:
            r0 = move-exception
            r8 = r34
            r15 = r35
            r20 = r4
            r21 = r5
            r22 = r6
            r9 = r24
            r10 = r25
            r11 = r26
            r6 = 0
            r2 = r0
        L_0x09e4:
            r12 = r17
            r14 = r18
            goto L_0x0a01
        L_0x09e9:
            r0 = move-exception
            r8 = r34
            r15 = r35
            r20 = r4
            r21 = r5
            r22 = r6
            r9 = r24
            r10 = r25
            r11 = r26
            r6 = 0
            r2 = r0
            r12 = r17
            r14 = r18
            r3 = 0
        L_0x0a01:
            if (r3 == 0) goto L_0x0a0c
            kfw r4 = defpackage.kfw.b     // Catch:{ IOException -> 0x0a09 }
            r4.j(r3)     // Catch:{ IOException -> 0x0a09 }
            goto L_0x0a0c
        L_0x0a09:
            r0 = move-exception
        L_0x0a0a:
            r2 = r0
            goto L_0x0a12
        L_0x0a0c:
            if (r3 == 0) goto L_0x0a11
            defpackage.kcs.r(r3)     // Catch:{ IOException -> 0x0a09 }
        L_0x0a11:
            throw r2     // Catch:{ IOException -> 0x0a09 }
        L_0x0a12:
            r17 = r12
            goto L_0x0a3b
        L_0x0a15:
            r0 = move-exception
            r8 = r34
            r15 = r35
            r20 = r4
            r21 = r5
            r22 = r6
            r9 = r24
            r10 = r25
            r11 = r26
            r6 = 0
            goto L_0x0a38
        L_0x0a28:
            r0 = move-exception
            r20 = r4
            r21 = r5
            r22 = r6
            r23 = r8
            r18 = r14
            r19 = r15
            r6 = 0
            r8 = r2
            r15 = r3
        L_0x0a38:
            r2 = r0
        L_0x0a39:
            r14 = r18
        L_0x0a3b:
            java.net.Socket r3 = r7.c     // Catch:{ all -> 0x0ac0 }
            if (r3 == 0) goto L_0x0a42
            defpackage.kcs.r(r3)     // Catch:{ all -> 0x0ac0 }
        L_0x0a42:
            java.net.Socket r3 = r7.b     // Catch:{ all -> 0x0ac0 }
            if (r3 == 0) goto L_0x0a49
            defpackage.kcs.r(r3)     // Catch:{ all -> 0x0ac0 }
        L_0x0a49:
            r3 = 0
            r7.c = r3     // Catch:{ all -> 0x0ac0 }
            r7.b = r3     // Catch:{ all -> 0x0ac0 }
            r7.g = r3     // Catch:{ all -> 0x0ac0 }
            r7.h = r3     // Catch:{ all -> 0x0ac0 }
            r7.d = r3     // Catch:{ all -> 0x0ac0 }
            r7.e = r3     // Catch:{ all -> 0x0ac0 }
            r7.f = r3     // Catch:{ all -> 0x0ac0 }
            r3 = 1
            r7.k = r3     // Catch:{ all -> 0x0ac0 }
            kcp r4 = r7.a     // Catch:{ all -> 0x0ac0 }
            java.net.InetSocketAddress r5 = r4.c     // Catch:{ all -> 0x0ac0 }
            java.net.Proxy r4 = r4.b     // Catch:{ all -> 0x0ac0 }
            java.lang.String r12 = "inetSocketAddress"
            defpackage.jnu.e(r5, r12)     // Catch:{ all -> 0x0ac0 }
            java.lang.String r5 = "proxy"
            defpackage.jnu.e(r4, r5)     // Catch:{ all -> 0x0ac0 }
            if (r19 != 0) goto L_0x0a73
            kdl r4 = new kdl     // Catch:{ all -> 0x0ac0 }
            r4.<init>(r2)     // Catch:{ all -> 0x0ac0 }
            goto L_0x0a7c
        L_0x0a73:
            r4 = r19
            java.io.IOException r5 = r4.a     // Catch:{ all -> 0x0ac0 }
            defpackage.jji.a(r5, r2)     // Catch:{ all -> 0x0ac0 }
            r4.b = r2     // Catch:{ all -> 0x0ac0 }
        L_0x0a7c:
            if (r16 == 0) goto L_0x0aaa
            if (r14 == 0) goto L_0x0aaa
            boolean r5 = r2 instanceof java.net.ProtocolException     // Catch:{ all -> 0x0ac0 }
            if (r5 != 0) goto L_0x0aaa
            boolean r5 = r2 instanceof java.io.InterruptedIOException     // Catch:{ all -> 0x0ac0 }
            if (r5 != 0) goto L_0x0aaa
            boolean r5 = r2 instanceof javax.net.ssl.SSLHandshakeException     // Catch:{ all -> 0x0ac0 }
            if (r5 == 0) goto L_0x0a94
            java.lang.Throwable r5 = r2.getCause()     // Catch:{ all -> 0x0ac0 }
            boolean r5 = r5 instanceof java.security.cert.CertificateException     // Catch:{ all -> 0x0ac0 }
            if (r5 != 0) goto L_0x0aaa
        L_0x0a94:
            boolean r5 = r2 instanceof javax.net.ssl.SSLPeerUnverifiedException     // Catch:{ all -> 0x0ac0 }
            if (r5 != 0) goto L_0x0aaa
            boolean r2 = r2 instanceof javax.net.ssl.SSLException     // Catch:{ all -> 0x0ac0 }
            if (r2 == 0) goto L_0x0aaa
            r13 = r3
            r2 = r8
            r3 = r15
            r5 = r21
            r6 = r22
            r8 = r23
            r15 = r4
            r4 = r20
            goto L_0x0309
        L_0x0aaa:
            throw r4     // Catch:{ all -> 0x0ac0 }
        L_0x0aab:
            java.lang.String r2 = "H2_PRIOR_KNOWLEDGE cannot be used with HTTPS"
            kdl r3 = new kdl     // Catch:{ all -> 0x0ac0 }
            java.net.UnknownServiceException r4 = new java.net.UnknownServiceException     // Catch:{ all -> 0x0ac0 }
            r4.<init>(r2)     // Catch:{ all -> 0x0ac0 }
            r3.<init>(r4)     // Catch:{ all -> 0x0ac0 }
            throw r3     // Catch:{ all -> 0x0ac0 }
        L_0x0ab8:
            java.lang.IllegalStateException r2 = new java.lang.IllegalStateException     // Catch:{ all -> 0x0ac0 }
            java.lang.String r3 = "already connected"
            r2.<init>(r3)     // Catch:{ all -> 0x0ac0 }
            throw r2     // Catch:{ all -> 0x0ac0 }
        L_0x0ac0:
            r0 = move-exception
            r2 = r0
            kdg r3 = r1.b     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r4 = 0
            r3.m = r4     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r2     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x0ac8:
            java.io.IOException r2 = new java.io.IOException     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r3 = "Canceled"
            r2.<init>(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r2     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x0ad0:
            java.util.NoSuchElementException r2 = new java.util.NoSuchElementException     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            r2.<init>()     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r2     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x0ad6:
            java.io.IOException r2 = new java.io.IOException     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            java.lang.String r3 = "Canceled"
            r2.<init>(r3)     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
            throw r2     // Catch:{ kdl -> 0x0ae9, IOException -> 0x0ade }
        L_0x0ade:
            r0 = move-exception
            r2 = r0
            r1.b(r2)
            kdl r3 = new kdl
            r3.<init>(r2)
            throw r3
        L_0x0ae9:
            r0 = move-exception
            r2 = r0
            java.io.IOException r3 = r2.b
            r1.b(r3)
            throw r2
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.kdc.a(kcg, kdu):kds");
    }

    public final void b(IOException iOException) {
        jnu.e(iOException, "e");
        this.h = null;
        if ((iOException instanceof kfp) && ((kfp) iOException).a == keh.REFUSED_STREAM) {
            this.e++;
        } else if (iOException instanceof keg) {
            this.f++;
        } else {
            this.g++;
        }
    }

    public final boolean c(kcd kcd) {
        int i2 = kcd.d;
        kcd kcd2 = this.a.i;
        if (i2 != kcd2.d || !jnu.i(kcd.c, kcd2.c)) {
            return false;
        }
        return true;
    }
}
