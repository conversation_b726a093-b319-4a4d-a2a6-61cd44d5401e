package defpackage;

/* renamed from: cwl  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwl implements hko {
    public final /* synthetic */ cwm a;
    public final /* synthetic */ boolean b;
    public final /* synthetic */ boolean c;
    public final /* synthetic */ csx d;

    public /* synthetic */ cwl(cwm cwm, boolean z, boolean z2, csx csx) {
        this.a = cwm;
        this.b = z;
        this.c = z2;
        this.d = csx;
    }

    public final hme a(Object obj) {
        Void voidR = (Void) obj;
        if (this.b && !this.c) {
            return hfc.K(haq.a);
        }
        return this.a.c.k(this.d);
    }
}
