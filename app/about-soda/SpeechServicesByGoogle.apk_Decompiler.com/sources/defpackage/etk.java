package defpackage;

/* renamed from: etk  reason: default package */
/* compiled from: PG */
final class etk implements hls {
    final /* synthetic */ jix a;
    final /* synthetic */ int b;

    public etk(jix jix, int i) {
        this.a = jix;
        this.b = i;
    }

    public final void a(Throwable th) {
        ((hby) ((hby) etm.a.h().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl$2", "onFailure", 297, "AudioServiceImpl.java")).s("#audio# Failed to get HotwordStopListeningStatus. sessionToken: %d", this.b);
        this.a.c(eki.i(eag.FAILED_CLOSING_ERROR_IN_GETTING_AUDIO_SOURCE_CLOSING_STATUS, eam.CLIENT_REQUESTED));
        this.a.b(th);
    }

    public final /* bridge */ /* synthetic */ void b(Object obj) {
        this.a.c((ebp) obj);
        this.a.a();
    }
}
