package defpackage;

import j$.util.concurrent.ConcurrentLinkedQueue;
import java.util.Queue;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;

/* renamed from: cqd  reason: default package */
/* compiled from: PG */
public final class cqd {
    public final Queue a = new ConcurrentLinkedQueue();
    private final Executor b;
    private final AtomicReference c = new AtomicReference();
    private final cqb d = new cqb();
    private Throwable e;

    public cqd(Executor executor) {
        jnu.e(executor, "executorForThrowables");
        this.b = executor;
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v44, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v2, resolved type: cqg} */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object a(defpackage.hkn r36) {
        /*
            r35 = this;
            r1 = r35
            cdr r0 = new cdr
            r2 = 2
            r0.<init>(r1, r2)
            r2 = r36
            hme r2 = defpackage.hfc.P(r2, r0)
            cqh r0 = defpackage.cqj.a
            java.lang.Thread r3 = java.lang.Thread.currentThread()
            java.lang.Thread r4 = defpackage.cqj.b
            if (r3 != r4) goto L_0x0021
            cqg r3 = defpackage.cqj.c
            if (r3 != 0) goto L_0x0036
            cqg r3 = r0.a()
            goto L_0x0036
        L_0x0021:
            boolean r0 = r3 instanceof defpackage.cpu
            if (r0 == 0) goto L_0x002a
            cpu r3 = (defpackage.cpu) r3
            cqg r3 = r3.c
            goto L_0x0036
        L_0x002a:
            cqi r0 = defpackage.cqi.a
            java.lang.Object r0 = r0.get()
            defpackage.jnu.b(r0)
            r3 = r0
            cqg r3 = (defpackage.cqg) r3
        L_0x0036:
            boolean r0 = r3.c
            if (r0 != 0) goto L_0x0047
            int r0 = r3.b
            int r0 = android.os.Process.getThreadPriority(r0)
            r4 = 0
            long r4 = defpackage.cqe.b(r0, r4)
            goto L_0x0059
        L_0x0047:
            java.util.concurrent.atomic.AtomicLong r0 = r3.f
            long r4 = r0.get()
            int r0 = defpackage.cqf.d(r4)
            long r4 = defpackage.cqf.e(r4)
            long r4 = defpackage.cqe.b(r0, r4)
        L_0x0059:
            cpz r6 = new cpz
            r6.<init>(r4, r3, r2)
        L_0x005e:
            boolean r0 = r2.isDone()
            if (r0 != 0) goto L_0x02aa
            java.util.concurrent.atomic.AtomicReference r0 = r1.c
            boolean r0 = defpackage.a.i(r0, r6)
            r4 = 1
            r5 = 0
            if (r0 == 0) goto L_0x015c
            boolean r7 = r3.d
            if (r7 != 0) goto L_0x0074
            r3.d = r4
        L_0x0074:
            cqb r0 = r1.d
            java.lang.Thread r4 = r3.a
            r0.a(r4)
            long r8 = android.os.Binder.clearCallingIdentity()
            android.os.StrictMode$ThreadPolicy r4 = android.os.StrictMode.getThreadPolicy()
            android.os.StrictMode$ThreadPolicy$Builder r0 = new android.os.StrictMode$ThreadPolicy$Builder
            r0.<init>(r4)
            android.os.StrictMode$ThreadPolicy$Builder r0 = r0.permitDiskReads()
            android.os.StrictMode$ThreadPolicy$Builder r0 = r0.permitDiskWrites()
            android.os.StrictMode$ThreadPolicy r0 = r0.build()
            android.os.StrictMode.setThreadPolicy(r0)
            boolean r0 = java.lang.Thread.interrupted()
            r10 = r0
        L_0x009c:
            r11 = 0
            java.util.Queue r0 = r1.a     // Catch:{ all -> 0x012c }
            java.lang.Object r0 = r0.poll()     // Catch:{ all -> 0x012c }
            java.lang.Runnable r0 = (java.lang.Runnable) r0     // Catch:{ all -> 0x012c }
            if (r0 != 0) goto L_0x00c7
            boolean r0 = r2.isDone()     // Catch:{ all -> 0x012c }
            if (r0 != 0) goto L_0x00fd
            java.lang.String r0 = "Expected "
            java.lang.String r12 = " to be done, as no runnables were queued"
            java.lang.String r0 = defpackage.a.ao(r2, r0, r12)     // Catch:{ all -> 0x012c }
            java.lang.Throwable r2 = r1.e     // Catch:{ all -> 0x012c }
            if (r2 == 0) goto L_0x00c1
            java.util.concurrent.ExecutionException r2 = new java.util.concurrent.ExecutionException     // Catch:{ all -> 0x012c }
            java.lang.Throwable r12 = r1.e     // Catch:{ all -> 0x012c }
            r2.<init>(r0, r12)     // Catch:{ all -> 0x012c }
            throw r2     // Catch:{ all -> 0x012c }
        L_0x00c1:
            cqc r2 = new cqc     // Catch:{ all -> 0x012c }
            r2.<init>(r0)     // Catch:{ all -> 0x012c }
            throw r2     // Catch:{ all -> 0x012c }
        L_0x00c7:
            r0.run()     // Catch:{ RuntimeException -> 0x00e8, Error -> 0x00d3 }
        L_0x00ca:
            boolean r0 = java.lang.Thread.interrupted()     // Catch:{ all -> 0x012c }
            r0 = r0 | r10
            r10 = r0
            goto L_0x00fd
        L_0x00d1:
            r0 = move-exception
            goto L_0x012e
        L_0x00d3:
            r0 = move-exception
            r12 = r0
            r1.e = r12     // Catch:{ all -> 0x00d1 }
            java.util.concurrent.Executor r0 = r1.b     // Catch:{ all -> 0x00d1 }
            cmp r13 = new cmp     // Catch:{ all -> 0x00d1 }
            r14 = 9
            r13.<init>(r12, r14)     // Catch:{ all -> 0x00d1 }
            java.lang.Runnable r12 = defpackage.gof.h(r13)     // Catch:{ all -> 0x00d1 }
            r0.execute(r12)     // Catch:{ all -> 0x00d1 }
            goto L_0x00ca
        L_0x00e8:
            r0 = move-exception
            r12 = r0
            r1.e = r12     // Catch:{ all -> 0x00d1 }
            java.util.concurrent.Executor r0 = r1.b     // Catch:{ all -> 0x00d1 }
            cmp r13 = new cmp     // Catch:{ all -> 0x00d1 }
            r14 = 8
            r13.<init>(r12, r14)     // Catch:{ all -> 0x00d1 }
            java.lang.Runnable r12 = defpackage.gof.h(r13)     // Catch:{ all -> 0x00d1 }
            r0.execute(r12)     // Catch:{ all -> 0x00d1 }
            goto L_0x00ca
        L_0x00fd:
            boolean r0 = r2.isDone()     // Catch:{ all -> 0x012c }
            if (r0 == 0) goto L_0x009c
            cqb r0 = r1.d
            r0.a(r11)
            java.util.concurrent.atomic.AtomicReference r0 = r1.c
            r0.set(r11)
            r6.a()
            if (r7 != 0) goto L_0x011d
            r3.d = r5
            boolean r0 = r3.e
            if (r0 == 0) goto L_0x011d
            r3.e = r5
            r3.b()
        L_0x011d:
            android.os.Binder.restoreCallingIdentity(r8)
            android.os.StrictMode.setThreadPolicy(r4)
            if (r10 == 0) goto L_0x005e
            java.lang.Thread r0 = r3.a
            r0.interrupt()
            goto L_0x005e
        L_0x012c:
            r0 = move-exception
            goto L_0x0134
        L_0x012e:
            boolean r2 = java.lang.Thread.interrupted()     // Catch:{ all -> 0x012c }
            r10 = r10 | r2
            throw r0     // Catch:{ all -> 0x012c }
        L_0x0134:
            cqb r2 = r1.d
            r2.a(r11)
            java.util.concurrent.atomic.AtomicReference r2 = r1.c
            r2.set(r11)
            r6.a()
            if (r7 != 0) goto L_0x014e
            r3.d = r5
            boolean r2 = r3.e
            if (r2 == 0) goto L_0x014e
            r3.e = r5
            r3.b()
        L_0x014e:
            android.os.Binder.restoreCallingIdentity(r8)
            android.os.StrictMode.setThreadPolicy(r4)
            if (r10 == 0) goto L_0x015b
            java.lang.Thread r2 = r3.a
            r2.interrupt()
        L_0x015b:
            throw r0
        L_0x015c:
            java.util.concurrent.atomic.AtomicReference r0 = r1.c
            java.lang.Object r0 = r0.get()
            cpz r0 = (defpackage.cpz) r0
            if (r0 == 0) goto L_0x005e
            cqg r7 = r0.a
            boolean r7 = defpackage.jnu.i(r7, r3)
            if (r7 != 0) goto L_0x02a2
            java.lang.String r7 = "myIdentifier"
            defpackage.jnu.e(r3, r7)
            cpy r7 = new cpy
            r7.<init>(r3)
        L_0x0178:
            java.util.concurrent.atomic.AtomicReference r8 = r0.c
            java.lang.Object r8 = r8.get()
            cqx r8 = (defpackage.cqx) r8
            cpx r9 = defpackage.cpx.a
            if (r8 == r9) goto L_0x005e
            r9 = r8
            cpy r9 = (defpackage.cpy) r9
            r7.b = r9
            java.util.concurrent.atomic.AtomicReference r9 = r0.c
            boolean r8 = defpackage.a.l(r9, r8, r7)
            if (r8 == 0) goto L_0x0178
            r7 = r5
        L_0x0192:
            java.util.concurrent.atomic.AtomicLong r8 = r3.f     // Catch:{ all -> 0x0296 }
            long r14 = r8.get()     // Catch:{ all -> 0x0296 }
            java.util.concurrent.atomic.AtomicLong r8 = r3.f     // Catch:{ all -> 0x0296 }
            r16 = 0
            r18 = 123(0x7b, float:1.72E-43)
            r11 = 0
            r12 = 0
            r13 = 1
            r19 = 0
            r20 = 0
            r9 = r14
            r4 = r14
            r14 = r19
            r15 = r20
            long r9 = defpackage.cqf.i(r9, r11, r12, r13, r14, r15, r16, r18)     // Catch:{ all -> 0x0296 }
            boolean r8 = r8.compareAndSet(r4, r9)     // Catch:{ all -> 0x0296 }
            if (r8 == 0) goto L_0x0292
            boolean r8 = r3.c     // Catch:{ all -> 0x0296 }
            if (r8 == 0) goto L_0x01be
            int r4 = defpackage.cqf.d(r4)     // Catch:{ all -> 0x0296 }
            goto L_0x01c4
        L_0x01be:
            int r4 = r3.b     // Catch:{ all -> 0x0296 }
            int r4 = android.os.Process.getThreadPriority(r4)     // Catch:{ all -> 0x0296 }
        L_0x01c4:
            int r5 = r0.get()     // Catch:{ all -> 0x0296 }
            boolean r8 = defpackage.cqa.c(r5)     // Catch:{ all -> 0x0296 }
            if (r8 != 0) goto L_0x0274
            int r8 = defpackage.cqa.b(r5)     // Catch:{ all -> 0x0296 }
            if (r8 > r4) goto L_0x01d6
            goto L_0x0274
        L_0x01d6:
            r8 = 4
            r9 = 1
            r10 = 0
            int r8 = defpackage.cqa.e(r5, r4, r9, r10, r8)     // Catch:{ all -> 0x0296 }
            boolean r5 = r0.compareAndSet(r5, r8)     // Catch:{ all -> 0x0296 }
            if (r5 == 0) goto L_0x01c4
            cqg r5 = r0.a     // Catch:{ all -> 0x0296 }
            long r11 = r0.b     // Catch:{ all -> 0x0296 }
            long r11 = defpackage.cqe.b(r4, r11)     // Catch:{ all -> 0x0296 }
            boolean r4 = r5.c     // Catch:{ all -> 0x0296 }
            if (r4 == 0) goto L_0x0274
        L_0x01ef:
            java.util.concurrent.atomic.AtomicLong r4 = r5.f     // Catch:{ all -> 0x0296 }
            long r13 = r4.get()     // Catch:{ all -> 0x0296 }
            long r15 = defpackage.cqf.e(r13)     // Catch:{ all -> 0x0296 }
            long r17 = defpackage.cqe.c(r11)     // Catch:{ all -> 0x0296 }
            int r4 = (r15 > r17 ? 1 : (r15 == r17 ? 0 : -1))
            if (r4 != 0) goto L_0x0274
            int r4 = defpackage.cqf.a(r13)     // Catch:{ all -> 0x0296 }
            r8 = -21
            if (r4 == r8) goto L_0x0213
            int r4 = defpackage.cqf.a(r13)     // Catch:{ all -> 0x0296 }
            int r8 = defpackage.cqe.a(r11)     // Catch:{ all -> 0x0296 }
            if (r4 <= r8) goto L_0x0274
        L_0x0213:
            int r19 = defpackage.cqe.a(r11)     // Catch:{ all -> 0x0296 }
            r20 = 0
            r22 = 95
            r15 = 0
            r16 = 0
            r17 = 0
            r18 = 0
            r23 = r13
            long r13 = defpackage.cqf.i(r13, r15, r16, r17, r18, r19, r20, r22)     // Catch:{ all -> 0x0296 }
            boolean r4 = defpackage.cqf.g(r23)     // Catch:{ all -> 0x0296 }
            if (r4 == 0) goto L_0x023c
            java.util.concurrent.atomic.AtomicLong r4 = r5.f     // Catch:{ all -> 0x0296 }
            r9 = r23
            boolean r4 = r4.compareAndSet(r9, r13)     // Catch:{ all -> 0x0296 }
            if (r4 == 0) goto L_0x0239
            goto L_0x0274
        L_0x0239:
            r9 = 1
            r10 = 0
            goto L_0x01ef
        L_0x023c:
            r9 = r23
            int r4 = defpackage.cqf.d(r9)     // Catch:{ all -> 0x0296 }
            int r8 = defpackage.cqf.d(r13)     // Catch:{ all -> 0x0296 }
            if (r4 != r8) goto L_0x0251
            java.util.concurrent.atomic.AtomicLong r4 = r5.f     // Catch:{ all -> 0x0296 }
            boolean r4 = r4.compareAndSet(r9, r13)     // Catch:{ all -> 0x0296 }
            if (r4 == 0) goto L_0x0239
            goto L_0x0274
        L_0x0251:
            java.util.concurrent.atomic.AtomicLong r4 = r5.f     // Catch:{ all -> 0x0296 }
            r32 = 0
            r34 = 125(0x7d, float:1.75E-43)
            r27 = 0
            r28 = 1
            r29 = 0
            r30 = 0
            r31 = 0
            r25 = r13
            long r13 = defpackage.cqf.i(r25, r27, r28, r29, r30, r31, r32, r34)     // Catch:{ all -> 0x0296 }
            boolean r4 = r4.compareAndSet(r9, r13)     // Catch:{ all -> 0x0296 }
            if (r4 == 0) goto L_0x0239
            int r4 = defpackage.cqf.d(r9)     // Catch:{ all -> 0x0296 }
            r5.a(r4)     // Catch:{ all -> 0x0296 }
        L_0x0274:
            java.util.concurrent.locks.LockSupport.park(r0)     // Catch:{ all -> 0x0296 }
            java.util.concurrent.atomic.AtomicReference r4 = r0.c     // Catch:{ all -> 0x0296 }
            java.lang.Object r4 = r4.get()     // Catch:{ all -> 0x0296 }
            cpx r5 = defpackage.cpx.a     // Catch:{ all -> 0x0296 }
            if (r4 != r5) goto L_0x028d
            r3.d()
            if (r7 == 0) goto L_0x005e
            java.lang.Thread r0 = r3.a
            r0.interrupt()
            goto L_0x005e
        L_0x028d:
            boolean r4 = java.lang.Thread.interrupted()     // Catch:{ all -> 0x0296 }
            r7 = r7 | r4
        L_0x0292:
            r4 = 1
            r5 = 0
            goto L_0x0192
        L_0x0296:
            r0 = move-exception
            r3.d()
            if (r7 == 0) goto L_0x02a1
            java.lang.Thread r2 = r3.a
            r2.interrupt()
        L_0x02a1:
            throw r0
        L_0x02a2:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            java.lang.String r2 = "Reentrant call would deadlock!"
            r0.<init>(r2)
            throw r0
        L_0x02aa:
            java.lang.Object r0 = defpackage.hfc.S(r2)
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cqd.a(hkn):java.lang.Object");
    }

    public final void b(Runnable runnable) {
        jnu.e(runnable, "r");
        this.a.add(runnable);
    }
}
