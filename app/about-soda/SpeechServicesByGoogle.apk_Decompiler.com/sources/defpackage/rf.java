package defpackage;

import android.content.Context;
import android.content.res.TypedArray;
import android.util.AttributeSet;
import android.util.Log;
import android.view.ViewGroup;

/* renamed from: rf  reason: default package */
/* compiled from: PG */
public final class rf extends ViewGroup.MarginLayoutParams {
    public int A = Integer.MIN_VALUE;
    public int B = Integer.MIN_VALUE;
    public int C = Integer.MIN_VALUE;
    public int D = 0;
    boolean E = true;
    boolean F = true;
    public float G = 0.5f;
    public float H = 0.5f;
    public String I = null;

    /* renamed from: J  reason: collision with root package name */
    float f40J = 0.0f;
    int K = 1;
    public float L = -1.0f;
    public float M = -1.0f;
    public int N = 0;
    public int O = 0;
    public int P = 0;
    public int Q = 0;
    public int R = 0;
    public int S = 0;
    public int T = 0;
    public int U = 0;
    public float V = 1.0f;
    public float W = 1.0f;
    public int X = -1;
    public int Y = -1;
    public int Z = -1;
    public int a = -1;
    public boolean aa = false;
    public boolean ab = false;
    public String ac = null;
    public int ad = 0;
    public boolean ae = true;
    public boolean af = true;
    public boolean ag = false;
    public boolean ah = false;
    public boolean ai = false;
    public boolean aj = false;
    public boolean ak = false;
    public int al = -1;
    public int am = -1;
    public int an = -1;
    public int ao = -1;
    public int ap = Integer.MIN_VALUE;
    public int aq = Integer.MIN_VALUE;
    public float ar = 0.5f;
    public int as;
    public int at;
    public float au;
    public qg av = new qg();
    public boolean aw = false;
    public int b = -1;
    public float c = -1.0f;
    public boolean d = true;
    public int e = -1;
    public int f = -1;
    public int g = -1;
    public int h = -1;
    public int i = -1;
    public int j = -1;
    public int k = -1;
    public int l = -1;
    public int m = -1;
    public int n = -1;
    public int o = -1;
    public int p = -1;
    public int q = 0;
    public float r = 0.0f;
    public int s = -1;
    public int t = -1;
    public int u = -1;
    public int v = -1;
    public int w = Integer.MIN_VALUE;
    public int x = Integer.MIN_VALUE;
    public int y = Integer.MIN_VALUE;
    public int z = Integer.MIN_VALUE;

    public rf() {
        super(-2, -2);
    }

    public final void a() {
        this.ah = false;
        this.ae = true;
        this.af = true;
        if (this.width == -2 && this.aa) {
            this.ae = false;
            if (this.P == 0) {
                this.P = 1;
            }
        }
        if (this.height == -2 && this.ab) {
            this.af = false;
            if (this.Q == 0) {
                this.Q = 1;
            }
        }
        if (this.width == 0 || this.width == -1) {
            this.ae = false;
            if (this.width == 0 && this.P == 1) {
                this.width = -2;
                this.aa = true;
            }
        }
        if (this.height == 0 || this.height == -1) {
            this.af = false;
            if (this.height == 0 && this.Q == 1) {
                this.height = -2;
                this.ab = true;
            }
        }
        if (this.c != -1.0f || this.a != -1 || this.b != -1) {
            this.ah = true;
            this.ae = true;
            this.af = true;
            if (!(this.av instanceof qj)) {
                this.av = new qj();
            }
            ((qj) this.av).c(this.Z);
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:12:0x0045  */
    /* JADX WARNING: Removed duplicated region for block: B:15:0x004c  */
    /* JADX WARNING: Removed duplicated region for block: B:18:0x0053  */
    /* JADX WARNING: Removed duplicated region for block: B:21:0x0059  */
    /* JADX WARNING: Removed duplicated region for block: B:24:0x005f  */
    /* JADX WARNING: Removed duplicated region for block: B:33:0x0075  */
    /* JADX WARNING: Removed duplicated region for block: B:34:0x007d  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void resolveLayoutDirection(int r11) {
        /*
            r10 = this;
            int r0 = r10.leftMargin
            int r1 = r10.rightMargin
            super.resolveLayoutDirection(r11)
            int r11 = r10.getLayoutDirection()
            r2 = -1
            r10.an = r2
            r10.ao = r2
            r10.al = r2
            r10.am = r2
            int r3 = r10.w
            r10.ap = r3
            int r3 = r10.y
            r10.aq = r3
            float r3 = r10.G
            r10.ar = r3
            int r4 = r10.a
            r10.as = r4
            int r5 = r10.b
            r10.at = r5
            float r6 = r10.c
            r10.au = r6
            r7 = -2147483648(0xffffffff80000000, float:-0.0)
            r8 = 1
            if (r11 != r8) goto L_0x008d
            int r11 = r10.s
            if (r11 == r2) goto L_0x0039
            r10.an = r11
        L_0x0037:
            r11 = r8
            goto L_0x0041
        L_0x0039:
            int r11 = r10.t
            if (r11 == r2) goto L_0x0040
            r10.ao = r11
            goto L_0x0037
        L_0x0040:
            r11 = 0
        L_0x0041:
            int r9 = r10.u
            if (r9 == r2) goto L_0x0048
            r10.am = r9
            r11 = r8
        L_0x0048:
            int r9 = r10.v
            if (r9 == r2) goto L_0x004f
            r10.al = r9
            r11 = r8
        L_0x004f:
            int r9 = r10.A
            if (r9 == r7) goto L_0x0055
            r10.aq = r9
        L_0x0055:
            int r9 = r10.B
            if (r9 == r7) goto L_0x005b
            r10.ap = r9
        L_0x005b:
            r7 = 1065353216(0x3f800000, float:1.0)
            if (r11 == 0) goto L_0x0063
            float r11 = r7 - r3
            r10.ar = r11
        L_0x0063:
            boolean r11 = r10.ah
            if (r11 == 0) goto L_0x00b1
            int r11 = r10.Z
            if (r11 != r8) goto L_0x00b1
            boolean r11 = r10.d
            if (r11 == 0) goto L_0x00b1
            r11 = -1082130432(0xffffffffbf800000, float:-1.0)
            int r3 = (r6 > r11 ? 1 : (r6 == r11 ? 0 : -1))
            if (r3 == 0) goto L_0x007d
            float r7 = r7 - r6
            r10.au = r7
            r10.as = r2
            r10.at = r2
            goto L_0x00b1
        L_0x007d:
            if (r4 == r2) goto L_0x0086
            r10.at = r4
            r10.as = r2
        L_0x0083:
            r10.au = r11
            goto L_0x00b1
        L_0x0086:
            if (r5 == r2) goto L_0x00b1
            r10.as = r5
            r10.at = r2
            goto L_0x0083
        L_0x008d:
            int r11 = r10.s
            if (r11 == r2) goto L_0x0093
            r10.am = r11
        L_0x0093:
            int r11 = r10.t
            if (r11 == r2) goto L_0x0099
            r10.al = r11
        L_0x0099:
            int r11 = r10.u
            if (r11 == r2) goto L_0x009f
            r10.an = r11
        L_0x009f:
            int r11 = r10.v
            if (r11 == r2) goto L_0x00a5
            r10.ao = r11
        L_0x00a5:
            int r11 = r10.A
            if (r11 == r7) goto L_0x00ab
            r10.ap = r11
        L_0x00ab:
            int r11 = r10.B
            if (r11 == r7) goto L_0x00b1
            r10.aq = r11
        L_0x00b1:
            int r11 = r10.u
            if (r11 != r2) goto L_0x00fb
            int r11 = r10.v
            if (r11 != r2) goto L_0x00fb
            int r11 = r10.t
            if (r11 != r2) goto L_0x00fb
            int r11 = r10.s
            if (r11 != r2) goto L_0x00fb
            int r11 = r10.g
            if (r11 == r2) goto L_0x00d0
            r10.an = r11
            int r11 = r10.rightMargin
            if (r11 > 0) goto L_0x00de
            if (r1 <= 0) goto L_0x00de
            r10.rightMargin = r1
            goto L_0x00de
        L_0x00d0:
            int r11 = r10.h
            if (r11 == r2) goto L_0x00de
            r10.ao = r11
            int r11 = r10.rightMargin
            if (r11 > 0) goto L_0x00de
            if (r1 <= 0) goto L_0x00de
            r10.rightMargin = r1
        L_0x00de:
            int r11 = r10.e
            if (r11 == r2) goto L_0x00ed
            r10.al = r11
            int r11 = r10.leftMargin
            if (r11 > 0) goto L_0x00fb
            if (r0 <= 0) goto L_0x00fb
            r10.leftMargin = r0
            return
        L_0x00ed:
            int r11 = r10.f
            if (r11 == r2) goto L_0x00fb
            r10.am = r11
            int r11 = r10.leftMargin
            if (r11 > 0) goto L_0x00fb
            if (r0 <= 0) goto L_0x00fb
            r10.leftMargin = r0
        L_0x00fb:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.rf.resolveLayoutDirection(int):void");
    }

    public rf(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, rs.b);
        int indexCount = obtainStyledAttributes.getIndexCount();
        for (int i2 = 0; i2 < indexCount; i2++) {
            int index = obtainStyledAttributes.getIndex(i2);
            int i3 = re.a.get(index);
            switch (i3) {
                case 1:
                    this.Z = obtainStyledAttributes.getInt(index, this.Z);
                    break;
                case 2:
                    int resourceId = obtainStyledAttributes.getResourceId(index, this.p);
                    this.p = resourceId;
                    if (resourceId != -1) {
                        break;
                    } else {
                        this.p = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case 3:
                    this.q = obtainStyledAttributes.getDimensionPixelSize(index, this.q);
                    break;
                case 4:
                    float f2 = obtainStyledAttributes.getFloat(index, this.r) % 360.0f;
                    this.r = f2;
                    if (f2 >= 0.0f) {
                        break;
                    } else {
                        this.r = (360.0f - f2) % 360.0f;
                        break;
                    }
                case 5:
                    this.a = obtainStyledAttributes.getDimensionPixelOffset(index, this.a);
                    break;
                case 6:
                    this.b = obtainStyledAttributes.getDimensionPixelOffset(index, this.b);
                    break;
                case 7:
                    this.c = obtainStyledAttributes.getFloat(index, this.c);
                    break;
                case 8:
                    int resourceId2 = obtainStyledAttributes.getResourceId(index, this.e);
                    this.e = resourceId2;
                    if (resourceId2 != -1) {
                        break;
                    } else {
                        this.e = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER:
                    int resourceId3 = obtainStyledAttributes.getResourceId(index, this.f);
                    this.f = resourceId3;
                    if (resourceId3 != -1) {
                        break;
                    } else {
                        this.f = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER:
                    int resourceId4 = obtainStyledAttributes.getResourceId(index, this.g);
                    this.g = resourceId4;
                    if (resourceId4 != -1) {
                        break;
                    } else {
                        this.g = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER:
                    int resourceId5 = obtainStyledAttributes.getResourceId(index, this.h);
                    this.h = resourceId5;
                    if (resourceId5 != -1) {
                        break;
                    } else {
                        this.h = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER:
                    int resourceId6 = obtainStyledAttributes.getResourceId(index, this.i);
                    this.i = resourceId6;
                    if (resourceId6 != -1) {
                        break;
                    } else {
                        this.i = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER:
                    int resourceId7 = obtainStyledAttributes.getResourceId(index, this.j);
                    this.j = resourceId7;
                    if (resourceId7 != -1) {
                        break;
                    } else {
                        this.j = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER:
                    int resourceId8 = obtainStyledAttributes.getResourceId(index, this.k);
                    this.k = resourceId8;
                    if (resourceId8 != -1) {
                        break;
                    } else {
                        this.k = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case 15:
                    int resourceId9 = obtainStyledAttributes.getResourceId(index, this.l);
                    this.l = resourceId9;
                    if (resourceId9 != -1) {
                        break;
                    } else {
                        this.l = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case 16:
                    int resourceId10 = obtainStyledAttributes.getResourceId(index, this.m);
                    this.m = resourceId10;
                    if (resourceId10 != -1) {
                        break;
                    } else {
                        this.m = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case 17:
                    int resourceId11 = obtainStyledAttributes.getResourceId(index, this.s);
                    this.s = resourceId11;
                    if (resourceId11 != -1) {
                        break;
                    } else {
                        this.s = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case 18:
                    int resourceId12 = obtainStyledAttributes.getResourceId(index, this.t);
                    this.t = resourceId12;
                    if (resourceId12 != -1) {
                        break;
                    } else {
                        this.t = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case 19:
                    int resourceId13 = obtainStyledAttributes.getResourceId(index, this.u);
                    this.u = resourceId13;
                    if (resourceId13 != -1) {
                        break;
                    } else {
                        this.u = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case 20:
                    int resourceId14 = obtainStyledAttributes.getResourceId(index, this.v);
                    this.v = resourceId14;
                    if (resourceId14 != -1) {
                        break;
                    } else {
                        this.v = obtainStyledAttributes.getInt(index, -1);
                        break;
                    }
                case 21:
                    this.w = obtainStyledAttributes.getDimensionPixelSize(index, this.w);
                    break;
                case 22:
                    this.x = obtainStyledAttributes.getDimensionPixelSize(index, this.x);
                    break;
                case 23:
                    this.y = obtainStyledAttributes.getDimensionPixelSize(index, this.y);
                    break;
                case 24:
                    this.z = obtainStyledAttributes.getDimensionPixelSize(index, this.z);
                    break;
                case 25:
                    this.A = obtainStyledAttributes.getDimensionPixelSize(index, this.A);
                    break;
                case 26:
                    this.B = obtainStyledAttributes.getDimensionPixelSize(index, this.B);
                    break;
                case 27:
                    this.aa = obtainStyledAttributes.getBoolean(index, this.aa);
                    break;
                case 28:
                    this.ab = obtainStyledAttributes.getBoolean(index, this.ab);
                    break;
                case 29:
                    this.G = obtainStyledAttributes.getFloat(index, this.G);
                    break;
                case 30:
                    this.H = obtainStyledAttributes.getFloat(index, this.H);
                    break;
                case 31:
                    int i4 = obtainStyledAttributes.getInt(index, 0);
                    this.P = i4;
                    if (i4 != 1) {
                        break;
                    } else {
                        Log.e("ConstraintLayout", "layout_constraintWidth_default=\"wrap\" is deprecated.\nUse layout_width=\"WRAP_CONTENT\" and layout_constrainedWidth=\"true\" instead.");
                        break;
                    }
                case 32:
                    int i5 = obtainStyledAttributes.getInt(index, 0);
                    this.Q = i5;
                    if (i5 != 1) {
                        break;
                    } else {
                        Log.e("ConstraintLayout", "layout_constraintHeight_default=\"wrap\" is deprecated.\nUse layout_height=\"WRAP_CONTENT\" and layout_constrainedHeight=\"true\" instead.");
                        break;
                    }
                case 33:
                    try {
                        this.R = obtainStyledAttributes.getDimensionPixelSize(index, this.R);
                        break;
                    } catch (Exception unused) {
                        if (obtainStyledAttributes.getInt(index, this.R) != -2) {
                            break;
                        } else {
                            this.R = -2;
                            break;
                        }
                    }
                case 34:
                    try {
                        this.T = obtainStyledAttributes.getDimensionPixelSize(index, this.T);
                        break;
                    } catch (Exception unused2) {
                        if (obtainStyledAttributes.getInt(index, this.T) != -2) {
                            break;
                        } else {
                            this.T = -2;
                            break;
                        }
                    }
                case 35:
                    this.V = Math.max(0.0f, obtainStyledAttributes.getFloat(index, this.V));
                    this.P = 2;
                    break;
                case 36:
                    try {
                        this.S = obtainStyledAttributes.getDimensionPixelSize(index, this.S);
                        break;
                    } catch (Exception unused3) {
                        if (obtainStyledAttributes.getInt(index, this.S) != -2) {
                            break;
                        } else {
                            this.S = -2;
                            break;
                        }
                    }
                case 37:
                    try {
                        this.U = obtainStyledAttributes.getDimensionPixelSize(index, this.U);
                        break;
                    } catch (Exception unused4) {
                        if (obtainStyledAttributes.getInt(index, this.U) != -2) {
                            break;
                        } else {
                            this.U = -2;
                            break;
                        }
                    }
                case 38:
                    this.W = Math.max(0.0f, obtainStyledAttributes.getFloat(index, this.W));
                    this.Q = 2;
                    break;
                default:
                    switch (i3) {
                        case 44:
                            ro.f(this, obtainStyledAttributes.getString(index));
                            break;
                        case 45:
                            this.L = obtainStyledAttributes.getFloat(index, this.L);
                            break;
                        case 46:
                            this.M = obtainStyledAttributes.getFloat(index, this.M);
                            break;
                        case 47:
                            this.N = obtainStyledAttributes.getInt(index, 0);
                            break;
                        case 48:
                            this.O = obtainStyledAttributes.getInt(index, 0);
                            break;
                        case 49:
                            this.X = obtainStyledAttributes.getDimensionPixelOffset(index, this.X);
                            break;
                        case 50:
                            this.Y = obtainStyledAttributes.getDimensionPixelOffset(index, this.Y);
                            break;
                        case 51:
                            this.ac = obtainStyledAttributes.getString(index);
                            break;
                        case 52:
                            int resourceId15 = obtainStyledAttributes.getResourceId(index, this.n);
                            this.n = resourceId15;
                            if (resourceId15 != -1) {
                                break;
                            } else {
                                this.n = obtainStyledAttributes.getInt(index, -1);
                                break;
                            }
                        case 53:
                            int resourceId16 = obtainStyledAttributes.getResourceId(index, this.o);
                            this.o = resourceId16;
                            if (resourceId16 != -1) {
                                break;
                            } else {
                                this.o = obtainStyledAttributes.getInt(index, -1);
                                break;
                            }
                        case 54:
                            this.D = obtainStyledAttributes.getDimensionPixelSize(index, this.D);
                            break;
                        case 55:
                            this.C = obtainStyledAttributes.getDimensionPixelSize(index, this.C);
                            break;
                        default:
                            switch (i3) {
                                case 64:
                                    ro.e(this, obtainStyledAttributes, index, 0);
                                    this.E = true;
                                    break;
                                case 65:
                                    ro.e(this, obtainStyledAttributes, index, 1);
                                    this.F = true;
                                    break;
                                case 66:
                                    this.ad = obtainStyledAttributes.getInt(index, this.ad);
                                    break;
                                case 67:
                                    this.d = obtainStyledAttributes.getBoolean(index, this.d);
                                    break;
                            }
                    }
            }
        }
        obtainStyledAttributes.recycle();
        a();
    }

    public rf(ViewGroup.LayoutParams layoutParams) {
        super(layoutParams);
        if (layoutParams instanceof rf) {
            rf rfVar = (rf) layoutParams;
            this.a = rfVar.a;
            this.b = rfVar.b;
            this.c = rfVar.c;
            this.d = rfVar.d;
            this.e = rfVar.e;
            this.f = rfVar.f;
            this.g = rfVar.g;
            this.h = rfVar.h;
            this.i = rfVar.i;
            this.j = rfVar.j;
            this.k = rfVar.k;
            this.l = rfVar.l;
            this.m = rfVar.m;
            this.n = rfVar.n;
            this.o = rfVar.o;
            this.p = rfVar.p;
            this.q = rfVar.q;
            this.r = rfVar.r;
            this.s = rfVar.s;
            this.t = rfVar.t;
            this.u = rfVar.u;
            this.v = rfVar.v;
            this.w = rfVar.w;
            this.x = rfVar.x;
            this.y = rfVar.y;
            this.z = rfVar.z;
            this.A = rfVar.A;
            this.B = rfVar.B;
            this.C = rfVar.C;
            this.D = rfVar.D;
            this.G = rfVar.G;
            this.H = rfVar.H;
            this.I = rfVar.I;
            this.f40J = rfVar.f40J;
            this.K = rfVar.K;
            this.L = rfVar.L;
            this.M = rfVar.M;
            this.N = rfVar.N;
            this.O = rfVar.O;
            this.aa = rfVar.aa;
            this.ab = rfVar.ab;
            this.P = rfVar.P;
            this.Q = rfVar.Q;
            this.R = rfVar.R;
            this.T = rfVar.T;
            this.S = rfVar.S;
            this.U = rfVar.U;
            this.V = rfVar.V;
            this.W = rfVar.W;
            this.X = rfVar.X;
            this.Y = rfVar.Y;
            this.Z = rfVar.Z;
            this.ae = rfVar.ae;
            this.af = rfVar.af;
            this.ag = rfVar.ag;
            this.ah = rfVar.ah;
            this.al = rfVar.al;
            this.am = rfVar.am;
            this.an = rfVar.an;
            this.ao = rfVar.ao;
            this.ap = rfVar.ap;
            this.aq = rfVar.aq;
            this.ar = rfVar.ar;
            this.ac = rfVar.ac;
            this.ad = rfVar.ad;
            this.av = rfVar.av;
            this.E = rfVar.E;
            this.F = rfVar.F;
        }
    }
}
