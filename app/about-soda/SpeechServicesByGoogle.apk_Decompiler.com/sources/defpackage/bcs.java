package defpackage;

import androidx.work.impl.WorkDatabase;
import java.util.List;
import java.util.concurrent.Executor;

/* renamed from: bcs  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bcs implements bce {
    public final /* synthetic */ Executor a;
    public final /* synthetic */ List b;
    public final /* synthetic */ bam c;
    public final /* synthetic */ WorkDatabase d;

    public /* synthetic */ bcs(Executor executor, List list, bam bam, WorkDatabase workDatabase) {
        this.a = executor;
        this.b = list;
        this.c = bam;
        this.d = workDatabase;
    }

    public final void a(bgt bgt, boolean z) {
        this.a.execute(new ww(this.b, bgt, this.c, this.d, 9));
    }
}
