package defpackage;

import android.view.KeyEvent;
import android.widget.TextView;

/* renamed from: bmx  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bmx implements TextView.OnEditorActionListener {
    public final /* synthetic */ bna a;

    public /* synthetic */ bmx(bna bna) {
        this.a = bna;
    }

    public final boolean onEditorAction(TextView textView, int i, KeyEvent keyEvent) {
        int keyCode;
        bna bna = this.a;
        boolean z = false;
        if (i == 6 || i == 3) {
            bna.d();
        } else if (keyEvent != null && ((keyCode = keyEvent.getKeyCode()) == 66 || keyCode == 160 || keyCode == 84)) {
            z = true;
            if (keyEvent.getAction() == 1) {
                bna.d();
                return true;
            }
        }
        return z;
    }
}
