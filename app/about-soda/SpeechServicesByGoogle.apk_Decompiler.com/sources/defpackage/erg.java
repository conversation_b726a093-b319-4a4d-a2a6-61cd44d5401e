package defpackage;

/* renamed from: erg  reason: default package */
/* compiled from: PG */
public final class erg extends htq implements hvb {
    public static final erg a;
    private static volatile hvh b;

    static {
        erg erg = new erg();
        a = erg;
        htq.z(erg.class, erg);
    }

    private erg() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(a, "\u0004\u0000", (Object[]) null);
        }
        if (i2 == 3) {
            return new erg();
        }
        if (i2 == 4) {
            return new htk((htq) a);
        }
        if (i2 == 5) {
            return a;
        }
        if (i2 != 6) {
            return null;
        }
        hvh hvh = b;
        if (hvh == null) {
            synchronized (erg.class) {
                hvh = b;
                if (hvh == null) {
                    hvh = new htl(a);
                    b = hvh;
                }
            }
        }
        return hvh;
    }
}
