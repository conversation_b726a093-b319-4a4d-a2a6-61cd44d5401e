package defpackage;

import android.os.Process;
import android.os.SystemClock;
import androidx.preference.Preference;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicLong;

/* renamed from: eoz  reason: default package */
/* compiled from: PG */
public final class eoz {
    public final Object a;
    public final Object b;

    public eoz() {
        this((List) null, 3);
    }

    public final synchronized dyx a(eoa eoa, int i, eam eam) {
        jnu.e(eam, "reason");
        return ((eoj) this.a).a(eoa, i, eam, false);
    }

    public final synchronized ebk b(int i, eam eam) {
        jnu.e(eam, "reason");
        return ((eor) this.b).a(i, eam, false);
    }

    public final synchronized eoc c(int i) {
        return ((eoj) this.a).b(i);
    }

    public final synchronized eoc d(eoa eoa, int i) {
        return ((eoj) this.a).c(eoa, i);
    }

    public final synchronized eoc e(eoa eoa, eow eow, dze dze) {
        jnu.e(dze, "params");
        return ((eoj) this.a).i(eoa, eow, dze, new eoz(((eor) this.b).c(), (jne) new eoy(this)));
    }

    public final synchronized eon f(int i) {
        return ((eor) this.b).b(i);
    }

    public final synchronized eon g(ebn ebn, eow eow) {
        jnu.e(ebn, "params");
        return ((eor) this.b).g(ebn, eow, new eoz(((eoj) this.a).d(), 2));
    }

    public final synchronized void h(eoa eoa, eam eam) {
        jnu.e(eam, "reason");
        ((eoj) this.a).e(eoa, eam);
    }

    public final synchronized void i(eow eow, eam eam) {
        jnu.e(eam, "reason");
        ((eoj) this.a).f(eow, eam);
    }

    public final synchronized void j(eow eow, eam eam) {
        jnu.e(eam, "reason");
        ((eor) this.b).d(eow, eam);
    }

    public final synchronized boolean k(dzb... dzbArr) {
        return ((eoj) this.a).g((dzb[]) Arrays.copyOf(dzbArr, 2));
    }

    public final synchronized ekf l(int i, eam eam) {
        jnu.e(eam, "reason");
        return ((eor) this.b).e(i, eam);
    }

    public final int m() {
        return ((Random) this.b).nextInt(Preference.DEFAULT_ORDER);
    }

    public final long n() {
        return ((AtomicLong) this.a).incrementAndGet();
    }

    public final hme o(jne jne) {
        ejj ejj = new ejj(this, jne, 1, (byte[]) null);
        hme D = ((bzl) this.b).D(ejj, hld.a);
        jnu.d(D, "submitAsync(...)");
        return D;
    }

    /* JADX WARNING: type inference failed for: r0v0, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v2, types: [java.lang.Object, jjk] */
    public final eud p(int i, hme hme, Runnable runnable, jix jix) {
        hme.getClass();
        jqs jqs = (jqs) this.b.b();
        jqs.getClass();
        Executor executor = (Executor) this.a.b();
        executor.getClass();
        return new eud(i, hme, runnable, jix, jqs, executor);
    }

    public final synchronized esh q(esg esg, dzn dzn) {
        jnu.e(dzn, "params");
        return ((esl) this.a).e(esg, dzn, new bzj((Object) ((esr) this.b).c(), (byte[]) null));
    }

    public final synchronized esh r(esg esg, Integer num) {
        return ((esl) this.a).a(esg, num);
    }

    public final synchronized eso s(eab eab) {
        jnu.e(eab, "params");
        return ((esr) this.b).a(eab);
    }

    public final synchronized eso t(Integer num) {
        return ((esr) this.b).b(num);
    }

    public final synchronized void u(esg esg, int i, dzx dzx) {
        jnu.e(dzx, "reason");
        ((esl) this.a).b(esg, i, dzx);
    }

    public final synchronized void v(esg esg, dzx dzx) {
        jnu.e(dzx, "reason");
        ((esl) this.a).c(esg, dzx);
    }

    public final synchronized void w(int i, dzx dzx) {
        jnu.e(dzx, "reason");
        ((esr) this.b).d(i, dzx);
    }

    public final synchronized void x(esg esg) {
        ((esl) this.a).d(esg);
    }

    /* JADX WARNING: type inference failed for: r1v0, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r2v0, types: [java.lang.Object, jjk] */
    public final ese y(erq erq) {
        jqs jqs = (jqs) this.a.b();
        jqs.getClass();
        eks eks = (eks) this.b.b();
        eks.getClass();
        return new ese(erq, jqs, eks);
    }

    public eoz(eoj eoj, eor eor) {
        jnu.e(eoj, "audioSessionsRegistry");
        jnu.e(eor, "hotwordSessionsRegistry");
        this.a = eoj;
        this.b = eor;
    }

    public eoz(esl esl, esr esr) {
        jnu.e(esl, "audioRoutesRegistry");
        jnu.e(esr, "hotwordRoutesRegistry");
        this.a = esl;
        this.b = esr;
    }

    public eoz(List list, jne jne) {
        jnu.e(list, "sessions");
        this.a = list;
        this.b = jne;
    }

    /* JADX INFO: this call moved to the top of the method (can break code semantics) */
    public /* synthetic */ eoz(List list, int i) {
        this((i & 1) != 0 ? jkq.a : list, (jne) null);
    }

    public eoz(jjk jjk, jjk jjk2, byte[] bArr) {
        jjk.getClass();
        this.b = jjk;
        jjk2.getClass();
        this.a = jjk2;
    }

    public eoz(jjk jjk, jjk jjk2, char[] cArr) {
        jjk.getClass();
        this.a = jjk;
        jjk2.getClass();
        this.b = jjk2;
    }

    public eoz(jqs jqs) {
        this.a = jqs;
        this.b = new bzl((short[]) null);
    }

    public eoz(cqx cqx) {
        jnu.e(cqx, "clock");
        this.b = new Random();
        this.a = new AtomicLong((((long) Process.myPid()) << 40) + SystemClock.elapsedRealtime());
    }

    public eoz(jjk jjk, jjk jjk2) {
        jjk.getClass();
        this.b = jjk;
        jjk2.getClass();
        this.a = jjk2;
    }
}
