package defpackage;

/* renamed from: elu  reason: default package */
/* compiled from: PG */
final class elu extends jmi implements jne {
    int a;
    final /* synthetic */ elx b;
    final /* synthetic */ ehg c;
    final /* synthetic */ long d;
    final /* synthetic */ ejn e;
    final /* synthetic */ ebl f;
    private /* synthetic */ Object g;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public elu(elx elx, ehg ehg, long j, ejn ejn, ebl ebl, jlr jlr) {
        super(2, jlr);
        this.b = elx;
        this.c = ehg;
        this.d = j;
        this.e = ejn;
        this.f = ebl;
    }

    public final /* bridge */ /* synthetic */ Object b(Object obj, Object obj2) {
        return ((elu) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:12:0x002f, code lost:
        if (r12 != r0) goto L_0x0031;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:26:0x00a9, code lost:
        if (r12 == r0) goto L_0x00ab;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:27:0x00ab, code lost:
        return r0;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r12) {
        /*
            r11 = this;
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r1 = r11.a
            r2 = 0
            r3 = 1
            if (r1 == 0) goto L_0x001a
            if (r1 == r3) goto L_0x0012
            defpackage.jji.c(r12)     // Catch:{ all -> 0x000f }
            goto L_0x00ac
        L_0x000f:
            r12 = move-exception
            goto L_0x00af
        L_0x0012:
            java.lang.Object r1 = r11.g
            jqs r1 = (defpackage.jqs) r1
            defpackage.jji.c(r12)     // Catch:{ all -> 0x0034 }
            goto L_0x0031
        L_0x001a:
            defpackage.jji.c(r12)
            java.lang.Object r12 = r11.g
            jqs r12 = (defpackage.jqs) r12
            ebl r1 = r11.f
            hme r1 = r1.c()     // Catch:{ all -> 0x0034 }
            r11.g = r12     // Catch:{ all -> 0x0034 }
            r11.a = r3     // Catch:{ all -> 0x0034 }
            java.lang.Object r12 = defpackage.jqw.x(r1, r11)     // Catch:{ all -> 0x0034 }
            if (r12 == r0) goto L_0x00ab
        L_0x0031:
            ebj r12 = (defpackage.ebj) r12     // Catch:{ all -> 0x0034 }
            goto L_0x0039
        L_0x0034:
            r12 = move-exception
            java.lang.Object r12 = defpackage.jji.b(r12)
        L_0x0039:
            eaj r1 = defpackage.eaj.UNKNOWN_OPENING_FAILURE
            ebj r1 = defpackage.ejw.d(r1)
            boolean r4 = r12 instanceof defpackage.jjt
            if (r3 != r4) goto L_0x0044
            r12 = r1
        L_0x0044:
            elx r1 = r11.b
            ehg r4 = r11.c
            long r5 = r11.d
            ebj r12 = (defpackage.ebj) r12
            java.lang.Object r7 = r12.f()
            java.lang.String r8 = "<get-startListeningStatus>(...)"
            defpackage.jnu.d(r7, r8)
            ejn r8 = r11.e
            java.lang.String r9 = "clientInfo"
            defpackage.jnu.e(r4, r9)
            java.lang.String r9 = "status"
            defpackage.jnu.e(r7, r9)
            dxm r9 = defpackage.dwt.f
            java.lang.String r10 = "AUDIO_START_LISTENING_DONE"
            defpackage.jnu.d(r9, r10)
            ebo r7 = (defpackage.ebo) r7
            eak r7 = r7.b
            if (r7 != 0) goto L_0x0070
            eak r7 = defpackage.eak.c
        L_0x0070:
            dzq r8 = r8.b
            java.lang.String r10 = "getAudioSourceOpeningStatus(...)"
            defpackage.jnu.d(r7, r10)
            dxh r7 = defpackage.elx.C(r9, r7)
            defpackage.elx.F(r7, r5)
            gnk r5 = defpackage.elx.a
            jyh r6 = defpackage.jyh.HOTWORD
            jyl r6 = defpackage.elx.z(r4, r6, r8, r2)
            r7.g(r5, r6)
            r1.x(r7, r4)
            grh r1 = r12.c()
            boolean r1 = r1.f()
            if (r1 == 0) goto L_0x00c4
            grh r12 = r12.c()     // Catch:{ all -> 0x000f }
            java.lang.Object r12 = r12.b()     // Catch:{ all -> 0x000f }
            hme r12 = (defpackage.hme) r12     // Catch:{ all -> 0x000f }
            r11.g = r2     // Catch:{ all -> 0x000f }
            r1 = 2
            r11.a = r1     // Catch:{ all -> 0x000f }
            java.lang.Object r12 = defpackage.jqw.x(r12, r11)     // Catch:{ all -> 0x000f }
            if (r12 != r0) goto L_0x00ac
        L_0x00ab:
            return r0
        L_0x00ac:
            dyv r12 = (defpackage.dyv) r12     // Catch:{ all -> 0x000f }
            goto L_0x00b3
        L_0x00af:
            java.lang.Object r12 = defpackage.jji.b(r12)
        L_0x00b3:
            boolean r0 = r12 instanceof defpackage.jjt
            if (r3 != r0) goto L_0x00b8
            goto L_0x00b9
        L_0x00b8:
            r2 = r12
        L_0x00b9:
            dyv r2 = (defpackage.dyv) r2
            elx r12 = r11.b
            ehg r0 = r11.c
            long r1 = r11.d
            r12.w(r0, r1)
        L_0x00c4:
            jkd r12 = defpackage.jkd.a
            return r12
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.elu.bk(java.lang.Object):java.lang.Object");
    }

    public final jlr c(Object obj, jlr jlr) {
        elu elu = new elu(this.b, this.c, this.d, this.e, this.f, jlr);
        elu.g = obj;
        return elu;
    }
}
