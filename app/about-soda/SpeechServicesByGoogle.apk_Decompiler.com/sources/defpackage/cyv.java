package defpackage;

/* renamed from: cyv  reason: default package */
/* compiled from: PG */
public final class cyv implements iiu {
    private final jjk a;
    private final jjk b;

    public cyv(jjk jjk, jjk jjk2) {
        this.a = jjk;
        this.b = jjk2;
    }

    /* renamed from: a */
    public final gig b() {
        gif a2 = gig.a();
        a2.a = "LoggingState.pb";
        a2.d(cti.f);
        gqd gqd = gqd.a;
        fpj d = fpl.d(((iim) this.a).a(), (hmh) this.b.b());
        d.c = cqh.E("gms_icing_mdd_network_usage_monitor", gqd);
        d.b();
        d.c(new cym());
        a2.b(d.a());
        return a2.a();
    }
}
