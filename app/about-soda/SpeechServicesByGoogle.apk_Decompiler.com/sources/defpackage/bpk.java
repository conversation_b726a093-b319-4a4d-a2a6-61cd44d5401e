package defpackage;

import android.os.Parcel;
import android.os.Parcelable;
import j$.util.Objects;
import java.util.List;

/* renamed from: bpk  reason: default package */
/* compiled from: PG */
public final class bpk implements Parcelable {
    public static final Parcelable.Creator CREATOR = new aqu(19);
    public final gxq a;
    public final bpg b;

    public bpk(List list, bpg bpg) {
        this.a = gxq.o(list);
        this.b = bpg;
    }

    public final int describeContents() {
        return 0;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof bpk)) {
            return false;
        }
        bpk bpk = (bpk) obj;
        if (!Objects.equals(this.a, bpk.a) || !Objects.equals(this.b, bpk.b)) {
            return false;
        }
        return true;
    }

    public final int hashCode() {
        return Objects.hash(this.a, this.b);
    }

    public final void writeToParcel(Parcel parcel, int i) {
        gxq gxq = this.a;
        int i2 = cgr.i(parcel);
        cgr.C(parcel, 1, gxq);
        cgr.x(parcel, 2, this.b, i);
        cgr.k(parcel, i2);
    }
}
