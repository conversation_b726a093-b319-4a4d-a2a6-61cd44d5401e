package defpackage;

import android.content.Context;
import android.content.SharedPreferences;
import java.util.ArrayList;
import java.util.Iterator;

/* renamed from: cxd  reason: default package */
/* compiled from: PG */
public final class cxd implements cxa {
    private final Context a;
    private final cuk b;
    private final grh c;

    public cxd(Context context, cuk cuk, grh grh) {
        this.a = context;
        this.b = cuk;
        this.c = grh;
    }

    public final hme a() {
        cqh.B(this.a, "gms_icing_mdd_shared_files", this.c).edit().clear().commit();
        return hfc.K((Object) null);
    }

    public final hme c() {
        ArrayList arrayList = new ArrayList();
        SharedPreferences B = cqh.B(this.a, "gms_icing_mdd_shared_files", this.c);
        SharedPreferences.Editor editor = null;
        for (String next : B.getAll().keySet()) {
            try {
                arrayList.add(cqh.K(next, this.a, this.b));
            } catch (czh e) {
                cyh.q(e, "Failed to deserialize newFileKey:".concat(String.valueOf(next)));
                cuk cuk = this.b;
                gry.c("|").g(next).size();
                cuk.a();
                if (editor == null) {
                    editor = B.edit();
                }
                editor.remove(next);
            }
        }
        if (editor != null) {
            editor.commit();
        }
        return hfc.K(arrayList);
    }

    public final hme d() {
        Iterator<String> it;
        SharedPreferences.Editor edit;
        Iterator<String> it2;
        String next;
        Iterator<String> it3;
        boolean z = false;
        if (cqh.v(this.a)) {
            cwf a2 = cwf.a(cqh.l());
            cwf u = cqh.u(this.a, this.b);
            int i = a2.d;
            int i2 = u.d;
            int i3 = 1;
            if (i == i2) {
                z = true;
            } else {
                int i4 = 2;
                if (i < i2) {
                    cyh.i("%s Cannot migrate back from value %s to %s. Clear everything!", "SharedFilesMetadata", u, a2);
                    cuk cuk = this.b;
                    new Exception("Downgraded file key from " + String.valueOf(u) + " to " + String.valueOf(a2) + ".");
                    cuk.a();
                    cqh.w(this.a, a2);
                } else {
                    int i5 = i2 + 1;
                    while (i5 <= a2.d) {
                        try {
                            cwf a3 = cwf.a(i5);
                            int ordinal = a3.ordinal();
                            if (ordinal == i3) {
                                cyh.c("%s: Starting migration to add download transform", "SharedFilesMetadata");
                                SharedPreferences B = cqh.B(this.a, "gms_icing_mdd_shared_files", this.c);
                                SharedPreferences.Editor edit2 = B.edit();
                                Iterator<String> it4 = B.getAll().keySet().iterator();
                                while (it4.hasNext()) {
                                    String next2 = it4.next();
                                    try {
                                        ctj K = cqh.K(next2, this.a, this.b);
                                        it = it4;
                                        ctl ctl = (ctl) cqh.D(B, next2, (hvh) ctl.h.C(7));
                                        if (ctl == null) {
                                            cyh.g("%s: Unable to read sharedFile from shared preferences.", "SharedFilesMetadata");
                                            edit2.remove(next2);
                                        } else {
                                            cqh.G(edit2, next2);
                                            cqh.H(edit2, cqh.O(K), ctl);
                                        }
                                    } catch (czh unused) {
                                        it = it4;
                                        cyh.h("%s Failed to deserialize file key %s, remove and continue.", "SharedFilesMetadata", next2);
                                        this.b.a();
                                        edit2.remove(next2);
                                    }
                                    it4 = it;
                                }
                                if (!edit2.commit()) {
                                    cyh.f("Failed to commit migration metadata to disk");
                                    cuk cuk2 = this.b;
                                    new Exception("Migrate to DownloadTransform failed.");
                                    cuk2.a();
                                }
                                cqh.w(this.a, cwf.a(i5));
                                i5++;
                                i3 = 1;
                                i4 = 2;
                            } else if (ordinal == i4) {
                                cyh.c("%s: Starting migration to dedup on checksum only", "SharedFilesMetadata");
                                SharedPreferences B2 = cqh.B(this.a, "gms_icing_mdd_shared_files", this.c);
                                edit = B2.edit();
                                it2 = B2.getAll().keySet().iterator();
                                while (it2.hasNext()) {
                                    next = it2.next();
                                    ctj K2 = cqh.K(next, this.a, this.b);
                                    it3 = it2;
                                    ctl ctl2 = (ctl) cqh.D(B2, next, (hvh) ctl.h.C(7));
                                    if (ctl2 == null) {
                                        cyh.g("%s: Unable to read sharedFile from shared preferences.", "SharedFilesMetadata");
                                        edit.remove(next);
                                    } else {
                                        cqh.G(edit, next);
                                        cqh.H(edit, cqh.N(K2), ctl2);
                                    }
                                    it2 = it3;
                                }
                                if (!edit.commit()) {
                                    cyh.f("Failed to commit migration metadata to disk");
                                    cuk cuk3 = this.b;
                                    new Exception("Migrate to ChecksumOnly failed.");
                                    cuk3.a();
                                }
                                cqh.w(this.a, cwf.a(i5));
                                i5++;
                                i3 = 1;
                                i4 = 2;
                            } else {
                                throw new UnsupportedOperationException("Upgrade to version " + a3.name() + "not supported!");
                            }
                            if (cqh.u(this.a, this.b).d != a2.d && !cqh.w(this.a, a2)) {
                                cyh.f(String.valueOf(a2));
                                cuk cuk4 = this.b;
                                new Exception(String.valueOf(a2));
                                cuk4.a();
                            }
                            z = false;
                        } catch (czh unused2) {
                            it3 = it2;
                            cyh.h("%s Failed to deserialize file key %s, remove and continue.", "SharedFilesMetadata", next);
                            this.b.a();
                            edit.remove(next);
                        } catch (Throwable th) {
                            if (cqh.u(this.a, this.b).d != a2.d && !cqh.w(this.a, a2)) {
                                cyh.f(String.valueOf(a2));
                                cuk cuk5 = this.b;
                                new Exception(String.valueOf(a2));
                                cuk5.a();
                            }
                            throw th;
                        }
                    }
                    if (cqh.u(this.a, this.b).d != a2.d && !cqh.w(this.a, a2)) {
                        cyh.f(String.valueOf(a2));
                        cuk cuk6 = this.b;
                        new Exception(String.valueOf(a2));
                        cuk6.a();
                    }
                    z = true;
                }
            }
            return hfc.K(Boolean.valueOf(z));
        }
        cyh.c("%s Device isn't migrated to new file key, clear and set migration.", "SharedFilesMetadata");
        cqh.x(this.a);
        cqh.w(this.a, cwf.a(cqh.l()));
        return hfc.K(false);
    }

    public final hme e(ctj ctj) {
        return ftd.K(f(new hbi(ctj)), new cwn(ctj, 17), hld.a);
    }

    public final hme f(gyo gyo) {
        SharedPreferences B = cqh.B(this.a, "gms_icing_mdd_shared_files", this.c);
        gxr gxr = new gxr();
        hbp k = gyo.iterator();
        while (k.hasNext()) {
            ctj ctj = (ctj) k.next();
            ctl ctl = (ctl) cqh.D(B, cqh.L(ctj, this.a, this.b), (hvh) ctl.h.C(7));
            if (ctl != null) {
                gxr.d(ctj, ctl);
            }
        }
        return hfc.K(gxr.a());
    }

    public final hme g(ctj ctj) {
        Context context = this.a;
        grh grh = this.c;
        return hfc.K(Boolean.valueOf(cqh.I(cqh.B(context, "gms_icing_mdd_shared_files", grh), cqh.L(ctj, context, this.b))));
    }

    public final hme h(ctj ctj, ctl ctl) {
        Context context = this.a;
        grh grh = this.c;
        return hfc.K(Boolean.valueOf(cqh.J(cqh.B(context, "gms_icing_mdd_shared_files", grh), cqh.L(ctj, context, this.b), ctl)));
    }
}
