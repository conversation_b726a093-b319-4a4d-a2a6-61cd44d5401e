package defpackage;

import j$.util.DesugarCollections;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.TreeMap;

/* renamed from: itg  reason: default package */
/* compiled from: PG */
public final class itg {
    public static final List a;
    public static final itg b = itd.OK.a();
    public static final itg c = itd.CANCELLED.a();
    public static final itg d = itd.UNKNOWN.a();
    public static final itg e = itd.DEADLINE_EXCEEDED.a();
    public static final itg f = itd.PERMISSION_DENIED.a();
    public static final itg g = itd.RESOURCE_EXHAUSTED.a();
    public static final itg h = itd.FAILED_PRECONDITION.a();
    public static final itg i = itd.UNIMPLEMENTED.a();
    public static final itg j = itd.INTERNAL.a();
    public static final itg k = itd.UNAVAILABLE.a();
    static final irr l = new iru("grpc-status", false, new ite());
    static final irr m;
    private static final irv q;
    public final itd n;
    public final String o;
    public final Throwable p;

    static {
        TreeMap treeMap = new TreeMap();
        itd[] values = itd.values();
        int length = values.length;
        int i2 = 0;
        while (i2 < length) {
            itd itd = values[i2];
            itg itg = (itg) treeMap.put(Integer.valueOf(itd.r), new itg(itd, (String) null, (Throwable) null));
            if (itg == null) {
                i2++;
            } else {
                throw new IllegalStateException("Code value duplication between " + itg.n.name() + " & " + itd.name());
            }
        }
        a = DesugarCollections.unmodifiableList(new ArrayList(treeMap.values()));
        itd.INVALID_ARGUMENT.a();
        itd.NOT_FOUND.a();
        itd.ALREADY_EXISTS.a();
        itd.UNAUTHENTICATED.a();
        itd.ABORTED.a();
        itd.OUT_OF_RANGE.a();
        itd.DATA_LOSS.a();
        itf itf = new itf();
        q = itf;
        m = new iru("grpc-message", false, itf);
    }

    private itg(itd itd, String str, Throwable th) {
        a.w(itd, "code");
        this.n = itd;
        this.o = str;
        this.p = th;
    }

    public static itg b(int i2) {
        if (i2 >= 0) {
            List list = a;
            if (i2 < list.size()) {
                return (itg) list.get(i2);
            }
        }
        return d.e(a.ak(i2, "Unknown code "));
    }

    public static itg c(Throwable th) {
        a.w(th, "t");
        for (Throwable th2 = th; th2 != null; th2 = th2.getCause()) {
            if (th2 instanceof ith) {
                return ((ith) th2).a;
            }
            if (th2 instanceof iti) {
                return ((iti) th2).a;
            }
        }
        return d.d(th);
    }

    static String f(itg itg) {
        if (itg.o == null) {
            return itg.n.toString();
        }
        String obj = itg.n.toString();
        String str = itg.o;
        return obj + ": " + str;
    }

    public final itg a(String str) {
        String str2 = this.o;
        if (str2 == null) {
            return new itg(this.n, str, this.p);
        }
        return new itg(this.n, a.as(str, str2, "\n"), this.p);
    }

    public final itg d(Throwable th) {
        if (a.k(this.p, th)) {
            return this;
        }
        return new itg(this.n, this.o, th);
    }

    public final itg e(String str) {
        if (a.k(this.o, str)) {
            return this;
        }
        return new itg(this.n, str, this.p);
    }

    public final boolean g() {
        if (itd.OK == this.n) {
            return true;
        }
        return false;
    }

    public final String toString() {
        grg r = ftd.r(this);
        r.b("code", this.n.name());
        r.b("description", this.o);
        Throwable th = this.p;
        Object obj = th;
        if (th != null) {
            StringWriter stringWriter = new StringWriter();
            th.printStackTrace(new PrintWriter(stringWriter));
            obj = stringWriter.toString();
        }
        r.b("cause", obj);
        return r.toString();
    }
}
