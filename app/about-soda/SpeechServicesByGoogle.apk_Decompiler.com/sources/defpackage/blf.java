package defpackage;

import android.app.Activity;
import android.content.Context;
import android.content.res.TypedArray;
import android.content.res.XmlResourceParser;
import android.graphics.drawable.Drawable;
import android.util.AttributeSet;
import android.util.Log;
import android.util.Xml;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import com.google.android.tts.R;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import org.xmlpull.v1.XmlPullParserException;

/* renamed from: blf  reason: default package */
/* compiled from: PG */
public class blf {
    public static List c(Context context, int i) {
        Throwable th;
        TypedArray obtainStyledAttributes;
        TypedArray typedArray;
        boolean z;
        boolean z2;
        boolean z3;
        bmo bmo;
        bmi bmi;
        boolean z4;
        Context context2 = context;
        int i2 = i;
        if (i2 == 0) {
            return new ArrayList();
        }
        try {
            XmlResourceParser xml = context.getResources().getXml(i2);
            try {
                AttributeSet asAttributeSet = Xml.asAttributeSet(xml);
                ArrayList arrayList = new ArrayList();
                xml.next();
                xml.next();
                int i3 = 2;
                String str = null;
                xml.require(2, (String) null, "MenuItems");
                while (xml.next() != 3) {
                    xml.require(i3, str, "MenuItem");
                    obtainStyledAttributes = context2.obtainStyledAttributes(asAttributeSet, bjo.d);
                    int resourceId = obtainStyledAttributes.getResourceId(6, -1);
                    String string = obtainStyledAttributes.getString(12);
                    Drawable drawable = obtainStyledAttributes.getDrawable(i3);
                    boolean z5 = obtainStyledAttributes.getBoolean(8, false);
                    boolean z6 = obtainStyledAttributes.getBoolean(9, false);
                    boolean z7 = obtainStyledAttributes.getBoolean(11, true);
                    boolean z8 = obtainStyledAttributes.getBoolean(14, true);
                    boolean z9 = obtainStyledAttributes.getBoolean(10, false);
                    boolean z10 = obtainStyledAttributes.getBoolean(3, false);
                    AttributeSet attributeSet = asAttributeSet;
                    boolean z11 = obtainStyledAttributes.getBoolean(4, false);
                    boolean hasValue = obtainStyledAttributes.hasValue(4);
                    boolean z12 = obtainStyledAttributes.getBoolean(0, false);
                    boolean z13 = z11;
                    ArrayList arrayList2 = arrayList;
                    boolean z14 = obtainStyledAttributes.getBoolean(1, false);
                    boolean hasValue2 = obtainStyledAttributes.hasValue(1);
                    int i4 = obtainStyledAttributes.getInt(5, 0);
                    boolean z15 = z14;
                    int i5 = obtainStyledAttributes.getInt(13, 0);
                    String string2 = obtainStyledAttributes.getString(7);
                    if (string2 != null) {
                        typedArray = obtainStyledAttributes;
                        try {
                            Activity e = bnv.e(context);
                            if (e != null) {
                                z3 = hasValue;
                                z = z5;
                                z2 = z6;
                                bmo = new bmo(e.getClass().getMethod(string2, new Class[]{bml.class}), e);
                            } else {
                                throw new RuntimeException("Couldn't find an activity for the MenuItem");
                            }
                        } catch (NoSuchMethodException e2) {
                            throw new RuntimeException(a.ap(string2, "OnClick method ", "(MenuItem) not found in your activity"), e2);
                        } catch (Throwable th2) {
                            th = th2;
                            typedArray.recycle();
                            throw th;
                        }
                    } else {
                        typedArray = obtainStyledAttributes;
                        z3 = hasValue;
                        z = z5;
                        z2 = z6;
                        bmo = null;
                    }
                    if (i4 == 0) {
                        bmi = bmi.ALWAYS;
                    } else {
                        bmi = bmi.NEVER;
                    }
                    xml.next();
                    xml.require(3, (String) null, "MenuItem");
                    bmh bmh = new bmh(context2);
                    bmh.f = resourceId;
                    bmh.g = string;
                    bmh.h = drawable;
                    bmh.i = bmo;
                    bmh.t = i5;
                    bmh.k = z7;
                    bmh.o = z8;
                    bmh.l = z9;
                    bmh.j = bmi;
                    if (z) {
                        bmh.b = ((Context) bmh.a.get()).getString(R.string.car_ui_toolbar_menu_item_search_title);
                        bmh.d = ((Context) bmh.a.get()).getDrawable(R.drawable.car_ui_icon_search);
                        bmh.r = true;
                        bmh.g = bmh.b;
                        bmh.h = bmh.d;
                    }
                    if (z2) {
                        bmh.c = ((Context) bmh.a.get()).getString(R.string.car_ui_toolbar_menu_item_settings_title);
                        bmh.e = ((Context) bmh.a.get()).getDrawable(R.drawable.car_ui_icon_settings);
                        bmh.s = true;
                        bmh.g = bmh.c;
                        bmh.h = bmh.e;
                        bmh.t = 64;
                    }
                    if (!z10) {
                        if (!z3) {
                            z4 = true;
                            if (z12 || hasValue2) {
                                bmh.p = z4;
                                bmh.q = z15;
                            }
                            bml a = bmh.a();
                            typedArray.recycle();
                            arrayList = arrayList2;
                            arrayList.add(a);
                            str = null;
                            asAttributeSet = attributeSet;
                            i3 = 2;
                        }
                    }
                    z4 = true;
                    bmh.m = true;
                    bmh.n = z13;
                    bmh.p = z4;
                    bmh.q = z15;
                    bml a2 = bmh.a();
                    typedArray.recycle();
                    arrayList = arrayList2;
                    arrayList.add(a2);
                    str = null;
                    asAttributeSet = attributeSet;
                    i3 = 2;
                }
                if (xml != null) {
                    xml.close();
                }
                return arrayList;
            } catch (Throwable th3) {
                th = th3;
                if (xml != null) {
                    xml.close();
                }
                throw th;
            }
        } catch (IOException | XmlPullParserException e3) {
            throw new RuntimeException("Unable to parse Menu Items", e3);
        } catch (Throwable th4) {
            th.addSuppressed(th4);
        }
    }

    @Deprecated
    public static void d() {
        Log.w("carui", "setMessageToShowWhenDisabledPreferenceClicked is deprecated, and does nothing!");
    }

    public static View e(Context context, AttributeSet attributeSet) {
        int i;
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, bjo.b, 0, 0);
        TypedArray obtainStyledAttributes2 = context.obtainStyledAttributes(attributeSet, bjo.k, 0, 0);
        int i2 = obtainStyledAttributes2.getInt(18, 0);
        obtainStyledAttributes2.recycle();
        switch (i2) {
            case 1:
                i = R.layout.car_ui_preference_dropdown_internal;
                break;
            case 2:
                i = R.layout.car_ui_preference_primary_switch_internal;
                break;
            case 3:
                i = R.layout.car_ui_two_action_preference_internal;
                break;
            case 4:
                i = R.layout.car_ui_preference_two_action_text_internal;
                break;
            case 5:
                i = R.layout.car_ui_preference_two_action_text_borderless_internal;
                break;
            case 6:
                i = R.layout.car_ui_preference_two_action_icon_internal;
                break;
            case 7:
                i = R.layout.car_ui_preference_two_action_switch_internal;
                break;
            case 8:
                i = R.layout.car_ui_preference_dialog_edittext_internal;
                break;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                i = R.layout.car_ui_seekbar_dialog_internal;
                break;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                i = R.layout.car_ui_preference_footer_internal;
                break;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                i = R.layout.car_ui_preference_category_internal;
                break;
            default:
                i = R.layout.car_ui_preference_internal;
                break;
        }
        obtainStyledAttributes.recycle();
        return LayoutInflater.from(context).inflate(i, (ViewGroup) null, false);
    }
}
