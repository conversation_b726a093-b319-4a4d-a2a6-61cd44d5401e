package defpackage;

/* renamed from: crs  reason: default package */
/* compiled from: PG */
public final class crs extends ipy {
    final /* synthetic */ cru a;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public crs(ioy ioy, cru cru) {
        super(ioy);
        this.a = cru;
    }

    public final void a(ftc ftc, irw irw) {
        this.c.a(new crr(this, ftc), irw);
    }
}
