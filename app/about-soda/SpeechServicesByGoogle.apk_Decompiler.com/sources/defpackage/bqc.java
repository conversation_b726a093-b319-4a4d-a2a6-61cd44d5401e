package defpackage;

/* renamed from: bqc  reason: default package */
/* compiled from: PG */
public final class bqc {
    public final String a;

    public bqc() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof bqc) {
            return this.a.equals(((bqc) obj).a);
        }
        return false;
    }

    public final int hashCode() {
        return this.a.hashCode() ^ 1000003;
    }

    public final String toString() {
        return "RosieRobotMessage{text=" + this.a + "}";
    }

    public bqc(String str) {
        if (str != null) {
            this.a = str;
            return;
        }
        throw new NullPointerException("Null text");
    }
}
