package defpackage;

import java.util.function.Consumer;

/* renamed from: bmm  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bmm {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;

    public bmm(bfd bfd, jty jty) {
        this.a = bfd;
        this.b = jty;
    }

    /* JADX WARNING: type inference failed for: r2v2, types: [jub, java.lang.Object] */
    public final void a(Object obj) {
        Object obj2;
        if (((bfd) this.a).e(obj)) {
            obj2 = new ber(((bfd) this.a).d());
        } else {
            obj2 = beq.a;
        }
        this.b.g(obj2);
    }

    public /* synthetic */ bmm(bmn bmn, Consumer consumer) {
        this.a = bmn;
        this.b = consumer;
    }
}
