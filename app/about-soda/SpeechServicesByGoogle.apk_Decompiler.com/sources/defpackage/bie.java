package defpackage;

/* renamed from: bie  reason: default package */
/* compiled from: PG */
public final class bie {
    public static final String a = bbk.b("NetworkRequestCompat");
    public final Object b;

    public bie() {
        this((byte[]) null);
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if ((obj instanceof bie) && jnu.i(this.b, ((bie) obj).b)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        Object obj = this.b;
        if (obj == null) {
            return 0;
        }
        return obj.hashCode();
    }

    public final String toString() {
        return "NetworkRequestCompat(wrapped=" + this.b + ')';
    }

    public bie(Object obj) {
        this.b = obj;
    }

    public /* synthetic */ bie(byte[] bArr) {
        this((Object) null);
    }
}
