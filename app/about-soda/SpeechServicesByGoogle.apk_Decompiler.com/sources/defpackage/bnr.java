package defpackage;

/* renamed from: bnr  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bnr implements bmj {
    public final /* synthetic */ bnu a;

    public /* synthetic */ bnr(bnu bnu) {
        this.a = bnu;
    }

    public final void onMenuItemChanged(bml bml) {
        bnu bnu = this.a;
        int indexOf = bnu.e.indexOf(bml);
        if (indexOf >= 0) {
            bnu.f.set(indexOf, bnu.a(bml));
            bnu.g.notifyItemChanged(indexOf);
        } else {
            bnu.b();
        }
        bnu.c();
    }
}
