package defpackage;

/* renamed from: bqj  reason: default package */
/* compiled from: PG */
public final class bqj extends htq implements hvb {
    public static final bqj d;
    private static volatile hvh e;
    public int a;
    public hsq b = hsq.b;
    public int c = 1024;

    static {
        bqj bqj = new bqj();
        d = bqj;
        htq.z(bqj.class, bqj);
    }

    private bqj() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(d, "\u0001\u0002\u0000\u0001\u0001\u0002\u0002\u0000\u0000\u0000\u0001ည\u0000\u0002င\u0001", new Object[]{"a", "b", "c"});
        } else if (i2 == 3) {
            return new bqj();
        } else {
            if (i2 == 4) {
                return new htk((htq) d);
            }
            if (i2 == 5) {
                return d;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = e;
            if (hvh == null) {
                synchronized (bqj.class) {
                    hvh = e;
                    if (hvh == null) {
                        hvh = new htl(d);
                        e = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
