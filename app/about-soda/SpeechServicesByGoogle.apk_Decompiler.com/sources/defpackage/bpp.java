package defpackage;

/* renamed from: bpp  reason: default package */
/* compiled from: PG */
public final class bpp extends Exception {
    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public bpp(int r8, int r9, java.lang.String r10, java.lang.Throwable r11) {
        /*
            r7 = this;
            java.lang.Integer r0 = java.lang.Integer.valueOf(r8)
            r1 = 3
            r2 = 2
            r3 = 1
            if (r8 == r3) goto L_0x0016
            if (r8 == r2) goto L_0x0013
            if (r8 == r1) goto L_0x0010
            java.lang.String r8 = "CONNECTION_ERROR"
            goto L_0x0018
        L_0x0010:
            java.lang.String r8 = "PREPARATION_ERROR"
            goto L_0x0018
        L_0x0013:
            java.lang.String r8 = "INFERENCE_ERROR"
            goto L_0x0018
        L_0x0016:
            java.lang.String r8 = "DOWNLOAD_ERROR"
        L_0x0018:
            java.lang.Integer r4 = java.lang.Integer.valueOf(r9)
            r5 = 15
            if (r9 == r5) goto L_0x0061
            r5 = 16
            if (r9 == r5) goto L_0x005e
            r5 = 501(0x1f5, float:7.02E-43)
            if (r9 == r5) goto L_0x005b
            switch(r9) {
                case 2: goto L_0x0058;
                case 3: goto L_0x0055;
                case 4: goto L_0x0061;
                case 5: goto L_0x0052;
                case 6: goto L_0x004f;
                case 7: goto L_0x004c;
                case 8: goto L_0x0049;
                case 9: goto L_0x0046;
                case 10: goto L_0x0061;
                case 11: goto L_0x0043;
                case 12: goto L_0x0040;
                default: goto L_0x002b;
            }
        L_0x002b:
            switch(r9) {
                case 601: goto L_0x003d;
                case 602: goto L_0x003a;
                case 603: goto L_0x0037;
                case 604: goto L_0x0034;
                case 605: goto L_0x0031;
                default: goto L_0x002e;
            }
        L_0x002e:
            java.lang.String r9 = "UNKNOWN"
            goto L_0x0063
        L_0x0031:
            java.lang.String r9 = "NULL_BINDING"
            goto L_0x0063
        L_0x0034:
            java.lang.String r9 = "NEEDS_SYSTEM_UPDATE"
            goto L_0x0063
        L_0x0037:
            java.lang.String r9 = "BINDING_DIED"
            goto L_0x0063
        L_0x003a:
            java.lang.String r9 = "SERVICE_DISCONNECTED"
            goto L_0x0063
        L_0x003d:
            java.lang.String r9 = "BINDING_FAILURE"
            goto L_0x0063
        L_0x0040:
            java.lang.String r9 = "REQUEST_TOO_LARGE"
            goto L_0x0063
        L_0x0043:
            java.lang.String r9 = "RESPONSE_PROCESSING_ERROR"
            goto L_0x0063
        L_0x0046:
            java.lang.String r9 = "BUSY"
            goto L_0x0063
        L_0x0049:
            java.lang.String r9 = "NOT_AVAILABLE"
            goto L_0x0063
        L_0x004c:
            java.lang.String r9 = "CANCELLED"
            goto L_0x0063
        L_0x004f:
            java.lang.String r9 = "IPC_ERROR"
            goto L_0x0063
        L_0x0052:
            java.lang.String r9 = "COMPUTE_ERROR"
            goto L_0x0063
        L_0x0055:
            java.lang.String r9 = "BAD_REQUEST"
            goto L_0x0063
        L_0x0058:
            java.lang.String r9 = "BAD_DATA"
            goto L_0x0063
        L_0x005b:
            java.lang.String r9 = "NOT_ENOUGH_DISK_SPACE"
            goto L_0x0063
        L_0x005e:
            java.lang.String r9 = "NOT_SUPPORTED"
            goto L_0x0063
        L_0x0061:
            java.lang.String r9 = "REQUEST_PROCESSING_ERROR"
        L_0x0063:
            r5 = 5
            java.lang.Object[] r5 = new java.lang.Object[r5]
            r6 = 0
            r5[r6] = r0
            r5[r3] = r8
            r5[r2] = r4
            r5[r1] = r9
            r8 = 4
            r5[r8] = r10
            java.lang.String r8 = "AICore failed with error type %s-%s and error code %s-%s: %s"
            java.lang.String r8 = java.lang.String.format(r8, r5)
            r7.<init>(r8, r11)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bpp.<init>(int, int, java.lang.String, java.lang.Throwable):void");
    }
}
