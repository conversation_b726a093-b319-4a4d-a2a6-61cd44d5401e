package defpackage;

import android.content.Context;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import androidx.wear.ambient.AmbientModeSupport;
import j$.time.Duration;
import j$.time.ZoneId;
import j$.time.format.DateTimeFormatter;
import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.ReentrantLock;

/* renamed from: cyw  reason: default package */
/* compiled from: PG */
public final class cyw {
    public final Object a;
    public final Object b;
    public final Object c;
    public final Object d;

    public cyw(Context context, diz diz, kjd kjd, AmbientModeSupport.AmbientController ambientController) {
        this.b = context;
        this.c = diz;
        this.d = kjd;
        this.a = ambientController;
    }

    public static cyw i(Executor executor) {
        return new cyw(executor, (czd) new czc());
    }

    /* JADX INFO: finally extract failed */
    public final synchronized ejk A(int i, dzn dzn) {
        ejk ejk;
        dzr dzr;
        eaa eaa;
        dzn dzn2 = dzn;
        synchronized (this) {
            try {
                ejf b2 = ((eji) this.a).b(Integer.valueOf(i));
                jnu.e(dzn2, "params");
                Object obj = this.b;
                if (((erm) obj).b(b2)) {
                    hby hby = (hby) erm.a.f().h(hdg.a, "ALT.SourcePolAdptr").j("com/google/android/libraries/search/audio/policies/adapter/SourcePolicyControllerAdapter", "updateAudioRouteRequestParams", 96, "SourcePolicyControllerAdapter.kt");
                    ehg ehg = b2.b().b;
                    if (ehg == null) {
                        ehg = ehg.c;
                    }
                    String name = ehf.a(ehg.a).name();
                    dzb b3 = dzb.b(b2.b().c);
                    if (b3 == null) {
                        b3 = dzb.DEFAULT;
                    }
                    hby.C("#audio# consulting policy for audio route params client(%s) intent(%s)", name, b3.name());
                    htk l = erb.f.l();
                    jnu.d(l, "newBuilder(...)");
                    erb u = jnu.e(l, "builder").u();
                    dzq dzq = dzn2.b;
                    if (dzq == null) {
                        dzq = dzq.c;
                    }
                    switch (dzp.a(dzq.a).ordinal()) {
                        case 0:
                        case 1:
                        case 2:
                        case 5:
                        case 6:
                        case 7:
                        case 8:
                            htk l2 = erb.f.l();
                            jnu.d(l2, "newBuilder(...)");
                            u = jnu.e(l2, "builder").u();
                            break;
                        case 3:
                            dzq dzq2 = dzn2.b;
                            if (dzq2 == null) {
                                dzq2 = dzq.c;
                            }
                            if (dzq2.a == 4) {
                                dzr = (dzr) dzq2.b;
                            } else {
                                dzr = dzr.d;
                            }
                            jnu.d(dzr, "getBistoAudioRoute(...)");
                            if ((dzr.a & 2) == 0) {
                                ((hby) erm.a.h().h(hdg.a, "ALT.SourcePolAdptr").j("com/google/android/libraries/search/audio/policies/adapter/SourcePolicyControllerAdapter", "maybeAddCurrentSource", 161, "SourcePolicyControllerAdapter.kt")).r("#audio# BISTO route with no buffer ID, not using as current");
                                break;
                            } else {
                                htk htk = (htk) u.C(5);
                                htk.x(u);
                                jnu.d(htk, "toBuilder(...)");
                                dlv W = jnu.e(htk, "builder");
                                htm htm = (htm) era.c.l();
                                jnu.d(htm, "newBuilder(...)");
                                bzl aa = jnu.e(htm, "builder");
                                htm htm2 = (htm) eqz.a.l();
                                jnu.d(htm2, "newBuilder(...)");
                                dlv X = jnu.e(htm2, "builder");
                                gnk gnk = erd.a;
                                jnu.d(gnk, "bistoSource");
                                htk l3 = erc.c.l();
                                jnu.d(l3, "newBuilder(...)");
                                jnu.e(l3, "builder");
                                ead ead = dzr.c;
                                if (ead == null) {
                                    ead = ead.c;
                                }
                                jnu.e(ead, "value");
                                if (!l3.b.B()) {
                                    l3.u();
                                }
                                erc erc = (erc) l3.b;
                                ead.getClass();
                                erc.b = ead;
                                erc.a |= 1;
                                htq o = l3.r();
                                jnu.d(o, "build(...)");
                                X.B(gnk, (erc) o);
                                aa.N(X.w());
                                W.v(aa.M());
                                u = W.u();
                                break;
                            }
                        case 4:
                            dzq dzq3 = dzn2.b;
                            if (dzq3 == null) {
                                dzq3 = dzq.c;
                            }
                            if (dzq3.a == 5) {
                                eaa = (eaa) dzq3.b;
                            } else {
                                eaa = eaa.c;
                            }
                            jnu.d(eaa, "getHandoverRoute(...)");
                            if ((eaa.a & 1) == 0) {
                                ((hby) erm.a.h().h(hdg.a, "ALT.SourcePolAdptr").j("com/google/android/libraries/search/audio/policies/adapter/SourcePolicyControllerAdapter", "maybeAddCurrentSource", 177, "SourcePolicyControllerAdapter.kt")).r("#audio# HANDOVER route with no handoff data, not using as current");
                                break;
                            } else {
                                htk htk2 = (htk) u.C(5);
                                htk2.x(u);
                                jnu.d(htk2, "toBuilder(...)");
                                dlv W2 = jnu.e(htk2, "builder");
                                htm htm3 = (htm) era.c.l();
                                jnu.d(htm3, "newBuilder(...)");
                                bzl aa2 = jnu.e(htm3, "builder");
                                htm htm4 = (htm) eqz.a.l();
                                jnu.d(htm4, "newBuilder(...)");
                                dlv X2 = jnu.e(htm4, "builder");
                                gnk gnk2 = erk.a;
                                jnu.d(gnk2, "handoverSource");
                                htk l4 = erj.c.l();
                                jnu.d(l4, "newBuilder(...)");
                                jnu.e(l4, "builder");
                                ebh ebh = eaa.b;
                                if (ebh == null) {
                                    ebh = ebh.c;
                                }
                                jnu.e(ebh, "value");
                                if (!l4.b.B()) {
                                    l4.u();
                                }
                                erj erj = (erj) l4.b;
                                ebh.getClass();
                                erj.b = ebh;
                                erj.a |= 1;
                                htq o2 = l4.r();
                                jnu.d(o2, "build(...)");
                                X2.B(gnk2, (erj) o2);
                                aa2.N(X2.w());
                                W2.v(aa2.M());
                                u = W2.u();
                                break;
                            }
                        default:
                            throw new jjq();
                    }
                    ero ero = ((erm) obj).b;
                    dzc b4 = b2.b();
                    jnu.e(u, "request");
                    jnu.e(b4, "params");
                    htk htk3 = (htk) u.C(5);
                    htk3.x(u);
                    jnu.d(htk3, "toBuilder(...)");
                    dlv W3 = jnu.e(htk3, "builder");
                    List unmodifiableList = Collections.unmodifiableList(((erb) ((htk) W3.a).b).e);
                    jnu.d(unmodifiableList, "getAvailableSourcesDataList(...)");
                    new hwm(unmodifiableList);
                    efl efl = ero.c;
                    gnk gnk3 = dyq.a;
                    jnu.d(gnk3, "sourceConfig");
                    List<dyo> hty = new hty(((dyp) ftc.aR(b4, gnk3)).a, dyp.b);
                    if (ero.a) {
                        List arrayList = new ArrayList();
                        for (Object next : hty) {
                            if (((dyo) next) != dyo.SOURCE_BISTO) {
                                arrayList.add(next);
                            }
                        }
                        hty = arrayList;
                    }
                    if (ero.b) {
                        List arrayList2 = new ArrayList();
                        for (Object next2 : hty) {
                            if (((dyo) next2) != dyo.SOURCE_BLUETOOTH) {
                                arrayList2.add(next2);
                            }
                        }
                        hty = arrayList2;
                    }
                    ArrayList arrayList3 = new ArrayList();
                    hby hby2 = (hby) efl.a.c().h(hdg.a, "ALT.AudioSrcAvailable").j("com/google/android/libraries/search/audio/audiosourceavailability/impl/AudioSourceAvailabilityImpl", "getAvailableAudioSources", 38, "AudioSourceAvailabilityImpl.kt");
                    ArrayList arrayList4 = new ArrayList(jji.K(hty));
                    for (dyo name2 : hty) {
                        arrayList4.add(name2.name());
                    }
                    hby2.u("#audio# collecting sources, disallowed=%s", arrayList4);
                    if (efl.c.isPresent() && !hty.contains(dyo.SOURCE_BISTO)) {
                        ((hby) efl.a.c().h(hdg.a, "ALT.AudioSrcAvailable").j("com/google/android/libraries/search/audio/audiosourceavailability/impl/AudioSourceAvailabilityImpl", "getAvailableAudioSources", 40, "AudioSourceAvailabilityImpl.kt")).r("#audio# collecting BISTO source if available");
                        jji.J(arrayList3, ((efk) efl.c.get()).a());
                    }
                    if (efl.b.isPresent() && !hty.contains(dyo.SOURCE_BLUETOOTH)) {
                        ((hby) efl.a.c().h(hdg.a, "ALT.AudioSrcAvailable").j("com/google/android/libraries/search/audio/audiosourceavailability/impl/AudioSourceAvailabilityImpl", "getAvailableAudioSources", 45, "AudioSourceAvailabilityImpl.kt")).r("#audio# collecting BLUETOOTH source if available");
                        jji.J(arrayList3, ((efk) efl.b.get()).a());
                    }
                    htm htm5 = (htm) era.c.l();
                    jnu.d(htm5, "newBuilder(...)");
                    bzl aa3 = jnu.e(htm5, "builder");
                    htm htm6 = (htm) eqz.a.l();
                    jnu.d(htm6, "newBuilder(...)");
                    dlv X3 = jnu.e(htm6, "builder");
                    gnk gnk4 = erh.a;
                    jnu.d(gnk4, "builtInSource");
                    htk l5 = erg.a.l();
                    jnu.d(l5, "newBuilder(...)");
                    X3.B(gnk4, jnu.e(l5, "builder").M());
                    aa3.N(X3.w());
                    arrayList3.add(aa3.M());
                    Object obj2 = W3.a;
                    if (!((htk) obj2).b.B()) {
                        ((htk) obj2).u();
                    }
                    erb erb = (erb) ((htk) obj2).b;
                    huf huf = erb.e;
                    if (!huf.c()) {
                        erb.e = htq.s(huf);
                    }
                    hrz.g(arrayList3, erb.e);
                    erb u2 = W3.u();
                    ero.a(b4, true);
                    jnu.e(u2, "request");
                    htm htm7 = (htm) era.c.l();
                    jnu.d(htm7, "newBuilder(...)");
                    bzl aa4 = jnu.e(htm7, "builder");
                    htm htm8 = (htm) eqz.a.l();
                    jnu.d(htm8, "newBuilder(...)");
                    dlv X4 = jnu.e(htm8, "builder");
                    gnk gnk5 = erh.a;
                    jnu.d(gnk5, "builtInSource");
                    htk l6 = erg.a.l();
                    jnu.d(l6, "newBuilder(...)");
                    X4.B(gnk5, jnu.e(l6, "builder").M());
                    aa4.N(X4.w());
                    era M = aa4.M();
                    htk l7 = dzn.c.l();
                    jnu.d(l7, "newBuilder(...)");
                    dku f = jnu.e(l7, "builder");
                    htk l8 = dzq.c.l();
                    jnu.d(l8, "newBuilder(...)");
                    dlv g = jnu.e(l8, "builder");
                    eqz eqz = M.b;
                    if (eqz == null) {
                        eqz = eqz.a;
                    }
                    gnk gnk6 = erk.a;
                    eqz.h(gnk6);
                    if (eqz.r.n((htp) gnk6.c)) {
                        htk l9 = eaa.c.l();
                        jnu.d(l9, "newBuilder(...)");
                        jnu.e(l9, "builder");
                        eqz eqz2 = M.b;
                        if (eqz2 == null) {
                            eqz2 = eqz.a;
                        }
                        gnk gnk7 = erk.a;
                        jnu.d(gnk7, "handoverSource");
                        ebh ebh2 = ((erj) ftc.aR(eqz2, gnk7)).b;
                        if (ebh2 == null) {
                            ebh2 = ebh.c;
                        }
                        jnu.e(ebh2, "value");
                        if (!l9.b.B()) {
                            l9.u();
                        }
                        eaa eaa2 = (eaa) l9.b;
                        ebh2.getClass();
                        eaa2.b = ebh2;
                        eaa2.a |= 1;
                        htq o3 = l9.r();
                        jnu.d(o3, "build(...)");
                        eaa eaa3 = (eaa) o3;
                        jnu.e(eaa3, "value");
                        Object obj3 = g.a;
                        if (!((htk) obj3).b.B()) {
                            ((htk) obj3).u();
                        }
                        dzq dzq4 = (dzq) ((htk) obj3).b;
                        eaa3.getClass();
                        dzq4.b = eaa3;
                        dzq4.a = 5;
                    } else {
                        eqz eqz3 = M.b;
                        if (eqz3 == null) {
                            eqz3 = eqz.a;
                        }
                        gnk gnk8 = erd.a;
                        eqz3.h(gnk8);
                        if (eqz3.r.n((htp) gnk8.c)) {
                            htk l10 = dzr.d.l();
                            jnu.d(l10, "newBuilder(...)");
                            jnu.e(l10, "builder");
                            eqz eqz4 = M.b;
                            if (eqz4 == null) {
                                eqz4 = eqz.a;
                            }
                            gnk gnk9 = erd.a;
                            jnu.d(gnk9, "bistoSource");
                            if ((((erc) ftc.aR(eqz4, gnk9)).a & 1) != 0) {
                                eqz eqz5 = M.b;
                                if (eqz5 == null) {
                                    eqz5 = eqz.a;
                                }
                                gnk gnk10 = erd.a;
                                jnu.d(gnk10, "bistoSource");
                                ead ead2 = ((erc) ftc.aR(eqz5, gnk10)).b;
                                if (ead2 == null) {
                                    ead2 = ead.c;
                                }
                                jnu.e(ead2, "value");
                                if (!l10.b.B()) {
                                    l10.u();
                                }
                                dzr dzr2 = (dzr) l10.b;
                                ead2.getClass();
                                dzr2.c = ead2;
                                dzr2.a |= 2;
                            }
                            htq o4 = l10.r();
                            jnu.d(o4, "build(...)");
                            dzr dzr3 = (dzr) o4;
                            jnu.e(dzr3, "value");
                            Object obj4 = g.a;
                            if (!((htk) obj4).b.B()) {
                                ((htk) obj4).u();
                            }
                            dzq dzq5 = (dzq) ((htk) obj4).b;
                            dzr3.getClass();
                            dzq5.b = dzr3;
                            dzq5.a = 4;
                        } else {
                            eqz eqz6 = M.b;
                            if (eqz6 == null) {
                                eqz6 = eqz.a;
                            }
                            gnk gnk11 = erf.a;
                            eqz6.h(gnk11);
                            if (eqz6.r.n((htp) gnk11.c)) {
                                htk l11 = dzu.c.l();
                                jnu.d(l11, "newBuilder(...)");
                                g.l(jnu.e(l11, "builder").x());
                            } else {
                                htk l12 = dzv.a.l();
                                jnu.d(l12, "newBuilder(...)");
                                jnu.e(l12, "builder");
                                htq o5 = l12.r();
                                jnu.d(o5, "build(...)");
                                dzv dzv = (dzv) o5;
                                jnu.e(dzv, "value");
                                Object obj5 = g.a;
                                if (!((htk) obj5).b.B()) {
                                    ((htk) obj5).u();
                                }
                                dzq dzq6 = (dzq) ((htk) obj5).b;
                                dzv.getClass();
                                dzq6.b = dzv;
                                dzq6.a = 2;
                            }
                        }
                    }
                    f.k(g.k());
                    dzn2 = f.j();
                }
                esh q = ((eoz) this.c).q(b2.d(), dzn2);
                dyz b5 = q.b();
                int a2 = q.a();
                jnu.e(b5, "routeSession");
                ejk = new ejk(b5, a2);
            } catch (Throwable th) {
                while (true) {
                    throw th;
                }
            }
        }
        return ejk;
    }

    public final synchronized ejl B(dzc dzc) {
        ejf a2;
        a2 = ((eji) this.a).a(dzc);
        return new ejl(((ejg) a2).a, ((ejg) a2).b);
    }

    public final synchronized ejm C(int i, int i2) {
        eoc d2;
        d2 = ((eoz) this.d).d(((eji) this.a).b(Integer.valueOf(i)).c(), i2);
        return new ejm(d2.b(), d2.a());
    }

    public final synchronized ejm D(int i, dze dze) {
        eoc e;
        ejf b2 = ((eji) this.a).b(Integer.valueOf(i));
        esh r = ((eoz) this.c).r(b2.d(), (Integer) null);
        dze a2 = ((erm) this.b).a(b2, dze);
        e = ((eoz) this.d).e(b2.c(), r.e(), a2);
        return new ejm(((eof) e).a, ((eof) e).d);
    }

    public final synchronized ejm E(dzl dzl, dze dze) {
        eoc e;
        ebf ebf = dzl.b;
        if (ebf == null) {
            ebf = ebf.c;
        }
        ejf b2 = ((eji) this.a).b(Integer.valueOf(ebf.b));
        Object obj = this.c;
        esg d2 = b2.d();
        ebw ebw = dzl.c;
        if (ebw == null) {
            ebw = ebw.c;
        }
        esh r = ((eoz) obj).r(d2, Integer.valueOf(ebw.b));
        dze a2 = ((erm) this.b).a(b2, dze);
        e = ((eoz) this.d).e(b2.c(), r.e(), a2);
        return new ejm(((eof) e).a, ((eof) e).d);
    }

    public final synchronized ejo F(eab eab) {
        ebi c2;
        int a2;
        eso s = ((eoz) this.c).s(eab);
        c2 = s.c();
        a2 = s.a();
        jnu.e(c2, "routeSession");
        return new ejo(c2, a2);
    }

    public final ejp G(int i) {
        eon f = ((eoz) this.d).f(i);
        return new ejp(f.b(), f.a());
    }

    public final synchronized ejp H(ebn ebn) {
        eon g;
        g = ((eoz) this.d).g(ebn, ((eoz) this.c).t((Integer) null).e());
        return new ejp(((eop) g).a, ((eop) g).d);
    }

    public final synchronized ejp I(eac eac, ebn ebn) {
        eon g;
        ebw ebw = eac.b;
        if (ebw == null) {
            ebw = ebw.c;
        }
        eso t = ((eoz) this.c).t(Integer.valueOf(ebw.b));
        g = ((eoz) this.d).g(ebn, t.e());
        return new ejp(((eop) g).a, ((eop) g).d);
    }

    public final synchronized void J(int i, dzf dzf) {
        ((eji) this.a).c(i, dzf);
    }

    public final synchronized void K(int i, int i2, dzx dzx) {
        ((eoz) this.c).u(((eji) this.a).b(Integer.valueOf(i)).d(), i2, dzx);
    }

    public final synchronized void L(int i, dzx dzx) {
        ((eoz) this.c).w(i, dzx);
    }

    public final synchronized ekf M(int i, eam eam) {
        return ((eoz) this.d).l(i, eam);
    }

    public final /* synthetic */ void a(Runnable runnable) {
        ((bih) this.a).execute(runnable);
    }

    /* JADX WARNING: type inference failed for: r0v8, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v13, types: [java.lang.Object, jjk] */
    public final void b() {
        if (!((AtomicBoolean) this.d).getAndSet(true)) {
            if (!((grh) this.b).f()) {
                ((dmx) ((grm) this.a).a.b()).k();
            }
            ((dpe) ((grm) this.c).a.b()).a();
        }
    }

    public final dbl c(String str, int i) {
        File f = ((dfl) this.b).f(new dcd((String) this.d, dct.d(str, i)));
        if (f.isDirectory()) {
            List d2 = dcp.d(f);
            if (d2.isEmpty()) {
                ((hdc) ((hdc) dcs.a.h()).j("com/google/android/libraries/micore/superpacks/FileManifestStore", "getManifest", 90, "FileManifestStore.java")).r("Got a compressed manifest but no files after uncompressing");
                return null;
            } else if (d2.size() > 1) {
                ((hdc) ((hdc) dcs.a.h()).j("com/google/android/libraries/micore/superpacks/FileManifestStore", "getManifest", 85, "FileManifestStore.java")).r("Unexpectedly got more than one file after uncompressing a manifest");
                return null;
            } else {
                f = (File) d2.get(0);
            }
        }
        return ((bmu) this.a).s(str, i, f);
    }

    /* JADX WARNING: type inference failed for: r3v3, types: [hco, hdc] */
    /* JADX WARNING: type inference failed for: r12v4, types: [hco, hdc] */
    /* JADX WARNING: type inference failed for: r8v10, types: [hco, hdc] */
    /* JADX WARNING: type inference failed for: r11v10, types: [hco, hdc] */
    public final void d(String str, Set set, int i) {
        ((ReentrantLock) this.c).lock();
        try {
            ((hdc) dcs.a.l().j("com/google/android/libraries/micore/superpacks/FileManifestStore", "collectGarbage", 108, "FileManifestStore.java")).u("Starting manifest GC or %s", str);
            List<File> d2 = dcp.d(((dfl) this.b).g((String) this.d));
            if (d2.isEmpty()) {
                ((hdc) dcs.a.l().j("com/google/android/libraries/micore/superpacks/FileManifestStore", "collectGarbage", 112, "FileManifestStore.java")).r("No manifest files to collect");
            } else {
                ArrayList<File> arrayList = new ArrayList<>();
                ArrayList arrayList2 = new ArrayList();
                for (File file : d2) {
                    ddt c2 = dct.c(file.getName());
                    if (c2 == null) {
                        arrayList.add(file);
                    } else if (!set.contains(Integer.valueOf(((dci) c2).b)) && str.equals(((dci) c2).a)) {
                        ((hdc) dcs.a.l().j("com/google/android/libraries/micore/superpacks/FileManifestStore", "collectGarbage", 127, "FileManifestStore.java")).C("Adding GC candidate with versioned name: %s, %s", c2, file);
                        arrayList2.add(file);
                    }
                }
                int size = arrayList2.size();
                ((hdc) dcs.a.l().j("com/google/android/libraries/micore/superpacks/FileManifestStore", "collectGarbage", 135, "FileManifestStore.java")).v("Number of GC candidates: %d, keep count: %d", size, i);
                if (size > i) {
                    Collections.sort(arrayList2, new aom(19));
                    arrayList.addAll(arrayList2.subList(i, arrayList2.size()));
                }
                for (File file2 : arrayList) {
                    ((hdc) ((hdc) dcs.a.f()).j("com/google/android/libraries/micore/superpacks/FileManifestStore", "collectGarbage", 152, "FileManifestStore.java")).C("Deleting file %s from manifest directory, last modified: %s", file2, dds.d(file2.lastModified()));
                    Object obj = this.b;
                    dfl dfl = (dfl) obj;
                    dfl.j(new dcd((String) this.d, file2.getName()), true, dhk.MANIFEST_GC);
                }
            }
        } finally {
            ((ReentrantLock) this.c).unlock();
        }
    }

    /* JADX WARNING: type inference failed for: r4v1, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme e(String str, hme hme) {
        cyh.k("%s: submitting request to add in-progress download future with key: %s", "DownloadFutureMap", str);
        return ((bzj) this.b).q(new dbr((Object) this, (Object) str, (Object) hme, 1), this.c);
    }

    /* JADX WARNING: type inference failed for: r1v2, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme f(String str) {
        cyh.k("%s: submitting check for in-progress download future with key: %s", "DownloadFutureMap", str);
        return ((bzj) this.b).p(new czb(this, str, 0), this.c);
    }

    /* JADX WARNING: type inference failed for: r1v2, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme g(String str) {
        cyh.k("%s: submitting request for in-progress download future with key: %s", "DownloadFutureMap", str);
        return ((bzj) this.b).p(new czb(this, str, 2), this.c);
    }

    /* JADX WARNING: type inference failed for: r1v2, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme h(String str) {
        cyh.k("%s: submitting request to remove in-progress download future with key: %s", "DownloadFutureMap", str);
        return ((bzj) this.b).q(new ctp(this, str, 12, (byte[]) null), this.c);
    }

    public final eol j() {
        if (!cqx.O((Context) this.a, "android.permission.RECORD_AUDIO")) {
            return new eol(eaj.FAILED_OPENING_MICROPHONE_PERMISSION_DENIED, eag.FAILED_CLOSING_MICROPHONE_PERMISSION_DENIED, "microphone permission denied");
        }
        return null;
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [elp, java.lang.Object] */
    public final void k(String str, int i) {
        jnu.e(str, "identity");
        this.a.a(str, i);
        htk l = eaw.h.l();
        jnu.d(l, "newBuilder(...)");
        byw C = jnu.e(l, "builder");
        htk l2 = ean.g.l();
        jnu.d(l2, "newBuilder(...)");
        bzl z = jnu.e(l2, "builder");
        z.l(eau.OUTCOME_BUFFER_CLOSED);
        z.j(str);
        z.k(i);
        C.j(z.i());
        ((eug) this.b).e(C.i());
        emd emd = (emd) this.d;
        ((fqv) ((emi) emd.b.b()).b.a()).b((double) i, (String) emd.d.a());
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [elp, java.lang.Object] */
    public final void l(String str) {
        jnu.e(str, "identity");
        this.a.d(str);
    }

    /* JADX WARNING: type inference failed for: r3v0, types: [elp, java.lang.Object] */
    public final void m(esh esh) {
        ehg ehg = esh.f().b.b;
        if (ehg == null) {
            ehg = ehg.c;
        }
        jnu.d(ehg, "getClientInfo(...)");
        Integer valueOf = Integer.valueOf(esh.f().a);
        dzq dzq = esh.c().b;
        if (dzq == null) {
            dzq = dzq.c;
        }
        ? r3 = this.a;
        jnu.d(dzq, "getAudioRouteType(...)");
        hme a2 = esh.b().a();
        jnu.d(a2, "getAudioRouteDisconnectStatus(...)");
        r3.n(ehg, valueOf, dzq, a2);
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [elp, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r10v1, types: [java.lang.Object, jqs] */
    public final void n(ehg ehg, hme hme, Integer num, dzq dzq) {
        jnu.e(ehg, "clientInfo");
        this.a.r(ehg, num);
        job.S(this.c, (jlv) null, (jqt) null, new elw(this, ehg, num, dzq, hme, (jlr) null, 2), 3);
    }

    /* JADX WARNING: type inference failed for: r4v0, types: [elp, java.lang.Object] */
    public final void o(hme hme, esh esh) {
        jnu.e(hme, "updateStatus");
        hme a2 = esh.b().a();
        grh g = grh.g(Integer.valueOf(esh.f().a));
        dzq dzq = esh.c().b;
        if (dzq == null) {
            dzq = dzq.c;
        }
        ((eug) this.b).c(hme, a2, g, dzq);
        ? r4 = this.a;
        ehg ehg = esh.f().b.b;
        if (ehg == null) {
            ehg = ehg.c;
        }
        ehg ehg2 = ehg;
        jnu.d(ehg2, "getClientInfo(...)");
        hme a3 = esh.b().a();
        jnu.d(a3, "getAudioRouteDisconnectStatus(...)");
        Integer valueOf = Integer.valueOf(esh.f().a);
        dzq dzq2 = esh.c().b;
        if (dzq2 == null) {
            dzq2 = dzq.c;
        }
        dzq dzq3 = dzq2;
        jnu.d(dzq3, "getAudioRouteType(...)");
        r4.p(ehg2, hme, a3, valueOf, dzq3);
        ((emd) this.d).b(hme, esh.d());
    }

    public final void p(eel eel, dyu dyu) {
        Object obj;
        boolean z;
        htk l = eaw.h.l();
        jnu.d(l, "newBuilder(...)");
        byw C = jnu.e(l, "builder");
        htk l2 = ear.e.l();
        jnu.d(l2, "newBuilder(...)");
        jnu.e(l2, "builder");
        jnu.e(eel, "value");
        if (!l2.b.B()) {
            l2.u();
        }
        ear ear = (ear) l2.b;
        ear.b = eel.n;
        ear.a |= 1;
        jnu.e(dyu, "value");
        if (!l2.b.B()) {
            l2.u();
        }
        ear ear2 = (ear) l2.b;
        dyu.getClass();
        ear2.c = dyu;
        ear2.a |= 2;
        if ((dyu.a & 1) != 0) {
            String format = hji.a(fbi.u(dyu.b)).atZone(ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("MM-dd HH:mm:ss.SSS"));
            jnu.e(format, "value");
            if (!l2.b.B()) {
                l2.u();
            }
            ear ear3 = (ear) l2.b;
            format.getClass();
            ear3.a |= 4;
            ear3.d = format;
        }
        htq o = l2.r();
        jnu.d(o, "build(...)");
        ear ear4 = (ear) o;
        jnu.e(ear4, "value");
        htk htk = (htk) C.a;
        if (!htk.b.B()) {
            htk.u();
        }
        Object obj2 = this.b;
        eaw eaw = (eaw) htk.b;
        ear4.getClass();
        eaw.c = ear4;
        eaw.b = 4;
        ((eug) obj2).e(C.i());
        Object obj3 = this.d;
        gnk gnk = evk.a;
        dyu.h(gnk);
        if (dyu.r.n((htp) gnk.c)) {
            gnk gnk2 = evk.a;
            dyu.h(gnk2);
            Object k = dyu.r.k((htp) gnk2.c);
            if (k == null) {
                obj = gnk2.d;
            } else {
                obj = gnk2.n(k);
            }
            evi evi = (evi) obj;
            double millis = (double) Duration.ofNanos(evi.c).toMillis();
            emd emd = (emd) obj3;
            emi emi = (emi) emd.b.b();
            String name = eel.name();
            evj b2 = evj.b(evi.b);
            if (b2 == null) {
                b2 = evj.REASON_UNKNOWN;
            }
            String name2 = b2.name();
            if (1 != (dyu.a & 1)) {
                z = false;
            } else {
                z = true;
            }
            ((fqv) emi.j.a()).b(millis, name, name2, Boolean.valueOf(z), (String) emd.d.a());
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:16:0x0074  */
    /* JADX WARNING: Removed duplicated region for block: B:19:0x0079  */
    /* JADX WARNING: Removed duplicated region for block: B:20:0x007e  */
    /* JADX WARNING: Removed duplicated region for block: B:23:0x008b  */
    /* JADX WARNING: Removed duplicated region for block: B:26:0x009e A[SYNTHETIC, Splitter:B:26:0x009e] */
    /* JADX WARNING: Removed duplicated region for block: B:34:0x00ba  */
    /* JADX WARNING: Removed duplicated region for block: B:37:0x00bf  */
    /* JADX WARNING: Removed duplicated region for block: B:40:0x00cf  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void q(java.lang.Integer r7, defpackage.efr r8, defpackage.eas r9) {
        /*
            r6 = this;
            eaw r0 = defpackage.eaw.h
            htk r0 = r0.l()
            java.lang.String r1 = "newBuilder(...)"
            defpackage.jnu.d(r0, r1)
            byw r0 = defpackage.jnu.e(r0, "builder")
            r7.intValue()
            java.lang.Object r2 = r0.a
            int r7 = r7.intValue()
            htk r2 = (defpackage.htk) r2
            htq r3 = r2.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x0025
            r2.u()
        L_0x0025:
            htq r2 = r2.b
            eaw r2 = (defpackage.eaw) r2
            int r3 = r2.a
            r3 = r3 | 8
            r2.a = r3
            r2.g = r7
            android.bluetooth.BluetoothDevice r7 = r8.b
            if (r7 == 0) goto L_0x00e6
            eat r7 = defpackage.eat.f
            htk r7 = r7.l()
            defpackage.jnu.d(r7, r1)
            byw r7 = defpackage.jnu.e(r7, "builder")
            r7.w(r9)
            android.bluetooth.BluetoothDevice r9 = r8.b
            java.lang.String r9 = defpackage.efs.d(r9)
            r7.v(r9)
            android.bluetooth.BluetoothDevice r9 = r8.b
            r1 = 0
            if (r9 == 0) goto L_0x0068
            android.bluetooth.BluetoothClass r9 = r9.getBluetoothClass()     // Catch:{ all -> 0x0062 }
            if (r9 == 0) goto L_0x0068
            int r9 = r9.getDeviceClass()     // Catch:{ all -> 0x0062 }
            java.lang.Integer r9 = java.lang.Integer.valueOf(r9)     // Catch:{ all -> 0x0062 }
            goto L_0x0069
        L_0x0062:
            r9 = move-exception
            java.lang.Object r9 = defpackage.jji.b(r9)
            goto L_0x0069
        L_0x0068:
            r9 = r1
        L_0x0069:
            boolean r2 = r9 instanceof defpackage.jjt
            r3 = -16777216(0xffffffffff000000, float:-1.7014118E38)
            java.lang.Integer r4 = java.lang.Integer.valueOf(r3)
            r5 = 1
            if (r5 != r2) goto L_0x0075
            r9 = r4
        L_0x0075:
            java.lang.Integer r9 = (java.lang.Integer) r9
            if (r9 == 0) goto L_0x007e
            int r9 = r9.intValue()
            goto L_0x007f
        L_0x007e:
            r9 = r3
        L_0x007f:
            java.lang.Object r2 = r7.a
            htk r2 = (defpackage.htk) r2
            htq r4 = r2.b
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x008e
            r2.u()
        L_0x008e:
            htq r2 = r2.b
            eat r2 = (defpackage.eat) r2
            int r4 = r2.a
            r4 = r4 | 4
            r2.a = r4
            r2.d = r9
            android.bluetooth.BluetoothDevice r8 = r8.b
            if (r8 == 0) goto L_0x00b2
            android.bluetooth.BluetoothClass r8 = r8.getBluetoothClass()     // Catch:{ all -> 0x00ad }
            if (r8 == 0) goto L_0x00b2
            int r8 = r8.getMajorDeviceClass()     // Catch:{ all -> 0x00ad }
            java.lang.Integer r1 = java.lang.Integer.valueOf(r8)     // Catch:{ all -> 0x00ad }
            goto L_0x00b2
        L_0x00ad:
            r8 = move-exception
            java.lang.Object r1 = defpackage.jji.b(r8)
        L_0x00b2:
            java.lang.Integer r8 = java.lang.Integer.valueOf(r3)
            boolean r9 = r1 instanceof defpackage.jjt
            if (r5 != r9) goto L_0x00bb
            r1 = r8
        L_0x00bb:
            java.lang.Integer r1 = (java.lang.Integer) r1
            if (r1 == 0) goto L_0x00c3
            int r3 = r1.intValue()
        L_0x00c3:
            java.lang.Object r8 = r7.a
            htk r8 = (defpackage.htk) r8
            htq r9 = r8.b
            boolean r9 = r9.B()
            if (r9 != 0) goto L_0x00d2
            r8.u()
        L_0x00d2:
            htq r8 = r8.b
            eat r8 = (defpackage.eat) r8
            int r9 = r8.a
            r9 = r9 | 8
            r8.a = r9
            r8.e = r3
            eat r7 = r7.u()
            r0.k(r7)
            goto L_0x0106
        L_0x00e6:
            eat r7 = defpackage.eat.f
            htk r7 = r7.l()
            defpackage.jnu.d(r7, r1)
            byw r7 = defpackage.jnu.e(r7, "builder")
            r7.w(r9)
            android.media.AudioDeviceInfo r8 = r8.a
            java.lang.String r8 = defpackage.efs.e(r8)
            r7.v(r8)
            eat r7 = r7.u()
            r0.k(r7)
        L_0x0106:
            java.lang.Object r7 = r6.b
            eaw r8 = r0.i()
            eug r7 = (defpackage.eug) r7
            r7.e(r8)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cyw.q(java.lang.Integer, efr, eas):void");
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [elp, java.lang.Object] */
    public final void r(ehg ehg) {
        jnu.e(ehg, "clientInfo");
        this.a.o(ehg, (Integer) null);
    }

    /* JADX WARNING: type inference failed for: r2v3, types: [elp, java.lang.Object] */
    public final void s(ehg ehg, hme hme, eso eso) {
        jnu.e(ehg, "clientInfo");
        jnu.e(hme, "updateStatus");
        hme a2 = eso.c().a();
        gqd gqd = gqd.a;
        dzq dzq = eso.b().b;
        if (dzq == null) {
            dzq = dzq.c;
        }
        ((eug) this.b).c(hme, a2, gqd, dzq);
        ((emd) this.d).b(hme, eso.d());
        ? r2 = this.a;
        hme a3 = eso.c().a();
        jnu.d(a3, "getAudioRouteDisconnectStatus(...)");
        dzq dzq2 = eso.b().b;
        if (dzq2 == null) {
            dzq2 = dzq.c;
        }
        dzq dzq3 = dzq2;
        jnu.d(dzq3, "getAudioRouteType(...)");
        r2.p(ehg, hme, a3, (Integer) null, dzq3);
    }

    public final void t(eay eay, eon eon, eam eam) {
        htk l = eav.n.l();
        jnu.d(l, "newBuilder(...)");
        byw D = jnu.e(l, "builder");
        D.o(eay);
        eoo eoo = (eoo) eon;
        D.s((long) eoo.c);
        D.t(eam);
        ehg ehg = eoo.b.f;
        if (ehg == null) {
            ehg = ehg.c;
        }
        Object obj = this.b;
        D.p(ehg);
        ((eug) obj).d(D.l());
    }

    public final void u(hme hme, ejn ejn) {
        jnu.e(hme, "updateStatus");
        ((emd) this.d).a(hme, ejn);
    }

    /* JADX WARNING: type inference failed for: r9v1, types: [java.lang.Object, jqs] */
    public final void v(String str, ehg ehg, hme hme, hme hme2) {
        job.S(this.c, (jlv) null, (jqt) null, new elz(this, str, ehg, hme, hme2, (jlr) null), 3);
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [elp, java.lang.Object] */
    public final void w(elq elq, eaj eaj) {
        eak eak;
        if (eaj != null) {
            htk l = eak.c.l();
            jnu.d(l, "newBuilder(...)");
            dlv e = jnu.e(l, "builder");
            e.f(eaj);
            eak = e.e();
        } else {
            htk l2 = eak.c.l();
            jnu.d(l2, "newBuilder(...)");
            dlv e2 = jnu.e(l2, "builder");
            e2.g(eal.OPENED);
            eak = e2.e();
        }
        this.a.u(elq, eak);
    }

    public final synchronized dyx x(int i, int i2, eam eam) {
        return ((eoz) this.d).a(((eji) this.a).b(Integer.valueOf(i)).c(), i2, eam);
    }

    public final synchronized ebk y(int i, eam eam) {
        return ((eoz) this.d).b(i, eam);
    }

    public final synchronized ebv z(dzk dzk) {
        return new ejy(((eoz) this.d).c(dzk.a).b());
    }

    public cyw(Uri uri, Uri uri2, Uri uri3, Uri uri4) {
        this.a = uri;
        this.b = uri2;
        this.c = uri3;
        this.d = uri4;
    }

    public cyw(cvy cvy, cvz cvz, cyk cyk, Executor executor) {
        this.a = cvy;
        this.b = cvz;
        this.c = cyk;
        this.d = executor;
    }

    public cyw(eji eji, eoz eoz, eoz eoz2, erm erm) {
        this.a = eji;
        this.c = eoz;
        this.d = eoz2;
        this.b = erm;
    }

    public cyw(emt emt, epv epv, dku dku, Context context) {
        jnu.e(epv, "micOccupationStateProvider");
        this.d = emt;
        this.c = epv;
        this.b = dku;
        this.a = context;
    }

    public cyw(eug eug, emd emd, elp elp, cqx cqx, fbi fbi, jqs jqs) {
        jnu.e(eug, "audioEventsHolder");
        jnu.e(elp, "appFlowLogger");
        jnu.e(cqx, "clock");
        jnu.e(fbi, "audioTimeConverter");
        jnu.e(jqs, "lightweightScope");
        this.b = eug;
        this.d = emd;
        this.a = elp;
        this.c = jqs;
    }

    public cyw(Executor executor) {
        this.b = new Handler(Looper.getMainLooper());
        this.c = new bit(this);
        bih bih = new bih(executor, 0);
        this.a = bih;
        this.d = jnu.I(bih);
    }

    public cyw(Executor executor, czd czd) {
        this.b = new bzj();
        this.d = new HashMap();
        this.c = executor;
        this.a = czd;
    }

    public cyw(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, byte[] bArr) {
        jjk.getClass();
        this.d = jjk;
        jjk2.getClass();
        this.a = jjk2;
        jjk3.getClass();
        this.b = jjk3;
        jjk4.getClass();
        this.c = jjk4;
    }

    public cyw(dfl dfl, bmu bmu) {
        this.c = new ReentrantLock();
        this.b = dfl;
        this.a = bmu;
        this.d = "manifests";
    }

    public cyw(grh grh, grh grh2, grh grh3) {
        this.d = new AtomicBoolean(false);
        this.a = grh;
        this.b = grh2;
        this.c = grh3;
    }

    public cyw(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4) {
        jjk.getClass();
        this.b = jjk;
        jjk2.getClass();
        this.d = jjk2;
        jjk3.getClass();
        this.c = jjk3;
        jjk4.getClass();
        this.a = jjk4;
    }
}
