package defpackage;

import android.os.Handler;
import android.os.Looper;

/* renamed from: coc  reason: default package */
/* compiled from: PG */
public final class coc implements iiu {
    private final jjk a;

    public coc(jjk jjk) {
        this.a = jjk;
    }

    /* renamed from: a */
    public final Handler b() {
        Handler handler;
        int ordinal = ((coz) ((grh) ((iiv) this.a).a).d(coz.NON_ASYNC_HANDLER)).ordinal();
        if (ordinal == 0) {
            handler = new Handler(Looper.getMainLooper());
        } else if (ordinal == 1) {
            handler = a.ah(Looper.getMainLooper());
        } else {
            throw new AssertionError("No matching UiThreadHandlerMode found.");
        }
        hzz.u(handler);
        return handler;
    }
}
