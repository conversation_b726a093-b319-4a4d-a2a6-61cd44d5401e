<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    tools:context=".MainActivity">

    <!-- 标题 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="Google 离线语音识别"
        android:textSize="24sp"
        android:textStyle="bold"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- 状态显示 -->
    <TextView
        android:id="@+id/statusTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="准备就绪"
        android:textSize="16sp"
        android:gravity="center"
        android:background="#E3F2FD"
        android:padding="8dp"
        android:layout_marginBottom="16dp" />

    <!-- 异步API测试按钮 -->
    <Button
        android:id="@+id/asyncTestButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="异步API测试"
        android:layout_marginBottom="16dp"
        android:background="#9C27B0"
        android:textColor="@android:color/white"
        android:textSize="18sp"
        android:padding="16dp" />

    <!-- 麦克风语音识别按钮 -->
    <Button
        android:id="@+id/micRecognitionButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="开始麦克风识别"
        android:layout_marginBottom="16dp"
        android:background="#4CAF50"
        android:textColor="@android:color/white"
        android:textSize="18sp"
        android:padding="16dp" />

    <!-- 识别结果区域 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="识别结果："
        android:textSize="18sp"
        android:textStyle="bold"
        android:layout_marginBottom="8dp" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="#F5F5F5"
        android:padding="8dp">

        <TextView
            android:id="@+id/debugTextView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="等待识别结果..."
            android:textSize="14sp"
            android:lineSpacingExtra="4dp" />

    </ScrollView>

</LinearLayout>