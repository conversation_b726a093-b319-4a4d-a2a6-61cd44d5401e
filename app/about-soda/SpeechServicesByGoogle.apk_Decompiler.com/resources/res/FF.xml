<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:persistent="false" android:key="voiceime_with_downloaded_languages_screen">
    <SwitchPreference android:persistent="false" android:enabled="false" android:title="@string/transcription_prefTitle_profanityFilter" android:key="profanity_filter" android:summary="@string/transcription_prefSummary_profanityFilter" android:defaultValue="true" app:iconSpaceReserved="false"/>
    <PreferenceCategory android:persistent="false" app:iconSpaceReserved="false" app:key="language_category" app:title="@string/transcription_categoryTitle_voice_languages">
        <MultiSelectListPreference android:persistent="false" android:enabled="false" android:entries="@array/transcription_emptyArray" android:title="@string/transcription_prefTitle_voice_languages" android:key="voice_languages" android:defaultValue="@array/transcription_emptyArray" android:entryValues="@array/transcription_emptyArray" app:iconSpaceReserved="false"/>
        <ListPreference android:persistent="false" android:enabled="false" android:entries="@array/transcription_emptyArray" android:title="@string/transcription_prefTitle_language" android:key="primary_language" android:defaultValue="" android:entryValues="@array/transcription_emptyArray" app:iconSpaceReserved="false"/>
    </PreferenceCategory>
    <PreferenceCategory android:persistent="false" android:title="@string/offline_speech_category_title" android:key="@string/offline_speech_category_key">
        <Preference android:icon="@drawable/quantum_gm_ic_add_googblue_24" android:persistent="false" android:title="@string/add_language_title" android:key="@string/add_language_key" android:summary="@string/add_language_details"/>
        <com.google.android.apps.speech.tts.googletts.settings.asr.AutoUpdateDialogPreference android:icon="@drawable/quantum_gm_ic_sync_googblue_24" android:persistent="false" android:entries="@array/pref_download_policy_entries" android:title="@string/auto_update_title" android:key="@string/auto_update_key" android:entryValues="@array/pref_download_policy_entry_values_placeholder"/>
    </PreferenceCategory>
    <PreferenceCategory android:persistent="false" android:title="@string/downloaded_languages_category_title" android:key="@string/downloaded_languages_category"/>
</PreferenceScreen>
