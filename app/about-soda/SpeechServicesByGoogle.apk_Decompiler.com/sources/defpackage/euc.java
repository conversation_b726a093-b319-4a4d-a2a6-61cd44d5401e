package defpackage;

/* renamed from: euc  reason: default package */
/* compiled from: PG */
final class euc extends jme {
    /* synthetic */ Object a;
    final /* synthetic */ eud b;
    int c;
    eud d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public euc(eud eud, jlr jlr) {
        super(jlr);
        this.b = eud;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.c |= Integer.MIN_VALUE;
        return this.b.a(this);
    }
}
