package defpackage;

/* renamed from: bbe  reason: default package */
/* compiled from: PG */
public final class bbe extends jmi implements jne {
    int a;
    final /* synthetic */ Object b;
    final /* synthetic */ Object c;
    private /* synthetic */ Object d;
    private final /* synthetic */ int e;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bbe(aus aus, jlr jlr, jna jna, int i) {
        super(2, jlr);
        this.e = i;
        this.c = aus;
        this.b = jna;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        if (this.e != 0) {
            return ((bbe) c((avp) obj, (jlr) obj2)).bk(jkd.a);
        }
        return ((bbe) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX WARNING: type inference failed for: r2v1, types: [jne, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v12, types: [java.lang.Object, jna] */
    /* JADX WARNING: type inference failed for: r5v13, types: [java.lang.Object, jna] */
    /* JADX WARNING: Code restructure failed: missing block: B:12:0x003f, code lost:
        if (r5.a(r4) == r0) goto L_0x0056;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r5) {
        /*
            r4 = this;
            int r0 = r4.e
            r1 = 1
            if (r0 == 0) goto L_0x006d
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r4.a
            if (r2 == 0) goto L_0x0058
            r3 = 2
            if (r2 == r1) goto L_0x0020
            if (r2 == r3) goto L_0x0018
            java.lang.Object r0 = r4.d
            avp r0 = (defpackage.avp) r0
            defpackage.jji.c(r5)
            goto L_0x006c
        L_0x0018:
            java.lang.Object r1 = r4.d
            avp r1 = (defpackage.avp) r1
            defpackage.jji.c(r5)
            goto L_0x0042
        L_0x0020:
            java.lang.Object r1 = r4.d
            avp r1 = (defpackage.avp) r1
            defpackage.jji.c(r5)
            java.lang.Boolean r5 = (java.lang.Boolean) r5
            boolean r5 = r5.booleanValue()
            if (r5 != 0) goto L_0x0042
            java.lang.Object r5 = r4.c
            aus r5 = (defpackage.aus) r5
            auk r5 = r5.b()
            r4.d = r1
            r4.a = r3
            java.lang.Object r5 = r5.a(r4)
            if (r5 != r0) goto L_0x0042
            goto L_0x0056
        L_0x0042:
            java.lang.Object r5 = r4.b
            avi r2 = new avi
            r3 = 0
            r2.<init>(r3, r5)
            r4.d = r1
            r5 = 3
            r4.a = r5
            r5 = 0
            java.lang.Object r5 = r1.c(r5, r2, r4)
            if (r5 != r0) goto L_0x006c
        L_0x0056:
            r5 = r0
            goto L_0x006c
        L_0x0058:
            defpackage.jji.c(r5)
            java.lang.Object r5 = r4.d
            avp r5 = (defpackage.avp) r5
            java.lang.String r0 = "null cannot be cast to non-null type androidx.room.coroutines.RawConnectionAccessor"
            defpackage.jnu.c(r5, r0)
            byw r5 = r5.a
            java.lang.Object r0 = r4.b
            java.lang.Object r5 = r0.a(r5)
        L_0x006c:
            return r5
        L_0x006d:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r4.a
            if (r2 == 0) goto L_0x0079
            defpackage.jji.c(r5)     // Catch:{ CancellationException -> 0x009b, all -> 0x0077 }
            goto L_0x008b
        L_0x0077:
            r5 = move-exception
            goto L_0x0093
        L_0x0079:
            defpackage.jji.c(r5)
            java.lang.Object r5 = r4.d
            jqs r5 = (defpackage.jqs) r5
            java.lang.Object r2 = r4.b     // Catch:{ CancellationException -> 0x009b, all -> 0x0077 }
            r4.a = r1     // Catch:{ CancellationException -> 0x009b, all -> 0x0077 }
            java.lang.Object r5 = r2.b(r5, r4)     // Catch:{ CancellationException -> 0x009b, all -> 0x0077 }
            if (r5 != r0) goto L_0x008b
            goto L_0x00b2
        L_0x008b:
            java.lang.Object r0 = r4.c     // Catch:{ CancellationException -> 0x009b, all -> 0x0077 }
            po r0 = (defpackage.po) r0     // Catch:{ CancellationException -> 0x009b, all -> 0x0077 }
            r0.c(r5)     // Catch:{ CancellationException -> 0x009b, all -> 0x0077 }
            goto L_0x00b0
        L_0x0093:
            java.lang.Object r0 = r4.c
            po r0 = (defpackage.po) r0
            r0.d(r5)
            goto L_0x00b0
        L_0x009b:
            java.lang.Object r5 = r4.c
            po r5 = (defpackage.po) r5
            r5.d = r1
            ps r0 = r5.b
            if (r0 == 0) goto L_0x00b0
            pn r0 = r0.b
            boolean r0 = r0.cancel(r1)
            if (r0 == 0) goto L_0x00b0
            r5.b()
        L_0x00b0:
            jkd r0 = defpackage.jkd.a
        L_0x00b2:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bbe.bk(java.lang.Object):java.lang.Object");
    }

    /* JADX WARNING: type inference failed for: r1v0, types: [jne, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v1, types: [java.lang.Object, jna] */
    public final jlr c(Object obj, jlr jlr) {
        if (this.e != 0) {
            bbe bbe = new bbe((aus) this.c, jlr, (jna) this.b, 1);
            bbe.d = obj;
            return bbe;
        }
        bbe bbe2 = new bbe((jne) this.b, (po) this.c, jlr, 0);
        bbe2.d = obj;
        return bbe2;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bbe(jne jne, po poVar, jlr jlr, int i) {
        super(2, jlr);
        this.e = i;
        this.b = jne;
        this.c = poVar;
    }
}
