<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:layout_width="match_parent" android:layout_height="match_parent" android:layout_marginTop="48dp" android:layout_marginBottom="48dp" android:overScrollMode="ifContentScrolls">
    <LinearLayout android:orientation="vertical" android:layout_width="match_parent" android:layout_height="wrap_content">
        <TextView android:textColor="?android:attr/textColorSecondary" android:id="@android:id/message" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginLeft="24dp" android:layout_marginRight="24dp" android:layout_marginStart="24dp" android:layout_marginEnd="24dp" style="?android:attr/textAppearanceSmall"/>
        <EditText android:id="@android:id/edit" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginLeft="20dp" android:layout_marginRight="20dp" android:minHeight="48dp" android:layout_marginStart="20dp" android:layout_marginEnd="20dp"/>
    </LinearLayout>
</ScrollView>
