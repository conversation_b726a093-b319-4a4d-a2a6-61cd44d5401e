package defpackage;

import j$.time.Duration;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/* renamed from: emv  reason: default package */
/* compiled from: PG */
public final class emv implements dyy {
    private static final hca c = hca.m("com/google/android/libraries/search/audio/microphone/impl/AudioRequestListeningSessionImpl");
    private static final Duration d = Duration.ofMinutes(10);
    public final hme a;
    public final hme b;
    private final int e;
    private final int f;
    private final dyt g;
    private final Future h;
    private final cyw i;

    public emv(int i2, int i3, dyt dyt, hme hme, hme hme2, ehg ehg, cyw cyw, emd emd, hmi hmi) {
        long j;
        this.f = i2;
        this.e = i3;
        this.g = dyt;
        this.a = hme;
        this.i = cyw;
        this.b = hme2;
        ww wwVar = new ww((Object) this, (Object) hme2, (Object) emd, (Object) ehg, 13);
        if ((dyt.a & 128) != 0) {
            j = dyt.h;
        } else {
            j = d.toMillis();
        }
        this.h = hmi.b(wwVar, j, TimeUnit.MILLISECONDS);
        ((fqy) ((emi) emd.b.b()).d.a()).b(Integer.toString(dyt.d), Integer.toString(dyt.c), Integer.toString(dyt.e), ehf.a(ehg.a).name());
    }

    public final dyx a() {
        return e(eam.CLIENT_REQUESTED);
    }

    public final hme b() {
        return hfc.K(this.g);
    }

    public final hme c() {
        return this.a;
    }

    public final /* synthetic */ Object d() {
        return new elh(this, 2);
    }

    public final dyx e(eam eam) {
        int i2 = this.e;
        ((hby) ((hby) c.f().h(hdg.a, "ALT.ARCSession")).j("com/google/android/libraries/search/audio/microphone/impl/AudioRequestListeningSessionImpl", "stopListening", 93, "AudioRequestListeningSessionImpl.java")).G("#audio# stop(reason(%s)) listening audio request client(token(%d)) session(token(%d))", eam.name(), Integer.valueOf(this.f), Integer.valueOf(i2));
        Future future = this.h;
        dyx x = this.i.x(this.f, this.e, eam);
        future.cancel(false);
        return x;
    }
}
