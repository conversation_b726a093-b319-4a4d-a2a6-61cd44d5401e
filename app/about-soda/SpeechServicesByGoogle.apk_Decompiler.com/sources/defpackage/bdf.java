package defpackage;

/* renamed from: bdf  reason: default package */
/* compiled from: PG */
public final class bdf extends avu {
    public bdf() {
        super(17, 18);
    }

    public final void a(awl awl) {
        awl.g("ALTER TABLE `WorkSpec` ADD COLUMN `next_schedule_time_override` INTEGER NOT NULL DEFAULT 9223372036854775807");
        awl.g("ALTER TABLE `WorkSpec` ADD COLUMN `next_schedule_time_override_generation` INTEGER NOT NULL DEFAULT 0");
    }
}
