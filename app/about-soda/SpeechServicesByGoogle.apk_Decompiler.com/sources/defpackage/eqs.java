package defpackage;

import android.media.AudioManager;
import android.media.AudioRecordingConfiguration;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/* renamed from: eqs  reason: default package */
/* compiled from: PG */
public final class eqs implements epv {
    private static final List a = jji.o(5, 7, 4);
    private final AudioManager b;
    private final eqm c;

    public eqs(AudioManager audioManager, eqm eqm) {
        this.b = audioManager;
        this.c = eqm;
    }

    private static final boolean b(AudioRecordingConfiguration audioRecordingConfiguration) {
        return a.contains(Integer.valueOf(audioRecordingConfiguration.getClientAudioSource()));
    }

    public final boolean a(epu epu) {
        Object obj;
        Object obj2;
        jnu.e(epu, "mode");
        jnu.e(epu, "mode");
        List m = this.b.getActiveRecordingConfigurations();
        jnu.d(m, "getActiveRecordingConfigurations(...)");
        ArrayList arrayList = new ArrayList(jji.K(m));
        Iterator it = m.iterator();
        while (true) {
            Object obj3 = null;
            if (it.hasNext()) {
                AudioRecordingConfiguration m2 = ag$$ExternalSyntheticApiModelOutline1.m(it.next());
                jnu.b(m2);
                int ordinal = epu.ordinal();
                if (ordinal != 0) {
                    if (ordinal != 1) {
                        throw new jjq();
                    } else if (b(m2)) {
                        obj2 = epx.a;
                    } else {
                        obj2 = epx.b;
                    }
                } else if (b(m2)) {
                    obj2 = epx.a;
                } else if (m2.getClientAudioSource() == 1999) {
                    obj2 = epx.b;
                } else {
                    eqf a2 = this.c.a(m2, eqc.PLATFORM, eqc.SELF);
                    if (jnu.i(a2, eqe.a)) {
                        obj2 = new epw((char[]) null);
                    } else if (a2 instanceof eqd) {
                        eqd eqd = (eqd) a2;
                        obj2 = new epw((byte[]) null);
                    } else if (jnu.i(a2, eqe.b)) {
                        obj2 = epx.b;
                    } else {
                        throw new jjq();
                    }
                }
                arrayList.add(obj2);
            } else {
                Iterator it2 = arrayList.iterator();
                while (true) {
                    if (!it2.hasNext()) {
                        obj = null;
                        break;
                    }
                    obj = it2.next();
                    if (jnu.i((epy) obj, epx.a)) {
                        break;
                    }
                }
                Object obj4 = (epy) obj;
                if (obj4 == null) {
                    Iterator it3 = arrayList.iterator();
                    while (true) {
                        if (!it3.hasNext()) {
                            break;
                        }
                        Object next = it3.next();
                        if (((epy) next) instanceof epw) {
                            obj3 = next;
                            break;
                        }
                    }
                    obj4 = (epy) obj3;
                    if (obj4 == null) {
                        if (this.b.getMode() == 2) {
                            obj4 = epx.a;
                        } else {
                            obj4 = epx.b;
                        }
                    }
                }
                if (!jnu.i(obj4, epx.b)) {
                    return true;
                }
                return false;
            }
        }
    }
}
