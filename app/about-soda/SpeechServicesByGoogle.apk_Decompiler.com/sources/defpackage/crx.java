package defpackage;

/* renamed from: crx  reason: default package */
/* compiled from: PG */
public final class crx {
    public final csc a;
    private final grh b;
    private final grh c;

    public crx() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof crx) {
            crx crx = (crx) obj;
            if (!this.a.equals(crx.a) || !this.b.equals(crx.b) || !this.c.equals(crx.c)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        csc csc = this.a;
        if (csc.B()) {
            i = csc.i();
        } else {
            int i2 = csc.memoizedHashCode;
            if (i2 == 0) {
                i2 = csc.i();
                csc.memoizedHashCode = i2;
            }
            i = i2;
        }
        return ((((i ^ 1000003) * 1000003) ^ **********) * 1000003) ^ **********;
    }

    public final String toString() {
        grh grh = this.c;
        grh grh2 = this.b;
        String valueOf = String.valueOf(this.a);
        String valueOf2 = String.valueOf(grh2);
        String valueOf3 = String.valueOf(grh);
        return "AddFileGroupRequest{dataFileGroup=" + valueOf + ", accountOptional=" + valueOf2 + ", variantIdOptional=" + valueOf3 + "}";
    }

    public crx(csc csc, grh grh, grh grh2) {
        this.a = csc;
        this.b = grh;
        this.c = grh2;
    }
}
