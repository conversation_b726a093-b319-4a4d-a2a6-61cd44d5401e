package defpackage;

import android.content.Context;
import android.os.RemoteException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/* renamed from: bpr  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bpr implements hko {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ bpr(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r2v15, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v23, types: [java.lang.Object, bpm] */
    /* JADX WARNING: type inference failed for: r3v30, types: [cvz, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v94, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v42, types: [cvz, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v18, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v99, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v20, types: [java.lang.Object, cxa] */
    /* JADX WARNING: type inference failed for: r0v102, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v23, types: [cvz, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v109, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v28, types: [cvz, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v125, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v50, types: [cvz, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v27, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v130, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme a(Object obj) {
        char c = 1;
        String str = "ExpirationHandler";
        switch (this.b) {
            case 0:
                bpl bpl = (bpl) obj;
                try {
                    return hfc.K(new bpx(((bpy) this.a).b(bpl.a), bpl.b));
                } catch (RemoteException e) {
                    return hfc.J(new bpp(2, 6, "Failed to initialize service.", e));
                } catch (RuntimeException e2) {
                    return hfc.J(new bpp(2, 0, "Failed to initialize service.", e2));
                }
            case 1:
                Void voidR = (Void) obj;
                return ((bpy) this.a).b.a();
            case 2:
                bpx bpx = (bpx) obj;
                return bpx.b.d(kq.f(new bpv(this.a, (Object) bpx, 0)), new bps(2));
            case 3:
                dbl dbl = (dbl) obj;
                btf btf = (btf) this.a;
                fvf.aP(btf.e);
                ((hby) ((hby) btf.a.f()).j("com/google/android/apps/speech/tts/googletts/local/voicepack/VoiceDataDownloader", "init", 255, "VoiceDataDownloader.java")).r("Superpacks created and default voices checked.");
                dbw dbw = btf.e;
                dbw.e();
                return hfc.K(dbw.a("ttsvoices", btf.j, "getSuperpackManifest"));
            case 4:
                dbl dbl2 = (dbl) obj;
                btf btf2 = (btf) this.a;
                fvf.aP(btf2.e);
                ((hby) ((hby) btf.a.f()).j("com/google/android/apps/speech/tts/googletts/local/voicepack/VoiceDataDownloader", "fetchVoiceInternal", 299, "VoiceDataDownloader.java")).u("Syncing superpack %s", dbl2.c());
                dbw dbw2 = btf2.e;
                ddt c2 = ddt.c(dbl2.c(), dbl2.a());
                ddb ddb = ddb.a;
                return hke.g(dbw2.b(c2, btf.a(dbl2)), new cwq(dbw2, c2, 14, (byte[]) null), dbw2.g);
            case 5:
                dbl dbl3 = (dbl) obj;
                brt brt = new brt(brv.h(Locale.US));
                buc buc = (buc) this.a;
                bty a2 = buc.e.a(brt);
                fvf.aP(a2);
                return buc.c.k(a2);
            case 6:
                gxv gxv = (gxv) obj;
                return ((byr) this.a).d.a();
            case 7:
                Object obj2 = this.a;
                byr byr = (byr) obj2;
                byr.g.J(hma.a, "LanguagePackSettings:installedPacks");
                byr.g.J(hma.a, "LanguagePackSettings:supportedPacks");
                return (hme) ((grh) ((cxj) obj).a).d(hfc.K(exl.a));
            case 8:
                gxv gxv2 = (gxv) obj;
                return ((byr) this.a).d.a();
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                cna cna = (cna) this.a;
                return cna.a(cna.b.get(), (gxq) obj);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                Void voidR2 = (Void) obj;
                return ((cuf) this.a).n();
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                Void voidR3 = (Void) obj;
                cyh.c("%s verifyAllPendingGroups", "MDDManager");
                cuf cuf = (cuf) this.a;
                cwm cwm = cuf.c;
                return ftd.L(cwm.f(), new cwa((Object) cwm, (Object) cuf.g, 17), cwm.h);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                grh grh = (grh) obj;
                if (grh.f()) {
                    cyh.k("%s: CancelForegroundDownload future found for key = %s, cancelling...", "MobileDataDownload", this.a);
                    ((hme) grh.b()).cancel(false);
                }
                return hma.a;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                boolean booleanValue = ((Boolean) obj).booleanValue();
                Object obj3 = this.a;
                if (booleanValue) {
                    cuc cuc = (cuc) obj3;
                    if (cuc.b.j == 2) {
                        sk skVar = cuc.c;
                        skVar.n = "status";
                        skVar.e(cuc.f);
                        skVar.i(17301633);
                        skVar.g(true);
                        skVar.h(0, 0, false);
                        cuc.d.a(cuc.e, cuc.c.a());
                    }
                }
                cuc cuc2 = (cuc) obj3;
                if (cuc2.b.g.f()) {
                    ((csl) cuc2.b.g.b()).d();
                }
                return hma.a;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                ArrayList arrayList = new ArrayList();
                Iterator it = ((List) obj).iterator();
                while (true) {
                    Object obj4 = this.a;
                    if (it.hasNext()) {
                        cxg cxg = (cxg) it.next();
                        ctg ctg = cxg.a;
                        csx csx = cxg.b;
                        long g = cqx.g(csx);
                        Object valueOf = Long.valueOf(g);
                        Object obj5 = csx.c;
                        Object[] objArr = new Object[3];
                        objArr[0] = str;
                        objArr[c] = obj5;
                        objArr[2] = valueOf;
                        cyh.e("%s: Checking group %s with expiration date %s", objArr);
                        valueOf.getClass();
                        if (cqx.q(g)) {
                            dmd dmd = (dmd) obj4;
                            Object obj6 = dmd.a;
                            String str2 = str;
                            ((cyk) obj6).e(1051, csx.c, csx.e, csx.r, csx.s);
                            cyh.e("%s: Expired group %s with expiration date %s", str2, csx.c, valueOf);
                            arrayList.add(ctg);
                            if (cqx.o(csx)) {
                                Object obj7 = dmd.h;
                                Context context = (Context) obj7;
                                cqx.K(context, (grh) dmd.d, csx, (kjd) dmd.f);
                            }
                            str = str2;
                            c = 1;
                        }
                    } else {
                        dmd dmd2 = (dmd) obj4;
                        return ftd.K(dmd2.e.j(arrayList), new brg(obj4, 11), dmd2.c);
                    }
                }
            case 15:
                Void voidR4 = (Void) obj;
                Object obj8 = this.a;
                dmd dmd3 = (dmd) obj8;
                return ftd.L(ftd.L(dmd3.e.c(), new bpr(obj8, 14), dmd3.c), new bpr(obj8, 20), dmd3.c);
            case 16:
                Object obj9 = this.a;
                dmd dmd4 = (dmd) obj9;
                return ftd.L(dmd4.g.c(), new bpt(obj9, (Set) obj, 11), dmd4.c);
            case 17:
                ArrayList arrayList2 = new ArrayList();
                Iterator it2 = ((List) obj).iterator();
                while (true) {
                    Object obj10 = this.a;
                    if (it2.hasNext()) {
                        csx csx2 = (csx) it2.next();
                        TimeUnit timeUnit = TimeUnit.SECONDS;
                        csw csw = csx2.b;
                        if (csw == null) {
                            csw = csw.i;
                        }
                        if (cqx.q(Math.min(timeUnit.toMillis(csw.b), cqx.g(csx2)))) {
                            dmd dmd5 = (dmd) obj10;
                            ((cyk) dmd5.a).e(1052, csx2.c, csx2.e, csx2.r, csx2.s);
                            if (cqx.o(csx2)) {
                                Object obj11 = dmd5.h;
                                Context context2 = (Context) obj11;
                                cqx.K(context2, (grh) dmd5.d, csx2, (kjd) dmd5.f);
                            }
                        } else {
                            arrayList2.add(csx2);
                        }
                    } else {
                        dmd dmd6 = (dmd) obj10;
                        return ftd.L(dmd6.e.k(), new bpt(obj10, arrayList2, 12), dmd6.c);
                    }
                }
            case 18:
                if (!((Boolean) obj).booleanValue()) {
                    ((cyk) ((dmd) this.a).a).d(1036);
                    cyh.g("%s: Failed to write back stale groups!", str);
                }
                return hma.a;
            case 19:
                HashSet hashSet = new HashSet();
                ArrayList arrayList3 = new ArrayList();
                for (cxg cxg2 : (List) obj) {
                    arrayList3.add(cxg2.b);
                }
                dmd dmd7 = (dmd) this.a;
                return ftd.K(dmd7.e.e(), new cvf(dmd7, arrayList3, hashSet), dmd7.c);
            default:
                Void voidR5 = (Void) obj;
                Object obj12 = this.a;
                dmd dmd8 = (dmd) obj12;
                return ftd.L(ftd.L(dmd8.e.c(), new bpr(obj12, 19), dmd8.c), new bpr(obj12, 16), dmd8.c);
        }
    }
}
