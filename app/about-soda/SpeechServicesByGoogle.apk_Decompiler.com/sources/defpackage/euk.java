package defpackage;

/* renamed from: euk  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class euk implements hkn {
    private final /* synthetic */ int a;

    public /* synthetic */ euk(int i) {
        this.a = i;
    }

    public final hme a() {
        int i = this.a;
        if (i == 0) {
            return hfc.K(jkd.a);
        }
        if (i != 1) {
            return hma.a;
        }
        return hma.a;
    }
}
