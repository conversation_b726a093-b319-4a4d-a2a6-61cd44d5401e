package defpackage;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.IntentFilter;

/* renamed from: cyk  reason: default package */
/* compiled from: PG */
public final class cyk {
    public grh a = gqd.a;
    private final Context b;
    private final String c;
    private final grh d;
    private final cxi e;
    private final bzj f;

    public cyk(Context context, bzj bzj, cxi cxi, grh grh) {
        this.b = context;
        this.f = bzj;
        this.c = context.getPackageName();
        this.e = cxi;
        this.d = grh;
    }

    private final void k(int i, htk htk, long j) {
        htk l = hiw.f.l();
        if (!l.b.B()) {
            l.u();
        }
        hiw hiw = (hiw) l.b;
        hiw.a |= 1;
        hiw.b = false;
        h(i, htk, j, (hiw) l.r());
    }

    public final void a(hij hij) {
        long c2 = (long) ((int) ikg.a.a().c());
        if (cyh.b(c2)) {
            htk l = hii.u.l();
            if (!l.b.B()) {
                l.u();
            }
            hii hii = (hii) l.b;
            hij.getClass();
            hii.p = hij;
            hii.c |= 4;
            k(1075, l, c2);
        }
    }

    public final hme b(int i, hkn hkn, int i2) {
        return ftd.L(this.e.b((long) i2, this.a), new cyj(this, hkn, i, i2, 0), hld.a);
    }

    public final void c(int i, int i2) {
        k(i, hii.u.l(), (long) i2);
    }

    public final void d(int i) {
        i(i, hii.u.l(), (long) cqh.o());
    }

    public final void e(int i, String str, int i2, long j, String str2) {
        htk l = hig.k.l();
        if (!l.b.B()) {
            l.u();
        }
        htq htq = l.b;
        hig hig = (hig) htq;
        str.getClass();
        hig.a |= 1;
        hig.b = str;
        if (!htq.B()) {
            l.u();
        }
        htq htq2 = l.b;
        hig hig2 = (hig) htq2;
        hig2.a |= 2;
        hig2.c = i2;
        if (!htq2.B()) {
            l.u();
        }
        htq htq3 = l.b;
        hig hig3 = (hig) htq3;
        hig3.a |= 64;
        hig3.h = j;
        if (!htq3.B()) {
            l.u();
        }
        hig hig4 = (hig) l.b;
        str2.getClass();
        hig4.a |= 128;
        hig4.i = str2;
        hig hig5 = (hig) l.r();
        htk l2 = hii.u.l();
        if (!l2.b.B()) {
            l2.u();
        }
        hii hii = (hii) l2.b;
        hig5.getClass();
        hii.d = hig5;
        hii.a |= 256;
        i(i, l2, (long) cqh.o());
    }

    public final void f(int i, int i2) {
        htk l = hii.u.l();
        htk l2 = him.d.l();
        if (!l2.b.B()) {
            l2.u();
        }
        htq htq = l2.b;
        him him = (him) htq;
        him.a |= 2;
        him.c = i2;
        if (!htq.B()) {
            l2.u();
        }
        him him2 = (him) l2.b;
        him2.b = i - 2;
        him2.a |= 1;
        if (!l.b.B()) {
            l.u();
        }
        hii hii = (hii) l.b;
        him him3 = (him) l2.r();
        him3.getClass();
        hii.j = him3;
        hii.b |= 32768;
        i(1053, l, (long) cqh.o());
    }

    public final void g(hig hig, int i, long j, long j2, String str, int i2) {
        htk l = hii.u.l();
        htk l2 = hip.h.l();
        if (!l2.b.B()) {
            l2.u();
        }
        htq htq = l2.b;
        hip hip = (hip) htq;
        hig.getClass();
        hip.b = hig;
        hip.a |= 1;
        if (!htq.B()) {
            l2.u();
        }
        htq htq2 = l2.b;
        hip hip2 = (hip) htq2;
        hip2.c = a.E(i);
        hip2.a |= 2;
        if (!htq2.B()) {
            l2.u();
        }
        htq htq3 = l2.b;
        hip hip3 = (hip) htq3;
        hip3.a |= 4;
        hip3.d = j;
        if (!htq3.B()) {
            l2.u();
        }
        htq htq4 = l2.b;
        hip hip4 = (hip) htq4;
        hip4.a |= 8;
        hip4.e = j2;
        if (!htq4.B()) {
            l2.u();
        }
        htq htq5 = l2.b;
        hip hip5 = (hip) htq5;
        str.getClass();
        hip5.a |= 16;
        hip5.f = str;
        if (!htq5.B()) {
            l2.u();
        }
        hip hip6 = (hip) l2.b;
        hip6.a |= 32;
        hip6.g = i2;
        if (!l.b.B()) {
            l.u();
        }
        hii hii = (hii) l.b;
        hip hip7 = (hip) l2.r();
        hip7.getClass();
        hii.m = hip7;
        hii.b |= 4194304;
        i(1068, l, (long) cqh.o());
    }

    public final void h(int i, htk htk, long j, hiw hiw) {
        boolean z;
        htk l = hif.e.l();
        if (!l.b.B()) {
            l.u();
        }
        String str = this.c;
        htq htq = l.b;
        hif hif = (hif) htq;
        str.getClass();
        hif.a |= 2;
        hif.c = str;
        if (!htq.B()) {
            l.u();
        }
        hif hif2 = (hif) l.b;
        hif2.a |= 1;
        hif2.b = 616991814;
        if (this.d.f()) {
            String str2 = (String) this.d.b();
            if (!l.b.B()) {
                l.u();
            }
            hif hif3 = (hif) l.b;
            hif3.a |= 4;
            hif3.d = str2;
        }
        if (!htk.b.B()) {
            htk.u();
        }
        hii hii = (hii) htk.b;
        hii hii2 = hii.u;
        hii.a |= 524288;
        hii.e = j;
        htk l2 = hih.c.l();
        if (this.b.registerReceiver((BroadcastReceiver) null, new IntentFilter("android.intent.action.DEVICE_STORAGE_LOW")) != null) {
            z = true;
        } else {
            z = false;
        }
        if (!l2.b.B()) {
            l2.u();
        }
        hih hih = (hih) l2.b;
        hih.a |= 1;
        hih.b = z;
        if (!htk.b.B()) {
            htk.u();
        }
        hii hii3 = (hii) htk.b;
        hih hih2 = (hih) l2.r();
        hih2.getClass();
        hii3.h = hih2;
        hii3.b |= 128;
        if (!htk.b.B()) {
            htk.u();
        }
        hii hii4 = (hii) htk.b;
        hif hif4 = (hif) l.r();
        hif4.getClass();
        hii4.l = hif4;
        hii4.b |= 524288;
        if (!htk.b.B()) {
            htk.u();
        }
        hii hii5 = (hii) htk.b;
        hiw.getClass();
        hii5.f = hiw;
        hii5.a |= 1048576;
        cab f2 = ((cad) this.f.a).f(htk.r());
        f2.e(i - 2);
        f2.b();
    }

    public final void i(int i, htk htk, long j) {
        ftd.M(this.e.b(j, this.a), new dae(this, i, htk, j, 1), hld.a);
    }

    public final void j(int i, hig hig, int i2) {
        htk l = hii.u.l();
        htk l2 = hil.f.l();
        if (!l2.b.B()) {
            l2.u();
        }
        htq htq = l2.b;
        hil hil = (hil) htq;
        if (i != 1) {
            hil.b = i - 2;
            hil.a |= 1;
            if (!htq.B()) {
                l2.u();
            }
            htq htq2 = l2.b;
            hil hil2 = (hil) htq2;
            hig.getClass();
            hil2.c = hig;
            hil2.a |= 2;
            if (!htq2.B()) {
                l2.u();
            }
            htq htq3 = l2.b;
            hil hil3 = (hil) htq3;
            if (i2 != 1) {
                hil3.d = i2 - 2;
                hil3.a |= 4;
                if (!htq3.B()) {
                    l2.u();
                }
                hil hil4 = (hil) l2.b;
                hil4.a |= 8;
                hil4.e = 0;
                if (!l.b.B()) {
                    l.u();
                }
                hii hii = (hii) l.b;
                hil hil5 = (hil) l2.r();
                hil5.getClass();
                hii.n = hil5;
                hii.b |= Integer.MIN_VALUE;
                i(1070, l, (long) cqh.o());
                return;
            }
            throw new IllegalArgumentException("Can't get the number of an unknown enum value.");
        }
        throw new IllegalArgumentException("Can't get the number of an unknown enum value.");
    }
}
