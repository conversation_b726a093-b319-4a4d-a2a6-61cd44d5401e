package defpackage;

/* renamed from: bps  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bps implements gsb {
    private final /* synthetic */ int a;

    public /* synthetic */ bps(int i) {
        this.a = i;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:14:0x0041, code lost:
        if (r1.importance >= 400) goto L_0x004b;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object a() {
        /*
            r15 = this;
            java.lang.String r0 = "PhenotypeProcessReaper"
            int r1 = r15.a
            r2 = 3
            java.lang.String r3 = "AICore service disconnected"
            r4 = 6
            r5 = 1
            r6 = 0
            java.lang.Boolean r7 = java.lang.Boolean.valueOf(r6)
            r8 = 0
            switch(r1) {
                case 0: goto L_0x00d9;
                case 1: goto L_0x00c5;
                case 2: goto L_0x00bf;
                case 3: goto L_0x00b9;
                case 4: goto L_0x00b8;
                case 5: goto L_0x00b8;
                case 6: goto L_0x00b8;
                case 7: goto L_0x00b1;
                case 8: goto L_0x0096;
                case 9: goto L_0x0091;
                case 10: goto L_0x008b;
                case 11: goto L_0x0085;
                case 12: goto L_0x0080;
                case 13: goto L_0x0065;
                case 14: goto L_0x005f;
                case 15: goto L_0x0050;
                case 16: goto L_0x0022;
                case 17: goto L_0x001d;
                case 18: goto L_0x0018;
                case 19: goto L_0x0015;
                default: goto L_0x0012;
            }
        L_0x0012:
            gqd r0 = defpackage.gqd.a
            return r0
        L_0x0015:
            gqd r0 = defpackage.gqd.a
            return r0
        L_0x0018:
            hme r0 = defpackage.hfc.K(r8)
            return r0
        L_0x001d:
            hme r0 = defpackage.hfc.K(r8)
            return r0
        L_0x0022:
            android.app.ActivityManager$RunningAppProcessInfo r1 = new android.app.ActivityManager$RunningAppProcessInfo
            r1.<init>()
            android.app.ActivityManager.getMyMemoryState(r1)     // Catch:{ RuntimeException -> 0x0044 }
            int r2 = r1.importance
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            java.lang.String r4 = "Memory state is: "
            r3.<init>(r4)
            r3.append(r2)
            java.lang.String r2 = r3.toString()
            android.util.Log.i(r0, r2)
            int r0 = r1.importance
            r1 = 400(0x190, float:5.6E-43)
            if (r0 < r1) goto L_0x004a
            goto L_0x004b
        L_0x0044:
            r1 = move-exception
            java.lang.String r2 = "Failed to retrieve memory state, not killing process."
            android.util.Log.w(r0, r2, r1)
        L_0x004a:
            r5 = r6
        L_0x004b:
            java.lang.Boolean r0 = java.lang.Boolean.valueOf(r5)
            return r0
        L_0x0050:
            cnv r0 = new cnv
            r1 = 2
            r0.<init>(r1)
            java.util.concurrent.ScheduledExecutorService r0 = java.util.concurrent.Executors.newSingleThreadScheduledExecutor(r0)
            hmi r0 = defpackage.hfc.F(r0)
            return r0
        L_0x005f:
            kak r0 = new kak
            r0.<init>()
            return r0
        L_0x0065:
            boolean r0 = android.app.ActivityManager.isUserAMonkey()
            if (r0 == 0) goto L_0x006c
            goto L_0x007b
        L_0x006c:
            int r0 = android.os.Build.VERSION.SDK_INT
            r1 = 29
            if (r0 >= r1) goto L_0x0077
            boolean r5 = android.app.ActivityManager.isRunningInTestHarness()
            goto L_0x007b
        L_0x0077:
            boolean r5 = defpackage.dp$$ExternalSyntheticApiModelOutline0.m()
        L_0x007b:
            java.lang.Boolean r0 = java.lang.Boolean.valueOf(r5)
            return r0
        L_0x0080:
            grh r0 = defpackage.dpm.a()
            return r0
        L_0x0085:
            java.util.zip.CRC32 r0 = new java.util.zip.CRC32
            r0.<init>()
            return r0
        L_0x008b:
            java.util.zip.Adler32 r0 = new java.util.zip.Adler32
            r0.<init>()
            return r0
        L_0x0091:
            java.lang.Boolean r0 = java.lang.Boolean.valueOf(r5)
            return r0
        L_0x0096:
            ikc r0 = defpackage.ikc.a
            ikd r0 = r0.a()
            cst r0 = r0.c()
            int r0 = r0.a
            css r0 = defpackage.css.b(r0)
            if (r0 != 0) goto L_0x00aa
            css r0 = defpackage.css.UNDEFINED
        L_0x00aa:
            css r1 = defpackage.css.UNDEFINED
            if (r0 != r1) goto L_0x00b0
            css r0 = defpackage.css.SHARED_PREFERENCES_ONLY
        L_0x00b0:
            return r0
        L_0x00b1:
            java.lang.String r0 = "com/google/android/libraries/concurrent/monitoring/ThreadMonitoring"
            hca r0 = defpackage.hca.m(r0)
            return r0
        L_0x00b8:
            return r7
        L_0x00b9:
            bpp r0 = new bpp
            r0.<init>(r5, r4, r3, r8)
            return r0
        L_0x00bf:
            bpp r0 = new bpp
            r0.<init>(r2, r4, r3, r8)
            return r0
        L_0x00c5:
            aik r0 = new aik
            aph r10 = new aph
            r10.<init>()
            r13 = 2500(0x9c4, float:3.503E-42)
            r14 = 5000(0x1388, float:7.006E-42)
            r12 = 50000(0xc350, float:7.0065E-41)
            r9 = r0
            r11 = r12
            r9.<init>(r10, r11, r12, r13, r14)
            return r0
        L_0x00d9:
            bpp r0 = new bpp
            r0.<init>(r2, r4, r3, r8)
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bps.a():java.lang.Object");
    }
}
