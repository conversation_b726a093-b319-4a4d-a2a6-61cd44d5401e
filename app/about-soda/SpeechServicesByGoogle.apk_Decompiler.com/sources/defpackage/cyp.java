package defpackage;

import java.util.List;

/* renamed from: cyp  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cyp implements hko {
    public final /* synthetic */ List a;
    public final /* synthetic */ int b;
    public final /* synthetic */ eix c;

    public /* synthetic */ cyp(eix eix, List list, int i) {
        this.c = eix;
        this.a = list;
        this.b = i;
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v23, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r11v12, resolved type: cys} */
    /* JADX WARNING: type inference failed for: r2v3, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r11v6, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r11v8, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r10v20, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme a(java.lang.Object r30) {
        /*
            r29 = this;
            r0 = r29
            r1 = r30
            java.util.List r1 = (java.util.List) r1
            java.util.ArrayList r2 = new java.util.ArrayList
            r2.<init>()
            java.util.List r3 = r0.a
            java.util.Iterator r3 = r3.iterator()
        L_0x0011:
            boolean r4 = r3.hasNext()
            if (r4 == 0) goto L_0x0021
            java.lang.Object r4 = r3.next()
            cxg r4 = (defpackage.cxg) r4
            r2.add(r4)
            goto L_0x0011
        L_0x0021:
            java.util.Iterator r1 = r1.iterator()
        L_0x0025:
            boolean r3 = r1.hasNext()
            r4 = 1
            if (r3 == 0) goto L_0x00a0
            java.lang.Object r3 = r1.next()
            csx r3 = (defpackage.csx) r3
            ctg r5 = defpackage.ctg.g
            htk r5 = r5.l()
            java.lang.String r6 = r3.c
            htq r7 = r5.b
            boolean r7 = r7.B()
            if (r7 != 0) goto L_0x0045
            r5.u()
        L_0x0045:
            htq r7 = r5.b
            ctg r7 = (defpackage.ctg) r7
            r6.getClass()
            int r8 = r7.a
            r4 = r4 | r8
            r7.a = r4
            r7.b = r6
            java.lang.String r4 = r3.d
            boolean r4 = r4.isEmpty()
            if (r4 == 0) goto L_0x0075
            htq r4 = r5.b
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x0066
            r5.u()
        L_0x0066:
            htq r4 = r5.b
            ctg r4 = (defpackage.ctg) r4
            int r6 = r4.a
            r6 = r6 | 2
            r4.a = r6
            java.lang.String r6 = "com.google.android.gms"
            r4.c = r6
            goto L_0x0091
        L_0x0075:
            java.lang.String r4 = r3.d
            htq r6 = r5.b
            boolean r6 = r6.B()
            if (r6 != 0) goto L_0x0082
            r5.u()
        L_0x0082:
            htq r6 = r5.b
            ctg r6 = (defpackage.ctg) r6
            r4.getClass()
            int r7 = r6.a
            r7 = r7 | 2
            r6.a = r7
            r6.c = r4
        L_0x0091:
            htq r4 = r5.r()
            ctg r4 = (defpackage.ctg) r4
            cxg r5 = new cxg
            r5.<init>(r4, r3)
            r2.add(r5)
            goto L_0x0025
        L_0x00a0:
            java.util.HashMap r8 = new java.util.HashMap
            r8.<init>()
            java.util.HashMap r1 = new java.util.HashMap
            r1.<init>()
            java.util.HashMap r3 = new java.util.HashMap
            r3.<init>()
            java.util.HashMap r9 = new java.util.HashMap
            r9.<init>()
            java.util.HashSet r5 = new java.util.HashSet
            r5.<init>()
            java.util.concurrent.atomic.AtomicLong r7 = new java.util.concurrent.atomic.AtomicLong
            r10 = 0
            r7.<init>(r10)
            java.util.ArrayList r6 = new java.util.ArrayList
            r6.<init>()
            int r15 = r2.size()
            r10 = 0
            r14 = r10
        L_0x00cb:
            eix r13 = r0.c
            if (r14 >= r15) goto L_0x01c7
            java.lang.Object r10 = r2.get(r14)
            r12 = r10
            cxg r12 = (defpackage.cxg) r12
            ctg r10 = r12.a
            java.lang.String r10 = defpackage.eix.a(r10)
            java.util.Set r19 = defpackage.eix.b(r1, r10)
            ctg r10 = r12.a
            java.lang.String r10 = defpackage.eix.a(r10)
            java.lang.Object r11 = r8.get(r10)
            cys r11 = (defpackage.cys) r11
            if (r11 != 0) goto L_0x00fd
            cys r11 = new cys
            r11.<init>()
            r8.put(r10, r11)
            java.lang.Object r10 = r8.get(r10)
            r11 = r10
            cys r11 = (defpackage.cys) r11
        L_0x00fd:
            ctg r10 = r12.a
            boolean r4 = r10.e
            if (r4 == 0) goto L_0x0119
            java.lang.String r4 = defpackage.eix.a(r10)
            java.util.Set r4 = defpackage.eix.b(r3, r4)
            ctg r10 = r12.a
            java.lang.String r10 = defpackage.eix.a(r10)
            r20 = r1
            csx r1 = r12.b
            r9.put(r10, r1)
            goto L_0x011c
        L_0x0119:
            r20 = r1
            r4 = 0
        L_0x011c:
            csx r1 = r12.b
            huf r1 = r1.n
            int r1 = r1.size()
            csx r10 = r12.b
            huf r10 = r10.n
            java.util.Iterator r21 = r10.iterator()
        L_0x012c:
            boolean r10 = r21.hasNext()
            if (r10 == 0) goto L_0x01b4
            java.lang.Object r10 = r21.next()
            csv r10 = (defpackage.csv) r10
            boolean r16 = defpackage.cqx.n(r10)
            r22 = r2
            csx r2 = r12.b
            int r2 = r2.i
            int r2 = defpackage.a.x(r2)
            if (r2 != 0) goto L_0x0149
            r2 = 1
        L_0x0149:
            ctj r2 = defpackage.cqh.t(r10, r2)
            java.lang.Object r10 = r13.e
            dbw r10 = (defpackage.dbw) r10
            hme r10 = r10.l(r2)
            czw r10 = defpackage.czw.e(r10)
            r23 = r3
            bub r3 = new bub
            r17 = r11
            r11 = 14
            r3.<init>(r11)
            java.lang.Object r11 = r13.g
            r18 = r12
            java.lang.Class<cwz> r12 = defpackage.cwz.class
            czw r3 = r10.d(r12, r3, r11)
            cyg r10 = new cyg
            r11 = 8
            r10.<init>(r13, r11)
            java.lang.Object r11 = r13.g
            czw r3 = r3.f(r10, r11)
            cyq r12 = new cyq
            r10 = r12
            r24 = r17
            r11 = r5
            r26 = r5
            r5 = r12
            r25 = r18
            r12 = r2
            r2 = r13
            r13 = r7
            r27 = r14
            r14 = r19
            r28 = r15
            r15 = r16
            r16 = r24
            r17 = r25
            r18 = r4
            r10.<init>(r11, r12, r13, r14, r15, r16, r17, r18)
            java.lang.Object r10 = r2.g
            hme r3 = defpackage.ftd.K(r3, r5, r10)
            r6.add(r3)
            r13 = r2
            r2 = r22
            r3 = r23
            r11 = r24
            r12 = r25
            r5 = r26
            r14 = r27
            r15 = r28
            goto L_0x012c
        L_0x01b4:
            r22 = r2
            r23 = r3
            r26 = r5
            r27 = r14
            r28 = r15
            r11.e = r1
            int r14 = r27 + 1
            r1 = r20
            r4 = 1
            goto L_0x00cb
        L_0x01c7:
            r2 = r13
            int r11 = r0.b
            bzj r1 = defpackage.cqh.U(r6)
            cyr r3 = new cyr
            r6 = r3
            r4 = r7
            r7 = r2
            r10 = r4
            r6.<init>(r7, r8, r9, r10, r11)
            java.lang.Object r2 = r2.g
            hme r1 = r1.n(r3, r2)
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cyp.a(java.lang.Object):hme");
    }
}
