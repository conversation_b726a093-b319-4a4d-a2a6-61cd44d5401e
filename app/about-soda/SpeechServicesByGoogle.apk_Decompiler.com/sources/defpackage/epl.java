package defpackage;

/* renamed from: epl  reason: default package */
/* compiled from: PG */
public final class epl {
    public final int a;
    public final ebn b;

    public epl(int i, ebn ebn) {
        jnu.e(ebn, "params");
        this.a = i;
        this.b = ebn;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof epl)) {
            return false;
        }
        epl epl = (epl) obj;
        if (this.a == epl.a && jnu.i(this.b, epl.b)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        ebn ebn = this.b;
        if (ebn.B()) {
            i = ebn.i();
        } else {
            int i2 = ebn.memoizedHashCode;
            if (i2 == 0) {
                i2 = ebn.i();
                ebn.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (this.a * 31) + i;
    }

    public final String toString() {
        return "HotwordSourceSessionData(sessionToken=" + this.a + ", params=" + this.b + ")";
    }
}
