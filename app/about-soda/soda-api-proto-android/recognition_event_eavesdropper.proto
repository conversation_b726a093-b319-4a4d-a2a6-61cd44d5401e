syntax = "proto2";

package greco;

import "speech/greco3/core/params.proto";

option objc_class_prefix = "GRC";
option optimize_for = CODE_SIZE;

message RecognitionEventEavesdropperParams {
    optional string filename = 1;
    optional string decoder_events_filename = 3;
    optional bool binary = 2 [default = false];
    
    extend Params {
        optional RecognitionEventEavesdropperParams id = 51983768;
    }
}
