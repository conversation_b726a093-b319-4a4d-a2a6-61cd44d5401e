package defpackage;

/* renamed from: bgp  reason: default package */
/* compiled from: PG */
final class bgp extends aub {
    public bgp(aus aus) {
        super(aus);
    }

    /* access modifiers changed from: protected */
    public final String a() {
        return "INSERT OR REPLACE INTO `SystemIdInfo` (`work_spec_id`,`generation`,`system_id`) VALUES (?,?,?)";
    }

    /* access modifiers changed from: protected */
    public final /* bridge */ /* synthetic */ void c(axc axc, Object obj) {
        bgn bgn = (bgn) obj;
        axc.g(1, bgn.a);
        axc.e(2, (long) bgn.b);
        axc.e(3, (long) bgn.c);
    }
}
