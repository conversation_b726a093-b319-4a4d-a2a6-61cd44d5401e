package defpackage;

/* renamed from: ern  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ern implements erl {
    private final /* synthetic */ int a;
    private final /* synthetic */ dzb b;

    public ern(int i, dzb dzb) {
        jnu.e(dzb, "intent");
        this.a = i;
        this.b = dzb;
    }

    public final /* synthetic */ int a() {
        return this.a;
    }

    public final /* synthetic */ Class annotationType() {
        return erl.class;
    }

    public final /* synthetic */ dzb b() {
        return this.b;
    }

    public final boolean equals(Object obj) {
        if (!(obj instanceof erl)) {
            return false;
        }
        erl erl = (erl) obj;
        if (this.a == erl.a() && this.b == erl.b()) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (this.a ^ -1129414950) + (this.b.hashCode() ^ -14018716);
    }

    public final String toString() {
        return "@com.google.android.libraries.search.audio.policies.PolicyUse(clientOrdinal=" + this.a + ", intent=" + this.b + ")";
    }
}
