package defpackage;

import java.util.concurrent.Callable;

/* renamed from: czb  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class czb implements Callable {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    private final /* synthetic */ int c;

    public /* synthetic */ czb(Object obj, Object obj2, int i) {
        this.c = i;
        this.a = obj;
        this.b = obj2;
    }

    /* JADX WARNING: type inference failed for: r2v3, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v4, types: [java.lang.Object, hkn] */
    /* JADX WARNING: type inference failed for: r2v7, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v12, types: [java.util.Collection, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v14, types: [hco, hdc] */
    /* JADX WARNING: type inference failed for: r2v10, types: [java.util.Collection, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v12, types: [hva, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v74, types: [fdo, fev, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v87, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v74, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v151, types: [java.lang.Object, java.util.concurrent.Callable] */
    /* JADX WARNING: type inference failed for: r0v155, types: [java.lang.Object, java.lang.Runnable] */
    /* JADX WARNING: type inference failed for: r0v157, types: [java.lang.Object, java.util.concurrent.Callable] */
    /* JADX WARNING: type inference failed for: r0v159, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v51, types: [java.util.Set, java.lang.Object] */
    /* JADX WARNING: Can't fix incorrect switch cases order */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object call() {
        /*
            r18 = this;
            r1 = r18
            java.lang.String r2 = "Failed to parse flag"
            java.lang.String r3 = "MendelPackageState"
            java.lang.String r4 = "newBuilder(...)"
            int r0 = r1.c
            r5 = 8
            r7 = 6
            r8 = 3
            r9 = -1
            r10 = 4
            r11 = 5
            r12 = 0
            r13 = 2
            java.lang.String r14 = "com/google/android/libraries/speech/transcription/recognition/grpc/impl/RecognitionSession"
            java.lang.String r15 = "RecognitionSession.java"
            r16 = 0
            r6 = 1
            switch(r0) {
                case 0: goto L_0x07d6;
                case 1: goto L_0x07cb;
                case 2: goto L_0x07b8;
                case 3: goto L_0x07aa;
                case 4: goto L_0x0788;
                case 5: goto L_0x0740;
                case 6: goto L_0x070f;
                case 7: goto L_0x0674;
                case 8: goto L_0x05b4;
                case 9: goto L_0x04e8;
                case 10: goto L_0x049f;
                case 11: goto L_0x0407;
                case 12: goto L_0x03d5;
                case 13: goto L_0x0280;
                case 14: goto L_0x0154;
                case 15: goto L_0x00fa;
                case 16: goto L_0x00dc;
                case 17: goto L_0x00d4;
                case 18: goto L_0x00b4;
                case 19: goto L_0x002f;
                default: goto L_0x001d;
            }
        L_0x001d:
            java.lang.Object r2 = r1.a
            r3 = r2
            giw r3 = (defpackage.giw) r3
            java.util.concurrent.locks.ReentrantReadWriteLock r0 = r3.b
            java.util.concurrent.locks.ReentrantReadWriteLock$WriteLock r0 = r0.writeLock()
            r0.lock()
            java.lang.Object r4 = r1.b
            goto L_0x07e7
        L_0x002f:
            java.lang.Object r5 = r1.a
            java.lang.Object r0 = r1.b
            java.lang.Object r0 = defpackage.hfc.S(r0)     // Catch:{ ExecutionException -> 0x003a }
            gcq r0 = (defpackage.gcq) r0     // Catch:{ ExecutionException -> 0x003a }
            goto L_0x0063
        L_0x003a:
            r0 = move-exception
            java.lang.Throwable r6 = r0.getCause()
            boolean r6 = r6 instanceof java.io.IOException
            if (r6 == 0) goto L_0x00b3
            r6 = r5
            gdx r6 = (defpackage.gdx) r6
            boolean r6 = r6.q
            if (r6 == 0) goto L_0x0052
            java.lang.Throwable r6 = r0.getCause()
            boolean r6 = r6 instanceof defpackage.fnw
            if (r6 != 0) goto L_0x00b3
        L_0x0052:
            gcq r0 = defpackage.gcq.i
            htk r0 = r0.l()
            defpackage.jnu.d(r0, r4)
            bzj r0 = defpackage.jnu.e(r0, "builder")
            gcq r0 = r0.K()
        L_0x0063:
            r6 = r5
            gdx r6 = (defpackage.gdx) r6     // Catch:{ hui -> 0x0088, RuntimeException -> 0x006b }
            gxv r2 = r6.c(r0)     // Catch:{ hui -> 0x0088, RuntimeException -> 0x006b }
            goto L_0x00a4
        L_0x006b:
            r0 = move-exception
            android.util.Log.e(r3, r2, r0)
            gcq r0 = defpackage.gcq.i
            htk r0 = r0.l()
            defpackage.jnu.d(r0, r4)
            bzj r0 = defpackage.jnu.e(r0, "builder")
            gcq r0 = r0.K()
            r2 = r5
            gdx r2 = (defpackage.gdx) r2
            gxv r2 = r2.c(r0)
            goto L_0x00a4
        L_0x0088:
            r0 = move-exception
            android.util.Log.e(r3, r2, r0)
            gcq r0 = defpackage.gcq.i
            htk r0 = r0.l()
            defpackage.jnu.d(r0, r4)
            bzj r0 = defpackage.jnu.e(r0, "builder")
            gcq r0 = r0.K()
            r2 = r5
            gdx r2 = (defpackage.gdx) r2
            gxv r2 = r2.c(r0)
        L_0x00a4:
            gdx r5 = (defpackage.gdx) r5
            jne r3 = r5.j
            gdv r0 = defpackage.ftc.v(r0)
            java.lang.Object r0 = r3.b(r2, r0)
            gdn r0 = (defpackage.gdn) r0
            return r0
        L_0x00b3:
            throw r0
        L_0x00b4:
            java.lang.Object r0 = r1.b
            java.lang.String r2 = "$callable"
            defpackage.jnu.e(r0, r2)
            java.lang.Object r2 = r1.a
            java.lang.Object r0 = r0.call()
            gbk r2 = (defpackage.gbk) r2
            java.lang.ThreadLocal r3 = r2.a
            java.lang.Object r3 = r3.get()
            java.lang.Throwable r3 = (java.lang.Throwable) r3
            java.lang.ThreadLocal r2 = r2.a
            r2.remove()
            if (r3 != 0) goto L_0x00d3
            return r0
        L_0x00d3:
            throw r3
        L_0x00d4:
            java.lang.Object r0 = r1.b
            r0.run()
            java.lang.Object r0 = r1.a
            return r0
        L_0x00dc:
            java.lang.Object r0 = r1.a
            r2 = r0
            fqd r2 = (defpackage.fqd) r2
            androidx.wear.ambient.AmbientModeSupport$AmbientController r0 = r2.e
            r0.f()
            java.lang.Object r0 = r1.b
            java.lang.Object r0 = r0.call()     // Catch:{ all -> 0x00f2 }
            androidx.wear.ambient.AmbientModeSupport$AmbientController r2 = r2.e
            r2.e()
            return r0
        L_0x00f2:
            r0 = move-exception
            r3 = r0
            androidx.wear.ambient.AmbientModeSupport$AmbientController r0 = r2.e
            r0.e()
            throw r3
        L_0x00fa:
            fkl r0 = defpackage.fkl.d
            htk r0 = r0.l()
            java.lang.Object r2 = r1.a
            java.lang.Object r2 = defpackage.hfc.S(r2)
            fkg r2 = (defpackage.fkg) r2
            htq r3 = r0.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x0113
            r0.u()
        L_0x0113:
            java.lang.Object r3 = r1.b
            htq r4 = r0.b
            fkl r4 = (defpackage.fkl) r4
            r2.getClass()
            r4.b = r2
            int r2 = r4.a
            r2 = r2 | r6
            r4.a = r2
            java.lang.Object r2 = defpackage.hfc.S(r3)
            fct r2 = (defpackage.fct) r2
            huf r2 = r2.a
            htq r3 = r0.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x0136
            r0.u()
        L_0x0136:
            htq r3 = r0.b
            fkl r3 = (defpackage.fkl) r3
            huf r4 = r3.c
            boolean r5 = r4.c()
            if (r5 != 0) goto L_0x0148
            huf r4 = defpackage.htq.s(r4)
            r3.c = r4
        L_0x0148:
            huf r3 = r3.c
            defpackage.hrz.g(r2, r3)
            htq r0 = r0.r()
            fkl r0 = (defpackage.fkl) r0
            return r0
        L_0x0154:
            hca r0 = defpackage.fhm.a
            hco r0 = r0.f()
            hby r0 = (defpackage.hby) r0
            java.lang.String r2 = "onResults"
            r3 = 383(0x17f, float:5.37E-43)
            hco r0 = r0.j(r14, r2, r3, r15)
            hby r0 = (defpackage.hby) r0
            java.lang.Object r2 = r1.a
            fdc r2 = (defpackage.fdc) r2
            huf r3 = r2.b
            int r3 = r3.size()
            java.lang.Object r4 = r1.b
            fhm r4 = (defpackage.fhm) r4
            java.lang.String r7 = "[%s] #onFinalRecognitionResults: %d hypothesis"
            java.lang.String r9 = r4.g
            r0.A(r7, r9, r3)
            int r0 = defpackage.fhh.a
            fgp r0 = defpackage.fgp.f
            htk r0 = r0.l()
            int r3 = r2.a
            r3 = r3 & r6
            if (r3 == 0) goto L_0x01a3
            java.lang.String r3 = r2.e
            htq r7 = r0.b
            boolean r7 = r7.B()
            if (r7 != 0) goto L_0x0195
            r0.u()
        L_0x0195:
            htq r7 = r0.b
            fgp r7 = (defpackage.fgp) r7
            r3.getClass()
            int r9 = r7.a
            r6 = r6 | r9
            r7.a = r6
            r7.b = r3
        L_0x01a3:
            huf r3 = r2.b
            int r3 = r3.size()
            if (r3 <= 0) goto L_0x01cf
            huf r3 = r2.b
            htq r6 = r0.b
            boolean r6 = r6.B()
            if (r6 != 0) goto L_0x01b8
            r0.u()
        L_0x01b8:
            htq r6 = r0.b
            fgp r6 = (defpackage.fgp) r6
            huf r7 = r6.c
            boolean r9 = r7.c()
            if (r9 != 0) goto L_0x01ca
            huf r7 = defpackage.htq.s(r7)
            r6.c = r7
        L_0x01ca:
            huf r6 = r6.c
            defpackage.hrz.g(r3, r6)
        L_0x01cf:
            htv r3 = r2.c
            int r3 = r3.size()
            if (r3 <= 0) goto L_0x01fb
            htv r3 = r2.c
            htq r6 = r0.b
            boolean r6 = r6.B()
            if (r6 != 0) goto L_0x01e4
            r0.u()
        L_0x01e4:
            htq r6 = r0.b
            fgp r6 = (defpackage.fgp) r6
            htv r7 = r6.d
            boolean r9 = r7.c()
            if (r9 != 0) goto L_0x01f6
            htv r7 = defpackage.htq.p(r7)
            r6.d = r7
        L_0x01f6:
            htv r6 = r6.d
            defpackage.hrz.g(r3, r6)
        L_0x01fb:
            huf r3 = r2.f
            int r3 = r3.size()
            if (r3 <= 0) goto L_0x0249
            huf r3 = r2.f
            j$.util.stream.Stream r3 = j$.util.Collection.EL.stream(r3)
            eyf r6 = new eyf
            r7 = 14
            r6.<init>(r7)
            j$.util.stream.Stream r3 = r3.filter(r6)
            fba r6 = new fba
            r6.<init>(r5)
            j$.util.stream.Stream r3 = r3.map(r6)
            int r5 = defpackage.gxq.d
            j$.util.stream.Collector r5 = defpackage.gvx.a
            java.lang.Object r3 = r3.collect(r5)
            gxq r3 = (defpackage.gxq) r3
            htq r5 = r0.b
            boolean r5 = r5.B()
            if (r5 != 0) goto L_0x0232
            r0.u()
        L_0x0232:
            htq r5 = r0.b
            fgp r5 = (defpackage.fgp) r5
            huf r6 = r5.e
            boolean r7 = r6.c()
            if (r7 != 0) goto L_0x0244
            huf r6 = defpackage.htq.s(r6)
            r5.e = r6
        L_0x0244:
            huf r5 = r5.e
            defpackage.hrz.g(r3, r5)
        L_0x0249:
            fgu r3 = defpackage.fgu.c
            htk r3 = r3.l()
            htm r3 = (defpackage.htm) r3
            htq r0 = r0.r()
            fgp r0 = (defpackage.fgp) r0
            htq r5 = r3.b
            boolean r5 = r5.B()
            if (r5 != 0) goto L_0x0262
            r3.u()
        L_0x0262:
            androidx.wear.ambient.AmbientMode$AmbientController r5 = r4.l
            htq r6 = r3.b
            fgu r6 = (defpackage.fgu) r6
            r0.getClass()
            r6.b = r0
            r6.a = r8
            htq r0 = r3.r()
            fgu r0 = (defpackage.fgu) r0
            r5.c(r0)
            fec r0 = r4.e
            java.lang.String r3 = r4.g
            r0.f(r3, r2)
            return r16
        L_0x0280:
            hca r0 = defpackage.fhm.a
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            r2 = 146(0x92, float:2.05E-43)
            java.lang.String r3 = "startRecognition"
            hco r0 = r0.j(r14, r3, r2, r15)
            hby r0 = (defpackage.hby) r0
            java.lang.Object r2 = r1.b
            r4 = r2
            fhm r4 = (defpackage.fhm) r4
            java.lang.String r5 = r4.g
            java.lang.String r7 = "[%s] #startRecognition"
            r0.u(r7, r5)
            boolean r0 = r4.h
            if (r0 == 0) goto L_0x02bb
            hca r0 = defpackage.fhm.a
            hco r0 = r0.h()
            hby r0 = (defpackage.hby) r0
            r2 = 148(0x94, float:2.07E-43)
            hco r0 = r0.j(r14, r3, r2, r15)
            hby r0 = (defpackage.hby) r0
            java.lang.String r2 = r4.g
            java.lang.String r3 = "[%s] Recognition already started, ignore start request."
            r0.u(r3, r2)
            goto L_0x03d4
        L_0x02bb:
            java.lang.Object r0 = r1.a
            fhl r0 = (defpackage.fhl) r0
            fgk r5 = r0.a
            int r7 = r5.b
            iai r7 = defpackage.iai.b(r7)
            if (r7 != 0) goto L_0x02cb
            iai r7 = defpackage.iai.UNKNOWN
        L_0x02cb:
            iai r8 = defpackage.iai.UNKNOWN
            if (r7 != r8) goto L_0x0346
            hca r0 = defpackage.fhm.a
            hco r0 = r0.h()
            hby r0 = (defpackage.hby) r0
            r2 = 155(0x9b, float:2.17E-43)
            hco r0 = r0.j(r14, r3, r2, r15)
            hby r0 = (defpackage.hby) r0
            java.lang.String r2 = "Can't start recognition without application domain."
            r0.r(r2)
            androidx.wear.ambient.AmbientMode$AmbientController r0 = r4.l
            fgu r2 = defpackage.fgu.c
            htk r2 = r2.l()
            htm r2 = (defpackage.htm) r2
            fgt r3 = defpackage.fgt.d
            htk r3 = r3.l()
            htq r4 = r3.b
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x02ff
            r3.u()
        L_0x02ff:
            htq r4 = r3.b
            r5 = r4
            fgt r5 = (defpackage.fgt) r5
            r5.b = r11
            int r7 = r5.a
            r7 = r7 | r6
            r5.a = r7
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x0314
            r3.u()
        L_0x0314:
            htq r4 = r3.b
            fgt r4 = (defpackage.fgt) r4
            r4.c = r6
            int r5 = r4.a
            r5 = r5 | r13
            r4.a = r5
            htq r3 = r3.r()
            fgt r3 = (defpackage.fgt) r3
            htq r4 = r2.b
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x0330
            r2.u()
        L_0x0330:
            htq r4 = r2.b
            fgu r4 = (defpackage.fgu) r4
            r3.getClass()
            r4.b = r3
            r4.a = r6
            htq r2 = r2.r()
            fgu r2 = (defpackage.fgu) r2
            r0.c(r2)
            goto L_0x03d4
        L_0x0346:
            j$.util.Optional r7 = r0.b
            hca r8 = defpackage.fhm.a
            hco r8 = r8.f()
            hby r8 = (defpackage.hby) r8
            r10 = 171(0xab, float:2.4E-43)
            hco r3 = r8.j(r14, r3, r10, r15)
            hby r3 = (defpackage.hby) r3
            java.lang.String r8 = r4.g
            j$.util.Optional r10 = r0.b
            boolean r10 = r10.isPresent()
            java.lang.Boolean r10 = java.lang.Boolean.valueOf(r10)
            j$.util.Optional r11 = r0.c
            java.lang.String r12 = "[%s] Start recognition with external audio source: %b, attr: %s"
            r3.G(r12, r8, r10, r11)
            fed r3 = r4.f
            java.lang.String r8 = r4.g
            fee r10 = defpackage.fef.a()
            few r11 = defpackage.few.GRPC_SERVICE_API
            r10.d(r11)
            ffm r11 = r5.a
            if (r11 != 0) goto L_0x037e
            ffm r11 = defpackage.ffm.b
        L_0x037e:
            java.lang.String r11 = r11.a
            r10.b(r11)
            java.lang.String r11 = r5.c
            r10.h(r11)
            int r11 = r5.e
            int r11 = defpackage.a.z(r11)
            if (r11 != 0) goto L_0x0391
            r11 = r6
        L_0x0391:
            r10.j(r11)
            boolean r11 = r7.isPresent()
            r10.f(r11)
            boolean r11 = r5.g
            r10.e(r11)
            fef r10 = r10.a()
            r3.k(r8, r10)
            fhh r3 = r4.b
            fey r3 = r3.a(r5, r7)
            int r5 = android.os.Build.VERSION.SDK_INT
            r7 = 35
            if (r5 < r7) goto L_0x03c1
            fex r5 = new fex
            r5.<init>((defpackage.fey) r3)
            j$.util.Optional r0 = r0.c
            r5.e(r0)
            fey r3 = r5.a()
        L_0x03c1:
            alx r0 = r4.k
            fdz r0 = r0.d(r3, r2, r2)
            r0.b()
            j$.util.Optional r0 = j$.util.Optional.of(r0)
            r4.j = r0
            r4.h = r6
            r4.i = r9
        L_0x03d4:
            return r16
        L_0x03d5:
            hca r0 = defpackage.fhm.a
            hco r0 = r0.h()
            hby r0 = (defpackage.hby) r0
            java.lang.Object r2 = r1.a
            r3 = r2
            java.lang.Throwable r3 = (java.lang.Throwable) r3
            hco r0 = r0.i(r3)
            hby r0 = (defpackage.hby) r0
            java.lang.String r3 = "onFallback"
            r4 = 455(0x1c7, float:6.38E-43)
            hco r0 = r0.j(r14, r3, r4, r15)
            hby r0 = (defpackage.hby) r0
            java.lang.Object r3 = r1.b
            fhm r3 = (defpackage.fhm) r3
            java.lang.String r4 = "[%s] onFallback"
            java.lang.String r5 = r3.g
            r0.u(r4, r5)
            java.lang.String r0 = r3.g
            fed r3 = r3.f
            fck r2 = (defpackage.fck) r2
            r3.e(r0, r2)
            return r16
        L_0x0407:
            java.lang.Object r0 = r1.b
            j$.util.Optional r0 = (j$.util.Optional) r0
            boolean r2 = r0.isPresent()
            java.lang.Object r3 = r1.a
            java.lang.String r4 = "cancelRecognition"
            if (r2 == 0) goto L_0x043c
            hca r2 = defpackage.fhm.a
            hco r2 = r2.h()
            hby r2 = (defpackage.hby) r2
            java.lang.Object r0 = r0.get()
            java.lang.Throwable r0 = (java.lang.Throwable) r0
            hco r0 = r2.i(r0)
            hby r0 = (defpackage.hby) r0
            r2 = 229(0xe5, float:3.21E-43)
            hco r0 = r0.j(r14, r4, r2, r15)
            hby r0 = (defpackage.hby) r0
            r2 = r3
            fhm r2 = (defpackage.fhm) r2
            java.lang.String r2 = r2.g
            java.lang.String r5 = "[%s] #cancelRecognition due to error."
            r0.u(r5, r2)
            goto L_0x0456
        L_0x043c:
            hca r0 = defpackage.fhm.a
            hco r0 = r0.f()
            hby r0 = (defpackage.hby) r0
            r2 = 232(0xe8, float:3.25E-43)
            hco r0 = r0.j(r14, r4, r2, r15)
            hby r0 = (defpackage.hby) r0
            r2 = r3
            fhm r2 = (defpackage.fhm) r2
            java.lang.String r2 = r2.g
            java.lang.String r5 = "[%s] #cancelRecognition for client request."
            r0.u(r5, r2)
        L_0x0456:
            fhm r3 = (defpackage.fhm) r3
            j$.util.Optional r0 = r3.j
            boolean r0 = r0.isEmpty()
            if (r0 == 0) goto L_0x0478
            hca r0 = defpackage.fhm.a
            hco r0 = r0.g()
            hby r0 = (defpackage.hby) r0
            r2 = 236(0xec, float:3.31E-43)
            hco r0 = r0.j(r14, r4, r2, r15)
            hby r0 = (defpackage.hby) r0
            java.lang.String r2 = r3.g
            java.lang.String r4 = "[%s] RecognitionClient is null, ignore #cancelRecognition"
            r0.u(r4, r2)
            goto L_0x0489
        L_0x0478:
            j$.util.Optional r0 = r3.j
            java.lang.Object r0 = r0.get()
            fdz r0 = (defpackage.fdz) r0
            r0.a()
            j$.util.Optional r0 = j$.util.Optional.empty()
            r3.j = r0
        L_0x0489:
            androidx.wear.ambient.AmbientMode$AmbientController r0 = r3.l
            r0.b()
            r3.h = r12
            r3.i = r9
            fed r0 = r3.f
            java.lang.String r2 = r3.g
            fbx r3 = new fbx
            r3.<init>()
            r0.n(r2, r3)
            return r16
        L_0x049f:
            hca r0 = defpackage.fhm.a
            hco r0 = r0.b()
            hby r0 = (defpackage.hby) r0
            java.lang.String r2 = "onSodaEvent"
            r3 = 443(0x1bb, float:6.21E-43)
            hco r0 = r0.j(r14, r2, r3, r15)
            hby r0 = (defpackage.hby) r0
            java.lang.Object r2 = r1.a
            fhm r2 = (defpackage.fhm) r2
            java.lang.String r3 = "[%s] onSodaEvent"
            java.lang.String r4 = r2.g
            r0.u(r3, r4)
            fgu r0 = defpackage.fgu.c
            htk r0 = r0.l()
            htm r0 = (defpackage.htm) r0
            htq r3 = r0.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x04cf
            r0.u()
        L_0x04cf:
            androidx.wear.ambient.AmbientMode$AmbientController r2 = r2.l
            java.lang.Object r3 = r1.b
            htq r4 = r0.b
            fgu r4 = (defpackage.fgu) r4
            r3.getClass()
            r4.b = r3
            r4.a = r7
            htq r0 = r0.r()
            fgu r0 = (defpackage.fgu) r0
            r2.c(r0)
            return r16
        L_0x04e8:
            hca r0 = defpackage.fhm.a
            hco r0 = r0.h()
            hby r0 = (defpackage.hby) r0
            java.lang.String r2 = "onError"
            r3 = 295(0x127, float:4.13E-43)
            hco r0 = r0.j(r14, r2, r3, r15)
            hby r0 = (defpackage.hby) r0
            java.lang.Object r2 = r1.b
            r3 = r2
            fhm r3 = (defpackage.fhm) r3
            java.lang.String r4 = "[%s] onError"
            java.lang.String r14 = r3.g
            r0.u(r4, r14)
            java.lang.Object r0 = r1.a
            r4 = r0
            fck r4 = (defpackage.fck) r4
            fbw r14 = r4.a
            int r15 = r14.b
            int r17 = r15 + -1
            if (r15 == 0) goto L_0x05b3
            switch(r17) {
                case 0: goto L_0x0532;
                case 1: goto L_0x052f;
                case 2: goto L_0x0527;
                case 3: goto L_0x0525;
                case 4: goto L_0x0532;
                case 5: goto L_0x0523;
                case 6: goto L_0x0532;
                case 7: goto L_0x0521;
                case 8: goto L_0x051f;
                case 9: goto L_0x0532;
                case 10: goto L_0x0532;
                case 11: goto L_0x0518;
                case 12: goto L_0x0523;
                case 13: goto L_0x0532;
                case 14: goto L_0x0532;
                default: goto L_0x0516;
            }
        L_0x0516:
            r5 = r6
            goto L_0x0533
        L_0x0518:
            int r7 = r14.a
            if (r7 != r13) goto L_0x0533
            r5 = 9
            goto L_0x0533
        L_0x051f:
            r5 = r7
            goto L_0x0533
        L_0x0521:
            r5 = r8
            goto L_0x0533
        L_0x0523:
            r5 = r10
            goto L_0x0533
        L_0x0525:
            r5 = 7
            goto L_0x0533
        L_0x0527:
            int r5 = r14.a
            r8 = 102(0x66, float:1.43E-43)
            if (r5 != r8) goto L_0x051f
            r5 = r11
            goto L_0x0533
        L_0x052f:
            r5 = 10
            goto L_0x0533
        L_0x0532:
            r5 = r13
        L_0x0533:
            r3.h = r12
            fgu r7 = defpackage.fgu.c
            htk r7 = r7.l()
            htm r7 = (defpackage.htm) r7
            fgt r8 = defpackage.fgt.d
            htk r8 = r8.l()
            htq r10 = r8.b
            boolean r10 = r10.B()
            if (r10 != 0) goto L_0x054e
            r8.u()
        L_0x054e:
            htq r10 = r8.b
            r14 = r10
            fgt r14 = (defpackage.fgt) r14
            r14.b = r11
            int r11 = r14.a
            r11 = r11 | r6
            r14.a = r11
            boolean r10 = r10.B()
            if (r10 != 0) goto L_0x0563
            r8.u()
        L_0x0563:
            htq r10 = r8.b
            fgt r10 = (defpackage.fgt) r10
            int r5 = r5 + r9
            r10.c = r5
            int r5 = r10.a
            r5 = r5 | r13
            r10.a = r5
            htq r5 = r8.r()
            fgt r5 = (defpackage.fgt) r5
            htq r8 = r7.b
            boolean r8 = r8.B()
            if (r8 != 0) goto L_0x0580
            r7.u()
        L_0x0580:
            htq r8 = r7.b
            fgu r8 = (defpackage.fgu) r8
            r5.getClass()
            r8.b = r5
            r8.a = r6
            htq r5 = r7.r()
            fgu r5 = (defpackage.fgu) r5
            if (r15 != r13) goto L_0x05a7
            androidx.wear.ambient.AmbientMode$AmbientController r0 = r3.l
            r0.c(r5)
            androidx.wear.ambient.AmbientMode$AmbientController r0 = r3.l
            r0.b()
            fed r0 = r3.f
            java.lang.String r2 = r3.g
            r0.n(r2, r4)
            r3.h = r12
            goto L_0x05b2
        L_0x05a7:
            fhw r3 = r3.d
            ety r4 = new ety
            r6 = 5
            r4.<init>((java.lang.Object) r2, (java.lang.Object) r5, (java.lang.Object) r0, (int) r6)
            r3.a(r4)
        L_0x05b2:
            return r16
        L_0x05b3:
            throw r16
        L_0x05b4:
            hca r0 = defpackage.fhm.a
            hco r0 = r0.f()
            hby r0 = (defpackage.hby) r0
            java.lang.String r2 = "onPartialResults"
            r3 = 398(0x18e, float:5.58E-43)
            hco r0 = r0.j(r14, r2, r3, r15)
            hby r0 = (defpackage.hby) r0
            java.lang.Object r2 = r1.b
            fhm r2 = (defpackage.fhm) r2
            java.lang.String r3 = "[%s] #onPartialResults"
            java.lang.String r4 = r2.g
            r0.u(r3, r4)
            int r0 = defpackage.fhh.a
            fgr r0 = defpackage.fgr.e
            htk r0 = r0.l()
            java.lang.Object r3 = r1.a
            feg r3 = (defpackage.feg) r3
            int r4 = r3.a
            r4 = r4 & r10
            if (r4 == 0) goto L_0x05fd
            java.lang.String r4 = r3.d
            htq r5 = r0.b
            boolean r5 = r5.B()
            if (r5 != 0) goto L_0x05ef
            r0.u()
        L_0x05ef:
            htq r5 = r0.b
            fgr r5 = (defpackage.fgr) r5
            r4.getClass()
            int r7 = r5.a
            r7 = r7 | r6
            r5.a = r7
            r5.b = r4
        L_0x05fd:
            int r4 = r3.a
            r4 = r4 & r6
            if (r4 == 0) goto L_0x061d
            java.lang.String r4 = r3.b
            htq r5 = r0.b
            boolean r5 = r5.B()
            if (r5 != 0) goto L_0x060f
            r0.u()
        L_0x060f:
            htq r5 = r0.b
            fgr r5 = (defpackage.fgr) r5
            r4.getClass()
            int r6 = r5.a
            r6 = r6 | r13
            r5.a = r6
            r5.c = r4
        L_0x061d:
            int r4 = r3.a
            r4 = r4 & r13
            if (r4 == 0) goto L_0x063d
            java.lang.String r4 = r3.c
            htq r5 = r0.b
            boolean r5 = r5.B()
            if (r5 != 0) goto L_0x062f
            r0.u()
        L_0x062f:
            htq r5 = r0.b
            fgr r5 = (defpackage.fgr) r5
            r4.getClass()
            int r6 = r5.a
            r6 = r6 | r10
            r5.a = r6
            r5.d = r4
        L_0x063d:
            fgu r4 = defpackage.fgu.c
            htk r4 = r4.l()
            htm r4 = (defpackage.htm) r4
            htq r0 = r0.r()
            fgr r0 = (defpackage.fgr) r0
            htq r5 = r4.b
            boolean r5 = r5.B()
            if (r5 != 0) goto L_0x0656
            r4.u()
        L_0x0656:
            androidx.wear.ambient.AmbientMode$AmbientController r5 = r2.l
            htq r6 = r4.b
            fgu r6 = (defpackage.fgu) r6
            r0.getClass()
            r6.b = r0
            r6.a = r13
            htq r0 = r4.r()
            fgu r0 = (defpackage.fgu) r0
            r5.c(r0)
            fec r0 = r2.e
            java.lang.String r2 = r2.g
            r0.j(r2, r3)
            return r16
        L_0x0674:
            hca r0 = defpackage.fer.a
            hco r0 = r0.f()
            hby r0 = (defpackage.hby) r0
            r2 = 846(0x34e, float:1.185E-42)
            java.lang.String r3 = "com/google/android/libraries/speech/transcription/recognition/SodaSpeechRecognizer$SodaCallbackImpl"
            java.lang.String r4 = "handleStop"
            java.lang.String r5 = "SodaSpeechRecognizer.java"
            hco r0 = r0.j(r3, r4, r2, r5)
            hby r0 = (defpackage.hby) r0
            java.lang.Object r2 = r1.a
            java.lang.String r6 = "#handleStop: %s"
            r0.u(r6, r2)
            java.lang.Object r0 = r1.b
            cmy r6 = defpackage.cmy.ERROR
            if (r2 != r6) goto L_0x06a7
            r2 = r0
            feq r2 = (defpackage.feq) r2
            fer r2 = r2.b
            fci r6 = new fci
            r6.<init>(r8)
            fev r2 = r2.l
            r2.c(r6)
            goto L_0x06b1
        L_0x06a7:
            r2 = r0
            feq r2 = (defpackage.feq) r2
            fer r2 = r2.b
            fev r2 = r2.l
            r2.h()
        L_0x06b1:
            feq r0 = (defpackage.feq) r0
            fer r2 = r0.b
            grh r2 = r2.e
            boolean r6 = r2.f()
            if (r6 == 0) goto L_0x06c6
            java.lang.Object r2 = r2.b()
            fcl r2 = (defpackage.fcl) r2
            r2.b()
        L_0x06c6:
            grh r2 = r0.a
            boolean r6 = r2.f()
            if (r6 == 0) goto L_0x06d7
            java.lang.Object r2 = r2.b()
            fei r2 = (defpackage.fei) r2
            r2.d()
        L_0x06d7:
            fer r2 = r0.b
            cms r2 = r2.w
            if (r2 == 0) goto L_0x070e
            hca r2 = defpackage.fer.a
            hco r2 = r2.f()
            hby r2 = (defpackage.hby) r2
            r6 = 861(0x35d, float:1.207E-42)
            hco r2 = r2.j(r3, r4, r6, r5)
            hby r2 = (defpackage.hby) r2
            java.lang.String r3 = "#handleStop: scheduleSodaTimeout"
            r2.r(r3)
            fer r0 = r0.b
            htb r2 = r0.g
            long r3 = r2.a
            r5 = -1
            int r3 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            if (r3 != 0) goto L_0x0709
            cms r0 = r0.w
            htb r2 = defpackage.hwp.a
            defpackage.hwp.d(r2)
            r0.e(r2)
            goto L_0x070e
        L_0x0709:
            cms r0 = r0.w
            r0.e(r2)
        L_0x070e:
            return r16
        L_0x070f:
            java.lang.Object r0 = r1.a
            ade r0 = (defpackage.ade) r0
            java.lang.Object r0 = r0.a
            java.lang.Object r2 = r1.b
            eeb r2 = (defpackage.eeb) r2
            android.media.AudioManager r3 = r2.c
            android.media.AudioFocusRequest r0 = defpackage.ag$$ExternalSyntheticApiModelOutline0.m((java.lang.Object) r0)
            int r0 = r3.requestAudioFocus(r0)
            if (r0 != r6) goto L_0x072a
            dyk r0 = defpackage.eki.n(r13)
            goto L_0x073f
        L_0x072a:
            android.media.AudioManager r0 = r2.c
            int r0 = r0.getMode()
            if (r0 == r13) goto L_0x073a
            if (r0 != r6) goto L_0x0735
            goto L_0x073a
        L_0x0735:
            dyk r0 = defpackage.eki.n(r10)
            goto L_0x073f
        L_0x073a:
            r0 = 7
            dyk r0 = defpackage.eki.n(r0)
        L_0x073f:
            return r0
        L_0x0740:
            hoj r0 = new hoj
            r0.<init>()
            java.lang.Object r2 = r1.b
            java.lang.Object r3 = r1.a
            r4 = r3
            gef r4 = (defpackage.gef) r4     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            java.lang.Object r4 = r4.c     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            dte r4 = (defpackage.dte) r4     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            kjd r4 = r4.g()     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            r5 = r3
            gef r5 = (defpackage.gef) r5     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            java.lang.Object r5 = r5.b     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            fot r7 = new fot     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            r7.<init>(r2)     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            hoj[] r2 = new defpackage.hoj[r6]     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            r2[r12] = r0     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            r7.a = r2     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            android.net.Uri r5 = (android.net.Uri) r5     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            java.lang.Object r0 = r4.e(r5, r7)     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            java.lang.Void r0 = (java.lang.Void) r0     // Catch:{ IOException -> 0x076f, RuntimeException -> 0x076d }
            goto L_0x0787
        L_0x076d:
            r0 = move-exception
            goto L_0x0770
        L_0x076f:
            r0 = move-exception
        L_0x0770:
            gef r3 = (defpackage.gef) r3
            java.lang.Object r2 = r3.c
            java.util.logging.Level r4 = java.util.logging.Level.WARNING
            dte r2 = (defpackage.dte) r2
            hmi r2 = r2.d()
            java.lang.Object r3 = r3.d
            java.lang.Object[] r5 = new java.lang.Object[r6]
            r5[r12] = r3
            java.lang.String r3 = "Failed to update snapshot for %s flags may be stale."
            defpackage.don.b(r4, r2, r0, r3, r5)
        L_0x0787:
            return r16
        L_0x0788:
            hdf r0 = defpackage.dcs.a
            hco r0 = r0.l()
            java.lang.String r2 = "requestNewPacks"
            r3 = 288(0x120, float:4.04E-43)
            java.lang.String r4 = "com/google/android/libraries/micore/superpacks/packs/PacksRequest"
            java.lang.String r5 = "PacksRequest.java"
            hco r0 = r0.j(r4, r2, r3, r5)
            hdc r0 = (defpackage.hdc) r0
            java.lang.Object r2 = r1.a
            java.lang.String r3 = "Done Requesting %d slices"
            int r2 = r2.size()
            r0.s(r3, r2)
            java.lang.Object r0 = r1.b
            return r0
        L_0x07aa:
            java.lang.Object r0 = r1.a
            java.lang.Object[] r2 = new java.lang.Object[r6]
            r2[r12] = r0
            java.lang.Object r0 = r1.b
            java.lang.String r3 = "Cancellation request for pack '%s' failed"
            defpackage.dcj.a(r0, r3, r2)
            return r16
        L_0x07b8:
            java.lang.Object r0 = r1.b
            java.lang.Object r2 = r1.a
            cyw r2 = (defpackage.cyw) r2
            java.lang.Object r2 = r2.d
            java.lang.Object r0 = r2.get(r0)
            hme r0 = (defpackage.hme) r0
            grh r0 = defpackage.grh.g(r0)
            return r0
        L_0x07cb:
            java.lang.Object r0 = r1.a
            java.lang.Object r2 = r1.b
            cqd r0 = (defpackage.cqd) r0
            java.lang.Object r0 = r0.a(r2)
            return r0
        L_0x07d6:
            java.lang.Object r0 = r1.b
            java.lang.Object r2 = r1.a
            cyw r2 = (defpackage.cyw) r2
            java.lang.Object r2 = r2.d
            boolean r0 = r2.containsKey(r0)
            java.lang.Boolean r0 = java.lang.Boolean.valueOf(r0)
            return r0
        L_0x07e7:
            gkh r5 = defpackage.gkh.f     // Catch:{ all -> 0x08a1 }
            r0 = r2
            giw r0 = (defpackage.giw) r0     // Catch:{ IOException -> 0x07f2 }
            gkh r5 = r0.a()     // Catch:{ IOException -> 0x07f2 }
        L_0x07f0:
            r6 = 5
            goto L_0x0811
        L_0x07f2:
            r0 = move-exception
            r12 = r0
            r0 = r2
            giw r0 = (defpackage.giw) r0     // Catch:{ all -> 0x08a1 }
            boolean r0 = r0.h(r12)     // Catch:{ all -> 0x08a1 }
            if (r0 != 0) goto L_0x07f0
            hca r0 = defpackage.giw.a     // Catch:{ all -> 0x08a1 }
            hco r6 = r0.g()     // Catch:{ all -> 0x08a1 }
            java.lang.String r8 = "com/google/apps/tiktok/sync/impl/SyncManagerDataStore"
            java.lang.String r9 = "updateScheduledAccountIds"
            java.lang.String r11 = "SyncManagerDataStore.java"
            java.lang.String r7 = "Unable to read or clear store, will not update scheduled account ids. "
            r10 = 429(0x1ad, float:6.01E-43)
            ((defpackage.hby) ((defpackage.hby) ((defpackage.hby) r6).i(r12)).j(r8, r9, r10, r11)).r(r7)     // Catch:{ all -> 0x08a1 }
            goto L_0x07f0
        L_0x0811:
            java.lang.Object r0 = r5.C(r6)     // Catch:{ all -> 0x08a1 }
            htk r0 = (defpackage.htk) r0     // Catch:{ all -> 0x08a1 }
            r0.x(r5)     // Catch:{ all -> 0x08a1 }
            htq r5 = r0.b     // Catch:{ all -> 0x08a1 }
            boolean r5 = r5.B()     // Catch:{ all -> 0x08a1 }
            if (r5 != 0) goto L_0x0825
            r0.u()     // Catch:{ all -> 0x08a1 }
        L_0x0825:
            htq r5 = r0.b     // Catch:{ all -> 0x08a1 }
            gkh r5 = (defpackage.gkh) r5     // Catch:{ all -> 0x08a1 }
            htr r6 = defpackage.htr.a     // Catch:{ all -> 0x08a1 }
            r5.e = r6     // Catch:{ all -> 0x08a1 }
            java.util.TreeSet r5 = new java.util.TreeSet     // Catch:{ all -> 0x08a1 }
            r5.<init>()     // Catch:{ all -> 0x08a1 }
            java.util.Iterator r4 = r4.iterator()     // Catch:{ all -> 0x08a1 }
        L_0x0836:
            boolean r6 = r4.hasNext()     // Catch:{ all -> 0x08a1 }
            if (r6 == 0) goto L_0x0854
            java.lang.Object r6 = r4.next()     // Catch:{ all -> 0x08a1 }
            gjf r6 = (defpackage.gjf) r6     // Catch:{ all -> 0x08a1 }
            boolean r7 = r6.a()     // Catch:{ all -> 0x08a1 }
            if (r7 == 0) goto L_0x0836
            fwm r6 = r6.c     // Catch:{ all -> 0x08a1 }
            int r6 = r6.a     // Catch:{ all -> 0x08a1 }
            java.lang.Integer r6 = java.lang.Integer.valueOf(r6)     // Catch:{ all -> 0x08a1 }
            r5.add(r6)     // Catch:{ all -> 0x08a1 }
            goto L_0x0836
        L_0x0854:
            htq r4 = r0.b     // Catch:{ all -> 0x08a1 }
            boolean r4 = r4.B()     // Catch:{ all -> 0x08a1 }
            if (r4 != 0) goto L_0x085f
            r0.u()     // Catch:{ all -> 0x08a1 }
        L_0x085f:
            htq r4 = r0.b     // Catch:{ all -> 0x08a1 }
            gkh r4 = (defpackage.gkh) r4     // Catch:{ all -> 0x08a1 }
            htw r6 = r4.e     // Catch:{ all -> 0x08a1 }
            boolean r7 = r6.c()     // Catch:{ all -> 0x08a1 }
            if (r7 != 0) goto L_0x0871
            htw r6 = defpackage.htq.q(r6)     // Catch:{ all -> 0x08a1 }
            r4.e = r6     // Catch:{ all -> 0x08a1 }
        L_0x0871:
            htw r4 = r4.e     // Catch:{ all -> 0x08a1 }
            defpackage.hrz.g(r5, r4)     // Catch:{ all -> 0x08a1 }
            htq r0 = r0.r()     // Catch:{ IOException -> 0x0882 }
            gkh r0 = (defpackage.gkh) r0     // Catch:{ IOException -> 0x0882 }
            giw r2 = (defpackage.giw) r2     // Catch:{ IOException -> 0x0882 }
            r2.g(r0)     // Catch:{ IOException -> 0x0882 }
            goto L_0x0897
        L_0x0882:
            r0 = move-exception
            r10 = r0
            hca r0 = defpackage.giw.a     // Catch:{ all -> 0x08a1 }
            hco r4 = r0.g()     // Catch:{ all -> 0x08a1 }
            java.lang.String r6 = "com/google/apps/tiktok/sync/impl/SyncManagerDataStore"
            java.lang.String r7 = "updateScheduledAccountIds"
            java.lang.String r9 = "SyncManagerDataStore.java"
            java.lang.String r5 = "Error writing scheduled account ids"
            r8 = 450(0x1c2, float:6.3E-43)
            ((defpackage.hby) ((defpackage.hby) ((defpackage.hby) r4).i(r10)).j(r6, r7, r8, r9)).r(r5)     // Catch:{ all -> 0x08a1 }
        L_0x0897:
            java.util.concurrent.locks.ReentrantReadWriteLock r0 = r3.b
            java.util.concurrent.locks.ReentrantReadWriteLock$WriteLock r0 = r0.writeLock()
            r0.unlock()
            return r16
        L_0x08a1:
            r0 = move-exception
            java.util.concurrent.locks.ReentrantReadWriteLock r2 = r3.b
            java.util.concurrent.locks.ReentrantReadWriteLock$WriteLock r2 = r2.writeLock()
            r2.unlock()
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.czb.call():java.lang.Object");
    }

    public /* synthetic */ czb(Object obj, Object obj2, int i, byte[] bArr) {
        this.c = i;
        this.b = obj;
        this.a = obj2;
    }
}
