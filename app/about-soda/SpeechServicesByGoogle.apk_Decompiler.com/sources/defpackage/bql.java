package defpackage;

/* renamed from: bql  reason: default package */
/* compiled from: PG */
public enum bql implements hts {
    ENQUEUE_STATUS_FAIL(0),
    ENQUEUE_STATUS_ALREADY_DOWNLOADING(1),
    ENQUEUE_STATUS_SUCCESS(2),
    ENQUEUE_STATUS_NOT_DOWNLOADABLE(3),
    ENQUEUE_STATUS_ALREADY_INSTALLED(4);
    
    public final int f;

    private bql(int i) {
        this.f = i;
    }

    public static bql b(int i) {
        if (i == 0) {
            return ENQUEUE_STATUS_FAIL;
        }
        if (i == 1) {
            return ENQUEUE_STATUS_ALREADY_DOWNLOADING;
        }
        if (i == 2) {
            return ENQUEUE_STATUS_SUCCESS;
        }
        if (i == 3) {
            return ENQUEUE_STATUS_NOT_DOWNLOADABLE;
        }
        if (i != 4) {
            return null;
        }
        return ENQUEUE_STATUS_ALREADY_INSTALLED;
    }

    public final int a() {
        return this.f;
    }

    public final String toString() {
        return Integer.toString(this.f);
    }
}
