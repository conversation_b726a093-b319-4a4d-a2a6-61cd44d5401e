package defpackage;

import org.json.JSONObject;

/* renamed from: evm  reason: default package */
/* compiled from: PG */
public final class evm implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;
    private final jjk e;
    private final jjk f;
    private final jjk g;
    private final jjk h;
    private final jjk i;
    private final jjk j;

    public evm(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, jjk jjk9, jjk jjk10) {
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
        this.e = jjk5;
        this.f = jjk6;
        this.g = jjk7;
        this.h = jjk8;
        this.i = jjk9;
        this.j = jjk10;
    }

    public final /* synthetic */ Object b() {
        boolean z;
        boolean booleanValue = ((ijj) this.a).a().booleanValue();
        String a2 = ((iml) this.b).a();
        htb a3 = ((imm) this.c).a();
        boolean booleanValue2 = ((ijj) this.d).a().booleanValue();
        boolean booleanValue3 = ((inb) this.e).a().booleanValue();
        boolean booleanValue4 = ((ijj) this.f).a().booleanValue();
        String a4 = ((iml) this.g).a();
        htb a5 = ((imm) this.h).a();
        boolean booleanValue5 = ((ijj) this.i).a().booleanValue();
        long longValue = ((ijk) this.j).a().longValue();
        JSONObject put = new JSONObject().put("quic_version", "h3").put("connection_options", a2).put("migrate_sessions_on_network_change_v2", true).put("retransmittable_on_wire_timeout_milliseconds", 200).put("set_quic_flags", "FLAGS_quic_max_aggressive_retransmittable_on_wire_ping_count=200").put("migrate_sessions_early_v2", true).put("retry_on_alternate_network_before_handshake", true).put("race_cert_verification", true).put("max_server_configs_stored_in_properties", 20).put("idle_connection_timeout_seconds", 300);
        long j2 = a3.a;
        if (j2 > 0) {
            put.put("initial_delay_for_broken_alternative_service_seconds", j2);
        }
        JSONObject put2 = new JSONObject().put("AsyncDNS", new JSONObject().put("enable", booleanValue4));
        JSONObject put3 = new JSONObject().put("enable", true);
        hwp.d(a5);
        JSONObject put4 = put2.put("StaleDNS", put3.put("delay_ms", hfc.an(hfc.ao(a5.a, 1000), (long) (a5.b / 1000000))).put("allow_other_network", true).put("persist_to_disk", true).put("max_expired_time_ms", 1814400000).put("use_stale_on_name_not_resolved", true)).put("enable_telemetry", booleanValue2);
        if (a4.length() > 0) {
            put4.put("HostResolverRules", new JSONObject().put("host_resolver_rules", a4));
        }
        if (booleanValue3) {
            put4.put("QUIC", put);
        }
        hov hov = new hov();
        hov.e(true);
        hov.b(false);
        hov.c(false);
        hov.i = (byte) (hov.i | 8);
        hov.d(false);
        hov.a(1000);
        hov.i = (byte) (hov.i | 64);
        hov.f(20);
        hov.e(booleanValue3);
        hov.b(true);
        hov.g = put4.toString();
        hov.d(booleanValue);
        hov.f(-2);
        hov.a = "cronet_cache";
        hov.c(booleanValue5);
        hov.a((int) longValue);
        if (hov.i != -1) {
            StringBuilder sb = new StringBuilder();
            if ((hov.i & 1) == 0) {
                sb.append(" enableQuic");
            }
            if ((hov.i & 2) == 0) {
                sb.append(" enableBrotli");
            }
            if ((hov.i & 4) == 0) {
                sb.append(" enableCertificateCache");
            }
            if ((hov.i & 8) == 0) {
                sb.append(" enableHttpCache");
            }
            if ((hov.i & 16) == 0) {
                sb.append(" enableNetworkQualityEstimator");
            }
            if ((hov.i & 32) == 0) {
                sb.append(" diskCacheSizeBytes");
            }
            if ((hov.i & 64) == 0) {
                sb.append(" inMemoryFallbackCacheSizeBytes");
            }
            if ((hov.i & 128) == 0) {
                sb.append(" threadPriority");
            }
            throw new IllegalStateException("Missing required properties:".concat(sb.toString()));
        }
        how how = new how(hov.a, hov.b, hov.c, hov.d, hov.e, hov.f, hov.g, hov.h);
        if (how.d) {
            if (how.a != null) {
                z = true;
            } else {
                z = false;
            }
            fvf.aG(z, "Must specify cache storage path.");
        }
        return how;
    }
}
