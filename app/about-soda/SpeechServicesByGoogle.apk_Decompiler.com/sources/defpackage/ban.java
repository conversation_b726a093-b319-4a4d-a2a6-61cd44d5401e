package defpackage;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/* renamed from: ban  reason: default package */
/* compiled from: PG */
public final class ban implements ThreadFactory {
    final /* synthetic */ boolean a;
    private final AtomicInteger b = new AtomicInteger(0);

    public ban(boolean z) {
        this.a = z;
    }

    public final Thread newThread(Runnable runnable) {
        String str;
        jnu.e(runnable, "runnable");
        StringBuilder sb = new StringBuilder();
        if (true != this.a) {
            str = "androidx.work-";
        } else {
            str = "WM.task-";
        }
        sb.append(str);
        sb.append(this.b.incrementAndGet());
        return new Thread(runnable, sb.toString());
    }
}
