package defpackage;

/* renamed from: cut  reason: default package */
/* compiled from: PG */
final class cut implements Runnable {
    final /* synthetic */ cuu a;
    private final Runnable b;

    public cut(cuu cuu, Runnable runnable) {
        this.a = cuu;
        this.b = runnable;
    }

    public final void run() {
        try {
            this.b.run();
        } finally {
            this.a.a();
        }
    }
}
