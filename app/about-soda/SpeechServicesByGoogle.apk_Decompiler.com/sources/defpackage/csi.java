package defpackage;

/* renamed from: csi  reason: default package */
/* compiled from: PG */
public final class csi extends Exception {
    public final csh a;
    public final int b = 0;
    public final int c;

    public csi(kml kml) {
        super((String) kml.c, (Throwable) kml.d);
        this.a = (csh) kml.b;
        this.c = kml.a;
    }

    public static kml a() {
        kml kml = new kml((byte[]) null);
        kml.a = 2;
        return kml;
    }
}
