package j$.time;

abstract /* synthetic */ class m {
    static final /* synthetic */ int[] a;

    /* JADX WARNING: Can't wrap try/catch for region: R(6:0|1|2|3|4|6) */
    /* JADX WARNING: Code restructure failed: missing block: B:7:?, code lost:
        return;
     */
    /* JADX WARNING: Failed to process nested try/catch */
    /* JADX WARNING: Missing exception handler attribute for start block: B:3:0x0012 */
    static {
        /*
            j$.time.temporal.a[] r0 = j$.time.temporal.a.values()
            int r0 = r0.length
            int[] r0 = new int[r0]
            a = r0
            j$.time.temporal.a r1 = j$.time.temporal.a.DAY_OF_MONTH     // Catch:{ NoSuchFieldError -> 0x0012 }
            int r1 = r1.ordinal()     // Catch:{ NoSuchFieldError -> 0x0012 }
            r2 = 1
            r0[r1] = r2     // Catch:{ NoSuchFieldError -> 0x0012 }
        L_0x0012:
            int[] r0 = a     // Catch:{ NoSuchFieldError -> 0x001d }
            j$.time.temporal.a r1 = j$.time.temporal.a.MONTH_OF_YEAR     // Catch:{ NoSuchFieldError -> 0x001d }
            int r1 = r1.ordinal()     // Catch:{ NoSuchFieldError -> 0x001d }
            r2 = 2
            r0[r1] = r2     // Catch:{ NoSuchFieldError -> 0x001d }
        L_0x001d:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: j$.time.m.<clinit>():void");
    }
}
