package defpackage;

import android.database.Cursor;
import androidx.work.impl.WorkDatabase;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;
import java.util.UUID;

/* renamed from: bii  reason: default package */
/* compiled from: PG */
public final class bii extends jnv implements jna {
    final /* synthetic */ byw a;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bii(byw byw) {
        super(1);
        this.a = byw;
    }

    /* JADX INFO: finally extract failed */
    /* JADX WARNING: type inference failed for: r5v0, types: [java.util.Collection, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v24, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v8, types: [java.util.Collection, java.lang.Object] */
    public final /* synthetic */ Object a(Object obj) {
        bat bat;
        boolean z;
        Iterator it;
        ArrayList arrayList;
        bbw bbw;
        bbw bbw2;
        long j;
        boolean z2;
        String str;
        bbx bbx;
        bat bat2;
        long j2;
        long j3;
        long j4;
        int i;
        bak bak;
        long j5;
        long j6;
        int i2;
        int i3;
        int i4;
        long j7;
        int i5;
        bbl o;
        bie r;
        boolean z3;
        boolean z4;
        boolean z5;
        boolean z6;
        long j8;
        int i6;
        WorkDatabase workDatabase = (WorkDatabase) obj;
        jnu.e(workDatabase, "db");
        String str2 = bhe.a;
        bgk w = workDatabase.w();
        ArrayList arrayList2 = new ArrayList();
        StringBuilder sb = new StringBuilder("SELECT * FROM workspec");
        byw byw = this.a;
        if (!byw.a.isEmpty()) {
            sb.append(" WHERE id IN (SELECT work_spec_id FROM worktag WHERE tag IN (");
            int size = byw.a.size();
            if (size > 0) {
                ArrayList arrayList3 = new ArrayList(size);
                for (int i7 = 0; i7 < size; i7++) {
                    arrayList3.add("?");
                }
                sb.append(jji.M(arrayList3, ",", (CharSequence) null, (CharSequence) null, (jna) null, 62));
            }
            sb.append("))");
            arrayList2.addAll(byw.a);
        }
        sb.append(";");
        awk awk = new awk(sb.toString(), arrayList2.toArray(new Object[0]));
        bgm bgm = (bgm) w;
        bgm.a.k();
        Cursor f = vy.f(bgm.a, awk, true);
        try {
            int g = vy.g(f, "id");
            int g2 = vy.g(f, "state");
            int g3 = vy.g(f, "output");
            int g4 = vy.g(f, "initial_delay");
            int g5 = vy.g(f, "interval_duration");
            int g6 = vy.g(f, "flex_duration");
            int g7 = vy.g(f, "run_attempt_count");
            int g8 = vy.g(f, "backoff_policy");
            int g9 = vy.g(f, "backoff_delay_duration");
            int g10 = vy.g(f, "last_enqueue_time");
            int g11 = vy.g(f, "period_count");
            int g12 = vy.g(f, "generation");
            int g13 = vy.g(f, "next_schedule_time_override");
            int g14 = vy.g(f, "stop_reason");
            int g15 = vy.g(f, "required_network_type");
            int g16 = vy.g(f, "required_network_request");
            int g17 = vy.g(f, "requires_charging");
            int g18 = vy.g(f, "requires_device_idle");
            int g19 = vy.g(f, "requires_battery_not_low");
            int g20 = vy.g(f, "requires_storage_not_low");
            int g21 = vy.g(f, "trigger_content_update_delay");
            int g22 = vy.g(f, "trigger_max_content_delay");
            int g23 = vy.g(f, "content_uri_triggers");
            HashMap hashMap = new HashMap();
            int i8 = g13;
            HashMap hashMap2 = new HashMap();
            while (f.moveToNext()) {
                int i9 = g12;
                String string = f.getString(g);
                if (!hashMap.containsKey(string)) {
                    i6 = g11;
                    hashMap.put(string, new ArrayList());
                } else {
                    i6 = g11;
                }
                String string2 = f.getString(g);
                if (!hashMap2.containsKey(string2)) {
                    hashMap2.put(string2, new ArrayList());
                }
                g12 = i9;
                g11 = i6;
            }
            int i10 = g12;
            int i11 = g11;
            int i12 = -1;
            f.moveToPosition(-1);
            ((bgm) w).b(hashMap);
            ((bgm) w).a(hashMap2);
            ArrayList arrayList4 = new ArrayList(f.getCount());
            while (true) {
                Set set = null;
                long j9 = 0;
                if (!f.moveToNext()) {
                    break;
                }
                if (g == i12) {
                    str = null;
                } else {
                    str = f.getString(g);
                }
                if (g2 == i12) {
                    bbx = null;
                } else {
                    bbx = xm.q(f.getInt(g2));
                }
                if (g3 == i12) {
                    bat2 = null;
                } else {
                    bat2 = bat.a(f.getBlob(g3));
                }
                if (g4 == i12) {
                    j2 = 0;
                } else {
                    j2 = f.getLong(g4);
                }
                if (g5 == i12) {
                    j3 = 0;
                } else {
                    j3 = f.getLong(g5);
                }
                if (g6 == i12) {
                    j4 = 0;
                } else {
                    j4 = f.getLong(g6);
                }
                if (g7 == i12) {
                    i = 0;
                } else {
                    i = f.getInt(g7);
                }
                if (g8 == i12) {
                    bak = null;
                } else {
                    bak = xm.n(f.getInt(g8));
                }
                if (g9 == i12) {
                    j5 = 0;
                } else {
                    j5 = f.getLong(g9);
                }
                if (g10 == i12) {
                    i2 = i11;
                    j6 = 0;
                } else {
                    j6 = f.getLong(g10);
                    i2 = i11;
                }
                if (i2 == i12) {
                    i3 = 0;
                } else {
                    i3 = f.getInt(i2);
                }
                int i13 = i10;
                int i14 = g2;
                int i15 = i13;
                if (i15 == i12) {
                    i4 = 0;
                } else {
                    i4 = f.getInt(i15);
                }
                int i16 = i8;
                int i17 = i15;
                int i18 = i16;
                if (i18 == i12) {
                    j7 = 0;
                } else {
                    j7 = f.getLong(i18);
                }
                int i19 = g14;
                int i20 = i18;
                int i21 = i19;
                if (i21 == i12) {
                    i5 = 0;
                } else {
                    i5 = f.getInt(i21);
                }
                int i22 = g15;
                int i23 = i21;
                int i24 = i22;
                if (i24 == i12) {
                    o = null;
                } else {
                    o = xm.o(f.getInt(i24));
                }
                int i25 = g16;
                int i26 = i24;
                int i27 = i25;
                if (i27 == i12) {
                    r = null;
                } else {
                    r = xm.r(f.getBlob(i27));
                }
                int i28 = g17;
                int i29 = i27;
                int i30 = i28;
                if (i30 == i12 || f.getInt(i30) == 0) {
                    z3 = false;
                } else {
                    z3 = true;
                }
                int i31 = g18;
                int i32 = i30;
                int i33 = i31;
                if (i33 == i12 || f.getInt(i33) == 0) {
                    z4 = false;
                } else {
                    z4 = true;
                }
                int i34 = g19;
                int i35 = i33;
                int i36 = i34;
                if (i36 == i12 || f.getInt(i36) == 0) {
                    z5 = false;
                } else {
                    z5 = true;
                }
                int i37 = g20;
                int i38 = i36;
                int i39 = i37;
                if (i39 == i12 || f.getInt(i39) == 0) {
                    z6 = false;
                } else {
                    z6 = true;
                }
                int i40 = g21;
                int i41 = i39;
                int i42 = i40;
                if (i42 == i12) {
                    j8 = 0;
                } else {
                    j8 = f.getLong(i42);
                }
                int i43 = g22;
                int i44 = i42;
                int i45 = i43;
                if (i45 != i12) {
                    j9 = f.getLong(i45);
                }
                long j10 = j9;
                int i46 = g23;
                int i47 = i45;
                int i48 = i46;
                if (i48 != i12) {
                    set = xm.s(f.getBlob(i48));
                }
                arrayList4.add(new bhd(str, bbx, bat2, j2, j3, j4, new baq(r, o, z3, z4, z5, z6, j8, j10, set), i, bak, j5, j6, i3, i4, j7, i5, (ArrayList) hashMap.get(f.getString(g)), (ArrayList) hashMap2.get(f.getString(g))));
                i11 = i2;
                i12 = -1;
                int i49 = i47;
                g23 = i48;
                g2 = i14;
                i10 = i17;
                i8 = i20;
                g14 = i23;
                g15 = i26;
                g16 = i29;
                g17 = i32;
                g18 = i35;
                g19 = i38;
                g20 = i41;
                g21 = i44;
                g22 = i49;
            }
            f.close();
            ArrayList arrayList5 = new ArrayList(jji.K(arrayList4));
            Iterator it2 = arrayList4.iterator();
            while (it2.hasNext()) {
                bhd bhd = (bhd) it2.next();
                if (!bhd.q.isEmpty()) {
                    bat = (bat) bhd.q.get(0);
                } else {
                    bat = bat.a;
                }
                bat bat3 = bat;
                UUID fromString = UUID.fromString(bhd.a);
                jnu.d(fromString, "fromString(id)");
                bbx bbx2 = bhd.b;
                HashSet hashSet = new HashSet(bhd.p);
                bat bat4 = bhd.c;
                int i50 = bhd.h;
                int i51 = bhd.m;
                baq baq = bhd.g;
                long j11 = bhd.d;
                long j12 = bhd.e;
                int i52 = (j12 > 0 ? 1 : (j12 == 0 ? 0 : -1));
                if (i52 != 0) {
                    z = false;
                } else {
                    z = true;
                }
                if (i52 != 0) {
                    it = it2;
                    arrayList = arrayList5;
                    bbw = new bbw(j12, bhd.f);
                } else {
                    it = it2;
                    arrayList = arrayList5;
                    bbw = null;
                }
                bbx bbx3 = bhd.b;
                bbx bbx4 = bbx.ENQUEUED;
                if (bbx3 == bbx4) {
                    if (bbx3 != bbx4 || i50 <= 0) {
                        z2 = false;
                    } else {
                        z2 = true;
                    }
                    bbw2 = bbw;
                    j = xm.w(z2, i50, bhd.i, bhd.j, bhd.k, bhd.l, !z, j11, bhd.f, j12, bhd.n);
                } else {
                    bbw2 = bbw;
                    j = Long.MAX_VALUE;
                }
                bby bby = r5;
                bby bby2 = new bby(fromString, bbx2, hashSet, bat4, bat3, i50, i51, baq, j11, bbw2, j, bhd.o);
                ArrayList arrayList6 = arrayList;
                arrayList6.add(bby);
                arrayList5 = arrayList6;
                it2 = it;
            }
            ArrayList arrayList7 = arrayList5;
            jnu.d(arrayList7, "WORK_INFO_MAPPER.apply(d…(querySpec.toRawQuery()))");
            return arrayList7;
        } catch (Throwable th) {
            f.close();
            throw th;
        }
    }
}
