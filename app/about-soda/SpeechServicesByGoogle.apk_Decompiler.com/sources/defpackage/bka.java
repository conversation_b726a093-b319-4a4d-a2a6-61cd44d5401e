package defpackage;

import com.android.car.ui.pluginsupport.OemApiUtil;
import dalvik.system.PathClassLoader;
import java.util.regex.Pattern;

/* renamed from: bka  reason: default package */
/* compiled from: PG */
final class bka extends PathClassLoader {
    private static final Pattern a;
    private static final Pattern b = Pattern.compile("^com\\.android\\.car\\.ui\\.plugin\\.(oemapis\\..*|PluginVersionProviderImpl)$");
    private final ClassLoader c;

    static {
        String quote = Pattern.quote(OemApiUtil.class.getName());
        String quote2 = Pattern.quote(bkb.class.getName());
        a = Pattern.compile("^com\\.android\\.car\\.ui\\..*AdapterV[0-9]+(\\$.*)?$|Lambda|^" + quote + "|^" + quote2 + "$");
    }

    public bka(String str, String str2, ClassLoader classLoader, ClassLoader classLoader2) {
        super(str, str2, classLoader);
        this.c = classLoader2;
    }

    /* access modifiers changed from: protected */
    /* JADX WARNING: Can't wrap try/catch for region: R(3:23|24|25) */
    /* JADX WARNING: Code restructure failed: missing block: B:26:0x0047, code lost:
        r2 = move-exception;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:27:0x0048, code lost:
        if (r3 == null) goto L_0x004a;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:28:0x004a, code lost:
        throw r2;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:29:0x004b, code lost:
        throw r3;
     */
    /* JADX WARNING: Failed to process nested try/catch */
    /* JADX WARNING: Missing exception handler attribute for start block: B:23:0x003e */
    /* JADX WARNING: Removed duplicated region for block: B:20:0x0037 A[SYNTHETIC, Splitter:B:20:0x0037] */
    /* JADX WARNING: Removed duplicated region for block: B:23:0x003e A[SYNTHETIC, Splitter:B:23:0x003e] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Class loadClass(java.lang.String r2, boolean r3) {
        /*
            r1 = this;
            java.lang.Class r3 = r1.findLoadedClass(r2)
            if (r3 == 0) goto L_0x0007
            return r3
        L_0x0007:
            java.lang.Class<java.lang.Object> r3 = java.lang.Object.class
            java.lang.ClassLoader r3 = r3.getClassLoader()     // Catch:{ ClassNotFoundException -> 0x0012 }
            java.lang.Class r2 = r3.loadClass(r2)     // Catch:{ ClassNotFoundException -> 0x0012 }
            return r2
        L_0x0012:
            r3 = 0
            if (r2 == 0) goto L_0x0027
            java.util.regex.Pattern r0 = a
            java.util.regex.Matcher r0 = r0.matcher(r2)
            boolean r0 = r0.find()
            if (r0 == 0) goto L_0x0027
            java.lang.Class r2 = r1.findClass(r2)     // Catch:{ ClassNotFoundException -> 0x0026 }
            return r2
        L_0x0026:
            r3 = move-exception
        L_0x0027:
            java.lang.ClassLoader r0 = r1.c
            if (r0 == 0) goto L_0x003e
            java.util.regex.Pattern r0 = b
            java.util.regex.Matcher r0 = r0.matcher(r2)
            boolean r0 = r0.matches()
            if (r0 == 0) goto L_0x003e
            java.lang.ClassLoader r0 = r1.c     // Catch:{ ClassNotFoundException -> 0x003e }
            java.lang.Class r2 = r0.loadClass(r2)     // Catch:{ ClassNotFoundException -> 0x003e }
            return r2
        L_0x003e:
            java.lang.ClassLoader r0 = r1.getParent()     // Catch:{ ClassNotFoundException -> 0x0047 }
            java.lang.Class r2 = r0.loadClass(r2)     // Catch:{ ClassNotFoundException -> 0x0047 }
            return r2
        L_0x0047:
            r2 = move-exception
            if (r3 != 0) goto L_0x004b
            throw r2
        L_0x004b:
            throw r3
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bka.loadClass(java.lang.String, boolean):java.lang.Class");
    }
}
