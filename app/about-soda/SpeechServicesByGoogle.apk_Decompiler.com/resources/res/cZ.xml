<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="match_parent" android:layout_height="wrap_content" android:minHeight="?android:attr/listPreferredItemHeight" android:paddingStart="?android:attr/listPreferredItemPaddingStart" android:paddingEnd="?android:attr/listPreferredItemPaddingEnd">
    <LinearLayout android:orientation="horizontal" android:id="@+id/morphed_voice_entity" android:background="?android:attr/selectableItemBackground" android:focusable="true" android:layout_width="0dp" android:layout_height="match_parent" android:layout_weight="1">
        <RadioButton android:id="@+id/morphed_voice_entity_make_current" android:clickable="false" android:layout_width="wrap_content" android:layout_height="match_parent" android:paddingStart="10dp" android:paddingEnd="15dp"/>
        <LinearLayout android:gravity="center_vertical" android:layout_gravity="left|center_vertical|center_horizontal|center|start" android:orientation="vertical" android:id="@+id/morphed_voice_entity_text" android:layout_width="0dp" android:layout_height="match_parent" android:layout_weight="2">
            <TextView android:textAppearance="?android:attr/textAppearanceListItem" android:textColor="?android:attr/textColorAlertDialogListItem" android:gravity="left|center_vertical|center_horizontal|center|start" android:id="@+id/morphed_voice_entity_name" android:layout_width="wrap_content" android:layout_height="wrap_content" android:singleLine="false"/>
        </LinearLayout>
    </LinearLayout>
    <ImageView android:layout_gravity="end" android:id="@+id/morphed_voice_entity_play_button" android:background="?android:attr/selectableItemBackground" android:focusable="true" android:clickable="true" android:layout_width="wrap_content" android:layout_height="match_parent" android:src="@drawable/playIcon" android:tint="?android:attr/colorControlNormal" android:contentDescription="@string/play_sample" android:paddingStart="10dp" android:paddingEnd="10dp"/>
</LinearLayout>
