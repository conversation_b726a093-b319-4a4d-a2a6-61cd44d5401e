package defpackage;

/* renamed from: ejl  reason: default package */
/* compiled from: PG */
public final class ejl {
    public final dza a;
    public final int b;

    public ejl() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof ejl) {
            ejl ejl = (ejl) obj;
            if (!this.a.equals(ejl.a) || this.b != ejl.b) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return ((this.a.hashCode() ^ 1000003) * 1000003) ^ this.b;
    }

    public final String toString() {
        String obj = this.a.toString();
        return "AudioRequestClientData{audioRequestClient=" + obj + ", clientToken=" + this.b + "}";
    }

    public ejl(dza dza, int i) {
        this.a = dza;
        this.b = i;
    }
}
