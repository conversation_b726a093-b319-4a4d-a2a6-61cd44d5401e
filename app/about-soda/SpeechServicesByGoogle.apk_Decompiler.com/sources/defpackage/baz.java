package defpackage;

import android.app.Notification;

/* renamed from: baz  reason: default package */
/* compiled from: PG */
public final class baz {
    public final int a;
    public final int b;
    public final Notification c;

    public baz(int i, Notification notification, int i2) {
        this.a = i;
        this.c = notification;
        this.b = i2;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        baz baz = (baz) obj;
        if (this.a == baz.a && this.b == baz.b) {
            return this.c.equals(baz.c);
        }
        return false;
    }

    public final int hashCode() {
        return (((this.a * 31) + this.b) * 31) + this.c.hashCode();
    }

    public final String toString() {
        return "ForegroundInfo{mNotificationId=" + this.a + ", mForegroundServiceType=" + this.b + ", mNotification=" + this.c + '}';
    }
}
