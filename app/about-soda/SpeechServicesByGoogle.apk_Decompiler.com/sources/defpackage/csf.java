package defpackage;

/* renamed from: csf  reason: default package */
/* compiled from: PG */
public final class csf extends htq implements hvb {
    public static final csf d;
    private static volatile hvh e;
    public int a;
    public int b;
    public int c;

    static {
        csf csf = new csf();
        d = csf;
        htq.z(csf.class, csf);
    }

    private csf() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(d, "\u0001\u0002\u0000\u0001\u0001\u0002\u0002\u0000\u0000\u0000\u0001᠌\u0000\u0002᠌\u0001", new Object[]{"a", "b", bqk.k, "c", bqk.j});
        } else if (i2 == 3) {
            return new csf();
        } else {
            if (i2 == 4) {
                return new htk((htq) d);
            }
            if (i2 == 5) {
                return d;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = e;
            if (hvh == null) {
                synchronized (csf.class) {
                    hvh = e;
                    if (hvh == null) {
                        hvh = new htl(d);
                        e = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
