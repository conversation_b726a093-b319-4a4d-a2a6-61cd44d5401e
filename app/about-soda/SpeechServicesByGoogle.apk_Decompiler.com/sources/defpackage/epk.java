package defpackage;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Executor;

/* renamed from: epk  reason: default package */
/* compiled from: PG */
public final class epk {
    private static final hca b = hca.m("com/google/android/libraries/search/audio/microphone/source/registry/HotwordSourceDataRegistry");
    public final cxi a;
    private final emr c;
    private final Executor d;
    private final jqs e;
    private final hll f;
    private final List g = new ArrayList();
    private final dou h;
    private final cxp i;
    private final cyw j;
    private final fnn k;
    private final bmu l;
    private final bmu m;
    private final byw n;

    public epk(bmu bmu, fnn fnn, emr emr, dou dou, cxp cxp, Executor executor, jqs jqs, cyw cyw, hll hll, bmu bmu2, cxi cxi, byw byw) {
        jnu.e(emr, "audioSessionToMicStateUpdater");
        jnu.e(executor, "lightweightExecutor");
        jnu.e(jqs, "lightweightScope");
        jnu.e(hll, "audioExecutionSequencer");
        jnu.e(byw, "sessionAudioStoreFactory");
        this.m = bmu;
        this.k = fnn;
        this.c = emr;
        this.h = dou;
        this.i = cxp;
        this.d = executor;
        this.e = jqs;
        this.j = cyw;
        this.f = hll;
        this.l = bmu2;
        this.a = cxi;
        this.n = byw;
    }

    public final synchronized ebk a(int i2, eam eam) {
        Object obj;
        jnu.e(eam, "reason");
        Iterator it = this.g.iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            if (((epi) obj).c.a == i2) {
                break;
            }
        }
        epi epi = (epi) obj;
        if (epi == null) {
            ((hby) b.f().h(hdg.a, "ALT.HotwordSrcDataRegy").j("com/google/android/libraries/search/audio/microphone/source/registry/HotwordSourceDataRegistry", "stopHotwordSource$java_com_google_android_libraries_search_audio_microphone_source_registry_hotword_source_data_registry", 306, "HotwordSourceDataRegistry.kt")).x("#audio# no hotword source session(%d) to stop(%s), skipping", i2, eam.name());
            return eki.h(eag.FAILED_CLOSING_NO_AUDIO_SOURCE, eam);
        }
        jji.N(this.g, new eoh(i2, 5));
        ((hby) b.f().h(hdg.a, "ALT.HotwordSrcDataRegy").j("com/google/android/libraries/search/audio/microphone/source/registry/HotwordSourceDataRegistry", "markStopped", 322, "HotwordSourceDataRegistry.kt")).A("#audio# stopping(%s) hotword source session(%d)", eam.name(), epi.c.a);
        hme b2 = this.f.b(gof.c(new ejj(epi, eam, 3)), this.d);
        jnu.d(b2, "submitAsync(...)");
        return new ekc(b2, eam, this.d);
    }

    /* JADX WARNING: type inference failed for: r3v14, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r4v18, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r4v22, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r2v17 */
    /* JADX WARNING: type inference failed for: r3v16, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r4v29, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r1v10, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r3v25, types: [java.lang.Object, jqs] */
    /* JADX WARNING: type inference failed for: r3v35, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r3v37, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r3v51, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r7v15, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r11v19, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r23v1, types: [eno] */
    /* JADX WARNING: type inference failed for: r11v21, types: [eng] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final synchronized defpackage.ebl b(defpackage.epp r33, defpackage.epl r34) {
        /*
            r32 = this;
            r8 = r32
            r0 = r33
            r9 = r34
            java.lang.String r1 = "Route("
            monitor-enter(r32)
            hca r2 = b     // Catch:{ all -> 0x04dd }
            hco r3 = r2.f()     // Catch:{ all -> 0x04dd }
            hcr r4 = defpackage.hdg.a     // Catch:{ all -> 0x04dd }
            java.lang.String r5 = "ALT.HotwordSrcDataRegy"
            hco r3 = r3.h(r4, r5)     // Catch:{ all -> 0x04dd }
            java.lang.String r4 = "HotwordSourceDataRegistry.kt"
            java.lang.String r5 = "com/google/android/libraries/search/audio/microphone/source/registry/HotwordSourceDataRegistry"
            java.lang.String r6 = "startHotwordSessionSource$java_com_google_android_libraries_search_audio_microphone_source_registry_hotword_source_data_registry"
            r7 = 102(0x66, float:1.43E-43)
            hco r3 = r3.j(r5, r6, r7, r4)     // Catch:{ all -> 0x04dd }
            hby r3 = (defpackage.hby) r3     // Catch:{ all -> 0x04dd }
            int r4 = r9.a     // Catch:{ all -> 0x04dd }
            java.lang.Integer r4 = java.lang.Integer.valueOf(r4)     // Catch:{ all -> 0x04dd }
            java.lang.String r5 = r33.a()     // Catch:{ all -> 0x04dd }
            ebn r6 = r9.b     // Catch:{ all -> 0x04dd }
            ehg r6 = r6.f     // Catch:{ all -> 0x04dd }
            if (r6 != 0) goto L_0x0037
            ehg r6 = defpackage.ehg.c     // Catch:{ all -> 0x04dd }
        L_0x0037:
            java.lang.String r7 = "getClientInfo(...)"
            defpackage.jnu.d(r6, r7)     // Catch:{ all -> 0x04dd }
            java.lang.String r6 = defpackage.fbi.s(r6)     // Catch:{ all -> 0x04dd }
            java.lang.String r7 = "#audio# starting hotword source session(%d) on %s for %s"
            r3.G(r7, r4, r5, r6)     // Catch:{ all -> 0x04dd }
            hco r2 = r2.f()     // Catch:{ all -> 0x04dd }
            hcr r3 = defpackage.hdg.a     // Catch:{ all -> 0x04dd }
            java.lang.String r4 = "ALT.HotwordSrcDataRegy"
            hco r2 = r2.h(r3, r4)     // Catch:{ all -> 0x04dd }
            java.lang.String r3 = "HotwordSourceDataRegistry.kt"
            java.lang.String r4 = "com/google/android/libraries/search/audio/microphone/source/registry/HotwordSourceDataRegistry"
            java.lang.String r5 = "enforceConcurrencyStateOnNewHotwordSource"
            r6 = 177(0xb1, float:2.48E-43)
            hco r2 = r2.j(r4, r5, r6, r3)     // Catch:{ all -> 0x04dd }
            hby r2 = (defpackage.hby) r2     // Catch:{ all -> 0x04dd }
            java.lang.String r3 = "#audio# enforcing concurrency state on a new hotword source"
            r2.r(r3)     // Catch:{ all -> 0x04dd }
            java.util.List r2 = r8.g     // Catch:{ all -> 0x04dd }
            java.lang.Object r2 = defpackage.jji.u(r2)     // Catch:{ all -> 0x04dd }
            epi r2 = (defpackage.epi) r2     // Catch:{ all -> 0x04dd }
            if (r2 == 0) goto L_0x0077
            epl r2 = r2.c     // Catch:{ all -> 0x04dd }
            int r2 = r2.a     // Catch:{ all -> 0x04dd }
            eam r3 = defpackage.eam.UNEXPECTED_AUDIO_SOURCE_OPENED     // Catch:{ all -> 0x04dd }
            r8.a(r2, r3)     // Catch:{ all -> 0x04dd }
        L_0x0077:
            ejn r2 = r0.b     // Catch:{ all -> 0x04dd }
            dzq r2 = r2.b     // Catch:{ all -> 0x04dd }
            int r3 = r2.a     // Catch:{ all -> 0x04dd }
            dzp r3 = defpackage.dzp.a(r3)     // Catch:{ all -> 0x04dd }
            int r3 = r3.ordinal()     // Catch:{ all -> 0x04dd }
            r5 = 3
            r10 = 1
            r6 = 2
            switch(r3) {
                case 0: goto L_0x04be;
                case 1: goto L_0x014f;
                case 2: goto L_0x014f;
                case 3: goto L_0x008f;
                case 4: goto L_0x04be;
                case 5: goto L_0x04be;
                case 6: goto L_0x04be;
                case 7: goto L_0x04be;
                case 8: goto L_0x014f;
                default: goto L_0x008b;
            }     // Catch:{ all -> 0x04dd }
        L_0x008b:
            jjq r0 = new jjq     // Catch:{ all -> 0x04dd }
            goto L_0x04d9
        L_0x008f:
            cxp r1 = r8.i     // Catch:{ all -> 0x04dd }
            ebn r3 = r9.b     // Catch:{ all -> 0x04dd }
            ehg r3 = r3.f     // Catch:{ all -> 0x04dd }
            if (r3 != 0) goto L_0x0099
            ehg r3 = defpackage.ehg.c     // Catch:{ all -> 0x04dd }
        L_0x0099:
            r12 = r3
            gqd r13 = defpackage.gqd.a     // Catch:{ all -> 0x04dd }
            int r14 = r9.a     // Catch:{ all -> 0x04dd }
            dou r3 = r8.h     // Catch:{ all -> 0x04dd }
            ejn r7 = r0.b     // Catch:{ all -> 0x04dd }
            ecg r11 = defpackage.ecg.BISTO     // Catch:{ all -> 0x04dd }
            eck r15 = r3.d(r11, r7)     // Catch:{ all -> 0x04dd }
            int r3 = r2.a     // Catch:{ all -> 0x04dd }
            r7 = 4
            if (r3 != r7) goto L_0x00b2
            java.lang.Object r2 = r2.b     // Catch:{ all -> 0x04dd }
            dzr r2 = (defpackage.dzr) r2     // Catch:{ all -> 0x04dd }
            goto L_0x00b4
        L_0x00b2:
            dzr r2 = defpackage.dzr.d     // Catch:{ all -> 0x04dd }
        L_0x00b4:
            java.lang.String r3 = "getBistoAudioRoute(...)"
            defpackage.jnu.d(r2, r3)     // Catch:{ all -> 0x04dd }
            ebn r3 = r9.b     // Catch:{ all -> 0x04dd }
            java.lang.String r7 = "<this>"
            defpackage.jnu.e(r2, r7)     // Catch:{ all -> 0x04dd }
            java.lang.String r7 = "hotwordMicInputParams"
            defpackage.jnu.e(r3, r7)     // Catch:{ all -> 0x04dd }
            int r7 = r2.a     // Catch:{ all -> 0x04dd }
            r7 = r7 & r6
            if (r7 == 0) goto L_0x00e5
            ead r2 = r2.c     // Catch:{ all -> 0x04dd }
            if (r2 != 0) goto L_0x00d0
            ead r2 = defpackage.ead.c     // Catch:{ all -> 0x04dd }
        L_0x00d0:
            java.lang.String r7 = "getPrecachedBufferId(...)"
            defpackage.jnu.d(r2, r7)     // Catch:{ all -> 0x04dd }
            dyt r3 = r3.d     // Catch:{ all -> 0x04dd }
            if (r3 != 0) goto L_0x00db
            dyt r3 = defpackage.dyt.l     // Catch:{ all -> 0x04dd }
        L_0x00db:
            java.lang.String r7 = "getAudioLibInputParams(...)"
            defpackage.jnu.d(r3, r7)     // Catch:{ all -> 0x04dd }
            ech r2 = defpackage.doe.l(r2, r3)     // Catch:{ all -> 0x04dd }
            goto L_0x00fc
        L_0x00e5:
            ebs r2 = defpackage.dlh.A(r2)     // Catch:{ all -> 0x04dd }
            grh r2 = defpackage.grh.g(r2)     // Catch:{ all -> 0x04dd }
            dyt r3 = r3.d     // Catch:{ all -> 0x04dd }
            if (r3 != 0) goto L_0x00f3
            dyt r3 = defpackage.dyt.l     // Catch:{ all -> 0x04dd }
        L_0x00f3:
            java.lang.String r7 = "getAudioLibInputParams(...)"
            defpackage.jnu.d(r3, r7)     // Catch:{ all -> 0x04dd }
            ech r2 = defpackage.doe.n(r2, r3)     // Catch:{ all -> 0x04dd }
        L_0x00fc:
            r16 = r2
            int r2 = r9.a     // Catch:{ all -> 0x04dd }
            java.lang.Integer r2 = java.lang.Integer.valueOf(r2)     // Catch:{ all -> 0x04dd }
            grh r17 = defpackage.grh.g(r2)     // Catch:{ all -> 0x04dd }
            eng r2 = new eng     // Catch:{ all -> 0x04dd }
            r12.getClass()     // Catch:{ all -> 0x04dd }
            r16.getClass()     // Catch:{ all -> 0x04dd }
            java.lang.Object r3 = r1.c     // Catch:{ all -> 0x04dd }
            java.lang.Object r7 = r1.a     // Catch:{ all -> 0x04dd }
            java.lang.Object r3 = r3.b()     // Catch:{ all -> 0x04dd }
            java.lang.Object r7 = r7.b()     // Catch:{ all -> 0x04dd }
            r19 = r7
            jqs r19 = (defpackage.jqs) r19     // Catch:{ all -> 0x04dd }
            r19.getClass()     // Catch:{ all -> 0x04dd }
            java.lang.Object r7 = r1.e     // Catch:{ all -> 0x04dd }
            java.lang.Object r11 = r1.b     // Catch:{ all -> 0x04dd }
            emu r7 = (defpackage.emu) r7     // Catch:{ all -> 0x04dd }
            emt r20 = r7.b()     // Catch:{ all -> 0x04dd }
            java.lang.Object r7 = r11.b()     // Catch:{ all -> 0x04dd }
            cqx r7 = (defpackage.cqx) r7     // Catch:{ all -> 0x04dd }
            r7.getClass()     // Catch:{ all -> 0x04dd }
            java.lang.Object r1 = r1.d     // Catch:{ all -> 0x04dd }
            iiv r1 = (defpackage.iiv) r1     // Catch:{ all -> 0x04dd }
            java.lang.Object r1 = r1.a     // Catch:{ all -> 0x04dd }
            r21 = r1
            bzj r21 = (defpackage.bzj) r21     // Catch:{ all -> 0x04dd }
            r21.getClass()     // Catch:{ all -> 0x04dd }
            r18 = r3
            dlv r18 = (defpackage.dlv) r18     // Catch:{ all -> 0x04dd }
            r11 = r2
            r11.<init>(r12, r13, r14, r15, r16, r17, r18, r19, r20, r21)     // Catch:{ all -> 0x04dd }
        L_0x014b:
            r18 = r2
            goto L_0x0322
        L_0x014f:
            fnn r1 = r8.k     // Catch:{ all -> 0x04dd }
            ebn r2 = r9.b     // Catch:{ all -> 0x04dd }
            int r3 = r9.a     // Catch:{ all -> 0x04dd }
            ejn r7 = r0.b     // Catch:{ all -> 0x04dd }
            elq r15 = new elq     // Catch:{ all -> 0x04dd }
            ehg r11 = r2.f     // Catch:{ all -> 0x04dd }
            if (r11 != 0) goto L_0x015f
            ehg r11 = defpackage.ehg.c     // Catch:{ all -> 0x04dd }
        L_0x015f:
            r12 = r11
            java.lang.String r11 = "getClientInfo(...)"
            defpackage.jnu.d(r12, r11)     // Catch:{ all -> 0x04dd }
            int r11 = r0.a     // Catch:{ all -> 0x04dd }
            int r13 = r9.a     // Catch:{ all -> 0x04dd }
            java.lang.Integer r14 = java.lang.Integer.valueOf(r11)     // Catch:{ all -> 0x04dd }
            java.lang.Integer r16 = java.lang.Integer.valueOf(r13)     // Catch:{ all -> 0x04dd }
            r17 = 0
            r18 = 8
            r11 = r15
            r13 = r14
            r14 = r16
            r22 = r15
            r15 = r17
            r16 = r18
            r11.<init>(r12, r13, r14, r15, r16)     // Catch:{ all -> 0x04dd }
            dyt r11 = r2.d     // Catch:{ all -> 0x04dd }
            if (r11 != 0) goto L_0x0188
            dyt r11 = defpackage.dyt.l     // Catch:{ all -> 0x04dd }
        L_0x0188:
            int r11 = r11.a     // Catch:{ all -> 0x04dd }
            r11 = r11 & 256(0x100, float:3.59E-43)
            if (r11 == 0) goto L_0x019e
            ekw r11 = new ekw     // Catch:{ all -> 0x04dd }
            dyt r12 = r2.d     // Catch:{ all -> 0x04dd }
            if (r12 != 0) goto L_0x0196
            dyt r12 = defpackage.dyt.l     // Catch:{ all -> 0x04dd }
        L_0x0196:
            r11.<init>(r12)     // Catch:{ all -> 0x04dd }
            grh r11 = defpackage.grh.h(r11)     // Catch:{ all -> 0x04dd }
            goto L_0x01a0
        L_0x019e:
            gqd r11 = defpackage.gqd.a     // Catch:{ all -> 0x04dd }
        L_0x01a0:
            java.lang.Object r12 = r1.c     // Catch:{ all -> 0x04dd }
            dyt r13 = r2.d     // Catch:{ all -> 0x04dd }
            if (r13 != 0) goto L_0x01a8
            dyt r13 = defpackage.dyt.l     // Catch:{ all -> 0x04dd }
        L_0x01a8:
            gxl r14 = new gxl     // Catch:{ all -> 0x04dd }
            r14.<init>()     // Catch:{ all -> 0x04dd }
            java.lang.Object r15 = r1.b     // Catch:{ all -> 0x04dd }
            dyt r4 = r2.d     // Catch:{ all -> 0x04dd }
            if (r4 != 0) goto L_0x01b5
            dyt r4 = defpackage.dyt.l     // Catch:{ all -> 0x04dd }
        L_0x01b5:
            bzl r15 = (defpackage.bzl) r15     // Catch:{ all -> 0x04dd }
            eds r4 = r15.o(r4)     // Catch:{ all -> 0x04dd }
            r14.h(r4)     // Catch:{ all -> 0x04dd }
            java.lang.Object r4 = r1.a     // Catch:{ all -> 0x04dd }
            dyt r15 = r2.d     // Catch:{ all -> 0x04dd }
            if (r15 != 0) goto L_0x01c6
            dyt r15 = defpackage.dyt.l     // Catch:{ all -> 0x04dd }
        L_0x01c6:
            dsy r4 = (defpackage.dsy) r4     // Catch:{ all -> 0x04dd }
            edo r4 = r4.b(r15)     // Catch:{ all -> 0x04dd }
            r14.h(r4)     // Catch:{ all -> 0x04dd }
            gxq r4 = r14.g()     // Catch:{ all -> 0x04dd }
            dou r12 = (defpackage.dou) r12     // Catch:{ all -> 0x04dd }
            efo r26 = r12.f(r13, r11, r4)     // Catch:{ all -> 0x04dd }
            java.lang.Object r4 = r1.d     // Catch:{ all -> 0x04dd }
            dzq r12 = r7.b     // Catch:{ all -> 0x04dd }
            r13 = r4
            efc r13 = (defpackage.efc) r13     // Catch:{ all -> 0x04dd }
            eeh r13 = r13.b()     // Catch:{ all -> 0x04dd }
            if (r13 != 0) goto L_0x0227
            int r14 = r2.a     // Catch:{ all -> 0x04dd }
            r14 = r14 & r6
            if (r14 == 0) goto L_0x01ff
            boolean r14 = r2.e     // Catch:{ all -> 0x04dd }
            if (r14 == 0) goto L_0x01ff
            r13 = r4
            efc r13 = (defpackage.efc) r13     // Catch:{ all -> 0x04dd }
            cxk r13 = r13.d     // Catch:{ all -> 0x04dd }
            dyt r14 = r2.d     // Catch:{ all -> 0x04dd }
            if (r14 != 0) goto L_0x01fa
            dyt r14 = defpackage.dyt.l     // Catch:{ all -> 0x04dd }
        L_0x01fa:
            eeu r13 = r13.h(r14)     // Catch:{ all -> 0x04dd }
            goto L_0x0227
        L_0x01ff:
            int r14 = r2.b     // Catch:{ all -> 0x04dd }
            r15 = 13
            if (r14 != r15) goto L_0x020a
            java.lang.Object r14 = r2.c     // Catch:{ all -> 0x04dd }
            ebx r14 = (defpackage.ebx) r14     // Catch:{ all -> 0x04dd }
            goto L_0x020c
        L_0x020a:
            ebx r14 = defpackage.ebx.c     // Catch:{ all -> 0x04dd }
        L_0x020c:
            int r14 = r14.a     // Catch:{ all -> 0x04dd }
            r14 = r14 & r10
            if (r14 == 0) goto L_0x0227
            r13 = r4
            efc r13 = (defpackage.efc) r13     // Catch:{ all -> 0x04dd }
            cxk r13 = r13.c     // Catch:{ all -> 0x04dd }
            int r14 = r2.b     // Catch:{ all -> 0x04dd }
            if (r14 != r15) goto L_0x021f
            java.lang.Object r14 = r2.c     // Catch:{ all -> 0x04dd }
            ebx r14 = (defpackage.ebx) r14     // Catch:{ all -> 0x04dd }
            goto L_0x0221
        L_0x021f:
            ebx r14 = defpackage.ebx.c     // Catch:{ all -> 0x04dd }
        L_0x0221:
            java.lang.String r14 = r14.b     // Catch:{ all -> 0x04dd }
            eff r13 = r13.g(r14)     // Catch:{ all -> 0x04dd }
        L_0x0227:
            if (r13 == 0) goto L_0x022e
            defpackage.efc.a(r11)     // Catch:{ all -> 0x04dd }
            goto L_0x02bb
        L_0x022e:
            efc r4 = (defpackage.efc) r4     // Catch:{ all -> 0x04dd }
            eez r4 = r4.b     // Catch:{ all -> 0x04dd }
            eeq r13 = defpackage.eeq.j     // Catch:{ all -> 0x04dd }
            htk r13 = r13.l()     // Catch:{ all -> 0x04dd }
            dyt r14 = r2.d     // Catch:{ all -> 0x04dd }
            if (r14 != 0) goto L_0x023e
            dyt r14 = defpackage.dyt.l     // Catch:{ all -> 0x04dd }
        L_0x023e:
            htq r15 = r13.b     // Catch:{ all -> 0x04dd }
            boolean r15 = r15.B()     // Catch:{ all -> 0x04dd }
            if (r15 != 0) goto L_0x0249
            r13.u()     // Catch:{ all -> 0x04dd }
        L_0x0249:
            htq r15 = r13.b     // Catch:{ all -> 0x04dd }
            eeq r15 = (defpackage.eeq) r15     // Catch:{ all -> 0x04dd }
            r14.getClass()     // Catch:{ all -> 0x04dd }
            r15.d = r14     // Catch:{ all -> 0x04dd }
            int r14 = r15.a     // Catch:{ all -> 0x04dd }
            r14 = r14 | r10
            r15.a = r14     // Catch:{ all -> 0x04dd }
            int r14 = r12.a     // Catch:{ all -> 0x04dd }
            if (r14 != r5) goto L_0x0260
            java.lang.Object r14 = r12.b     // Catch:{ all -> 0x04dd }
            dzy r14 = (defpackage.dzy) r14     // Catch:{ all -> 0x04dd }
            goto L_0x0262
        L_0x0260:
            dzy r14 = defpackage.dzy.c     // Catch:{ all -> 0x04dd }
        L_0x0262:
            int r14 = r14.a     // Catch:{ all -> 0x04dd }
            r14 = r14 & r10
            if (r14 == 0) goto L_0x028a
            int r14 = r12.a     // Catch:{ all -> 0x04dd }
            if (r14 != r5) goto L_0x0270
            java.lang.Object r12 = r12.b     // Catch:{ all -> 0x04dd }
            dzy r12 = (defpackage.dzy) r12     // Catch:{ all -> 0x04dd }
            goto L_0x0272
        L_0x0270:
            dzy r12 = defpackage.dzy.c     // Catch:{ all -> 0x04dd }
        L_0x0272:
            int r12 = r12.b     // Catch:{ all -> 0x04dd }
            htq r14 = r13.b     // Catch:{ all -> 0x04dd }
            boolean r14 = r14.B()     // Catch:{ all -> 0x04dd }
            if (r14 != 0) goto L_0x027f
            r13.u()     // Catch:{ all -> 0x04dd }
        L_0x027f:
            htq r14 = r13.b     // Catch:{ all -> 0x04dd }
            eeq r14 = (defpackage.eeq) r14     // Catch:{ all -> 0x04dd }
            int r15 = r14.a     // Catch:{ all -> 0x04dd }
            r15 = r15 | r6
            r14.a = r15     // Catch:{ all -> 0x04dd }
            r14.e = r12     // Catch:{ all -> 0x04dd }
        L_0x028a:
            int r12 = r2.b     // Catch:{ all -> 0x04dd }
            r14 = 9
            if (r12 != r14) goto L_0x02af
            java.lang.Object r12 = r2.c     // Catch:{ all -> 0x04dd }
            ebq r12 = (defpackage.ebq) r12     // Catch:{ all -> 0x04dd }
            htq r14 = r13.b     // Catch:{ all -> 0x04dd }
            boolean r14 = r14.B()     // Catch:{ all -> 0x04dd }
            if (r14 != 0) goto L_0x029f
            r13.u()     // Catch:{ all -> 0x04dd }
        L_0x029f:
            htq r14 = r13.b     // Catch:{ all -> 0x04dd }
            eeq r14 = (defpackage.eeq) r14     // Catch:{ all -> 0x04dd }
            r12.getClass()     // Catch:{ all -> 0x04dd }
            r14.h = r12     // Catch:{ all -> 0x04dd }
            int r12 = r14.a     // Catch:{ all -> 0x04dd }
            r15 = 16
            r12 = r12 | r15
            r14.a = r12     // Catch:{ all -> 0x04dd }
        L_0x02af:
            htq r12 = r13.r()     // Catch:{ all -> 0x04dd }
            eeq r12 = (defpackage.eeq) r12     // Catch:{ all -> 0x04dd }
            r13 = r22
            eeh r13 = r4.a(r12, r11, r13)     // Catch:{ all -> 0x04dd }
        L_0x02bb:
            efh r25 = defpackage.dom.k(r7, r13)     // Catch:{ all -> 0x04dd }
            java.lang.Object r1 = r1.e     // Catch:{ all -> 0x04dd }
            ehg r4 = r2.f     // Catch:{ all -> 0x04dd }
            if (r4 != 0) goto L_0x02c7
            ehg r4 = defpackage.ehg.c     // Catch:{ all -> 0x04dd }
        L_0x02c7:
            r24 = r4
            dyt r2 = r2.d     // Catch:{ all -> 0x04dd }
            if (r2 != 0) goto L_0x02cf
            dyt r2 = defpackage.dyt.l     // Catch:{ all -> 0x04dd }
        L_0x02cf:
            r27 = r2
            java.lang.Integer r2 = java.lang.Integer.valueOf(r3)     // Catch:{ all -> 0x04dd }
            grh r28 = defpackage.grh.h(r2)     // Catch:{ all -> 0x04dd }
            eno r2 = new eno     // Catch:{ all -> 0x04dd }
            r24.getClass()     // Catch:{ all -> 0x04dd }
            r27.getClass()     // Catch:{ all -> 0x04dd }
            r3 = r1
            cyw r3 = (defpackage.cyw) r3     // Catch:{ all -> 0x04dd }
            java.lang.Object r3 = r3.d     // Catch:{ all -> 0x04dd }
            r4 = r1
            cyw r4 = (defpackage.cyw) r4     // Catch:{ all -> 0x04dd }
            java.lang.Object r4 = r4.a     // Catch:{ all -> 0x04dd }
            java.lang.Object r3 = r3.b()     // Catch:{ all -> 0x04dd }
            java.lang.Object r4 = r4.b()     // Catch:{ all -> 0x04dd }
            r30 = r4
            jqs r30 = (defpackage.jqs) r30     // Catch:{ all -> 0x04dd }
            r30.getClass()     // Catch:{ all -> 0x04dd }
            r4 = r1
            cyw r4 = (defpackage.cyw) r4     // Catch:{ all -> 0x04dd }
            java.lang.Object r4 = r4.b     // Catch:{ all -> 0x04dd }
            java.lang.Object r4 = r4.b()     // Catch:{ all -> 0x04dd }
            cqx r4 = (defpackage.cqx) r4     // Catch:{ all -> 0x04dd }
            r4.getClass()     // Catch:{ all -> 0x04dd }
            cyw r1 = (defpackage.cyw) r1     // Catch:{ all -> 0x04dd }
            java.lang.Object r1 = r1.c     // Catch:{ all -> 0x04dd }
            iiv r1 = (defpackage.iiv) r1     // Catch:{ all -> 0x04dd }
            java.lang.Object r1 = r1.a     // Catch:{ all -> 0x04dd }
            r31 = r1
            bzj r31 = (defpackage.bzj) r31     // Catch:{ all -> 0x04dd }
            r31.getClass()     // Catch:{ all -> 0x04dd }
            r29 = r3
            dlv r29 = (defpackage.dlv) r29     // Catch:{ all -> 0x04dd }
            r23 = r2
            r23.<init>(r24, r25, r26, r27, r28, r29, r30, r31)     // Catch:{ all -> 0x04dd }
            goto L_0x014b
        L_0x0322:
            bmu r1 = r8.l     // Catch:{ all -> 0x04dd }
            int r2 = r9.a     // Catch:{ all -> 0x04dd }
            java.lang.Integer r2 = java.lang.Integer.valueOf(r2)     // Catch:{ all -> 0x04dd }
            grh r19 = defpackage.grh.g(r2)     // Catch:{ all -> 0x04dd }
            eny r2 = new eny     // Catch:{ all -> 0x04dd }
            java.lang.Object r3 = r1.c     // Catch:{ all -> 0x04dd }
            java.lang.Object r3 = r3.b()     // Catch:{ all -> 0x04dd }
            java.lang.Object r4 = r1.a     // Catch:{ all -> 0x04dd }
            java.lang.Object r4 = r4.b()     // Catch:{ all -> 0x04dd }
            r21 = r4
            eks r21 = (defpackage.eks) r21     // Catch:{ all -> 0x04dd }
            r21.getClass()     // Catch:{ all -> 0x04dd }
            java.lang.Object r1 = r1.b     // Catch:{ all -> 0x04dd }
            java.lang.Object r1 = r1.b()     // Catch:{ all -> 0x04dd }
            r22 = r1
            jqs r22 = (defpackage.jqs) r22     // Catch:{ all -> 0x04dd }
            r22.getClass()     // Catch:{ all -> 0x04dd }
            r20 = r3
            dlv r20 = (defpackage.dlv) r20     // Catch:{ all -> 0x04dd }
            r17 = r2
            r17.<init>(r18, r19, r20, r21, r22)     // Catch:{ all -> 0x04dd }
            hll r1 = r8.f     // Catch:{ all -> 0x04dd }
            cpw r3 = new cpw     // Catch:{ all -> 0x04dd }
            r4 = 16
            r3.<init>(r2, r4)     // Catch:{ all -> 0x04dd }
            hkn r3 = defpackage.gof.c(r3)     // Catch:{ all -> 0x04dd }
            java.util.concurrent.Executor r4 = r8.d     // Catch:{ all -> 0x04dd }
            hme r1 = r1.b(r3, r4)     // Catch:{ all -> 0x04dd }
            java.lang.String r3 = "submitAsync(...)"
            defpackage.jnu.d(r1, r3)     // Catch:{ all -> 0x04dd }
            java.lang.String r3 = "result"
            defpackage.jnu.e(r1, r3)     // Catch:{ all -> 0x04dd }
            wl r3 = defpackage.wl.p     // Catch:{ all -> 0x04dd }
            ejr r4 = new ejr     // Catch:{ all -> 0x04dd }
            r4.<init>(r3, r6)     // Catch:{ all -> 0x04dd }
            hld r3 = defpackage.hld.a     // Catch:{ all -> 0x04dd }
            hme r1 = defpackage.ftd.K(r1, r4, r3)     // Catch:{ all -> 0x04dd }
            cyw r3 = r8.j     // Catch:{ all -> 0x04dd }
            ebn r4 = r9.b     // Catch:{ all -> 0x04dd }
            ehg r4 = r4.f     // Catch:{ all -> 0x04dd }
            if (r4 != 0) goto L_0x038d
            ehg r4 = defpackage.ehg.c     // Catch:{ all -> 0x04dd }
        L_0x038d:
            java.lang.String r6 = "getClientInfo(...)"
            defpackage.jnu.d(r4, r6)     // Catch:{ all -> 0x04dd }
            eej r6 = r2.i     // Catch:{ all -> 0x04dd }
            ejn r7 = r0.b     // Catch:{ all -> 0x04dd }
            hme r13 = r6.b()     // Catch:{ all -> 0x04dd }
            dzq r6 = r7.b     // Catch:{ all -> 0x04dd }
            java.lang.String r7 = "clientInfo"
            defpackage.jnu.e(r4, r7)     // Catch:{ all -> 0x04dd }
            java.lang.Object r7 = r3.d     // Catch:{ all -> 0x04dd }
            emd r7 = (defpackage.emd) r7     // Catch:{ all -> 0x04dd }
            r7.f(r4, r1, r6)     // Catch:{ all -> 0x04dd }
            java.lang.String r6 = "HOTWORD"
            r3.v(r6, r4, r13, r1)     // Catch:{ all -> 0x04dd }
            ely r4 = new ely     // Catch:{ all -> 0x04dd }
            r15 = 0
            r16 = 0
            r11 = r4
            r12 = r3
            r14 = r1
            r11.<init>(r12, r13, r14, r15, r16)     // Catch:{ all -> 0x04dd }
            java.lang.Object r3 = r3.c     // Catch:{ all -> 0x04dd }
            r6 = 0
            defpackage.job.S(r3, r6, r6, r4, r5)     // Catch:{ all -> 0x04dd }
            epm r11 = new epm     // Catch:{ all -> 0x04dd }
            r11.<init>(r2, r1)     // Catch:{ all -> 0x04dd }
            jqs r1 = r8.e     // Catch:{ all -> 0x04dd }
            hme r2 = r11.b     // Catch:{ all -> 0x04dd }
            java.util.concurrent.Executor r3 = r8.d     // Catch:{ all -> 0x04dd }
            bub r4 = new bub     // Catch:{ all -> 0x04dd }
            r5 = 17
            r4.<init>(r5)     // Catch:{ all -> 0x04dd }
            hko r4 = defpackage.gof.d(r4)     // Catch:{ all -> 0x04dd }
            java.lang.Class<java.lang.Throwable> r5 = java.lang.Throwable.class
            hme r2 = defpackage.hjl.g(r2, r5, r4, r3)     // Catch:{ all -> 0x04dd }
            edc r3 = new edc     // Catch:{ all -> 0x04dd }
            r4 = 15
            r3.<init>((defpackage.hme) r2, (defpackage.jlr) r6, (int) r4, (char[]) r6)     // Catch:{ all -> 0x04dd }
            jqz r3 = defpackage.job.R(r1, r3)     // Catch:{ all -> 0x04dd }
            mz r1 = new mz     // Catch:{ all -> 0x04dd }
            r1.<init>(r2, r4)     // Catch:{ all -> 0x04dd }
            r3.w(r1)     // Catch:{ all -> 0x04dd }
            enr r12 = r11.a     // Catch:{ all -> 0x04dd }
            r1 = r12
            eny r1 = (defpackage.eny) r1     // Catch:{ all -> 0x04dd }
            grh r1 = r1.g     // Catch:{ all -> 0x04dd }
            java.lang.Object r1 = r1.b()     // Catch:{ all -> 0x04dd }
            ens r1 = (defpackage.ens) r1     // Catch:{ all -> 0x04dd }
            hme r15 = r1.c     // Catch:{ all -> 0x04dd }
            jqs r13 = r8.e     // Catch:{ all -> 0x04dd }
            epj r14 = new epj     // Catch:{ all -> 0x04dd }
            r6 = 0
            r7 = 0
            r1 = r14
            r2 = r32
            r4 = r15
            r5 = r12
            r1.<init>((defpackage.epk) r2, (defpackage.jqz) r3, (defpackage.hme) r4, (defpackage.enr) r5, (defpackage.jlr) r6, (int) r7)     // Catch:{ all -> 0x04dd }
            hme r1 = defpackage.jqw.z(r13, r14)     // Catch:{ all -> 0x04dd }
            bmu r2 = r8.m     // Catch:{ all -> 0x04dd }
            int r14 = r9.a     // Catch:{ all -> 0x04dd }
            ebn r3 = r9.b     // Catch:{ all -> 0x04dd }
            ehg r4 = r3.f     // Catch:{ all -> 0x04dd }
            if (r4 != 0) goto L_0x041a
            ehg r4 = defpackage.ehg.c     // Catch:{ all -> 0x04dd }
        L_0x041a:
            dyt r3 = r3.d     // Catch:{ all -> 0x04dd }
            if (r3 != 0) goto L_0x0420
            dyt r3 = defpackage.dyt.l     // Catch:{ all -> 0x04dd }
        L_0x0420:
            java.lang.String r5 = "getAudioLibInputParams(...)"
            defpackage.jnu.d(r3, r5)     // Catch:{ all -> 0x04dd }
            eny r12 = (defpackage.eny) r12     // Catch:{ all -> 0x04dd }
            dyt r5 = r12.h     // Catch:{ all -> 0x04dd }
            dyt r16 = defpackage.eve.a(r3, r5)     // Catch:{ all -> 0x04dd }
            java.lang.String r3 = "result"
            defpackage.jnu.e(r1, r3)     // Catch:{ all -> 0x04dd }
            wl r3 = defpackage.wl.n     // Catch:{ all -> 0x04dd }
            ejr r5 = new ejr     // Catch:{ all -> 0x04dd }
            r5.<init>(r3, r10)     // Catch:{ all -> 0x04dd }
            hld r3 = defpackage.hld.a     // Catch:{ all -> 0x04dd }
            hme r17 = defpackage.ftd.K(r1, r5, r3)     // Catch:{ all -> 0x04dd }
            enb r1 = new enb     // Catch:{ all -> 0x04dd }
            r4.getClass()     // Catch:{ all -> 0x04dd }
            r16.getClass()     // Catch:{ all -> 0x04dd }
            r15.getClass()     // Catch:{ all -> 0x04dd }
            java.lang.Object r3 = r2.b     // Catch:{ all -> 0x04dd }
            java.lang.Object r3 = r3.b()     // Catch:{ all -> 0x04dd }
            r19 = r3
            cyw r19 = (defpackage.cyw) r19     // Catch:{ all -> 0x04dd }
            r19.getClass()     // Catch:{ all -> 0x04dd }
            java.lang.Object r3 = r2.c     // Catch:{ all -> 0x04dd }
            java.lang.Object r3 = r3.b()     // Catch:{ all -> 0x04dd }
            r20 = r3
            hmi r20 = (defpackage.hmi) r20     // Catch:{ all -> 0x04dd }
            r20.getClass()     // Catch:{ all -> 0x04dd }
            java.lang.Object r2 = r2.a     // Catch:{ all -> 0x04dd }
            emf r2 = (defpackage.emf) r2     // Catch:{ all -> 0x04dd }
            emd r21 = r2.b()     // Catch:{ all -> 0x04dd }
            r13 = r1
            r2 = r15
            r15 = r4
            r18 = r2
            r13.<init>(r14, r15, r16, r17, r18, r19, r20, r21)     // Catch:{ all -> 0x04dd }
            emr r2 = r8.c     // Catch:{ all -> 0x04dd }
            int r3 = r9.a     // Catch:{ all -> 0x04dd }
            enr r4 = r11.a     // Catch:{ all -> 0x04dd }
            eny r4 = (defpackage.eny) r4     // Catch:{ all -> 0x04dd }
            eej r4 = r4.i     // Catch:{ all -> 0x04dd }
            r2.i(r1, r3, r4)     // Catch:{ all -> 0x04dd }
            java.util.List r2 = r8.g     // Catch:{ all -> 0x04dd }
            enr r3 = r11.a     // Catch:{ all -> 0x04dd }
            epi r4 = new epi     // Catch:{ all -> 0x04dd }
            r4.<init>(r3, r0, r9)     // Catch:{ all -> 0x04dd }
            hme r12 = r1.b     // Catch:{ all -> 0x04dd }
            byw r9 = r8.n     // Catch:{ all -> 0x04dd }
            epl r0 = r4.c     // Catch:{ all -> 0x04dd }
            ebn r0 = r0.b     // Catch:{ all -> 0x04dd }
            ehg r0 = r0.f     // Catch:{ all -> 0x04dd }
            if (r0 != 0) goto L_0x0498
            ehg r0 = defpackage.ehg.c     // Catch:{ all -> 0x04dd }
        L_0x0498:
            r10 = r0
            java.lang.String r0 = "getClientInfo(...)"
            defpackage.jnu.d(r10, r0)     // Catch:{ all -> 0x04dd }
            enr r0 = r4.a     // Catch:{ all -> 0x04dd }
            enr r3 = r4.a     // Catch:{ all -> 0x04dd }
            epl r5 = r4.c     // Catch:{ all -> 0x04dd }
            eny r3 = (defpackage.eny) r3     // Catch:{ all -> 0x04dd }
            dyt r13 = r3.h     // Catch:{ all -> 0x04dd }
            eny r0 = (defpackage.eny) r0     // Catch:{ all -> 0x04dd }
            eej r0 = r0.i     // Catch:{ all -> 0x04dd }
            hme r11 = r0.b()     // Catch:{ all -> 0x04dd }
            int r14 = r5.a     // Catch:{ all -> 0x04dd }
            euz r0 = r9.K(r10, r11, r12, r13, r14)     // Catch:{ all -> 0x04dd }
            r0.c()     // Catch:{ all -> 0x04dd }
            r2.add(r4)     // Catch:{ all -> 0x04dd }
            monitor-exit(r32)
            return r1
        L_0x04be:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException     // Catch:{ all -> 0x04dd }
            java.lang.String r2 = defpackage.fbi.p(r2)     // Catch:{ all -> 0x04dd }
            java.lang.StringBuilder r3 = new java.lang.StringBuilder     // Catch:{ all -> 0x04dd }
            r3.<init>(r1)     // Catch:{ all -> 0x04dd }
            r3.append(r2)     // Catch:{ all -> 0x04dd }
            java.lang.String r1 = ") is unexpected"
            r3.append(r1)     // Catch:{ all -> 0x04dd }
            java.lang.String r1 = r3.toString()     // Catch:{ all -> 0x04dd }
            r0.<init>(r1)     // Catch:{ all -> 0x04dd }
            throw r0     // Catch:{ all -> 0x04dd }
        L_0x04d9:
            r0.<init>()     // Catch:{ all -> 0x04dd }
            throw r0     // Catch:{ all -> 0x04dd }
        L_0x04dd:
            r0 = move-exception
            monitor-exit(r32)     // Catch:{ all -> 0x04dd }
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.epk.b(epp, epl):ebl");
    }

    public final synchronized List c() {
        ArrayList arrayList;
        List<epi> list = this.g;
        arrayList = new ArrayList(jji.K(list));
        for (epi epi : list) {
            epl epl = epi.c;
            arrayList.add(new epn(epl.a, epl.b, epi.b, epi.a));
        }
        return arrayList;
    }

    public final synchronized void d(int i2) {
        Object obj;
        Iterator it = this.g.iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            if (((epi) obj).c.a == i2) {
                break;
            }
        }
        if (((epi) obj) == null) {
            ((hby) b.h().h(hdg.a, "ALT.HotwordSrcDataRegy").j("com/google/android/libraries/search/audio/microphone/source/registry/HotwordSourceDataRegistry", "handoverHotwordSource$java_com_google_android_libraries_search_audio_microphone_source_registry_hotword_source_data_registry", 256, "HotwordSourceDataRegistry.kt")).s("#audio# no hotword source session(%d) found for handover", i2);
            return;
        }
        jji.N(this.g, new eoh(i2, 4));
        ((hby) b.f().h(hdg.a, "ALT.HotwordSrcDataRegy").j("com/google/android/libraries/search/audio/microphone/source/registry/HotwordSourceDataRegistry", "handoverHotwordSource$java_com_google_android_libraries_search_audio_microphone_source_registry_hotword_source_data_registry", 260, "HotwordSourceDataRegistry.kt")).s("#audio# hotword source session(%d) has been handed off", i2);
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v22, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v6, resolved type: ens} */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final synchronized defpackage.ekf e(int r6, defpackage.eam r7) {
        /*
            r5 = this;
            monitor-enter(r5)
            java.lang.String r0 = "reason"
            defpackage.jnu.e(r7, r0)     // Catch:{ all -> 0x00c8 }
            java.util.List r0 = r5.g     // Catch:{ all -> 0x00c8 }
            java.util.Iterator r0 = r0.iterator()     // Catch:{ all -> 0x00c8 }
        L_0x000c:
            boolean r1 = r0.hasNext()     // Catch:{ all -> 0x00c8 }
            r2 = 0
            if (r1 == 0) goto L_0x0021
            java.lang.Object r1 = r0.next()     // Catch:{ all -> 0x00c8 }
            r3 = r1
            epi r3 = (defpackage.epi) r3     // Catch:{ all -> 0x00c8 }
            epl r3 = r3.c     // Catch:{ all -> 0x00c8 }
            int r3 = r3.a     // Catch:{ all -> 0x00c8 }
            if (r3 != r6) goto L_0x000c
            goto L_0x0022
        L_0x0021:
            r1 = r2
        L_0x0022:
            epi r1 = (defpackage.epi) r1     // Catch:{ all -> 0x00c8 }
            if (r1 == 0) goto L_0x0035
            enr r0 = r1.a     // Catch:{ all -> 0x00c8 }
            eny r0 = (defpackage.eny) r0     // Catch:{ all -> 0x00c8 }
            grh r0 = r0.g     // Catch:{ all -> 0x00c8 }
            if (r0 == 0) goto L_0x0035
            java.lang.Object r0 = r0.e()     // Catch:{ all -> 0x00c8 }
            r2 = r0
            ens r2 = (defpackage.ens) r2     // Catch:{ all -> 0x00c8 }
        L_0x0035:
            if (r2 != 0) goto L_0x0077
            hca r0 = b     // Catch:{ all -> 0x00c8 }
            hco r0 = r0.f()     // Catch:{ all -> 0x00c8 }
            hcr r1 = defpackage.hdg.a     // Catch:{ all -> 0x00c8 }
            java.lang.String r2 = "ALT.HotwordSrcDataRegy"
            hco r0 = r0.h(r1, r2)     // Catch:{ all -> 0x00c8 }
            java.lang.String r1 = "HotwordSourceDataRegistry.kt"
            java.lang.String r2 = "com/google/android/libraries/search/audio/microphone/source/registry/HotwordSourceDataRegistry"
            java.lang.String r3 = "stopHotwordSourceSeamlessly$java_com_google_android_libraries_search_audio_microphone_source_registry_hotword_source_data_registry"
            r4 = 277(0x115, float:3.88E-43)
            hco r0 = r0.j(r2, r3, r4, r1)     // Catch:{ all -> 0x00c8 }
            hby r0 = (defpackage.hby) r0     // Catch:{ all -> 0x00c8 }
            java.lang.String r1 = r7.name()     // Catch:{ all -> 0x00c8 }
            java.lang.String r2 = "#audio# no hotword source session(%d) to stop(%s) seamlessly, skipping"
            r0.x(r2, r6, r1)     // Catch:{ all -> 0x00c8 }
            eag r6 = defpackage.eag.FAILED_TO_KEEP_MIC_OPEN_FOR_SEAMLESS_HANDOVER     // Catch:{ all -> 0x00c8 }
            ebr r0 = defpackage.ebr.c     // Catch:{ all -> 0x00c8 }
            htk r0 = r0.l()     // Catch:{ all -> 0x00c8 }
            java.lang.String r1 = "newBuilder(...)"
            defpackage.jnu.d(r0, r1)     // Catch:{ all -> 0x00c8 }
            bzj r0 = defpackage.jnu.e(r0, "builder")     // Catch:{ all -> 0x00c8 }
            ebr r0 = r0.u()     // Catch:{ all -> 0x00c8 }
            ekf r6 = defpackage.eki.l(r6, r0, r7)     // Catch:{ all -> 0x00c8 }
            monitor-exit(r5)
            return r6
        L_0x0077:
            po r6 = r2.d     // Catch:{ all -> 0x00c8 }
            eai r0 = defpackage.eai.KEPT_OPEN_FOR_SEAMLESS_HANDOVER     // Catch:{ all -> 0x00c8 }
            eah r0 = defpackage.eki.e(r0)     // Catch:{ all -> 0x00c8 }
            ebp r0 = defpackage.eki.j(r0, r7)     // Catch:{ all -> 0x00c8 }
            r6.c(r0)     // Catch:{ all -> 0x00c8 }
            eai r6 = defpackage.eai.KEPT_OPEN_FOR_SEAMLESS_HANDOVER     // Catch:{ all -> 0x00c8 }
            ebr r0 = defpackage.ebr.c     // Catch:{ all -> 0x00c8 }
            htk r0 = r0.l()     // Catch:{ all -> 0x00c8 }
            java.lang.String r1 = "newBuilder(...)"
            defpackage.jnu.d(r0, r1)     // Catch:{ all -> 0x00c8 }
            bzj r0 = defpackage.jnu.e(r0, "builder")     // Catch:{ all -> 0x00c8 }
            java.lang.Object r1 = r0.a     // Catch:{ all -> 0x00c8 }
            r3 = r1
            htk r3 = (defpackage.htk) r3     // Catch:{ all -> 0x00c8 }
            htq r3 = r3.b     // Catch:{ all -> 0x00c8 }
            boolean r3 = r3.B()     // Catch:{ all -> 0x00c8 }
            if (r3 != 0) goto L_0x00aa
            r3 = r1
            htk r3 = (defpackage.htk) r3     // Catch:{ all -> 0x00c8 }
            r3.u()     // Catch:{ all -> 0x00c8 }
        L_0x00aa:
            int r2 = r2.b     // Catch:{ all -> 0x00c8 }
            htk r1 = (defpackage.htk) r1     // Catch:{ all -> 0x00c8 }
            htq r1 = r1.b     // Catch:{ all -> 0x00c8 }
            ebr r1 = (defpackage.ebr) r1     // Catch:{ all -> 0x00c8 }
            int r3 = r1.a     // Catch:{ all -> 0x00c8 }
            r3 = r3 | 1
            r1.a = r3     // Catch:{ all -> 0x00c8 }
            r1.b = r2     // Catch:{ all -> 0x00c8 }
            ebr r0 = r0.u()     // Catch:{ all -> 0x00c8 }
            eah r6 = defpackage.eki.e(r6)     // Catch:{ all -> 0x00c8 }
            ekf r6 = defpackage.eki.m(r6, r0, r7)     // Catch:{ all -> 0x00c8 }
            monitor-exit(r5)
            return r6
        L_0x00c8:
            r6 = move-exception
            monitor-exit(r5)     // Catch:{ all -> 0x00c8 }
            throw r6
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.epk.e(int, eam):ekf");
    }
}
