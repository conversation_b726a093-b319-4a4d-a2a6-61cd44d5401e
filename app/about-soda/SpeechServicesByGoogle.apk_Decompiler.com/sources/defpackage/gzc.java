package defpackage;

import java.util.ArrayDeque;
import java.util.Deque;
import java.util.Iterator;
import java.util.NoSuchElementException;

/* renamed from: gzc  reason: default package */
/* compiled from: PG */
final class gzc implements Iterator {
    private Iterator a;
    private Iterator b = gzb.a;
    private Iterator c;
    private Deque d;

    public gzc(Iterator it) {
        this.c = it;
    }

    public final boolean hasNext() {
        Iterator it;
        while (true) {
            Iterator it2 = this.b;
            fvf.aP(it2);
            if (it2.hasNext()) {
                return true;
            }
            while (true) {
                Iterator it3 = this.c;
                if (it3 != null && it3.hasNext()) {
                    it = this.c;
                    break;
                }
                Deque deque = this.d;
                if (deque == null || deque.isEmpty()) {
                    it = null;
                } else {
                    this.c = (Iterator) this.d.removeFirst();
                }
            }
            it = null;
            this.c = it;
            if (it == null) {
                return false;
            }
            Iterator it4 = (Iterator) it.next();
            this.b = it4;
            if (it4 instanceof gzc) {
                gzc gzc = (gzc) it4;
                this.b = gzc.b;
                if (this.d == null) {
                    this.d = new ArrayDeque();
                }
                this.d.addFirst(this.c);
                if (gzc.d != null) {
                    while (!gzc.d.isEmpty()) {
                        this.d.addFirst((Iterator) gzc.d.removeLast());
                    }
                }
                this.c = gzc.c;
            }
        }
    }

    public final Object next() {
        if (hasNext()) {
            Iterator it = this.b;
            this.a = it;
            return it.next();
        }
        throw new NoSuchElementException();
    }

    public final void remove() {
        Iterator it = this.a;
        if (it != null) {
            it.remove();
            this.a = null;
            return;
        }
        throw new IllegalStateException("no calls to next() since the last call to remove()");
    }
}
