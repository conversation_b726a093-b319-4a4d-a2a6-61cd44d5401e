package defpackage;

import android.content.Context;
import com.google.android.apps.speech.tts.googletts.dispatch.LanguageRegistry;
import com.google.android.apps.speech.tts.googletts.dispatch.VoicePolicyManager;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/* renamed from: bsa  reason: default package */
/* compiled from: PG */
public final class bsa {
    private static final hca c = hca.m("com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher");
    public final Map a = new HashMap();
    public final Map b = new HashMap();
    private final LanguageRegistry d;
    private final VoicePolicyManager e;
    private final btj f;
    private final bxc g;
    private final bvj h;

    public bsa(btj btj, bxc bxc, Context context) {
        this.f = btj;
        this.g = bxc;
        this.h = ((bry) ftd.k(context, bry.class)).L();
        this.e = (VoicePolicyManager) ((bry) ftd.k(context, bry.class)).x().b();
        this.d = ((bry) ftd.k(context, bry.class)).i();
        btj.c(new bvc(this, 1));
    }

    /* JADX WARNING: Removed duplicated region for block: B:40:0x0127  */
    /* JADX WARNING: Removed duplicated region for block: B:64:0x0114 A[SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    private final defpackage.bsk c(defpackage.brr r17, java.util.List r18, int r19) {
        /*
            r16 = this;
            r0 = r17
            r1 = r19
            java.util.Iterator r2 = r18.iterator()
            r3 = 0
        L_0x0009:
            boolean r4 = r2.hasNext()
            if (r4 == 0) goto L_0x0159
            java.lang.Object r4 = r2.next()
            brz r4 = (defpackage.brz) r4
            bsk r5 = r4.a
            boolean r5 = r5.v()
            if (r5 != 0) goto L_0x0155
            boolean r5 = r17.m()
            if (r5 == 0) goto L_0x0035
            java.lang.String r5 = r0.i
            if (r5 == 0) goto L_0x0035
            bsk r5 = r4.a
            java.lang.String r5 = r5.o()
            java.lang.String r6 = r0.i
            boolean r5 = r5.equals(r6)
            if (r5 == 0) goto L_0x0159
        L_0x0035:
            if (r1 != 0) goto L_0x003a
            bsk r0 = r4.a
            return r0
        L_0x003a:
            if (r3 != 0) goto L_0x0144
            bsk r5 = r4.a
            java.lang.Boolean r5 = r5.j()
            boolean r5 = r5.booleanValue()
            if (r5 != 0) goto L_0x0144
            r5 = r16
            btj r6 = r5.f
            bsk r7 = r4.a
            java.lang.String r8 = r0.j
            boolean r9 = r7.v()
            java.lang.String r10 = "Cannot initiate download for network voice"
            if (r9 != 0) goto L_0x013e
            java.lang.String r9 = r7.t()
            java.lang.String r11 = "Cannot download voice with no voicepack name"
            if (r9 == 0) goto L_0x0138
            r9 = 2
            r12 = 1
            if (r1 != r9) goto L_0x0094
            btf r13 = r6.e
            r13.g()
            java.util.List r13 = r13.m
            java.lang.String r14 = r7.t()
            bty r15 = r7.g()
            int r15 = r15.d
            java.lang.StringBuilder r9 = new java.lang.StringBuilder
            r9.<init>()
            r9.append(r14)
            java.lang.String r14 = "-r"
            r9.append(r14)
            r9.append(r15)
            java.lang.String r9 = r9.toString()
            boolean r9 = r13.contains(r9)
            if (r9 == 0) goto L_0x0094
            java.lang.String r6 = r6.a(r7)
            goto L_0x010b
        L_0x0094:
            btf r9 = r6.e
            bty r13 = r7.g()
            java.lang.String r13 = r13.b
            boolean r9 = r9.h(r13)
            java.lang.String r13 = "downloadAsync"
            java.lang.String r14 = "com/google/android/apps/speech/tts/googletts/local/voicepack/VoiceDataManager"
            java.lang.String r15 = "VoiceDataManager.java"
            if (r9 == 0) goto L_0x00c4
            hca r6 = defpackage.btj.a
            hco r6 = r6.f()
            hby r6 = (defpackage.hby) r6
            r8 = 239(0xef, float:3.35E-43)
            hco r6 = r6.j(r14, r13, r8, r15)
            hby r6 = (defpackage.hby) r6
            bty r7 = r7.g()
            java.lang.String r7 = r7.b
            java.lang.String r8 = "Already downloading %s, ignoring async download request..."
            r6.u(r8, r7)
            goto L_0x0109
        L_0x00c4:
            boolean r9 = r7.v()
            if (r9 != 0) goto L_0x0132
            java.lang.String r9 = r7.t()
            if (r9 == 0) goto L_0x012c
            java.lang.String r8 = defpackage.ftd.o(r8)
            hca r9 = defpackage.btj.a
            hco r9 = r9.f()
            hby r9 = (defpackage.hby) r9
            r10 = 255(0xff, float:3.57E-43)
            hco r9 = r9.j(r14, r13, r10, r15)
            hby r9 = (defpackage.hby) r9
            java.lang.String r10 = r7.t()
            java.lang.String r11 = "Explicitly adding voicepack to downloads: %s"
            r9.u(r11, r10)
            btf r9 = r6.e
            bsv r9 = r9.n
            java.lang.String r10 = r7.t()
            java.util.List r9 = r9.a
            r9.add(r10)
            btf r9 = r6.e
            bty r7 = r7.g()
            buu r10 = new buu
            r10.<init>(r6, r12)
            r6 = 3
            r9.j(r7, r6, r8, r10)
        L_0x0109:
            java.lang.String r6 = ""
        L_0x010b:
            if (r6 == 0) goto L_0x0127
            boolean r7 = r6.isEmpty()
            if (r7 == 0) goto L_0x0114
            goto L_0x0127
        L_0x0114:
            bsk r0 = r4.a
            bsj r0 = r0.c()
            java.lang.Boolean r1 = java.lang.Boolean.valueOf(r12)
            r0.h = r1
            r0.a = r6
            bsk r0 = r0.a()
            return r0
        L_0x0127:
            r6 = 2
            if (r1 == r6) goto L_0x0146
            r3 = r12
            goto L_0x0146
        L_0x012c:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            r0.<init>(r11)
            throw r0
        L_0x0132:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            r0.<init>(r10)
            throw r0
        L_0x0138:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            r0.<init>(r11)
            throw r0
        L_0x013e:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            r0.<init>(r10)
            throw r0
        L_0x0144:
            r5 = r16
        L_0x0146:
            bsk r6 = r4.a
            java.lang.Boolean r6 = r6.j()
            boolean r6 = r6.booleanValue()
            if (r6 == 0) goto L_0x0009
            bsk r0 = r4.a
            return r0
        L_0x0155:
            r5 = r16
            goto L_0x0009
        L_0x0159:
            r5 = r16
            r0 = 0
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bsa.c(brr, java.util.List, int):bsk");
    }

    private static final bsk d(brr brr, List list) {
        Iterator it = list.iterator();
        while (it.hasNext()) {
            brz brz = (brz) it.next();
            if (brz.a.v()) {
                if (!brr.m() || brr.i == null || brz.a.o().equals(brr.i)) {
                    return brz.a;
                }
                return null;
            }
        }
        return null;
    }

    public final Locale a(Locale locale) {
        Locale a2 = this.d.a(locale);
        if (a2 == null || a2.getLanguage().equals(locale.getLanguage())) {
            return locale;
        }
        ((hby) ((hby) c.f()).j("com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher", "redirectIfLanguageDifferent", 226, "RankedDispatcher.java")).C("Redirect from %s to %s", locale, a2);
        return a2;
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r15v5, resolved type: boolean} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v95, resolved type: boolean} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v21, resolved type: boolean} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r22v2, resolved type: boolean} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v137, resolved type: htq} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r15v11, resolved type: ife} */
    /* JADX WARNING: type inference failed for: r3v43, types: [java.util.Set, java.lang.Object] */
    /* JADX WARNING: Code restructure failed: missing block: B:95:0x039e, code lost:
        if (r9.d() != defpackage.btu.TTS_GOOGLE_ONLY_ALTERNATIVE) goto L_0x03a2;
     */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:101:0x03a9  */
    /* JADX WARNING: Removed duplicated region for block: B:102:0x03ab  */
    /* JADX WARNING: Removed duplicated region for block: B:104:0x03af  */
    /* JADX WARNING: Removed duplicated region for block: B:105:0x03b1  */
    /* JADX WARNING: Removed duplicated region for block: B:108:0x03bf  */
    /* JADX WARNING: Removed duplicated region for block: B:111:0x03c8  */
    /* JADX WARNING: Removed duplicated region for block: B:119:0x03e2  */
    /* JADX WARNING: Removed duplicated region for block: B:18:0x00dd A[Catch:{ hui -> 0x00c4 }] */
    /* JADX WARNING: Removed duplicated region for block: B:191:0x0616  */
    /* JADX WARNING: Removed duplicated region for block: B:19:0x00f9 A[Catch:{ hui -> 0x00c4 }] */
    /* JADX WARNING: Removed duplicated region for block: B:211:0x03e4 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:67:0x02a2  */
    /* JADX WARNING: Removed duplicated region for block: B:74:0x02fa  */
    /* JADX WARNING: Removed duplicated region for block: B:80:0x0359  */
    /* JADX WARNING: Removed duplicated region for block: B:81:0x035b  */
    /* JADX WARNING: Removed duplicated region for block: B:84:0x0360  */
    /* JADX WARNING: Removed duplicated region for block: B:85:0x0362  */
    /* JADX WARNING: Removed duplicated region for block: B:92:0x038e  */
    /* JADX WARNING: Removed duplicated region for block: B:98:0x03a4  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.dda b(defpackage.brr r27, int r28) {
        /*
            r26 = this;
            r1 = r26
            r2 = r27
            r3 = r28
            hca r0 = c
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            java.lang.String r4 = "RankedDispatcher.java"
            java.lang.String r5 = "com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher"
            java.lang.String r6 = "dispatchOnLanguage"
            r7 = 242(0xf2, float:3.39E-43)
            hco r0 = r0.j(r5, r6, r7, r4)
            hby r0 = (defpackage.hby) r0
            java.lang.String r4 = "Dispatch on locale: %s"
            java.util.Locale r5 = r27.d()
            r0.u(r4, r5)
            java.util.Map r4 = r1.a
            monitor-enter(r4)
            int r0 = r27.b()     // Catch:{ all -> 0x0673 }
            java.util.Map r5 = r1.a     // Catch:{ all -> 0x0673 }
            java.lang.Integer r6 = java.lang.Integer.valueOf(r0)     // Catch:{ all -> 0x0673 }
            java.lang.Object r0 = r5.get(r6)     // Catch:{ all -> 0x0673 }
            r5 = 169(0xa9, float:2.37E-43)
            r7 = 2
            r8 = 3
            r11 = 0
            if (r0 == 0) goto L_0x0047
            java.util.Map r0 = r1.a     // Catch:{ all -> 0x0673 }
            java.lang.Object r0 = r0.get(r6)     // Catch:{ all -> 0x0673 }
            java.util.Set r0 = (java.util.Set) r0     // Catch:{ all -> 0x0673 }
            goto L_0x0157
        L_0x0047:
            java.util.HashSet r12 = new java.util.HashSet     // Catch:{ all -> 0x0673 }
            btj r0 = r1.f     // Catch:{ all -> 0x0673 }
            java.util.List r0 = r0.b()     // Catch:{ all -> 0x0673 }
            r12.<init>(r0)     // Catch:{ all -> 0x0673 }
            com.google.android.apps.speech.tts.googletts.dispatch.VoicePolicyManager r13 = r1.e     // Catch:{ all -> 0x0673 }
            ife r0 = defpackage.ife.b     // Catch:{ all -> 0x0673 }
            htk r0 = r0.l()     // Catch:{ all -> 0x0673 }
            j$.util.stream.Stream r14 = j$.util.Collection.EL.stream(r12)     // Catch:{ all -> 0x0673 }
            bpf r15 = new bpf     // Catch:{ all -> 0x0673 }
            r15.<init>(r7)     // Catch:{ all -> 0x0673 }
            j$.util.stream.Stream r14 = r14.map(r15)     // Catch:{ all -> 0x0673 }
            j$.util.stream.Collector r15 = defpackage.gvx.b     // Catch:{ all -> 0x0673 }
            java.lang.Object r14 = r14.collect(r15)     // Catch:{ all -> 0x0673 }
            java.lang.Iterable r14 = (java.lang.Iterable) r14     // Catch:{ all -> 0x0673 }
            r0.U(r14)     // Catch:{ all -> 0x0673 }
            htq r0 = r0.r()     // Catch:{ all -> 0x0673 }
            ife r0 = (defpackage.ife) r0     // Catch:{ all -> 0x0673 }
            java.lang.String r0 = r2.j     // Catch:{ all -> 0x0673 }
            java.lang.String r0 = defpackage.ftd.o(r0)     // Catch:{ all -> 0x0673 }
            java.util.Locale r14 = r27.d()     // Catch:{ all -> 0x0673 }
            java.lang.String r14 = r14.toLanguageTag()     // Catch:{ all -> 0x0673 }
            r13.b()     // Catch:{ all -> 0x0673 }
            long r9 = r13.b     // Catch:{ all -> 0x0673 }
            r16 = 0
            int r16 = (r9 > r16 ? 1 : (r9 == r16 ? 0 : -1))
            if (r16 != 0) goto L_0x00ae
            hca r0 = com.google.android.apps.speech.tts.googletts.dispatch.VoicePolicyManager.a     // Catch:{ all -> 0x0673 }
            hco r0 = r0.h()     // Catch:{ all -> 0x0673 }
            hby r0 = (defpackage.hby) r0     // Catch:{ all -> 0x0673 }
            java.lang.String r9 = "VoicePolicyManager.java"
            java.lang.String r10 = "applyVoicePolicy"
            java.lang.String r14 = "com/google/android/apps/speech/tts/googletts/dispatch/VoicePolicyManager"
            r15 = 191(0xbf, float:2.68E-43)
            hco r0 = r0.j(r14, r10, r15, r9)     // Catch:{ all -> 0x0673 }
            hby r0 = (defpackage.hby) r0     // Catch:{ all -> 0x0673 }
            java.lang.String r9 = "Native voice policy not available."
            r0.r(r9)     // Catch:{ all -> 0x0673 }
        L_0x00ac:
            r15 = 0
            goto L_0x00db
        L_0x00ae:
            byte[] r0 = r13.nativeManagerApplyVoicePolicy(r9, r0, r14)     // Catch:{ all -> 0x0673 }
            hte r9 = defpackage.hte.a()     // Catch:{ hui -> 0x00c4 }
            ife r10 = defpackage.ife.b     // Catch:{ hui -> 0x00c4 }
            int r14 = r0.length     // Catch:{ hui -> 0x00c4 }
            htq r0 = defpackage.htq.o(r10, r0, r11, r14, r9)     // Catch:{ hui -> 0x00c4 }
            defpackage.htq.D(r0)     // Catch:{ hui -> 0x00c4 }
            r15 = r0
            ife r15 = (defpackage.ife) r15     // Catch:{ hui -> 0x00c4 }
            goto L_0x00db
        L_0x00c4:
            r0 = move-exception
            r23 = r0
            hca r0 = com.google.android.apps.speech.tts.googletts.dispatch.VoicePolicyManager.a     // Catch:{ all -> 0x0673 }
            hco r17 = r0.g()     // Catch:{ all -> 0x0673 }
            java.lang.String r22 = "VoicePolicyManager.java"
            java.lang.String r20 = "applyVoicePolicy"
            java.lang.String r19 = "com/google/android/apps/speech/tts/googletts/dispatch/VoicePolicyManager"
            java.lang.String r18 = "Could not parse voice proto"
            r21 = 203(0xcb, float:2.84E-43)
            ((defpackage.hby) ((defpackage.hby) ((defpackage.hby) r17).i(r23)).j(r19, r20, r21, r22)).r(r18)     // Catch:{ all -> 0x0673 }
            goto L_0x00ac
        L_0x00db:
            if (r15 != 0) goto L_0x00f9
            hca r0 = com.google.android.apps.speech.tts.googletts.dispatch.VoicePolicyManager.a     // Catch:{ all -> 0x0673 }
            hco r0 = r0.h()     // Catch:{ all -> 0x0673 }
            hby r0 = (defpackage.hby) r0     // Catch:{ all -> 0x0673 }
            java.lang.String r9 = "VoicePolicyManager.java"
            java.lang.String r10 = "applyVoicePolicy"
            java.lang.String r12 = "com/google/android/apps/speech/tts/googletts/dispatch/VoicePolicyManager"
            hco r0 = r0.j(r12, r10, r5, r9)     // Catch:{ all -> 0x0673 }
            hby r0 = (defpackage.hby) r0     // Catch:{ all -> 0x0673 }
            java.lang.String r9 = "Failed to apply native voice policy. Defaulting to all voices blocked."
            r0.r(r9)     // Catch:{ all -> 0x0673 }
            hau r0 = defpackage.hau.a     // Catch:{ all -> 0x0673 }
            goto L_0x0126
        L_0x00f9:
            huf r0 = r15.a     // Catch:{ all -> 0x0673 }
            j$.util.stream.Stream r0 = j$.util.Collection.EL.stream(r0)     // Catch:{ all -> 0x0673 }
            bpf r9 = new bpf     // Catch:{ all -> 0x0673 }
            r9.<init>(r8)     // Catch:{ all -> 0x0673 }
            j$.util.stream.Stream r0 = r0.map(r9)     // Catch:{ all -> 0x0673 }
            j$.util.stream.Collector r9 = defpackage.gvx.b     // Catch:{ all -> 0x0673 }
            java.lang.Object r0 = r0.collect(r9)     // Catch:{ all -> 0x0673 }
            gyo r0 = (defpackage.gyo) r0     // Catch:{ all -> 0x0673 }
            j$.util.stream.Stream r9 = j$.util.Collection.EL.stream(r12)     // Catch:{ all -> 0x0673 }
            bod r10 = new bod     // Catch:{ all -> 0x0673 }
            r12 = 7
            r10.<init>(r0, r12)     // Catch:{ all -> 0x0673 }
            j$.util.stream.Stream r0 = r9.filter(r10)     // Catch:{ all -> 0x0673 }
            j$.util.stream.Collector r9 = defpackage.gvx.b     // Catch:{ all -> 0x0673 }
            java.lang.Object r0 = r0.collect(r9)     // Catch:{ all -> 0x0673 }
            gyo r0 = (defpackage.gyo) r0     // Catch:{ all -> 0x0673 }
        L_0x0126:
            j$.util.stream.Stream r0 = j$.util.Collection.EL.stream(r0)     // Catch:{ all -> 0x0673 }
            bsg r9 = new bsg     // Catch:{ all -> 0x0673 }
            r10 = 1
            r9.<init>((com.google.android.apps.speech.tts.googletts.dispatch.VoicePolicyManager) r13, (defpackage.brr) r2, (int) r10)     // Catch:{ all -> 0x0673 }
            j$.util.stream.Stream r0 = r0.filter(r9)     // Catch:{ all -> 0x0673 }
            j$.util.stream.Collector r9 = defpackage.gvx.b     // Catch:{ all -> 0x0673 }
            java.lang.Object r0 = r0.collect(r9)     // Catch:{ all -> 0x0673 }
            gyo r0 = (defpackage.gyo) r0     // Catch:{ all -> 0x0673 }
            j$.util.stream.Stream r0 = j$.util.Collection.EL.stream(r0)     // Catch:{ all -> 0x0673 }
            bod r9 = new bod     // Catch:{ all -> 0x0673 }
            r10 = 4
            r9.<init>(r2, r10)     // Catch:{ all -> 0x0673 }
            j$.util.stream.Stream r0 = r0.filter(r9)     // Catch:{ all -> 0x0673 }
            j$.util.stream.Collector r9 = defpackage.gvx.b     // Catch:{ all -> 0x0673 }
            java.lang.Object r0 = r0.collect(r9)     // Catch:{ all -> 0x0673 }
            gyo r0 = (defpackage.gyo) r0     // Catch:{ all -> 0x0673 }
            java.util.Map r9 = r1.a     // Catch:{ all -> 0x0673 }
            r9.put(r6, r0)     // Catch:{ all -> 0x0673 }
        L_0x0157:
            monitor-exit(r4)     // Catch:{ all -> 0x0673 }
            defpackage.fvf.aP(r0)
            java.util.Map r6 = r1.b
            monitor-enter(r6)
            int r4 = r27.b()     // Catch:{ all -> 0x0670 }
            int r4 = r4 * 41
            java.util.Locale r9 = r27.d()     // Catch:{ all -> 0x0670 }
            int r9 = r9.hashCode()     // Catch:{ all -> 0x0670 }
            int r4 = r4 + r9
            int r4 = r4 * 41
            boolean r9 = r27.i()     // Catch:{ all -> 0x0670 }
            r10 = 1
            if (r10 == r9) goto L_0x0179
            r9 = 4457(0x1169, float:6.246E-42)
            goto L_0x017b
        L_0x0179:
            r9 = 4441(0x1159, float:6.223E-42)
        L_0x017b:
            int r4 = r4 + r9
            java.lang.String r9 = r2.m     // Catch:{ all -> 0x0670 }
            if (r9 == 0) goto L_0x0187
            int r4 = r4 * 41
            int r9 = r9.hashCode()     // Catch:{ all -> 0x0670 }
            int r4 = r4 + r9
        L_0x0187:
            int r4 = r4 * 41
            boolean r9 = r27.h()     // Catch:{ all -> 0x0670 }
            r10 = 1
            if (r10 == r9) goto L_0x0193
            r9 = 1999(0x7cf, float:2.801E-42)
            goto L_0x0195
        L_0x0193:
            r9 = 1993(0x7c9, float:2.793E-42)
        L_0x0195:
            int r4 = r4 + r9
            int r4 = r4 * 41
            if (r3 != 0) goto L_0x019d
            r9 = 3919(0xf4f, float:5.492E-42)
            goto L_0x019f
        L_0x019d:
            r9 = 3911(0xf47, float:5.48E-42)
        L_0x019f:
            java.util.Map r10 = r1.b     // Catch:{ all -> 0x0670 }
            int r4 = r4 + r9
            java.lang.Integer r4 = java.lang.Integer.valueOf(r4)     // Catch:{ all -> 0x0670 }
            java.lang.Object r9 = r10.get(r4)     // Catch:{ all -> 0x0670 }
            if (r9 == 0) goto L_0x01b6
            java.util.Map r0 = r1.b     // Catch:{ all -> 0x0670 }
            java.lang.Object r0 = r0.get(r4)     // Catch:{ all -> 0x0670 }
            java.util.Set r0 = (java.util.Set) r0     // Catch:{ all -> 0x0670 }
            goto L_0x025f
        L_0x01b6:
            java.util.HashSet r9 = new java.util.HashSet     // Catch:{ all -> 0x0670 }
            r9.<init>()     // Catch:{ all -> 0x0670 }
            java.util.Locale r10 = r27.d()     // Catch:{ all -> 0x0670 }
            java.util.Locale r10 = r1.a(r10)     // Catch:{ all -> 0x0670 }
            java.lang.String r12 = r10.getLanguage()     // Catch:{ all -> 0x0670 }
            r13 = 95
            r14 = 45
            java.lang.String r12 = r12.replace(r13, r14)     // Catch:{ all -> 0x0670 }
            java.util.Locale r12 = java.util.Locale.forLanguageTag(r12)     // Catch:{ all -> 0x0670 }
            boolean r13 = r27.i()     // Catch:{ all -> 0x0670 }
            if (r13 == 0) goto L_0x01f5
            hca r12 = c     // Catch:{ all -> 0x0670 }
            hco r12 = r12.c()     // Catch:{ all -> 0x0670 }
            hby r12 = (defpackage.hby) r12     // Catch:{ all -> 0x0670 }
            java.lang.String r13 = "com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher"
            java.lang.String r14 = "getAvailableVoicesForLanguages"
            java.lang.String r15 = "RankedDispatcher.java"
            r5 = 341(0x155, float:4.78E-43)
            hco r5 = r12.j(r13, r14, r5, r15)     // Catch:{ all -> 0x0670 }
            hby r5 = (defpackage.hby) r5     // Catch:{ all -> 0x0670 }
            java.lang.String r12 = "Inter-language fallback disabled, restricting voices to requested locale only."
            r5.r(r12)     // Catch:{ all -> 0x0670 }
            r12 = r10
        L_0x01f5:
            j$.util.stream.Stream r5 = j$.util.Collection.EL.stream(r0)     // Catch:{ all -> 0x0670 }
            bod r13 = new bod     // Catch:{ all -> 0x0670 }
            r14 = 5
            r13.<init>(r12, r14)     // Catch:{ all -> 0x0670 }
            j$.util.stream.Stream r5 = r5.filter(r13)     // Catch:{ all -> 0x0670 }
            j$.util.Objects.requireNonNull(r9)     // Catch:{ all -> 0x0670 }
            bme r12 = new bme     // Catch:{ all -> 0x0670 }
            r12.<init>(r9, r8)     // Catch:{ all -> 0x0670 }
            r5.forEachOrdered(r12)     // Catch:{ all -> 0x0670 }
            java.lang.String r5 = r2.m     // Catch:{ all -> 0x0670 }
            if (r5 == 0) goto L_0x022b
            j$.util.stream.Stream r12 = j$.util.Collection.EL.stream(r0)     // Catch:{ all -> 0x0670 }
            bod r13 = new bod     // Catch:{ all -> 0x0670 }
            r14 = 6
            r13.<init>(r5, r14)     // Catch:{ all -> 0x0670 }
            j$.util.stream.Stream r5 = r12.filter(r13)     // Catch:{ all -> 0x0670 }
            j$.util.Objects.requireNonNull(r9)     // Catch:{ all -> 0x0670 }
            bme r12 = new bme     // Catch:{ all -> 0x0670 }
            r12.<init>(r9, r8)     // Catch:{ all -> 0x0670 }
            r5.forEachOrdered(r12)     // Catch:{ all -> 0x0670 }
        L_0x022b:
            if (r3 == 0) goto L_0x0259
            boolean r5 = r27.h()     // Catch:{ all -> 0x0670 }
            if (r5 == 0) goto L_0x0259
            java.lang.String r5 = r10.getLanguage()     // Catch:{ all -> 0x0670 }
            java.lang.String r10 = "en"
            boolean r5 = r5.equals(r10)     // Catch:{ all -> 0x0670 }
            if (r5 != 0) goto L_0x0259
            j$.util.stream.Stream r0 = j$.util.Collection.EL.stream(r0)     // Catch:{ all -> 0x0670 }
            boa r5 = new boa     // Catch:{ all -> 0x0670 }
            r10 = 12
            r5.<init>(r10)     // Catch:{ all -> 0x0670 }
            j$.util.stream.Stream r0 = r0.filter(r5)     // Catch:{ all -> 0x0670 }
            j$.util.Objects.requireNonNull(r9)     // Catch:{ all -> 0x0670 }
            bme r5 = new bme     // Catch:{ all -> 0x0670 }
            r5.<init>(r9, r8)     // Catch:{ all -> 0x0670 }
            r0.forEachOrdered(r5)     // Catch:{ all -> 0x0670 }
        L_0x0259:
            java.util.Map r0 = r1.b     // Catch:{ all -> 0x0670 }
            r0.put(r4, r9)     // Catch:{ all -> 0x0670 }
            r0 = r9
        L_0x025f:
            monitor-exit(r6)     // Catch:{ all -> 0x0670 }
            defpackage.fvf.aP(r0)
            java.util.ArrayList r4 = new java.util.ArrayList
            int r5 = r0.size()
            r4.<init>(r5)
            java.util.Locale r5 = r27.d()
            java.util.HashMap r6 = new java.util.HashMap
            r6.<init>()
            java.lang.String r8 = r2.m
            java.util.Iterator r0 = r0.iterator()
        L_0x027b:
            boolean r9 = r0.hasNext()
            if (r9 == 0) goto L_0x03f5
            java.lang.Object r9 = r0.next()
            bsk r9 = (defpackage.bsk) r9
            boolean r10 = r27.g()
            if (r10 == 0) goto L_0x029f
            java.lang.String r10 = r2.i
            defpackage.fvf.aP(r10)
            java.lang.String r12 = r9.o()
            boolean r10 = r10.equals(r12)
            if (r10 == 0) goto L_0x029f
            r10 = 65536(0x10000, float:9.18355E-41)
            goto L_0x02a0
        L_0x029f:
            r10 = r11
        L_0x02a0:
            if (r8 == 0) goto L_0x02e0
            hca r12 = c
            hco r12 = r12.c()
            hby r12 = (defpackage.hby) r12
            java.lang.String r13 = "RankedDispatcher.java"
            java.lang.String r14 = "com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher"
            java.lang.String r15 = "scoreAndSortVoices"
            r7 = 419(0x1a3, float:5.87E-43)
            hco r7 = r12.j(r14, r15, r7, r13)
            hby r7 = (defpackage.hby) r7
            java.lang.String r12 = "Dispatching based on language detection / localespan"
            r7.r(r12)
            gxq r7 = r9.i()
            int r12 = r7.size()
            r13 = r11
        L_0x02c6:
            if (r13 >= r12) goto L_0x02e0
            java.lang.Object r14 = r7.get(r13)
            java.lang.String r14 = (java.lang.String) r14
            java.lang.String[] r14 = defpackage.brv.k(r14)
            r14 = r14[r11]
            boolean r14 = r14.equals(r8)
            int r13 = r13 + 1
            if (r14 == 0) goto L_0x02c6
            r7 = 32768(0x8000, float:4.5918E-41)
            int r10 = r10 + r7
        L_0x02e0:
            com.google.android.apps.speech.tts.googletts.dispatch.LanguageRegistry r7 = r1.d
            java.util.Locale r7 = r7.a(r5)
            gxq r12 = r9.i()
            int r13 = r12.size()
            r20 = r0
            r21 = r8
            r0 = r11
            r8 = r0
            r14 = r8
            r15 = r14
            r22 = r15
        L_0x02f8:
            if (r14 >= r13) goto L_0x0356
            java.lang.Object r23 = r12.get(r14)
            r24 = r12
            r12 = r23
            java.lang.String r12 = (java.lang.String) r12
            r23 = r13
            java.util.Locale r13 = defpackage.brv.g(r12)
            bvj r3 = r1.h
            java.lang.Object r3 = r3.a
            boolean r3 = r3.contains(r13)
            r11 = r11 | r3
            boolean r3 = defpackage.brv.i(r13, r5)
            r15 = r15 | r3
            java.lang.String r3 = r13.getLanguage()
            r25 = r11
            java.lang.String r11 = r5.getLanguage()
            boolean r3 = r3.equals(r11)
            r0 = r0 | r3
            java.lang.Object r3 = r6.get(r12)
            java.lang.String r3 = (java.lang.String) r3
            if (r3 != 0) goto L_0x033c
            bxc r3 = r1.g
            java.lang.String r11 = r13.toString()
            java.lang.String r3 = r3.b(r11)
            r6.put(r12, r3)
        L_0x033c:
            java.lang.String r11 = r9.o()
            boolean r3 = r11.equals(r3)
            r8 = r8 | r3
            boolean r3 = r13.equals(r7)
            r22 = r3 | r22
            int r14 = r14 + 1
            r3 = r28
            r13 = r23
            r12 = r24
            r11 = r25
            goto L_0x02f8
        L_0x0356:
            r3 = 1
            if (r3 == r15) goto L_0x035b
            r7 = 0
            goto L_0x035d
        L_0x035b:
            r7 = 16384(0x4000, float:2.2959E-41)
        L_0x035d:
            int r10 = r10 + r7
            if (r3 == r0) goto L_0x0362
            r0 = 0
            goto L_0x0364
        L_0x0362:
            r0 = 8192(0x2000, float:1.14794E-41)
        L_0x0364:
            java.util.List r3 = r2.k
            gxq r7 = r9.i()
            r12 = 0
            java.lang.Object r7 = r7.get(r12)
            java.lang.String r7 = (java.lang.String) r7
            java.util.Locale r13 = java.util.Locale.getDefault()
            java.lang.String r7 = r7.toLowerCase(r13)
            boolean r3 = r3.contains(r7)
            int r10 = r10 + r0
            if (r3 == 0) goto L_0x03a2
            boolean r0 = r27.f()
            if (r0 == 0) goto L_0x03a2
            btu r0 = r9.d()
            btu r3 = defpackage.btu.TTS_FIRST_PARTY_PREFERRED
            if (r0 == r3) goto L_0x03a0
            gyo r0 = defpackage.bzw.a
            btu r0 = r9.d()
            btu r3 = defpackage.btu.TTS_GOOGLE_ONLY_DEFAULT
            if (r0 == r3) goto L_0x03a0
            btu r0 = r9.d()
            btu r3 = defpackage.btu.TTS_GOOGLE_ONLY_ALTERNATIVE
            if (r0 != r3) goto L_0x03a2
        L_0x03a0:
            int r10 = r10 + 256
        L_0x03a2:
            if (r22 == 0) goto L_0x03a6
            int r10 = r10 + 128
        L_0x03a6:
            r3 = 1
            if (r3 == r11) goto L_0x03ab
            r0 = r12
            goto L_0x03ad
        L_0x03ab:
            r0 = 64
        L_0x03ad:
            if (r3 == r8) goto L_0x03b1
            r7 = r12
            goto L_0x03b3
        L_0x03b1:
            r7 = 16
        L_0x03b3:
            java.lang.Boolean r8 = r9.k()
            boolean r8 = r8.booleanValue()
            int r10 = r10 + r0
            int r10 = r10 + r7
            if (r8 == 0) goto L_0x03c1
            int r10 = r10 + 8
        L_0x03c1:
            int r0 = r9.u()
            r7 = 2
            if (r0 != r7) goto L_0x03ca
            int r10 = r10 + 4
        L_0x03ca:
            btu r0 = r9.d()
            btu r8 = defpackage.btu.TTS_GOOGLE_ONLY_DEFAULT
            if (r0 == r8) goto L_0x03da
            btu r0 = r9.d()
            btu r8 = defpackage.btu.TTS_UNRESTRICTED_DEFAULT
            if (r0 != r8) goto L_0x03dc
        L_0x03da:
            int r10 = r10 + 2
        L_0x03dc:
            boolean r0 = r9.v()
            if (r0 != 0) goto L_0x03e4
            int r10 = r10 + 1
        L_0x03e4:
            brz r0 = new brz
            r0.<init>(r9, r10)
            r4.add(r0)
            r3 = r28
            r11 = r12
            r0 = r20
            r8 = r21
            goto L_0x027b
        L_0x03f5:
            r12 = r11
            java.util.Comparator r0 = java.util.Collections.reverseOrder()
            java.util.Collections.sort(r4, r0)
            java.util.Iterator r0 = r4.iterator()
        L_0x0401:
            boolean r3 = r0.hasNext()
            if (r3 == 0) goto L_0x041c
            java.lang.Object r3 = r0.next()
            brz r3 = (defpackage.brz) r3
            bsk r5 = r3.a
            boolean r5 = r5.v()
            if (r5 != 0) goto L_0x0401
            bsk r0 = r3.a
            java.lang.String r0 = r0.o()
            goto L_0x041e
        L_0x041c:
            java.lang.String r0 = ""
        L_0x041e:
            int r3 = r4.size()
            r5 = r12
        L_0x0423:
            if (r5 >= r3) goto L_0x049c
            java.lang.Object r6 = r4.get(r5)
            brz r6 = (defpackage.brz) r6
            bsk r7 = r6.a
            java.lang.String r7 = r7.o()
            boolean r7 = r7.equals(r0)
            if (r7 != 0) goto L_0x0438
            goto L_0x0499
        L_0x0438:
            bsk r7 = r6.a
            btv r7 = r7.e()
            btv r8 = defpackage.btv.TYPE_DARWINN_JANEIRO
            if (r7 != r8) goto L_0x0448
            boolean r7 = defpackage.inq.d()
            if (r7 != 0) goto L_0x0458
        L_0x0448:
            bsk r7 = r6.a
            btv r7 = r7.e()
            btv r8 = defpackage.btv.TYPE_DARWINN_RIO
            if (r7 != r8) goto L_0x045e
            boolean r7 = defpackage.inq.e()
            if (r7 == 0) goto L_0x045e
        L_0x0458:
            int r7 = r6.b
            int r7 = r7 + 4096
            r6.b = r7
        L_0x045e:
            boolean r7 = defpackage.inq.c()
            if (r7 == 0) goto L_0x0467
            btw r7 = defpackage.btw.TYPE_LEMONBALM_HAVOC
            goto L_0x047b
        L_0x0467:
            boolean r7 = defpackage.ioa.c()
            if (r7 == 0) goto L_0x0470
            btw r7 = defpackage.btw.TYPE_SEANET
            goto L_0x047b
        L_0x0470:
            boolean r7 = defpackage.inu.c()
            if (r7 == 0) goto L_0x0479
            btw r7 = defpackage.btw.TYPE_LOCOMEL
            goto L_0x047b
        L_0x0479:
            btw r7 = defpackage.btw.TYPE_LSTM_CADENZA
        L_0x047b:
            bsk r8 = r6.a
            btw r8 = r8.f()
            if (r8 != r7) goto L_0x0489
            int r7 = r6.b
            int r7 = r7 + 2048
            r6.b = r7
        L_0x0489:
            bsk r7 = r6.a
            btw r7 = r7.f()
            btw r8 = defpackage.btw.TYPE_LSTM_CADENZA
            if (r7 == r8) goto L_0x0499
            int r7 = r6.b
            int r7 = r7 + 1024
            r6.b = r7
        L_0x0499:
            int r5 = r5 + 1
            goto L_0x0423
        L_0x049c:
            java.util.Comparator r0 = java.util.Collections.reverseOrder()
            java.util.Collections.sort(r4, r0)
            boolean r0 = defpackage.brp.b()
            if (r0 == 0) goto L_0x04fc
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            java.lang.String r3 = "Scores:"
            r0.<init>(r3)
            java.util.Iterator r3 = r4.iterator()
            r11 = r12
        L_0x04b5:
            boolean r5 = r3.hasNext()
            if (r5 == 0) goto L_0x04e1
            java.lang.Object r5 = r3.next()
            brz r5 = (defpackage.brz) r5
            r6 = 8
            if (r11 < r6) goto L_0x04c6
            goto L_0x04e1
        L_0x04c6:
            java.lang.String r6 = " "
            r0.append(r6)
            bsk r6 = r5.a
            java.lang.String r6 = r6.p()
            r0.append(r6)
            java.lang.String r6 = " "
            r0.append(r6)
            int r5 = r5.b
            r0.append(r5)
            int r11 = r11 + 1
            goto L_0x04b5
        L_0x04e1:
            hca r3 = c
            hco r3 = r3.f()
            hby r3 = (defpackage.hby) r3
            java.lang.String r5 = "RankedDispatcher.java"
            java.lang.String r6 = "com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher"
            java.lang.String r7 = "dispatchOnLanguage"
            r8 = 302(0x12e, float:4.23E-43)
            hco r3 = r3.j(r6, r7, r8, r5)
            hby r3 = (defpackage.hby) r3
            java.lang.String r5 = "%s"
            r3.u(r5, r0)
        L_0x04fc:
            boolean r0 = r27.j()
            if (r0 == 0) goto L_0x0526
            hca r0 = c
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            java.lang.String r3 = "RankedDispatcher.java"
            java.lang.String r5 = "com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher"
            java.lang.String r6 = "dispatch"
            r7 = 169(0xa9, float:2.37E-43)
            hco r0 = r0.j(r5, r6, r7, r3)
            hby r0 = (defpackage.hby) r0
            java.lang.String r3 = "Dispatch local only"
            r0.r(r3)
            r3 = r28
            bsk r15 = r1.c(r2, r4, r3)
            r3 = r15
            r0 = 0
            goto L_0x0596
        L_0x0526:
            r3 = r28
            boolean r0 = r27.l()
            if (r0 == 0) goto L_0x0550
            hca r0 = c
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            java.lang.String r3 = "RankedDispatcher.java"
            java.lang.String r5 = "com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher"
            java.lang.String r6 = "dispatch"
            r7 = 172(0xac, float:2.41E-43)
            hco r0 = r0.j(r5, r6, r7, r3)
            hby r0 = (defpackage.hby) r0
            java.lang.String r3 = "Dispatch network only"
            r0.r(r3)
            bsk r15 = d(r2, r4)
            r0 = r15
            r3 = 0
            goto L_0x0596
        L_0x0550:
            boolean r0 = r27.k()
            if (r0 == 0) goto L_0x0572
            hca r0 = c
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            java.lang.String r5 = "RankedDispatcher.java"
            java.lang.String r6 = "com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher"
            java.lang.String r7 = "dispatch"
            r8 = 176(0xb0, float:2.47E-43)
            hco r0 = r0.j(r6, r7, r8, r5)
            hby r0 = (defpackage.hby) r0
            java.lang.String r5 = "Dispatch network first"
            r0.r(r5)
            goto L_0x058d
        L_0x0572:
            hca r0 = c
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            java.lang.String r5 = "RankedDispatcher.java"
            java.lang.String r6 = "com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher"
            java.lang.String r7 = "dispatch"
            r8 = 178(0xb2, float:2.5E-43)
            hco r0 = r0.j(r6, r7, r8, r5)
            hby r0 = (defpackage.hby) r0
            java.lang.String r5 = "Dispatch local first"
            r0.r(r5)
        L_0x058d:
            bsk r15 = r1.c(r2, r4, r3)
            bsk r0 = d(r2, r4)
            r3 = r15
        L_0x0596:
            if (r0 == 0) goto L_0x05ad
            if (r3 == 0) goto L_0x05ad
            boolean r5 = r27.k()
            if (r5 == 0) goto L_0x05a6
            dda r9 = new dda
            r9.<init>(r0, r3)
            goto L_0x0614
        L_0x05a6:
            dda r0 = new dda
            r5 = 0
            r0.<init>(r3, r5)
            goto L_0x05f5
        L_0x05ad:
            if (r0 == 0) goto L_0x05d2
            hca r3 = c
            hco r3 = r3.c()
            hby r3 = (defpackage.hby) r3
            java.lang.String r5 = "RankedDispatcher.java"
            java.lang.String r6 = "com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher"
            java.lang.String r7 = "dispatch"
            r8 = 193(0xc1, float:2.7E-43)
            hco r3 = r3.j(r6, r7, r8, r5)
            hby r3 = (defpackage.hby) r3
            java.lang.String r5 = "Local voice not installed, we will not be able to do local fallback"
            r3.r(r5)
            dda r3 = new dda
            r5 = 0
            r3.<init>(r0, r5)
            r9 = r3
            goto L_0x0614
        L_0x05d2:
            if (r3 == 0) goto L_0x05f7
            hca r0 = c
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            java.lang.String r5 = "RankedDispatcher.java"
            java.lang.String r6 = "com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher"
            java.lang.String r7 = "dispatch"
            r8 = 196(0xc4, float:2.75E-43)
            hco r0 = r0.j(r6, r7, r8, r5)
            hby r0 = (defpackage.hby) r0
            java.lang.String r5 = "No network voice found, will fall back to local immediately"
            r0.r(r5)
            dda r0 = new dda
            r5 = 0
            r0.<init>(r3, r5)
        L_0x05f5:
            r9 = r0
            goto L_0x0614
        L_0x05f7:
            r5 = 0
            hca r0 = c
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            java.lang.String r3 = "RankedDispatcher.java"
            java.lang.String r6 = "com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher"
            java.lang.String r7 = "dispatch"
            r8 = 199(0xc7, float:2.79E-43)
            hco r0 = r0.j(r6, r7, r8, r3)
            hby r0 = (defpackage.hby) r0
            java.lang.String r3 = "No local or network voice found, failing dispatch"
            r0.r(r3)
            r9 = r5
        L_0x0614:
            if (r9 != 0) goto L_0x066f
            boolean r0 = r27.g()
            if (r0 == 0) goto L_0x0650
            hca r0 = c
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            java.lang.String r3 = "RankedDispatcher.java"
            java.lang.String r5 = "com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher"
            java.lang.String r6 = "dispatch"
            r7 = 204(0xcc, float:2.86E-43)
            hco r0 = r0.j(r5, r6, r7, r3)
            hby r0 = (defpackage.hby) r0
            java.util.Locale r3 = r27.d()
            java.lang.String r5 = r2.i
            java.lang.String r6 = "Could not find voice for %s and voice name %s"
            r0.C(r6, r3, r5)
            boolean r0 = r27.m()
            if (r0 == 0) goto L_0x066f
            boolean r0 = r4.isEmpty()
            if (r0 == 0) goto L_0x064a
            goto L_0x066f
        L_0x064a:
            bsl r0 = new bsl
            r0.<init>()
            throw r0
        L_0x0650:
            hca r0 = c
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            java.lang.String r3 = "RankedDispatcher.java"
            java.lang.String r4 = "com/google/android/apps/speech/tts/googletts/dispatch/RankedDispatcher"
            java.lang.String r5 = "dispatch"
            r6 = 211(0xd3, float:2.96E-43)
            hco r0 = r0.j(r4, r5, r6, r3)
            hby r0 = (defpackage.hby) r0
            java.util.Locale r2 = r27.d()
            java.lang.String r3 = "Could not find voice for %s"
            r0.u(r3, r2)
        L_0x066f:
            return r9
        L_0x0670:
            r0 = move-exception
            monitor-exit(r6)     // Catch:{ all -> 0x0670 }
            throw r0
        L_0x0673:
            r0 = move-exception
            monitor-exit(r4)     // Catch:{ all -> 0x0673 }
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bsa.b(brr, int):dda");
    }
}
