<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:tag="carUiPreference" android:layout_width="match_parent" android:layout_height="wrap_content" android:minHeight="?android:attr/listPreferredItemHeightSmall">
    <com.android.car.ui.uxr.DrawableStateConstraintLayout android:id="@+id/car_ui_first_action_container" android:background="?android:attr/selectableItemBackground" android:layout_width="0dp" android:layout_height="wrap_content" android:paddingStart="?android:attr/listPreferredItemPaddingStart" android:paddingEnd="?android:attr/listPreferredItemPaddingEnd" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toStartOf="@+id/car_ui_second_action_container" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0">
        <com.android.car.ui.uxr.DrawableStateImageView android:id="@android:id/icon" android:layout_width="@dimen/car_ui_preference_icon_size" android:layout_height="@dimen/car_ui_preference_icon_size" android:scaleType="fitCenter" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0" style="@style/Preference.CarUi.Icon"/>
        <com.android.car.ui.uxr.DrawableStateTextView android:textAppearance="@style/TextAppearance.CarUi.PreferenceTitle" android:id="@android:id/title" android:layout_width="0dp" android:layout_height="wrap_content" android:singleLine="true" android:textAlignment="viewStart" android:layout_marginStart="@dimen/car_ui_preference_icon_margin_end" app:layout_constraintBottom_toTopOf="@android:id/summary" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toEndOf="@android:id/icon" app:layout_constraintTop_toTopOf="0" app:layout_constraintVertical_chainStyle="2" app:layout_goneMarginStart="0dp"/>
        <com.android.car.ui.uxr.DrawableStateTextView android:textAppearance="@style/TextAppearance.CarUi.PreferenceSummary" android:id="@android:id/summary" android:layout_width="0dp" android:layout_height="wrap_content" android:maxLines="2" android:textAlignment="viewStart" android:layout_marginStart="@dimen/car_ui_preference_icon_margin_end" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toEndOf="@android:id/icon" app:layout_constraintTop_toBottomOf="@android:id/title" app:layout_goneMarginStart="0dp"/>
    </com.android.car.ui.uxr.DrawableStateConstraintLayout>
    <View android:id="@+id/car_ui_divider" android:layout_width="@dimen/car_ui_divider_width" android:layout_height="0dp" android:layout_marginTop="@dimen/car_ui_preference_content_margin_top" android:layout_marginBottom="@dimen/car_ui_preference_content_margin_bottom" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintStart_toEndOf="@+id/car_ui_first_action_container" app:layout_constraintTop_toTopOf="0" style="@style/Preference.CarUi.Divider"/>
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/car_ui_second_action_container" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toEndOf="@+id/car_ui_first_action_container" app:layout_constraintTop_toTopOf="0">
        <com.android.car.ui.uxr.DrawableStateFrameLayout android:id="@+id/car_ui_secondary_action" android:background="?android:attr/selectableItemBackground" android:layout_width="?android:attr/listPreferredItemHeightSmall" android:layout_height="match_parent" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toStartOf="@android:id/widget_frame" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0">
            <com.android.car.ui.uxr.DrawableStateSwitch android:layout_gravity="center" android:id="@+id/car_ui_secondary_action_concrete" android:focusable="false" android:clickable="false" android:layout_width="wrap_content" android:layout_height="wrap_content"/>
        </com.android.car.ui.uxr.DrawableStateFrameLayout>
        <FrameLayout android:id="@android:id/widget_frame" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintTop_toTopOf="0"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
