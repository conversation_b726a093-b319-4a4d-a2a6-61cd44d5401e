package defpackage;

/* renamed from: eoy  reason: default package */
/* compiled from: PG */
final /* synthetic */ class eoy extends jnm implements jne {
    public eoy(Object obj) {
        super(obj, eoz.class);
    }

    public final /* bridge */ /* synthetic */ Object b(Object obj, Object obj2) {
        int intValue = ((Number) obj).intValue();
        eam eam = (eam) obj2;
        jnu.e(eam, "p1");
        ((eoz) this.a).b(intValue, eam);
        return jkd.a;
    }
}
