package defpackage;

import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import com.android.car.ui.recyclerview.CarUiRecyclerView;

/* renamed from: bkd  reason: default package */
/* compiled from: PG */
public interface bkd {
    View createCarUiPreferenceView(Context context, AttributeSet attributeSet);

    CarUiRecyclerView createRecyclerView(Context context, AttributeSet attributeSet);

    bok createTextView(Context context, AttributeSet attributeSet);

    bnq installBaseLayoutAround(Context context, View view, bjw bjw, boolean z, boolean z2);
}
