package defpackage;

import android.os.Process;

/* renamed from: cqi  reason: default package */
/* compiled from: PG */
final class cqi extends ThreadLocal {
    public static final cqi a = new cqi();

    private cqi() {
    }

    public final /* bridge */ /* synthetic */ Object initialValue() {
        int myTid = Process.myTid();
        return new cqg(Thread.currentThread(), myTid, true, Process.getThreadPriority(myTid), false, 112);
    }
}
