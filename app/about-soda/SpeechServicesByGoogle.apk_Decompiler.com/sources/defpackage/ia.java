package defpackage;

import android.content.Context;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Parcelable;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.widget.Spinner;
import android.widget.SpinnerAdapter;

/* renamed from: ia  reason: default package */
/* compiled from: PG */
public final class ia extends Spinner {
    private static final int[] e = {16843505};
    public final Context a;
    public hz b;
    int c;
    final Rect d = new Rect();
    private final he f;
    private je g;
    private SpinnerAdapter h;
    private final boolean i;

    /* JADX WARNING: Removed duplicated region for block: B:27:0x00a2  */
    /* JADX WARNING: Removed duplicated region for block: B:30:0x00af  */
    /* JADX WARNING: Removed duplicated region for block: B:33:0x00c9  */
    /* JADX WARNING: Removed duplicated region for block: B:39:0x00d8  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public ia(android.content.Context r11, android.util.AttributeSet r12) {
        /*
            r10 = this;
            r0 = 2130969844(0x7f0404f4, float:1.7548381E38)
            r10.<init>(r11, r12, r0)
            android.graphics.Rect r1 = new android.graphics.Rect
            r1.<init>()
            r10.d = r1
            android.content.Context r1 = r10.getContext()
            defpackage.lr.d(r10, r1)
            int[] r1 = defpackage.er.u
            r2 = 0
            androidx.wear.ambient.AmbientDelegate r1 = androidx.wear.ambient.AmbientDelegate.A(r11, r12, r1, r0, r2)
            he r3 = new he
            r3.<init>(r10)
            r10.f = r3
            r3 = 4
            int r3 = r1.p(r3, r2)
            if (r3 == 0) goto L_0x0031
            nx r4 = new nx
            r4.<init>(r11, r3)
            r10.a = r4
            goto L_0x0033
        L_0x0031:
            r10.a = r11
        L_0x0033:
            r3 = 1
            r4 = 0
            int[] r5 = e     // Catch:{ Exception -> 0x0096, all -> 0x0094 }
            android.content.res.TypedArray r5 = r11.obtainStyledAttributes(r12, r5, r0, r2)     // Catch:{ Exception -> 0x0096, all -> 0x0094 }
            boolean r6 = r5.hasValue(r2)     // Catch:{ Exception -> 0x0092 }
            if (r6 == 0) goto L_0x0046
            int r6 = r5.getInt(r2, r2)     // Catch:{ Exception -> 0x0092 }
            goto L_0x0047
        L_0x0046:
            r6 = -1
        L_0x0047:
            if (r5 == 0) goto L_0x004c
            r5.recycle()
        L_0x004c:
            r5 = 2
            if (r6 == 0) goto L_0x0083
            if (r6 == r3) goto L_0x0052
            goto L_0x00a5
        L_0x0052:
            hx r6 = new hx
            android.content.Context r7 = r10.a
            r6.<init>(r10, r7, r12)
            android.content.Context r7 = r10.a
            int[] r8 = defpackage.er.u
            androidx.wear.ambient.AmbientDelegate r7 = androidx.wear.ambient.AmbientDelegate.A(r7, r12, r8, r0, r2)
            r8 = 3
            r9 = -2
            int r8 = r7.o(r8, r9)
            r10.c = r8
            android.graphics.drawable.Drawable r8 = r7.r(r3)
            r6.f(r8)
            java.lang.String r5 = r1.u(r5)
            r6.a = r5
            r7.v()
            r10.b = r6
            ht r5 = new ht
            r5.<init>(r10, r10, r6)
            r10.g = r5
            goto L_0x00a5
        L_0x0083:
            hu r6 = new hu
            r6.<init>(r10)
            r10.b = r6
            java.lang.String r5 = r1.u(r5)
            r6.i(r5)
            goto L_0x00a5
        L_0x0092:
            r6 = move-exception
            goto L_0x0099
        L_0x0094:
            r11 = move-exception
            goto L_0x00d6
        L_0x0096:
            r5 = move-exception
            r6 = r5
            r5 = r4
        L_0x0099:
            java.lang.String r7 = "AppCompatSpinner"
            java.lang.String r8 = "Could not read android:spinnerMode"
            android.util.Log.i(r7, r8, r6)     // Catch:{ all -> 0x00d4 }
            if (r5 == 0) goto L_0x00a5
            r5.recycle()
        L_0x00a5:
            java.lang.Object r5 = r1.b
            android.content.res.TypedArray r5 = (android.content.res.TypedArray) r5
            java.lang.CharSequence[] r2 = r5.getTextArray(r2)
            if (r2 == 0) goto L_0x00c0
            android.widget.ArrayAdapter r5 = new android.widget.ArrayAdapter
            r6 = 17367048(0x1090008, float:2.5162948E-38)
            r5.<init>(r11, r6, r2)
            r11 = 2131624119(0x7f0e00b7, float:1.8875409E38)
            r5.setDropDownViewResource(r11)
            r10.setAdapter((android.widget.SpinnerAdapter) r5)
        L_0x00c0:
            r1.v()
            r10.i = r3
            android.widget.SpinnerAdapter r11 = r10.h
            if (r11 == 0) goto L_0x00ce
            r10.setAdapter((android.widget.SpinnerAdapter) r11)
            r10.h = r4
        L_0x00ce:
            he r11 = r10.f
            r11.b(r12, r0)
            return
        L_0x00d4:
            r11 = move-exception
            r4 = r5
        L_0x00d6:
            if (r4 == 0) goto L_0x00db
            r4.recycle()
        L_0x00db:
            throw r11
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.ia.<init>(android.content.Context, android.util.AttributeSet):void");
    }

    /* access modifiers changed from: package-private */
    public final int a(SpinnerAdapter spinnerAdapter, Drawable drawable) {
        int i2;
        int i3 = 0;
        if (spinnerAdapter == null) {
            return 0;
        }
        int makeMeasureSpec = View.MeasureSpec.makeMeasureSpec(getMeasuredWidth(), 0);
        int makeMeasureSpec2 = View.MeasureSpec.makeMeasureSpec(getMeasuredHeight(), 0);
        int max = Math.max(0, getSelectedItemPosition());
        int min = Math.min(spinnerAdapter.getCount(), max + 15);
        int max2 = Math.max(0, max - (15 - (min - max)));
        View view = null;
        int i4 = 0;
        while (max2 < min) {
            int itemViewType = spinnerAdapter.getItemViewType(max2);
            if (itemViewType != i3) {
                i2 = itemViewType;
            } else {
                i2 = i3;
            }
            if (itemViewType != i3) {
                view = null;
            }
            view = spinnerAdapter.getView(max2, view, this);
            if (view.getLayoutParams() == null) {
                view.setLayoutParams(new ViewGroup.LayoutParams(-2, -2));
            }
            view.measure(makeMeasureSpec, makeMeasureSpec2);
            i4 = Math.max(i4, view.getMeasuredWidth());
            max2++;
            i3 = i2;
        }
        if (drawable == null) {
            return i4;
        }
        drawable.getPadding(this.d);
        Rect rect = this.d;
        return i4 + rect.left + rect.right;
    }

    public final void b() {
        this.b.l(getTextDirection(), getTextAlignment());
    }

    /* access modifiers changed from: protected */
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        he heVar = this.f;
        if (heVar != null) {
            heVar.a();
        }
    }

    public final int getDropDownHorizontalOffset() {
        hz hzVar = this.b;
        if (hzVar != null) {
            return hzVar.a();
        }
        return super.getDropDownHorizontalOffset();
    }

    public final int getDropDownVerticalOffset() {
        hz hzVar = this.b;
        if (hzVar != null) {
            return hzVar.b();
        }
        return super.getDropDownVerticalOffset();
    }

    public final int getDropDownWidth() {
        if (this.b != null) {
            return this.c;
        }
        return super.getDropDownWidth();
    }

    public final Drawable getPopupBackground() {
        hz hzVar = this.b;
        if (hzVar != null) {
            return hzVar.c();
        }
        return super.getPopupBackground();
    }

    public final Context getPopupContext() {
        return this.a;
    }

    public final CharSequence getPrompt() {
        hz hzVar = this.b;
        if (hzVar != null) {
            return hzVar.d();
        }
        return super.getPrompt();
    }

    /* access modifiers changed from: protected */
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        hz hzVar = this.b;
        if (hzVar != null && hzVar.u()) {
            this.b.k();
        }
    }

    /* access modifiers changed from: protected */
    public final void onMeasure(int i2, int i3) {
        super.onMeasure(i2, i3);
        if (this.b != null && View.MeasureSpec.getMode(i2) == Integer.MIN_VALUE) {
            setMeasuredDimension(Math.min(Math.max(getMeasuredWidth(), a(getAdapter(), getBackground())), View.MeasureSpec.getSize(i2)), getMeasuredHeight());
        }
    }

    public final void onRestoreInstanceState(Parcelable parcelable) {
        ViewTreeObserver viewTreeObserver;
        hy hyVar = (hy) parcelable;
        super.onRestoreInstanceState(hyVar.getSuperState());
        if (hyVar.a && (viewTreeObserver = getViewTreeObserver()) != null) {
            viewTreeObserver.addOnGlobalLayoutListener(new gj(this, 2));
        }
    }

    public final Parcelable onSaveInstanceState() {
        hy hyVar = new hy(super.onSaveInstanceState());
        hz hzVar = this.b;
        boolean z = false;
        if (hzVar != null && hzVar.u()) {
            z = true;
        }
        hyVar.a = z;
        return hyVar;
    }

    public final boolean onTouchEvent(MotionEvent motionEvent) {
        je jeVar = this.g;
        if (jeVar == null || !jeVar.onTouch(this, motionEvent)) {
            return super.onTouchEvent(motionEvent);
        }
        return true;
    }

    public final boolean performClick() {
        hz hzVar = this.b;
        if (hzVar == null) {
            return super.performClick();
        }
        if (hzVar.u()) {
            return true;
        }
        b();
        return true;
    }

    public final void setBackgroundDrawable(Drawable drawable) {
        super.setBackgroundDrawable(drawable);
        he heVar = this.f;
        if (heVar != null) {
            heVar.e();
        }
    }

    public final void setBackgroundResource(int i2) {
        super.setBackgroundResource(i2);
        he heVar = this.f;
        if (heVar != null) {
            heVar.c(i2);
        }
    }

    public final void setDropDownHorizontalOffset(int i2) {
        hz hzVar = this.b;
        if (hzVar != null) {
            hzVar.h(i2);
            this.b.g(i2);
            return;
        }
        super.setDropDownHorizontalOffset(i2);
    }

    public final void setDropDownVerticalOffset(int i2) {
        hz hzVar = this.b;
        if (hzVar != null) {
            hzVar.j(i2);
        } else {
            super.setDropDownVerticalOffset(i2);
        }
    }

    public final void setDropDownWidth(int i2) {
        if (this.b != null) {
            this.c = i2;
        } else {
            super.setDropDownWidth(i2);
        }
    }

    public final void setPopupBackgroundDrawable(Drawable drawable) {
        hz hzVar = this.b;
        if (hzVar != null) {
            hzVar.f(drawable);
        } else {
            super.setPopupBackgroundDrawable(drawable);
        }
    }

    public final void setPopupBackgroundResource(int i2) {
        setPopupBackgroundDrawable(ke.h(this.a, i2));
    }

    public final void setPrompt(CharSequence charSequence) {
        hz hzVar = this.b;
        if (hzVar != null) {
            hzVar.i(charSequence);
        } else {
            super.setPrompt(charSequence);
        }
    }

    public final void setAdapter(SpinnerAdapter spinnerAdapter) {
        if (!this.i) {
            this.h = spinnerAdapter;
            return;
        }
        super.setAdapter(spinnerAdapter);
        if (this.b != null) {
            Context context = this.a;
            if (context == null) {
                context = getContext();
            }
            this.b.e(new hv(spinnerAdapter, context.getTheme()));
        }
    }
}
