package defpackage;

import java.util.Objects;
import java.util.concurrent.CancellationException;

/* renamed from: ets  reason: default package */
/* compiled from: PG */
final class ets extends jmi implements jne {
    /* synthetic */ Object a;
    final /* synthetic */ eua b;
    final /* synthetic */ jna c;
    final /* synthetic */ jqs d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public ets(eua eua, jna jna, jqs jqs, jlr jlr) {
        super(2, jlr);
        this.b = eua;
        this.c = jna;
        this.d = jqs;
    }

    public final /* bridge */ /* synthetic */ Object b(Object obj, Object obj2) {
        return ((ets) c((dyc) obj, (jlr) obj2)).bk(jkd.a);
    }

    public final Object bk(Object obj) {
        jji.c(obj);
        jna jna = this.c;
        if (jju.a(this.b.f((etf) jna.a((dyc) this.a))) != null) {
            jqs jqs = this.d;
            ((hby) eua.a.h().h(hdg.a, "ALT.GrpcARCRespSender").j("com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender$sendAudioBytesReceiverRegistry$2$1", "invokeSuspend", 238, "StreamListeningSessionResponseSender.kt")).r("#audio# Stopping sending bytes to the remote client");
            jrz jrz = (jrz) jqs.bB().get(jrz.c);
            if (jrz != null) {
                jrz.r((CancellationException) null);
            } else {
                Objects.toString(jqs);
                throw new IllegalStateException("Scope cannot be cancelled because it does not have a job: ".concat(String.valueOf(jqs)));
            }
        }
        return jkd.a;
    }

    public final jlr c(Object obj, jlr jlr) {
        ets ets = new ets(this.b, this.c, this.d, jlr);
        ets.a = obj;
        return ets;
    }
}
