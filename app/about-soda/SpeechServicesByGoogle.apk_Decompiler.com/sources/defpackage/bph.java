package defpackage;

import android.os.Parcel;
import android.os.Parcelable;

/* renamed from: bph  reason: default package */
/* compiled from: PG */
public final class bph implements Parcelable.Creator {
    public final /* bridge */ /* synthetic */ Object createFromParcel(Parcel parcel) {
        Parcel parcel2 = parcel;
        int I = cgr.I(parcel);
        int i = -1;
        int i2 = -1;
        int i3 = 0;
        int i4 = 0;
        int i5 = 0;
        int i6 = 0;
        int i7 = 0;
        int i8 = 0;
        int i9 = 0;
        int i10 = 0;
        int i11 = 0;
        int i12 = 0;
        int i13 = 0;
        boolean z = false;
        int i14 = 0;
        int i15 = 0;
        int i16 = 0;
        int i17 = 0;
        int i18 = 0;
        int i19 = 0;
        int i20 = 0;
        int i21 = 0;
        int i22 = 0;
        long j = 0;
        long j2 = 0;
        long j3 = 0;
        long j4 = 0;
        long j5 = 0;
        long j6 = 0;
        long j7 = 0;
        long j8 = 0;
        long j9 = 0;
        long j10 = 0;
        byte[] bArr = null;
        float f = 0.0f;
        double d = 0.0d;
        double d2 = 0.0d;
        while (parcel.dataPosition() < I) {
            int readInt = parcel.readInt();
            switch (cgr.E(readInt)) {
                case 1:
                    i3 = cgr.G(parcel2, readInt);
                    break;
                case 2:
                    i4 = cgr.G(parcel2, readInt);
                    break;
                case 3:
                    i5 = cgr.G(parcel2, readInt);
                    break;
                case 4:
                    i6 = cgr.G(parcel2, readInt);
                    break;
                case 5:
                    j = cgr.J(parcel2, readInt);
                    break;
                case 6:
                    i7 = cgr.G(parcel2, readInt);
                    break;
                case 7:
                    i8 = cgr.G(parcel2, readInt);
                    break;
                case 8:
                    i9 = cgr.G(parcel2, readInt);
                    break;
                case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                    j2 = cgr.J(parcel2, readInt);
                    break;
                case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                    j3 = cgr.J(parcel2, readInt);
                    break;
                case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                    j4 = cgr.J(parcel2, readInt);
                    break;
                case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                    j5 = cgr.J(parcel2, readInt);
                    break;
                case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                    j6 = cgr.J(parcel2, readInt);
                    break;
                case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                    j7 = cgr.J(parcel2, readInt);
                    break;
                case 15:
                    i10 = cgr.G(parcel2, readInt);
                    break;
                case 16:
                    i11 = cgr.G(parcel2, readInt);
                    break;
                case 17:
                    i12 = cgr.G(parcel2, readInt);
                    break;
                case 18:
                    i13 = cgr.G(parcel2, readInt);
                    break;
                case 19:
                    z = cgr.V(parcel2, readInt);
                    break;
                case 20:
                    i14 = cgr.G(parcel2, readInt);
                    break;
                case 21:
                    j8 = cgr.J(parcel2, readInt);
                    break;
                case 22:
                    d = cgr.D(parcel2, readInt);
                    break;
                case 23:
                    d2 = cgr.D(parcel2, readInt);
                    break;
                case 24:
                    i15 = cgr.G(parcel2, readInt);
                    break;
                case 25:
                    cgr.T(parcel2, readInt, 4);
                    f = parcel.readFloat();
                    break;
                case 26:
                    bArr = cgr.W(parcel2, readInt);
                    break;
                case 27:
                    i16 = cgr.G(parcel2, readInt);
                    break;
                case 28:
                    i17 = cgr.G(parcel2, readInt);
                    break;
                case 29:
                    j9 = cgr.J(parcel2, readInt);
                    break;
                case 30:
                    j10 = cgr.J(parcel2, readInt);
                    break;
                case 31:
                    i = cgr.G(parcel2, readInt);
                    break;
                case 32:
                    i18 = cgr.G(parcel2, readInt);
                    break;
                case 33:
                    i19 = cgr.G(parcel2, readInt);
                    break;
                case 34:
                    i20 = cgr.G(parcel2, readInt);
                    break;
                case 35:
                    i21 = cgr.G(parcel2, readInt);
                    break;
                case 36:
                    i22 = cgr.G(parcel2, readInt);
                    break;
                case 37:
                    i2 = cgr.G(parcel2, readInt);
                    break;
                default:
                    cgr.U(parcel2, readInt);
                    break;
            }
        }
        cgr.S(parcel2, I);
        return new bpg(i3, i4, i5, i6, j, i7, i8, i9, j2, j3, j4, j5, j6, j7, i10, i11, i12, i13, z, i14, j8, d, d2, i15, f, bArr, i16, i17, j9, j10, i, i18, i19, i20, i21, i22, i2);
    }

    public final /* synthetic */ Object[] newArray(int i) {
        return new bpg[i];
    }
}
