<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:orientation="vertical" android:id="@+id/content_parent" android:fitsSystemWindows="true" android:layout_width="match_parent" android:layout_height="match_parent" android:transitionGroup="true">
    <Toolbar android:theme="?android:attr/actionBarTheme" android:id="@+id/action_bar" android:layout_width="match_parent" android:layout_height="wrap_content" style="?android:attr/actionBarStyle"/>
    <android.support.v7.widget.Toolbar android:theme="?android:attr/actionBarTheme" android:id="@+id/support_action_bar" android:visibility="gone" android:layout_width="match_parent" android:layout_height="wrap_content"/>
    <FrameLayout android:id="@+id/content_frame" android:layout_width="match_parent" android:layout_height="match_parent"/>
</LinearLayout>
