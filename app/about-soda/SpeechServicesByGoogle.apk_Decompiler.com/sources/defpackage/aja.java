package defpackage;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.Surface;
import androidx.wear.ambient.AmbientMode;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.TimeoutException;

/* renamed from: aja  reason: default package */
/* compiled from: PG */
public final class aja extends adl implements air {
    public static final /* synthetic */ int w = 0;
    private final CopyOnWriteArraySet A;
    private final aev B;
    private final boolean C;
    private final Looper D;
    private final afm E;
    private final aix F;
    private final aiy G;
    private final long H;
    private akc I;

    /* renamed from: J  reason: collision with root package name */
    private aiq f3J;
    private aej K;
    private Object L;
    private int M;
    private agd N;
    private int O;
    private adj P;
    private boolean Q;
    private boolean R;
    private int S;
    private aej T;
    private bmu U;
    private final AmbientMode.AmbientController V;
    final aeq b;
    public final aet c;
    public final apf d;
    public final afq e;
    public final ajf f;
    public final afv g;
    public final List h;
    public final apg i;
    public int j;
    public int k;
    public boolean l;
    public aeq m;
    public Surface n;
    public ajt o;
    public int p;
    public final akn q;
    public final aiw r;
    public final gxr s;
    public final abq t;
    final dip u;
    public final abq v;
    private final afo x = new afo();
    private final Context y;
    private final ajy[] z;

    static {
        aeh.b("media3.exoplayer");
    }

    /* JADX WARNING: type inference failed for: r5v4, types: [akb, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v11, types: [apg, java.lang.Object] */
    /*  JADX ERROR: IndexOutOfBoundsException in pass: RegionMakerVisitor
        java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
        	at java.util.ArrayList.rangeCheck(ArrayList.java:659)
        	at java.util.ArrayList.get(ArrayList.java:435)
        	at jadx.core.dex.nodes.InsnNode.getArg(InsnNode.java:101)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:611)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.processMonitorEnter(RegionMaker.java:561)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverse(RegionMaker.java:133)
        	at jadx.core.dex.visitors.regions.RegionMaker.makeRegion(RegionMaker.java:86)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:49)
        */
    /* JADX WARNING: Removed duplicated region for block: B:14:0x0193 A[Catch:{ all -> 0x02f2 }] */
    /* JADX WARNING: Removed duplicated region for block: B:16:0x019b A[Catch:{ all -> 0x02f2 }] */
    /* JADX WARNING: Removed duplicated region for block: B:19:0x01f3 A[Catch:{ all -> 0x02f2 }] */
    /* JADX WARNING: Removed duplicated region for block: B:20:0x01f5 A[Catch:{ all -> 0x02f2 }] */
    /* JADX WARNING: Removed duplicated region for block: B:23:0x024f A[Catch:{ all -> 0x02f2 }] */
    public aja(defpackage.aip r25) {
        /*
            r24 = this;
            r1 = r24
            r0 = r25
            java.lang.String r2 = "Init "
            r24.<init>()
            afo r3 = new afo
            r3.<init>()
            r1.x = r3
            java.lang.String r3 = "ExoPlayerImpl"
            int r4 = java.lang.System.identityHashCode(r24)     // Catch:{ all -> 0x02f2 }
            java.lang.String r4 = java.lang.Integer.toHexString(r4)     // Catch:{ all -> 0x02f2 }
            java.lang.String r5 = defpackage.agh.e     // Catch:{ all -> 0x02f2 }
            java.lang.StringBuilder r6 = new java.lang.StringBuilder     // Catch:{ all -> 0x02f2 }
            r6.<init>(r2)     // Catch:{ all -> 0x02f2 }
            r6.append(r4)     // Catch:{ all -> 0x02f2 }
            java.lang.String r2 = " [AndroidXMedia3/1.4.0] ["
            r6.append(r2)     // Catch:{ all -> 0x02f2 }
            r6.append(r5)     // Catch:{ all -> 0x02f2 }
            java.lang.String r2 = "]"
            r6.append(r2)     // Catch:{ all -> 0x02f2 }
            java.lang.String r2 = r6.toString()     // Catch:{ all -> 0x02f2 }
            defpackage.afy.d(r3, r2)     // Catch:{ all -> 0x02f2 }
            android.content.Context r2 = r0.a     // Catch:{ all -> 0x02f2 }
            android.content.Context r2 = r2.getApplicationContext()     // Catch:{ all -> 0x02f2 }
            r1.y = r2     // Catch:{ all -> 0x02f2 }
            gqx r2 = r0.h     // Catch:{ all -> 0x02f2 }
            afm r3 = r0.b     // Catch:{ all -> 0x02f2 }
            java.lang.Object r2 = r2.apply(r3)     // Catch:{ all -> 0x02f2 }
            akn r2 = (defpackage.akn) r2     // Catch:{ all -> 0x02f2 }
            r1.q = r2     // Catch:{ all -> 0x02f2 }
            r2 = -1000(0xfffffffffffffc18, float:NaN)
            r1.S = r2     // Catch:{ all -> 0x02f2 }
            adj r2 = r0.j     // Catch:{ all -> 0x02f2 }
            r1.P = r2     // Catch:{ all -> 0x02f2 }
            r2 = 1
            r1.M = r2     // Catch:{ all -> 0x02f2 }
            r3 = 2000(0x7d0, double:9.88E-321)
            r1.H = r3     // Catch:{ all -> 0x02f2 }
            aix r3 = new aix     // Catch:{ all -> 0x02f2 }
            r3.<init>(r1)     // Catch:{ all -> 0x02f2 }
            r1.F = r3     // Catch:{ all -> 0x02f2 }
            aiy r4 = new aiy     // Catch:{ all -> 0x02f2 }
            r4.<init>()     // Catch:{ all -> 0x02f2 }
            r1.G = r4     // Catch:{ all -> 0x02f2 }
            android.os.Handler r4 = new android.os.Handler     // Catch:{ all -> 0x02f2 }
            android.os.Looper r5 = r0.i     // Catch:{ all -> 0x02f2 }
            r4.<init>(r5)     // Catch:{ all -> 0x02f2 }
            gsb r5 = r0.c     // Catch:{ all -> 0x02f2 }
            aio r5 = (defpackage.aio) r5     // Catch:{ all -> 0x02f2 }
            java.lang.Object r5 = r5.a     // Catch:{ all -> 0x02f2 }
            ajy[] r3 = r5.a(r4, r3)     // Catch:{ all -> 0x02f2 }
            r1.z = r3     // Catch:{ all -> 0x02f2 }
            int r5 = r3.length     // Catch:{ all -> 0x02f2 }
            defpackage.yi.h(r2)     // Catch:{ all -> 0x02f2 }
            gsb r5 = r0.e     // Catch:{ all -> 0x02f2 }
            java.lang.Object r5 = r5.a()     // Catch:{ all -> 0x02f2 }
            apf r5 = (defpackage.apf) r5     // Catch:{ all -> 0x02f2 }
            r1.d = r5     // Catch:{ all -> 0x02f2 }
            gsb r5 = r0.d     // Catch:{ all -> 0x02f2 }
            r5.a()     // Catch:{ all -> 0x02f2 }
            gsb r5 = r0.g     // Catch:{ all -> 0x02f2 }
            java.lang.Object r5 = r5.a()     // Catch:{ all -> 0x02f2 }
            r1.i = r5     // Catch:{ all -> 0x02f2 }
            r1.C = r2     // Catch:{ all -> 0x02f2 }
            akc r5 = r0.k     // Catch:{ all -> 0x02f2 }
            r1.I = r5     // Catch:{ all -> 0x02f2 }
            android.os.Looper r5 = r0.i     // Catch:{ all -> 0x02f2 }
            r1.D = r5     // Catch:{ all -> 0x02f2 }
            afm r6 = r0.b     // Catch:{ all -> 0x02f2 }
            r1.E = r6     // Catch:{ all -> 0x02f2 }
            r1.c = r1     // Catch:{ all -> 0x02f2 }
            afv r7 = new afv     // Catch:{ all -> 0x02f2 }
            aki r8 = new aki     // Catch:{ all -> 0x02f2 }
            r8.<init>(r2)     // Catch:{ all -> 0x02f2 }
            r7.<init>(r5, r6, r8)     // Catch:{ all -> 0x02f2 }
            r1.g = r7     // Catch:{ all -> 0x02f2 }
            java.util.concurrent.CopyOnWriteArraySet r5 = new java.util.concurrent.CopyOnWriteArraySet     // Catch:{ all -> 0x02f2 }
            r5.<init>()     // Catch:{ all -> 0x02f2 }
            r1.A = r5     // Catch:{ all -> 0x02f2 }
            java.util.ArrayList r5 = new java.util.ArrayList     // Catch:{ all -> 0x02f2 }
            r5.<init>()     // Catch:{ all -> 0x02f2 }
            r1.h = r5     // Catch:{ all -> 0x02f2 }
            bmu r5 = new bmu     // Catch:{ all -> 0x02f2 }
            r5.<init>()     // Catch:{ all -> 0x02f2 }
            r1.U = r5     // Catch:{ all -> 0x02f2 }
            aiq r5 = defpackage.aiq.a     // Catch:{ all -> 0x02f2 }
            r1.f3J = r5     // Catch:{ all -> 0x02f2 }
            dip r5 = new dip     // Catch:{ all -> 0x02f2 }
            int r3 = r3.length     // Catch:{ all -> 0x02f2 }
            aka[] r3 = new defpackage.aka[r2]     // Catch:{ all -> 0x02f2 }
            aon[] r6 = new defpackage.aon[r2]     // Catch:{ all -> 0x02f2 }
            afe r7 = defpackage.afe.a     // Catch:{ all -> 0x02f2 }
            r8 = 0
            r5.<init>(r3, r6, r7, r8)     // Catch:{ all -> 0x02f2 }
            r1.u = r5     // Catch:{ all -> 0x02f2 }
            aev r3 = new aev     // Catch:{ all -> 0x02f2 }
            r3.<init>()     // Catch:{ all -> 0x02f2 }
            r1.B = r3     // Catch:{ all -> 0x02f2 }
            ads r3 = new ads     // Catch:{ all -> 0x02f2 }
            r3.<init>()     // Catch:{ all -> 0x02f2 }
            r5 = 20
            int[] r6 = new int[r5]     // Catch:{ all -> 0x02f2 }
            r6 = {1, 2, 3, 13, 14, 15, 16, 17, 18, 19, 31, 20, 30, 21, 35, 22, 24, 27, 28, 32} // fill-array     // Catch:{ all -> 0x02f2 }
            r7 = 0
            r9 = r7
        L_0x00f0:
            if (r9 >= r5) goto L_0x00fa
            r10 = r6[r9]     // Catch:{ all -> 0x02f2 }
            r3.b(r10)     // Catch:{ all -> 0x02f2 }
            int r9 = r9 + 1
            goto L_0x00f0
        L_0x00fa:
            r5 = 29
            defpackage.yh.j(r5, r2, r3)     // Catch:{ all -> 0x02f2 }
            r5 = 23
            defpackage.yh.j(r5, r7, r3)     // Catch:{ all -> 0x02f2 }
            r5 = 25
            defpackage.yh.j(r5, r7, r3)     // Catch:{ all -> 0x02f2 }
            r5 = 33
            defpackage.yh.j(r5, r7, r3)     // Catch:{ all -> 0x02f2 }
            r5 = 26
            defpackage.yh.j(r5, r7, r3)     // Catch:{ all -> 0x02f2 }
            r5 = 34
            defpackage.yh.j(r5, r7, r3)     // Catch:{ all -> 0x02f2 }
            aeq r3 = defpackage.yh.h(r3)     // Catch:{ all -> 0x02f2 }
            r1.b = r3     // Catch:{ all -> 0x02f2 }
            ads r5 = new ads     // Catch:{ all -> 0x02f2 }
            r5.<init>()     // Catch:{ all -> 0x02f2 }
            defpackage.yh.i(r3, r5)     // Catch:{ all -> 0x02f2 }
            r3 = 4
            r5.b(r3)     // Catch:{ all -> 0x02f2 }
            r6 = 10
            r5.b(r6)     // Catch:{ all -> 0x02f2 }
            aeq r5 = defpackage.yh.h(r5)     // Catch:{ all -> 0x02f2 }
            r1.m = r5     // Catch:{ all -> 0x02f2 }
            afm r5 = r1.E     // Catch:{ all -> 0x02f2 }
            android.os.Looper r9 = r1.D     // Catch:{ all -> 0x02f2 }
            afq r5 = r5.b(r9, r8)     // Catch:{ all -> 0x02f2 }
            r1.e = r5     // Catch:{ all -> 0x02f2 }
            androidx.wear.ambient.AmbientMode$AmbientController r5 = new androidx.wear.ambient.AmbientMode$AmbientController     // Catch:{ all -> 0x02f2 }
            r5.<init>(r1, r8)     // Catch:{ all -> 0x02f2 }
            r1.V = r5     // Catch:{ all -> 0x02f2 }
            dip r9 = r1.u     // Catch:{ all -> 0x02f2 }
            ajt r9 = defpackage.ajt.h(r9)     // Catch:{ all -> 0x02f2 }
            r1.o = r9     // Catch:{ all -> 0x02f2 }
            akn r9 = r1.q     // Catch:{ all -> 0x02f2 }
            aet r10 = r1.c     // Catch:{ all -> 0x02f2 }
            android.os.Looper r13 = r1.D     // Catch:{ all -> 0x02f2 }
            aet r11 = r9.e     // Catch:{ all -> 0x02f2 }
            if (r11 == 0) goto L_0x0165
            akm r11 = r9.b     // Catch:{ all -> 0x02f2 }
            gxq r11 = r11.b     // Catch:{ all -> 0x02f2 }
            boolean r11 = r11.isEmpty()     // Catch:{ all -> 0x02f2 }
            if (r11 == 0) goto L_0x0163
            goto L_0x0165
        L_0x0163:
            r11 = r7
            goto L_0x0166
        L_0x0165:
            r11 = r2
        L_0x0166:
            defpackage.yi.h(r11)     // Catch:{ all -> 0x02f2 }
            defpackage.yi.k(r10)     // Catch:{ all -> 0x02f2 }
            r9.e = r10     // Catch:{ all -> 0x02f2 }
            afm r11 = r9.a     // Catch:{ all -> 0x02f2 }
            afq r11 = r11.b(r13, r8)     // Catch:{ all -> 0x02f2 }
            r9.f = r11     // Catch:{ all -> 0x02f2 }
            afv r11 = r9.d     // Catch:{ all -> 0x02f2 }
            akh r15 = new akh     // Catch:{ all -> 0x02f2 }
            r15.<init>(r9, r10)     // Catch:{ all -> 0x02f2 }
            afm r14 = r11.a     // Catch:{ all -> 0x02f2 }
            java.util.concurrent.CopyOnWriteArraySet r12 = r11.d     // Catch:{ all -> 0x02f2 }
            afv r10 = new afv     // Catch:{ all -> 0x02f2 }
            boolean r11 = r11.e     // Catch:{ all -> 0x02f2 }
            r16 = r11
            r11 = r10
            r11.<init>(r12, r13, r14, r15, r16)     // Catch:{ all -> 0x02f2 }
            r9.d = r10     // Catch:{ all -> 0x02f2 }
            int r9 = defpackage.agh.a     // Catch:{ all -> 0x02f2 }
            r10 = 31
            if (r9 >= r10) goto L_0x019b
            akt r9 = new akt     // Catch:{ all -> 0x02f2 }
            r9.<init>()     // Catch:{ all -> 0x02f2 }
        L_0x0198:
            r21 = r9
            goto L_0x01a4
        L_0x019b:
            android.content.Context r9 = r1.y     // Catch:{ all -> 0x02f2 }
            java.lang.String r10 = r0.m     // Catch:{ all -> 0x02f2 }
            akt r9 = defpackage.aiw.a(r9, r1, r2, r10)     // Catch:{ all -> 0x02f2 }
            goto L_0x0198
        L_0x01a4:
            ajf r15 = new ajf     // Catch:{ all -> 0x02f2 }
            ajy[] r10 = r1.z     // Catch:{ all -> 0x02f2 }
            apf r11 = r1.d     // Catch:{ all -> 0x02f2 }
            dip r12 = r1.u     // Catch:{ all -> 0x02f2 }
            gsb r9 = r0.f     // Catch:{ all -> 0x02f2 }
            java.lang.Object r9 = r9.a()     // Catch:{ all -> 0x02f2 }
            r13 = r9
            aik r13 = (defpackage.aik) r13     // Catch:{ all -> 0x02f2 }
            apg r14 = r1.i     // Catch:{ all -> 0x02f2 }
            akn r9 = r1.q     // Catch:{ all -> 0x02f2 }
            akc r3 = r1.I     // Catch:{ all -> 0x02f2 }
            aii r6 = r0.n     // Catch:{ all -> 0x02f2 }
            android.os.Looper r7 = r1.D     // Catch:{ all -> 0x02f2 }
            afm r8 = r1.E     // Catch:{ all -> 0x02f2 }
            aiq r2 = r1.f3J     // Catch:{ all -> 0x02f2 }
            r16 = r9
            r9 = r15
            r23 = r4
            r4 = r15
            r15 = r16
            r16 = r3
            r17 = r6
            r18 = r7
            r19 = r8
            r20 = r5
            r22 = r2
            r9.<init>(r10, r11, r12, r13, r14, r15, r16, r17, r18, r19, r20, r21, r22)     // Catch:{ all -> 0x02f2 }
            r1.f = r4     // Catch:{ all -> 0x02f2 }
            aej r2 = defpackage.aej.a     // Catch:{ all -> 0x02f2 }
            r1.K = r2     // Catch:{ all -> 0x02f2 }
            aej r2 = defpackage.aej.a     // Catch:{ all -> 0x02f2 }
            r1.T = r2     // Catch:{ all -> 0x02f2 }
            r2 = -1
            r1.p = r2     // Catch:{ all -> 0x02f2 }
            android.content.Context r3 = r1.y     // Catch:{ all -> 0x02f2 }
            java.lang.String r4 = "audio"
            java.lang.Object r3 = r3.getSystemService(r4)     // Catch:{ all -> 0x02f2 }
            android.media.AudioManager r3 = (android.media.AudioManager) r3     // Catch:{ all -> 0x02f2 }
            if (r3 != 0) goto L_0x01f5
            r3 = r2
            goto L_0x01f9
        L_0x01f5:
            int r3 = r3.generateAudioSessionId()     // Catch:{ all -> 0x02f2 }
        L_0x01f9:
            r1.O = r3     // Catch:{ all -> 0x02f2 }
            int r3 = defpackage.afl.a     // Catch:{ all -> 0x02f2 }
            r3 = 1
            r1.Q = r3     // Catch:{ all -> 0x02f2 }
            akn r3 = r1.q     // Catch:{ all -> 0x02f2 }
            r1.i(r3)     // Catch:{ all -> 0x02f2 }
            apg r3 = r1.i     // Catch:{ all -> 0x02f2 }
            android.os.Handler r4 = new android.os.Handler     // Catch:{ all -> 0x02f2 }
            android.os.Looper r5 = r1.D     // Catch:{ all -> 0x02f2 }
            r4.<init>(r5)     // Catch:{ all -> 0x02f2 }
            akn r5 = r1.q     // Catch:{ all -> 0x02f2 }
            defpackage.yi.k(r5)     // Catch:{ all -> 0x02f2 }
            defpackage.yi.k(r5)     // Catch:{ all -> 0x02f2 }
            api r3 = (defpackage.api) r3     // Catch:{ all -> 0x02f2 }
            byw r3 = r3.g     // Catch:{ all -> 0x02f2 }
            r3.Y(r5)     // Catch:{ all -> 0x02f2 }
            awh r6 = new awh     // Catch:{ all -> 0x02f2 }
            r6.<init>((android.os.Handler) r4, (defpackage.akn) r5)     // Catch:{ all -> 0x02f2 }
            java.lang.Object r3 = r3.a     // Catch:{ all -> 0x02f2 }
            java.util.concurrent.CopyOnWriteArrayList r3 = (java.util.concurrent.CopyOnWriteArrayList) r3     // Catch:{ all -> 0x02f2 }
            r3.add(r6)     // Catch:{ all -> 0x02f2 }
            aix r3 = r1.F     // Catch:{ all -> 0x02f2 }
            java.util.concurrent.CopyOnWriteArraySet r4 = r1.A     // Catch:{ all -> 0x02f2 }
            r4.add(r3)     // Catch:{ all -> 0x02f2 }
            aiw r3 = new aiw     // Catch:{ all -> 0x02f2 }
            android.content.Context r4 = r0.a     // Catch:{ all -> 0x02f2 }
            r5 = r23
            r3.<init>(r4, r5)     // Catch:{ all -> 0x02f2 }
            r1.r = r3     // Catch:{ all -> 0x02f2 }
            gxr r3 = new gxr     // Catch:{ all -> 0x02f2 }
            android.content.Context r4 = r0.a     // Catch:{ all -> 0x02f2 }
            aix r5 = r1.F     // Catch:{ all -> 0x02f2 }
            r3.<init>(r4, r5)     // Catch:{ all -> 0x02f2 }
            r1.s = r3     // Catch:{ all -> 0x02f2 }
            java.lang.Object r4 = r3.c     // Catch:{ all -> 0x02f2 }
            r4 = 0
            boolean r5 = j$.util.Objects.equals(r4, r4)     // Catch:{ all -> 0x02f2 }
            if (r5 != 0) goto L_0x025a
            r3.c = r4     // Catch:{ all -> 0x02f2 }
            r4 = 0
            r3.a = r4     // Catch:{ all -> 0x02f2 }
            java.lang.String r3 = "Automatic handling of audio focus is only available for USAGE_MEDIA and USAGE_GAME."
            r4 = 1
            defpackage.yi.g(r4, r3)     // Catch:{ all -> 0x02f2 }
        L_0x025a:
            abq r3 = new abq     // Catch:{ all -> 0x02f2 }
            android.content.Context r4 = r0.a     // Catch:{ all -> 0x02f2 }
            r5 = 0
            r3.<init>(r4, r5)     // Catch:{ all -> 0x02f2 }
            r1.v = r3     // Catch:{ all -> 0x02f2 }
            abq r3 = new abq     // Catch:{ all -> 0x02f2 }
            android.content.Context r0 = r0.a     // Catch:{ all -> 0x02f2 }
            r3.<init>(r0)     // Catch:{ all -> 0x02f2 }
            r1.t = r3     // Catch:{ all -> 0x02f2 }
            defpackage.adi.b()     // Catch:{ all -> 0x02f2 }
            int r0 = defpackage.aff.a     // Catch:{ all -> 0x02f2 }
            agd r0 = defpackage.agd.a     // Catch:{ all -> 0x02f2 }
            r1.N = r0     // Catch:{ all -> 0x02f2 }
            apf r0 = r1.d     // Catch:{ all -> 0x02f2 }
            adj r3 = r1.P     // Catch:{ all -> 0x02f2 }
            r4 = r0
            apd r4 = (defpackage.apd) r4     // Catch:{ all -> 0x02f2 }
            java.lang.Object r4 = r4.b     // Catch:{ all -> 0x02f2 }
            monitor-enter(r4)     // Catch:{ all -> 0x02f2 }
            r5 = r0
            apd r5 = (defpackage.apd) r5     // Catch:{ all -> 0x02ef }
            adj r5 = r5.f     // Catch:{ all -> 0x02ef }
            boolean r5 = r5.equals(r3)     // Catch:{ all -> 0x02ef }
            r6 = r0
            apd r6 = (defpackage.apd) r6     // Catch:{ all -> 0x02ef }
            r6.f = r3     // Catch:{ all -> 0x02ef }
            monitor-exit(r4)     // Catch:{ all -> 0x02ef }
            if (r5 != 0) goto L_0x0296
            apd r0 = (defpackage.apd) r0     // Catch:{ all -> 0x02f2 }
            r0.d()     // Catch:{ all -> 0x02f2 }
        L_0x0296:
            int r0 = r1.O     // Catch:{ all -> 0x02f2 }
            java.lang.Integer r0 = java.lang.Integer.valueOf(r0)     // Catch:{ all -> 0x02f2 }
            r3 = 10
            r4 = 1
            r1.r(r4, r3, r0)     // Catch:{ all -> 0x02f2 }
            int r0 = r1.O     // Catch:{ all -> 0x02f2 }
            java.lang.Integer r0 = java.lang.Integer.valueOf(r0)     // Catch:{ all -> 0x02f2 }
            r4 = 2
            r1.r(r4, r3, r0)     // Catch:{ all -> 0x02f2 }
            adj r0 = r1.P     // Catch:{ all -> 0x02f2 }
            r3 = 3
            r5 = 1
            r1.r(r5, r3, r0)     // Catch:{ all -> 0x02f2 }
            int r0 = r1.M     // Catch:{ all -> 0x02f2 }
            java.lang.Integer r0 = java.lang.Integer.valueOf(r0)     // Catch:{ all -> 0x02f2 }
            r3 = 4
            r1.r(r4, r3, r0)     // Catch:{ all -> 0x02f2 }
            r0 = 0
            java.lang.Integer r3 = java.lang.Integer.valueOf(r0)     // Catch:{ all -> 0x02f2 }
            r5 = 5
            r1.r(r4, r5, r3)     // Catch:{ all -> 0x02f2 }
            java.lang.Boolean r0 = java.lang.Boolean.valueOf(r0)     // Catch:{ all -> 0x02f2 }
            r3 = 9
            r5 = 1
            r1.r(r5, r3, r0)     // Catch:{ all -> 0x02f2 }
            aiy r0 = r1.G     // Catch:{ all -> 0x02f2 }
            r3 = 7
            r1.r(r4, r3, r0)     // Catch:{ all -> 0x02f2 }
            aiy r0 = r1.G     // Catch:{ all -> 0x02f2 }
            r3 = 6
            r4 = 8
            r1.r(r3, r4, r0)     // Catch:{ all -> 0x02f2 }
            int r0 = r1.S     // Catch:{ all -> 0x02f2 }
            java.lang.Integer r0 = java.lang.Integer.valueOf(r0)     // Catch:{ all -> 0x02f2 }
            r3 = 16
            r1.r(r2, r3, r0)     // Catch:{ all -> 0x02f2 }
            afo r0 = r1.x
            r0.e()
            return
        L_0x02ef:
            r0 = move-exception
            monitor-exit(r4)     // Catch:{ all -> 0x02ef }
            throw r0     // Catch:{ all -> 0x02f2 }
        L_0x02f2:
            r0 = move-exception
            afo r2 = r1.x
            r2.e()
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.aja.<init>(aip):void");
    }

    private final void A(ain ain) {
        ajt ajt = this.o;
        ajt a = ajt.a(ajt.c);
        a.q = a.s;
        a.r = 0;
        ajt d2 = a.d(1);
        if (ain != null) {
            d2 = d2.c(ain);
        }
        this.j++;
        this.f.c.e(6).d();
        u(d2, 0, false, 5, -9223372036854775807L);
    }

    private final int x(ajt ajt) {
        if (ajt.b.p()) {
            return this.p;
        }
        return ajt.b.n(ajt.c.a, this.B).c;
    }

    private final long y(ajt ajt) {
        if (ajt.b.p()) {
            return agh.i(0);
        }
        boolean z2 = ajt.p;
        long j2 = ajt.s;
        if (ajt.c.b()) {
            return j2;
        }
        v(ajt.b, ajt.c);
        return j2;
    }

    private static long z(ajt ajt) {
        aew aew = new aew();
        aev aev = new aev();
        ajt.b.n(ajt.c.a, aev);
        long j2 = ajt.d;
        if (j2 != -9223372036854775807L) {
            return j2;
        }
        long j3 = ajt.b.o(aev.c, aew).l;
        return 0;
    }

    public final int a() {
        t();
        if (m()) {
            return this.o.c.b;
        }
        return -1;
    }

    public final int b() {
        t();
        if (m()) {
            return this.o.c.c;
        }
        return -1;
    }

    public final int c() {
        t();
        int x2 = x(this.o);
        if (x2 == -1) {
            return 0;
        }
        return x2;
    }

    public final int d() {
        t();
        return this.o.f;
    }

    public final int e() {
        t();
        return this.o.n;
    }

    public final long f() {
        t();
        return agh.m(y(this.o));
    }

    public final aex g() {
        t();
        return this.o.b;
    }

    public final afe h() {
        t();
        return (afe) this.o.u.b;
    }

    public final void i(aer aer) {
        yi.k(aer);
        this.g.a(aer);
    }

    public final void j() {
        int i2;
        t();
        w(l(), 1);
        ajt ajt = this.o;
        if (ajt.f == 1) {
            ajt c2 = ajt.c((ain) null);
            if (true != c2.b.p()) {
                i2 = 2;
            } else {
                i2 = 4;
            }
            ajt d2 = c2.d(i2);
            this.j++;
            this.f.c.e(29).d();
            u(d2, 1, false, 5, -9223372036854775807L);
        }
    }

    public final void k() {
        t();
        l();
        A((ain) null);
        int i2 = afl.a;
        int i3 = gxq.d;
        gxq gxq = hal.a;
        long j2 = this.o.s;
        gxq.o(gxq);
    }

    public final boolean l() {
        t();
        return this.o.l;
    }

    public final boolean m() {
        t();
        return this.o.c.b();
    }

    public final void n() {
        t();
        d();
        w(true, 1);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:60:0x019d, code lost:
        if (r4 != r8.c) goto L_0x019f;
     */
    /* JADX WARNING: Removed duplicated region for block: B:100:0x02bd  */
    /* JADX WARNING: Removed duplicated region for block: B:86:0x0267  */
    /* JADX WARNING: Removed duplicated region for block: B:94:0x027c  */
    /* JADX WARNING: Removed duplicated region for block: B:99:0x02bb  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void o(defpackage.anm r29) {
        /*
            r28 = this;
            r7 = r28
            r28.t()
            java.util.List r0 = java.util.Collections.singletonList(r29)
            r28.t()
            r28.t()
            ajt r1 = r7.o
            r7.x(r1)
            r28.f()
            int r1 = r7.j
            r2 = 1
            int r1 = r1 + r2
            r7.j = r1
            java.util.List r1 = r7.h
            boolean r1 = r1.isEmpty()
            r3 = 0
            if (r1 != 0) goto L_0x0072
            java.util.List r1 = r7.h
            int r1 = r1.size()
            int r4 = r1 + -1
        L_0x002e:
            if (r4 < 0) goto L_0x0038
            java.util.List r5 = r7.h
            r5.remove(r4)
            int r4 = r4 + -1
            goto L_0x002e
        L_0x0038:
            bmu r4 = r7.U
            java.lang.Object r5 = r4.a
            int[] r5 = (int[]) r5
            int r5 = r5.length
            int r5 = r5 - r1
            int[] r5 = new int[r5]
            r6 = r3
            r8 = r6
        L_0x0044:
            java.lang.Object r9 = r4.a
            int[] r9 = (int[]) r9
            int r10 = r9.length
            if (r6 >= r10) goto L_0x005e
            r9 = r9[r6]
            if (r9 < 0) goto L_0x0054
            if (r9 >= r1) goto L_0x0054
            int r8 = r8 + 1
            goto L_0x005b
        L_0x0054:
            int r10 = r6 - r8
            if (r9 < 0) goto L_0x0059
            int r9 = r9 - r1
        L_0x0059:
            r5[r10] = r9
        L_0x005b:
            int r6 = r6 + 1
            goto L_0x0044
        L_0x005e:
            java.lang.Object r1 = r4.b
            bmu r4 = new bmu
            java.util.Random r6 = new java.util.Random
            java.util.Random r1 = (java.util.Random) r1
            long r8 = r1.nextLong()
            r6.<init>(r8)
            r4.<init>((int[]) r5, (java.util.Random) r6)
            r7.U = r4
        L_0x0072:
            java.util.ArrayList r11 = new java.util.ArrayList
            r11.<init>()
            r1 = r3
        L_0x0078:
            int r4 = r0.size()
            if (r1 >= r4) goto L_0x009f
            ajq r4 = new ajq
            java.lang.Object r5 = r0.get(r1)
            anm r5 = (defpackage.anm) r5
            boolean r6 = r7.C
            r4.<init>(r5, r6)
            r11.add(r4)
            java.util.List r5 = r7.h
            java.lang.Object r6 = r4.b
            anh r4 = r4.a
            aiz r8 = new aiz
            r8.<init>(r6, r4)
            r5.add(r1, r8)
            int r1 = r1 + 1
            goto L_0x0078
        L_0x009f:
            bmu r0 = r7.U
            int r1 = r11.size()
            bmu r0 = r0.c(r1)
            r7.U = r0
            java.util.List r0 = r7.h
            aic r1 = new aic
            bmu r4 = r7.U
            r1.<init>(r0, r4)
            boolean r0 = r1.p()
            if (r0 != 0) goto L_0x00c5
            int r0 = r1.b
            if (r0 < 0) goto L_0x00bf
            goto L_0x00c5
        L_0x00bf:
            adw r0 = new adw
            r0.<init>()
            throw r0
        L_0x00c5:
            int r0 = r1.g(r3)
            ajt r4 = r7.o
            boolean r5 = r1.p()
            r6 = -1
            if (r5 == 0) goto L_0x00d6
            r7.p = r0
            r5 = 0
            goto L_0x0101
        L_0x00d6:
            if (r0 == r6) goto L_0x00e4
            int r5 = r1.b
            if (r0 < r5) goto L_0x00dd
            goto L_0x00e4
        L_0x00dd:
            r15 = r0
            r12 = -9223372036854775807(0x8000000000000001, double:-4.9E-324)
            goto L_0x00f3
        L_0x00e4:
            int r5 = r1.g(r3)
            aew r10 = r7.a
            aew r10 = r1.o(r5, r10)
            long r12 = r10.a()
            r15 = r5
        L_0x00f3:
            aew r5 = r7.a
            aev r14 = r7.B
            long r16 = defpackage.agh.i(r12)
            r12 = r1
            r13 = r5
            android.util.Pair r5 = r12.k(r13, r14, r15, r16)
        L_0x0101:
            boolean r10 = r1.p()
            if (r10 != 0) goto L_0x010c
            if (r5 == 0) goto L_0x010a
            goto L_0x010c
        L_0x010a:
            r10 = r3
            goto L_0x010d
        L_0x010c:
            r10 = r2
        L_0x010d:
            defpackage.yi.f(r10)
            aex r10 = r4.b
            long r12 = r7.p(r4)
            ajt r14 = r4.e(r1)
            boolean r4 = r1.p()
            r8 = 0
            if (r4 == 0) goto L_0x0149
            ank r4 = defpackage.ajt.a
            long r20 = defpackage.agh.i(r8)
            dip r5 = r7.u
            aoh r24 = defpackage.aoh.a
            int r8 = defpackage.gxq.d
            gxq r26 = defpackage.hal.a
            r22 = 0
            r15 = r4
            r16 = r20
            r18 = r20
            r25 = r5
            ajt r5 = r14.g(r15, r16, r18, r20, r22, r24, r25, r26)
            ajt r14 = r5.a(r4)
            long r4 = r14.s
            r14.q = r4
        L_0x0145:
            r27 = r11
            goto L_0x0262
        L_0x0149:
            ank r4 = r14.c
            java.lang.Object r4 = r4.a
            int r15 = defpackage.agh.a
            java.lang.Object r15 = r5.first
            boolean r15 = r4.equals(r15)
            if (r15 != 0) goto L_0x015f
            ank r3 = new ank
            java.lang.Object r8 = r5.first
            r3.<init>(r8)
            goto L_0x0161
        L_0x015f:
            ank r3 = r14.c
        L_0x0161:
            java.lang.Object r5 = r5.second
            java.lang.Long r5 = (java.lang.Long) r5
            long r8 = r5.longValue()
            long r12 = defpackage.agh.i(r12)
            boolean r5 = r10.p()
            if (r5 != 0) goto L_0x0178
            aev r5 = r7.B
            r10.n(r4, r5)
        L_0x0178:
            if (r15 == 0) goto L_0x0224
            int r4 = (r8 > r12 ? 1 : (r8 == r12 ? 0 : -1))
            if (r4 >= 0) goto L_0x0180
            goto L_0x0224
        L_0x0180:
            if (r4 != 0) goto L_0x01e5
            ank r4 = r14.k
            java.lang.Object r4 = r4.a
            int r4 = r1.a(r4)
            if (r4 == r6) goto L_0x019f
            aev r5 = r7.B
            aev r4 = r1.m(r4, r5)
            int r4 = r4.c
            java.lang.Object r5 = r3.a
            aev r8 = r7.B
            r1.n(r5, r8)
            int r5 = r8.c
            if (r4 == r5) goto L_0x0145
        L_0x019f:
            java.lang.Object r4 = r3.a
            aev r5 = r7.B
            r1.n(r4, r5)
            boolean r4 = r3.b()
            if (r4 == 0) goto L_0x01b7
            aev r4 = r7.B
            int r5 = r3.b
            int r8 = r3.c
            long r4 = r4.e(r5, r8)
            goto L_0x01bb
        L_0x01b7:
            aev r4 = r7.B
            long r4 = r4.d
        L_0x01bb:
            long r8 = r14.s
            long r12 = r14.s
            long r6 = r14.e
            r27 = r11
            long r10 = r14.s
            long r22 = r4 - r10
            aoh r10 = r14.i
            dip r11 = r14.u
            java.util.List r15 = r14.j
            r26 = r15
            r15 = r3
            r16 = r8
            r18 = r12
            r20 = r6
            r24 = r10
            r25 = r11
            ajt r6 = r14.g(r15, r16, r18, r20, r22, r24, r25, r26)
            ajt r14 = r6.a(r3)
            r14.q = r4
            goto L_0x0221
        L_0x01e5:
            r27 = r11
            boolean r4 = r3.b()
            r4 = r4 ^ r2
            defpackage.yi.h(r4)
            long r4 = r14.r
            long r6 = r8 - r12
            long r4 = r4 - r6
            r6 = 0
            long r22 = java.lang.Math.max(r6, r4)
            long r4 = r14.q
            ank r6 = r14.k
            ank r7 = r14.c
            boolean r6 = r6.equals(r7)
            if (r6 == 0) goto L_0x0208
            long r4 = r8 + r22
        L_0x0208:
            aoh r6 = r14.i
            dip r7 = r14.u
            java.util.List r10 = r14.j
            r15 = r3
            r16 = r8
            r18 = r8
            r20 = r8
            r24 = r6
            r25 = r7
            r26 = r10
            ajt r14 = r14.g(r15, r16, r18, r20, r22, r24, r25, r26)
            r14.q = r4
        L_0x0221:
            r7 = r28
            goto L_0x0262
        L_0x0224:
            r27 = r11
            boolean r4 = r3.b()
            r4 = r4 ^ r2
            defpackage.yi.h(r4)
            if (r15 != 0) goto L_0x0233
            aoh r4 = defpackage.aoh.a
            goto L_0x0235
        L_0x0233:
            aoh r4 = r14.i
        L_0x0235:
            r24 = r4
            if (r15 != 0) goto L_0x023e
            r7 = r28
            dip r4 = r7.u
            goto L_0x0242
        L_0x023e:
            r7 = r28
            dip r4 = r14.u
        L_0x0242:
            r25 = r4
            if (r15 != 0) goto L_0x024b
            int r4 = defpackage.gxq.d
            gxq r4 = defpackage.hal.a
            goto L_0x024d
        L_0x024b:
            java.util.List r4 = r14.j
        L_0x024d:
            r26 = r4
            r22 = 0
            r15 = r3
            r16 = r8
            r18 = r8
            r20 = r8
            ajt r4 = r14.g(r15, r16, r18, r20, r22, r24, r25, r26)
            ajt r14 = r4.a(r3)
            r14.q = r8
        L_0x0262:
            int r3 = r14.f
            r4 = -1
            if (r0 == r4) goto L_0x027c
            if (r3 == r2) goto L_0x027a
            boolean r3 = r1.p()
            r4 = 4
            if (r3 != 0) goto L_0x0277
            int r1 = r1.b
            if (r0 < r1) goto L_0x0275
            goto L_0x0277
        L_0x0275:
            r3 = 2
            goto L_0x027a
        L_0x0277:
            r13 = r0
            r3 = r4
            goto L_0x027d
        L_0x027a:
            r13 = r0
            goto L_0x027d
        L_0x027c:
            r13 = r4
        L_0x027d:
            ajt r1 = r14.d(r3)
            ajf r0 = r7.f
            r3 = -9223372036854775807(0x8000000000000001, double:-4.9E-324)
            long r14 = defpackage.agh.i(r3)
            bmu r12 = r7.U
            afq r0 = r0.c
            ajb r3 = new ajb
            r10 = r3
            r11 = r27
            r10.<init>(r11, r12, r13, r14)
            r4 = 17
            iih r0 = r0.f(r4, r3)
            r0.d()
            ajt r0 = r7.o
            ank r0 = r0.c
            java.lang.Object r0 = r0.a
            ank r3 = r1.c
            java.lang.Object r3 = r3.a
            boolean r0 = r0.equals(r3)
            if (r0 != 0) goto L_0x02bd
            ajt r0 = r7.o
            aex r0 = r0.b
            boolean r0 = r0.p()
            if (r0 != 0) goto L_0x02bd
            r3 = r2
            goto L_0x02be
        L_0x02bd:
            r3 = 0
        L_0x02be:
            r4 = 4
            long r5 = r7.y(r1)
            r2 = 0
            r0 = r28
            r0.u(r1, r2, r3, r4, r5)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.aja.o(anm):void");
    }

    public final long p(ajt ajt) {
        if (!ajt.c.b()) {
            return agh.m(y(ajt));
        }
        ajt.b.n(ajt.c.a, this.B);
        if (ajt.d == -9223372036854775807L) {
            return ajt.b.o(x(ajt), this.a).a();
        }
        return this.B.f() + agh.m(ajt.d);
    }

    public final void q(int i2, int i3) {
        agd agd = this.N;
        if (i2 != agd.b || i3 != agd.c) {
            this.N = new agd(i2, i3);
            this.g.e(24, new ait(i2, i3));
            r(2, 14, new agd(i2, i3));
        }
    }

    public final void r(int i2, int i3, Object obj) {
        ajy[] ajyArr = this.z;
        int length = ajyArr.length;
        for (int i4 = 0; i4 <= 0; i4++) {
            ajy ajy = ajyArr[i4];
            if (i2 != -1) {
                ajy.J();
                if (i2 != 1) {
                }
            }
            x(this.o);
            ajf ajf = this.f;
            aex aex = this.o.b;
            ajw ajw = new ajw(ajf, ajy, ajf.d);
            yi.h(!ajw.g);
            ajw.c = i3;
            yi.h(!ajw.g);
            ajw.d = obj;
            yi.h(!ajw.g);
            yi.f(true);
            ajw.g = true;
            ajw.b.d(ajw);
        }
    }

    public final void s(Object obj) {
        ArrayList<ajw> arrayList = new ArrayList<>();
        ajy[] ajyArr = this.z;
        int length = ajyArr.length;
        boolean z2 = false;
        for (int i2 = 0; i2 <= 0; i2++) {
            ajyArr[i2].J();
        }
        Object obj2 = this.L;
        if (!(obj2 == null || obj2 == obj)) {
            try {
                for (ajw b2 : arrayList) {
                    b2.b(this.H);
                }
            } catch (InterruptedException unused) {
                Thread.currentThread().interrupt();
            } catch (TimeoutException unused2) {
                z2 = true;
            }
            Object obj3 = this.L;
            Surface surface = this.n;
            if (obj3 == surface) {
                surface.release();
                this.n = null;
            }
        }
        this.L = obj;
        if (z2) {
            A(new ain(2, new ajg(3), 1003));
        }
    }

    public final void t() {
        IllegalStateException illegalStateException;
        this.x.b();
        if (Thread.currentThread() != this.D.getThread()) {
            String r2 = agh.r("Player is accessed on the wrong thread.\nCurrent thread: '%s'\nExpected thread: '%s'\nSee https://developer.android.com/guide/topics/media/issues/player-accessed-on-wrong-thread", Thread.currentThread().getName(), this.D.getThread().getName());
            if (!this.Q) {
                if (this.R) {
                    illegalStateException = null;
                } else {
                    illegalStateException = new IllegalStateException();
                }
                afy.f("ExoPlayerImpl", r2, illegalStateException);
                this.R = true;
                return;
            }
            throw new IllegalStateException(r2);
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:131:0x021d  */
    /* JADX WARNING: Removed duplicated region for block: B:132:0x021f  */
    /* JADX WARNING: Removed duplicated region for block: B:135:0x0226  */
    /* JADX WARNING: Removed duplicated region for block: B:136:0x0228  */
    /* JADX WARNING: Removed duplicated region for block: B:146:0x024a  */
    /* JADX WARNING: Removed duplicated region for block: B:147:0x024c  */
    /* JADX WARNING: Removed duplicated region for block: B:149:0x024f  */
    /* JADX WARNING: Removed duplicated region for block: B:151:0x025f  */
    /* JADX WARNING: Removed duplicated region for block: B:170:0x0308  */
    /* JADX WARNING: Removed duplicated region for block: B:171:0x0338  */
    /* JADX WARNING: Removed duplicated region for block: B:174:0x0350  */
    /* JADX WARNING: Removed duplicated region for block: B:175:0x035d  */
    /* JADX WARNING: Removed duplicated region for block: B:178:0x0380  */
    /* JADX WARNING: Removed duplicated region for block: B:181:0x0396  */
    /* JADX WARNING: Removed duplicated region for block: B:186:0x03b4  */
    /* JADX WARNING: Removed duplicated region for block: B:188:0x03c5  */
    /* JADX WARNING: Removed duplicated region for block: B:189:0x03d5  */
    /* JADX WARNING: Removed duplicated region for block: B:191:0x03d8  */
    /* JADX WARNING: Removed duplicated region for block: B:197:0x03f7  */
    /* JADX WARNING: Removed duplicated region for block: B:205:0x041c  */
    /* JADX WARNING: Removed duplicated region for block: B:208:0x0431  */
    /* JADX WARNING: Removed duplicated region for block: B:211:0x0449  */
    /* JADX WARNING: Removed duplicated region for block: B:216:0x047a  */
    /* JADX WARNING: Removed duplicated region for block: B:217:0x047c  */
    /* JADX WARNING: Removed duplicated region for block: B:221:0x0489  */
    /* JADX WARNING: Removed duplicated region for block: B:226:0x04a9  */
    /* JADX WARNING: Removed duplicated region for block: B:227:0x04ad  */
    /* JADX WARNING: Removed duplicated region for block: B:235:0x04e2  */
    /* JADX WARNING: Removed duplicated region for block: B:236:0x04e4  */
    /* JADX WARNING: Removed duplicated region for block: B:241:0x04fd  */
    /* JADX WARNING: Removed duplicated region for block: B:242:0x04ff  */
    /* JADX WARNING: Removed duplicated region for block: B:245:0x0517 A[ADDED_TO_REGION] */
    /* JADX WARNING: Removed duplicated region for block: B:250:0x0521 A[ADDED_TO_REGION] */
    /* JADX WARNING: Removed duplicated region for block: B:255:0x052b A[ADDED_TO_REGION] */
    /* JADX WARNING: Removed duplicated region for block: B:263:0x053c A[ADDED_TO_REGION] */
    /* JADX WARNING: Removed duplicated region for block: B:268:0x0548 A[ADDED_TO_REGION] */
    /* JADX WARNING: Removed duplicated region for block: B:276:0x0561 A[ADDED_TO_REGION] */
    /* JADX WARNING: Removed duplicated region for block: B:281:0x056f A[ADDED_TO_REGION] */
    /* JADX WARNING: Removed duplicated region for block: B:286:0x0585  */
    /* JADX WARNING: Removed duplicated region for block: B:36:0x00e8  */
    /* JADX WARNING: Removed duplicated region for block: B:41:0x010f  */
    /* JADX WARNING: Removed duplicated region for block: B:48:0x012d  */
    /* JADX WARNING: Removed duplicated region for block: B:56:0x0159  */
    /* JADX WARNING: Removed duplicated region for block: B:57:0x015d  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void u(defpackage.ajt r43, int r44, boolean r45, int r46, long r47) {
        /*
            r42 = this;
            r0 = r42
            r1 = r43
            r2 = r46
            ajt r3 = r0.o
            r0.o = r1
            aex r4 = r3.b
            aex r5 = r1.b
            boolean r4 = r4.equals(r5)
            aex r5 = r3.b
            aex r6 = r1.b
            boolean r7 = r6.p()
            r8 = 2
            r9 = 3
            r10 = -1
            java.lang.Integer r11 = java.lang.Integer.valueOf(r10)
            r12 = 1
            java.lang.Boolean r13 = java.lang.Boolean.valueOf(r12)
            r14 = 0
            java.lang.Boolean r15 = java.lang.Boolean.valueOf(r14)
            if (r7 == 0) goto L_0x0039
            boolean r7 = r5.p()
            if (r7 == 0) goto L_0x0039
            android.util.Pair r5 = new android.util.Pair
            r5.<init>(r15, r11)
            goto L_0x004c
        L_0x0039:
            boolean r7 = r6.p()
            boolean r10 = r5.p()
            if (r7 == r10) goto L_0x0052
            android.util.Pair r5 = new android.util.Pair
            java.lang.Integer r6 = java.lang.Integer.valueOf(r9)
            r5.<init>(r13, r6)
        L_0x004c:
            r6 = r5
            r5 = r2
            r2 = r45
            goto L_0x00d6
        L_0x0052:
            ank r7 = r3.c
            java.lang.Object r7 = r7.a
            aev r10 = r0.B
            aev r7 = r5.n(r7, r10)
            int r7 = r7.c
            aew r10 = r0.a
            aew r5 = r5.o(r7, r10)
            java.lang.Object r5 = r5.b
            ank r7 = r1.c
            java.lang.Object r7 = r7.a
            aev r10 = r0.B
            aev r7 = r6.n(r7, r10)
            int r7 = r7.c
            aew r10 = r0.a
            aew r6 = r6.o(r7, r10)
            java.lang.Object r6 = r6.b
            boolean r5 = r5.equals(r6)
            if (r5 != 0) goto L_0x00ac
            if (r45 == 0) goto L_0x008a
            if (r2 != 0) goto L_0x0088
            r5 = r12
            r6 = r5
            r2 = r14
            goto L_0x0096
        L_0x0088:
            r5 = r12
            goto L_0x008b
        L_0x008a:
            r5 = r14
        L_0x008b:
            r6 = r5
            if (r5 == 0) goto L_0x0093
            if (r2 != r12) goto L_0x0093
            r5 = r6
            r6 = r8
            goto L_0x0096
        L_0x0093:
            if (r4 != 0) goto L_0x00a6
            r6 = r9
        L_0x0096:
            android.util.Pair r7 = new android.util.Pair
            java.lang.Integer r6 = java.lang.Integer.valueOf(r6)
            r7.<init>(r13, r6)
            r6 = r7
            r41 = r5
            r5 = r2
            r2 = r41
            goto L_0x00d6
        L_0x00a6:
            java.lang.IllegalStateException r1 = new java.lang.IllegalStateException
            r1.<init>()
            throw r1
        L_0x00ac:
            if (r45 == 0) goto L_0x00cf
            if (r2 != 0) goto L_0x00cc
            ank r2 = r3.c
            long r5 = r2.d
            ank r2 = r1.c
            long r9 = r2.d
            int r2 = (r5 > r9 ? 1 : (r5 == r9 ? 0 : -1))
            if (r2 >= 0) goto L_0x00c9
            android.util.Pair r5 = new android.util.Pair
            java.lang.Integer r2 = java.lang.Integer.valueOf(r14)
            r5.<init>(r13, r2)
            r6 = r5
            r2 = r12
            r5 = r14
            goto L_0x00d6
        L_0x00c9:
            r2 = r12
            r5 = r14
            goto L_0x00d1
        L_0x00cc:
            r5 = r2
            r2 = r12
            goto L_0x00d1
        L_0x00cf:
            r5 = r2
            r2 = r14
        L_0x00d1:
            android.util.Pair r6 = new android.util.Pair
            r6.<init>(r15, r11)
        L_0x00d6:
            java.lang.Object r9 = r6.first
            java.lang.Boolean r9 = (java.lang.Boolean) r9
            boolean r9 = r9.booleanValue()
            java.lang.Object r6 = r6.second
            java.lang.Integer r6 = (java.lang.Integer) r6
            int r6 = r6.intValue()
            if (r9 == 0) goto L_0x010f
            aex r11 = r1.b
            boolean r11 = r11.p()
            if (r11 != 0) goto L_0x0109
            aex r11 = r1.b
            ank r13 = r1.c
            java.lang.Object r13 = r13.a
            aev r15 = r0.B
            aev r11 = r11.n(r13, r15)
            int r11 = r11.c
            aex r13 = r1.b
            aew r15 = r0.a
            aew r11 = r13.o(r11, r15)
            aeg r11 = r11.d
            goto L_0x010a
        L_0x0109:
            r11 = 0
        L_0x010a:
            aej r13 = defpackage.aej.a
            r0.T = r13
            goto L_0x0110
        L_0x010f:
            r11 = 0
        L_0x0110:
            if (r9 != 0) goto L_0x011c
            java.util.List r13 = r3.j
            java.util.List r15 = r1.j
            boolean r13 = defpackage.fvf.D(r13, r15)
            if (r13 != 0) goto L_0x014f
        L_0x011c:
            aej r13 = r0.T
            aei r15 = new aei
            r15.<init>(r13)
            java.util.List r13 = r1.j
            r7 = r14
        L_0x0126:
            r10 = r13
            hal r10 = (defpackage.hal) r10
            int r10 = r10.c
            if (r7 >= r10) goto L_0x0148
            java.lang.Object r10 = r13.get(r7)
            ael r10 = (defpackage.ael) r10
        L_0x0133:
            int r12 = r10.a()
            if (r14 >= r12) goto L_0x0143
            aek r12 = r10.b(r14)
            r12.a(r15)
            int r14 = r14 + 1
            goto L_0x0133
        L_0x0143:
            int r7 = r7 + 1
            r12 = 1
            r14 = 0
            goto L_0x0126
        L_0x0148:
            aej r7 = new aej
            r7.<init>(r15)
            r0.T = r7
        L_0x014f:
            aex r7 = r42.g()
            boolean r10 = r7.p()
            if (r10 == 0) goto L_0x015d
            aej r7 = r0.T
            goto L_0x020f
        L_0x015d:
            int r10 = r42.c()
            aew r12 = r0.a
            aew r7 = r7.o(r10, r12)
            aeg r7 = r7.d
            aej r10 = r0.T
            aei r12 = new aei
            r12.<init>(r10)
            aej r7 = r7.d
            if (r7 != 0) goto L_0x0176
            goto L_0x020a
        L_0x0176:
            java.lang.CharSequence r10 = r7.b
            if (r10 == 0) goto L_0x017c
            r12.a = r10
        L_0x017c:
            java.lang.CharSequence r10 = r7.c
            if (r10 == 0) goto L_0x0182
            r12.b = r10
        L_0x0182:
            java.lang.CharSequence r10 = r7.d
            if (r10 == 0) goto L_0x0188
            r12.c = r10
        L_0x0188:
            java.lang.CharSequence r10 = r7.e
            if (r10 == 0) goto L_0x018e
            r12.d = r10
        L_0x018e:
            java.lang.CharSequence r10 = r7.f
            if (r10 == 0) goto L_0x0194
            r12.e = r10
        L_0x0194:
            byte[] r10 = r7.g
            if (r10 == 0) goto L_0x01a4
            java.lang.Integer r13 = r7.h
            java.lang.Object r10 = r10.clone()
            byte[] r10 = (byte[]) r10
            r12.f = r10
            r12.g = r13
        L_0x01a4:
            java.lang.Integer r10 = r7.i
            if (r10 == 0) goto L_0x01aa
            r12.h = r10
        L_0x01aa:
            java.lang.Integer r10 = r7.j
            if (r10 == 0) goto L_0x01b0
            r12.i = r10
        L_0x01b0:
            java.lang.Integer r10 = r7.k
            if (r10 == 0) goto L_0x01b6
            r12.j = r10
        L_0x01b6:
            java.lang.Boolean r10 = r7.l
            if (r10 == 0) goto L_0x01bc
            r12.k = r10
        L_0x01bc:
            java.lang.Integer r10 = r7.m
            if (r10 == 0) goto L_0x01c2
            r12.l = r10
        L_0x01c2:
            java.lang.Integer r10 = r7.n
            if (r10 == 0) goto L_0x01c8
            r12.l = r10
        L_0x01c8:
            java.lang.Integer r10 = r7.o
            if (r10 == 0) goto L_0x01ce
            r12.m = r10
        L_0x01ce:
            java.lang.Integer r10 = r7.p
            if (r10 == 0) goto L_0x01d4
            r12.n = r10
        L_0x01d4:
            java.lang.Integer r10 = r7.q
            if (r10 == 0) goto L_0x01da
            r12.o = r10
        L_0x01da:
            java.lang.Integer r10 = r7.r
            if (r10 == 0) goto L_0x01e0
            r12.p = r10
        L_0x01e0:
            java.lang.Integer r10 = r7.s
            if (r10 == 0) goto L_0x01e6
            r12.q = r10
        L_0x01e6:
            java.lang.CharSequence r10 = r7.t
            if (r10 == 0) goto L_0x01ec
            r12.r = r10
        L_0x01ec:
            java.lang.CharSequence r10 = r7.u
            if (r10 == 0) goto L_0x01f2
            r12.s = r10
        L_0x01f2:
            java.lang.CharSequence r10 = r7.v
            if (r10 == 0) goto L_0x01f8
            r12.t = r10
        L_0x01f8:
            java.lang.CharSequence r10 = r7.w
            if (r10 == 0) goto L_0x01fe
            r12.u = r10
        L_0x01fe:
            java.lang.CharSequence r10 = r7.x
            if (r10 == 0) goto L_0x0204
            r12.v = r10
        L_0x0204:
            java.lang.Integer r7 = r7.y
            if (r7 == 0) goto L_0x020a
            r12.w = r7
        L_0x020a:
            aej r7 = new aej
            r7.<init>(r12)
        L_0x020f:
            aej r10 = r0.K
            boolean r10 = r7.equals(r10)
            r0.K = r7
            boolean r7 = r3.l
            boolean r12 = r1.l
            if (r7 == r12) goto L_0x021f
            r12 = 1
            goto L_0x0220
        L_0x021f:
            r12 = 0
        L_0x0220:
            int r7 = r3.f
            int r13 = r1.f
            if (r7 == r13) goto L_0x0228
            r13 = 1
            goto L_0x0229
        L_0x0228:
            r13 = 0
        L_0x0229:
            if (r13 != 0) goto L_0x022d
            if (r12 == 0) goto L_0x0244
        L_0x022d:
            int r7 = r42.d()
            if (r7 == r8) goto L_0x0237
            r14 = 3
            if (r7 == r14) goto L_0x0237
            goto L_0x0244
        L_0x0237:
            r42.t()
            ajt r14 = r0.o
            boolean r14 = r14.p
            r42.l()
            r42.l()
        L_0x0244:
            boolean r14 = r3.h
            boolean r15 = r1.h
            if (r14 == r15) goto L_0x024c
            r14 = 1
            goto L_0x024d
        L_0x024c:
            r14 = 0
        L_0x024d:
            if (r4 != 0) goto L_0x025d
            afv r4 = r0.g
            aiv r15 = new aiv
            r7 = r44
            r8 = 1
            r15.<init>(r1, r7, r8)
            r7 = 0
            r4.c(r7, r15)
        L_0x025d:
            if (r2 == 0) goto L_0x037e
            aev r2 = new aev
            r2.<init>()
            aex r7 = r3.b
            boolean r7 = r7.p()
            if (r7 != 0) goto L_0x0298
            ank r7 = r3.c
            java.lang.Object r7 = r7.a
            aex r8 = r3.b
            r8.n(r7, r2)
            int r8 = r2.c
            aex r15 = r3.b
            int r15 = r15.a(r7)
            aex r4 = r3.b
            r18 = r7
            aew r7 = r0.a
            aew r4 = r4.o(r8, r7)
            java.lang.Object r4 = r4.b
            aew r7 = r0.a
            aeg r7 = r7.d
            r20 = r4
            r22 = r7
            r21 = r8
            r24 = r15
            r23 = r18
            goto L_0x02a2
        L_0x0298:
            r20 = 0
            r21 = -1
            r22 = 0
            r23 = 0
            r24 = -1
        L_0x02a2:
            if (r5 != 0) goto L_0x02cc
            ank r4 = r3.c
            boolean r4 = r4.b()
            if (r4 == 0) goto L_0x02bb
            ank r4 = r3.c
            int r7 = r4.b
            int r4 = r4.c
            long r7 = r2.e(r7, r4)
            long r18 = z(r3)
            goto L_0x02df
        L_0x02bb:
            ank r4 = r3.c
            int r4 = r4.e
            r7 = -1
            if (r4 == r7) goto L_0x02c9
            ajt r2 = r0.o
            long r7 = z(r2)
            goto L_0x02dd
        L_0x02c9:
            long r7 = r2.d
            goto L_0x02dd
        L_0x02cc:
            ank r2 = r3.c
            boolean r2 = r2.b()
            if (r2 == 0) goto L_0x02db
            long r7 = r3.s
            long r18 = z(r3)
            goto L_0x02df
        L_0x02db:
            long r7 = r3.s
        L_0x02dd:
            r18 = r7
        L_0x02df:
            aes r2 = new aes
            int r4 = defpackage.agh.a
            ank r4 = r3.c
            int r15 = r4.b
            int r4 = r4.c
            long r25 = defpackage.agh.m(r7)
            long r27 = defpackage.agh.m(r18)
            r19 = r2
            r29 = r15
            r30 = r4
            r19.<init>(r20, r21, r22, r23, r24, r25, r27, r29, r30)
            int r4 = r42.c()
            ajt r7 = r0.o
            aex r7 = r7.b
            boolean r7 = r7.p()
            if (r7 != 0) goto L_0x0338
            ajt r7 = r0.o
            ank r8 = r7.c
            java.lang.Object r8 = r8.a
            aex r7 = r7.b
            aev r15 = r0.B
            r7.n(r8, r15)
            ajt r7 = r0.o
            aex r7 = r7.b
            int r7 = r7.a(r8)
            ajt r15 = r0.o
            aex r15 = r15.b
            r46 = r7
            aew r7 = r0.a
            aew r7 = r15.o(r4, r7)
            java.lang.Object r7 = r7.b
            aew r15 = r0.a
            aeg r15 = r15.d
            r34 = r46
            r30 = r7
            r33 = r8
            r32 = r15
            goto L_0x0340
        L_0x0338:
            r30 = 0
            r32 = 0
            r33 = 0
            r34 = -1
        L_0x0340:
            long r35 = defpackage.agh.m(r47)
            aes r7 = new aes
            ajt r8 = r0.o
            ank r8 = r8.c
            boolean r8 = r8.b()
            if (r8 == 0) goto L_0x035d
            ajt r8 = r0.o
            long r18 = z(r8)
            long r18 = defpackage.agh.m(r18)
            r37 = r18
            goto L_0x035f
        L_0x035d:
            r37 = r35
        L_0x035f:
            ajt r8 = r0.o
            ank r8 = r8.c
            int r15 = r8.b
            int r8 = r8.c
            r29 = r7
            r31 = r4
            r39 = r15
            r40 = r8
            r29.<init>(r30, r31, r32, r33, r34, r35, r37, r39, r40)
            afv r4 = r0.g
            aiu r8 = new aiu
            r8.<init>(r5, r2, r7)
            r2 = 11
            r4.c(r2, r8)
        L_0x037e:
            if (r9 == 0) goto L_0x038c
            afv r2 = r0.g
            aiv r4 = new aiv
            r5 = 0
            r4.<init>(r11, r6, r5)
            r5 = 1
            r2.c(r5, r4)
        L_0x038c:
            ain r2 = r3.g
            ain r4 = r1.g
            r5 = 9
            r6 = 10
            if (r2 == r4) goto L_0x03ae
            afv r2 = r0.g
            ais r4 = new ais
            r4.<init>(r1, r5)
            r2.c(r6, r4)
            ain r2 = r1.g
            if (r2 == 0) goto L_0x03ae
            afv r2 = r0.g
            ais r4 = new ais
            r4.<init>(r1, r6)
            r2.c(r6, r4)
        L_0x03ae:
            dip r2 = r3.u
            dip r4 = r1.u
            if (r2 == r4) goto L_0x03c3
            java.lang.Object r2 = r4.d
            afv r2 = r0.g
            ais r4 = new ais
            r7 = 11
            r4.<init>(r1, r7)
            r7 = 2
            r2.c(r7, r4)
        L_0x03c3:
            if (r10 != 0) goto L_0x03d5
            aej r2 = r0.K
            afv r4 = r0.g
            ais r7 = new ais
            r8 = 1
            r7.<init>(r2, r8)
            r2 = 14
            r4.c(r2, r7)
            goto L_0x03d6
        L_0x03d5:
            r8 = 1
        L_0x03d6:
            if (r14 == 0) goto L_0x03e4
            afv r2 = r0.g
            ais r4 = new ais
            r7 = 0
            r4.<init>(r1, r7)
            r7 = 3
            r2.c(r7, r4)
        L_0x03e4:
            if (r13 != 0) goto L_0x03e8
            if (r12 == 0) goto L_0x03f4
        L_0x03e8:
            afv r2 = r0.g
            ais r4 = new ais
            r9 = 2
            r4.<init>(r1, r9)
            r9 = -1
            r2.c(r9, r4)
        L_0x03f4:
            r2 = 4
            if (r13 == 0) goto L_0x0402
            afv r4 = r0.g
            ais r9 = new ais
            r7 = 3
            r9.<init>(r1, r7)
            r4.c(r2, r9)
        L_0x0402:
            r4 = 5
            if (r12 != 0) goto L_0x040b
            int r7 = r3.m
            int r9 = r1.m
            if (r7 == r9) goto L_0x0415
        L_0x040b:
            afv r7 = r0.g
            ais r9 = new ais
            r9.<init>(r1, r2)
            r7.c(r4, r9)
        L_0x0415:
            int r7 = r3.n
            int r9 = r1.n
            r10 = 6
            if (r7 == r9) goto L_0x0426
            afv r7 = r0.g
            ais r9 = new ais
            r9.<init>(r1, r10)
            r7.c(r10, r9)
        L_0x0426:
            boolean r7 = r3.f()
            boolean r9 = r43.f()
            r11 = 7
            if (r7 == r9) goto L_0x043b
            afv r7 = r0.g
            ais r9 = new ais
            r9.<init>(r1, r11)
            r7.c(r11, r9)
        L_0x043b:
            aep r7 = r3.o
            aep r9 = r1.o
            boolean r7 = r7.equals(r9)
            r9 = 8
            r12 = 12
            if (r7 != 0) goto L_0x0453
            afv r7 = r0.g
            ais r13 = new ais
            r13.<init>(r1, r9)
            r7.c(r12, r13)
        L_0x0453:
            aeq r7 = r0.m
            aet r13 = r0.c
            aeq r14 = r0.b
            int r15 = defpackage.agh.a
            boolean r15 = r13.m()
            r8 = r13
            adl r8 = (defpackage.adl) r8
            aex r12 = r8.g()
            boolean r17 = r12.p()
            if (r17 != 0) goto L_0x047c
            int r6 = r8.c()
            aew r5 = r8.a
            aew r5 = r12.o(r6, r5)
            boolean r5 = r5.h
            if (r5 == 0) goto L_0x047c
            r5 = 1
            goto L_0x047d
        L_0x047c:
            r5 = 0
        L_0x047d:
            aex r6 = r8.g()
            boolean r12 = r6.p()
            if (r12 == 0) goto L_0x0489
        L_0x0487:
            r6 = 0
            goto L_0x049f
        L_0x0489:
            int r12 = r8.c()
            r17 = r13
            aja r17 = (defpackage.aja) r17
            r17.t()
            r17.t()
            int r6 = r6.q(r12)
            r12 = -1
            if (r6 == r12) goto L_0x0487
            r6 = 1
        L_0x049f:
            aex r12 = r8.g()
            boolean r17 = r12.p()
            if (r17 == 0) goto L_0x04ad
            r11 = 0
            r16 = 0
            goto L_0x04c8
        L_0x04ad:
            int r9 = r8.c()
            r17 = r13
            aja r17 = (defpackage.aja) r17
            r17.t()
            r17.t()
            r11 = 0
            int r9 = r12.j(r9, r11, r11)
            r12 = -1
            if (r9 == r12) goto L_0x04c6
            r16 = 1
            goto L_0x04c8
        L_0x04c6:
            r16 = r11
        L_0x04c8:
            aex r9 = r8.g()
            boolean r12 = r9.p()
            if (r12 != 0) goto L_0x04e4
            int r12 = r8.c()
            aew r11 = r8.a
            aew r9 = r9.o(r12, r11)
            boolean r9 = r9.b()
            if (r9 == 0) goto L_0x04e4
            r9 = 1
            goto L_0x04e5
        L_0x04e4:
            r9 = 0
        L_0x04e5:
            aex r11 = r8.g()
            boolean r12 = r11.p()
            if (r12 != 0) goto L_0x04ff
            int r12 = r8.c()
            aew r8 = r8.a
            aew r8 = r11.o(r12, r8)
            boolean r8 = r8.i
            if (r8 == 0) goto L_0x04ff
            r8 = 1
            goto L_0x0500
        L_0x04ff:
            r8 = 0
        L_0x0500:
            aex r11 = r13.g()
            boolean r11 = r11.p()
            ads r12 = new ads
            r12.<init>()
            defpackage.yh.i(r14, r12)
            r13 = r15 ^ 1
            defpackage.yh.j(r2, r13, r12)
            if (r5 == 0) goto L_0x051b
            if (r15 != 0) goto L_0x051b
            r2 = 1
            goto L_0x051c
        L_0x051b:
            r2 = 0
        L_0x051c:
            defpackage.yh.j(r4, r2, r12)
            if (r6 == 0) goto L_0x0525
            if (r15 != 0) goto L_0x0525
            r2 = 1
            goto L_0x0526
        L_0x0525:
            r2 = 0
        L_0x0526:
            defpackage.yh.j(r10, r2, r12)
            if (r11 != 0) goto L_0x0535
            if (r6 != 0) goto L_0x0531
            if (r9 == 0) goto L_0x0531
            if (r5 == 0) goto L_0x0535
        L_0x0531:
            if (r15 != 0) goto L_0x0535
            r2 = 1
            goto L_0x0536
        L_0x0535:
            r2 = 0
        L_0x0536:
            r6 = 7
            defpackage.yh.j(r6, r2, r12)
            if (r16 == 0) goto L_0x0540
            if (r15 != 0) goto L_0x0540
            r2 = 1
            goto L_0x0541
        L_0x0540:
            r2 = 0
        L_0x0541:
            r6 = 8
            defpackage.yh.j(r6, r2, r12)
            if (r11 != 0) goto L_0x0554
            if (r16 != 0) goto L_0x054e
            if (r9 == 0) goto L_0x0554
            if (r8 == 0) goto L_0x0554
        L_0x054e:
            if (r15 != 0) goto L_0x0554
            r2 = 9
            r8 = 1
            goto L_0x0557
        L_0x0554:
            r2 = 9
            r8 = 0
        L_0x0557:
            defpackage.yh.j(r2, r8, r12)
            r2 = 10
            defpackage.yh.j(r2, r13, r12)
            if (r5 == 0) goto L_0x0567
            if (r15 != 0) goto L_0x0567
            r2 = 11
            r8 = 1
            goto L_0x056a
        L_0x0567:
            r2 = 11
            r8 = 0
        L_0x056a:
            defpackage.yh.j(r2, r8, r12)
            if (r5 == 0) goto L_0x0573
            if (r15 != 0) goto L_0x0573
            r2 = 1
            goto L_0x0574
        L_0x0573:
            r2 = 0
        L_0x0574:
            r5 = 12
            defpackage.yh.j(r5, r2, r12)
            aeq r2 = defpackage.yh.h(r12)
            r0.m = r2
            boolean r2 = r2.equals(r7)
            if (r2 != 0) goto L_0x0591
            afv r2 = r0.g
            ais r5 = new ais
            r5.<init>(r0, r4)
            r4 = 13
            r2.c(r4, r5)
        L_0x0591:
            afv r2 = r0.g
            r2.b()
            boolean r2 = r3.p
            boolean r1 = r1.p
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.aja.u(ajt, int, boolean, int, long):void");
    }

    public final void v(aex aex, ank ank) {
        aex.n(ank.a, this.B);
    }

    public final void w(boolean z2, int i2) {
        ajt ajt = this.o;
        if (ajt.l != z2 || ajt.n != 0 || ajt.m != 1) {
            this.j++;
            boolean z3 = ajt.p;
            ajt b2 = ajt.b(z2, 1, 0);
            Handler handler = ((agf) this.f.c).b;
            iih h2 = agf.h();
            h2.a = handler.obtainMessage(1, z2 ? 1 : 0, 1);
            h2.d();
            u(b2, 0, false, 5, -9223372036854775807L);
        }
    }
}
