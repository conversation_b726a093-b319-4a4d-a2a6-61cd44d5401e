package j$.time.temporal;

import j$.time.chrono.C0010i;
import j$.time.chrono.u;
import j$.time.g;

/* 'enum' modifier removed */
final class e extends f {
    e() {
        super("WEEK_BASED_YEAR", 3);
    }

    public final long g(m mVar) {
        if (p(mVar)) {
            return (long) f.M(g.K(mVar));
        }
        throw new RuntimeException("Unsupported field: WeekBasedYear");
    }

    public final t m() {
        return a.YEAR.m();
    }

    public final boolean p(m mVar) {
        if (mVar.e(a.EPOCH_DAY)) {
            p pVar = h.a;
            if (C0010i.p(mVar).equals(u.d)) {
                return true;
            }
        }
        return false;
    }

    public final k s(k kVar, long j) {
        if (p(kVar)) {
            int a = a.YEAR.m().a(j, f.WEEK_BASED_YEAR);
            g K = g.K(kVar);
            a aVar = a.DAY_OF_WEEK;
            int p = K.p(aVar);
            int I = f.I(K);
            if (I == 53 && f.N(a) == 52) {
                I = 52;
            }
            g T = g.T(a, 1, 4);
            return kVar.s(T.X((long) (((I - 1) * 7) + (p - T.p(aVar)))));
        }
        throw new RuntimeException("Unsupported field: WeekBasedYear");
    }

    public final t t(m mVar) {
        if (p(mVar)) {
            return m();
        }
        throw new RuntimeException("Unsupported field: WeekBasedYear");
    }

    public final String toString() {
        return "WeekBasedYear";
    }
}
