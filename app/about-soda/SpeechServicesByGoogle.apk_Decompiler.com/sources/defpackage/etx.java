package defpackage;

/* renamed from: etx  reason: default package */
/* compiled from: PG */
final class etx extends jme {
    /* synthetic */ Object a;
    final /* synthetic */ eua b;
    int c;
    eua d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public etx(eua eua, jlr jlr) {
        super(jlr);
        this.b = eua;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.c |= Integer.MIN_VALUE;
        return this.b.g(this);
    }
}
