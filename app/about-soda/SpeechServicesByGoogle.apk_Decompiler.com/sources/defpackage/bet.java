package defpackage;

/* renamed from: bet  reason: default package */
/* compiled from: PG */
public final class bet extends jmi implements jne {
    int a;
    final /* synthetic */ Object b;
    final /* synthetic */ Object c;
    private final /* synthetic */ int d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bet(avg avg, jmp jmp, jlr jlr, int i) {
        super(2, jlr);
        this.d = i;
        this.b = avg;
        this.c = jmp;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        if (this.d != 0) {
            return ((bet) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
        return ((bet) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    public final Object bk(Object obj) {
        if (this.d != 0) {
            jlx jlx = jlx.COROUTINE_SUSPENDED;
            int i = this.a;
            jji.c(obj);
            if (i == 0) {
                Object obj2 = this.b;
                this.a = 1;
                if (((avg) obj2).c(this) == jlx) {
                    return jlx;
                }
            }
            return jkd.a;
        }
        jlx jlx2 = jlx.COROUTINE_SUSPENDED;
        if (this.a != 0) {
            jji.c(obj);
        } else {
            jji.c(obj);
            Object obj3 = this.b;
            this.a = 1;
            if (jnu.M(((bew) obj3).b, this) == jlx2) {
                return jlx2;
            }
        }
        bbk.a();
        long j = bfc.a;
        ((jtq) this.c).g(new ber(7));
        return jkd.a;
    }

    /* JADX WARNING: type inference failed for: r0v2, types: [java.lang.Object, jmp] */
    public final jlr c(Object obj, jlr jlr) {
        if (this.d != 0) {
            return new bet((avg) this.b, (jmp) this.c, jlr, 1);
        }
        return new bet((bew) this.b, (jty) this.c, jlr, 0);
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bet(bew bew, jty jty, jlr jlr, int i) {
        super(2, jlr);
        this.d = i;
        this.b = bew;
        this.c = jty;
    }
}
