package defpackage;

/* renamed from: evw  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class evw implements gsb {
    public final /* synthetic */ bmu a;
    private final /* synthetic */ int b;

    public /* synthetic */ evw(bmu bmu, int i) {
        this.b = i;
        this.a = bmu;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                fqy g = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/headphone/reach_media_push_quota_counter", new fqx("is_quota_reached", Boolean.class));
                g.c();
                return g;
            case 1:
                fqv c = ((frc) this.a.a).c("/client_streamz/android_gsa/assistant/verticals/memory/backfill/duration", new fqx[0]);
                c.c();
                return c;
            case 2:
                fqy g2 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/headphone/recommendation_icon_duplicate_counter", new fqx("icon_duplicates", Integer.class));
                g2.c();
                return g2;
            case 3:
                fqy g3 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/headphone/recommendation_icon_loading_error_counter", new fqx[0]);
                g3.c();
                return g3;
            case 4:
                fqy g4 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/headphone/recommendation_status_counter", new fqx("status", String.class), new fqx("producer_status", String.class), new fqx("is_tng", Boolean.class), new fqx("device_type", String.class));
                g4.c();
                return g4;
            case 5:
                fqy g5 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/appactions/clock_inventory_data_fetch_count", new fqx("android_version", Integer.class), new fqx("source", String.class));
                g5.c();
                return g5;
            case 6:
                fqy g6 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/headphone/recommendation_sync_status_counter", new fqx("status", String.class), new fqx("type", String.class), new fqx("app", String.class), new fqx("minutes_since_last_update", Integer.class), new fqx("rendered_card_size_bytes", Integer.class), new fqx("number_of_media_items_removed", Integer.class));
                g6.c();
                return g6;
            case 7:
                fqy g7 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/headphone/recommendation_timeout_counter", new fqx[0]);
                g7.c();
                return g7;
            case 8:
                fqy g8 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/headphone/successful_triggers_device_type", new fqx("device_type", String.class));
                g8.c();
                return g8;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fqy g9 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/holiday_alarms/changed_by_user_count", new fqx("changed", Boolean.class));
                g9.c();
                return g9;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fqy g10 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/holiday_alarms/extract_alarm_failure_count", new fqx("app_package_name", String.class), new fqx("status_code", Integer.class));
                g10.c();
                return g10;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                fqy g11 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/verticals/memory/backfill/finished_count", new fqx("outcome", String.class));
                g11.c();
                return g11;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                fqy g12 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/holiday_alarms/fetch_public_events_attempt_count", new fqx("success", Boolean.class));
                g12.c();
                return g12;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                fqy g13 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/holiday_alarms/public_events_count", new fqx("event_mid", String.class), new fqx("is_tng", Boolean.class));
                g13.c();
                return g13;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                fqy g14 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/hotel/hotel_check_in_time_granularity", new fqx("hotel_check_in_time_granularity", String.class));
                g14.c();
                return g14;
            case 15:
                fqy g15 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/hotel/hotel_check_out_time_granularity", new fqx("hotel_check_out_time_granularity", String.class));
                g15.c();
                return g15;
            case 16:
                fqy g16 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/impression", new fqx("app_version", String.class), new fqx("code_path", String.class), new fqx("content_type", String.class), new fqx("surfaces", String.class));
                g16.c();
                return g16;
            case 17:
                fqy g17 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/appactions/clock_inventory_data_fetch_failed_count", new fqx("exception", String.class), new fqx("android_version", Integer.class), new fqx("source", String.class));
                g17.c();
                return g17;
            case 18:
                fqy g18 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/location_card_feedback", new fqx("app_version", String.class), new fqx("feedback_type", String.class));
                g18.c();
                return g18;
            case 19:
                fqy g19 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/location_inference_finished", new fqx("app_version", String.class), new fqx("is_place_inferred", Boolean.class));
                g19.c();
                return g19;
            default:
                fqv c2 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/location_inference_latency", new fqx("app_version", String.class));
                c2.c();
                return c2;
        }
    }
}
