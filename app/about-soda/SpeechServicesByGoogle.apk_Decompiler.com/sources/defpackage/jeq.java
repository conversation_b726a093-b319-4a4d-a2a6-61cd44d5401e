package defpackage;

import j$.util.DesugarCollections;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.logging.Logger;

/* renamed from: jeq  reason: default package */
/* compiled from: PG */
public final class jeq extends isv implements iqk {
    public static final Logger b = Logger.getLogger(jeq.class.getName());
    public static final jew c = new jel();
    public Executor d;
    public final iqc e;
    public final iqc f;
    public final List g;
    public final isy[] h;
    public final long i;
    public itg j;
    public boolean k;
    public final Object l = new Object();
    public boolean m;
    public final Set n = new HashSet();
    public final ipq o;
    public final ipu p;
    public final iqi q;
    public final ixo r;
    public final jfb s;
    private final iql t;
    private final jcf u;
    private boolean v;
    private boolean w;
    private boolean x;
    private final izz y;

    public jeq(jet jet, izz izz, ipq ipq) {
        jcf jcf = jet.i;
        a.w(jcf, "executorPool");
        this.u = jcf;
        dlv dlv = jet.s;
        HashMap hashMap = new HashMap();
        for (ixj d2 : ((HashMap) dlv.a).values()) {
            for (ixj ixj : d2.d()) {
                hashMap.put(((isa) ixj.a).b, ixj);
            }
        }
        DesugarCollections.unmodifiableList(new ArrayList(((HashMap) dlv.a).values()));
        this.e = new izy(DesugarCollections.unmodifiableMap(hashMap));
        iqc iqc = jet.h;
        a.w(iqc, "fallbackRegistry");
        this.f = iqc;
        this.y = izz;
        this.t = iql.b("Server", String.valueOf(f()));
        a.w(ipq, "rootContext");
        this.o = new ipq(ipq.f, ipq.g + 1);
        this.p = jet.j;
        this.g = DesugarCollections.unmodifiableList(new ArrayList(jet.d));
        List list = jet.e;
        this.h = (isy[]) list.toArray(new isy[list.size()]);
        this.i = jet.l;
        iqi iqi = jet.q;
        this.q = iqi;
        this.r = new ixo(jfl.a);
        jfb jfb = jet.r;
        a.w(jfb, "ticker");
        this.s = jfb;
        iqh iqh = (iqh) iqi.g.put(Long.valueOf(iqi.a(this)), new iqh());
        iqi.b(iqi.c, this);
    }

    private final List f() {
        List unmodifiableList;
        synchronized (this.l) {
            unmodifiableList = DesugarCollections.unmodifiableList(this.y.b());
        }
        return unmodifiableList;
    }

    public final List a() {
        List f2;
        synchronized (this.l) {
            fvf.aG(this.v, "Not started");
            fvf.aG(!this.x, "Already terminated");
            f2 = f();
        }
        return f2;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:22:0x003a, code lost:
        if (r3 == false) goto L_?;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:23:0x003c, code lost:
        r1 = r2.size();
        r3 = 0;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:24:0x0041, code lost:
        if (r3 >= r1) goto L_0x004f;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:25:0x0043, code lost:
        ((defpackage.jex) r2.get(r3)).p(r0);
        r3 = r3 + 1;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:41:?, code lost:
        return;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:42:?, code lost:
        return;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final /* bridge */ /* synthetic */ void b() {
        /*
            r5 = this;
            java.lang.Object r0 = r5.l
            monitor-enter(r0)
            boolean r1 = r5.w     // Catch:{ all -> 0x0053 }
            if (r1 == 0) goto L_0x0009
            monitor-exit(r0)     // Catch:{ all -> 0x0053 }
            goto L_0x001d
        L_0x0009:
            r1 = 1
            r5.w = r1     // Catch:{ all -> 0x0053 }
            boolean r2 = r5.v     // Catch:{ all -> 0x0053 }
            if (r2 != 0) goto L_0x0015
            r5.m = r1     // Catch:{ all -> 0x0053 }
            r5.e()     // Catch:{ all -> 0x0053 }
        L_0x0015:
            monitor-exit(r0)     // Catch:{ all -> 0x0053 }
            if (r2 == 0) goto L_0x001d
            izz r0 = r5.y
            r0.d()
        L_0x001d:
            itg r0 = defpackage.itg.k
            java.lang.String r1 = "Server shutdownNow invoked"
            itg r0 = r0.e(r1)
            java.lang.Object r1 = r5.l
            monitor-enter(r1)
            itg r2 = r5.j     // Catch:{ all -> 0x0050 }
            if (r2 == 0) goto L_0x002e
            monitor-exit(r1)     // Catch:{ all -> 0x0050 }
            return
        L_0x002e:
            r5.j = r0     // Catch:{ all -> 0x0050 }
            java.util.ArrayList r2 = new java.util.ArrayList     // Catch:{ all -> 0x0050 }
            java.util.Set r3 = r5.n     // Catch:{ all -> 0x0050 }
            r2.<init>(r3)     // Catch:{ all -> 0x0050 }
            boolean r3 = r5.k     // Catch:{ all -> 0x0050 }
            monitor-exit(r1)     // Catch:{ all -> 0x0050 }
            if (r3 == 0) goto L_0x004f
            int r1 = r2.size()
            r3 = 0
        L_0x0041:
            if (r3 >= r1) goto L_0x004f
            java.lang.Object r4 = r2.get(r3)
            jex r4 = (defpackage.jex) r4
            r4.p(r0)
            int r3 = r3 + 1
            goto L_0x0041
        L_0x004f:
            return
        L_0x0050:
            r0 = move-exception
            monitor-exit(r1)     // Catch:{ all -> 0x0050 }
            throw r0
        L_0x0053:
            r1 = move-exception
            monitor-exit(r0)     // Catch:{ all -> 0x0053 }
            throw r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.jeq.b():void");
    }

    public final iql c() {
        return this.t;
    }

    /* JADX WARNING: type inference failed for: r1v7, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final /* synthetic */ void d() {
        synchronized (this.l) {
            fvf.aG(!this.v, "Already started");
            fvf.aG(!this.w, "Shutting down");
            this.y.e(new jem(this));
            ? a = this.u.a();
            a.w(a, "executor");
            this.d = a;
            this.v = true;
        }
    }

    public final void e() {
        synchronized (this.l) {
            if (this.w && this.n.isEmpty() && this.m) {
                if (!this.x) {
                    this.x = true;
                    iqi iqi = this.q;
                    iqi.c(iqi.c, this);
                    iqh iqh = (iqh) iqi.g.remove(Long.valueOf(iqi.a(this)));
                    Executor executor = this.d;
                    if (executor != null) {
                        this.u.b(executor);
                        this.d = null;
                    }
                    this.l.notifyAll();
                } else {
                    throw new AssertionError("Server already terminated");
                }
            }
        }
    }

    public final String toString() {
        grg r2 = ftd.r(this);
        r2.f("logId", this.t.a);
        r2.b("transportServer", this.y);
        return r2.toString();
    }
}
