package defpackage;

import android.media.AudioManager$AudioPlaybackCallback;
import java.util.List;

/* renamed from: emj  reason: default package */
/* compiled from: PG */
final class emj extends AudioManager$AudioPlaybackCallback {
    final /* synthetic */ emk a;

    public emj(emk emk) {
        this.a = emk;
    }

    public final void onPlaybackConfigChanged(List list) {
        this.a.c(list);
    }
}
