<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt">
    <item android:state_pressed="true" android:color="?attr/colorPrimaryStateContentGoogle"/>
    <item android:state_focused="true" android:color="?attr/colorPrimaryStateContentGoogle"/>
    <item android:color="?attr/colorPrimaryStateContentGoogle" android:state_hovered="true"/>
    <item android:color="?attr/colorOnSurface"/>
</selector>
