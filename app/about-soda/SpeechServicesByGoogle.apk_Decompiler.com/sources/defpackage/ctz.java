package defpackage;

/* renamed from: ctz  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ctz implements hko {
    public final /* synthetic */ cuf a;
    public final /* synthetic */ ctg b;
    public final /* synthetic */ boolean c;
    public final /* synthetic */ csk d;
    public final /* synthetic */ String e;

    public /* synthetic */ ctz(cuf cuf, ctg ctg, boolean z, csk csk, String str) {
        this.a = cuf;
        this.b = ctg;
        this.c = z;
        this.d = csk;
        this.e = str;
    }

    public final hme a(Object obj) {
        boolean z;
        cxh cxh = (cxh) obj;
        csx csx = cxh.a;
        if (csx != null) {
            return hfc.K(new cva(csx));
        }
        csk csk = this.d;
        boolean z2 = this.c;
        ctg ctg = this.b;
        csx csx2 = cxh.b;
        if (csx2 == null) {
            kml a2 = csi.a();
            a2.b = csh.GROUP_NOT_FOUND_ERROR;
            a2.c = "Nothing to download for file group: ".concat(String.valueOf(ctg.b));
            csi a3 = a2.a();
            if (z2) {
                ((csl) csk.g.b()).b(a3);
            }
            return hfc.J(a3);
        }
        cuf cuf = this.a;
        if (z2) {
            ((czp) cuf.e.b()).g(csk.a, (csl) csk.g.b());
            z = true;
        } else {
            z = false;
        }
        String str = this.e;
        czw f = czw.e(cuf.q(csx2, cuf.o(ctg, csx2), (String) null, 2, false, csk.i, cuf.c, cuf.d, cuf.k)).f(new amv(12), cuf.d).f(new cua(cuf, z, csk, str), cuf.d);
        ftd.M(f.b, new cub(cuf, z, str), cuf.d);
        return f.f(new amv(11), hld.a);
    }
}
