package defpackage;

import android.media.MediaFormat;
import android.media.metrics.LogSessionId;
import java.math.RoundingMode;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Locale;

/* renamed from: ame  reason: default package */
/* compiled from: PG */
public final class ame {
    public static void a(alx alx, akt akt) {
        aks aks = akt.b;
        yi.k(aks);
        LogSessionId logSessionId = aks.a;
        if (!logSessionId.equals(ut$$ExternalSyntheticApiModelOutline0.m())) {
            ((MediaFormat) alx.b).setString("log-session-id", logSessionId.getStringId());
        }
    }

    public static final long b(aqc aqc, long j) {
        if (j == -1 || j == 0) {
            return -9223372036854775807L;
        }
        return agh.k((j * ((long) aqc.g)) - 1, 1000000, (long) aqc.d, RoundingMode.FLOOR);
    }

    public static int c(agc agc, int i) {
        byte[] bArr = agc.a;
        int i2 = agc.b;
        int i3 = i2;
        while (true) {
            int i4 = i3 + 1;
            if (i4 >= i2 + i) {
                return i;
            }
            if ((bArr[i3] & 255) == 255 && bArr[i4] == 0) {
                System.arraycopy(bArr, i3 + 2, bArr, i4, (i - (i3 - i2)) - 2);
                i--;
            }
            i3 = i4;
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:19:0x0043, code lost:
        r4 = false;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static boolean d(defpackage.agc r21, int r22, int r23, boolean r24) {
        /*
            r1 = r21
            r0 = r22
            int r2 = r1.b
        L_0x0006:
            int r3 = r21.a()     // Catch:{ all -> 0x00a6 }
            r4 = 1
            r5 = r23
            if (r3 < r5) goto L_0x00a2
            r3 = 3
            r6 = 0
            if (r0 < r3) goto L_0x0020
            int r7 = r21.c()     // Catch:{ all -> 0x00a6 }
            long r8 = r21.o()     // Catch:{ all -> 0x00a6 }
            int r10 = r21.k()     // Catch:{ all -> 0x00a6 }
            goto L_0x002a
        L_0x0020:
            int r7 = r21.i()     // Catch:{ all -> 0x00a6 }
            int r8 = r21.i()     // Catch:{ all -> 0x00a6 }
            long r8 = (long) r8     // Catch:{ all -> 0x00a6 }
            r10 = r6
        L_0x002a:
            r11 = 0
            if (r7 != 0) goto L_0x0036
            int r7 = (r8 > r11 ? 1 : (r8 == r11 ? 0 : -1))
            if (r7 != 0) goto L_0x0036
            if (r10 != 0) goto L_0x0036
            goto L_0x00a2
        L_0x0036:
            r7 = 4
            if (r0 != r7) goto L_0x0066
            if (r24 != 0) goto L_0x0066
            r13 = 8421504(0x808080, double:4.160776E-317)
            long r13 = r13 & r8
            int r11 = (r13 > r11 ? 1 : (r13 == r11 ? 0 : -1))
            if (r11 == 0) goto L_0x0046
        L_0x0043:
            r4 = r6
            goto L_0x00a2
        L_0x0046:
            r11 = 255(0xff, double:1.26E-321)
            long r13 = r8 & r11
            r15 = 8
            long r15 = r8 >> r15
            r17 = 16
            long r17 = r8 >> r17
            r19 = 24
            long r8 = r8 >> r19
            long r15 = r15 & r11
            long r11 = r17 & r11
            r17 = 7
            long r15 = r15 << r17
            long r13 = r13 | r15
            r15 = 14
            long r11 = r11 << r15
            long r11 = r11 | r13
            r13 = 21
            long r8 = r8 << r13
            long r8 = r8 | r11
        L_0x0066:
            if (r0 != r7) goto L_0x0076
            r3 = r10 & 64
            if (r3 == 0) goto L_0x006d
            goto L_0x006e
        L_0x006d:
            r4 = r6
        L_0x006e:
            r3 = r10 & 1
            r20 = r4
            r4 = r3
            r3 = r20
            goto L_0x0088
        L_0x0076:
            if (r0 != r3) goto L_0x0086
            r3 = r10 & 32
            if (r3 == 0) goto L_0x007e
            r3 = r4
            goto L_0x007f
        L_0x007e:
            r3 = r6
        L_0x007f:
            r7 = r10 & 128(0x80, float:1.794E-43)
            if (r7 == 0) goto L_0x0084
            goto L_0x0088
        L_0x0084:
            r4 = r6
            goto L_0x0088
        L_0x0086:
            r3 = r6
            r4 = r3
        L_0x0088:
            if (r4 == 0) goto L_0x008c
            int r3 = r3 + 4
        L_0x008c:
            long r3 = (long) r3     // Catch:{ all -> 0x00a6 }
            int r3 = (r8 > r3 ? 1 : (r8 == r3 ? 0 : -1))
            if (r3 >= 0) goto L_0x0092
            goto L_0x0043
        L_0x0092:
            int r3 = r21.a()     // Catch:{ all -> 0x00a6 }
            long r3 = (long) r3     // Catch:{ all -> 0x00a6 }
            int r3 = (r3 > r8 ? 1 : (r3 == r8 ? 0 : -1))
            if (r3 >= 0) goto L_0x009c
            goto L_0x0043
        L_0x009c:
            int r3 = (int) r8     // Catch:{ all -> 0x00a6 }
            r1.w(r3)     // Catch:{ all -> 0x00a6 }
            goto L_0x0006
        L_0x00a2:
            r1.v(r2)
            return r4
        L_0x00a6:
            r0 = move-exception
            r1.v(r2)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.ame.d(agc, int, int, boolean):boolean");
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v13, resolved type: agc} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v15, resolved type: agc} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v10, resolved type: aqs} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v11, resolved type: aqs} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r6v2, resolved type: aqr} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v14, resolved type: arb} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r26v1, resolved type: aqt} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v28, resolved type: arb} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v48, resolved type: arb} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v71, resolved type: agc} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v74, resolved type: agc} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v77, resolved type: agc} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v78, resolved type: agc} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v79, resolved type: agc} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v80, resolved type: agc} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v81, resolved type: agc} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r26v3, resolved type: arb} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r26v4, resolved type: aqs} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v84, resolved type: agc} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v87, resolved type: java.lang.String} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v88, resolved type: java.lang.String} */
    /* JADX WARNING: type inference failed for: r2v7 */
    /* JADX WARNING: type inference failed for: r3v13 */
    /* JADX WARNING: type inference failed for: r3v21, types: [aqv] */
    /* JADX WARNING: type inference failed for: r3v25, types: [aqq] */
    /* JADX WARNING: Code restructure failed: missing block: B:135:0x0270, code lost:
        r0 = e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:136:0x0273, code lost:
        r24 = r5;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:137:0x0276, code lost:
        r0 = e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:138:0x0279, code lost:
        r24 = r5;
        r23 = "Id3Decoder";
     */
    /* JADX WARNING: Code restructure failed: missing block: B:139:0x027d, code lost:
        r8 = r2;
        r3 = r6;
        r4 = r7;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:163:0x02dd, code lost:
        r0 = e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:228:0x041f, code lost:
        r0 = th;
        r8 = r8;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:256:0x049b, code lost:
        r0 = e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:258:0x049d, code lost:
        r0 = e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:259:0x049e, code lost:
        r22 = r6;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:260:0x04a1, code lost:
        r0 = e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:262:0x04a3, code lost:
        r0 = e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:263:0x04a4, code lost:
        r22 = r6;
        r21 = r7;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:276:0x04ed, code lost:
        r0 = e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:277:0x04f0, code lost:
        r2 = r0;
        r10 = r18;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:278:0x04f4, code lost:
        r0 = move-exception;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:279:0x04f7, code lost:
        r22 = r6;
        r21 = r7;
        r18 = r10;
        r2 = r0;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:280:0x04fe, code lost:
        r4 = r21;
        r3 = r22;
        r8 = r8;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:294:0x0574, code lost:
        r0 = move-exception;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:295:0x0577, code lost:
        r2 = r0;
        r8 = r8;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:87:0x0162, code lost:
        r0 = move-exception;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:88:0x0163, code lost:
        r1 = r0;
        r8 = r2;
     */
    /* JADX WARNING: Exception block dominator not found, dom blocks: [B:198:0x0369, B:239:0x043b] */
    /* JADX WARNING: Exception block dominator not found, dom blocks: [B:198:0x0369, B:248:0x0476] */
    /* JADX WARNING: Exception block dominator not found, dom blocks: [B:198:0x0369, B:251:0x047a] */
    /* JADX WARNING: Exception block dominator not found, dom blocks: [B:198:0x0369, B:254:0x0482] */
    /* JADX WARNING: Exception block dominator not found, dom blocks: [B:198:0x0369, B:289:0x051e] */
    /* JADX WARNING: Exception block dominator not found, dom blocks: [B:78:0x0102, B:126:0x0223] */
    /* JADX WARNING: Exception block dominator not found, dom blocks: [B:78:0x0102, B:129:0x023b] */
    /* JADX WARNING: Exception block dominator not found, dom blocks: [B:78:0x0102, B:132:0x0240] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:158:0x02bb A[SYNTHETIC, Splitter:B:158:0x02bb] */
    /* JADX WARNING: Removed duplicated region for block: B:164:0x02e1  */
    /* JADX WARNING: Removed duplicated region for block: B:228:0x041f A[ExcHandler: all (th java.lang.Throwable), PHI: r8 r25 
      PHI: (r8v15 agc) = (r8v71 agc), (r8v74 agc), (r8v77 agc), (r8v78 agc), (r8v79 agc), (r8v80 agc), (r8v81 agc), (r8v84 agc), (r8v88 java.lang.String) binds: [B:289:0x051e, B:239:0x043b, B:248:0x0476, B:249:?, B:251:0x047a, B:252:?, B:254:0x0482, B:198:0x0369, B:176:0x0312] A[DONT_GENERATE, DONT_INLINE]
      PHI: (r25v5 int) = (r25v8 int), (r25v8 int), (r25v8 int), (r25v8 int), (r25v8 int), (r25v8 int), (r25v8 int), (r25v8 int), (r25v15 int) binds: [B:289:0x051e, B:239:0x043b, B:248:0x0476, B:249:?, B:251:0x047a, B:252:?, B:254:0x0482, B:198:0x0369, B:176:0x0312] A[DONT_GENERATE, DONT_INLINE], Splitter:B:198:0x0369] */
    /* JADX WARNING: Removed duplicated region for block: B:283:0x050c  */
    /* JADX WARNING: Removed duplicated region for block: B:297:0x057f  */
    /* JADX WARNING: Removed duplicated region for block: B:311:0x05b3  */
    /* JADX WARNING: Removed duplicated region for block: B:87:0x0162 A[Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }, ExcHandler: all (r0v1 'th' java.lang.Throwable A[CUSTOM_DECLARE, Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }]), Splitter:B:78:0x0102] */
    /* JADX WARNING: Unknown variable types count: 1 */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static defpackage.aqy e(int r36, defpackage.agc r37, boolean r38, int r39, defpackage.amd r40) {
        /*
            r1 = r36
            r2 = r37
            r3 = r38
            r4 = r39
            int r5 = r37.h()
            int r6 = r37.h()
            int r7 = r37.h()
            r9 = 3
            if (r1 < r9) goto L_0x001c
            int r10 = r37.h()
            goto L_0x001d
        L_0x001c:
            r10 = 0
        L_0x001d:
            r11 = 4
            if (r1 != r11) goto L_0x003c
            int r12 = r37.j()
            if (r3 != 0) goto L_0x0047
            r13 = r12 & 255(0xff, float:3.57E-43)
            int r14 = r12 >> 8
            r14 = r14 & 255(0xff, float:3.57E-43)
            int r15 = r12 >> 16
            r15 = r15 & 255(0xff, float:3.57E-43)
            int r12 = r12 >> 24
            int r14 = r14 << 7
            r13 = r13 | r14
            int r14 = r15 << 14
            r13 = r13 | r14
            int r12 = r12 << 21
            r12 = r12 | r13
            goto L_0x0047
        L_0x003c:
            if (r1 != r9) goto L_0x0043
            int r12 = r37.j()
            goto L_0x0047
        L_0x0043:
            int r12 = r37.i()
        L_0x0047:
            if (r1 < r9) goto L_0x004e
            int r13 = r37.k()
            goto L_0x004f
        L_0x004e:
            r13 = 0
        L_0x004f:
            r14 = 0
            if (r5 != 0) goto L_0x0063
            if (r6 != 0) goto L_0x0063
            if (r7 != 0) goto L_0x0063
            if (r10 != 0) goto L_0x0063
            if (r12 != 0) goto L_0x0063
            if (r13 == 0) goto L_0x005d
            goto L_0x0063
        L_0x005d:
            int r1 = r2.c
            r2.v(r1)
            return r14
        L_0x0063:
            int r15 = r2.b
            int r15 = r15 + r12
            int r8 = r2.c
            java.lang.String r11 = "Id3Decoder"
            if (r15 <= r8) goto L_0x0077
            java.lang.String r1 = "Frame size exceeds remaining tag data"
            defpackage.afy.e(r11, r1)
            int r1 = r2.c
            r2.v(r1)
            return r14
        L_0x0077:
            r8 = 1
            if (r1 != r9) goto L_0x0098
            r17 = r13 & 64
            r9 = r13 & 128(0x80, float:1.794E-43)
            if (r9 == 0) goto L_0x0082
            r9 = r8
            goto L_0x0083
        L_0x0082:
            r9 = 0
        L_0x0083:
            if (r17 == 0) goto L_0x0088
            r17 = r8
            goto L_0x008a
        L_0x0088:
            r17 = 0
        L_0x008a:
            r13 = r13 & 32
            if (r13 == 0) goto L_0x0090
            r13 = r8
            goto L_0x0091
        L_0x0090:
            r13 = 0
        L_0x0091:
            r19 = r17
            r20 = 0
            r17 = r9
            goto L_0x00cc
        L_0x0098:
            r9 = 4
            if (r1 != r9) goto L_0x00c4
            r9 = r13 & 64
            if (r9 == 0) goto L_0x00a1
            r9 = r8
            goto L_0x00a2
        L_0x00a1:
            r9 = 0
        L_0x00a2:
            r17 = r13 & 8
            if (r17 == 0) goto L_0x00a9
            r17 = r8
            goto L_0x00ab
        L_0x00a9:
            r17 = 0
        L_0x00ab:
            r19 = r13 & 4
            if (r19 == 0) goto L_0x00b2
            r19 = r8
            goto L_0x00b4
        L_0x00b2:
            r19 = 0
        L_0x00b4:
            r20 = r13 & 2
            if (r20 == 0) goto L_0x00bb
            r20 = r8
            goto L_0x00bd
        L_0x00bb:
            r20 = 0
        L_0x00bd:
            r13 = r13 & r8
            r35 = r13
            r13 = r9
            r9 = r35
            goto L_0x00cc
        L_0x00c4:
            r9 = 0
            r13 = 0
            r17 = 0
            r19 = 0
            r20 = 0
        L_0x00cc:
            if (r17 != 0) goto L_0x05d5
            if (r19 == 0) goto L_0x00d2
            goto L_0x05d5
        L_0x00d2:
            if (r13 == 0) goto L_0x00d9
            r2.w(r8)
            int r12 = r12 + -1
        L_0x00d9:
            if (r9 == 0) goto L_0x00e1
            r9 = 4
            r2.w(r9)
            int r12 = r12 + -4
        L_0x00e1:
            if (r20 == 0) goto L_0x00e7
            int r12 = c(r2, r12)
        L_0x00e7:
            r9 = 84
            r13 = 88
            r8 = 2
            if (r5 != r9) goto L_0x012d
            if (r6 != r13) goto L_0x012d
            if (r7 != r13) goto L_0x012d
            if (r1 == r8) goto L_0x00f6
            if (r10 != r13) goto L_0x012d
        L_0x00f6:
            if (r12 > 0) goto L_0x0102
            r8 = r2
            r24 = r5
            r3 = r6
            r4 = r7
            r23 = r11
            r2 = r14
            goto L_0x0597
        L_0x0102:
            int r3 = r37.h()     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            int r4 = r12 + -1
            byte[] r8 = new byte[r4]     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r9 = 0
            r2.r(r8, r9, r4)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            int r4 = g(r8, r9, r3)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            java.lang.String r13 = new java.lang.String     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            java.nio.charset.Charset r14 = l(r3)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r13.<init>(r8, r9, r4, r14)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            int r9 = f(r3)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            int r4 = r4 + r9
            gxq r3 = i(r8, r3, r4)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            ard r4 = new ard     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            java.lang.String r8 = "TXXX"
            r4.<init>(r8, r13, r3)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            goto L_0x01d3
        L_0x012d:
            if (r5 != r9) goto L_0x0174
            java.lang.String r3 = k(r1, r9, r6, r7, r10)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            if (r12 > 0) goto L_0x013f
        L_0x0135:
            r8 = r2
            r24 = r5
            r3 = r6
            r4 = r7
            r23 = r11
        L_0x013c:
            r2 = 0
            goto L_0x0597
        L_0x013f:
            int r4 = r37.h()     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            int r8 = r12 + -1
            byte[] r9 = new byte[r8]     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r13 = 0
            r2.r(r9, r13, r8)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            gxq r4 = i(r9, r4, r13)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            ard r8 = new ard     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r9 = 0
            r8.<init>(r3, r9, r4)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r24 = r5
            r3 = r6
            r4 = r7
            r23 = r11
        L_0x015b:
            r35 = r8
            r8 = r2
            r2 = r35
            goto L_0x0597
        L_0x0162:
            r0 = move-exception
            r1 = r0
            r8 = r2
            goto L_0x05a2
        L_0x0167:
            r0 = move-exception
            goto L_0x016a
        L_0x0169:
            r0 = move-exception
        L_0x016a:
            r8 = r2
            r24 = r5
            r3 = r6
            r4 = r7
            r23 = r11
        L_0x0171:
            r2 = r0
            goto L_0x05ad
        L_0x0174:
            r14 = 87
            if (r5 != r14) goto L_0x01b5
            if (r6 != r13) goto L_0x01b3
            if (r7 != r13) goto L_0x01b3
            if (r1 == r8) goto L_0x0180
            if (r10 != r13) goto L_0x01b3
        L_0x0180:
            if (r12 > 0) goto L_0x0183
            goto L_0x0135
        L_0x0183:
            int r3 = r37.h()     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            int r4 = r12 + -1
            byte[] r8 = new byte[r4]     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r9 = 0
            r2.r(r8, r9, r4)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            int r4 = g(r8, r9, r3)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            java.lang.String r13 = new java.lang.String     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            java.nio.charset.Charset r14 = l(r3)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r13.<init>(r8, r9, r4, r14)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            int r3 = f(r3)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            int r4 = r4 + r3
            int r3 = h(r8, r4)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            java.nio.charset.Charset r9 = defpackage.gqr.b     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            java.lang.String r3 = j(r8, r4, r3, r9)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            are r4 = new are     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            java.lang.String r8 = "WXXX"
            r4.<init>(r8, r13, r3)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            goto L_0x01d3
        L_0x01b3:
            r13 = r14
            goto L_0x01b6
        L_0x01b5:
            r13 = r5
        L_0x01b6:
            if (r13 != r14) goto L_0x01dd
            java.lang.String r3 = k(r1, r14, r6, r7, r10)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            byte[] r4 = new byte[r12]     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r8 = 0
            r2.r(r4, r8, r12)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            int r9 = h(r4, r8)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            java.lang.String r13 = new java.lang.String     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            java.nio.charset.Charset r14 = defpackage.gqr.b     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r13.<init>(r4, r8, r9, r14)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            are r4 = new are     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r8 = 0
            r4.<init>(r3, r8, r13)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
        L_0x01d3:
            r8 = r2
            r2 = r4
            r24 = r5
            r3 = r6
            r4 = r7
            r23 = r11
            goto L_0x0597
        L_0x01dd:
            r14 = 73
            r9 = 80
            if (r13 != r9) goto L_0x020b
            r13 = 82
            if (r6 != r13) goto L_0x020a
            if (r7 != r14) goto L_0x020a
            r13 = 86
            if (r10 != r13) goto L_0x020a
            byte[] r3 = new byte[r12]     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r4 = 0
            r2.r(r3, r4, r12)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            int r8 = h(r3, r4)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            java.lang.String r9 = new java.lang.String     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            java.nio.charset.Charset r13 = defpackage.gqr.b     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r9.<init>(r3, r4, r8, r13)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r4 = 1
            int r8 = r8 + r4
            byte[] r3 = m(r3, r8, r12)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            arc r4 = new arc     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            r4.<init>(r9, r3)     // Catch:{ OutOfMemoryError -> 0x0169, Exception -> 0x0167, all -> 0x0162 }
            goto L_0x01d3
        L_0x020a:
            r13 = r9
        L_0x020b:
            r14 = 79
            r9 = 71
            if (r13 != r9) goto L_0x0288
            r13 = 69
            if (r6 != r13) goto L_0x0282
            if (r7 != r14) goto L_0x0282
            r13 = 66
            if (r10 == r13) goto L_0x0223
            if (r1 != r8) goto L_0x021e
            goto L_0x0223
        L_0x021e:
            r24 = r5
            r13 = r9
            goto L_0x028a
        L_0x0223:
            int r3 = r37.h()     // Catch:{ OutOfMemoryError -> 0x0278, Exception -> 0x0276, all -> 0x0162 }
            java.nio.charset.Charset r4 = l(r3)     // Catch:{ OutOfMemoryError -> 0x0278, Exception -> 0x0276, all -> 0x0162 }
            int r8 = r12 + -1
            byte[] r9 = new byte[r8]     // Catch:{ OutOfMemoryError -> 0x0278, Exception -> 0x0276, all -> 0x0162 }
            r13 = 0
            r2.r(r9, r13, r8)     // Catch:{ OutOfMemoryError -> 0x0278, Exception -> 0x0276, all -> 0x0162 }
            int r14 = h(r9, r13)     // Catch:{ OutOfMemoryError -> 0x0278, Exception -> 0x0276, all -> 0x0162 }
            java.lang.String r13 = new java.lang.String     // Catch:{ OutOfMemoryError -> 0x0278, Exception -> 0x0276, all -> 0x0162 }
            r23 = r11
            java.nio.charset.Charset r11 = defpackage.gqr.b     // Catch:{ OutOfMemoryError -> 0x0272, Exception -> 0x0270, all -> 0x0162 }
            r24 = r5
            r5 = 0
            r13.<init>(r9, r5, r14, r11)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            java.lang.String r5 = defpackage.aem.b(r13)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            r11 = 1
            int r14 = r14 + r11
            int r11 = g(r9, r14, r3)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            java.lang.String r13 = j(r9, r14, r11, r4)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            int r14 = f(r3)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            int r11 = r11 + r14
            int r14 = g(r9, r11, r3)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            java.lang.String r4 = j(r9, r11, r14, r4)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            int r3 = f(r3)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            int r14 = r14 + r3
            byte[] r3 = m(r9, r14, r8)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            aqw r8 = new aqw     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            r8.<init>(r5, r13, r4, r3)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            r3 = r6
            r4 = r7
            goto L_0x015b
        L_0x0270:
            r0 = move-exception
            goto L_0x0273
        L_0x0272:
            r0 = move-exception
        L_0x0273:
            r24 = r5
            goto L_0x027d
        L_0x0276:
            r0 = move-exception
            goto L_0x0279
        L_0x0278:
            r0 = move-exception
        L_0x0279:
            r24 = r5
            r23 = r11
        L_0x027d:
            r8 = r2
            r3 = r6
            r4 = r7
            goto L_0x0171
        L_0x0282:
            r24 = r5
            r23 = r11
            r13 = r9
            goto L_0x028c
        L_0x0288:
            r24 = r5
        L_0x028a:
            r23 = r11
        L_0x028c:
            r5 = 65
            r9 = 67
            if (r1 != r8) goto L_0x029d
            r11 = 80
            if (r13 != r11) goto L_0x033f
            r14 = 73
            if (r6 != r14) goto L_0x033f
            if (r7 != r9) goto L_0x033f
            goto L_0x02a9
        L_0x029d:
            r11 = 80
            r14 = 73
            if (r13 != r5) goto L_0x033f
            if (r6 != r11) goto L_0x033f
            if (r7 != r14) goto L_0x033f
            if (r10 != r9) goto L_0x033f
        L_0x02a9:
            int r3 = r37.h()     // Catch:{ OutOfMemoryError -> 0x0335, Exception -> 0x0333, all -> 0x032b }
            java.nio.charset.Charset r4 = l(r3)     // Catch:{ OutOfMemoryError -> 0x0335, Exception -> 0x0333, all -> 0x032b }
            int r5 = r12 + -1
            byte[] r9 = new byte[r5]     // Catch:{ OutOfMemoryError -> 0x0335, Exception -> 0x0333, all -> 0x032b }
            r11 = 0
            r2.r(r9, r11, r5)     // Catch:{ OutOfMemoryError -> 0x0335, Exception -> 0x0333, all -> 0x032b }
            if (r1 != r8) goto L_0x02e1
            java.lang.String r13 = new java.lang.String     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            java.nio.charset.Charset r14 = defpackage.gqr.b     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            r8 = 3
            r13.<init>(r9, r11, r8, r14)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            java.lang.String r8 = defpackage.ftd.t(r13)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            java.lang.String r11 = "image/"
            java.lang.String r8 = java.lang.String.valueOf(r8)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            java.lang.String r8 = r11.concat(r8)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            java.lang.String r11 = "image/jpg"
            boolean r11 = r11.equals(r8)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            if (r11 == 0) goto L_0x02db
            java.lang.String r8 = "image/jpeg"
        L_0x02db:
            r11 = 2
            goto L_0x0304
        L_0x02dd:
            r0 = move-exception
            goto L_0x027d
        L_0x02df:
            r0 = move-exception
            goto L_0x027d
        L_0x02e1:
            r8 = r11
            int r11 = h(r9, r8)     // Catch:{ OutOfMemoryError -> 0x0335, Exception -> 0x0333, all -> 0x032b }
            java.lang.String r13 = new java.lang.String     // Catch:{ OutOfMemoryError -> 0x0335, Exception -> 0x0333, all -> 0x032b }
            java.nio.charset.Charset r14 = defpackage.gqr.b     // Catch:{ OutOfMemoryError -> 0x0335, Exception -> 0x0333, all -> 0x032b }
            r13.<init>(r9, r8, r11, r14)     // Catch:{ OutOfMemoryError -> 0x0335, Exception -> 0x0333, all -> 0x032b }
            java.lang.String r8 = defpackage.ftd.t(r13)     // Catch:{ OutOfMemoryError -> 0x0335, Exception -> 0x0333, all -> 0x032b }
            r13 = 47
            int r13 = r8.indexOf(r13)     // Catch:{ OutOfMemoryError -> 0x0335, Exception -> 0x0333, all -> 0x032b }
            r14 = -1
            if (r13 != r14) goto L_0x0304
            java.lang.String r13 = "image/"
            java.lang.String r8 = java.lang.String.valueOf(r8)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
            java.lang.String r8 = r13.concat(r8)     // Catch:{ OutOfMemoryError -> 0x02df, Exception -> 0x02dd, all -> 0x0162 }
        L_0x0304:
            int r13 = r11 + 1
            byte r13 = r9[r13]     // Catch:{ OutOfMemoryError -> 0x0335, Exception -> 0x0333, all -> 0x032b }
            r13 = r13 & 255(0xff, float:3.57E-43)
            r14 = 2
            int r11 = r11 + r14
            int r14 = g(r9, r11, r3)     // Catch:{ OutOfMemoryError -> 0x0335, Exception -> 0x0333, all -> 0x032b }
            r25 = r15
            java.lang.String r15 = new java.lang.String     // Catch:{ OutOfMemoryError -> 0x039f, Exception -> 0x039d, all -> 0x0398 }
            int r2 = r14 - r11
            r15.<init>(r9, r11, r2, r4)     // Catch:{ OutOfMemoryError -> 0x039f, Exception -> 0x039d, all -> 0x0398 }
            int r2 = f(r3)     // Catch:{ OutOfMemoryError -> 0x039f, Exception -> 0x039d, all -> 0x0398 }
            int r14 = r14 + r2
            byte[] r2 = m(r9, r14, r5)     // Catch:{ OutOfMemoryError -> 0x039f, Exception -> 0x039d, all -> 0x0398 }
            aqq r3 = new aqq     // Catch:{ OutOfMemoryError -> 0x039f, Exception -> 0x039d, all -> 0x0398 }
            r3.<init>(r8, r15, r13, r2)     // Catch:{ OutOfMemoryError -> 0x039f, Exception -> 0x039d, all -> 0x0398 }
            r8 = r37
            goto L_0x0393
        L_0x032b:
            r0 = move-exception
            r25 = r15
            r8 = r37
        L_0x0330:
            r1 = r0
            goto L_0x05a2
        L_0x0333:
            r0 = move-exception
            goto L_0x0336
        L_0x0335:
            r0 = move-exception
        L_0x0336:
            r25 = r15
            r8 = r37
            r2 = r0
            r3 = r6
            r4 = r7
            goto L_0x05ad
        L_0x033f:
            r25 = r15
            r2 = 77
            if (r13 != r9) goto L_0x03a4
            r8 = 79
            if (r6 != r8) goto L_0x03a4
            if (r7 != r2) goto L_0x03a4
            if (r10 == r2) goto L_0x0350
            r8 = 2
            if (r1 != r8) goto L_0x03a4
        L_0x0350:
            r2 = 4
            if (r12 >= r2) goto L_0x035b
            r8 = r37
            r3 = r6
            r4 = r7
            r15 = r25
            goto L_0x013c
        L_0x035b:
            int r2 = r37.h()     // Catch:{ OutOfMemoryError -> 0x039f, Exception -> 0x039d, all -> 0x0398 }
            java.nio.charset.Charset r3 = l(r2)     // Catch:{ OutOfMemoryError -> 0x039f, Exception -> 0x039d, all -> 0x0398 }
            r4 = 3
            byte[] r5 = new byte[r4]     // Catch:{ OutOfMemoryError -> 0x039f, Exception -> 0x039d, all -> 0x0398 }
            r8 = r37
            r9 = 0
            r8.r(r5, r9, r4)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            java.lang.String r11 = new java.lang.String     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            r11.<init>(r5, r9, r4)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            int r4 = r12 + -4
            byte[] r5 = new byte[r4]     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            r8.r(r5, r9, r4)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            int r4 = g(r5, r9, r2)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            java.lang.String r13 = new java.lang.String     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            r13.<init>(r5, r9, r4, r3)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            int r9 = f(r2)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            int r4 = r4 + r9
            int r2 = g(r5, r4, r2)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            java.lang.String r2 = j(r5, r4, r2, r3)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            aqv r3 = new aqv     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            r3.<init>(r11, r13, r2)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
        L_0x0393:
            r2 = r3
        L_0x0394:
            r3 = r6
            r4 = r7
            goto L_0x0595
        L_0x0398:
            r0 = move-exception
            r8 = r37
            goto L_0x0420
        L_0x039d:
            r0 = move-exception
            goto L_0x03a0
        L_0x039f:
            r0 = move-exception
        L_0x03a0:
            r8 = r37
            goto L_0x0428
        L_0x03a4:
            r8 = r37
            if (r13 != r9) goto L_0x042f
            r11 = 72
            if (r6 != r11) goto L_0x042f
            if (r7 != r5) goto L_0x042f
            r5 = 80
            if (r10 != r5) goto L_0x042f
            int r2 = r8.b     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            byte[] r5 = r8.a     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            int r5 = h(r5, r2)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            java.lang.String r9 = new java.lang.String     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            byte[] r11 = r8.a     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            int r13 = r5 - r2
            java.nio.charset.Charset r14 = defpackage.gqr.b     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            r9.<init>(r11, r2, r13, r14)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            r11 = 1
            int r5 = r5 + r11
            r8.v(r5)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            int r28 = r37.c()     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            int r29 = r37.c()     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            long r13 = r37.o()     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            r15 = 4294967295(0xffffffff, double:2.1219957905E-314)
            int r5 = (r13 > r15 ? 1 : (r13 == r15 ? 0 : -1))
            if (r5 != 0) goto L_0x03e1
            r13 = -1
        L_0x03e1:
            r30 = r13
            long r13 = r37.o()     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            r15 = 4294967295(0xffffffff, double:2.1219957905E-314)
            int r5 = (r13 > r15 ? 1 : (r13 == r15 ? 0 : -1))
            if (r5 != 0) goto L_0x03f2
            r13 = -1
        L_0x03f2:
            r32 = r13
            java.util.ArrayList r5 = new java.util.ArrayList     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            r5.<init>()     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            int r2 = r2 + r12
        L_0x03fa:
            int r11 = r8.b     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            if (r11 >= r2) goto L_0x0409
            r11 = 0
            aqy r13 = e(r1, r8, r3, r4, r11)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            if (r13 == 0) goto L_0x03fa
            r5.add(r13)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            goto L_0x03fa
        L_0x0409:
            r2 = 0
            aqy[] r2 = new defpackage.aqy[r2]     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            java.lang.Object[] r2 = r5.toArray(r2)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            r34 = r2
            aqy[] r34 = (defpackage.aqy[]) r34     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            aqs r2 = new aqs     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            r26 = r2
            r27 = r9
            r26.<init>(r27, r28, r29, r30, r32, r34)     // Catch:{ OutOfMemoryError -> 0x0427, Exception -> 0x0425, all -> 0x041f }
            goto L_0x0394
        L_0x041f:
            r0 = move-exception
        L_0x0420:
            r1 = r0
            r15 = r25
            goto L_0x05a2
        L_0x0425:
            r0 = move-exception
            goto L_0x0428
        L_0x0427:
            r0 = move-exception
        L_0x0428:
            r2 = r0
            r3 = r6
            r4 = r7
        L_0x042b:
            r15 = r25
            goto L_0x05ad
        L_0x042f:
            if (r13 != r9) goto L_0x0504
            r5 = 84
            if (r6 != r5) goto L_0x0504
            r5 = 79
            if (r7 != r5) goto L_0x0504
            if (r10 != r9) goto L_0x0504
            int r2 = r8.b     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            byte[] r5 = r8.a     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            int r5 = h(r5, r2)     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            java.lang.String r9 = new java.lang.String     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            byte[] r11 = r8.a     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            int r13 = r5 - r2
            java.nio.charset.Charset r14 = defpackage.gqr.b     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            r9.<init>(r11, r2, r13, r14)     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            r11 = 1
            int r5 = r5 + r11
            r8.v(r5)     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            int r5 = r37.h()     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            r13 = r5 & 2
            if (r13 == 0) goto L_0x045e
            r28 = r11
            goto L_0x0460
        L_0x045e:
            r28 = 0
        L_0x0460:
            r5 = r5 & r11
            int r11 = r37.h()     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            java.lang.String[] r13 = new java.lang.String[r11]     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            r14 = 0
        L_0x0468:
            if (r14 >= r11) goto L_0x04a9
            int r15 = r8.b     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            r16 = r11
            byte[] r11 = r8.a     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            int r11 = h(r11, r15)     // Catch:{ OutOfMemoryError -> 0x04f6, Exception -> 0x04f4, all -> 0x041f }
            r18 = r10
            java.lang.String r10 = new java.lang.String     // Catch:{ OutOfMemoryError -> 0x04a3, Exception -> 0x04a1, all -> 0x041f }
            r21 = r7
            byte[] r7 = r8.a     // Catch:{ OutOfMemoryError -> 0x049d, Exception -> 0x049b, all -> 0x041f }
            r22 = r6
            int r6 = r11 - r15
            r20 = r9
            java.nio.charset.Charset r9 = defpackage.gqr.b     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            r10.<init>(r7, r15, r6, r9)     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            r13[r14] = r10     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            int r11 = r11 + 1
            r8.v(r11)     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            int r14 = r14 + 1
            r11 = r16
            r10 = r18
            r9 = r20
            r7 = r21
            r6 = r22
            goto L_0x0468
        L_0x049b:
            r0 = move-exception
            goto L_0x049e
        L_0x049d:
            r0 = move-exception
        L_0x049e:
            r22 = r6
            goto L_0x04f0
        L_0x04a1:
            r0 = move-exception
            goto L_0x04a4
        L_0x04a3:
            r0 = move-exception
        L_0x04a4:
            r22 = r6
            r21 = r7
            goto L_0x04f0
        L_0x04a9:
            r22 = r6
            r21 = r7
            r20 = r9
            r18 = r10
            java.util.ArrayList r6 = new java.util.ArrayList     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            r6.<init>()     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            int r2 = r2 + r12
        L_0x04b7:
            int r7 = r8.b     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            if (r7 >= r2) goto L_0x04c6
            r7 = 0
            aqy r9 = e(r1, r8, r3, r4, r7)     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            if (r9 == 0) goto L_0x04b7
            r6.add(r9)     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            goto L_0x04b7
        L_0x04c6:
            r2 = 0
            aqy[] r3 = new defpackage.aqy[r2]     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            java.lang.Object[] r2 = r6.toArray(r3)     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            r31 = r2
            aqy[] r31 = (defpackage.aqy[]) r31     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            aqt r4 = new aqt     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            r2 = 1
            if (r2 == r5) goto L_0x04d9
            r29 = 0
            goto L_0x04db
        L_0x04d9:
            r29 = r2
        L_0x04db:
            r26 = r4
            r27 = r20
            r30 = r13
            r26.<init>(r27, r28, r29, r30, r31)     // Catch:{ OutOfMemoryError -> 0x04ef, Exception -> 0x04ed, all -> 0x041f }
            r2 = r4
            r10 = r18
            r4 = r21
            r3 = r22
            goto L_0x0595
        L_0x04ed:
            r0 = move-exception
            goto L_0x04f0
        L_0x04ef:
            r0 = move-exception
        L_0x04f0:
            r2 = r0
            r10 = r18
            goto L_0x04fe
        L_0x04f4:
            r0 = move-exception
            goto L_0x04f7
        L_0x04f6:
            r0 = move-exception
        L_0x04f7:
            r22 = r6
            r21 = r7
            r18 = r10
            r2 = r0
        L_0x04fe:
            r4 = r21
            r3 = r22
            goto L_0x042b
        L_0x0504:
            r22 = r6
            r21 = r7
            r18 = r10
            if (r13 != r2) goto L_0x057f
            r2 = 76
            r3 = r22
            if (r3 != r2) goto L_0x057a
            r2 = 76
            r4 = r21
            r10 = r18
            if (r4 != r2) goto L_0x0585
            r2 = 84
            if (r10 != r2) goto L_0x0585
            int r27 = r37.k()     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            int r28 = r37.i()     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            int r29 = r37.i()     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            int r2 = r37.h()     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            int r5 = r37.h()     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            agb r6 = new agb     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            r6.<init>()     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            byte[] r7 = r8.a     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            int r9 = r8.c     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            r6.d = r7     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            r7 = 0
            r6.a = r7     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            r6.b = r7     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            r6.c = r9     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            int r7 = r8.b     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            int r7 = r7 * 8
            r6.c(r7)     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            int r7 = r12 + -10
            int r7 = r7 * 8
            int r9 = r2 + r5
            int r7 = r7 / r9
            int[] r9 = new int[r7]     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            int[] r11 = new int[r7]     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            r13 = 0
        L_0x0557:
            if (r13 >= r7) goto L_0x0568
            int r14 = r6.a(r2)     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            int r15 = r6.a(r5)     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            r9[r13] = r14     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            r11[r13] = r15     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            int r13 = r13 + 1
            goto L_0x0557
        L_0x0568:
            arb r2 = new arb     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            r26 = r2
            r30 = r9
            r31 = r11
            r26.<init>(r27, r28, r29, r30, r31)     // Catch:{ OutOfMemoryError -> 0x0576, Exception -> 0x0574, all -> 0x041f }
            goto L_0x0595
        L_0x0574:
            r0 = move-exception
            goto L_0x0577
        L_0x0576:
            r0 = move-exception
        L_0x0577:
            r2 = r0
            goto L_0x042b
        L_0x057a:
            r10 = r18
            r4 = r21
            goto L_0x0585
        L_0x057f:
            r10 = r18
            r4 = r21
            r3 = r22
        L_0x0585:
            java.lang.String r2 = k(r1, r13, r3, r4, r10)     // Catch:{ OutOfMemoryError -> 0x05a8, Exception -> 0x05a6, all -> 0x059d }
            byte[] r5 = new byte[r12]     // Catch:{ OutOfMemoryError -> 0x05a8, Exception -> 0x05a6, all -> 0x059d }
            r6 = 0
            r8.r(r5, r6, r12)     // Catch:{ OutOfMemoryError -> 0x05a8, Exception -> 0x05a6, all -> 0x059d }
            aqr r6 = new aqr     // Catch:{ OutOfMemoryError -> 0x05a8, Exception -> 0x05a6, all -> 0x059d }
            r6.<init>(r2, r5)     // Catch:{ OutOfMemoryError -> 0x05a8, Exception -> 0x05a6, all -> 0x059d }
            r2 = r6
        L_0x0595:
            r15 = r25
        L_0x0597:
            r8.v(r15)
            r14 = r2
            r2 = 0
            goto L_0x05b1
        L_0x059d:
            r0 = move-exception
            r15 = r25
            goto L_0x0330
        L_0x05a2:
            r8.v(r15)
            throw r1
        L_0x05a6:
            r0 = move-exception
            goto L_0x05a9
        L_0x05a8:
            r0 = move-exception
        L_0x05a9:
            r15 = r25
            goto L_0x0171
        L_0x05ad:
            r8.v(r15)
            r14 = 0
        L_0x05b1:
            if (r14 != 0) goto L_0x05d4
            r5 = r24
            java.lang.String r1 = k(r1, r5, r3, r4, r10)
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            java.lang.String r4 = "Failed to decode frame: id="
            r3.<init>(r4)
            r3.append(r1)
            java.lang.String r1 = ", frameSize="
            r3.append(r1)
            r3.append(r12)
            java.lang.String r1 = r3.toString()
            r3 = r23
            defpackage.afy.f(r3, r1, r2)
        L_0x05d4:
            return r14
        L_0x05d5:
            r8 = r2
            r3 = r11
            java.lang.String r1 = "Skipping unsupported compressed or encrypted frame"
            defpackage.afy.e(r3, r1)
            r8.v(r15)
            r1 = 0
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.ame.e(int, agc, boolean, int, amd):aqy");
    }

    private static int f(int i) {
        if (i == 0 || i == 3) {
            return 1;
        }
        return 2;
    }

    private static int g(byte[] bArr, int i, int i2) {
        int h = h(bArr, i);
        if (i2 == 0 || i2 == 3) {
            return h;
        }
        while (true) {
            int length = bArr.length;
            if (h >= length - 1) {
                return length;
            }
            int i3 = h + 1;
            if ((h - i) % 2 == 0 && bArr[i3] == 0) {
                return h;
            }
            h = h(bArr, i3);
        }
    }

    private static int h(byte[] bArr, int i) {
        while (true) {
            int length = bArr.length;
            if (i >= length) {
                return length;
            }
            if (bArr[i] == 0) {
                return i;
            }
            i++;
        }
    }

    private static gxq i(byte[] bArr, int i, int i2) {
        if (i2 >= bArr.length) {
            return gxq.q("");
        }
        gxl gxl = new gxl();
        int g = g(bArr, i2, i);
        while (i2 < g) {
            gxl.h(new String(bArr, i2, g - i2, l(i)));
            i2 = f(i) + g;
            g = g(bArr, i2, i);
        }
        gxq g2 = gxl.g();
        if (g2.isEmpty()) {
            return gxq.q("");
        }
        return g2;
    }

    private static String j(byte[] bArr, int i, int i2, Charset charset) {
        if (i2 <= i || i2 > bArr.length) {
            return "";
        }
        return new String(bArr, i, i2 - i, charset);
    }

    private static String k(int i, int i2, int i3, int i4, int i5) {
        if (i == 2) {
            return String.format(Locale.US, "%c%c%c", new Object[]{Integer.valueOf(i2), Integer.valueOf(i3), Integer.valueOf(i4)});
        }
        return String.format(Locale.US, "%c%c%c%c", new Object[]{Integer.valueOf(i2), Integer.valueOf(i3), Integer.valueOf(i4), Integer.valueOf(i5)});
    }

    private static Charset l(int i) {
        if (i == 1) {
            return gqr.e;
        }
        if (i == 2) {
            return gqr.d;
        }
        if (i != 3) {
            return gqr.b;
        }
        return gqr.c;
    }

    private static byte[] m(byte[] bArr, int i, int i2) {
        if (i2 <= i) {
            return agh.f;
        }
        return Arrays.copyOfRange(bArr, i, i2);
    }
}
