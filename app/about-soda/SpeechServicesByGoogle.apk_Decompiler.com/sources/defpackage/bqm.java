package defpackage;

/* renamed from: bqm  reason: default package */
/* compiled from: PG */
public final class bqm extends htq implements hvb {
    public static final bqm c;
    private static volatile hvh d;
    public int a;
    public int b;

    static {
        bqm bqm = new bqm();
        c = bqm;
        htq.z(bqm.class, bqm);
    }

    private bqm() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(c, "\u0004\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001᠌\u0000", new Object[]{"a", "b", bqk.a});
        } else if (i2 == 3) {
            return new bqm();
        } else {
            if (i2 == 4) {
                return new htk((htq) c);
            }
            if (i2 == 5) {
                return c;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = d;
            if (hvh == null) {
                synchronized (bqm.class) {
                    hvh = d;
                    if (hvh == null) {
                        hvh = new htl(c);
                        d = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
