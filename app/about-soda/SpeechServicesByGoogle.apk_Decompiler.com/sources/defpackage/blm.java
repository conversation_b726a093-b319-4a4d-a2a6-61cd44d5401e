package defpackage;

import android.support.v7.widget.LinearLayoutManager;
import java.util.HashSet;

/* renamed from: blm  reason: default package */
/* compiled from: PG */
final class blm extends LinearLayoutManager {
    final /* synthetic */ blp a;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public blm(blp blp, int i, boolean z) {
        super(i, z);
        this.a = blp;
    }

    public final void p(la laVar) {
        super.p(laVar);
        for (Runnable run : new HashSet(this.a.b)) {
            run.run();
        }
    }
}
