package defpackage;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutionException;

/* renamed from: bvi  reason: default package */
/* compiled from: PG */
public final class bvi {
    private static final hca a = hca.m("com/google/android/apps/speech/tts/googletts/local/voicepack/ui/VoicePackEntryHelper");

    /* JADX WARNING: type inference failed for: r0v1, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v4, types: [java.util.List, java.lang.Object] */
    public static List a(bxc bxc, btj btj, btf btf) {
        List<brt> list;
        int i;
        boolean contains;
        gxq gxq;
        int i2;
        int i3;
        btf btf2 = btf;
        ArrayList arrayList = new ArrayList();
        byw e = btj.e();
        ArrayList arrayList2 = new ArrayList();
        for (bty bty : e.a) {
            Iterator it = bty.f.iterator();
            while (true) {
                if (it.hasNext()) {
                    btw b = btw.b(((btx) it.next()).b);
                    if (b == null) {
                        b = btw.TYPE_UNKNOWN;
                    }
                    if (b != btw.TYPE_NETWORK) {
                        arrayList2.add(bty);
                        break;
                    }
                } else {
                    break;
                }
            }
        }
        byw byw = new byw((Object) arrayList2);
        ArrayList arrayList3 = new ArrayList();
        for (bty bty2 : byw.a) {
            Iterator it2 = bty2.f.iterator();
            while (true) {
                if (it2.hasNext()) {
                    if (bzw.f((btx) it2.next())) {
                        arrayList3.add(bty2);
                        break;
                    }
                } else {
                    break;
                }
            }
        }
        Map f = new byw((Object) arrayList3).f();
        HashMap hashMap = new HashMap();
        try {
            for (Map.Entry entry : ((Map) btf.c().get()).entrySet()) {
                bss bss = (bss) entry.getValue();
                if (!bss.d) {
                    hashMap.put((String) entry.getKey(), bss);
                }
            }
        } catch (InterruptedException | ExecutionException e2) {
            ((hby) ((hby) ((hby) btf.a.g()).i(e2)).j("com/google/android/apps/speech/tts/googletts/local/voicepack/VoiceDataDownloader", "getAvailablePublicVoicesInfo", 493, "VoiceDataDownloader.java")).r("Failed to get available public voices");
            hashMap = new HashMap();
        }
        HashSet<String> hashSet = new HashSet<>(f.keySet());
        hashSet.addAll(hashMap.keySet());
        HashMap hashMap2 = new HashMap();
        for (String str : hashSet) {
            bty bty3 = (bty) f.get(str);
            bss bss2 = (bss) hashMap.get(str);
            if (bty3 != null) {
                gxq = gxq.o(brv.d(bty3));
            } else if (bss2 != null) {
                gxq = bss2.e;
            } else {
                ((hby) ((hby) a.g()).j("com/google/android/apps/speech/tts/googletts/local/voicepack/ui/VoicePackEntryHelper", "getAll", 75, "VoicePackEntryHelper.java")).u("Couldn't find locale of voice %s", str);
            }
            int size = gxq.size();
            for (int i4 = 0; i4 < size; i4++) {
                brt brt = (brt) gxq.get(i4);
                bvh bvh = (bvh) hashMap2.get(brt);
                if (bvh == null) {
                    if (bss2 != null) {
                        i3 = 1;
                    } else {
                        i3 = 0;
                    }
                    hashMap2.put(brt, new bvh(i3));
                } else {
                    if (bss2 != null) {
                        i2 = 1;
                    } else {
                        i2 = 0;
                    }
                    bvh.a += i2;
                    bvh.b++;
                }
            }
        }
        for (String str2 : hashSet) {
            bss bss3 = (bss) hashMap.get(str2);
            bty bty4 = (bty) f.get(str2);
            if (bty4 != null) {
                list = brv.d(bty4);
            } else if (bss3 != null) {
                bty4 = bss3.a;
                list = bss3.e;
            } else {
                bxc bxc2 = bxc;
                ((hby) ((hby) a.g()).j("com/google/android/apps/speech/tts/googletts/local/voicepack/ui/VoicePackEntryHelper", "getAll", 103, "VoicePackEntryHelper.java")).u("Couldn't find locale of voice %s", str2);
            }
            for (brt brt2 : list) {
                bvh bvh2 = (bvh) hashMap2.get(brt2);
                if (bvh2 == null || bvh2.a <= 1) {
                    i = 0;
                } else {
                    i = 2;
                }
                if (bvh2 != null && bvh2.b > 1) {
                    i |= 1;
                }
                if (str2.equals(bxc.b(brt2.toString()))) {
                    i |= 8;
                }
                if (btf2.h(str2)) {
                    i |= 16;
                }
                synchronized (btf2.l) {
                    contains = btf2.l.contains(str2);
                }
                if (contains) {
                    i |= 32;
                }
                arrayList.add(new iul(bty4, bss3, i, brt2));
            }
            bxc bxc3 = bxc;
        }
        Collections.sort(arrayList, new aom(15));
        return arrayList;
    }
}
