package defpackage;

import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;

/* renamed from: brp  reason: default package */
/* compiled from: PG */
public final class brp {
    private static final hca a = hca.m("com/google/android/apps/speech/tts/googletts/buildinfo/BuildInfo");
    private static final brp b = new brp();
    private boolean c;
    private boolean d;

    private brp() {
    }

    public static void a(Context context) {
        synchronized (b) {
            fvf.aP(context);
            String str = "RELEASE";
            try {
                ApplicationInfo applicationInfo = context.getPackageManager().getApplicationInfo(context.getPackageName(), 128);
                String str2 = null;
                if (applicationInfo != null) {
                    if (applicationInfo.metaData != null) {
                        str2 = applicationInfo.metaData.getString("com.google.android.tts.config.BuildType");
                    }
                }
                if (str2 == null) {
                    ((hby) ((hby) a.h()).j("com/google/android/apps/speech/tts/googletts/buildinfo/BuildInfo", "initBuildTypeInternal", 80, "BuildInfo.java")).r("Could not find metadata from application info for build type");
                } else {
                    str = str2;
                }
            } catch (PackageManager.NameNotFoundException e) {
                ((hby) ((hby) ((hby) a.h()).i(e)).j("com/google/android/apps/speech/tts/googletts/buildinfo/BuildInfo", "initBuildTypeInternal", 'V', "BuildInfo.java")).r("Could not get metadata from application info for build type");
            }
            brp brp = b;
            brp.c = "DEV".equals(str);
            brp.d = "UNITTEST".equals(str);
        }
    }

    public static boolean b() {
        return b.c;
    }

    public static boolean c() {
        return b.d;
    }
}
