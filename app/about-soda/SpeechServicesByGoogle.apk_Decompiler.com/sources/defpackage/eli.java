package defpackage;

import j$.time.Duration;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/* renamed from: eli  reason: default package */
/* compiled from: PG */
public final class eli implements dyy {
    public static final Duration a;
    public final hme b;
    private final hme c;
    private final dyt d;
    private final jna e;
    private final hmi f;
    private final Future g;

    static {
        Duration ofMinutes = Duration.ofMinutes(10);
        jnu.d(ofMinutes, "ofMinutes(...)");
        a = ofMinutes;
    }

    public eli(hme hme, hme hme2, dyt dyt, jna jna, hmi hmi) {
        long j;
        jnu.e(hme, "startListeningResult");
        jnu.e(dyt, "params");
        jnu.e(hmi, "scheduledExecutorService");
        this.c = hme;
        this.b = hme2;
        this.d = dyt;
        this.e = jna;
        this.f = hmi;
        bdr bdr = new bdr((Object) this, 12);
        if ((dyt.a & 128) != 0) {
            j = dyt.h;
        } else {
            j = a.toMillis();
        }
        this.g = hmi.c(bdr, j, TimeUnit.MILLISECONDS);
    }

    public final dyx a() {
        return e(eam.CLIENT_REQUESTED);
    }

    public final hme b() {
        return hfc.K(this.d);
    }

    public final hme c() {
        return this.c;
    }

    public final /* synthetic */ Object d() {
        return new elh(this, 0);
    }

    public final dyx e(eam eam) {
        this.g.cancel(false);
        this.e.a(eam);
        return new elh(this, 1);
    }
}
