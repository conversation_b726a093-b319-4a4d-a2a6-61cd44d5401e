package defpackage;

import java.util.EnumSet;

/* renamed from: cuo  reason: default package */
/* compiled from: PG */
public final class cuo {
    public static final cuo a;
    public static final cuo b;
    public static final cuo c;
    private final boolean d;
    private final gyo e;

    static {
        ddj a2 = a();
        a2.e(EnumSet.noneOf(cun.class));
        a2.d(false);
        a = a2.c();
        ddj a3 = a();
        a3.e(EnumSet.of(cun.ANY));
        a3.d(true);
        b = a3.c();
        ddj a4 = a();
        a4.e(EnumSet.of(cun.ANY));
        a4.d(false);
        c = a4.c();
    }

    public cuo() {
        throw null;
    }

    public static ddj a() {
        ddj ddj = new ddj();
        ddj.d(false);
        return ddj;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof cuo) {
            cuo cuo = (cuo) obj;
            if (this.d != cuo.d || !this.e.equals(cuo.e)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        if (true != this.d) {
            i = 1237;
        } else {
            i = 1231;
        }
        return ((i ^ 1000003) * 1000003) ^ this.e.hashCode();
    }

    public final String toString() {
        String valueOf = String.valueOf(this.e);
        return "DownloadConstraints{requireUnmeteredNetwork=" + this.d + ", requiredNetworkTypes=" + valueOf + "}";
    }

    public cuo(boolean z, gyo gyo) {
        this.d = z;
        this.e = gyo;
    }
}
