package defpackage;

import android.util.Log;

/* renamed from: bbk  reason: default package */
/* compiled from: PG */
public final class bbk {
    public static final Object a = new Object();
    public static volatile bbk b;
    public final int c;

    public bbk() {
        throw null;
    }

    public static bbk a() {
        bbk bbk;
        synchronized (a) {
            if (b == null) {
                b = new bbk(3);
            }
            bbk = b;
        }
        return bbk;
    }

    public static String b(String str) {
        StringBuilder sb = new StringBuilder(23);
        sb.append("WM-");
        if (str.length() >= 20) {
            sb.append(str.substring(0, 20));
        } else {
            sb.append(str);
        }
        return sb.toString();
    }

    public final void c(String str, String str2) {
        if (this.c <= 6) {
            Log.e(str, str2);
        }
    }

    public final void d(String str, String str2, Throwable th) {
        if (this.c <= 6) {
            Log.e(str, str2, th);
        }
    }

    public final void e(String str, String str2) {
        if (this.c <= 4) {
            Log.i(str, str2);
        }
    }

    public final void f(String str, String str2) {
        if (this.c <= 5) {
            Log.w(str, str2);
        }
    }

    public final void g(String str, String str2, Throwable th) {
        if (this.c <= 5) {
            Log.w(str, str2, th);
        }
    }

    public bbk(int i) {
        this.c = i;
    }
}
