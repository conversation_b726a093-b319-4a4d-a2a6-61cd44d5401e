package defpackage;

import android.net.Uri;
import java.util.concurrent.Executor;

/* renamed from: cwc  reason: default package */
/* compiled from: PG */
public final class cwc implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;
    private final jjk e;
    private final jjk f;
    private final jjk g;
    private final jjk h;
    private final jjk i;
    private final jjk j;

    public cwc(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, jjk jjk9, jjk jjk10) {
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
        this.e = jjk5;
        this.f = jjk6;
        this.g = jjk7;
        this.h = jjk8;
        this.i = jjk9;
        this.j = jjk10;
    }

    /* renamed from: a */
    public final cwb b() {
        cqh cqh = (cqh) this.j.b();
        return new cwb((cyk) this.a.b(), ((cxc) this.b).b(), (cwo) this.c.b(), (cwo) this.d.b(), (Uri) this.e.b(), (Uri) this.f.b(), ((cwp) this.g).b(), (kjd) this.h.b(), (Executor) this.i.b());
    }
}
