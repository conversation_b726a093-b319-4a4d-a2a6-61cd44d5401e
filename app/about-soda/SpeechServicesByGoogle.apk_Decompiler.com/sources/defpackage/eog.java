package defpackage;

/* renamed from: eog  reason: default package */
/* compiled from: PG */
final class eog extends jnv implements jna {
    final /* synthetic */ eoj a;
    final /* synthetic */ eoa b;
    final /* synthetic */ int c;
    private final /* synthetic */ int d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eog(eoj eoj, eoa eoa, int i, int i2) {
        super(1);
        this.d = i2;
        this.a = eoj;
        this.b = eoa;
        this.c = i;
    }

    public final /* synthetic */ Object a(Object obj) {
        if (this.d != 0) {
            eam eam = (eam) obj;
            jnu.e(eam, "it");
            this.a.a(this.b, this.c, eam, true);
            return jkd.a;
        }
        eam eam2 = (eam) obj;
        jnu.e(eam2, "it");
        this.a.a(this.b, this.c, eam2, true);
        return jkd.a;
    }
}
