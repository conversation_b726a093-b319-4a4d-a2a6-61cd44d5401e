package defpackage;

import java.util.HashMap;
import java.util.Map;

/* renamed from: bdz  reason: default package */
/* compiled from: PG */
public final class bdz {
    public static final /* synthetic */ int d = 0;
    public final bcr a;
    public final bbv b;
    public final Map c = new HashMap();

    static {
        bbk.b("DelayedWorkTracker");
    }

    public bdz(bcr bcr, bbv bbv) {
        this.a = bcr;
        this.b = bbv;
    }
}
