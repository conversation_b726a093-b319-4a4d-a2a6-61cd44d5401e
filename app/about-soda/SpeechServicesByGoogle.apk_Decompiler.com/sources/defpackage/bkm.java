package defpackage;

import android.os.Bundle;
import android.view.KeyEvent;
import android.view.View;
import android.widget.EditText;
import android.widget.TextView;
import androidx.preference.EditTextPreference;

/* renamed from: bkm  reason: default package */
/* compiled from: PG */
public final class bkm extends bkr implements TextView.OnEditorActionListener {
    private EditText an;
    private CharSequence ao;
    private final boolean ap = true;

    private final EditTextPreference G() {
        return (EditTextPreference) F();
    }

    /* access modifiers changed from: protected */
    public final void B(View view) {
        super.B(view);
        EditText editText = (EditText) bnv.h(view, 16908291);
        this.an = editText;
        if (editText != null) {
            editText.requestFocus();
            this.an.setText(this.ao);
            this.an.setInputType(1);
            this.an.setImeOptions(6);
            this.an.setOnEditorActionListener(this);
            EditText editText2 = this.an;
            editText2.setSelection(editText2.getText().length());
            return;
        }
        throw new IllegalStateException("Dialog view must contain an EditText with id @android:id/edit");
    }

    /* access modifiers changed from: protected */
    public final void C(boolean z) {
        if (z) {
            String obj = this.an.getText().toString();
            if (G().callChangeListener(obj)) {
                G().setText(obj);
            }
        }
    }

    /* access modifiers changed from: protected */
    public final boolean D() {
        return true;
    }

    public final void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        if (bundle == null) {
            this.ao = G().getText();
        } else {
            this.ao = bundle.getCharSequence("EditTextPreferenceDialogFragment.text");
        }
    }

    public final boolean onEditorAction(TextView textView, int i, KeyEvent keyEvent) {
        if (i != 6 || !this.ap) {
            return false;
        }
        G().callChangeListener(textView.getText());
        c();
        return true;
    }

    public final void onSaveInstanceState(Bundle bundle) {
        super.onSaveInstanceState(bundle);
        bundle.putCharSequence("EditTextPreferenceDialogFragment.text", this.ao);
    }
}
