package android.support.v7.widget;

import android.animation.LayoutTransition;
import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.StateListDrawable;
import android.os.Parcelable;
import android.os.SystemClock;
import android.os.Trace;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.view.Display;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.VelocityTracker;
import android.view.View;
import android.view.ViewConfiguration;
import android.view.ViewGroup;
import android.view.ViewParent;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityManager;
import android.view.animation.Interpolator;
import android.widget.EdgeEffect;
import android.widget.OverScroller;
import androidx.preference.Preference;
import androidx.wear.ambient.AmbientMode;
import com.google.android.tts.R;
import java.lang.ref.WeakReference;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Objects;

/* compiled from: PG */
public class RecyclerView extends ViewGroup implements vg {
    public static final boolean a = true;
    public static final /* synthetic */ int ab = 0;
    private static final int[] ac = {16843830};
    private static final float ad = ((float) (Math.log(0.78d) / Math.log(0.9d)));
    private static final Class[] ae;
    public static final boolean b = true;
    public static final Interpolator c = new yq(1);
    static final lb d = new lb();
    public EdgeEffect A;
    public EdgeEffect B;
    public EdgeEffect C;
    public EdgeEffect D;
    public kh E;
    public ko F;
    public final int G;
    public final int H;
    public float I;

    /* renamed from: J  reason: collision with root package name */
    public float f7J;
    public final lc K;
    public jh L;
    public jf M;
    public final la N;
    public boolean O;
    public boolean P;
    public boolean Q;
    public lf R;
    public final int[] S;
    final List T;
    boolean U;
    va V;
    public dxr W;
    private final int[] aA;
    private final int[] aB;
    private Runnable aC;
    private boolean aD;
    private int aE;
    private int aF;
    private final vb aG;
    private ke aH;
    private AmbientMode.AmbientController aI;
    private final AmbientMode.AmbientController aJ;
    public final bvj aa;
    private final float af;
    private final ku ag;
    private final Rect ah;
    private int ai;
    private boolean aj;
    private int ak;
    private final AccessibilityManager al;
    private int am;
    private int an;
    private int ao;
    private int ap;
    private VelocityTracker aq;
    private int ar;
    private int as;
    private int at;
    private int au;
    private int av;
    private boolean aw;
    private List ax;
    private final int[] ay;
    private vh az;
    public final ks e;
    public kw f;
    public il g;
    public boolean h;
    public final Runnable i;
    public final Rect j;
    public final RectF k;
    public kc l;
    public kl m;
    public final List n;
    public final ArrayList o;
    public final ArrayList p;
    public kp q;
    public boolean r;
    public boolean s;
    public boolean t;
    public boolean u;
    public boolean v;
    public boolean w;
    public List x;
    public boolean y;
    boolean z;

    static {
        Class cls = Integer.TYPE;
        ae = new Class[]{Context.class, AttributeSet.class, cls, cls};
    }

    public RecyclerView(Context context) {
        this(context, (AttributeSet) null);
    }

    public static void K(View view, Rect rect) {
        km kmVar = (km) view.getLayoutParams();
        Rect rect2 = kmVar.d;
        rect.set((view.getLeft() - rect2.left) - kmVar.leftMargin, (view.getTop() - rect2.top) - kmVar.topMargin, view.getRight() + rect2.right + kmVar.rightMargin, view.getBottom() + rect2.bottom + kmVar.bottomMargin);
    }

    private final int a(int i2, float f2) {
        float width = ((float) i2) / ((float) getWidth());
        float height = f2 / ((float) getHeight());
        EdgeEffect edgeEffect = this.A;
        float f3 = 0.0f;
        if (edgeEffect == null || xm.b(edgeEffect) == 0.0f) {
            EdgeEffect edgeEffect2 = this.C;
            if (!(edgeEffect2 == null || xm.b(edgeEffect2) == 0.0f)) {
                if (canScrollHorizontally(1)) {
                    this.C.onRelease();
                } else {
                    float c2 = xm.c(this.C, width, height);
                    if (xm.b(this.C) == 0.0f) {
                        this.C.onRelease();
                    }
                    f3 = c2;
                }
                invalidate();
            }
        } else {
            if (canScrollHorizontally(-1)) {
                this.A.onRelease();
            } else {
                float f4 = -xm.c(this.A, -width, 1.0f - height);
                if (xm.b(this.A) == 0.0f) {
                    this.A.onRelease();
                }
                f3 = f4;
            }
            invalidate();
        }
        return Math.round(f3 * ((float) getWidth()));
    }

    private final void aA() {
        aJ();
        ad(0);
    }

    private final void aB() {
        View view;
        mf mfVar;
        long j2;
        int i2;
        View l2;
        boolean z2 = true;
        this.N.b(1);
        J(this.N);
        this.N.i = false;
        af();
        this.aa.p();
        Q();
        aF();
        ld ldVar = null;
        if (!this.aw || !hasFocus() || this.l == null) {
            view = null;
        } else {
            view = getFocusedChild();
        }
        if (!(view == null || (l2 = l(view)) == null)) {
            ldVar = i(l2);
        }
        if (ldVar == null) {
            aI();
        } else {
            la laVar = this.N;
            if (this.l.hasStableIds()) {
                j2 = ldVar.getItemId();
            } else {
                j2 = -1;
            }
            laVar.m = j2;
            la laVar2 = this.N;
            if (this.y) {
                i2 = -1;
            } else if (ldVar.isRemoved()) {
                i2 = ldVar.mOldPosition;
            } else {
                i2 = ldVar.getAbsoluteAdapterPosition();
            }
            laVar2.l = i2;
            la laVar3 = this.N;
            View view2 = ldVar.itemView;
            int id = view2.getId();
            while (!view2.isFocused() && (view2 instanceof ViewGroup) && view2.hasFocus()) {
                view2 = ((ViewGroup) view2).getFocusedChild();
                if (view2.getId() != -1) {
                    id = view2.getId();
                }
            }
            laVar3.n = id;
        }
        la laVar4 = this.N;
        if (!laVar4.j || !this.P) {
            z2 = false;
        }
        laVar4.h = z2;
        this.P = false;
        this.O = false;
        laVar4.g = laVar4.k;
        laVar4.e = this.l.getItemCount();
        aD(this.ay);
        if (this.N.j) {
            int a2 = this.g.a();
            for (int i3 = 0; i3 < a2; i3++) {
                ld j3 = j(this.g.e(i3));
                if (!j3.shouldIgnore() && (!j3.isInvalid() || this.l.hasStableIds())) {
                    kh.j(j3);
                    j3.getUnmodifiedPayloads();
                    this.aa.w(j3, kh.k(j3));
                    if (this.N.h && j3.isUpdated() && !j3.isRemoved() && !j3.shouldIgnore() && !j3.isInvalid()) {
                        this.aa.o(e(j3), j3);
                    }
                }
            }
        }
        if (this.N.k) {
            int c2 = this.g.c();
            for (int i4 = 0; i4 < c2; i4++) {
                ld j4 = j(this.g.f(i4));
                if (!j4.shouldIgnore()) {
                    j4.saveOldPosition();
                }
            }
            la laVar5 = this.N;
            boolean z3 = laVar5.f;
            laVar5.f = false;
            this.m.o(this.e, laVar5);
            this.N.f = z3;
            for (int i5 = 0; i5 < this.g.a(); i5++) {
                ld j5 = j(this.g.e(i5));
                if (!j5.shouldIgnore() && ((mfVar = (mf) ((pa) this.aa.b).get(j5)) == null || (mfVar.a & 4) == 0)) {
                    kh.j(j5);
                    boolean hasAnyOfTheFlags = j5.hasAnyOfTheFlags(8192);
                    j5.getUnmodifiedPayloads();
                    bvh k2 = kh.k(j5);
                    if (hasAnyOfTheFlags) {
                        ax(j5, k2);
                    } else {
                        bvj bvj = this.aa;
                        mf mfVar2 = (mf) ((pa) bvj.b).get(j5);
                        if (mfVar2 == null) {
                            Object obj = bvj.b;
                            mfVar2 = mf.a();
                            ((pa) obj).put(j5, mfVar2);
                        }
                        mfVar2.a |= 2;
                        mfVar2.c = k2;
                    }
                }
            }
            x();
        } else {
            x();
        }
        R();
        ag(false);
        this.N.d = 2;
    }

    private final void aC() {
        boolean z2;
        af();
        Q();
        this.N.b(6);
        this.W.f();
        int itemCount = this.l.getItemCount();
        la laVar = this.N;
        laVar.e = itemCount;
        laVar.c = 0;
        if (this.f != null && this.l.canRestoreState()) {
            Parcelable parcelable = this.f.a;
            if (parcelable != null) {
                this.m.U(parcelable);
            }
            this.f = null;
        }
        la laVar2 = this.N;
        laVar2.g = false;
        this.m.o(this.e, laVar2);
        la laVar3 = this.N;
        laVar3.f = false;
        if (!laVar3.j || this.E == null) {
            z2 = false;
        } else {
            z2 = true;
        }
        laVar3.j = z2;
        laVar3.d = 4;
        R();
        ag(false);
    }

    private final void aD(int[] iArr) {
        int a2 = this.g.a();
        if (a2 != 0) {
            int i2 = Integer.MIN_VALUE;
            int i3 = Preference.DEFAULT_ORDER;
            for (int i4 = 0; i4 < a2; i4++) {
                ld j2 = j(this.g.e(i4));
                if (!j2.shouldIgnore()) {
                    int layoutPosition = j2.getLayoutPosition();
                    if (layoutPosition < i3) {
                        i3 = layoutPosition;
                    }
                    if (layoutPosition > i2) {
                        i2 = layoutPosition;
                    }
                }
            }
            iArr[0] = i3;
            iArr[1] = i2;
            return;
        }
        iArr[0] = -1;
        iArr[1] = -1;
    }

    private final void aE(MotionEvent motionEvent) {
        int i2;
        int actionIndex = motionEvent.getActionIndex();
        if (motionEvent.getPointerId(actionIndex) == this.ap) {
            if (actionIndex == 0) {
                i2 = 1;
            } else {
                i2 = 0;
            }
            this.ap = motionEvent.getPointerId(i2);
            int x2 = (int) (motionEvent.getX(i2) + 0.5f);
            this.at = x2;
            this.ar = x2;
            int y2 = (int) (motionEvent.getY(i2) + 0.5f);
            this.au = y2;
            this.as = y2;
        }
    }

    private final void aF() {
        boolean z2;
        boolean z3;
        boolean z4;
        if (this.y) {
            this.W.k();
            if (this.z) {
                this.m.y();
            }
        }
        if (aN()) {
            this.W.h();
        } else {
            this.W.f();
        }
        boolean z5 = true;
        if (this.O || this.P) {
            z2 = true;
        } else {
            z2 = false;
        }
        la laVar = this.N;
        if (!this.t || this.E == null || ((!(z4 = this.y) && !z2 && !this.m.v) || (z4 && !this.l.hasStableIds()))) {
            z3 = false;
        } else {
            z3 = true;
        }
        laVar.j = z3;
        la laVar2 = this.N;
        if (!laVar2.j || !z2 || this.y || !aN()) {
            z5 = false;
        }
        laVar2.k = z5;
    }

    private final void aG() {
        boolean z2;
        EdgeEffect edgeEffect = this.A;
        if (edgeEffect != null) {
            edgeEffect.onRelease();
            z2 = this.A.isFinished();
        } else {
            z2 = false;
        }
        EdgeEffect edgeEffect2 = this.B;
        if (edgeEffect2 != null) {
            edgeEffect2.onRelease();
            z2 |= this.B.isFinished();
        }
        EdgeEffect edgeEffect3 = this.C;
        if (edgeEffect3 != null) {
            edgeEffect3.onRelease();
            z2 |= this.C.isFinished();
        }
        EdgeEffect edgeEffect4 = this.D;
        if (edgeEffect4 != null) {
            edgeEffect4.onRelease();
            z2 |= this.D.isFinished();
        }
        if (z2) {
            postInvalidateOnAnimation();
        }
    }

    private final void aH(View view, View view2) {
        View view3;
        boolean z2;
        if (view2 != null) {
            view3 = view2;
        } else {
            view3 = view;
        }
        this.j.set(0, 0, view3.getWidth(), view3.getHeight());
        ViewGroup.LayoutParams layoutParams = view3.getLayoutParams();
        if (layoutParams instanceof km) {
            km kmVar = (km) layoutParams;
            if (!kmVar.e) {
                Rect rect = kmVar.d;
                this.j.left -= rect.left;
                this.j.right += rect.right;
                this.j.top -= rect.top;
                this.j.bottom += rect.bottom;
            }
        }
        if (view2 != null) {
            offsetDescendantRectToMyCoords(view2, this.j);
            offsetRectIntoDescendantCoords(view, this.j);
        } else {
            view2 = null;
        }
        kl klVar = this.m;
        Rect rect2 = this.j;
        boolean z3 = !this.t;
        if (view2 == null) {
            z2 = true;
        } else {
            z2 = false;
        }
        klVar.bb(this, view, rect2, z3, z2);
    }

    private final void aI() {
        la laVar = this.N;
        laVar.m = -1;
        laVar.l = -1;
        laVar.n = -1;
    }

    private final void aJ() {
        VelocityTracker velocityTracker = this.aq;
        if (velocityTracker != null) {
            velocityTracker.clear();
        }
        ah(0);
        aG();
    }

    private final void aK(int i2) {
        boolean Y = this.m.Y();
        if (this.m.Z()) {
            Y |= true;
        }
        aw(Y ? 1 : 0, i2);
    }

    private final void aL() {
        kz kzVar;
        this.K.d();
        kl klVar = this.m;
        if (klVar != null && (kzVar = klVar.u) != null) {
            kzVar.d();
        }
    }

    private final boolean aM(MotionEvent motionEvent) {
        ArrayList arrayList = this.p;
        int action = motionEvent.getAction();
        int size = arrayList.size();
        int i2 = 0;
        while (i2 < size) {
            kp kpVar = (kp) this.p.get(i2);
            if (!kpVar.i(motionEvent) || action == 3) {
                i2++;
            } else {
                this.q = kpVar;
                return true;
            }
        }
        return false;
    }

    private final boolean aN() {
        if (this.E == null || !this.m.v()) {
            return false;
        }
        return true;
    }

    private final boolean aO(EdgeEffect edgeEffect, int i2, int i3) {
        if (i2 > 0) {
            return true;
        }
        float b2 = xm.b(edgeEffect) * ((float) i3);
        double log = Math.log((double) ((((float) Math.abs(-i2)) * 0.35f) / (this.af * 0.015f)));
        double d2 = (double) ad;
        if (((float) (((double) (this.af * 0.015f)) * Math.exp((d2 / (-1.0d + d2)) * log))) < b2) {
            return true;
        }
        return false;
    }

    private final void aP(Context context, String str, AttributeSet attributeSet, int i2) {
        ClassLoader classLoader;
        Object[] objArr;
        Constructor<? extends U> constructor;
        if (str != null) {
            String trim = str.trim();
            if (!trim.isEmpty()) {
                if (trim.charAt(0) == '.') {
                    trim = String.valueOf(context.getPackageName()).concat(String.valueOf(trim));
                } else if (!trim.contains(".")) {
                    trim = RecyclerView.class.getPackage().getName() + '.' + trim;
                }
                try {
                    if (isInEditMode()) {
                        classLoader = getClass().getClassLoader();
                    } else {
                        classLoader = context.getClassLoader();
                    }
                    Class<? extends U> asSubclass = Class.forName(trim, false, classLoader).asSubclass(kl.class);
                    try {
                        constructor = asSubclass.getConstructor(ae);
                        objArr = new Object[]{context, attributeSet, Integer.valueOf(i2), 0};
                    } catch (NoSuchMethodException e2) {
                        objArr = null;
                        constructor = asSubclass.getConstructor((Class[]) null);
                    }
                    constructor.setAccessible(true);
                    ac((kl) constructor.newInstance(objArr));
                } catch (NoSuchMethodException e3) {
                    e3.initCause(e2);
                    throw new IllegalStateException(a.aq(trim, attributeSet, ": Error creating LayoutManager "), e3);
                } catch (ClassNotFoundException e4) {
                    throw new IllegalStateException(a.aq(trim, attributeSet, ": Unable to find LayoutManager "), e4);
                } catch (InvocationTargetException e5) {
                    throw new IllegalStateException(a.aq(trim, attributeSet, ": Could not instantiate the LayoutManager: "), e5);
                } catch (InstantiationException e6) {
                    throw new IllegalStateException(a.aq(trim, attributeSet, ": Could not instantiate the LayoutManager: "), e6);
                } catch (IllegalAccessException e7) {
                    throw new IllegalStateException(a.aq(trim, attributeSet, ": Cannot access non-public constructor "), e7);
                } catch (ClassCastException e8) {
                    throw new IllegalStateException(a.aq(trim, attributeSet, ": Class is not a LayoutManager "), e8);
                }
            }
        }
    }

    public static final int ar(int i2, EdgeEffect edgeEffect, EdgeEffect edgeEffect2, int i3) {
        if (i2 > 0 && edgeEffect != null && xm.b(edgeEffect) != 0.0f) {
            int round = Math.round((((float) (-i3)) / 4.0f) * xm.c(edgeEffect, (((float) (-i2)) * 4.0f) / ((float) i3), 0.5f));
            if (round != i2) {
                edgeEffect.finish();
            }
            return i2 - round;
        } else if (i2 >= 0 || edgeEffect2 == null || xm.b(edgeEffect2) == 0.0f) {
            return i2;
        } else {
            float f2 = (float) i3;
            int round2 = Math.round((f2 / 4.0f) * xm.c(edgeEffect2, (((float) i2) * 4.0f) / f2, 0.5f));
            if (round2 != i2) {
                edgeEffect2.finish();
            }
            return i2 - round2;
        }
    }

    public static final long as() {
        if (b) {
            return System.nanoTime();
        }
        return 0;
    }

    private final int ay(int i2, float f2) {
        float height = ((float) i2) / ((float) getHeight());
        float width = f2 / ((float) getWidth());
        EdgeEffect edgeEffect = this.B;
        float f3 = 0.0f;
        if (edgeEffect == null || xm.b(edgeEffect) == 0.0f) {
            EdgeEffect edgeEffect2 = this.D;
            if (!(edgeEffect2 == null || xm.b(edgeEffect2) == 0.0f)) {
                if (canScrollVertically(1)) {
                    this.D.onRelease();
                } else {
                    float c2 = xm.c(this.D, height, 1.0f - width);
                    if (xm.b(this.D) == 0.0f) {
                        this.D.onRelease();
                    }
                    f3 = c2;
                }
                invalidate();
            }
        } else {
            if (canScrollVertically(-1)) {
                this.B.onRelease();
            } else {
                float f4 = -xm.c(this.B, -height, width);
                if (xm.b(this.B) == 0.0f) {
                    this.B.onRelease();
                }
                f3 = f4;
            }
            invalidate();
        }
        return Math.round(f3 * ((float) getHeight()));
    }

    private final vh az() {
        if (this.az == null) {
            this.az = new vh(this);
        }
        return this.az;
    }

    public static ld j(View view) {
        if (view == null) {
            return null;
        }
        return ((km) view.getLayoutParams()).c;
    }

    public static RecyclerView k(View view) {
        if (!(view instanceof ViewGroup)) {
            return null;
        }
        if (view instanceof RecyclerView) {
            return (RecyclerView) view;
        }
        ViewGroup viewGroup = (ViewGroup) view;
        int childCount = viewGroup.getChildCount();
        for (int i2 = 0; i2 < childCount; i2++) {
            RecyclerView k2 = k(viewGroup.getChildAt(i2));
            if (k2 != null) {
                return k2;
            }
        }
        return null;
    }

    public static void w(ld ldVar) {
        WeakReference weakReference = ldVar.mNestedRecyclerView;
        if (weakReference != null) {
            View view = (View) weakReference.get();
            while (view != null) {
                if (view != ldVar.itemView) {
                    ViewParent parent = view.getParent();
                    if (parent instanceof View) {
                        view = (View) parent;
                    } else {
                        view = null;
                    }
                } else {
                    return;
                }
            }
            ldVar.mNestedRecyclerView = null;
        }
    }

    public final void A(int i2, int i3) {
        int paddingLeft = getPaddingLeft() + getPaddingRight();
        int[] iArr = wj.a;
        setMeasuredDimension(kl.an(i2, paddingLeft, getMinimumWidth()), kl.an(i3, getPaddingTop() + getPaddingBottom(), getMinimumHeight()));
    }

    public final void B(View view) {
        ld j2 = j(view);
        kc kcVar = this.l;
        if (!(kcVar == null || j2 == null)) {
            kcVar.onViewDetachedFromWindow(j2);
        }
        List list = this.x;
        if (list != null) {
            int size = list.size();
            while (true) {
                size--;
                if (size >= 0) {
                    ((kn) this.x.get(size)).b();
                } else {
                    return;
                }
            }
        }
    }

    /* access modifiers changed from: package-private */
    /* JADX WARNING: Code restructure failed: missing block: B:136:0x0313, code lost:
        if (r0.g.k(getFocusedChild()) != false) goto L_0x0315;
     */
    /* JADX WARNING: Removed duplicated region for block: B:164:0x0381  */
    /* JADX WARNING: Removed duplicated region for block: B:165:0x0384  */
    /* JADX WARNING: Removed duplicated region for block: B:187:0x03d0  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void C() {
        /*
            r16 = this;
            r0 = r16
            kc r1 = r0.l
            java.lang.String r2 = "RecyclerView"
            if (r1 != 0) goto L_0x000e
            java.lang.String r1 = "No adapter attached; skipping layout"
            android.util.Log.w(r2, r1)
            return
        L_0x000e:
            kl r1 = r0.m
            if (r1 != 0) goto L_0x0018
            java.lang.String r1 = "No layout manager attached; skipping layout"
            android.util.Log.e(r2, r1)
            return
        L_0x0018:
            la r1 = r0.N
            r3 = 0
            r1.i = r3
            boolean r1 = r0.aD
            r4 = 1
            if (r1 == 0) goto L_0x0034
            int r1 = r0.aE
            int r5 = r16.getWidth()
            if (r1 != r5) goto L_0x0032
            int r1 = r0.aF
            int r5 = r16.getHeight()
            if (r1 == r5) goto L_0x0034
        L_0x0032:
            r1 = r4
            goto L_0x0035
        L_0x0034:
            r1 = r3
        L_0x0035:
            r0.aE = r3
            r0.aF = r3
            r0.aD = r3
            la r5 = r0.N
            int r5 = r5.d
            if (r5 != r4) goto L_0x004d
            r16.aB()
            kl r1 = r0.m
            r1.aT(r0)
            r16.aC()
            goto L_0x0088
        L_0x004d:
            dxr r5 = r0.W
            java.lang.Object r6 = r5.c
            java.util.ArrayList r6 = (java.util.ArrayList) r6
            boolean r6 = r6.isEmpty()
            if (r6 != 0) goto L_0x0064
            java.lang.Object r5 = r5.d
            java.util.ArrayList r5 = (java.util.ArrayList) r5
            boolean r5 = r5.isEmpty()
            if (r5 != 0) goto L_0x0064
            goto L_0x0080
        L_0x0064:
            if (r1 != 0) goto L_0x0080
            kl r1 = r0.m
            int r1 = r1.D
            int r5 = r16.getWidth()
            if (r1 != r5) goto L_0x0080
            kl r1 = r0.m
            int r1 = r1.E
            int r5 = r16.getHeight()
            if (r1 != r5) goto L_0x0080
            kl r1 = r0.m
            r1.aT(r0)
            goto L_0x0088
        L_0x0080:
            kl r1 = r0.m
            r1.aT(r0)
            r16.aC()
        L_0x0088:
            la r1 = r0.N
            r5 = 4
            r1.b(r5)
            r16.af()
            r16.Q()
            la r1 = r0.N
            r1.d = r4
            boolean r1 = r1.j
            r6 = -1
            r7 = 0
            if (r1 == 0) goto L_0x0286
            il r1 = r0.g
            int r1 = r1.a()
            int r1 = r1 + r6
        L_0x00a5:
            if (r1 < 0) goto L_0x01c9
            il r8 = r0.g
            android.view.View r8 = r8.e(r1)
            ld r8 = j(r8)
            boolean r9 = r8.shouldIgnore()
            if (r9 == 0) goto L_0x00b9
            goto L_0x01c4
        L_0x00b9:
            long r9 = r0.e(r8)
            bvh r11 = new bvh
            r11.<init>()
            r11.d(r8)
            bvj r12 = r0.aa
            java.lang.Object r12 = r12.a
            ox r12 = (defpackage.ox) r12
            java.lang.Object r12 = r12.c(r9)
            ld r12 = (defpackage.ld) r12
            if (r12 == 0) goto L_0x01bf
            boolean r13 = r12.shouldIgnore()
            if (r13 != 0) goto L_0x01bf
            bvj r13 = r0.aa
            boolean r13 = r13.s(r12)
            bvj r14 = r0.aa
            boolean r14 = r14.s(r8)
            if (r13 == 0) goto L_0x00f0
            if (r12 != r8) goto L_0x00f0
            bvj r9 = r0.aa
            r9.v(r8, r11)
            goto L_0x01c4
        L_0x00f0:
            bvj r15 = r0.aa
            bvh r15 = r15.u(r12, r5)
            bvj r5 = r0.aa
            r5.v(r8, r11)
            bvj r5 = r0.aa
            r11 = 8
            bvh r5 = r5.u(r8, r11)
            if (r15 != 0) goto L_0x0195
            il r5 = r0.g
            int r5 = r5.a()
            r11 = r3
        L_0x010c:
            if (r11 >= r5) goto L_0x0174
            il r13 = r0.g
            android.view.View r13 = r13.e(r11)
            ld r13 = j(r13)
            if (r13 != r8) goto L_0x011b
            goto L_0x0171
        L_0x011b:
            long r14 = r0.e(r13)
            int r14 = (r14 > r9 ? 1 : (r14 == r9 ? 0 : -1))
            if (r14 != 0) goto L_0x0171
            kc r1 = r0.l
            java.lang.String r2 = " \n View Holder 2:"
            if (r1 == 0) goto L_0x0150
            boolean r1 = r1.hasStableIds()
            if (r1 == 0) goto L_0x0150
            java.lang.IllegalStateException r1 = new java.lang.IllegalStateException
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            java.lang.String r4 = "Two different ViewHolders have the same stable ID. Stable IDs in your adapter MUST BE unique and SHOULD NOT change.\n ViewHolder 1:"
            r3.<init>(r4)
            r3.append(r13)
            r3.append(r2)
            r3.append(r8)
            java.lang.String r2 = r16.m()
            r3.append(r2)
            java.lang.String r2 = r3.toString()
            r1.<init>(r2)
            throw r1
        L_0x0150:
            java.lang.IllegalStateException r1 = new java.lang.IllegalStateException
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            java.lang.String r4 = "Two different ViewHolders have the same change ID. This might happen due to inconsistent Adapter update events or if the LayoutManager lays out the same View multiple times.\n ViewHolder 1:"
            r3.<init>(r4)
            r3.append(r13)
            r3.append(r2)
            r3.append(r8)
            java.lang.String r2 = r16.m()
            r3.append(r2)
            java.lang.String r2 = r3.toString()
            r1.<init>(r2)
            throw r1
        L_0x0171:
            int r11 = r11 + 1
            goto L_0x010c
        L_0x0174:
            java.lang.StringBuilder r5 = new java.lang.StringBuilder
            java.lang.String r9 = "Problem while matching changed view holders with the newones. The pre-layout information for the change holder "
            r5.<init>(r9)
            r5.append(r12)
            java.lang.String r9 = " cannot be found but it is necessary for "
            r5.append(r9)
            r5.append(r8)
            java.lang.String r8 = r16.m()
            r5.append(r8)
            java.lang.String r5 = r5.toString()
            android.util.Log.e(r2, r5)
            goto L_0x01c4
        L_0x0195:
            r12.setIsRecyclable(r3)
            if (r13 == 0) goto L_0x019d
            r0.s(r12)
        L_0x019d:
            if (r12 == r8) goto L_0x01b3
            if (r14 == 0) goto L_0x01a4
            r0.s(r8)
        L_0x01a4:
            r12.mShadowedHolder = r8
            r0.s(r12)
            ks r9 = r0.e
            r9.m(r12)
            r8.setIsRecyclable(r3)
            r8.mShadowingHolder = r12
        L_0x01b3:
            kh r9 = r0.E
            boolean r5 = r9.l(r12, r8, r15, r5)
            if (r5 == 0) goto L_0x01c4
            r16.T()
            goto L_0x01c4
        L_0x01bf:
            bvj r5 = r0.aa
            r5.v(r8, r11)
        L_0x01c4:
            int r1 = r1 + -1
            r5 = 4
            goto L_0x00a5
        L_0x01c9:
            bvj r1 = r0.aa
            androidx.wear.ambient.AmbientMode$AmbientController r2 = r0.aJ
            java.lang.Object r5 = r1.b
            pa r5 = (defpackage.pa) r5
            int r5 = r5.f
            int r5 = r5 + r6
        L_0x01d4:
            if (r5 < 0) goto L_0x0286
            java.lang.Object r8 = r1.b
            pa r8 = (defpackage.pa) r8
            java.lang.Object r8 = r8.d(r5)
            r10 = r8
            ld r10 = (defpackage.ld) r10
            java.lang.Object r8 = r1.b
            pa r8 = (defpackage.pa) r8
            java.lang.Object r8 = r8.e(r5)
            mf r8 = (defpackage.mf) r8
            int r9 = r8.a
            r11 = r9 & 3
            r12 = 3
            if (r11 != r12) goto L_0x01f7
            r2.v(r10)
            goto L_0x027e
        L_0x01f7:
            r11 = r9 & 1
            if (r11 == 0) goto L_0x020b
            bvh r9 = r8.c
            if (r9 != 0) goto L_0x0204
            r2.v(r10)
            goto L_0x027e
        L_0x0204:
            bvh r11 = r8.d
            r2.x(r10, r9, r11)
            goto L_0x027e
        L_0x020b:
            r11 = r9 & 14
            r12 = 14
            if (r11 != r12) goto L_0x021a
            bvh r9 = r8.c
            bvh r11 = r8.d
            r2.w(r10, r9, r11)
            goto L_0x027e
        L_0x021a:
            r11 = r9 & 12
            r12 = 12
            if (r11 != r12) goto L_0x0269
            bvh r9 = r8.c
            bvh r11 = r8.d
            r10.setIsRecyclable(r3)
            java.lang.Object r12 = r2.a
            android.support.v7.widget.RecyclerView r12 = (android.support.v7.widget.RecyclerView) r12
            boolean r13 = r12.y
            if (r13 == 0) goto L_0x023f
            kh r12 = r12.E
            boolean r9 = r12.l(r10, r10, r9, r11)
            if (r9 == 0) goto L_0x027e
            java.lang.Object r9 = r2.a
            android.support.v7.widget.RecyclerView r9 = (android.support.v7.widget.RecyclerView) r9
            r9.T()
            goto L_0x027e
        L_0x023f:
            kh r12 = r12.E
            int r13 = r9.b
            int r14 = r11.b
            if (r13 != r14) goto L_0x0252
            int r15 = r9.a
            int r6 = r11.a
            if (r15 == r6) goto L_0x024e
            goto L_0x0252
        L_0x024e:
            r12.a(r10)
            goto L_0x027e
        L_0x0252:
            int r6 = r9.a
            int r15 = r11.a
            r9 = r12
            r11 = r13
            r12 = r6
            r13 = r14
            r14 = r15
            boolean r6 = r9.g(r10, r11, r12, r13, r14)
            if (r6 == 0) goto L_0x027e
            java.lang.Object r6 = r2.a
            android.support.v7.widget.RecyclerView r6 = (android.support.v7.widget.RecyclerView) r6
            r6.T()
            goto L_0x027e
        L_0x0269:
            r6 = r9 & 4
            if (r6 == 0) goto L_0x0273
            bvh r6 = r8.c
            r2.x(r10, r6, r7)
            goto L_0x027e
        L_0x0273:
            r6 = r9 & 8
            if (r6 == 0) goto L_0x027e
            bvh r6 = r8.c
            bvh r9 = r8.d
            r2.w(r10, r6, r9)
        L_0x027e:
            defpackage.mf.b(r8)
            int r5 = r5 + -1
            r6 = -1
            goto L_0x01d4
        L_0x0286:
            kl r1 = r0.m
            ks r2 = r0.e
            r1.aO(r2)
            la r1 = r0.N
            int r2 = r1.e
            r1.b = r2
            r0.y = r3
            r0.z = r3
            r1.j = r3
            r1.k = r3
            kl r1 = r0.m
            r1.v = r3
            ks r1 = r0.e
            java.util.ArrayList r1 = r1.b
            if (r1 == 0) goto L_0x02a8
            r1.clear()
        L_0x02a8:
            kl r1 = r0.m
            boolean r2 = r1.A
            if (r2 == 0) goto L_0x02b7
            r1.z = r3
            r1.A = r3
            ks r1 = r0.e
            r1.n()
        L_0x02b7:
            kl r1 = r0.m
            la r2 = r0.N
            r1.p(r2)
            r16.R()
            r0.ag(r3)
            bvj r1 = r0.aa
            r1.p()
            int[] r1 = r0.ay
            r2 = r1[r3]
            r5 = r1[r4]
            r0.aD(r1)
            int[] r1 = r0.ay
            r6 = r1[r3]
            if (r6 != r2) goto L_0x02dc
            r1 = r1[r4]
            if (r1 == r5) goto L_0x02df
        L_0x02dc:
            r0.E(r3, r3)
        L_0x02df:
            boolean r1 = r0.aw
            if (r1 == 0) goto L_0x03e9
            kc r1 = r0.l
            if (r1 == 0) goto L_0x03e9
            boolean r1 = r16.hasFocus()
            if (r1 == 0) goto L_0x03e9
            int r1 = r16.getDescendantFocusability()
            r2 = 393216(0x60000, float:5.51013E-40)
            if (r1 == r2) goto L_0x03e9
            int r1 = r16.getDescendantFocusability()
            r2 = 131072(0x20000, float:1.83671E-40)
            if (r1 != r2) goto L_0x0303
            boolean r1 = r16.isFocused()
            if (r1 != 0) goto L_0x03e9
        L_0x0303:
            boolean r1 = r16.isFocused()
            if (r1 != 0) goto L_0x0315
            android.view.View r1 = r16.getFocusedChild()
            il r2 = r0.g
            boolean r1 = r2.k(r1)
            if (r1 == 0) goto L_0x03e9
        L_0x0315:
            la r1 = r0.N
            long r1 = r1.m
            r4 = -1
            int r1 = (r1 > r4 ? 1 : (r1 == r4 ? 0 : -1))
            if (r1 == 0) goto L_0x036b
            kc r1 = r0.l
            boolean r1 = r1.hasStableIds()
            if (r1 == 0) goto L_0x036b
            la r1 = r0.N
            long r1 = r1.m
            kc r6 = r0.l
            if (r6 == 0) goto L_0x036b
            boolean r6 = r6.hasStableIds()
            if (r6 != 0) goto L_0x0336
            goto L_0x036b
        L_0x0336:
            il r6 = r0.g
            int r6 = r6.c()
            r8 = r3
            r9 = r7
        L_0x033e:
            if (r8 >= r6) goto L_0x036c
            il r10 = r0.g
            android.view.View r10 = r10.f(r8)
            ld r10 = j(r10)
            if (r10 == 0) goto L_0x0368
            boolean r11 = r10.isRemoved()
            if (r11 != 0) goto L_0x0368
            long r11 = r10.getItemId()
            int r11 = (r11 > r1 ? 1 : (r11 == r1 ? 0 : -1))
            if (r11 != 0) goto L_0x0368
            il r9 = r0.g
            android.view.View r11 = r10.itemView
            boolean r9 = r9.k(r11)
            if (r9 == 0) goto L_0x0366
            r9 = r10
            goto L_0x0368
        L_0x0366:
            r9 = r10
            goto L_0x036c
        L_0x0368:
            int r8 = r8 + 1
            goto L_0x033e
        L_0x036b:
            r9 = r7
        L_0x036c:
            if (r9 == 0) goto L_0x0384
            il r1 = r0.g
            android.view.View r2 = r9.itemView
            boolean r1 = r1.k(r2)
            if (r1 != 0) goto L_0x0384
            android.view.View r1 = r9.itemView
            boolean r1 = r1.hasFocusable()
            if (r1 != 0) goto L_0x0381
            goto L_0x0384
        L_0x0381:
            android.view.View r7 = r9.itemView
            goto L_0x03ce
        L_0x0384:
            il r1 = r0.g
            int r1 = r1.a()
            if (r1 <= 0) goto L_0x03ce
            la r1 = r0.N
            int r2 = r1.l
            r6 = -1
            if (r2 != r6) goto L_0x0394
            goto L_0x0395
        L_0x0394:
            r3 = r2
        L_0x0395:
            int r1 = r1.a()
            r2 = r3
        L_0x039a:
            if (r2 >= r1) goto L_0x03b1
            ld r6 = r0.h(r2)
            if (r6 != 0) goto L_0x03a3
            goto L_0x03b1
        L_0x03a3:
            android.view.View r8 = r6.itemView
            boolean r8 = r8.hasFocusable()
            if (r8 == 0) goto L_0x03ae
            android.view.View r7 = r6.itemView
            goto L_0x03ce
        L_0x03ae:
            int r2 = r2 + 1
            goto L_0x039a
        L_0x03b1:
            int r1 = java.lang.Math.min(r1, r3)
            r2 = -1
            int r1 = r1 + r2
        L_0x03b7:
            if (r1 < 0) goto L_0x03ce
            ld r2 = r0.h(r1)
            if (r2 != 0) goto L_0x03c0
            goto L_0x03ce
        L_0x03c0:
            android.view.View r3 = r2.itemView
            boolean r3 = r3.hasFocusable()
            if (r3 == 0) goto L_0x03cb
            android.view.View r7 = r2.itemView
            goto L_0x03ce
        L_0x03cb:
            int r1 = r1 + -1
            goto L_0x03b7
        L_0x03ce:
            if (r7 == 0) goto L_0x03e9
            la r1 = r0.N
            int r1 = r1.n
            long r2 = (long) r1
            int r2 = (r2 > r4 ? 1 : (r2 == r4 ? 0 : -1))
            if (r2 == 0) goto L_0x03e6
            android.view.View r1 = r7.findViewById(r1)
            if (r1 == 0) goto L_0x03e6
            boolean r2 = r1.isFocusable()
            if (r2 == 0) goto L_0x03e6
            r7 = r1
        L_0x03e6:
            r7.requestFocus()
        L_0x03e9:
            r16.aI()
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: android.support.v7.widget.RecyclerView.C():void");
    }

    public final void D(int i2, int i3, int i4, int i5, int[] iArr, int i6, int[] iArr2) {
        az().g(i2, i3, i4, i5, iArr, i6, iArr2);
    }

    public final void E(int i2, int i3) {
        this.an++;
        int scrollX = getScrollX();
        int scrollY = getScrollY();
        onScrollChanged(scrollX, scrollY, scrollX - i2, scrollY - i3);
        List list = this.ax;
        if (list != null) {
            int size = list.size();
            while (true) {
                size--;
                if (size < 0) {
                    break;
                }
                ((kq) this.ax.get(size)).a(this, i2, i3);
            }
        }
        this.an--;
    }

    public final void F() {
        if (this.D == null) {
            EdgeEffect c2 = this.aH.c(this);
            this.D = c2;
            if (this.h) {
                c2.setSize((getMeasuredWidth() - getPaddingLeft()) - getPaddingRight(), (getMeasuredHeight() - getPaddingTop()) - getPaddingBottom());
            } else {
                c2.setSize(getMeasuredWidth(), getMeasuredHeight());
            }
        }
    }

    public final void G() {
        if (this.A == null) {
            EdgeEffect c2 = this.aH.c(this);
            this.A = c2;
            if (this.h) {
                c2.setSize((getMeasuredHeight() - getPaddingTop()) - getPaddingBottom(), (getMeasuredWidth() - getPaddingLeft()) - getPaddingRight());
            } else {
                c2.setSize(getMeasuredHeight(), getMeasuredWidth());
            }
        }
    }

    public final void H() {
        if (this.C == null) {
            EdgeEffect c2 = this.aH.c(this);
            this.C = c2;
            if (this.h) {
                c2.setSize((getMeasuredHeight() - getPaddingTop()) - getPaddingBottom(), (getMeasuredWidth() - getPaddingLeft()) - getPaddingRight());
            } else {
                c2.setSize(getMeasuredHeight(), getMeasuredWidth());
            }
        }
    }

    public final void I() {
        if (this.B == null) {
            EdgeEffect c2 = this.aH.c(this);
            this.B = c2;
            if (this.h) {
                c2.setSize((getMeasuredWidth() - getPaddingLeft()) - getPaddingRight(), (getMeasuredHeight() - getPaddingTop()) - getPaddingBottom());
            } else {
                c2.setSize(getMeasuredWidth(), getMeasuredHeight());
            }
        }
    }

    /* access modifiers changed from: package-private */
    public final void J(la laVar) {
        if (this.ao == 2) {
            OverScroller overScroller = this.K.a;
            laVar.o = overScroller.getFinalX() - overScroller.getCurrX();
            laVar.p = overScroller.getFinalY() - overScroller.getCurrY();
            return;
        }
        laVar.o = 0;
        laVar.p = 0;
    }

    /* access modifiers changed from: package-private */
    public final void L() {
        this.D = null;
        this.B = null;
        this.C = null;
        this.A = null;
    }

    public void M() {
        if (this.o.size() != 0) {
            kl klVar = this.m;
            if (klVar != null) {
                klVar.Q("Cannot invalidate item decorations during a scroll or layout");
            }
            O();
            requestLayout();
        }
    }

    public final void N(int i2) {
        if (this.m != null) {
            ad(2);
            this.m.V(i2);
            awakenScrollBars();
        }
    }

    /* access modifiers changed from: package-private */
    public final void O() {
        int c2 = this.g.c();
        for (int i2 = 0; i2 < c2; i2++) {
            ((km) this.g.f(i2).getLayoutParams()).e = true;
        }
        ks ksVar = this.e;
        int size = ksVar.c.size();
        for (int i3 = 0; i3 < size; i3++) {
            km kmVar = (km) ((ld) ksVar.c.get(i3)).itemView.getLayoutParams();
            if (kmVar != null) {
                kmVar.e = true;
            }
        }
    }

    public final void P(int i2, int i3, boolean z2) {
        int i4;
        int c2 = this.g.c();
        int i5 = 0;
        while (true) {
            i4 = i2 + i3;
            if (i5 >= c2) {
                break;
            }
            ld j2 = j(this.g.f(i5));
            if (j2 != null && !j2.shouldIgnore()) {
                int i6 = j2.mPosition;
                if (i6 >= i4) {
                    j2.offsetPosition(-i3, z2);
                    this.N.f = true;
                } else if (i6 >= i2) {
                    j2.flagRemovedAndOffsetPosition(i2 - 1, -i3, z2);
                    this.N.f = true;
                }
            }
            i5++;
        }
        ks ksVar = this.e;
        int size = ksVar.c.size();
        while (true) {
            size--;
            if (size >= 0) {
                ld ldVar = (ld) ksVar.c.get(size);
                if (ldVar != null) {
                    int i7 = ldVar.mPosition;
                    if (i7 >= i4) {
                        ldVar.offsetPosition(-i3, z2);
                    } else if (i7 >= i2) {
                        ldVar.addFlags(8);
                        ksVar.i(size);
                    }
                }
            } else {
                requestLayout();
                return;
            }
        }
    }

    public final void Q() {
        this.am++;
    }

    /* access modifiers changed from: package-private */
    public final void R() {
        S(true);
    }

    public final void S(boolean z2) {
        int i2;
        int i3 = this.am - 1;
        this.am = i3;
        if (i3 <= 0) {
            this.am = 0;
            if (z2) {
                int i4 = this.ak;
                this.ak = 0;
                if (i4 != 0 && ao()) {
                    AccessibilityEvent obtain = AccessibilityEvent.obtain();
                    obtain.setEventType(2048);
                    obtain.setContentChangeTypes(i4);
                    sendAccessibilityEventUnchecked(obtain);
                }
                for (int size = this.T.size() - 1; size >= 0; size--) {
                    ld ldVar = (ld) this.T.get(size);
                    if (ldVar.itemView.getParent() == this && !ldVar.shouldIgnore() && (i2 = ldVar.mPendingAccessibilityState) != -1) {
                        ldVar.itemView.setImportantForAccessibility(i2);
                        ldVar.mPendingAccessibilityState = -1;
                    }
                }
                this.T.clear();
            }
        }
    }

    public final void T() {
        if (!this.Q && this.r) {
            Runnable runnable = this.aC;
            int[] iArr = wj.a;
            postOnAnimation(runnable);
            this.Q = true;
        }
    }

    public final void U(boolean z2) {
        this.z = z2 | this.z;
        this.y = true;
        int c2 = this.g.c();
        for (int i2 = 0; i2 < c2; i2++) {
            ld j2 = j(this.g.f(i2));
            if (j2 != null && !j2.shouldIgnore()) {
                j2.addFlags(6);
            }
        }
        O();
        ks ksVar = this.e;
        int size = ksVar.c.size();
        for (int i3 = 0; i3 < size; i3++) {
            ld ldVar = (ld) ksVar.c.get(i3);
            if (ldVar != null) {
                ldVar.addFlags(6);
                ldVar.addChangePayload((Object) null);
            }
        }
        kc kcVar = ksVar.f.l;
        if (kcVar == null || !kcVar.hasStableIds()) {
            ksVar.h();
        }
    }

    public final void V() {
        kh khVar = this.E;
        if (khVar != null) {
            khVar.e();
        }
        kl klVar = this.m;
        if (klVar != null) {
            klVar.aN(this.e);
            this.m.aO(this.e);
        }
        this.e.d();
    }

    public final void W(ki kiVar) {
        boolean z2;
        kl klVar = this.m;
        if (klVar != null) {
            klVar.Q("Cannot remove item decoration during a scroll  or layout");
        }
        this.o.remove(kiVar);
        if (this.o.isEmpty()) {
            if (getOverScrollMode() == 2) {
                z2 = true;
            } else {
                z2 = false;
            }
            setWillNotDraw(z2);
        }
        O();
        requestLayout();
    }

    public final void X(kq kqVar) {
        List list = this.ax;
        if (list != null) {
            list.remove(kqVar);
        }
    }

    public final void Y(int i2, int i3, int[] iArr) {
        int i4;
        int i5;
        ld ldVar;
        af();
        Q();
        Trace.beginSection("RV Scroll");
        J(this.N);
        if (i2 != 0) {
            i4 = this.m.d(i2, this.e, this.N);
        } else {
            i4 = 0;
        }
        if (i3 != 0) {
            i5 = this.m.e(i3, this.e, this.N);
        } else {
            i5 = 0;
        }
        Trace.endSection();
        int a2 = this.g.a();
        for (int i6 = 0; i6 < a2; i6++) {
            View e2 = this.g.e(i6);
            ld i7 = i(e2);
            if (!(i7 == null || (ldVar = i7.mShadowingHolder) == null)) {
                int left = e2.getLeft();
                int top = e2.getTop();
                View view = ldVar.itemView;
                if (left != view.getLeft() || top != view.getTop()) {
                    view.layout(left, top, view.getWidth() + left, view.getHeight() + top);
                }
            }
        }
        R();
        ag(false);
        if (iArr != null) {
            iArr[0] = i4;
            iArr[1] = i5;
        }
    }

    public void Z(int i2) {
        if (!this.v) {
            ai();
            kl klVar = this.m;
            if (klVar == null) {
                Log.e("RecyclerView", "Cannot scroll to position a LayoutManager set. Call setLayoutManager with a non-null argument.");
                return;
            }
            klVar.V(i2);
            awakenScrollBars();
        }
    }

    public final void aa(lf lfVar) {
        this.R = lfVar;
        wj.m(this, lfVar);
    }

    public void ab(kc kcVar) {
        suppressLayout(false);
        kc kcVar2 = this.l;
        if (kcVar2 != null) {
            kcVar2.unregisterAdapterDataObserver(this.ag);
            this.l.onDetachedFromRecyclerView(this);
        }
        V();
        this.W.k();
        kc kcVar3 = this.l;
        this.l = kcVar;
        if (kcVar != null) {
            kcVar.registerAdapterDataObserver(this.ag);
            kcVar.onAttachedToRecyclerView(this);
        }
        kl klVar = this.m;
        if (klVar != null) {
            klVar.bo();
        }
        ks ksVar = this.e;
        kc kcVar4 = this.l;
        ksVar.d();
        ksVar.f(kcVar3, true);
        hpw p2 = ksVar.p();
        if (kcVar3 != null) {
            p2.b--;
        }
        if (p2.b == 0) {
            for (int i2 = 0; i2 < ((SparseArray) p2.c).size(); i2++) {
                kr krVar = (kr) ((SparseArray) p2.c).valueAt(i2);
                ArrayList arrayList = krVar.a;
                int size = arrayList.size();
                for (int i3 = 0; i3 < size; i3++) {
                    wa.t(((ld) arrayList.get(i3)).itemView);
                }
                krVar.a.clear();
            }
        }
        if (kcVar4 != null) {
            p2.b++;
        }
        ksVar.e();
        this.N.f = true;
        U(false);
        requestLayout();
    }

    public final void ac(kl klVar) {
        if (klVar != this.m) {
            ai();
            if (this.m != null) {
                kh khVar = this.E;
                if (khVar != null) {
                    khVar.e();
                }
                this.m.aN(this.e);
                this.m.aO(this.e);
                this.e.d();
                if (this.r) {
                    this.m.br(this);
                }
                this.m.aX((RecyclerView) null);
                this.m = null;
            } else {
                this.e.d();
            }
            il ilVar = this.g;
            ilVar.a.d();
            int size = ilVar.b.size();
            while (true) {
                size--;
                if (size < 0) {
                    break;
                }
                ilVar.e.t((View) ilVar.b.get(size));
                ilVar.b.remove(size);
            }
            AmbientMode.AmbientController ambientController = ilVar.e;
            int q2 = ambientController.q();
            for (int i2 = 0; i2 < q2; i2++) {
                View s2 = ambientController.s(i2);
                ((RecyclerView) ambientController.a).B(s2);
                s2.clearAnimation();
            }
            ((RecyclerView) ambientController.a).removeAllViews();
            this.m = klVar;
            if (klVar != null) {
                if (klVar.t == null) {
                    this.m.aX(this);
                    if (this.r) {
                        this.m.bd();
                    }
                } else {
                    throw new IllegalArgumentException("LayoutManager " + klVar + " is already attached to a RecyclerView:" + klVar.t.m());
                }
            }
            this.e.n();
            requestLayout();
        }
    }

    public final void ad(int i2) {
        if (i2 != this.ao) {
            this.ao = i2;
            if (i2 != 2) {
                aL();
            }
            kl klVar = this.m;
            if (klVar != null) {
                klVar.aM(i2);
            }
            List list = this.ax;
            if (list != null) {
                int size = list.size();
                while (true) {
                    size--;
                    if (size >= 0) {
                        ((kq) this.ax.get(size)).b(i2);
                    } else {
                        return;
                    }
                }
            }
        }
    }

    public final void ae(int i2) {
        if (!this.v) {
            kl klVar = this.m;
            if (klVar == null) {
                Log.e("RecyclerView", "Cannot smooth scroll without a LayoutManager set. Call setLayoutManager with a non-null argument.");
            } else {
                klVar.am(this, i2);
            }
        }
    }

    public final void af() {
        int i2 = this.ai + 1;
        this.ai = i2;
        if (i2 == 1 && !this.v) {
            this.u = false;
        }
    }

    public final void ag(boolean z2) {
        int i2 = this.ai;
        if (i2 <= 0) {
            this.ai = 1;
            i2 = 1;
        }
        if (!z2 && !this.v) {
            this.u = false;
        }
        if (i2 == 1) {
            if (z2 && this.u && !this.v && this.m != null && this.l != null) {
                C();
            }
            if (!this.v) {
                this.u = false;
            }
        }
        this.ai--;
    }

    public final void ah(int i2) {
        az().b(i2);
    }

    public final void ai() {
        ad(0);
        aL();
    }

    public final boolean ak(int i2, int i3, int[] iArr, int[] iArr2, int i4) {
        return az().e(i2, i3, iArr, iArr2, i4);
    }

    public final boolean al(int i2, int i3) {
        return am(i2, i3, this.G, this.H);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:97:0x0153, code lost:
        r15 = r5 - 1;
     */
    /* JADX WARNING: Removed duplicated region for block: B:42:0x0087  */
    /* JADX WARNING: Removed duplicated region for block: B:61:0x00cb  */
    /* JADX WARNING: Removed duplicated region for block: B:66:0x00ed  */
    /* JADX WARNING: Removed duplicated region for block: B:74:0x00ff  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean am(int r18, int r19, int r20, int r21) {
        /*
            r17 = this;
            r0 = r17
            r1 = r20
            r2 = r21
            kl r3 = r0.m
            r4 = 0
            if (r3 != 0) goto L_0x0013
            java.lang.String r1 = "RecyclerView"
            java.lang.String r2 = "Cannot fling without a LayoutManager set. Call setLayoutManager with a non-null argument."
            android.util.Log.e(r1, r2)
            return r4
        L_0x0013:
            boolean r5 = r0.v
            if (r5 == 0) goto L_0x0018
            return r4
        L_0x0018:
            boolean r5 = r3.Y()
            boolean r3 = r3.Z()
            if (r5 == 0) goto L_0x002c
            int r6 = java.lang.Math.abs(r18)
            if (r6 >= r1) goto L_0x0029
            goto L_0x002c
        L_0x0029:
            r6 = r18
            goto L_0x002d
        L_0x002c:
            r6 = r4
        L_0x002d:
            if (r3 == 0) goto L_0x0039
            int r7 = java.lang.Math.abs(r19)
            if (r7 >= r1) goto L_0x0036
            goto L_0x0039
        L_0x0036:
            r1 = r19
            goto L_0x003a
        L_0x0039:
            r1 = r4
        L_0x003a:
            if (r6 != 0) goto L_0x0041
            if (r1 == 0) goto L_0x0040
            r6 = r4
            goto L_0x0041
        L_0x0040:
            return r4
        L_0x0041:
            r7 = 0
            if (r6 == 0) goto L_0x0084
            android.widget.EdgeEffect r8 = r0.A
            if (r8 == 0) goto L_0x0066
            float r8 = defpackage.xm.b(r8)
            int r8 = (r8 > r7 ? 1 : (r8 == r7 ? 0 : -1))
            if (r8 == 0) goto L_0x0066
            android.widget.EdgeEffect r8 = r0.A
            int r9 = -r6
            int r10 = r17.getWidth()
            boolean r8 = r0.aO(r8, r9, r10)
            if (r8 == 0) goto L_0x0063
            android.widget.EdgeEffect r6 = r0.A
            r6.onAbsorb(r9)
        L_0x0062:
            r6 = r4
        L_0x0063:
            r8 = r6
            r6 = r4
            goto L_0x0085
        L_0x0066:
            android.widget.EdgeEffect r8 = r0.C
            if (r8 == 0) goto L_0x0084
            float r8 = defpackage.xm.b(r8)
            int r8 = (r8 > r7 ? 1 : (r8 == r7 ? 0 : -1))
            if (r8 == 0) goto L_0x0084
            android.widget.EdgeEffect r8 = r0.C
            int r9 = r17.getWidth()
            boolean r8 = r0.aO(r8, r6, r9)
            if (r8 == 0) goto L_0x0063
            android.widget.EdgeEffect r8 = r0.C
            r8.onAbsorb(r6)
            goto L_0x0062
        L_0x0084:
            r8 = r4
        L_0x0085:
            if (r1 == 0) goto L_0x00c6
            android.widget.EdgeEffect r9 = r0.B
            if (r9 == 0) goto L_0x00a8
            float r9 = defpackage.xm.b(r9)
            int r9 = (r9 > r7 ? 1 : (r9 == r7 ? 0 : -1))
            if (r9 == 0) goto L_0x00a8
            android.widget.EdgeEffect r9 = r0.B
            int r10 = -r1
            int r11 = r17.getHeight()
            boolean r9 = r0.aO(r9, r10, r11)
            if (r9 == 0) goto L_0x00a6
            android.widget.EdgeEffect r1 = r0.B
            r1.onAbsorb(r10)
        L_0x00a5:
            r1 = r4
        L_0x00a6:
            r9 = r4
            goto L_0x00c8
        L_0x00a8:
            android.widget.EdgeEffect r9 = r0.D
            if (r9 == 0) goto L_0x00c6
            float r9 = defpackage.xm.b(r9)
            int r9 = (r9 > r7 ? 1 : (r9 == r7 ? 0 : -1))
            if (r9 == 0) goto L_0x00c6
            android.widget.EdgeEffect r9 = r0.D
            int r10 = r17.getHeight()
            boolean r9 = r0.aO(r9, r1, r10)
            if (r9 == 0) goto L_0x00a6
            android.widget.EdgeEffect r9 = r0.D
            r9.onAbsorb(r1)
            goto L_0x00a5
        L_0x00c6:
            r9 = r1
            r1 = r4
        L_0x00c8:
            r10 = 1
            if (r8 != 0) goto L_0x00d2
            if (r1 == 0) goto L_0x00cf
            r8 = r4
            goto L_0x00d2
        L_0x00cf:
            r1 = r4
            r8 = r1
            goto L_0x00eb
        L_0x00d2:
            int r11 = -r2
            int r8 = java.lang.Math.min(r8, r2)
            int r8 = java.lang.Math.max(r11, r8)
            int r1 = java.lang.Math.min(r1, r2)
            int r1 = java.lang.Math.max(r11, r1)
            r0.aK(r10)
            lc r11 = r0.K
            r11.a(r8, r1)
        L_0x00eb:
            if (r6 != 0) goto L_0x00f7
            if (r9 != 0) goto L_0x00f6
            if (r8 != 0) goto L_0x00f5
            if (r1 == 0) goto L_0x00f4
            goto L_0x00f5
        L_0x00f4:
            return r4
        L_0x00f5:
            return r10
        L_0x00f6:
            r6 = r4
        L_0x00f7:
            float r1 = (float) r6
            float r8 = (float) r9
            boolean r11 = r0.dispatchNestedPreFling(r1, r8)
            if (r11 != 0) goto L_0x01cb
            if (r5 != 0) goto L_0x0106
            if (r3 == 0) goto L_0x0104
            goto L_0x0106
        L_0x0104:
            r3 = r4
            goto L_0x0107
        L_0x0106:
            r3 = r10
        L_0x0107:
            r0.dispatchNestedFling(r1, r8, r3)
            ko r1 = r0.F
            if (r1 == 0) goto L_0x01ae
            android.support.v7.widget.RecyclerView r5 = r1.a
            kl r8 = r5.m
            if (r8 != 0) goto L_0x0116
            goto L_0x01ae
        L_0x0116:
            kc r5 = r5.g()
            if (r5 == 0) goto L_0x01ae
            android.support.v7.widget.RecyclerView r5 = r1.a
            int r5 = r5.G
            int r11 = java.lang.Math.abs(r9)
            if (r11 > r5) goto L_0x012c
            int r11 = java.lang.Math.abs(r6)
            if (r11 <= r5) goto L_0x01ae
        L_0x012c:
            boolean r5 = r8 instanceof defpackage.ky
            if (r5 == 0) goto L_0x01ae
            r5 = r1
            blr r5 = (defpackage.blr) r5
            android.content.Context r5 = r5.d
            blq r11 = new blq
            r11.<init>(r5)
            int r5 = r8.aq()
            r12 = -1
            if (r5 != 0) goto L_0x0145
        L_0x0141:
            r1 = r12
            r15 = r1
            goto L_0x01a3
        L_0x0145:
            android.view.View r13 = r1.d(r8)
            if (r13 != 0) goto L_0x014c
            goto L_0x0141
        L_0x014c:
            int r13 = defpackage.kl.bk(r13)
            if (r13 != r12) goto L_0x0153
            goto L_0x0141
        L_0x0153:
            r14 = r8
            ky r14 = (defpackage.ky) r14
            int r15 = r5 + -1
            android.graphics.PointF r14 = r14.M(r15)
            if (r14 != 0) goto L_0x015f
            goto L_0x0141
        L_0x015f:
            boolean r16 = r8.Y()
            if (r16 == 0) goto L_0x0175
            ka r12 = r1.f(r8)
            int r12 = r1.c(r8, r12, r6, r4)
            float r10 = r14.x
            int r10 = (r10 > r7 ? 1 : (r10 == r7 ? 0 : -1))
            if (r10 >= 0) goto L_0x0176
            int r12 = -r12
            goto L_0x0176
        L_0x0175:
            r12 = r4
        L_0x0176:
            boolean r10 = r8.Z()
            if (r10 == 0) goto L_0x018c
            ka r10 = r1.g(r8)
            int r1 = r1.c(r8, r10, r4, r9)
            float r10 = r14.y
            int r7 = (r10 > r7 ? 1 : (r10 == r7 ? 0 : -1))
            if (r7 >= 0) goto L_0x018d
            int r1 = -r1
            goto L_0x018d
        L_0x018c:
            r1 = r4
        L_0x018d:
            boolean r7 = r8.Z()
            r10 = 1
            if (r10 != r7) goto L_0x0195
            r12 = r1
        L_0x0195:
            if (r12 != 0) goto L_0x019a
            r1 = -1
            r15 = -1
            goto L_0x01a3
        L_0x019a:
            int r13 = r13 + r12
            if (r13 >= 0) goto L_0x019e
            r13 = r4
        L_0x019e:
            if (r13 < r5) goto L_0x01a1
            goto L_0x01a2
        L_0x01a1:
            r15 = r13
        L_0x01a2:
            r1 = -1
        L_0x01a3:
            if (r15 == r1) goto L_0x01ac
            r11.a = r15
            r8.aY(r11)
            r1 = 1
            return r1
        L_0x01ac:
            r1 = 1
            goto L_0x01af
        L_0x01ae:
            r1 = r10
        L_0x01af:
            if (r3 == 0) goto L_0x01cb
            int r3 = -r2
            r0.aK(r1)
            int r4 = java.lang.Math.min(r6, r2)
            int r4 = java.lang.Math.max(r3, r4)
            int r2 = java.lang.Math.min(r9, r2)
            int r2 = java.lang.Math.max(r3, r2)
            lc r3 = r0.K
            r3.a(r4, r2)
            return r1
        L_0x01cb:
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: android.support.v7.widget.RecyclerView.am(int, int, int, int):boolean");
    }

    public final boolean an() {
        if (!this.t || this.y || this.W.m()) {
            return true;
        }
        return false;
    }

    public final boolean ao() {
        AccessibilityManager accessibilityManager = this.al;
        if (accessibilityManager == null || !accessibilityManager.isEnabled()) {
            return false;
        }
        return true;
    }

    public final boolean ap() {
        if (this.am > 0) {
            return true;
        }
        return false;
    }

    /* access modifiers changed from: package-private */
    /* JADX WARNING: Code restructure failed: missing block: B:35:0x0113, code lost:
        if (r6 == 0) goto L_0x0118;
     */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x00db  */
    /* JADX WARNING: Removed duplicated region for block: B:29:0x00f1  */
    /* JADX WARNING: Removed duplicated region for block: B:50:0x013e  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean aq(int r19, int r20, android.view.MotionEvent r21, int r22) {
        /*
            r18 = this;
            r8 = r18
            r9 = r19
            r10 = r20
            r11 = r21
            r18.z()
            kc r0 = r8.l
            r12 = 1
            r13 = 0
            if (r0 == 0) goto L_0x002b
            int[] r0 = r8.S
            r0[r13] = r13
            r0[r12] = r13
            r8.Y(r9, r10, r0)
            int[] r0 = r8.S
            r1 = r0[r13]
            r0 = r0[r12]
            int r2 = r9 - r1
            int r3 = r10 - r0
            r14 = r0
            r15 = r1
            r16 = r2
            r17 = r3
            goto L_0x0031
        L_0x002b:
            r14 = r13
            r15 = r14
            r16 = r15
            r17 = r16
        L_0x0031:
            java.util.ArrayList r0 = r8.o
            boolean r0 = r0.isEmpty()
            if (r0 != 0) goto L_0x003c
            r18.invalidate()
        L_0x003c:
            int[] r7 = r8.S
            r7[r13] = r13
            r7[r12] = r13
            int[] r5 = r8.aA
            r0 = r18
            r1 = r15
            r2 = r14
            r3 = r16
            r4 = r17
            r6 = r22
            r0.D(r1, r2, r3, r4, r5, r6, r7)
            int[] r0 = r8.S
            r1 = r0[r13]
            int r2 = r16 - r1
            r0 = r0[r12]
            int r3 = r17 - r0
            if (r1 != 0) goto L_0x0062
            if (r0 == 0) goto L_0x0060
            goto L_0x0062
        L_0x0060:
            r0 = r13
            goto L_0x0063
        L_0x0062:
            r0 = r12
        L_0x0063:
            int r1 = r8.at
            int[] r4 = r8.aA
            r5 = r4[r13]
            int r1 = r1 - r5
            r8.at = r1
            int r1 = r8.au
            r4 = r4[r12]
            int r1 = r1 - r4
            r8.au = r1
            int[] r1 = r8.aB
            r6 = r1[r13]
            int r6 = r6 + r5
            r1[r13] = r6
            r5 = r1[r12]
            int r5 = r5 + r4
            r1[r12] = r5
            int r1 = r18.getOverScrollMode()
            r4 = 2
            if (r1 == r4) goto L_0x012c
            if (r11 == 0) goto L_0x0129
            r1 = 8194(0x2002, float:1.1482E-41)
            boolean r1 = defpackage.vy.e(r11, r1)
            if (r1 != 0) goto L_0x0129
            float r1 = r21.getX()
            float r2 = (float) r2
            float r4 = r21.getY()
            float r3 = (float) r3
            r5 = 0
            int r6 = (r2 > r5 ? 1 : (r2 == r5 ? 0 : -1))
            r7 = 1065353216(0x3f800000, float:1.0)
            if (r6 >= 0) goto L_0x00bb
            r18.G()
            android.widget.EdgeEffect r6 = r8.A
            float r12 = -r2
            int r13 = r18.getWidth()
            float r13 = (float) r13
            int r5 = r18.getHeight()
            float r5 = (float) r5
            float r4 = r4 / r5
            float r4 = r7 - r4
            float r12 = r12 / r13
            defpackage.xm.c(r6, r12, r4)
        L_0x00b8:
            r4 = 1
        L_0x00b9:
            r5 = 0
            goto L_0x00d7
        L_0x00bb:
            int r6 = (r2 > r5 ? 1 : (r2 == r5 ? 0 : -1))
            if (r6 <= 0) goto L_0x00d5
            r18.H()
            android.widget.EdgeEffect r5 = r8.C
            int r6 = r18.getWidth()
            float r6 = (float) r6
            float r6 = r2 / r6
            int r12 = r18.getHeight()
            float r12 = (float) r12
            float r4 = r4 / r12
            defpackage.xm.c(r5, r6, r4)
            goto L_0x00b8
        L_0x00d5:
            r4 = 0
            goto L_0x00b9
        L_0x00d7:
            int r6 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            if (r6 >= 0) goto L_0x00f1
            r18.I()
            android.widget.EdgeEffect r2 = r8.B
            float r3 = -r3
            int r4 = r18.getHeight()
            float r4 = (float) r4
            int r5 = r18.getWidth()
            float r5 = (float) r5
            float r1 = r1 / r5
            float r3 = r3 / r4
            defpackage.xm.c(r2, r3, r1)
            goto L_0x0115
        L_0x00f1:
            r5 = 0
            int r6 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            if (r6 <= 0) goto L_0x010c
            r18.F()
            android.widget.EdgeEffect r2 = r8.D
            int r4 = r18.getHeight()
            float r4 = (float) r4
            float r3 = r3 / r4
            int r4 = r18.getWidth()
            float r4 = (float) r4
            float r1 = r1 / r4
            float r7 = r7 - r1
            defpackage.xm.c(r2, r3, r7)
            goto L_0x0115
        L_0x010c:
            if (r4 != 0) goto L_0x0115
            r1 = 0
            int r1 = (r2 > r1 ? 1 : (r2 == r1 ? 0 : -1))
            if (r1 != 0) goto L_0x0115
            if (r6 == 0) goto L_0x0118
        L_0x0115:
            r18.postInvalidateOnAnimation()
        L_0x0118:
            int r1 = android.os.Build.VERSION.SDK_INT
            r2 = 31
            if (r1 < r2) goto L_0x0129
            r1 = 4194304(0x400000, float:5.877472E-39)
            boolean r1 = defpackage.vy.e(r11, r1)
            if (r1 == 0) goto L_0x0129
            r18.aG()
        L_0x0129:
            r18.y(r19, r20)
        L_0x012c:
            if (r15 != 0) goto L_0x0135
            if (r14 == 0) goto L_0x0132
            r15 = 0
            goto L_0x0135
        L_0x0132:
            r14 = 0
            r15 = 0
            goto L_0x0138
        L_0x0135:
            r8.E(r15, r14)
        L_0x0138:
            boolean r1 = r18.awakenScrollBars()
            if (r1 != 0) goto L_0x0141
            r18.invalidate()
        L_0x0141:
            if (r0 != 0) goto L_0x014a
            if (r15 != 0) goto L_0x014a
            if (r14 == 0) goto L_0x0148
            goto L_0x014a
        L_0x0148:
            r0 = 0
            return r0
        L_0x014a:
            r0 = 1
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: android.support.v7.widget.RecyclerView.aq(int, int, android.view.MotionEvent, int):boolean");
    }

    public final void at(ld ldVar, int i2) {
        if (ap()) {
            ldVar.mPendingAccessibilityState = i2;
            this.T.add(ldVar);
            return;
        }
        ldVar.itemView.setImportantForAccessibility(i2);
    }

    public final void au(int i2, int i3) {
        av(i2, i3, false);
    }

    public final void av(int i2, int i3, boolean z2) {
        kl klVar = this.m;
        if (klVar == null) {
            Log.e("RecyclerView", "Cannot smooth scroll without a LayoutManager set. Call setLayoutManager with a non-null argument.");
        } else if (!this.v) {
            int i4 = 0;
            if (true != klVar.Y()) {
                i2 = 0;
            }
            if (true != klVar.Z()) {
                i3 = 0;
            }
            if (i2 == 0) {
                if (i3 != 0) {
                    i2 = 0;
                } else {
                    return;
                }
            }
            if (z2) {
                if (i2 != 0) {
                    i4 = 1;
                }
                if (i3 != 0) {
                    i4 |= 2;
                }
                aw(i4, 1);
            }
            this.K.c(i2, i3, Integer.MIN_VALUE, (Interpolator) null);
        }
    }

    public final void aw(int i2, int i3) {
        az().i(i2, i3);
    }

    public final void ax(ld ldVar, bvh bvh) {
        ldVar.setFlags(0, 8192);
        if (this.N.h && ldVar.isUpdated() && !ldVar.isRemoved() && !ldVar.shouldIgnore()) {
            this.aa.o(e(ldVar), ldVar);
        }
        this.aa.w(ldVar, bvh);
    }

    public final int b(ld ldVar) {
        if (ldVar.hasAnyOfTheFlags(524) || !ldVar.isBound()) {
            return -1;
        }
        dxr dxr = this.W;
        int i2 = ldVar.mPosition;
        int size = ((ArrayList) dxr.d).size();
        for (int i3 = 0; i3 < size; i3++) {
            hc hcVar = (hc) ((ArrayList) dxr.d).get(i3);
            int i4 = hcVar.a;
            if (i4 != 1) {
                if (i4 == 2) {
                    int i5 = hcVar.b;
                    if (i5 <= i2) {
                        int i6 = hcVar.d;
                        if (i5 + i6 > i2) {
                            return -1;
                        }
                        i2 -= i6;
                    } else {
                        continue;
                    }
                } else if (i4 == 8) {
                    int i7 = hcVar.b;
                    if (i7 == i2) {
                        i2 = hcVar.d;
                    } else {
                        if (i7 < i2) {
                            i2--;
                        }
                        if (hcVar.d <= i2) {
                            i2++;
                        }
                    }
                }
            } else if (hcVar.b <= i2) {
                i2 += hcVar.d;
            }
        }
        return i2;
    }

    public final int c(View view) {
        ld j2 = j(view);
        if (j2 != null) {
            return j2.getAbsoluteAdapterPosition();
        }
        return -1;
    }

    public final boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        if (!(layoutParams instanceof km) || !this.m.t((km) layoutParams)) {
            return false;
        }
        return true;
    }

    public final int computeHorizontalScrollExtent() {
        kl klVar = this.m;
        if (klVar != null && klVar.Y()) {
            return klVar.C(this.N);
        }
        return 0;
    }

    public final int computeHorizontalScrollOffset() {
        kl klVar = this.m;
        if (klVar != null && klVar.Y()) {
            return klVar.D(this.N);
        }
        return 0;
    }

    public final int computeHorizontalScrollRange() {
        kl klVar = this.m;
        if (klVar != null && klVar.Y()) {
            return klVar.E(this.N);
        }
        return 0;
    }

    public final int computeVerticalScrollExtent() {
        kl klVar = this.m;
        if (klVar != null && klVar.Z()) {
            return klVar.F(this.N);
        }
        return 0;
    }

    public final int computeVerticalScrollOffset() {
        kl klVar = this.m;
        if (klVar != null && klVar.Z()) {
            return klVar.G(this.N);
        }
        return 0;
    }

    public final int computeVerticalScrollRange() {
        kl klVar = this.m;
        if (klVar != null && klVar.Z()) {
            return klVar.H(this.N);
        }
        return 0;
    }

    public final int d(View view) {
        ld j2 = j(view);
        if (j2 != null) {
            return j2.getLayoutPosition();
        }
        return -1;
    }

    public final boolean dispatchKeyEvent(KeyEvent keyEvent) {
        if (super.dispatchKeyEvent(keyEvent)) {
            return true;
        }
        kl klVar = this.m;
        int i2 = 0;
        if (klVar == null) {
            return false;
        }
        if (klVar.Z()) {
            int keyCode = keyEvent.getKeyCode();
            if (keyCode == 92 || keyCode == 93) {
                int measuredHeight = getMeasuredHeight();
                if (keyCode == 93) {
                    au(0, measuredHeight);
                } else {
                    au(0, -measuredHeight);
                }
                return true;
            } else if (keyCode == 122 || keyCode == 123) {
                boolean ac2 = klVar.ac();
                if (keyCode == 122) {
                    if (ac2) {
                        i2 = g().getItemCount();
                    }
                } else if (!ac2) {
                    i2 = g().getItemCount();
                }
                ae(i2);
                return true;
            }
        } else if (klVar.Y()) {
            int keyCode2 = keyEvent.getKeyCode();
            if (keyCode2 == 92 || keyCode2 == 93) {
                int measuredWidth = getMeasuredWidth();
                if (keyCode2 == 93) {
                    au(measuredWidth, 0);
                } else {
                    au(-measuredWidth, 0);
                }
                return true;
            } else if (keyCode2 == 122 || keyCode2 == 123) {
                boolean ac3 = klVar.ac();
                if (keyCode2 == 122) {
                    if (ac3) {
                        i2 = g().getItemCount();
                    }
                } else if (!ac3) {
                    i2 = g().getItemCount();
                }
                ae(i2);
                return true;
            }
        }
        return false;
    }

    public final boolean dispatchNestedFling(float f2, float f3, boolean z2) {
        return az().c(f2, f3, z2);
    }

    public final boolean dispatchNestedPreFling(float f2, float f3) {
        return az().d(f2, f3);
    }

    public final boolean dispatchNestedPreScroll(int i2, int i3, int[] iArr, int[] iArr2) {
        return az().e(i2, i3, iArr, iArr2, 0);
    }

    public final boolean dispatchNestedScroll(int i2, int i3, int i4, int i5, int[] iArr) {
        return az().f(i2, i3, i4, i5, iArr);
    }

    public final boolean dispatchPopulateAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        onPopulateAccessibilityEvent(accessibilityEvent);
        return true;
    }

    /* access modifiers changed from: protected */
    public final void dispatchRestoreInstanceState(SparseArray sparseArray) {
        dispatchThawSelfOnly(sparseArray);
    }

    /* access modifiers changed from: protected */
    public final void dispatchSaveInstanceState(SparseArray sparseArray) {
        dispatchFreezeSelfOnly(sparseArray);
    }

    public final void draw(Canvas canvas) {
        boolean z2;
        int i2;
        boolean z3;
        boolean z4;
        int i3;
        super.draw(canvas);
        int size = this.o.size();
        boolean z5 = false;
        for (int i4 = 0; i4 < size; i4++) {
            ((ki) this.o.get(i4)).h(canvas, this);
        }
        EdgeEffect edgeEffect = this.A;
        if (edgeEffect == null || edgeEffect.isFinished()) {
            z2 = false;
        } else {
            int save = canvas.save();
            if (this.h) {
                i3 = getPaddingBottom();
            } else {
                i3 = 0;
            }
            canvas.rotate(270.0f);
            canvas.translate((float) ((-getHeight()) + i3), 0.0f);
            EdgeEffect edgeEffect2 = this.A;
            if (edgeEffect2 == null || !edgeEffect2.draw(canvas)) {
                z2 = false;
            } else {
                z2 = true;
            }
            canvas.restoreToCount(save);
        }
        EdgeEffect edgeEffect3 = this.B;
        if (edgeEffect3 != null && !edgeEffect3.isFinished()) {
            int save2 = canvas.save();
            if (this.h) {
                canvas.translate((float) getPaddingLeft(), (float) getPaddingTop());
            }
            EdgeEffect edgeEffect4 = this.B;
            if (edgeEffect4 == null || !edgeEffect4.draw(canvas)) {
                z4 = false;
            } else {
                z4 = true;
            }
            z2 |= z4;
            canvas.restoreToCount(save2);
        }
        EdgeEffect edgeEffect5 = this.C;
        if (edgeEffect5 != null && !edgeEffect5.isFinished()) {
            int save3 = canvas.save();
            int width = getWidth();
            if (this.h) {
                i2 = getPaddingTop();
            } else {
                i2 = 0;
            }
            canvas.rotate(90.0f);
            canvas.translate((float) i2, (float) (-width));
            EdgeEffect edgeEffect6 = this.C;
            if (edgeEffect6 == null || !edgeEffect6.draw(canvas)) {
                z3 = false;
            } else {
                z3 = true;
            }
            z2 |= z3;
            canvas.restoreToCount(save3);
        }
        EdgeEffect edgeEffect7 = this.D;
        if (edgeEffect7 != null && !edgeEffect7.isFinished()) {
            int save4 = canvas.save();
            canvas.rotate(180.0f);
            if (this.h) {
                canvas.translate((float) ((-getWidth()) + getPaddingRight()), (float) ((-getHeight()) + getPaddingBottom()));
            } else {
                canvas.translate((float) (-getWidth()), (float) (-getHeight()));
            }
            EdgeEffect edgeEffect8 = this.D;
            if (edgeEffect8 != null && edgeEffect8.draw(canvas)) {
                z5 = true;
            }
            z2 |= z5;
            canvas.restoreToCount(save4);
        }
        if (z2 || (this.E != null && this.o.size() > 0 && this.E.h())) {
            postInvalidateOnAnimation();
        }
    }

    public final boolean drawChild(Canvas canvas, View view, long j2) {
        return super.drawChild(canvas, view, j2);
    }

    /* access modifiers changed from: package-private */
    public final long e(ld ldVar) {
        if (this.l.hasStableIds()) {
            return ldVar.getItemId();
        }
        return (long) ldVar.mPosition;
    }

    public final Rect f(View view) {
        km kmVar = (km) view.getLayoutParams();
        if (!kmVar.e) {
            return kmVar.d;
        }
        if (this.N.g && (kmVar.b() || kmVar.c.isInvalid())) {
            return kmVar.d;
        }
        Rect rect = kmVar.d;
        rect.set(0, 0, 0, 0);
        int size = this.o.size();
        for (int i2 = 0; i2 < size; i2++) {
            this.j.set(0, 0, 0, 0);
            ((ki) this.o.get(i2)).a(this.j, view, this, this.N);
            rect.left += this.j.left;
            rect.top += this.j.top;
            rect.right += this.j.right;
            rect.bottom += this.j.bottom;
        }
        kmVar.e = false;
        return rect;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:106:0x0190, code lost:
        if (r2 > 0) goto L_0x01c6;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:109:0x01ae, code lost:
        if (r10 > 0) goto L_0x01c6;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:110:0x01b1, code lost:
        if (r2 < 0) goto L_0x01c6;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:111:0x01b4, code lost:
        if (r10 < 0) goto L_0x01c6;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:115:0x01bc, code lost:
        if ((r10 * r3) <= 0) goto L_0x01c7;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:119:0x01c4, code lost:
        if ((r10 * r3) >= 0) goto L_0x01c7;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:21:0x003d, code lost:
        if (r3.findNextFocus(r13, r14, r0) != null) goto L_0x003f;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:36:0x0061, code lost:
        if (r3.findNextFocus(r13, r14, r0) == null) goto L_0x0063;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:37:0x0063, code lost:
        z();
     */
    /* JADX WARNING: Code restructure failed: missing block: B:38:0x006a, code lost:
        if (l(r14) != null) goto L_0x006d;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:39:0x006c, code lost:
        return null;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:40:0x006d, code lost:
        af();
        r13.m.j(r14, r15, r13.e, r13.N);
        ag(false);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:74:0x0116, code lost:
        if (r13.j.right <= r13.ah.left) goto L_0x0118;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:81:0x0136, code lost:
        if (r13.j.left >= r13.ah.right) goto L_0x0138;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:89:0x0157, code lost:
        if (r13.j.bottom <= r13.ah.top) goto L_0x0159;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:96:0x0177, code lost:
        if (r13.j.top >= r13.ah.bottom) goto L_0x0179;
     */
    /* JADX WARNING: Removed duplicated region for block: B:101:0x0186  */
    /* JADX WARNING: Removed duplicated region for block: B:116:0x01bf  */
    /* JADX WARNING: Removed duplicated region for block: B:53:0x00ad  */
    /* JADX WARNING: Removed duplicated region for block: B:59:0x00bc A[ADDED_TO_REGION] */
    /* JADX WARNING: Removed duplicated region for block: B:88:0x014f  */
    /* JADX WARNING: Removed duplicated region for block: B:92:0x0163  */
    /* JADX WARNING: Removed duplicated region for block: B:95:0x016f  */
    /* JADX WARNING: Removed duplicated region for block: B:99:0x0183  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final android.view.View focusSearch(android.view.View r14, int r15) {
        /*
            r13 = this;
            kc r0 = r13.l
            r1 = 1
            r2 = 0
            if (r0 == 0) goto L_0x0016
            kl r0 = r13.m
            if (r0 == 0) goto L_0x0016
            boolean r0 = r13.ap()
            if (r0 != 0) goto L_0x0016
            boolean r0 = r13.v
            if (r0 != 0) goto L_0x0016
            r0 = r1
            goto L_0x0017
        L_0x0016:
            r0 = r2
        L_0x0017:
            android.view.FocusFinder r3 = android.view.FocusFinder.getInstance()
            r4 = 17
            r5 = 66
            r6 = 130(0x82, float:1.82E-43)
            r7 = 33
            r8 = 0
            r9 = 2
            if (r0 == 0) goto L_0x0081
            if (r15 == r9) goto L_0x002c
            if (r15 != r1) goto L_0x0081
            r15 = r1
        L_0x002c:
            kl r0 = r13.m
            boolean r0 = r0.Z()
            if (r0 == 0) goto L_0x003f
            if (r15 != r9) goto L_0x0038
            r0 = r6
            goto L_0x0039
        L_0x0038:
            r0 = r7
        L_0x0039:
            android.view.View r0 = r3.findNextFocus(r13, r14, r0)
            if (r0 == 0) goto L_0x0063
        L_0x003f:
            kl r0 = r13.m
            boolean r0 = r0.Y()
            if (r0 == 0) goto L_0x007c
            kl r0 = r13.m
            int r0 = r0.ar()
            if (r0 != r1) goto L_0x0051
            r0 = r1
            goto L_0x0052
        L_0x0051:
            r0 = r2
        L_0x0052:
            if (r15 != r9) goto L_0x0056
            r10 = r1
            goto L_0x0057
        L_0x0056:
            r10 = r2
        L_0x0057:
            r0 = r0 ^ r10
            if (r1 == r0) goto L_0x005c
            r0 = r4
            goto L_0x005d
        L_0x005c:
            r0 = r5
        L_0x005d:
            android.view.View r0 = r3.findNextFocus(r13, r14, r0)
            if (r0 != 0) goto L_0x007c
        L_0x0063:
            r13.z()
            android.view.View r0 = r13.l(r14)
            if (r0 != 0) goto L_0x006d
            return r8
        L_0x006d:
            r13.af()
            kl r0 = r13.m
            ks r10 = r13.e
            la r11 = r13.N
            r0.j(r14, r15, r10, r11)
            r13.ag(r2)
        L_0x007c:
            android.view.View r0 = r3.findNextFocus(r13, r14, r15)
            goto L_0x00a5
        L_0x0081:
            android.view.View r3 = r3.findNextFocus(r13, r14, r15)
            if (r3 != 0) goto L_0x00a4
            if (r0 == 0) goto L_0x00a4
            r13.z()
            android.view.View r0 = r13.l(r14)
            if (r0 != 0) goto L_0x0093
            return r8
        L_0x0093:
            r13.af()
            kl r0 = r13.m
            ks r3 = r13.e
            la r10 = r13.N
            android.view.View r0 = r0.j(r14, r15, r3, r10)
            r13.ag(r2)
            goto L_0x00a5
        L_0x00a4:
            r0 = r3
        L_0x00a5:
            if (r0 == 0) goto L_0x00bc
            boolean r3 = r0.hasFocusable()
            if (r3 != 0) goto L_0x00bc
            android.view.View r1 = r13.getFocusedChild()
            if (r1 != 0) goto L_0x00b8
            android.view.View r14 = super.focusSearch(r14, r15)
            return r14
        L_0x00b8:
            r13.aH(r0, r8)
            return r14
        L_0x00bc:
            if (r0 == 0) goto L_0x01c7
            if (r0 == r13) goto L_0x01c7
            if (r0 != r14) goto L_0x00c4
            goto L_0x01c7
        L_0x00c4:
            android.view.View r3 = r13.l(r0)
            if (r3 == 0) goto L_0x01c7
            if (r14 != 0) goto L_0x00ce
            goto L_0x01c6
        L_0x00ce:
            android.view.View r3 = r13.l(r14)
            if (r3 == 0) goto L_0x01c6
            android.graphics.Rect r3 = r13.j
            int r8 = r14.getWidth()
            int r10 = r14.getHeight()
            r3.set(r2, r2, r8, r10)
            android.graphics.Rect r3 = r13.ah
            int r8 = r0.getWidth()
            int r10 = r0.getHeight()
            r3.set(r2, r2, r8, r10)
            android.graphics.Rect r3 = r13.j
            r13.offsetDescendantRectToMyCoords(r14, r3)
            android.graphics.Rect r3 = r13.ah
            r13.offsetDescendantRectToMyCoords(r0, r3)
            kl r3 = r13.m
            int r3 = r3.ar()
            r8 = -1
            if (r3 != r1) goto L_0x0103
            r3 = r8
            goto L_0x0104
        L_0x0103:
            r3 = r1
        L_0x0104:
            android.graphics.Rect r10 = r13.j
            android.graphics.Rect r11 = r13.ah
            int r10 = r10.left
            int r11 = r11.left
            if (r10 < r11) goto L_0x0118
            android.graphics.Rect r10 = r13.j
            android.graphics.Rect r11 = r13.ah
            int r10 = r10.right
            int r11 = r11.left
            if (r10 > r11) goto L_0x0124
        L_0x0118:
            android.graphics.Rect r10 = r13.j
            android.graphics.Rect r11 = r13.ah
            int r10 = r10.right
            int r11 = r11.right
            if (r10 >= r11) goto L_0x0124
            r10 = r1
            goto L_0x0145
        L_0x0124:
            android.graphics.Rect r10 = r13.j
            android.graphics.Rect r11 = r13.ah
            int r10 = r10.right
            int r11 = r11.right
            if (r10 > r11) goto L_0x0138
            android.graphics.Rect r10 = r13.j
            android.graphics.Rect r11 = r13.ah
            int r10 = r10.left
            int r11 = r11.right
            if (r10 < r11) goto L_0x0144
        L_0x0138:
            android.graphics.Rect r10 = r13.j
            android.graphics.Rect r11 = r13.ah
            int r10 = r10.left
            int r11 = r11.left
            if (r10 <= r11) goto L_0x0144
            r10 = r8
            goto L_0x0145
        L_0x0144:
            r10 = r2
        L_0x0145:
            android.graphics.Rect r11 = r13.j
            android.graphics.Rect r12 = r13.ah
            int r11 = r11.top
            int r12 = r12.top
            if (r11 < r12) goto L_0x0159
            android.graphics.Rect r11 = r13.j
            android.graphics.Rect r12 = r13.ah
            int r11 = r11.bottom
            int r12 = r12.top
            if (r11 > r12) goto L_0x0165
        L_0x0159:
            android.graphics.Rect r11 = r13.j
            android.graphics.Rect r12 = r13.ah
            int r11 = r11.bottom
            int r12 = r12.bottom
            if (r11 >= r12) goto L_0x0165
            r2 = r1
            goto L_0x0184
        L_0x0165:
            android.graphics.Rect r11 = r13.j
            android.graphics.Rect r12 = r13.ah
            int r11 = r11.bottom
            int r12 = r12.bottom
            if (r11 > r12) goto L_0x0179
            android.graphics.Rect r11 = r13.j
            android.graphics.Rect r12 = r13.ah
            int r11 = r11.top
            int r12 = r12.bottom
            if (r11 < r12) goto L_0x0184
        L_0x0179:
            android.graphics.Rect r11 = r13.j
            android.graphics.Rect r12 = r13.ah
            int r11 = r11.top
            int r12 = r12.top
            if (r11 <= r12) goto L_0x0184
            r2 = r8
        L_0x0184:
            if (r15 == r1) goto L_0x01bf
            if (r15 == r9) goto L_0x01b7
            if (r15 == r4) goto L_0x01b4
            if (r15 == r7) goto L_0x01b1
            if (r15 == r5) goto L_0x01ae
            if (r15 != r6) goto L_0x0193
            if (r2 <= 0) goto L_0x01c7
            goto L_0x01c6
        L_0x0193:
            java.lang.IllegalArgumentException r14 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            java.lang.String r1 = "Invalid direction: "
            r0.<init>(r1)
            r0.append(r15)
            java.lang.String r15 = r13.m()
            r0.append(r15)
            java.lang.String r15 = r0.toString()
            r14.<init>(r15)
            throw r14
        L_0x01ae:
            if (r10 <= 0) goto L_0x01c7
            goto L_0x01c6
        L_0x01b1:
            if (r2 >= 0) goto L_0x01c7
            goto L_0x01c6
        L_0x01b4:
            if (r10 >= 0) goto L_0x01c7
            goto L_0x01c6
        L_0x01b7:
            if (r2 > 0) goto L_0x01c6
            if (r2 != 0) goto L_0x01c7
            int r10 = r10 * r3
            if (r10 <= 0) goto L_0x01c7
            goto L_0x01c6
        L_0x01bf:
            if (r2 < 0) goto L_0x01c6
            if (r2 != 0) goto L_0x01c7
            int r10 = r10 * r3
            if (r10 >= 0) goto L_0x01c7
        L_0x01c6:
            return r0
        L_0x01c7:
            android.view.View r14 = super.focusSearch(r14, r15)
            return r14
        */
        throw new UnsupportedOperationException("Method not decompiled: android.support.v7.widget.RecyclerView.focusSearch(android.view.View, int):android.view.View");
    }

    public kc g() {
        return this.l;
    }

    public final ViewGroup.LayoutParams generateDefaultLayoutParams() {
        kl klVar = this.m;
        if (klVar != null) {
            return klVar.f();
        }
        throw new IllegalStateException("RecyclerView has no LayoutManager".concat(m()));
    }

    public final ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        kl klVar = this.m;
        if (klVar != null) {
            return klVar.h(getContext(), attributeSet);
        }
        throw new IllegalStateException("RecyclerView has no LayoutManager".concat(m()));
    }

    public final CharSequence getAccessibilityClassName() {
        return "android.support.v7.widget.RecyclerView";
    }

    public final int getBaseline() {
        if (this.m != null) {
            return -1;
        }
        return super.getBaseline();
    }

    public final boolean getClipToPadding() {
        return this.h;
    }

    public final ld h(int i2) {
        ld ldVar = null;
        if (this.y) {
            return null;
        }
        int c2 = this.g.c();
        for (int i3 = 0; i3 < c2; i3++) {
            ld j2 = j(this.g.f(i3));
            if (j2 != null && !j2.isRemoved() && b(j2) == i2) {
                if (!this.g.k(j2.itemView)) {
                    return j2;
                }
                ldVar = j2;
            }
        }
        return ldVar;
    }

    public final boolean hasNestedScrollingParent() {
        return az().h(0);
    }

    public final ld i(View view) {
        ViewParent parent = view.getParent();
        if (parent == null || parent == this) {
            return j(view);
        }
        throw new IllegalArgumentException(a.ar(this, view, "View ", " is not a direct child of "));
    }

    public final boolean isAttachedToWindow() {
        return this.r;
    }

    public final boolean isLayoutSuppressed() {
        return this.v;
    }

    public final boolean isNestedScrollingEnabled() {
        return az().a;
    }

    public final View l(View view) {
        ViewParent parent = view.getParent();
        while (parent != null && parent != this && (parent instanceof View)) {
            view = (View) parent;
            parent = view.getParent();
        }
        if (parent == this) {
            return view;
        }
        return null;
    }

    public final String m() {
        return " " + super.toString() + ", adapter:" + this.l + ", layout:" + this.m + ", context:" + getContext();
    }

    /* access modifiers changed from: protected */
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.am = 0;
        boolean z2 = true;
        this.r = true;
        if (!this.t || isLayoutRequested()) {
            z2 = false;
        }
        this.t = z2;
        this.e.e();
        kl klVar = this.m;
        if (klVar != null) {
            klVar.bd();
        }
        this.Q = false;
        if (b) {
            jh jhVar = (jh) jh.a.get();
            this.L = jhVar;
            if (jhVar == null) {
                this.L = new jh();
                int[] iArr = wj.a;
                Display display = getDisplay();
                float f2 = 60.0f;
                if (!isInEditMode() && display != null) {
                    float refreshRate = display.getRefreshRate();
                    if (refreshRate >= 30.0f) {
                        f2 = refreshRate;
                    }
                }
                jh jhVar2 = this.L;
                jhVar2.e = (long) (1.0E9f / f2);
                jh.a.set(jhVar2);
            }
            this.L.c.add(this);
        }
    }

    /* access modifiers changed from: protected */
    public void onDetachedFromWindow() {
        jh jhVar;
        super.onDetachedFromWindow();
        kh khVar = this.E;
        if (khVar != null) {
            khVar.e();
        }
        ai();
        this.r = false;
        kl klVar = this.m;
        if (klVar != null) {
            klVar.br(this);
        }
        this.T.clear();
        removeCallbacks(this.aC);
        do {
        } while (mf.b.a() != null);
        ks ksVar = this.e;
        for (int i2 = 0; i2 < ksVar.c.size(); i2++) {
            wa.t(((ld) ksVar.c.get(i2)).itemView);
        }
        ksVar.f(ksVar.f.l, false);
        Iterator a2 = new wn(this, 1).a();
        while (a2.hasNext()) {
            wa.z((View) a2.next()).ad();
        }
        if (b && (jhVar = this.L) != null) {
            jhVar.c.remove(this);
            this.L = null;
        }
    }

    public final void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        int size = this.o.size();
        for (int i2 = 0; i2 < size; i2++) {
            ((ki) this.o.get(i2)).b(canvas, this);
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:30:0x0082  */
    /* JADX WARNING: Removed duplicated region for block: B:31:0x009f  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean onGenericMotionEvent(android.view.MotionEvent r19) {
        /*
            r18 = this;
            r6 = r18
            r7 = r19
            kl r0 = r6.m
            r8 = 0
            if (r0 != 0) goto L_0x000b
            goto L_0x0137
        L_0x000b:
            boolean r0 = r6.v
            if (r0 != 0) goto L_0x0137
            int r0 = r19.getAction()
            r1 = 8
            if (r0 != r1) goto L_0x0137
            int r0 = r19.getSource()
            r0 = r0 & 2
            r9 = 26
            r1 = 0
            if (r0 == 0) goto L_0x0044
            kl r0 = r6.m
            boolean r0 = r0.Z()
            if (r0 == 0) goto L_0x0032
            r0 = 9
            float r0 = r7.getAxisValue(r0)
            float r0 = -r0
            goto L_0x0033
        L_0x0032:
            r0 = r1
        L_0x0033:
            kl r2 = r6.m
            boolean r2 = r2.Y()
            if (r2 == 0) goto L_0x0041
            r1 = 10
            float r1 = r7.getAxisValue(r1)
        L_0x0041:
            r10 = r8
            r11 = r10
            goto L_0x006e
        L_0x0044:
            int r0 = r19.getSource()
            r2 = 4194304(0x400000, float:5.877472E-39)
            r0 = r0 & r2
            if (r0 == 0) goto L_0x0074
            float r0 = r7.getAxisValue(r9)
            kl r2 = r6.m
            boolean r2 = r2.Z()
            if (r2 == 0) goto L_0x005b
            float r0 = -r0
            goto L_0x006a
        L_0x005b:
            kl r2 = r6.m
            boolean r2 = r2.Y()
            if (r2 == 0) goto L_0x0069
            r17 = r1
            r1 = r0
            r0 = r17
            goto L_0x006a
        L_0x0069:
            r0 = r1
        L_0x006a:
            boolean r2 = r6.U
            r10 = r2
            r11 = r9
        L_0x006e:
            r17 = r1
            r1 = r0
            r0 = r17
            goto L_0x0077
        L_0x0074:
            r0 = r1
            r10 = r8
            r11 = r10
        L_0x0077:
            float r2 = r6.f7J
            float r1 = r1 * r2
            float r2 = r6.I
            float r0 = r0 * r2
            int r0 = (int) r0
            int r1 = (int) r1
            r12 = 1
            if (r10 == 0) goto L_0x009f
            lc r2 = r6.K
            android.widget.OverScroller r2 = r2.a
            int r3 = r2.getFinalY()
            int r4 = r2.getCurrY()
            int r3 = r3 - r4
            int r1 = r1 + r3
            int r3 = r2.getFinalX()
            int r2 = r2.getCurrX()
            int r3 = r3 - r2
            int r0 = r0 + r3
            r6.av(r0, r1, r12)
            goto L_0x012e
        L_0x009f:
            kl r2 = r6.m
            if (r2 != 0) goto L_0x00ac
            java.lang.String r0 = "RecyclerView"
            java.lang.String r1 = "Cannot scroll without a LayoutManager set. Call setLayoutManager with a non-null argument."
            android.util.Log.e(r0, r1)
            goto L_0x012e
        L_0x00ac:
            boolean r3 = r6.v
            if (r3 != 0) goto L_0x012e
            int[] r3 = r6.S
            r3[r8] = r8
            r3[r12] = r8
            boolean r13 = r2.Y()
            boolean r14 = r2.Z()
            if (r14 == 0) goto L_0x00c3
            r2 = r13 | 2
            goto L_0x00c4
        L_0x00c3:
            r2 = r13
        L_0x00c4:
            r3 = 1073741824(0x40000000, float:2.0)
            if (r7 != 0) goto L_0x00cf
            int r4 = r18.getHeight()
            float r4 = (float) r4
            float r4 = r4 / r3
            goto L_0x00d3
        L_0x00cf:
            float r4 = r19.getY()
        L_0x00d3:
            if (r7 != 0) goto L_0x00dc
            int r5 = r18.getWidth()
            float r5 = (float) r5
            float r5 = r5 / r3
            goto L_0x00e0
        L_0x00dc:
            float r5 = r19.getX()
        L_0x00e0:
            int r3 = r6.a(r0, r4)
            int r15 = r0 - r3
            int r0 = r6.ay(r1, r5)
            int r16 = r1 - r0
            r6.aw(r2, r12)
            if (r12 == r13) goto L_0x00f3
            r1 = r8
            goto L_0x00f4
        L_0x00f3:
            r1 = r15
        L_0x00f4:
            if (r12 == r14) goto L_0x00f8
            r2 = r8
            goto L_0x00fa
        L_0x00f8:
            r2 = r16
        L_0x00fa:
            int[] r3 = r6.S
            int[] r4 = r6.aA
            r5 = 1
            r0 = r18
            boolean r0 = r0.ak(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0110
            int[] r0 = r6.S
            r1 = r0[r8]
            int r15 = r15 - r1
            r0 = r0[r12]
            int r16 = r16 - r0
        L_0x0110:
            r0 = r16
            if (r12 == r13) goto L_0x0116
            r1 = r8
            goto L_0x0117
        L_0x0116:
            r1 = r15
        L_0x0117:
            if (r12 == r14) goto L_0x011b
            r2 = r8
            goto L_0x011c
        L_0x011b:
            r2 = r0
        L_0x011c:
            r6.aq(r1, r2, r7, r12)
            jh r1 = r6.L
            if (r1 == 0) goto L_0x012b
            if (r15 != 0) goto L_0x0128
            if (r0 == 0) goto L_0x012b
            r15 = r8
        L_0x0128:
            r1.a(r6, r15, r0)
        L_0x012b:
            r6.ah(r12)
        L_0x012e:
            if (r11 == 0) goto L_0x0137
            if (r10 != 0) goto L_0x0137
            va r0 = r6.V
            r0.a(r7, r9)
        L_0x0137:
            return r8
        */
        throw new UnsupportedOperationException("Method not decompiled: android.support.v7.widget.RecyclerView.onGenericMotionEvent(android.view.MotionEvent):boolean");
    }

    /* JADX WARNING: Code restructure failed: missing block: B:42:0x00c9, code lost:
        if (r3 != false) goto L_0x00cb;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:79:0x018f, code lost:
        if (r9.ao != 2) goto L_0x019e;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean onInterceptTouchEvent(android.view.MotionEvent r10) {
        /*
            r9 = this;
            boolean r0 = r9.v
            r1 = 0
            if (r0 == 0) goto L_0x0006
            return r1
        L_0x0006:
            r0 = 0
            r9.q = r0
            boolean r0 = r9.aM(r10)
            r2 = 1
            if (r0 == 0) goto L_0x0014
            r9.aA()
            return r2
        L_0x0014:
            kl r0 = r9.m
            if (r0 != 0) goto L_0x0019
            return r1
        L_0x0019:
            boolean r3 = r0.Y()
            boolean r0 = r0.Z()
            android.view.VelocityTracker r4 = r9.aq
            if (r4 != 0) goto L_0x002b
            android.view.VelocityTracker r4 = android.view.VelocityTracker.obtain()
            r9.aq = r4
        L_0x002b:
            android.view.VelocityTracker r4 = r9.aq
            r4.addMovement(r10)
            int r4 = r10.getActionMasked()
            int r5 = r10.getActionIndex()
            r6 = 2
            r7 = 1056964608(0x3f000000, float:0.5)
            if (r4 == 0) goto L_0x00da
            if (r4 == r2) goto L_0x00d0
            if (r4 == r6) goto L_0x0072
            r0 = 3
            if (r4 == r0) goto L_0x006d
            r0 = 5
            if (r4 == r0) goto L_0x0051
            r0 = 6
            if (r4 == r0) goto L_0x004c
            goto L_0x01a7
        L_0x004c:
            r9.aE(r10)
            goto L_0x01a7
        L_0x0051:
            int r0 = r10.getPointerId(r5)
            r9.ap = r0
            float r0 = r10.getX(r5)
            float r0 = r0 + r7
            int r0 = (int) r0
            r9.at = r0
            r9.ar = r0
            float r10 = r10.getY(r5)
            float r10 = r10 + r7
            int r10 = (int) r10
            r9.au = r10
            r9.as = r10
            goto L_0x01a7
        L_0x006d:
            r9.aA()
            goto L_0x01a7
        L_0x0072:
            int r4 = r9.ap
            int r4 = r10.findPointerIndex(r4)
            if (r4 >= 0) goto L_0x0095
            java.lang.StringBuilder r10 = new java.lang.StringBuilder
            java.lang.String r0 = "Error processing scroll; pointer index for id "
            r10.<init>(r0)
            int r0 = r9.ap
            r10.append(r0)
            java.lang.String r0 = " not found. Did any MotionEvents get skipped?"
            r10.append(r0)
            java.lang.String r10 = r10.toString()
            java.lang.String r0 = "RecyclerView"
            android.util.Log.e(r0, r10)
            return r1
        L_0x0095:
            float r5 = r10.getX(r4)
            float r5 = r5 + r7
            float r10 = r10.getY(r4)
            float r10 = r10 + r7
            int r4 = r9.ao
            if (r4 == r2) goto L_0x01a7
            int r10 = (int) r10
            int r4 = (int) r5
            int r5 = r9.ar
            int r5 = r4 - r5
            int r6 = r9.as
            int r6 = r10 - r6
            if (r3 == 0) goto L_0x00bb
            int r3 = java.lang.Math.abs(r5)
            int r5 = r9.av
            if (r3 <= r5) goto L_0x00bb
            r9.at = r4
            r3 = r2
            goto L_0x00bc
        L_0x00bb:
            r3 = r1
        L_0x00bc:
            if (r0 == 0) goto L_0x00c9
            int r0 = java.lang.Math.abs(r6)
            int r4 = r9.av
            if (r0 <= r4) goto L_0x00c9
            r9.au = r10
            goto L_0x00cb
        L_0x00c9:
            if (r3 == 0) goto L_0x01a7
        L_0x00cb:
            r9.ad(r2)
            goto L_0x01a7
        L_0x00d0:
            android.view.VelocityTracker r10 = r9.aq
            r10.clear()
            r9.ah(r1)
            goto L_0x01a7
        L_0x00da:
            boolean r0 = r9.aj
            if (r0 == 0) goto L_0x00e0
            r9.aj = r1
        L_0x00e0:
            int r0 = r10.getPointerId(r1)
            r9.ap = r0
            float r0 = r10.getX()
            float r0 = r0 + r7
            int r0 = (int) r0
            r9.at = r0
            r9.ar = r0
            float r0 = r10.getY()
            float r0 = r0 + r7
            int r0 = (int) r0
            r9.au = r0
            r9.as = r0
            android.widget.EdgeEffect r0 = r9.A
            r3 = 1065353216(0x3f800000, float:1.0)
            r4 = -1
            r5 = 0
            if (r0 == 0) goto L_0x0123
            float r0 = defpackage.xm.b(r0)
            int r0 = (r0 > r5 ? 1 : (r0 == r5 ? 0 : -1))
            if (r0 == 0) goto L_0x0123
            boolean r0 = r9.canScrollHorizontally(r4)
            if (r0 != 0) goto L_0x0123
            android.widget.EdgeEffect r0 = r9.A
            float r7 = r10.getY()
            int r8 = r9.getHeight()
            float r8 = (float) r8
            float r7 = r7 / r8
            float r7 = r3 - r7
            defpackage.xm.c(r0, r5, r7)
            r0 = r2
            goto L_0x0124
        L_0x0123:
            r0 = r1
        L_0x0124:
            android.widget.EdgeEffect r7 = r9.C
            if (r7 == 0) goto L_0x0146
            float r7 = defpackage.xm.b(r7)
            int r7 = (r7 > r5 ? 1 : (r7 == r5 ? 0 : -1))
            if (r7 == 0) goto L_0x0146
            boolean r7 = r9.canScrollHorizontally(r2)
            if (r7 != 0) goto L_0x0146
            android.widget.EdgeEffect r0 = r9.C
            float r7 = r10.getY()
            int r8 = r9.getHeight()
            float r8 = (float) r8
            float r7 = r7 / r8
            defpackage.xm.c(r0, r5, r7)
            r0 = r2
        L_0x0146:
            android.widget.EdgeEffect r7 = r9.B
            if (r7 == 0) goto L_0x0168
            float r7 = defpackage.xm.b(r7)
            int r7 = (r7 > r5 ? 1 : (r7 == r5 ? 0 : -1))
            if (r7 == 0) goto L_0x0168
            boolean r4 = r9.canScrollVertically(r4)
            if (r4 != 0) goto L_0x0168
            android.widget.EdgeEffect r0 = r9.B
            float r4 = r10.getX()
            int r7 = r9.getWidth()
            float r7 = (float) r7
            float r4 = r4 / r7
            defpackage.xm.c(r0, r5, r4)
            r0 = r2
        L_0x0168:
            android.widget.EdgeEffect r4 = r9.D
            if (r4 == 0) goto L_0x018b
            float r4 = defpackage.xm.b(r4)
            int r4 = (r4 > r5 ? 1 : (r4 == r5 ? 0 : -1))
            if (r4 == 0) goto L_0x018b
            boolean r4 = r9.canScrollVertically(r2)
            if (r4 != 0) goto L_0x018b
            android.widget.EdgeEffect r0 = r9.D
            float r10 = r10.getX()
            int r4 = r9.getWidth()
            float r4 = (float) r4
            float r10 = r10 / r4
            float r3 = r3 - r10
            defpackage.xm.c(r0, r5, r3)
            goto L_0x0191
        L_0x018b:
            if (r0 != 0) goto L_0x0191
            int r10 = r9.ao
            if (r10 != r6) goto L_0x019e
        L_0x0191:
            android.view.ViewParent r10 = r9.getParent()
            r10.requestDisallowInterceptTouchEvent(r2)
            r9.ad(r2)
            r9.ah(r2)
        L_0x019e:
            int[] r10 = r9.aB
            r10[r2] = r1
            r10[r1] = r1
            r9.aK(r1)
        L_0x01a7:
            int r10 = r9.ao
            if (r10 != r2) goto L_0x01ac
            return r2
        L_0x01ac:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: android.support.v7.widget.RecyclerView.onInterceptTouchEvent(android.view.MotionEvent):boolean");
    }

    /* access modifiers changed from: protected */
    public final void onLayout(boolean z2, int i2, int i3, int i4, int i5) {
        Trace.beginSection("RV OnLayout");
        C();
        Trace.endSection();
        this.t = true;
    }

    /* access modifiers changed from: protected */
    public final void onMeasure(int i2, int i3) {
        kl klVar = this.m;
        if (klVar == null) {
            A(i2, i3);
            return;
        }
        boolean z2 = false;
        if (klVar.aa()) {
            int mode = View.MeasureSpec.getMode(i2);
            int mode2 = View.MeasureSpec.getMode(i3);
            this.m.bp(i2, i3);
            if (mode == 1073741824 && mode2 == 1073741824) {
                z2 = true;
            }
            this.aD = z2;
            if (!z2 && this.l != null) {
                if (this.N.d == 1) {
                    aB();
                }
                this.m.aU(i2, i3);
                this.N.i = true;
                aC();
                this.m.aW(i2, i3);
                if (this.m.ae()) {
                    this.m.aU(View.MeasureSpec.makeMeasureSpec(getMeasuredWidth(), 1073741824), View.MeasureSpec.makeMeasureSpec(getMeasuredHeight(), 1073741824));
                    this.N.i = true;
                    aC();
                    this.m.aW(i2, i3);
                }
                this.aE = getMeasuredWidth();
                this.aF = getMeasuredHeight();
            }
        } else if (this.s) {
            klVar.bp(i2, i3);
        } else {
            if (this.w) {
                af();
                Q();
                aF();
                R();
                la laVar = this.N;
                if (laVar.k) {
                    laVar.g = true;
                } else {
                    this.W.f();
                    this.N.g = false;
                }
                this.w = false;
                ag(false);
            } else if (this.N.k) {
                setMeasuredDimension(getMeasuredWidth(), getMeasuredHeight());
                return;
            }
            kc kcVar = this.l;
            if (kcVar != null) {
                this.N.e = kcVar.getItemCount();
            } else {
                this.N.e = 0;
            }
            af();
            this.m.bp(i2, i3);
            ag(false);
            this.N.g = false;
        }
    }

    /* access modifiers changed from: protected */
    public final boolean onRequestFocusInDescendants(int i2, Rect rect) {
        if (ap()) {
            return false;
        }
        return super.onRequestFocusInDescendants(i2, rect);
    }

    /* access modifiers changed from: protected */
    public final void onRestoreInstanceState(Parcelable parcelable) {
        if (!(parcelable instanceof kw)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        kw kwVar = (kw) parcelable;
        this.f = kwVar;
        super.onRestoreInstanceState(kwVar.d);
        requestLayout();
    }

    /* access modifiers changed from: protected */
    public final Parcelable onSaveInstanceState() {
        Parcelable parcelable;
        kw kwVar = new kw(super.onSaveInstanceState());
        kw kwVar2 = this.f;
        if (kwVar2 != null) {
            kwVar.a = kwVar2.a;
        } else {
            kl klVar = this.m;
            if (klVar != null) {
                parcelable = klVar.N();
            } else {
                parcelable = null;
            }
            kwVar.a = parcelable;
        }
        return kwVar;
    }

    /* access modifiers changed from: protected */
    public final void onSizeChanged(int i2, int i3, int i4, int i5) {
        super.onSizeChanged(i2, i3, i4, i5);
        if (i2 != i4 || i3 != i5) {
            L();
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:81:0x0191, code lost:
        if (r0 != 0) goto L_0x0195;
     */
    /* JADX WARNING: Removed duplicated region for block: B:47:0x00ec  */
    /* JADX WARNING: Removed duplicated region for block: B:53:0x0106  */
    /* JADX WARNING: Removed duplicated region for block: B:55:0x0109  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public boolean onTouchEvent(android.view.MotionEvent r18) {
        /*
            r17 = this;
            r6 = r17
            r7 = r18
            boolean r0 = r6.v
            r8 = 0
            if (r0 != 0) goto L_0x0212
            boolean r0 = r6.aj
            if (r0 == 0) goto L_0x000f
            goto L_0x0212
        L_0x000f:
            kp r0 = r6.q
            r1 = 3
            r9 = 1
            if (r0 != 0) goto L_0x0200
            int r0 = r18.getAction()
            if (r0 != 0) goto L_0x001c
            goto L_0x0024
        L_0x001c:
            boolean r0 = r17.aM(r18)
            if (r0 == 0) goto L_0x0024
            goto L_0x020e
        L_0x0024:
            kl r0 = r6.m
            if (r0 == 0) goto L_0x0212
            boolean r2 = r0.Y()
            boolean r0 = r0.Z()
            android.view.VelocityTracker r3 = r6.aq
            if (r3 != 0) goto L_0x003a
            android.view.VelocityTracker r3 = android.view.VelocityTracker.obtain()
            r6.aq = r3
        L_0x003a:
            int r3 = r18.getActionMasked()
            int r4 = r18.getActionIndex()
            if (r3 != 0) goto L_0x004b
            int[] r3 = r6.aB
            r3[r9] = r8
            r3[r8] = r8
            r3 = r8
        L_0x004b:
            android.view.MotionEvent r10 = android.view.MotionEvent.obtain(r18)
            int[] r5 = r6.aB
            r11 = r5[r8]
            float r11 = (float) r11
            r5 = r5[r9]
            float r5 = (float) r5
            r10.offsetLocation(r11, r5)
            r5 = 1056964608(0x3f000000, float:0.5)
            if (r3 == 0) goto L_0x01da
            if (r3 == r9) goto L_0x0199
            r11 = 2
            if (r3 == r11) goto L_0x0093
            if (r3 == r1) goto L_0x008e
            r0 = 5
            if (r3 == r0) goto L_0x0072
            r0 = 6
            if (r3 == r0) goto L_0x006d
            goto L_0x01f7
        L_0x006d:
            r17.aE(r18)
            goto L_0x01f7
        L_0x0072:
            int r0 = r7.getPointerId(r4)
            r6.ap = r0
            float r0 = r7.getX(r4)
            float r0 = r0 + r5
            int r0 = (int) r0
            r6.at = r0
            r6.ar = r0
            float r0 = r7.getY(r4)
            float r0 = r0 + r5
            int r0 = (int) r0
            r6.au = r0
            r6.as = r0
            goto L_0x01f7
        L_0x008e:
            r17.aA()
            goto L_0x01f7
        L_0x0093:
            int r1 = r6.ap
            int r1 = r7.findPointerIndex(r1)
            if (r1 >= 0) goto L_0x00b6
            java.lang.StringBuilder r0 = new java.lang.StringBuilder
            java.lang.String r1 = "Error processing scroll; pointer index for id "
            r0.<init>(r1)
            int r1 = r6.ap
            r0.append(r1)
            java.lang.String r1 = " not found. Did any MotionEvents get skipped?"
            r0.append(r1)
            java.lang.String r0 = r0.toString()
            java.lang.String r1 = "RecyclerView"
            android.util.Log.e(r1, r0)
            return r8
        L_0x00b6:
            float r3 = r7.getX(r1)
            float r3 = r3 + r5
            float r1 = r7.getY(r1)
            float r1 = r1 + r5
            int r4 = r6.at
            int r11 = (int) r3
            int r4 = r4 - r11
            int r3 = r6.au
            int r12 = (int) r1
            int r3 = r3 - r12
            int r1 = r6.ao
            if (r1 == r9) goto L_0x010c
            if (r2 == 0) goto L_0x00e8
            if (r4 <= 0) goto L_0x00d8
            int r1 = r6.av
            int r4 = r4 - r1
            int r1 = java.lang.Math.max(r8, r4)
            goto L_0x00df
        L_0x00d8:
            int r1 = r6.av
            int r4 = r4 + r1
            int r1 = java.lang.Math.min(r8, r4)
        L_0x00df:
            if (r1 == 0) goto L_0x00e4
            r4 = r1
            r1 = r9
            goto L_0x00e9
        L_0x00e4:
            r4 = r1
            r1 = r8
            r2 = r9
            goto L_0x00ea
        L_0x00e8:
            r1 = r8
        L_0x00e9:
            r2 = r1
        L_0x00ea:
            if (r0 == 0) goto L_0x0106
            if (r3 <= 0) goto L_0x00f6
            int r0 = r6.av
            int r3 = r3 - r0
            int r0 = java.lang.Math.max(r8, r3)
            goto L_0x00fd
        L_0x00f6:
            int r0 = r6.av
            int r3 = r3 + r0
            int r0 = java.lang.Math.min(r8, r3)
        L_0x00fd:
            if (r0 == 0) goto L_0x0103
            r3 = r0
            r0 = r9
            r1 = r0
            goto L_0x0107
        L_0x0103:
            r3 = r0
            r0 = r9
            goto L_0x0107
        L_0x0106:
            r0 = r8
        L_0x0107:
            if (r1 == 0) goto L_0x010c
            r6.ad(r9)
        L_0x010c:
            r13 = r0
            r14 = r2
            int r0 = r6.ao
            if (r0 != r9) goto L_0x01f7
            int[] r0 = r6.S
            r0[r8] = r8
            r0[r9] = r8
            float r0 = r18.getY()
            int r0 = r6.a(r4, r0)
            int r15 = r4 - r0
            float r0 = r18.getX()
            int r0 = r6.ay(r3, r0)
            int r16 = r3 - r0
            if (r9 == r14) goto L_0x0130
            r1 = r8
            goto L_0x0131
        L_0x0130:
            r1 = r15
        L_0x0131:
            if (r9 == r13) goto L_0x0135
            r2 = r8
            goto L_0x0137
        L_0x0135:
            r2 = r16
        L_0x0137:
            int[] r3 = r6.S
            int[] r4 = r6.aA
            r5 = 0
            r0 = r17
            boolean r0 = r0.ak(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0166
            int[] r0 = r6.S
            r1 = r0[r8]
            int r15 = r15 - r1
            r0 = r0[r9]
            int r16 = r16 - r0
            int[] r0 = r6.aB
            r1 = r0[r8]
            int[] r2 = r6.aA
            r3 = r2[r8]
            int r1 = r1 + r3
            r0[r8] = r1
            r1 = r0[r9]
            r2 = r2[r9]
            int r1 = r1 + r2
            r0[r9] = r1
            android.view.ViewParent r0 = r17.getParent()
            r0.requestDisallowInterceptTouchEvent(r9)
        L_0x0166:
            r0 = r16
            int[] r1 = r6.aA
            r2 = r1[r8]
            int r11 = r11 - r2
            r6.at = r11
            r1 = r1[r9]
            int r12 = r12 - r1
            r6.au = r12
            if (r9 == r14) goto L_0x0178
            r1 = r8
            goto L_0x0179
        L_0x0178:
            r1 = r15
        L_0x0179:
            if (r9 == r13) goto L_0x017d
            r2 = r8
            goto L_0x017e
        L_0x017d:
            r2 = r0
        L_0x017e:
            boolean r1 = r6.aq(r1, r2, r7, r8)
            if (r1 == 0) goto L_0x018b
            android.view.ViewParent r1 = r17.getParent()
            r1.requestDisallowInterceptTouchEvent(r9)
        L_0x018b:
            jh r1 = r6.L
            if (r1 == 0) goto L_0x01f7
            if (r15 != 0) goto L_0x0194
            if (r0 == 0) goto L_0x01f7
            goto L_0x0195
        L_0x0194:
            r8 = r15
        L_0x0195:
            r1.a(r6, r8, r0)
            goto L_0x01f7
        L_0x0199:
            android.view.VelocityTracker r1 = r6.aq
            r1.addMovement(r10)
            android.view.VelocityTracker r1 = r6.aq
            int r3 = r6.H
            float r3 = (float) r3
            r4 = 1000(0x3e8, float:1.401E-42)
            r1.computeCurrentVelocity(r4, r3)
            r1 = 0
            if (r2 == 0) goto L_0x01b5
            android.view.VelocityTracker r2 = r6.aq
            int r3 = r6.ap
            float r2 = r2.getXVelocity(r3)
            float r2 = -r2
            goto L_0x01b6
        L_0x01b5:
            r2 = r1
        L_0x01b6:
            if (r0 == 0) goto L_0x01c2
            android.view.VelocityTracker r0 = r6.aq
            int r3 = r6.ap
            float r0 = r0.getYVelocity(r3)
            float r0 = -r0
            goto L_0x01c3
        L_0x01c2:
            r0 = r1
        L_0x01c3:
            int r3 = (r2 > r1 ? 1 : (r2 == r1 ? 0 : -1))
            if (r3 != 0) goto L_0x01cb
            int r1 = (r0 > r1 ? 1 : (r0 == r1 ? 0 : -1))
            if (r1 == 0) goto L_0x01d3
        L_0x01cb:
            int r1 = (int) r2
            int r0 = (int) r0
            boolean r0 = r6.al(r1, r0)
            if (r0 != 0) goto L_0x01d6
        L_0x01d3:
            r6.ad(r8)
        L_0x01d6:
            r17.aJ()
            goto L_0x01fc
        L_0x01da:
            int r0 = r7.getPointerId(r8)
            r6.ap = r0
            float r0 = r18.getX()
            float r0 = r0 + r5
            int r0 = (int) r0
            r6.at = r0
            r6.ar = r0
            float r0 = r18.getY()
            float r0 = r0 + r5
            int r0 = (int) r0
            r6.au = r0
            r6.as = r0
            r6.aK(r8)
        L_0x01f7:
            android.view.VelocityTracker r0 = r6.aq
            r0.addMovement(r10)
        L_0x01fc:
            r10.recycle()
            return r9
        L_0x0200:
            r0.k(r7)
            int r0 = r18.getAction()
            if (r0 == r1) goto L_0x020b
            if (r0 != r9) goto L_0x020e
        L_0x020b:
            r0 = 0
            r6.q = r0
        L_0x020e:
            r17.aA()
            return r9
        L_0x0212:
            return r8
        */
        throw new UnsupportedOperationException("Method not decompiled: android.support.v7.widget.RecyclerView.onTouchEvent(android.view.MotionEvent):boolean");
    }

    public final void removeDetachedView(View view, boolean z2) {
        ld j2 = j(view);
        if (j2 != null) {
            if (j2.isTmpDetached()) {
                j2.clearTmpDetachFlag();
            } else if (!j2.shouldIgnore()) {
                throw new IllegalArgumentException("Called removeDetachedView with a view which is not flagged as tmp detached." + j2 + m());
            }
        }
        view.clearAnimation();
        B(view);
        super.removeDetachedView(view, z2);
    }

    public final void requestChildFocus(View view, View view2) {
        if (!this.m.ba() && !ap() && view2 != null) {
            aH(view, view2);
        }
        super.requestChildFocus(view, view2);
    }

    public final boolean requestChildRectangleOnScreen(View view, Rect rect, boolean z2) {
        return this.m.bb(this, view, rect, z2, false);
    }

    public final void requestDisallowInterceptTouchEvent(boolean z2) {
        int size = this.p.size();
        for (int i2 = 0; i2 < size; i2++) {
            ((kp) this.p.get(i2)).j();
        }
        super.requestDisallowInterceptTouchEvent(z2);
    }

    public final void requestLayout() {
        if (this.ai != 0 || this.v) {
            this.u = true;
        } else {
            super.requestLayout();
        }
    }

    public final void s(ld ldVar) {
        View view = ldVar.itemView;
        ViewParent parent = view.getParent();
        this.e.m(i(view));
        if (ldVar.isTmpDetached()) {
            this.g.h(view, -1, view.getLayoutParams(), true);
        } else if (parent != this) {
            this.g.g(view, -1, true);
        } else {
            il ilVar = this.g;
            int r2 = ilVar.e.r(view);
            if (r2 >= 0) {
                ilVar.a.e(r2);
                ilVar.j(view);
                return;
            }
            Objects.toString(view);
            throw new IllegalArgumentException("view is not a child, cannot hide ".concat(String.valueOf(view)));
        }
    }

    public final void scrollBy(int i2, int i3) {
        kl klVar = this.m;
        if (klVar == null) {
            Log.e("RecyclerView", "Cannot scroll without a LayoutManager set. Call setLayoutManager with a non-null argument.");
        } else if (!this.v) {
            boolean Y = klVar.Y();
            boolean Z = klVar.Z();
            if (!Y) {
                if (Z) {
                    Z = true;
                } else {
                    return;
                }
            }
            if (true != Y) {
                i2 = 0;
            }
            if (true != Z) {
                i3 = 0;
            }
            aq(i2, i3, (MotionEvent) null, 0);
        }
    }

    public final void scrollTo(int i2, int i3) {
        Log.w("RecyclerView", "RecyclerView does not support scrolling to an absolute position. Use scrollToPosition instead");
    }

    public final void sendAccessibilityEventUnchecked(AccessibilityEvent accessibilityEvent) {
        int i2;
        if (ap()) {
            int i3 = 0;
            if (accessibilityEvent != null) {
                i2 = accessibilityEvent.getContentChangeTypes();
            } else {
                i2 = 0;
            }
            if (i2 != 0) {
                i3 = i2;
            }
            this.ak |= i3;
            return;
        }
        super.sendAccessibilityEventUnchecked(accessibilityEvent);
    }

    public final void setClipToPadding(boolean z2) {
        if (z2 != this.h) {
            L();
        }
        this.h = z2;
        super.setClipToPadding(z2);
        if (this.t) {
            requestLayout();
        }
    }

    @Deprecated
    public final void setLayoutTransition(LayoutTransition layoutTransition) {
        if (layoutTransition == null) {
            super.setLayoutTransition((LayoutTransition) null);
            return;
        }
        throw new IllegalArgumentException("Providing a LayoutTransition into RecyclerView is not supported. Please use setItemAnimator() instead for animating changes to the items in this RecyclerView");
    }

    public final void setNestedScrollingEnabled(boolean z2) {
        az().a(z2);
    }

    public final boolean startNestedScroll(int i2) {
        return az().i(i2, 0);
    }

    public final void stopNestedScroll() {
        az().b(0);
    }

    public final void suppressLayout(boolean z2) {
        if (z2 != this.v) {
            v("Do not suppressLayout in layout or scroll");
            if (!z2) {
                this.v = false;
                if (!(!this.u || this.m == null || this.l == null)) {
                    requestLayout();
                }
                this.u = false;
                return;
            }
            long uptimeMillis = SystemClock.uptimeMillis();
            onTouchEvent(MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, 0.0f, 0.0f, 0));
            this.v = true;
            this.aj = true;
            ai();
        }
    }

    public void t(ki kiVar) {
        kl klVar = this.m;
        if (klVar != null) {
            klVar.Q("Cannot add item decoration during a scroll  or layout");
        }
        if (this.o.isEmpty()) {
            setWillNotDraw(false);
        }
        this.o.add(kiVar);
        O();
        requestLayout();
    }

    public final void u(kq kqVar) {
        if (this.ax == null) {
            this.ax = new ArrayList();
        }
        this.ax.add(kqVar);
    }

    public final void v(String str) {
        if (ap()) {
            if (str == null) {
                throw new IllegalStateException("Cannot call this method while RecyclerView is computing a layout or scrolling".concat(m()));
            }
            throw new IllegalStateException(str);
        } else if (this.an > 0) {
            Log.w("RecyclerView", "Cannot call this method in a scroll callback. Scroll callbacks mightbe run during a measure & layout pass where you cannot change theRecyclerView data. Any method call that might change the structureof the RecyclerView or the adapter contents should be postponed tothe next frame.", new IllegalStateException("".concat(m())));
        }
    }

    /* access modifiers changed from: package-private */
    public final void x() {
        int c2 = this.g.c();
        for (int i2 = 0; i2 < c2; i2++) {
            ld j2 = j(this.g.f(i2));
            if (!j2.shouldIgnore()) {
                j2.clearOldPosition();
            }
        }
        ks ksVar = this.e;
        int size = ksVar.c.size();
        for (int i3 = 0; i3 < size; i3++) {
            ((ld) ksVar.c.get(i3)).clearOldPosition();
        }
        int size2 = ksVar.a.size();
        for (int i4 = 0; i4 < size2; i4++) {
            ((ld) ksVar.a.get(i4)).clearOldPosition();
        }
        ArrayList arrayList = ksVar.b;
        if (arrayList != null) {
            int size3 = arrayList.size();
            for (int i5 = 0; i5 < size3; i5++) {
                ((ld) ksVar.b.get(i5)).clearOldPosition();
            }
        }
    }

    public final void y(int i2, int i3) {
        EdgeEffect edgeEffect = this.A;
        boolean z2 = false;
        if (edgeEffect != null && !edgeEffect.isFinished() && i2 > 0) {
            this.A.onRelease();
            z2 = this.A.isFinished();
        }
        EdgeEffect edgeEffect2 = this.C;
        if (edgeEffect2 != null && !edgeEffect2.isFinished() && i2 < 0) {
            this.C.onRelease();
            z2 |= this.C.isFinished();
        }
        EdgeEffect edgeEffect3 = this.B;
        if (edgeEffect3 != null && !edgeEffect3.isFinished() && i3 > 0) {
            this.B.onRelease();
            z2 |= this.B.isFinished();
        }
        EdgeEffect edgeEffect4 = this.D;
        if (edgeEffect4 != null && !edgeEffect4.isFinished() && i3 < 0) {
            this.D.onRelease();
            z2 |= this.D.isFinished();
        }
        if (z2) {
            postInvalidateOnAnimation();
        }
    }

    public final void z() {
        if (!this.t || this.y) {
            Trace.beginSection("RV FullInvalidate");
            C();
            Trace.endSection();
        } else if (this.W.m()) {
            if (this.W.l(4) && !this.W.l(11)) {
                Trace.beginSection("RV PartialInvalidate");
                af();
                Q();
                this.W.h();
                if (!this.u) {
                    int a2 = this.g.a();
                    int i2 = 0;
                    while (true) {
                        if (i2 < a2) {
                            ld j2 = j(this.g.e(i2));
                            if (j2 != null && !j2.shouldIgnore() && j2.isUpdated()) {
                                C();
                                break;
                            }
                            i2++;
                        } else {
                            this.W.e();
                            break;
                        }
                    }
                }
                ag(true);
                R();
                Trace.endSection();
            } else if (this.W.m()) {
                Trace.beginSection("RV FullInvalidate");
                C();
                Trace.endSection();
            }
        }
    }

    public RecyclerView(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, R.attr.recyclerViewStyle);
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public RecyclerView(Context context, AttributeSet attributeSet, int i2) {
        super(context, attributeSet, i2);
        Context context2 = context;
        AttributeSet attributeSet2 = attributeSet;
        int i3 = i2;
        this.ag = new ku(this);
        this.e = new ks(this);
        jf jfVar = null;
        this.aa = new bvj((byte[]) null);
        this.i = new ak(this, 20, (byte[]) null);
        this.j = new Rect();
        this.ah = new Rect();
        this.k = new RectF();
        this.n = new ArrayList();
        this.o = new ArrayList();
        this.p = new ArrayList();
        this.ai = 0;
        this.y = false;
        this.z = false;
        this.am = 0;
        this.an = 0;
        this.aH = d;
        this.E = new kh((byte[]) null);
        this.ao = 0;
        this.ap = -1;
        this.I = Float.MIN_VALUE;
        this.f7J = Float.MIN_VALUE;
        this.aw = true;
        this.K = new lc(this);
        this.M = b ? new jf() : jfVar;
        this.N = new la();
        this.O = false;
        this.P = false;
        this.aI = new AmbientMode.AmbientController((Object) this);
        this.Q = false;
        this.ay = new int[2];
        this.aA = new int[2];
        this.aB = new int[2];
        this.S = new int[2];
        this.T = new ArrayList();
        this.aC = new lk(this, 1);
        this.aE = 0;
        this.aF = 0;
        this.aJ = new AmbientMode.AmbientController((Object) this);
        yk ykVar = new yk(this, 1);
        this.aG = ykVar;
        this.V = new va(getContext(), ykVar);
        setScrollContainer(true);
        setFocusableInTouchMode(true);
        ViewConfiguration viewConfiguration = ViewConfiguration.get(context);
        this.av = viewConfiguration.getScaledTouchSlop();
        this.I = viewConfiguration.getScaledHorizontalScrollFactor();
        this.f7J = viewConfiguration.getScaledVerticalScrollFactor();
        this.G = viewConfiguration.getScaledMinimumFlingVelocity();
        this.H = viewConfiguration.getScaledMaximumFlingVelocity();
        this.af = context.getResources().getDisplayMetrics().density * 160.0f * 386.0878f * 0.84f;
        setWillNotDraw(getOverScrollMode() == 2);
        this.E.m = this.aI;
        this.W = new dxr(new AmbientMode.AmbientController((Object) this));
        this.g = new il(new AmbientMode.AmbientController((Object) this));
        if (wc.a(this) == 0) {
            wc.b(this, 8);
        }
        if (getImportantForAccessibility() == 0) {
            setImportantForAccessibility(1);
        }
        this.al = (AccessibilityManager) getContext().getSystemService("accessibility");
        aa(new lf(this));
        TypedArray obtainStyledAttributes = context2.obtainStyledAttributes(attributeSet2, et.a, i3, 0);
        TypedArray typedArray = obtainStyledAttributes;
        wj.l(this, context, et.a, attributeSet, obtainStyledAttributes, i2, 0);
        String string = typedArray.getString(8);
        if (typedArray.getInt(2, -1) == -1) {
            setDescendantFocusability(262144);
        }
        this.h = typedArray.getBoolean(1, true);
        if (typedArray.getBoolean(3, false)) {
            StateListDrawable stateListDrawable = (StateListDrawable) typedArray.getDrawable(6);
            Drawable drawable = typedArray.getDrawable(7);
            StateListDrawable stateListDrawable2 = (StateListDrawable) typedArray.getDrawable(4);
            Drawable drawable2 = typedArray.getDrawable(5);
            if (stateListDrawable == null || drawable == null || stateListDrawable2 == null || drawable2 == null) {
                throw new IllegalArgumentException("Trying to set fast scroller without both required drawables.".concat(m()));
            }
            Resources resources = getContext().getResources();
            new jd(this, stateListDrawable, drawable, stateListDrawable2, drawable2, resources.getDimensionPixelSize(R.dimen.fastscroll_default_thickness), resources.getDimensionPixelSize(R.dimen.fastscroll_minimum_range), resources.getDimensionPixelOffset(R.dimen.fastscroll_margin));
        }
        typedArray.recycle();
        this.U = context.getPackageManager().hasSystemFeature("android.hardware.rotaryencoder.lowres");
        aP(context2, string, attributeSet2, i3);
        int[] iArr = ac;
        TypedArray obtainStyledAttributes2 = context2.obtainStyledAttributes(attributeSet2, iArr, i3, 0);
        wj.l(this, context, iArr, attributeSet, obtainStyledAttributes2, i2, 0);
        boolean z2 = obtainStyledAttributes2.getBoolean(0, true);
        obtainStyledAttributes2.recycle();
        setNestedScrollingEnabled(z2);
        setTag(R.id.is_pooling_container_tag, true);
    }

    public final ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        kl klVar = this.m;
        if (klVar != null) {
            return klVar.g(layoutParams);
        }
        throw new IllegalStateException("RecyclerView has no LayoutManager".concat(m()));
    }
}
