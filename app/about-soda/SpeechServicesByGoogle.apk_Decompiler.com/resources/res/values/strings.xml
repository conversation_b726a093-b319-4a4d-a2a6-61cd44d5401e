<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="abc_action_bar_up_description">Navigate up</string>
    <string name="abc_action_mode_done">Done</string>
    <string name="abc_capital_off">OFF</string>
    <string name="abc_capital_on">ON</string>
    <string name="abc_menu_alt_shortcut_label">Alt+</string>
    <string name="abc_menu_ctrl_shortcut_label">Ctrl+</string>
    <string name="abc_menu_delete_shortcut_label">delete</string>
    <string name="abc_menu_enter_shortcut_label">enter</string>
    <string name="abc_menu_function_shortcut_label">Function+</string>
    <string name="abc_menu_meta_shortcut_label">Meta+</string>
    <string name="abc_menu_shift_shortcut_label">Shift+</string>
    <string name="abc_menu_space_shortcut_label">space</string>
    <string name="abc_menu_sym_shortcut_label">Sym+</string>
    <string name="abc_prepend_shortcut_label">Menu+</string>
    <string name="abc_search_hint">Search…</string>
    <string name="abc_searchview_description_clear">Clear query</string>
    <string name="abc_searchview_description_search">Search</string>
    <string name="abc_searchview_description_submit">Submit query</string>
    <string name="abc_searchview_description_voice">Voice search</string>
    <string name="abc_toolbar_collapse_description">Collapse</string>
    <string name="action_licenses">Open-Source licenses</string>
    <string name="action_primes">Primes debug</string>
    <string name="action_voice_pack_uninstall">Uninstall</string>
    <string name="action_voice_pack_update">Update</string>
    <string name="add_language_details">Manage offline languages</string>
    <string name="add_language_key">add_language</string>
    <string name="add_language_title">Add a language</string>
    <string name="analytics_info_icon_description">Info Icon</string>
    <string name="analytics_screen_explanation">When this feature is turned on, we collect anonymous usage reports. This helps us better understand the performance of Google text-to-speech on your device.\10\10Examples of aggregate metrics that we collect:\10How often text-to-speech is used.\10The performance of text-to-speech requests.\10Which languages are used.\10How often text-to-speech fails to deliver speech output.\10</string>
    <string name="analytics_screen_key">analytics</string>
    <string name="analytics_summary_off">Off</string>
    <string name="analytics_summary_on">On</string>
    <string name="analytics_title">Anonymous usage reports</string>
    <string name="app_name">Speech Recognition and Synthesis from Google</string>
    <string name="appbar_scrolling_view_behavior">com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior</string>
    <string name="auto_update_key">auto_update_languages</string>
    <string name="auto_update_title">Auto update languages</string>
    <string name="auto_update_wifi_key">auto_update_wifi</string>
    <string name="auto_update_wifi_summary">Download voices data using only Wi-Fi, this conserves data usage</string>
    <string name="auto_update_wifi_title">Use Wi-Fi only</string>
    <string name="available_languages_key">available_languages</string>
    <string name="available_languages_title">Available languages</string>
    <string name="bottomsheet_action_expand_halfway">Expand halfway</string>
    <string name="cancel_prompt">Cancel</string>
    <string name="car_ui_alert_dialog_default_button">Close</string>
    <string name="car_ui_dialog_preference_negative">@android:string/cancel</string>
    <string name="car_ui_dialog_preference_positive">@android:string/ok</string>
    <string name="car_ui_ellipsis">…</string>
    <string name="car_ui_ime_wide_screen_system_property_name">ro.build.automotive.ime.wide_screen.enabled</string>
    <string name="car_ui_installer_process_name" />
    <string name="car_ui_plugin_package_provider_authority_name">com.android.car.ui.plugin</string>
    <string name="car_ui_preference_switch_off">Off</string>
    <string name="car_ui_preference_switch_on">On</string>
    <string name="car_ui_restricted_while_driving">Feature not available while driving</string>
    <string name="car_ui_scrollbar_component" />
    <string name="car_ui_scrollbar_page_down_button">Scroll down</string>
    <string name="car_ui_scrollbar_page_up_button">Scroll up</string>
    <string name="car_ui_scrolling_limited_message">Scrolling limited while driving</string>
    <string name="car_ui_toolbar_default_search_hint">Search…</string>
    <string name="car_ui_toolbar_menu_item_overflow_title">Overflow</string>
    <string name="car_ui_toolbar_menu_item_search_title">Search</string>
    <string name="car_ui_toolbar_menu_item_settings_title">Settings</string>
    <string name="car_ui_toolbar_nav_icon_content_description">Back</string>
    <string name="common_google_play_services_enable_button">Enable</string>
    <string name="common_google_play_services_enable_text">%1$s won\'t work unless you enable Google Play services.</string>
    <string name="common_google_play_services_enable_title">Enable Google Play services</string>
    <string name="common_google_play_services_install_button">Install</string>
    <string name="common_google_play_services_install_text">%1$s won\'t run without Google Play services, which are missing from your device.</string>
    <string name="common_google_play_services_install_title">Get Google Play services</string>
    <string name="common_google_play_services_notification_channel_name">Google Play services availability</string>
    <string name="common_google_play_services_notification_ticker">Google Play services error</string>
    <string name="common_google_play_services_unknown_issue">%1$s is having trouble with Google Play services. Please try again.</string>
    <string name="common_google_play_services_unsupported_text">%1$s won\'t run without Google Play services, which are not supported by your device.</string>
    <string name="common_google_play_services_update_button">Update</string>
    <string name="common_google_play_services_update_text">%1$s won\'t run unless you update Google Play services.</string>
    <string name="common_google_play_services_update_title">Update Google Play services</string>
    <string name="common_google_play_services_updating_text">%1$s won\'t run without Google Play services, which are currently updating.</string>
    <string name="common_google_play_services_wear_update_text">New version of Google Play services needed. It will update itself shortly.</string>
    <string name="common_open_on_phone">Open on phone</string>
    <string name="copy">Copy</string>
    <string name="default_voice_prefix_key">default_voice_for_</string>
    <string name="download_cancel">Cancel</string>
    <string name="download_language_pack_details">Download this update (%1$s) to use speech recognition offline</string>
    <string name="download_prompt">Download</string>
    <string name="download_title">Download %1$s update</string>
    <string name="download_ui_process" />
    <string name="downloaded_languages_category">downloaded_languages_category</string>
    <string name="downloaded_languages_category_title">Downloaded languages</string>
    <string name="expand_button_title">Advanced</string>
    <string name="expandable_downloaded_languages_screen">expandable_downloaded_languages_screen</string>
    <string name="feedback_key">feedback</string>
    <string name="feedback_title">Feedback</string>
    <string name="gm_sys_motion_easing_accelerated">cubic-bezier(0.4, 0.0, 1.0, 1.0)</string>
    <string name="gm_sys_motion_easing_decelerated">cubic-bezier(0.0, 0.0, 0.2, 1.0)</string>
    <string name="gm_sys_motion_easing_emphasized">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string>
    <string name="gm_sys_motion_easing_linear">cubic-bezier(0.0, 0.0, 1.0, 1.0)</string>
    <string name="gm_sys_motion_easing_standard">cubic-bezier(0.4, 0.0, 0.2, 1.0)</string>
    <string name="install_voices_key">install_voices</string>
    <string name="install_voices_title">Install voice data</string>
    <string name="language_detection_options_key">language_detection_options</string>
    <string name="language_detection_title">Language detection</string>
    <string name="language_pack_download_notification_title">Downloading %1$s Update</string>
    <string name="loudness_key">loudness_key</string>
    <string name="loudness_summary">Make spoken text louder than normal so it\'s easier to hear over other audio that might be playing</string>
    <string name="loudness_title">Amplify speech volume</string>
    <string name="material_clock_display_divider">:</string>
    <string name="material_motion_easing_accelerated">cubic-bezier(0.4, 0.0, 1.0, 1.0)</string>
    <string name="material_motion_easing_decelerated">cubic-bezier(0.0, 0.0, 0.2, 1.0)</string>
    <string name="material_motion_easing_emphasized">path(M 0,0 C 0.05, 0, 0.133333, 0.06, 0.166666, 0.4 C 0.208333, 0.82, 0.25, 1, 1, 1)</string>
    <string name="material_motion_easing_linear">cubic-bezier(0.0, 0.0, 1.0, 1.0)</string>
    <string name="material_motion_easing_standard">cubic-bezier(0.4, 0.0, 0.2, 1.0)</string>
    <string name="mdd_download_notification_channel_name">Data Download Notification Channel</string>
    <string name="mdd_foreground_service_notification_title">Downloading</string>
    <string name="mdd_notification_action_cancel">Cancel</string>
    <string name="mdd_notification_download_failed">Download failed</string>
    <string name="mdd_notification_download_paused">Waiting for network connection</string>
    <string name="mdd_notification_download_paused_wifi">Waiting for WiFi connection</string>
    <string name="mtrl_checkbox_button_icon_path_checked">M14,18.2 11.4,15.6 10,17 14,21 22,13 20.6,11.6z</string>
    <string name="mtrl_checkbox_button_icon_path_group_name">icon</string>
    <string name="mtrl_checkbox_button_icon_path_indeterminate">M13.4,15 11,15 11,17 13.4,17 21,17 21,15z</string>
    <string name="mtrl_checkbox_button_icon_path_name">icon path</string>
    <string name="mtrl_checkbox_button_path_checked">M23,7H9C7.9,7,7,7.9,7,9v14c0,1.1,0.9,2,2,2h14c1.1,0,2-0.9,2-2V9C25,7.9,24.1,7,23,7z</string>
    <string name="mtrl_checkbox_button_path_group_name">button</string>
    <string name="mtrl_checkbox_button_path_name">button path</string>
    <string name="mtrl_checkbox_button_path_unchecked">M23,7H9C7.9,7,7,7.9,7,9v14c0,1.1,0.9,2,2,2h14c1.1,0,2-0.9,2-2V9C25,7.9,24.1,7,23,7z M23,23H9V9h14V23z</string>
    <string name="mtrl_checkbox_state_description_checked">Checked</string>
    <string name="mtrl_checkbox_state_description_indeterminate">Partially checked</string>
    <string name="mtrl_checkbox_state_description_unchecked">Not checked</string>
    <string name="multi_voice_pack_title">%1$s voices</string>
    <string name="not_set">Not set</string>
    <string name="offline_speech_category_key">offline_speech_category</string>
    <string name="offline_speech_category_title">Offline speech</string>
    <string name="open_source_licenses">Open Source Licenses</string>
    <string name="open_source_licenses_key">open_source_licenses</string>
    <string name="password_toggle_content_description">Show password</string>
    <string name="path_password_eye">M12,4.5C7,4.5 2.73,7.61 1,12c1.73,4.39 6,7.5 11,7.5s9.27,-3.11 11,-7.5c-1.73,-4.39 -6,-7.5 -11,-7.5zM12,17c-2.76,0 -5,-2.24 -5,-5s2.24,-5 5,-5 5,2.24 5,5 -2.24,5 -5,5zM12,9c-1.66,0 -3,1.34 -3,3s1.34,3 3,3 3,-1.34 3,-3 -1.34,-3 -3,-3z</string>
    <string name="path_password_eye_mask_strike_through">M2,4.27 L19.73,22 L22.27,19.46 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_eye_mask_visible">M2,4.27 L2,4.27 L4.54,1.73 L4.54,1.73 L4.54,1 L23,1 L23,23 L1,23 L1,4.27 Z</string>
    <string name="path_password_strike_through">M3.27,4.27 L19.74,20.74</string>
    <string name="pending_language_pack_details">Download in progress for %1$s update (%2$s)</string>
    <string name="pending_title">Downloading %1$s update</string>
    <string name="play_sample">Play sample text</string>
    <string name="play_sample_text_button_description">Play sample text for %1$s</string>
    <string name="preference_copied">\"%1$s\" copied to clipboard.</string>
    <string name="preferences_license_title">Third Party Notices</string>
    <string name="primes_marker">PrImEs</string>
    <string name="privacy_dashboard_permission_usage">Google Speech Recognition and Synthesis uses your device’s microphone to transcribe audio into text. The microphone indicator will display whenever an app utilizes this feature, ensuring you are aware of active audio transcription.</string>
    <string name="recognizer_service_label">Speech Recognition and Synthesis from Google</string>
    <string name="sample_text_synthesis_failure">Failed to synthesize an example for this voice.</string>
    <string name="stop_pending_download_prompt">Stop download</string>
    <string name="summary_collapsed_preference_list">%1$s, %2$s</string>
    <string name="superpacks_manifest_name">superpacks_manifest.json</string>
    <string name="transcription_app_audio_rationale_message">Google Speech Services converts audio to text and shares the text with this app.</string>
    <string name="transcription_app_big_ellipsis">• • •</string>
    <string name="transcription_app_common_google_logo">Google logo</string>
    <string name="transcription_app_network_error">Network not connected</string>
    <string name="transcription_app_no_speech_detected_error">Didn\'t catch that. Try speaking again.</string>
    <string name="transcription_app_permission_error">We need your permission to use the mic</string>
    <string name="transcription_app_start_speaking">Try saying something</string>
    <string name="transcription_app_tap_to_stop_listening">Tap to stop listening</string>
    <string name="transcription_app_tap_to_try_again">Tap to try again</string>
    <string name="transcription_app_try_again">Try again</string>
    <string name="transcription_app_voice_ime_audio_rationale_message">Google Speech Services converts audio to text and shares the text with this app.</string>
    <string name="transcription_app_voice_ime_can_not_be_used">Google Voice Typing cannot be used for this field.</string>
    <string name="transcription_app_voice_ime_hint_prev_ime">Keyboard input</string>
    <string name="transcription_app_voice_ime_hint_remove_last_word">Undo</string>
    <string name="transcription_app_voice_ime_hint_settings">Settings</string>
    <string name="transcription_app_voice_ime_language_label">Automatic</string>
    <string name="transcription_app_voice_ime_service_label">Google Voice Typing</string>
    <string name="transcription_app_voice_ime_tap_to_pause">Tap to pause</string>
    <string name="transcription_app_voice_ime_tap_to_speak">Tap to speak</string>
    <string name="transcription_app_voice_search_unavailable_error">Voice search isn\'t available</string>
    <string name="transcription_categoryTitle_voice_languages">Voice Typing Language</string>
    <string name="transcription_multiSelectDialogButton_negative">Cancel</string>
    <string name="transcription_multiSelectDialogButton_positive">Save</string>
    <string name="transcription_prefSummary_default">Default language</string>
    <string name="transcription_prefSummary_language">Primary language: %1$s</string>
    <string name="transcription_prefSummary_languages_multiple">Languages: %1$s</string>
    <string name="transcription_prefSummary_languages_single">Language: %1$s</string>
    <string name="transcription_prefSummary_profanityFilter">Hide recognized offensive voice results</string>
    <string name="transcription_prefTitle_language">Primary language</string>
    <string name="transcription_prefTitle_profanityFilter">Block offensive words</string>
    <string name="transcription_prefTitle_voice_languages">Languages</string>
    <string name="tts_default_example">This is an example of speech synthesis.</string>
    <string name="tts_settings_label">Google TTS Options</string>
    <string name="uninstall_language_pack_details">Without this update, you won\'t be able to use speech recognition offline with this app and others</string>
    <string name="uninstall_prompt">Uninstall</string>
    <string name="uninstall_title">Uninstall %1$s update</string>
    <string name="v7_preference_off">OFF</string>
    <string name="v7_preference_on">ON</string>
    <string name="voice_data_install_title">Google TTS voice data</string>
    <string name="voice_entity_status_voice">Voice %1$s</string>
    <string name="voice_entry_cancel_download_desc">Cancel voice pack download</string>
    <string name="voice_entry_delete_desc">Delete voice pack</string>
    <string name="voice_entry_download_desc">Download voice pack</string>
    <string name="voice_entry_overflow_desc">Additional voice pack actions</string>
    <string name="voice_pack_status_canceling">Canceling…</string>
    <string name="voice_pack_status_downloadable">Download (~%1$s)</string>
    <string name="voice_pack_status_downloading">Downloading…</string>
    <string name="voice_pack_status_size">Size: ~%1$s</string>
    <string name="voice_pack_status_updatable">Update available</string>
    <string name="voice_pack_uninstall_dialog">Uninstall this voice?</string>
    <string name="voice_pack_uninstall_keep">Keep</string>
    <string name="voice_pack_uninstall_uninstall">Uninstall</string>
    <string name="voices_list_next_name">voices-list-dsig.pb</string>
    <string name="your_languages_key">your_languages</string>
    <string name="your_languages_title">Your languages</string>
</resources>
