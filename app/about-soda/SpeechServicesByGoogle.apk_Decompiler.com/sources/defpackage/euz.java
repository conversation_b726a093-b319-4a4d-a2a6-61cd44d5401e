package defpackage;

import java.util.concurrent.Executor;

/* renamed from: euz  reason: default package */
/* compiled from: PG */
public final class euz {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/store/session/SessionAudioStore");
    private static final eld h;
    public final Executor b;
    public final Executor c;
    public final hme d;
    public final hme e;
    public final dyt f;
    public final int g;
    private final jqs i;
    private final ehg j;
    private final eum k;

    static {
        htk l = eld.c.l();
        jnu.d(l, "newBuilder(...)");
        jnu.e(l, "builder");
        ele ele = ele.MODE_PERSISTENT_IF_ALLOWED;
        jnu.e(ele, "value");
        if (!l.b.B()) {
            l.u();
        }
        eld eld = (eld) l.b;
        eld.b = ele.d;
        eld.a |= 1;
        htq o = l.r();
        jnu.d(o, "build(...)");
        h = (eld) o;
    }

    public euz(jqs jqs, Executor executor, Executor executor2, eum eum, ehg ehg, hme hme, hme hme2, dyt dyt, int i2) {
        jnu.e(jqs, "lightweightScope");
        jnu.e(executor, "lightweightExecutor");
        jnu.e(executor2, "audioReadBlockingExecutor");
        jnu.e(eum, "audioStore");
        jnu.e(ehg, "clientInfo");
        jnu.e(dyt, "params");
        this.i = jqs;
        this.b = executor;
        this.c = executor2;
        this.k = eum;
        this.j = ehg;
        this.d = hme;
        this.e = hme2;
        this.f = dyt;
        this.g = i2;
    }

    public final eui a(String str) {
        htm htm = (htm) ebd.e.l();
        jnu.d(htm, "newBuilder(...)");
        jnu.e(htm, "builder");
        int i2 = this.f.c;
        if (!htm.b.B()) {
            htm.u();
        }
        ebd ebd = (ebd) htm.b;
        ebd.a |= 1;
        ebd.b = i2;
        int bitCount = Integer.bitCount(this.f.d);
        if (!htm.b.B()) {
            htm.u();
        }
        ebd ebd2 = (ebd) htm.b;
        ebd2.a |= 2;
        ebd2.c = bitCount;
        int i3 = this.f.e;
        if (!htm.b.B()) {
            htm.u();
        }
        eum eum = this.k;
        ebd ebd3 = (ebd) htm.b;
        ebd3.a |= 4;
        ebd3.d = i3;
        gnk gnk = elg.a;
        jnu.d(gnk, "clientInfo");
        doe.e(gnk, this.j, htm);
        gnk gnk2 = elg.b;
        jnu.d(gnk2, "source");
        doe.e(gnk2, str, htm);
        gnk gnk3 = elg.c;
        jnu.d(gnk3, "token");
        doe.e(gnk3, String.valueOf(this.g), htm);
        gnk gnk4 = elf.a;
        jnu.d(gnk4, "storingDestination");
        doe.e(gnk4, h, htm);
        htq o = htm.r();
        jnu.d(o, "build(...)");
        ebd ebd4 = (ebd) o;
        jnu.e(ebd4, "metadata");
        gnk gnk5 = elf.a;
        jnu.d(gnk5, "storingDestination");
        Object aR = ftc.aR(ebd4, gnk5);
        jnu.d(aR, "get(...)");
        if (eum.a((eld) aR)) {
            return new eul(eum, ebd4);
        }
        return eum.a;
    }

    public final jqz b(String str, dya dya) {
        jqh jqh = new jqh();
        eui a2 = a(str);
        jnz jnz = new jnz();
        jnz.a = dya.k(new euy(a2, jqh, jnz, this));
        return jqh;
    }

    public final void c() {
        if (!this.k.a(h)) {
            ((hby) a.f().h(hdg.a, "ALT.SessionAudioStore").j("com/google/android/libraries/search/audio/store/session/SessionAudioStore", "maybeStore", 57, "SessionAudioStore.kt")).s("#audio# skipping session(%s) storing process, not enabled", this.g);
            return;
        }
        jqs jqs = this.i;
        jlw jlw = jlw.a;
        jqw.p(jqs, ftd.Y(jlw), jqt.DEFAULT, new euw((jlr) null, this));
    }
}
