package defpackage;

import java.util.concurrent.Callable;

/* renamed from: ctw  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ctw implements Callable {
    private final /* synthetic */ int a;

    public /* synthetic */ ctw(int i) {
        this.a = i;
    }

    public final Object call() {
        switch (this.a) {
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                boolean z = cwm.a;
                return null;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                boolean z2 = cwm.a;
                return null;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                boolean z3 = cwm.a;
                return null;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                boolean z4 = cwm.a;
                return null;
            default:
                return null;
        }
    }
}
