package defpackage;

import android.os.Parcel;
import android.os.Parcelable;
import j$.util.Objects;
import java.util.Arrays;

/* renamed from: aqq  reason: default package */
/* compiled from: PG */
public final class aqq extends aqy {
    public static final Parcelable.Creator CREATOR = new aa(18);
    public final String a;
    public final String b;
    public final int c;
    public final byte[] d;

    public aqq(Parcel parcel) {
        super("APIC");
        String readString = parcel.readString();
        int i = agh.a;
        this.a = readString;
        this.b = parcel.readString();
        this.c = parcel.readInt();
        this.d = parcel.createByteArray();
    }

    public final void a(aei aei) {
        aei.a(this.d, this.c);
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj != null && getClass() == obj.getClass()) {
            aqq aqq = (aqq) obj;
            if (this.c != aqq.c || !Objects.equals(this.a, aqq.a) || !Objects.equals(this.b, aqq.b) || !Arrays.equals(this.d, aqq.d)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        String str = this.a;
        int i2 = 0;
        if (str != null) {
            i = str.hashCode();
        } else {
            i = 0;
        }
        int i3 = this.c;
        String str2 = this.b;
        if (str2 != null) {
            i2 = str2.hashCode();
        }
        return ((((((i3 + 527) * 31) + i) * 31) + i2) * 31) + Arrays.hashCode(this.d);
    }

    public final String toString() {
        return this.f + ": mimeType=" + this.a + ", description=" + this.b;
    }

    public final void writeToParcel(Parcel parcel, int i) {
        parcel.writeString(this.a);
        parcel.writeString(this.b);
        parcel.writeInt(this.c);
        parcel.writeByteArray(this.d);
    }

    public aqq(String str, String str2, int i, byte[] bArr) {
        super("APIC");
        this.a = str;
        this.b = str2;
        this.c = i;
        this.d = bArr;
    }
}
