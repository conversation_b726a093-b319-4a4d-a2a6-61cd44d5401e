package defpackage;

import android.content.ComponentName;
import android.content.Context;
import android.content.pm.PackageManager;

/* renamed from: bif  reason: default package */
/* compiled from: PG */
public final class bif {
    static {
        bbk.b("PackageManagerHelper");
    }

    public static void a(Context context, Class cls, boolean z) {
        int i;
        try {
            int componentEnabledSetting = context.getPackageManager().getComponentEnabledSetting(new ComponentName(context, cls.getName()));
            boolean z2 = false;
            if (componentEnabledSetting != 0) {
                if (componentEnabledSetting == 1) {
                    z2 = true;
                }
            }
            if (z == z2) {
                bbk.a();
                cls.getName();
                return;
            }
            PackageManager packageManager = context.getPackageManager();
            ComponentName componentName = new ComponentName(context, cls.getName());
            if (true != z) {
                i = 2;
            } else {
                i = 1;
            }
            packageManager.setComponentEnabledSetting(componentName, i, 1);
            bbk.a();
            cls.getName();
        } catch (Exception unused) {
            bbk.a();
            cls.getName();
        }
    }
}
