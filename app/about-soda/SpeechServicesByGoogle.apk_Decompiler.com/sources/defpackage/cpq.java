package defpackage;

import java.util.Map;

/* renamed from: cpq  reason: default package */
/* compiled from: PG */
public final class cpq implements iiu {
    private final jjk a;

    public cpq(jjk jjk) {
        this.a = jjk;
    }

    /* renamed from: a */
    public final cpp b() {
        cpp cpp;
        Map map = (Map) ((iiv) this.a).a;
        int size = map.size();
        if (size == 0) {
            cpp = new cpf();
        } else if (size == 1) {
            cpp = (cpp) fvf.T(map.keySet());
        } else {
            throw new IllegalArgumentException("More than 1 ThreadMonitoringConfiguration");
        }
        cpp.d();
        cpp.d();
        cpp.d();
        fvf.aK(true, "ThreadMonitoringConfiguration.threadCountSamplesPerThousand() must be between [0, %s] but found %s", 1000, 1);
        cpp.e();
        cpp.e();
        fvf.aH(true, "ThreadMonitoringConfiguration.threadCountThreshold must be positive but found %s", 1000);
        cpp.a();
        cpp.a();
        cpp.a();
        fvf.aK(true, "ThreadMonitoringConfiguration.queueSizeSamplesPerThousand() must be between [0, %s] but found %s", 1000, 1);
        cpp.b();
        cpp.b();
        fvf.aH(true, "ThreadMonitoringConfiguration.queueSizeThreshold must be positive but found %s", 1000);
        cpp.c();
        cpp.c();
        cpp.c();
        fvf.aK(true, "ThreadMonitoringConfiguration.taskTimeoutSamplesPerThousand() must be between [0, %s] but found %s", 1000, 1);
        cpp.f();
        cpp.f();
        fvf.aI(true, "ThreadMonitoringConfiguration.taskTimeoutDuration must be positive but found %s", 1);
        hzz.u(cpp);
        return cpp;
    }
}
