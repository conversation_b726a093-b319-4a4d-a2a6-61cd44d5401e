package defpackage;

import android.view.View;
import j$.util.Collection;
import j$.util.function.Predicate$CC;
import java.io.IOException;
import java.util.Locale;
import java.util.function.Predicate;
import java.util.zip.ZipEntry;

/* renamed from: bod  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bod implements Predicate {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ bod(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    public final /* synthetic */ Predicate and(Predicate predicate) {
        switch (this.b) {
            case 0:
                return Predicate$CC.$default$and(this, predicate);
            case 1:
                return Predicate$CC.$default$and(this, predicate);
            case 2:
                return Predicate$CC.$default$and(this, predicate);
            case 3:
                return Predicate$CC.$default$and(this, predicate);
            case 4:
                return Predicate$CC.$default$and(this, predicate);
            case 5:
                return Predicate$CC.$default$and(this, predicate);
            case 6:
                return Predicate$CC.$default$and(this, predicate);
            case 7:
                return Predicate$CC.$default$and(this, predicate);
            case 8:
                return Predicate$CC.$default$and(this, predicate);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return Predicate$CC.$default$and(this, predicate);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return Predicate$CC.$default$and(this, predicate);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return Predicate$CC.$default$and(this, predicate);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return Predicate$CC.$default$and(this, predicate);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return Predicate$CC.$default$and(this, predicate);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return Predicate$CC.$default$and(this, predicate);
            case 15:
                return Predicate$CC.$default$and(this, predicate);
            case 16:
                return Predicate$CC.$default$and(this, predicate);
            case 17:
                return Predicate$CC.$default$and(this, predicate);
            case 18:
                return Predicate$CC.$default$and(this, predicate);
            case 19:
                return Predicate$CC.$default$and(this, predicate);
            default:
                return Predicate$CC.$default$and(this, predicate);
        }
    }

    public final /* synthetic */ Predicate negate() {
        switch (this.b) {
            case 0:
                return Predicate$CC.$default$negate(this);
            case 1:
                return Predicate$CC.$default$negate(this);
            case 2:
                return Predicate$CC.$default$negate(this);
            case 3:
                return Predicate$CC.$default$negate(this);
            case 4:
                return Predicate$CC.$default$negate(this);
            case 5:
                return Predicate$CC.$default$negate(this);
            case 6:
                return Predicate$CC.$default$negate(this);
            case 7:
                return Predicate$CC.$default$negate(this);
            case 8:
                return Predicate$CC.$default$negate(this);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return Predicate$CC.$default$negate(this);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return Predicate$CC.$default$negate(this);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return Predicate$CC.$default$negate(this);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return Predicate$CC.$default$negate(this);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return Predicate$CC.$default$negate(this);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return Predicate$CC.$default$negate(this);
            case 15:
                return Predicate$CC.$default$negate(this);
            case 16:
                return Predicate$CC.$default$negate(this);
            case 17:
                return Predicate$CC.$default$negate(this);
            case 18:
                return Predicate$CC.$default$negate(this);
            case 19:
                return Predicate$CC.$default$negate(this);
            default:
                return Predicate$CC.$default$negate(this);
        }
    }

    public final /* synthetic */ Predicate or(Predicate predicate) {
        switch (this.b) {
            case 0:
                return Predicate$CC.$default$or(this, predicate);
            case 1:
                return Predicate$CC.$default$or(this, predicate);
            case 2:
                return Predicate$CC.$default$or(this, predicate);
            case 3:
                return Predicate$CC.$default$or(this, predicate);
            case 4:
                return Predicate$CC.$default$or(this, predicate);
            case 5:
                return Predicate$CC.$default$or(this, predicate);
            case 6:
                return Predicate$CC.$default$or(this, predicate);
            case 7:
                return Predicate$CC.$default$or(this, predicate);
            case 8:
                return Predicate$CC.$default$or(this, predicate);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return Predicate$CC.$default$or(this, predicate);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return Predicate$CC.$default$or(this, predicate);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return Predicate$CC.$default$or(this, predicate);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return Predicate$CC.$default$or(this, predicate);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return Predicate$CC.$default$or(this, predicate);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return Predicate$CC.$default$or(this, predicate);
            case 15:
                return Predicate$CC.$default$or(this, predicate);
            case 16:
                return Predicate$CC.$default$or(this, predicate);
            case 17:
                return Predicate$CC.$default$or(this, predicate);
            case 18:
                return Predicate$CC.$default$or(this, predicate);
            case 19:
                return Predicate$CC.$default$or(this, predicate);
            default:
                return Predicate$CC.$default$or(this, predicate);
        }
    }

    /* JADX WARNING: type inference failed for: r0v44, types: [java.util.Collection, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v49, types: [java.util.Collection, java.lang.Object] */
    public final boolean test(Object obj) {
        switch (this.b) {
            case 0:
                View view = (View) obj;
                if (view == this.a || !yi.x(view)) {
                    return false;
                }
                return true;
            case 1:
                View view2 = (View) obj;
                if (view2 == this.a || yi.C(view2) || !yi.x(view2) || !yi.D(view2)) {
                    return false;
                }
                return true;
            case 2:
                View view3 = (View) obj;
                if (view3 == this.a || !view3.isSelected() || !yi.x(view3)) {
                    return false;
                }
                return true;
            case 3:
                View view4 = (View) obj;
                if (view4 == this.a || !ag$$ExternalSyntheticApiModelOutline0.m(view4) || !yi.x(view4)) {
                    return false;
                }
                return true;
            case 4:
                bsk bsk = (bsk) obj;
                brr brr = (brr) this.a;
                if (brr.j() && bsk.v()) {
                    return false;
                }
                if (!brr.l() || bsk.v()) {
                    return true;
                }
                return false;
            case 5:
                return brv.j((Locale) this.a, (String) ((bsk) obj).i().get(0));
            case 6:
                return brv.j(Locale.forLanguageTag(((String) this.a).replace('_', '-')), (String) ((bsk) obj).i().get(0));
            case 7:
                return ((gyo) this.a).contains(((bsk) obj).p());
            case 8:
                return Collection.EL.stream(((ext) this.a).c).noneMatch(new bod((exo) obj, 10));
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                exo exo = (exo) obj;
                hdf hdf = byr.a;
                Locale locale = exo.a;
                exo exo2 = (exo) this.a;
                if (!locale.equals(exo2.a) || exo.b != exo2.b) {
                    return false;
                }
                return true;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                hdf hdf2 = byr.a;
                return ((exo) obj).a.equals(((exo) this.a).a);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                hdf hdf3 = byr.a;
                return ((exo) obj).a.equals(((exo) this.a).a);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                try {
                    return dhj.e((ZipEntry) this.a).startsWith((String) obj);
                } catch (IOException unused) {
                    return false;
                }
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                hdf hdf4 = exy.a;
                if (!((eyv) obj).equals(this.a)) {
                    return true;
                }
                return false;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                hdf hdf5 = exy.a;
                if (!((gyo) this.a).contains((exo) obj)) {
                    return true;
                }
                return false;
            case 15:
                return Collection.EL.stream(this.a).noneMatch(new bod((exo) obj, 16));
            case 16:
                hdf hdf6 = eyc.a;
                return ((exo) obj).d((exo) this.a);
            case 17:
                exo exo3 = (exo) obj;
                boolean noneMatch = Collection.EL.stream(this.a).noneMatch(new bod(exo3, 19));
                if (noneMatch) {
                    ((hdc) ((hdc) eyg.a.f()).j("com/google/android/libraries/speech/modeldownload/languagepacks/defaultmanager/DefaultLanguagePackManagerImpl", "calculateRolledBackPacks", 271, "DefaultLanguagePackManagerImpl.java")).u("Rolling back previously requested pack that is no longer available: %s", exo3);
                }
                return noneMatch;
            case 18:
                return ((gxq) this.a).contains((exo) obj);
            case 19:
                exo exo4 = (exo) obj;
                hdf hdf7 = eyg.a;
                exo exo5 = (exo) this.a;
                if (!exo4.d(exo5) || exo4.h != exo5.h) {
                    return false;
                }
                return true;
            default:
                int intValue = ((Integer) obj).intValue();
                hrt hrt = (hrt) this.a;
                return eym.d(intValue, hrt.b, hrt.d);
        }
    }
}
