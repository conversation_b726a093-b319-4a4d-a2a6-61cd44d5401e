package defpackage;

/* renamed from: ctp  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ctp implements hkn {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    private final /* synthetic */ int c;

    public /* synthetic */ ctp(Object obj, Object obj2, int i) {
        this.c = i;
        this.a = obj;
        this.b = obj2;
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v125, resolved type: hme[]} */
    /* JADX WARNING: type inference failed for: r1v3, types: [hco, hdc] */
    /* JADX WARNING: type inference failed for: r0v7, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v9, types: [java.lang.Object, dfa] */
    /* JADX WARNING: type inference failed for: r0v80, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v62, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v67, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v46, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v54, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v57, types: [java.lang.Object, czd] */
    /* JADX WARNING: type inference failed for: r1v73, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v58, types: [java.lang.Object, java.lang.Iterable] */
    /* JADX WARNING: type inference failed for: r0v128, types: [jne, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v130, types: [jne, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v132, types: [jne, java.lang.Object] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x013d  */
    /* JADX WARNING: Removed duplicated region for block: B:48:0x01f9  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme a() {
        /*
            r13 = this;
            int r0 = r13.c
            r1 = 17301642(0x108008a, float:2.4979642E-38)
            r2 = 6
            r3 = 5
            r4 = 8
            r5 = 3
            r6 = 0
            r7 = 2
            r8 = 1
            r9 = 0
            java.lang.Boolean r10 = java.lang.Boolean.valueOf(r9)
            switch(r0) {
                case 0: goto L_0x072f;
                case 1: goto L_0x06b4;
                case 2: goto L_0x067d;
                case 3: goto L_0x061b;
                case 4: goto L_0x05a7;
                case 5: goto L_0x0476;
                case 6: goto L_0x045e;
                case 7: goto L_0x03bb;
                case 8: goto L_0x034a;
                case 9: goto L_0x0330;
                case 10: goto L_0x0308;
                case 11: goto L_0x02d5;
                case 12: goto L_0x02a0;
                case 13: goto L_0x0291;
                case 14: goto L_0x027a;
                case 15: goto L_0x01ff;
                case 16: goto L_0x008b;
                case 17: goto L_0x003c;
                case 18: goto L_0x002f;
                case 19: goto L_0x0022;
                default: goto L_0x0015;
            }
        L_0x0015:
            java.lang.Object r0 = r13.a
            java.lang.Object r1 = r13.b
            egw r1 = (defpackage.egw) r1
            jqs r1 = r1.c
            hme r0 = defpackage.jqw.z(r1, r0)
            return r0
        L_0x0022:
            java.lang.Object r0 = r13.a
            java.lang.Object r1 = r13.b
            egu r1 = (defpackage.egu) r1
            jqs r1 = r1.d
            hme r0 = defpackage.jqw.z(r1, r0)
            return r0
        L_0x002f:
            java.lang.Object r0 = r13.a
            java.lang.Object r1 = r13.b
            egi r1 = (defpackage.egi) r1
            jqs r1 = r1.c
            hme r0 = defpackage.jqw.z(r1, r0)
            return r0
        L_0x003c:
            java.lang.Object r0 = r13.b
            dwm r0 = (defpackage.dwm) r0
            ihn r0 = r0.c
            java.lang.Object r0 = r0.b()
            r2 = r0
            bmu r2 = (defpackage.bmu) r2
            java.lang.Object r0 = r2.b
            dxv r0 = (defpackage.dxv) r0
            grh r0 = r0.b
            bps r1 = new bps
            r3 = 17
            r1.<init>(r3)
            r0.d(r1)
            java.lang.Object r4 = r1.a()
            java.lang.Object r0 = r2.b
            dxv r0 = (defpackage.dxv) r0
            grh r0 = r0.c
            bps r1 = new bps
            r3 = 18
            r1.<init>(r3)
            r0.d(r1)
            java.lang.Object r5 = r1.a()
            hme[] r0 = new defpackage.hme[r7]
            r0[r9] = r4
            r0[r8] = r5
            bzl r0 = defpackage.ftd.ae(r0)
            java.lang.Object r3 = r13.a
            cvs r7 = new cvs
            r6 = 7
            r1 = r7
            r1.<init>((java.lang.Object) r2, (java.lang.Object) r3, (java.lang.Object) r4, (java.lang.Object) r5, (int) r6)
            hld r1 = defpackage.hld.a
            gpa r0 = r0.B(r7, r1)
            return r0
        L_0x008b:
            java.lang.Object r0 = r13.a
            drh r0 = (defpackage.drh) r0
            ihn r1 = r0.c
            java.lang.Object r1 = r1.b()
            drd r1 = (defpackage.drd) r1
            boolean r1 = r1.b()
            if (r1 != 0) goto L_0x00a1
            hme r0 = defpackage.hma.a
            goto L_0x01fb
        L_0x00a1:
            ihn r1 = r0.b
            java.lang.Object r1 = r1.b()
            drb r1 = (defpackage.drb) r1
            drs r1 = r0.d
            boolean r1 = r1.c()
            if (r1 == 0) goto L_0x00b5
            hme r0 = defpackage.hma.a
            goto L_0x01fb
        L_0x00b5:
            java.lang.Object r1 = r13.b
            drs r2 = r0.d
            r2.b()
            defpackage.fnk.b()
            r2 = r1
            drf r2 = (defpackage.drf) r2
            int r4 = r2.a()
            if (r4 != 0) goto L_0x00ca
        L_0x00c8:
            r1 = r6
            goto L_0x0133
        L_0x00ca:
            dfj r4 = new dfj
            r4.<init>(r3)
            java.util.List r3 = r2.d
            monitor-enter(r3)
            r5 = r1
            drf r5 = (defpackage.drf) r5     // Catch:{ all -> 0x01fc }
            java.util.List r5 = r5.d     // Catch:{ all -> 0x01fc }
            java.util.Collections.sort(r5, r4)     // Catch:{ all -> 0x01fc }
            r5 = r1
            drf r5 = (defpackage.drf) r5     // Catch:{ all -> 0x01fc }
            dqz r5 = r5.b     // Catch:{ all -> 0x01fc }
            drf r1 = (defpackage.drf) r1     // Catch:{ all -> 0x01fc }
            java.util.List r1 = r1.d     // Catch:{ all -> 0x01fc }
            r5.b(r1)     // Catch:{ all -> 0x01fc }
            monitor-exit(r3)     // Catch:{ all -> 0x01fc }
            java.util.Map r1 = r2.c
            java.util.ArrayList r3 = new java.util.ArrayList
            java.util.Set r1 = r1.keySet()
            r3.<init>(r1)
            java.util.Collections.sort(r3, r4)
            dqz r1 = r2.b
            r1.b(r3)
            dqz r1 = r2.b
            dra r2 = new dra
            r2.<init>(r1)
            java.util.ArrayList r1 = new java.util.ArrayList
            r1.<init>()
            dqz r3 = r2.a
            r4 = 0
            r2.a(r3, r4, r1)
            int r2 = r1.size()
            if (r2 != r8) goto L_0x012f
            hca r1 = defpackage.djs.a
            hco r1 = r1.c()
            hby r1 = (defpackage.hby) r1
            java.lang.String r2 = "SpanProtoGenerator.java"
            java.lang.String r3 = "com/google/android/libraries/performance/primes/metrics/trace/SpanProtoGenerator"
            java.lang.String r4 = "generate"
            r5 = 71
            hco r1 = r1.j(r3, r4, r5, r2)
            hby r1 = (defpackage.hby) r1
            java.lang.String r2 = "No other span except for root span. Dropping trace..."
            r1.r(r2)
            goto L_0x00c8
        L_0x012f:
            java.util.List r1 = j$.util.DesugarCollections.unmodifiableList(r1)
        L_0x0133:
            if (r1 == 0) goto L_0x01f9
            boolean r2 = r1.isEmpty()
            if (r2 == 0) goto L_0x013d
            goto L_0x01f9
        L_0x013d:
            jzs r2 = defpackage.jzs.f
            htk r2 = r2.l()
            java.util.UUID r3 = java.util.UUID.randomUUID()
            long r3 = r3.getLeastSignificantBits()
            htq r5 = r2.b
            boolean r5 = r5.B()
            if (r5 != 0) goto L_0x0156
            r2.u()
        L_0x0156:
            htq r5 = r2.b
            r7 = r5
            jzs r7 = (defpackage.jzs) r7
            int r10 = r7.a
            r8 = r8 | r10
            r7.a = r8
            r7.b = r3
            boolean r3 = r5.B()
            if (r3 != 0) goto L_0x016b
            r2.u()
        L_0x016b:
            htq r3 = r2.b
            jzs r3 = (defpackage.jzs) r3
            r3.c()
            huf r3 = r3.d
            defpackage.hrz.g(r1, r3)
            htq r1 = r2.r()
            jzs r1 = (defpackage.jzs) r1
            huf r2 = r1.d
            int r2 = r2.size()
            if (r2 <= 0) goto L_0x018f
            huf r2 = r1.d
            java.lang.Object r2 = r2.get(r9)
            jzt r2 = (defpackage.jzt) r2
            java.lang.String r6 = r2.b
        L_0x018f:
            cxm r2 = r0.f
            long r2 = r2.a(r6)
            r4 = -1
            int r4 = (r2 > r4 ? 1 : (r2 == r4 ? 0 : -1))
            if (r4 != 0) goto L_0x019e
            hme r0 = defpackage.hma.a
            goto L_0x01fb
        L_0x019e:
            kbc r4 = defpackage.kbc.y
            htk r4 = r4.l()
            htq r5 = r4.b
            boolean r5 = r5.B()
            if (r5 != 0) goto L_0x01af
            r4.u()
        L_0x01af:
            htq r5 = r4.b
            kbc r5 = (defpackage.kbc) r5
            r1.getClass()
            r5.l = r1
            int r7 = r5.a
            r7 = r7 | 2048(0x800, float:2.87E-42)
            r5.a = r7
            htq r4 = r4.r()
            kbc r4 = (defpackage.kbc) r4
            hca r5 = defpackage.djs.a
            hco r5 = r5.d()
            hby r5 = (defpackage.hby) r5
            java.lang.String r7 = "TraceMetricServiceImpl.java"
            java.lang.String r8 = "com/google/android/libraries/performance/primes/metrics/trace/TraceMetricServiceImpl"
            java.lang.String r9 = "endTracingIfStarted"
            r10 = 185(0xb9, float:2.59E-43)
            hco r5 = r5.j(r8, r9, r10, r7)
            hby r5 = (defpackage.hby) r5
            long r7 = r1.b
            java.lang.String r1 = "Recording trace %d: %s"
            r5.z(r1, r7, r6)
            cxm r0 = r0.f
            ffg r1 = defpackage.dma.a()
            r1.i(r4)
            java.lang.Long r2 = java.lang.Long.valueOf(r2)
            r1.e = r2
            dma r1 = r1.e()
            hme r0 = r0.b(r1)
            goto L_0x01fb
        L_0x01f9:
            hme r0 = defpackage.hma.a
        L_0x01fb:
            return r0
        L_0x01fc:
            r0 = move-exception
            monitor-exit(r3)     // Catch:{ all -> 0x01fc }
            throw r0
        L_0x01ff:
            java.lang.Object r0 = r13.b
            r1 = r0
            dqf r1 = (defpackage.dqf) r1
            cxm r2 = r1.c
            boolean r2 = r2.c(r6)
            if (r2 != 0) goto L_0x0210
            hme r0 = defpackage.hma.a
            goto L_0x0279
        L_0x0210:
            java.lang.Object r2 = r13.a
            r3 = r2
            htk r3 = (defpackage.htk) r3
            htq r3 = r3.b
            jzw r3 = (defpackage.jzw) r3
            int r4 = r3.r
            int r10 = defpackage.a.y(r4)
            if (r10 != 0) goto L_0x0222
            goto L_0x0224
        L_0x0222:
            if (r10 == r5) goto L_0x022d
        L_0x0224:
            int r4 = defpackage.a.y(r4)
            if (r4 != 0) goto L_0x022b
            goto L_0x0236
        L_0x022b:
            if (r4 != r7) goto L_0x0236
        L_0x022d:
            int r3 = r3.a
            r3 = r3 & 16
            if (r3 != 0) goto L_0x0236
            hme r0 = defpackage.hma.a
            goto L_0x0279
        L_0x0236:
            ihn r1 = r1.a
            java.lang.Object r1 = r1.b()
            dpw r1 = (defpackage.dpw) r1
            grh r3 = r1.b
            dom r4 = new dom
            r4.<init>((byte[]) r6)
            r3.d(r4)
            grh r1 = r1.a
            gqd r3 = defpackage.gqd.a
            hme r3 = defpackage.hfc.K(r3)
            don r4 = new don
            r4.<init>()
            r1.d(r4)
            gqd r1 = defpackage.gqd.a
            hme r1 = defpackage.hfc.K(r1)
            hme[] r4 = new defpackage.hme[r7]
            r4[r9] = r3
            r4[r8] = r1
            ipt r10 = defpackage.hfc.Z(r4)
            cvs r11 = new cvs
            r9 = 5
            r4 = r11
            r5 = r0
            r6 = r2
            r7 = r3
            r8 = r1
            r4.<init>((java.lang.Object) r5, (java.lang.Object) r6, (java.lang.Object) r7, (defpackage.hme) r8, (int) r9)
            hld r0 = defpackage.hld.a
            hme r0 = r10.b(r11, r0)
        L_0x0279:
            return r0
        L_0x027a:
            java.lang.Object r0 = r13.b
            dpt r0 = (defpackage.dpt) r0
            ihn r1 = r0.d
            java.lang.Object r1 = r1.b()
            dpq r1 = (defpackage.dpq) r1
            java.lang.Object r2 = r13.a
            kbc r1 = r1.c(r2)
            hme r0 = r0.b(r1)
            return r0
        L_0x0291:
            java.lang.Object r0 = r13.a
            java.lang.Object r1 = r13.b
            com.google.android.libraries.mdi.download.workmanager.workers.PeriodicWorker r1 = (com.google.android.libraries.mdi.download.workmanager.workers.PeriodicWorker) r1
            cto r1 = r1.e
            java.lang.String r0 = (java.lang.String) r0
            hme r0 = r1.f(r0)
            return r0
        L_0x02a0:
            java.lang.Object r0 = r13.a
            java.lang.Object r1 = r13.b
            r2 = r1
            cyw r2 = (defpackage.cyw) r2     // Catch:{ Exception -> 0x02c2 }
            java.lang.Object r2 = r2.d     // Catch:{ Exception -> 0x02c2 }
            r2.remove(r0)     // Catch:{ Exception -> 0x02c2 }
            r2 = r1
            cyw r2 = (defpackage.cyw) r2     // Catch:{ Exception -> 0x02c2 }
            java.lang.Object r2 = r2.a     // Catch:{ Exception -> 0x02c2 }
            cyw r1 = (defpackage.cyw) r1     // Catch:{ Exception -> 0x02c2 }
            java.lang.Object r1 = r1.d     // Catch:{ Exception -> 0x02c2 }
            int r1 = r1.size()     // Catch:{ Exception -> 0x02c2 }
            r3 = r0
            java.lang.String r3 = (java.lang.String) r3     // Catch:{ Exception -> 0x02c2 }
            r2.b(r3, r1)     // Catch:{ Exception -> 0x02c2 }
            hme r0 = defpackage.hma.a
            goto L_0x02d4
        L_0x02c2:
            r1 = move-exception
            java.lang.Object[] r2 = new java.lang.Object[r7]
            java.lang.String r3 = "DownloadFutureMap"
            r2[r9] = r3
            r2[r8] = r0
            java.lang.String r0 = "%s: Failed to remove download future (%s) from map"
            defpackage.cyh.j(r1, r0, r2)
            hme r0 = defpackage.hfc.J(r1)
        L_0x02d4:
            return r0
        L_0x02d5:
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
        L_0x02da:
            java.lang.Object r1 = r13.b
            int r2 = r1.size()
            if (r9 >= r2) goto L_0x0303
            java.lang.Object r2 = r13.a
            java.lang.Object r1 = r1.get(r9)
            ctg r1 = (defpackage.ctg) r1
            java.lang.Object r2 = r2.get(r9)
            java.util.concurrent.Future r2 = (java.util.concurrent.Future) r2
            java.lang.Object r2 = defpackage.hfc.S(r2)
            csx r2 = (defpackage.csx) r2
            if (r2 == 0) goto L_0x0300
            cxg r3 = new cxg
            r3.<init>(r1, r2)
            r0.add(r3)
        L_0x0300:
            int r9 = r9 + 1
            goto L_0x02da
        L_0x0303:
            hme r0 = defpackage.hfc.K(r0)
            return r0
        L_0x0308:
            java.lang.Object r0 = r13.b
            java.lang.Object r1 = r13.a
            cvy r1 = (defpackage.cvy) r1
            ctg r0 = (defpackage.ctg) r0
            hme r2 = r1.g(r0, r9)
            hme r0 = r1.g(r0, r8)
            hme[] r3 = new defpackage.hme[r7]
            r3[r9] = r2
            r3[r8] = r0
            bzj r3 = defpackage.cqh.X(r3)
            ctp r4 = new ctp
            r5 = 9
            r4.<init>(r2, r0, r5, r6)
            java.util.concurrent.Executor r0 = r1.e
            hme r0 = r3.o(r4, r0)
            return r0
        L_0x0330:
            java.lang.Object r0 = r13.b
            java.lang.Object r0 = defpackage.hfc.S(r0)
            csx r0 = (defpackage.csx) r0
            java.lang.Object r1 = r13.a
            java.lang.Object r1 = defpackage.hfc.S(r1)
            csx r1 = (defpackage.csx) r1
            cxh r2 = new cxh
            r2.<init>(r0, r1)
            hme r0 = defpackage.hfc.K(r2)
            return r0
        L_0x034a:
            java.lang.Object r0 = r13.b
            cuc r0 = (defpackage.cuc) r0
            csk r2 = r0.b
            int r2 = r2.j
            if (r2 != r7) goto L_0x0382
            sk r2 = r0.c
            java.util.ArrayList r2 = r2.b
            r2.clear()
            sk r2 = r0.c
            java.lang.String r3 = "status"
            r2.n = r3
            cuf r3 = r0.g
            android.content.Context r3 = r3.a
            java.lang.String r3 = defpackage.cqh.e(r3)
            r2.e(r3)
            r2.g(r9)
            r2.i(r1)
            r2.h(r9, r9, r9)
            sp r1 = r0.d
            int r2 = r0.e
            sk r3 = r0.c
            android.app.Notification r3 = r3.a()
            r1.a(r2, r3)
        L_0x0382:
            csk r1 = r0.b
            grh r1 = r1.g
            boolean r1 = r1.f()
            if (r1 == 0) goto L_0x039d
            java.lang.Object r1 = r13.a
            csk r2 = r0.b
            grh r2 = r2.g
            java.lang.Object r2 = r2.b()
            csl r2 = (defpackage.csl) r2
            java.lang.Throwable r1 = (java.lang.Throwable) r1
            r2.b(r1)
        L_0x039d:
            cuf r1 = r0.g
            grh r1 = r1.e
            java.lang.Object r1 = r1.b()
            czp r1 = (defpackage.czp) r1
            csk r2 = r0.b
            java.lang.String r2 = r2.a
            r1.i(r2)
            cuf r1 = r0.g
            cuv r0 = r0.a
            cyw r1 = r1.i
            java.lang.String r0 = r0.a
            hme r0 = r1.h(r0)
            return r0
        L_0x03bb:
            java.lang.Object r0 = r13.a
            r2 = r0
            cuc r2 = (defpackage.cuc) r2
            csk r3 = r2.b
            java.lang.Object r4 = r13.b
            grh r3 = r3.g
            boolean r3 = r3.f()
            if (r3 == 0) goto L_0x03f2
            cuc r0 = (defpackage.cuc) r0     // Catch:{ Exception -> 0x03df }
            csk r0 = r0.b     // Catch:{ Exception -> 0x03df }
            grh r0 = r0.g     // Catch:{ Exception -> 0x03df }
            java.lang.Object r0 = r0.b()     // Catch:{ Exception -> 0x03df }
            csl r0 = (defpackage.csl) r0     // Catch:{ Exception -> 0x03df }
            r3 = r4
            crw r3 = (defpackage.crw) r3     // Catch:{ Exception -> 0x03df }
            r0.a(r3)     // Catch:{ Exception -> 0x03df }
            goto L_0x03f2
        L_0x03df:
            r0 = move-exception
            crw r4 = (defpackage.crw) r4
            java.lang.String r3 = r4.b
            java.lang.Object[] r4 = new java.lang.Object[r7]
            java.lang.String r5 = "DownloadListener"
            r4[r9] = r5
            r4[r8] = r3
            java.lang.String r3 = "%s: Delegate onComplete failed for group %s, showing failure notification."
            defpackage.cyh.o(r0, r3, r4)
            goto L_0x03f3
        L_0x03f2:
            r8 = r9
        L_0x03f3:
            csk r0 = r2.b
            int r0 = r0.j
            if (r0 != r7) goto L_0x0440
            sk r0 = r2.c
            java.util.ArrayList r0 = r0.b
            r0.clear()
            if (r8 == 0) goto L_0x042a
            sk r0 = r2.c
            java.lang.String r3 = "status"
            r0.n = r3
            cuf r3 = r2.g
            android.content.Context r3 = r3.a
            java.lang.String r3 = defpackage.cqh.e(r3)
            r0.e(r3)
            r0.g(r9)
            r0.i(r1)
            r0.h(r9, r9, r9)
            sp r0 = r2.d
            int r1 = r2.e
            sk r3 = r2.c
            android.app.Notification r3 = r3.a()
            r0.a(r1, r3)
            goto L_0x0440
        L_0x042a:
            cuf r0 = r2.g
            csk r1 = r2.b
            sp r3 = new sp
            android.content.Context r0 = r0.a
            r3.<init>(r0)
            android.app.NotificationManager r0 = r3.d
            java.lang.String r1 = r1.a
            int r1 = r1.hashCode()
            r0.cancel(r6, r1)
        L_0x0440:
            cuf r0 = r2.g
            grh r0 = r0.e
            java.lang.Object r0 = r0.b()
            czp r0 = (defpackage.czp) r0
            csk r1 = r2.b
            java.lang.String r1 = r1.a
            r0.i(r1)
            cuf r0 = r2.g
            cuv r1 = r2.a
            cyw r0 = r0.i
            java.lang.String r1 = r1.a
            hme r0 = r0.h(r1)
            return r0
        L_0x045e:
            java.lang.Object r0 = r13.b
            java.lang.Object r1 = r13.a
            cuf r1 = (defpackage.cuf) r1
            csk r0 = (defpackage.csk) r0
            hme r2 = r1.m(r0)
            ctu r3 = new ctu
            r3.<init>(r1, r0)
            java.util.concurrent.Executor r0 = r1.d
            hme r0 = defpackage.ftd.L(r2, r3, r0)
            return r0
        L_0x0476:
            java.lang.Object r0 = r13.b
            crx r0 = (defpackage.crx) r0
            csc r1 = r0.a
            java.lang.String r4 = r1.b
            java.lang.String r6 = r1.i
            long r11 = r1.h
            java.lang.Long r1 = java.lang.Long.valueOf(r11)
            java.lang.Object[] r2 = new java.lang.Object[r2]
            java.lang.String r11 = "MobileDataDownload"
            r2[r9] = r11
            r2[r8] = r4
            r2[r7] = r6
            r2[r5] = r1
            java.lang.String r1 = "null"
            r4 = 4
            r2[r4] = r1
            r2[r3] = r1
            java.lang.String r1 = "%s: Adding for download group = '%s', variant = '%s', buildId = '%d' and associating it with account = '%s', variant = '%s'"
            defpackage.cyh.e(r1, r2)
            defpackage.cqh.k()
            csc r0 = r0.a
            int r1 = r0.a
            r1 = r1 & r7
            java.lang.Object r2 = r13.a
            if (r1 == 0) goto L_0x04dc
            r1 = r2
            cuf r1 = (defpackage.cuf) r1
            android.content.Context r3 = r1.a
            java.lang.String r3 = r3.getPackageName()
            java.lang.String r6 = r0.c
            boolean r3 = r3.equals(r6)
            if (r3 != 0) goto L_0x050d
            java.lang.String r2 = r0.b
            android.content.Context r1 = r1.a
            java.lang.String r1 = r1.getPackageName()
            java.lang.String r0 = r0.c
            java.lang.Object[] r3 = new java.lang.Object[r4]
            java.lang.String r4 = "MobileDataDownload"
            r3[r9] = r4
            r3[r8] = r2
            r3[r7] = r1
            r3[r5] = r0
            java.lang.String r0 = "%s: Added group = '%s' with wrong owner package: '%s' v.s. '%s' "
            defpackage.cyh.i(r0, r3)
            hme r0 = defpackage.hfc.K(r10)
            goto L_0x05a6
        L_0x04dc:
            java.lang.Object r1 = r0.C(r3)
            htk r1 = (defpackage.htk) r1
            r1.x(r0)
            r0 = r2
            cuf r0 = (defpackage.cuf) r0
            android.content.Context r0 = r0.a
            java.lang.String r0 = r0.getPackageName()
            htq r3 = r1.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x04f9
            r1.u()
        L_0x04f9:
            htq r3 = r1.b
            csc r3 = (defpackage.csc) r3
            r0.getClass()
            int r5 = r3.a
            r5 = r5 | r7
            r3.a = r5
            r3.c = r0
            htq r0 = r1.r()
            csc r0 = (defpackage.csc) r0
        L_0x050d:
            ctg r1 = defpackage.ctg.g
            htk r1 = r1.l()
            java.lang.String r3 = r0.b
            htq r5 = r1.b
            boolean r5 = r5.B()
            if (r5 != 0) goto L_0x0520
            r1.u()
        L_0x0520:
            htq r5 = r1.b
            r6 = r5
            ctg r6 = (defpackage.ctg) r6
            r3.getClass()
            int r11 = r6.a
            r11 = r11 | r8
            r6.a = r11
            r6.b = r3
            java.lang.String r3 = r0.c
            boolean r5 = r5.B()
            if (r5 != 0) goto L_0x053a
            r1.u()
        L_0x053a:
            htq r5 = r1.b
            ctg r5 = (defpackage.ctg) r5
            r3.getClass()
            int r6 = r5.a
            r6 = r6 | r7
            r5.a = r6
            r5.c = r3
            byte[] r0 = r0.g()     // Catch:{ hui -> 0x0596 }
            hte r3 = defpackage.hte.a     // Catch:{ hui -> 0x0596 }
            hvj r3 = defpackage.hvj.a     // Catch:{ hui -> 0x0596 }
            hte r3 = defpackage.hte.a     // Catch:{ hui -> 0x0596 }
            csx r5 = defpackage.csx.w     // Catch:{ hui -> 0x0596 }
            int r6 = r0.length     // Catch:{ hui -> 0x0596 }
            htq r0 = defpackage.htq.o(r5, r0, r9, r6, r3)     // Catch:{ hui -> 0x0596 }
            defpackage.htq.D(r0)     // Catch:{ hui -> 0x0596 }
            csx r0 = (defpackage.csx) r0     // Catch:{ hui -> 0x0596 }
            r3 = r2
            cuf r3 = (defpackage.cuf) r3     // Catch:{ hui -> 0x0596 }
            cwm r3 = r3.c     // Catch:{ hui -> 0x0596 }
            htq r1 = r1.r()     // Catch:{ hui -> 0x0596 }
            ctg r1 = (defpackage.ctg) r1     // Catch:{ hui -> 0x0596 }
            r5 = r2
            cuf r5 = (defpackage.cuf) r5     // Catch:{ hui -> 0x0596 }
            hko r5 = r5.g     // Catch:{ hui -> 0x0596 }
            java.lang.String r6 = "%s addGroupForDownload %s"
            java.lang.String r7 = "MDDManager"
            java.lang.String r11 = r1.b     // Catch:{ hui -> 0x0596 }
            defpackage.cyh.d(r6, r7, r11)     // Catch:{ hui -> 0x0596 }
            hme r6 = r3.f()     // Catch:{ hui -> 0x0596 }
            cwk r7 = new cwk     // Catch:{ hui -> 0x0596 }
            r7.<init>(r3, r0, r1, r5)     // Catch:{ hui -> 0x0596 }
            java.util.concurrent.Executor r0 = r3.h     // Catch:{ hui -> 0x0596 }
            hme r0 = defpackage.ftd.L(r6, r7, r0)     // Catch:{ hui -> 0x0596 }
            java.lang.Class<java.io.IOException> r1 = java.io.IOException.class
            bub r3 = new bub     // Catch:{ hui -> 0x0596 }
            r3.<init>(r4)     // Catch:{ hui -> 0x0596 }
            cuf r2 = (defpackage.cuf) r2     // Catch:{ hui -> 0x0596 }
            java.util.concurrent.Executor r2 = r2.d     // Catch:{ hui -> 0x0596 }
            hme r0 = defpackage.ftd.G(r0, r1, r3, r2)     // Catch:{ hui -> 0x0596 }
            goto L_0x05a6
        L_0x0596:
            r0 = move-exception
            java.lang.Object[] r1 = new java.lang.Object[r8]
            java.lang.String r2 = "MobileDataDownload"
            r1[r9] = r2
            java.lang.String r2 = "%s: Unable to convert from DataFileGroup to DataFileGroupInternal."
            defpackage.cyh.j(r0, r2, r1)
            hme r0 = defpackage.hfc.K(r10)
        L_0x05a6:
            return r0
        L_0x05a7:
            ctg r0 = defpackage.ctg.g
            htk r0 = r0.l()
            htq r1 = r0.b
            boolean r1 = r1.B()
            if (r1 != 0) goto L_0x05b8
            r0.u()
        L_0x05b8:
            java.lang.Object r1 = r13.a
            java.lang.Object r2 = r13.b
            htq r3 = r0.b
            ctg r3 = (defpackage.ctg) r3
            cuj r1 = (defpackage.cuj) r1
            java.lang.String r1 = r1.a
            r1.getClass()
            int r5 = r3.a
            r5 = r5 | r8
            r3.a = r5
            r3.b = r1
            cuf r2 = (defpackage.cuf) r2
            android.content.Context r1 = r2.a
            java.lang.String r1 = r1.getPackageName()
            htq r3 = r0.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x05e1
            r0.u()
        L_0x05e1:
            htq r3 = r0.b
            ctg r3 = (defpackage.ctg) r3
            r1.getClass()
            int r5 = r3.a
            r5 = r5 | r7
            r3.a = r5
            r3.c = r1
            htq r0 = r0.r()
            ctg r0 = (defpackage.ctg) r0
            cwm r1 = r2.c
            java.lang.String r3 = r0.b
            java.lang.String r5 = "%s removeFileGroup %s"
            java.lang.String r6 = "MDDManager"
            defpackage.cyh.d(r5, r6, r3)
            hme r3 = r1.f()
            cwq r5 = new cwq
            r5.<init>(r1, r0, r8)
            java.util.concurrent.Executor r0 = r1.h
            hme r0 = defpackage.ftd.L(r3, r5, r0)
            amv r1 = new amv
            r1.<init>(r4)
            java.util.concurrent.Executor r2 = r2.d
            hme r0 = defpackage.ftd.K(r0, r1, r2)
            return r0
        L_0x061b:
            ctg r0 = defpackage.ctg.g
            htk r0 = r0.l()
            htq r1 = r0.b
            boolean r1 = r1.B()
            if (r1 != 0) goto L_0x062c
            r0.u()
        L_0x062c:
            java.lang.Object r1 = r13.b
            java.lang.Object r2 = r13.a
            htq r3 = r0.b
            ctg r3 = (defpackage.ctg) r3
            r4 = r1
            csp r4 = (defpackage.csp) r4
            java.lang.String r4 = r4.a
            r4.getClass()
            int r6 = r3.a
            r6 = r6 | r8
            r3.a = r6
            r3.b = r4
            r3 = r2
            cuf r3 = (defpackage.cuf) r3
            android.content.Context r4 = r3.a
            java.lang.String r4 = r4.getPackageName()
            htq r6 = r0.b
            boolean r6 = r6.B()
            if (r6 != 0) goto L_0x0657
            r0.u()
        L_0x0657:
            htq r6 = r0.b
            ctg r6 = (defpackage.ctg) r6
            r4.getClass()
            int r9 = r6.a
            r7 = r7 | r9
            r6.a = r7
            r6.c = r4
            htq r0 = r0.r()
            ctg r0 = (defpackage.ctg) r0
            cwm r4 = r3.c
            hme r4 = r4.e(r0, r8)
            bsx r6 = new bsx
            r6.<init>((java.lang.Object) r2, (defpackage.ctg) r0, (java.lang.Object) r1, (int) r5)
            java.util.concurrent.Executor r0 = r3.d
            hme r0 = defpackage.ftd.L(r4, r6, r0)
            return r0
        L_0x067d:
            java.lang.String r0 = "%s getAllFreshGroups"
            java.lang.String r1 = "MDDManager"
            defpackage.cyh.c(r0, r1)
            java.lang.Object r0 = r13.a
            r1 = r0
            cuf r1 = (defpackage.cuf) r1
            cwm r2 = r1.c
            hme r3 = r2.f()
            cwi r5 = new cwi
            r7 = 7
            r5.<init>(r2, r7)
            java.util.concurrent.Executor r2 = r2.h
            hme r2 = defpackage.ftd.L(r3, r5, r2)
            brg r3 = new brg
            java.lang.Object r5 = r13.b
            r3.<init>(r5, r4)
            java.util.concurrent.Executor r7 = r1.d
            hme r2 = defpackage.ftd.K(r2, r3, r7)
            bpt r3 = new bpt
            r3.<init>(r0, r5, r4, r6)
            java.util.concurrent.Executor r0 = r1.d
            hme r0 = defpackage.ftd.L(r2, r3, r0)
            return r0
        L_0x06b4:
            java.lang.Object r0 = r13.b
            btf r0 = (defpackage.btf) r0
            dbw r0 = r0.e
            hdf r1 = defpackage.dcs.a
            hco r1 = r1.l()
            java.lang.String r2 = "Superpacks.java"
            java.lang.String r3 = "com/google/android/libraries/micore/superpacks/Superpacks"
            java.lang.String r4 = "release"
            r7 = 1026(0x402, float:1.438E-42)
            hco r1 = r1.j(r3, r4, r7, r2)
            hdc r1 = (defpackage.hdc) r1
            java.lang.String r2 = "Releasing superpack %s"
            java.lang.Object r3 = r13.a
            r1.u(r2, r3)
            java.util.ArrayList r1 = new java.util.ArrayList
            r1.<init>()
            java.lang.Object r2 = r0.e     // Catch:{ IOException -> 0x06f6 }
            java.lang.Object r4 = r0.b     // Catch:{ IOException -> 0x06f6 }
            r7 = r3
            java.lang.String r7 = (java.lang.String) r7     // Catch:{ IOException -> 0x06f6 }
            java.util.List r4 = r4.b(r7)     // Catch:{ IOException -> 0x06f6 }
            java.util.Set r4 = defpackage.ddd.c(r4)     // Catch:{ IOException -> 0x06f6 }
            eez r2 = (defpackage.eez) r2     // Catch:{ IOException -> 0x06f6 }
            r7 = r3
            java.lang.String r7 = (java.lang.String) r7     // Catch:{ IOException -> 0x06f6 }
            hme r2 = r2.b(r7, r4)     // Catch:{ IOException -> 0x06f6 }
            r1.add(r2)     // Catch:{ IOException -> 0x06f6 }
            goto L_0x06fe
        L_0x06f6:
            r2 = move-exception
            hme r2 = defpackage.hfc.J(r2)
            r1.add(r2)
        L_0x06fe:
            java.lang.Object r2 = r0.e
            r4 = r3
            java.lang.String r4 = (java.lang.String) r4
            java.lang.String r4 = defpackage.dbw.c(r4)
            java.util.Set r7 = java.util.Collections.emptySet()
            eez r2 = (defpackage.eez) r2
            hme r2 = r2.b(r4, r7)
            r1.add(r2)
            boolean r2 = r1.isEmpty()
            if (r2 == 0) goto L_0x071f
            hme r0 = defpackage.hfc.K(r6)
            goto L_0x072e
        L_0x071f:
            ipt r2 = defpackage.hfc.Y(r1)
            cmj r4 = new cmj
            r4.<init>((java.lang.Object) r0, (java.lang.Object) r1, (java.lang.Object) r3, (int) r5)
            java.lang.Object r0 = r0.g
            hme r0 = r2.a(r4, r0)
        L_0x072e:
            return r0
        L_0x072f:
            java.lang.Object r0 = r13.b
            java.lang.Object r1 = r13.a
            r3 = r1
            cuf r3 = (defpackage.cuf) r3
            r4 = r0
            csk r4 = (defpackage.csk) r4
            hme r4 = r3.m(r4)
            bpt r5 = new bpt
            r5.<init>(r1, r0, r2, r6)
            java.util.concurrent.Executor r0 = r3.d
            hme r0 = defpackage.ftd.L(r4, r5, r0)
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.ctp.a():hme");
    }

    public /* synthetic */ ctp(Object obj, Object obj2, int i, byte[] bArr) {
        this.c = i;
        this.b = obj;
        this.a = obj2;
    }
}
