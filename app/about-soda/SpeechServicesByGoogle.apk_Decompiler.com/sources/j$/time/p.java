package j$.time;

import j$.time.chrono.C0010i;
import j$.time.chrono.u;
import j$.time.temporal.ChronoUnit;
import j$.time.temporal.TemporalUnit;
import j$.time.temporal.a;
import j$.time.temporal.k;
import j$.time.temporal.l;
import j$.time.temporal.n;
import j$.time.temporal.r;
import j$.time.temporal.t;
import j$.util.Objects;
import java.io.InvalidObjectException;
import java.io.ObjectInput;
import java.io.ObjectInputStream;
import java.io.ObjectOutput;
import java.io.Serializable;

public final class p implements k, n, Comparable, Serializable {
    private static final long serialVersionUID = 2287754244819255394L;
    private final i a;
    private final x b;

    static {
        i iVar = i.c;
        x xVar = x.h;
        iVar.getClass();
        I(iVar, xVar);
        i iVar2 = i.d;
        x xVar2 = x.g;
        iVar2.getClass();
        I(iVar2, xVar2);
    }

    private p(i iVar, x xVar) {
        this.a = (i) Objects.requireNonNull(iVar, "dateTime");
        this.b = (x) Objects.requireNonNull(xVar, "offset");
    }

    public static p I(i iVar, x xVar) {
        return new p(iVar, xVar);
    }

    static p K(ObjectInput objectInput) {
        i iVar = i.c;
        g gVar = g.d;
        return new p(i.Q(g.T(objectInput.readInt(), objectInput.readByte(), objectInput.readByte()), k.X(objectInput)), x.T(objectInput));
    }

    private p M(i iVar, x xVar) {
        if (this.a != iVar || !this.b.equals(xVar)) {
            return new p(iVar, xVar);
        }
        return this;
    }

    private void readObject(ObjectInputStream objectInputStream) {
        throw new InvalidObjectException("Deserialization via serialization delegate");
    }

    private Object writeReplace() {
        return new s((byte) 10, this);
    }

    public final long B(j$.time.temporal.p pVar) {
        if (!(pVar instanceof a)) {
            return pVar.g(this);
        }
        int i = o.a[((a) pVar).ordinal()];
        x xVar = this.b;
        i iVar = this.a;
        if (i == 1) {
            iVar.getClass();
            return C0010i.n(iVar, xVar);
        } else if (i != 2) {
            return iVar.B(pVar);
        } else {
            return (long) xVar.O();
        }
    }

    public final Object F(r rVar) {
        if (rVar == l.h() || rVar == l.j()) {
            return this.b;
        }
        if (rVar == l.k()) {
            return null;
        }
        r f = l.f();
        i iVar = this.a;
        if (rVar == f) {
            return iVar.V();
        }
        if (rVar == l.g()) {
            return iVar.b();
        }
        if (rVar == l.e()) {
            return u.d;
        }
        if (rVar == l.i()) {
            return ChronoUnit.NANOS;
        }
        return rVar.a(this);
    }

    /* renamed from: J */
    public final p f(long j, TemporalUnit temporalUnit) {
        if (temporalUnit instanceof ChronoUnit) {
            return M(this.a.f(j, temporalUnit), this.b);
        }
        return (p) temporalUnit.m(this, j);
    }

    public final i L() {
        return this.a;
    }

    public final int compareTo(Object obj) {
        int i;
        p pVar = (p) obj;
        x xVar = pVar.b;
        x xVar2 = this.b;
        boolean equals = xVar2.equals(xVar);
        i iVar = pVar.a;
        i iVar2 = this.a;
        if (equals) {
            i = iVar2.compareTo(iVar);
        } else {
            iVar2.getClass();
            long n = C0010i.n(iVar2, xVar2);
            iVar.getClass();
            i = Long.compare(n, C0010i.n(iVar, pVar.b));
            if (i == 0) {
                i = iVar2.b().N() - iVar.b().N();
            }
        }
        if (i == 0) {
            return iVar2.compareTo(iVar);
        }
        return i;
    }

    public final k d(long j, j$.time.temporal.p pVar) {
        if (!(pVar instanceof a)) {
            return (p) pVar.s(this, j);
        }
        a aVar = (a) pVar;
        int i = o.a[aVar.ordinal()];
        x xVar = this.b;
        i iVar = this.a;
        if (i == 1) {
            Instant ofEpochSecond = Instant.ofEpochSecond(j, (long) iVar.K());
            Objects.requireNonNull(ofEpochSecond, "instant");
            Objects.requireNonNull(xVar, "zone");
            x d = xVar.I().d(ofEpochSecond);
            return new p(i.R(ofEpochSecond.getEpochSecond(), ofEpochSecond.K(), d), d);
        } else if (i != 2) {
            return M(iVar.d(j, pVar), xVar);
        } else {
            return M(iVar, x.R(aVar.F(j)));
        }
    }

    public final boolean e(j$.time.temporal.p pVar) {
        if ((pVar instanceof a) || (pVar != null && pVar.p(this))) {
            return true;
        }
        return false;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof p)) {
            return false;
        }
        p pVar = (p) obj;
        if (!this.a.equals(pVar.a) || !this.b.equals(pVar.b)) {
            return false;
        }
        return true;
    }

    public final k g(k kVar) {
        a aVar = a.EPOCH_DAY;
        i iVar = this.a;
        return kVar.d(iVar.V().C(), aVar).d(iVar.b().Y(), a.NANO_OF_DAY).d((long) this.b.O(), a.OFFSET_SECONDS);
    }

    public final int hashCode() {
        return this.a.hashCode() ^ this.b.hashCode();
    }

    public final k m(long j, ChronoUnit chronoUnit) {
        if (j == Long.MIN_VALUE) {
            return f(Long.MAX_VALUE, chronoUnit).f(1, chronoUnit);
        }
        return f(-j, chronoUnit);
    }

    public final int p(j$.time.temporal.p pVar) {
        if (!(pVar instanceof a)) {
            return l.a(this, pVar);
        }
        int i = o.a[((a) pVar).ordinal()];
        if (i == 1) {
            throw new RuntimeException("Invalid field 'InstantSeconds' for get() method, use getLong() instead");
        } else if (i != 2) {
            return this.a.p(pVar);
        } else {
            return this.b.O();
        }
    }

    public final k s(g gVar) {
        return M(this.a.X(gVar), this.b);
    }

    public final t t(j$.time.temporal.p pVar) {
        if (!(pVar instanceof a)) {
            return pVar.t(this);
        }
        if (pVar == a.INSTANT_SECONDS || pVar == a.OFFSET_SECONDS) {
            return ((a) pVar).m();
        }
        return this.a.t(pVar);
    }

    public final String toString() {
        String iVar = this.a.toString();
        String xVar = this.b.toString();
        return iVar + xVar;
    }

    /* access modifiers changed from: package-private */
    public final void writeExternal(ObjectOutput objectOutput) {
        this.a.Z(objectOutput);
        this.b.U(objectOutput);
    }
}
