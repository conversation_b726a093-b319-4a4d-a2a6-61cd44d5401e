package defpackage;

import android.util.Log;
import java.lang.reflect.Constructor;

/* renamed from: bkb  reason: default package */
/* compiled from: PG */
public final class bkb implements Comparable {
    public final Class a;
    public final Class b;
    public final int c;

    public bkb(String str, Class cls, int i) {
        Class<?> cls2;
        try {
            cls2 = Class.forName(str);
        } catch (ClassNotFoundException unused) {
            cls2 = null;
        }
        this.a = cls2;
        this.b = cls;
        this.c = i;
    }

    public final bkd a(Object obj) {
        try {
            Constructor declaredConstructor = this.b.getDeclaredConstructor(new Class[]{this.a});
            declaredConstructor.setAccessible(true);
            return (bkd) declaredConstructor.newInstance(new Object[]{obj});
        } catch (ReflectiveOperationException unused) {
            Class cls = this.b;
            Class cls2 = this.a;
            String obj2 = cls.toString();
            String valueOf = String.valueOf(cls2);
            Log.e("carui", obj2 + " must have a constructor that accepts " + valueOf);
            return null;
        }
    }

    public final /* bridge */ /* synthetic */ int compareTo(Object obj) {
        return ((bkb) obj).c - this.c;
    }
}
