package defpackage;

/* renamed from: esp  reason: default package */
/* compiled from: PG */
public final class esp implements eso {
    public final erp a;
    public final int b;
    public final eab c;
    public final ejn d;
    private final ebi e;
    private final boolean f;

    public /* synthetic */ esp(erp erp, ebi ebi, int i, eab eab) {
        boolean z;
        ejn a2 = erp.a();
        jnu.e(eab, "params");
        this.a = erp;
        this.e = ebi;
        this.b = i;
        this.c = eab;
        if (i == -1) {
            z = true;
        } else {
            z = false;
        }
        this.f = z;
        this.d = a2;
    }

    public final int a() {
        return this.b;
    }

    public final eab b() {
        return this.c;
    }

    public final ebi c() {
        return this.e;
    }

    public final ejn d() {
        return this.d;
    }

    public final /* synthetic */ eow e() {
        return fbi.w(this);
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof esp)) {
            return false;
        }
        esp esp = (esp) obj;
        if (jnu.i(this.a, esp.a) && jnu.i(this.e, esp.e) && this.b == esp.b && jnu.i(this.c, esp.c) && this.f == esp.f && jnu.i(this.d, esp.d)) {
            return true;
        }
        return false;
    }

    public final boolean f() {
        return this.f;
    }

    public final int hashCode() {
        int i;
        int hashCode = (this.a.hashCode() * 31) + this.e.hashCode();
        eab eab = this.c;
        if (eab.B()) {
            i = eab.i();
        } else {
            int i2 = eab.memoizedHashCode;
            if (i2 == 0) {
                i2 = eab.i();
                eab.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (((((((hashCode * 31) + this.b) * 31) + i) * 31) + a.f(this.f)) * 31) + this.d.hashCode();
    }

    public final String toString() {
        return "HotwordRouteSessionDataInternal(audioRoute=" + this.a + ", session=" + this.e + ", routeToken=" + this.b + ", params=" + this.c + ", isInactive=" + this.f + ", routeData=" + this.d + ")";
    }
}
