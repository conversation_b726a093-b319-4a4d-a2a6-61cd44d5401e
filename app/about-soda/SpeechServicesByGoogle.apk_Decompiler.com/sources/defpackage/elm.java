package defpackage;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;

/* renamed from: elm  reason: default package */
/* compiled from: PG */
public final class elm {
    private static final hca b = hca.m("com/google/android/libraries/search/audio/listener/reporter/MicUpdateReporter");
    public final Executor a;
    private final Set c;
    private final Map d = new HashMap();

    public elm(Set set, Executor executor) {
        this.c = set;
        this.a = executor;
    }

    private final synchronized hll c(elk elk) {
        if (!this.d.containsKey(elk)) {
            this.d.put(elk, new hll());
        }
        return (hll) this.d.get(elk);
    }

    public final void a(int i, hme hme, hme hme2) {
        hfc.T(hme, gof.g(new ell(this, i, hme2)), this.a);
    }

    public final synchronized void b() {
        ((hby) ((hby) b.f()).j("com/google/android/libraries/search/audio/listener/reporter/MicUpdateReporter", "reportMicUpdate", 132, "MicUpdateReporter.java")).r("#audio# reportMicUpdate");
        for (elk elk : this.c) {
            fyz.c(c(elk).a(gof.i(new bdr((Object) elk, 13)), this.a), "Failed to notify MicUpdateListener.", new Object[0]);
        }
    }
}
