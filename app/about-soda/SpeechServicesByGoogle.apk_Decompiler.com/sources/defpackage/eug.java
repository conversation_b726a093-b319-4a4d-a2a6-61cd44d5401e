package defpackage;

import java.util.Queue;
import java.util.concurrent.Executor;

/* renamed from: eug  reason: default package */
/* compiled from: PG */
public final class eug {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl");
    public final Executor b;
    private final Queue c = new hbm(new gwt(128));

    public eug(Executor executor) {
        this.b = executor;
    }

    public static eax a(dzm dzm, grh grh, dzq dzq) {
        htk l = eax.h.l();
        if (grh.f()) {
            htk l2 = eap.e.l();
            if (!l2.b.B()) {
                l2.u();
            }
            htq htq = l2.b;
            eap eap = (eap) htq;
            dzm.getClass();
            eap.c = dzm;
            eap.b = 2;
            if (!htq.B()) {
                l2.u();
            }
            eap eap2 = (eap) l2.b;
            dzq.getClass();
            eap2.d = dzq;
            eap2.a |= 1;
            if (!l.b.B()) {
                l.u();
            }
            eax eax = (eax) l.b;
            eap eap3 = (eap) l2.r();
            eap3.getClass();
            eax.c = eap3;
            eax.b = 8;
            long intValue = (long) ((Integer) grh.b()).intValue();
            if (!l.b.B()) {
                l.u();
            }
            eax eax2 = (eax) l.b;
            eax2.a = 8 | eax2.a;
            eax2.g = intValue;
        } else {
            htk l3 = eaz.e.l();
            if (!l3.b.B()) {
                l3.u();
            }
            htq htq2 = l3.b;
            eaz eaz = (eaz) htq2;
            dzm.getClass();
            eaz.c = dzm;
            eaz.b = 2;
            if (!htq2.B()) {
                l3.u();
            }
            eaz eaz2 = (eaz) l3.b;
            dzq.getClass();
            eaz2.d = dzq;
            eaz2.a |= 1;
            if (!l.b.B()) {
                l.u();
            }
            eax eax3 = (eax) l.b;
            eaz eaz3 = (eaz) l3.r();
            eaz3.getClass();
            eax3.c = eaz3;
            eax3.b = 102;
        }
        return (eax) l.r();
    }

    public static eax b(dzo dzo, grh grh, dzq dzq) {
        htk l = eax.h.l();
        if (grh.f()) {
            htk l2 = eap.e.l();
            if (!l2.b.B()) {
                l2.u();
            }
            htq htq = l2.b;
            eap eap = (eap) htq;
            dzo.getClass();
            eap.c = dzo;
            eap.b = 1;
            if (!htq.B()) {
                l2.u();
            }
            eap eap2 = (eap) l2.b;
            dzq.getClass();
            eap2.d = dzq;
            eap2.a |= 1;
            if (!l.b.B()) {
                l.u();
            }
            eax eax = (eax) l.b;
            eap eap3 = (eap) l2.r();
            eap3.getClass();
            eax.c = eap3;
            eax.b = 8;
            long intValue = (long) ((Integer) grh.b()).intValue();
            if (!l.b.B()) {
                l.u();
            }
            eax eax2 = (eax) l.b;
            eax2.a = 8 | eax2.a;
            eax2.g = intValue;
        } else {
            htk l3 = eaz.e.l();
            if (!l3.b.B()) {
                l3.u();
            }
            htq htq2 = l3.b;
            eaz eaz = (eaz) htq2;
            dzo.getClass();
            eaz.c = dzo;
            eaz.b = 1;
            if (!htq2.B()) {
                l3.u();
            }
            eaz eaz2 = (eaz) l3.b;
            dzq.getClass();
            eaz2.d = dzq;
            eaz2.a |= 1;
            if (!l.b.B()) {
                l.u();
            }
            eax eax3 = (eax) l.b;
            eaz eaz3 = (eaz) l3.r();
            eaz3.getClass();
            eax3.c = eaz3;
            eax3.b = 102;
        }
        return (eax) l.r();
    }

    public final void c(hme hme, hme hme2, grh grh, dzq dzq) {
        hfc.T(hme, gof.g(new emc((Object) this, (Object) grh, (Object) dzq, 5)), this.b);
        hfc.T(hme2, gof.g(new emc((Object) this, (Object) grh, (Object) dzq, 2)), this.b);
    }

    public final void d(eav eav) {
        htk l = eao.e.l();
        long epochMilli = cqx.G().toEpochMilli();
        if (!l.b.B()) {
            l.u();
        }
        htq htq = l.b;
        eao eao = (eao) htq;
        eao.a |= 1;
        eao.d = epochMilli;
        if (!htq.B()) {
            l.u();
        }
        eao eao2 = (eao) l.b;
        eav.getClass();
        eao2.c = eav;
        eao2.b = 2;
        eao eao3 = (eao) l.r();
        ((hby) ((hby) a.c().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl", "logClientEvent", 100, "AudioEventsHolderImpl.java")).u("#audio# %s", new eqh(eao3, 4));
        this.c.add(eao3);
    }

    public final void e(eaw eaw) {
        htk l = eao.e.l();
        long epochMilli = cqx.G().toEpochMilli();
        if (!l.b.B()) {
            l.u();
        }
        htq htq = l.b;
        eao eao = (eao) htq;
        eao.a |= 1;
        eao.d = epochMilli;
        if (!htq.B()) {
            l.u();
        }
        eao eao2 = (eao) l.b;
        eaw.getClass();
        eao2.c = eaw;
        eao2.b = 4;
        eao eao3 = (eao) l.r();
        ((hby) ((hby) a.c().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl", "logClientEventOutcome", 122, "AudioEventsHolderImpl.java")).u("#audio# %s", new eqh(eao3, 3));
        this.c.add(eao3);
    }

    public final void f(eax eax) {
        htk l = eao.e.l();
        long epochMilli = cqx.G().toEpochMilli();
        if (!l.b.B()) {
            l.u();
        }
        htq htq = l.b;
        eao eao = (eao) htq;
        eao.a |= 1;
        eao.d = epochMilli;
        if (!htq.B()) {
            l.u();
        }
        eao eao2 = (eao) l.b;
        eax.getClass();
        eao2.c = eax;
        eao2.b = 3;
        eao eao3 = (eao) l.r();
        ((hby) ((hby) a.c().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl", "logClientEventStatus", 111, "AudioEventsHolderImpl.java")).u("#audio# %s", new eqh(eao3, 5));
        this.c.add(eao3);
    }
}
