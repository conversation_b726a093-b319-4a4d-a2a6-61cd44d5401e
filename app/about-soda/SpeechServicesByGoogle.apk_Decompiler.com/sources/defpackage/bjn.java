package defpackage;

import android.support.v7.widget.RecyclerView;
import android.view.ViewTreeObserver;
import com.android.car.ui.FocusParkingView;

/* renamed from: bjn  reason: default package */
/* compiled from: PG */
public final class bjn implements ViewTreeObserver.OnGlobalLayoutListener {
    final /* synthetic */ RecyclerView a;
    final /* synthetic */ FocusParkingView b;

    public bjn(FocusParkingView focusParkingView, RecyclerView recyclerView) {
        this.a = recyclerView;
        this.b = focusParkingView;
    }

    public final void onGlobalLayout() {
        this.a.sendAccessibilityEvent(8);
        this.b.getViewTreeObserver().removeOnGlobalLayoutListener(this);
    }
}
