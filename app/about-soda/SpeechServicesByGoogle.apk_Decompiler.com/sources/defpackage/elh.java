package defpackage;

/* renamed from: elh  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class elh implements dyx {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ elh(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r0v13, types: [hme, java.lang.Object] */
    public final hme a() {
        int i = this.b;
        if (i == 0) {
            return ((eli) this.a).b;
        }
        if (i == 1) {
            return ((eli) this.a).b;
        }
        if (i == 2) {
            return ((emv) this.a).b;
        }
        if (i == 3) {
            return ((emz) this.a).c;
        }
        ? r0 = this.a;
        jnu.e(r0, "$stopListeningStatus");
        return r0;
    }
}
