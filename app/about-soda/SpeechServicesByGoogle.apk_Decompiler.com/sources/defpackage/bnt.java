package defpackage;

import android.view.View;
import j$.util.function.Consumer$CC;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/* renamed from: bnt  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bnt implements Consumer {
    public final /* synthetic */ bnu a;
    public final /* synthetic */ View[] b;
    public final /* synthetic */ int c;
    public final /* synthetic */ AtomicInteger d;

    public /* synthetic */ bnt(bnu bnu, View[] viewArr, int i, AtomicInteger atomicInteger) {
        this.a = bnu;
        this.b = viewArr;
        this.c = i;
        this.d = atomicInteger;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:15:0x0029, code lost:
        return;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void accept(java.lang.Object r6) {
        /*
            r5 = this;
            int r0 = r5.c
            bnu r1 = r5.a
            java.util.concurrent.atomic.AtomicInteger r2 = r5.d
            android.view.View r6 = (android.view.View) r6
            monitor-enter(r1)
            android.view.View[] r3 = r1.h     // Catch:{ all -> 0x002a }
            android.view.View[] r4 = r5.b
            if (r4 == r3) goto L_0x0011
            monitor-exit(r1)     // Catch:{ all -> 0x002a }
            return
        L_0x0011:
            r4[r0] = r6     // Catch:{ all -> 0x002a }
            r6 = 1
            int r6 = r2.addAndGet(r6)     // Catch:{ all -> 0x002a }
            int r0 = r4.length     // Catch:{ all -> 0x002a }
            if (r6 != r0) goto L_0x0028
            r6 = 0
        L_0x001c:
            if (r6 >= r0) goto L_0x0028
            r2 = r4[r6]     // Catch:{ all -> 0x002a }
            android.view.ViewGroup r3 = r1.a     // Catch:{ all -> 0x002a }
            r3.addView(r2)     // Catch:{ all -> 0x002a }
            int r6 = r6 + 1
            goto L_0x001c
        L_0x0028:
            monitor-exit(r1)     // Catch:{ all -> 0x002a }
            return
        L_0x002a:
            r6 = move-exception
            monitor-exit(r1)     // Catch:{ all -> 0x002a }
            throw r6
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bnt.accept(java.lang.Object):void");
    }

    public final /* synthetic */ Consumer andThen(Consumer consumer) {
        return Consumer$CC.$default$andThen(this, consumer);
    }
}
