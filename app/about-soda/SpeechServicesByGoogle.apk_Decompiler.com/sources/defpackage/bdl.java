package defpackage;

import android.app.job.JobInfo;
import android.app.job.JobScheduler;
import android.content.Context;
import android.os.Build;
import android.util.Base64;
import java.util.List;

/* renamed from: bdl  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bdl implements jmp {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ bdl(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                bdm bdm = (bdm) this.a;
                Context context = bdm.b;
                if (Build.VERSION.SDK_INT >= 34) {
                    ben.a(context).cancelAll();
                }
                JobScheduler jobScheduler = (JobScheduler) context.getSystemService("jobscheduler");
                List<JobInfo> e = bep.e(context, jobScheduler);
                if (e != null && !e.isEmpty()) {
                    for (JobInfo id : e) {
                        bep.f(jobScheduler, id.getId());
                    }
                }
                bhf A = bdm.d.A();
                bhx bhx = (bhx) A;
                bhx.a.k();
                axc d = bhx.h.d();
                try {
                    ((bhx) A).a.l();
                    d.a();
                    ((bhx) A).a.o();
                    ((bhx) A).a.m();
                    bhx.h.f(d);
                    bct.a(bdm.c, bdm.d, bdm.e);
                    return jkd.a;
                } catch (Throwable th) {
                    bhx.h.f(d);
                    throw th;
                }
            case 1:
                bic.a((bcy) this.a);
                return jkd.a;
            case 2:
                Object obj = this.a;
                try {
                    byte[] decode = Base64.decode("CgV0ZXNsYQoOcGFuYXNvbmljIGRlY3QKCHNhbnRhIGZlCgx0b3lvdGEgcHJpdXM", 3);
                    hxc hxc = hxc.b;
                    int length = decode.length;
                    hte hte = hte.a;
                    hvj hvj = hvj.a;
                    htq o = htq.o(hxc, decode, 0, length, hte.a);
                    htq.D(o);
                    return (hva) ((grh) obj).d((hxc) o);
                } catch (RuntimeException e2) {
                    throw e2;
                } catch (Exception e3) {
                    throw new RuntimeException(e3);
                }
            case 3:
                Object obj2 = this.a;
                try {
                    byte[] decode2 = Base64.decode("CAI", 3);
                    htb htb = htb.c;
                    int length2 = decode2.length;
                    hte hte2 = hte.a;
                    hvj hvj2 = hvj.a;
                    htq o2 = htq.o(htb, decode2, 0, length2, hte.a);
                    htq.D(o2);
                    return (hva) ((grh) obj2).d((htb) o2);
                } catch (RuntimeException e4) {
                    throw e4;
                } catch (Exception e5) {
                    throw new RuntimeException(e5);
                }
            case 4:
                Object obj3 = this.a;
                try {
                    byte[] decode3 = Base64.decode("CgV0ZXNsYQoOcGFuYXNvbmljIGRlY3QKBWFjcnV4", 3);
                    hxc hxc2 = hxc.b;
                    int length3 = decode3.length;
                    hte hte3 = hte.a;
                    hvj hvj3 = hvj.a;
                    htq o3 = htq.o(hxc2, decode3, 0, length3, hte.a);
                    htq.D(o3);
                    return (hva) ((grh) obj3).d((hxc) o3);
                } catch (RuntimeException e6) {
                    throw e6;
                } catch (Exception e7) {
                    throw new RuntimeException(e7);
                }
            case 5:
                Object obj4 = this.a;
                try {
                    byte[] decode4 = Base64.decode("EIDC1y8", 3);
                    htb htb2 = htb.c;
                    int length4 = decode4.length;
                    hte hte4 = hte.a;
                    hvj hvj4 = hvj.a;
                    htq o4 = htq.o(htb2, decode4, 0, length4, hte.a);
                    htq.D(o4);
                    return (hva) ((grh) obj4).d((htb) o4);
                } catch (RuntimeException e8) {
                    throw e8;
                } catch (Exception e9) {
                    throw new RuntimeException(e9);
                }
            case 6:
                Object obj5 = this.a;
                try {
                    byte[] decode5 = Base64.decode("CJAc", 3);
                    htb htb3 = htb.c;
                    int length5 = decode5.length;
                    hte hte5 = hte.a;
                    hvj hvj5 = hvj.a;
                    htq o5 = htq.o(htb3, decode5, 0, length5, hte.a);
                    htq.D(o5);
                    return (hva) ((grh) obj5).d((htb) o5);
                } catch (RuntimeException e10) {
                    throw e10;
                } catch (Exception e11) {
                    throw new RuntimeException(e11);
                }
            case 7:
                Object obj6 = this.a;
                try {
                    byte[] decode6 = Base64.decode("CAM", 3);
                    crh crh = crh.a;
                    int length6 = decode6.length;
                    hte hte6 = hte.a;
                    hvj hvj6 = hvj.a;
                    htq o6 = htq.o(crh, decode6, 0, length6, hte.a);
                    htq.D(o6);
                    return (hva) ((grh) obj6).d((crh) o6);
                } catch (RuntimeException e12) {
                    throw e12;
                } catch (Exception e13) {
                    throw new RuntimeException(e13);
                }
            case 8:
                return hzz.i((grh) this.a);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return hzz.l((grh) this.a);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return hzz.l((grh) this.a);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return hzz.m((grh) this.a);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return hzz.n((grh) this.a);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                Object obj7 = this.a;
                try {
                    byte[] decode7 = Base64.decode("EMCEPQ", 3);
                    htb htb4 = htb.c;
                    int length7 = decode7.length;
                    hte hte7 = hte.a;
                    hvj hvj7 = hvj.a;
                    htq o7 = htq.o(htb4, decode7, 0, length7, hte.a);
                    htq.D(o7);
                    return (hva) ((grh) obj7).d((htb) o7);
                } catch (RuntimeException e14) {
                    throw e14;
                } catch (Exception e15) {
                    throw new RuntimeException(e15);
                }
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return hzz.n((grh) this.a);
            case 15:
                return a.s((grh) this.a);
            case 16:
                return hzz.m((grh) this.a);
            case 17:
                return hzz.m((grh) this.a);
            case 18:
                Object obj8 = this.a;
                try {
                    byte[] decode8 = Base64.decode("CMDRAg", 3);
                    htb htb5 = htb.c;
                    int length8 = decode8.length;
                    hte hte8 = hte.a;
                    hvj hvj8 = hvj.a;
                    htq o8 = htq.o(htb5, decode8, 0, length8, hte.a);
                    htq.D(o8);
                    return (hva) ((grh) obj8).d((htb) o8);
                } catch (RuntimeException e16) {
                    throw e16;
                } catch (Exception e17) {
                    throw new RuntimeException(e17);
                }
            case 19:
                Object obj9 = this.a;
                try {
                    byte[] decode9 = Base64.decode("CAE", 3);
                    htb htb6 = htb.c;
                    int length9 = decode9.length;
                    hte hte9 = hte.a;
                    hvj hvj9 = hvj.a;
                    htq o9 = htq.o(htb6, decode9, 0, length9, hte.a);
                    htq.D(o9);
                    return (hva) ((grh) obj9).d((htb) o9);
                } catch (RuntimeException e18) {
                    throw e18;
                } catch (Exception e19) {
                    throw new RuntimeException(e19);
                }
            default:
                Object obj10 = this.a;
                try {
                    byte[] decode10 = Base64.decode("CgQCBA0O", 3);
                    hxb hxb = hxb.a;
                    int length10 = decode10.length;
                    hte hte10 = hte.a;
                    hvj hvj10 = hvj.a;
                    htq o10 = htq.o(hxb, decode10, 0, length10, hte.a);
                    htq.D(o10);
                    return (hva) ((grh) obj10).d((hxb) o10);
                } catch (RuntimeException e20) {
                    throw e20;
                } catch (Exception e21) {
                    throw new RuntimeException(e21);
                }
        }
    }
}
