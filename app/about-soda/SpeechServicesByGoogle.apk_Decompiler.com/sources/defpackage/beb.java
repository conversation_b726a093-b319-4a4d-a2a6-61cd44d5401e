package defpackage;

import android.content.Context;
import android.text.TextUtils;
import androidx.wear.ambient.AmbientModeSupport;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CancellationException;

/* renamed from: beb  reason: default package */
/* compiled from: PG */
public final class beb implements bcr, bey, bce {
    private static final String b = bbk.b("GreedyScheduler");
    Boolean a;
    private final Context c;
    private final Map d = new HashMap();
    private final bdz e;
    private boolean f;
    private final Object g = new Object();
    private final bcp h;
    private final bam i;
    private final Map j = new HashMap();
    private final bec k;
    private final cyw l;
    private final bxq m;
    private final bxq n = new bxq();
    private final byw o;

    public beb(Context context, bam bam, alx alx, bcp bcp, bxq bxq, cyw cyw) {
        this.c = context;
        bbv bbv = bam.e;
        AmbientModeSupport.AmbientCallback ambientCallback = bam.q;
        this.e = new bdz(this, bbv);
        this.k = new bec(bbv, bxq);
        this.l = cyw;
        this.o = new byw(alx, (byte[]) null);
        this.i = bam;
        this.h = bcp;
        this.m = bxq;
    }

    private final void f() {
        this.a = Boolean.valueOf(big.a(this.c, this.i));
    }

    private final void g() {
        if (!this.f) {
            this.h.a(this);
            this.f = true;
        }
    }

    public final void a(bgt bgt, boolean z) {
        jrz jrz;
        byw H = this.n.H(bgt);
        if (H != null) {
            this.k.a(H);
        }
        synchronized (this.g) {
            jrz = (jrz) this.d.remove(bgt);
        }
        if (jrz != null) {
            bbk.a();
            Objects.toString(bgt);
            jrz.r((CancellationException) null);
        }
        if (!z) {
            synchronized (this.g) {
                this.j.remove(bgt);
            }
        }
    }

    public final void b(String str) {
        Runnable runnable;
        if (this.a == null) {
            f();
        }
        if (!this.a.booleanValue()) {
            bbk.a().e(b, "Ignoring schedule request in non-main process");
            return;
        }
        g();
        bbk.a();
        bdz bdz = this.e;
        if (!(bdz == null || (runnable = (Runnable) bdz.c.remove(str)) == null)) {
            bdz.b.a(runnable);
        }
        for (byw byw : this.n.e(str)) {
            this.k.a(byw);
            we.k(this.m, byw);
        }
    }

    public final void c(bhe... bheArr) {
        long max;
        if (this.a == null) {
            f();
        }
        if (!this.a.booleanValue()) {
            bbk.a().e(b, "Ignoring schedule request in a secondary process");
            return;
        }
        g();
        HashSet<bhe> hashSet = new HashSet<>();
        HashSet hashSet2 = new HashSet();
        for (bhe bhe : bheArr) {
            if (!this.n.f(wg.f(bhe))) {
                synchronized (this.g) {
                    bgt f2 = wg.f(bhe);
                    bea bea = (bea) this.j.get(f2);
                    if (bea == null) {
                        int i2 = bhe.l;
                        AmbientModeSupport.AmbientCallback ambientCallback = this.i.q;
                        bea = new bea(i2, System.currentTimeMillis());
                        this.j.put(f2, bea);
                    }
                    max = bea.b + (((long) Math.max((bhe.l - bea.a) - 5, 0)) * 30000);
                }
                long max2 = Math.max(bhe.a(), max);
                AmbientModeSupport.AmbientCallback ambientCallback2 = this.i.q;
                long currentTimeMillis = System.currentTimeMillis();
                if (bhe.c == bbx.ENQUEUED) {
                    if (currentTimeMillis < max2) {
                        bdz bdz = this.e;
                        if (bdz != null) {
                            Runnable runnable = (Runnable) bdz.c.remove(bhe.b);
                            if (runnable != null) {
                                bdz.b.a(runnable);
                            }
                            aku aku = new aku((Object) bdz, (Object) bhe, 6, (byte[]) null);
                            bdz.c.put(bhe.b, aku);
                            bdz.b.b(max2 - System.currentTimeMillis(), aku);
                        }
                    } else if (bhe.b()) {
                        baq baq = bhe.k;
                        if (baq.e) {
                            bbk.a();
                            Objects.toString(bhe);
                        } else if (baq.b()) {
                            bbk.a();
                            Objects.toString(bhe);
                        } else {
                            hashSet.add(bhe);
                            hashSet2.add(bhe.b);
                        }
                    } else if (!this.n.f(wg.f(bhe))) {
                        bbk.a();
                        String str = bhe.b;
                        bxq bxq = this.n;
                        jnu.e(bhe, "spec");
                        byw I = bxq.I(wg.f(bhe));
                        this.k.b(I);
                        this.m.F(I);
                    }
                }
            }
        }
        synchronized (this.g) {
            if (!hashSet.isEmpty()) {
                TextUtils.join(",", hashSet2);
                bbk.a();
                for (bhe bhe2 : hashSet) {
                    bgt f3 = wg.f(bhe2);
                    if (!this.d.containsKey(f3)) {
                        this.d.put(f3, bfc.a(this.o, bhe2, (jqp) this.l.d, this));
                    }
                }
            }
        }
    }

    public final boolean d() {
        return false;
    }

    public final void e(bhe bhe, wf wfVar) {
        boolean z = wfVar instanceof beq;
        bgt f2 = wg.f(bhe);
        if (!z) {
            bbk.a();
            Objects.toString(f2);
            f2.toString();
            byw H = this.n.H(f2);
            if (H != null) {
                this.k.a(H);
                this.m.G(H, ((ber) wfVar).a);
            }
        } else if (!this.n.f(f2)) {
            bbk.a();
            Objects.toString(f2);
            f2.toString();
            byw I = this.n.I(f2);
            this.k.b(I);
            this.m.F(I);
        }
    }
}
