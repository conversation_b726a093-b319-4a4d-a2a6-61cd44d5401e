package defpackage;

import android.content.ContentValues;
import android.content.Context;
import android.graphics.drawable.BitmapDrawable;
import android.net.Uri;
import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowInsets;
import android.widget.FrameLayout;
import com.android.car.ui.core.SearchResultsProvider;
import com.android.car.ui.recyclerview.CarUiRecyclerView;

/* renamed from: bnc  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bnc implements View.OnApplyWindowInsetsListener {
    public final /* synthetic */ bne a;

    public /* synthetic */ bnc(bne bne) {
        this.a = bne;
    }

    /* JADX WARNING: type inference failed for: r1v11, types: [java.util.List, java.lang.Object] */
    public final WindowInsets onApplyWindowInsets(View view, WindowInsets windowInsets) {
        FrameLayout frameLayout;
        byte[] bArr;
        String str;
        if (Build.VERSION.SDK_INT >= 30) {
            bne bne = this.a;
            if (bne.d == null) {
                return view.onApplyWindowInsets(windowInsets);
            }
            if (sk$$ExternalSyntheticApiModelOutline1.m(windowInsets, sk$$ExternalSyntheticApiModelOutline1.m())) {
                View a2 = bne.a();
                if (bne.h || a2 == null) {
                    frameLayout = null;
                } else {
                    bne.j = a2.getLayoutParams();
                    bne.i = (ViewGroup) a2.getParent();
                    ViewGroup viewGroup = bne.i;
                    if (viewGroup != null) {
                        viewGroup.removeView(a2);
                    }
                    FrameLayout.LayoutParams layoutParams = new FrameLayout.LayoutParams(-1, -1);
                    Object obj = bne.k.a;
                    if (obj instanceof CarUiRecyclerView) {
                        View view2 = (View) obj;
                        layoutParams.topMargin = -view2.getPaddingTop();
                        layoutParams.bottomMargin = -view2.getPaddingBottom();
                        layoutParams.leftMargin = -view2.getPaddingLeft();
                        layoutParams.rightMargin = -view2.getPaddingRight();
                    }
                    frameLayout = new FrameLayout(bne.b);
                    frameLayout.addView(a2, layoutParams);
                }
                Context context = bne.b;
                Uri a3 = SearchResultsProvider.a(context);
                context.getContentResolver().delete(a3, (String) null, (String[]) null);
                if (bne.a() == null || !bne.b().a) {
                    ? r1 = bne.k.c;
                    if (r1 == 0) {
                        bne.c.sendAppPrivateCommand(bne.d, "automotive_wide_screen", (Bundle) null);
                    } else {
                        for (int i = 0; i < r1.size(); i++) {
                            bla bla = (bla) r1.get(i);
                            ContentValues contentValues = new ContentValues();
                            Integer valueOf = Integer.valueOf(i);
                            contentValues.put("primaryId", valueOf);
                            contentValues.put("secondary", valueOf);
                            BitmapDrawable bitmapDrawable = (BitmapDrawable) bla.a;
                            if (bitmapDrawable != null) {
                                bArr = bnv.q(bitmapDrawable.getBitmap());
                            } else {
                                bArr = null;
                            }
                            contentValues.put("primary_image", bArr);
                            contentValues.put("secondary_image", (byte[]) null);
                            bjj bjj = bla.b;
                            if (bjj != null) {
                                str = bjj.a().toString();
                            } else {
                                str = null;
                            }
                            contentValues.put("title", str);
                            contentValues.put("subtitle", (String) null);
                            bne.b.getContentResolver().insert(a3, contentValues);
                        }
                        bne.c.sendAppPrivateCommand(bne.d, "wide_screen_search_results", new Bundle());
                    }
                }
                bne.a.post(new aku((Object) bne, (Object) frameLayout, 13, (char[]) null));
            } else {
                View a4 = bne.a();
                if (!(bne.i == null || a4 == null)) {
                    bne.a.post(new aku((Object) bne, (Object) a4, 12, (char[]) null));
                }
                bne.h = false;
            }
            return view.onApplyWindowInsets(windowInsets);
        }
        throw new IllegalStateException("Cannot check if the ime is visible pre R");
    }
}
