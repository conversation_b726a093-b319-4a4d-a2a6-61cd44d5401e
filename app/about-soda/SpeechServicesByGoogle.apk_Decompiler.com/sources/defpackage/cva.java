package defpackage;

/* renamed from: cva  reason: default package */
/* compiled from: PG */
public final class cva extends cvb {
    private final csx a;

    public cva(csx csx) {
        this.a = csx;
    }

    public final int b() {
        return 1;
    }

    public final csx d() {
        return this.a;
    }

    public final boolean equals(Object obj) {
        if (obj instanceof cvc) {
            cvc cvc = (cvc) obj;
            if (cvc.b() != 1 || !this.a.equals(cvc.d())) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        csx csx = this.a;
        if (csx.B()) {
            return csx.i();
        }
        int i = csx.memoizedHashCode;
        if (i == 0) {
            i = csx.i();
            csx.memoizedHashCode = i;
        }
        return i;
    }
}
