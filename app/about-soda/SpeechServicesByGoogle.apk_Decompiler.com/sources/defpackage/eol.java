package defpackage;

/* renamed from: eol  reason: default package */
/* compiled from: PG */
public final class eol {
    public final eaj a;
    public final eag b;
    public final String c;

    public eol(eaj eaj, eag eag, String str) {
        jnu.e(eaj, "openingFailure");
        jnu.e(eag, "closingFailure");
        this.a = eaj;
        this.b = eag;
        this.c = str;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof eol)) {
            return false;
        }
        eol eol = (eol) obj;
        if (this.a == eol.a && this.b == eol.b && jnu.i(this.c, eol.c)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (((this.a.hashCode() * 31) + this.b.hashCode()) * 31) + this.c.hashCode();
    }

    public final String toString() {
        return "FailedSessionStatuses(openingFailure=" + this.a + ", closingFailure=" + this.b + ", message=" + this.c + ")";
    }
}
