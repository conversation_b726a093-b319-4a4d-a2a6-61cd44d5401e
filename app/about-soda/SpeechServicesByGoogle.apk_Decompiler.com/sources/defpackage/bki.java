package defpackage;

import android.app.Activity;
import android.content.Context;
import android.os.Build;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.FrameLayout;
import com.android.car.ui.recyclerview.CarUiRecyclerView;
import com.google.android.tts.R;

/* renamed from: bki  reason: default package */
/* compiled from: PG */
public final class bki implements bkd {
    public final View createCarUiPreferenceView(Context context, AttributeSet attributeSet) {
        return blf.e(context, attributeSet);
    }

    public final CarUiRecyclerView createRecyclerView(Context context, AttributeSet attributeSet) {
        return new blp(context, attributeSet);
    }

    public final bok createTextView(Context context, AttributeSet attributeSet) {
        return new bol(context, attributeSet);
    }

    public final bnq installBaseLayoutAround(Context context, View view, bjw bjw, boolean z, boolean z2) {
        boolean z3;
        int i;
        if (Build.VERSION.SDK_INT <= 29) {
            z3 = true;
        } else {
            z3 = false;
        }
        if (!z) {
            i = R.layout.car_ui_base_layout;
        } else if (!z3) {
            i = R.layout.car_ui_base_layout_toolbar;
        } else if (true != view.getResources().getBoolean(R.bool.car_ui_toolbar_tabs_on_second_row)) {
            i = R.layout.car_ui_base_layout_toolbar_legacy;
        } else {
            i = R.layout.car_ui_base_layout_toolbar_legacy_two_row;
        }
        bnu bnu = null;
        View inflate = LayoutInflater.from(context).inflate(i, (ViewGroup) null, false);
        ViewGroup viewGroup = (ViewGroup) view.getParent();
        int indexOfChild = viewGroup.indexOfChild(view);
        viewGroup.removeView(view);
        viewGroup.addView(inflate, indexOfChild, view.getLayoutParams());
        ((FrameLayout) bnv.i(inflate, R.id.car_ui_base_layout_content_container)).addView(view, new FrameLayout.LayoutParams(-1, -1));
        if (z) {
            if (z3) {
                bnu = new bnu(context, bnv.i(inflate, R.id.car_ui_toolbar));
            } else {
                bnu = new bnu(context, inflate);
            }
        }
        if (Build.VERSION.SDK_INT >= 32 && context.getResources().getBoolean(R.bool.car_ui_omit_display_cut_out_insets)) {
            Context f = bnv.f(context);
            if (f instanceof Activity) {
                Window window = ((Activity) f).getWindow();
                if (window.getAttributes().layoutInDisplayCutoutMode == 3) {
                    window.getDecorView().setOnApplyWindowInsetsListener(new bkf(0));
                }
            }
        }
        new bkh(inflate, view).g = bjw;
        return bnu;
    }
}
