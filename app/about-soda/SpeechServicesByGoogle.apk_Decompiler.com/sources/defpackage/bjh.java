package defpackage;

import android.content.DialogInterface;
import android.view.View;
import android.view.ViewGroup;

/* renamed from: bjh  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bjh implements DialogInterface.OnDismissListener {
    public final /* synthetic */ bji a;

    public /* synthetic */ bjh(bji bji) {
        this.a = bji;
    }

    public final void onDismiss(DialogInterface dialogInterface) {
        ViewGroup viewGroup = this.a.d;
        if (viewGroup != null) {
            viewGroup.setOnApplyWindowInsetsListener((View.OnApplyWindowInsetsListener) null);
        }
    }
}
