package defpackage;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Collection;
import java.util.Iterator;
import java.util.Locale;
import java.util.concurrent.CancellationException;
import java.util.concurrent.ExecutionException;

/* renamed from: cry  reason: default package */
/* compiled from: PG */
public final class cry extends Exception {
    public final gxq a;

    private cry(String str, Throwable th, gxq gxq) {
        super(str, th);
        this.a = gxq;
    }

    static Throwable a(Throwable th) {
        Throwable cause = th.getCause();
        if (cause != null && th.getClass().equals(ExecutionException.class)) {
            return a(cause);
        }
        return th;
    }

    public static void b(Collection collection, Object... objArr) {
        PrintWriter printWriter;
        Iterator it = collection.iterator();
        gxl gxl = null;
        while (it.hasNext()) {
            try {
                hfc.S((hme) it.next());
            } catch (CancellationException | ExecutionException e) {
                if (gxl == null) {
                    gxl = new gxl();
                }
                gxl.h(a(e));
            }
        }
        if (gxl != null) {
            gxq g = gxl.g();
            String format = String.format(Locale.US, "Failed to download file group %s", objArr);
            int i = ((hal) g).c;
            if (i > 1) {
                String str = format + "\n" + i + " failure(s) in total:\n";
                try {
                    StringWriter stringWriter = new StringWriter();
                    try {
                        printWriter = new PrintWriter(stringWriter);
                        printWriter.println(str);
                        int i2 = 0;
                        while (i2 < ((hal) g).c) {
                            i2++;
                            printWriter.printf("--- Failure %d ----------------------------\n", new Object[]{Integer.valueOf(i2)});
                            printWriter.println(c((Throwable) g.get(i2), 1));
                        }
                        printWriter.println("-------------------------------------------");
                        format = stringWriter.toString();
                        printWriter.close();
                        stringWriter.close();
                    } catch (Throwable th) {
                        stringWriter.close();
                        throw th;
                    }
                } catch (Throwable th2) {
                    format = "Failed to build string from throwables: ".concat(th2.toString());
                }
            }
            throw new cry(format, (Throwable) g.get(0), g);
        }
        return;
        throw th;
    }

    private static String c(Throwable th, int i) {
        String str = th.getClass().getName() + ": " + th.getMessage();
        Throwable cause = th.getCause();
        if (cause == null) {
            return str;
        }
        if (i >= 5) {
            return str.concat("\n(...)");
        }
        return str + "\nCaused by: " + c(cause, i + 1);
    }
}
