package j$.time;

import j$.time.chrono.C0010i;
import j$.time.chrono.u;
import j$.time.format.s;
import j$.time.temporal.a;
import j$.time.temporal.k;
import j$.time.temporal.l;
import j$.time.temporal.m;
import j$.time.temporal.p;
import j$.time.temporal.r;
import j$.time.temporal.t;
import j$.util.Objects;
import java.io.DataOutput;
import java.io.InvalidObjectException;
import java.io.ObjectInput;
import java.io.ObjectInputStream;
import java.io.Serializable;

public final class n implements m, j$.time.temporal.n, Comparable, Serializable {
    private static final long serialVersionUID = -939150713474957432L;
    private final int a;
    private final int b;

    static {
        s sVar = new s();
        sVar.f("--");
        sVar.o(a.MONTH_OF_YEAR, 2);
        sVar.e('-');
        sVar.o(a.DAY_OF_MONTH, 2);
        sVar.x();
    }

    private n(int i, int i2) {
        this.a = i;
        this.b = i2;
    }

    static n I(ObjectInput objectInput) {
        byte readByte = objectInput.readByte();
        byte readByte2 = objectInput.readByte();
        l L = l.L(readByte);
        Objects.requireNonNull(L, "month");
        a.DAY_OF_MONTH.I((long) readByte2);
        if (readByte2 <= L.K()) {
            return new n(L.getValue(), readByte2);
        }
        String name = L.name();
        throw new RuntimeException("Illegal value for DayOfMonth field, value " + readByte2 + " is not valid for month " + name);
    }

    private void readObject(ObjectInputStream objectInputStream) {
        throw new InvalidObjectException("Deserialization via serialization delegate");
    }

    private Object writeReplace() {
        return new s((byte) 13, this);
    }

    public final long B(p pVar) {
        int i;
        if (!(pVar instanceof a)) {
            return pVar.g(this);
        }
        int i2 = m.a[((a) pVar).ordinal()];
        if (i2 == 1) {
            i = this.b;
        } else if (i2 == 2) {
            i = this.a;
        } else {
            throw new RuntimeException("Unsupported field: ".concat(String.valueOf(pVar)));
        }
        return (long) i;
    }

    public final Object F(r rVar) {
        if (rVar == l.e()) {
            return u.d;
        }
        return l.c(this, rVar);
    }

    /* access modifiers changed from: package-private */
    public final void J(DataOutput dataOutput) {
        dataOutput.writeByte(this.a);
        dataOutput.writeByte(this.b);
    }

    public final int compareTo(Object obj) {
        n nVar = (n) obj;
        int i = this.a - nVar.a;
        if (i == 0) {
            return this.b - nVar.b;
        }
        return i;
    }

    public final boolean e(p pVar) {
        if (pVar instanceof a) {
            if (pVar == a.MONTH_OF_YEAR || pVar == a.DAY_OF_MONTH) {
                return true;
            }
            return false;
        } else if (pVar == null || !pVar.p(this)) {
            return false;
        } else {
            return true;
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:5:0x0009, code lost:
        r5 = (j$.time.n) r5;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean equals(java.lang.Object r5) {
        /*
            r4 = this;
            r0 = 1
            if (r4 != r5) goto L_0x0004
            return r0
        L_0x0004:
            boolean r1 = r5 instanceof j$.time.n
            r2 = 0
            if (r1 == 0) goto L_0x001a
            j$.time.n r5 = (j$.time.n) r5
            int r1 = r5.a
            int r3 = r4.a
            if (r3 != r1) goto L_0x0018
            int r1 = r4.b
            int r5 = r5.b
            if (r1 != r5) goto L_0x0018
            goto L_0x0019
        L_0x0018:
            r0 = 0
        L_0x0019:
            return r0
        L_0x001a:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: j$.time.n.equals(java.lang.Object):boolean");
    }

    public final k g(k kVar) {
        if (C0010i.p(kVar).equals(u.d)) {
            k d = kVar.d((long) this.a, a.MONTH_OF_YEAR);
            a aVar = a.DAY_OF_MONTH;
            return d.d(Math.min(d.t(aVar).d(), (long) this.b), aVar);
        }
        throw new RuntimeException("Adjustment only supported on ISO date-time");
    }

    public final int hashCode() {
        return (this.a << 6) + this.b;
    }

    public final int p(p pVar) {
        return t(pVar).a(B(pVar), pVar);
    }

    public final t t(p pVar) {
        int i;
        if (pVar == a.MONTH_OF_YEAR) {
            return pVar.m();
        }
        if (pVar != a.DAY_OF_MONTH) {
            return l.d(this, pVar);
        }
        int i2 = this.a;
        int ordinal = l.L(i2).ordinal();
        if (ordinal == 1) {
            i = 28;
        } else if (ordinal == 3 || ordinal == 5 || ordinal == 8 || ordinal == 10) {
            i = 30;
        } else {
            i = 31;
        }
        return t.k(1, (long) i, (long) l.L(i2).K());
    }

    public final String toString() {
        String str;
        String str2;
        StringBuilder sb = new StringBuilder(10);
        sb.append("--");
        int i = this.a;
        if (i < 10) {
            str = "0";
        } else {
            str = "";
        }
        sb.append(str);
        sb.append(i);
        int i2 = this.b;
        if (i2 < 10) {
            str2 = "-0";
        } else {
            str2 = "-";
        }
        sb.append(str2);
        sb.append(i2);
        return sb.toString();
    }
}
