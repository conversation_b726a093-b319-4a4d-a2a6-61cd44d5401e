package defpackage;

/* renamed from: ell  reason: default package */
/* compiled from: PG */
public final class ell implements hls {
    final /* synthetic */ hme a;
    public final /* synthetic */ elm b;
    public final /* synthetic */ int c;

    public ell(elm elm, int i, hme hme) {
        this.c = i;
        this.a = hme;
        this.b = elm;
    }

    public final /* bridge */ /* synthetic */ void b(Object obj) {
        eal eal;
        eak eak = (eak) obj;
        if (eak.a == 1) {
            elm elm = this.b;
            htk l = ebu.e.l();
            if (eak.a == 1) {
                eal = eal.b(((Integer) eak.b).intValue());
                if (eal == null) {
                    eal = eal.UNKNOWN_OPENING_SUCCESS;
                }
            } else {
                eal = eal.UNKNOWN_OPENING_SUCCESS;
            }
            if (!l.b.B()) {
                l.u();
            }
            ebu ebu = (ebu) l.b;
            ebu.c = Integer.valueOf(eal.e);
            ebu.b = 2;
            int i = this.c;
            if (!l.b.B()) {
                l.u();
            }
            ebu ebu2 = (ebu) l.b;
            ebu2.d = i - 1;
            ebu2.a |= 1;
            ebu ebu3 = (ebu) l.r();
            elm.b();
            hfc.T(this.a, gof.g(new cmk(this, 5)), this.b.a);
        }
    }

    public final void a(Throwable th) {
    }
}
