package defpackage;

/* renamed from: evu  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class evu implements gsb {
    public final /* synthetic */ bmu a;
    private final /* synthetic */ int b;

    public /* synthetic */ evu(bmu bmu, int i) {
        this.b = i;
        this.a = bmu;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                fqv c = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/flight/flight_landing_gcm_message_receive_latency", new fqx[0]);
                c.c();
                return c;
            case 1:
                fqy g = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/flight/flight_landing_baggage_claim_status", new fqx("app_version", String.class), new fqx("baggage_claim_status", String.class), new fqx("country", String.class), new fqx("airline", String.class), new fqx("destination_airport", String.class));
                g.c();
                return g;
            case 2:
                fqv c2 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/flight/flight_landing_location_mismatch_distance", new fqx[0]);
                c2.c();
                return c2;
            case 3:
                fqy g2 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/flight/flight_landing_precision", new fqx("app_version", String.class), new fqx("flight_landing_precision", String.class));
                g2.c();
                return g2;
            case 4:
                fqy g3 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/flight/flight_landing_weather", new fqx("app_version", String.class), new fqx("flight_landing_weather", String.class));
                g3.c();
                return g3;
            case 5:
                fqv c3 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/flight/flight_status_change_latency", new fqx("flight_status_data_source", String.class));
                c3.c();
                return c3;
            case 6:
                fqy g4 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/flight/flight_status_sync_status", new fqx("app_version", String.class), new fqx("flight_status_sync_status", String.class));
                g4.c();
                return g4;
            case 7:
                fqv c4 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/flight/push_message_latency", new fqx("flight_status_data_source", String.class));
                c4.c();
                return c4;
            case 8:
                fqy g5 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/geo/notification_count", new fqx("card_type", String.class));
                g5.c();
                return g5;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fqv c5 = ((frc) this.a.a).c("/client_streamz/android_gsa/assistant/tapas/monitoring/quantities", new fqx("component", String.class), new fqx("label", String.class));
                c5.c();
                return c5;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fqy g6 = ((frc) this.a.a).g("/client_streamz/android_gsa/appintegration/start_session", new fqx("service_name", String.class), new fqx("client_package", String.class), new fqx("client_version", String.class), new fqx("client_version_code", Integer.class));
                g6.c();
                return g6;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                fqy g7 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/get_cached_content", new fqx("success", Boolean.class), new fqx("status", String.class));
                g7.c();
                return g7;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                fqy g8 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/grocery/card_generation_events", new fqx("shipping_type", String.class), new fqx("event_name", String.class));
                g8.c();
                return g8;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                fqy g9 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/grocery/deletion_request_count", new fqx[0]);
                g9.c();
                return g9;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                fqy g10 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/grocery/delivery_status_count", new fqx("status", String.class));
                g10.c();
                return g10;
            case 15:
                fqy g11 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/grocery/from_new_corpus_count", new fqx("is_from_new_corpus", Boolean.class));
                g11.c();
                return g11;
            case 16:
                fqy g12 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/grocery/pcp_events", new fqx("shipping_type", String.class), new fqx("event_name", String.class));
                g12.c();
                return g12;
            case 17:
                fqy g13 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/grocery/pickup_status_count", new fqx("status", String.class));
                g13.c();
                return g13;
            case 18:
                fqv c6 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/grocery/time_diff_between_acceptance_and_scheduled_window", new fqx("shipping_type", String.class));
                c6.c();
                return c6;
            case 19:
                fqy g14 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/habits/media_profile_age", new fqx("app_version", String.class), new fqx("age_days", Integer.class));
                g14.c();
                return g14;
            default:
                fqy g15 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/verticals/calendar/localcalendar/account", new fqx("is_logged_in", Boolean.class));
                g15.c();
                return g15;
        }
    }
}
