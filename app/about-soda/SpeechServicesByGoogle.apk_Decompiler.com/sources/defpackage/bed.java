package defpackage;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.Context;
import androidx.work.impl.WorkDatabase;
import java.util.Objects;

/* renamed from: bed  reason: default package */
/* compiled from: PG */
final class bed {
    public static final /* synthetic */ int a = 0;

    static {
        bbk.b("Alarms");
    }

    public static void a(Context context, bgt bgt, int i) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService("alarm");
        PendingIntent service = PendingIntent.getService(context, i, bee.c(context, bgt), 603979776);
        if (service != null && alarmManager != null) {
            bbk.a();
            Objects.toString(bgt);
            alarmManager.cancel(service);
        }
    }

    public static void b(Context context, WorkDatabase workDatabase, bgt bgt, long j) {
        bgo x = workDatabase.x();
        bgn f = wf.f(x, bgt);
        if (f != null) {
            a(context, bgt, f.c);
            c(context, bgt, f.c, j);
            return;
        }
        byw byw = new byw(workDatabase, (byte[]) null);
        Object e = ((aus) byw.a).e(new bdr((Object) byw, 3));
        jnu.d(e, "workDatabase.runInTransa…NAGER_ID_KEY) }\n        )");
        int intValue = ((Number) e).intValue();
        x.a(wf.e(bgt, intValue));
        c(context, bgt, intValue, j);
    }

    private static void c(Context context, bgt bgt, int i, long j) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService("alarm");
        PendingIntent service = PendingIntent.getService(context, i, bee.c(context, bgt), 201326592);
        if (alarmManager != null) {
            alarmManager.setExact(0, j, service);
        }
    }
}
