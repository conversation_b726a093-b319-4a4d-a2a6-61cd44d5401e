package defpackage;

/* renamed from: bgn  reason: default package */
/* compiled from: PG */
public final class bgn {
    public final String a;
    public final int b;
    public final int c;

    public bgn(String str, int i, int i2) {
        jnu.e(str, "workSpecId");
        this.a = str;
        this.b = i;
        this.c = i2;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof bgn)) {
            return false;
        }
        bgn bgn = (bgn) obj;
        if (jnu.i(this.a, bgn.a) && this.b == bgn.b && this.c == bgn.c) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (((this.a.hashCode() * 31) + this.b) * 31) + this.c;
    }

    public final String toString() {
        return "SystemIdInfo(workSpecId=" + this.a + ", generation=" + this.b + ", systemId=" + this.c + ')';
    }
}
