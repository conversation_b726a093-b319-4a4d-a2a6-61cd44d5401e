package defpackage;

import android.database.Cursor;
import androidx.wear.ambient.AmbientModeSupport;
import androidx.work.impl.WorkDatabase;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/* renamed from: bct  reason: default package */
/* compiled from: PG */
public final class bct {
    public static final /* synthetic */ int a = 0;

    static {
        bbk.b("Schedulers");
    }

    public static void a(bam bam, WorkDatabase workDatabase, List list) {
        Cursor f;
        auu auu;
        ArrayList arrayList;
        auu auu2;
        boolean z;
        int i;
        String string;
        boolean z2;
        int i2;
        boolean z3;
        int i3;
        boolean z4;
        int i4;
        boolean z5;
        int i5;
        boolean z6;
        int i6;
        String string2;
        boolean z7;
        int i7;
        boolean z8;
        int i8;
        boolean z9;
        int i9;
        boolean z10;
        int i10;
        bam bam2 = bam;
        String str = "next_schedule_time_override";
        String str2 = "generation";
        String str3 = "period_count";
        String str4 = "out_of_quota_policy";
        if (list != null && list.size() != 0) {
            String str5 = "run_in_foreground";
            bhf A = workDatabase.A();
            workDatabase.l();
            String str6 = "schedule_requested_at";
            String str7 = "minimum_retention_duration";
            try {
                auu a2 = auu.a("SELECT * FROM workspec WHERE state=0 AND schedule_requested_at=-1 AND LENGTH(content_uri_triggers)<>0 ORDER BY last_enqueue_time", 0);
                ((bhx) A).a.k();
                bhf bhf = A;
                f = vy.f(((bhx) A).a, a2, false);
                try {
                    int h = vy.h(f, "id");
                    String str8 = "id";
                    int h2 = vy.h(f, "state");
                    String str9 = "state";
                    int h3 = vy.h(f, "worker_class_name");
                    String str10 = "worker_class_name";
                    int h4 = vy.h(f, "input_merger_class_name");
                    String str11 = "input_merger_class_name";
                    int h5 = vy.h(f, "input");
                    String str12 = "input";
                    int h6 = vy.h(f, "output");
                    String str13 = "output";
                    int h7 = vy.h(f, "initial_delay");
                    String str14 = "initial_delay";
                    int h8 = vy.h(f, "interval_duration");
                    String str15 = "interval_duration";
                    int h9 = vy.h(f, "flex_duration");
                    String str16 = "flex_duration";
                    int h10 = vy.h(f, "run_attempt_count");
                    String str17 = "run_attempt_count";
                    int h11 = vy.h(f, "backoff_policy");
                    String str18 = "backoff_policy";
                    int h12 = vy.h(f, "backoff_delay_duration");
                    String str19 = "backoff_delay_duration";
                    int h13 = vy.h(f, "last_enqueue_time");
                    String str20 = "last_enqueue_time";
                    String str21 = str7;
                    auu = a2;
                    int h14 = vy.h(f, str21);
                    String str22 = str21;
                    String str23 = str6;
                    int i11 = h14;
                    int h15 = vy.h(f, str23);
                    String str24 = str23;
                    String str25 = str5;
                    int i12 = h15;
                    int h16 = vy.h(f, str25);
                    String str26 = str25;
                    String str27 = str4;
                    int i13 = h16;
                    int h17 = vy.h(f, str27);
                    String str28 = str27;
                    String str29 = str3;
                    int i14 = h17;
                    int h18 = vy.h(f, str29);
                    String str30 = str29;
                    String str31 = str2;
                    int i15 = h18;
                    int h19 = vy.h(f, str31);
                    String str32 = str31;
                    String str33 = str;
                    int i16 = h19;
                    int h20 = vy.h(f, str33);
                    String str34 = str33;
                    int h21 = vy.h(f, "next_schedule_time_override_generation");
                    int h22 = vy.h(f, "stop_reason");
                    int h23 = vy.h(f, "trace_tag");
                    int h24 = vy.h(f, "required_network_type");
                    int h25 = vy.h(f, "required_network_request");
                    int h26 = vy.h(f, "requires_charging");
                    int h27 = vy.h(f, "requires_device_idle");
                    int h28 = vy.h(f, "requires_battery_not_low");
                    int h29 = vy.h(f, "requires_storage_not_low");
                    int h30 = vy.h(f, "trigger_content_update_delay");
                    int h31 = vy.h(f, "trigger_max_content_delay");
                    int h32 = vy.h(f, "content_uri_triggers");
                    int i17 = h20;
                    ArrayList arrayList2 = new ArrayList(f.getCount());
                    while (true) {
                        arrayList = arrayList2;
                        if (!f.moveToNext()) {
                            break;
                        }
                        String string3 = f.getString(h);
                        bbx q = xm.q(f.getInt(h2));
                        String string4 = f.getString(h3);
                        String string5 = f.getString(h4);
                        bat a3 = bat.a(f.getBlob(h5));
                        bat a4 = bat.a(f.getBlob(h6));
                        long j = f.getLong(h7);
                        long j2 = f.getLong(h8);
                        long j3 = f.getLong(h9);
                        int i18 = f.getInt(h10);
                        bak n = xm.n(f.getInt(h11));
                        long j4 = f.getLong(h12);
                        long j5 = f.getLong(h13);
                        int i19 = i11;
                        long j6 = f.getLong(i19);
                        int i20 = i12;
                        long j7 = f.getLong(i20);
                        int i21 = h6;
                        int i22 = i13;
                        if (f.getInt(i22) != 0) {
                            i13 = i22;
                            i6 = i14;
                            z6 = true;
                        } else {
                            i13 = i22;
                            i6 = i14;
                            z6 = false;
                        }
                        bbt p = xm.p(f.getInt(i6));
                        i14 = i6;
                        int i23 = i15;
                        int i24 = f.getInt(i23);
                        i15 = i23;
                        int i25 = i16;
                        int i26 = f.getInt(i25);
                        i16 = i25;
                        int i27 = i17;
                        long j8 = f.getLong(i27);
                        i17 = i27;
                        int i28 = h21;
                        int i29 = f.getInt(i28);
                        h21 = i28;
                        int i30 = h22;
                        int i31 = f.getInt(i30);
                        h22 = i30;
                        int i32 = h23;
                        if (f.isNull(i32)) {
                            string2 = null;
                        } else {
                            string2 = f.getString(i32);
                        }
                        String str35 = string2;
                        h23 = i32;
                        int i33 = h24;
                        bbl o = xm.o(f.getInt(i33));
                        h24 = i33;
                        int i34 = h25;
                        bie r = xm.r(f.getBlob(i34));
                        h25 = i34;
                        int i35 = h26;
                        if (f.getInt(i35) != 0) {
                            h26 = i35;
                            i7 = h27;
                            z7 = true;
                        } else {
                            h26 = i35;
                            i7 = h27;
                            z7 = false;
                        }
                        if (f.getInt(i7) != 0) {
                            h27 = i7;
                            i8 = h28;
                            z8 = true;
                        } else {
                            h27 = i7;
                            i8 = h28;
                            z8 = false;
                        }
                        if (f.getInt(i8) != 0) {
                            h28 = i8;
                            i9 = h29;
                            z9 = true;
                        } else {
                            h28 = i8;
                            i9 = h29;
                            z9 = false;
                        }
                        if (f.getInt(i9) != 0) {
                            h29 = i9;
                            i10 = h30;
                            z10 = true;
                        } else {
                            h29 = i9;
                            i10 = h30;
                            z10 = false;
                        }
                        long j9 = f.getLong(i10);
                        h30 = i10;
                        int i36 = h31;
                        long j10 = f.getLong(i36);
                        h31 = i36;
                        int i37 = h32;
                        h32 = i37;
                        int i38 = h5;
                        ArrayList arrayList3 = arrayList;
                        arrayList3.add(new bhe(string3, q, string4, string5, a3, a4, j, j2, j3, new baq(r, o, z7, z8, z9, z10, j9, j10, xm.s(f.getBlob(i37))), i18, n, j4, j5, j6, j7, z6, p, i24, i26, j8, i29, i31, str35));
                        i11 = i19;
                        h6 = i21;
                        i12 = i20;
                        arrayList2 = arrayList3;
                        h5 = i38;
                    }
                    ArrayList arrayList4 = arrayList;
                    f.close();
                    auu.j();
                    bam bam3 = bam;
                    String str36 = str12;
                    AmbientModeSupport.AmbientCallback ambientCallback = bam3.q;
                    bhf bhf2 = bhf;
                    b(bhf2, arrayList4);
                    int i39 = bam3.m;
                    auu a5 = auu.a("SELECT * FROM workspec WHERE state=0 AND schedule_requested_at=-1 ORDER BY last_enqueue_time LIMIT (SELECT MAX(?-COUNT(*), 0) FROM workspec WHERE schedule_requested_at<>-1 AND LENGTH(content_uri_triggers)=0 AND state NOT IN (2, 3, 5))", 1);
                    a5.e(1, (long) i39);
                    ((bhx) bhf2).a.k();
                    Cursor f2 = vy.f(((bhx) bhf2).a, a5, false);
                    try {
                        int h33 = vy.h(f2, str8);
                        int h34 = vy.h(f2, str9);
                        int h35 = vy.h(f2, str10);
                        int h36 = vy.h(f2, str11);
                        int h37 = vy.h(f2, str36);
                        int h38 = vy.h(f2, str13);
                        int h39 = vy.h(f2, str14);
                        int h40 = vy.h(f2, str15);
                        int h41 = vy.h(f2, str16);
                        int h42 = vy.h(f2, str17);
                        int h43 = vy.h(f2, str18);
                        ArrayList arrayList5 = arrayList4;
                        int h44 = vy.h(f2, str19);
                        bhf bhf3 = bhf2;
                        int h45 = vy.h(f2, str20);
                        int h46 = vy.h(f2, str22);
                        auu2 = a5;
                        try {
                            int h47 = vy.h(f2, str24);
                            int h48 = vy.h(f2, str26);
                            int h49 = vy.h(f2, str28);
                            int h50 = vy.h(f2, str30);
                            int h51 = vy.h(f2, str32);
                            int h52 = vy.h(f2, str34);
                            int h53 = vy.h(f2, "next_schedule_time_override_generation");
                            int h54 = vy.h(f2, "stop_reason");
                            int h55 = vy.h(f2, "trace_tag");
                            int h56 = vy.h(f2, "required_network_type");
                            int h57 = vy.h(f2, "required_network_request");
                            int h58 = vy.h(f2, "requires_charging");
                            int h59 = vy.h(f2, "requires_device_idle");
                            int h60 = vy.h(f2, "requires_battery_not_low");
                            int h61 = vy.h(f2, "requires_storage_not_low");
                            int h62 = vy.h(f2, "trigger_content_update_delay");
                            int h63 = vy.h(f2, "trigger_max_content_delay");
                            int h64 = vy.h(f2, "content_uri_triggers");
                            int i40 = h46;
                            ArrayList arrayList6 = new ArrayList(f2.getCount());
                            while (f2.moveToNext()) {
                                String string6 = f2.getString(h33);
                                bbx q2 = xm.q(f2.getInt(h34));
                                String string7 = f2.getString(h35);
                                String string8 = f2.getString(h36);
                                bat a6 = bat.a(f2.getBlob(h37));
                                bat a7 = bat.a(f2.getBlob(h38));
                                long j11 = f2.getLong(h39);
                                long j12 = f2.getLong(h40);
                                long j13 = f2.getLong(h41);
                                int i41 = f2.getInt(h42);
                                bak n2 = xm.n(f2.getInt(h43));
                                long j14 = f2.getLong(h44);
                                long j15 = f2.getLong(h45);
                                int i42 = i40;
                                long j16 = f2.getLong(i42);
                                i40 = i42;
                                int i43 = h47;
                                long j17 = f2.getLong(i43);
                                h47 = i43;
                                int i44 = h48;
                                if (f2.getInt(i44) != 0) {
                                    h48 = i44;
                                    i = h49;
                                    z = true;
                                } else {
                                    h48 = i44;
                                    i = h49;
                                    z = false;
                                }
                                bbt p2 = xm.p(f2.getInt(i));
                                h49 = i;
                                int i45 = h50;
                                int i46 = f2.getInt(i45);
                                h50 = i45;
                                int i47 = h51;
                                int i48 = f2.getInt(i47);
                                h51 = i47;
                                int i49 = h52;
                                long j18 = f2.getLong(i49);
                                h52 = i49;
                                int i50 = h53;
                                int i51 = f2.getInt(i50);
                                h53 = i50;
                                int i52 = h54;
                                int i53 = f2.getInt(i52);
                                h54 = i52;
                                int i54 = h55;
                                if (f2.isNull(i54)) {
                                    string = null;
                                } else {
                                    string = f2.getString(i54);
                                }
                                String str37 = string;
                                h55 = i54;
                                int i55 = h56;
                                bbl o2 = xm.o(f2.getInt(i55));
                                h56 = i55;
                                int i56 = h57;
                                bie r2 = xm.r(f2.getBlob(i56));
                                h57 = i56;
                                int i57 = h58;
                                if (f2.getInt(i57) != 0) {
                                    h58 = i57;
                                    i2 = h59;
                                    z2 = true;
                                } else {
                                    h58 = i57;
                                    i2 = h59;
                                    z2 = false;
                                }
                                if (f2.getInt(i2) != 0) {
                                    h59 = i2;
                                    i3 = h60;
                                    z3 = true;
                                } else {
                                    h59 = i2;
                                    i3 = h60;
                                    z3 = false;
                                }
                                if (f2.getInt(i3) != 0) {
                                    h60 = i3;
                                    i4 = h61;
                                    z4 = true;
                                } else {
                                    h60 = i3;
                                    i4 = h61;
                                    z4 = false;
                                }
                                if (f2.getInt(i4) != 0) {
                                    h61 = i4;
                                    i5 = h62;
                                    z5 = true;
                                } else {
                                    h61 = i4;
                                    i5 = h62;
                                    z5 = false;
                                }
                                long j19 = f2.getLong(i5);
                                h62 = i5;
                                int i58 = h63;
                                long j20 = f2.getLong(i58);
                                h63 = i58;
                                int i59 = h64;
                                h64 = i59;
                                arrayList6.add(new bhe(string6, q2, string7, string8, a6, a7, j11, j12, j13, new baq(r2, o2, z2, z3, z4, z5, j19, j20, xm.s(f2.getBlob(i59))), i41, n2, j14, j15, j16, j17, z, p2, i46, i48, j18, i51, i53, str37));
                            }
                            f2.close();
                            auu2.j();
                            AmbientModeSupport.AmbientCallback ambientCallback2 = bam.q;
                            bhf bhf4 = bhf3;
                            b(bhf4, arrayList6);
                            arrayList6.addAll(arrayList5);
                            List k = bhf4.k();
                            workDatabase.o();
                            workDatabase.m();
                            if (arrayList6.size() > 0) {
                                bhe[] bheArr = (bhe[]) arrayList6.toArray(new bhe[arrayList6.size()]);
                                Iterator it = list.iterator();
                                while (it.hasNext()) {
                                    bcr bcr = (bcr) it.next();
                                    if (bcr.d()) {
                                        bcr.c(bheArr);
                                    }
                                }
                            }
                            if (k.size() > 0) {
                                bhe[] bheArr2 = (bhe[]) k.toArray(new bhe[k.size()]);
                                Iterator it2 = list.iterator();
                                while (it2.hasNext()) {
                                    bcr bcr2 = (bcr) it2.next();
                                    if (!bcr2.d()) {
                                        bcr2.c(bheArr2);
                                    }
                                }
                            }
                        } catch (Throwable th) {
                            th = th;
                            f2.close();
                            auu2.j();
                            throw th;
                        }
                    } catch (Throwable th2) {
                        th = th2;
                        auu2 = a5;
                        f2.close();
                        auu2.j();
                        throw th;
                    }
                } catch (Throwable th3) {
                    th = th3;
                    auu = a2;
                    f.close();
                    auu.j();
                    throw th;
                }
            } catch (Throwable th4) {
                workDatabase.m();
                throw th4;
            }
        }
    }

    private static void b(bhf bhf, List list) {
        if (list.size() > 0) {
            long currentTimeMillis = System.currentTimeMillis();
            Iterator it = list.iterator();
            while (it.hasNext()) {
                bhf.l(((bhe) it.next()).b, currentTimeMillis);
            }
        }
    }
}
