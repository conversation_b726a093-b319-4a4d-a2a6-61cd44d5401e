package defpackage;

import android.media.AudioManager;
import android.media.AudioManager$AudioPlaybackCallback;
import android.os.Handler;
import java.util.concurrent.atomic.AtomicBoolean;

/* renamed from: emk  reason: default package */
/* compiled from: PG */
public final class emk {
    private static final hca b = hca.m("com/google/android/libraries/search/audio/micmuter/MicMuter");
    public final AtomicBoolean a = new AtomicBoolean();
    private final AudioManager c;
    private final boolean d;
    private AudioManager$AudioPlaybackCallback e;

    public emk(dze dze, AudioManager audioManager) {
        this.c = audioManager;
        this.d = dze.h;
    }

    private final synchronized void d() {
        if (this.e == null) {
            emj emj = new emj(this);
            this.e = emj;
            this.c.registerAudioPlaybackCallback(emj, (<PERSON><PERSON>) null);
            c(this.c.getActivePlaybackConfigurations());
        }
    }

    private final void e(boolean z) {
        String str;
        if (this.a.getAndSet(z) != z) {
            hby hby = (hby) ((hby) b.f().h(hdg.a, "ALT.MicMuter")).j("com/google/android/libraries/search/audio/micmuter/MicMuter", "setIsTalkbackAudioActive", 93, "MicMuter.java");
            if (true != z) {
                str = "stopped";
            } else {
                str = "started";
            }
            hby.u("#audio# Talkback audio %s", str);
        }
    }

    private final synchronized void f() {
        this.a.set(false);
        AudioManager$AudioPlaybackCallback audioManager$AudioPlaybackCallback = this.e;
        if (audioManager$AudioPlaybackCallback != null) {
            this.c.unregisterAudioPlaybackCallback(audioManager$AudioPlaybackCallback);
            this.e = null;
        }
    }

    public final synchronized void a() {
        f();
    }

    public final synchronized void b() {
        if (this.d) {
            d();
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:16:0x0054, code lost:
        if (r4.readInt() == 2) goto L_0x0056;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void c(java.util.List r9) {
        /*
            r8 = this;
            java.util.Iterator r9 = r9.iterator()
        L_0x0004:
            boolean r0 = r9.hasNext()
            r1 = 0
            if (r0 == 0) goto L_0x0063
            java.lang.Object r0 = r9.next()
            android.media.AudioPlaybackConfiguration r0 = defpackage.ag$$ExternalSyntheticApiModelOutline0.m((java.lang.Object) r0)
            android.media.AudioAttributes r2 = r0.getAudioAttributes()
            int r2 = r2.getUsage()
            android.media.AudioAttributes r3 = r0.getAudioAttributes()
            int r3 = r3.getContentType()
            int r4 = android.os.Build.VERSION.SDK_INT
            r5 = 30
            r6 = 4
            r7 = 1
            if (r4 < r5) goto L_0x0056
            android.os.Parcel r4 = android.os.Parcel.obtain()
            r0.writeToParcel(r4, r1)
            r4.setDataPosition(r1)
            int r0 = android.os.Build.VERSION.SDK_INT
            r5 = 34
            if (r0 < r5) goto L_0x003d
            r0 = 6
            goto L_0x0046
        L_0x003d:
            int r0 = android.os.Build.VERSION.SDK_INT
            r5 = 31
            if (r0 < r5) goto L_0x0045
            r0 = 5
            goto L_0x0046
        L_0x0045:
            r0 = r6
        L_0x0046:
            r5 = r1
        L_0x0047:
            if (r5 >= r0) goto L_0x004f
            r4.readInt()
            int r5 = r5 + 1
            goto L_0x0047
        L_0x004f:
            int r0 = r4.readInt()
            r4 = 2
            if (r0 != r4) goto L_0x0057
        L_0x0056:
            r1 = r7
        L_0x0057:
            r0 = 11
            if (r2 != r0) goto L_0x0004
            if (r3 == r6) goto L_0x0004
            if (r1 == 0) goto L_0x0004
            r8.e(r7)
            return
        L_0x0063:
            r8.e(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.emk.c(java.util.List):void");
    }
}
