package defpackage;

import java.util.ArrayList;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/* renamed from: bca  reason: default package */
/* compiled from: PG */
public abstract class bca {
    public UUID a;
    public bhe b;
    public final Set c;
    private final Class d;

    public bca(Class cls) {
        this.d = cls;
        UUID randomUUID = UUID.randomUUID();
        jnu.d(randomUUID, "randomUUID()");
        this.a = randomUUID;
        String uuid = this.a.toString();
        jnu.d(uuid, "id.toString()");
        String name = cls.getName();
        jnu.d(name, "workerClass.name");
        jnu.e(uuid, "id");
        jnu.e(name, "workerClassName_");
        this.b = new bhe(uuid, (bbx) null, name, (String) null, (bat) null, (bat) null, 0, 0, 0, (baq) null, 0, (bak) null, 0, 0, 0, 0, false, (bbt) null, 0, 0, 0, 0, (String) null, 16777210);
        String name2 = cls.getName();
        jnu.d(name2, "workerClass.name");
        LinkedHashSet linkedHashSet = new LinkedHashSet(jji.e(1));
        jji.am(new String[]{name2}, linkedHashSet);
        this.c = linkedHashSet;
    }

    public abstract bmu a();

    public final void b(baq baq) {
        jnu.e(baq, "constraints");
        this.b.k = baq;
    }

    public final void c(long j, TimeUnit timeUnit) {
        this.b.h = timeUnit.toMillis(j);
        if (Long.MAX_VALUE - System.currentTimeMillis() <= this.b.h) {
            throw new IllegalArgumentException("The given initial delay is too large and will cause an overflow!");
        }
    }

    public final void d(bat bat) {
        jnu.e(bat, "inputData");
        this.b.f = bat;
    }

    public final bmu e() {
        boolean z;
        List list;
        String str;
        bmu a2 = a();
        baq baq = this.b.k;
        if (baq.b() || baq.f || baq.d || baq.e) {
            z = true;
        } else {
            z = false;
        }
        bhe bhe = this.b;
        if (bhe.r) {
            if (z) {
                throw new IllegalArgumentException("Expedited jobs only support network and storage constraints");
            } else if (bhe.h > 0) {
                throw new IllegalArgumentException("Expedited jobs cannot be delayed");
            }
        }
        if (bhe.y == null) {
            String str2 = bhe.d;
            String[] strArr = {"."};
            jnu.e(str2, "<this>");
            String str3 = strArr[0];
            if (str3.length() == 0) {
                jpg<jom> jpg = new jpg(job.D(str2, strArr));
                list = new ArrayList(jji.K(jpg));
                for (jom l : jpg) {
                    list.add(job.l(str2, l));
                }
            } else {
                list = job.H(str2, str3);
            }
            if (list.size() == 1) {
                str = (String) list.get(0);
            } else {
                str = (String) jji.v(list);
            }
            if (str.length() > 127) {
                str = job.q(str, 127);
            }
            bhe.y = str;
        }
        UUID randomUUID = UUID.randomUUID();
        jnu.d(randomUUID, "randomUUID()");
        jnu.e(randomUUID, "id");
        this.a = randomUUID;
        String uuid = randomUUID.toString();
        String str4 = uuid;
        jnu.d(uuid, "id.toString()");
        bhe bhe2 = this.b;
        jnu.e(uuid, "newId");
        jnu.e(bhe2, "other");
        String str5 = bhe2.d;
        bbx bbx = bhe2.c;
        String str6 = bhe2.e;
        bat bat = r2;
        bat bat2 = new bat(bhe2.f);
        bat bat3 = r2;
        bat bat4 = new bat(bhe2.g);
        long j = bhe2.h;
        long j2 = bhe2.i;
        bmu bmu = a2;
        bhe bhe3 = bhe2;
        long j3 = bhe2.j;
        baq baq2 = r1;
        baq baq3 = new baq(bhe3.k);
        this.b = new bhe(str4, bbx, str5, str6, bat, bat3, j, j2, j3, baq2, bhe3.l, bhe3.m, bhe3.n, bhe3.o, bhe3.p, bhe3.q, bhe3.r, bhe3.s, bhe3.t, bhe3.v, bhe3.w, bhe3.x, bhe3.y, 524288);
        return bmu;
    }
}
