package defpackage;

/* renamed from: euu  reason: default package */
/* compiled from: PG */
public final class euu implements euh {
    final /* synthetic */ euv a;
    private final eus b;

    /* JADX WARNING: type inference failed for: r13v4, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r13v6, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r12v4, types: [java.lang.Object, jjk] */
    public euu(euv euv, ebd ebd) {
        jnu.e(ebd, "metadata");
        this.a = euv;
        byw byw = euv.c;
        StringBuilder sb = new StringBuilder();
        gnk gnk = elg.a;
        jnu.d(gnk, "clientInfo");
        sb.append(euv.a(String.valueOf(ehf.a(((ehg) ftc.aR(ebd, gnk)).a).K)));
        gnk gnk2 = elg.c;
        jnu.d(gnk2, "token");
        Object aR = ftc.aR(ebd, gnk2);
        jnu.d(aR, "get(...)");
        sb.append("-".concat(euv.a((String) aR)));
        gnk gnk3 = elg.b;
        jnu.d(gnk3, "source");
        Object aR2 = ftc.aR(ebd, gnk3);
        jnu.d(aR2, "get(...)");
        sb.append("-".concat(euv.a((String) aR2)));
        eup eup = new eup(sb.toString(), ebd.d, ebd.c, ebd.b);
        fpi fpi = (fpi) byw.a;
        this.b = new eus(((iim) fpi.a).a(), (jqs) fpi.g.b(), (cqx) fpi.e.b(), ((ijj) fpi.b).a().booleanValue(), ((ijk) fpi.d).a().longValue(), ((ijk) fpi.f).a().longValue(), (fbi) fpi.c.b(), eup);
    }

    public final hme a() {
        return this.b.a();
    }

    public final hme b(dyc dyc) {
        dyb dyb;
        Integer num;
        jnu.e(dyc, "audioData");
        jnu.e(dyc, "audioData");
        if (dyc.b == 1) {
            dyb = (dyb) dyc.c;
        } else {
            dyb = dyb.c;
        }
        hsq hsq = dyb.b;
        jnu.d(hsq, "getBytes(...)");
        Long l = null;
        if ((dyc.a & 4) != 0) {
            num = Integer.valueOf(dyc.f);
        } else {
            num = null;
        }
        if ((dyc.a & 2) != 0) {
            l = Long.valueOf(dyc.e);
        }
        return this.b.b(hsq, num, l);
    }

    public final hme c(hsq hsq) {
        jnu.e(hsq, "bytes");
        jnu.e(hsq, "bytes");
        return this.b.b(hsq, (Integer) null, (Long) null);
    }
}
