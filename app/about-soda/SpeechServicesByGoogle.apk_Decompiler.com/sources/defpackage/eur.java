package defpackage;

import java.io.RandomAccessFile;
import java.util.Objects;

/* renamed from: eur  reason: default package */
/* compiled from: PG */
public final class eur extends jnv implements jna {
    final /* synthetic */ Long a;
    final /* synthetic */ String b;
    final /* synthetic */ Integer c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eur(Long l, String str, Integer num) {
        super(1);
        this.a = l;
        this.b = str;
        this.c = num;
    }

    public final /* bridge */ /* synthetic */ Object a(Object obj) {
        RandomAccessFile randomAccessFile = (RandomAccessFile) obj;
        jnu.e(randomAccessFile, "$this$runOpsSafely");
        Long l = this.a;
        Objects.toString(l);
        randomAccessFile.writeBytes(l.toString().concat(","));
        randomAccessFile.writeBytes(String.valueOf(this.b).concat(","));
        Integer num = this.c;
        Objects.toString(num);
        randomAccessFile.writeBytes(num.toString().concat("\n"));
        return jkd.a;
    }
}
