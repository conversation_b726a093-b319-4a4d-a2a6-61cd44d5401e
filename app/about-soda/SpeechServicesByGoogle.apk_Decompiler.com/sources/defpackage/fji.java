package defpackage;

import android.content.res.Resources;
import j$.util.concurrent.atomic.DesugarAtomicReference;
import java.io.InputStream;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/* renamed from: fji  reason: default package */
/* compiled from: PG */
public final class fji {
    public final Object a;
    public final Object b;
    public final /* synthetic */ Object c;

    public fji(dvw dvw, String str, int i, int i2) {
        this.c = dvw;
        this.b = str;
        this.a = new dvt(new dvv(this, i, i2));
    }

    public final fjj a() {
        return (fjj) DesugarAtomicReference.updateAndGet((AtomicReference) this.b, new fjh(2));
    }

    public final void b(huz huz, Resources resources, int i) {
        InputStream openRawResource = resources.openRawResource(i);
        try {
            ((dvw) this.c).e.b++;
            ((htk) huz).j(hsu.M(openRawResource, Math.max(512, Math.min(4096, openRawResource.available()))), hte.a());
            if (openRawResource != null) {
                openRawResource.close();
                return;
            }
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public fji(gnk gnk) {
        this.c = gnk;
        gnk gnk2 = gnk;
        this.a = Long.toString(((AtomicLong) gnk.d).getAndIncrement());
        this.b = new AtomicReference(new fjj(true, 0));
    }
}
