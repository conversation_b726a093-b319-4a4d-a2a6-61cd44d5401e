package defpackage;

/* renamed from: esc  reason: default package */
/* compiled from: PG */
final class esc extends jme {
    /* synthetic */ Object a;
    final /* synthetic */ ese b;
    int c;
    ese d;
    jqh e;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public esc(ese ese, jlr jlr) {
        super(jlr);
        this.b = ese;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.c |= Integer.MIN_VALUE;
        return this.b.f(this);
    }
}
