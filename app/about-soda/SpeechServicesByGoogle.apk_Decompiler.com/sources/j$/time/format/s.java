package j$.time.format;

import j$.time.chrono.u;
import j$.time.temporal.a;
import j$.time.temporal.h;
import j$.time.temporal.j;
import j$.time.temporal.p;
import j$.util.Objects;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Locale;

public final class s {
    private static final C0014a h = new Object();
    private static final HashMap i;
    private s a;
    private final s b;
    private final ArrayList c;
    private final boolean d;
    private int e;
    private char f;
    private int g;

    /* JADX WARNING: type inference failed for: r0v0, types: [j$.time.format.a, java.lang.Object] */
    static {
        HashMap hashMap = new HashMap();
        i = hashMap;
        hashMap.put('G', a.ERA);
        hashMap.put('y', a.YEAR_OF_ERA);
        hashMap.put('u', a.YEAR);
        p pVar = h.a;
        hashMap.put('Q', pVar);
        hashMap.put('q', pVar);
        a aVar = a.MONTH_OF_YEAR;
        hashMap.put('M', aVar);
        hashMap.put('L', aVar);
        hashMap.put('D', a.DAY_OF_YEAR);
        hashMap.put('d', a.DAY_OF_MONTH);
        hashMap.put('F', a.ALIGNED_DAY_OF_WEEK_IN_MONTH);
        a aVar2 = a.DAY_OF_WEEK;
        hashMap.put('E', aVar2);
        hashMap.put('c', aVar2);
        hashMap.put('e', aVar2);
        hashMap.put('a', a.AMPM_OF_DAY);
        hashMap.put('H', a.HOUR_OF_DAY);
        hashMap.put('k', a.CLOCK_HOUR_OF_DAY);
        hashMap.put('K', a.HOUR_OF_AMPM);
        hashMap.put('h', a.CLOCK_HOUR_OF_AMPM);
        hashMap.put('m', a.MINUTE_OF_HOUR);
        hashMap.put('s', a.SECOND_OF_MINUTE);
        a aVar3 = a.NANO_OF_SECOND;
        hashMap.put('S', aVar3);
        hashMap.put('A', a.MILLI_OF_DAY);
        hashMap.put('n', aVar3);
        hashMap.put('N', a.NANO_OF_DAY);
        hashMap.put('g', j.a);
    }

    public s() {
        this.a = this;
        this.c = new ArrayList();
        this.g = -1;
        this.b = null;
        this.d = false;
    }

    private int d(f fVar) {
        Objects.requireNonNull(fVar, "pp");
        s sVar = this.a;
        int i2 = sVar.e;
        if (i2 > 0) {
            if (fVar != null) {
                fVar = new l(fVar, i2, sVar.f);
            }
            sVar.e = 0;
            sVar.f = 0;
        }
        sVar.c.add(fVar);
        s sVar2 = this.a;
        sVar2.g = -1;
        return sVar2.c.size() - 1;
    }

    private void m(j jVar) {
        j jVar2;
        s sVar = this.a;
        int i2 = sVar.g;
        if (i2 >= 0) {
            j jVar3 = (j) sVar.c.get(i2);
            int i3 = jVar.b;
            int i4 = jVar.c;
            if (i3 == i4 && jVar.d == A.NOT_NEGATIVE) {
                jVar2 = jVar3.d(i4);
                d(jVar.c());
                this.a.g = i2;
            } else {
                jVar2 = jVar3.c();
                this.a.g = d(jVar);
            }
            this.a.c.set(i2, jVar2);
            return;
        }
        sVar.g = d(jVar);
    }

    private DateTimeFormatter z(Locale locale, z zVar, u uVar) {
        Objects.requireNonNull(locale, "locale");
        while (this.a.b != null) {
            r();
        }
        e eVar = new e(this.c, false);
        y yVar = y.a;
        return new DateTimeFormatter(eVar, locale, zVar, uVar);
    }

    public final void a(DateTimeFormatter dateTimeFormatter) {
        Objects.requireNonNull(dateTimeFormatter, "formatter");
        d(dateTimeFormatter.e());
    }

    public final void b(a aVar, int i2, int i3, boolean z) {
        if (i2 != i3 || z) {
            d(new g(aVar, i2, i3, z));
        } else {
            m(new g(aVar, i2, i3, z));
        }
    }

    /* JADX WARNING: type inference failed for: r0v0, types: [j$.time.format.f, java.lang.Object] */
    public final void c() {
        d(new Object());
    }

    public final void e(char c2) {
        d(new d(c2));
    }

    public final void f(String str) {
        Objects.requireNonNull(str, "literal");
        if (str.isEmpty()) {
            return;
        }
        if (str.length() == 1) {
            d(new d(str.charAt(0)));
        } else {
            d(new i(1, str));
        }
    }

    public final void g(B b2) {
        Objects.requireNonNull(b2, "style");
        if (b2 == B.FULL || b2 == B.SHORT) {
            d(new i(0, b2));
            return;
        }
        throw new IllegalArgumentException("Style must be either full or short");
    }

    public final void h(String str, String str2) {
        d(new k(str, str2));
    }

    public final void i() {
        d(k.e);
    }

    /* JADX WARNING: Removed duplicated region for block: B:138:0x0236  */
    /* JADX WARNING: Removed duplicated region for block: B:139:0x0245  */
    /* JADX WARNING: Removed duplicated region for block: B:246:0x045b  */
    /* JADX WARNING: Removed duplicated region for block: B:275:0x0474 A[SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void j(java.lang.String r17) {
        /*
            r16 = this;
            r0 = r16
            r1 = r17
            java.lang.String r2 = "pattern"
            j$.util.Objects.requireNonNull(r1, r2)
            r2 = 0
            r3 = 0
        L_0x000b:
            int r4 = r17.length()
            if (r3 >= r4) goto L_0x04c7
            char r4 = r1.charAt(r3)
            r5 = 1
            r6 = 122(0x7a, float:1.71E-43)
            r7 = 97
            r8 = 90
            r9 = 65
            if (r4 < r9) goto L_0x0022
            if (r4 <= r8) goto L_0x0026
        L_0x0022:
            if (r4 < r7) goto L_0x042f
            if (r4 > r6) goto L_0x042f
        L_0x0026:
            int r10 = r3 + 1
        L_0x0028:
            int r11 = r17.length()
            if (r10 >= r11) goto L_0x0037
            char r11 = r1.charAt(r10)
            if (r11 != r4) goto L_0x0037
            int r10 = r10 + 1
            goto L_0x0028
        L_0x0037:
            int r3 = r10 - r3
            r11 = 112(0x70, float:1.57E-43)
            r12 = -1
            if (r4 != r11) goto L_0x0098
            int r11 = r17.length()
            if (r10 >= r11) goto L_0x0064
            char r4 = r1.charAt(r10)
            if (r4 < r9) goto L_0x004c
            if (r4 <= r8) goto L_0x0050
        L_0x004c:
            if (r4 < r7) goto L_0x0064
            if (r4 > r6) goto L_0x0064
        L_0x0050:
            int r11 = r10 + 1
        L_0x0052:
            int r13 = r17.length()
            if (r11 >= r13) goto L_0x0061
            char r13 = r1.charAt(r11)
            if (r13 != r4) goto L_0x0061
            int r11 = r11 + 1
            goto L_0x0052
        L_0x0061:
            int r10 = r11 - r10
            goto L_0x0067
        L_0x0064:
            r11 = r10
            r10 = r3
            r3 = 0
        L_0x0067:
            if (r3 == 0) goto L_0x008c
            if (r3 < r5) goto L_0x0078
            j$.time.format.s r13 = r0.a
            r13.e = r3
            r3 = 32
            r13.f = r3
            r13.g = r12
            r3 = r10
            r13 = r11
            goto L_0x0099
        L_0x0078:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            java.lang.String r4 = "The pad width must be at least one but was "
            r2.<init>(r4)
            r2.append(r3)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x008c:
            java.lang.IllegalArgumentException r2 = new java.lang.IllegalArgumentException
            java.lang.String r3 = "Pad letter 'p' must be followed by valid pad pattern: "
            java.lang.String r1 = r3.concat(r1)
            r2.<init>(r1)
            throw r2
        L_0x0098:
            r13 = r10
        L_0x0099:
            java.util.HashMap r10 = i
            java.lang.Character r11 = java.lang.Character.valueOf(r4)
            java.lang.Object r10 = r10.get(r11)
            j$.time.temporal.p r10 = (j$.time.temporal.p) r10
            r11 = 2
            r14 = 5
            java.lang.String r15 = "Too many pattern letters: "
            r12 = 4
            if (r10 == 0) goto L_0x0261
            r6 = 19
            if (r4 == r9) goto L_0x0259
            r8 = 81
            r9 = 3
            if (r4 == r8) goto L_0x01f4
            r8 = 83
            if (r4 == r8) goto L_0x01ed
            if (r4 == r7) goto L_0x01d2
            r7 = 107(0x6b, float:1.5E-43)
            if (r4 == r7) goto L_0x01b2
            r7 = 113(0x71, float:1.58E-43)
            if (r4 == r7) goto L_0x01b0
            r7 = 115(0x73, float:1.61E-43)
            if (r4 == r7) goto L_0x01b2
            r7 = 117(0x75, float:1.64E-43)
            if (r4 == r7) goto L_0x0188
            r7 = 121(0x79, float:1.7E-43)
            if (r4 == r7) goto L_0x0188
            r7 = 103(0x67, float:1.44E-43)
            if (r4 == r7) goto L_0x0181
            r7 = 104(0x68, float:1.46E-43)
            if (r4 == r7) goto L_0x01b2
            r7 = 109(0x6d, float:1.53E-43)
            if (r4 == r7) goto L_0x01b2
            r7 = 110(0x6e, float:1.54E-43)
            if (r4 == r7) goto L_0x0259
            switch(r4) {
                case 68: goto L_0x015c;
                case 69: goto L_0x01f4;
                case 70: goto L_0x0143;
                case 71: goto L_0x0112;
                case 72: goto L_0x01b2;
                default: goto L_0x00e2;
            }
        L_0x00e2:
            switch(r4) {
                case 75: goto L_0x01b2;
                case 76: goto L_0x01b0;
                case 77: goto L_0x01f4;
                case 78: goto L_0x0259;
                default: goto L_0x00e5;
            }
        L_0x00e5:
            switch(r4) {
                case 99: goto L_0x00f4;
                case 100: goto L_0x01b2;
                case 101: goto L_0x01f4;
                default: goto L_0x00e8;
            }
        L_0x00e8:
            if (r3 != r5) goto L_0x00ef
            r0.n(r10)
            goto L_0x025e
        L_0x00ef:
            r0.o(r10, r3)
            goto L_0x025e
        L_0x00f4:
            if (r3 != r5) goto L_0x0106
            j$.time.format.p r12 = new j$.time.format.p
            r11 = 0
            r6 = r12
            r7 = r4
            r8 = r3
            r9 = r3
            r10 = r3
            r6.<init>(r7, r8, r9, r10, r11)
            r0.m(r12)
            goto L_0x025e
        L_0x0106:
            if (r3 == r11) goto L_0x010a
            goto L_0x01b0
        L_0x010a:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.String r2 = "Invalid pattern \"cc\""
            r1.<init>(r2)
            throw r1
        L_0x0112:
            if (r3 == r5) goto L_0x013c
            if (r3 == r11) goto L_0x013c
            if (r3 == r9) goto L_0x013c
            if (r3 == r12) goto L_0x0135
            if (r3 != r14) goto L_0x0123
            j$.time.format.B r3 = j$.time.format.B.NARROW
            r0.l(r10, r3)
            goto L_0x025e
        L_0x0123:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>(r15)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x0135:
            j$.time.format.B r3 = j$.time.format.B.FULL
            r0.l(r10, r3)
            goto L_0x025e
        L_0x013c:
            j$.time.format.B r3 = j$.time.format.B.SHORT
            r0.l(r10, r3)
            goto L_0x025e
        L_0x0143:
            if (r3 != r5) goto L_0x014a
            r0.n(r10)
            goto L_0x025e
        L_0x014a:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>(r15)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x015c:
            if (r3 != r5) goto L_0x0163
            r0.n(r10)
            goto L_0x025e
        L_0x0163:
            if (r3 == r11) goto L_0x017a
            if (r3 != r9) goto L_0x0168
            goto L_0x017a
        L_0x0168:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>(r15)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x017a:
            j$.time.format.A r4 = j$.time.format.A.NOT_NEGATIVE
            r0.p(r10, r3, r9, r4)
            goto L_0x025e
        L_0x0181:
            j$.time.format.A r4 = j$.time.format.A.NORMAL
            r0.p(r10, r3, r6, r4)
            goto L_0x025e
        L_0x0188:
            if (r3 != r11) goto L_0x01a0
            j$.time.g r3 = j$.time.format.m.h
            java.lang.String r4 = "field"
            j$.util.Objects.requireNonNull(r10, r4)
            java.lang.String r4 = "baseDate"
            j$.util.Objects.requireNonNull(r3, r4)
            j$.time.format.m r4 = new j$.time.format.m
            r4.<init>(r10, r3)
            r0.m(r4)
            goto L_0x025e
        L_0x01a0:
            if (r3 >= r12) goto L_0x01a9
            j$.time.format.A r4 = j$.time.format.A.NORMAL
            r0.p(r10, r3, r6, r4)
            goto L_0x025e
        L_0x01a9:
            j$.time.format.A r4 = j$.time.format.A.EXCEEDS_PAD
            r0.p(r10, r3, r6, r4)
            goto L_0x025e
        L_0x01b0:
            r6 = 1
            goto L_0x01f5
        L_0x01b2:
            if (r3 != r5) goto L_0x01b9
            r0.n(r10)
            goto L_0x025e
        L_0x01b9:
            if (r3 != r11) goto L_0x01c0
            r0.o(r10, r3)
            goto L_0x025e
        L_0x01c0:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>(r15)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x01d2:
            if (r3 != r5) goto L_0x01db
            j$.time.format.B r3 = j$.time.format.B.SHORT
            r0.l(r10, r3)
            goto L_0x025e
        L_0x01db:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>(r15)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x01ed:
            j$.time.temporal.a r4 = j$.time.temporal.a.NANO_OF_SECOND
            r0.b(r4, r3, r3, r2)
            goto L_0x025e
        L_0x01f4:
            r6 = 0
        L_0x01f5:
            if (r3 == r5) goto L_0x0232
            if (r3 == r11) goto L_0x0232
            if (r3 == r9) goto L_0x0227
            if (r3 == r12) goto L_0x021c
            if (r3 != r14) goto L_0x020a
            if (r6 == 0) goto L_0x0204
            j$.time.format.B r3 = j$.time.format.B.NARROW_STANDALONE
            goto L_0x0206
        L_0x0204:
            j$.time.format.B r3 = j$.time.format.B.NARROW
        L_0x0206:
            r0.l(r10, r3)
            goto L_0x025e
        L_0x020a:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>(r15)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x021c:
            if (r6 == 0) goto L_0x0221
            j$.time.format.B r3 = j$.time.format.B.FULL_STANDALONE
            goto L_0x0223
        L_0x0221:
            j$.time.format.B r3 = j$.time.format.B.FULL
        L_0x0223:
            r0.l(r10, r3)
            goto L_0x025e
        L_0x0227:
            if (r6 == 0) goto L_0x022c
            j$.time.format.B r3 = j$.time.format.B.SHORT_STANDALONE
            goto L_0x022e
        L_0x022c:
            j$.time.format.B r3 = j$.time.format.B.SHORT
        L_0x022e:
            r0.l(r10, r3)
            goto L_0x025e
        L_0x0232:
            r6 = 101(0x65, float:1.42E-43)
            if (r4 != r6) goto L_0x0245
            j$.time.format.p r12 = new j$.time.format.p
            r11 = 0
            r6 = r12
            r7 = r4
            r8 = r3
            r9 = r3
            r10 = r3
            r6.<init>(r7, r8, r9, r10, r11)
            r0.m(r12)
            goto L_0x025e
        L_0x0245:
            r6 = 69
            if (r4 != r6) goto L_0x024f
            j$.time.format.B r3 = j$.time.format.B.SHORT
            r0.l(r10, r3)
            goto L_0x025e
        L_0x024f:
            if (r3 != r5) goto L_0x0255
            r0.n(r10)
            goto L_0x025e
        L_0x0255:
            r0.o(r10, r11)
            goto L_0x025e
        L_0x0259:
            j$.time.format.A r4 = j$.time.format.A.NOT_NEGATIVE
            r0.p(r10, r3, r6, r4)
        L_0x025e:
            r3 = -1
            goto L_0x0418
        L_0x0261:
            if (r4 != r6) goto L_0x028f
            if (r3 > r12) goto L_0x027d
            if (r3 != r12) goto L_0x0272
            j$.time.format.B r3 = j$.time.format.B.FULL
            j$.time.format.r r4 = new j$.time.format.r
            r4.<init>(r3, r2)
            r0.d(r4)
            goto L_0x025e
        L_0x0272:
            j$.time.format.B r3 = j$.time.format.B.SHORT
            j$.time.format.r r4 = new j$.time.format.r
            r4.<init>(r3, r2)
            r0.d(r4)
            goto L_0x025e
        L_0x027d:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>(r15)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x028f:
            r6 = 86
            if (r4 != r6) goto L_0x02b8
            if (r3 != r11) goto L_0x02a4
            j$.time.format.q r3 = new j$.time.format.q
            j$.time.temporal.r r4 = j$.time.temporal.l.k()
            java.lang.String r6 = "ZoneId()"
            r3.<init>(r4, r6)
            r0.d(r3)
            goto L_0x025e
        L_0x02a4:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            java.lang.String r3 = "Pattern letter count must be 2: "
            r2.<init>(r3)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x02b8:
            r6 = 118(0x76, float:1.65E-43)
            if (r4 != r6) goto L_0x02ea
            if (r3 != r5) goto L_0x02c9
            j$.time.format.B r3 = j$.time.format.B.SHORT
            j$.time.format.r r4 = new j$.time.format.r
            r4.<init>(r3, r5)
            r0.d(r4)
            goto L_0x025e
        L_0x02c9:
            if (r3 != r12) goto L_0x02d6
            j$.time.format.B r3 = j$.time.format.B.FULL
            j$.time.format.r r4 = new j$.time.format.r
            r4.<init>(r3, r5)
            r0.d(r4)
            goto L_0x025e
        L_0x02d6:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            java.lang.String r3 = "Wrong number of  pattern letters: "
            r2.<init>(r3)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x02ea:
            java.lang.String r6 = "Z"
            java.lang.String r7 = "+0000"
            if (r4 != r8) goto L_0x031d
            if (r3 >= r12) goto L_0x02f9
            java.lang.String r3 = "+HHMM"
            r0.h(r3, r7)
            goto L_0x025e
        L_0x02f9:
            if (r3 != r12) goto L_0x0302
            j$.time.format.B r3 = j$.time.format.B.FULL
            r0.g(r3)
            goto L_0x025e
        L_0x0302:
            if (r3 != r14) goto L_0x030b
            java.lang.String r3 = "+HH:MM:ss"
            r0.h(r3, r6)
            goto L_0x025e
        L_0x030b:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>(r15)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x031d:
            r8 = 79
            if (r4 != r8) goto L_0x0347
            if (r3 != r5) goto L_0x032a
            j$.time.format.B r3 = j$.time.format.B.SHORT
            r0.g(r3)
            goto L_0x025e
        L_0x032a:
            if (r3 != r12) goto L_0x0333
            j$.time.format.B r3 = j$.time.format.B.FULL
            r0.g(r3)
            goto L_0x025e
        L_0x0333:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            java.lang.String r3 = "Pattern letter count must be 1 or 4: "
            r2.<init>(r3)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x0347:
            r8 = 88
            if (r4 != r8) goto L_0x036e
            if (r3 > r14) goto L_0x035c
            java.lang.String[] r4 = j$.time.format.k.d
            if (r3 != r5) goto L_0x0353
            r7 = 0
            goto L_0x0354
        L_0x0353:
            r7 = 1
        L_0x0354:
            int r3 = r3 + r7
            r3 = r4[r3]
            r0.h(r3, r6)
            goto L_0x025e
        L_0x035c:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>(r15)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x036e:
            r6 = 120(0x78, float:1.68E-43)
            if (r4 != r6) goto L_0x03a1
            if (r3 > r14) goto L_0x038f
            if (r3 != r5) goto L_0x0379
            java.lang.String r7 = "+00"
            goto L_0x0380
        L_0x0379:
            int r4 = r3 % 2
            if (r4 != 0) goto L_0x037e
            goto L_0x0380
        L_0x037e:
            java.lang.String r7 = "+00:00"
        L_0x0380:
            java.lang.String[] r4 = j$.time.format.k.d
            if (r3 != r5) goto L_0x0386
            r6 = 0
            goto L_0x0387
        L_0x0386:
            r6 = 1
        L_0x0387:
            int r3 = r3 + r6
            r3 = r4[r3]
            r0.h(r3, r7)
            goto L_0x025e
        L_0x038f:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>(r15)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x03a1:
            r6 = 87
            if (r4 != r6) goto L_0x03c9
            if (r3 > r5) goto L_0x03b7
            j$.time.format.p r12 = new j$.time.format.p
            r11 = 0
            r6 = r12
            r7 = r4
            r8 = r3
            r9 = r3
            r10 = r3
            r6.<init>(r7, r8, r9, r10, r11)
            r0.m(r12)
            goto L_0x025e
        L_0x03b7:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>(r15)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x03c9:
            r6 = 119(0x77, float:1.67E-43)
            if (r4 != r6) goto L_0x03f1
            if (r3 > r11) goto L_0x03df
            j$.time.format.p r12 = new j$.time.format.p
            r11 = 0
            r10 = 2
            r6 = r12
            r7 = r4
            r8 = r3
            r9 = r3
            r6.<init>(r7, r8, r9, r10, r11)
            r0.m(r12)
            goto L_0x025e
        L_0x03df:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            r2.<init>(r15)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x03f1:
            r6 = 89
            if (r4 != r6) goto L_0x041b
            if (r3 != r11) goto L_0x0407
            j$.time.format.p r12 = new j$.time.format.p
            r11 = 0
            r10 = 2
            r6 = r12
            r7 = r4
            r8 = r3
            r9 = r3
            r6.<init>(r7, r8, r9, r10, r11)
            r0.m(r12)
            goto L_0x025e
        L_0x0407:
            j$.time.format.p r12 = new j$.time.format.p
            r11 = 0
            r10 = 19
            r6 = r12
            r7 = r4
            r8 = r3
            r9 = r3
            r6.<init>(r7, r8, r9, r10, r11)
            r0.m(r12)
            goto L_0x025e
        L_0x0418:
            int r3 = r3 + r13
            goto L_0x04ad
        L_0x041b:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            java.lang.String r3 = "Unknown pattern letter: "
            r2.<init>(r3)
            r2.append(r4)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x042f:
            java.lang.String r6 = "'"
            r7 = 39
            if (r4 != r7) goto L_0x0480
            int r3 = r3 + 1
            r4 = r3
        L_0x0438:
            int r8 = r17.length()
            if (r4 >= r8) goto L_0x0455
            char r8 = r1.charAt(r4)
            if (r8 != r7) goto L_0x0453
            int r8 = r4 + 1
            int r9 = r17.length()
            if (r8 >= r9) goto L_0x0455
            char r9 = r1.charAt(r8)
            if (r9 != r7) goto L_0x0455
            r4 = r8
        L_0x0453:
            int r4 = r4 + r5
            goto L_0x0438
        L_0x0455:
            int r8 = r17.length()
            if (r4 >= r8) goto L_0x0474
            java.lang.String r3 = r1.substring(r3, r4)
            boolean r8 = r3.isEmpty()
            if (r8 == 0) goto L_0x0469
            r0.e(r7)
            goto L_0x0472
        L_0x0469:
            java.lang.String r7 = "''"
            java.lang.String r3 = r3.replace(r7, r6)
            r0.f(r3)
        L_0x0472:
            r3 = r4
            goto L_0x04ad
        L_0x0474:
            java.lang.IllegalArgumentException r2 = new java.lang.IllegalArgumentException
            java.lang.String r3 = "Pattern ends with an incomplete string literal: "
            java.lang.String r1 = r3.concat(r1)
            r2.<init>(r1)
            throw r2
        L_0x0480:
            r7 = 91
            if (r4 != r7) goto L_0x0488
            r16.s()
            goto L_0x04ad
        L_0x0488:
            r7 = 93
            if (r4 != r7) goto L_0x049e
            j$.time.format.s r4 = r0.a
            j$.time.format.s r4 = r4.b
            if (r4 == 0) goto L_0x0496
            r16.r()
            goto L_0x04ad
        L_0x0496:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.String r2 = "Pattern invalid as it contains ] without previous ["
            r1.<init>(r2)
            throw r1
        L_0x049e:
            r7 = 123(0x7b, float:1.72E-43)
            if (r4 == r7) goto L_0x04b0
            r7 = 125(0x7d, float:1.75E-43)
            if (r4 == r7) goto L_0x04b0
            r7 = 35
            if (r4 == r7) goto L_0x04b0
            r0.e(r4)
        L_0x04ad:
            int r3 = r3 + r5
            goto L_0x000b
        L_0x04b0:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            java.lang.String r3 = "Pattern includes reserved character: '"
            r2.<init>(r3)
            r2.append(r4)
            r2.append(r6)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x04c7:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: j$.time.format.s.j(java.lang.String):void");
    }

    public final void k(a aVar, HashMap hashMap) {
        Objects.requireNonNull(aVar, "field");
        Objects.requireNonNull(hashMap, "textLookup");
        LinkedHashMap linkedHashMap = new LinkedHashMap(hashMap);
        B b2 = B.FULL;
        d(new o(aVar, b2, new C0015b(new w(Collections.singletonMap(b2, linkedHashMap)))));
    }

    public final void l(p pVar, B b2) {
        Objects.requireNonNull(pVar, "field");
        Objects.requireNonNull(b2, "textStyle");
        d(new o(pVar, b2, x.c()));
    }

    public final void n(p pVar) {
        Objects.requireNonNull(pVar, "field");
        m(new j(pVar, 1, 19, A.NORMAL));
    }

    public final void o(p pVar, int i2) {
        Objects.requireNonNull(pVar, "field");
        if (i2 < 1 || i2 > 19) {
            throw new IllegalArgumentException("The width must be from 1 to 19 inclusive but was " + i2);
        }
        m(new j(pVar, i2, i2, A.NOT_NEGATIVE));
    }

    public final void p(p pVar, int i2, int i3, A a2) {
        if (i2 == i3 && a2 == A.NOT_NEGATIVE) {
            o(pVar, i3);
            return;
        }
        Objects.requireNonNull(pVar, "field");
        Objects.requireNonNull(a2, "signStyle");
        if (i2 < 1 || i2 > 19) {
            throw new IllegalArgumentException("The minimum width must be from 1 to 19 inclusive but was " + i2);
        } else if (i3 < 1 || i3 > 19) {
            throw new IllegalArgumentException("The maximum width must be from 1 to 19 inclusive but was " + i3);
        } else if (i3 >= i2) {
            m(new j(pVar, i2, i3, a2));
        } else {
            throw new IllegalArgumentException("The maximum width must exceed or equal the minimum width but " + i3 + " < " + i2);
        }
    }

    public final void q() {
        d(new q(h, "ZoneRegionId()"));
    }

    public final void r() {
        s sVar = this.a;
        if (sVar.b == null) {
            throw new IllegalStateException("Cannot call optionalEnd() as there was no previous call to optionalStart()");
        } else if (sVar.c.size() > 0) {
            s sVar2 = this.a;
            e eVar = new e(sVar2.c, sVar2.d);
            this.a = this.a.b;
            d(eVar);
        } else {
            this.a = this.a.b;
        }
    }

    public final void s() {
        s sVar = this.a;
        sVar.g = -1;
        this.a = new s(sVar);
    }

    public final void t() {
        d(n.INSENSITIVE);
    }

    public final void u() {
        d(n.SENSITIVE);
    }

    public final void v() {
        d(n.LENIENT);
    }

    public final void w() {
        d(n.STRICT);
    }

    public final DateTimeFormatter x() {
        return z(Locale.getDefault(), z.SMART, (u) null);
    }

    /* access modifiers changed from: package-private */
    public final DateTimeFormatter y(z zVar, u uVar) {
        return z(Locale.getDefault(), zVar, uVar);
    }

    private s(s sVar) {
        this.a = this;
        this.c = new ArrayList();
        this.g = -1;
        this.b = sVar;
        this.d = true;
    }
}
