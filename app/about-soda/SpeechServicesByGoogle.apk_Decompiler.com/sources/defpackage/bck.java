package defpackage;

/* renamed from: bck  reason: default package */
/* compiled from: PG */
public final class bck extends avu {
    public static final bck c = new bck();

    private bck() {
        super(3, 4);
    }

    public final void a(awl awl) {
        awl.g("\n    UPDATE workspec SET schedule_requested_at = 0\n    WHERE state NOT IN (2, 3, 5)\n        AND schedule_requested_at = -1\n        AND interval_duration <> 0\n    ");
    }
}
