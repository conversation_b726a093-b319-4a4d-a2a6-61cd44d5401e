package defpackage;

/* renamed from: cxe  reason: default package */
/* compiled from: PG */
public final class cxe implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;

    public cxe(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4) {
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
    }

    /* renamed from: a */
    public final cxd b() {
        cqh cqh = (cqh) this.d.b();
        return new cxd(((iim) this.a).a(), (cuk) this.b.b(), (grh) this.c.b());
    }
}
