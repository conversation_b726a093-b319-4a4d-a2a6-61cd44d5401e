package defpackage;

import java.util.Comparator;
import java.util.List;

/* renamed from: cvp  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvp implements hko {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    private final /* synthetic */ int d;

    public /* synthetic */ cvp(cvy cvy, List list, hko hko, int i) {
        this.d = i;
        this.c = cvy;
        this.a = list;
        this.b = hko;
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v70, resolved type: csx} */
    /* JADX WARNING: type inference failed for: r0v25, types: [java.util.List, java.lang.Object, java.lang.Iterable] */
    /* JADX WARNING: type inference failed for: r0v35, types: [java.lang.Object, hko] */
    /* JADX WARNING: type inference failed for: r14v67, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v58, types: [java.lang.Object, java.util.Comparator] */
    /* JADX WARNING: type inference failed for: r0v60, types: [java.lang.Object, java.util.Comparator] */
    /* JADX WARNING: type inference failed for: r1v59, types: [java.lang.Object, java.util.Comparator] */
    /* JADX WARNING: type inference failed for: r0v68, types: [java.lang.Object, hko] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme a(java.lang.Object r14) {
        /*
            r13 = this;
            int r0 = r13.d
            r1 = 4
            r2 = 20
            r3 = 18
            r4 = 1036(0x40c, float:1.452E-42)
            r5 = 3
            r6 = 0
            r7 = 8
            java.lang.String r8 = "FileGroupManager"
            r9 = 5
            r10 = 2
            r11 = 0
            r12 = 1
            switch(r0) {
                case 0: goto L_0x05ac;
                case 1: goto L_0x055c;
                case 2: goto L_0x0516;
                case 3: goto L_0x0435;
                case 4: goto L_0x03f4;
                case 5: goto L_0x03b3;
                case 6: goto L_0x028b;
                case 7: goto L_0x026a;
                case 8: goto L_0x021f;
                case 9: goto L_0x020a;
                case 10: goto L_0x01dc;
                case 11: goto L_0x0186;
                case 12: goto L_0x013c;
                case 13: goto L_0x0129;
                case 14: goto L_0x0104;
                case 15: goto L_0x00f1;
                case 16: goto L_0x00a0;
                case 17: goto L_0x0079;
                case 18: goto L_0x0059;
                case 19: goto L_0x0022;
                default: goto L_0x0016;
            }
        L_0x0016:
            csx r14 = (defpackage.csx) r14
            if (r14 != 0) goto L_0x05e5
            cvx r14 = defpackage.cvx.PENDING
            hme r14 = defpackage.hfc.K(r14)
            goto L_0x05fc
        L_0x0022:
            java.lang.Boolean r14 = (java.lang.Boolean) r14
            boolean r14 = r14.booleanValue()
            if (r14 == 0) goto L_0x0056
            java.lang.Object r14 = r13.a
            java.lang.Object r0 = r13.c
            java.lang.Object r1 = r13.b
            r4 = r1
            cwm r4 = (defpackage.cwm) r4
            cvy r5 = r4.c
            ctg r0 = (defpackage.ctg) r0
            hme r5 = r5.g(r0, r11)
            czw r6 = defpackage.czw.e(r5)
            cvp r7 = new cvp
            r7.<init>((java.lang.Object) r1, (defpackage.ctg) r0, (java.lang.Object) r14, (int) r2)
            java.util.concurrent.Executor r14 = r4.h
            czw r14 = r6.g(r7, r14)
            cwa r0 = new cwa
            r0.<init>((java.lang.Object) r1, (java.lang.Object) r5, (int) r3)
            java.util.concurrent.Executor r1 = r4.h
            czw r14 = r14.g(r0, r1)
            goto L_0x0058
        L_0x0056:
            hme r14 = defpackage.hma.a
        L_0x0058:
            return r14
        L_0x0059:
            java.lang.Object r0 = r13.a
            cwd r0 = (defpackage.cwd) r0
            cws r1 = r0.b
            java.lang.Boolean r14 = (java.lang.Boolean) r14
            java.lang.Object r2 = r13.b
            java.lang.Object r4 = r13.c
            ctj r4 = (defpackage.ctj) r4
            ctl r2 = (defpackage.ctl) r2
            hme r1 = r1.h(r4, r2)
            brg r2 = new brg
            r2.<init>(r14, r3)
            java.util.concurrent.Executor r14 = r0.c
            hme r14 = defpackage.ftd.K(r1, r2, r14)
            return r14
        L_0x0079:
            java.lang.Object r0 = r13.a
            r1 = r0
            cwd r1 = (defpackage.cwd) r1
            cws r2 = r1.b
            cze r14 = (defpackage.cze) r14
            java.lang.Object r3 = r13.b
            java.lang.Object r4 = r13.c
            ctj r4 = (defpackage.ctj) r4
            ctl r3 = (defpackage.ctl) r3
            hme r2 = r2.h(r4, r3)
            hme r2 = r1.b(r2)
            cwa r3 = new cwa
            r4 = 9
            r3.<init>((java.lang.Object) r0, (java.lang.Object) r14, (int) r4)
            java.util.concurrent.Executor r14 = r1.c
            hme r14 = defpackage.ftd.L(r2, r3, r14)
            return r14
        L_0x00a0:
            cze r14 = (defpackage.cze) r14
            int r0 = defpackage.cqh.q()
            long r1 = (long) r0
            java.lang.Object r3 = r13.b
            boolean r1 = defpackage.cyh.b(r1)
            if (r1 == 0) goto L_0x00cf
            java.lang.Object r1 = r13.a
            java.lang.Object r2 = r13.c
            r4 = r3
            cze r4 = (defpackage.cze) r4
            boolean r14 = defpackage.cze.d(r4, r14, r1)
            if (r14 == 0) goto L_0x00c6
            cwd r2 = (defpackage.cwd) r2
            cyk r14 = r2.d
            r1 = 1106(0x452, float:1.55E-42)
            r14.c(r1, r0)
            goto L_0x00cf
        L_0x00c6:
            cwd r2 = (defpackage.cwd) r2
            cyk r14 = r2.d
            r1 = 1103(0x44f, float:1.546E-42)
            r14.c(r1, r0)
        L_0x00cf:
            cze r3 = (defpackage.cze) r3
            boolean r14 = r3.a
            if (r14 == 0) goto L_0x00e3
            java.lang.Object r14 = r3.a()
            java.util.List r14 = (java.util.List) r14
            defpackage.fvf.aP(r14)
            hme r14 = defpackage.hfc.K(r14)
            goto L_0x00f0
        L_0x00e3:
            java.lang.Object r14 = r3.b()
            defpackage.fvf.aP(r14)
            java.lang.Throwable r14 = (java.lang.Throwable) r14
            hme r14 = defpackage.hfc.J(r14)
        L_0x00f0:
            return r14
        L_0x00f1:
            cze r14 = (defpackage.cze) r14
            java.lang.Object r0 = r13.a
            java.lang.Object r1 = r13.b
            java.lang.Object r2 = r13.c
            cwb r2 = (defpackage.cwb) r2
            cze r1 = (defpackage.cze) r1
            r3 = 1093(0x445, float:1.532E-42)
            hme r14 = r2.p(r1, r14, r0, r3)
            return r14
        L_0x0104:
            java.lang.Object r0 = r13.a
            r1 = r0
            cwb r1 = (defpackage.cwb) r1
            cwo r2 = r1.a
            cze r14 = (defpackage.cze) r14
            java.lang.Object r3 = r13.c
            java.lang.Object r4 = r13.b
            ctg r4 = (defpackage.ctg) r4
            csx r3 = (defpackage.csx) r3
            hme r2 = r2.l(r4, r3)
            hme r2 = r1.n(r2)
            cwa r3 = new cwa
            r3.<init>((java.lang.Object) r0, (java.lang.Object) r14, (int) r10)
            java.util.concurrent.Executor r14 = r1.b
            hme r14 = defpackage.ftd.L(r2, r3, r14)
            return r14
        L_0x0129:
            cze r14 = (defpackage.cze) r14
            java.lang.Object r0 = r13.a
            java.lang.Object r1 = r13.b
            java.lang.Object r2 = r13.c
            cwb r2 = (defpackage.cwb) r2
            cze r1 = (defpackage.cze) r1
            r3 = 1092(0x444, float:1.53E-42)
            hme r14 = r2.p(r1, r14, r0, r3)
            return r14
        L_0x013c:
            java.lang.Void r14 = (java.lang.Void) r14
            java.lang.Object r3 = r13.b
            r14 = r3
            htq r14 = (defpackage.htq) r14
            java.lang.Object r0 = r14.C(r9)
            htk r0 = (defpackage.htk) r0
            r0.x(r14)
            htq r14 = r0.b
            boolean r14 = r14.B()
            if (r14 != 0) goto L_0x0157
            r0.u()
        L_0x0157:
            java.lang.Object r4 = r13.c
            java.lang.Object r1 = r13.a
            htq r14 = r0.b
            ctg r14 = (defpackage.ctg) r14
            ctg r2 = defpackage.ctg.g
            int r2 = r14.a
            r2 = r2 | r7
            r14.a = r2
            r14.e = r12
            htq r14 = r0.r()
            r2 = r14
            ctg r2 = (defpackage.ctg) r2
            r14 = r1
            cvy r14 = (defpackage.cvy) r14
            cvz r0 = r14.c
            hme r7 = r0.g(r2)
            cvd r8 = new cvd
            r5 = 11
            r6 = 0
            r0 = r8
            r0.<init>((java.lang.Object) r1, (java.lang.Object) r2, (java.lang.Object) r3, (java.lang.Object) r4, (int) r5, (char[]) r6)
            hme r14 = r14.q(r7, r8)
            return r14
        L_0x0186:
            java.lang.Boolean r14 = (java.lang.Boolean) r14
            boolean r14 = r14.booleanValue()
            java.lang.Object r0 = r13.b
            java.lang.Object r1 = r13.a
            if (r14 != 0) goto L_0x01c2
            ctg r0 = (defpackage.ctg) r0
            java.lang.String r14 = r0.b
            java.lang.String r2 = r0.d
            java.lang.Object[] r3 = new java.lang.Object[r5]
            r3[r11] = r8
            r3[r12] = r14
            r3[r10] = r2
            java.lang.String r14 = "%s: Failed to remove the downloaded version for group: '%s'; account: '%s'"
            defpackage.cyh.i(r14, r3)
            cvy r1 = (defpackage.cvy) r1
            cyk r14 = r1.i
            r14.d(r4)
            java.io.IOException r14 = new java.io.IOException
            java.lang.String r0 = r0.b
            java.lang.String r0 = java.lang.String.valueOf(r0)
            java.lang.String r1 = "Failed to remove downloaded group: "
            java.lang.String r0 = r1.concat(r0)
            r14.<init>(r0)
            hme r14 = defpackage.hfc.J(r14)
            goto L_0x01db
        L_0x01c2:
            java.lang.Object r14 = r13.c
            r3 = r1
            cvy r3 = (defpackage.cvy) r3
            cvz r4 = r3.c
            r5 = r14
            csx r5 = (defpackage.csx) r5
            hme r4 = r4.a(r5)
            bsx r5 = new bsx
            ctg r0 = (defpackage.ctg) r0
            r5.<init>((java.lang.Object) r1, (defpackage.ctg) r0, (java.lang.Object) r14, (int) r2)
            hme r14 = r3.q(r4, r5)
        L_0x01db:
            return r14
        L_0x01dc:
            java.lang.Boolean r14 = (java.lang.Boolean) r14
            boolean r14 = r14.booleanValue()
            if (r14 != 0) goto L_0x0207
            java.lang.Object r14 = r13.c
            java.lang.Object r0 = r13.b
            cvy r0 = (defpackage.cvy) r0
            cyk r0 = r0.i
            r0.d(r4)
            java.io.IOException r0 = new java.io.IOException
            ctg r14 = (defpackage.ctg) r14
            java.lang.String r14 = r14.b
            java.lang.String r14 = java.lang.String.valueOf(r14)
            java.lang.String r1 = "Failed to write updated group: "
            java.lang.String r14 = r1.concat(r14)
            r0.<init>(r14)
            hme r14 = defpackage.hfc.J(r0)
            goto L_0x0209
        L_0x0207:
            java.lang.Object r14 = r13.a
        L_0x0209:
            return r14
        L_0x020a:
            grh r14 = (defpackage.grh) r14
            java.lang.Object r14 = r13.a
            cvy r14 = (defpackage.cvy) r14
            cvz r14 = r14.c
            java.lang.Object r0 = r13.c
            java.lang.Object r1 = r13.b
            ctg r1 = (defpackage.ctg) r1
            csx r0 = (defpackage.csx) r0
            hme r14 = r14.l(r1, r0)
            return r14
        L_0x021f:
            java.lang.Boolean r14 = (java.lang.Boolean) r14
            boolean r14 = r14.booleanValue()
            java.lang.Object r0 = r13.a
            if (r14 != 0) goto L_0x025b
            java.lang.Object r14 = r13.b
            ctg r14 = (defpackage.ctg) r14
            java.lang.String r1 = r14.b
            java.lang.String r2 = r14.d
            java.lang.Object[] r3 = new java.lang.Object[r5]
            r3[r11] = r8
            r3[r12] = r1
            r3[r10] = r2
            java.lang.String r1 = "%s: Failed to remove pending version for group: '%s'; account: '%s'"
            defpackage.cyh.i(r1, r3)
            cvy r0 = (defpackage.cvy) r0
            cyk r0 = r0.i
            r0.d(r4)
            java.io.IOException r0 = new java.io.IOException
            java.lang.String r14 = r14.b
            java.lang.String r14 = java.lang.String.valueOf(r14)
            java.lang.String r1 = "Failed to remove pending group: "
            java.lang.String r14 = r1.concat(r14)
            r0.<init>(r14)
            hme r14 = defpackage.hfc.J(r0)
            goto L_0x0269
        L_0x025b:
            java.lang.Object r14 = r13.c
            cvy r0 = (defpackage.cvy) r0
            cxz r0 = r0.h
            gxq r14 = defpackage.gxq.q(r14)
            hme r14 = r0.a(r14)
        L_0x0269:
            return r14
        L_0x026a:
            csx r14 = (defpackage.csx) r14
            if (r14 != 0) goto L_0x0275
            cvx r14 = defpackage.cvx.PENDING
            hme r14 = defpackage.hfc.K(r14)
            goto L_0x028a
        L_0x0275:
            java.lang.Object r0 = r13.a
            java.lang.Object r1 = r13.c
            java.lang.Object r2 = r13.b
            cvy r2 = (defpackage.cvy) r2
            cyk r3 = r2.i
            cxi r4 = new cxi
            r4.<init>((java.lang.Object) r3, (char[]) r6)
            ctg r1 = (defpackage.ctg) r1
            hme r14 = r2.B(r1, r14, r0, r4)
        L_0x028a:
            return r14
        L_0x028b:
            csx r14 = (defpackage.csx) r14
            java.lang.Object r0 = r13.c
            ctg r0 = (defpackage.ctg) r0
            java.lang.String r2 = "%s: Received new config for group: %s"
            java.lang.String r3 = r0.b
            defpackage.cyh.d(r2, r8, r3)
            hig r2 = defpackage.hig.k
            htk r2 = r2.l()
            java.lang.String r3 = r14.c
            htq r4 = r2.b
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x02ab
            r2.u()
        L_0x02ab:
            htq r4 = r2.b
            r5 = r4
            hig r5 = (defpackage.hig) r5
            r3.getClass()
            int r6 = r5.a
            r6 = r6 | r12
            r5.a = r6
            r5.b = r3
            java.lang.String r3 = r14.d
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x02c5
            r2.u()
        L_0x02c5:
            htq r4 = r2.b
            r5 = r4
            hig r5 = (defpackage.hig) r5
            r3.getClass()
            int r6 = r5.a
            r1 = r1 | r6
            r5.a = r1
            r5.d = r3
            int r1 = r14.e
            boolean r3 = r4.B()
            if (r3 != 0) goto L_0x02df
            r2.u()
        L_0x02df:
            htq r3 = r2.b
            r4 = r3
            hig r4 = (defpackage.hig) r4
            int r5 = r4.a
            r5 = r5 | r10
            r4.a = r5
            r4.c = r1
            long r4 = r14.r
            boolean r1 = r3.B()
            if (r1 != 0) goto L_0x02f6
            r2.u()
        L_0x02f6:
            htq r1 = r2.b
            r3 = r1
            hig r3 = (defpackage.hig) r3
            int r6 = r3.a
            r6 = r6 | 64
            r3.a = r6
            r3.h = r4
            java.lang.String r3 = r14.s
            boolean r1 = r1.B()
            if (r1 != 0) goto L_0x030e
            r2.u()
        L_0x030e:
            java.lang.Object r1 = r13.a
            htq r4 = r2.b
            hig r4 = (defpackage.hig) r4
            r3.getClass()
            int r5 = r4.a
            r5 = r5 | 128(0x80, float:1.794E-43)
            r4.a = r5
            r4.i = r3
            htq r2 = r2.r()
            hig r2 = (defpackage.hig) r2
            hiv r3 = defpackage.hiv.c
            htk r3 = r3.l()
            grh r1 = (defpackage.grh) r1
            java.lang.Object r1 = r1.b()
            hix r1 = (defpackage.hix) r1
            htq r4 = r3.b
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x033e
            r3.u()
        L_0x033e:
            htq r4 = r3.b
            hiv r4 = (defpackage.hiv) r4
            int r1 = r1.a()
            r4.b = r1
            int r1 = r4.a
            r1 = r1 | r12
            r4.a = r1
            htq r1 = r3.r()
            hiv r1 = (defpackage.hiv) r1
            hii r3 = defpackage.hii.u
            htk r3 = r3.l()
            htq r4 = r3.b
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x0364
            r3.u()
        L_0x0364:
            htq r4 = r3.b
            r5 = r4
            hii r5 = (defpackage.hii) r5
            r1.getClass()
            r5.s = r1
            int r1 = r5.c
            r1 = r1 | 512(0x200, float:7.175E-43)
            r5.c = r1
            boolean r1 = r4.B()
            if (r1 != 0) goto L_0x037d
            r3.u()
        L_0x037d:
            java.lang.Object r1 = r13.b
            htq r4 = r3.b
            hii r4 = (defpackage.hii) r4
            r2.getClass()
            r4.d = r2
            int r2 = r4.a
            r2 = r2 | 256(0x100, float:3.59E-43)
            r4.a = r2
            int r2 = defpackage.cqh.o()
            long r4 = (long) r2
            r2 = r1
            cvy r2 = (defpackage.cvy) r2
            cyk r6 = r2.i
            r7 = 1018(0x3fa, float:1.427E-42)
            r6.i(r7, r3, r4)
            huf r3 = r14.n
            int r3 = r3.size()
            hme r3 = r2.o(r14, r11, r3)
            bsx r4 = new bsx
            r5 = 14
            r4.<init>((java.lang.Object) r1, (defpackage.ctg) r0, (java.lang.Object) r14, (int) r5)
            hme r14 = r2.q(r3, r4)
            return r14
        L_0x03b3:
            java.lang.Void r14 = (java.lang.Void) r14
            java.lang.Object r14 = r13.a
            cvy r14 = (defpackage.cvy) r14
            grh r0 = r14.g
            boolean r0 = r0.f()
            if (r0 == 0) goto L_0x03eb
            java.lang.Object r0 = r13.c
            csx r0 = (defpackage.csx) r0
            int r1 = r0.q
            int r1 = defpackage.cqh.A(r1)
            if (r1 != 0) goto L_0x03ce
            goto L_0x03eb
        L_0x03ce:
            if (r1 == r12) goto L_0x03eb
            java.lang.Object r1 = r13.b
            grh r14 = r14.g
            java.lang.Object r14 = r14.b()
            gsb r14 = (defpackage.gsb) r14
            java.lang.Object r14 = r14.a()
            dah r14 = (defpackage.dah) r14
            int r0 = r0.q
            ctg r1 = (defpackage.ctg) r1
            java.lang.String r0 = r1.b
            hme r14 = r14.b()
            goto L_0x03f3
        L_0x03eb:
            java.lang.Boolean r14 = java.lang.Boolean.valueOf(r12)
            hme r14 = defpackage.hfc.K(r14)
        L_0x03f3:
            return r14
        L_0x03f4:
            java.util.List r14 = (java.util.List) r14
            java.util.Iterator r14 = r14.iterator()
        L_0x03fa:
            java.lang.Object r0 = r13.a
            java.lang.Object r2 = r13.c
            boolean r3 = r14.hasNext()
            if (r3 == 0) goto L_0x0423
            java.lang.Object r3 = r13.b
            java.lang.Object r4 = r14.next()
            ctg r4 = (defpackage.ctg) r4
            cvy r2 = (defpackage.cvy) r2
            cvz r5 = r2.c
            hme r5 = r5.g(r4)
            bpt r7 = new bpt
            r8 = 17
            r7.<init>(r3, r4, r8, r6)
            hme r2 = r2.q(r5, r7)
            r0.add(r2)
            goto L_0x03fa
        L_0x0423:
            bzj r14 = defpackage.cqh.U(r0)
            ctw r0 = new ctw
            r0.<init>(r1)
            cvy r2 = (defpackage.cvy) r2
            java.util.concurrent.Executor r1 = r2.e
            hme r14 = r14.n(r0, r1)
            return r14
        L_0x0435:
            grh r14 = (defpackage.grh) r14
            boolean r0 = r14.f()
            java.lang.Object r1 = r13.b
            if (r0 != 0) goto L_0x0452
            ctg r1 = (defpackage.ctg) r1
            java.lang.String r14 = r1.b
            java.lang.String r0 = "%s: Received duplicate config for group: %s"
            defpackage.cyh.d(r0, r8, r14)
            java.lang.Boolean r14 = java.lang.Boolean.valueOf(r11)
            hme r14 = defpackage.hfc.K(r14)
            goto L_0x0515
        L_0x0452:
            java.lang.Object r0 = r13.c
            r2 = r0
            csx r2 = (defpackage.csx) r2
            boolean r3 = defpackage.cqx.o(r2)
            if (r3 == 0) goto L_0x04c9
            int r3 = defpackage.hgk.a
            hgh r3 = defpackage.hgj.a
            hgi r3 = r3.a()
            java.lang.String r4 = r2.s
            r3.e(r4)
            java.lang.String r4 = "|"
            r3.e(r4)
            r5 = r1
            ctg r5 = (defpackage.ctg) r5
            java.lang.String r5 = r5.d
            r3.e(r5)
            r3.e(r4)
            long r4 = r2.r
            r6 = r3
            hgb r6 = (defpackage.hgb) r6
            java.nio.ByteBuffer r8 = r6.a
            r8.putLong(r4)
            r6.b(r7)
            hgg r3 = r3.n()
            java.lang.String r3 = r3.toString()
            java.lang.String r2 = r2.c
            java.lang.Object[] r4 = new java.lang.Object[r10]
            r4[r11] = r2
            r4[r12] = r3
            java.lang.String r2 = "%s_%s"
            java.lang.String r2 = java.lang.String.format(r2, r4)
            htq r0 = (defpackage.htq) r0
            java.lang.Object r3 = r0.C(r9)
            htk r3 = (defpackage.htk) r3
            r3.x(r0)
            htq r0 = r3.b
            boolean r0 = r0.B()
            if (r0 != 0) goto L_0x04b3
            r3.u()
        L_0x04b3:
            htq r0 = r3.b
            csx r0 = (defpackage.csx) r0
            r2.getClass()
            int r4 = r0.a
            r5 = 524288(0x80000, float:7.34684E-40)
            r4 = r4 | r5
            r0.a = r4
            r0.v = r2
            htq r0 = r3.r()
            csx r0 = (defpackage.csx) r0
        L_0x04c9:
            r2 = r1
            htq r2 = (defpackage.htq) r2
            java.lang.Object r3 = r2.C(r9)
            htk r3 = (defpackage.htk) r3
            r3.x(r2)
            htq r2 = r3.b
            boolean r2 = r2.B()
            if (r2 != 0) goto L_0x04e0
            r3.u()
        L_0x04e0:
            java.lang.Object r2 = r13.a
            htq r4 = r3.b
            ctg r4 = (defpackage.ctg) r4
            ctg r5 = defpackage.ctg.g
            int r5 = r4.a
            r5 = r5 | r7
            r4.a = r5
            r4.e = r11
            htq r3 = r3.r()
            ctg r3 = (defpackage.ctg) r3
            r4 = r2
            cvy r4 = (defpackage.cvy) r4
            cvz r5 = r4.c
            hme r3 = r5.g(r3)
            cvi r5 = new cvi
            r6 = 11
            r5.<init>(r0, r6)
            hme r0 = r4.q(r3, r5)
            cvp r3 = new cvp
            ctg r1 = (defpackage.ctg) r1
            r5 = 6
            r3.<init>((java.lang.Object) r2, (defpackage.ctg) r1, (java.lang.Object) r14, (int) r5)
            hme r14 = r4.q(r0, r3)
        L_0x0515:
            return r14
        L_0x0516:
            java.lang.Boolean r14 = (java.lang.Boolean) r14
            java.lang.Object r14 = r13.b
            htq r14 = (defpackage.htq) r14
            java.lang.Object r0 = r14.C(r9)
            htk r0 = (defpackage.htk) r0
            r0.x(r14)
            htq r1 = r0.b
            boolean r1 = r1.B()
            if (r1 != 0) goto L_0x0530
            r0.u()
        L_0x0530:
            java.lang.Object r1 = r13.c
            java.lang.Object r2 = r13.a
            htq r3 = r0.b
            ctg r3 = (defpackage.ctg) r3
            ctg r4 = defpackage.ctg.g
            int r4 = r3.a
            r4 = r4 | r7
            r3.a = r4
            r3.e = r11
            htq r0 = r0.r()
            ctg r0 = (defpackage.ctg) r0
            r3 = r2
            cvy r3 = (defpackage.cvy) r3
            cvz r4 = r3.c
            hme r0 = r4.g(r0)
            cvp r4 = new cvp
            htq r1 = (defpackage.htq) r1
            r4.<init>((java.lang.Object) r2, (defpackage.htq) r1, (defpackage.htq) r14, (int) r12)
            hme r14 = r3.q(r0, r4)
            return r14
        L_0x055c:
            csx r14 = (defpackage.csx) r14
            java.lang.Object r0 = r13.c
            if (r14 == 0) goto L_0x056d
            csx r0 = (defpackage.csx) r0
            grh r14 = defpackage.cvy.a(r0, r14)
            hme r14 = defpackage.hfc.K(r14)
            goto L_0x05ab
        L_0x056d:
            java.lang.Object r14 = r13.b
            htq r14 = (defpackage.htq) r14
            java.lang.Object r1 = r14.C(r9)
            htk r1 = (defpackage.htk) r1
            r1.x(r14)
            htq r14 = r1.b
            boolean r14 = r14.B()
            if (r14 != 0) goto L_0x0585
            r1.u()
        L_0x0585:
            java.lang.Object r14 = r13.a
            htq r2 = r1.b
            ctg r2 = (defpackage.ctg) r2
            ctg r3 = defpackage.ctg.g
            int r3 = r2.a
            r3 = r3 | r7
            r2.a = r3
            r2.e = r12
            htq r1 = r1.r()
            ctg r1 = (defpackage.ctg) r1
            cvy r14 = (defpackage.cvy) r14
            cvz r2 = r14.c
            hme r1 = r2.g(r1)
            cvi r2 = new cvi
            r2.<init>(r0, r11)
            hme r14 = r14.q(r1, r2)
        L_0x05ab:
            return r14
        L_0x05ac:
            cth r14 = (defpackage.cth) r14
            if (r14 != 0) goto L_0x05b2
            cth r14 = defpackage.cth.b
        L_0x05b2:
            boolean r14 = r14.a
            if (r14 == 0) goto L_0x05bb
            hme r14 = defpackage.hfc.K(r6)
            return r14
        L_0x05bb:
            java.lang.Object r14 = r13.c
            java.lang.Object r0 = r13.b
            java.lang.Object r1 = r13.a
            ctg r0 = (defpackage.ctg) r0
            java.lang.String r2 = r0.b
            java.lang.String r0 = r0.c
            java.lang.Object[] r3 = new java.lang.Object[r5]
            r3[r11] = r8
            r3[r12] = r2
            r3[r10] = r0
            java.lang.String r0 = "%s: Trying to add group %s that requires activation %s."
            defpackage.cyh.e(r0, r3)
            cvy r1 = (defpackage.cvy) r1
            cyk r0 = r1.i
            csx r14 = (defpackage.csx) r14
            r1 = 1055(0x41f, float:1.478E-42)
            defpackage.cvy.z(r1, r0, r14)
            cux r14 = new cux
            r14.<init>()
            throw r14
        L_0x05e5:
            java.lang.Object r0 = r13.a
            java.lang.Object r1 = r13.c
            java.lang.Object r2 = r13.b
            cwm r2 = (defpackage.cwm) r2
            cyk r3 = r2.l
            cxi r4 = new cxi
            r4.<init>((java.lang.Object) r3, (char[]) r6)
            cvy r2 = r2.c
            ctg r1 = (defpackage.ctg) r1
            hme r14 = r2.B(r1, r14, r0, r4)
        L_0x05fc:
            return r14
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cvp.a(java.lang.Object):hme");
    }

    public /* synthetic */ cvp(Object obj, ctg ctg, csx csx, int i) {
        this.d = i;
        this.a = obj;
        this.b = ctg;
        this.c = csx;
    }

    public /* synthetic */ cvp(Object obj, ctg ctg, Object obj2, int i) {
        this.d = i;
        this.b = obj;
        this.c = ctg;
        this.a = obj2;
    }

    public /* synthetic */ cvp(Object obj, cze cze, Comparator comparator, int i) {
        this.d = i;
        this.c = obj;
        this.b = cze;
        this.a = comparator;
    }

    public /* synthetic */ cvp(Object obj, htq htq, htq htq2, int i) {
        this.d = i;
        this.a = obj;
        this.c = htq;
        this.b = htq2;
    }
}
