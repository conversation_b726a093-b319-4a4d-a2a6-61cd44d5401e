package defpackage;

/* renamed from: cte  reason: default package */
/* compiled from: PG */
public final class cte extends htq implements hvb {
    public static final cte d;
    private static volatile hvh e;
    public huv a = huv.a;
    public huv b = huv.a;
    public huf c = hvk.a;

    static {
        cte cte = new cte();
        d = cte;
        htq.z(cte.class, cte);
    }

    private cte() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(d, "\u0001\u0003\u0000\u0000\u0001\u0003\u0003\u0002\u0001\u0000\u00012\u00022\u0003\u001b", new Object[]{"a", ctc.a, "b", ctd.a, "c", csx.class});
        } else if (i2 == 3) {
            return new cte();
        } else {
            if (i2 == 4) {
                return new htk((htq) d);
            }
            if (i2 == 5) {
                return d;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = e;
            if (hvh == null) {
                synchronized (cte.class) {
                    hvh = e;
                    if (hvh == null) {
                        hvh = new htl(d);
                        e = hvh;
                    }
                }
            }
            return hvh;
        }
    }

    public final huv b() {
        huv huv = this.a;
        if (!huv.b) {
            this.a = huv.a();
        }
        return this.a;
    }
}
