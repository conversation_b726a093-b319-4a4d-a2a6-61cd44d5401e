package defpackage;

import android.media.Spatializer$OnSpatializerStateChangedListener;
import android.os.RemoteException;
import android.util.Log;
import android.view.Surface;
import android.widget.Toast;
import androidx.wear.ambient.AmbientMode;
import com.google.android.apps.speech.tts.googletts.local.voicepack.ui.MultipleVoicesActivity;
import com.google.android.tts.R;
import java.util.Iterator;

/* renamed from: bpu  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bpu implements Runnable {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ bpu(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r0v9, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v10, types: [aet, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v11, types: [aet, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v38, types: [aet, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v42, types: [java.lang.Object, aer] */
    public final void run() {
        aoy aoy;
        Spatializer$OnSpatializerStateChangedListener spatializer$OnSpatializerStateChangedListener;
        switch (this.b) {
            case 0:
                String str = bpy.a;
                try {
                    ((bpb) this.a).e();
                    return;
                } catch (RemoteException e) {
                    Log.w(bpy.a, "Failed to cancel inference", e);
                    return;
                }
            case 1:
                String str2 = bpy.a;
                try {
                    ((bpb) this.a).e();
                    return;
                } catch (RemoteException e2) {
                    Log.w(bpy.a, "Failed to cancel preparation of inference engine", e2);
                    return;
                }
            case 2:
                this.a.cancel(false);
                return;
            case 3:
                this.a.k();
                return;
            case 4:
                this.a.j();
                return;
            case 5:
                ((brm) this.a).c.n();
                return;
            case 6:
                Object obj = this.a;
                aja aja = (aja) ((brk) obj).a.c;
                aja.t();
                afv afv = aja.g;
                afv.f();
                Iterator it = afv.d.iterator();
                while (it.hasNext()) {
                    afu afu = (afu) it.next();
                    if (afu.a.equals(obj)) {
                        afu.a(afv.c);
                        afv.d.remove(afu);
                    }
                }
                return;
            case 7:
                ((MultipleVoicesActivity) ((buu) this.a).a).G();
                return;
            case 8:
                ((MultipleVoicesActivity) ((buu) this.a).a).G();
                return;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                ((MultipleVoicesActivity) ((buu) this.a).a).G();
                return;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                ((MultipleVoicesActivity) ((buv) this.a).b).G();
                return;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                Toast.makeText(((bva) this.a).b, R.string.sample_text_synthesis_failure, 0).show();
                return;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                ((bve) this.a).E();
                return;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                this.a.j();
                return;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                bvp bvp = (bvp) this.a;
                adl adl = (adl) bvp.h;
                if (adl.d() == 3 && adl.l() && adl.e() == 0) {
                    bvp.h.k();
                    return;
                }
                return;
            case 15:
                ? r0 = this.a;
                bvp bvp2 = r0;
                bvp2.h.i(r0);
                bvp2.h.n();
                return;
            case 16:
                Object obj2 = this.a;
                String hexString = Integer.toHexString(System.identityHashCode(obj2));
                String str3 = agh.e;
                String a2 = aeh.a();
                afy.d("ExoPlayerImpl", "Release " + hexString + " [AndroidXMedia3/1.4.0] [" + str3 + "] [" + a2 + "]");
                aja aja2 = (aja) obj2;
                aja2.t();
                aiw aiw = aja2.r;
                abq abq = aja2.v;
                abq abq2 = aja2.t;
                aja2.s.b = null;
                if (!aja2.f.e()) {
                    aja2.g.e(10, new akf(1));
                }
                aja2.g.d();
                ((agf) aja2.e).b.removeCallbacksAndMessages((Object) null);
                apg apg = aja2.i;
                ((api) apg).g.Y(aja2.q);
                ajt ajt = aja2.o;
                boolean z = ajt.p;
                aja2.o = ajt.d(1);
                ajt ajt2 = aja2.o;
                aja2.o = ajt2.a(ajt2.c);
                ajt ajt3 = aja2.o;
                ajt3.q = ajt3.s;
                aja2.o.r = 0;
                akn akn = aja2.q;
                afq afq = akn.f;
                yi.l(afq);
                afq.c(new lk(akn, 16, (byte[]) null));
                apf apf = aja2.d;
                synchronized (((apd) apf).b) {
                    if (!(agh.a < 32 || (aoy = ((apd) apf).e) == null || (spatializer$OnSpatializerStateChangedListener = aoy.d) == null || aoy.c == null)) {
                        aoy.a.removeOnSpatializerStateChangedListener(spatializer$OnSpatializerStateChangedListener);
                        aoy.c.removeCallbacksAndMessages((Object) null);
                        aoy.c = null;
                        aoy.d = null;
                    }
                }
                apf.g = null;
                apf.h = null;
                Surface surface = aja2.n;
                if (surface != null) {
                    surface.release();
                    aja2.n = null;
                }
                int i = afl.a;
                return;
            case 17:
                ((cds) this.a).h();
                return;
            case 18:
                ccj ccj = ((cds) ((AmbientMode.AmbientController) this.a).a).b;
                ccj.i(String.valueOf(ccj.getClass().getName()).concat(" disconnecting because it was signed out."));
                return;
            case 19:
                ((cek) this.a).f.b(new cbe(4));
                return;
            default:
                synchronized (((ckn) this.a).a) {
                    ((ckk) ((ckn) this.a).b).a.j();
                }
                return;
        }
    }

    public bpu(Object obj, int i, byte[] bArr) {
        this.b = i;
        this.a = obj;
    }
}
