package defpackage;

/* renamed from: end  reason: default package */
/* compiled from: PG */
final class end extends jme {
    /* synthetic */ Object a;
    final /* synthetic */ eng b;
    int c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public end(eng eng, jlr jlr) {
        super(jlr);
        this.b = eng;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.c |= Integer.MIN_VALUE;
        return this.b.l(this);
    }
}
