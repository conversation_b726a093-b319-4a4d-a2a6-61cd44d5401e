package defpackage;

/* renamed from: cvo  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvo implements gqx {
    public final /* synthetic */ boolean a;
    public final /* synthetic */ csx b;
    public final /* synthetic */ cxi c;

    public /* synthetic */ cvo(boolean z, cxi cxi, csx csx) {
        this.a = z;
        this.c = cxi;
        this.b = csx;
    }

    public final Object apply(Object obj) {
        Void voidR = (Void) obj;
        if (!this.a) {
            cxi cxi = this.c;
            csx csx = this.b;
            cxi.e(1009, csx);
            htk l = hig.k.l();
            String str = csx.d;
            if (!l.b.B()) {
                l.u();
            }
            htq htq = l.b;
            hig hig = (hig) htq;
            str.getClass();
            hig.a |= 4;
            hig.d = str;
            String str2 = csx.c;
            if (!htq.B()) {
                l.u();
            }
            htq htq2 = l.b;
            hig hig2 = (hig) htq2;
            str2.getClass();
            hig2.a |= 1;
            hig2.b = str2;
            int i = csx.e;
            if (!htq2.B()) {
                l.u();
            }
            hig hig3 = (hig) l.b;
            hig3.a |= 2;
            hig3.c = i;
            int size = csx.n.size();
            if (!l.b.B()) {
                l.u();
            }
            htq htq3 = l.b;
            hig hig4 = (hig) htq3;
            hig4.a |= 8;
            hig4.e = size;
            long j = csx.r;
            if (!htq3.B()) {
                l.u();
            }
            htq htq4 = l.b;
            hig hig5 = (hig) htq4;
            hig5.a |= 64;
            hig5.h = j;
            String str3 = csx.s;
            if (!htq4.B()) {
                l.u();
            }
            hig hig6 = (hig) l.b;
            str3.getClass();
            hig6.a |= 128;
            hig6.i = str3;
            hig hig7 = (hig) l.r();
            csw csw = csx.b;
            if (csw == null) {
                csw = csw.i;
            }
            long j2 = csw.c;
            long j3 = csw.e;
            long j4 = csw.d;
            htk l2 = hik.f.l();
            int i2 = csw.f;
            if (!l2.b.B()) {
                l2.u();
            }
            htq htq5 = l2.b;
            hik hik = (hik) htq5;
            hik.a |= 1;
            hik.b = i2;
            long j5 = j4 - j3;
            if (!htq5.B()) {
                l2.u();
            }
            htq htq6 = l2.b;
            hik hik2 = (hik) htq6;
            hik2.a |= 2;
            hik2.c = j5;
            long j6 = j4 - j2;
            if (!htq6.B()) {
                l2.u();
            }
            htq htq7 = l2.b;
            hik hik3 = (hik) htq7;
            hik3.a |= 4;
            hik3.d = j6;
            csw csw2 = csx.b;
            if (csw2 == null) {
                csw2 = csw.i;
            }
            boolean z = csw2.h;
            if (!htq7.B()) {
                l2.u();
            }
            hik hik4 = (hik) l2.b;
            hik4.a |= 8;
            hik4.e = z;
            hik hik5 = (hik) l2.r();
            Object obj2 = cxi.a;
            htk l3 = hii.u.l();
            if (!l3.b.B()) {
                l3.u();
            }
            htq htq8 = l3.b;
            hii hii = (hii) htq8;
            hik5.getClass();
            hii.q = hik5;
            hii.c |= 8;
            if (!htq8.B()) {
                l3.u();
            }
            hii hii2 = (hii) l3.b;
            hig7.getClass();
            hii2.d = hig7;
            hii2.a |= 256;
            ((cyk) obj2).i(1082, l3, (long) cqh.o());
        }
        return cvx.DOWNLOADED;
    }
}
