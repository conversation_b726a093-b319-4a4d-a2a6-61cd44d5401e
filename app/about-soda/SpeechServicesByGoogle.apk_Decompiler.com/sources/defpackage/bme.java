package defpackage;

import android.graphics.drawable.Drawable;
import android.util.Log;
import android.widget.TextView;
import androidx.preference.DialogPreference;
import androidx.preference.PreferenceGroup;
import j$.util.Optional;
import j$.util.function.Consumer$CC;
import java.util.function.Consumer;

/* renamed from: bme  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bme implements Consumer {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ bme(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r5v2, types: [java.lang.Object, java.lang.Runnable] */
    /* JADX WARNING: type inference failed for: r5v4, types: [java.lang.Object, java.lang.Runnable] */
    /* JADX WARNING: type inference failed for: r0v3, types: [java.util.Set, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v24, types: [java.lang.Object, jna] */
    /* JADX WARNING: type inference failed for: r5v20, types: [dwj, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v22, types: [dwj, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v24, types: [dwj, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v26, types: [dwj, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v30, types: [dwj, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v34, types: [dwj, java.lang.Object] */
    public final void accept(Object obj) {
        switch (this.b) {
            case 0:
                Drawable drawable = (Drawable) obj;
                this.a.run();
                return;
            case 1:
                CharSequence charSequence = (CharSequence) obj;
                if (charSequence != null) {
                    charSequence.toString();
                }
                this.a.run();
                return;
            case 2:
                ((bne) this.a).d((TextView) obj);
                return;
            case 3:
                this.a.add((bsk) obj);
                return;
            case 4:
                ((bzj) obj).N((String) this.a, 6);
                return;
            case 5:
                ((bzj) obj).N((String) this.a, 2);
                return;
            case 6:
                ((byw) obj).H((String) this.a, 2);
                return;
            case 7:
                ((PreferenceGroup) ((grm) this.a).a).g((DialogPreference) obj);
                return;
            case 8:
                ((cmu) this.a).b((awh) obj);
                return;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                cai a2 = cai.a(((Integer) obj).intValue(), ihj.EVENT_OVERRIDE);
                ihj ihj = a2.b;
                if (ihj == ihj.EVENT_OVERRIDE || ihj == ihj.EVENT_DEFERRING) {
                    ((cab) this.a).l = a2;
                    return;
                }
                String obj2 = ihj.toString();
                String valueOf = String.valueOf(ihj.EVENT_OVERRIDE);
                String valueOf2 = String.valueOf(ihj.EVENT_DEFERRING);
                Log.e("AbstractLogEventBuilder", "The given event-level ProductIdOrigin value " + obj2 + " is not one of the values expected for a value set at the event-level: " + valueOf + " or " + valueOf2);
                return;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                hdf hdf = exx.a;
                this.a.a(obj);
                return;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                ((byw) obj).a.a(dwv.aF.d("model_manager", (String) ((Optional) this.a).get()));
                return;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                ((byw) obj).a.a(dwv.aC.d("model_manager", (String) ((Optional) this.a).get()));
                return;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                ((byw) obj).a.a(dwv.aD.d("model_manager", (String) ((Optional) this.a).get()));
                return;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                ((bzj) obj).a.a(dwv.aM.d("model_manager", ((ezz) this.a).d));
                return;
            case 15:
                ((faa) obj).a(((ezz) this.a).h);
                return;
            case 16:
                ((bzj) obj).N((String) this.a, 7);
                return;
            case 17:
                ((bzj) obj).a.a(dwv.aI.d("model_manager", (String) this.a));
                return;
            case 18:
                ((bzj) obj).N((String) ((emc) this.a).a, 4);
                return;
            case 19:
                ((byw) obj).H(((faf) this.a).a, 4);
                return;
            default:
                ((bzj) obj).a.a(dwv.aK.d("model_manager", (String) this.a));
                return;
        }
    }

    public final /* synthetic */ Consumer andThen(Consumer consumer) {
        switch (this.b) {
            case 0:
                return Consumer$CC.$default$andThen(this, consumer);
            case 1:
                return Consumer$CC.$default$andThen(this, consumer);
            case 2:
                return Consumer$CC.$default$andThen(this, consumer);
            case 3:
                return Consumer$CC.$default$andThen(this, consumer);
            case 4:
                return Consumer$CC.$default$andThen(this, consumer);
            case 5:
                return Consumer$CC.$default$andThen(this, consumer);
            case 6:
                return Consumer$CC.$default$andThen(this, consumer);
            case 7:
                return Consumer$CC.$default$andThen(this, consumer);
            case 8:
                return Consumer$CC.$default$andThen(this, consumer);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return Consumer$CC.$default$andThen(this, consumer);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return Consumer$CC.$default$andThen(this, consumer);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return Consumer$CC.$default$andThen(this, consumer);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return Consumer$CC.$default$andThen(this, consumer);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return Consumer$CC.$default$andThen(this, consumer);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return Consumer$CC.$default$andThen(this, consumer);
            case 15:
                return Consumer$CC.$default$andThen(this, consumer);
            case 16:
                return Consumer$CC.$default$andThen(this, consumer);
            case 17:
                return Consumer$CC.$default$andThen(this, consumer);
            case 18:
                return Consumer$CC.$default$andThen(this, consumer);
            case 19:
                return Consumer$CC.$default$andThen(this, consumer);
            default:
                return Consumer$CC.$default$andThen(this, consumer);
        }
    }
}
