package defpackage;

import android.os.Process;

/* renamed from: cpu  reason: default package */
/* compiled from: PG */
public final class cpu extends Thread {
    public Runnable a;
    public Runnable b;
    public final cqg c;

    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public cpu(int r30, java.lang.Runnable r31) {
        /*
            r29 = this;
            r7 = r29
            r0 = r31
            java.lang.String r1 = "r"
            defpackage.jnu.e(r0, r1)
            r7.<init>(r0)
            cqg r8 = new cqg
            r5 = 1
            r6 = 26
            r2 = 0
            r3 = 0
            r4 = 0
            r0 = r8
            r1 = r29
            r0.<init>(r1, r2, r3, r4, r5, r6)
            r7.c = r8
            boolean r0 = r8.c
            if (r0 == 0) goto L_0x007b
        L_0x0020:
            java.util.concurrent.atomic.AtomicLong r0 = r8.f
            long r0 = r0.get()
            r16 = 0
            r18 = 119(0x77, float:1.67E-43)
            r11 = 0
            r12 = 0
            r13 = 0
            r15 = 0
            r9 = r0
            r14 = r30
            long r2 = defpackage.cqf.i(r9, r11, r12, r13, r14, r15, r16, r18)
            boolean r4 = defpackage.cqf.g(r0)
            if (r4 == 0) goto L_0x0044
            java.util.concurrent.atomic.AtomicLong r4 = r8.f
            boolean r0 = r4.compareAndSet(r0, r2)
            if (r0 == 0) goto L_0x0020
            goto L_0x0056
        L_0x0044:
            int r4 = defpackage.cqf.d(r0)
            int r5 = defpackage.cqf.d(r2)
            if (r4 != r5) goto L_0x0057
            java.util.concurrent.atomic.AtomicLong r4 = r8.f
            boolean r0 = r4.compareAndSet(r0, r2)
            if (r0 == 0) goto L_0x0020
        L_0x0056:
            return
        L_0x0057:
            java.util.concurrent.atomic.AtomicLong r4 = r8.f
            r26 = 0
            r28 = 125(0x7d, float:1.75E-43)
            r21 = 0
            r22 = 1
            r23 = 0
            r24 = 0
            r25 = 0
            r19 = r2
            long r2 = defpackage.cqf.i(r19, r21, r22, r23, r24, r25, r26, r28)
            boolean r2 = r4.compareAndSet(r0, r2)
            if (r2 == 0) goto L_0x0020
            int r0 = defpackage.cqf.d(r0)
            r8.a(r0)
            return
        L_0x007b:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            java.lang.String r1 = "Cannot override priority of non-boostable thread"
            r0.<init>(r1)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cpu.<init>(int, java.lang.Runnable):void");
    }

    public final void run() {
        long j;
        Throwable th;
        cqg cqg = this.c;
        cqg.b = Process.myTid();
        do {
            j = cqg.f.get();
        } while (!cqg.f.compareAndSet(j, cqf.i(j, true, true, false, 0, 0, 0, 124)));
        if (!cqf.g(j)) {
            cqg.a(-21);
        }
        Runnable runnable = null;
        try {
            Runnable runnable2 = this.a;
            if (runnable2 == null) {
                jnu.h("startedCallback");
                runnable2 = null;
            }
            runnable2.run();
            super.run();
            Runnable runnable3 = this.b;
            if (runnable3 == null) {
                jnu.h("finishedCallback");
            } else {
                runnable = runnable3;
            }
            runnable.run();
            this.c.c();
            return;
        } catch (Throwable th2) {
            th = th2;
        }
        this.c.c();
        throw th;
    }
}
