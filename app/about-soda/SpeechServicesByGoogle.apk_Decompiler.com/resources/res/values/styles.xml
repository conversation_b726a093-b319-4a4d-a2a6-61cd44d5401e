<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="AlertDialog.AppCompat">
    </style>
    <style name="AlertDialog.AppCompat.Light">
    </style>
    <style name="Animation.AppCompat.Dialog">
    </style>
    <style name="Animation.AppCompat.DropDownUp">
    </style>
    <style name="Animation.MaterialComponents.BottomSheetDialog">
        <item name="android:windowEnterAnimation">@anim/mtrl_bottom_sheet_slide_in</item>
        <item name="android:windowExitAnimation">@anim/mtrl_bottom_sheet_slide_out</item>
    </style>
    <style name="Base.AlertDialog.AppCompat">
        <item name="android:layout">@layout/abc_alert_dialog_material</item>
        <item name="buttonIconDimen">@dimen/abc_alert_dialog_button_dimen</item>
        <item name="listItemLayout">@layout/select_dialog_item_material</item>
        <item name="listLayout">@layout/abc_select_dialog_material</item>
        <item name="multiChoiceItemLayout">@layout/select_dialog_multichoice_material</item>
        <item name="singleChoiceItemLayout">@layout/select_dialog_singlechoice_material</item>
    </style>
    <style name="Base.AlertDialog.AppCompat.Light">
    </style>
    <style name="Base.Animation.AppCompat.Dialog">
        <item name="android:windowEnterAnimation">@anim/abc_popup_enter</item>
        <item name="android:windowExitAnimation">@anim/abc_popup_exit</item>
    </style>
    <style name="Base.Animation.AppCompat.DropDownUp">
        <item name="android:windowEnterAnimation">@anim/abc_grow_fade_in_from_bottom</item>
        <item name="android:windowExitAnimation">@anim/abc_shrink_fade_out_from_bottom</item>
    </style>
    <style name="Base.CardView">
        <item name="cardCornerRadius">@dimen/cardview_default_radius</item>
        <item name="cardElevation">@dimen/cardview_default_elevation</item>
        <item name="cardMaxElevation">@dimen/cardview_default_elevation</item>
        <item name="cardPreventCornerOverlap">true</item>
        <item name="cardUseCompatPadding">false</item>
    </style>
    <style name="Base.DialogWindowTitle.AppCompat">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Title</item>
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
    </style>
    <style name="Base.DialogWindowTitleBackground.AppCompat">
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
        <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
    </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Icon">
        <item name="android:layout_width">32dp</item>
        <item name="android:layout_height">32dp</item>
        <item name="android:src">@null</item>
        <item name="android:scaleType">fitCenter</item>
        <item name="android:contentDescription">@null</item>
        <item name="android:importantForAccessibility">no</item>
    </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Panel">
        <item name="android:paddingLeft">?attr/dialogPreferredPadding</item>
        <item name="android:paddingTop">@dimen/abc_dialog_padding_top_material</item>
        <item name="android:paddingRight">?attr/dialogPreferredPadding</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>
    <style name="Base.MaterialAlertDialog.MaterialComponents.Title.Text">
        <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="android:textColor">@color/material_on_surface_emphasis_high_type</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:singleLine">true</item>
    </style>
    <style name="Base.TextAppearance.AppCompat">
    </style>
    <style name="Base.TextAppearance.AppCompat.Body1">
    </style>
    <style name="Base.TextAppearance.AppCompat.Body2">
    </style>
    <style name="Base.TextAppearance.AppCompat.Button">
    </style>
    <style name="Base.TextAppearance.AppCompat.Caption">
    </style>
    <style name="Base.TextAppearance.AppCompat.Display1">
    </style>
    <style name="Base.TextAppearance.AppCompat.Display2">
    </style>
    <style name="Base.TextAppearance.AppCompat.Display3">
    </style>
    <style name="Base.TextAppearance.AppCompat.Display4">
    </style>
    <style name="Base.TextAppearance.AppCompat.Headline">
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.SearchResult.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Subhead">
    </style>
    <style name="Base.TextAppearance.AppCompat.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Menu">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Button.Colored">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Header">
        <item name="android:textSize">@dimen/abc_text_size_menu_header_material</item>
        <item name="android:textColor">?android:attr/textColorSecondary</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Large">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.PopupMenu.Small">
    </style>
    <style name="Base.TextAppearance.AppCompat.Widget.Switch">
    </style>
    <style name="Base.TextAppearance.GoogleMaterial.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0179</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.GoogleMaterial.OpenSearch">
        <item name="android:textSize">@dimen/google_opensearchbar_text_size</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:textColorHint">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="Base.TextAppearance.GoogleMaterial.Overline">
        <item name="android:textSize">@dimen/gm_sys_typescale_overline_text_size</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_overline_text_all_caps</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_overline_letter_spacing</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.GoogleMaterial.Subtitle1">
        <item name="android:textSize">@dimen/gm_sys_typescale_subtitle1_text_size</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_subtitle1_text_all_caps</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_subtitle1_letter_spacing</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.GoogleMaterial.Subtitle2">
        <item name="android:textSize">@dimen/gm_sys_typescale_subtitle2_text_size</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_subtitle2_text_all_caps</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_subtitle2_letter_spacing</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.MaterialComponents.Badge">
        <item name="android:textSize">@dimen/mtrl_badge_text_size</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">?attr/colorOnError</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0893</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.MaterialComponents.Button">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0893</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.MaterialComponents.Headline6">
        <item name="android:textSize">20sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0125</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.MaterialComponents.Subtitle2">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:letterSpacing">0.0071</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
    </style>
    <style name="Base.TextAppearance.Widget.AppCompat.Toolbar.Title">
    </style>
    <style name="Base.Theme.AppCompat">
    </style>
    <style name="Base.Theme.AppCompat.CompactMenu">
        <item name="android:listViewStyle">@style/Widget.AppCompat.ListView.Menu</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.DropDownUp</item>
        <item name="android:itemTextAppearance">?android:attr/textAppearanceMedium</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog">
    </style>
    <style name="Base.Theme.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light">
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.AppCompat.Light.Dialog.MinWidth">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.GoogleMaterial.Dark.Dialog">
    </style>
    <style name="Base.Theme.GoogleMaterial.Dark.Dialog.Alert">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.GoogleMaterial.Light.Dialog">
    </style>
    <style name="Base.Theme.GoogleMaterial.Light.Dialog.Alert">
        <item name="windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.Theme.MaterialComponents">
    </style>
    <style name="Base.Theme.MaterialComponents.Dialog.Bridge">
    </style>
    <style name="Base.Theme.MaterialComponents.Light">
    </style>
    <style name="Base.Theme.MaterialComponents.Light.Dialog.Bridge">
    </style>
    <style name="Base.ThemeOverlay.AppCompat">
    </style>
    <style name="Base.ThemeOverlay.AppCompat.ActionBar">
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dark">
        <item name="android:colorForeground">@color/foreground_material_dark</item>
        <item name="android:colorBackground">@color/background_material_dark</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_light</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_light</item>
        <item name="android:windowBackground">@color/background_material_dark</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_dark</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_dark</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_light</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_light</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="isLightTheme">false</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog">
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Dialog.Alert">
        <item name="android:windowMinWidthMajor">@dimen/abc_dialog_min_width_major</item>
        <item name="android:windowMinWidthMinor">@dimen/abc_dialog_min_width_minor</item>
    </style>
    <style name="Base.ThemeOverlay.AppCompat.Light">
        <item name="android:colorForeground">@color/foreground_material_light</item>
        <item name="android:colorBackground">@color/background_material_light</item>
        <item name="android:textColorPrimary">@color/abc_primary_text_material_light</item>
        <item name="android:textColorPrimaryDisableOnly">@color/abc_primary_text_disable_only_material_light</item>
        <item name="android:textColorSecondary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorPrimaryInverse">@color/abc_primary_text_material_dark</item>
        <item name="android:textColorSecondaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorHintInverse">@color/abc_hint_foreground_material_dark</item>
        <item name="android:windowBackground">@color/background_material_light</item>
        <item name="android:textColorHighlight">@color/highlighted_text_material_light</item>
        <item name="android:textColorHint">@color/abc_hint_foreground_material_light</item>
        <item name="android:colorForegroundInverse">@color/foreground_material_dark</item>
        <item name="android:textColorTertiary">@color/abc_secondary_text_material_light</item>
        <item name="android:textColorTertiaryInverse">@color/abc_secondary_text_material_dark</item>
        <item name="android:textColorPrimaryInverseDisableOnly">@color/abc_primary_text_disable_only_material_dark</item>
        <item name="android:colorBackgroundCacheHint">@color/abc_background_cache_hint_selector_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="isLightTheme">true</item>
    </style>
    <style name="Base.ThemeOverlay.GoogleMaterial.AutoCompleteTextView">
        <item name="colorControlActivated">?attr/colorPrimaryGoogle</item>
    </style>
    <style name="Base.ThemeOverlay.GoogleMaterial.Dialog">
    </style>
    <style name="Base.ThemeOverlay.GoogleMaterial.Dialog.Alert">
    </style>
    <style name="Base.ThemeOverlay.GoogleMaterial.Dialog.Alert.Framework">
    </style>
    <style name="Base.ThemeOverlay.GoogleMaterial.Light.Dialog.Alert.Framework">
    </style>
    <style name="Base.ThemeOverlay.GoogleMaterial.MaterialAlertDialog">
    </style>
    <style name="Base.ThemeOverlay.GoogleMaterial.TextInputEditText">
        <item name="colorControlActivated">?attr/colorPrimaryGoogle</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog">
        <item name="android:windowBackground">@drawable/mtrl_dialog_background</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert">
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework">
        <item name="android:buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework">
        <item name="android:buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    </style>
    <style name="Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog">
    </style>
    <style name="Base.V14.Theme.GoogleMaterial.Dark.Dialog">
        <item name="android:colorBackground">@color/google_dark_default_color_background</item>
        <item name="actionBarStyle">@style/Widget.GoogleMaterial.ActionBar.Solid</item>
        <item name="actionModeStyle">@style/Widget.GoogleMaterial.ActionMode</item>
        <item name="actionOverflowMenuStyle">@style/Widget.GoogleMaterial.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.GoogleMaterial.Dialog.Alert</item>
        <item name="borderlessButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton</item>
        <item name="buttonBarButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Dialog</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button.Colored</item>
        <item name="checkboxStyle">@style/Widget.GoogleMaterial.CompoundButton.CheckBox</item>
        <item name="collapsingToolbarLayoutLargeSize">@dimen/google_appbar_size_large</item>
        <item name="collapsingToolbarLayoutLargeStyle">@style/Widget.GoogleMaterial.CollapsingToolbar.Large</item>
        <item name="collapsingToolbarLayoutMediumSize">@dimen/google_appbar_size_medium</item>
        <item name="collapsingToolbarLayoutMediumStyle">@style/Widget.GoogleMaterial.CollapsingToolbar.Medium</item>
        <item name="colorAccent">?attr/colorPrimaryGoogle</item>
        <item name="colorError">@color/google_dark_default_color_error</item>
        <item name="colorPrimary">@color/google_dark_default_color_primary</item>
        <item name="colorPrimaryDark">@color/google_dark_default_color_primary_dark</item>
        <item name="listPopupWindowStyle">@style/Widget.GoogleMaterial.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.GoogleMaterial.MaterialAlertDialog</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.GoogleMaterial.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.GoogleMaterial.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.GoogleMaterial.MaterialCalendar</item>
        <item name="materialTimePickerTheme">@style/ThemeOverlay.GoogleMaterial.MaterialTimePicker</item>
        <item name="popupMenuStyle">@style/Widget.GoogleMaterial.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.GoogleMaterial.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.GoogleMaterial.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.GoogleMaterial.Body1</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.GoogleMaterial.Body1</item>
        <item name="toolbarStyle">@style/Widget.GoogleMaterial.Toolbar</item>
        <item name="toolbarSurfaceStyle">@style/Widget.GoogleMaterial.Toolbar.Surface</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.GoogleMaterial.Dark.Dialog.Bridge">
        <item name="appBarLayoutStyle">@style/Widget.GoogleMaterial.AppBarLayout</item>
        <item name="bottomAppBarStyle">@style/Widget.GoogleMaterial.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.GoogleMaterial.BottomNavigationView</item>
        <item name="chipGroupStyle">@style/Widget.GoogleMaterial.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.GoogleMaterial.Chip.Input</item>
        <item name="chipStyle">@style/Widget.GoogleMaterial.Chip.Assistive</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.GoogleMaterial.CircularProgressIndicator</item>
        <item name="colorErrorStateContent">@color/google_dark_default_color_error_state_content</item>
        <item name="colorHairline">@color/google_dark_default_color_hairline</item>
        <item name="colorOnBackground">@color/google_dark_default_color_on_background</item>
        <item name="colorOnError">@color/google_dark_default_color_on_error</item>
        <item name="colorOnPrimary">@color/google_dark_default_color_on_primary</item>
        <item name="colorOnPrimaryContainer">@color/google_dark_default_color_on_primary_container</item>
        <item name="colorOnPrimaryContainerStateContent">@color/google_dark_default_color_on_primary_container_state_content</item>
        <item name="colorOnPrimaryContainerStateLayer">@color/google_dark_default_color_on_primary_container_state_layer</item>
        <item name="colorOnPrimaryGoogle">@color/google_dark_default_color_on_primary_google</item>
        <item name="colorOnPrimaryStateContent">@color/google_dark_default_color_on_primary_state_content</item>
        <item name="colorOnPrimaryStateLayer">@color/google_dark_default_color_on_primary_state_layer</item>
        <item name="colorOnPrimaryStateLayerGoogle">@color/google_dark_default_color_on_primary_state_layer_google</item>
        <item name="colorOnSecondary">@color/google_dark_default_color_on_secondary</item>
        <item name="colorOnSecondaryContainer">@color/google_dark_default_color_on_secondary_container</item>
        <item name="colorOnSecondaryContainerStateContent">@color/google_dark_default_color_on_secondary_container_state_content</item>
        <item name="colorOnSecondaryContainerStateLayer">@color/google_dark_default_color_on_secondary_container_state_layer</item>
        <item name="colorOnSecondaryStateContent">@color/google_dark_color_on_secondary_state_content</item>
        <item name="colorOnSecondaryStateLayer">?attr/colorOnSecondary</item>
        <item name="colorOnSurface">@color/google_dark_default_color_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/google_dark_default_color_inverse_on_surface</item>
        <item name="colorOnSurfaceStateContent">@color/google_dark_color_on_surface_state_content</item>
        <item name="colorOnSurfaceStateLayer">?attr/colorOnSurface</item>
        <item name="colorOnSurfaceVariant">@color/google_dark_default_color_on_surface_variant</item>
        <item name="colorOnSurfaceVariantStateContent">@color/google_dark_default_color_on_surface_variant_state_content</item>
        <item name="colorOnSurfaceVariantStateLayer">@color/google_dark_default_color_on_surface_variant_state_layer</item>
        <item name="colorOnTertiaryContainer">@color/google_dark_default_color_on_tertiary_container</item>
        <item name="colorOnTertiaryContainerStateContent">@color/google_dark_default_color_on_tertiary_container_state_content</item>
        <item name="colorOnTertiaryContainerStateLayer">@color/google_dark_default_color_on_tertiary_container_state_layer</item>
        <item name="colorOutline">@color/google_dark_default_color_outline</item>
        <item name="colorPrimaryContainer">@color/google_dark_default_color_primary_container</item>
        <item name="colorPrimaryGoogle">@color/google_dark_default_color_primary_google</item>
        <item name="colorPrimaryGoogleInverse">@color/google_dark_default_color_inverse_primary_google</item>
        <item name="colorPrimaryInverse">@color/google_default_color_primary_google</item>
        <item name="colorPrimaryStateContent">@color/google_dark_default_color_primary_state_content</item>
        <item name="colorPrimaryStateContentGoogle">@color/google_dark_default_color_primary_state_content_google</item>
        <item name="colorPrimaryStateContentGoogleInverse">@color/google_dark_default_color_inverse_primary_state_content_google</item>
        <item name="colorPrimaryStateContentInverse">@color/google_default_color_primary_state_content</item>
        <item name="colorPrimaryStateLayer">?attr/colorPrimary</item>
        <item name="colorPrimaryStateLayerGoogleInverse">?attr/colorPrimaryInverse</item>
        <item name="colorPrimaryStateLayerInverse">?attr/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/google_dark_default_color_primary_variant</item>
        <item name="colorPrimaryVariantGoogle">@color/google_dark_default_color_primary_variant_google</item>
        <item name="colorSecondary">@color/google_dark_default_color_secondary</item>
        <item name="colorSecondaryContainer">@color/google_dark_default_color_secondary_container</item>
        <item name="colorSecondaryStateContent">@color/google_dark_default_color_secondary_state_content</item>
        <item name="colorSecondaryStateLayer">@color/google_dark_default_color_secondary_state_layer</item>
        <item name="colorSecondaryVariant">@color/google_dark_default_color_secondary_variant</item>
        <item name="colorSurface">@color/google_dark_default_color_surface</item>
        <item name="colorSurfaceInverse">@color/google_dark_default_color_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/google_dark_default_color_surface_variant</item>
        <item name="colorTertiaryContainer">@color/google_dark_default_color_tertiary_container</item>
        <item name="colorTextFieldError">@color/google_dark_default_color_textfield_error</item>
        <item name="colorTextFieldHairline">@color/google_dark_color_textfield_hairline</item>
        <item name="colorTextFieldOnSurfaceVariant">@color/google_dark_color_textfield_on_surface_variant</item>
        <item name="colorTextFieldPrimary">?attr/colorPrimaryGoogle</item>
        <item name="colorTextFieldSurface">@color/google_dark_default_color_textfield_surface</item>
        <item name="colorTimePickerSurface">@color/google_dark_default_color_timepicker_surface</item>
        <item name="extendedFloatingActionButtonBrandedStyle">@style/Widget.GoogleMaterial.ExtendedFloatingActionButton.Icon.Branded.Dark</item>
        <item name="extendedFloatingActionButtonPrimaryStyle">@style/Widget.GoogleMaterial.ExtendedFloatingActionButton.Icon.Primary</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.GoogleMaterial.ExtendedFloatingActionButton.Icon</item>
        <item name="floatingActionButtonBrandedStyle">@style/Widget.GoogleMaterial.FloatingActionButton.Branded.Dark</item>
        <item name="floatingActionButtonPrimaryStyle">@style/Widget.GoogleMaterial.FloatingActionButton.Primary</item>
        <item name="floatingActionButtonSecondaryStyle">@style/Widget.GoogleMaterial.FloatingActionButton.Secondary</item>
        <item name="floatingActionButtonStyle">@style/Widget.GoogleMaterial.FloatingActionButton</item>
        <item name="floatingActionButtonSurfaceStyle">@style/Widget.GoogleMaterial.FloatingActionButton</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.GoogleMaterial.LinearProgressIndicator</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.GoogleMaterial.Button.StrokedButton</item>
        <item name="materialButtonStyle">@style/Widget.GoogleMaterial.Button</item>
        <item name="materialCardViewElevatedStyle">@style/Widget.GoogleMaterial.CardView.Elevated</item>
        <item name="materialCardViewOutlinedStyle">@style/Widget.GoogleMaterial.CardView</item>
        <item name="materialCardViewStyle">?attr/materialCardViewOutlinedStyle</item>
        <item name="materialSearchBarStyle">?attr/openSearchBarStyle</item>
        <item name="materialSearchViewPrefixStyle">?attr/openSearchViewPrefixStyle</item>
        <item name="materialSearchViewStyle">?attr/openSearchViewStyle</item>
        <item name="materialSearchViewToolbarHeight">@dimen/google_opensearchbar_height</item>
        <item name="materialSearchViewToolbarStyle">@style/Widget.GoogleMaterial.OpenSearchView.Toolbar</item>
        <item name="minTouchTargetSize">0dp</item>
        <item name="motionDurationLong1">@integer/gm_sys_motion_duration_long1</item>
        <item name="motionDurationLong2">@integer/gm_sys_motion_duration_long2</item>
        <item name="motionDurationMedium1">@integer/gm_sys_motion_duration_medium1</item>
        <item name="motionDurationMedium2">@integer/gm_sys_motion_duration_medium2</item>
        <item name="motionDurationShort1">@integer/gm_sys_motion_duration_short1</item>
        <item name="motionDurationShort2">@integer/gm_sys_motion_duration_short2</item>
        <item name="motionEasingAccelerated">@string/gm_sys_motion_easing_accelerated</item>
        <item name="motionEasingDecelerated">@string/gm_sys_motion_easing_decelerated</item>
        <item name="motionEasingEmphasized">@string/gm_sys_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/gm_sys_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/gm_sys_motion_easing_standard</item>
        <item name="motionPath">1</item>
        <item name="navigationRailStyle">@style/Widget.GoogleMaterial.NavigationRailView</item>
        <item name="navigationViewStyle">@style/Widget.GoogleMaterial.NavigationView</item>
        <item name="openSearchBarStyle">@style/Widget.GoogleMaterial.OpenSearchBar</item>
        <item name="openSearchViewPrefixStyle">@style/Widget.GoogleMaterial.OpenSearchView.Prefix</item>
        <item name="openSearchViewStyle">@style/Widget.GoogleMaterial.OpenSearchView</item>
        <item name="openSearchViewToolbarHeight">@dimen/google_opensearchbar_height</item>
        <item name="openSearchViewToolbarStyle">@style/Widget.GoogleMaterial.OpenSearchView.Toolbar</item>
        <item name="productLockupViewStyle">@style/Widget.GoogleMaterial.ProductLockupView.Dark</item>
        <item name="scrimBackground">@color/google_scrim</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.GoogleMaterial.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.GoogleMaterial.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.GoogleMaterial.SmallComponent</item>
        <item name="sliderStyle">@style/Widget.GoogleMaterial.Slider</item>
        <item name="snackbarButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.GoogleMaterial.Snackbar</item>
        <item name="snackbarTextViewStyle">@style/Widget.GoogleMaterial.Snackbar.TextView</item>
        <item name="tabSecondaryStyle">@style/Widget.GoogleMaterial.TabLayout.Secondary</item>
        <item name="tabStyle">@style/Widget.GoogleMaterial.TabLayout</item>
        <item name="textAppearanceBody1">@style/TextAppearance.GoogleMaterial.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.GoogleMaterial.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.GoogleMaterial.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.GoogleMaterial.Caption</item>
        <item name="textAppearanceDisplay1">@style/TextAppearance.GoogleMaterial.Display1</item>
        <item name="textAppearanceDisplay2">@style/TextAppearance.GoogleMaterial.Display2</item>
        <item name="textAppearanceDisplay3">@style/TextAppearance.GoogleMaterial.Display3</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.GoogleMaterial.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.GoogleMaterial.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.GoogleMaterial.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.GoogleMaterial.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.GoogleMaterial.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.GoogleMaterial.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.GoogleMaterial.Overline</item>
        <item name="textAppearanceSubhead1">@style/TextAppearance.GoogleMaterial.Subhead1</item>
        <item name="textAppearanceSubhead2">@style/TextAppearance.GoogleMaterial.Subhead2</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.GoogleMaterial.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.GoogleMaterial.Subtitle2</item>
        <item name="textInputFilledDenseStyle">@style/Widget.GoogleMaterial.TextInputLayout.FilledBox.Dense</item>
        <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.GoogleMaterial.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
        <item name="textInputFilledStyle">@style/Widget.GoogleMaterial.TextInputLayout.FilledBox</item>
        <item name="textInputOutlinedDenseStyle">@style/Widget.GoogleMaterial.TextInputLayout.OutlinedBox.Dense</item>
        <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.GoogleMaterial.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
        <item name="textInputOutlinedStyle">@style/Widget.GoogleMaterial.TextInputLayout.OutlinedBox</item>
        <item name="textInputStyle">?attr/textInputOutlinedStyle</item>
        <item name="themeFontFamily">@font/google_sans</item>
        <item name="themeFontFamilyContent">sans-serif</item>
        <item name="themeFontFamilyContentMedium">sans-serif-medium</item>
        <item name="themeFontFamilyDisplay">@font/google_sans_display</item>
        <item name="themeFontFamilyMedium">@font/google_sans_medium</item>
    </style>
    <style name="Base.V14.Theme.GoogleMaterial.Light.Dialog">
        <item name="android:colorBackground">@color/google_default_color_background</item>
        <item name="actionBarStyle">@style/Widget.GoogleMaterial.Light.ActionBar.Solid</item>
        <item name="actionModeStyle">@style/Widget.GoogleMaterial.ActionMode</item>
        <item name="actionOverflowMenuStyle">@style/Widget.GoogleMaterial.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.GoogleMaterial.Dialog.Alert</item>
        <item name="borderlessButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton</item>
        <item name="buttonBarButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Dialog</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button.Colored</item>
        <item name="checkboxStyle">@style/Widget.GoogleMaterial.CompoundButton.CheckBox</item>
        <item name="collapsingToolbarLayoutLargeSize">@dimen/google_appbar_size_large</item>
        <item name="collapsingToolbarLayoutLargeStyle">@style/Widget.GoogleMaterial.CollapsingToolbar.Large</item>
        <item name="collapsingToolbarLayoutMediumSize">@dimen/google_appbar_size_medium</item>
        <item name="collapsingToolbarLayoutMediumStyle">@style/Widget.GoogleMaterial.CollapsingToolbar.Medium</item>
        <item name="colorAccent">?attr/colorPrimaryGoogle</item>
        <item name="colorError">@color/google_default_color_error</item>
        <item name="colorPrimary">@color/google_default_color_primary</item>
        <item name="colorPrimaryDark">@color/google_default_color_primary_dark</item>
        <item name="listPopupWindowStyle">@style/Widget.GoogleMaterial.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.GoogleMaterial.MaterialAlertDialog</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.GoogleMaterial.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.GoogleMaterial.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.GoogleMaterial.MaterialCalendar</item>
        <item name="materialTimePickerTheme">@style/ThemeOverlay.GoogleMaterial.MaterialTimePicker</item>
        <item name="popupMenuStyle">@style/Widget.GoogleMaterial.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.GoogleMaterial.CompoundButton.RadioButton</item>
        <item name="switchStyle">@style/Widget.GoogleMaterial.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.GoogleMaterial.Body1</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.GoogleMaterial.Body1</item>
        <item name="toolbarStyle">@style/Widget.GoogleMaterial.Toolbar</item>
        <item name="toolbarSurfaceStyle">@style/Widget.GoogleMaterial.Toolbar.Surface</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.GoogleMaterial.Light.Dialog.Bridge">
        <item name="appBarLayoutStyle">@style/Widget.GoogleMaterial.AppBarLayout</item>
        <item name="bottomAppBarStyle">@style/Widget.GoogleMaterial.BottomAppBar</item>
        <item name="bottomNavigationStyle">@style/Widget.GoogleMaterial.BottomNavigationView</item>
        <item name="chipGroupStyle">@style/Widget.GoogleMaterial.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.GoogleMaterial.Chip.Input</item>
        <item name="chipStyle">@style/Widget.GoogleMaterial.Chip.Assistive</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.GoogleMaterial.CircularProgressIndicator</item>
        <item name="colorErrorStateContent">@color/google_default_color_error_state_content</item>
        <item name="colorHairline">@color/google_default_color_hairline</item>
        <item name="colorOnBackground">@color/google_default_color_on_background</item>
        <item name="colorOnError">@color/google_default_color_on_error</item>
        <item name="colorOnPrimary">@color/google_default_color_on_primary</item>
        <item name="colorOnPrimaryContainer">@color/google_default_color_on_primary_container</item>
        <item name="colorOnPrimaryContainerStateContent">@color/google_default_color_on_primary_container_state_content</item>
        <item name="colorOnPrimaryContainerStateLayer">@color/google_default_color_on_primary_container_state_layer</item>
        <item name="colorOnPrimaryGoogle">@color/google_default_color_on_primary_google</item>
        <item name="colorOnPrimaryStateContent">@color/google_default_color_on_primary_state_content</item>
        <item name="colorOnPrimaryStateLayer">@color/google_default_color_on_primary_state_layer</item>
        <item name="colorOnPrimaryStateLayerGoogle">@color/google_default_color_on_primary_state_layer_google</item>
        <item name="colorOnSecondary">@color/google_default_color_on_secondary</item>
        <item name="colorOnSecondaryContainer">@color/google_default_color_on_secondary_container</item>
        <item name="colorOnSecondaryContainerStateContent">@color/google_default_color_on_secondary_container_state_content</item>
        <item name="colorOnSecondaryContainerStateLayer">@color/google_default_color_on_secondary_container_state_layer</item>
        <item name="colorOnSecondaryStateContent">@color/google_default_color_on_secondary_state_content</item>
        <item name="colorOnSecondaryStateLayer">?attr/colorOnSecondary</item>
        <item name="colorOnSurface">@color/google_default_color_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/google_default_color_inverse_on_surface</item>
        <item name="colorOnSurfaceStateContent">@color/google_default_color_on_surface_state_content</item>
        <item name="colorOnSurfaceStateLayer">?attr/colorOnSurface</item>
        <item name="colorOnSurfaceVariant">@color/google_default_color_on_surface_variant</item>
        <item name="colorOnSurfaceVariantStateContent">@color/google_default_color_on_surface_variant_state_content</item>
        <item name="colorOnSurfaceVariantStateLayer">@color/google_default_color_on_surface_variant_state_layer</item>
        <item name="colorOnTertiaryContainer">@color/google_default_color_on_tertiary_container</item>
        <item name="colorOnTertiaryContainerStateContent">@color/google_default_color_on_tertiary_container_state_content</item>
        <item name="colorOnTertiaryContainerStateLayer">@color/google_default_color_on_tertiary_container_state_layer</item>
        <item name="colorOutline">@color/google_default_color_outline</item>
        <item name="colorPrimaryContainer">@color/google_default_color_primary_container</item>
        <item name="colorPrimaryGoogle">@color/google_default_color_primary_google</item>
        <item name="colorPrimaryGoogleInverse">@color/google_default_color_inverse_primary_google</item>
        <item name="colorPrimaryInverse">@color/google_dark_default_color_primary_google</item>
        <item name="colorPrimaryStateContent">@color/google_default_color_primary_state_content</item>
        <item name="colorPrimaryStateContentGoogle">@color/google_default_color_primary_state_content_google</item>
        <item name="colorPrimaryStateContentGoogleInverse">@color/google_default_color_inverse_primary_state_content_google</item>
        <item name="colorPrimaryStateContentInverse">@color/google_dark_default_color_primary_state_content</item>
        <item name="colorPrimaryStateLayer">?attr/colorPrimary</item>
        <item name="colorPrimaryStateLayerGoogleInverse">?attr/colorPrimaryInverse</item>
        <item name="colorPrimaryStateLayerInverse">?attr/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/google_default_color_primary_variant</item>
        <item name="colorPrimaryVariantGoogle">@color/google_default_color_primary_variant_google</item>
        <item name="colorSecondary">@color/google_default_color_secondary</item>
        <item name="colorSecondaryContainer">@color/google_default_color_secondary_container</item>
        <item name="colorSecondaryStateContent">@color/google_default_color_secondary_state_content</item>
        <item name="colorSecondaryStateLayer">@color/google_default_color_secondary_state_layer</item>
        <item name="colorSecondaryVariant">@color/google_default_color_secondary_variant</item>
        <item name="colorSurface">@color/google_default_color_surface</item>
        <item name="colorSurfaceInverse">@color/google_default_color_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/google_default_color_surface_variant</item>
        <item name="colorTertiaryContainer">@color/google_default_color_tertiary_container</item>
        <item name="colorTextFieldError">@color/google_default_color_textfield_error</item>
        <item name="colorTextFieldHairline">@color/google_default_color_textfield_hairline</item>
        <item name="colorTextFieldOnSurfaceVariant">@color/google_default_color_textfield_on_surface_variant</item>
        <item name="colorTextFieldPrimary">?attr/colorOnSecondary</item>
        <item name="colorTextFieldSurface">@color/google_default_color_textfield_surface</item>
        <item name="colorTimePickerSurface">@color/google_default_color_timepicker_surface</item>
        <item name="extendedFloatingActionButtonBrandedStyle">@style/Widget.GoogleMaterial.ExtendedFloatingActionButton.Icon.Branded.Light</item>
        <item name="extendedFloatingActionButtonPrimaryStyle">@style/Widget.GoogleMaterial.ExtendedFloatingActionButton.Icon.Primary</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.GoogleMaterial.ExtendedFloatingActionButton.Icon</item>
        <item name="floatingActionButtonBrandedStyle">@style/Widget.GoogleMaterial.FloatingActionButton.Branded.Light</item>
        <item name="floatingActionButtonPrimaryStyle">@style/Widget.GoogleMaterial.FloatingActionButton.Primary</item>
        <item name="floatingActionButtonSecondaryStyle">@style/Widget.GoogleMaterial.FloatingActionButton.Secondary</item>
        <item name="floatingActionButtonStyle">@style/Widget.GoogleMaterial.FloatingActionButton</item>
        <item name="floatingActionButtonSurfaceStyle">@style/Widget.GoogleMaterial.FloatingActionButton</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.GoogleMaterial.LinearProgressIndicator</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.GoogleMaterial.Button.StrokedButton</item>
        <item name="materialButtonStyle">@style/Widget.GoogleMaterial.Button</item>
        <item name="materialCardViewElevatedStyle">@style/Widget.GoogleMaterial.CardView.Elevated</item>
        <item name="materialCardViewOutlinedStyle">@style/Widget.GoogleMaterial.CardView</item>
        <item name="materialCardViewStyle">?attr/materialCardViewOutlinedStyle</item>
        <item name="materialSearchBarStyle">?attr/openSearchBarStyle</item>
        <item name="materialSearchViewPrefixStyle">?attr/openSearchViewPrefixStyle</item>
        <item name="materialSearchViewStyle">?attr/openSearchViewStyle</item>
        <item name="materialSearchViewToolbarHeight">@dimen/google_opensearchbar_height</item>
        <item name="materialSearchViewToolbarStyle">@style/Widget.GoogleMaterial.OpenSearchView.Toolbar</item>
        <item name="minTouchTargetSize">0dp</item>
        <item name="motionDurationLong1">@integer/gm_sys_motion_duration_long1</item>
        <item name="motionDurationLong2">@integer/gm_sys_motion_duration_long2</item>
        <item name="motionDurationMedium1">@integer/gm_sys_motion_duration_medium1</item>
        <item name="motionDurationMedium2">@integer/gm_sys_motion_duration_medium2</item>
        <item name="motionDurationShort1">@integer/gm_sys_motion_duration_short1</item>
        <item name="motionDurationShort2">@integer/gm_sys_motion_duration_short2</item>
        <item name="motionEasingAccelerated">@string/gm_sys_motion_easing_accelerated</item>
        <item name="motionEasingDecelerated">@string/gm_sys_motion_easing_decelerated</item>
        <item name="motionEasingEmphasized">@string/gm_sys_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/gm_sys_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/gm_sys_motion_easing_standard</item>
        <item name="motionPath">1</item>
        <item name="navigationRailStyle">@style/Widget.GoogleMaterial.NavigationRailView</item>
        <item name="navigationViewStyle">@style/Widget.GoogleMaterial.NavigationView</item>
        <item name="openSearchBarStyle">@style/Widget.GoogleMaterial.OpenSearchBar</item>
        <item name="openSearchViewPrefixStyle">@style/Widget.GoogleMaterial.OpenSearchView.Prefix</item>
        <item name="openSearchViewStyle">@style/Widget.GoogleMaterial.OpenSearchView</item>
        <item name="openSearchViewToolbarHeight">@dimen/google_opensearchbar_height</item>
        <item name="openSearchViewToolbarStyle">@style/Widget.GoogleMaterial.OpenSearchView.Toolbar</item>
        <item name="productLockupViewStyle">@style/Widget.GoogleMaterial.ProductLockupView</item>
        <item name="scrimBackground">@color/google_scrim</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.GoogleMaterial.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.GoogleMaterial.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.GoogleMaterial.SmallComponent</item>
        <item name="sliderStyle">@style/Widget.GoogleMaterial.Slider</item>
        <item name="snackbarButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.GoogleMaterial.Snackbar</item>
        <item name="snackbarTextViewStyle">@style/Widget.GoogleMaterial.Snackbar.TextView</item>
        <item name="tabSecondaryStyle">@style/Widget.GoogleMaterial.TabLayout.Secondary</item>
        <item name="tabStyle">@style/Widget.GoogleMaterial.TabLayout</item>
        <item name="textAppearanceBody1">@style/TextAppearance.GoogleMaterial.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.GoogleMaterial.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.GoogleMaterial.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.GoogleMaterial.Caption</item>
        <item name="textAppearanceDisplay1">@style/TextAppearance.GoogleMaterial.Display1</item>
        <item name="textAppearanceDisplay2">@style/TextAppearance.GoogleMaterial.Display2</item>
        <item name="textAppearanceDisplay3">@style/TextAppearance.GoogleMaterial.Display3</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.GoogleMaterial.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.GoogleMaterial.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.GoogleMaterial.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.GoogleMaterial.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.GoogleMaterial.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.GoogleMaterial.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.GoogleMaterial.Overline</item>
        <item name="textAppearanceSubhead1">@style/TextAppearance.GoogleMaterial.Subhead1</item>
        <item name="textAppearanceSubhead2">@style/TextAppearance.GoogleMaterial.Subhead2</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.GoogleMaterial.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.GoogleMaterial.Subtitle2</item>
        <item name="textInputFilledDenseStyle">@style/Widget.GoogleMaterial.TextInputLayout.FilledBox.Dense</item>
        <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.GoogleMaterial.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
        <item name="textInputFilledStyle">@style/Widget.GoogleMaterial.TextInputLayout.FilledBox</item>
        <item name="textInputOutlinedDenseStyle">@style/Widget.GoogleMaterial.TextInputLayout.OutlinedBox.Dense</item>
        <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.GoogleMaterial.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
        <item name="textInputOutlinedStyle">@style/Widget.GoogleMaterial.TextInputLayout.OutlinedBox</item>
        <item name="textInputStyle">?attr/textInputOutlinedStyle</item>
        <item name="themeFontFamily">@font/google_sans</item>
        <item name="themeFontFamilyContent">sans-serif</item>
        <item name="themeFontFamilyContentMedium">sans-serif-medium</item>
        <item name="themeFontFamilyDisplay">@font/google_sans_display</item>
        <item name="themeFontFamilyMedium">@font/google_sans_medium</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents">
        <item name="android:colorBackground">@color/design_dark_default_color_background</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:timePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:datePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="?0x1010501">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Dark</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.ActionBar.Surface</item>
        <item name="actionBarTheme">@style/ThemeOverlay.MaterialComponents.ActionBar.Surface</item>
        <item name="actionModeStyle">@style/Widget.MaterialComponents.ActionMode</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Surface</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.MaterialComponents.BottomSheetDialog</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorError">@color/design_dark_default_color_error</item>
        <item name="colorPrimary">@color/design_dark_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_dark_default_color_primary_dark</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
        <item name="materialTimePickerTheme">@style/ThemeOverlay.MaterialComponents.TimePicker</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>
        <item name="textInputFilledDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense</item>
        <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
        <item name="textInputFilledStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="textInputOutlinedDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense</item>
        <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
        <item name="textInputOutlinedStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Bridge">
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
        <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
        <item name="colorOnError">@color/design_dark_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_dark_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?attr/colorOnSurface</item>
        <item name="colorOnSecondary">@color/design_dark_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
        <item name="colorPrimarySurface">?attr/colorSurface</item>
        <item name="colorPrimaryVariant">@color/design_dark_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_dark_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_dark_default_color_surface</item>
        <item name="elevationOverlayColor">?attr/colorOnSurface</item>
        <item name="elevationOverlayEnabled">true</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="isMaterialTheme">true</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.MaterialComponents.LinearProgressIndicator</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
        <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>
        <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
        <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
        <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
        <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
        <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
        <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
        <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/material_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
        <item name="motionPath">@integer/material_motion_path</item>
        <item name="navigationRailStyle">@style/Widget.MaterialComponents.NavigationRailView</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background_overlay</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Dialog.Bridge">
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
        <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
        <item name="colorOnError">@color/design_dark_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_dark_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?attr/colorOnSurface</item>
        <item name="colorOnSecondary">@color/design_dark_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
        <item name="colorPrimarySurface">?attr/colorSurface</item>
        <item name="colorPrimaryVariant">@color/design_dark_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_dark_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_dark_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_dark_default_color_surface</item>
        <item name="elevationOverlayColor">?attr/colorOnSurface</item>
        <item name="elevationOverlayEnabled">true</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="isMaterialTheme">true</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.MaterialComponents.LinearProgressIndicator</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
        <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>
        <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
        <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
        <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
        <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
        <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
        <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
        <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/material_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
        <item name="motionPath">@integer/material_motion_path</item>
        <item name="navigationRailStyle">@style/Widget.MaterialComponents.NavigationRailView</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background_overlay</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light">
        <item name="android:colorBackground">@color/design_default_color_background</item>
        <item name="android:buttonBarButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="android:timePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="android:datePickerDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog</item>
        <item name="?0x1010501">@style/Widget.MaterialComponents.PopupMenu.ContextMenu</item>
        <item name="actionBarPopupTheme">@style/ThemeOverlay.MaterialComponents.Light</item>
        <item name="actionBarSize">@dimen/mtrl_toolbar_default_height</item>
        <item name="actionBarStyle">@style/Widget.MaterialComponents.Light.ActionBar.Solid</item>
        <item name="actionModeStyle">@style/Widget.MaterialComponents.ActionMode</item>
        <item name="actionOverflowMenuStyle">@style/Widget.MaterialComponents.PopupMenu.Overflow</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert</item>
        <item name="appBarLayoutStyle">@style/Widget.MaterialComponents.AppBarLayout.Primary</item>
        <item name="borderlessButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="bottomNavigationStyle">@style/Widget.MaterialComponents.BottomNavigationView</item>
        <item name="bottomSheetDialogTheme">@style/ThemeOverlay.MaterialComponents.BottomSheetDialog</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton</item>
        <item name="checkboxStyle">@style/Widget.MaterialComponents.CompoundButton.CheckBox</item>
        <item name="colorAccent">?attr/colorSecondary</item>
        <item name="colorError">@color/design_default_color_error</item>
        <item name="colorPrimary">@color/design_default_color_primary</item>
        <item name="colorPrimaryDark">@color/design_default_color_primary_dark</item>
        <item name="floatingActionButtonStyle">@style/Widget.MaterialComponents.FloatingActionButton</item>
        <item name="listPopupWindowStyle">@style/Widget.MaterialComponents.PopupMenu.ListPopupWindow</item>
        <item name="materialAlertDialogTheme">@style/ThemeOverlay.MaterialComponents.MaterialAlertDialog</item>
        <item name="materialCalendarFullscreenTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
        <item name="materialCalendarTheme">@style/ThemeOverlay.MaterialComponents.MaterialCalendar</item>
        <item name="materialTimePickerTheme">@style/ThemeOverlay.MaterialComponents.TimePicker</item>
        <item name="navigationViewStyle">@style/Widget.MaterialComponents.NavigationView</item>
        <item name="popupMenuStyle">@style/Widget.MaterialComponents.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.MaterialComponents.CompoundButton.RadioButton</item>
        <item name="snackbarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Snackbar</item>
        <item name="snackbarStyle">@style/Widget.MaterialComponents.Snackbar</item>
        <item name="snackbarTextViewStyle">@style/Widget.MaterialComponents.Snackbar.TextView</item>
        <item name="switchStyle">@style/Widget.MaterialComponents.CompoundButton.Switch</item>
        <item name="tabStyle">@style/Widget.MaterialComponents.TabLayout</item>
        <item name="textAppearanceLargePopupMenu">?attr/textAppearanceSubtitle1</item>
        <item name="textAppearanceSmallPopupMenu">?attr/textAppearanceSubtitle1</item>
        <item name="textInputFilledDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense</item>
        <item name="textInputFilledExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu</item>
        <item name="textInputFilledStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="textInputOutlinedDenseStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense</item>
        <item name="textInputOutlinedExposedDropdownMenuStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu</item>
        <item name="textInputOutlinedStyle">@style/Widget.MaterialComponents.TextInputLayout.OutlinedBox</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="toolbarStyle">@style/Widget.MaterialComponents.Toolbar</item>
        <item name="viewInflaterClass">com.google.android.material.theme.MaterialComponentsViewInflater</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Bridge">
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
        <item name="colorOnBackground">@color/design_default_color_on_background</item>
        <item name="colorOnError">@color/design_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?attr/colorOnPrimary</item>
        <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_default_color_on_surface</item>
        <item name="colorPrimarySurface">?attr/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_default_color_surface</item>
        <item name="elevationOverlayColor">?attr/colorOnSurface</item>
        <item name="elevationOverlayEnabled">false</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="isMaterialTheme">true</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.MaterialComponents.LinearProgressIndicator</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
        <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>
        <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
        <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
        <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
        <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
        <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
        <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
        <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/material_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
        <item name="motionPath">@integer/material_motion_path</item>
        <item name="navigationRailStyle">@style/Widget.MaterialComponents.NavigationRailView</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge">
        <item name="badgeStyle">@style/Widget.MaterialComponents.Badge</item>
        <item name="bottomAppBarStyle">@style/Widget.MaterialComponents.BottomAppBar</item>
        <item name="chipGroupStyle">@style/Widget.MaterialComponents.ChipGroup</item>
        <item name="chipStandaloneStyle">@style/Widget.MaterialComponents.Chip.Entry</item>
        <item name="chipStyle">@style/Widget.MaterialComponents.Chip.Action</item>
        <item name="circularProgressIndicatorStyle">@style/Widget.MaterialComponents.CircularProgressIndicator</item>
        <item name="colorOnBackground">@color/design_default_color_on_background</item>
        <item name="colorOnError">@color/design_default_color_on_error</item>
        <item name="colorOnPrimary">@color/design_default_color_on_primary</item>
        <item name="colorOnPrimarySurface">?attr/colorOnPrimary</item>
        <item name="colorOnSecondary">@color/design_default_color_on_secondary</item>
        <item name="colorOnSurface">@color/design_default_color_on_surface</item>
        <item name="colorPrimarySurface">?attr/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/design_default_color_primary_variant</item>
        <item name="colorSecondary">@color/design_default_color_secondary</item>
        <item name="colorSecondaryVariant">@color/design_default_color_secondary_variant</item>
        <item name="colorSurface">@color/design_default_color_surface</item>
        <item name="elevationOverlayColor">?attr/colorOnSurface</item>
        <item name="elevationOverlayEnabled">false</item>
        <item name="extendedFloatingActionButtonStyle">@style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon</item>
        <item name="isMaterialTheme">true</item>
        <item name="linearProgressIndicatorStyle">@style/Widget.MaterialComponents.LinearProgressIndicator</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.Button.OutlinedButton</item>
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button</item>
        <item name="materialButtonToggleGroupStyle">@style/Widget.MaterialComponents.MaterialButtonToggleGroup</item>
        <item name="materialCardViewStyle">@style/Widget.MaterialComponents.CardView</item>
        <item name="minTouchTargetSize">@dimen/mtrl_min_touch_target_size</item>
        <item name="motionDurationLong1">@integer/material_motion_duration_long_1</item>
        <item name="motionDurationLong2">@integer/material_motion_duration_long_2</item>
        <item name="motionDurationMedium1">@integer/material_motion_duration_medium_1</item>
        <item name="motionDurationMedium2">@integer/material_motion_duration_medium_2</item>
        <item name="motionDurationShort1">@integer/material_motion_duration_short_1</item>
        <item name="motionDurationShort2">@integer/material_motion_duration_short_2</item>
        <item name="motionEasingAccelerated">@string/material_motion_easing_accelerated</item>
        <item name="motionEasingDecelerated">@string/material_motion_easing_decelerated</item>
        <item name="motionEasingEmphasized">@string/material_motion_easing_emphasized</item>
        <item name="motionEasingLinear">@string/material_motion_easing_linear</item>
        <item name="motionEasingStandard">@string/material_motion_easing_standard</item>
        <item name="motionPath">@integer/material_motion_path</item>
        <item name="navigationRailStyle">@style/Widget.MaterialComponents.NavigationRailView</item>
        <item name="popupMenuBackground">@drawable/mtrl_popupmenu_background</item>
        <item name="scrimBackground">@color/mtrl_scrim_color</item>
        <item name="shapeAppearanceLargeComponent">@style/ShapeAppearance.MaterialComponents.LargeComponent</item>
        <item name="shapeAppearanceMediumComponent">@style/ShapeAppearance.MaterialComponents.MediumComponent</item>
        <item name="shapeAppearanceSmallComponent">@style/ShapeAppearance.MaterialComponents.SmallComponent</item>
        <item name="sliderStyle">@style/Widget.MaterialComponents.Slider</item>
        <item name="textAppearanceBody1">@style/TextAppearance.MaterialComponents.Body1</item>
        <item name="textAppearanceBody2">@style/TextAppearance.MaterialComponents.Body2</item>
        <item name="textAppearanceButton">@style/TextAppearance.MaterialComponents.Button</item>
        <item name="textAppearanceCaption">@style/TextAppearance.MaterialComponents.Caption</item>
        <item name="textAppearanceHeadline1">@style/TextAppearance.MaterialComponents.Headline1</item>
        <item name="textAppearanceHeadline2">@style/TextAppearance.MaterialComponents.Headline2</item>
        <item name="textAppearanceHeadline3">@style/TextAppearance.MaterialComponents.Headline3</item>
        <item name="textAppearanceHeadline4">@style/TextAppearance.MaterialComponents.Headline4</item>
        <item name="textAppearanceHeadline5">@style/TextAppearance.MaterialComponents.Headline5</item>
        <item name="textAppearanceHeadline6">@style/TextAppearance.MaterialComponents.Headline6</item>
        <item name="textAppearanceOverline">@style/TextAppearance.MaterialComponents.Overline</item>
        <item name="textAppearanceSubtitle1">@style/TextAppearance.MaterialComponents.Subtitle1</item>
        <item name="textAppearanceSubtitle2">@style/TextAppearance.MaterialComponents.Subtitle2</item>
    </style>
    <style name="Base.V14.ThemeOverlay.GoogleMaterial.Dialog">
        <item name="android:buttonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Dialog</item>
        <item name="materialButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Dialog</item>
    </style>
    <style name="Base.V14.ThemeOverlay.GoogleMaterial.Dialog.Alert">
        <item name="buttonBarButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Dialog</item>
    </style>
    <style name="Base.V14.ThemeOverlay.GoogleMaterial.MaterialAlertDialog">
        <item name="android:backgroundDimAmount">0.6</item>
        <item name="android:checkedTextViewStyle">@style/Widget.GoogleMaterial.CheckedTextView</item>
        <item name="alertDialogStyle">@style/MaterialAlertDialog.GoogleMaterial</item>
        <item name="buttonBarButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Dialog</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Dialog</item>
        <item name="buttonBarNeutralButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Dialog.Flush</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Dialog</item>
        <item name="materialAlertDialogBodyTextStyle">@style/MaterialAlertDialog.GoogleMaterial.Body.Text</item>
        <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.GoogleMaterial.Title.Text</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">false</item>
        <item name="android:windowAnimationStyle">@style/Animation.MaterialComponents.BottomSheetDialog</item>
        <item name="bottomSheetStyle">@style/Widget.MaterialComponents.BottomSheet.Modal</item>
        <item name="enableEdgeToEdge">true</item>
        <item name="paddingBottomSystemWindowInsets">true</item>
        <item name="paddingLeftSystemWindowInsets">true</item>
        <item name="paddingRightSystemWindowInsets">true</item>
        <item name="paddingTopSystemWindowInsets">true</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog">
        <item name="materialButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert">
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
    </style>
    <style name="Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="android:backgroundDimAmount">@dimen/m3_comp_scrim_container_opacity</item>
        <item name="android:checkedTextViewStyle">@style/Widget.MaterialComponents.CheckedTextView</item>
        <item name="alertDialogStyle">@style/MaterialAlertDialog.MaterialComponents</item>
        <item name="buttonBarButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarNeutralButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="materialAlertDialogBodyTextStyle">@style/MaterialAlertDialog.MaterialComponents.Body.Text</item>
        <item name="materialAlertDialogButtonSpacerVisibility">@integer/mtrl_view_invisible</item>
    </style>
    <style name="Base.V14.Widget.MaterialComponents.AutoCompleteTextView">
        <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingTop">17dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:paddingBottom">17dp</item>
        <item name="android:dropDownVerticalOffset">@dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="android:popupElevation">@dimen/mtrl_exposed_dropdown_menu_popup_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
    </style>
    <style name="Base.V21.Theme.AppCompat">
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="actionBarDivider">?android:attr/actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionBarSize">?android:attr/actionBarSize</item>
        <item name="actionButtonStyle">?android:attr/actionButtonStyle</item>
        <item name="actionModeBackground">?android:attr/actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:attr/actionModeCloseDrawable</item>
        <item name="borderlessButtonStyle">?android:attr/borderlessButtonStyle</item>
        <item name="buttonStyle">?android:attr/buttonStyle</item>
        <item name="buttonStyleSmall">?android:attr/buttonStyleSmall</item>
        <item name="checkboxStyle">?android:attr/checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="dividerHorizontal">?android:attr/dividerHorizontal</item>
        <item name="dividerVertical">?android:attr/dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/editTextColor</item>
        <item name="homeAsUpIndicator">?android:attr/homeAsUpIndicator</item>
        <item name="listChoiceBackgroundIndicator">?android:attr/listChoiceBackgroundIndicator</item>
        <item name="listPreferredItemHeightSmall">?android:attr/listPreferredItemHeightSmall</item>
        <item name="radioButtonStyle">?android:attr/radioButtonStyle</item>
        <item name="ratingBarStyle">?android:attr/ratingBarStyle</item>
        <item name="selectableItemBackground">?android:attr/selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="spinnerStyle">?android:attr/spinnerStyle</item>
        <item name="textAppearanceLargePopupMenu">?android:attr/textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:attr/textAppearanceSmallPopupMenu</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light">
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
        <item name="actionBarDivider">?android:attr/actionBarDivider</item>
        <item name="actionBarItemBackground">@drawable/abc_action_bar_item_background_material</item>
        <item name="actionBarSize">?android:attr/actionBarSize</item>
        <item name="actionButtonStyle">?android:attr/actionButtonStyle</item>
        <item name="actionModeBackground">?android:attr/actionModeBackground</item>
        <item name="actionModeCloseDrawable">?android:attr/actionModeCloseDrawable</item>
        <item name="borderlessButtonStyle">?android:attr/borderlessButtonStyle</item>
        <item name="buttonStyle">?android:attr/buttonStyle</item>
        <item name="buttonStyleSmall">?android:attr/buttonStyleSmall</item>
        <item name="checkboxStyle">?android:attr/checkboxStyle</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="dividerHorizontal">?android:attr/dividerHorizontal</item>
        <item name="dividerVertical">?android:attr/dividerVertical</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/editTextColor</item>
        <item name="homeAsUpIndicator">?android:attr/homeAsUpIndicator</item>
        <item name="listChoiceBackgroundIndicator">?android:attr/listChoiceBackgroundIndicator</item>
        <item name="listPreferredItemHeightSmall">?android:attr/listPreferredItemHeightSmall</item>
        <item name="radioButtonStyle">?android:attr/radioButtonStyle</item>
        <item name="ratingBarStyle">?android:attr/ratingBarStyle</item>
        <item name="selectableItemBackground">?android:attr/selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="spinnerStyle">?android:attr/spinnerStyle</item>
        <item name="textAppearanceLargePopupMenu">?android:attr/textAppearanceLargePopupMenu</item>
        <item name="textAppearanceSmallPopupMenu">?android:attr/textAppearanceSmallPopupMenu</item>
    </style>
    <style name="Base.V21.Theme.AppCompat.Light.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.Theme.GoogleMaterial.Dark.Dialog">
        <item name="android:alertDialogTheme">@style/ThemeOverlay.GoogleMaterial.Dialog.Alert.Framework</item>
        <item name="android:colorEdgeEffect">@color/google_grey500</item>
    </style>
    <style name="Base.V21.Theme.GoogleMaterial.Light.Dialog">
        <item name="android:alertDialogTheme">@style/ThemeOverlay.GoogleMaterial.Light.Dialog.Alert.Framework</item>
        <item name="android:colorEdgeEffect">@color/google_grey500</item>
    </style>
    <style name="Base.V21.Theme.MaterialComponents">
        <item name="android:alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework</item>
    </style>
    <style name="Base.V21.Theme.MaterialComponents.Light">
        <item name="android:alertDialogTheme">@style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework</item>
    </style>
    <style name="Base.V21.ThemeOverlay.AppCompat.Dialog">
        <item name="android:windowElevation">@dimen/abc_floating_window_z</item>
    </style>
    <style name="Base.V21.ThemeOverlay.GoogleMaterial.Dialog">
        <item name="android:windowBackground">@drawable/google_dialog_background</item>
    </style>
    <style name="Base.V21.ThemeOverlay.GoogleMaterial.Dialog.Alert.Framework">
        <item name="android:buttonBarButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Dialog</item>
    </style>
    <style name="Base.V21.ThemeOverlay.GoogleMaterial.Light.Dialog.Alert.Framework">
        <item name="android:buttonBarButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Dialog</item>
    </style>
    <style name="Base.V21.ThemeOverlay.GoogleMaterial.MaterialAlertDialog">
        <item name="android:windowElevation">@dimen/google_window_elevation</item>
    </style>
    <style name="Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog">
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>
    <style name="Base.V22.Theme.AppCompat">
        <item name="actionModeShareDrawable">?android:attr/actionModeShareDrawable</item>
        <item name="editTextBackground">?android:attr/editTextBackground</item>
    </style>
    <style name="Base.V22.Theme.AppCompat.Light">
        <item name="actionModeShareDrawable">?android:attr/actionModeShareDrawable</item>
        <item name="editTextBackground">?android:attr/editTextBackground</item>
    </style>
    <style name="Base.V23.Theme.AppCompat">
        <item name="actionBarItemBackground">?android:attr/actionBarItemBackground</item>
        <item name="actionMenuTextAppearance">?android:attr/actionMenuTextAppearance</item>
        <item name="actionMenuTextColor">?android:attr/actionMenuTextColor</item>
        <item name="actionOverflowButtonStyle">?android:attr/actionOverflowButtonStyle</item>
        <item name="controlBackground">@drawable/abc_control_background_material</item>
        <item name="ratingBarStyleIndicator">?android:attr/ratingBarStyleIndicator</item>
        <item name="ratingBarStyleSmall">?android:attr/ratingBarStyleSmall</item>
    </style>
    <style name="Base.V23.Theme.AppCompat.Light">
        <item name="actionBarItemBackground">?android:attr/actionBarItemBackground</item>
        <item name="actionMenuTextAppearance">?android:attr/actionMenuTextAppearance</item>
        <item name="actionMenuTextColor">?android:attr/actionMenuTextColor</item>
        <item name="actionOverflowButtonStyle">?android:attr/actionOverflowButtonStyle</item>
        <item name="controlBackground">@drawable/abc_control_background_material</item>
        <item name="ratingBarStyleIndicator">?android:attr/ratingBarStyleIndicator</item>
        <item name="ratingBarStyleSmall">?android:attr/ratingBarStyleSmall</item>
    </style>
    <style name="Base.V24.Theme.GoogleMaterial.Dark.Dialog">
        <item name="?0x1010501">@style/Widget.GoogleMaterial.PopupMenu.ContextMenu</item>
    </style>
    <style name="Base.V24.Theme.GoogleMaterial.Light.Dialog">
        <item name="?0x1010501">@style/Widget.GoogleMaterial.PopupMenu.ContextMenu</item>
    </style>
    <style name="Base.V26.Theme.AppCompat">
        <item name="colorError">?unknown_attr_ref: 1010543</item>
    </style>
    <style name="Base.V26.Theme.AppCompat.Light">
        <item name="colorError">?unknown_attr_ref: 1010543</item>
    </style>
    <style name="Base.V26.Widget.AppCompat.Toolbar">
        <item name="android:touchscreenBlocksFocus">true</item>
        <item name="?0x1010540">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseContentDescription">@string/abc_action_mode_done</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeTheme">?attr/actionBarTheme</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="checkedTextViewStyle">?android:attr/checkedTextViewStyle</item>
        <item name="colorAccent">@color/accent_material_dark</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorError">@color/error_color_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>
        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>
        <item name="isLightTheme">false</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">false</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Dialog">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustUnspecified|stateVisible|adjustResize</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24dp</item>
        <item name="listPreferredItemPaddingRight">24dp</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light">
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarStyle">@style/Widget.AppCompat.Light.ActionBar.Solid</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.Light.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.Light.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.Light.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseContentDescription">@string/abc_action_mode_done</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionModeTheme">?attr/actionBarTheme</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.Light.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat.Light</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_light</item>
        <item name="colorBackgroundFloating">@color/background_floating_material_light</item>
        <item name="colorButtonNormal">@color/button_material_light</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_light</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorError">@color/error_color_material_light</item>
        <item name="colorPrimary">@color/primary_material_light</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_light</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_light</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>
        <item name="editTextBackground">@drawable/abc_edit_text_material</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>
        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>
        <item name="isLightTheme">true</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_light</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.Light.PopupMenu</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.Light.SearchView</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_light</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_light</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="tooltipForegroundColor">@color/foreground_material_dark</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_dark</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">false</item>
    </style>
    <style name="Base.V7.Theme.AppCompat.Light.Dialog">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustUnspecified|stateVisible|adjustResize</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24dp</item>
        <item name="listPreferredItemPaddingRight">24dp</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
    </style>
    <style name="Base.V7.ThemeOverlay.AppCompat.Dialog">
        <item name="android:colorBackground">?attr/colorBackgroundFloating</item>
        <item name="android:windowBackground">@drawable/abc_dialog_material_background</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/RtlOverlay.DialogWindowTitle.AppCompat</item>
        <item name="android:windowTitleBackgroundStyle">@style/Base.DialogWindowTitleBackground.AppCompat</item>
        <item name="android:windowAnimationStyle">@style/Animation.AppCompat.Dialog</item>
        <item name="android:listDivider">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowSoftInputMode">adjustUnspecified|stateVisible|adjustResize</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="android:buttonBarStyle">@style/Widget.AppCompat.ButtonBar.AlertDialog</item>
        <item name="android:windowCloseOnTouchOutside">true</item>
        <item name="listPreferredItemPaddingLeft">24dp</item>
        <item name="listPreferredItemPaddingRight">24dp</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionModeOverlay">true</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
    </style>
    <style name="Base.V7.Widget.AppCompat.Toolbar">
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="android:paddingStart">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingEnd">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="buttonGravity">30</item>
        <item name="collapseContentDescription">@string/abc_toolbar_collapse_description</item>
        <item name="collapseIcon">?attr/homeAsUpIndicator</item>
        <item name="contentInsetStart">16dp</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle</item>
        <item name="titleMargin">4dp</item>
        <item name="titleTextAppearance">@style/TextAppearance.Widget.AppCompat.Toolbar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar">
        <item name="android:gravity">clip_horizontal</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="background">@null</item>
        <item name="backgroundSplit">@null</item>
        <item name="backgroundStacked">@null</item>
        <item name="contentInsetEnd">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStart">@dimen/abc_action_bar_content_inset_material</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="displayOptions">8</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="elevation">@dimen/abc_action_bar_elevation_material</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="popupTheme">?attr/actionBarPopupTheme</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionBar.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabBar">
        <item name="divider">?attr/actionBarDivider</item>
        <item name="dividerPadding">8dp</item>
        <item name="showDividers">2</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.ActionBar.TabView">
    </style>
    <style name="Base.Widget.AppCompat.ActionButton">
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.CloseMode">
        <item name="android:minWidth">56dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ActionButton.Overflow">
    </style>
    <style name="Base.Widget.AppCompat.ActionMode">
        <item name="android:paddingLeft">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingRight">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="android:minHeight">?attr/actionBarSize</item>
        <item name="android:paddingStart">@dimen/abc_action_bar_default_padding_start_material</item>
        <item name="android:paddingEnd">@dimen/abc_action_bar_default_padding_end_material</item>
        <item name="background">?attr/actionModeBackground</item>
        <item name="backgroundSplit">?attr/actionModeSplitBackground</item>
        <item name="buttonGravity">30</item>
        <item name="closeItemLayout">@layout/abc_action_mode_close_item_material</item>
        <item name="contentInsetStart">16dp</item>
        <item name="contentInsetStartWithNavigation">@dimen/abc_action_bar_content_inset_with_nav</item>
        <item name="height">?attr/actionBarSize</item>
        <item name="maxButtonHeight">@dimen/abc_action_bar_default_height_material</item>
        <item name="subtitleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle</item>
        <item name="titleMargin">4dp</item>
        <item name="titleTextStyle">@style/TextAppearance.AppCompat.Widget.ActionMode.Title</item>
    </style>
    <style name="Base.Widget.AppCompat.ActivityChooserView">
        <item name="android:gravity">left|center_horizontal|clip_horizontal</item>
        <item name="android:background">@drawable/abc_ab_share_pack_mtrl_alpha</item>
        <item name="divider">?attr/dividerVertical</item>
        <item name="dividerPadding">6dp</item>
        <item name="showDividers">2</item>
    </style>
    <style name="Base.Widget.AppCompat.AutoCompleteTextView">
        <item name="android:background">?attr/editTextBackground</item>
    </style>
    <style name="Base.Widget.AppCompat.Button">
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless">
    </style>
    <style name="Base.Widget.AppCompat.Button.Borderless.Colored">
    </style>
    <style name="Base.Widget.AppCompat.Button.ButtonBar.AlertDialog">
        <item name="android:minWidth">64dp</item>
        <item name="android:minHeight">@dimen/abc_alert_dialog_button_bar_height</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Colored">
        <item name="android:textAppearance">@style/TextAppearance.AppCompat.Widget.Button.Colored</item>
    </style>
    <style name="Base.Widget.AppCompat.Button.Small">
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar">
    </style>
    <style name="Base.Widget.AppCompat.ButtonBar.AlertDialog">
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.CheckBox">
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.RadioButton">
    </style>
    <style name="Base.Widget.AppCompat.CompoundButton.Switch">
        <item name="android:background">?attr/controlBackground</item>
        <item name="android:textOn">@string/abc_capital_on</item>
        <item name="android:textOff">@string/abc_capital_off</item>
        <item name="android:thumb">@drawable/abc_switch_thumb_material</item>
        <item name="showText">false</item>
        <item name="switchPadding">@dimen/abc_switch_padding</item>
        <item name="switchTextAppearance">@style/TextAppearance.AppCompat.Widget.Switch</item>
        <item name="track">@drawable/abc_switch_track_mtrl_alpha</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle">
        <item name="barLength">18dp</item>
        <item name="drawableSize">24dp</item>
        <item name="gapBetweenBars">3dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DrawerArrowToggle.Common">
        <item name="arrowHeadLength">8dp</item>
        <item name="arrowShaftLength">16dp</item>
        <item name="color">?android:attr/textColorSecondary</item>
        <item name="spinBars">true</item>
        <item name="thickness">2dp</item>
    </style>
    <style name="Base.Widget.AppCompat.DropDownItem.Spinner">
    </style>
    <style name="Base.Widget.AppCompat.EditText">
        <item name="?0x10104dd">@null</item>
        <item name="?0x10104de">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.ImageButton">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar">
        <item name="actionButtonStyle">@style/Widget.AppCompat.Light.ActionButton</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.Light.ActionButton.Overflow</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.Solid">
        <item name="background">?attr/colorPrimary</item>
        <item name="backgroundSplit">?attr/colorPrimary</item>
        <item name="backgroundStacked">?attr/colorPrimary</item>
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabBar">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabText">
    </style>
    <style name="Base.Widget.AppCompat.Light.ActionBar.TabView">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu">
    </style>
    <style name="Base.Widget.AppCompat.Light.PopupMenu.Overflow">
        <item name="android:dropDownHorizontalOffset">-4dp</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.ListMenuView">
        <item name="subMenuArrow">@drawable/abc_ic_arrow_drop_right_black_24dp</item>
    </style>
    <style name="Base.Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Base.Widget.AppCompat.ListView">
    </style>
    <style name="Base.Widget.AppCompat.ListView.DropDown">
    </style>
    <style name="Base.Widget.AppCompat.ListView.Menu">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu">
    </style>
    <style name="Base.Widget.AppCompat.PopupMenu.Overflow">
        <item name="android:dropDownHorizontalOffset">-4dp</item>
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.AppCompat.RatingBar">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Indicator">
    </style>
    <style name="Base.Widget.AppCompat.RatingBar.Small">
    </style>
    <style name="Base.Widget.AppCompat.SearchView">
        <item name="closeIcon">@drawable/abc_ic_clear_material</item>
        <item name="commitIcon">@drawable/abc_ic_commit_search_api_mtrl_alpha</item>
        <item name="goIcon">@drawable/abc_ic_go_search_api_material</item>
        <item name="layout">@layout/abc_search_view</item>
        <item name="queryBackground">@drawable/abc_textfield_search_material</item>
        <item name="searchHintIcon">@drawable/abc_ic_search_api_material</item>
        <item name="searchIcon">@drawable/abc_ic_search_api_material</item>
        <item name="submitBackground">@drawable/abc_textfield_search_material</item>
        <item name="suggestionRowLayout">@layout/abc_search_dropdown_item_icons_2line</item>
        <item name="voiceIcon">@drawable/abc_ic_voice_search_api_material</item>
    </style>
    <style name="Base.Widget.AppCompat.SearchView.ActionBar">
        <item name="defaultQueryHint">@string/abc_search_hint</item>
        <item name="queryBackground">@null</item>
        <item name="searchHintIcon">@null</item>
        <item name="submitBackground">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.SeekBar">
    </style>
    <style name="Base.Widget.AppCompat.Spinner">
    </style>
    <style name="Base.Widget.AppCompat.TextView">
        <item name="?0x10104dd">1</item>
        <item name="?0x10104de">@null</item>
    </style>
    <style name="Base.Widget.AppCompat.TextView.SpinnerItem">
    </style>
    <style name="Base.Widget.AppCompat.Toolbar">
    </style>
    <style name="Base.Widget.AppCompat.Toolbar.Button.Navigation">
    </style>
    <style name="Base.Widget.Design.TabLayout">
        <item name="android:background">@null</item>
        <item name="tabIconTint">@null</item>
        <item name="tabIndicator">@drawable/mtrl_tabs_default_indicator</item>
        <item name="tabIndicatorAnimationDuration">@integer/design_tab_indicator_anim_duration_ms</item>
        <item name="tabIndicatorAnimationMode">@null</item>
        <item name="tabIndicatorColor">?attr/colorAccent</item>
        <item name="tabIndicatorGravity">@null</item>
        <item name="tabMaxWidth">@dimen/design_tab_max_width</item>
        <item name="tabPaddingEnd">12dp</item>
        <item name="tabPaddingStart">12dp</item>
        <item name="tabRippleColor">?attr/colorControlHighlight</item>
        <item name="tabTextAppearance">@style/TextAppearance.Design.Tab</item>
        <item name="tabTextColor">@null</item>
        <item name="tabUnboundedRipple">false</item>
    </style>
    <style name="Base.Widget.GoogleMaterial.ActionBar.Solid">
        <item name="subtitleTextStyle">@style/TextAppearance.GoogleMaterial.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.GoogleMaterial.ActionBar.Title</item>
    </style>
    <style name="Base.Widget.GoogleMaterial.Chip">
        <item name="android:textAppearance">@style/TextAppearance.GoogleMaterial.Chip.Input</item>
        <item name="android:textColor">@color/google_chip_text_color</item>
        <item name="android:stateListAnimator">@animator/google_chip_state_list_anim</item>
        <item name="checkedIcon">@drawable/ic_google_chip_checked_circle</item>
        <item name="chipBackgroundColor">@color/google_chip_background_color</item>
        <item name="chipIcon">@null</item>
        <item name="chipIconSize">18dp</item>
        <item name="chipStrokeColor">@color/google_chip_stroke_color</item>
        <item name="chipStrokeWidth">1dp</item>
        <item name="chipSurfaceColor">@color/google_chip_surface_color</item>
        <item name="closeIcon">@drawable/ic_google_chip_close</item>
        <item name="closeIconSize">18dp</item>
        <item name="closeIconTint">@color/google_chip_text_color</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">false</item>
        <item name="ensureMinTouchTargetSize">false</item>
        <item name="rippleColor">@color/google_chip_ripple_color</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.GoogleMaterial.Chip</item>
    </style>
    <style name="Base.Widget.GoogleMaterial.CollapsingToolbar">
        <item name="collapsedTitleTextAppearance">?attr/textAppearanceHeadline6</item>
    </style>
    <style name="Base.Widget.GoogleMaterial.ExtendedFloatingActionButton.Icon.Branded">
        <item name="android:textColor">@color/google_extended_fab_branded_text_color</item>
        <item name="android:paddingLeft">@dimen/google_extended_fab_start_padding_branded</item>
        <item name="android:paddingTop">@dimen/google_extended_fab_top_padding_branded</item>
        <item name="android:paddingBottom">@dimen/google_extended_fab_bottom_padding_branded</item>
        <item name="android:paddingStart">@dimen/google_extended_fab_start_padding_branded</item>
        <item name="icon">@drawable/coloredicons_cic_create_36</item>
        <item name="iconPadding">@dimen/google_extended_fab_icon_text_spacing_branded</item>
        <item name="iconSize">@dimen/google_extended_fab_icon_size_branded</item>
        <item name="iconTint">@null</item>
        <item name="rippleColor">@color/google_fab_branded_ripple_color</item>
    </style>
    <style name="Base.Widget.GoogleMaterial.FloatingActionButton.Branded">
        <item name="fabSize">@null</item>
        <item name="maxImageSize">@dimen/google_fab_branded_image_size</item>
        <item name="rippleColor">@color/google_fab_branded_ripple_color</item>
        <item name="srcCompat">@drawable/coloredicons_cic_create_36</item>
        <item name="tint">@null</item>
    </style>
    <style name="Base.Widget.GoogleMaterial.MaterialCalendar.NavigationButton">
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
        <item name="iconTint">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="Base.Widget.Material3.CollapsingToolbar">
        <item name="android:clipToPadding">false</item>
        <item name="collapsedTitleTextAppearance">?attr/textAppearanceTitleLarge</item>
        <item name="collapsedTitleTextColor">?attr/colorOnSurface</item>
        <item name="expandedTitleMarginBottom">@dimen/m3_appbar_expanded_title_margin_bottom</item>
        <item name="expandedTitleMarginEnd">@dimen/m3_appbar_expanded_title_margin_horizontal</item>
        <item name="expandedTitleMarginStart">@dimen/m3_appbar_expanded_title_margin_horizontal</item>
        <item name="expandedTitleTextColor">?attr/colorOnSurface</item>
        <item name="extraMultilineHeightEnabled">true</item>
        <item name="forceApplySystemWindowInsetTop">true</item>
        <item name="scrimAnimationDuration">?attr/motionDurationMedium2</item>
        <item name="statusBarScrim">@null</item>
        <item name="titleCollapseMode">1</item>
    </style>
    <style name="Base.Widget.MaterialComponents.AutoCompleteTextView">
        <item name="android:popupBackground">@null</item>
    </style>
    <style name="Base.Widget.MaterialComponents.CheckedTextView">
    </style>
    <style name="Base.Widget.MaterialComponents.Chip">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
        <item name="android:textColor">@color/mtrl_chip_text_color</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="android:text">@null</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:checkable">false</item>
        <item name="android:stateListAnimator">@animator/mtrl_chip_state_list_anim</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_circle</item>
        <item name="checkedIconVisible">true</item>
        <item name="chipBackgroundColor">@color/mtrl_chip_background_color</item>
        <item name="chipEndPadding">6dp</item>
        <item name="chipIcon">@null</item>
        <item name="chipIconSize">24dp</item>
        <item name="chipIconVisible">true</item>
        <item name="chipMinHeight">32dp</item>
        <item name="chipMinTouchTargetSize">48dp</item>
        <item name="chipStartPadding">4dp</item>
        <item name="chipStrokeColor">?attr/colorOnSurface</item>
        <item name="chipStrokeWidth">0dp</item>
        <item name="chipSurfaceColor">@color/mtrl_chip_surface_color</item>
        <item name="closeIcon">@drawable/ic_mtrl_chip_close_circle</item>
        <item name="closeIconEndPadding">2dp</item>
        <item name="closeIconSize">18dp</item>
        <item name="closeIconStartPadding">2dp</item>
        <item name="closeIconTint">@color/mtrl_chip_close_icon_tint</item>
        <item name="closeIconVisible">true</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="ensureMinTouchTargetSize">true</item>
        <item name="iconEndPadding">0dp</item>
        <item name="iconStartPadding">0dp</item>
        <item name="rippleColor">@color/mtrl_on_surface_ripple_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.Chip</item>
        <item name="textEndPadding">6dp</item>
        <item name="textStartPadding">8dp</item>
    </style>
    <style name="Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton">
        <item name="android:background">?attr/actionBarItemBackground</item>
        <item name="android:tint">?attr/colorOnPrimary</item>
    </style>
    <style name="Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton">
        <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
        <item name="iconTint">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu">
        <item name="android:dropDownVerticalOffset">1px</item>
        <item name="overlapAnchor">false</item>
    </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu.ContextMenu">
        <item name="android:overlapAnchor">true</item>
    </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow">
        <item name="android:popupBackground">?attr/popupMenuBackground</item>
        <item name="android:dropDownVerticalOffset">1dp</item>
        <item name="android:popupElevation">8dp</item>
    </style>
    <style name="Base.Widget.MaterialComponents.PopupMenu.Overflow">
        <item name="android:dropDownVerticalOffset">1px</item>
    </style>
    <style name="Base.Widget.MaterialComponents.Slider">
        <item name="haloColor">@color/material_slider_halo_color</item>
        <item name="haloRadius">@dimen/mtrl_slider_halo_radius</item>
        <item name="labelStyle">@style/Widget.MaterialComponents.Tooltip</item>
        <item name="minSeparation">0dp</item>
        <item name="thumbColor">@color/material_slider_thumb_color</item>
        <item name="thumbElevation">@dimen/mtrl_slider_thumb_elevation</item>
        <item name="thumbRadius">@dimen/mtrl_slider_thumb_radius</item>
        <item name="tickColorActive">@color/material_slider_active_tick_marks_color</item>
        <item name="tickColorInactive">@color/material_slider_inactive_tick_marks_color</item>
        <item name="tickRadiusActive">@dimen/mtrl_slider_tick_radius</item>
        <item name="tickRadiusInactive">@dimen/mtrl_slider_tick_radius</item>
        <item name="trackColorActive">@color/material_slider_active_track_color</item>
        <item name="trackColorInactive">@color/material_slider_inactive_track_color</item>
        <item name="trackHeight">@dimen/mtrl_slider_track_height</item>
    </style>
    <style name="Base.Widget.MaterialComponents.Snackbar">
        <item name="android:paddingLeft">@dimen/mtrl_snackbar_padding_horizontal</item>
        <item name="android:paddingRight">@dimen/mtrl_snackbar_padding_horizontal</item>
        <item name="actionTextColorAlpha">@dimen/mtrl_snackbar_action_text_color_alpha</item>
        <item name="backgroundOverlayColorAlpha">@dimen/mtrl_snackbar_background_overlay_color_alpha</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextInputEditText">
        <item name="android:textAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingTop">17dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:paddingBottom">17dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="textInputLayoutFocusedRectEnabled">true</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextInputLayout">
        <item name="android:textColorHint">@color/mtrl_indicator_text_color</item>
        <item name="android:maxWidth">@dimen/material_textinput_max_width</item>
        <item name="android:minWidth">@dimen/material_textinput_min_width</item>
        <item name="boxBackgroundColor">@null</item>
        <item name="boxBackgroundMode">2</item>
        <item name="boxCollapsedPaddingTop">0dp</item>
        <item name="boxStrokeColor">@color/mtrl_outlined_stroke_color</item>
        <item name="boxStrokeErrorColor">@color/mtrl_error</item>
        <item name="boxStrokeWidth">@dimen/mtrl_textinput_box_stroke_width_default</item>
        <item name="boxStrokeWidthFocused">@dimen/mtrl_textinput_box_stroke_width_focused</item>
        <item name="counterOverflowTextAppearance">?attr/textAppearanceCaption</item>
        <item name="counterOverflowTextColor">@color/mtrl_error</item>
        <item name="counterTextAppearance">?attr/textAppearanceCaption</item>
        <item name="counterTextColor">@color/mtrl_indicator_text_color</item>
        <item name="cursorColor">@null</item>
        <item name="cursorErrorColor">@color/mtrl_error</item>
        <item name="endIconTint">@color/mtrl_outlined_icon_tint</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="errorIconDrawable">@drawable/mtrl_ic_error</item>
        <item name="errorIconTint">@color/mtrl_error</item>
        <item name="errorTextAppearance">?attr/textAppearanceCaption</item>
        <item name="errorTextColor">@color/mtrl_error</item>
        <item name="helperTextTextAppearance">?attr/textAppearanceCaption</item>
        <item name="helperTextTextColor">@color/mtrl_indicator_text_color</item>
        <item name="hintTextAppearance">?attr/textAppearanceCaption</item>
        <item name="hintTextColor">?attr/colorPrimary</item>
        <item name="placeholderTextAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="placeholderTextColor">@color/mtrl_indicator_text_color</item>
        <item name="prefixTextAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="prefixTextColor">@color/mtrl_indicator_text_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
        <item name="shapeAppearanceOverlay">@null</item>
        <item name="startIconTint">@color/mtrl_outlined_icon_tint</item>
        <item name="suffixTextAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="suffixTextColor">@color/mtrl_indicator_text_color</item>
    </style>
    <style name="Base.Widget.MaterialComponents.TextView">
    </style>
    <style name="BasePreferenceThemeOverlay">
        <item name="android:scrollbars">none</item>
        <item name="checkBoxPreferenceStyle">@style/Preference.CheckBoxPreference.Material</item>
        <item name="dialogPreferenceStyle">@style/Preference.DialogPreference.Material</item>
        <item name="dropdownPreferenceStyle">@style/Preference.DropDown.Material</item>
        <item name="editTextPreferenceStyle">@style/Preference.DialogPreference.EditTextPreference.Material</item>
        <item name="preferenceCategoryStyle">@style/Preference.Category.Material</item>
        <item name="preferenceCategoryTitleTextAppearance">@style/TextAppearance.AppCompat.Body2</item>
        <item name="preferenceFragmentCompatStyle">@style/PreferenceFragment.Material</item>
        <item name="preferenceFragmentListStyle">@style/PreferenceFragmentList.Material</item>
        <item name="preferenceFragmentStyle">@style/PreferenceFragment.Material</item>
        <item name="preferenceScreenStyle">@style/Preference.PreferenceScreen.Material</item>
        <item name="preferenceStyle">@style/Preference.Material</item>
        <item name="seekBarPreferenceStyle">@style/Preference.SeekBarPreference.Material</item>
        <item name="switchPreferenceCompatStyle">@style/Preference.SwitchPreferenceCompat.Material</item>
        <item name="switchPreferenceStyle">@style/Preference.SwitchPreference.Material</item>
    </style>
    <style name="CarUiPreferenceTheme">
        <item name="checkBoxPreferenceStyle">@style/Preference.CarUi.CheckBoxPreference</item>
        <item name="dialogPreferenceStyle">@style/Preference.CarUi.DialogPreference</item>
        <item name="dropdownPreferenceStyle">@style/Preference.CarUi.DropDown</item>
        <item name="editTextPreferenceStyle">@style/Preference.CarUi.DialogPreference.EditTextPreference</item>
        <item name="preferenceCategoryStyle">@style/Preference.CarUi.Category</item>
        <item name="preferenceFragmentCompatStyle">@style/PreferenceFragment.CarUi</item>
        <item name="preferenceFragmentListStyle">@style/PreferenceFragmentList.CarUi</item>
        <item name="preferenceFragmentStyle">@style/PreferenceFragment.CarUi</item>
        <item name="preferenceScreenStyle">@style/Preference.CarUi.PreferenceScreen</item>
        <item name="preferenceStyle">@style/Preference.CarUi</item>
        <item name="seekBarPreferenceStyle">@style/Preference.CarUi.SeekBarPreference</item>
        <item name="switchPreferenceStyle">@style/Preference.CarUi.SwitchPreference</item>
    </style>
    <style name="name">0</style>
    <style name="CardView">
        <item name="cardBackgroundColor">?unknown_attr_ref: 10104e2</item>
    </style>
    <style name="IntentApiCustom">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="MaterialAlertDialog.GoogleMaterial">
        <item name="enforceMaterialTheme">true</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialAlertDialog.GoogleMaterial</item>
    </style>
    <style name="MaterialAlertDialog.GoogleMaterial.Body.Text">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style>
    <style name="MaterialAlertDialog.GoogleMaterial.Title.Text">
        <item name="android:textAppearance">?attr/textAppearanceSubhead1</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents">
        <item name="android:layout">@layout/mtrl_alert_dialog</item>
        <item name="backgroundInsetBottom">@dimen/mtrl_alert_dialog_background_inset_bottom</item>
        <item name="backgroundInsetEnd">@dimen/mtrl_alert_dialog_background_inset_end</item>
        <item name="backgroundInsetStart">@dimen/mtrl_alert_dialog_background_inset_start</item>
        <item name="backgroundInsetTop">@dimen/mtrl_alert_dialog_background_inset_top</item>
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="listItemLayout">@layout/mtrl_alert_select_dialog_item</item>
        <item name="multiChoiceItemLayout">@layout/mtrl_alert_select_dialog_multichoice</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
        <item name="singleChoiceItemLayout">@layout/mtrl_alert_select_dialog_singlechoice</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Body.Text">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
        <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Icon">
        <item name="android:layout_gravity">right|center_horizontal|clip_horizontal</item>
        <item name="android:layout_marginRight">8dp</item>
        <item name="android:layout_marginEnd">8dp</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Panel">
        <item name="android:orientation">@null</item>
    </style>
    <style name="MaterialAlertDialog.MaterialComponents.Title.Text">
        <item name="android:layout_gravity">right|center_horizontal|clip_horizontal</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="Platform.AppCompat">
    </style>
    <style name="Platform.AppCompat.Light">
    </style>
    <style name="Platform.GoogleMaterial.Dark.Dialog.Bridge">
    </style>
    <style name="Platform.GoogleMaterial.Light.Dialog.Bridge">
    </style>
    <style name="Platform.MaterialComponents">
    </style>
    <style name="Platform.MaterialComponents.Dialog">
    </style>
    <style name="Platform.MaterialComponents.Light">
    </style>
    <style name="Platform.MaterialComponents.Light.Dialog">
    </style>
    <style name="Platform.ThemeOverlay.AppCompat">
        <item name="android:colorControlNormal">?attr/colorControlNormal</item>
        <item name="android:colorControlActivated">?attr/colorControlActivated</item>
        <item name="android:colorButtonNormal">?attr/colorButtonNormal</item>
        <item name="android:colorControlHighlight">?attr/colorControlHighlight</item>
        <item name="android:colorPrimary">?attr/colorPrimary</item>
        <item name="android:colorPrimaryDark">?attr/colorPrimaryDark</item>
        <item name="android:colorAccent">?attr/colorAccent</item>
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Dark">
    </style>
    <style name="Platform.ThemeOverlay.AppCompat.Light">
    </style>
    <style name="Platform.V25.AppCompat">
    </style>
    <style name="Platform.V25.AppCompat.Light">
    </style>
    <style name="Preference">
        <item name="android:layout">@layout/preference</item>
    </style>
    <style name="Preference.CarUi">
        <item name="android:layout">@layout/car_ui_preference</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">false</item>
    </style>
    <style name="Preference.CarUi.Category">
        <item name="android:layout">@layout/car_ui_preference_category</item>
        <item name="android:selectable">false</item>
        <item name="android:shouldDisableView">false</item>
    </style>
    <style name="Preference.CarUi.CheckBoxPreference">
        <item name="android:widgetLayout">@layout/car_ui_preference_widget_checkbox</item>
    </style>
    <style name="Preference.CarUi.DialogPreference">
        <item name="android:positiveButtonText">@string/car_ui_dialog_preference_positive</item>
        <item name="android:negativeButtonText">@string/car_ui_dialog_preference_negative</item>
    </style>
    <style name="Preference.CarUi.DialogPreference.EditTextPreference">
        <item name="android:dialogLayout">@layout/car_ui_preference_dialog_edittext</item>
    </style>
    <style name="Preference.CarUi.DialogSeekBarPreference">
    </style>
    <style name="Preference.CarUi.DialogSeekBarPreference.LeftText">
    </style>
    <style name="Preference.CarUi.DialogSeekBarPreference.RightText">
    </style>
    <style name="Preference.CarUi.DialogSeekBarPreference.Seekbar">
    </style>
    <style name="Preference.CarUi.DialogSeekBarPreference.TopText">
    </style>
    <style name="Preference.CarUi.Divider">
        <item name="android:background">@color/car_ui_preference_two_action_divider_color</item>
    </style>
    <style name="Preference.CarUi.DropDown">
        <item name="android:layout">@layout/car_ui_preference_dropdown</item>
    </style>
    <style name="Preference.CarUi.Icon">
    </style>
    <style name="Preference.CarUi.Information">
        <item name="android:enabled">false</item>
        <item name="android:shouldDisableView">false</item>
    </style>
    <style name="Preference.CarUi.Preference">
    </style>
    <style name="Preference.CarUi.PreferenceScreen">
    </style>
    <style name="Preference.CarUi.SeekBarPreference">
        <item name="android:layout">@layout/car_ui_preference_widget_seekbar</item>
        <item name="adjustable">true</item>
        <item name="showSeekBarValue">false</item>
    </style>
    <style name="Preference.CarUi.SwitchPreference">
        <item name="android:widgetLayout">@layout/car_ui_preference_widget_switch</item>
        <item name="android:switchTextOn">@string/car_ui_preference_switch_on</item>
        <item name="android:switchTextOff">@string/car_ui_preference_switch_off</item>
    </style>
    <style name="Preference.Category">
        <item name="android:layout">@layout/preference_category</item>
        <item name="android:selectable">false</item>
        <item name="android:shouldDisableView">false</item>
    </style>
    <style name="Preference.Category.Material">
        <item name="android:layout">@layout/preference_category_material</item>
        <item name="allowDividerAbove">true</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
    </style>
    <style name="Preference.CheckBoxPreference">
        <item name="android:widgetLayout">@layout/preference_widget_checkbox</item>
    </style>
    <style name="Preference.CheckBoxPreference.Material">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
    </style>
    <style name="Preference.DialogPreference">
        <item name="android:positiveButtonText">@android:string/ok</item>
        <item name="android:negativeButtonText">@android:string/cancel</item>
    </style>
    <style name="Preference.DialogPreference.EditTextPreference">
        <item name="android:dialogLayout">@layout/preference_dialog_edittext</item>
    </style>
    <style name="Preference.DialogPreference.EditTextPreference.Material">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
        <item name="singleLineTitle">false</item>
    </style>
    <style name="Preference.DialogPreference.Material">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
    </style>
    <style name="Preference.DropDown">
        <item name="android:layout">@layout/preference_dropdown</item>
    </style>
    <style name="Preference.DropDown.Material">
        <item name="android:layout">@layout/preference_dropdown_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
    </style>
    <style name="Preference.Material">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
        <item name="singleLineTitle">false</item>
    </style>
    <style name="Preference.PreferenceScreen">
    </style>
    <style name="Preference.PreferenceScreen.Material">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
    </style>
    <style name="Preference.SeekBarPreference">
        <item name="android:layout">@layout/preference_widget_seekbar</item>
        <item name="adjustable">true</item>
        <item name="showSeekBarValue">true</item>
        <item name="updatesContinuously">false</item>
    </style>
    <style name="Preference.SeekBarPreference.Material">
        <item name="android:layout">@layout/preference_widget_seekbar_material</item>
        <item name="adjustable">true</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
        <item name="showSeekBarValue">false</item>
    </style>
    <style name="Preference.SwitchPreference">
        <item name="android:widgetLayout">@layout/preference_widget_switch</item>
        <item name="android:switchTextOn">@string/v7_preference_on</item>
        <item name="android:switchTextOff">@string/v7_preference_off</item>
    </style>
    <style name="Preference.SwitchPreference.Material">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
        <item name="singleLineTitle">false</item>
    </style>
    <style name="Preference.SwitchPreferenceCompat">
        <item name="android:widgetLayout">@layout/preference_widget_switch_compat</item>
        <item name="android:switchTextOn">@string/v7_preference_on</item>
        <item name="android:switchTextOff">@string/v7_preference_off</item>
    </style>
    <style name="Preference.SwitchPreferenceCompat.Material">
        <item name="android:layout">@layout/preference_material</item>
        <item name="allowDividerAbove">false</item>
        <item name="allowDividerBelow">true</item>
        <item name="iconSpaceReserved">@bool/config_materialPreferenceIconSpaceReserved</item>
    </style>
    <style name="PreferenceCategoryTitleTextStyle">
        <item name="android:textAppearance">?attr/preferenceCategoryTitleTextAppearance</item>
        <item name="android:textColor">?attr/preferenceCategoryTitleTextColor</item>
    </style>
    <style name="PreferenceFragment">
        <item name="android:paddingLeft">0dp</item>
        <item name="android:paddingRight">0dp</item>
        <item name="android:divider">?android:attr/listDivider</item>
        <item name="android:paddingStart">0dp</item>
        <item name="android:paddingEnd">0dp</item>
    </style>
    <style name="PreferenceFragment.CarUi">
        <item name="android:layout">@layout/car_ui_preference_fragment</item>
        <item name="android:divider">?android:attr/listDivider</item>
    </style>
    <style name="name">0</style>
    <style name="PreferenceFragment.Material">
        <item name="android:divider">@drawable/preference_list_divider_material</item>
        <item name="allowDividerAfterLastItem">false</item>
    </style>
    <style name="PreferenceFragmentList">
        <item name="android:paddingLeft">16dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:paddingStart">16dp</item>
        <item name="android:paddingEnd">16dp</item>
    </style>
    <style name="PreferenceFragmentList.CarUi">
        <item name="android:paddingLeft">0dp</item>
        <item name="android:paddingTop">0dp</item>
        <item name="android:paddingRight">0dp</item>
        <item name="android:paddingBottom">0dp</item>
        <item name="android:paddingStart">0dp</item>
        <item name="android:paddingEnd">0dp</item>
    </style>
    <style name="PreferenceFragmentList.Material">
        <item name="android:paddingLeft">0dp</item>
        <item name="android:paddingRight">0dp</item>
        <item name="android:paddingStart">0dp</item>
        <item name="android:paddingEnd">0dp</item>
    </style>
    <style name="PreferenceSummaryTextStyle">
        <item name="android:textAppearance">?android:attr/textAppearanceListItemSecondary</item>
    </style>
    <style name="PreferenceThemeOverlay">
        <item name="preferenceCategoryTitleTextColor">?android:attr/colorAccent</item>
    </style>
    <style name="RtlOverlay.DialogWindowTitle.AppCompat">
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.ActionBar.TitleItem">
        <item name="android:layout_gravity">right|center_horizontal|clip_horizontal</item>
        <item name="android:paddingEnd">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem">
        <item name="android:paddingEnd">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup">
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut">
        <item name="android:textAlignment">viewEnd</item>
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow">
        <item name="android:layout_marginStart">8dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Text">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:layout_alignParentStart">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.PopupMenuItem.Title">
        <item name="android:textAlignment">viewStart</item>
        <item name="android:layout_marginStart">16dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown">
        <item name="android:paddingStart">@dimen/abc_dropdownitem_text_padding_left</item>
        <item name="android:paddingEnd">4dp</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1">
        <item name="android:layout_alignParentStart">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2">
        <item name="android:layout_toStartOf">@id/edit_query</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Query">
        <item name="android:layout_alignParentEnd">true</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.Search.DropDown.Text">
        <item name="android:layout_toStartOf">@android:id/icon2</item>
        <item name="android:layout_toEndOf">@android:id/icon1</item>
    </style>
    <style name="RtlOverlay.Widget.AppCompat.SearchView.MagIcon">
        <item name="android:layout_marginStart">@dimen/abc_dropdownitem_text_padding_left</item>
    </style>
    <style name="SettingsActionBarStyle">
        <item name="background">@null</item>
    </style>
    <style name="SettingsDropDownPreference">
        <item name="allowDividerAbove">true</item>
        <item name="allowDividerBelow">true</item>
    </style>
    <style name="SettingsPreferenceCategory">
        <item name="allowDividerAbove">true</item>
        <item name="allowDividerBelow">true</item>
    </style>
    <style name="SettingsPreferenceFragment">
        <item name="allowDividerAbove">true</item>
        <item name="allowDividerBelow">true</item>
    </style>
    <style name="SettingsPreferenceFragmentList">
        <item name="allowDividerAbove">true</item>
        <item name="allowDividerBelow">true</item>
    </style>
    <style name="SettingsPreferenceScreen">
        <item name="allowDividerAbove">true</item>
        <item name="allowDividerBelow">true</item>
    </style>
    <style name="SettingsPreferenceStyle">
        <item name="allowDividerAbove">true</item>
        <item name="allowDividerBelow">true</item>
    </style>
    <style name="SettingsSwitchPreferenceStyle">
        <item name="allowDividerAbove">true</item>
        <item name="allowDividerBelow">true</item>
    </style>
    <style name="SettingsTheme">
        <item name="android:windowNoTitle">true</item>
        <item name="android:panelBackground">@android:color/transparent</item>
        <item name="android:dropDownListViewStyle">@style/Widget.AppCompat.ListView.DropDown</item>
        <item name="android:textViewStyle">@style/Widget.AppCompat.TextView</item>
        <item name="android:dropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="android:spinnerItemStyle">@style/Widget.AppCompat.TextView.SpinnerItem</item>
        <item name="android:textAppearanceButton">@style/TextAppearance.AppCompat.Widget.Button</item>
        <item name="android:windowActionBar">false</item>
        <item name="actionBarDivider">?attr/dividerVertical</item>
        <item name="actionBarItemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">@dimen/abc_action_bar_default_height_material</item>
        <item name="actionBarSplitStyle">?attr/actionBarStyle</item>
        <item name="actionBarStyle">@style/SettingsActionBarStyle</item>
        <item name="actionBarTabBarStyle">@style/Widget.AppCompat.ActionBar.TabBar</item>
        <item name="actionBarTabStyle">@style/Widget.AppCompat.ActionBar.TabView</item>
        <item name="actionBarTabTextStyle">@style/Widget.AppCompat.ActionBar.TabText</item>
        <item name="actionBarTheme">@style/ThemeOverlay.AppCompat.ActionBar</item>
        <item name="actionBarWidgetTheme">@null</item>
        <item name="actionButtonStyle">@style/Widget.AppCompat.ActionButton</item>
        <item name="actionDropDownStyle">@style/Widget.AppCompat.Spinner.DropDown.ActionBar</item>
        <item name="actionMenuTextAppearance">@style/TextAppearance.AppCompat.Widget.ActionBar.Menu</item>
        <item name="actionMenuTextColor">?android:attr/textColorPrimaryDisableOnly</item>
        <item name="actionModeBackground">@drawable/abc_cab_background_top_material</item>
        <item name="actionModeCloseButtonStyle">@style/Widget.AppCompat.ActionButton.CloseMode</item>
        <item name="actionModeCloseDrawable">@drawable/abc_ic_ab_back_material</item>
        <item name="actionModeCopyDrawable">@drawable/abc_ic_menu_copy_mtrl_am_alpha</item>
        <item name="actionModeCutDrawable">@drawable/abc_ic_menu_cut_mtrl_alpha</item>
        <item name="actionModePasteDrawable">@drawable/abc_ic_menu_paste_mtrl_am_alpha</item>
        <item name="actionModeSelectAllDrawable">@drawable/abc_ic_menu_selectall_mtrl_alpha</item>
        <item name="actionModeShareDrawable">@drawable/abc_ic_menu_share_mtrl_alpha</item>
        <item name="actionModeSplitBackground">?attr/colorPrimaryDark</item>
        <item name="actionModeStyle">@style/Widget.AppCompat.ActionMode</item>
        <item name="actionOverflowButtonStyle">@style/Widget.AppCompat.ActionButton.Overflow</item>
        <item name="actionOverflowMenuStyle">@style/Widget.AppCompat.PopupMenu.Overflow</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">@style/AlertDialog.AppCompat</item>
        <item name="alertDialogTheme">@style/ThemeOverlay.AppCompat.Dialog.Alert</item>
        <item name="autoCompleteTextViewStyle">@style/Widget.AppCompat.AutoCompleteTextView</item>
        <item name="borderlessButtonStyle">@style/Widget.AppCompat.Button.Borderless</item>
        <item name="buttonBarButtonStyle">@style/Widget.AppCompat.Button.ButtonBar.AlertDialog</item>
        <item name="buttonBarNegativeButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?attr/buttonBarButtonStyle</item>
        <item name="buttonBarStyle">@style/Widget.AppCompat.ButtonBar</item>
        <item name="buttonStyle">@style/Widget.AppCompat.Button</item>
        <item name="buttonStyleSmall">@style/Widget.AppCompat.Button.Small</item>
        <item name="checkboxStyle">@style/Widget.AppCompat.CompoundButton.CheckBox</item>
        <item name="colorAccent">@color/accent_material_dark</item>
        <item name="colorButtonNormal">@color/button_material_dark</item>
        <item name="colorControlActivated">?attr/colorAccent</item>
        <item name="colorControlHighlight">@color/ripple_material_dark</item>
        <item name="colorControlNormal">?android:attr/textColorSecondary</item>
        <item name="colorError">@color/error_color_material_dark</item>
        <item name="colorPrimary">@color/primary_material_dark</item>
        <item name="colorPrimaryDark">@color/primary_dark_material_dark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">@dimen/abc_dialog_corner_radius_material</item>
        <item name="dialogPreferenceStyle">@style/Preference.DialogPreference.Material</item>
        <item name="dialogPreferredPadding">@dimen/abc_dialog_padding_material</item>
        <item name="dialogTheme">@style/ThemeOverlay.AppCompat.Dialog</item>
        <item name="dividerHorizontal">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="dividerVertical">@drawable/abc_list_divider_mtrl_alpha</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>
        <item name="dropdownPreferenceStyle">@style/SettingsDropDownPreference</item>
        <item name="editTextColor">?android:attr/textColorPrimary</item>
        <item name="editTextPreferenceStyle">@style/Preference.DialogPreference.EditTextPreference.Material</item>
        <item name="editTextStyle">@style/Widget.AppCompat.EditText</item>
        <item name="homeAsUpIndicator">@drawable/abc_ic_ab_back_material</item>
        <item name="imageButtonStyle">@style/Widget.AppCompat.ImageButton</item>
        <item name="isLightTheme">false</item>
        <item name="listChoiceBackgroundIndicator">@drawable/abc_list_selector_holo_dark</item>
        <item name="listDividerAlertDialog">@null</item>
        <item name="listMenuViewStyle">@style/Widget.AppCompat.ListMenuView</item>
        <item name="listPopupWindowStyle">@style/Widget.AppCompat.ListPopupWindow</item>
        <item name="listPreferredItemHeight">@dimen/abc_list_item_height_material</item>
        <item name="listPreferredItemHeightLarge">@dimen/abc_list_item_height_large_material</item>
        <item name="listPreferredItemHeightSmall">@dimen/abc_list_item_height_small_material</item>
        <item name="listPreferredItemPaddingEnd">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingLeft">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingRight">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="listPreferredItemPaddingStart">@dimen/abc_list_item_padding_horizontal_material</item>
        <item name="panelBackground">@drawable/abc_menu_hardkey_panel_mtrl_mult</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">@style/Widget.AppCompat.PopupMenu</item>
        <item name="preferenceCategoryStyle">@style/SettingsPreferenceCategory</item>
        <item name="preferenceFragmentListStyle">@style/SettingsPreferenceFragmentList</item>
        <item name="preferenceFragmentStyle">@style/SettingsPreferenceFragment</item>
        <item name="preferenceScreenStyle">@style/SettingsPreferenceScreen</item>
        <item name="preferenceStyle">@style/SettingsPreferenceStyle</item>
        <item name="radioButtonStyle">@style/Widget.AppCompat.CompoundButton.RadioButton</item>
        <item name="ratingBarStyle">@style/Widget.AppCompat.RatingBar</item>
        <item name="ratingBarStyleIndicator">@style/Widget.AppCompat.RatingBar.Indicator</item>
        <item name="ratingBarStyleSmall">@style/Widget.AppCompat.RatingBar.Small</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView</item>
        <item name="seekBarStyle">@style/Widget.AppCompat.SeekBar</item>
        <item name="selectableItemBackground">@drawable/abc_item_background_holo_dark</item>
        <item name="selectableItemBackgroundBorderless">?attr/selectableItemBackground</item>
        <item name="spinnerDropDownItemStyle">@style/Widget.AppCompat.DropDownItem.Spinner</item>
        <item name="spinnerStyle">@style/Widget.AppCompat.Spinner</item>
        <item name="switchPreferenceStyle">@style/SettingsSwitchPreferenceStyle</item>
        <item name="switchStyle">@style/Widget.AppCompat.CompoundButton.Switch</item>
        <item name="textAppearanceLargePopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Large</item>
        <item name="textAppearanceListItem">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearanceListItemSecondary">@style/TextAppearance.AppCompat.Body1</item>
        <item name="textAppearanceListItemSmall">@style/TextAppearance.AppCompat.Subhead</item>
        <item name="textAppearancePopupMenuHeader">@style/TextAppearance.AppCompat.Widget.PopupMenu.Header</item>
        <item name="textAppearanceSearchResultSubtitle">@style/TextAppearance.AppCompat.SearchResult.Subtitle</item>
        <item name="textAppearanceSearchResultTitle">@style/TextAppearance.AppCompat.SearchResult.Title</item>
        <item name="textAppearanceSmallPopupMenu">@style/TextAppearance.AppCompat.Widget.PopupMenu.Small</item>
        <item name="textColorAlertDialogListItem">@color/abc_primary_text_material_dark</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">@style/Widget.AppCompat.Toolbar</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="windowActionBar">true</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">false</item>
    </style>
    <style name="SettingsThemeAutomotive">
    </style>
    <style name="ShapeAppearance.Gm.Sys.Shape.Large">
        <item name="cornerFamily">@null</item>
        <item name="cornerSize">8dp</item>
    </style>
    <style name="ShapeAppearance.Gm.Sys.Shape.Medium">
        <item name="cornerFamily">@null</item>
        <item name="cornerSize">8dp</item>
    </style>
    <style name="ShapeAppearance.Gm.Sys.Shape.Small">
        <item name="cornerFamily">@null</item>
        <item name="cornerSize">4dp</item>
    </style>
    <style name="ShapeAppearance.GoogleMaterial.LargeComponent">
    </style>
    <style name="ShapeAppearance.GoogleMaterial.MediumComponent">
    </style>
    <style name="ShapeAppearance.GoogleMaterial.SmallComponent">
    </style>
    <style name="ShapeAppearance.GoogleMaterial.Tooltip">
        <item name="cornerSize">50%</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents">
        <item name="cornerFamily">@null</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.Badge">
        <item name="cornerFamily">?attr/shapeCornerFamily</item>
        <item name="cornerSize">50%</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.LargeComponent">
        <item name="cornerSize">@dimen/mtrl_shape_corner_size_large_component</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.MediumComponent">
        <item name="cornerSize">@dimen/mtrl_shape_corner_size_medium_component</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.SmallComponent">
        <item name="cornerSize">@dimen/mtrl_shape_corner_size_small_component</item>
    </style>
    <style name="ShapeAppearance.MaterialComponents.Tooltip">
        <item name="cornerFamily">@null</item>
        <item name="cornerSize">4dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.GoogleMaterial.Chip">
        <item name="cornerSize">16dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.GoogleMaterial.FloatingActionButton">
        <item name="cornerFamily">@null</item>
        <item name="cornerSize">@null</item>
    </style>
    <style name="ShapeAppearanceOverlay.GoogleMaterial.NavigationView.Item">
        <item name="cornerSizeBottomLeft">@dimen/google_navigation_item_shape_corner_size_left</item>
        <item name="cornerSizeBottomRight">@dimen/google_navigation_item_shape_corner_size_right</item>
        <item name="cornerSizeTopLeft">@dimen/google_navigation_item_shape_corner_size_left</item>
        <item name="cornerSizeTopRight">@dimen/google_navigation_item_shape_corner_size_right</item>
    </style>
    <style name="ShapeAppearanceOverlay.GoogleMaterial.OpenSearchBar">
        <item name="cornerSize">@dimen/google_opensearchbar_radius</item>
    </style>
    <style name="ShapeAppearanceOverlay.GoogleMaterial.TextField.Filled">
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialAlertDialog.GoogleMaterial">
        <item name="cornerFamily">@null</item>
        <item name="cornerSize">8dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.BottomSheet">
        <item name="cornerSizeBottomLeft">0dp</item>
        <item name="cornerSizeBottomRight">0dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.Chip">
        <item name="cornerSize">50%</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton">
        <item name="cornerSize">@null</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton">
        <item name="cornerSize">50%</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day">
        <item name="cornerSize">@dimen/mtrl_calendar_day_corner</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen">
        <item name="cornerSize">0dp</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year">
        <item name="cornerSize">@dimen/mtrl_calendar_year_corner</item>
    </style>
    <style name="ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox">
        <item name="cornerSizeBottomLeft">@dimen/mtrl_textinput_box_corner_radius_small</item>
        <item name="cornerSizeBottomRight">@dimen/mtrl_textinput_box_corner_radius_small</item>
    </style>
    <style name="TextAppearance.AppCompat">
    </style>
    <style name="TextAppearance.AppCompat.Body1">
    </style>
    <style name="TextAppearance.AppCompat.Body2">
    </style>
    <style name="TextAppearance.AppCompat.Button">
    </style>
    <style name="TextAppearance.AppCompat.Caption">
    </style>
    <style name="TextAppearance.AppCompat.Display1">
    </style>
    <style name="TextAppearance.AppCompat.Display2">
    </style>
    <style name="TextAppearance.AppCompat.Display3">
    </style>
    <style name="TextAppearance.AppCompat.Display4">
    </style>
    <style name="TextAppearance.AppCompat.Headline">
    </style>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Large">
    </style>
    <style name="TextAppearance.AppCompat.Light.Widget.PopupMenu.Small">
    </style>
    <style name="TextAppearance.AppCompat.SearchResult.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.SearchResult.Title">
    </style>
    <style name="TextAppearance.AppCompat.Subhead">
    </style>
    <style name="TextAppearance.AppCompat.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Menu">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionBar.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Subtitle">
    </style>
    <style name="TextAppearance.AppCompat.Widget.ActionMode.Title">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Button.Colored">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Header">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Large">
    </style>
    <style name="TextAppearance.AppCompat.Widget.PopupMenu.Small">
    </style>
    <style name="TextAppearance.AppCompat.Widget.Switch">
    </style>
    <style name="TextAppearance.CarUi">
        <item name="android:textColor">@color/car_ui_text_color_primary</item>
    </style>
    <style name="TextAppearance.CarUi.AlertDialog.Subtitle">
    </style>
    <style name="TextAppearance.CarUi.AlertDialog.Title">
    </style>
    <style name="TextAppearance.CarUi.Body1">
        <item name="android:textSize">@dimen/car_ui_body1_size</item>
    </style>
    <style name="TextAppearance.CarUi.Body2">
        <item name="android:textSize">@dimen/car_ui_body2_size</item>
    </style>
    <style name="TextAppearance.CarUi.Body3">
        <item name="android:textSize">@dimen/car_ui_body3_size</item>
    </style>
    <style name="TextAppearance.CarUi.ListItem">
    </style>
    <style name="TextAppearance.CarUi.ListItem.Body">
        <item name="android:textColor">@color/car_ui_text_color_secondary</item>
    </style>
    <style name="TextAppearance.CarUi.ListItem.Header">
        <item name="android:textColor">@color/car_ui_color_accent</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.CarUi.PreferenceCategoryTitle">
        <item name="android:textColor">@color/car_ui_color_accent</item>
        <item name="android:fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.CarUi.PreferenceEditTextDialogMessage">
    </style>
    <style name="TextAppearance.CarUi.PreferenceSummary">
        <item name="android:textColor">@color/car_ui_text_color_secondary</item>
    </style>
    <style name="TextAppearance.CarUi.PreferenceTitle">
    </style>
    <style name="TextAppearance.CarUi.Sub1">
        <item name="android:textSize">@dimen/car_ui_sub1_size</item>
    </style>
    <style name="TextAppearance.CarUi.Sub2">
        <item name="android:textSize">@dimen/car_ui_sub2_size</item>
    </style>
    <style name="TextAppearance.CarUi.Sub3">
        <item name="android:textSize">@dimen/car_ui_sub3_size</item>
    </style>
    <style name="TextAppearance.CarUi.Widget">
    </style>
    <style name="TextAppearance.CarUi.Widget.Toolbar">
    </style>
    <style name="TextAppearance.CarUi.Widget.Toolbar.Tab">
        <item name="android:textStyle">@null</item>
        <item name="android:textColor">@color/car_ui_toolbar_tab_item_selector</item>
    </style>
    <style name="TextAppearance.CarUi.Widget.Toolbar.Tab.Selected">
    </style>
    <style name="TextAppearance.CarUi.Widget.Toolbar.Title">
        <item name="android:singleLine">true</item>
    </style>
    <style name="TextAppearance.CategoryTitle.SettingsLib">
    </style>
    <style name="TextAppearance.Design.CollapsingToolbar.Expanded">
        <item name="android:textColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="TextAppearance.Design.Counter">
    </style>
    <style name="TextAppearance.Design.Counter.Overflow">
        <item name="android:textColor">@color/design_error</item>
    </style>
    <style name="TextAppearance.Design.Error">
        <item name="android:textColor">@color/design_error</item>
    </style>
    <style name="TextAppearance.Design.HelperText">
    </style>
    <style name="TextAppearance.Design.Hint">
        <item name="android:textColor">?attr/colorControlActivated</item>
    </style>
    <style name="TextAppearance.Design.Placeholder">
    </style>
    <style name="TextAppearance.Design.Prefix">
    </style>
    <style name="TextAppearance.Design.Suffix">
    </style>
    <style name="TextAppearance.Design.Tab">
        <item name="android:textSize">@dimen/design_tab_text_size</item>
        <item name="android:textColor">@color/mtrl_tabs_legacy_text_color_selector</item>
        <item name="textAllCaps">true</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.ActionBar.Subtitle">
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.ActionBar.Title">
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Body1">
        <item name="android:textSize">@dimen/gm_sys_typescale_body1_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_body1_text_all_caps</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_body1_letter_spacing</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Body2">
        <item name="android:textSize">@dimen/gm_sys_typescale_body2_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_body2_text_all_caps</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_body2_letter_spacing</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Button">
        <item name="android:textSize">@dimen/gm_sys_typescale_button_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_button_text_all_caps</item>
        <item name="android:fontFamily">@font/google_sans_medium</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_button_letter_spacing</item>
        <item name="fontFamily">@font/google_sans_medium</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Caption">
        <item name="android:textSize">@dimen/gm_sys_typescale_caption_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_caption_text_all_caps</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_caption_letter_spacing</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Chip.Assistive">
        <item name="android:textColor">@color/google_chip_assistive_text_color</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Chip.Input">
        <item name="android:textSize">@dimen/google_chip_input_text_size</item>
        <item name="android:textColor">@color/google_chip_text_color</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Chip.Suggestive">
        <item name="android:textSize">@dimen/google_chip_suggestive_text_size</item>
        <item name="android:textColor">@color/google_chip_text_color</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Display1">
        <item name="android:textSize">@dimen/gm_sys_typescale_display1_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_display1_text_all_caps</item>
        <item name="android:fontFamily">@font/google_sans</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_display1_letter_spacing</item>
        <item name="fontFamily">@font/google_sans</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Display2">
        <item name="android:textSize">@dimen/gm_sys_typescale_display2_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_display2_text_all_caps</item>
        <item name="android:fontFamily">@font/google_sans</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_display2_letter_spacing</item>
        <item name="fontFamily">@font/google_sans</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Display3">
        <item name="android:textSize">@dimen/gm_sys_typescale_display3_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_display3_text_all_caps</item>
        <item name="android:fontFamily">@font/google_sans</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_display3_letter_spacing</item>
        <item name="fontFamily">@font/google_sans</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Headline1">
        <item name="android:textSize">@dimen/gm_sys_typescale_headline1_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_headline1_text_all_caps</item>
        <item name="android:fontFamily">@font/google_sans</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_headline1_letter_spacing</item>
        <item name="fontFamily">@font/google_sans</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Headline2">
        <item name="android:textSize">@dimen/gm_sys_typescale_headline2_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_headline2_text_all_caps</item>
        <item name="android:fontFamily">@font/google_sans</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_headline2_letter_spacing</item>
        <item name="fontFamily">@font/google_sans</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Headline3">
        <item name="android:textSize">@dimen/gm_sys_typescale_headline3_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_headline3_text_all_caps</item>
        <item name="android:fontFamily">@font/google_sans</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_headline3_letter_spacing</item>
        <item name="fontFamily">@font/google_sans</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Headline4">
        <item name="android:textSize">@dimen/gm_sys_typescale_headline4_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_headline4_text_all_caps</item>
        <item name="android:fontFamily">@font/google_sans</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_headline4_letter_spacing</item>
        <item name="fontFamily">@font/google_sans</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Headline5">
        <item name="android:textSize">@dimen/gm_sys_typescale_headline5_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_headline5_text_all_caps</item>
        <item name="android:fontFamily">@font/google_sans</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_headline5_letter_spacing</item>
        <item name="fontFamily">@font/google_sans</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Headline6">
        <item name="android:textSize">@dimen/gm_sys_typescale_headline6_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_headline6_text_all_caps</item>
        <item name="android:fontFamily">@font/google_sans</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_headline6_letter_spacing</item>
        <item name="fontFamily">@font/google_sans</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.MaterialButton">
        <item name="android:textColor">@color/google_btn_text_color_selector</item>
        <item name="android:letterSpacing">@dimen/google_btn_letter_spacing</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.MaterialButton.Secondary">
        <item name="android:textColor">@color/google_btn_text_color_selector_secondary</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.OpenSearchBar">
    </style>
    <style name="TextAppearance.GoogleMaterial.OpenSearchView">
    </style>
    <style name="TextAppearance.GoogleMaterial.OpenSearchView.Prefix">
        <item name="android:textColor">?android:attr/textColorTertiary</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Overline">
        <item name="android:textStyle">@null</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Subhead1">
        <item name="android:textSize">@dimen/gm_sys_typescale_subhead1_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_subhead1_text_all_caps</item>
        <item name="android:fontFamily">@font/google_sans_medium</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_subhead1_letter_spacing</item>
        <item name="fontFamily">@font/google_sans_medium</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Subhead2">
        <item name="android:textSize">@dimen/gm_sys_typescale_subhead2_text_size</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">@bool/gm_sys_typescale_subhead2_text_all_caps</item>
        <item name="android:fontFamily">@font/google_sans_medium</item>
        <item name="android:letterSpacing">@dimen/gm_sys_typescale_subhead2_letter_spacing</item>
        <item name="fontFamily">@font/google_sans_medium</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Subtitle1">
        <item name="android:textStyle">@null</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Subtitle2">
        <item name="android:textStyle">@null</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Tab">
        <item name="android:textColor">@color/google_tabs_icon_color_selector</item>
        <item name="textAllCaps">false</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.TimePicker.Title">
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Toolbar.Subtitle">
        <item name="android:textSize">@dimen/google_toolbar_text_size_subtitle</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Toolbar.Subtitle.Surface">
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Toolbar.Title">
        <item name="android:textSize">@dimen/google_toolbar_text_size_title</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Toolbar.Title.Surface">
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style>
    <style name="TextAppearance.GoogleMaterial.Tooltip">
        <item name="android:textColor">?attr/colorOnPrimaryGoogle</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Badge">
        <item name="android:textStyle">@null</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body1">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0312</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Body2">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0179</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Button">
        <item name="android:textStyle">@null</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Caption">
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0333</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline1">
        <item name="android:textSize">96sp</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:letterSpacing">-0.0156</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline2">
        <item name="android:textSize">60sp</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif-light</item>
        <item name="android:letterSpacing">-0.0083</item>
        <item name="fontFamily">sans-serif-light</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline3">
        <item name="android:textSize">48sp</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">@null</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline4">
        <item name="android:textSize">34sp</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0074</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline5">
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">@null</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Headline6">
        <item name="android:textStyle">@null</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Overline">
        <item name="android:textSize">10sp</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">true</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.1667</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle1">
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">@null</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:letterSpacing">0.0094</item>
        <item name="fontFamily">sans-serif</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Subtitle2">
        <item name="android:textStyle">@null</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="fontFamily">sans-serif-medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.TimePicker.Title">
        <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="TextAppearance.MaterialComponents.Tooltip">
        <item name="android:textColor">?attr/colorOnPrimary</item>
    </style>
    <style name="TextAppearance.PreferenceTitle.SettingsLib">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Subtitle">
    </style>
    <style name="TextAppearance.Widget.AppCompat.Toolbar.Title">
    </style>
    <style name="Theme.AlertDialog.SettingsLib">
        <item name="android:clipChildren">true</item>
        <item name="android:clipToPadding">true</item>
        <item name="android:windowSoftInputMode">adjustUnspecified|stateHidden</item>
    </style>
    <style name="Theme.AppCompat">
    </style>
    <style name="Theme.AppCompat.CompactMenu">
    </style>
    <style name="Theme.AppCompat.DayNight">
    </style>
    <style name="Theme.AppCompat.DayNight.Dialog.Alert">
    </style>
    <style name="Theme.AppCompat.DayNight.Dialog.MinWidth">
    </style>
    <style name="Theme.AppCompat.Dialog">
    </style>
    <style name="Theme.AppCompat.Dialog.Alert">
    </style>
    <style name="Theme.AppCompat.Dialog.MinWidth">
    </style>
    <style name="Theme.AppCompat.Empty">
    </style>
    <style name="Theme.AppCompat.Light">
    </style>
    <style name="Theme.AppCompat.Light.Dialog">
    </style>
    <style name="Theme.AppCompat.Light.Dialog.Alert">
    </style>
    <style name="Theme.AppCompat.Light.Dialog.MinWidth">
    </style>
    <style name="Theme.AppCompat.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.Asr.Settings">
        <item name="android:alertDialogTheme">@style/Theme.AppCompat.Light.Dialog.Alert</item>
    </style>
    <style name="Theme.CarUi">
        <item name="android:textAppearance">@style/TextAppearance.CarUi</item>
        <item name="android:buttonStyle">@style/Widget.CarUi.Button</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:textColorHighlight">#00ffffff</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:borderlessButtonStyle">@style/Widget.CarUi.Button.Borderless.Colored</item>
        <item name="actionBarDivider">?android:attr/actionBarDivider</item>
        <item name="actionBarItemBackground">?android:attr/actionBarItemBackground</item>
        <item name="actionBarPopupTheme">@null</item>
        <item name="actionBarSize">?android:attr/actionBarSize</item>
        <item name="actionBarSplitStyle">?android:attr/actionBarSplitStyle</item>
        <item name="actionBarStyle">?android:attr/actionBarStyle</item>
        <item name="actionBarTabBarStyle">?android:attr/actionBarTabBarStyle</item>
        <item name="actionBarTabStyle">?android:attr/actionBarTabStyle</item>
        <item name="actionBarTabTextStyle">?android:attr/actionBarTabTextStyle</item>
        <item name="actionBarTheme">?android:attr/actionBarTheme</item>
        <item name="actionBarWidgetTheme">?android:attr/actionBarWidgetTheme</item>
        <item name="actionButtonStyle">?android:attr/actionButtonStyle</item>
        <item name="actionDropDownStyle">?android:attr/actionDropDownStyle</item>
        <item name="actionMenuTextAppearance">?android:attr/actionMenuTextAppearance</item>
        <item name="actionMenuTextColor">?android:attr/actionMenuTextColor</item>
        <item name="actionModeBackground">?android:attr/actionModeBackground</item>
        <item name="actionModeCloseButtonStyle">?android:attr/actionModeCloseButtonStyle</item>
        <item name="actionModeCloseDrawable">?android:attr/actionModeCloseDrawable</item>
        <item name="actionModeCopyDrawable">?android:attr/actionModeCopyDrawable</item>
        <item name="actionModeCutDrawable">?android:attr/actionModeCutDrawable</item>
        <item name="actionModePasteDrawable">?android:attr/actionModePasteDrawable</item>
        <item name="actionModeSelectAllDrawable">?android:attr/actionModeSelectAllDrawable</item>
        <item name="actionModeShareDrawable">?android:attr/actionModeShareDrawable</item>
        <item name="actionModeSplitBackground">?android:attr/actionModeSplitBackground</item>
        <item name="actionModeStyle">?android:attr/actionModeStyle</item>
        <item name="actionOverflowButtonStyle">?android:attr/actionOverflowButtonStyle</item>
        <item name="actionOverflowMenuStyle">?android:attr/actionOverflowMenuStyle</item>
        <item name="activityChooserViewStyle">@style/Widget.AppCompat.ActivityChooserView</item>
        <item name="alertDialogCenterButtons">false</item>
        <item name="alertDialogStyle">?android:attr/alertDialogStyle</item>
        <item name="alertDialogTheme">?android:attr/alertDialogTheme</item>
        <item name="autoCompleteTextViewStyle">?android:attr/autoCompleteTextViewStyle</item>
        <item name="borderlessButtonStyle">?android:attr/borderlessButtonStyle</item>
        <item name="buttonBarButtonStyle">?android:attr/buttonBarButtonStyle</item>
        <item name="buttonBarNegativeButtonStyle">?android:attr/buttonBarNegativeButtonStyle</item>
        <item name="buttonBarNeutralButtonStyle">?android:attr/buttonBarNeutralButtonStyle</item>
        <item name="buttonBarPositiveButtonStyle">?android:attr/buttonBarPositiveButtonStyle</item>
        <item name="buttonBarStyle">?android:attr/buttonBarStyle</item>
        <item name="buttonStyle">?android:attr/buttonStyle</item>
        <item name="buttonStyleSmall">?android:attr/buttonStyleSmall</item>
        <item name="carUiActivity">true</item>
        <item name="carUiBaseLayout">false</item>
        <item name="carUiToolbar">false</item>
        <item name="checkboxStyle">?android:attr/checkboxStyle</item>
        <item name="colorAccent">?android:attr/colorAccent</item>
        <item name="colorBackgroundFloating">?unknown_attr_ref: 10104e2</item>
        <item name="colorButtonNormal">?android:attr/colorButtonNormal</item>
        <item name="colorControlActivated">?android:attr/colorControlActivated</item>
        <item name="colorControlHighlight">?android:attr/colorControlHighlight</item>
        <item name="colorControlNormal">?android:attr/colorControlNormal</item>
        <item name="colorError">?unknown_attr_ref: 1010543</item>
        <item name="colorPrimary">?android:attr/colorPrimary</item>
        <item name="colorPrimaryDark">?android:attr/colorPrimaryDark</item>
        <item name="colorSwitchThumbNormal">@color/switch_thumb_material_dark</item>
        <item name="controlBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="dialogCornerRadius">?unknown_attr_ref: 1010571</item>
        <item name="dialogPreferredPadding">?android:attr/dialogPreferredPadding</item>
        <item name="dialogTheme">?android:attr/dialogTheme</item>
        <item name="dividerHorizontal">?android:attr/dividerHorizontal</item>
        <item name="dividerVertical">?android:attr/dividerVertical</item>
        <item name="drawerArrowStyle">@style/Widget.AppCompat.DrawerArrowToggle</item>
        <item name="dropDownListViewStyle">?android:attr/dropDownListViewStyle</item>
        <item name="dropdownListPreferredItemHeight">?attr/listPreferredItemHeightSmall</item>
        <item name="editTextBackground">?android:attr/editTextBackground</item>
        <item name="editTextColor">?android:attr/editTextColor</item>
        <item name="editTextStyle">?android:attr/editTextStyle</item>
        <item name="homeAsUpIndicator">?android:attr/homeAsUpIndicator</item>
        <item name="imageButtonStyle">?android:attr/imageButtonStyle</item>
        <item name="isLightTheme">@bool/car_ui_is_light_theme</item>
        <item name="listChoiceBackgroundIndicator">?android:attr/listChoiceBackgroundIndicator</item>
        <item name="listChoiceIndicatorMultipleAnimated">?android:attr/listChoiceIndicatorMultiple</item>
        <item name="listChoiceIndicatorSingleAnimated">?android:attr/listChoiceIndicatorSingle</item>
        <item name="listDividerAlertDialog">?android:attr/listDividerAlertDialog</item>
        <item name="listMenuViewStyle">?unknown_attr_ref: 10104f2</item>
        <item name="listPopupWindowStyle">?android:attr/listPopupWindowStyle</item>
        <item name="listPreferredItemHeight">?android:attr/listPreferredItemHeight</item>
        <item name="listPreferredItemHeightLarge">?android:attr/listPreferredItemHeightLarge</item>
        <item name="listPreferredItemHeightSmall">?android:attr/listPreferredItemHeightSmall</item>
        <item name="listPreferredItemPaddingLeft">?android:attr/listPreferredItemPaddingLeft</item>
        <item name="listPreferredItemPaddingRight">?android:attr/listPreferredItemPaddingRight</item>
        <item name="panelBackground">?android:attr/panelBackground</item>
        <item name="panelMenuListTheme">@style/Theme.AppCompat.CompactMenu</item>
        <item name="panelMenuListWidth">@dimen/abc_panel_menu_list_width</item>
        <item name="popupMenuStyle">?android:attr/popupMenuStyle</item>
        <item name="preferenceTheme">@style/CarUiPreferenceTheme</item>
        <item name="radioButtonStyle">?android:attr/radioButtonStyle</item>
        <item name="ratingBarStyle">?android:attr/ratingBarStyle</item>
        <item name="ratingBarStyleIndicator">?android:attr/ratingBarStyleIndicator</item>
        <item name="ratingBarStyleSmall">?android:attr/ratingBarStyleSmall</item>
        <item name="searchViewStyle">?android:attr/searchViewStyle</item>
        <item name="seekBarStyle">?android:attr/seekBarStyle</item>
        <item name="selectableItemBackground">?android:attr/selectableItemBackground</item>
        <item name="selectableItemBackgroundBorderless">?android:attr/selectableItemBackgroundBorderless</item>
        <item name="spinnerDropDownItemStyle">?android:attr/spinnerDropDownItemStyle</item>
        <item name="spinnerStyle">?android:attr/spinnerStyle</item>
        <item name="switchStyle">?android:attr/switchStyle</item>
        <item name="textAppearanceLargePopupMenu">?android:attr/textAppearanceLargePopupMenu</item>
        <item name="textAppearanceListItem">?android:attr/textAppearanceListItem</item>
        <item name="textAppearanceListItemSecondary">?android:attr/textAppearanceListItemSecondary</item>
        <item name="textAppearanceListItemSmall">?android:attr/textAppearanceListItemSmall</item>
        <item name="textAppearancePopupMenuHeader">?unknown_attr_ref: 1010502</item>
        <item name="textAppearanceSearchResultSubtitle">?android:attr/textAppearanceSearchResultSubtitle</item>
        <item name="textAppearanceSearchResultTitle">?android:attr/textAppearanceSearchResultTitle</item>
        <item name="textAppearanceSmallPopupMenu">?android:attr/textAppearanceSmallPopupMenu</item>
        <item name="textColorAlertDialogListItem">?android:attr/textColorAlertDialogListItem</item>
        <item name="textColorSearchUrl">@color/abc_search_url_text</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.AppCompat.Toolbar.Button.Navigation</item>
        <item name="toolbarStyle">?android:attr/toolbarStyle</item>
        <item name="tooltipForegroundColor">@color/foreground_material_light</item>
        <item name="tooltipFrameBackground">@drawable/tooltip_frame_light</item>
        <item name="viewInflaterClass">com.android.car.ui.CarUiLayoutInflaterFactory</item>
        <item name="windowActionBar">false</item>
        <item name="windowActionBarOverlay">false</item>
        <item name="windowActionModeOverlay">false</item>
        <item name="windowFixedHeightMajor">@null</item>
        <item name="windowFixedHeightMinor">@null</item>
        <item name="windowFixedWidthMajor">@null</item>
        <item name="windowFixedWidthMinor">@null</item>
        <item name="windowNoTitle">true</item>
    </style>
    <style name="Theme.CarUi.NoToolbar">
        <item name="carUiBaseLayout">true</item>
        <item name="carUiToolbar">false</item>
    </style>
    <style name="Theme.CarUi.WithToolbar">
        <item name="carUiBaseLayout">true</item>
        <item name="carUiToolbar">true</item>
    </style>
    <style name="Theme.GoogleMaterial.Dark.Dialog.Alert">
    </style>
    <style name="Theme.GoogleMaterial.DayNight.Dialog.Alert">
    </style>
    <style name="Theme.GoogleMaterial.Light.Dialog.Alert">
    </style>
    <style name="Theme.IntentApi.Material">
        <item name="android:background">@drawable/transcription_intent_api_rounded_background</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>
    <style name="Theme.MaterialComponents">
    </style>
    <style name="Theme.MaterialComponents.DayNight">
    </style>
    <style name="Theme.MaterialComponents.Dialog.Bridge">
    </style>
    <style name="Theme.MaterialComponents.Light">
    </style>
    <style name="Theme.MaterialComponents.Light.Dialog.Bridge">
    </style>
    <style name="Theme.SettingsBase">
        <item name="preferenceTheme">@style/PreferenceThemeOverlay</item>
    </style>
    <style name="Theme.SubSettingsBase">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowActionBar">false</item>
        <item name="alertDialogTheme">@style/Theme.AlertDialog.SettingsLib</item>
        <item name="colorControlNormal">?android:attr/colorControlNormal</item>
    </style>
    <style name="Theme.VoiceImeTransparent">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>
    <style name="ThemeOverlay.AppCompat.ActionBar">
    </style>
    <style name="ThemeOverlay.AppCompat.Dark">
    </style>
    <style name="ThemeOverlay.AppCompat.Dialog">
    </style>
    <style name="ThemeOverlay.AppCompat.Dialog.Alert">
    </style>
    <style name="ThemeOverlay.AppCompat.Light">
    </style>
    <style name="ThemeOverlay.Design.TextInputEditText">
    </style>
    <style name="ThemeOverlay.GoogleMaterial.AutoCompleteTextView">
        <item name="colorControlNormal">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.AutoCompleteTextView.FilledBox">
        <item name="autoCompleteTextViewStyle">@style/Widget.GoogleMaterial.AutoCompleteTextView.FilledBox</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.AutoCompleteTextView.OutlinedBox">
        <item name="autoCompleteTextViewStyle">@style/Widget.GoogleMaterial.AutoCompleteTextView.OutlinedBox</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.BottomAppBar">
        <item name="actionMenuTextColor">?attr/colorOnSurfaceVariant</item>
        <item name="colorControlNormal">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.Dark">
        <item name="android:colorBackground">@color/google_dark_default_color_background</item>
        <item name="colorError">@color/google_dark_default_color_error</item>
        <item name="colorErrorStateContent">@color/google_dark_default_color_error_state_content</item>
        <item name="colorHairline">@color/google_dark_default_color_hairline</item>
        <item name="colorOnBackground">@color/google_dark_default_color_on_background</item>
        <item name="colorOnError">@color/google_dark_default_color_on_error</item>
        <item name="colorOnPrimary">@color/google_dark_default_color_on_primary</item>
        <item name="colorOnPrimaryContainer">@color/google_dark_default_color_on_primary_container</item>
        <item name="colorOnPrimaryContainerStateContent">@color/google_dark_default_color_on_primary_container_state_content</item>
        <item name="colorOnPrimaryContainerStateLayer">@color/google_dark_default_color_on_primary_container_state_layer</item>
        <item name="colorOnPrimaryGoogle">@color/google_dark_default_color_on_primary_google</item>
        <item name="colorOnPrimaryStateContent">@color/google_dark_default_color_on_primary_state_content</item>
        <item name="colorOnPrimaryStateLayer">@color/google_dark_default_color_on_primary_state_layer</item>
        <item name="colorOnPrimaryStateLayerGoogle">@color/google_dark_default_color_on_primary_state_layer_google</item>
        <item name="colorOnSecondary">@color/google_dark_default_color_on_secondary</item>
        <item name="colorOnSecondaryContainer">@color/google_dark_default_color_on_secondary_container</item>
        <item name="colorOnSecondaryContainerStateContent">@color/google_dark_default_color_on_secondary_container_state_content</item>
        <item name="colorOnSecondaryContainerStateLayer">@color/google_dark_default_color_on_secondary_container_state_layer</item>
        <item name="colorOnSecondaryStateContent">@color/google_dark_color_on_secondary_state_content</item>
        <item name="colorOnSecondaryStateLayer">?attr/colorOnSecondary</item>
        <item name="colorOnSurface">@color/google_dark_default_color_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/google_dark_default_color_inverse_on_surface</item>
        <item name="colorOnSurfaceStateContent">@color/google_dark_color_on_surface_state_content</item>
        <item name="colorOnSurfaceStateLayer">?attr/colorOnSurface</item>
        <item name="colorOnSurfaceVariant">@color/google_dark_default_color_on_surface_variant</item>
        <item name="colorOnSurfaceVariantStateContent">@color/google_dark_default_color_on_surface_variant_state_content</item>
        <item name="colorOnSurfaceVariantStateLayer">@color/google_dark_default_color_on_surface_variant_state_layer</item>
        <item name="colorOnTertiaryContainer">@color/google_dark_default_color_on_tertiary_container</item>
        <item name="colorOnTertiaryContainerStateContent">@color/google_dark_default_color_on_tertiary_container_state_content</item>
        <item name="colorOnTertiaryContainerStateLayer">@color/google_dark_default_color_on_tertiary_container_state_layer</item>
        <item name="colorOutline">@color/google_dark_default_color_outline</item>
        <item name="colorPrimary">@color/google_dark_default_color_primary</item>
        <item name="colorPrimaryContainer">@color/google_dark_default_color_primary_container</item>
        <item name="colorPrimaryDark">@color/google_dark_default_color_primary_dark</item>
        <item name="colorPrimaryGoogle">@color/google_dark_default_color_primary_google</item>
        <item name="colorPrimaryGoogleInverse">@color/google_dark_default_color_inverse_primary_google</item>
        <item name="colorPrimaryInverse">@color/google_default_color_primary_google</item>
        <item name="colorPrimaryStateContent">@color/google_dark_default_color_primary_state_content</item>
        <item name="colorPrimaryStateContentGoogle">@color/google_dark_default_color_primary_state_content_google</item>
        <item name="colorPrimaryStateContentGoogleInverse">@color/google_dark_default_color_inverse_primary_state_content_google</item>
        <item name="colorPrimaryStateContentInverse">@color/google_default_color_primary_state_content</item>
        <item name="colorPrimaryStateLayer">?attr/colorPrimary</item>
        <item name="colorPrimaryStateLayerGoogleInverse">?attr/colorPrimaryInverse</item>
        <item name="colorPrimaryStateLayerInverse">?attr/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/google_dark_default_color_primary_variant</item>
        <item name="colorPrimaryVariantGoogle">@color/google_dark_default_color_primary_variant_google</item>
        <item name="colorSecondary">@color/google_dark_default_color_secondary</item>
        <item name="colorSecondaryContainer">@color/google_dark_default_color_secondary_container</item>
        <item name="colorSecondaryStateContent">@color/google_dark_default_color_secondary_state_content</item>
        <item name="colorSecondaryStateLayer">@color/google_dark_default_color_secondary_state_layer</item>
        <item name="colorSecondaryVariant">@color/google_dark_default_color_secondary_variant</item>
        <item name="colorSurface">@color/google_dark_default_color_surface</item>
        <item name="colorSurfaceInverse">@color/google_dark_default_color_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/google_dark_default_color_surface_variant</item>
        <item name="colorTertiaryContainer">@color/google_dark_default_color_tertiary_container</item>
        <item name="colorTextFieldError">@color/google_dark_default_color_textfield_error</item>
        <item name="colorTextFieldHairline">@color/google_dark_color_textfield_hairline</item>
        <item name="colorTextFieldOnSurfaceVariant">@color/google_dark_color_textfield_on_surface_variant</item>
        <item name="colorTextFieldPrimary">?attr/colorPrimaryGoogle</item>
        <item name="colorTextFieldSurface">@color/google_dark_default_color_textfield_surface</item>
        <item name="colorTimePickerSurface">@color/google_dark_default_color_timepicker_surface</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.Dialog">
    </style>
    <style name="ThemeOverlay.GoogleMaterial.Dialog.Alert">
    </style>
    <style name="ThemeOverlay.GoogleMaterial.Dialog.Alert.Framework">
    </style>
    <style name="ThemeOverlay.GoogleMaterial.Light">
        <item name="android:colorBackground">@color/google_default_color_background</item>
        <item name="colorError">@color/google_default_color_error</item>
        <item name="colorErrorStateContent">@color/google_default_color_error_state_content</item>
        <item name="colorHairline">@color/google_default_color_hairline</item>
        <item name="colorOnBackground">@color/google_default_color_on_background</item>
        <item name="colorOnError">@color/google_default_color_on_error</item>
        <item name="colorOnPrimary">@color/google_default_color_on_primary</item>
        <item name="colorOnPrimaryContainer">@color/google_default_color_on_primary_container</item>
        <item name="colorOnPrimaryContainerStateContent">@color/google_default_color_on_primary_container_state_content</item>
        <item name="colorOnPrimaryContainerStateLayer">@color/google_default_color_on_primary_container_state_layer</item>
        <item name="colorOnPrimaryGoogle">@color/google_default_color_on_primary_google</item>
        <item name="colorOnPrimaryStateContent">@color/google_default_color_on_primary_state_content</item>
        <item name="colorOnPrimaryStateLayer">@color/google_default_color_on_primary_state_layer</item>
        <item name="colorOnPrimaryStateLayerGoogle">@color/google_default_color_on_primary_state_layer_google</item>
        <item name="colorOnSecondary">@color/google_default_color_on_secondary</item>
        <item name="colorOnSecondaryContainer">@color/google_default_color_on_secondary_container</item>
        <item name="colorOnSecondaryContainerStateContent">@color/google_default_color_on_secondary_container_state_content</item>
        <item name="colorOnSecondaryContainerStateLayer">@color/google_default_color_on_secondary_container_state_layer</item>
        <item name="colorOnSecondaryStateContent">@color/google_default_color_on_secondary_state_content</item>
        <item name="colorOnSecondaryStateLayer">?attr/colorOnSecondary</item>
        <item name="colorOnSurface">@color/google_default_color_on_surface</item>
        <item name="colorOnSurfaceInverse">@color/google_default_color_inverse_on_surface</item>
        <item name="colorOnSurfaceStateContent">@color/google_default_color_on_surface_state_content</item>
        <item name="colorOnSurfaceStateLayer">?attr/colorOnSurface</item>
        <item name="colorOnSurfaceVariant">@color/google_default_color_on_surface_variant</item>
        <item name="colorOnSurfaceVariantStateContent">@color/google_default_color_on_surface_variant_state_content</item>
        <item name="colorOnSurfaceVariantStateLayer">@color/google_default_color_on_surface_variant_state_layer</item>
        <item name="colorOnTertiaryContainer">@color/google_default_color_on_tertiary_container</item>
        <item name="colorOnTertiaryContainerStateContent">@color/google_default_color_on_tertiary_container_state_content</item>
        <item name="colorOnTertiaryContainerStateLayer">@color/google_default_color_on_tertiary_container_state_layer</item>
        <item name="colorOutline">@color/google_default_color_outline</item>
        <item name="colorPrimary">@color/google_default_color_primary</item>
        <item name="colorPrimaryContainer">@color/google_default_color_primary_container</item>
        <item name="colorPrimaryDark">@color/google_default_color_primary_dark</item>
        <item name="colorPrimaryGoogle">@color/google_default_color_primary_google</item>
        <item name="colorPrimaryGoogleInverse">@color/google_default_color_inverse_primary_google</item>
        <item name="colorPrimaryInverse">@color/google_dark_default_color_primary_google</item>
        <item name="colorPrimaryStateContent">@color/google_default_color_primary_state_content</item>
        <item name="colorPrimaryStateContentGoogle">@color/google_default_color_primary_state_content_google</item>
        <item name="colorPrimaryStateContentGoogleInverse">@color/google_default_color_inverse_primary_state_content_google</item>
        <item name="colorPrimaryStateContentInverse">@color/google_dark_default_color_primary_state_content</item>
        <item name="colorPrimaryStateLayer">?attr/colorPrimary</item>
        <item name="colorPrimaryStateLayerGoogleInverse">?attr/colorPrimaryInverse</item>
        <item name="colorPrimaryStateLayerInverse">?attr/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/google_default_color_primary_variant</item>
        <item name="colorPrimaryVariantGoogle">@color/google_default_color_primary_variant_google</item>
        <item name="colorSecondary">@color/google_default_color_secondary</item>
        <item name="colorSecondaryContainer">@color/google_default_color_secondary_container</item>
        <item name="colorSecondaryStateContent">@color/google_default_color_secondary_state_content</item>
        <item name="colorSecondaryStateLayer">@color/google_default_color_secondary_state_layer</item>
        <item name="colorSecondaryVariant">@color/google_default_color_secondary_variant</item>
        <item name="colorSurface">@color/google_default_color_surface</item>
        <item name="colorSurfaceInverse">@color/google_default_color_inverse_surface</item>
        <item name="colorSurfaceVariant">@color/google_default_color_surface_variant</item>
        <item name="colorTertiaryContainer">@color/google_default_color_tertiary_container</item>
        <item name="colorTextFieldError">@color/google_default_color_textfield_error</item>
        <item name="colorTextFieldHairline">@color/google_default_color_textfield_hairline</item>
        <item name="colorTextFieldOnSurfaceVariant">@color/google_default_color_textfield_on_surface_variant</item>
        <item name="colorTextFieldPrimary">?attr/colorOnSecondary</item>
        <item name="colorTextFieldSurface">@color/google_default_color_textfield_surface</item>
        <item name="colorTimePickerSurface">@color/google_default_color_timepicker_surface</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.Light.Dialog.Alert.Framework">
    </style>
    <style name="ThemeOverlay.GoogleMaterial.MaterialAlertDialog">
    </style>
    <style name="ThemeOverlay.GoogleMaterial.MaterialCalendar">
        <item name="android:windowElevation">@dimen/google_window_elevation</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Dialog</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.GoogleMaterial.Button.TextButton.Dialog</item>
        <item name="materialCalendarDay">@style/Widget.GoogleMaterial.MaterialCalendar.DayTextView</item>
        <item name="materialCalendarDayOfWeekLabel">@style/Widget.GoogleMaterial.MaterialCalendar.DayOfWeekLabel</item>
        <item name="materialCalendarHeaderCancelButton">@style/Widget.GoogleMaterial.MaterialCalendar.HeaderCancelButton</item>
        <item name="materialCalendarHeaderConfirmButton">@style/Widget.GoogleMaterial.Button.TextButton</item>
        <item name="materialCalendarHeaderDivider">@style/Widget.GoogleMaterial.MaterialCalendar.HeaderDivider</item>
        <item name="materialCalendarHeaderLayout">@style/Widget.GoogleMaterial.MaterialCalendar.HeaderLayout</item>
        <item name="materialCalendarHeaderSelection">@style/Widget.GoogleMaterial.MaterialCalendar.HeaderSelection</item>
        <item name="materialCalendarHeaderTitle">@style/Widget.GoogleMaterial.MaterialCalendar.HeaderTitle</item>
        <item name="materialCalendarHeaderToggleButton">@style/Widget.GoogleMaterial.MaterialCalendar.HeaderToggleButton</item>
        <item name="materialCalendarMonth">@style/Widget.GoogleMaterial.MaterialCalendar.MonthTextView</item>
        <item name="materialCalendarMonthNavigationButton">@style/Widget.GoogleMaterial.MaterialCalendar.MonthNavigationButton</item>
        <item name="materialCalendarStyle">@style/Widget.GoogleMaterial.MaterialCalendar</item>
        <item name="materialCalendarYearNavigationButton">@style/Widget.GoogleMaterial.MaterialCalendar.YearNavigationButton</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.MaterialCalendar.Fullscreen">
        <item name="android:windowIsFloating">false</item>
        <item name="materialCalendarHeaderLayout">@style/Widget.GoogleMaterial.MaterialCalendar.HeaderLayout.Fullscreen</item>
        <item name="materialCalendarHeaderSelection">@style/Widget.GoogleMaterial.MaterialCalendar.HeaderSelection.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.GoogleMaterial.MaterialCalendar.Fullscreen</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.MaterialTimePicker">
        <item name="android:windowElevation">@dimen/google_window_elevation</item>
        <item name="chipStyle">@style/Widget.GoogleMaterial.TimePicker.Display</item>
        <item name="colorOnPrimary">?attr/colorOnPrimaryGoogle</item>
        <item name="colorPrimary">?attr/colorPrimaryGoogle</item>
        <item name="elevationOverlayEnabled">false</item>
        <item name="imageButtonStyle">@style/Widget.GoogleMaterial.MaterialTimePicker.ImageButton</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.GoogleMaterial.TimePicker.Button</item>
        <item name="materialClockStyle">@style/Widget.GoogleMaterial.MaterialTimePicker.Clock</item>
        <item name="materialDisplayDividerStyle">@style/Widget.MaterialComponents.TimePicker.Display.Divider</item>
        <item name="materialTimePickerStyle">@style/Widget.GoogleMaterial.MaterialTimePicker</item>
        <item name="materialTimePickerTitleStyle">@style/TextAppearance.GoogleMaterial.TimePicker.Title</item>
        <item name="minTouchTargetSize">@dimen/google_min_touch_target_size</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.OpenSearch">
        <item name="actionMenuTextColor">?attr/colorOnSurfaceVariant</item>
        <item name="actionOverflowButtonStyle">@style/Widget.GoogleMaterial.OpenSearch.ActionButton.Overflow</item>
        <item name="colorControlNormal">?attr/colorOnSurfaceVariant</item>
        <item name="toolbarNavigationButtonStyle">@style/Widget.GoogleMaterial.OpenSearch.Toolbar.Button.Navigation</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.Snackbar">
        <item name="colorOnSurface">?attr/colorOnBackground</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.TextInputEditText">
        <item name="colorControlNormal">@color/google_textfield_indicator_text_color</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.TextInputEditText.FilledBox">
        <item name="editTextStyle">@style/Widget.GoogleMaterial.TextInputEditText.FilledBox</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.TextInputEditText.FilledBox.Dense">
        <item name="editTextStyle">@style/Widget.GoogleMaterial.TextInputEditText.FilledBox.Dense</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.TextInputEditText.OutlinedBox">
        <item name="editTextStyle">@style/Widget.GoogleMaterial.TextInputEditText.OutlinedBox</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.TextInputEditText.OutlinedBox.Dense">
        <item name="editTextStyle">@style/Widget.GoogleMaterial.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="ThemeOverlay.GoogleMaterial.Toolbar.Surface">
        <item name="actionMenuTextColor">?attr/colorOnSurfaceVariant</item>
        <item name="colorControlNormal">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.ActionBar.Surface">
        <item name="android:colorBackground">?attr/colorSurface</item>
        <item name="android:textColorPrimary">@color/material_on_surface_emphasis_high_type</item>
        <item name="android:textColorSecondary">@color/material_on_surface_emphasis_medium</item>
        <item name="actionMenuTextColor">@color/material_on_surface_emphasis_medium</item>
        <item name="colorControlNormal">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView">
        <item name="colorControlActivated">?attr/colorPrimary</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox">
        <item name="autoCompleteTextViewStyle">@style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox">
        <item name="autoCompleteTextViewStyle">@style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.BottomSheetDialog">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dark">
        <item name="android:colorBackground">@color/design_dark_default_color_background</item>
        <item name="colorError">@color/design_dark_default_color_error</item>
        <item name="colorOnBackground">@color/design_dark_default_color_on_background</item>
        <item name="colorOnError">@color/design_dark_default_color_on_error</item>
        <item name="colorOnSurface">@color/design_dark_default_color_on_surface</item>
        <item name="colorSurface">@color/design_dark_default_color_surface</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dialog">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dialog.Alert">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Dialog.Alert.Framework">
    </style>
    <style name="ThemeOverlay.MaterialComponents.Light">
        <item name="android:colorBackground">@color/design_default_color_background</item>
        <item name="colorError">@color/design_default_color_error</item>
        <item name="colorOnBackground">@color/design_default_color_on_background</item>
        <item name="colorOnError">@color/design_default_color_on_error</item>
        <item name="colorOnSurface">@color/design_default_color_on_surface</item>
        <item name="colorSurface">@color/design_default_color_surface</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework">
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialAlertDialog">
        <item name="materialAlertDialogTitleIconStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Icon</item>
        <item name="materialAlertDialogTitlePanelStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Panel</item>
        <item name="materialAlertDialogTitleTextStyle">@style/MaterialAlertDialog.MaterialComponents.Title.Text</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialCalendar">
        <item name="buttonBarNegativeButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.MaterialComponents.Button.TextButton.Dialog</item>
        <item name="materialCalendarDay">@style/Widget.MaterialComponents.MaterialCalendar.DayTextView</item>
        <item name="materialCalendarDayOfWeekLabel">@style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel</item>
        <item name="materialCalendarHeaderCancelButton">@style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton</item>
        <item name="materialCalendarHeaderConfirmButton">@style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton</item>
        <item name="materialCalendarHeaderDivider">@style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider</item>
        <item name="materialCalendarHeaderLayout">@style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout</item>
        <item name="materialCalendarHeaderSelection">@style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection</item>
        <item name="materialCalendarHeaderTitle">@style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle</item>
        <item name="materialCalendarHeaderToggleButton">@style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton</item>
        <item name="materialCalendarMonth">@style/Widget.MaterialComponents.MaterialCalendar.MonthTextView</item>
        <item name="materialCalendarMonthNavigationButton">@style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar</item>
        <item name="materialCalendarYearNavigationButton">@style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen">
        <item name="android:windowIsFloating">false</item>
        <item name="materialCalendarHeaderLayout">@style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen</item>
        <item name="materialCalendarHeaderSelection">@style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen</item>
        <item name="materialCalendarStyle">@style/Widget.MaterialComponents.MaterialCalendar.Fullscreen</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText">
        <item name="android:editTextBackground">@null</item>
        <item name="colorControlActivated">?attr/colorPrimary</item>
        <item name="editTextBackground">@null</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense">
        <item name="editTextStyle">@style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TimePicker">
        <item name="chipStyle">@style/Widget.MaterialComponents.TimePicker.Display</item>
        <item name="elevationOverlayEnabled">false</item>
        <item name="imageButtonStyle">@style/Widget.MaterialComponents.TimePicker.ImageButton</item>
        <item name="materialButtonOutlinedStyle">@style/Widget.MaterialComponents.TimePicker.Button</item>
        <item name="materialClockStyle">@style/Widget.MaterialComponents.TimePicker.Clock</item>
        <item name="materialDisplayDividerStyle">@style/Widget.MaterialComponents.TimePicker.Display.Divider</item>
        <item name="materialTimePickerTitleStyle">@style/TextAppearance.MaterialComponents.TimePicker.Title</item>
        <item name="textInputStyle">@style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TimePicker.Display">
        <item name="elevationOverlayEnabled">false</item>
    </style>
    <style name="ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText">
        <item name="android:textViewStyle">@style/Widget.MaterialComponents.TimePicker.Display.HelperText</item>
        <item name="editTextStyle">@style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText</item>
    </style>
    <style name="Widget.AppCompat.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.ActionButton">
    </style>
    <style name="Widget.AppCompat.ActionButton.CloseMode">
    </style>
    <style name="Widget.AppCompat.ActionButton.Overflow">
    </style>
    <style name="Widget.AppCompat.ActionMode">
    </style>
    <style name="Widget.AppCompat.ActivityChooserView">
    </style>
    <style name="Widget.AppCompat.AutoCompleteTextView">
    </style>
    <style name="Widget.AppCompat.Button">
    </style>
    <style name="Widget.AppCompat.Button.Borderless">
    </style>
    <style name="Widget.AppCompat.Button.Borderless.Colored">
    </style>
    <style name="Widget.AppCompat.Button.ButtonBar.AlertDialog">
    </style>
    <style name="Widget.AppCompat.Button.Colored">
    </style>
    <style name="Widget.AppCompat.Button.Small">
    </style>
    <style name="Widget.AppCompat.ButtonBar">
    </style>
    <style name="Widget.AppCompat.ButtonBar.AlertDialog">
    </style>
    <style name="Widget.AppCompat.CompoundButton.CheckBox">
    </style>
    <style name="Widget.AppCompat.CompoundButton.RadioButton">
    </style>
    <style name="Widget.AppCompat.CompoundButton.Switch">
    </style>
    <style name="Widget.AppCompat.DrawerArrowToggle">
        <item name="color">?attr/colorControlNormal</item>
    </style>
    <style name="Widget.AppCompat.DropDownItem.Spinner">
    </style>
    <style name="Widget.AppCompat.EditText">
    </style>
    <style name="Widget.AppCompat.ImageButton">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.Solid">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabBar">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabText">
    </style>
    <style name="Widget.AppCompat.Light.ActionBar.TabView">
    </style>
    <style name="Widget.AppCompat.Light.ActionButton">
    </style>
    <style name="Widget.AppCompat.Light.ActionButton.Overflow">
    </style>
    <style name="Widget.AppCompat.Light.PopupMenu">
    </style>
    <style name="Widget.AppCompat.Light.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.Light.SearchView">
    </style>
    <style name="Widget.AppCompat.Light.Spinner.DropDown.ActionBar">
    </style>
    <style name="Widget.AppCompat.ListMenuView">
    </style>
    <style name="Widget.AppCompat.ListPopupWindow">
    </style>
    <style name="Widget.AppCompat.ListView">
    </style>
    <style name="Widget.AppCompat.ListView.DropDown">
    </style>
    <style name="Widget.AppCompat.ListView.Menu">
    </style>
    <style name="Widget.AppCompat.PopupMenu">
    </style>
    <style name="Widget.AppCompat.PopupMenu.Overflow">
    </style>
    <style name="Widget.AppCompat.RatingBar">
    </style>
    <style name="Widget.AppCompat.RatingBar.Indicator">
    </style>
    <style name="Widget.AppCompat.RatingBar.Small">
    </style>
    <style name="Widget.AppCompat.SearchView">
    </style>
    <style name="Widget.AppCompat.SearchView.ActionBar">
    </style>
    <style name="Widget.AppCompat.SeekBar">
    </style>
    <style name="Widget.AppCompat.Spinner">
    </style>
    <style name="Widget.AppCompat.Spinner.DropDown">
    </style>
    <style name="Widget.AppCompat.Spinner.DropDown.ActionBar">
    </style>
    <style name="Widget.AppCompat.TextView">
    </style>
    <style name="Widget.AppCompat.TextView.SpinnerItem">
    </style>
    <style name="Widget.AppCompat.Toolbar">
    </style>
    <style name="Widget.AppCompat.Toolbar.Button.Navigation">
    </style>
    <style name="Widget.CarUi">
    </style>
    <style name="Widget.CarUi.AlertDialog">
    </style>
    <style name="Widget.CarUi.AlertDialog.HeaderContainer">
        <item name="android:gravity">right|center_horizontal|clip_horizontal</item>
        <item name="android:orientation">@null</item>
        <item name="android:paddingTop">18dp</item>
        <item name="android:paddingBottom">18dp</item>
    </style>
    <style name="Widget.CarUi.AlertDialog.Icon">
        <item name="android:scaleType">fitCenter</item>
        <item name="android:layout_marginStart">@dimen/car_ui_dialog_title_margin</item>
    </style>
    <style name="Widget.CarUi.AlertDialog.TitleContainer">
        <item name="android:orientation">vertical</item>
        <item name="android:layout_marginStart">@dimen/car_ui_dialog_title_margin</item>
        <item name="android:layout_marginEnd">@dimen/car_ui_dialog_title_margin</item>
    </style>
    <style name="Widget.CarUi.AppStyledView">
    </style>
    <style name="Widget.CarUi.AppStyledView.WindowAnimations">
        <item name="android:windowEnterAnimation">@anim/car_ui_app_styled_view_enter_anim</item>
        <item name="android:windowExitAnimation">@anim/car_ui_app_styled_view_exit_anim</item>
    </style>
    <style name="Widget.CarUi.AppStyledView.WindowAnimations.Enter">
        <item name="android:windowEnterAnimation">@anim/car_ui_app_styled_view_enter_anim</item>
        <item name="android:windowExitAnimation">@null</item>
    </style>
    <style name="Widget.CarUi.AppStyledView.WindowAnimations.Exit">
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@anim/car_ui_app_styled_view_exit_anim</item>
    </style>
    <style name="Widget.CarUi.AppStyledView.WindowAnimations.Intermediate">
        <item name="android:windowEnterAnimation">@null</item>
        <item name="android:windowExitAnimation">@null</item>
    </style>
    <style name="Widget.CarUi.Button">
    </style>
    <style name="Widget.CarUi.Button.Borderless.Colored">
    </style>
    <style name="name">0</style>
    <style name="Widget.CarUi.SeekbarPreference">
    </style>
    <style name="Widget.CarUi.SeekbarPreference.Seekbar">
        <item name="android:background">@null</item>
        <item name="android:focusable">false</item>
        <item name="android:clickable">false</item>
    </style>
    <style name="Widget.CarUi.Toolbar">
    </style>
    <style name="Widget.CarUi.Toolbar.BottomView">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:height">0.01dp</item>
    </style>
    <style name="Widget.CarUi.Toolbar.Container">
    </style>
    <style name="Widget.CarUi.Toolbar.Logo">
    </style>
    <style name="Widget.CarUi.Toolbar.LogoContainer">
        <item name="android:paddingEnd">@dimen/car_ui_toolbar_title_logo_padding</item>
    </style>
    <style name="Widget.CarUi.Toolbar.MenuItem">
    </style>
    <style name="Widget.CarUi.Toolbar.MenuItem.Container">
        <item name="android:divider">@drawable/car_ui_toolbar_menu_item_divider</item>
        <item name="android:showDividers">none|beginning|middle|end</item>
    </style>
    <style name="Widget.CarUi.Toolbar.MenuItem.IndividualContainer">
        <item name="android:layout_gravity">clip_horizontal</item>
        <item name="android:minWidth">@dimen/car_ui_touch_target_width</item>
        <item name="android:minHeight">@dimen/car_ui_touch_target_height</item>
    </style>
    <style name="Widget.CarUi.Toolbar.NavIcon">
        <item name="android:background">@drawable/car_ui_toolbar_menu_item_icon_ripple</item>
        <item name="android:tint">@color/car_ui_toolbar_nav_icon_color</item>
    </style>
    <style name="Widget.CarUi.Toolbar.NavIconContainer">
    </style>
    <style name="Widget.CarUi.Toolbar.ProgressBar">
    </style>
    <style name="Widget.CarUi.Toolbar.Search.CloseIcon">
        <item name="android:background">@drawable/car_ui_toolbar_menu_item_icon_ripple</item>
    </style>
    <style name="Widget.CarUi.Toolbar.Search.EditText">
        <item name="android:textColor">@color/car_ui_text_color_primary</item>
    </style>
    <style name="Widget.CarUi.Toolbar.Search.SearchIcon">
    </style>
    <style name="Widget.CarUi.Toolbar.SeparatorView">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:height">0.01dp</item>
    </style>
    <style name="Widget.CarUi.Toolbar.Subtitle">
        <item name="android:textAppearance">?android:attr/textAppearanceSmall</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="Widget.CarUi.Toolbar.Tab">
    </style>
    <style name="Widget.CarUi.Toolbar.Tab.Container">
        <item name="android:gravity">left|center_horizontal|clip_horizontal</item>
        <item name="android:orientation">vertical</item>
        <item name="android:background">?android:attr/selectableItemBackground</item>
        <item name="android:paddingStart">@dimen/car_ui_toolbar_tab_padding_x</item>
        <item name="android:paddingEnd">@dimen/car_ui_toolbar_tab_padding_x</item>
    </style>
    <style name="Widget.CarUi.Toolbar.Tab.Icon">
        <item name="android:scaleType">fitCenter</item>
        <item name="android:tint">@color/car_ui_toolbar_tab_item_selector</item>
        <item name="android:tintMode">src_in</item>
    </style>
    <style name="Widget.CarUi.Toolbar.Tab.Text">
        <item name="android:textAppearance">@style/TextAppearance.CarUi.Widget.Toolbar.Tab</item>
        <item name="android:gravity">left|center_horizontal|clip_horizontal</item>
        <item name="android:singleLine">true</item>
    </style>
    <style name="Widget.CarUi.Toolbar.TextButton">
        <item name="android:maxWidth">350dp</item>
        <item name="android:drawablePadding">10dp</item>
        <item name="?0x10104d6">@color/car_ui_toolbar_menu_item_icon_color</item>
    </style>
    <style name="Widget.CarUi.Toolbar.TextButton.WithIcon">
        <item name="android:textColor">@color/car_ui_toolbar_menu_item_icon_color</item>
    </style>
    <style name="Widget.CarUi.Toolbar.Title">
        <item name="android:textAppearance">@style/TextAppearance.CarUi.Widget.Toolbar.Title</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="Widget.Design.AppBarLayout">
        <item name="android:background">?attr/colorPrimary</item>
        <item name="android:stateListAnimator">@animator/design_appbar_state_list_animator</item>
        <item name="android:touchscreenBlocksFocus">true</item>
        <item name="?0x1010540">true</item>
    </style>
    <style name="Widget.Design.BottomNavigationView">
        <item name="android:minHeight">@dimen/design_bottom_navigation_height</item>
        <item name="activeIndicatorLabelPadding">1dp</item>
        <item name="compatShadowEnabled">true</item>
        <item name="elevation">@dimen/design_bottom_navigation_elevation</item>
        <item name="enforceMaterialTheme">false</item>
        <item name="enforceTextAppearance">false</item>
        <item name="itemActiveIndicatorStyle">@null</item>
        <item name="itemBackground">?attr/selectableItemBackgroundBorderless</item>
        <item name="itemHorizontalTranslationEnabled">true</item>
        <item name="itemIconGravity">@null</item>
        <item name="itemIconSize">@dimen/design_bottom_navigation_icon_size</item>
        <item name="itemPaddingBottom">0dp</item>
        <item name="itemPaddingTop">@dimen/design_bottom_navigation_margin</item>
        <item name="labelVisibilityMode">-1</item>
        <item name="measureBottomPaddingFromLabelBaseline">false</item>
    </style>
    <style name="Widget.Design.BottomSheet.Modal">
        <item name="android:background">?android:attr/colorBackground</item>
        <item name="android:elevation">@dimen/design_bottom_sheet_modal_elevation</item>
        <item name="backgroundTint">@null</item>
        <item name="behavior_hideable">true</item>
        <item name="behavior_peekHeight">-1</item>
        <item name="behavior_skipCollapsed">false</item>
        <item name="enforceMaterialTheme">false</item>
        <item name="shapeAppearance">@null</item>
        <item name="shapeAppearanceOverlay">@null</item>
    </style>
    <style name="Widget.Design.CollapsingToolbar">
        <item name="expandedTitleMargin">32dp</item>
        <item name="statusBarScrim">?attr/colorPrimaryDark</item>
    </style>
    <style name="Widget.Design.FloatingActionButton">
        <item name="android:background">@drawable/design_fab_background</item>
        <item name="android:focusable">true</item>
        <item name="android:clickable">true</item>
        <item name="backgroundTint">?attr/colorAccent</item>
        <item name="borderWidth">@dimen/design_fab_border_width</item>
        <item name="elevation">@dimen/design_fab_elevation</item>
        <item name="fabSize">-1</item>
        <item name="hideMotionSpec">@animator/design_fab_hide_motion_spec</item>
        <item name="hoveredFocusedTranslationZ">@dimen/design_fab_translation_z_hovered_focused</item>
        <item name="maxImageSize">@dimen/design_fab_image_size</item>
        <item name="pressedTranslationZ">@dimen/design_fab_translation_z_pressed</item>
        <item name="rippleColor">?attr/colorControlHighlight</item>
        <item name="showMotionSpec">@animator/design_fab_show_motion_spec</item>
    </style>
    <style name="Widget.Design.NavigationView">
        <item name="android:background">?android:attr/windowBackground</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:maxWidth">@dimen/design_navigation_max_width</item>
        <item name="drawerLayoutCornerSize">0dp</item>
        <item name="elevation">@dimen/design_navigation_elevation</item>
        <item name="itemHorizontalPadding">@dimen/design_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/design_navigation_item_icon_padding</item>
        <item name="subheaderInsetEnd">?attr/listPreferredItemPaddingEnd</item>
        <item name="subheaderInsetStart">?attr/listPreferredItemPaddingStart</item>
    </style>
    <style name="Widget.Design.ScrimInsetsFrameLayout">
        <item name="insetForeground">#0000</item>
    </style>
    <style name="Widget.Design.Snackbar">
        <item name="android:background">@drawable/design_snackbar_background</item>
        <item name="android:paddingLeft">@dimen/design_snackbar_padding_horizontal</item>
        <item name="android:paddingRight">@dimen/design_snackbar_padding_horizontal</item>
        <item name="android:maxWidth">@dimen/design_snackbar_max_width</item>
        <item name="android:minWidth">@dimen/design_snackbar_min_width</item>
        <item name="actionTextColorAlpha">@dimen/design_snackbar_action_text_color_alpha</item>
        <item name="animationMode">@null</item>
        <item name="elevation">@dimen/design_snackbar_elevation</item>
        <item name="maxActionInlineWidth">@dimen/design_snackbar_action_inline_max_width</item>
    </style>
    <style name="Widget.Design.TabLayout">
        <item name="tabGravity">@null</item>
        <item name="tabIndicatorFullWidth">true</item>
        <item name="tabMode">1</item>
    </style>
    <style name="Widget.Design.TextInputEditText">
        <item name="enforceMaterialTheme">false</item>
        <item name="enforceTextAppearance">false</item>
    </style>
    <style name="Widget.Design.TextInputLayout">
        <item name="boxBackgroundMode">@null</item>
        <item name="boxStrokeColor">@color/design_box_stroke_color</item>
        <item name="counterOverflowTextAppearance">@style/TextAppearance.Design.Counter.Overflow</item>
        <item name="counterOverflowTextColor">@null</item>
        <item name="counterTextAppearance">@style/TextAppearance.Design.Counter</item>
        <item name="counterTextColor">@null</item>
        <item name="endIconTint">@color/design_icon_tint</item>
        <item name="enforceMaterialTheme">false</item>
        <item name="enforceTextAppearance">false</item>
        <item name="errorIconDrawable">@null</item>
        <item name="errorTextAppearance">@style/TextAppearance.Design.Error</item>
        <item name="errorTextColor">@null</item>
        <item name="helperTextTextAppearance">@style/TextAppearance.Design.HelperText</item>
        <item name="helperTextTextColor">@null</item>
        <item name="hintTextAppearance">@style/TextAppearance.Design.Hint</item>
        <item name="hintTextColor">@null</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.Design.TextInputEditText</item>
        <item name="passwordToggleContentDescription">@string/password_toggle_content_description</item>
        <item name="passwordToggleDrawable">@drawable/design_password_eye</item>
        <item name="passwordToggleTint">@color/design_icon_tint</item>
        <item name="placeholderTextAppearance">@style/TextAppearance.Design.Placeholder</item>
        <item name="placeholderTextColor">@null</item>
        <item name="prefixTextAppearance">@style/TextAppearance.Design.Prefix</item>
        <item name="prefixTextColor">@null</item>
        <item name="shapeAppearance">@null</item>
        <item name="shapeAppearanceOverlay">@null</item>
        <item name="startIconTint">@color/design_icon_tint</item>
        <item name="suffixTextAppearance">@style/TextAppearance.Design.Suffix</item>
        <item name="suffixTextColor">@null</item>
    </style>
    <style name="Widget.GoogleMaterial.ActionBar.Solid">
        <item name="background">@drawable/google_appbar_surface_background_dark</item>
        <item name="backgroundSplit">@drawable/google_appbar_surface_background_dark</item>
        <item name="backgroundStacked">@drawable/google_appbar_surface_background_dark</item>
    </style>
    <style name="Widget.GoogleMaterial.ActionMode">
        <item name="subtitleTextStyle">@style/TextAppearance.GoogleMaterial.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.GoogleMaterial.ActionBar.Title</item>
    </style>
    <style name="Widget.GoogleMaterial.AppBarLayout">
    </style>
    <style name="Widget.GoogleMaterial.AutoCompleteTextView.FilledBox">
        <item name="android:textAppearance">?attr/textAppearanceBody1</item>
    </style>
    <style name="Widget.GoogleMaterial.AutoCompleteTextView.OutlinedBox">
        <item name="android:textAppearance">?attr/textAppearanceBody1</item>
    </style>
    <style name="Widget.GoogleMaterial.BottomAppBar">
        <item name="backgroundTint">@color/google_surface_bar_bg_color</item>
        <item name="elevation">@dimen/gm_sys_elevation_level2</item>
        <item name="fabAnimationMode">1</item>
        <item name="fabCradleMargin">@dimen/google_bottomappbar_fab_cradle_margin</item>
        <item name="fabCradleRoundedCornerRadius">@dimen/google_bottomappbar_fab_cradle_rounded_corner_radius</item>
        <item name="fabCradleVerticalOffset">@dimen/google_bottomappbar_fab_cradle_vertical_offset</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.BottomAppBar</item>
    </style>
    <style name="Widget.GoogleMaterial.BottomNavigationView">
        <item name="android:background">@null</item>
        <item name="backgroundTint">@color/google_surface_bar_bg_color</item>
        <item name="elevation">@dimen/google_bottomnavigation_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="itemIconTint">@color/google_bottom_nav_icon_tint</item>
        <item name="itemRippleColor">@color/google_bottom_nav_ripple_color_selector</item>
        <item name="itemTextAppearanceActive">?attr/textAppearanceSubhead2</item>
        <item name="itemTextAppearanceInactive">?attr/textAppearanceSubhead2</item>
        <item name="itemTextColor">@color/google_bottom_nav_text_color</item>
    </style>
    <style name="Widget.GoogleMaterial.Button">
        <item name="android:textAppearance">@style/TextAppearance.GoogleMaterial.MaterialButton</item>
        <item name="android:textColor">@color/google_btn_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/google_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/google_btn_padding_right</item>
        <item name="android:maxWidth">@null</item>
        <item name="android:stateListAnimator">@animator/google_btn_state_list_anim</item>
        <item name="backgroundTint">@color/google_btn_filled_btn_bg_color_selector</item>
        <item name="elevation">0dp</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">false</item>
        <item name="iconTint">@color/google_btn_text_color_selector</item>
        <item name="rippleColor">@color/google_btn_filled_btn_ripple_color</item>
    </style>
    <style name="Widget.GoogleMaterial.Button.StrokedButton">
        <item name="android:paddingLeft">@dimen/google_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/google_btn_padding_right</item>
        <item name="strokeColor">@color/google_btn_stroke_color_selector</item>
        <item name="strokeWidth">@dimen/mtrl_btn_stroke_size</item>
    </style>
    <style name="Widget.GoogleMaterial.Button.TextButton">
        <item name="android:textAppearance">@style/TextAppearance.GoogleMaterial.MaterialButton.Secondary</item>
        <item name="android:textColor">@color/google_btn_text_color_selector_secondary</item>
        <item name="backgroundTint">@color/google_btn_text_btn_bg_color_selector</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">false</item>
        <item name="iconTint">@color/google_btn_text_color_selector_secondary</item>
        <item name="rippleColor">@color/google_btn_ripple_color</item>
    </style>
    <style name="Widget.GoogleMaterial.Button.TextButton.Dialog">
        <item name="android:ellipsize">end</item>
        <item name="android:layout_marginLeft">@dimen/google_btn_dialog_btn_spacing</item>
        <item name="android:minWidth">@dimen/mtrl_btn_dialog_btn_min_width</item>
        <item name="android:lines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_marginStart">@dimen/google_btn_dialog_btn_spacing</item>
    </style>
    <style name="Widget.GoogleMaterial.Button.TextButton.Dialog.Flush">
        <item name="android:layout_marginLeft">0dp</item>
        <item name="android:layout_marginStart">0dp</item>
    </style>
    <style name="Widget.GoogleMaterial.Button.TextButton.Snackbar">
        <item name="android:textAppearance">@style/TextAppearance.GoogleMaterial.Subhead1</item>
        <item name="android:textColor">@color/google_btn_snackbar_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="rippleColor">@color/google_btn_snackbar_ripple_color</item>
    </style>
    <style name="Widget.GoogleMaterial.CardView">
        <item name="android:stateListAnimator">@animator/google_card_state_list_anim</item>
        <item name="cardCornerRadius">@dimen/google_card_corner_radius</item>
        <item name="cardElevation">@dimen/google_card_elevation</item>
        <item name="cardForegroundColor">@color/google_card_foreground</item>
        <item name="checkedIcon">@drawable/quantum_ic_check_box_vd_theme_24</item>
        <item name="checkedIconTint">?attr/colorPrimaryGoogle</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="rippleColor">@color/google_card_ripple</item>
        <item name="shapeAppearance">@null</item>
        <item name="strokeColor">@color/google_card_stroke</item>
        <item name="strokeWidth">@dimen/google_card_stroke_width</item>
    </style>
    <style name="Widget.GoogleMaterial.CardView.Elevated">
        <item name="android:stateListAnimator">@animator/google_card_elevated_state_list_anim</item>
        <item name="cardElevation">1dp</item>
        <item name="strokeWidth">0dp</item>
    </style>
    <style name="Widget.GoogleMaterial.CheckedTextView">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
    </style>
    <style name="Widget.GoogleMaterial.Chip.Assistive">
        <item name="android:textAppearance">@style/TextAppearance.GoogleMaterial.Chip.Assistive</item>
        <item name="android:textColor">@color/google_chip_assistive_text_color</item>
        <item name="android:checkable">false</item>
        <item name="checkedIconVisible">false</item>
        <item name="chipEndPadding">8dp</item>
        <item name="chipIconVisible">true</item>
        <item name="chipMinHeight">32dp</item>
        <item name="chipStartPadding">8dp</item>
        <item name="closeIconEndPadding">0dp</item>
        <item name="closeIconStartPadding">0dp</item>
        <item name="closeIconVisible">false</item>
        <item name="enforceTextAppearance">false</item>
        <item name="iconEndPadding">0dp</item>
        <item name="iconStartPadding">0dp</item>
        <item name="rippleColor">@color/google_chip_ripple_color</item>
        <item name="textEndPadding">8dp</item>
        <item name="textStartPadding">8dp</item>
    </style>
    <style name="Widget.GoogleMaterial.Chip.Input">
        <item name="android:textAppearance">@style/TextAppearance.GoogleMaterial.Chip.Input</item>
        <item name="android:checkable">true</item>
        <item name="chipEndPadding">4dp</item>
        <item name="chipIconSize">28dp</item>
        <item name="chipMinHeight">32dp</item>
        <item name="chipStartPadding">4dp</item>
        <item name="closeIconEndPadding">2dp</item>
        <item name="closeIconStartPadding">0dp</item>
        <item name="enforceTextAppearance">false</item>
        <item name="iconEndPadding">0dp</item>
        <item name="iconStartPadding">-2dp</item>
        <item name="textEndPadding">8dp</item>
        <item name="textStartPadding">8dp</item>
    </style>
    <style name="Widget.GoogleMaterial.ChipGroup">
        <item name="chipSpacingVertical">8dp</item>
    </style>
    <style name="Widget.GoogleMaterial.CircularProgressIndicator">
        <item name="indicatorColor">?attr/colorPrimaryVariantGoogle</item>
    </style>
    <style name="Widget.GoogleMaterial.CollapsingToolbar.Large">
        <item name="expandedTitleMarginBottom">28dp</item>
        <item name="expandedTitleMarginEnd">24dp</item>
        <item name="expandedTitleMarginStart">56dp</item>
        <item name="expandedTitleTextAppearance">?attr/textAppearanceHeadline3</item>
    </style>
    <style name="Widget.GoogleMaterial.CollapsingToolbar.Medium">
        <item name="expandedTitleMarginBottom">18dp</item>
        <item name="expandedTitleMarginEnd">16dp</item>
        <item name="expandedTitleMarginStart">16dp</item>
        <item name="expandedTitleTextAppearance">?attr/textAppearanceHeadline5</item>
    </style>
    <style name="Widget.GoogleMaterial.CompoundButton.CheckBox">
        <item name="android:background">@drawable/google_checkbox_ripple</item>
        <item name="buttonTint">@color/google_selection_control_button_tint</item>
        <item name="useMaterialThemeColors">false</item>
    </style>
    <style name="Widget.GoogleMaterial.CompoundButton.RadioButton">
        <item name="android:background">@drawable/google_radiobutton_ripple</item>
        <item name="buttonTint">@color/google_selection_control_button_tint</item>
        <item name="useMaterialThemeColors">false</item>
    </style>
    <style name="Widget.GoogleMaterial.CompoundButton.Switch">
    </style>
    <style name="Widget.GoogleMaterial.ExtendedFloatingActionButton">
        <item name="android:textAppearance">?attr/textAppearanceButton</item>
        <item name="android:textColor">@color/google_extended_fab_text_color_selector</item>
        <item name="backgroundTint">@color/google_extended_fab_bg_color_selector</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="iconTint">@color/google_extended_fab_text_color_selector</item>
        <item name="rippleColor">@color/google_fab_ripple_color</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.GoogleMaterial.FloatingActionButton</item>
    </style>
    <style name="Widget.GoogleMaterial.ExtendedFloatingActionButton.Icon">
        <item name="android:paddingLeft">@dimen/google_extended_fab_start_padding</item>
        <item name="android:paddingTop">@dimen/google_extended_fab_top_padding</item>
        <item name="android:paddingRight">@dimen/google_extended_fab_end_padding</item>
        <item name="android:paddingBottom">@dimen/google_extended_fab_bottom_padding</item>
        <item name="android:paddingStart">@dimen/google_extended_fab_start_padding</item>
        <item name="android:paddingEnd">@dimen/google_extended_fab_end_padding</item>
        <item name="iconPadding">@dimen/google_extended_fab_icon_text_spacing</item>
    </style>
    <style name="Widget.GoogleMaterial.ExtendedFloatingActionButton.Icon.Branded.Dark">
        <item name="icon">@drawable/google_fab_branded_icon_dark</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.Dark</item>
    </style>
    <style name="Widget.GoogleMaterial.ExtendedFloatingActionButton.Icon.Branded.Light">
        <item name="icon">@drawable/google_fab_branded_icon_light</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.Light</item>
    </style>
    <style name="Widget.GoogleMaterial.ExtendedFloatingActionButton.Icon.Primary">
        <item name="android:textColor">@color/google_extended_fab_primary_text_color_selector</item>
        <item name="backgroundTint">@color/google_extended_fab_primary_bg_color_selector</item>
        <item name="iconTint">@color/google_extended_fab_primary_text_color_selector</item>
        <item name="rippleColor">@color/google_extended_fab_primary_ripple_color</item>
    </style>
    <style name="Widget.GoogleMaterial.FloatingActionButton">
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="elevation">@dimen/google_fab_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="hoveredFocusedTranslationZ">@dimen/google_fab_translation_z_hovered_focused</item>
        <item name="pressedTranslationZ">@dimen/google_fab_translation_z_pressed</item>
        <item name="rippleColor">@color/google_fab_ripple_color</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.GoogleMaterial.FloatingActionButton</item>
        <item name="tint">@color/google_fab_tint</item>
    </style>
    <style name="Widget.GoogleMaterial.FloatingActionButton.Branded.Dark">
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.Dark</item>
        <item name="srcCompat">@drawable/google_fab_branded_icon_dark</item>
    </style>
    <style name="Widget.GoogleMaterial.FloatingActionButton.Branded.Light">
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.Light</item>
        <item name="srcCompat">@drawable/google_fab_branded_icon_light</item>
    </style>
    <style name="Widget.GoogleMaterial.FloatingActionButton.Primary">
        <item name="backgroundTint">@color/google_extended_fab_primary_bg_color_selector</item>
        <item name="rippleColor">@color/google_extended_fab_primary_ripple_color</item>
        <item name="tint">@color/google_extended_fab_primary_text_color_selector</item>
    </style>
    <style name="Widget.GoogleMaterial.FloatingActionButton.Secondary">
        <item name="elevation">@dimen/google_fab_secondary_elevation</item>
        <item name="hoveredFocusedTranslationZ">@dimen/google_fab_secondary_translation_z_hovered_focused</item>
        <item name="pressedTranslationZ">@dimen/google_fab_secondary_translation_z_pressed</item>
    </style>
    <style name="Widget.GoogleMaterial.Light.ActionBar.Solid">
        <item name="subtitleTextStyle">@style/TextAppearance.GoogleMaterial.ActionBar.Subtitle</item>
        <item name="titleTextStyle">@style/TextAppearance.GoogleMaterial.ActionBar.Title</item>
    </style>
    <style name="Widget.GoogleMaterial.LinearProgressIndicator">
        <item name="indicatorColor">?attr/colorPrimaryVariantGoogle</item>
        <item name="trackColor">?attr/colorSecondary</item>
        <item name="trackThickness">@dimen/google_linearprogressindicator_track_thickness_large</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar">
        <item name="android:windowFullscreen">false</item>
        <item name="dayInvalidStyle">@style/Widget.GoogleMaterial.MaterialCalendar.Day.Invalid</item>
        <item name="daySelectedStyle">@style/Widget.GoogleMaterial.MaterialCalendar.Day.Selected</item>
        <item name="dayStyle">@style/Widget.GoogleMaterial.MaterialCalendar.Day</item>
        <item name="dayTodayStyle">@style/Widget.GoogleMaterial.MaterialCalendar.Day.Today</item>
        <item name="rangeFillColor">?attr/colorSecondary</item>
        <item name="yearSelectedStyle">@style/Widget.GoogleMaterial.MaterialCalendar.Year.Selected</item>
        <item name="yearStyle">@style/Widget.GoogleMaterial.MaterialCalendar.Year</item>
        <item name="yearTodayStyle">@style/Widget.GoogleMaterial.MaterialCalendar.Year.Today</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.Day">
        <item name="android:height">@dimen/mtrl_calendar_day_height</item>
        <item name="android:width">@dimen/mtrl_calendar_day_width</item>
        <item name="android:insetLeft">@dimen/mtrl_calendar_day_horizontal_padding</item>
        <item name="android:insetRight">@dimen/mtrl_calendar_day_horizontal_padding</item>
        <item name="android:insetTop">@dimen/mtrl_calendar_day_vertical_padding</item>
        <item name="android:insetBottom">@dimen/mtrl_calendar_day_vertical_padding</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.Day.Invalid">
        <item name="itemStrokeWidth">0dp</item>
        <item name="itemTextColor">@color/google_picker_calendar_item_disabled_text</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.Day.Selected">
        <item name="itemFillColor">?attr/colorPrimaryGoogle</item>
        <item name="itemStrokeWidth">0dp</item>
        <item name="itemTextColor">?attr/colorOnPrimaryGoogle</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.Day.Today">
        <item name="itemStrokeColor">?attr/colorPrimaryGoogle</item>
        <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
        <item name="itemTextColor">?attr/colorPrimaryGoogle</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.DayOfWeekLabel">
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.DayTextView">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.Fullscreen">
        <item name="android:windowFullscreen">true</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.HeaderCancelButton">
        <item name="iconTint">?attr/colorOnSurfaceVariant</item>
        <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.HeaderDivider">
        <item name="android:background">?attr/colorHairline</item>
        <item name="android:visibility">@null</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.HeaderLayout">
        <item name="android:background">@android:color/transparent</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.HeaderLayout.Fullscreen">
        <item name="android:layout_height">@dimen/mtrl_calendar_header_height_fullscreen</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.HeaderSelection">
        <item name="android:textAppearance">?attr/textAppearanceHeadline2</item>
        <item name="android:textColor">?attr/colorOnBackground</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.HeaderSelection.Fullscreen">
        <item name="android:textAppearance">?attr/textAppearanceHeadline4</item>
        <item name="android:maxLines">1</item>
        <item name="autoSizeMaxTextSize">20sp</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.HeaderTitle">
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.HeaderToggleButton">
        <item name="android:tint">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.Item">
        <item name="itemFillColor">@android:color/transparent</item>
        <item name="itemShapeAppearance">?attr/shapeAppearanceSmallComponent</item>
        <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
        <item name="itemStrokeColor">@color/google_picker_calendar_item_stroke_color</item>
        <item name="itemStrokeWidth">1dp</item>
        <item name="itemTextColor">?attr/colorOnSurface</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.MonthNavigationButton">
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.MonthTextView">
        <item name="android:textColor">?attr/colorOnSurfaceVariant</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.Year">
        <item name="android:height">@dimen/mtrl_calendar_year_height</item>
        <item name="android:width">@dimen/mtrl_calendar_year_width</item>
        <item name="android:insetLeft">@dimen/mtrl_calendar_year_horizontal_padding</item>
        <item name="android:insetRight">@dimen/mtrl_calendar_year_horizontal_padding</item>
        <item name="android:insetTop">@dimen/mtrl_calendar_year_vertical_padding</item>
        <item name="android:insetBottom">@dimen/mtrl_calendar_year_vertical_padding</item>
        <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.Year.Selected">
        <item name="itemFillColor">?attr/colorPrimaryGoogle</item>
        <item name="itemStrokeColor">?attr/colorPrimaryGoogle</item>
        <item name="itemTextColor">?attr/colorOnPrimaryGoogle</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.Year.Today">
        <item name="itemStrokeColor">?attr/colorPrimaryGoogle</item>
        <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
        <item name="itemTextColor">?attr/colorPrimaryGoogle</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialCalendar.YearNavigationButton">
    </style>
    <style name="Widget.GoogleMaterial.MaterialTimePicker">
        <item name="keyboardIcon">@drawable/quantum_gm_ic_keyboard_black_24</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialTimePicker.Clock">
        <item name="clockFaceBackgroundColor">?attr/colorTimePickerSurface</item>
        <item name="clockHandColor">?attr/colorPrimaryGoogle</item>
        <item name="clockNumberTextColor">@color/google_timepicker_clock_text_color</item>
    </style>
    <style name="Widget.GoogleMaterial.MaterialTimePicker.ImageButton">
        <item name="iconTint">@color/google_picker_secondary_text_button_text_color</item>
        <item name="rippleColor">@color/google_picker_secondary_text_button_ripple_color</item>
    </style>
    <style name="Widget.GoogleMaterial.NavigationRailView">
        <item name="android:background">?attr/colorSurface</item>
        <item name="elevation">@dimen/google_navigation_rail_elevation</item>
        <item name="itemBackground">@null</item>
        <item name="itemIconTint">@color/google_navigation_rail_item_icon_tint</item>
        <item name="itemRippleColor">@color/google_navigation_rail_item_ripple_color</item>
        <item name="itemTextAppearanceActive">?attr/textAppearanceSubhead2</item>
        <item name="itemTextAppearanceInactive">?attr/textAppearanceSubhead2</item>
        <item name="itemTextColor">@color/google_navigation_rail_item_text_color</item>
    </style>
    <style name="Widget.GoogleMaterial.NavigationView">
        <item name="enforceMaterialTheme">true</item>
        <item name="itemBackground">@null</item>
        <item name="itemHorizontalPadding">@dimen/google_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/google_navigation_item_icon_padding</item>
        <item name="itemIconSize">@dimen/google_navigation_item_icon_size</item>
        <item name="itemIconTint">@color/google_navigation_item_icon_tint</item>
        <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.GoogleMaterial.NavigationView.Item</item>
        <item name="itemShapeFillColor">@color/google_navigation_item_background_color</item>
        <item name="itemShapeInsetBottom">@dimen/google_navigation_item_shape_vertical_margin</item>
        <item name="itemShapeInsetEnd">@dimen/google_navigation_item_shape_margin_end</item>
        <item name="itemShapeInsetStart">@dimen/google_navigation_item_shape_margin_start</item>
        <item name="itemShapeInsetTop">@dimen/google_navigation_item_shape_vertical_margin</item>
        <item name="itemTextAppearance">?attr/textAppearanceSubhead2</item>
        <item name="itemTextColor">@color/google_navigation_item_text_color</item>
    </style>
    <style name="Widget.GoogleMaterial.OpenSearch.ActionButton.Overflow">
        <item name="android:minWidth">@dimen/mtrl_min_touch_target_size</item>
    </style>
    <style name="Widget.GoogleMaterial.OpenSearch.Toolbar.Button.Navigation">
        <item name="android:minWidth">@dimen/mtrl_min_touch_target_size</item>
    </style>
    <style name="Widget.GoogleMaterial.OpenSearchBar">
        <item name="android:textAppearance">@style/TextAppearance.GoogleMaterial.OpenSearchBar</item>
        <item name="android:minHeight">@dimen/google_opensearchbar_height</item>
        <item name="android:paddingStart">@dimen/google_opensearchbar_padding_start</item>
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="defaultMarginsEnabled">true</item>
        <item name="defaultScrollFlagsEnabled">true</item>
        <item name="elevation">@dimen/google_opensearchbar_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="hideNavigationIcon">false</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.OpenSearch</item>
        <item name="maxButtonHeight">@dimen/google_opensearchbar_height</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.GoogleMaterial.OpenSearchBar</item>
    </style>
    <style name="Widget.GoogleMaterial.OpenSearchView">
        <item name="android:textAppearance">@style/TextAppearance.GoogleMaterial.OpenSearchView</item>
        <item name="android:elevation">@dimen/google_opensearchview_elevation</item>
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.OpenSearch</item>
    </style>
    <style name="Widget.GoogleMaterial.OpenSearchView.Prefix">
        <item name="android:textAppearance">@style/TextAppearance.GoogleMaterial.OpenSearchView.Prefix</item>
        <item name="android:paddingTop">8dp</item>
        <item name="android:paddingRight">16dp</item>
        <item name="android:paddingBottom">8dp</item>
        <item name="android:paddingEnd">16dp</item>
    </style>
    <style name="Widget.GoogleMaterial.OpenSearchView.Toolbar">
        <item name="android:paddingLeft">0dp</item>
        <item name="android:paddingRight">0dp</item>
        <item name="contentInsetStartWithNavigation">0dp</item>
        <item name="navigationIcon">@drawable/quantum_gm_ic_arrow_back_vd_theme_24</item>
        <item name="navigationIconTint">?attr/colorOnSurface</item>
    </style>
    <style name="Widget.GoogleMaterial.PopupMenu">
        <item name="android:popupElevation">@dimen/google_menu_elevation</item>
    </style>
    <style name="Widget.GoogleMaterial.PopupMenu.ContextMenu">
        <item name="android:popupElevation">@dimen/google_menu_elevation</item>
    </style>
    <style name="Widget.GoogleMaterial.PopupMenu.ListPopupWindow">
        <item name="android:popupElevation">@dimen/google_menu_elevation</item>
    </style>
    <style name="Widget.GoogleMaterial.PopupMenu.Overflow">
        <item name="android:popupElevation">@dimen/google_menu_elevation</item>
    </style>
    <style name="Widget.GoogleMaterial.ProductLockupView">
        <item name="lockupSizingMode">@null</item>
        <item name="logoColor">@null</item>
        <item name="productNameTextColor">@color/google_grey700</item>
    </style>
    <style name="Widget.GoogleMaterial.ProductLockupView.Dark">
        <item name="logoColor">2</item>
        <item name="productNameTextColor">@color/google_white</item>
    </style>
    <style name="Widget.GoogleMaterial.Slider">
        <item name="haloColor">@color/google_slider_halo_color</item>
        <item name="labelStyle">@style/Widget.GoogleMaterial.Tooltip</item>
        <item name="thumbColor">@color/google_slider_thumb_color</item>
        <item name="thumbElevation">@dimen/google_slider_thumb_elevation</item>
        <item name="tickColorActive">@color/google_slider_active_tick_marks_color</item>
        <item name="tickColorInactive">@color/google_slider_inactive_tick_marks_color</item>
        <item name="trackColorActive">@color/google_slider_active_track_color</item>
        <item name="trackColorInactive">@color/google_slider_inactive_track_color</item>
    </style>
    <style name="Widget.GoogleMaterial.Snackbar">
        <item name="actionTextColorAlpha">@dimen/design_snackbar_action_text_color_alpha</item>
        <item name="backgroundTint">?attr/colorSurfaceInverse</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.Snackbar</item>
    </style>
    <style name="Widget.GoogleMaterial.Snackbar.TextView">
        <item name="android:textColor">?attr/colorOnSurfaceInverse</item>
        <item name="android:alpha">1</item>
    </style>
    <style name="Widget.GoogleMaterial.TabLayout">
        <item name="android:background">?attr/colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">false</item>
        <item name="tabIconTint">@color/google_tabs_icon_color_selector</item>
        <item name="tabIndicator">@drawable/google_tabs_rounded_line_indicator</item>
        <item name="tabIndicatorAnimationMode">1</item>
        <item name="tabIndicatorColor">?attr/colorPrimaryGoogle</item>
        <item name="tabIndicatorFullWidth">false</item>
        <item name="tabRippleColor">@color/google_tabs_ripple_color_selector</item>
        <item name="tabTextAppearance">@style/TextAppearance.GoogleMaterial.Tab</item>
        <item name="tabTextColor">@color/google_tabs_icon_color_selector</item>
    </style>
    <style name="Widget.GoogleMaterial.TabLayout.Secondary">
        <item name="android:background">@drawable/google_tabs_secondary_tabs_background</item>
        <item name="tabIndicator">@drawable/google_tabs_line_indicator</item>
        <item name="tabIndicatorFullWidth">true</item>
        <item name="tabTextColor">@color/google_tabs_secondary_text_color_selector</item>
    </style>
    <style name="Widget.GoogleMaterial.TextInputEditText.FilledBox">
        <item name="android:textAppearance">?attr/textAppearanceBody1</item>
        <item name="android:textColor">@color/google_textfield_input_text_color</item>
    </style>
    <style name="Widget.GoogleMaterial.TextInputEditText.FilledBox.Dense">
        <item name="android:textAppearance">?attr/textAppearanceBody1</item>
        <item name="android:textColor">@color/google_textfield_input_text_color</item>
    </style>
    <style name="Widget.GoogleMaterial.TextInputEditText.OutlinedBox">
        <item name="android:textAppearance">?attr/textAppearanceBody1</item>
        <item name="android:textColor">@color/google_textfield_input_text_color</item>
    </style>
    <style name="Widget.GoogleMaterial.TextInputEditText.OutlinedBox.Dense">
        <item name="android:textAppearance">?attr/textAppearanceBody1</item>
        <item name="android:textColor">@color/google_textfield_input_text_color</item>
    </style>
    <style name="Widget.GoogleMaterial.TextInputLayout.FilledBox">
        <item name="android:textColorHint">@color/google_textfield_filled_label_color</item>
        <item name="boxBackgroundColor">?attr/colorTextFieldSurface</item>
        <item name="boxStrokeColor">@color/google_textfield_filled_stroke_color</item>
        <item name="boxStrokeErrorColor">@color/google_textfield_filled_error_color</item>
        <item name="counterOverflowTextColor">@color/google_textfield_filled_error_color</item>
        <item name="counterTextColor">@color/google_textfield_indicator_text_color</item>
        <item name="endIconTint">@color/google_textfield_filled_icon_tint</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="errorIconTint">@color/google_textfield_error_color</item>
        <item name="errorTextColor">@color/google_textfield_filled_error_color</item>
        <item name="helperTextTextColor">@color/google_textfield_indicator_text_color</item>
        <item name="hintTextColor">@color/google_textfield_filled_label_color</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.TextInputEditText.FilledBox</item>
        <item name="placeholderTextAppearance">?attr/textAppearanceBody1</item>
        <item name="placeholderTextColor">@color/google_textfield_filled_uneditable_text_color</item>
        <item name="prefixTextColor">@color/google_textfield_filled_uneditable_text_color</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.GoogleMaterial.TextField.Filled</item>
        <item name="startIconTint">@color/google_textfield_filled_icon_tint</item>
        <item name="suffixTextColor">@color/google_textfield_filled_uneditable_text_color</item>
    </style>
    <style name="Widget.GoogleMaterial.TextInputLayout.FilledBox.Dense">
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.TextInputEditText.FilledBox.Dense</item>
    </style>
    <style name="Widget.GoogleMaterial.TextInputLayout.FilledBox.ExposedDropdownMenu">
        <item name="endIconMode">3</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.AutoCompleteTextView.FilledBox</item>
    </style>
    <style name="Widget.GoogleMaterial.TextInputLayout.OutlinedBox">
        <item name="android:textColorHint">@color/google_textfield_outlined_label_color</item>
        <item name="boxStrokeColor">@color/google_textfield_outlined_stroke_color</item>
        <item name="boxStrokeErrorColor">@color/google_textfield_error_color</item>
        <item name="counterOverflowTextColor">@color/google_textfield_error_color</item>
        <item name="counterTextColor">@color/google_textfield_indicator_text_color</item>
        <item name="endIconTint">@color/google_textfield_outlined_icon_tint</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="errorIconTint">@color/google_textfield_error_color</item>
        <item name="errorTextColor">@color/google_textfield_error_color</item>
        <item name="helperTextTextColor">@color/google_textfield_indicator_text_color</item>
        <item name="hintTextColor">@color/google_textfield_outlined_label_color</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.TextInputEditText.OutlinedBox</item>
        <item name="placeholderTextAppearance">?attr/textAppearanceBody1</item>
        <item name="placeholderTextColor">@color/google_textfield_indicator_text_color</item>
        <item name="prefixTextColor">@color/google_textfield_indicator_text_color</item>
        <item name="shapeAppearanceOverlay">?attr/shapeAppearanceSmallComponent</item>
        <item name="startIconTint">@color/google_textfield_outlined_icon_tint</item>
        <item name="suffixTextColor">@color/google_textfield_indicator_text_color</item>
    </style>
    <style name="Widget.GoogleMaterial.TextInputLayout.OutlinedBox.Dense">
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="Widget.GoogleMaterial.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
        <item name="endIconMode">3</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.AutoCompleteTextView.OutlinedBox</item>
    </style>
    <style name="Widget.GoogleMaterial.TimePicker.Button">
        <item name="android:textAppearance">?attr/textAppearanceSubhead1</item>
        <item name="android:textColor">@color/google_btn_text_color_selector_secondary</item>
        <item name="backgroundTint">@color/google_btn_text_btn_bg_color_selector</item>
        <item name="iconTint">@color/google_btn_text_color_selector_secondary</item>
        <item name="rippleColor">@color/google_btn_ripple_color</item>
        <item name="strokeColor">?attr/colorHairline</item>
    </style>
    <style name="Widget.GoogleMaterial.TimePicker.Display">
        <item name="android:textAppearance">?attr/textAppearanceDisplay2</item>
        <item name="android:textColor">@color/google_timepicker_display_text_color</item>
        <item name="chipBackgroundColor">@color/google_timepicker_display_background_color</item>
        <item name="rippleColor">@color/google_timepicker_display_ripple_color</item>
    </style>
    <style name="Widget.GoogleMaterial.Toolbar">
        <item name="contentInsetStartWithNavigation">0dp</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.GoogleMaterial.Toolbar.Subtitle</item>
        <item name="titleTextAppearance">@style/TextAppearance.GoogleMaterial.Toolbar.Title</item>
    </style>
    <style name="Widget.GoogleMaterial.Toolbar.OnSurface">
        <item name="materialThemeOverlay">@style/ThemeOverlay.GoogleMaterial.Toolbar.Surface</item>
        <item name="navigationIconTint">?attr/colorOnSurface</item>
        <item name="subtitleTextAppearance">@style/TextAppearance.GoogleMaterial.Toolbar.Subtitle.Surface</item>
        <item name="titleTextAppearance">@style/TextAppearance.GoogleMaterial.Toolbar.Title.Surface</item>
    </style>
    <style name="Widget.GoogleMaterial.Toolbar.Surface">
        <item name="android:background">?attr/colorSurface</item>
    </style>
    <style name="Widget.GoogleMaterial.Tooltip">
        <item name="android:textAppearance">@style/TextAppearance.GoogleMaterial.Tooltip</item>
        <item name="android:padding">4dp</item>
        <item name="android:minWidth">28dp</item>
        <item name="android:minHeight">28dp</item>
        <item name="backgroundTint">?attr/colorPrimaryGoogle</item>
        <item name="shapeAppearance">@style/ShapeAppearance.GoogleMaterial.Tooltip</item>
    </style>
    <style name="Widget.Material3.CollapsingToolbar">
        <item name="expandedTitleTextAppearance">?attr/textAppearanceHeadlineSmall</item>
        <item name="scrimVisibleHeightTrigger">@dimen/m3_appbar_scrim_height_trigger</item>
    </style>
    <style name="Widget.MaterialComponents.ActionBar.Surface">
        <item name="background">?attr/colorSurface</item>
        <item name="elevation">0dp</item>
    </style>
    <style name="Widget.MaterialComponents.ActionMode">
        <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="subtitleTextStyle">?attr/textAppearanceSubtitle1</item>
        <item name="titleTextStyle">?attr/textAppearanceHeadline6</item>
    </style>
    <style name="Widget.MaterialComponents.AppBarLayout.Primary">
    </style>
    <style name="Widget.MaterialComponents.AppBarLayout.Surface">
        <item name="android:background">?attr/colorSurface</item>
    </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.FilledBox">
        <item name="android:paddingTop">24dp</item>
        <item name="android:paddingBottom">10dp</item>
    </style>
    <style name="Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox">
    </style>
    <style name="Widget.MaterialComponents.Badge">
        <item name="autoAdjustToWithinGrandparentBounds">false</item>
        <item name="backgroundColor">?attr/colorError</item>
        <item name="badgeGravity">8388661</item>
        <item name="badgeHeight">@dimen/mtrl_badge_size</item>
        <item name="badgeShapeAppearance">@style/ShapeAppearance.MaterialComponents.Badge</item>
        <item name="badgeTextAppearance">@style/TextAppearance.MaterialComponents.Badge</item>
        <item name="badgeWidePadding">@dimen/mtrl_badge_long_text_horizontal_padding</item>
        <item name="badgeWidth">@dimen/mtrl_badge_size</item>
        <item name="badgeWithTextHeight">@dimen/mtrl_badge_with_text_size</item>
        <item name="badgeWithTextShapeAppearance">@style/ShapeAppearance.MaterialComponents.Badge</item>
        <item name="badgeWithTextWidth">@dimen/mtrl_badge_with_text_size</item>
        <item name="maxNumber">@integer/m3_badge_max_number</item>
        <item name="offsetAlignmentMode">1</item>
    </style>
    <style name="Widget.MaterialComponents.BottomAppBar">
        <item name="android:minHeight">@dimen/mtrl_bottomappbar_height</item>
        <item name="addElevationShadow">true</item>
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="elevation">8dp</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="fabAlignmentMode">@null</item>
        <item name="fabAnchorMode">1</item>
        <item name="fabCradleMargin">@dimen/mtrl_bottomappbar_fab_cradle_margin</item>
        <item name="fabCradleRoundedCornerRadius">@dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius</item>
        <item name="fabCradleVerticalOffset">@dimen/mtrl_bottomappbar_fab_cradle_vertical_offset</item>
        <item name="maxButtonHeight">@dimen/mtrl_bottomappbar_height</item>
        <item name="menuAlignmentMode">@null</item>
        <item name="paddingBottomSystemWindowInsets">true</item>
        <item name="paddingLeftSystemWindowInsets">true</item>
        <item name="paddingRightSystemWindowInsets">true</item>
    </style>
    <style name="Widget.MaterialComponents.BottomNavigationView">
        <item name="android:background">?attr/colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="itemBackground">@null</item>
        <item name="itemHorizontalTranslationEnabled">false</item>
        <item name="itemIconTint">@color/mtrl_navigation_bar_item_tint</item>
        <item name="itemRippleColor">@color/mtrl_navigation_bar_ripple_color</item>
        <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
        <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
        <item name="itemTextColor">@color/mtrl_navigation_bar_item_tint</item>
    </style>
    <style name="Widget.MaterialComponents.BottomSheet">
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:background">@null</item>
        <item name="android:maxWidth">@dimen/material_bottom_sheet_max_width</item>
        <item name="android:elevation">@dimen/design_bottom_sheet_elevation</item>
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="shapeAppearance">?attr/shapeAppearanceLargeComponent</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet</item>
    </style>
    <style name="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="android:elevation">@dimen/design_bottom_sheet_modal_elevation</item>
    </style>
    <style name="Widget.MaterialComponents.Button">
        <item name="android:textAppearance">?attr/textAppearanceButton</item>
        <item name="android:textColor">@color/mtrl_btn_text_color_selector</item>
        <item name="android:background">@null</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingTop">@dimen/mtrl_btn_padding_top</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="android:paddingBottom">@dimen/mtrl_btn_padding_bottom</item>
        <item name="android:maxWidth">@dimen/mtrl_btn_max_width</item>
        <item name="android:insetLeft">0dp</item>
        <item name="android:insetRight">0dp</item>
        <item name="android:insetTop">@dimen/mtrl_btn_inset</item>
        <item name="android:insetBottom">@dimen/mtrl_btn_inset</item>
        <item name="android:stateListAnimator">@animator/mtrl_btn_state_list_anim</item>
        <item name="backgroundTint">@color/mtrl_btn_bg_color_selector</item>
        <item name="cornerRadius">@null</item>
        <item name="elevation">@dimen/mtrl_btn_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="iconPadding">@dimen/mtrl_btn_icon_padding</item>
        <item name="iconTint">@color/mtrl_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
    </style>
    <style name="Widget.MaterialComponents.Button.OutlinedButton">
        <item name="android:paddingLeft">@dimen/mtrl_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_padding_right</item>
        <item name="strokeColor">@color/mtrl_btn_stroke_color_selector</item>
        <item name="strokeWidth">@dimen/mtrl_btn_stroke_size</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton">
        <item name="android:textColor">@color/mtrl_text_btn_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
        <item name="android:paddingRight">@dimen/mtrl_btn_text_btn_padding_right</item>
        <item name="backgroundTint">@color/mtrl_btn_text_btn_bg_color_selector</item>
        <item name="iconPadding">@dimen/mtrl_btn_text_btn_icon_padding</item>
        <item name="iconTint">@color/mtrl_text_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_text_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog">
        <item name="android:ellipsize">end</item>
        <item name="android:layout_marginLeft">@dimen/mtrl_btn_text_btn_padding_left</item>
        <item name="android:minWidth">@dimen/mtrl_btn_dialog_btn_min_width</item>
        <item name="android:lines">1</item>
        <item name="android:singleLine">true</item>
        <item name="android:layout_marginStart">@dimen/mtrl_btn_text_btn_padding_left</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Dialog.Flush">
        <item name="android:layout_marginLeft">0dp</item>
        <item name="android:layout_marginStart">0dp</item>
    </style>
    <style name="Widget.MaterialComponents.Button.TextButton.Snackbar">
        <item name="android:textColor">?attr/colorPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.Button.UnelevatedButton">
        <item name="android:stateListAnimator">@animator/mtrl_btn_unelevated_state_list_anim</item>
        <item name="elevation">0dp</item>
    </style>
    <style name="Widget.MaterialComponents.CardView">
        <item name="android:stateListAnimator">@animator/mtrl_card_state_list_anim</item>
        <item name="cardBackgroundColor">?attr/colorSurface</item>
        <item name="cardCornerRadius">@null</item>
        <item name="cardElevation">@dimen/mtrl_card_elevation</item>
        <item name="cardForegroundColor">@color/mtrl_card_view_foreground</item>
        <item name="checkedIcon">@drawable/ic_mtrl_checked_circle</item>
        <item name="checkedIconMargin">@dimen/mtrl_card_checked_icon_margin</item>
        <item name="checkedIconSize">@dimen/mtrl_card_checked_icon_size</item>
        <item name="checkedIconTint">?attr/colorPrimary</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="rippleColor">@color/mtrl_card_view_ripple</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
    </style>
    <style name="Widget.MaterialComponents.CheckedTextView">
        <item name="android:textAppearance">?attr/textAppearanceBody1</item>
        <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Action">
        <item name="closeIconVisible">false</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Choice">
        <item name="android:textColor">@color/mtrl_choice_chip_text_color</item>
        <item name="android:checkable">true</item>
        <item name="checkedIcon">@drawable/ic_mtrl_chip_checked_black</item>
        <item name="checkedIconVisible">false</item>
        <item name="chipBackgroundColor">@color/mtrl_choice_chip_background_color</item>
        <item name="chipIconVisible">false</item>
        <item name="closeIconVisible">false</item>
        <item name="rippleColor">@color/mtrl_choice_chip_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.Chip.Entry">
        <item name="android:checkable">true</item>
    </style>
    <style name="Widget.MaterialComponents.ChipGroup">
        <item name="chipSpacingHorizontal">8dp</item>
        <item name="singleLine">false</item>
        <item name="singleSelection">false</item>
    </style>
    <style name="Widget.MaterialComponents.CircularProgressIndicator">
        <item name="indeterminateTrackVisible">true</item>
        <item name="indicatorDirectionCircular">@null</item>
        <item name="indicatorInset">@dimen/mtrl_progress_circular_inset_medium</item>
        <item name="indicatorSize">@dimen/mtrl_progress_circular_size_medium</item>
        <item name="trackColor">@android:color/transparent</item>
        <item name="trackThickness">@dimen/mtrl_progress_circular_track_thickness_medium</item>
    </style>
    <style name="Widget.MaterialComponents.CollapsingToolbar">
    </style>
    <style name="Widget.MaterialComponents.CompoundButton.CheckBox">
        <item name="android:minWidth">?attr/minTouchTargetSize</item>
        <item name="android:minHeight">?attr/minTouchTargetSize</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="useMaterialThemeColors">true</item>
    </style>
    <style name="Widget.MaterialComponents.CompoundButton.RadioButton">
        <item name="android:minWidth">?attr/minTouchTargetSize</item>
        <item name="android:minHeight">?attr/minTouchTargetSize</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="useMaterialThemeColors">true</item>
    </style>
    <style name="Widget.MaterialComponents.CompoundButton.Switch">
        <item name="android:minWidth">?attr/minTouchTargetSize</item>
        <item name="android:minHeight">?attr/minTouchTargetSize</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="useMaterialThemeColors">true</item>
    </style>
    <style name="Widget.MaterialComponents.ExtendedFloatingActionButton">
        <item name="android:textColor">@color/mtrl_fab_icon_text_color_selector</item>
        <item name="android:paddingLeft">@dimen/mtrl_extended_fab_start_padding</item>
        <item name="android:paddingTop">@dimen/mtrl_extended_fab_top_padding</item>
        <item name="android:paddingRight">@dimen/mtrl_extended_fab_end_padding</item>
        <item name="android:paddingBottom">@dimen/mtrl_extended_fab_bottom_padding</item>
        <item name="android:minWidth">@dimen/mtrl_extended_fab_min_width</item>
        <item name="android:minHeight">@dimen/mtrl_extended_fab_min_height</item>
        <item name="android:maxLines">1</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:paddingStart">@dimen/mtrl_extended_fab_start_padding</item>
        <item name="android:paddingEnd">@dimen/mtrl_extended_fab_end_padding</item>
        <item name="android:stateListAnimator">@animator/mtrl_extended_fab_state_list_animator</item>
        <item name="backgroundTint">@color/mtrl_fab_bg_color_selector</item>
        <item name="collapsedSize">@dimen/design_fab_size_normal</item>
        <item name="elevation">@dimen/mtrl_extended_fab_elevation</item>
        <item name="extendStrategy">1</item>
        <item name="iconPadding">@dimen/mtrl_extended_fab_icon_text_spacing</item>
        <item name="iconSize">@dimen/mtrl_extended_fab_icon_size</item>
        <item name="iconTint">@color/mtrl_fab_icon_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_fab_ripple_color</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton</item>
    </style>
    <style name="Widget.MaterialComponents.ExtendedFloatingActionButton.Icon">
        <item name="android:gravity">right|center_horizontal|clip_horizontal</item>
        <item name="android:paddingLeft">@dimen/mtrl_extended_fab_start_padding_icon</item>
        <item name="android:paddingRight">@dimen/mtrl_extended_fab_end_padding_icon</item>
        <item name="android:paddingStart">@dimen/mtrl_extended_fab_start_padding_icon</item>
        <item name="android:paddingEnd">@dimen/mtrl_extended_fab_end_padding_icon</item>
    </style>
    <style name="Widget.MaterialComponents.FloatingActionButton">
        <item name="android:background">@null</item>
        <item name="backgroundTint">@color/mtrl_fab_bg_color_selector</item>
        <item name="elevation">@dimen/mtrl_fab_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="ensureMinTouchTargetSize">true</item>
        <item name="hideMotionSpec">@animator/mtrl_fab_hide_motion_spec</item>
        <item name="hoveredFocusedTranslationZ">@dimen/mtrl_fab_translation_z_hovered_focused</item>
        <item name="pressedTranslationZ">@dimen/mtrl_fab_translation_z_pressed</item>
        <item name="rippleColor">@color/mtrl_fab_ripple_color</item>
        <item name="shapeAppearance">?attr/shapeAppearanceSmallComponent</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton</item>
        <item name="showMotionSpec">@animator/mtrl_fab_show_motion_spec</item>
        <item name="tint">@color/mtrl_fab_icon_text_color_selector</item>
    </style>
    <style name="Widget.MaterialComponents.Light.ActionBar.Solid">
        <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="subtitleTextStyle">?attr/textAppearanceSubtitle1</item>
        <item name="titleTextStyle">?attr/textAppearanceHeadline6</item>
    </style>
    <style name="Widget.MaterialComponents.LinearProgressIndicator">
        <item name="indeterminateAnimationType">1</item>
        <item name="indicatorDirectionLinear">2</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialButtonToggleGroup">
        <item name="singleSelection">false</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar">
        <item name="android:windowFullscreen">false</item>
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="dayInvalidStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid</item>
        <item name="daySelectedStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Selected</item>
        <item name="dayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day</item>
        <item name="dayTodayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Day.Today</item>
        <item name="rangeFillColor">@color/mtrl_calendar_selected_range</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
        <item name="yearSelectedStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year.Selected</item>
        <item name="yearStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year</item>
        <item name="yearTodayStyle">@style/Widget.MaterialComponents.MaterialCalendar.Year.Today</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day">
        <item name="android:height">@dimen/mtrl_calendar_day_height</item>
        <item name="android:width">@dimen/mtrl_calendar_day_width</item>
        <item name="android:insetLeft">@dimen/mtrl_calendar_day_horizontal_padding</item>
        <item name="android:insetRight">@dimen/mtrl_calendar_day_horizontal_padding</item>
        <item name="android:insetTop">@dimen/mtrl_calendar_day_vertical_padding</item>
        <item name="android:insetBottom">@dimen/mtrl_calendar_day_vertical_padding</item>
        <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Invalid">
        <item name="itemStrokeWidth">0dp</item>
        <item name="itemTextColor">@color/material_on_surface_disabled</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Selected">
        <item name="itemFillColor">?attr/colorPrimary</item>
        <item name="itemStrokeWidth">0dp</item>
        <item name="itemTextColor">?attr/colorOnPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Day.Today">
        <item name="itemStrokeColor">@color/material_on_surface_emphasis_high_type</item>
        <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
        <item name="android:gravity">left|center_horizontal|clip_horizontal</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.DayTextView">
        <item name="android:textAppearance">?attr/textAppearanceCaption</item>
        <item name="android:gravity">left|center_horizontal|clip_horizontal</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Fullscreen">
        <item name="android:windowFullscreen">true</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton">
        <item name="iconTint">?attr/colorOnPrimary</item>
        <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton">
        <item name="android:textColor">@color/mtrl_on_primary_text_btn_text_color_selector</item>
        <item name="rippleColor">@color/mtrl_btn_ripple_color</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderDivider">
        <item name="android:background">?attr/colorOnPrimary</item>
        <item name="android:visibility">gone</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout">
        <item name="android:background">?attr/colorPrimary</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">@dimen/mtrl_calendar_header_height</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen">
        <item name="android:layout_height">@dimen/mtrl_calendar_header_height_fullscreen</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection">
        <item name="android:textAppearance">?attr/textAppearanceHeadline4</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="android:ellipsize">end</item>
        <item name="android:layout_gravity">center_horizontal</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:maxLines">@integer/mtrl_calendar_selection_text_lines</item>
        <item name="autoSizeMaxTextSize">34sp</item>
        <item name="autoSizeMinTextSize">2sp</item>
        <item name="autoSizeTextType">1</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen">
        <item name="android:textAppearance">?attr/textAppearanceHeadline6</item>
        <item name="android:maxLines">1</item>
        <item name="autoSizeMaxTextSize">20sp</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderTitle">
        <item name="android:textAppearance">?attr/textAppearanceOverline</item>
        <item name="android:textColor">?attr/colorOnPrimary</item>
        <item name="android:ellipsize">end</item>
        <item name="android:maxLines">1</item>
        <item name="autoSizeMaxTextSize">10sp</item>
        <item name="autoSizeMinTextSize">2sp</item>
        <item name="autoSizeTextType">1</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton">
        <item name="android:layout_gravity">top|left|right|center_vertical|center_horizontal|fill_horizontal|center</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Item">
        <item name="itemFillColor">@android:color/transparent</item>
        <item name="itemShapeAppearance">?attr/shapeAppearanceSmallComponent</item>
        <item name="itemStrokeColor">@color/mtrl_calendar_item_stroke_color</item>
        <item name="itemStrokeWidth">1dp</item>
        <item name="itemTextColor">@color/material_on_surface_emphasis_high_type</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton">
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.MonthTextView">
        <item name="android:textAppearance">?attr/textAppearanceSubtitle2</item>
        <item name="android:textColor">@color/material_on_surface_emphasis_medium</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year">
        <item name="android:height">@dimen/mtrl_calendar_year_height</item>
        <item name="android:width">@dimen/mtrl_calendar_year_width</item>
        <item name="android:insetLeft">@dimen/mtrl_calendar_year_horizontal_padding</item>
        <item name="android:insetRight">@dimen/mtrl_calendar_year_horizontal_padding</item>
        <item name="android:insetTop">@dimen/mtrl_calendar_year_vertical_padding</item>
        <item name="android:insetBottom">@dimen/mtrl_calendar_year_vertical_padding</item>
        <item name="itemShapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year.Selected">
        <item name="itemFillColor">?attr/colorPrimary</item>
        <item name="itemStrokeColor">?attr/colorOnPrimary</item>
        <item name="itemStrokeWidth">0dp</item>
        <item name="itemTextColor">?attr/colorOnPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.Year.Today">
        <item name="itemStrokeColor">@color/material_on_surface_emphasis_high_type</item>
        <item name="itemStrokeWidth">@dimen/mtrl_calendar_day_today_stroke</item>
    </style>
    <style name="Widget.MaterialComponents.MaterialCalendar.YearNavigationButton">
    </style>
    <style name="Widget.MaterialComponents.NavigationRailView">
        <item name="android:background">?attr/colorSurface</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:minWidth">@dimen/mtrl_navigation_rail_default_width</item>
        <item name="contentMarginTop">@dimen/mtrl_navigation_rail_margin</item>
        <item name="elevation">@dimen/mtrl_navigation_rail_elevation</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="headerMarginBottom">@dimen/mtrl_navigation_rail_margin</item>
        <item name="itemActiveIndicatorStyle">@null</item>
        <item name="itemBackground">@null</item>
        <item name="itemIconGravity">@null</item>
        <item name="itemIconSize">@dimen/mtrl_navigation_rail_icon_size</item>
        <item name="itemIconTint">@color/mtrl_navigation_bar_item_tint</item>
        <item name="itemMinHeight">@null</item>
        <item name="itemPaddingBottom">@dimen/mtrl_navigation_rail_text_bottom_margin</item>
        <item name="itemPaddingTop">@dimen/mtrl_navigation_rail_icon_margin</item>
        <item name="itemRippleColor">@color/mtrl_navigation_bar_ripple_color</item>
        <item name="itemTextAppearanceActive">?attr/textAppearanceCaption</item>
        <item name="itemTextAppearanceInactive">?attr/textAppearanceCaption</item>
        <item name="itemTextColor">@color/mtrl_navigation_bar_item_tint</item>
        <item name="labelVisibilityMode">-1</item>
        <item name="measureBottomPaddingFromLabelBaseline">true</item>
        <item name="menuGravity">49</item>
    </style>
    <style name="Widget.MaterialComponents.NavigationView">
        <item name="android:background">?attr/colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="itemBackground">@null</item>
        <item name="itemHorizontalPadding">@dimen/mtrl_navigation_item_horizontal_padding</item>
        <item name="itemIconPadding">@dimen/mtrl_navigation_item_icon_padding</item>
        <item name="itemIconSize">@dimen/mtrl_navigation_item_icon_size</item>
        <item name="itemIconTint">@color/mtrl_navigation_item_icon_tint</item>
        <item name="itemShapeAppearance">?attr/shapeAppearanceSmallComponent</item>
        <item name="itemShapeFillColor">@color/mtrl_navigation_item_background_color</item>
        <item name="itemShapeInsetBottom">@dimen/mtrl_navigation_item_shape_vertical_margin</item>
        <item name="itemShapeInsetEnd">@dimen/mtrl_navigation_item_shape_horizontal_margin</item>
        <item name="itemShapeInsetStart">@dimen/mtrl_navigation_item_shape_horizontal_margin</item>
        <item name="itemShapeInsetTop">@dimen/mtrl_navigation_item_shape_vertical_margin</item>
        <item name="itemTextAppearance">?attr/textAppearanceSubtitle2</item>
        <item name="itemTextColor">@color/mtrl_navigation_item_text_color</item>
    </style>
    <style name="Widget.MaterialComponents.PopupMenu">
        <item name="android:popupBackground">?attr/popupMenuBackground</item>
        <item name="android:popupElevation">8dp</item>
    </style>
    <style name="Widget.MaterialComponents.PopupMenu.ContextMenu">
        <item name="android:popupBackground">?attr/popupMenuBackground</item>
        <item name="android:popupElevation">8dp</item>
    </style>
    <style name="Widget.MaterialComponents.PopupMenu.ListPopupWindow">
        <item name="android:popupBackground">?attr/popupMenuBackground</item>
        <item name="android:popupElevation">8dp</item>
    </style>
    <style name="Widget.MaterialComponents.PopupMenu.Overflow">
        <item name="android:popupBackground">?attr/popupMenuBackground</item>
        <item name="android:popupElevation">8dp</item>
    </style>
    <style name="Widget.MaterialComponents.ProgressIndicator">
        <item name="android:indeterminateOnly">false</item>
        <item name="indicatorColor">?attr/colorPrimary</item>
        <item name="trackThickness">@dimen/mtrl_progress_track_thickness</item>
    </style>
    <style name="Widget.MaterialComponents.Slider">
        <item name="android:background">?attr/controlBackground</item>
    </style>
    <style name="Widget.MaterialComponents.Snackbar">
        <item name="android:background">@null</item>
        <item name="android:layout_margin">@dimen/mtrl_snackbar_margin</item>
        <item name="animationMode">1</item>
    </style>
    <style name="Widget.MaterialComponents.Snackbar.TextView">
        <item name="android:textAppearance">?attr/textAppearanceBody2</item>
        <item name="android:textColor">?attr/colorSurface</item>
        <item name="android:ellipsize">end</item>
        <item name="android:paddingTop">@dimen/design_snackbar_padding_vertical</item>
        <item name="android:paddingBottom">@dimen/design_snackbar_padding_vertical</item>
        <item name="android:layout_marginLeft">@dimen/mtrl_snackbar_message_margin_horizontal</item>
        <item name="android:layout_marginRight">@dimen/mtrl_snackbar_message_margin_horizontal</item>
        <item name="android:maxLines">@integer/design_snackbar_text_max_lines</item>
        <item name="android:alpha">@dimen/material_emphasis_high_type</item>
        <item name="android:textAlignment">viewStart</item>
    </style>
    <style name="Widget.MaterialComponents.TabLayout">
        <item name="android:background">?attr/colorSurface</item>
        <item name="enforceMaterialTheme">true</item>
        <item name="enforceTextAppearance">true</item>
        <item name="tabIconTint">@color/mtrl_tabs_icon_color_selector</item>
        <item name="tabIndicatorAnimationDuration">@integer/mtrl_tab_indicator_anim_duration_ms</item>
        <item name="tabIndicatorColor">?attr/colorPrimary</item>
        <item name="tabRippleColor">@color/mtrl_tabs_ripple_color</item>
        <item name="tabTextAppearance">?attr/textAppearanceButton</item>
        <item name="tabTextColor">@color/mtrl_tabs_icon_color_selector</item>
        <item name="tabUnboundedRipple">true</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox">
        <item name="android:paddingTop">24dp</item>
        <item name="android:paddingBottom">10dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.FilledBox.Dense">
        <item name="android:paddingTop">24dp</item>
        <item name="android:paddingBottom">8dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox">
    </style>
    <style name="Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense">
        <item name="android:paddingTop">13dp</item>
        <item name="android:paddingBottom">13dp</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox">
        <item name="boxBackgroundColor">@color/mtrl_filled_background_color</item>
        <item name="boxBackgroundMode">1</item>
        <item name="boxCollapsedPaddingTop">10dp</item>
        <item name="boxStrokeColor">@color/mtrl_filled_stroke_color</item>
        <item name="endIconTint">@color/mtrl_filled_icon_tint</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox</item>
        <item name="shapeAppearanceOverlay">@style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox</item>
        <item name="startIconTint">@color/mtrl_filled_icon_tint</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.Dense">
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu">
        <item name="endIconMode">3</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox">
        <item name="boxCollapsedPaddingTop">0dp</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense">
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense</item>
    </style>
    <style name="Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu">
        <item name="endIconMode">3</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox</item>
    </style>
    <style name="Widget.MaterialComponents.TextView">
    </style>
    <style name="Widget.MaterialComponents.TimePicker">
        <item name="backgroundTint">?attr/colorSurface</item>
        <item name="clockIcon">@drawable/ic_clock_black_24dp</item>
        <item name="keyboardIcon">@drawable/ic_keyboard_black_24dp</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Button">
        <item name="android:textAppearance">?attr/textAppearanceSubtitle2</item>
        <item name="android:textSize">16sp</item>
        <item name="android:padding">0dp</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="android:textAlignment">center</item>
        <item name="backgroundTint">@color/material_timepicker_button_background</item>
        <item name="shapeAppearanceOverlay">?attr/shapeAppearanceMediumComponent</item>
        <item name="strokeColor">@color/material_timepicker_button_stroke</item>
        <item name="strokeWidth">@dimen/mtrl_btn_stroke_size</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Clock">
        <item name="clockFaceBackgroundColor">@color/material_timepicker_clockface</item>
        <item name="clockHandColor">?attr/colorPrimary</item>
        <item name="clockNumberTextColor">@color/material_timepicker_clock_text_color</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Display">
        <item name="android:textAppearance">?attr/textAppearanceHeadline3</item>
        <item name="android:textSize">56dp</item>
        <item name="android:textAlignment">center</item>
        <item name="ensureMinTouchTargetSize">false</item>
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TimePicker.Display</item>
        <item name="shapeAppearanceOverlay">?attr/shapeAppearanceMediumComponent</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Display.Divider">
        <item name="android:textAppearance">?attr/textAppearanceHeadline3</item>
        <item name="android:textSize">56dp</item>
        <item name="android:textColor">?attr/colorOnSurface</item>
        <item name="android:text">@string/material_clock_display_divider</item>
        <item name="android:maxEms">1</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:lineSpacingExtra">0dp</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Display.HelperText">
        <item name="android:textAppearance">?attr/textAppearanceCaption</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Display.TextInputEditText">
        <item name="android:textAppearance">?attr/textAppearanceHeadline3</item>
        <item name="android:textSize">56dp</item>
        <item name="android:gravity">left|center_horizontal|clip_horizontal</item>
        <item name="android:paddingTop">0dp</item>
        <item name="android:paddingBottom">0dp</item>
        <item name="android:minEms">2</item>
        <item name="android:includeFontPadding">false</item>
        <item name="android:maxLength">2</item>
        <item name="android:inputType">number</item>
        <item name="android:textAlignment">center</item>
        <item name="android:paddingStart">0dp</item>
        <item name="android:paddingEnd">0dp</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.Display.TextInputLayout">
        <item name="materialThemeOverlay">@style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText</item>
        <item name="shapeAppearance">?attr/shapeAppearanceMediumComponent</item>
        <item name="shapeAppearanceOverlay">@null</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.ImageButton">
        <item name="android:minWidth">?attr/minTouchTargetSize</item>
        <item name="android:minHeight">?attr/minTouchTargetSize</item>
        <item name="android:insetTop">0dp</item>
        <item name="android:insetBottom">0dp</item>
        <item name="iconGravity">2</item>
        <item name="iconPadding">0dp</item>
        <item name="iconTint">@color/material_timepicker_modebutton_tint</item>
        <item name="rippleColor">@color/mtrl_on_surface_ripple_color</item>
        <item name="shapeAppearance">@style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance</item>
    </style>
    <style name="Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance">
        <item name="cornerFamily">@null</item>
        <item name="cornerSize">50%</item>
    </style>
    <style name="Widget.MaterialComponents.Toolbar">
        <item name="android:minHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="maxButtonHeight">@dimen/mtrl_toolbar_default_height</item>
        <item name="subtitleTextAppearance">?attr/textAppearanceSubtitle1</item>
        <item name="subtitleTextColor">?android:attr/textColorSecondary</item>
        <item name="titleTextAppearance">?attr/textAppearanceHeadline6</item>
        <item name="titleTextColor">?android:attr/textColorPrimary</item>
    </style>
    <style name="Widget.MaterialComponents.Tooltip">
        <item name="android:textAppearance">@style/TextAppearance.MaterialComponents.Tooltip</item>
        <item name="android:padding">@dimen/mtrl_tooltip_padding</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:minWidth">@dimen/mtrl_tooltip_minWidth</item>
        <item name="android:minHeight">@dimen/mtrl_tooltip_minHeight</item>
        <item name="shapeAppearance">@style/ShapeAppearance.MaterialComponents.Tooltip</item>
    </style>
    <style name="Widget.Support.CoordinatorLayout">
        <item name="statusBarBackground">#000000</item>
    </style>
    <style name="WsPageIndicatorViewStyle">
        <item name="wsPageIndicatorDotColor">?android:attr/colorForeground</item>
        <item name="wsPageIndicatorDotColorSelected">?android:attr/colorForeground</item>
        <item name="wsPageIndicatorDotFadeInDuration">100</item>
        <item name="wsPageIndicatorDotFadeOutDelay">1000</item>
        <item name="wsPageIndicatorDotFadeOutDuration">250</item>
        <item name="wsPageIndicatorDotFadeWhenIdle">true</item>
        <item name="wsPageIndicatorDotRadius">2.1dp</item>
        <item name="wsPageIndicatorDotRadiusSelected">3.1dp</item>
        <item name="wsPageIndicatorDotShadowColor">#66000000</item>
        <item name="wsPageIndicatorDotShadowDx">0.5dp</item>
        <item name="wsPageIndicatorDotShadowDy">0.5dp</item>
        <item name="wsPageIndicatorDotShadowRadius">1dp</item>
        <item name="wsPageIndicatorDotSpacing">7.8dp</item>
    </style>
</resources>
