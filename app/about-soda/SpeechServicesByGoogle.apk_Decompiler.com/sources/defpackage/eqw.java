package defpackage;

/* renamed from: eqw  reason: default package */
/* compiled from: PG */
public final class eqw {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/playback/impl/adapter/LoopbackAudioProvider");
    public final ehg b;
    private final dyt c;
    private final grh d;
    private final Object e = new Object();
    private final dou f;

    public eqw(ehg ehg, dyt dyt, dou dou, grh grh) {
        jnu.e(dyt, "audioParams");
        jnu.e(grh, "playbackServiceStubOptional");
        this.b = ehg;
        this.c = dyt;
        this.f = dou;
        this.d = grh;
    }

    public final void a() {
        synchronized (this.e) {
            ((hby) a.f().h(hdg.a, "ALT.LoopbackProvider").j("com/google/android/libraries/search/audio/playback/impl/adapter/LoopbackAudioProvider", "stopListening", 102, "LoopbackAudioProvider.kt")).u("#audio# Stop called for %s.", fbi.s(this.b));
        }
    }

    public final void b() {
        synchronized (this.e) {
            dyt dyt = this.c;
            if (!dyt.j) {
                return;
            }
            if (!dyt.k) {
                ((hby) a.g().h(hdg.a, "ALT.LoopbackProvider").j("com/google/android/libraries/search/audio/playback/impl/adapter/LoopbackAudioProvider", "startListening", 43, "LoopbackAudioProvider.kt")).r("#audio# Only push mechanism is supported for loopback audio.");
            } else if (((eqy) this.d.e()) == null) {
                ((hby) a.g().h(hdg.a, "ALT.LoopbackProvider").j("com/google/android/libraries/search/audio/playback/impl/adapter/LoopbackAudioProvider", "startListening", 49, "LoopbackAudioProvider.kt")).r("#audio# no playback service found, check modules setup");
            } else {
                hca hca = a;
                ((hby) hca.f().h(hdg.a, "ALT.LoopbackProvider").j("com/google/android/libraries/search/audio/playback/impl/adapter/LoopbackAudioProvider", "startListening", 57, "LoopbackAudioProvider.kt")).u("#audio# Starting listening to the loopback audio for %s.", fbi.s(this.b));
                dou.e(this.f, this.c, jkq.a, 2);
                htk l = eqx.d.l();
                jnu.d(l, "newBuilder(...)");
                jnu.e(l, "builder");
                ehg ehg = this.b;
                if (!l.b.B()) {
                    l.u();
                }
                eqx eqx = (eqx) l.b;
                eqx.b = ehg;
                eqx.a |= 1;
                dyt dyt2 = this.c;
                jnu.e(dyt2, "value");
                if (!l.b.B()) {
                    l.u();
                }
                eqx eqx2 = (eqx) l.b;
                dyt2.getClass();
                eqx2.c = dyt2;
                eqx2.a |= 2;
                htq o = l.r();
                jnu.d(o, "build(...)");
                eqx eqx3 = (eqx) o;
                ((hby) hca.f().h(hdg.a, "ALT.LoopbackProvider").j("com/google/android/libraries/search/audio/playback/impl/adapter/LoopbackAudioProvider", "startListening", 69, "LoopbackAudioProvider.kt")).u("#audio# Started listening to the loopback audio for %s.", fbi.s(this.b));
                throw null;
            }
        }
    }
}
