package defpackage;

/* renamed from: enf  reason: default package */
/* compiled from: PG */
final class enf extends jme {
    Object a;
    /* synthetic */ Object b;
    final /* synthetic */ eng c;
    int d;
    eng e;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public enf(eng eng, jlr jlr) {
        super(jlr);
        this.c = eng;
    }

    public final Object bk(Object obj) {
        this.b = obj;
        this.d |= Integer.MIN_VALUE;
        return this.c.n((enk) null, this);
    }
}
