package defpackage;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import java.util.Objects;

/* renamed from: bef  reason: default package */
/* compiled from: PG */
public class bef extends BroadcastReceiver {
    public static final /* synthetic */ int a = 0;

    static {
        bbk.b("ConstraintProxy");
    }

    public final void onReceive(Context context, Intent intent) {
        bbk.a();
        Objects.toString(intent);
        context.startService(bee.b(context));
    }
}
