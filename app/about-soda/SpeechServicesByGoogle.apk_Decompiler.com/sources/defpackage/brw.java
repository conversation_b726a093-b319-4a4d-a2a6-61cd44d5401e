package defpackage;

import java.util.Locale;

/* renamed from: brw  reason: default package */
/* compiled from: PG */
public abstract class brw {
    public final Locale b;

    public brw(String str) {
        this.b = new Locale(str);
        a();
    }

    /* access modifiers changed from: protected */
    public abstract void a();

    public final String c() {
        return this.b.getCountry();
    }

    public final String d() {
        return this.b.getDisplayName();
    }

    public final String e() {
        return this.b.getLanguage();
    }

    public final boolean equals(Object obj) {
        if (!(obj instanceof brw)) {
            return false;
        }
        if (obj == this) {
            return true;
        }
        return this.b.equals(((brw) obj).b);
    }

    public final int hashCode() {
        return this.b.hashCode();
    }

    public final String toString() {
        Locale locale = this.b;
        String language = locale.getLanguage();
        if (locale.getCountry().isEmpty()) {
            return language;
        }
        String str = language + "-" + this.b.getCountry();
        if (this.b.getVariant().isEmpty()) {
            return str;
        }
        return str + "-" + this.b.getVariant();
    }

    public brw(String str, String str2) {
        this.b = new Locale(str, str2);
        a();
    }

    public brw(String str, String str2, String str3) {
        this.b = new Locale(str, str2, str3);
        a();
    }

    public brw(Locale locale) {
        this.b = locale;
        a();
    }
}
