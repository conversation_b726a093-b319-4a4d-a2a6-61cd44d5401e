package defpackage;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;

/* renamed from: cyd  reason: default package */
/* compiled from: PG */
public final class cyd implements cxz {
    public final csm a;
    public final Executor b;
    private final cvz c;

    public cyd(csm csm, cvz cvz, Executor executor) {
        this.a = csm;
        this.c = cvz;
        this.b = executor;
    }

    public static hme e(dtu dtu, String str, int[] iArr, String str2) {
        return dtu.e(iArr, str, str2);
    }

    public static final Set g(Map map, cyc cyc) {
        if (!map.containsKey(cyc)) {
            map.put(cyc, new HashSet());
        }
        return (Set) map.get(cyc);
    }

    public static boolean h(csx csx, String str) {
        if (!str.equals(csx.c) || (csx.a & 131072) == 0) {
            return false;
        }
        return true;
    }

    public static final cyc i(csx csx) {
        return new cyc(Long.valueOf(csx.r));
    }

    private final gxq j() {
        gxl gxl = new gxl();
        if (this.a.b().f()) {
            gxl.h((String) this.a.b().b());
        }
        if (this.a.c().f()) {
            gxl.h((String) this.a.c().b());
        }
        gxl.h("ICING");
        return gxl.g();
    }

    public final hme a(Collection collection) {
        cwn cwn = new cwn(this, 18);
        fvf.aP(collection);
        return cqh.U(new gyv(collection, cwn)).n(new ctw(17), this.b);
    }

    public final hme b(String str) {
        gxq j = j();
        int[] iArr = new int[0];
        dtu a2 = this.a.a();
        ArrayList arrayList = new ArrayList();
        int i = ((hal) j).c;
        for (int i2 = 0; i2 < i; i2++) {
            arrayList.add(e(a2, str, iArr, (String) j.get(i2)));
        }
        return cqh.U(arrayList).n(new ctw(18), this.b);
    }

    public final hme c(String str) {
        if (!cqh.h()) {
            return hma.a;
        }
        return cqh.V(ftd.L(ftd.K(d(str), new cwr(3), this.b), new cxr(this, 3), this.b), ftd.L(ftd.K(ftd.K(this.c.e(), new cwn(str, 20), this.b), new cwr(2), this.b), new cxr(this, 3), this.b)).n(new ctw(15), this.b);
    }

    public final hme d(String str) {
        return ftd.K(this.c.c(), new cwn(str, 19), this.b);
    }

    public final hme f(Map map) {
        ArrayList arrayList = new ArrayList();
        for (Map.Entry entry : map.entrySet()) {
            arrayList.addAll(fvf.C(j(), new cve((Object) this, (Object) hfc.af((Set) entry.getValue()), (Object) (cyc) entry.getKey(), 2)));
        }
        return cqh.W(arrayList).n(new ctw(16), hld.a);
    }
}
