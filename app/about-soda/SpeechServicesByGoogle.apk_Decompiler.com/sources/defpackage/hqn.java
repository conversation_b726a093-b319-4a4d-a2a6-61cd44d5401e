package defpackage;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ProgressBar;
import com.google.android.libraries.speech.transcription.ui.DrawSoundLevelsView;
import com.google.android.tts.R;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;

/* renamed from: hqn  reason: default package */
/* compiled from: PG */
public final class hqn {
    public final Object a;
    public final Object b;
    public final Object c;
    public final Object d;
    public final Object e;
    public final Object f;
    public final Object g;
    public final Object h;

    public hqn(grh grh, itv itv, String str, Map map, jjk jjk, grh grh2, gnk gnk, grh grh3) {
        this.a = grh;
        this.b = itv;
        this.c = str;
        this.d = map;
        this.e = jjk;
        this.f = grh2;
        this.h = gnk;
        this.g = grh3;
    }

    /* JADX WARNING: type inference failed for: r2v2, types: [dtu, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v4, types: [java.util.concurrent.ScheduledExecutorService, java.lang.Object] */
    /* JADX WARNING: Code restructure failed: missing block: B:16:0x0081, code lost:
        r8 = move-exception;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:18:?, code lost:
        defpackage.jnu.y(r1, r7);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:19:0x0085, code lost:
        throw r8;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:23:0x0088, code lost:
        r8 = move-exception;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:24:0x0089, code lost:
        defpackage.jnu.y(r0, r7);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:25:0x008c, code lost:
        throw r8;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme a(java.lang.String r7, boolean r8) {
        /*
            r6 = this;
            gmn r0 = defpackage.gmm.a
            java.lang.String r1 = "Fetching experiments for device"
            goq r2 = defpackage.goq.a
            gmj r0 = defpackage.ftd.T(r1, r2, r0)
            java.lang.String r1 = "PhenotypeApi.getConfigurationSnapshot"
            gmn r2 = defpackage.gmm.a     // Catch:{ all -> 0x0086 }
            goq r3 = defpackage.goq.a     // Catch:{ all -> 0x0086 }
            gmj r1 = defpackage.ftd.T(r1, r3, r2)     // Catch:{ all -> 0x0086 }
            java.lang.Object r2 = r6.h     // Catch:{ all -> 0x007f }
            java.lang.String r3 = ""
            hme r2 = r2.b(r7, r3)     // Catch:{ all -> 0x007f }
            if (r8 == 0) goto L_0x0028
            java.util.concurrent.TimeUnit r8 = java.util.concurrent.TimeUnit.SECONDS     // Catch:{ all -> 0x007f }
            java.lang.Object r3 = r6.a     // Catch:{ all -> 0x007f }
            r4 = 25
            hme r2 = defpackage.gol.a(r2, r4, r8, r3)     // Catch:{ all -> 0x007f }
        L_0x0028:
            r1.a(r2)     // Catch:{ all -> 0x007f }
            r8 = 0
            defpackage.jnu.y(r1, r8)     // Catch:{ all -> 0x0086 }
            ezm r1 = new ezm     // Catch:{ all -> 0x0086 }
            r3 = 3
            r1.<init>(r6, r7, r3, r8)     // Catch:{ all -> 0x0086 }
            hko r7 = defpackage.gof.d(r1)     // Catch:{ all -> 0x0086 }
            hld r1 = defpackage.hld.a     // Catch:{ all -> 0x0086 }
            hme r7 = defpackage.hke.g(r2, r7, r1)     // Catch:{ all -> 0x0086 }
            r0.a(r7)     // Catch:{ all -> 0x0086 }
            defpackage.jnu.y(r0, r8)
            java.lang.Object r8 = r6.d
            r0 = 2
            hme[] r0 = new defpackage.hme[r0]
            cxp r8 = (defpackage.cxp) r8
            hme r8 = r8.g()
            euq r1 = defpackage.euq.h
            gdp r2 = new gdp
            r3 = 1
            r2.<init>(r1, r3)
            hld r1 = defpackage.hld.a
            java.lang.Class<ccx> r4 = defpackage.ccx.class
            hme r8 = defpackage.ftd.F(r8, r4, r2, r1)
            r1 = 0
            r0[r1] = r8
            r0[r3] = r7
            java.util.List r8 = defpackage.jji.o(r0)
            hme r8 = defpackage.ftc.x(r8)
            fki r0 = new fki
            r1 = 20
            r0.<init>(r7, r1)
            gqx r7 = defpackage.gof.b(r0)
            hld r0 = defpackage.hld.a
            hme r7 = defpackage.hke.f(r8, r7, r0)
            return r7
        L_0x007f:
            r7 = move-exception
            throw r7     // Catch:{ all -> 0x0081 }
        L_0x0081:
            r8 = move-exception
            defpackage.jnu.y(r1, r7)     // Catch:{ all -> 0x0086 }
            throw r8     // Catch:{ all -> 0x0086 }
        L_0x0086:
            r7 = move-exception
            throw r7     // Catch:{ all -> 0x0088 }
        L_0x0088:
            r8 = move-exception
            defpackage.jnu.y(r0, r7)
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.hqn.a(java.lang.String, boolean):hme");
    }

    public final void b(View.OnClickListener onClickListener, String str) {
        ((FrameLayout) this.h).setOnClickListener(onClickListener);
        ((FrameLayout) this.h).setContentDescription(str);
        if (str == null) {
            ((FrameLayout) this.h).setFocusable(false);
        } else {
            ((FrameLayout) this.h).setFocusable(true);
        }
    }

    public final void c(int i) {
        ((hby) ((hby) DrawSoundLevelsView.a.f()).j("com/google/android/libraries/speech/transcription/ui/DrawSoundLevelsView", "setSpeechLevel", 53, "DrawSoundLevelsView.java")).s("#setSpeechLevel %d", i);
        DrawSoundLevelsView drawSoundLevelsView = (DrawSoundLevelsView) this.e;
        drawSoundLevelsView.b.add(Integer.valueOf(i));
        drawSoundLevelsView.invalidate();
    }

    public final void d() {
        ((ProgressBar) this.d).setVisibility(4);
        ((ImageView) this.b).setImageDrawable((Drawable) this.f);
        ((ImageView) this.c).setBackgroundResource(R.drawable.transcription_intent_api_red_ring);
        ((DrawSoundLevelsView) this.e).setEnabled(false);
    }

    public final void e() {
        ((ProgressBar) this.d).setVisibility(4);
        ((ImageView) this.b).setImageDrawable((Drawable) this.f);
        ((ImageView) this.c).setBackgroundResource(R.drawable.transcription_intent_api_initializing_mic_ring);
        ((DrawSoundLevelsView) this.e).setEnabled(false);
    }

    public final void f() {
        ((ProgressBar) this.d).setVisibility(4);
        ((ImageView) this.b).setImageDrawable((Drawable) this.g);
        ((ImageView) this.c).setBackgroundResource(R.drawable.transcription_intent_api_recording_mic_background);
        ((DrawSoundLevelsView) this.e).setEnabled(true);
    }

    public hqn(grh grh, ScheduledExecutorService scheduledExecutorService, gnk gnk, gnk gnk2, Map map, dtu dtu, bzj bzj, cxp cxp) {
        jnu.e(grh, "reaper");
        jnu.e(scheduledExecutorService, "executor");
        jnu.e(gnk, "uiDeviceState");
        jnu.e(gnk2, "deviceState");
        jnu.e(map, "packages");
        jnu.e(cxp, "storageInfoProvider");
        this.c = grh;
        this.a = scheduledExecutorService;
        this.e = gnk;
        this.f = gnk2;
        this.g = map;
        this.h = dtu;
        this.b = bzj;
        this.d = cxp;
    }

    public hqn(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8) {
        this.f = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.g = jjk4;
        this.h = jjk5;
        this.e = jjk6;
        this.d = jjk7;
        this.a = jjk8;
    }

    public hqn(View view) {
        this.h = (FrameLayout) view.findViewById(R.id.transcription_mic_container);
        this.e = (DrawSoundLevelsView) view.findViewById(R.id.transcription_sound_levels);
        this.b = (ImageView) view.findViewById(R.id.transcription_mic_image_indicator);
        this.c = (ImageView) view.findViewById(R.id.transcription_mic_image_indicator_background);
        this.d = (ProgressBar) view.findViewById(R.id.transcription_waiting_for_results);
        Context context = view.getContext();
        Drawable h2 = ke.h(context, R.drawable.quantum_ic_keyboard_voice_googblue_36);
        fvf.aP(h2);
        Drawable mutate = h2.mutate();
        this.f = mutate;
        Drawable drawable = mutate;
        mutate.setTint(context.getColor(R.color.agsa_color_primary));
        Drawable h3 = ke.h(context, R.drawable.quantum_ic_keyboard_voice_white_36);
        fvf.aP(h3);
        Drawable mutate2 = h3.mutate();
        this.g = mutate2;
        Drawable drawable2 = mutate2;
        mutate2.setTint(context.getColor(R.color.agsa_color_on_primary));
        Drawable h4 = ke.h(context, R.drawable.quantum_ic_done_green500_36);
        fvf.aP(h4);
        this.a = h4.mutate();
    }
}
