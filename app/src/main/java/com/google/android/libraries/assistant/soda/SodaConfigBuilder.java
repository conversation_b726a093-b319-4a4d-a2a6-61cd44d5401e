package com.google.android.libraries.assistant.soda;

import android.util.Log;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

/**
 * SODA配置构建器
 * 
 * 用于构建SODA引擎所需的protobuf配置数据
 * 包括识别配置和启动参数
 */
public class SodaConfigBuilder {
    private static final String TAG = "SodaConfigBuilder";
    
    // 音频参数
    private int sampleRate = 16000;
    private int channels = 1;
    private String language = "en-US";
    private String modelPath;
    
    /**
     * 设置采样率
     * @param sampleRate 采样率（Hz）
     * @return 构建器实例
     */
    public SodaConfigBuilder setSampleRate(int sampleRate) {
        this.sampleRate = sampleRate;
        return this;
    }
    
    /**
     * 设置声道数
     * @param channels 声道数
     * @return 构建器实例
     */
    public SodaConfigBuilder setChannels(int channels) {
        this.channels = channels;
        return this;
    }
    
    /**
     * 设置语言
     * @param language 语言代码（如"en-US", "zh-CN"）
     * @return 构建器实例
     */
    public SodaConfigBuilder setLanguage(String language) {
        this.language = language;
        return this;
    }
    
    /**
     * 设置模型路径
     * @param modelPath 模型文件路径
     * @return 构建器实例
     */
    public SodaConfigBuilder setModelPath(String modelPath) {
        this.modelPath = modelPath;
        return this;
    }
    
    /**
     * 从配置文件构建配置数据
     * @param configFilePath 配置文件路径
     * @return protobuf序列化的配置数据
     */
    public byte[] buildConfigFromFile(String configFilePath) {
        Log.d(TAG, "Building config from file: " + configFilePath);
        
        try {
            File configFile = new File(configFilePath);
            if (!configFile.exists()) {
                Log.e(TAG, "Config file does not exist: " + configFilePath);
                return null;
            }
            
            // 读取配置文件内容
            FileInputStream fis = new FileInputStream(configFile);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                baos.write(buffer, 0, bytesRead);
            }
            
            fis.close();
            byte[] configData = baos.toByteArray();
            
            Log.d(TAG, "Config file loaded, size: " + configData.length + " bytes");
            return configData;
            
        } catch (IOException e) {
            Log.e(TAG, "Failed to read config file: " + configFilePath, e);
            return null;
        }
    }
    
    /**
     * 构建基本的识别配置
     * @return protobuf序列化的配置数据
     */
    public byte[] buildRecognitionConfig() {
        Log.d(TAG, "Building recognition config - sampleRate: " + sampleRate + 
              ", channels: " + channels + ", language: " + language);
        
        // 这里应该构建实际的protobuf配置
        // 由于没有具体的protobuf定义，这里返回一个基本的配置
        // 在实际使用中，应该使用从配置文件读取的数据
        
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            // 写入基本的配置信息（简化版本）
            // 实际应该使用protobuf编码
            
            // 采样率
            baos.write(intToBytes(sampleRate));
            // 声道数
            baos.write(intToBytes(channels));
            // 语言代码长度和内容
            byte[] langBytes = language.getBytes("UTF-8");
            baos.write(intToBytes(langBytes.length));
            baos.write(langBytes);
            
            if (modelPath != null) {
                byte[] modelBytes = modelPath.getBytes("UTF-8");
                baos.write(intToBytes(modelBytes.length));
                baos.write(modelBytes);
            } else {
                baos.write(intToBytes(0));
            }
            
        } catch (IOException e) {
            Log.e(TAG, "Failed to build config", e);
            return null;
        }
        
        byte[] config = baos.toByteArray();
        Log.d(TAG, "Built recognition config, size: " + config.length + " bytes");
        return config;
    }
    
    /**
     * 构建启动参数
     * @return protobuf序列化的启动参数
     */
    public byte[] buildStartParams() {
        Log.d(TAG, "Building start params with audio format - sampleRate: " + sampleRate +
              ", channels: " + channels);

        // 构建包含音频格式信息的启动参数
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            // 基本启动标志
            baos.write(new byte[]{0x15, 0x00, 0x00, 0x7A, 0x46, 0x18, 0x01});

            // 添加音频格式信息（protobuf风格编码）
            // 字段1: 采样率 (tag=1, wire_type=0)
            baos.write(0x08); // tag=1, wire_type=0
            writeVarint(baos, sampleRate);

            // 字段2: 声道数 (tag=2, wire_type=0)
            baos.write(0x10); // tag=2, wire_type=0
            writeVarint(baos, channels);

            // 字段3: 编码格式 (tag=3, wire_type=0) - PCM_16BIT = 2
            baos.write(0x18); // tag=3, wire_type=0
            writeVarint(baos, 2); // AudioFormat.ENCODING_PCM_16BIT

        } catch (IOException e) {
            Log.e(TAG, "Failed to build start params", e);
            return null;
        }

        byte[] params = baos.toByteArray();
        Log.d(TAG, "Built start params with audio format, size: " + params.length + " bytes");
        return params;
    }

    /**
     * 写入变长整数（protobuf varint编码）
     */
    private void writeVarint(ByteArrayOutputStream baos, int value) throws IOException {
        while ((value & 0x80) != 0) {
            baos.write((value & 0x7F) | 0x80);
            value >>>= 7;
        }
        baos.write(value & 0x7F);
    }
    
    /**
     * 构建时间戳元数据
     * @param timestampUsec 时间戳（微秒）
     * @param sequenceNumber 序列号
     * @return 时间戳元数据
     */
    public byte[] buildTimestampMetadata(long timestampUsec, long sequenceNumber) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            // 写入时间戳（8字节）
            baos.write(longToBytes(Long.reverseBytes(timestampUsec)));
            // 写入序列号（8字节）
            baos.write(longToBytes(Long.reverseBytes(sequenceNumber)));
            
        } catch (IOException e) {
            Log.e(TAG, "Failed to build timestamp metadata", e);
            return null;
        }
        
        return baos.toByteArray();
    }
    
    /**
     * 将int转换为字节数组
     */
    private byte[] intToBytes(int value) {
        return new byte[] {
            (byte) (value & 0xFF),
            (byte) ((value >> 8) & 0xFF),
            (byte) ((value >> 16) & 0xFF),
            (byte) ((value >> 24) & 0xFF)
        };
    }
    
    /**
     * 将long转换为字节数组
     */
    private byte[] longToBytes(long value) {
        return new byte[] {
            (byte) (value & 0xFF),
            (byte) ((value >> 8) & 0xFF),
            (byte) ((value >> 16) & 0xFF),
            (byte) ((value >> 24) & 0xFF),
            (byte) ((value >> 32) & 0xFF),
            (byte) ((value >> 40) & 0xFF),
            (byte) ((value >> 48) & 0xFF),
            (byte) ((value >> 56) & 0xFF)
        };
    }
}
