package defpackage;

/* renamed from: ele  reason: default package */
/* compiled from: PG */
public enum ele implements hts {
    MODE_DEFAULT_MEMORY(0),
    MODE_MEMORY_PERSISTENT_IF_ALLOWED(1),
    MODE_PERSISTENT_IF_ALLOWED(2);
    
    public final int d;

    private ele(int i) {
        this.d = i;
    }

    public static ele b(int i) {
        if (i == 0) {
            return MODE_DEFAULT_MEMORY;
        }
        if (i == 1) {
            return MODE_MEMORY_PERSISTENT_IF_ALLOWED;
        }
        if (i != 2) {
            return null;
        }
        return MODE_PERSISTENT_IF_ALLOWED;
    }

    public final int a() {
        return this.d;
    }

    public final String toString() {
        return Integer.toString(this.d);
    }
}
