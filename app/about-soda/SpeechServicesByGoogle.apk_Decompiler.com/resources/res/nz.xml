<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:layout_width="wrap_content" android:layout_height="match_parent" style="@style/Widget.CarUi.Toolbar.MenuItem.IndividualContainer">
    <FrameLayout android:layout_gravity="center" android:id="@+id/car_ui_toolbar_menu_item_icon_container" android:layout_width="match_parent" android:layout_height="match_parent">
        <ImageView android:background="@drawable/car_ui_toolbar_menu_item_icon_ripple" android:layout_width="match_parent" android:layout_height="match_parent" android:src="@drawable/car_ui_toolbar_menu_item_icon_background" android:scaleType="center"/>
        <ImageView android:layout_gravity="center" android:id="@+id/car_ui_toolbar_menu_item_icon" android:layout_width="@dimen/car_ui_toolbar_menu_item_icon_size" android:layout_height="@dimen/car_ui_toolbar_menu_item_icon_size" android:tint="@color/car_ui_toolbar_menu_item_icon_color" android:tintMode="src_in"/>
    </FrameLayout>
    <com.android.car.ui.uxr.DrawableStateSwitch android:layout_gravity="center" android:id="@+id/car_ui_toolbar_menu_item_switch" android:clickable="false" android:layout_width="wrap_content" android:layout_height="wrap_content"/>
    <com.android.car.ui.uxr.DrawableStateButton android:layout_gravity="center" android:id="@+id/car_ui_toolbar_menu_item_text" android:clickable="false" android:layout_width="wrap_content" android:layout_height="match_parent" style="@style/Widget.CarUi.Toolbar.TextButton"/>
    <com.android.car.ui.uxr.DrawableStateButton android:layout_gravity="center" android:id="@+id/car_ui_toolbar_menu_item_text_with_icon" android:clickable="false" android:layout_width="wrap_content" android:layout_height="match_parent" style="@style/Widget.CarUi.Toolbar.TextButton.WithIcon"/>
</FrameLayout>
