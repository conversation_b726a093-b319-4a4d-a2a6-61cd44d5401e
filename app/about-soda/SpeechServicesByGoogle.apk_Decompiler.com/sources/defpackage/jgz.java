package defpackage;

import javax.security.auth.x500.X500Principal;

/* renamed from: jgz  reason: default package */
/* compiled from: PG */
final class jgz {
    public final String a;
    public final int b;
    public int c;
    public int d;
    public int e;
    public int f;
    public char[] g;

    public jgz(X500Principal x500Principal) {
        String name = x500Principal.getName("RFC2253");
        this.a = name;
        this.b = name.length();
    }

    public final char a() {
        int i;
        int i2;
        int i3 = this.c + 1;
        this.c = i3;
        if (i3 != this.b) {
            char c2 = this.g[i3];
            if (!(c2 == ' ' || c2 == '%' || c2 == '\\' || c2 == '_' || c2 == '\"' || c2 == '#')) {
                switch (c2) {
                    case '*':
                    case '+':
                    case ',':
                        break;
                    default:
                        switch (c2) {
                            case ';':
                            case '<':
                            case '=':
                            case '>':
                                break;
                            default:
                                int b2 = b(i3);
                                this.c++;
                                if (b2 >= 128) {
                                    if (b2 >= 192 && b2 <= 247) {
                                        if (b2 <= 223) {
                                            i2 = b2 & 31;
                                            i = 1;
                                        } else if (b2 <= 239) {
                                            i2 = b2 & 15;
                                            i = 2;
                                        } else {
                                            i2 = b2 & 7;
                                            i = 3;
                                        }
                                        int i4 = 0;
                                        while (true) {
                                            if (i4 < i) {
                                                int i5 = this.c;
                                                int i6 = i5 + 1;
                                                this.c = i6;
                                                if (i6 != this.b && this.g[i6] == '\\') {
                                                    int i7 = i5 + 2;
                                                    this.c = i7;
                                                    int b3 = b(i7);
                                                    this.c++;
                                                    if ((b3 & 192) == 128) {
                                                        i2 = (i2 << 6) + (b3 & 63);
                                                        i4++;
                                                    }
                                                }
                                            } else {
                                                b2 = (char) i2;
                                            }
                                        }
                                    }
                                    b2 = 63;
                                }
                                return (char) b2;
                        }
                }
            }
            return c2;
        }
        throw new IllegalStateException("Unexpected end of DN: ".concat(String.valueOf(this.a)));
    }

    public final int b(int i) {
        int i2;
        int i3;
        int i4 = i + 1;
        if (i4 < this.b) {
            char[] cArr = this.g;
            char c2 = cArr[i];
            if (c2 >= '0' && c2 <= '9') {
                i2 = c2 - '0';
            } else if (c2 >= 'a' && c2 <= 'f') {
                i2 = c2 - 'W';
            } else if (c2 < 'A' || c2 > 'F') {
                throw new IllegalStateException("Malformed DN: ".concat(String.valueOf(this.a)));
            } else {
                i2 = c2 - '7';
            }
            char c3 = cArr[i4];
            if (c3 >= '0' && c3 <= '9') {
                i3 = c3 - '0';
            } else if (c3 >= 'a' && c3 <= 'f') {
                i3 = c3 - 'W';
            } else if (c3 < 'A' || c3 > 'F') {
                throw new IllegalStateException("Malformed DN: ".concat(String.valueOf(this.a)));
            } else {
                i3 = c3 - '7';
            }
            return (i2 << 4) + i3;
        }
        throw new IllegalStateException("Malformed DN: ".concat(String.valueOf(this.a)));
    }

    /* JADX WARNING: Removed duplicated region for block: B:6:0x0015 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:7:0x0017  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.String c() {
        /*
            r6 = this;
        L_0x0000:
            int r0 = r6.c
            int r1 = r6.b
            r2 = 32
            if (r0 >= r1) goto L_0x0013
            char[] r3 = r6.g
            char r3 = r3[r0]
            if (r3 != r2) goto L_0x0013
            int r0 = r0 + 1
            r6.c = r0
            goto L_0x0000
        L_0x0013:
            if (r0 != r1) goto L_0x0017
            r0 = 0
            return r0
        L_0x0017:
            int r1 = r0 + 1
            r6.d = r0
            r6.c = r1
        L_0x001d:
            int r0 = r6.c
            int r1 = r6.b
            r3 = 61
            if (r0 >= r1) goto L_0x0032
            char[] r4 = r6.g
            char r4 = r4[r0]
            if (r4 == r3) goto L_0x0032
            if (r4 == r2) goto L_0x0032
            int r0 = r0 + 1
            r6.c = r0
            goto L_0x001d
        L_0x0032:
            java.lang.String r4 = "Unexpected end of DN: "
            if (r0 >= r1) goto L_0x00c1
            r6.e = r0
            char[] r1 = r6.g
            char r1 = r1[r0]
            if (r1 != r2) goto L_0x006a
        L_0x003e:
            int r0 = r6.c
            int r1 = r6.b
            if (r0 >= r1) goto L_0x0051
            char[] r5 = r6.g
            char r5 = r5[r0]
            if (r5 == r3) goto L_0x0051
            if (r5 != r2) goto L_0x0051
            int r0 = r0 + 1
            r6.c = r0
            goto L_0x003e
        L_0x0051:
            char[] r5 = r6.g
            char r5 = r5[r0]
            if (r5 != r3) goto L_0x005a
            if (r0 == r1) goto L_0x005a
            goto L_0x006a
        L_0x005a:
            java.lang.String r0 = r6.a
            java.lang.String r0 = java.lang.String.valueOf(r0)
            java.lang.IllegalStateException r1 = new java.lang.IllegalStateException
            java.lang.String r0 = r4.concat(r0)
            r1.<init>(r0)
            throw r1
        L_0x006a:
            int r0 = r0 + 1
            r6.c = r0
        L_0x006e:
            int r0 = r6.c
            int r1 = r6.b
            if (r0 >= r1) goto L_0x007f
            char[] r1 = r6.g
            char r1 = r1[r0]
            if (r1 != r2) goto L_0x007f
            int r0 = r0 + 1
            r6.c = r0
            goto L_0x006e
        L_0x007f:
            int r0 = r6.e
            int r1 = r6.d
            int r2 = r0 - r1
            r3 = 4
            if (r2 <= r3) goto L_0x00b8
            char[] r2 = r6.g
            int r3 = r1 + 3
            char r3 = r2[r3]
            r4 = 46
            if (r3 != r4) goto L_0x00b8
            char r3 = r2[r1]
            r4 = 79
            if (r3 == r4) goto L_0x009c
            r4 = 111(0x6f, float:1.56E-43)
            if (r3 != r4) goto L_0x00b8
        L_0x009c:
            int r3 = r1 + 1
            char r3 = r2[r3]
            r4 = 73
            if (r3 == r4) goto L_0x00a8
            r4 = 105(0x69, float:1.47E-43)
            if (r3 != r4) goto L_0x00b8
        L_0x00a8:
            int r3 = r1 + 2
            char r2 = r2[r3]
            r3 = 68
            if (r2 == r3) goto L_0x00b4
            r3 = 100
            if (r2 != r3) goto L_0x00b8
        L_0x00b4:
            int r1 = r1 + 4
            r6.d = r1
        L_0x00b8:
            java.lang.String r2 = new java.lang.String
            char[] r3 = r6.g
            int r0 = r0 - r1
            r2.<init>(r3, r1, r0)
            return r2
        L_0x00c1:
            java.lang.String r0 = r6.a
            java.lang.String r0 = java.lang.String.valueOf(r0)
            java.lang.IllegalStateException r1 = new java.lang.IllegalStateException
            java.lang.String r0 = r4.concat(r0)
            r1.<init>(r0)
            throw r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.jgz.c():java.lang.String");
    }
}
