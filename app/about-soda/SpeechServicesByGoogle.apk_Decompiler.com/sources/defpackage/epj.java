package defpackage;

/* renamed from: epj  reason: default package */
/* compiled from: PG */
final class epj extends jmi implements jne {
    Object a;
    int b;
    final /* synthetic */ jqz c;
    final /* synthetic */ hme d;
    final /* synthetic */ enr e;
    final /* synthetic */ Object f;
    private final /* synthetic */ int g;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public epj(epe epe, jqz jqz, hme hme, enr enr, jlr jlr, int i) {
        super(2, jlr);
        this.g = i;
        this.f = epe;
        this.c = jqz;
        this.d = hme;
        this.e = enr;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        if (this.g != 0) {
            return ((epj) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
        return ((epj) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    public final Object bk(Object obj) {
        Object obj2;
        Object obj3;
        if (this.g != 0) {
            jlx jlx = jlx.COROUTINE_SUSPENDED;
            if (this.b != 0) {
                obj3 = this.a;
                jji.c(obj);
            } else {
                jji.c(obj);
                Object obj4 = this.f;
                jqz jqz = this.c;
                cxi cxi = ((epe) obj4).d;
                this.a = cxi;
                this.b = 1;
                Object m = jqz.m(this);
                if (m == jlx) {
                    return jlx;
                }
                obj3 = cxi;
                obj = m;
            }
            return ((cxi) obj3).n((ebg) obj, new eoe(this.e, 3), this.d);
        }
        jlx jlx2 = jlx.COROUTINE_SUSPENDED;
        if (this.b != 0) {
            obj2 = this.a;
            jji.c(obj);
        } else {
            jji.c(obj);
            Object obj5 = this.f;
            jqz jqz2 = this.c;
            cxi cxi2 = ((epk) obj5).a;
            this.a = cxi2;
            this.b = 1;
            Object m2 = jqz2.m(this);
            if (m2 == jlx2) {
                return jlx2;
            }
            obj2 = cxi2;
            obj = m2;
        }
        return ((cxi) obj2).n((ebg) obj, new eoe(this.e, 5), this.d);
    }

    public final jlr c(Object obj, jlr jlr) {
        if (this.g != 0) {
            return new epj((epe) this.f, this.c, this.d, this.e, jlr, 1);
        }
        return new epj((epk) this.f, this.c, this.d, this.e, jlr, 0);
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public epj(epk epk, jqz jqz, hme hme, enr enr, jlr jlr, int i) {
        super(2, jlr);
        this.g = i;
        this.f = epk;
        this.c = jqz;
        this.d = hme;
        this.e = enr;
    }
}
