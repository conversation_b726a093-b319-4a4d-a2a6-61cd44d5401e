# 实时语音识别 JNI 调用文档（SODA）

## 概述

本文档描述了 Google Speech Services 中 SODA（Speech On Device Assistant）实时语音识别功能的 JNI 接口调用方法、参数说明和典型使用流程。

## 相关 Native 库

- **核心识别库**: `resources/lib/arm64-v8a/libgoogle_speech_jni.so`
- **编解码库**: `libogg_opus_encoder.so`（非识别核心）

## 主要 JNI 接口

### 资源生命周期管理

| 方法 | 说明 | 参数 |
|------|------|------|
| `nativeCreateSharedResources(Object self)` | 创建共享资源 | `self`: Java 对象引用 |
| `nativeConstruct(long sharedResHandle)` | 构造 SODA 实例 | `sharedResHandle`: 共享资源句柄 |
| `nativeDelete(long sodaHandle)` | 删除 SODA 实例 | `sodaHandle`: SODA 实例句柄 |
| `nativeDeleteSharedResources(Object self, long sharedResHandle)` | 删除共享资源 | `self`: Java 对象引用, `sharedResHandle`: 共享资源句柄 |
| `nativeDeleteSharedResourcesIfDiarizationProcessorAbsent(long sharedResHandle)` | 条件删除共享资源 | `sharedResHandle`: 共享资源句柄 |
| `nativeDeleteDiarizationProcessor(long sharedResHandle)` | 删除说话人分离处理器 | `sharedResHandle`: 共享资源句柄 |

### 初始化与配置

| 方法 | 说明 | 参数 |
|------|------|------|
| `nativeInit(long sodaHandle, byte[] configBytes)` | 初始化引擎 | `sodaHandle`: SODA 实例句柄, `configBytes`: 配置 protobuf 序列化数据 |
| `nativeRequiresReinitialization(long sodaHandle, byte[] configBytes)` | 检查是否需要重新初始化 | `sodaHandle`: SODA 实例句柄, `configBytes`: 新配置数据 |

### 数据提供与传输

| 方法 | 说明 | 参数 |
|------|------|------|
| `nativeSetDataProvider(long sodaHandle, SodaDataProviderJni dataProvider)` | 设置数据提供器 | `sodaHandle`: SODA 实例句柄, `dataProvider`: 数据提供器实例 |
| `nativeSetTransportFactory(long sodaHandle, SodaTransportFactory transport)` | 设置传输工厂 | `sodaHandle`: SODA 实例句柄, `transport`: 传输工厂实例 |

### 采集控制

| 方法 | 说明 | 参数 |
|------|------|------|
| `nativeStartCapture(long sodaHandle, byte[] startParamsBytes)` | 启动采集会话 | `sodaHandle`: SODA 实例句柄, `startParamsBytes`: 启动参数 protobuf 序列化数据 |
| `nativeStopCapture(long sodaHandle)` | 停止采集会话 | `sodaHandle`: SODA 实例句柄 |

### 音频输入

| 方法 | 说明 | 参数 |
|------|------|------|
| `nativeAddAudio(long sodaHandle, ByteBuffer audio, long byteCount)` | 添加音频数据 | `sodaHandle`: SODA 实例句柄, `audio`: 音频缓冲区, `byteCount`: 字节数 |
| `nativeAddTimestampedAudio(long sodaHandle, ByteBuffer audio, long byteCount, ByteBuffer tsMeta, long tsMetaByteCount)` | 添加带时间戳的音频数据 | `sodaHandle`: SODA 实例句柄, `audio`: 音频缓冲区, `byteCount`: 字节数, `tsMeta`: 时间戳元数据, `tsMetaByteCount`: 时间戳元数据字节数 |

### 数据回调

| 方法 | 说明 | 参数 |
|------|------|------|
| `nativeHandleDataResponse(long reqId, long token, byte[] payload)` | 处理数据响应回调 | `reqId`: 请求ID, `token`: 令牌, `payload`: 响应数据 |

## 调用顺序（实时识别典型流程）

### 1. 创建共享资源
```java
long sharedResHandle = Soda.nativeCreateSharedResources(this);
```

### 2. 构造 SODA 实例
```java
long sodaHandle = sodaInstance.nativeConstruct(sharedResHandle);
```

### 3. 设置数据提供与传输（可选）
```java
// 设置数据提供器
sodaInstance.nativeSetDataProvider(sodaHandle, new SodaDataProviderJni(provider));

// 设置传输工厂
sodaInstance.nativeSetTransportFactory(sodaHandle, transportFactory);
```

### 4. 初始化引擎
```java
byte[] configBytes = buildConfigProtoBytes(...); // 构建配置 protobuf
byte[] initStatus = sodaInstance.nativeInit(sodaHandle, configBytes);
// initStatus 非空通常表示初始化成功
```

### 5. 启动采集会话
```java
byte[] startParamsBytes = buildStartParamsProtoBytes(...); // 构建启动参数 protobuf
byte[] startStatus = sodaInstance.nativeStartCapture(sodaHandle, startParamsBytes);
```

### 6. 持续投喂音频数据
```java
ByteBuffer audioBuffer = ByteBuffer.allocateDirect(frameBytes);

// 无时间戳方式
while (hasMoreAudio) {
    // 填充 audioBuffer
    boolean success = sodaInstance.nativeAddAudio(sodaHandle, audioBuffer, frameBytes);
}

// 带时间戳方式
ByteBuffer tsMetaBuffer = ByteBuffer.allocateDirect(16); // 16字节时间戳元数据
while (hasMoreAudio) {
    // 填充 audioBuffer 和 tsMetaBuffer
    boolean success = sodaInstance.nativeAddTimestampedAudio(
        sodaHandle, audioBuffer, frameBytes, tsMetaBuffer, 16);
}
```

### 7. 结束采集
```java
sodaInstance.nativeStopCapture(sodaHandle);
```

### 8. 释放资源
```java
// 删除 SODA 实例
sodaInstance.nativeDelete(sodaHandle);

// 释放共享资源（根据业务需求选择）
// 方式1: 条件删除（如果还有说话人分离处理器则保留）
boolean deleted = sodaInstance.nativeDeleteSharedResourcesIfDiarizationProcessorAbsent(sharedResHandle);

// 方式2: 直接删除
boolean deleted = Soda.nativeDeleteSharedResources(this, sharedResHandle);
```

## 参数与缓冲要求

### 句柄参数
- `sharedResHandle`: 共享资源句柄，由 `nativeCreateSharedResources` 返回
- `sodaHandle`: SODA 实例句柄，由 `nativeConstruct` 返回
- 这些句柄为 native 层返回的 `long` 类型指针，需要按生命周期正确传递和管理

### 配置参数
- `configBytes`: 识别配置的 protobuf 序列化二进制数据
- `startParamsBytes`: 启动参数的 protobuf 序列化二进制数据
- 必须与 native 侧的 protobuf 定义版本匹配

### 音频缓冲区
- 使用 `ByteBuffer.allocateDirect(...)` 创建直接缓冲区，避免数据拷贝
- `byteCount`: 本次提交的音频字节数，应与缓冲区的实际可读长度一致
- 音频格式需与配置中的采样率、声道数、编码格式匹配

### 时间戳元数据
- 用于 `nativeAddTimestampedAudio` 方法
- 通常为 16 字节的直接缓冲区，包含两个 `long` 值
- 字节序需与实现保持一致（注意代码中的 `Long.reverseBytes(...)` 处理）
- `tsMetaByteCount` 通常为 16

## 完整示例代码

```java
public class SodaRecognitionExample {
    private long sharedResHandle;
    private long sodaHandle;
    private Soda sodaInstance;
    
    public void initializeRecognition() {
        // 1. 创建共享资源
        sharedResHandle = Soda.nativeCreateSharedResources(this);
        
        // 2. 构造 SODA 实例
        sodaHandle = sodaInstance.nativeConstruct(sharedResHandle);
        
        // 3. 设置数据提供器（可选）
        SodaDataProviderJni dataProvider = new SodaDataProviderJni(provider);
        sodaInstance.nativeSetDataProvider(sodaHandle, dataProvider);
        
        // 4. 初始化引擎
        byte[] configBytes = buildRecognitionConfig();
        byte[] initResult = sodaInstance.nativeInit(sodaHandle, configBytes);
        if (initResult == null) {
            throw new RuntimeException("Failed to initialize SODA");
        }
        
        // 5. 启动采集
        byte[] startParams = buildStartParams();
        byte[] startResult = sodaInstance.nativeStartCapture(sodaHandle, startParams);
        if (startResult == null) {
            throw new RuntimeException("Failed to start capture");
        }
    }
    
    public void processAudio(byte[] audioData) {
        ByteBuffer audioBuffer = ByteBuffer.allocateDirect(audioData.length);
        audioBuffer.put(audioData);
        audioBuffer.flip();
        
        boolean success = sodaInstance.nativeAddAudio(sodaHandle, audioBuffer, audioData.length);
        if (!success) {
            Log.w("SODA", "Failed to add audio data");
        }
    }
    
    public void stopRecognition() {
        // 停止采集
        sodaInstance.nativeStopCapture(sodaHandle);
        
        // 释放资源
        sodaInstance.nativeDelete(sodaHandle);
        Soda.nativeDeleteSharedResources(this, sharedResHandle);
    }
    
    private byte[] buildRecognitionConfig() {
        // 构建识别配置 protobuf
        // 具体实现需要根据 protobuf 定义来构建
        return new byte[0]; // 占位符
    }
    
    private byte[] buildStartParams() {
        // 构建启动参数 protobuf
        // 具体实现需要根据 protobuf 定义来构建
        return new byte[0]; // 占位符
    }
}
```

## 回调处理

### 数据响应回调
```java
public class SodaDataProviderJni {
    public native void nativeHandleDataResponse(long reqId, long token, byte[] payload);
    
    // 在 native 层会调用此方法来回传识别结果
    // reqId: 请求标识符
    // token: 令牌
    // payload: 识别结果数据（protobuf 序列化）
}
```

## 注意事项

### 性能优化
- **使用直接缓冲区**: 始终使用 `ByteBuffer.allocateDirect()` 创建音频缓冲区，避免 JVM 堆内存拷贝
- **批量处理**: 合理设置音频帧大小，平衡延迟和吞吐量
- **线程安全**: 避免并发调用同一 SODA 实例的方法

### 错误处理
- **句柄验证**: 确保句柄有效后再调用相关方法
- **返回值检查**: 检查 native 方法的返回值，特别是初始化相关的方法
- **异常捕获**: 适当处理 UnsatisfiedLinkError 等 JNI 相关异常

### 资源管理
- **释放顺序**: 严格按照 `nativeStopCapture` → `nativeDelete` → 删除共享资源的顺序释放
- **生命周期**: 确保 SODA 实例的生命周期与使用场景匹配
- **内存泄漏**: 及时释放不再使用的句柄和缓冲区

### 配置管理
- **版本兼容**: 确保 protobuf 配置与 native 库版本兼容
- **参数验证**: 验证音频格式参数（采样率、声道数等）与配置一致
- **动态配置**: 使用 `nativeRequiresReinitialization` 检查配置变化

## 故障排除

### 常见问题
1. **初始化失败**: 检查配置 protobuf 格式和版本
2. **音频处理失败**: 验证音频格式和缓冲区大小
3. **内存泄漏**: 确保按正确顺序释放所有资源
4. **性能问题**: 使用直接缓冲区和合适的帧大小

### 调试建议
- 启用详细日志记录
- 监控内存使用情况
- 验证 protobuf 序列化/反序列化
- 检查 native 库加载状态
