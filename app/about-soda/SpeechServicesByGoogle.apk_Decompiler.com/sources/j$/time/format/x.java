package j$.time.format;

import j$.time.chrono.n;
import j$.time.chrono.u;
import j$.time.temporal.a;
import j$.time.temporal.p;
import j$.util.concurrent.ConcurrentHashMap;
import java.text.DateFormatSymbols;
import java.text.SimpleDateFormat;
import java.util.AbstractMap;
import java.util.Calendar;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Locale;
import java.util.TimeZone;

class x {
    private static final ConcurrentHashMap a = new ConcurrentHashMap(16, 0.75f, 2);
    /* access modifiers changed from: private */
    public static final Comparator b = new Object();
    private static final x c = new Object();
    public static final /* synthetic */ int d = 0;

    private static String b(String str) {
        return str.substring(0, Character.charCount(str.codePointAt(0)));
    }

    static x c() {
        return c;
    }

    public String d(n nVar, p pVar, long j, B b2, Locale locale) {
        if (nVar == u.d || !(pVar instanceof a)) {
            return e(pVar, j, b2, locale);
        }
        return null;
    }

    public String e(p pVar, long j, B b2, Locale locale) {
        AbstractMap.SimpleImmutableEntry simpleImmutableEntry;
        Object obj;
        AbstractMap.SimpleImmutableEntry simpleImmutableEntry2;
        p pVar2 = pVar;
        Locale locale2 = locale;
        AbstractMap.SimpleImmutableEntry simpleImmutableEntry3 = new AbstractMap.SimpleImmutableEntry(pVar2, locale2);
        ConcurrentHashMap concurrentHashMap = a;
        Object obj2 = concurrentHashMap.get(simpleImmutableEntry3);
        if (obj2 == null) {
            HashMap hashMap = new HashMap();
            int i = 0;
            if (pVar2 == a.ERA) {
                DateFormatSymbols instance = DateFormatSymbols.getInstance(locale);
                HashMap hashMap2 = new HashMap();
                HashMap hashMap3 = new HashMap();
                String[] eras = instance.getEras();
                while (i < eras.length) {
                    if (!eras[i].isEmpty()) {
                        long j2 = (long) i;
                        hashMap2.put(Long.valueOf(j2), eras[i]);
                        hashMap3.put(Long.valueOf(j2), b(eras[i]));
                    }
                    i++;
                }
                if (!hashMap2.isEmpty()) {
                    hashMap.put(B.FULL, hashMap2);
                    hashMap.put(B.SHORT, hashMap2);
                    hashMap.put(B.NARROW, hashMap3);
                }
                obj = new w(hashMap);
                simpleImmutableEntry = simpleImmutableEntry3;
            } else {
                String str = "";
                int i2 = 1;
                if (pVar2 == a.MONTH_OF_YEAR) {
                    DateFormatSymbols instance2 = DateFormatSymbols.getInstance(locale);
                    int length = instance2.getMonths().length;
                    LinkedHashMap linkedHashMap = new LinkedHashMap();
                    LinkedHashMap linkedHashMap2 = new LinkedHashMap();
                    LinkedHashMap linkedHashMap3 = new LinkedHashMap();
                    while (i2 < length) {
                        TimeZone timeZone = TimeZone.getTimeZone("UTC");
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("LLLL", locale2);
                        simpleDateFormat.setTimeZone(timeZone);
                        Calendar instance3 = Calendar.getInstance();
                        instance3.set(i, i2, i);
                        String format = simpleDateFormat.format(instance3.getTime());
                        long j3 = (long) i2;
                        linkedHashMap.put(Long.valueOf(j3), format);
                        AbstractMap.SimpleImmutableEntry simpleImmutableEntry4 = simpleImmutableEntry3;
                        linkedHashMap2.put(Long.valueOf(j3), format.substring(0, Character.charCount(format.codePointAt(0))));
                        TimeZone timeZone2 = TimeZone.getTimeZone("UTC");
                        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("LLL", locale2);
                        simpleDateFormat2.setTimeZone(timeZone2);
                        Calendar instance4 = Calendar.getInstance();
                        instance4.set(0, i2, 0);
                        linkedHashMap3.put(Long.valueOf(j3), simpleDateFormat2.format(instance4.getTime()));
                        i2++;
                        str = str;
                        simpleImmutableEntry3 = simpleImmutableEntry4;
                        i = 0;
                    }
                    simpleImmutableEntry2 = simpleImmutableEntry3;
                    String str2 = str;
                    if (length > 0) {
                        long j4 = (long) length;
                        String str3 = str2;
                        linkedHashMap.put(Long.valueOf(j4), str3);
                        linkedHashMap2.put(Long.valueOf(j4), str3);
                        linkedHashMap3.put(Long.valueOf(j4), str3);
                        hashMap.put(B.FULL_STANDALONE, linkedHashMap);
                        hashMap.put(B.NARROW_STANDALONE, linkedHashMap2);
                        hashMap.put(B.SHORT_STANDALONE, linkedHashMap3);
                    }
                    HashMap hashMap4 = new HashMap();
                    HashMap hashMap5 = new HashMap();
                    String[] months = instance2.getMonths();
                    for (int i3 = 0; i3 < months.length; i3++) {
                        if (!months[i3].isEmpty()) {
                            long j5 = ((long) i3) + 1;
                            hashMap4.put(Long.valueOf(j5), months[i3]);
                            hashMap5.put(Long.valueOf(j5), b(months[i3]));
                        }
                    }
                    if (!hashMap4.isEmpty()) {
                        hashMap.put(B.FULL, hashMap4);
                        hashMap.put(B.NARROW, hashMap5);
                    }
                    HashMap hashMap6 = new HashMap();
                    String[] shortMonths = instance2.getShortMonths();
                    for (int i4 = 0; i4 < shortMonths.length; i4++) {
                        if (!shortMonths[i4].isEmpty()) {
                            hashMap6.put(Long.valueOf(((long) i4) + 1), shortMonths[i4]);
                        }
                    }
                    if (!hashMap6.isEmpty()) {
                        hashMap.put(B.SHORT, hashMap6);
                    }
                    obj = new w(hashMap);
                } else {
                    simpleImmutableEntry2 = simpleImmutableEntry3;
                    String str4 = str;
                    if (pVar2 == a.DAY_OF_WEEK) {
                        DateFormatSymbols instance5 = DateFormatSymbols.getInstance(locale);
                        HashMap hashMap7 = new HashMap();
                        String[] weekdays = instance5.getWeekdays();
                        hashMap7.put(1L, weekdays[2]);
                        hashMap7.put(2L, weekdays[3]);
                        hashMap7.put(3L, weekdays[4]);
                        hashMap7.put(4L, weekdays[5]);
                        hashMap7.put(5L, weekdays[6]);
                        hashMap7.put(6L, weekdays[7]);
                        hashMap7.put(7L, weekdays[1]);
                        hashMap.put(B.FULL, hashMap7);
                        HashMap hashMap8 = new HashMap();
                        hashMap8.put(1L, b(weekdays[2]));
                        hashMap8.put(2L, b(weekdays[3]));
                        hashMap8.put(3L, b(weekdays[4]));
                        hashMap8.put(4L, b(weekdays[5]));
                        hashMap8.put(5L, b(weekdays[6]));
                        hashMap8.put(6L, b(weekdays[7]));
                        hashMap8.put(7L, b(weekdays[1]));
                        hashMap.put(B.NARROW, hashMap8);
                        HashMap hashMap9 = new HashMap();
                        String[] shortWeekdays = instance5.getShortWeekdays();
                        hashMap9.put(1L, shortWeekdays[2]);
                        hashMap9.put(2L, shortWeekdays[3]);
                        hashMap9.put(3L, shortWeekdays[4]);
                        hashMap9.put(4L, shortWeekdays[5]);
                        hashMap9.put(5L, shortWeekdays[6]);
                        hashMap9.put(6L, shortWeekdays[7]);
                        hashMap9.put(7L, shortWeekdays[1]);
                        hashMap.put(B.SHORT, hashMap9);
                        obj = new w(hashMap);
                    } else if (pVar2 == a.AMPM_OF_DAY) {
                        DateFormatSymbols instance6 = DateFormatSymbols.getInstance(locale);
                        HashMap hashMap10 = new HashMap();
                        HashMap hashMap11 = new HashMap();
                        String[] amPmStrings = instance6.getAmPmStrings();
                        for (int i5 = 0; i5 < amPmStrings.length; i5++) {
                            if (!amPmStrings[i5].isEmpty()) {
                                long j6 = (long) i5;
                                hashMap10.put(Long.valueOf(j6), amPmStrings[i5]);
                                hashMap11.put(Long.valueOf(j6), b(amPmStrings[i5]));
                            }
                        }
                        if (!hashMap10.isEmpty()) {
                            hashMap.put(B.FULL, hashMap10);
                            hashMap.put(B.SHORT, hashMap10);
                            hashMap.put(B.NARROW, hashMap11);
                        }
                        obj = new w(hashMap);
                    } else {
                        obj = str4;
                    }
                }
                simpleImmutableEntry = simpleImmutableEntry2;
            }
            concurrentHashMap.putIfAbsent(simpleImmutableEntry, obj);
            obj2 = concurrentHashMap.get(simpleImmutableEntry);
        }
        if (obj2 instanceof w) {
            return ((w) obj2).a(j, b2);
        }
        return null;
    }
}
