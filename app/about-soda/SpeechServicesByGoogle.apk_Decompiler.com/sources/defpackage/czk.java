package defpackage;

import android.net.Uri;
import java.util.HashMap;
import java.util.Iterator;
import java.util.concurrent.Executor;

/* renamed from: czk  reason: default package */
/* compiled from: PG */
public final class czk implements foz, diw, czm {
    private final HashMap a = new HashMap();

    public czk(Executor executor) {
        new hmq(executor);
    }

    public final foy a(Uri uri) {
        return b(uri);
    }

    public final foy b(Uri uri) {
        synchronized (czk.class) {
            if (this.a.get(uri) == null) {
                return null;
            }
            foy foy = (foy) this.a.get(uri);
            return foy;
        }
    }

    public final void e() {
        synchronized (czk.class) {
            Iterator it = this.a.values().iterator();
            if (it.hasNext()) {
                czj czj = (czj) it.next();
                throw null;
            }
        }
    }

    public final void c() {
    }

    public final void d() {
    }

    public final void f() {
    }
}
