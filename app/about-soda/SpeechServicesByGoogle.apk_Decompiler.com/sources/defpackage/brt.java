package defpackage;

import android.text.TextUtils;
import java.util.Locale;

/* renamed from: brt  reason: default package */
/* compiled from: PG */
public final class brt extends brw {
    public static final gxq a = gxq.j("brx", "cmn", "doi", "fil", "kok", "mai", "mni", "sat", "yue");

    static {
        int i = gxq.d;
    }

    public brt(String str) {
        super(str);
    }

    /* access modifiers changed from: protected */
    public final void a() {
        String language = this.b.getLanguage();
        if (language.length() == 2 || a.contains(language)) {
            String country = this.b.getCountry();
            if (country.length() == 2 || country.length() == 0 || TextUtils.isDigitsOnly(country)) {
                Locale locale = this.b;
                if (!brv.h(locale).equals(locale)) {
                    throw new IllegalArgumentException("Expected it to be normalized :(");
                }
                return;
            }
            throw new IllegalArgumentException("Expected an iso2 country code but got:".concat(String.valueOf(this.b.getCountry())));
        }
        throw new IllegalArgumentException("Expected an iso2 language code but got:".concat(String.valueOf(this.b.getLanguage())));
    }

    public brt(String str, String str2) {
        super(str, str2);
    }

    public brt(String str, String str2, String str3) {
        super(str, str2, str3);
    }

    public brt(Locale locale) {
        super(locale);
    }
}
