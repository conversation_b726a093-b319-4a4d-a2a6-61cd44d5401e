package defpackage;

/* renamed from: bfe  reason: default package */
/* compiled from: PG */
public final class bfe extends bfd {
    public bfe(bft bft) {
        super(bft);
    }

    public final boolean b(bhe bhe) {
        jnu.e(bhe, "workSpec");
        return bhe.k.d;
    }

    public final int d() {
        return 6;
    }

    public final /* bridge */ /* synthetic */ boolean e(Object obj) {
        if (!((Boolean) obj).booleanValue()) {
            return true;
        }
        return false;
    }
}
