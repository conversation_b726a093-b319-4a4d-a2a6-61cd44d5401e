package defpackage;

import java.util.Arrays;

/* renamed from: cqs  reason: default package */
/* compiled from: PG */
public class cqs {
    private final int a;

    public cqs(int i) {
        this.a = i;
    }

    public final boolean equals(Object obj) {
        if ((obj instanceof cqs) && this.a == ((cqs) obj).a) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return Arrays.hashCode(new int[]{this.a});
    }

    public final String toString() {
        return "java_hash=" + this.a;
    }

    public void a() {
    }
}
