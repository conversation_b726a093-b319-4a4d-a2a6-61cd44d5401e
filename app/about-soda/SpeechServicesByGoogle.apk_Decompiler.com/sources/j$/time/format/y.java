package j$.time.format;

import j$.util.concurrent.ConcurrentHashMap;

public final class y {
    public static final y a = new Object();

    /* JADX WARNING: type inference failed for: r0v0, types: [j$.time.format.y, java.lang.Object] */
    static {
        new ConcurrentHashMap(16, 0.75f, 2);
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof y)) {
            return false;
        }
        ((y) obj).getClass();
        return true;
    }

    public final int hashCode() {
        return 182;
    }

    public final String toString() {
        return "DecimalStyle[0+-.]";
    }
}
