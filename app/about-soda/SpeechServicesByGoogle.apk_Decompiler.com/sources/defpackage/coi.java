package defpackage;

import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/* renamed from: coi  reason: default package */
/* compiled from: PG */
public final class coi extends hlr implements hmi {
    public static final /* synthetic */ int b = 0;
    public final hmi a;
    private final hmh c;

    public coi(hmh hmh, hmi hmi) {
        this.c = hmh;
        this.a = hmi;
    }

    public final /* synthetic */ Object a() {
        return this.c;
    }

    /* renamed from: b */
    public final hmg schedule(Runnable runnable, long j, TimeUnit timeUnit) {
        hmf hmf = new hmf(runnable);
        if (j <= 0) {
            return new coh(this.c.be(runnable), System.nanoTime());
        }
        return new cog(hmf, this.a.b(new ckm((Object) this, (Object) hmf, 8), j, timeUnit));
    }

    /* renamed from: c */
    public final hmg schedule(Callable callable, long j, TimeUnit timeUnit) {
        if (j <= 0) {
            return new coh(this.c.bf(callable), System.nanoTime());
        }
        hmf hmf = new hmf(callable);
        return new cog(hmf, this.a.b(new ckm((Object) this, (Object) hmf, 9), j, timeUnit));
    }

    /* renamed from: d */
    public final hmg scheduleAtFixedRate(Runnable runnable, long j, long j2, TimeUnit timeUnit) {
        hmq hmq = new hmq(this);
        hmr hmr = new hmr();
        Runnable runnable2 = runnable;
        return new cog(hmr, this.a.d(new coe(hmq, runnable, hmr), j, j2, timeUnit));
    }

    /* renamed from: e */
    public final hmg scheduleWithFixedDelay(Runnable runnable, long j, long j2, TimeUnit timeUnit) {
        hmr hmr = new hmr();
        cog cog = new cog(hmr, (hmg) null);
        long j3 = j;
        cog.a = this.a.b(new cof(this, runnable, hmr, cog, j2, timeUnit), j, timeUnit);
        return cog;
    }

    public final hmh f() {
        return this.c;
    }

    public final /* synthetic */ ExecutorService g() {
        return this.c;
    }
}
