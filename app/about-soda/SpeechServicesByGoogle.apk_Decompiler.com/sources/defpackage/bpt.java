package defpackage;

import android.content.Context;
import android.util.Log;
import j$.util.Collection;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;

/* renamed from: bpt  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bpt implements hko {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    private final /* synthetic */ int c;

    public /* synthetic */ bpt(Object obj, Object obj2, int i) {
        this.c = i;
        this.b = obj;
        this.a = obj2;
    }

    /* JADX WARNING: type inference failed for: r0v49, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v84, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v97, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v27, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r6v30, types: [cvz, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v110, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v31, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r6v33, types: [java.util.Set, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v29, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r11v26, types: [java.lang.Object, cxa] */
    /* JADX WARNING: type inference failed for: r3v31, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v33, types: [cvz, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v32, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v116, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v43, types: [java.lang.Object, hko] */
    /* JADX WARNING: type inference failed for: r0v165, types: [java.util.concurrent.Future, java.lang.Object] */
    public final hme a(Object obj) {
        Object obj2;
        int i = 0;
        switch (this.c) {
            case 0:
                bpx bpx = (bpx) obj;
                return bpx.b.d(kq.f(new bbd(this.b, bpx, this.a, 2, (byte[]) null)), new bps(0));
            case 1:
                Integer num = (Integer) obj;
                int intValue = num.intValue();
                Object obj3 = this.b;
                if (intValue == 0) {
                    bpp bpp = new bpp(1, 0, "Feature is unavailable.", (Throwable) null);
                    ((bpy) obj3).e.execute(new bpw(1));
                    return hfc.J(bpp);
                } else if (intValue == 1 || intValue == 2) {
                    Object obj4 = this.a;
                    bpy bpy = (bpy) obj3;
                    bpm bpm = bpy.b;
                    bqa bqa = (bqa) bpm;
                    return hke.g(bqa.a(), new bsx((Object) bpm, (Object) bpy.c, obj4, 1), bqa.d);
                } else if (intValue != 3) {
                    Objects.toString(num);
                    return hfc.J(new bpp(1, 0, "Unexpected feature status: ".concat(String.valueOf(num)), (Throwable) null));
                } else {
                    bpy bpy2 = (bpy) obj3;
                    bpq bpq = bpy2.c;
                    Log.i(bpy.a, "Feature " + bpq.a + " is downloaded and ready.");
                    bpy2.e.execute(new bpw(0));
                    return hma.a;
                }
            case 2:
                if (((dcb) obj) == null) {
                    return hfc.K("");
                }
                Object obj5 = this.b;
                Object obj6 = this.a;
                bsk bsk = (bsk) obj5;
                bty g = bsk.g();
                return hfc.P(gof.c(new cyy(((btj) obj6).e, g.b, g, bsk.q(), bsk.f(), bsk.e(), 1)), btf.b);
            case 3:
                dcb dcb = (dcb) obj;
                String b2 = ((bat) this.b).b("locale_to_sync");
                if (ftd.p(b2)) {
                    return hfc.K(new bbi());
                }
                Object obj7 = this.a;
                Locale forLanguageTag = Locale.forLanguageTag(b2);
                buc buc = (buc) obj7;
                Locale a2 = buc.d.a(forLanguageTag);
                if (a2 != null) {
                    forLanguageTag = a2;
                }
                if (forLanguageTag.equals(Locale.US)) {
                    return hfc.K(new bbi());
                }
                bty a3 = buc.e.a(new brt(brv.h(forLanguageTag)));
                if (a3 == null) {
                    return hfc.K(new bbi());
                }
                return hke.g(hly.q(buc.c.k(a3)), new bub(0), buc.b);
            case 4:
                ? r0 = this.a;
                ext ext = (ext) obj;
                gxv gxv = haq.a;
                try {
                    gxv = (gxv) hfc.S(r0);
                } catch (ExecutionException e) {
                    ((hdc) ((hdc) ((hdc) byr.a.h()).i(e)).j("com/google/android/apps/speech/tts/googletts/settings/asr/dataservice/impl/LanguagePackSettingsDataServiceImpl", "getInstalledLocalesSource", 193, "LanguagePackSettingsDataServiceImpl.java")).r("Language names could not be read properly! The settings page may not have readable language names.");
                }
                byr byr = (byr) this.b;
                return ftd.K(hfc.Q((gxq) Collection.EL.stream(fbi.e(gyo.n(ext.c))).filter(new boa(14)).map(new byq(byr, (gxv) Collection.EL.stream(fbi.e(gyo.n(ext.a))).collect(gvx.a(new bpf(13), new bpf(10))), ext, gxv)).collect(gvx.a)), new amv(6), byr.b);
            case 5:
                crw crw = (crw) obj;
                return ((cuf) this.b).j.h(((cuv) this.a).a);
            case 6:
                cvc cvc = (cvc) obj;
                int b3 = cvc.b() - 1;
                if (b3 == 1) {
                    return cvc.c();
                }
                if (b3 == 2) {
                    return hfc.K(cvc.a());
                }
                Object obj8 = this.b;
                htk l = ctg.g.l();
                if (!l.b.B()) {
                    l.u();
                }
                csk csk = (csk) obj8;
                String str = csk.a;
                Object obj9 = this.a;
                ctg ctg = (ctg) l.b;
                str.getClass();
                ctg.a = 1 | ctg.a;
                ctg.b = str;
                cuf cuf = (cuf) obj9;
                String packageName = cuf.a.getPackageName();
                if (!l.b.B()) {
                    l.u();
                }
                ctg ctg2 = (ctg) l.b;
                packageName.getClass();
                ctg2.a |= 2;
                ctg2.c = packageName;
                ctg ctg3 = (ctg) l.r();
                if (csk.g.f()) {
                    if (cuf.e.f()) {
                        ((czp) cuf.e.b()).g(str, (csl) csk.g.b());
                    } else {
                        kml a4 = csi.a();
                        a4.b = csh.DOWNLOAD_MONITOR_NOT_PROVIDED_ERROR;
                        a4.c = "downloadFileGroup: DownloadListener is present but Download Monitor is not provided!";
                        return hfc.J(a4.a());
                    }
                }
                try {
                    if (((csk) obj8).f.f()) {
                        obj2 = grh.h(cqh.R((csf) ((csk) obj8).f.b()));
                    } else {
                        obj2 = gqd.a;
                    }
                    cuv a5 = cuv.a(csk.a);
                    hmf hmf = new hmf((Callable) new ctw(2));
                    czw f = czw.e(hmf).g(new bsx(obj9, (Object) ctg3, obj2, 7), cuf.d).g(new bsx(obj9, ctg3, obj8, 8), cuf.d).f(new amv(12), cuf.d);
                    czw g2 = czw.e(cuf.j.e(a5.a, f)).g(new bpt(hmf, f, 10, (byte[]) null), cuf.d);
                    czw g3 = g2.g(new bpt(obj9, a5, 5), cuf.d).g(new cvd(obj9, (Object) g2, obj8, (Object) str, 1), cuf.d);
                    ftd.M(g3, new emb(cuf, csk, str, a5, 1), cuf.d);
                    return g3;
                } catch (hui e2) {
                    return hfc.J(e2);
                }
            case 7:
                Void voidR = (Void) obj;
                ((hmf) this.a).run();
                return this.b;
            case 8:
                gxq gxq = (gxq) obj;
                hme K = hfc.K(new gxl());
                int size = gxq.size();
                while (true) {
                    Object obj10 = this.a;
                    if (i >= size) {
                        return ftd.K(K, new amv(9), ((cuf) obj10).d);
                    }
                    K = ftd.L(K, new bsx(obj10, (cxg) gxq.get(i), this.b, 6, (byte[]) null), ((cuf) obj10).d);
                    i++;
                }
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                cuf cuf2 = (cuf) this.a;
                return ftd.K(cuf2.c.e((ctg) this.b, true), new brg((csx) obj, 9), cuf2.d);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                Void voidR2 = (Void) obj;
                ((hmf) this.a).run();
                return this.b;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                ArrayList arrayList = new ArrayList();
                ArrayList arrayList2 = new ArrayList();
                AtomicInteger atomicInteger = new AtomicInteger(0);
                ArrayList arrayList3 = new ArrayList();
                Iterator it = ((List) obj).iterator();
                while (true) {
                    Object obj11 = this.b;
                    if (it.hasNext()) {
                        ? r6 = this.a;
                        ctj ctj = (ctj) it.next();
                        if (!r6.contains(ctj)) {
                            dmd dmd = (dmd) obj11;
                            arrayList3.add(ftd.L(dmd.g.e(ctj), new cvd(obj11, (Object) arrayList2, (Object) ctj, (Object) atomicInteger, 0), dmd.c));
                        } else {
                            dmd dmd2 = (dmd) obj11;
                            arrayList3.add(ftd.K(((dbw) dmd2.b).l(ctj), new brg(arrayList, 12), dmd2.c));
                        }
                    } else {
                        if (cqh.j()) {
                            dmd dmd3 = (dmd) obj11;
                            arrayList3.add(ftd.K(ftd.K(dmd3.e.c(), new btb(obj11, new ArrayList(), 3), dmd3.c), new brg(arrayList, 13), dmd3.c));
                        } else {
                            dmd dmd4 = (dmd) obj11;
                            arrayList.add(cqx.s((Context) dmd4.h, (grh) dmd4.d));
                        }
                        dmd dmd5 = (dmd) obj11;
                        return cqh.U(arrayList3).n(new ddo(dmd5, atomicInteger, (List) arrayList2, (List) arrayList, 1), dmd5.c);
                    }
                }
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                Void voidR3 = (Void) obj;
                Object obj12 = this.b;
                dmd dmd6 = (dmd) obj12;
                return ftd.L(dmd6.e.m(this.a), new bpr(obj12, 18), dmd6.c);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                ArrayList arrayList4 = new ArrayList();
                Iterator it2 = ((List) obj).iterator();
                while (true) {
                    Object obj13 = this.b;
                    if (!it2.hasNext()) {
                        return cqh.U(arrayList4).n(new ctw(7), ((cvy) obj13).e);
                    }
                    ctg ctg4 = (ctg) it2.next();
                    if (!ctg4.d.isEmpty()) {
                        if (!((gyo) this.a).contains(ctg4.d)) {
                            cvy cvy = (cvy) obj13;
                            arrayList4.add(cvy.q(cvy.c.g(ctg4), new cvn(obj13, (htq) ctg4, 6)));
                        }
                    }
                }
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                cwz cwz = (cwz) obj;
                cyh.h("%s: Encountered SharedFileMissingException for group: %s", "FileGroupManager", ((csx) this.b).c);
                ((cvy) this.a).b.a();
                return hfc.K(ctf.NONE);
            case 15:
                csx csx = (csx) obj;
                if (csx == null) {
                    Object obj14 = this.b;
                    kml a6 = csi.a();
                    a6.b = csh.GROUP_NOT_FOUND_ERROR;
                    a6.c = "Nothing to download for file group: ".concat(String.valueOf(((ctg) obj14).b));
                    return hfc.J(a6.a());
                }
                ((AtomicReference) this.a).set(csx);
                return hfc.K(csx);
            case 16:
                csx csx2 = (csx) obj;
                htq htq = (htq) this.b;
                htk htk = (htk) htq.C(5);
                htk.x(htq);
                if (!htk.b.B()) {
                    htk.u();
                }
                Object obj15 = this.a;
                ctg ctg5 = (ctg) htk.b;
                ctg ctg6 = ctg.g;
                ctg5.a |= 8;
                ctg5.e = false;
                return ((cvy) obj15).c.l((ctg) htk.r(), csx2);
            case 17:
                csx csx3 = (csx) obj;
                if (csx3 == null) {
                    return hma.a;
                }
                return this.a.a(new cxg((ctg) this.b, csx3));
            case 18:
                if (((csx) obj) == null) {
                    return hma.a;
                }
                Object obj16 = this.b;
                Object obj17 = this.a;
                ctg ctg7 = (ctg) obj16;
                cyh.e("%s: Deleting file group %s for uninstalled app %s", "FileGroupManager", ctg7.b, ctg7.c);
                cvy cvy2 = (cvy) obj17;
                cvy2.i.d(1049);
                return cvy2.q(cvy2.c.i(ctg7), new cvi(obj17, 4));
            case 19:
                Void voidR4 = (Void) obj;
                return ((cvy) this.a).h.c(((csx) this.b).c);
            default:
                Void voidR5 = (Void) obj;
                csx csx4 = (csx) hfc.S(this.a);
                if (csx4 == null) {
                    return hma.a;
                }
                Object obj18 = this.b;
                htk l2 = hig.k.l();
                String str2 = csx4.c;
                if (!l2.b.B()) {
                    l2.u();
                }
                htq htq2 = l2.b;
                hig hig = (hig) htq2;
                str2.getClass();
                hig.a |= 1;
                hig.b = str2;
                String str3 = csx4.d;
                if (!htq2.B()) {
                    l2.u();
                }
                htq htq3 = l2.b;
                hig hig2 = (hig) htq3;
                str3.getClass();
                hig2.a = 4 | hig2.a;
                hig2.d = str3;
                int i2 = csx4.e;
                if (!htq3.B()) {
                    l2.u();
                }
                htq htq4 = l2.b;
                hig hig3 = (hig) htq4;
                hig3.a |= 2;
                hig3.c = i2;
                long j = csx4.r;
                if (!htq4.B()) {
                    l2.u();
                }
                htq htq5 = l2.b;
                hig hig4 = (hig) htq5;
                hig4.a |= 64;
                hig4.h = j;
                String str4 = csx4.s;
                if (!htq5.B()) {
                    l2.u();
                }
                hig hig5 = (hig) l2.b;
                str4.getClass();
                hig5.a |= 128;
                hig5.i = str4;
                hig hig6 = (hig) l2.r();
                csw csw = csx4.b;
                if (csw == null) {
                    csw = csw.i;
                }
                int i3 = csw.f;
                htk l3 = hit.c.l();
                if (!l3.b.B()) {
                    l3.u();
                }
                hit hit = (hit) l3.b;
                hit.a = 1 | hit.a;
                hit.b = i3;
                hit hit2 = (hit) l3.r();
                htk l4 = hii.u.l();
                if (!l4.b.B()) {
                    l4.u();
                }
                htq htq6 = l4.b;
                hii hii = (hii) htq6;
                hit2.getClass();
                hii.t = hit2;
                hii.c |= 2048;
                if (!htq6.B()) {
                    l4.u();
                }
                cvy cvy3 = (cvy) obj18;
                cyk cyk = cvy3.i;
                hii hii2 = (hii) l4.b;
                hig6.getClass();
                hii2.d = hig6;
                hii2.a |= 256;
                cyk.i(1117, l4, (long) cqh.o());
                return cvy3.h.a(gxq.q(csx4));
        }
    }

    public /* synthetic */ bpt(Object obj, Object obj2, int i, byte[] bArr) {
        this.c = i;
        this.a = obj;
        this.b = obj2;
    }
}
