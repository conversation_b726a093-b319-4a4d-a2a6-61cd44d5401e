package defpackage;

/* renamed from: eos  reason: default package */
/* compiled from: PG */
public final class eos {
    public final ehg a;
    public final int b;
    public final jmp c;
    public final Object d;

    public eos(ehg ehg, int i, jmp jmp, Object obj) {
        jnu.e(ehg, "client");
        this.a = ehg;
        this.b = i;
        this.c = jmp;
        this.d = obj;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof eos)) {
            return false;
        }
        eos eos = (eos) obj;
        if (jnu.i(this.a, eos.a) && this.b == eos.b && jnu.i(this.c, eos.c) && jnu.i(this.d, eos.d)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        int i2;
        ehg ehg = this.a;
        if (ehg.B()) {
            i = ehg.i();
        } else {
            int i3 = ehg.memoizedHashCode;
            if (i3 == 0) {
                i3 = ehg.i();
                ehg.memoizedHashCode = i3;
            }
            i = i3;
        }
        int hashCode = (((i * 31) + this.b) * 31) + this.c.hashCode();
        Object obj = this.d;
        if (obj == null) {
            i2 = 0;
        } else {
            i2 = obj.hashCode();
        }
        return (hashCode * 31) + i2;
    }

    public final String toString() {
        return "SessionData(client=" + this.a + ", sessionToken=" + this.b + ", stopped=" + this.c + ", params=" + this.d + ")";
    }
}
