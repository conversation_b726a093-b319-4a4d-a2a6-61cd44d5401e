package defpackage;

/* renamed from: eoo  reason: default package */
/* compiled from: PG */
public final class eoo implements eon {
    public final eow a;
    public final ebn b;
    public final int c;
    public final jrz d;
    private final ebl e;
    private final boolean f;

    public eoo(ebl ebl, eow eow, ebn ebn, int i, boolean z, jrz jrz) {
        jnu.e(ebl, "session");
        jnu.e(eow, "route");
        jnu.e(ebn, "params");
        this.e = ebl;
        this.a = eow;
        this.b = ebn;
        this.c = i;
        this.f = z;
        this.d = jrz;
    }

    public final int a() {
        return this.c;
    }

    public final ebl b() {
        return this.e;
    }

    public final ebn c() {
        return this.b;
    }

    public final eow d() {
        return this.a;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof eoo)) {
            return false;
        }
        eoo eoo = (eoo) obj;
        if (jnu.i(this.e, eoo.e) && jnu.i(this.a, eoo.a) && jnu.i(this.b, eoo.b) && this.c == eoo.c && this.f == eoo.f && jnu.i(this.d, eoo.d)) {
            return true;
        }
        return false;
    }

    public final /* synthetic */ hme f() {
        return cqx.S(this);
    }

    public final /* synthetic */ Object g(jlr jlr) {
        return cqx.T(this, jlr);
    }

    public final int hashCode() {
        int i;
        int hashCode = (this.e.hashCode() * 31) + this.a.hashCode();
        ebn ebn = this.b;
        if (ebn.B()) {
            i = ebn.i();
        } else {
            int i2 = ebn.memoizedHashCode;
            if (i2 == 0) {
                i2 = ebn.i();
                ebn.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (((((((hashCode * 31) + i) * 31) + this.c) * 31) + a.f(this.f)) * 31) + this.d.hashCode();
    }

    public final String toString() {
        return "HotwordSessionDataInternal(session=" + this.e + ", route=" + this.a + ", params=" + this.b + ", sessionToken=" + this.c + ", isInactive=" + this.f + ", routeDisconnectJob=" + this.d + ")";
    }
}
