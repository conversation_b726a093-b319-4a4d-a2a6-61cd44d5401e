package j$.time.format;

import j$.time.temporal.a;

final class i implements f {
    public final /* synthetic */ int a;
    private final Object b;

    public /* synthetic */ i(int i, Object obj) {
        this.a = i;
        this.b = obj;
    }

    private static void a(StringBuilder sb, int i) {
        sb.append((char) ((i / 10) + 48));
        sb.append((char) ((i % 10) + 48));
    }

    public final boolean g(u uVar, StringBuilder sb) {
        String str;
        switch (this.a) {
            case 0:
                Long e = uVar.e(a.OFFSET_SECONDS);
                if (e == null) {
                    return false;
                }
                sb.append("GMT");
                long longValue = e.longValue();
                int i = (int) longValue;
                if (longValue == ((long) i)) {
                    if (i != 0) {
                        int abs = Math.abs((i / 3600) % 100);
                        int abs2 = Math.abs((i / 60) % 60);
                        int abs3 = Math.abs(i % 60);
                        if (i < 0) {
                            str = "-";
                        } else {
                            str = "+";
                        }
                        sb.append(str);
                        if (((B) this.b) == B.FULL) {
                            a(sb, abs);
                            sb.append(':');
                            a(sb, abs2);
                            if (abs3 != 0) {
                                sb.append(':');
                                a(sb, abs3);
                            }
                        } else {
                            if (abs >= 10) {
                                sb.append((char) ((abs / 10) + 48));
                            }
                            sb.append((char) ((abs % 10) + 48));
                            if (!(abs2 == 0 && abs3 == 0)) {
                                sb.append(':');
                                a(sb, abs2);
                                if (abs3 != 0) {
                                    sb.append(':');
                                    a(sb, abs3);
                                }
                            }
                        }
                    }
                    return true;
                }
                throw new ArithmeticException();
            default:
                sb.append((String) this.b);
                return true;
        }
    }

    public final String toString() {
        switch (this.a) {
            case 0:
                String valueOf = String.valueOf((B) this.b);
                return "LocalizedOffset(" + valueOf + ")";
            default:
                String replace = ((String) this.b).replace("'", "''");
                return "'" + replace + "'";
        }
    }
}
