package j$.time.temporal;

import j$.time.d;
import j$.time.g;

enum f implements p {
    ;
    
    /* access modifiers changed from: private */
    public static final int[] a = null;

    static {
        a = new int[]{0, 90, 181, 273, 0, 91, 182, 274};
    }

    static int I(g gVar) {
        int ordinal = gVar.M().ordinal();
        int i = 1;
        int N = gVar.N() - 1;
        int i2 = (3 - ordinal) + N;
        int i3 = i2 - ((i2 / 7) * 7);
        int i4 = i3 - 3;
        if (i4 < -3) {
            i4 = i3 + 4;
        }
        if (N < i4) {
            return (int) t.j(1, (long) N(M(gVar.d0(180).Z(-1)))).d();
        }
        int i5 = ((N - i4) / 7) + 1;
        if (i5 != 53 || i4 == -3 || (i4 == -2 && gVar.o())) {
            i = i5;
        }
        return i;
    }

    static t L(g gVar) {
        return t.j(1, (long) N(M(gVar)));
    }

    /* access modifiers changed from: private */
    public static int M(g gVar) {
        int P = gVar.P();
        int N = gVar.N();
        if (N <= 3) {
            if (N - gVar.M().ordinal() < -2) {
                return P - 1;
            }
            return P;
        } else if (N < 363) {
            return P;
        } else {
            if (((N - 363) - (gVar.o() ? 1 : 0)) - gVar.M().ordinal() >= 0) {
                return P + 1;
            }
            return P;
        }
    }

    /* access modifiers changed from: private */
    public static int N(int i) {
        g T = g.T(i, 1, 1);
        if (T.M() == d.THURSDAY) {
            return 53;
        }
        if (T.M() != d.WEDNESDAY || !T.o()) {
            return 52;
        }
        return 53;
    }

    public final boolean B() {
        return true;
    }
}
