package defpackage;

import java.util.concurrent.TimeUnit;

/* renamed from: enb  reason: default package */
/* compiled from: PG */
public final class enb implements ebl {
    private static final hca e = hca.m("com/google/android/libraries/search/audio/microphone/impl/HotwordListeningSessionImpl");
    public final ehg a;
    public final hme b;
    public final hme c;
    public final emd d;
    private final int f;
    private final hmi g;
    private final grh h;
    private final cyw i;

    public enb(int i2, ehg ehg, dyt dyt, hme hme, hme hme2, cyw cyw, hmi hmi, emd emd) {
        grh grh;
        this.f = i2;
        this.a = ehg;
        this.b = hme;
        this.c = hme2;
        this.i = cyw;
        this.g = hmi;
        this.d = emd;
        if ((dyt.a & 128) != 0) {
            grh = grh.h(hmi.b(new ww((Object) this, (Object) hme2, (Object) emd, (Object) ehg, 14), dyt.h, TimeUnit.MILLISECONDS));
        } else {
            grh = gqd.a;
        }
        this.h = grh;
    }

    public final ebk a() {
        return new ena(this);
    }

    public final ebk b() {
        return e(eam.CLIENT_REQUESTED);
    }

    public final hme c() {
        return this.b;
    }

    public final ekf d() {
        ((hby) ((hby) e.f().h(hdg.a, "ALT.HWCSession")).j("com/google/android/libraries/search/audio/microphone/impl/HotwordListeningSessionImpl", "stopListeningForSeamlessMode", 103, "HotwordListeningSessionImpl.java")).s("#audio# stop listening hotword client session(token(%d)) seamlessly", this.f);
        ekf M = this.i.M(this.f, eam.CLIENT_REQUESTED);
        this.g.c(new bdr((Object) this, 14), 10000, TimeUnit.MILLISECONDS);
        return M;
    }

    /* JADX WARNING: type inference failed for: r0v9, types: [java.util.concurrent.Future, java.lang.Object] */
    public final ebk e(eam eam) {
        ((hby) ((hby) e.f().h(hdg.a, "ALT.HWCSession")).j("com/google/android/libraries/search/audio/microphone/impl/HotwordListeningSessionImpl", "stopListening", 90, "HotwordListeningSessionImpl.java")).A("#audio# stop(reason(%s)) listening hotword client session(token(%d))", eam.name(), this.f);
        grh grh = this.h;
        ebk y = this.i.y(this.f, eam);
        if (grh.f()) {
            this.h.b().cancel(false);
        }
        return y;
    }
}
