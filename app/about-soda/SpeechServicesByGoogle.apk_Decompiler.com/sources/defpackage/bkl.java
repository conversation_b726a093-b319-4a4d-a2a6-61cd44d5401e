package defpackage;

import android.view.MotionEvent;
import android.view.View;
import android.widget.PopupWindow;
import com.android.car.ui.preference.CarUiTwoActionSwitchPreference;

/* renamed from: bkl  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bkl implements View.OnTouchListener {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ bkl(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    public final boolean onTouch(View view, MotionEvent motionEvent) {
        int i = this.b;
        if (i == 0) {
            return ((CarUiTwoActionSwitchPreference) this.a).m32lambda$onBindViewHolder$2$comandroidcaruipreferenceCarUiTwoActionSwitchPreference(view, motionEvent);
        }
        if (i != 1) {
            ((hby) ((hby) fja.a.c()).j("com/google/android/libraries/speech/transcription/voiceime/BackspaceViewPeer", "onTouch", 71, "BackspaceViewPeer.java")).u("#onTouch(%s)", motionEvent);
            int action = motionEvent.getAction();
            Object obj = this.a;
            if (action == 1) {
                ((fja) obj).a((hme) null);
            }
            ((fja) obj).d.onTouchEvent(motionEvent);
            return false;
        }
        int action2 = motionEvent.getAction();
        int x = (int) motionEvent.getX();
        int y = (int) motionEvent.getY();
        if (action2 == 0) {
            PopupWindow popupWindow = ((ju) this.a).q;
            if (popupWindow != null && popupWindow.isShowing() && x >= 0 && x < ((ju) this.a).q.getWidth() && y >= 0 && y < ((ju) this.a).q.getHeight()) {
                ju juVar = (ju) this.a;
                juVar.o.postDelayed(juVar.r, 250);
            }
        } else if (action2 == 1) {
            ju juVar2 = (ju) this.a;
            juVar2.o.removeCallbacks(juVar2.r);
        }
        return false;
    }

    public bkl(ju juVar, int i) {
        this.b = i;
        this.a = juVar;
    }
}
