package defpackage;

/* renamed from: bqi  reason: default package */
/* compiled from: PG */
public final class bqi extends htq implements hvb {
    public static final bqi e;
    private static volatile hvh g;
    public long a;
    public huf b = hvk.a;
    public htb c;
    public float d;
    private int f;

    static {
        bqi bqi = new bqi();
        e = bqi;
        htq.z(bqi.class, bqi);
    }

    private bqi() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(e, "\u0001\u0004\u0000\u0001\u0001\u0004\u0004\u0000\u0001\u0000\u0001ဃ\u0000\u0002\u001b\u0003ဉ\u0001\u0004ခ\u0002", new Object[]{"f", "a", "b", bqh.class, "c", "d"});
        } else if (i2 == 3) {
            return new bqi();
        } else {
            if (i2 == 4) {
                return new htk((htq) e);
            }
            if (i2 == 5) {
                return e;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = g;
            if (hvh == null) {
                synchronized (bqi.class) {
                    hvh = g;
                    if (hvh == null) {
                        hvh = new htl(e);
                        g = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
