package defpackage;

import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;

/* renamed from: bqf  reason: default package */
/* compiled from: PG */
public final class bqf extends bpy {
    public bqf(bpm bpm, bpq bpq, abq abq) {
        super(bpm, bpq, abq);
    }

    /* access modifiers changed from: protected */
    public final /* bridge */ /* synthetic */ Object b(boz boz) {
        bpe bpe;
        boy a = this.c.a();
        Parcel a2 = boz.a();
        box.c(a2, a);
        Parcel b = boz.b(27, a2);
        IBinder readStrongBinder = b.readStrongBinder();
        if (readStrongBinder == null) {
            bpe = null;
        } else {
            IInterface queryLocalInterface = readStrongBinder.queryLocalInterface("com.google.android.apps.aicore.aidl.IRosieRobotService");
            if (queryLocalInterface instanceof bpe) {
                bpe = (bpe) queryLocalInterface;
            } else {
                bpe = new bpe(readStrongBinder);
            }
        }
        b.recycle();
        return bpe;
    }
}
