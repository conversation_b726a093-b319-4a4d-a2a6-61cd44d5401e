package defpackage;

/* renamed from: csa  reason: default package */
/* compiled from: PG */
public final class csa extends htq implements hvb {
    public static final csa a;
    private static volatile hvh c;
    private huv b = huv.a;

    static {
        csa csa = new csa();
        a = csa;
        htq.z(csa.class, csa);
    }

    private csa() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(a, "\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u00012", new Object[]{"b", crz.a});
        } else if (i2 == 3) {
            return new csa();
        } else {
            if (i2 == 4) {
                return new htk((htq) a);
            }
            if (i2 == 5) {
                return a;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = c;
            if (hvh == null) {
                synchronized (csa.class) {
                    hvh = c;
                    if (hvh == null) {
                        hvh = new htl(a);
                        c = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
