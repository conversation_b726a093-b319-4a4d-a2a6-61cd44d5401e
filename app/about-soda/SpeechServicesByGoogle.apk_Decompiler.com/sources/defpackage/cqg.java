package defpackage;

import android.os.Process;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.LockSupport;

/* renamed from: cqg  reason: default package */
/* compiled from: PG */
public final class cqg {
    public final Thread a;
    public int b;
    public final boolean c;
    public boolean d;
    public boolean e;
    public final AtomicLong f;
    private final boolean g;

    public /* synthetic */ cqg(Thread thread, int i, boolean z, int i2, boolean z2, int i3) {
        boolean z3;
        jnu.e(thread, "thread");
        this.a = thread;
        this.b = (i3 & 2) != 0 ? -1 : i;
        boolean z4 = false;
        if ((i3 & 16) != 0) {
            z3 = true;
        } else {
            z3 = false;
        }
        this.c = z3;
        this.g = ((i3 & 32) == 0 ? true : z4) & z2;
        this.d = !z3;
        this.f = new AtomicLong(cqx.z(z, false, false, (i3 & 8) != 0 ? -21 : i2, -21, -21, 0));
    }

    private final void e(long j) {
        if (cqf.g(j)) {
            boolean z = false;
            while (true) {
                try {
                    LockSupport.park(this);
                    if (!cqf.f(this.f.get())) {
                        break;
                    }
                    z |= Thread.interrupted();
                } catch (Throwable th) {
                    if (z) {
                        this.a.interrupt();
                    }
                    throw th;
                }
            }
            if (z) {
                this.a.interrupt();
            }
        }
    }

    public final void a(int i) {
        long j;
        do {
            j = this.f.get();
            int d2 = cqf.d(j);
            if (!cqf.g(j)) {
                throw new IllegalStateException("Unexpected not set, saw " + ("State{started=" + cqf.h(j) + ", setting=" + cqf.g(j) + ", pool=" + cqf.c(j) + ", local=" + cqf.b(j) + ", inherited=" + cqf.a(j) + "}") + " " + Long.toBinaryString(j));
            } else if (!cqf.h(j)) {
                int a2 = cpt.a(d2);
                if (a2 != cpt.a(i)) {
                    this.a.setPriority(a2);
                }
            } else if (d2 != i) {
                Process.setThreadPriority(this.b, d2);
                i = d2;
            }
        } while (!this.f.compareAndSet(j, cqf.i(j, false, false, false, 0, 0, 0, 121)));
        if (cqf.f(j)) {
            LockSupport.unpark(this.a);
        }
    }

    public final void b() {
        long j;
        while (true) {
            j = this.f.get();
            long j2 = j;
            long i = cqf.i(j2, false, false, cqf.g(j) & (!this.g), 0, -21, cqf.e(j) + 1, 27);
            if (cqf.g(j)) {
                if (this.f.compareAndSet(j, i)) {
                    break;
                }
            } else if (cqf.d(j) == cqf.d(i)) {
                if (this.f.compareAndSet(j, i)) {
                    break;
                }
            } else if (this.f.compareAndSet(j, cqf.i(i, false, true, false, 0, 0, 0, 125))) {
                a(cqf.d(j));
                break;
            }
        }
        if (!this.g) {
            e(j);
        }
    }

    public final void c() {
        AtomicLong atomicLong;
        long j;
        do {
            atomicLong = this.f;
            j = atomicLong.get();
        } while (!atomicLong.compareAndSet(j, cqf.i(j, false, false, cqf.g(j), 0, 0, 0, 122)));
        e(j);
    }

    /* JADX WARNING: Removed duplicated region for block: B:0:0x0000 A[LOOP_START, MTH_ENTER_BLOCK] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void d() {
        /*
            r13 = this;
        L_0x0000:
            java.util.concurrent.atomic.AtomicLong r0 = r13.f
            long r11 = r0.get()
            boolean r0 = defpackage.cqf.f(r11)
            if (r0 == 0) goto L_0x0022
            java.util.concurrent.atomic.AtomicLong r0 = r13.f
            r8 = 0
            r10 = 123(0x7b, float:1.72E-43)
            r3 = 0
            r4 = 0
            r5 = 0
            r6 = 0
            r7 = 0
            r1 = r11
            long r1 = defpackage.cqf.i(r1, r3, r4, r5, r6, r7, r8, r10)
            boolean r0 = r0.compareAndSet(r11, r1)
            if (r0 == 0) goto L_0x0000
        L_0x0022:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cqg.d():void");
    }

    public final String toString() {
        String name = this.a.getName();
        int i = this.b;
        return name + " " + i;
    }
}
