package defpackage;

/* renamed from: etf  reason: default package */
/* compiled from: PG */
public final class etf extends htq implements hvb {
    public static final etf c;
    private static volatile hvh e;
    public int a = 0;
    public Object b;
    private byte d = 2;

    static {
        etf etf = new etf();
        c = etf;
        htq.z(etf.class, etf);
    }

    private etf() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return Byte.valueOf(this.d);
        }
        byte b2 = 1;
        if (i2 == 2) {
            Class<dyc> cls = dyc.class;
            return new hvl(c, "\u0004\u0007\u0001\u0000\u0001\n\u0007\u0000\u0000\u0001\u0001<\u0000\u0004<\u0000\u0006<\u0000\u0007<\u0000\bм\u0000\t<\u0000\n<\u0000", new Object[]{"b", "a", etc.class, dyv.class, cls, dzi.class, dyu.class, htc.class, cls});
        } else if (i2 == 3) {
            return new etf();
        } else {
            if (i2 == 4) {
                return new htk((htq) c);
            }
            if (i2 == 5) {
                return c;
            }
            if (i2 != 6) {
                if (obj == null) {
                    b2 = 0;
                }
                this.d = b2;
                return null;
            }
            hvh hvh = e;
            if (hvh == null) {
                synchronized (etf.class) {
                    hvh = e;
                    if (hvh == null) {
                        hvh = new htl(c);
                        e = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
