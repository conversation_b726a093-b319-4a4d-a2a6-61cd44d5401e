package defpackage;

/* renamed from: esj  reason: default package */
/* compiled from: PG */
public final class esj implements esh {
    private final dyz a;
    private final int b;
    private final ejn c;
    private final esg d;
    private final dzn e;
    private final boolean f = true;

    public esj(dyz dyz, int i, ejn ejn, esg esg, dzn dzn) {
        jnu.e(dyz, "session");
        jnu.e(ejn, "routeData");
        jnu.e(dzn, "params");
        this.a = dyz;
        this.b = i;
        this.c = ejn;
        this.d = esg;
        this.e = dzn;
    }

    public static /* synthetic */ esj h(esj esj, int i, esg esg, dzn dzn, int i2) {
        dyz dyz;
        ejn ejn = null;
        if ((i2 & 1) != 0) {
            dyz = esj.a;
        } else {
            dyz = null;
        }
        if ((i2 & 2) != 0) {
            i = esj.b;
        }
        int i3 = i;
        if ((i2 & 4) != 0) {
            ejn = esj.c;
        }
        ejn ejn2 = ejn;
        if ((i2 & 8) != 0) {
            esg = esj.d;
        }
        esg esg2 = esg;
        if ((i2 & 16) != 0) {
            dzn = esj.e;
        }
        dzn dzn2 = dzn;
        jnu.e(dyz, "session");
        jnu.e(ejn2, "routeData");
        jnu.e(dzn2, "params");
        return new esj(dyz, i3, ejn2, esg2, dzn2);
    }

    public final int a() {
        return this.b;
    }

    public final dyz b() {
        return this.a;
    }

    public final dzn c() {
        return this.e;
    }

    public final ejn d() {
        return this.c;
    }

    public final /* synthetic */ eow e() {
        return cqx.L(this);
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof esj)) {
            return false;
        }
        esj esj = (esj) obj;
        if (!jnu.i(this.a, esj.a) || this.b != esj.b || !jnu.i(this.c, esj.c) || !jnu.i(this.d, esj.d) || !jnu.i(this.e, esj.e)) {
            return false;
        }
        boolean z = esj.f;
        return true;
    }

    public final esg f() {
        return this.d;
    }

    public final boolean g() {
        return true;
    }

    public final int hashCode() {
        int i;
        int hashCode = (((((this.a.hashCode() * 31) + this.b) * 31) + this.c.hashCode()) * 31) + this.d.hashCode();
        dzn dzn = this.e;
        if (dzn.B()) {
            i = dzn.i();
        } else {
            int i2 = dzn.memoizedHashCode;
            if (i2 == 0) {
                i2 = dzn.i();
                dzn.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (((hashCode * 31) + i) * 31) + a.f(true);
    }

    public final String toString() {
        return "AudioRouteSessionDataSimple(session=" + this.a + ", routeToken=" + this.b + ", routeData=" + this.c + ", client=" + this.d + ", params=" + this.e + ", isInactive=true)";
    }
}
