package defpackage;

import java.util.HashMap;

/* renamed from: bjp  reason: default package */
/* compiled from: PG */
public final class bjp extends HashMap {
    public final int a;
    public final int b;

    public bjp(int i, int i2) {
        this.a = i;
        this.b = i2;
        if (i == 2 && i2 <= 0) {
            throw new IllegalArgumentException("Expiration time must be positive if CacheType is CACHE_TYPE_EXPIRED_AFTER_SOME_TIME");
        }
    }
}
