package defpackage;

import android.os.IInterface;
import android.os.Parcel;
import java.util.Locale;

/* renamed from: bpc  reason: default package */
/* compiled from: PG */
public final class bpc extends bow implements IInterface {
    final /* synthetic */ po a;
    public final /* synthetic */ bqa b;
    private final /* synthetic */ int c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bpc(bqa bqa, po poVar, int i) {
        super("com.google.android.apps.aicore.aidl.IDownloadListener2");
        this.c = i;
        this.a = poVar;
        this.b = bqa;
    }

    /* access modifiers changed from: protected */
    public final boolean y(int i, Parcel parcel, Parcel parcel2) {
        int i2 = 0;
        if (this.c != 0) {
            if (i == 2) {
                parcel.readString();
                parcel.readLong();
                box.b(parcel);
                bqa bqa = this.b;
                bqa.f.execute(new bpw(6));
                return true;
            } else if (i == 3) {
                parcel.readString();
                parcel.readLong();
                box.b(parcel);
                bqa bqa2 = this.b;
                bqa2.f.execute(new bpw(7));
                return true;
            } else if (i == 4) {
                String readString = parcel.readString();
                String readString2 = parcel.readString();
                box.b(parcel);
                bpp bpp = new bpp(1, 0, String.format(Locale.ENGLISH, "Feature %s failed with failure %s.", new Object[]{readString, readString2}), (Throwable) null);
                bqa bqa3 = this.b;
                bqa3.f.execute(new bpw(9));
                this.a.d(bpp);
                return true;
            } else if (i != 5) {
                return false;
            } else {
                parcel.readString();
                box.b(parcel);
                bqa bqa4 = this.b;
                bqa4.f.execute(new bpw(8));
                this.a.c((Object) null);
                return true;
            }
        } else if (i == 2) {
            parcel.readString();
            parcel.readLong();
            box.b(parcel);
            bqa bqa5 = this.b;
            bqa5.f.execute(new bpw(3));
            return true;
        } else if (i == 3) {
            parcel.readString();
            parcel.readLong();
            box.b(parcel);
            bqa bqa6 = this.b;
            bqa6.f.execute(new bpw(5));
            return true;
        } else if (i == 4) {
            String readString3 = parcel.readString();
            int readInt = parcel.readInt();
            String readString4 = parcel.readString();
            box.b(parcel);
            String format = String.format(Locale.ENGLISH, "Feature %s failed with failure status %d and error %s.", new Object[]{readString3, Integer.valueOf(readInt), readString4});
            if (readInt == 1) {
                i2 = 501;
            }
            bpp bpp2 = new bpp(1, i2, format, (Throwable) null);
            bqa bqa7 = this.b;
            bqa7.f.execute(new bpw(2));
            this.a.d(bpp2);
            return true;
        } else if (i != 5) {
            return false;
        } else {
            parcel.readString();
            box.b(parcel);
            bqa bqa8 = this.b;
            bqa8.f.execute(new bpw(4));
            this.a.c((Object) null);
            return true;
        }
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bpc(bqa bqa, po poVar, int i, byte[] bArr) {
        super("com.google.android.apps.aicore.aidl.IDownloadListener");
        this.c = i;
        this.a = poVar;
        this.b = bqa;
    }
}
