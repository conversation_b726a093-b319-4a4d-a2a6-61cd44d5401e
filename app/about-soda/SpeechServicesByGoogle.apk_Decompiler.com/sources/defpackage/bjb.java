package defpackage;

/* renamed from: bjb  reason: default package */
/* compiled from: PG */
public final class bjb implements juo {
    final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public bjb(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r9v1, types: [juo, java.lang.Object] */
    public final Object a(jup jup, jlr jlr) {
        if (this.b != 0) {
            Object obj = this.a;
            jvs jvs = new jvs((juo[]) obj, new mq(obj, 12), new bez((jlr) null), jup, (jlr) null);
            jvu jvu = new jvu(jlr.d(), jlr);
            Object ad = jnu.ad(jvu, jvu, jvs);
            if (ad == jlx.COROUTINE_SUSPENDED) {
                jji.as(jlr);
            }
            Object obj2 = jlx.COROUTINE_SUSPENDED;
            if (ad != obj2) {
                ad = jkd.a;
            }
            if (ad == obj2) {
                return ad;
            }
            return jkd.a;
        }
        Object a2 = this.a.a(new bja(jup), jlr);
        if (a2 == jlx.COROUTINE_SUSPENDED) {
            return a2;
        }
        return jkd.a;
    }
}
