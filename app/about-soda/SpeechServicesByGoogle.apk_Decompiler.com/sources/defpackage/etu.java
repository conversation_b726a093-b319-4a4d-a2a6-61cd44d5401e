package defpackage;

import androidx.preference.Preference;

/* renamed from: etu  reason: default package */
/* compiled from: PG */
final class etu extends jmi implements jne {
    int a;
    final /* synthetic */ dya b;
    final /* synthetic */ eua c;
    final /* synthetic */ jna d;
    private /* synthetic */ Object e;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public etu(dya dya, eua eua, jna jna, jlr jlr) {
        super(2, jlr);
        this.b = dya;
        this.c = eua;
        this.d = jna;
    }

    public final /* bridge */ /* synthetic */ Object b(Object obj, Object obj2) {
        return ((etu) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    public final Object bk(Object obj) {
        jlx jlx = jlx.COROUTINE_SUSPENDED;
        int i = this.a;
        jji.c(obj);
        if (i == 0) {
            jqs jqs = (jqs) this.e;
            juo Y = job.Y(new jug(new eot(this.b, this.c.e, (jlr) null, 2)), Preference.DEFAULT_ORDER);
            if (!(Y instanceof juh)) {
                Y = new juj(Y);
            }
            juy juy = new juy(new juy(Y, new ets(this.c, this.d, jqs, (jlr) null), 2), new ett(this.c, (jlr) null), 1);
            this.a = 1;
            if (job.V(juy, this) == jlx) {
                return jlx;
            }
        }
        return jkd.a;
    }

    public final jlr c(Object obj, jlr jlr) {
        etu etu = new etu(this.b, this.c, this.d, jlr);
        etu.e = obj;
        return etu;
    }
}
