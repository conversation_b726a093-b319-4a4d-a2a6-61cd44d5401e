package defpackage;

/* renamed from: cye  reason: default package */
/* compiled from: PG */
public final class cye {
    public final hin a;
    public final hig b;

    public cye() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof cye) {
            cye cye = (cye) obj;
            if (!this.a.equals(cye.a) || !this.b.equals(cye.b)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        int i2;
        hin hin = this.a;
        if (hin.B()) {
            i = hin.i();
        } else {
            int i3 = hin.memoizedHashCode;
            if (i3 == 0) {
                i3 = hin.i();
                hin.memoizedHashCode = i3;
            }
            i = i3;
        }
        hig hig = this.b;
        if (hig.B()) {
            i2 = hig.i();
        } else {
            int i4 = hig.memoizedHashCode;
            if (i4 == 0) {
                i4 = hig.i();
                hig.memoizedHashCode = i4;
            }
            i2 = i4;
        }
        return ((i ^ 1000003) * 1000003) ^ i2;
    }

    public final String toString() {
        hig hig = this.b;
        String obj = this.a.toString();
        String obj2 = hig.toString();
        return "FileGroupStatusWithDetails{fileGroupStatus=" + obj + ", fileGroupDetails=" + obj2 + "}";
    }

    public cye(hin hin, hig hig) {
        if (hin != null) {
            this.a = hin;
            if (hig != null) {
                this.b = hig;
                return;
            }
            throw new NullPointerException("Null fileGroupDetails");
        }
        throw new NullPointerException("Null fileGroupStatus");
    }
}
