package defpackage;

import android.view.Window;
import android.view.WindowManager;
import java.util.Iterator;
import java.util.List;

/* renamed from: bjs  reason: default package */
/* compiled from: PG */
final class bjs extends wt {
    int b;
    int c;
    WindowManager.LayoutParams d;
    int e;
    boolean f;
    final int g;
    final /* synthetic */ Window h;
    final /* synthetic */ bjt i;

    public bjs(bjt bjt, Window window) {
        this.h = window;
        this.i = bjt;
        this.g = (int) bnv.a(bjt.mContext.getResources(), 32);
    }

    private static final boolean e(iih iih) {
        if ((iih.f() & 8) != 0) {
            return true;
        }
        return false;
    }

    public final void a() {
        if (!this.f) {
            this.i.updateAttributes();
            this.i.copyWindowInsets();
        }
    }

    public final void b(List list) {
        iih iih;
        int i2;
        float f2;
        Iterator it = list.iterator();
        while (true) {
            if (!it.hasNext()) {
                iih = null;
                break;
            }
            iih = (iih) it.next();
            if (e(iih)) {
                break;
            }
        }
        if (iih != null) {
            WindowManager.LayoutParams layoutParams = this.d;
            int i3 = this.c;
            layoutParams.height = (int) (((float) i3) - (((float) (i3 - this.b)) * iih.e()));
            this.h.setAttributes(this.d);
            if (this.f) {
                i2 = this.g;
                f2 = iih.e();
            } else {
                int i4 = this.g;
                f2 = iih.e();
                i2 = -i4;
            }
            float f3 = ((float) i2) * f2;
            bjt bjt = this.i;
            bjt.mContent.setPadding(bjt.mContent.getPaddingLeft(), this.i.mContent.getPaddingTop(), this.i.mContent.getPaddingRight(), (int) (((float) this.e) + f3));
        }
    }

    public final void c(iih iih) {
        if (e(iih)) {
            this.h.setSoftInputMode(48);
            WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
            this.d = layoutParams;
            layoutParams.copyFrom(this.h.getAttributes());
            this.c = this.d.height;
            int[] iArr = new int[2];
            this.h.getDecorView().getRootView().getLocationOnScreen(iArr);
            int i2 = iArr[0];
            int i3 = iArr[1];
            this.e = this.i.mContent.getPaddingBottom();
            this.d.gravity = 51;
            this.d.setFitInsetsTypes(0);
            this.d.x = i2;
            this.d.y = i3;
            this.h.setAttributes(this.d);
        }
    }

    public final void d(iih iih, ws wsVar) {
        boolean z;
        if (e(iih)) {
            int i2 = 0;
            if (wb.a(this.h.getDecorView().getRootView()).f(8) != tg.a) {
                z = true;
            } else {
                z = false;
            }
            this.f = z;
            WindowManager.LayoutParams dialogWindowLayoutParam = this.i.getDialogWindowLayoutParam(this.h.getAttributes());
            if (this.f) {
                tg tgVar = wsVar.b;
                int[] iArr = new int[2];
                this.h.getDecorView().getRootView().getLocationOnScreen(iArr);
                int i3 = iArr[1] + dialogWindowLayoutParam.height;
                int i4 = bnv.g(this.i.mContext).heightPixels - tgVar.e;
                if (i4 < i3) {
                    i2 = (i3 - i4) - this.g;
                }
            }
            this.b = dialogWindowLayoutParam.height - i2;
        }
    }
}
