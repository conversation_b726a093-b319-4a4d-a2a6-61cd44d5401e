package defpackage;

import android.animation.Animator;
import android.animation.AnimatorSet;
import android.util.SparseArray;
import android.util.SparseIntArray;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ListView;
import androidx.wear.ambient.AmbientLifecycleObserver;
import java.util.ArrayList;
import java.util.Map;

/* renamed from: ayc  reason: default package */
/* compiled from: PG */
public abstract class ayc implements Cloneable {
    private static final wd C = new wd();
    private static final Animator[] u = new Animator[0];
    private static final int[] v = {2, 1, 3, 4};
    private static final ThreadLocal w = new ThreadLocal();
    private boolean A = false;
    private ArrayList B = null;
    public long a = -1;
    long b = -1;
    public final ArrayList c = new ArrayList();
    public final ArrayList d = new ArrayList();
    public ayn e = new ayn();
    public ayn f = new ayn();
    ayk g = null;
    public final int[] h = v;
    public ArrayList i;
    public ArrayList j;
    final ArrayList k = new ArrayList();
    int l = 0;
    boolean m = false;
    public ayc n = null;
    public ArrayList o = new ArrayList();
    long p;
    public axy q;
    long r;
    public AmbientLifecycleObserver.AmbientLifecycleCallback.CC s;
    public wd t = C;
    private final String x = getClass().getName();
    private axz[] y;
    private Animator[] z = u;

    private static boolean N(aym aym, aym aym2, String str) {
        Map map = aym2.a;
        Object obj = aym.a.get(str);
        Object obj2 = map.get(str);
        if (obj == null && obj2 == null) {
            return false;
        }
        if (obj == null || obj2 == null) {
            return true;
        }
        return !obj.equals(obj2);
    }

    private static void f(ayn ayn, View view, aym aym) {
        ((pa) ayn.a).put(view, aym);
        int id = view.getId();
        if (id >= 0) {
            if (((SparseArray) ayn.b).indexOfKey(id) >= 0) {
                ((SparseArray) ayn.b).put(id, (Object) null);
            } else {
                ((SparseArray) ayn.b).put(id, view);
            }
        }
        String f2 = wa.f(view);
        if (f2 != null) {
            if (((pa) ayn.d).containsKey(f2)) {
                ((pa) ayn.d).put(f2, (Object) null);
            } else {
                ((pa) ayn.d).put(f2, view);
            }
        }
        if (view.getParent() instanceof ListView) {
            ListView listView = (ListView) view.getParent();
            if (listView.getAdapter().hasStableIds()) {
                long itemIdAtPosition = listView.getItemIdAtPosition(listView.getPositionForView(view));
                ox oxVar = (ox) ayn.c;
                if (oxVar.a) {
                    int i2 = oxVar.d;
                    long[] jArr = oxVar.b;
                    Object[] objArr = oxVar.c;
                    int i3 = 0;
                    for (int i4 = 0; i4 < i2; i4++) {
                        Object obj = objArr[i4];
                        if (obj != oy.a) {
                            if (i4 != i3) {
                                jArr[i3] = jArr[i4];
                                objArr[i3] = obj;
                                objArr[i4] = null;
                            }
                            i3++;
                        }
                    }
                    oxVar.a = false;
                    oxVar.d = i3;
                }
                if (pd.b(oxVar.b, oxVar.d, itemIdAtPosition) >= 0) {
                    View view2 = (View) ((ox) ayn.c).c(itemIdAtPosition);
                    if (view2 != null) {
                        view2.setHasTransientState(false);
                        ((ox) ayn.c).f(itemIdAtPosition, (Object) null);
                        return;
                    }
                    return;
                }
                view.setHasTransientState(true);
                ((ox) ayn.c).f(itemIdAtPosition, view);
            }
        }
    }

    private final void g(View view, boolean z2) {
        if (view != null) {
            view.getId();
            if (view.getParent() instanceof ViewGroup) {
                aym aym = new aym(view);
                if (z2) {
                    c(aym);
                } else {
                    b(aym);
                }
                aym.c.add(this);
                o(aym);
                if (z2) {
                    f(this.e, view, aym);
                } else {
                    f(this.f, view, aym);
                }
            }
            if (view instanceof ViewGroup) {
                ViewGroup viewGroup = (ViewGroup) view;
                for (int i2 = 0; i2 < viewGroup.getChildCount(); i2++) {
                    g(viewGroup.getChildAt(i2), z2);
                }
            }
        }
    }

    public static ot h() {
        ThreadLocal threadLocal = w;
        ot otVar = (ot) threadLocal.get();
        if (otVar != null) {
            return otVar;
        }
        ot otVar2 = new ot();
        threadLocal.set(otVar2);
        return otVar2;
    }

    public boolean A() {
        if (!this.k.isEmpty()) {
            return true;
        }
        return false;
    }

    public boolean B(aym aym, aym aym2) {
        if (aym == null || aym2 == null) {
            return false;
        }
        String[] e2 = e();
        if (e2 != null) {
            int i2 = 0;
            while (i2 < e2.length) {
                if (!N(aym, aym2, e2[i2])) {
                    i2++;
                }
            }
            return false;
        }
        for (String N : aym.a.keySet()) {
            if (N(aym, aym2, N)) {
            }
        }
        return false;
        return true;
    }

    /* access modifiers changed from: package-private */
    public final boolean C(View view) {
        int id = view.getId();
        if ((this.c.size() != 0 || this.d.size() != 0) && !this.c.contains(Integer.valueOf(id)) && !this.d.contains(view)) {
            return false;
        }
        return true;
    }

    public final void D(axz axz) {
        if (this.B == null) {
            this.B = new ArrayList();
        }
        this.B.add(axz);
    }

    public void E(View view) {
        this.d.add(view);
    }

    public final void F(axz axz) {
        ayc ayc;
        ArrayList arrayList = this.B;
        if (arrayList != null) {
            if (!arrayList.remove(axz) && (ayc = this.n) != null) {
                ayc.F(axz);
            }
            if (this.B.size() == 0) {
                this.B = null;
            }
        }
    }

    public void G(View view) {
        this.d.remove(view);
    }

    public void J(long j2) {
        this.a = j2;
    }

    public void K() {
        this.b = 0;
    }

    public void L(AmbientLifecycleObserver.AmbientLifecycleCallback.CC r1) {
        this.s = r1;
    }

    public void M(wd wdVar) {
        if (wdVar == null) {
            this.t = C;
        } else {
            this.t = wdVar;
        }
    }

    public Animator a(ViewGroup viewGroup, aym aym, aym aym2) {
        return null;
    }

    public abstract void b(aym aym);

    public abstract void c(aym aym);

    public boolean d() {
        throw null;
    }

    public String[] e() {
        return null;
    }

    /* renamed from: i */
    public ayc clone() {
        try {
            ayc ayc = (ayc) super.clone();
            ayc.o = new ArrayList();
            ayc.e = new ayn();
            ayc.f = new ayn();
            ayc.i = null;
            ayc.j = null;
            ayc.q = null;
            ayc.n = this;
            ayc.B = null;
            return ayc;
        } catch (CloneNotSupportedException e2) {
            throw new RuntimeException(e2);
        }
    }

    public final ayc j() {
        ayk ayk = this.g;
        if (ayk != null) {
            return ayk.j();
        }
        return this;
    }

    /* access modifiers changed from: package-private */
    public final aym k(View view, boolean z2) {
        ArrayList arrayList;
        ArrayList arrayList2;
        ayk ayk = this.g;
        if (ayk != null) {
            return ayk.k(view, z2);
        }
        if (z2) {
            arrayList = this.i;
        } else {
            arrayList = this.j;
        }
        if (arrayList == null) {
            return null;
        }
        int size = arrayList.size();
        int i2 = 0;
        while (true) {
            if (i2 >= size) {
                i2 = -1;
                break;
            }
            aym aym = (aym) arrayList.get(i2);
            if (aym == null) {
                return null;
            }
            if (aym.b == view) {
                break;
            }
            i2++;
        }
        if (i2 < 0) {
            return null;
        }
        if (z2) {
            arrayList2 = this.j;
        } else {
            arrayList2 = this.i;
        }
        return (aym) arrayList2.get(i2);
    }

    public final aym l(View view, boolean z2) {
        ayn ayn;
        ayk ayk = this.g;
        if (ayk != null) {
            return ayk.l(view, z2);
        }
        if (z2) {
            ayn = this.e;
        } else {
            ayn = this.f;
        }
        return (aym) ((pa) ayn.a).get(view);
    }

    public String m(String str) {
        StringBuilder sb = new StringBuilder(str);
        sb.append(getClass().getSimpleName());
        sb.append("@");
        sb.append(Integer.toHexString(hashCode()));
        sb.append(": ");
        if (this.b != -1) {
            sb.append("dur(");
            sb.append(this.b);
            sb.append(") ");
        }
        if (this.a != -1) {
            sb.append("dly(");
            sb.append(this.a);
            sb.append(") ");
        }
        if (this.c.size() > 0 || this.d.size() > 0) {
            sb.append("tgts(");
            if (this.c.size() > 0) {
                for (int i2 = 0; i2 < this.c.size(); i2++) {
                    if (i2 > 0) {
                        sb.append(", ");
                    }
                    sb.append(this.c.get(i2));
                }
            }
            if (this.d.size() > 0) {
                for (int i3 = 0; i3 < this.d.size(); i3++) {
                    if (i3 > 0) {
                        sb.append(", ");
                    }
                    sb.append(this.d.get(i3));
                }
            }
            sb.append(")");
        }
        return sb.toString();
    }

    public void n() {
        ArrayList arrayList = this.k;
        int size = arrayList.size();
        Animator[] animatorArr = (Animator[]) arrayList.toArray(this.z);
        this.z = u;
        while (true) {
            size--;
            if (size >= 0) {
                Animator animator = animatorArr[size];
                animatorArr[size] = null;
                animator.cancel();
            } else {
                this.z = animatorArr;
                t(this, ayb.c, false);
                return;
            }
        }
    }

    /* access modifiers changed from: package-private */
    public final void p(ViewGroup viewGroup, boolean z2) {
        boolean z3;
        q(z2);
        if (this.c.size() > 0 || this.d.size() > 0) {
            int i2 = 0;
            while (true) {
                boolean z4 = true;
                if (i2 >= this.c.size()) {
                    break;
                }
                View findViewById = viewGroup.findViewById(((Integer) this.c.get(i2)).intValue());
                if (findViewById != null) {
                    aym aym = new aym(findViewById);
                    if (z2) {
                        c(aym);
                    } else {
                        b(aym);
                        z4 = false;
                    }
                    aym.c.add(this);
                    o(aym);
                    if (z4) {
                        f(this.e, findViewById, aym);
                    } else {
                        f(this.f, findViewById, aym);
                    }
                }
                i2++;
            }
            for (int i3 = 0; i3 < this.d.size(); i3++) {
                View view = (View) this.d.get(i3);
                aym aym2 = new aym(view);
                if (z2) {
                    c(aym2);
                    z3 = true;
                } else {
                    b(aym2);
                    z3 = false;
                }
                aym2.c.add(this);
                o(aym2);
                if (z3) {
                    f(this.e, view, aym2);
                } else {
                    f(this.f, view, aym2);
                }
            }
            return;
        }
        g(viewGroup, z2);
    }

    /* access modifiers changed from: package-private */
    public final void q(boolean z2) {
        if (z2) {
            ((pa) this.e.a).clear();
            ((SparseArray) this.e.b).clear();
            ((ox) this.e.c).e();
            return;
        }
        ((pa) this.f.a).clear();
        ((SparseArray) this.f.b).clear();
        ((ox) this.f.c).e();
    }

    public void r(ViewGroup viewGroup, ayn ayn, ayn ayn2, ArrayList arrayList, ArrayList arrayList2) {
        int i2;
        Animator a2;
        AnimatorSet animatorSet;
        aym aym;
        View view;
        Animator animator;
        aym aym2;
        ot h2 = h();
        SparseIntArray sparseIntArray = new SparseIntArray();
        int size = arrayList.size();
        axy axy = j().q;
        int i3 = 0;
        while (i3 < size) {
            aym aym3 = (aym) arrayList.get(i3);
            aym aym4 = (aym) arrayList2.get(i3);
            if (aym3 != null && !aym3.c.contains(this)) {
                aym3 = null;
            }
            if (aym4 != null && !aym4.c.contains(this)) {
                aym4 = null;
            }
            if (!(aym3 == null && aym4 == null) && ((aym3 == null || aym4 == null || B(aym3, aym4)) && (a2 = a(viewGroup, aym3, aym4)) != null)) {
                if (aym4 != null) {
                    View view2 = aym4.b;
                    String[] e2 = e();
                    if (e2 != null) {
                        aym aym5 = new aym(view2);
                        aym aym6 = (aym) ((pa) ayn2.a).get(view2);
                        animator = a2;
                        if (aym6 != null) {
                            int i4 = 0;
                            while (i4 < e2.length) {
                                Map map = aym5.a;
                                String str = e2[i4];
                                map.put(str, aym6.a.get(str));
                                i4++;
                                ayn ayn3 = ayn2;
                                e2 = e2;
                            }
                        }
                        int i5 = h2.f;
                        int i6 = 0;
                        while (true) {
                            if (i6 >= i5) {
                                aym2 = aym5;
                                break;
                            }
                            axx axx = (axx) h2.get((Animator) h2.d(i6));
                            if (axx.c != null && axx.a == view2) {
                                if (((String) axx.b).equals(this.x) && ((aym) axx.c).equals(aym5)) {
                                    aym2 = aym5;
                                    animator = null;
                                    break;
                                }
                            }
                            i6++;
                        }
                    } else {
                        animator = a2;
                        aym2 = null;
                    }
                    view = view2;
                    aym = aym2;
                    animatorSet = animator;
                } else {
                    view = aym3.b;
                    animatorSet = a2;
                    aym = null;
                }
                if (animatorSet != null) {
                    i2 = size;
                    axx axx2 = r0;
                    axx axx3 = new axx(view, this.x, this, viewGroup.getWindowId(), aym, animatorSet);
                    if (axy != null) {
                        AnimatorSet animatorSet2 = new AnimatorSet();
                        animatorSet2.play(animatorSet);
                        animatorSet = animatorSet2;
                    }
                    h2.put(animatorSet, axx2);
                    this.o.add(animatorSet);
                    i3++;
                    size = i2;
                }
            }
            i2 = size;
            i3++;
            size = i2;
        }
        if (sparseIntArray.size() != 0) {
            for (int i7 = 0; i7 < sparseIntArray.size(); i7++) {
                axx axx4 = (axx) h2.get((Animator) this.o.get(sparseIntArray.keyAt(i7)));
                ((Animator) axx4.f).setStartDelay((((long) sparseIntArray.valueAt(i7)) - Long.MAX_VALUE) + ((Animator) axx4.f).getStartDelay());
            }
        }
    }

    /* access modifiers changed from: protected */
    public final void s() {
        int i2 = this.l - 1;
        this.l = i2;
        if (i2 == 0) {
            t(this, ayb.b, false);
            for (int i3 = 0; i3 < ((ox) this.e.c).a(); i3++) {
                View view = (View) ((ox) this.e.c).d(i3);
                if (view != null) {
                    view.setHasTransientState(false);
                }
            }
            for (int i4 = 0; i4 < ((ox) this.f.c).a(); i4++) {
                View view2 = (View) ((ox) this.f.c).d(i4);
                if (view2 != null) {
                    view2.setHasTransientState(false);
                }
            }
            this.m = true;
        }
    }

    public final void t(ayc ayc, ayb ayb, boolean z2) {
        ayc ayc2 = this.n;
        if (ayc2 != null) {
            ayc2.t(ayc, ayb, z2);
        }
        ArrayList arrayList = this.B;
        if (arrayList != null && !arrayList.isEmpty()) {
            int size = this.B.size();
            axz[] axzArr = this.y;
            if (axzArr == null) {
                axzArr = new axz[size];
            }
            this.y = null;
            axz[] axzArr2 = (axz[]) this.B.toArray(axzArr);
            for (int i2 = 0; i2 < size; i2++) {
                ayb.a(axzArr2[i2], ayc);
                axzArr2[i2] = null;
            }
            this.y = axzArr2;
        }
    }

    public final String toString() {
        return m("");
    }

    public void u(View view) {
        if (!this.m) {
            ArrayList arrayList = this.k;
            int size = arrayList.size();
            Animator[] animatorArr = (Animator[]) arrayList.toArray(this.z);
            this.z = u;
            while (true) {
                size--;
                if (size >= 0) {
                    Animator animator = animatorArr[size];
                    animatorArr[size] = null;
                    animator.pause();
                } else {
                    this.z = animatorArr;
                    t(this, ayb.d, false);
                    this.A = true;
                    return;
                }
            }
        }
    }

    public void v() {
        ot h2 = h();
        this.p = 0;
        for (int i2 = 0; i2 < this.o.size(); i2++) {
            Animator animator = (Animator) this.o.get(i2);
            axx axx = (axx) h2.get(animator);
            if (!(animator == null || axx == null)) {
                if (this.b >= 0) {
                    ((Animator) axx.f).setDuration(0);
                }
                long j2 = this.a;
                if (j2 >= 0) {
                    Animator animator2 = (Animator) axx.f;
                    animator2.setStartDelay(j2 + animator2.getStartDelay());
                }
                this.k.add(animator);
                this.p = Math.max(this.p, animator.getTotalDuration());
            }
        }
        this.o.clear();
    }

    public void w(View view) {
        if (this.A) {
            if (!this.m) {
                ArrayList arrayList = this.k;
                int size = arrayList.size();
                Animator[] animatorArr = (Animator[]) arrayList.toArray(this.z);
                this.z = u;
                while (true) {
                    size--;
                    if (size < 0) {
                        break;
                    }
                    Animator animator = animatorArr[size];
                    animatorArr[size] = null;
                    animator.resume();
                }
                this.z = animatorArr;
                t(this, ayb.e, false);
            }
            this.A = false;
        }
    }

    /* access modifiers changed from: protected */
    public void x() {
        z();
        ot h2 = h();
        ArrayList arrayList = this.o;
        int size = arrayList.size();
        for (int i2 = 0; i2 < size; i2++) {
            Animator animator = (Animator) arrayList.get(i2);
            if (h2.containsKey(animator)) {
                z();
                if (animator != null) {
                    animator.addListener(new axv(this, h2));
                    if (this.b >= 0) {
                        animator.setDuration(0);
                    }
                    long j2 = this.a;
                    if (j2 >= 0) {
                        animator.setStartDelay(j2 + animator.getStartDelay());
                    }
                    animator.addListener(new axw(this));
                    animator.start();
                }
            }
        }
        this.o.clear();
        s();
    }

    public void y(long j2, long j3) {
        boolean z2;
        long j4 = j2;
        int i2 = (j4 > j3 ? 1 : (j4 == j3 ? 0 : -1));
        long j5 = this.p;
        if (i2 < 0) {
            z2 = true;
        } else {
            z2 = false;
        }
        int i3 = (j3 > 0 ? 1 : (j3 == 0 ? 0 : -1));
        if ((i3 < 0 && j4 >= 0) || (j3 > j5 && j4 <= j5)) {
            this.m = false;
            t(this, ayb.a, z2);
        }
        ArrayList arrayList = this.k;
        int size = arrayList.size();
        Animator[] animatorArr = (Animator[]) arrayList.toArray(this.z);
        this.z = u;
        for (int i4 = 0; i4 < size; i4++) {
            Animator animator = animatorArr[i4];
            animatorArr[i4] = null;
            ((AnimatorSet) animator).setCurrentPlayTime(Math.min(Math.max(0, j4), animator.getTotalDuration()));
        }
        this.z = animatorArr;
        int i5 = (j4 > j5 ? 1 : (j4 == j5 ? 0 : -1));
        if ((i5 > 0 && j3 <= j5) || (j4 < 0 && i3 >= 0)) {
            if (i5 > 0) {
                this.m = true;
            }
            t(this, ayb.b, z2);
        }
    }

    /* access modifiers changed from: protected */
    public final void z() {
        if (this.l == 0) {
            t(this, ayb.a, false);
            this.m = false;
        }
        this.l++;
    }

    public void H() {
    }

    public void I() {
    }

    public void o(aym aym) {
    }
}
