package defpackage;

import android.content.Context;
import java.util.concurrent.Executor;

/* renamed from: cwm  reason: default package */
/* compiled from: PG */
public final class cwm {
    public static volatile boolean a = false;
    public final Context b;
    public final cvy c;
    public final cvz d;
    public final cxa e;
    public final cuk f;
    public final grh g;
    public final Executor h;
    public final grh i;
    public final cyi j;
    public final cxz k;
    public final cyk l;
    public final dbw m;
    public final cyw n;
    public final cxk o;
    public final dmd p;
    public final eix q;

    public cwm(Context context, cyk cyk, dbw dbw, cxa cxa, cvy cvy, cvz cvz, dmd dmd, cuk cuk, eix eix, cyw cyw, cxk cxk, grh grh, Executor executor, grh grh2, cyi cyi, cxz cxz) {
        this.b = context;
        this.l = cyk;
        this.m = dbw;
        this.e = cxa;
        this.c = cvy;
        this.d = cvz;
        this.p = dmd;
        this.f = cuk;
        this.q = eix;
        this.n = cyw;
        this.o = cxk;
        this.g = grh;
        this.h = executor;
        this.i = grh2;
        this.j = cyi;
        this.k = cxz;
    }

    public final hme a() {
        return ftd.L(this.m.j(), new cvi(this, 19), this.h);
    }

    public final hme b(csx csx) {
        long j2 = csx.r;
        return this.k.b("mdd_" + j2);
    }

    public final hme c(boolean z, hko hko) {
        cyh.d("%s downloadAllPendingGroups on wifi = %s", "MDDManager", Boolean.valueOf(z));
        return ftd.L(f(), new cvk((Object) this, z, (Object) hko, 2), this.h);
    }

    public final hme d(ctg ctg, grh grh, hko hko) {
        cyh.e("%s downloadFileGroup %s %s", "MDDManager", ctg.b, ctg.c);
        return ftd.L(f(), new cvd((Object) this, ctg, (Object) grh, (Object) hko, 14), this.h);
    }

    public final hme e(ctg ctg, boolean z) {
        cyh.e("%s getFileGroup %s %s", "MDDManager", ctg.b, ctg.c);
        return ftd.L(f(), new cvk(this, ctg, z, 3), this.h);
    }

    public final hme f() {
        if (a) {
            return hma.a;
        }
        return czw.e(hma.a).g(new cwi(this, 0), this.h).g(new cwi(this, 2), this.h).g(new cwi(this, 3), this.h).g(new cwi(this, 4), this.h).f(new cwh(4), this.h);
    }
}
