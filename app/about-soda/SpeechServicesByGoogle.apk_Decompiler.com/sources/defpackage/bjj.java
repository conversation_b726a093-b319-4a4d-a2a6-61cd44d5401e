package defpackage;

import androidx.preference.Preference;

/* renamed from: bjj  reason: default package */
/* compiled from: PG */
public final class bjj {
    public final int a;
    public final int b;
    public final Object c;

    public bjj(byw byw) {
        this.c = byw.a;
        this.b = Preference.DEFAULT_ORDER;
        this.a = Preference.DEFAULT_ORDER;
    }

    /* JADX WARNING: type inference failed for: r0v0, types: [java.util.List, java.lang.Object] */
    public final CharSequence a() {
        return (CharSequence) this.c.get(0);
    }

    public bjj(String str) {
        this.c = str;
        this.a = 443;
        this.b = 443;
    }
}
