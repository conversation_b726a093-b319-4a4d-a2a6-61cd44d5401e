package defpackage;

/* renamed from: elz  reason: default package */
/* compiled from: PG */
public final class elz extends jmi implements jne {
    Object a;
    Object b;
    Object c;
    Object d;
    int e;
    final /* synthetic */ String f;
    final /* synthetic */ ehg g;
    final /* synthetic */ hme h;
    final /* synthetic */ hme i;
    final /* synthetic */ cyw j;
    private /* synthetic */ Object m;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public elz(cyw cyw, String str, ehg ehg, hme hme, hme hme2, jlr jlr) {
        super(2, jlr);
        this.j = cyw;
        this.f = str;
        this.g = ehg;
        this.h = hme;
        this.i = hme2;
    }

    public final /* bridge */ /* synthetic */ Object b(Object obj, Object obj2) {
        return ((elz) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:24:0x00a8, code lost:
        if (r14 != r0) goto L_0x00aa;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:29:0x00db, code lost:
        if (r14 == r0) goto L_0x0185;
     */
    /* JADX WARNING: Removed duplicated region for block: B:35:0x0106 A[Catch:{ all -> 0x0186 }] */
    /* JADX WARNING: Removed duplicated region for block: B:39:0x0126 A[Catch:{ all -> 0x0186 }] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r14) {
        /*
            r13 = this;
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r1 = r13.e
            r2 = 4
            r3 = 3
            r4 = 2
            r5 = 1
            if (r1 == 0) goto L_0x0089
            if (r1 == r5) goto L_0x0075
            if (r1 == r4) goto L_0x005d
            if (r1 == r3) goto L_0x0044
            if (r1 == r2) goto L_0x002b
            java.lang.Object r0 = r13.d
            dyu r0 = (defpackage.dyu) r0
            java.lang.Object r1 = r13.c
            eel r1 = (defpackage.eel) r1
            java.lang.Object r6 = r13.b
            cyw r6 = (defpackage.cyw) r6
            java.lang.Object r7 = r13.a
            java.lang.String r7 = (java.lang.String) r7
            java.lang.Object r8 = r13.m
            ehg r8 = (defpackage.ehg) r8
            defpackage.jji.c(r14)     // Catch:{ all -> 0x0186 }
            goto L_0x011e
        L_0x002b:
            java.lang.Object r1 = r13.d
            dyu r1 = (defpackage.dyu) r1
            java.lang.Object r6 = r13.c
            eel r6 = (defpackage.eel) r6
            java.lang.Object r7 = r13.b
            cyw r7 = (defpackage.cyw) r7
            java.lang.Object r8 = r13.a
            java.lang.String r8 = (java.lang.String) r8
            java.lang.Object r9 = r13.m
            ehg r9 = (defpackage.ehg) r9
            defpackage.jji.c(r14)     // Catch:{ all -> 0x0186 }
            goto L_0x00f8
        L_0x0044:
            java.lang.Object r1 = r13.d
            eel r1 = (defpackage.eel) r1
            java.lang.Object r6 = r13.c
            cyw r6 = (defpackage.cyw) r6
            java.lang.Object r7 = r13.b
            java.lang.String r7 = (java.lang.String) r7
            java.lang.Object r8 = r13.a
            ehg r8 = (defpackage.ehg) r8
            java.lang.Object r9 = r13.m
            hme r9 = (defpackage.hme) r9
            defpackage.jji.c(r14)     // Catch:{ all -> 0x0186 }
            goto L_0x00dd
        L_0x005d:
            java.lang.Object r1 = r13.d
            eel r1 = (defpackage.eel) r1
            java.lang.Object r6 = r13.c
            cyw r6 = (defpackage.cyw) r6
            java.lang.Object r7 = r13.b
            java.lang.String r7 = (java.lang.String) r7
            java.lang.Object r8 = r13.a
            ehg r8 = (defpackage.ehg) r8
            java.lang.Object r9 = r13.m
            hme r9 = (defpackage.hme) r9
            defpackage.jji.c(r14)     // Catch:{ all -> 0x0186 }
            goto L_0x00c5
        L_0x0075:
            java.lang.Object r1 = r13.c
            cyw r1 = (defpackage.cyw) r1
            java.lang.Object r6 = r13.b
            java.lang.String r6 = (java.lang.String) r6
            java.lang.Object r7 = r13.a
            ehg r7 = (defpackage.ehg) r7
            java.lang.Object r8 = r13.m
            hme r8 = (defpackage.hme) r8
            defpackage.jji.c(r14)     // Catch:{ all -> 0x0186 }
            goto L_0x00aa
        L_0x0089:
            defpackage.jji.c(r14)
            java.lang.Object r14 = r13.m
            jqs r14 = (defpackage.jqs) r14
            cyw r1 = r13.j
            java.lang.String r6 = r13.f
            ehg r7 = r13.g
            hme r14 = r13.h
            hme r8 = r13.i
            r13.m = r8     // Catch:{ all -> 0x0186 }
            r13.a = r7     // Catch:{ all -> 0x0186 }
            r13.b = r6     // Catch:{ all -> 0x0186 }
            r13.c = r1     // Catch:{ all -> 0x0186 }
            r13.e = r5     // Catch:{ all -> 0x0186 }
            java.lang.Object r14 = defpackage.jqw.x(r14, r13)     // Catch:{ all -> 0x0186 }
            if (r14 == r0) goto L_0x0185
        L_0x00aa:
            eel r14 = (defpackage.eel) r14     // Catch:{ all -> 0x0186 }
            r13.m = r8     // Catch:{ all -> 0x0186 }
            r13.a = r7     // Catch:{ all -> 0x0186 }
            r13.b = r6     // Catch:{ all -> 0x0186 }
            r13.c = r1     // Catch:{ all -> 0x0186 }
            r13.d = r14     // Catch:{ all -> 0x0186 }
            r13.e = r4     // Catch:{ all -> 0x0186 }
            java.lang.Object r9 = defpackage.jqw.x(r8, r13)     // Catch:{ all -> 0x0186 }
            if (r9 == r0) goto L_0x0185
            r12 = r1
            r1 = r14
            r14 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            r6 = r12
        L_0x00c5:
            ebg r14 = (defpackage.ebg) r14     // Catch:{ all -> 0x0186 }
            hme r14 = r14.e()     // Catch:{ all -> 0x0186 }
            r13.m = r9     // Catch:{ all -> 0x0186 }
            r13.a = r8     // Catch:{ all -> 0x0186 }
            r13.b = r7     // Catch:{ all -> 0x0186 }
            r13.c = r6     // Catch:{ all -> 0x0186 }
            r13.d = r1     // Catch:{ all -> 0x0186 }
            r13.e = r3     // Catch:{ all -> 0x0186 }
            java.lang.Object r14 = defpackage.jqw.x(r14, r13)     // Catch:{ all -> 0x0186 }
            if (r14 == r0) goto L_0x0185
        L_0x00dd:
            dyu r14 = (defpackage.dyu) r14     // Catch:{ all -> 0x0186 }
            r13.m = r8     // Catch:{ all -> 0x0186 }
            r13.a = r7     // Catch:{ all -> 0x0186 }
            r13.b = r6     // Catch:{ all -> 0x0186 }
            r13.c = r1     // Catch:{ all -> 0x0186 }
            r13.d = r14     // Catch:{ all -> 0x0186 }
            r13.e = r2     // Catch:{ all -> 0x0186 }
            java.lang.Object r9 = defpackage.jqw.x(r9, r13)     // Catch:{ all -> 0x0186 }
            if (r9 == r0) goto L_0x0185
            r12 = r1
            r1 = r14
            r14 = r9
            r9 = r8
            r8 = r7
            r7 = r6
            r6 = r12
        L_0x00f8:
            ebg r14 = (defpackage.ebg) r14     // Catch:{ all -> 0x0186 }
            grh r14 = r14.c()     // Catch:{ all -> 0x0186 }
            java.lang.Object r14 = r14.e()     // Catch:{ all -> 0x0186 }
            hme r14 = (defpackage.hme) r14     // Catch:{ all -> 0x0186 }
            if (r14 == 0) goto L_0x0126
            r13.m = r9     // Catch:{ all -> 0x0186 }
            r13.a = r8     // Catch:{ all -> 0x0186 }
            r13.b = r7     // Catch:{ all -> 0x0186 }
            r13.c = r6     // Catch:{ all -> 0x0186 }
            r13.d = r1     // Catch:{ all -> 0x0186 }
            r10 = 5
            r13.e = r10     // Catch:{ all -> 0x0186 }
            java.lang.Object r14 = defpackage.jqw.x(r14, r13)     // Catch:{ all -> 0x0186 }
            if (r14 == r0) goto L_0x0185
            r0 = r1
            r1 = r6
            r6 = r7
            r7 = r8
            r8 = r9
        L_0x011e:
            dyv r14 = (defpackage.dyv) r14     // Catch:{ all -> 0x0186 }
            r9 = r8
            r8 = r7
            r7 = r6
            r6 = r1
            r1 = r0
            goto L_0x0127
        L_0x0126:
            r14 = 0
        L_0x0127:
            if (r14 == 0) goto L_0x018a
            int r0 = r14.a     // Catch:{ all -> 0x0186 }
            r0 = r0 & r5
            if (r0 == 0) goto L_0x018a
            int r0 = r1.a     // Catch:{ all -> 0x0186 }
            r0 = r0 & r5
            if (r0 == 0) goto L_0x018a
            java.lang.Object r0 = r7.d     // Catch:{ all -> 0x0186 }
            long r10 = r14.b     // Catch:{ all -> 0x0186 }
            j$.time.Duration r14 = j$.time.Duration.ofMillis(r10)     // Catch:{ all -> 0x0186 }
            long r10 = r1.b     // Catch:{ all -> 0x0186 }
            j$.time.Duration r1 = j$.time.Duration.ofNanos(r10)     // Catch:{ all -> 0x0186 }
            j$.time.Duration r14 = r14.minus(r1)     // Catch:{ all -> 0x0186 }
            long r10 = r14.toMillis()     // Catch:{ all -> 0x0186 }
            long r10 = java.lang.Math.abs(r10)     // Catch:{ all -> 0x0186 }
            double r10 = (double) r10     // Catch:{ all -> 0x0186 }
            emd r0 = (defpackage.emd) r0     // Catch:{ all -> 0x0186 }
            ihn r0 = r0.b     // Catch:{ all -> 0x0186 }
            java.lang.Object r0 = r0.b()     // Catch:{ all -> 0x0186 }
            emi r0 = (defpackage.emi) r0     // Catch:{ all -> 0x0186 }
            boolean r14 = r14.isNegative()     // Catch:{ all -> 0x0186 }
            int r1 = r9.a     // Catch:{ all -> 0x0186 }
            ehf r1 = defpackage.ehf.a(r1)     // Catch:{ all -> 0x0186 }
            java.lang.String r1 = r1.name()     // Catch:{ all -> 0x0186 }
            gsb r0 = r0.i     // Catch:{ all -> 0x0186 }
            java.lang.String r6 = r6.name()     // Catch:{ all -> 0x0186 }
            java.lang.Object r0 = r0.a()     // Catch:{ all -> 0x0186 }
            fqv r0 = (defpackage.fqv) r0     // Catch:{ all -> 0x0186 }
            java.lang.Boolean r14 = java.lang.Boolean.valueOf(r14)     // Catch:{ all -> 0x0186 }
            java.lang.Object[] r2 = new java.lang.Object[r2]     // Catch:{ all -> 0x0186 }
            r7 = 0
            r2[r7] = r14     // Catch:{ all -> 0x0186 }
            r2[r5] = r8     // Catch:{ all -> 0x0186 }
            r2[r4] = r1     // Catch:{ all -> 0x0186 }
            r2[r3] = r6     // Catch:{ all -> 0x0186 }
            r0.b(r10, r2)     // Catch:{ all -> 0x0186 }
            goto L_0x018a
        L_0x0185:
            return r0
        L_0x0186:
            r14 = move-exception
            defpackage.jji.b(r14)
        L_0x018a:
            jkd r14 = defpackage.jkd.a
            return r14
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.elz.bk(java.lang.Object):java.lang.Object");
    }

    public final jlr c(Object obj, jlr jlr) {
        elz elz = new elz(this.j, this.f, this.g, this.h, this.i, jlr);
        elz.m = obj;
        return elz;
    }
}
