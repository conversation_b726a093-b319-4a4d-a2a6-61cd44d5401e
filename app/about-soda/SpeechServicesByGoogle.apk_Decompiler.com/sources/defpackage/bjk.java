package defpackage;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.os.SystemClock;
import android.util.AttributeSet;
import android.util.SparseArray;
import android.util.SparseIntArray;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import com.android.car.ui.FocusArea;
import com.google.android.tts.R;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/* renamed from: bjk  reason: default package */
/* compiled from: PG */
public final class bjk {
    public static final List a = Arrays.asList(new Integer[]{17, 66, 33, 130});
    public static final List b = Arrays.asList(new Integer[]{1, 16777216, 33554432, 536870912});
    public final ViewTreeObserver.OnTouchModeChangeListener A;
    public boolean B = true;
    public final long C;
    public long D;
    public FocusArea E;
    public final bvj F;
    private boolean G;
    private final SparseIntArray H;
    private final Set I;
    public final ViewGroup c;
    public boolean d;
    public final boolean e;
    public final boolean f;
    public final Drawable g;
    public final Drawable h;
    public int i;
    public int j;
    public int k;
    public int l;
    public int m;
    public int n;
    public int o;
    public int p;
    public boolean q;
    public int r;
    public View s;
    public SparseArray t;
    public final SparseIntArray u;
    public SparseArray v;
    public boolean w;
    public final boolean x;
    public View y;
    public final ViewTreeObserver.OnGlobalFocusChangeListener z;

    public bjk(ViewGroup viewGroup, AttributeSet attributeSet) {
        boolean z2;
        int i2;
        int i3;
        int i4;
        boolean z3;
        SparseIntArray sparseIntArray = new SparseIntArray();
        this.H = sparseIntArray;
        SparseIntArray sparseIntArray2 = new SparseIntArray();
        this.u = sparseIntArray2;
        HashSet hashSet = new HashSet();
        this.I = hashSet;
        boolean z4 = true;
        this.c = viewGroup;
        this.z = new bjl(this, 1);
        Context context = viewGroup.getContext();
        Resources resources = context.getResources();
        this.e = resources.getBoolean(R.bool.car_ui_enable_focus_area_foreground_highlight);
        this.f = resources.getBoolean(R.bool.car_ui_enable_focus_area_background_highlight);
        this.g = resources.getDrawable(R.drawable.car_ui_focus_area_foreground_highlight, context.getTheme());
        this.h = resources.getDrawable(R.drawable.car_ui_focus_area_background_highlight, context.getTheme());
        this.x = resources.getBoolean(R.bool.car_ui_clear_focus_area_history_when_rotating);
        this.F = new bvj(resources.getInteger(R.integer.car_ui_focus_history_cache_type), resources.getInteger(R.integer.car_ui_focus_history_expiration_period_ms), resources.getInteger(R.integer.car_ui_focus_area_history_cache_type), resources.getInteger(R.integer.car_ui_focus_area_history_expiration_period_ms));
        this.C = (long) resources.getInteger(R.integer.car_ui_touch_mode_skip_restore_focus_ms);
        this.A = new bjm(this, 1);
        viewGroup.setImportantForAccessibility(1);
        viewGroup.setWillNotDraw(false);
        if (attributeSet != null) {
            TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, bjo.j);
            try {
                this.r = obtainStyledAttributes.getResourceId(1, -1);
                int dimensionPixelSize = obtainStyledAttributes.getDimensionPixelSize(7, -1);
                dimensionPixelSize = dimensionPixelSize == -1 ? obtainStyledAttributes.getDimensionPixelSize(6, 0) : dimensionPixelSize;
                int dimensionPixelSize2 = obtainStyledAttributes.getDimensionPixelSize(5, -1);
                dimensionPixelSize2 = dimensionPixelSize2 == -1 ? obtainStyledAttributes.getDimensionPixelSize(6, 0) : dimensionPixelSize2;
                int layoutDirection = viewGroup.getLayoutDirection();
                if (layoutDirection == 1) {
                    z2 = true;
                } else {
                    z2 = false;
                }
                this.q = z2;
                if (layoutDirection == 1) {
                    i2 = dimensionPixelSize2;
                } else {
                    i2 = dimensionPixelSize;
                }
                this.i = i2;
                if (layoutDirection == 1) {
                    i3 = dimensionPixelSize;
                } else {
                    i3 = dimensionPixelSize2;
                }
                this.j = i3;
                int dimensionPixelSize3 = obtainStyledAttributes.getDimensionPixelSize(8, -1);
                this.k = dimensionPixelSize3;
                if (dimensionPixelSize3 == -1) {
                    this.k = obtainStyledAttributes.getDimensionPixelSize(9, 0);
                }
                int dimensionPixelSize4 = obtainStyledAttributes.getDimensionPixelSize(4, -1);
                this.l = dimensionPixelSize4;
                if (dimensionPixelSize4 == -1) {
                    this.l = obtainStyledAttributes.getDimensionPixelSize(9, 0);
                }
                int dimensionPixelSize5 = obtainStyledAttributes.getDimensionPixelSize(25, -1);
                dimensionPixelSize5 = dimensionPixelSize5 == -1 ? obtainStyledAttributes.getDimensionPixelSize(10, dimensionPixelSize) : dimensionPixelSize5;
                int dimensionPixelSize6 = obtainStyledAttributes.getDimensionPixelSize(3, -1);
                dimensionPixelSize6 = dimensionPixelSize6 == -1 ? obtainStyledAttributes.getDimensionPixelSize(10, dimensionPixelSize2) : dimensionPixelSize6;
                boolean z5 = this.q;
                if (true != z5) {
                    i4 = dimensionPixelSize5;
                } else {
                    i4 = dimensionPixelSize6;
                }
                this.m = i4;
                if (true != z5) {
                    dimensionPixelSize5 = dimensionPixelSize6;
                }
                this.n = dimensionPixelSize5;
                int dimensionPixelSize7 = obtainStyledAttributes.getDimensionPixelSize(26, -1);
                this.o = dimensionPixelSize7;
                if (dimensionPixelSize7 == -1) {
                    this.o = obtainStyledAttributes.getDimensionPixelSize(27, this.k);
                }
                int dimensionPixelSize8 = obtainStyledAttributes.getDimensionPixelSize(0, -1);
                this.p = dimensionPixelSize8;
                if (dimensionPixelSize8 == -1) {
                    this.p = obtainStyledAttributes.getDimensionPixelSize(27, this.l);
                }
                if (obtainStyledAttributes.hasValue(16)) {
                    sparseIntArray.put(17, obtainStyledAttributes.getResourceId(16, -1));
                }
                if (obtainStyledAttributes.hasValue(19)) {
                    sparseIntArray.put(66, obtainStyledAttributes.getResourceId(19, -1));
                }
                if (obtainStyledAttributes.hasValue(24)) {
                    sparseIntArray.put(33, obtainStyledAttributes.getResourceId(24, -1));
                }
                if (obtainStyledAttributes.hasValue(13)) {
                    sparseIntArray.put(130, obtainStyledAttributes.getResourceId(13, -1));
                }
                int resourceId = obtainStyledAttributes.getResourceId(20, -1);
                int i5 = obtainStyledAttributes.getInt(21, -1);
                if (resourceId == -1) {
                    z3 = true;
                } else {
                    z3 = false;
                }
                if (i5 != -1) {
                    z4 = false;
                }
                if (!(z4 ^ z3)) {
                    if (resourceId != -1) {
                        if (sparseIntArray.size() <= 0) {
                            sparseIntArray.put(i5, resourceId);
                        } else {
                            throw new IllegalStateException("Don't use nudgeShortcut/nudgeShortcutDirection and nudge*Shortcut in the same focus area. Use nudge*Shortcut only.");
                        }
                    }
                    if (obtainStyledAttributes.hasValue(14)) {
                        sparseIntArray2.put(17, obtainStyledAttributes.getResourceId(14, -1));
                    }
                    if (obtainStyledAttributes.hasValue(17)) {
                        sparseIntArray2.put(66, obtainStyledAttributes.getResourceId(17, -1));
                    }
                    if (obtainStyledAttributes.hasValue(22)) {
                        sparseIntArray2.put(33, obtainStyledAttributes.getResourceId(22, -1));
                    }
                    if (obtainStyledAttributes.hasValue(11)) {
                        sparseIntArray2.put(130, obtainStyledAttributes.getResourceId(11, -1));
                    }
                    if (obtainStyledAttributes.getBoolean(15, false)) {
                        hashSet.add(17);
                    }
                    if (obtainStyledAttributes.getBoolean(18, false)) {
                        hashSet.add(66);
                    }
                    if (obtainStyledAttributes.getBoolean(23, false)) {
                        hashSet.add(33);
                    }
                    if (obtainStyledAttributes.getBoolean(12, false)) {
                        hashSet.add(130);
                    }
                    this.G = obtainStyledAttributes.getBoolean(2, false);
                    this.w = obtainStyledAttributes.getBoolean(28, false);
                    return;
                }
                throw new IllegalStateException("nudgeShortcut and nudgeShortcutDirection must be specified together");
            } finally {
                obtainStyledAttributes.recycle();
            }
        }
    }

    public static int a(Bundle bundle) {
        if (bundle == null) {
            return -1;
        }
        return bundle.getInt("com.android.car.ui.utils.NUDGE_DIRECTION", -1);
    }

    public final void b() {
        if (this.t == null) {
            View rootView = this.c.getRootView();
            this.t = new SparseArray();
            for (Integer intValue : a) {
                int intValue2 = intValue.intValue();
                this.t.put(intValue2, rootView.findViewById(this.H.get(intValue2, -1)));
            }
        }
    }

    public final boolean c() {
        return yi.w(this.c, 1, ((bjq) this.F.a).a(SystemClock.uptimeMillis()), this.G, false);
    }

    public final boolean d(int i2) {
        return this.I.contains(Integer.valueOf(i2));
    }

    public final boolean e() {
        View rootView = this.c.getRootView();
        return yi.v(rootView, yi.o(rootView.findFocus()), (View) null, false);
    }

    public final boolean f() {
        if (SystemClock.uptimeMillis() > this.D) {
            return false;
        }
        this.D = 0;
        return true;
    }
}
