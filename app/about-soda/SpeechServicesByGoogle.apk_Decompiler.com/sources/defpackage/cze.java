package defpackage;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;

/* renamed from: cze  reason: default package */
/* compiled from: PG */
public final class cze {
    public final boolean a;
    private final Object b;
    private final Object c;

    public cze(boolean z, Object obj, Object obj2) {
        this.a = z;
        this.b = obj;
        this.c = obj2;
    }

    public static boolean d(cze cze, cze cze2, Comparator comparator) {
        Object obj;
        if (cze == null || !cze.a || (obj = cze.b) == null || cze2 == null || !cze2.a || cze2.b == null) {
            return a.k(cze, cze2);
        }
        ArrayList arrayList = new ArrayList((Collection) obj);
        ArrayList arrayList2 = new ArrayList((Collection) cze2.b);
        Collections.sort(arrayList, comparator);
        Collections.sort(arrayList2, comparator);
        return arrayList.equals(arrayList2);
    }

    public final Object a() {
        if (this.a) {
            return this.b;
        }
        throw new IllegalStateException("Either was not left");
    }

    public final Object b() {
        if (c()) {
            return this.c;
        }
        throw new IllegalStateException("Either was not right");
    }

    public final boolean c() {
        if (!this.a) {
            return true;
        }
        return false;
    }

    public final boolean equals(Object obj) {
        if (!(obj instanceof cze)) {
            return false;
        }
        cze cze = (cze) obj;
        if (this.a) {
            if (!cze.a || !a.k(a(), cze.a())) {
                return false;
            }
            return true;
        } else if (!cze.c() || !a.k(b(), cze.b())) {
            return false;
        } else {
            return true;
        }
    }

    public final int hashCode() {
        return Arrays.hashCode(new Object[]{Boolean.valueOf(this.a), this.b, this.c});
    }
}
