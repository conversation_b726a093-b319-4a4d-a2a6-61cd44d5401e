package defpackage;

/* renamed from: csp  reason: default package */
/* compiled from: PG */
public final class csp {
    public final String a;
    public final boolean b;
    public final boolean c;
    private final grh d;
    private final grh e;

    public csp() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof csp) {
            csp csp = (csp) obj;
            if (!this.a.equals(csp.a) || !this.d.equals(csp.d) || !this.e.equals(csp.e) || this.b != csp.b || this.c != csp.c) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        int hashCode = this.a.hashCode() ^ 1000003;
        int i2 = 1237;
        if (true != this.b) {
            i = 1237;
        } else {
            i = 1231;
        }
        int i3 = ((((((hashCode * 1000003) ^ **********) * 1000003) ^ **********) * 1000003) ^ i) * 1000003;
        if (true == this.c) {
            i2 = 1231;
        }
        return i3 ^ i2;
    }

    public final String toString() {
        grh grh = this.e;
        String valueOf = String.valueOf(this.d);
        String valueOf2 = String.valueOf(grh);
        return "GetFileGroupRequest{groupName=" + this.a + ", accountOptional=" + valueOf + ", variantIdOptional=" + valueOf2 + ", preserveZipDirectories=" + this.b + ", verifyIsolatedStructure=" + this.c + "}";
    }

    public csp(String str, grh grh, grh grh2, boolean z, boolean z2) {
        this.a = str;
        this.d = grh;
        this.e = grh2;
        this.b = z;
        this.c = z2;
    }
}
