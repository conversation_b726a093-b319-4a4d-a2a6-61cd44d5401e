<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:orientation="vertical" android:layout_width="match_parent" android:layout_height="match_parent" android:layout_marginTop="@dimen/car_ui_preference_edit_text_dialog_margin_top" android:layout_marginBottom="@dimen/car_ui_preference_edit_text_dialog_margin_bottom">
    <TextView android:id="@android:id/message" android:visibility="gone" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginBottom="@dimen/car_ui_preference_edit_text_dialog_message_margin_bottom" android:layout_marginStart="@dimen/car_ui_preference_edit_text_dialog_message_margin_start" android:layout_marginEnd="@dimen/car_ui_preference_edit_text_dialog_message_margin_end" style="@style/TextAppearance.CarUi.PreferenceEditTextDialogMessage"/>
    <com.android.car.ui.toolbar.CarUiEditText android:id="@android:id/edit" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginStart="@dimen/car_ui_preference_edit_text_dialog_text_margin_start" android:layout_marginEnd="@dimen/car_ui_preference_edit_text_dialog_text_margin_end"/>
</LinearLayout>
