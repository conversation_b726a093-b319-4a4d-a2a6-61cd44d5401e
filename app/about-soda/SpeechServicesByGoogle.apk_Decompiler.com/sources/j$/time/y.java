package j$.time;

import j$.time.zone.e;
import j$.time.zone.f;
import j$.time.zone.i;
import j$.util.Objects;
import java.io.DataOutput;
import java.io.InvalidObjectException;
import java.io.ObjectInputStream;
import java.io.ObjectOutput;

final class y extends ZoneId {
    public static final /* synthetic */ int d = 0;
    private static final long serialVersionUID = 8386373296231747096L;
    private final String b;
    private final transient e c;

    y(String str, e eVar) {
        this.b = str;
        this.c = eVar;
    }

    static y N(String str, boolean z) {
        e eVar;
        Objects.requireNonNull(str, "zoneId");
        int length = str.length();
        if (length >= 2) {
            for (int i = 0; i < length; i++) {
                char charAt = str.charAt(i);
                if ((charAt < 'a' || charAt > 'z') && ((charAt < 'A' || charAt > 'Z') && ((charAt != '/' || i == 0) && ((charAt < '0' || charAt > '9' || i == 0) && ((charAt != '~' || i == 0) && ((charAt != '.' || i == 0) && ((charAt != '_' || i == 0) && ((charAt != '+' || i == 0) && (charAt != '-' || i == 0))))))))) {
                    throw new RuntimeException("Invalid ID for region-based ZoneId, invalid format: ".concat(str));
                }
            }
            try {
                eVar = i.a(str, true);
            } catch (f e) {
                if (!z) {
                    eVar = null;
                } else {
                    throw e;
                }
            }
            return new y(str, eVar);
        }
        throw new RuntimeException("Invalid ID for region-based ZoneId, invalid format: ".concat(str));
    }

    private void readObject(ObjectInputStream objectInputStream) {
        throw new InvalidObjectException("Deserialization via serialization delegate");
    }

    private Object writeReplace() {
        return new s((byte) 7, this);
    }

    public final e I() {
        e eVar = this.c;
        if (eVar != null) {
            return eVar;
        }
        return i.a(this.b, false);
    }

    /* access modifiers changed from: package-private */
    public final void M(ObjectOutput objectOutput) {
        objectOutput.writeByte(7);
        objectOutput.writeUTF(this.b);
    }

    /* access modifiers changed from: package-private */
    public final void O(DataOutput dataOutput) {
        dataOutput.writeUTF(this.b);
    }

    public final String l() {
        return this.b;
    }
}
