package com.android.car.ui.toolbar;

import j$.util.function.Function$CC;
import java.util.function.Function;

/* compiled from: PG */
public final /* synthetic */ class ToolbarControllerAdapterV3$$ExternalSyntheticLambda2 implements Function {
    public /* synthetic */ Function andThen(Function function) {
        return Function$CC.$default$andThen(this, function);
    }

    public final Object apply(Object obj) {
        return new TabAdapterV1((bng) obj);
    }

    public /* synthetic */ Function compose(Function function) {
        return Function$CC.$default$compose(this, function);
    }
}
