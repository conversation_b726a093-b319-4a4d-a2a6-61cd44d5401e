package defpackage;

import android.content.Context;
import androidx.work.impl.workers.ConstraintTrackingWorker;
import java.util.concurrent.atomic.AtomicInteger;

/* renamed from: biw  reason: default package */
/* compiled from: PG */
public final class biw extends jmi implements jne {
    int a;
    final /* synthetic */ Object b;
    final /* synthetic */ Object c;
    final /* synthetic */ Object d;
    final /* synthetic */ Object e;
    private final /* synthetic */ int f;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public biw(ConstraintTrackingWorker constraintTrackingWorker, bbj bbj, byw byw, bhe bhe, jlr jlr, int i) {
        super(2, jlr);
        this.f = i;
        this.d = constraintTrackingWorker;
        this.b = bbj;
        this.c = byw;
        this.e = bhe;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        int i = this.f;
        if (i == 0) {
            return ((biw) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        } else if (i != 1) {
            return ((biw) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        } else {
            return ((biw) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
    }

    /* JADX WARNING: type inference failed for: r6v4, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r6v15, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: Code restructure failed: missing block: B:15:0x004c, code lost:
        if (r6 != r0) goto L_0x004e;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r6) {
        /*
            r5 = this;
            int r0 = r5.f
            r1 = 1
            if (r0 == 0) goto L_0x00a1
            if (r0 == r1) goto L_0x002b
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r5.a
            defpackage.jji.c(r6)
            if (r2 == 0) goto L_0x0011
            goto L_0x002a
        L_0x0011:
            java.lang.Object r6 = r5.d
            java.lang.Object r2 = r5.b
            java.lang.Object r3 = r5.c
            java.lang.Object r4 = r5.e
            r5.a = r1
            bhe r4 = (defpackage.bhe) r4
            byw r3 = (defpackage.byw) r3
            bbj r2 = (defpackage.bbj) r2
            androidx.work.impl.workers.ConstraintTrackingWorker r6 = (androidx.work.impl.workers.ConstraintTrackingWorker) r6
            java.lang.Object r6 = r6.j(r2, r3, r4, r5)
            if (r6 != r0) goto L_0x002a
            return r0
        L_0x002a:
            return r6
        L_0x002b:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r5.a
            if (r2 == 0) goto L_0x003b
            if (r2 == r1) goto L_0x0037
            defpackage.jji.c(r6)
            goto L_0x0080
        L_0x0037:
            defpackage.jji.c(r6)
            goto L_0x004e
        L_0x003b:
            defpackage.jji.c(r6)
            java.lang.Object r6 = r5.e
            bbj r6 = (defpackage.bbj) r6
            hme r2 = r6.a()
            r5.a = r1
            java.lang.Object r6 = defpackage.bdy.a(r2, r6, r5)
            if (r6 == r0) goto L_0x00a0
        L_0x004e:
            baz r6 = (defpackage.baz) r6
            if (r6 == 0) goto L_0x0081
            int r1 = defpackage.bin.a
            defpackage.bbk.a()
            java.lang.Object r1 = r5.c
            java.lang.Object r2 = r5.b
            java.lang.Object r3 = r5.e
            bbj r3 = (defpackage.bbj) r3
            java.util.UUID r3 = r3.e()
            bio r4 = new bio
            android.content.Context r2 = (android.content.Context) r2
            bip r1 = (defpackage.bip) r1
            r4.<init>(r1, r3, r6, r2)
            cyw r6 = r1.c
            java.lang.Object r6 = r6.a
            java.lang.String r1 = "setForegroundAsync"
            hme r6 = androidx.wear.ambient.AmbientLifecycleObserverKt.a(r6, r1, r4)
            r1 = 2
            r5.a = r1
            java.lang.Object r6 = defpackage.kq.d(r6, r5)
            if (r6 != r0) goto L_0x0080
            goto L_0x00a0
        L_0x0080:
            return r6
        L_0x0081:
            java.lang.StringBuilder r6 = new java.lang.StringBuilder
            java.lang.String r0 = "Worker was marked important ("
            r6.<init>(r0)
            java.lang.Object r0 = r5.d
            bhe r0 = (defpackage.bhe) r0
            java.lang.String r0 = r0.d
            r6.append(r0)
            java.lang.String r0 = ") but did not provide ForegroundInfo"
            r6.append(r0)
            java.lang.String r6 = r6.toString()
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            r0.<init>(r6)
            throw r0
        L_0x00a0:
            return r0
        L_0x00a1:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r5.a
            if (r2 == 0) goto L_0x00ab
            defpackage.jji.c(r6)
            goto L_0x00bf
        L_0x00ab:
            defpackage.jji.c(r6)
            java.lang.Object r6 = r5.b
            java.lang.Object r2 = r5.c
            r5.a = r1
            bhe r2 = (defpackage.bhe) r2
            byw r6 = (defpackage.byw) r6
            java.lang.Object r6 = defpackage.bje.a(r6, r2, r5)
            if (r6 != r0) goto L_0x00bf
            return r0
        L_0x00bf:
            java.lang.Number r6 = (java.lang.Number) r6
            int r6 = r6.intValue()
            java.lang.Object r0 = r5.d
            java.util.concurrent.atomic.AtomicInteger r0 = (java.util.concurrent.atomic.AtomicInteger) r0
            r0.set(r6)
            java.lang.Object r6 = r5.e
            r6.cancel(r1)
            jkd r6 = defpackage.jkd.a
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.biw.bk(java.lang.Object):java.lang.Object");
    }

    /* JADX WARNING: type inference failed for: r6v0, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v2, types: [bba, java.lang.Object] */
    public final jlr c(Object obj, jlr jlr) {
        int i = this.f;
        if (i == 0) {
            Object obj2 = this.b;
            Object obj3 = this.c;
            return new biw((byw) obj2, (bhe) obj3, (AtomicInteger) this.d, (hme) this.e, jlr, 0);
        } else if (i != 1) {
            Object obj4 = this.d;
            Object obj5 = this.b;
            return new biw((ConstraintTrackingWorker) obj4, (bbj) obj5, (byw) this.c, (bhe) this.e, jlr, 2);
        } else {
            Object obj6 = this.e;
            return new biw((bbj) obj6, (bhe) this.d, (bba) this.c, (Context) this.b, jlr, 1);
        }
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public biw(bbj bbj, bhe bhe, bba bba, Context context, jlr jlr, int i) {
        super(2, jlr);
        this.f = i;
        this.e = bbj;
        this.d = bhe;
        this.c = bba;
        this.b = context;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public biw(byw byw, bhe bhe, AtomicInteger atomicInteger, hme hme, jlr jlr, int i) {
        super(2, jlr);
        this.f = i;
        this.b = byw;
        this.c = bhe;
        this.d = atomicInteger;
        this.e = hme;
    }
}
