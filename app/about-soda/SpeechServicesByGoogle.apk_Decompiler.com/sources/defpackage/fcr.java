package defpackage;

/* renamed from: fcr  reason: default package */
/* compiled from: PG */
public final class fcr implements iiu {
    public final /* synthetic */ Object b() {
        String str;
        String str2;
        boolean z;
        boolean z2;
        boolean z3;
        boolean z4;
        boolean z5;
        boolean z6;
        boolean z7;
        String[] strArr;
        boolean z8;
        boolean z9 = true;
        fvf.aw(true, "property name must be defined");
        fvf.aw(true, "default hostname must be defined");
        fvf.aw(true, "defaultPort must be bigger than 0");
        bzj bzj = fmy.a;
        String str3 = "speechs3proto2-pa.googleapis.com:443";
        if ("true".equals(fni.a("debug.social", "true")) && "true".equals(fni.a((String) bzj.a, "true")) && (str3 = fni.a("s3.grpc.url", str3)) != null && str3.length() == 91) {
            StringBuilder sb = new StringBuilder(str3);
            int i = 2;
            while (true) {
                int i2 = i + 1;
                String a = fni.a(a.ak(i, "s3.grpc.url"), "");
                sb.append(a);
                if (a.length() != 91) {
                    break;
                }
                i = i2;
            }
            str3 = sb.toString();
        }
        fvf.aP(str3);
        int i3 = -1;
        String str4 = null;
        if (str3.startsWith("[")) {
            if (str3.charAt(0) == '[') {
                z6 = true;
            } else {
                z6 = false;
            }
            fvf.aA(z6, "Bracketed host-port string must start with a bracket: %s", str3);
            int indexOf = str3.indexOf(58);
            int lastIndexOf = str3.lastIndexOf(93);
            if (indexOf < 0 || lastIndexOf <= indexOf) {
                z7 = false;
            } else {
                z7 = true;
            }
            fvf.aA(z7, "Invalid bracketed host/port: %s", str3);
            String substring = str3.substring(1, lastIndexOf);
            int i4 = lastIndexOf + 1;
            if (i4 == str3.length()) {
                strArr = new String[]{substring, ""};
            } else {
                if (str3.charAt(i4) == ':') {
                    z8 = true;
                } else {
                    z8 = false;
                }
                fvf.aA(z8, "Only a colon may follow a close bracket: %s", str3);
                int i5 = lastIndexOf + 2;
                for (int i6 = i5; i6 < str3.length(); i6++) {
                    fvf.aA(Character.isDigit(str3.charAt(i6)), "Port must be numeric: %s", str3);
                }
                strArr = new String[]{substring, str3.substring(i5)};
            }
            str2 = strArr[0];
            str = strArr[1];
        } else {
            int indexOf2 = str3.indexOf(58);
            if (indexOf2 >= 0) {
                int i7 = indexOf2 + 1;
                if (str3.indexOf(58, i7) == -1) {
                    str2 = str3.substring(0, indexOf2);
                    str = str3.substring(i7);
                }
            }
            str = null;
            str2 = str3;
        }
        if (!ftd.p(str)) {
            if (str.startsWith("+") || !gqg.a.e(str)) {
                z4 = false;
            } else {
                z4 = true;
            }
            fvf.aA(z4, "Unparseable port number: %s", str3);
            try {
                i3 = Integer.parseInt(str);
                if (i3 < 0 || i3 > 65535) {
                    z5 = false;
                } else {
                    z5 = true;
                }
                fvf.aA(z5, "Port number out of range: %s", str3);
            } catch (NumberFormatException unused) {
                throw new IllegalArgumentException("Unparseable port number: ".concat(String.valueOf(str3)));
            }
        }
        hjc hjc = new hjc(str2, i3);
        if (!hjc.a.startsWith("speechs3proto2-pa.googleapis.com")) {
            ((hby) ((hby) evl.a.f()).j("com/google/android/libraries/search/hostname/Hostname", "propOrDefault", 40, "Hostname.java")).G("gRPC hostname was overwritten. %s:%d -> %s", "speechs3proto2-pa.googleapis.com", 443, hjc);
        }
        String str5 = hjc.a;
        fvf.aF(hjc.a());
        Integer valueOf = Integer.valueOf(hjc.b);
        iad iad = iad.f;
        if (iad != null) {
            ghm ghm = new ghm(str5, valueOf, iad);
            String str6 = ghm.a;
            if (ghm.b != null) {
                z = false;
            } else {
                z = true;
            }
            if (str6 != null) {
                z2 = false;
            } else {
                z2 = true;
            }
            if (z2 == z) {
                z3 = true;
            } else {
                z3 = false;
            }
            fvf.aG(z3, "If host is set, port must be set (and vice-versa)");
            if (str6 != null) {
                if (str6.matches("[a-zA-Z0-9-.]+")) {
                    str4 = str6;
                } else {
                    str4 = str6;
                    z9 = false;
                }
            }
            fvf.aJ(z9, "Host string is invalid: %s", str4);
            return ghm;
        }
        throw new NullPointerException("Null rpcServiceConfig");
    }
}
