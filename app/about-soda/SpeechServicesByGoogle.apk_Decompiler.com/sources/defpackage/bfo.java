package defpackage;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

/* renamed from: bfo  reason: default package */
/* compiled from: PG */
public final class bfo extends bfr {
    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bfo(Context context, cyw cyw) {
        super(context, cyw);
        jnu.e(context, "context");
    }

    public final IntentFilter a() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("android.intent.action.BATTERY_OKAY");
        intentFilter.addAction("android.intent.action.BATTERY_LOW");
        return intentFilter;
    }

    public final /* bridge */ /* synthetic */ Object b() {
        Intent registerReceiver = this.a.registerReceiver((BroadcastReceiver) null, new IntentFilter("android.intent.action.BATTERY_CHANGED"));
        boolean z = false;
        if (registerReceiver == null) {
            bbk.a().c(bfp.a, "getInitialState - null intent received");
            return false;
        }
        int intExtra = registerReceiver.getIntExtra("status", -1);
        float intExtra2 = (float) registerReceiver.getIntExtra("level", -1);
        float intExtra3 = (float) registerReceiver.getIntExtra("scale", -1);
        if (intExtra == 1 || intExtra2 / intExtra3 > 0.15f) {
            z = true;
        }
        return Boolean.valueOf(z);
    }

    public final void c(Intent intent) {
        jnu.e(intent, "intent");
        if (intent.getAction() != null) {
            bbk.a();
            String str = bfp.a;
            intent.getAction();
            String action = intent.getAction();
            if (action != null) {
                int hashCode = action.hashCode();
                if (hashCode != -1980154005) {
                    if (hashCode == 490310653 && action.equals("android.intent.action.BATTERY_LOW")) {
                        f(false);
                    }
                } else if (action.equals("android.intent.action.BATTERY_OKAY")) {
                    f(true);
                }
            }
        }
    }
}
