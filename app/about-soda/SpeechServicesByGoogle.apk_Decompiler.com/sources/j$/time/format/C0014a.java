package j$.time.format;

import j$.time.ZoneId;
import j$.time.temporal.l;
import j$.time.temporal.m;
import j$.time.temporal.r;
import j$.time.x;

/* renamed from: j$.time.format.a  reason: case insensitive filesystem */
public final /* synthetic */ class C0014a implements r {
    public final Object a(m mVar) {
        ZoneId zoneId = (ZoneId) mVar.F(l.k());
        if (zoneId == null || (zoneId instanceof x)) {
            return null;
        }
        return zoneId;
    }
}
