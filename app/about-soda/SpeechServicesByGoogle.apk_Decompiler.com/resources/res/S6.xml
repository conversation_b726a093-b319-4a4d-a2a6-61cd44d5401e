<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:background="@drawable/car_ui_app_styled_view_background" android:padding="0dp" android:layout_width="match_parent" android:layout_height="match_parent">
    <com.android.car.ui.FocusParkingView android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintTop_toTopOf="0"/>
    <com.android.car.ui.FocusArea android:id="@+id/car_ui_focus_area" android:layout_width="match_parent" android:layout_height="@dimen/car_ui_toolbar_first_row_height" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintTop_toTopOf="0">
        <FrameLayout android:id="@+id/car_ui_app_styled_view_nav_icon_container" android:background="@drawable/car_ui_app_styled_view_ripple" android:layout_width="@dimen/car_ui_toolbar_margin" android:layout_height="match_parent" android:layout_marginHorizontal="18dp" android:layout_marginVertical="10dp" app:layout_constraintHorizontal_bias="0">
            <ImageView android:layout_gravity="center" android:id="@+id/car_ui_app_styled_view_icon_close" android:layout_width="@dimen/car_ui_toolbar_nav_icon_size" android:layout_height="@dimen/car_ui_toolbar_nav_icon_size" android:scaleType="fitXY" android:tint="@color/car_ui_toolbar_nav_icon_color"/>
        </FrameLayout>
    </com.android.car.ui.FocusArea>
    <ScrollView android:scrollbars="none" android:layout_width="match_parent" android:layout_height="0dp" android:fillViewport="true" android:overScrollMode="never" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/car_ui_focus_area">
        <FrameLayout android:id="@+id/car_ui_app_styled_content" android:layout_width="match_parent" android:layout_height="wrap_content"/>
    </ScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>
