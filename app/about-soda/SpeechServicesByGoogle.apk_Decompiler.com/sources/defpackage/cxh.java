package defpackage;

/* renamed from: cxh  reason: default package */
/* compiled from: PG */
public final class cxh {
    public final csx a;
    public final csx b;

    public cxh() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof cxh) {
            cxh cxh = (cxh) obj;
            csx csx = this.a;
            if (csx != null ? csx.equals(cxh.a) : cxh.a == null) {
                csx csx2 = this.b;
                csx csx3 = cxh.b;
                if (csx2 != null ? csx2.equals(csx3) : csx3 == null) {
                    return true;
                }
            }
        }
        return false;
    }

    public final int hashCode() {
        int i;
        csx csx = this.a;
        int i2 = 0;
        if (csx == null) {
            i = 0;
        } else if (csx.B()) {
            i = csx.i();
        } else {
            int i3 = csx.memoizedHashCode;
            if (i3 == 0) {
                i3 = csx.i();
                csx.memoizedHashCode = i3;
            }
            i = i3;
        }
        csx csx2 = this.b;
        if (csx2 != null) {
            if (csx2.B()) {
                i2 = csx2.i();
            } else {
                i2 = csx2.memoizedHashCode;
                if (i2 == 0) {
                    i2 = csx2.i();
                    csx2.memoizedHashCode = i2;
                }
            }
        }
        return ((i ^ 1000003) * 1000003) ^ i2;
    }

    public final String toString() {
        csx csx = this.b;
        String valueOf = String.valueOf(this.a);
        String valueOf2 = String.valueOf(csx);
        return "GroupPair{pendingGroup=" + valueOf + ", downloadedGroup=" + valueOf2 + "}";
    }

    public cxh(csx csx, csx csx2) {
        this.a = csx;
        this.b = csx2;
    }
}
