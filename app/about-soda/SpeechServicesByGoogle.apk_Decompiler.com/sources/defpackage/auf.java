package defpackage;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteCallbackList;
import android.os.RemoteException;
import android.util.Log;
import androidx.room.MultiInstanceInvalidationService;

/* renamed from: auf  reason: default package */
/* compiled from: PG */
public final class auf extends Binder implements aug {
    final /* synthetic */ MultiInstanceInvalidationService a;

    public auf() {
        attachInterface(this, b);
    }

    public final boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) {
        String str = b;
        if (i > 0 && i <= 16777215) {
            parcel.enforceInterface(str);
        }
        if (i == 1598968902) {
            parcel2.writeString(str);
            return true;
        }
        IInterface iInterface = null;
        int i3 = 0;
        if (i == 1) {
            IBinder readStrongBinder = parcel.readStrongBinder();
            int i4 = aud.a;
            if (readStrongBinder != null) {
                IInterface queryLocalInterface = readStrongBinder.queryLocalInterface(aud.b);
                if (queryLocalInterface == null || !(queryLocalInterface instanceof aue)) {
                    iInterface = new auc(readStrongBinder);
                } else {
                    iInterface = (aue) queryLocalInterface;
                }
            }
            String readString = parcel.readString();
            jnu.e(iInterface, "callback");
            if (readString != null) {
                MultiInstanceInvalidationService multiInstanceInvalidationService = this.a;
                synchronized (multiInstanceInvalidationService.c) {
                    int i5 = multiInstanceInvalidationService.a + 1;
                    multiInstanceInvalidationService.a = i5;
                    RemoteCallbackList remoteCallbackList = multiInstanceInvalidationService.c;
                    Integer valueOf = Integer.valueOf(i5);
                    if (remoteCallbackList.register(iInterface, valueOf)) {
                        multiInstanceInvalidationService.b.put(valueOf, readString);
                        i3 = i5;
                    } else {
                        multiInstanceInvalidationService.a--;
                    }
                }
            }
            parcel2.writeNoException();
            parcel2.writeInt(i3);
        } else if (i == 2) {
            IBinder readStrongBinder2 = parcel.readStrongBinder();
            int i6 = aud.a;
            if (readStrongBinder2 != null) {
                IInterface queryLocalInterface2 = readStrongBinder2.queryLocalInterface(aud.b);
                if (queryLocalInterface2 == null || !(queryLocalInterface2 instanceof aue)) {
                    iInterface = new auc(readStrongBinder2);
                } else {
                    iInterface = (aue) queryLocalInterface2;
                }
            }
            int readInt = parcel.readInt();
            jnu.e(iInterface, "callback");
            MultiInstanceInvalidationService multiInstanceInvalidationService2 = this.a;
            synchronized (multiInstanceInvalidationService2.c) {
                multiInstanceInvalidationService2.c.unregister(iInterface);
                String str2 = (String) multiInstanceInvalidationService2.b.remove(Integer.valueOf(readInt));
            }
            parcel2.writeNoException();
        } else if (i != 3) {
            return super.onTransact(i, parcel, parcel2, i2);
        } else {
            int readInt2 = parcel.readInt();
            String[] createStringArray = parcel.createStringArray();
            jnu.e(createStringArray, "tables");
            MultiInstanceInvalidationService multiInstanceInvalidationService3 = this.a;
            synchronized (multiInstanceInvalidationService3.c) {
                String str3 = (String) multiInstanceInvalidationService3.b.get(Integer.valueOf(readInt2));
                if (str3 == null) {
                    Log.w("ROOM", "Remote invalidation client ID not registered");
                } else {
                    int beginBroadcast = multiInstanceInvalidationService3.c.beginBroadcast();
                    while (i3 < beginBroadcast) {
                        try {
                            Object broadcastCookie = multiInstanceInvalidationService3.c.getBroadcastCookie(i3);
                            jnu.c(broadcastCookie, "null cannot be cast to non-null type kotlin.Int");
                            Integer num = (Integer) broadcastCookie;
                            int intValue = num.intValue();
                            String str4 = (String) multiInstanceInvalidationService3.b.get(num);
                            if (readInt2 != intValue && jnu.i(str3, str4)) {
                                ((aue) multiInstanceInvalidationService3.c.getBroadcastItem(i3)).a(createStringArray);
                            }
                        } catch (RemoteException e) {
                            Log.w("ROOM", "Error invoking a remote callback", e);
                        } catch (Throwable th) {
                            multiInstanceInvalidationService3.c.finishBroadcast();
                            throw th;
                        }
                        i3++;
                    }
                    multiInstanceInvalidationService3.c.finishBroadcast();
                }
            }
        }
        return true;
    }

    public auf(MultiInstanceInvalidationService multiInstanceInvalidationService) {
        this.a = multiInstanceInvalidationService;
        attachInterface(this, b);
    }

    public final IBinder asBinder() {
        return this;
    }
}
