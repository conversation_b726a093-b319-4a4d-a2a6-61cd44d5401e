package defpackage;

import android.database.Cursor;

/* renamed from: bgj  reason: default package */
/* compiled from: PG */
public final class bgj implements bgh {
    private final aus a;
    private final aub b;

    public bgj(aus aus) {
        this.a = aus;
        this.b = new bgi(aus);
    }

    public final Long a(String str) {
        auu a2 = auu.a("SELECT long_value FROM Preference where `key`=?", 1);
        a2.g(1, str);
        this.a.k();
        Cursor f = vy.f(this.a, a2, false);
        try {
            Long l = null;
            if (f.moveToFirst()) {
                if (!f.isNull(0)) {
                    l = Long.valueOf(f.getLong(0));
                }
            }
            return l;
        } finally {
            f.close();
            a2.j();
        }
    }

    public final void b(bgg bgg) {
        this.a.k();
        this.a.l();
        try {
            this.b.b(bgg);
            this.a.o();
        } finally {
            this.a.m();
        }
    }
}
