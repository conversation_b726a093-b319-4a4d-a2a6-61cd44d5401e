package j$.time.temporal;

public enum a implements p {
    NANO_OF_SECOND("NanoOfSecond", r4, r17, t.j(0, 999999999)),
    NANO_OF_DAY("NanoOfDay", r4, r27, t.j(0, 86399999999999L)),
    MICRO_OF_SECOND("MicroOfSecond", r4, r17, t.j(0, 999999)),
    MICRO_OF_DAY("MicroOfDay", r4, r27, t.j(0, 86399999999L)),
    MILLI_OF_SECOND("MilliOfSecond", r4, r17, t.j(0, 999)),
    MILLI_OF_DAY("MilliOfDay", r4, r27, t.j(0, 86399999)),
    SECOND_OF_MINUTE("SecondOfMinute", r17, r32, t.j(0, 59), 0),
    SECOND_OF_DAY("SecondOfDay", r17, r5, t.j(0, 86399)),
    MINUTE_OF_HOUR("MinuteOfHour", r32, r13, t.j(0, 59), 0),
    MINUTE_OF_DAY("MinuteOfDay", r32, r5, t.j(0, 1439)),
    HOUR_OF_AMPM("HourOfAmPm", r13, r16, t.j(0, 11)),
    CLOCK_HOUR_OF_AMPM("ClockHourOfAmPm", r4, r16, t.j(1, 12)),
    HOUR_OF_DAY("HourOfDay", r13, r24, t.j(0, 23), 0),
    CLOCK_HOUR_OF_DAY("ClockHourOfDay", r4, r27, t.j(1, 24)),
    AMPM_OF_DAY("AmPmOfDay", r16, r24, t.j(0, 1), 0),
    DAY_OF_WEEK("DayOfWeek", r27, r36, t.j(1, 7), 0),
    ALIGNED_DAY_OF_WEEK_IN_MONTH("AlignedDayOfWeekInMonth", r4, r36, t.j(1, 7)),
    ALIGNED_DAY_OF_WEEK_IN_YEAR("AlignedDayOfWeekInYear", r4, r36, t.j(1, 7)),
    DAY_OF_MONTH("DayOfMonth", r27, r15, t.k(1, 28, 31), 0),
    DAY_OF_YEAR("DayOfYear", r4, r41, t.k(1, 365, 366)),
    EPOCH_DAY("EpochDay", r4, r44, t.j(-365243219162L, 365241780471L)),
    ALIGNED_WEEK_OF_MONTH("AlignedWeekOfMonth", r23, r15, t.k(1, 4, 5)),
    ALIGNED_WEEK_OF_YEAR("AlignedWeekOfYear", r23, r24, t.j(1, 53)),
    MONTH_OF_YEAR("MonthOfYear", r15, r24, t.j(1, 12), 0),
    PROLEPTIC_MONTH("ProlepticMonth", r15, r44, t.j(-11999999988L, 11999999999L)),
    YEAR_OF_ERA("YearOfEra", r41, r5, t.k(1, 999999999, 1000000000)),
    YEAR("Year", r41, r24, t.j(-999999999, 999999999), 0),
    ERA("Era", ChronoUnit.ERAS, r24, t.j(0, 1), 0),
    INSTANT_SECONDS("InstantSeconds", r4, r5, t.j(Long.MIN_VALUE, Long.MAX_VALUE)),
    OFFSET_SECONDS("OffsetSeconds", r4, r5, t.j(-64800, 64800));
    
    private final String a;
    private final t b;

    private a(String str, ChronoUnit chronoUnit, ChronoUnit chronoUnit2, t tVar) {
        this.a = str;
        this.b = tVar;
    }

    public final boolean B() {
        if (ordinal() < DAY_OF_WEEK.ordinal() || ordinal() > ERA.ordinal()) {
            return false;
        }
        return true;
    }

    public final int F(long j) {
        return this.b.a(j, this);
    }

    public final void I(long j) {
        this.b.b(j, this);
    }

    public final boolean J() {
        if (ordinal() < DAY_OF_WEEK.ordinal()) {
            return true;
        }
        return false;
    }

    public final long g(m mVar) {
        return mVar.B(this);
    }

    public final t m() {
        return this.b;
    }

    public final boolean p(m mVar) {
        return mVar.e(this);
    }

    public final k s(k kVar, long j) {
        return kVar.d(j, this);
    }

    public final t t(m mVar) {
        return mVar.t(this);
    }

    public final String toString() {
        return this.a;
    }

    private a(String str, ChronoUnit chronoUnit, ChronoUnit chronoUnit2, t tVar, int i) {
        this.a = str;
        this.b = tVar;
    }
}
