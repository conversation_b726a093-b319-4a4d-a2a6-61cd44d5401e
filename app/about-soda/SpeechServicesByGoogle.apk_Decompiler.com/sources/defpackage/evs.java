package defpackage;

/* renamed from: evs  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class evs implements gsb {
    public final /* synthetic */ bmu a;
    private final /* synthetic */ int b;

    public /* synthetic */ evs(bmu bmu, int i) {
        this.b = i;
        this.a = bmu;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                fqv c = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/bluechip/update_card_count", new fqx[0]);
                c.c();
                return c;
            case 1:
                fqy g = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/assistant_settings_media_app_status", new fqx("app_version", String.class), new fqx("status", String.class));
                g.c();
                return g;
            case 2:
                fqy g2 = ((frc) this.a.a).g("/client_streamz/android_gsa/appactions/app_inventory_api/request_failed_count", new fqx("exception", String.class), new fqx("android_version", Integer.class), new fqx("method_name", String.class), new fqx("client_id", String.class));
                g2.c();
                return g2;
            case 3:
                fqy g3 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/calendar/account_type_count", new fqx("type", String.class));
                g3.c();
                return g3;
            case 4:
                fqy g4 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/calendar/consistency_at_aggregator", new fqx("is_consistent", Boolean.class));
                g4.c();
                return g4;
            case 5:
                fqv c2 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/calendar/display_latency", new fqx("is_blue_chip", Boolean.class), new fqx("aod_enabled", Integer.class), new fqx("is_from_pcp", Boolean.class));
                c2.c();
                return c2;
            case 6:
                fqy g5 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/surfaces/voice_search/request_count", new fqx("lang", String.class), new fqx("success", Boolean.class));
                g5.c();
                return g5;
            case 7:
                fqy g6 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/calendar/event_by_account_type_count", new fqx("type", String.class));
                g6.c();
                return g6;
            case 8:
                fqy g7 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/calendar_event_reservation_dedup", new fqx("app_version", String.class), new fqx("counter_name", String.class));
                g7.c();
                return g7;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fqy g8 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/calendar_flight_dedup", new fqx("app_version", String.class), new fqx("counter_name", String.class));
                g8.c();
                return g8;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fqy g9 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/calendar_train_dedup", new fqx("app_version", String.class), new fqx("counter_name", String.class));
                g9.c();
                return g9;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                fqy g10 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/calendar_ttl_merge", new fqx("app_version", String.class), new fqx("counter_name", String.class));
                g10.c();
                return g10;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                fqy g11 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/card_push", new fqx("app_version", String.class), new fqx("card_type", String.class), new fqx("dau", Boolean.class), new fqx("locus_id", String.class));
                g11.c();
                return g11;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                fqy g12 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/card_push_from_pcp", new fqx("client_type", String.class), new fqx("feature_type", String.class), new fqx("card_type", String.class));
                g12.c();
                return g12;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                fqv c3 = ((frc) this.a.a).c("/client_streamz/android_gsa/appactions/app_inventory_api/request_latency", new fqx("android_version", Integer.class), new fqx("method_name", String.class), new fqx("client_id", String.class));
                c3.c();
                return c3;
            case 15:
                fqy g13 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/card_push_status", new fqx("app_version", String.class), new fqx("aiai_app_version", String.class), new fqx("agsa_type", String.class), new fqx("cause", String.class), new fqx("dau", Boolean.class), new fqx("content_capture_enabled", Integer.class), new fqx("locus_id", String.class));
                g13.c();
                return g13;
            case 16:
                fqv c4 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/card_update_latency", new fqx("app_version", String.class), new fqx("surface", String.class), new fqx("card_type", String.class), new fqx("aod_enabled", Integer.class), new fqx("data_source", String.class));
                c4.c();
                return c4;
            case 17:
                fqy g14 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/tapas/monitoring/counters", new fqx("component", String.class), new fqx("label", String.class));
                g14.c();
                return g14;
            case 18:
                fqy g15 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/commute/accurate_triggering", new fqx("app_version", String.class), new fqx("client_type", String.class));
                g15.c();
                return g15;
            case 19:
                fqy g16 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/commute/unique_session", new fqx("app_version", String.class), new fqx("client_type", String.class));
                g16.c();
                return g16;
            default:
                fqy g17 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/cross_device/geller_fcm_ping_x_device_timer_counter", new fqx[0]);
                g17.c();
                return g17;
        }
    }
}
