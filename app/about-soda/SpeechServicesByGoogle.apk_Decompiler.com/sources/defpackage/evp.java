package defpackage;

/* renamed from: evp  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class evp implements gsb {
    public final /* synthetic */ bmu a;
    private final /* synthetic */ int b;

    public /* synthetic */ evp(bmu bmu, int i) {
        this.b = i;
        this.a = bmu;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                fqy g = ((frc) this.a.a).g("/client_streamz/android_gsa/proactive_api/content_is_active_before_removed", new fqx("journey_type", String.class), new fqx("content_type", String.class), new fqx("is_active", Boolean.class));
                g.c();
                return g;
            case 1:
                fqy g2 = ((frc) this.a.a).g("/client_streamz/android_gsa/podcasts/playback_realtime_ms", new fqx("app_version", String.class));
                g2.c();
                return g2;
            case 2:
                fqy g3 = ((frc) this.a.a).g("/client_streamz/android_gsa/proactive_api/received_travel_journey", new fqx[0]);
                g3.c();
                return g3;
            case 3:
                fqy g4 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/surfaces/dictation/instant_voice_replies/eligible_notification_count", new fqx[0]);
                g4.c();
                return g4;
            case 4:
                fqy g5 = ((frc) this.a.a).g("/client_streamz/android_gsa/amp/cannot_create_firebase_stamp_share_url", new fqx("cause", String.class));
                g5.c();
                return g5;
            case 5:
                fqy g6 = ((frc) this.a.a).g("/client_streamz/android_gsa/aap/assistant_service_failure", new fqx("description", String.class));
                g6.c();
                return g6;
            case 6:
                fqy g7 = ((frc) this.a.a).g("/client_streamz/android_gsa/proactive_api/registered_trip_weather_forecast", new fqx[0]);
                g7.c();
                return g7;
            case 7:
                fqv c = ((frc) this.a.a).c("/client_streamz/android_gsa/proactive_api/time_fence_absolute_time_interval_latency", new fqx("client_action_type", String.class), new fqx("journey_type", String.class));
                c.c();
                return c;
            case 8:
                fqy g8 = ((frc) this.a.a).g("/client_streamz/android_gsa/proactive_api/triggered_trip_weather_forecast", new fqx[0]);
                g8.c();
                return g8;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fqy g9 = ((frc) this.a.a).g("/client_streamz/android_gsa/quickphrases/connect_ui_status_count", new fqx("status", String.class));
                g9.c();
                return g9;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fqy g10 = ((frc) this.a.a).g("/client_streamz/android_gsa/quickphrases/dismiss_ui_status_count", new fqx("status", String.class));
                g10.c();
                return g10;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                fqy g11 = ((frc) this.a.a).g("/client_streamz/android_gsa/quickphrases/listening_started_status_count", new fqx("quick_phrase_type", String.class), new fqx("status", String.class));
                g11.c();
                return g11;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                fqy g12 = ((frc) this.a.a).g("/client_streamz/android_gsa/quickphrases/query_execution_status_count", new fqx("quick_phrase_type", String.class), new fqx("status", String.class));
                g12.c();
                return g12;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                fqy g13 = ((frc) this.a.a).g("/client_streamz/android_gsa/quickphrases/show_ui_status_count", new fqx("status", String.class));
                g13.c();
                return g13;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                fqy g14 = ((frc) this.a.a).g("/client_streamz/android_gsa/aap/start_session_error_counter", new fqx("counter_name", String.class));
                g14.c();
                return g14;
            case 15:
                fqy g15 = ((frc) this.a.a).g("/client_streamz/android_gsa/quickphrasesmanager/handle_soda_event_exception", new fqx("reason", String.class));
                g15.c();
                return g15;
            case 16:
                fqv c2 = ((frc) this.a.a).c("/client_streamz/android_gsa/quickphrasesmanager/no_account_shutdown_latency", new fqx[0]);
                c2.c();
                return c2;
            case 17:
                fqy g16 = ((frc) this.a.a).g("/client_streamz/android_gsa/amp/opening_stamp_without_caused_by_field", new fqx("source", String.class));
                g16.c();
                return g16;
            case 18:
                fqy g17 = ((frc) this.a.a).g("/client_streamz/android_gsa/quickphrasesmanager/soda_qp_frontend_shutdown_exception", new fqx("reason", String.class));
                g17.c();
                return g17;
            case 19:
                fqy g18 = ((frc) this.a.a).g("/client_streamz/android_gsa/quickphrasesmanager/start_event_has_no_qp_types", new fqx[0]);
                g18.c();
                return g18;
            default:
                fqy g19 = ((frc) this.a.a).g("/client_streamz/android_gsa/quickphrasesmanager/unknown_soda_event_received", new fqx[0]);
                g19.c();
                return g19;
        }
    }
}
