package defpackage;

import android.app.Activity;
import android.content.Context;
import android.content.ContextWrapper;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Bitmap;
import android.os.Build;
import android.os.Parcel;
import android.text.TextUtils;
import android.util.DisplayMetrics;
import android.util.Log;
import android.util.SparseArray;
import android.util.TypedValue;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import com.google.android.tts.R;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Function;

/* renamed from: bnv  reason: default package */
/* compiled from: PG */
public final class bnv {
    public static final SparseArray a = new SparseArray();
    private static int[] b;

    public static float a(Resources resources, int i) {
        return TypedValue.applyDimension(1, (float) i, resources.getDisplayMetrics());
    }

    public static float b(Resources resources, int i) {
        TypedValue typedValue = new TypedValue();
        resources.getValue(i, typedValue, true);
        return typedValue.getFloat();
    }

    public static int c(Context context, int i, int i2) {
        TypedValue typedValue = new TypedValue();
        context.getTheme().resolveAttribute(i, typedValue, true);
        if (typedValue.resourceId != 0) {
            return i;
        }
        return i2;
    }

    public static int d(Context context, int i) {
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(0, new int[]{i});
        int resourceId = obtainStyledAttributes.getResourceId(0, 0);
        obtainStyledAttributes.recycle();
        return resourceId;
    }

    public static Activity e(Context context) {
        while (context instanceof ContextWrapper) {
            if (context instanceof Activity) {
                return (Activity) context;
            }
            context = ((ContextWrapper) context).getBaseContext();
        }
        return null;
    }

    public static Context f(Context context) {
        while (!(context instanceof Activity) && (context instanceof ContextWrapper)) {
            context = ((ContextWrapper) context).getBaseContext();
        }
        return context;
    }

    public static DisplayMetrics g(Context context) {
        DisplayMetrics displayMetrics = new DisplayMetrics();
        try {
            if (Build.VERSION.SDK_INT >= 30 && sk$$ExternalSyntheticApiModelOutline1.m(context) != null) {
                sk$$ExternalSyntheticApiModelOutline1.m(context).getRealMetrics(displayMetrics);
                return displayMetrics;
            }
        } catch (UnsupportedOperationException unused) {
        }
        ((WindowManager) context.getSystemService(WindowManager.class)).getDefaultDisplay().getRealMetrics(displayMetrics);
        return displayMetrics;
    }

    public static View h(View view, int i) {
        if (Build.VERSION.SDK_INT >= 30) {
            return view.findViewById(i);
        }
        TypedValue typedValue = new TypedValue();
        view.getResources().getValue(i, typedValue, true);
        return view.findViewById(typedValue.resourceId);
    }

    public static View i(View view, int i) {
        View h = h(view, i);
        if (h != null) {
            return h;
        }
        String resourceName = view.getResources().getResourceName(i);
        throw new IllegalArgumentException("ID " + resourceName + " does not reference a View inside this View");
    }

    public static String j(CharSequence charSequence) {
        if (charSequence == null) {
            return null;
        }
        return charSequence.toString();
    }

    public static String k(String str) {
        try {
            Class<?> cls = Class.forName("android.os.SystemProperties");
            try {
                try {
                    String str2 = (String) cls.getMethod("get", new Class[]{String.class}).invoke(cls, new Object[]{str});
                    if (TextUtils.isEmpty(str2)) {
                        return null;
                    }
                    return str2;
                } catch (Exception e) {
                    Log.w("CarUiUtils", "Failed to invoke SystemProperties.get(): ", e);
                    return null;
                }
            } catch (NoSuchMethodException e2) {
                Log.w("CarUiUtils", "Cannot find SystemProperties.get(): ", e2);
                return null;
            }
        } catch (ClassNotFoundException e3) {
            Log.w("CarUiUtils", "Cannot find android.os.SystemProperties: ", e3);
            return null;
        }
    }

    public static List l(List list, Function function) {
        if (list == null) {
            return null;
        }
        ArrayList arrayList = new ArrayList();
        for (Object m : list) {
            arrayList.add(function.apply(m));
        }
        return arrayList;
    }

    public static void m(View view, int[] iArr, int[] iArr2) {
        if (view instanceof boj) {
            ((boj) view).a(iArr, iArr2);
        }
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                m(viewGroup.getChildAt(i), iArr, iArr2);
            }
        }
    }

    public static void n(View view, boolean z, boolean z2) {
        int[] iArr;
        if (view != null) {
            r(view);
            int[] iArr2 = null;
            if (z) {
                if (z2) {
                    int[] iArr3 = b;
                    int length = iArr3.length;
                    iArr = new int[(length + 1)];
                    iArr[0] = 16842910;
                    System.arraycopy(iArr3, 0, iArr, 1, length);
                } else {
                    iArr = new int[]{16842910};
                }
            } else if (z2) {
                iArr = b;
            } else {
                iArr = null;
            }
            if (!z) {
                iArr2 = new int[]{16842910};
            }
            m(view, iArr, iArr2);
        }
    }

    public static void o(View view, boolean z) {
        int[] iArr;
        if (view != null) {
            r(view);
            if (z) {
                iArr = b;
            } else {
                iArr = null;
            }
            m(view, iArr, (int[]) null);
        }
    }

    public static boolean p(Activity activity, int i) {
        TypedArray obtainStyledAttributes = activity.getTheme().obtainStyledAttributes(new int[]{i});
        try {
            return obtainStyledAttributes.getBoolean(0, false);
        } finally {
            obtainStyledAttributes.recycle();
        }
    }

    public static byte[] q(Bitmap bitmap) {
        if (bitmap == null) {
            return null;
        }
        try {
            Parcel obtain = Parcel.obtain();
            bitmap.writeToParcel(obtain, 0);
            byte[] marshall = obtain.marshall();
            obtain.recycle();
            return marshall;
        } catch (RuntimeException e) {
            Log.e("CarUiUtils", "failed to write bitmap", e);
            return null;
        }
    }

    private static void r(View view) {
        if (b == null) {
            int identifier = view.getResources().getIdentifier("state_ux_restricted", "attr", "android");
            if (identifier == 0) {
                b = new int[]{R.attr.state_ux_restricted};
            } else {
                b = new int[]{R.attr.state_ux_restricted, identifier};
            }
        }
    }
}
