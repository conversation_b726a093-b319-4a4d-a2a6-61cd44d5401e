package defpackage;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import android.support.v7.widget.RecyclerView;
import android.util.Log;
import android.util.Pair;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewParent;
import androidx.preference.DialogPreference;
import androidx.preference.DropDownPreference;
import androidx.preference.EditTextPreference;
import androidx.preference.ListPreference;
import androidx.preference.MultiSelectListPreference;
import androidx.preference.Preference;
import androidx.preference.PreferenceGroup;
import androidx.preference.PreferenceScreen;
import androidx.preference.SwitchPreference;
import androidx.preference.TwoStatePreference;
import com.android.car.ui.FocusArea;
import com.android.car.ui.baselayout.Insets;
import com.android.car.ui.core.BaseLayoutController;
import com.android.car.ui.preference.CarUiDropDownPreference;
import com.android.car.ui.preference.CarUiEditTextPreference;
import com.android.car.ui.preference.CarUiListPreference;
import com.android.car.ui.preference.CarUiMultiSelectListPreference;
import com.android.car.ui.preference.CarUiPreference;
import com.android.car.ui.preference.CarUiSeekBarDialogPreference;
import com.android.car.ui.preference.CarUiSwitchPreference;
import com.android.car.ui.recyclerview.CarUiRecyclerView;
import com.google.android.tts.R;
import java.lang.reflect.Field;
import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/* renamed from: bku  reason: default package */
/* compiled from: PG */
public abstract class bku extends atg implements bjw {
    private static final List ai = Arrays.asList(new Pair[]{new Pair(DropDownPreference.class, CarUiDropDownPreference.class), new Pair(ListPreference.class, CarUiListPreference.class), new Pair(MultiSelectListPreference.class, CarUiMultiSelectListPreference.class), new Pair(EditTextPreference.class, CarUiEditTextPreference.class), new Pair(SwitchPreference.class, CarUiSwitchPreference.class), new Pair(Preference.class, CarUiPreference.class)});
    public ViewParent ag;
    public int ah;
    private String aj;
    public CarUiRecyclerView d;

    private static Preference K(Preference preference) {
        Class<?> cls = preference.getClass();
        Iterator it = ai.iterator();
        while (true) {
            if (!it.hasNext()) {
                break;
            }
            Pair pair = (Pair) it.next();
            Class<Preference> cls2 = (Class) pair.first;
            Class<?> cls3 = (Class) pair.second;
            if (cls2.isAssignableFrom(cls)) {
                if (cls == cls2) {
                    try {
                        Preference preference2 = (Preference) cls3.getDeclaredConstructor(new Class[]{Context.class}).newInstance(new Object[]{preference.getContext()});
                        preference2.setTitle(preference.getTitle());
                        preference2.setOnPreferenceClickListener(preference.getOnPreferenceClickListener());
                        preference2.setOnPreferenceChangeListener(preference.getOnPreferenceChangeListener());
                        preference2.setIcon(preference.getIcon());
                        preference2.setFragment(preference.getFragment());
                        preference2.setIntent(preference.getIntent());
                        preference2.setKey(preference.getKey());
                        preference2.setOrder(preference.getOrder());
                        preference2.setSelectable(preference.isSelectable());
                        preference2.setPersistent(preference.isPersistent());
                        preference2.setIconSpaceReserved(preference.isIconSpaceReserved());
                        preference2.setWidgetLayoutResource(preference.getWidgetLayoutResource());
                        preference2.setPreferenceDataStore(preference.getPreferenceDataStore());
                        preference2.setVisible(preference.isVisible());
                        preference2.setLayoutResource(preference.getLayoutResource());
                        preference2.setCopyingEnabled(preference.isCopyingEnabled());
                        M(preference2, O(preference));
                        L(preference2, N(preference));
                        if (!(preference2 instanceof bkw)) {
                            preference2.setShouldDisableView(preference.getShouldDisableView());
                        }
                        if (preference.getSummaryProvider() != null) {
                            preference2.setSummaryProvider(preference.getSummaryProvider());
                        } else {
                            preference2.setSummary(preference.getSummary());
                        }
                        if (preference.peekExtras() != null) {
                            preference2.getExtras().putAll(preference.peekExtras());
                        }
                        if (preference instanceof DialogPreference) {
                            DialogPreference dialogPreference = (DialogPreference) preference;
                            DialogPreference dialogPreference2 = (DialogPreference) preference2;
                            dialogPreference2.setDialogTitle(dialogPreference.getDialogTitle());
                            dialogPreference2.setDialogIcon(dialogPreference.getDialogIcon());
                            dialogPreference2.setDialogMessage(dialogPreference.getDialogMessage());
                            dialogPreference2.setDialogLayoutResource(dialogPreference.getDialogLayoutResource());
                            dialogPreference2.setNegativeButtonText(dialogPreference.getNegativeButtonText());
                            dialogPreference2.setPositiveButtonText(dialogPreference.getPositiveButtonText());
                        }
                        if (preference instanceof ListPreference) {
                            ListPreference listPreference = (ListPreference) preference;
                            ListPreference listPreference2 = (ListPreference) preference2;
                            listPreference2.setEntries(listPreference.getEntries());
                            listPreference2.setEntryValues(listPreference.getEntryValues());
                            listPreference2.setValue(listPreference.getValue());
                        } else if (preference instanceof EditTextPreference) {
                            ((EditTextPreference) preference2).setText(((EditTextPreference) preference).getText());
                        } else if (preference instanceof MultiSelectListPreference) {
                            MultiSelectListPreference multiSelectListPreference = (MultiSelectListPreference) preference;
                            MultiSelectListPreference multiSelectListPreference2 = (MultiSelectListPreference) preference2;
                            multiSelectListPreference2.setEntries(multiSelectListPreference.getEntries());
                            multiSelectListPreference2.setEntryValues(multiSelectListPreference.getEntryValues());
                            multiSelectListPreference2.setValues(multiSelectListPreference.getValues());
                        } else if (preference instanceof TwoStatePreference) {
                            TwoStatePreference twoStatePreference = (TwoStatePreference) preference;
                            TwoStatePreference twoStatePreference2 = (TwoStatePreference) preference2;
                            twoStatePreference2.setSummaryOn(twoStatePreference.getSummaryOn());
                            twoStatePreference2.setSummaryOff(twoStatePreference.getSummaryOff());
                            if (preference instanceof SwitchPreference) {
                                SwitchPreference switchPreference = (SwitchPreference) preference;
                                SwitchPreference switchPreference2 = (SwitchPreference) preference2;
                                switchPreference2.setSwitchTextOn(switchPreference.getSwitchTextOn());
                                switchPreference2.setSwitchTextOff(switchPreference.getSwitchTextOff());
                            }
                        }
                        return preference2;
                    } catch (ReflectiveOperationException e) {
                        String valueOf = String.valueOf(String.valueOf(preference));
                        Throwable cause = e.getCause();
                        Throwable th = e;
                        if (cause != null) {
                            th = e.getCause();
                        }
                        throw new RuntimeException("Failed to copy Preference ".concat(valueOf), th);
                    }
                } else if (cls != cls3 && cls2 != Preference.class) {
                    String simpleName = cls2.getSimpleName();
                    String simpleName2 = cls3.getSimpleName();
                    Log.w("CarUiPreferenceFragment", "Subclass of " + simpleName + " was used, preventing us from substituting it with " + simpleName2);
                }
            }
        }
        return preference;
    }

    private static void L(Preference preference, boolean z) {
        try {
            Field declaredField = Preference.class.getDeclaredField("mAllowDividerAbove");
            declaredField.setAccessible(true);
            declaredField.set(preference, Boolean.valueOf(z));
        } catch (IllegalAccessException | NoSuchFieldException unused) {
        }
    }

    private static void M(Preference preference, boolean z) {
        try {
            Field declaredField = Preference.class.getDeclaredField("mAllowDividerBelow");
            declaredField.setAccessible(true);
            declaredField.set(preference, Boolean.valueOf(z));
        } catch (IllegalAccessException | NoSuchFieldException unused) {
        }
    }

    private static boolean N(Preference preference) {
        try {
            Field declaredField = Preference.class.getDeclaredField("mAllowDividerAbove");
            declaredField.setAccessible(true);
            return ((Boolean) declaredField.get(preference)).booleanValue();
        } catch (IllegalAccessException | NoSuchFieldException unused) {
            return false;
        }
    }

    private static boolean O(Preference preference) {
        try {
            Field declaredField = Preference.class.getDeclaredField("mAllowDividerBelow");
            declaredField.setAccessible(true);
            return ((Boolean) declaredField.get(preference)).booleanValue();
        } catch (IllegalAccessException | NoSuchFieldException unused) {
            return false;
        }
    }

    public final void B(Preference preference) {
        bc bcVar;
        if ((!(getActivity() instanceof atc) || !((atc) getActivity()).a()) && getParentFragmentManager().e("com.android.car.ui.PreferenceFragment.DIALOG") == null) {
            if (preference instanceof EditTextPreference) {
                String key = preference.getKey();
                bcVar = new bkm();
                Bundle bundle = new Bundle(1);
                bundle.putString("key", key);
                bcVar.setArguments(bundle);
            } else if (preference instanceof ListPreference) {
                String key2 = preference.getKey();
                bcVar = new bko();
                Bundle bundle2 = new Bundle(1);
                bundle2.putString("key", key2);
                bcVar.setArguments(bundle2);
            } else if (preference instanceof MultiSelectListPreference) {
                String key3 = preference.getKey();
                bcVar = new bkq();
                Bundle bundle3 = new Bundle(1);
                bundle3.putString("key", key3);
                bcVar.setArguments(bundle3);
            } else if (preference instanceof CarUiSeekBarDialogPreference) {
                String key4 = preference.getKey();
                bcVar = new bkv();
                Bundle bundle4 = new Bundle(1);
                bundle4.putString("key", key4);
                bcVar.setArguments(bundle4);
            } else {
                String simpleName = preference.getClass().getSimpleName();
                throw new IllegalArgumentException("Cannot display dialog for an unknown Preference type: " + simpleName + ". Make sure to implement onPreferenceDisplayDialog() to handle displaying a custom dialog for this Preference.");
            }
            bcVar.setTargetFragment(this, 0);
            if (bcVar instanceof as) {
                ((as) bcVar).e(this.A, "com.android.car.ui.PreferenceFragment.DIALOG");
            } else if (getActivity() == null) {
                throw new IllegalStateException("Preference fragment is not attached to an Activity.");
            } else if (this.P != null) {
                Context context = getContext();
                y yVar = new y(getParentFragmentManager());
                int d2 = bnv.d(context, 16843493);
                int d3 = bnv.d(context, 16843494);
                int d4 = bnv.d(context, 16843495);
                int d5 = bnv.d(context, 16843496);
                yVar.e = d2;
                yVar.f = d3;
                yVar.g = d4;
                yVar.h = d5;
                yVar.r(((ViewGroup) this.P.getParent()).getId(), bcVar);
                yVar.n();
                yVar.h();
                bnq J2 = J();
                if (J2 != null) {
                    J2.setTitle(preference.getTitle());
                    J2.setSubtitle("");
                    if (J2.isStateSet()) {
                        J2.setState(bnp.SUBPAGE);
                    } else {
                        J2.setNavButtonMode(bmp.BACK);
                    }
                    J2.setLogo((Drawable) null);
                    J2.setMenuItems((List) null);
                    J2.setTabs(Collections.emptyList());
                }
            } else {
                throw new IllegalStateException("Preference fragment must have a layout.");
            }
        }
    }

    public final void D(PreferenceScreen preferenceScreen) {
        HashMap hashMap = new HashMap();
        ArrayList arrayList = new ArrayList();
        ArrayDeque arrayDeque = new ArrayDeque();
        arrayDeque.addFirst(preferenceScreen);
        while (!arrayDeque.isEmpty()) {
            Preference preference = (Preference) arrayDeque.removeFirst();
            if (preference instanceof PreferenceGroup) {
                PreferenceGroup preferenceGroup = (PreferenceGroup) preference;
                arrayList.clear();
                for (int i = 0; i < preferenceGroup.a(); i++) {
                    arrayList.add(preferenceGroup.c(i));
                }
                preferenceGroup.d();
                int size = arrayList.size();
                for (int i2 = 0; i2 < size; i2++) {
                    Preference preference2 = (Preference) arrayList.get(i2);
                    Preference K = K(preference2);
                    hashMap.put(K, preference2.getDependency());
                    preferenceGroup.g(K);
                    arrayDeque.addFirst(K);
                }
            }
        }
        super.D(preferenceScreen);
        for (Map.Entry entry : hashMap.entrySet()) {
            ((Preference) entry.getKey()).setDependency((String) entry.getValue());
        }
    }

    public final boolean F(Preference preference) {
        this.aj = preference.getKey();
        if (this.ag != null) {
            View findFocus = this.P.findFocus();
            while (true) {
                if (findFocus != null) {
                    ViewParent parent = findFocus.getParent();
                    if (parent != this.ag) {
                        if (parent == null || !(parent instanceof View)) {
                            break;
                        }
                        findFocus = (View) parent;
                    } else {
                        this.ah = this.d.getChildLayoutPosition(findFocus);
                        break;
                    }
                } else {
                    break;
                }
            }
        }
        return super.F(preference);
    }

    /* access modifiers changed from: protected */
    public final Insets I() {
        BaseLayoutController baseLayoutController = BaseLayoutController.getBaseLayoutController(getActivity());
        if (baseLayoutController != null) {
            return baseLayoutController.getInsets();
        }
        return null;
    }

    /* access modifiers changed from: protected */
    public final bnq J() {
        return yh.k(getActivity());
    }

    public final void a(Insets insets) {
        View requireView = requireView();
        FocusArea focusArea = (FocusArea) bnv.i(requireView, R.id.car_ui_focus_area);
        focusArea.c(0, insets.getTop(), 0, insets.getBottom());
        focusArea.b(0, insets.getTop(), 0, insets.getBottom());
        this.d.setPadding(0, insets.getTop(), 0, insets.getBottom());
        requireView.setPadding(insets.getLeft(), 0, insets.getRight(), 0);
    }

    public final RecyclerView c(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        RecyclerView recyclerView;
        CarUiRecyclerView carUiRecyclerView = (CarUiRecyclerView) bnv.i(viewGroup, R.id.recycler_view);
        this.d = carUiRecyclerView;
        if (carUiRecyclerView instanceof bkt) {
            recyclerView = ((bkt) carUiRecyclerView).getRecyclerView();
        } else {
            recyclerView = null;
        }
        if (recyclerView == null) {
            recyclerView = super.c(layoutInflater, viewGroup, bundle);
        }
        CarUiRecyclerView carUiRecyclerView2 = this.d;
        if (carUiRecyclerView2 != null) {
            carUiRecyclerView2.addOnChildAttachStateChangeListener(new bks(this));
        }
        return recyclerView;
    }

    public void onResume() {
        super.onResume();
        String str = this.aj;
        if (str != null) {
            aku aku = new aku((Object) this, (Object) str, 3, (byte[]) null);
            if (this.b == null) {
                this.c = aku;
            } else {
                aku.run();
            }
        }
    }

    public final void onStart() {
        super.onStart();
        Insets I = I();
        if (I != null) {
            a(I);
        }
    }

    public final void onViewCreated(View view, Bundle bundle) {
        super.onViewCreated(view, bundle);
        bnq J2 = J();
        if (J2 != null) {
            J2.setNavButtonMode(bnk.BACK);
            PreferenceScreen e = e();
            if (e != null) {
                J2.setTitle(e.getTitle());
            } else {
                J2.setTitle((CharSequence) "");
            }
        }
    }
}
