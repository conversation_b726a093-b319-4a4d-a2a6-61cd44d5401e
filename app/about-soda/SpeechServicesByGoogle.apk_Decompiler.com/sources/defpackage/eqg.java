package defpackage;

/* renamed from: eqg  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class eqg implements hcb {
    public final /* synthetic */ eqb a;

    public /* synthetic */ eqg(eqb eqb) {
        this.a = eqb;
    }

    public final Object a() {
        String str;
        eah eah;
        eah eah2;
        eag eag;
        eak eak;
        eak eak2;
        eaj eaj;
        eqb eqb = this.a;
        jnu.e(eqb, "$delta");
        StringBuilder sb = new StringBuilder("[");
        int i = 0;
        for (eps eps : eqb.a) {
            int i2 = i + 1;
            if (i > 0) {
                str = ",{";
            } else {
                str = "{";
            }
            sb.append(str);
            ept b = ept.b(eps.d);
            if (b == null) {
                b = ept.CLIENT_TYPE_UNKNOWN;
            }
            sb.append("type=".concat(String.valueOf(b.name())));
            if ((eps.a & 2) != 0) {
                int i3 = eps.e;
                sb.append(",recordId=" + i3);
            }
            if (eps.b == 2) {
                eak eak3 = (eak) eps.c;
                if (eak3.a == 1) {
                    eal b2 = eal.b(((Integer) eak3.b).intValue());
                    if (b2 == null) {
                        b2 = eal.UNKNOWN_OPENING_SUCCESS;
                    }
                    sb.append(",open=".concat(String.valueOf(b2.name())));
                }
                int i4 = eps.b;
                if (i4 == 2) {
                    eak = (eak) eps.c;
                } else {
                    eak = eak.c;
                }
                if (eak.a == 2) {
                    if (i4 == 2) {
                        eak2 = (eak) eps.c;
                    } else {
                        eak2 = eak.c;
                    }
                    if (eak2.a == 2) {
                        eaj = eaj.b(((Integer) eak2.b).intValue());
                        if (eaj == null) {
                            eaj = eaj.UNKNOWN_OPENING_FAILURE;
                        }
                    } else {
                        eaj = eaj.UNKNOWN_OPENING_FAILURE;
                    }
                    sb.append(",open=".concat(String.valueOf(eaj.name())));
                }
            }
            if (eps.b == 3) {
                eah eah3 = (eah) eps.c;
                if (eah3.a == 1) {
                    eai b3 = eai.b(((Integer) eah3.b).intValue());
                    if (b3 == null) {
                        b3 = eai.UNKNOWN_CLOSING_SUCCESS;
                    }
                    sb.append(",close=".concat(String.valueOf(b3.name())));
                }
                int i5 = eps.b;
                if (i5 == 3) {
                    eah = (eah) eps.c;
                } else {
                    eah = eah.c;
                }
                if (eah.a == 2) {
                    if (i5 == 3) {
                        eah2 = (eah) eps.c;
                    } else {
                        eah2 = eah.c;
                    }
                    if (eah2.a == 2) {
                        eag = eag.b(((Integer) eah2.b).intValue());
                        if (eag == null) {
                            eag = eag.UNKNOWN_CLOSING_FAILURE;
                        }
                    } else {
                        eag = eag.UNKNOWN_CLOSING_FAILURE;
                    }
                    sb.append(",close=".concat(String.valueOf(eag.name())));
                }
            }
            sb.append("}");
            i = i2;
        }
        sb.append("]");
        return sb.toString();
    }
}
