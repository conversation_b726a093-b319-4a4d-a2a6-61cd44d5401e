package defpackage;

/* renamed from: csw  reason: default package */
/* compiled from: PG */
public final class csw extends htq implements hvb {
    public static final csw i;
    private static volatile hvh j;
    public int a;
    public long b;
    public long c;
    public long d;
    public long e;
    public int f;
    public boolean g;
    public boolean h;

    static {
        csw csw = new csw();
        i = csw;
        htq.z(csw.class, csw);
    }

    private csw() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i2, Object obj) {
        int i3 = i2 - 1;
        if (i3 == 0) {
            return (byte) 1;
        }
        if (i3 == 2) {
            return new hvl(i, "\u0001\u0007\u0000\u0001\u0001\u0007\u0007\u0000\u0000\u0000\u0001ဂ\u0000\u0002ဂ\u0001\u0003ဂ\u0002\u0004ဂ\u0003\u0005င\u0004\u0006ဇ\u0005\u0007ဇ\u0006", new Object[]{"a", "b", "c", "d", "e", "f", "g", "h"});
        } else if (i3 == 3) {
            return new csw();
        } else {
            if (i3 == 4) {
                return new htk((htq) i);
            }
            if (i3 == 5) {
                return i;
            }
            if (i3 != 6) {
                return null;
            }
            hvh hvh = j;
            if (hvh == null) {
                synchronized (csw.class) {
                    hvh = j;
                    if (hvh == null) {
                        hvh = new htl(i);
                        j = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
