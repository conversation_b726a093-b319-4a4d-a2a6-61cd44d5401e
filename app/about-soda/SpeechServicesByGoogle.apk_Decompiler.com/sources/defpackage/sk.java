package defpackage;

import android.app.Notification;
import android.app.PendingIntent;
import android.content.Context;
import android.os.Bundle;
import java.util.ArrayList;

/* renamed from: sk  reason: default package */
/* compiled from: PG */
public final class sk {
    public final Context a;
    public final ArrayList b = new ArrayList();
    public final ArrayList c = new ArrayList();
    final ArrayList d = new ArrayList();
    CharSequence e;
    CharSequence f;
    public PendingIntent g;
    public int h;
    sl i;
    int j;
    int k;
    boolean l;
    public boolean m = false;
    public String n;
    Bundle o;
    public String p;
    public final Notification q;
    @Deprecated
    public final ArrayList r;

    public sk(Context context, String str) {
        Notification notification = new Notification();
        this.q = notification;
        this.a = context;
        this.p = str;
        notification.when = System.currentTimeMillis();
        notification.audioStreamType = -1;
        this.h = 0;
        this.r = new ArrayList();
    }

    public static CharSequence c(CharSequence charSequence) {
        if (charSequence == null) {
            return null;
        }
        if (charSequence.length() > 5120) {
            return charSequence.subSequence(0, 5120);
        }
        return charSequence;
    }

    /* JADX WARNING: type inference failed for: r7v26, types: [java.lang.Throwable, java.lang.CharSequence[], java.lang.CharSequence, android.app.Notification$BubbleMetadata, long[], android.net.Uri, java.lang.String] */
    /* JADX WARNING: type inference failed for: r7v27 */
    /* JADX WARNING: type inference failed for: r7v30 */
    /* JADX WARNING: Removed duplicated region for block: B:154:0x020a A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:71:0x01bf  */
    /* JADX WARNING: Removed duplicated region for block: B:80:0x01f5  */
    /* JADX WARNING: Removed duplicated region for block: B:83:0x01fe  */
    /* JADX WARNING: Removed duplicated region for block: B:86:0x0207  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final android.app.Notification a() {
        /*
            r16 = this;
            r1 = r16
            java.lang.String r2 = "Unable to get icon package"
            java.lang.String r3 = "IconCompat"
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            android.os.Bundle r4 = new android.os.Bundle
            r4.<init>()
            java.lang.String r0 = r1.p
            android.app.Notification$Builder r5 = new android.app.Notification$Builder
            android.content.Context r6 = r1.a
            r5.<init>(r6, r0)
            android.app.Notification r6 = r1.q
            long r7 = r6.when
            android.app.Notification$Builder r0 = r5.setWhen(r7)
            int r7 = r6.icon
            int r8 = r6.iconLevel
            android.app.Notification$Builder r0 = r0.setSmallIcon(r7, r8)
            android.widget.RemoteViews r7 = r6.contentView
            android.app.Notification$Builder r0 = r0.setContent(r7)
            java.lang.CharSequence r7 = r6.tickerText
            r8 = 0
            android.app.Notification$Builder r0 = r0.setTicker(r7, r8)
            long[] r7 = r6.vibrate
            android.app.Notification$Builder r0 = r0.setVibrate(r7)
            int r7 = r6.ledARGB
            int r9 = r6.ledOnMS
            int r10 = r6.ledOffMS
            android.app.Notification$Builder r0 = r0.setLights(r7, r9, r10)
            int r7 = r6.flags
            r9 = 2
            r7 = r7 & r9
            r11 = 0
            if (r7 == 0) goto L_0x004f
            r7 = 1
            goto L_0x0050
        L_0x004f:
            r7 = r11
        L_0x0050:
            android.app.Notification$Builder r0 = r0.setOngoing(r7)
            int r7 = r6.flags
            r7 = r7 & 8
            if (r7 == 0) goto L_0x005c
            r7 = 1
            goto L_0x005d
        L_0x005c:
            r7 = r11
        L_0x005d:
            android.app.Notification$Builder r0 = r0.setOnlyAlertOnce(r7)
            int r7 = r6.flags
            r7 = r7 & 16
            if (r7 == 0) goto L_0x0069
            r7 = 1
            goto L_0x006a
        L_0x0069:
            r7 = r11
        L_0x006a:
            android.app.Notification$Builder r0 = r0.setAutoCancel(r7)
            int r7 = r6.defaults
            android.app.Notification$Builder r0 = r0.setDefaults(r7)
            java.lang.CharSequence r7 = r1.e
            android.app.Notification$Builder r0 = r0.setContentTitle(r7)
            java.lang.CharSequence r7 = r1.f
            android.app.Notification$Builder r0 = r0.setContentText(r7)
            android.app.Notification$Builder r0 = r0.setContentInfo(r8)
            android.app.PendingIntent r7 = r1.g
            android.app.Notification$Builder r0 = r0.setContentIntent(r7)
            android.app.PendingIntent r7 = r6.deleteIntent
            android.app.Notification$Builder r0 = r0.setDeleteIntent(r7)
            int r7 = r6.flags
            r7 = r7 & 128(0x80, float:1.794E-43)
            if (r7 == 0) goto L_0x0098
            r7 = 1
            goto L_0x0099
        L_0x0098:
            r7 = r11
        L_0x0099:
            android.app.Notification$Builder r0 = r0.setFullScreenIntent(r8, r7)
            android.app.Notification$Builder r0 = r0.setNumber(r11)
            int r7 = r1.j
            int r12 = r1.k
            boolean r13 = r1.l
            r0.setProgress(r7, r12, r13)
            r5.setLargeIcon(r8)
            android.app.Notification$Builder r0 = r5.setSubText(r8)
            android.app.Notification$Builder r0 = r0.setUsesChronometer(r11)
            int r7 = r1.h
            r0.setPriority(r7)
            java.util.ArrayList r7 = r1.b
            int r12 = r7.size()
            r13 = r11
        L_0x00c1:
            java.lang.String r15 = "android.support.allowGeneratedReplies"
            r14 = 28
            if (r13 >= r12) goto L_0x0222
            java.lang.Object r0 = r7.get(r13)
            r10 = r0
            si r10 = (defpackage.si) r10
            androidx.core.graphics.drawable.IconCompat r11 = r10.a()
            if (r11 == 0) goto L_0x01b1
            int r0 = r11.b
            switch(r0) {
                case -1: goto L_0x01ac;
                case 0: goto L_0x00d9;
                case 1: goto L_0x0193;
                case 2: goto L_0x0128;
                case 3: goto L_0x0119;
                case 4: goto L_0x0110;
                case 5: goto L_0x0107;
                case 6: goto L_0x00e1;
                default: goto L_0x00d9;
            }
        L_0x00d9:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.String r2 = "Unknown type"
            r0.<init>(r2)
            throw r0
        L_0x00e1:
            int r0 = android.os.Build.VERSION.SDK_INT
            r9 = 30
            if (r0 < r9) goto L_0x00f0
            android.net.Uri r0 = r11.b()
            android.graphics.drawable.Icon r0 = android.graphics.drawable.Icon.createWithAdaptiveBitmapContentUri(r0)
            goto L_0x0125
        L_0x00f0:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            android.net.Uri r2 = r11.b()
            java.util.Objects.toString(r2)
            java.lang.String r2 = java.lang.String.valueOf(r2)
            java.lang.String r3 = "Context is required to resolve the file uri of the icon: "
            java.lang.String r2 = r3.concat(r2)
            r0.<init>(r2)
            throw r0
        L_0x0107:
            java.lang.Object r0 = r11.c
            android.graphics.Bitmap r0 = (android.graphics.Bitmap) r0
            android.graphics.drawable.Icon r0 = android.graphics.drawable.Icon.createWithAdaptiveBitmap(r0)
            goto L_0x0125
        L_0x0110:
            java.lang.Object r0 = r11.c
            java.lang.String r0 = (java.lang.String) r0
            android.graphics.drawable.Icon r0 = android.graphics.drawable.Icon.createWithContentUri(r0)
            goto L_0x0125
        L_0x0119:
            java.lang.Object r0 = r11.c
            byte[] r0 = (byte[]) r0
            int r9 = r11.f
            int r8 = r11.g
            android.graphics.drawable.Icon r0 = android.graphics.drawable.Icon.createWithData(r0, r9, r8)
        L_0x0125:
            r9 = 2
            goto L_0x019b
        L_0x0128:
            r8 = -1
            if (r0 != r8) goto L_0x015b
            java.lang.Object r0 = r11.c
            int r8 = android.os.Build.VERSION.SDK_INT
            if (r8 < r14) goto L_0x0139
            android.graphics.drawable.Icon r0 = (android.graphics.drawable.Icon) r0
            java.lang.String r0 = defpackage.a$$ExternalSyntheticApiModelOutline0.m((android.graphics.drawable.Icon) r0)
        L_0x0137:
            r9 = 2
            goto L_0x0179
        L_0x0139:
            java.lang.Class r8 = r0.getClass()     // Catch:{ IllegalAccessException -> 0x0155, InvocationTargetException -> 0x0150, NoSuchMethodException -> 0x014b }
            java.lang.String r9 = "getResPackage"
            r14 = 0
            java.lang.reflect.Method r8 = r8.getMethod(r9, r14)     // Catch:{ IllegalAccessException -> 0x0155, InvocationTargetException -> 0x0150, NoSuchMethodException -> 0x014b }
            java.lang.Object r0 = r8.invoke(r0, r14)     // Catch:{ IllegalAccessException -> 0x0155, InvocationTargetException -> 0x0150, NoSuchMethodException -> 0x014b }
            java.lang.String r0 = (java.lang.String) r0     // Catch:{ IllegalAccessException -> 0x0155, InvocationTargetException -> 0x0150, NoSuchMethodException -> 0x014b }
            goto L_0x0137
        L_0x014b:
            r0 = move-exception
            android.util.Log.e(r3, r2, r0)
            goto L_0x0159
        L_0x0150:
            r0 = move-exception
            android.util.Log.e(r3, r2, r0)
            goto L_0x0159
        L_0x0155:
            r0 = move-exception
            android.util.Log.e(r3, r2, r0)
        L_0x0159:
            r0 = 0
            goto L_0x0137
        L_0x015b:
            r9 = 2
            if (r0 != r9) goto L_0x0180
            java.lang.String r0 = r11.k
            if (r0 == 0) goto L_0x016c
            boolean r0 = android.text.TextUtils.isEmpty(r0)
            if (r0 == 0) goto L_0x0169
            goto L_0x016c
        L_0x0169:
            java.lang.String r0 = r11.k
            goto L_0x0179
        L_0x016c:
            java.lang.Object r0 = r11.c
            java.lang.String r0 = (java.lang.String) r0
            java.lang.String r14 = ":"
            java.lang.String[] r0 = r0.split(r14, r8)
            r8 = 0
            r0 = r0[r8]
        L_0x0179:
            int r8 = r11.f
            android.graphics.drawable.Icon r0 = android.graphics.drawable.Icon.createWithResource(r0, r8)
            goto L_0x019b
        L_0x0180:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            java.util.Objects.toString(r11)
            java.lang.String r2 = r11.toString()
            java.lang.String r3 = "called getResPackage() on "
            java.lang.String r2 = r3.concat(r2)
            r0.<init>(r2)
            throw r0
        L_0x0193:
            java.lang.Object r0 = r11.c
            android.graphics.Bitmap r0 = (android.graphics.Bitmap) r0
            android.graphics.drawable.Icon r0 = android.graphics.drawable.Icon.createWithBitmap(r0)
        L_0x019b:
            android.content.res.ColorStateList r8 = r11.h
            if (r8 == 0) goto L_0x01a2
            r0.setTintList(r8)
        L_0x01a2:
            android.graphics.PorterDuff$Mode r8 = r11.i
            android.graphics.PorterDuff$Mode r11 = androidx.core.graphics.drawable.IconCompat.a
            if (r8 == r11) goto L_0x01b2
            r0.setTintMode(r8)
            goto L_0x01b2
        L_0x01ac:
            java.lang.Object r0 = r11.c
            android.graphics.drawable.Icon r0 = (android.graphics.drawable.Icon) r0
            goto L_0x01b2
        L_0x01b1:
            r0 = 0
        L_0x01b2:
            java.lang.CharSequence r8 = r10.e
            android.app.PendingIntent r11 = r10.f
            android.app.Notification$Action$Builder r14 = new android.app.Notification$Action$Builder
            r14.<init>(r0, r8, r11)
            ki[] r0 = r10.g
            if (r0 == 0) goto L_0x01d7
            int r8 = r0.length
            android.app.RemoteInput[] r11 = new android.app.RemoteInput[r8]
            if (r8 > 0) goto L_0x01d0
            r0 = 0
        L_0x01c5:
            if (r0 >= r8) goto L_0x01d7
            r9 = r11[r0]
            r14.addRemoteInput(r9)
            int r0 = r0 + 1
            r9 = 2
            goto L_0x01c5
        L_0x01d0:
            r8 = 0
            r0 = r0[r8]
            android.app.RemoteInput$Builder r0 = new android.app.RemoteInput$Builder
            r2 = 0
            throw r2
        L_0x01d7:
            android.os.Bundle r0 = r10.a
            android.os.Bundle r8 = new android.os.Bundle
            r8.<init>(r0)
            boolean r0 = r10.b
            r9 = 1
            r8.putBoolean(r15, r9)
            boolean r0 = r10.b
            android.app.Notification.Action.Builder unused = r14.setAllowGeneratedReplies(r9)
            java.lang.String r0 = "android.support.action.semanticAction"
            r9 = 0
            r8.putInt(r0, r9)
            int r0 = android.os.Build.VERSION.SDK_INT
            r11 = 28
            if (r0 < r11) goto L_0x01f8
            android.app.Notification.Action.Builder unused = r14.setSemanticAction(r9)
        L_0x01f8:
            int r0 = android.os.Build.VERSION.SDK_INT
            r11 = 29
            if (r0 < r11) goto L_0x0201
            android.app.Notification.Action.Builder unused = r14.setContextual(r9)
        L_0x0201:
            int r0 = android.os.Build.VERSION.SDK_INT
            r11 = 31
            if (r0 < r11) goto L_0x020a
            android.app.Notification.Action.Builder unused = r14.setAuthenticationRequired(r9)
        L_0x020a:
            boolean r0 = r10.c
            java.lang.String r9 = "android.support.action.showsUserInterface"
            r8.putBoolean(r9, r0)
            r14.addExtras(r8)
            android.app.Notification$Action r0 = r14.build()
            r5.addAction(r0)
            int r13 = r13 + 1
            r8 = 0
            r9 = 2
            r11 = 0
            goto L_0x00c1
        L_0x0222:
            android.os.Bundle r0 = r1.o
            if (r0 == 0) goto L_0x0229
            r4.putAll(r0)
        L_0x0229:
            r2 = 1
            r5.setShowWhen(r2)
            boolean r0 = r1.m
            r5.setLocalOnly(r0)
            r2 = 0
            r5.setGroup(r2)
            r5.setSortKey(r2)
            r3 = 0
            r5.setGroupSummary(r3)
            java.lang.String r0 = r1.n
            r5.setCategory(r0)
            r5.setColor(r3)
            r5.setVisibility(r3)
            r5.setPublicVersion(r2)
            android.net.Uri r0 = r6.sound
            android.media.AudioAttributes r2 = r6.audioAttributes
            r5.setSound(r0, r2)
            int r0 = android.os.Build.VERSION.SDK_INT
            r2 = 28
            if (r0 >= r2) goto L_0x0291
            java.util.ArrayList r0 = r1.c
            java.util.ArrayList r2 = new java.util.ArrayList
            int r3 = r0.size()
            r2.<init>(r3)
            java.util.Iterator r0 = r0.iterator()
            boolean r3 = r0.hasNext()
            if (r3 != 0) goto L_0x0289
            java.util.ArrayList r0 = r1.r
            ov r3 = new ov
            int r6 = r2.size()
            int r7 = r0.size()
            int r6 = r6 + r7
            r3.<init>(r6)
            r3.addAll(r2)
            r3.addAll(r0)
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>(r3)
            goto L_0x0293
        L_0x0289:
            java.lang.Object r0 = r0.next()
            sq r0 = (defpackage.sq) r0
            r2 = 0
            throw r2
        L_0x0291:
            java.util.ArrayList r0 = r1.r
        L_0x0293:
            boolean r2 = r0.isEmpty()
            if (r2 != 0) goto L_0x02ad
            java.util.Iterator r0 = r0.iterator()
        L_0x029d:
            boolean r2 = r0.hasNext()
            if (r2 == 0) goto L_0x02ad
            java.lang.Object r2 = r0.next()
            java.lang.String r2 = (java.lang.String) r2
            r5.addPerson(r2)
            goto L_0x029d
        L_0x02ad:
            java.util.ArrayList r0 = r1.d
            int r0 = r0.size()
            if (r0 <= 0) goto L_0x035c
            android.os.Bundle r0 = r16.b()
            java.lang.String r2 = "android.car.EXTENSIONS"
            android.os.Bundle r0 = r0.getBundle(r2)
            if (r0 != 0) goto L_0x02c6
            android.os.Bundle r0 = new android.os.Bundle
            r0.<init>()
        L_0x02c6:
            android.os.Bundle r3 = new android.os.Bundle
            r3.<init>(r0)
            android.os.Bundle r6 = new android.os.Bundle
            r6.<init>()
            r7 = 0
        L_0x02d1:
            java.util.ArrayList r8 = r1.d
            int r8 = r8.size()
            if (r7 >= r8) goto L_0x0348
            java.util.ArrayList r8 = r1.d
            java.lang.String r9 = java.lang.Integer.toString(r7)
            java.lang.Object r8 = r8.get(r7)
            si r8 = (defpackage.si) r8
            android.os.Bundle r10 = new android.os.Bundle
            r10.<init>()
            androidx.core.graphics.drawable.IconCompat r11 = r8.a()
            if (r11 == 0) goto L_0x02f5
            int r11 = r11.a()
            goto L_0x02f6
        L_0x02f5:
            r11 = 0
        L_0x02f6:
            java.lang.String r12 = "icon"
            r10.putInt(r12, r11)
            java.lang.CharSequence r11 = r8.e
            java.lang.String r12 = "title"
            r10.putCharSequence(r12, r11)
            android.app.PendingIntent r11 = r8.f
            java.lang.String r12 = "actionIntent"
            r10.putParcelable(r12, r11)
            android.os.Bundle r11 = r8.a
            android.os.Bundle r12 = new android.os.Bundle
            r12.<init>(r11)
            boolean r11 = r8.b
            r11 = 1
            r12.putBoolean(r15, r11)
            java.lang.String r11 = "extras"
            r10.putBundle(r11, r12)
            ki[] r11 = r8.g
            if (r11 != 0) goto L_0x0321
            r14 = 0
            goto L_0x0326
        L_0x0321:
            int r12 = r11.length
            android.os.Bundle[] r14 = new android.os.Bundle[r12]
            if (r12 > 0) goto L_0x033e
        L_0x0326:
            java.lang.String r11 = "remoteInputs"
            r10.putParcelableArray(r11, r14)
            boolean r8 = r8.c
            java.lang.String r11 = "showsUserInterface"
            r10.putBoolean(r11, r8)
            java.lang.String r8 = "semanticAction"
            r12 = 0
            r10.putInt(r8, r12)
            r6.putBundle(r9, r10)
            int r7 = r7 + 1
            goto L_0x02d1
        L_0x033e:
            r12 = 0
            r0 = r11[r12]
            android.os.Bundle r0 = new android.os.Bundle
            r0.<init>()
            r7 = 0
            throw r7
        L_0x0348:
            r7 = 0
            java.lang.String r8 = "invisible_actions"
            r0.putBundle(r8, r6)
            r3.putBundle(r8, r6)
            android.os.Bundle r6 = r16.b()
            r6.putBundle(r2, r0)
            r4.putBundle(r2, r3)
            goto L_0x035d
        L_0x035c:
            r7 = 0
        L_0x035d:
            android.os.Bundle r0 = r1.o
            r5.setExtras(r0)
            android.app.Notification.Builder unused = r5.setRemoteInputHistory(r7)
            r2 = 0
            android.app.Notification.Builder unused = r5.setBadgeIconType(r2)
            android.app.Notification.Builder unused = r5.setSettingsText(r7)
            android.app.Notification.Builder unused = r5.setShortcutId(r7)
            r3 = 0
            android.app.Notification.Builder unused = r5.setTimeoutAfter(r3)
            android.app.Notification.Builder unused = r5.setGroupAlertBehavior(r2)
            java.lang.String r0 = r1.p
            boolean r0 = android.text.TextUtils.isEmpty(r0)
            if (r0 != 0) goto L_0x038e
            android.app.Notification$Builder r0 = r5.setSound(r7)
            android.app.Notification$Builder r0 = r0.setDefaults(r2)
            android.app.Notification$Builder r0 = r0.setLights(r2, r2, r2)
            r0.setVibrate(r7)
        L_0x038e:
            int r0 = android.os.Build.VERSION.SDK_INT
            r3 = 28
            if (r0 < r3) goto L_0x03a4
            java.util.ArrayList r0 = r1.c
            int r3 = r0.size()
            if (r3 > 0) goto L_0x039d
            goto L_0x03a4
        L_0x039d:
            java.lang.Object r0 = r0.get(r2)
            sq r0 = (defpackage.sq) r0
            throw r7
        L_0x03a4:
            int r0 = android.os.Build.VERSION.SDK_INT
            r2 = 29
            if (r0 < r2) goto L_0x03b1
            r2 = 1
            android.app.Notification.Builder unused = r5.setAllowSystemGeneratedContextualActions(r2)
            android.app.Notification.Builder unused = r5.setBubbleMetadata(r7)
        L_0x03b1:
            sl r0 = r1.i
            if (r0 == 0) goto L_0x03c6
            android.app.Notification$BigTextStyle r2 = new android.app.Notification$BigTextStyle
            r2.<init>(r5)
            android.app.Notification$BigTextStyle r2 = r2.setBigContentTitle(r7)
            r3 = r0
            sj r3 = (defpackage.sj) r3
            java.lang.CharSequence r3 = r3.a
            r2.bigText(r3)
        L_0x03c6:
            android.app.Notification r2 = r5.build()
            if (r0 == 0) goto L_0x03d7
            android.os.Bundle r0 = r2.extras
            if (r0 == 0) goto L_0x03d7
            java.lang.String r3 = "android.support.v4.app.extra.COMPAT_TEMPLATE"
            java.lang.String r4 = "androidx.core.app.NotificationCompat$BigTextStyle"
            r0.putString(r3, r4)
        L_0x03d7:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.sk.a():android.app.Notification");
    }

    public final Bundle b() {
        if (this.o == null) {
            this.o = new Bundle();
        }
        return this.o;
    }

    public final void d(int i2, boolean z) {
        if (z) {
            Notification notification = this.q;
            notification.flags = i2 | notification.flags;
            return;
        }
        Notification notification2 = this.q;
        notification2.flags = (~i2) & notification2.flags;
    }

    public final void e(CharSequence charSequence) {
        this.f = c(charSequence);
    }

    public final void f(CharSequence charSequence) {
        this.e = c(charSequence);
    }

    public final void g(boolean z) {
        d(2, z);
    }

    public final void h(int i2, int i3, boolean z) {
        this.j = i2;
        this.k = i3;
        this.l = z;
    }

    public final void i(int i2) {
        this.q.icon = i2;
    }

    public final void j(sl slVar) {
        if (this.i != slVar) {
            this.i = slVar;
            if (slVar != null && slVar.b != this) {
                slVar.b = this;
                sk skVar = slVar.b;
                if (skVar != null) {
                    skVar.j(slVar);
                }
            }
        }
    }
}
