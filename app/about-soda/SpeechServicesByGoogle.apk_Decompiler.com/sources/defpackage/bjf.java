package defpackage;

import android.database.Cursor;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/* renamed from: bjf  reason: default package */
/* compiled from: PG */
public final class bjf {
    public static final String a = bbk.b("DiagnosticsWrkr");

    /* JADX INFO: finally extract failed */
    public static final String a(bgu bgu, bhy bhy, bgo bgo, List list) {
        Integer num;
        StringBuilder sb = new StringBuilder("\n Id \t Class Name\t Job Id\t State\t Unique Name\t Tags\t");
        Iterator it = list.iterator();
        while (it.hasNext()) {
            bhe bhe = (bhe) it.next();
            bgn f = wf.f(bgo, wg.f(bhe));
            if (f != null) {
                num = Integer.valueOf(f.c);
            } else {
                num = null;
            }
            String str = bhe.b;
            auu a2 = auu.a("SELECT name FROM workname WHERE work_spec_id=?", 1);
            a2.g(1, str);
            bgw bgw = (bgw) bgu;
            bgw.a.k();
            Cursor f2 = vy.f(bgw.a, a2, false);
            try {
                ArrayList arrayList = new ArrayList(f2.getCount());
                while (f2.moveToNext()) {
                    arrayList.add(f2.getString(0));
                }
                f2.close();
                a2.j();
                String M = jji.M(arrayList, ",", (CharSequence) null, (CharSequence) null, (jna) null, 62);
                String M2 = jji.M(bhy.a(bhe.b), ",", (CharSequence) null, (CharSequence) null, (jna) null, 62);
                sb.append("\n" + bhe.b + "\t " + bhe.d + "\t " + num + "\t " + bhe.c.name() + "\t " + M + "\t " + M2 + 9);
            } catch (Throwable th) {
                f2.close();
                a2.j();
                throw th;
            }
        }
        return sb.toString();
    }
}
