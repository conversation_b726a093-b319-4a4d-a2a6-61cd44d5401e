package j$.time;

import j$.time.chrono.C0010i;
import j$.time.chrono.u;
import j$.time.temporal.ChronoUnit;
import j$.time.temporal.a;
import j$.time.temporal.k;
import j$.time.temporal.m;
import j$.time.temporal.n;
import j$.time.temporal.p;
import j$.time.temporal.r;
import j$.time.temporal.t;

public enum l implements m, n {
    ;
    
    private static final l[] a = null;

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v3, resolved type: j$.time.l[]} */
    /* JADX WARNING: type inference failed for: r12v0, types: [java.lang.Enum, j$.time.l] */
    /* JADX WARNING: type inference failed for: r13v1, types: [java.lang.Enum, j$.time.l] */
    /* JADX WARNING: type inference failed for: r14v1, types: [java.lang.Enum, j$.time.l] */
    /* JADX WARNING: type inference failed for: r15v1, types: [java.lang.Enum, j$.time.l] */
    /* JADX WARNING: type inference failed for: r9v2, types: [java.lang.Enum, j$.time.l] */
    /* JADX WARNING: type inference failed for: r8v2, types: [java.lang.Enum, j$.time.l] */
    /* JADX WARNING: type inference failed for: r7v2, types: [java.lang.Enum, j$.time.l] */
    /* JADX WARNING: type inference failed for: r6v2, types: [java.lang.Enum, j$.time.l] */
    /* JADX WARNING: type inference failed for: r5v2, types: [java.lang.Enum, j$.time.l] */
    /* JADX WARNING: type inference failed for: r4v2, types: [java.lang.Enum, j$.time.l] */
    /* JADX WARNING: type inference failed for: r3v2, types: [java.lang.Enum, j$.time.l] */
    /* JADX WARNING: type inference failed for: r2v2, types: [java.lang.Enum, j$.time.l] */
    /* JADX WARNING: Multi-variable type inference failed */
    static {
        /*
            r0 = 11
            r1 = 10
            r2 = 9
            r3 = 8
            r4 = 7
            r5 = 6
            r6 = 5
            r7 = 4
            r8 = 3
            r9 = 2
            r10 = 1
            r11 = 0
            j$.time.l r12 = new j$.time.l
            java.lang.String r13 = "JANUARY"
            r12.<init>(r13, r11)
            JANUARY = r12
            j$.time.l r13 = new j$.time.l
            java.lang.String r14 = "FEBRUARY"
            r13.<init>(r14, r10)
            FEBRUARY = r13
            j$.time.l r14 = new j$.time.l
            java.lang.String r15 = "MARCH"
            r14.<init>(r15, r9)
            MARCH = r14
            j$.time.l r15 = new j$.time.l
            java.lang.String r9 = "APRIL"
            r15.<init>(r9, r8)
            APRIL = r15
            j$.time.l r9 = new j$.time.l
            java.lang.String r8 = "MAY"
            r9.<init>(r8, r7)
            MAY = r9
            j$.time.l r8 = new j$.time.l
            java.lang.String r7 = "JUNE"
            r8.<init>(r7, r6)
            JUNE = r8
            j$.time.l r7 = new j$.time.l
            java.lang.String r6 = "JULY"
            r7.<init>(r6, r5)
            JULY = r7
            j$.time.l r6 = new j$.time.l
            java.lang.String r5 = "AUGUST"
            r6.<init>(r5, r4)
            AUGUST = r6
            j$.time.l r5 = new j$.time.l
            java.lang.String r4 = "SEPTEMBER"
            r5.<init>(r4, r3)
            SEPTEMBER = r5
            j$.time.l r4 = new j$.time.l
            java.lang.String r3 = "OCTOBER"
            r4.<init>(r3, r2)
            OCTOBER = r4
            j$.time.l r3 = new j$.time.l
            java.lang.String r2 = "NOVEMBER"
            r3.<init>(r2, r1)
            NOVEMBER = r3
            j$.time.l r2 = new j$.time.l
            java.lang.String r1 = "DECEMBER"
            r2.<init>(r1, r0)
            DECEMBER = r2
            r1 = 12
            j$.time.l[] r1 = new j$.time.l[r1]
            r1[r11] = r12
            r1[r10] = r13
            r10 = 2
            r1[r10] = r14
            r10 = 3
            r1[r10] = r15
            r10 = 4
            r1[r10] = r9
            r9 = 5
            r1[r9] = r8
            r8 = 6
            r1[r8] = r7
            r7 = 7
            r1[r7] = r6
            r6 = 8
            r1[r6] = r5
            r5 = 9
            r1[r5] = r4
            r4 = 10
            r1[r4] = r3
            r1[r0] = r2
            b = r1
            j$.time.l[] r0 = values()
            a = r0
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: j$.time.l.<clinit>():void");
    }

    public static l L(int i) {
        if (i >= 1 && i <= 12) {
            return a[i - 1];
        }
        throw new RuntimeException("Invalid value for MonthOfYear: " + i);
    }

    public final long B(p pVar) {
        if (pVar == a.MONTH_OF_YEAR) {
            return (long) getValue();
        }
        if (!(pVar instanceof a)) {
            return pVar.g(this);
        }
        throw new RuntimeException("Unsupported field: ".concat(String.valueOf(pVar)));
    }

    public final Object F(r rVar) {
        if (rVar == j$.time.temporal.l.e()) {
            return u.d;
        }
        if (rVar == j$.time.temporal.l.i()) {
            return ChronoUnit.MONTHS;
        }
        return j$.time.temporal.l.c(this, rVar);
    }

    public final int I(boolean z) {
        switch (ordinal()) {
            case 0:
                return 1;
            case 1:
                return 32;
            case 2:
                return (z ? 1 : 0) + true;
            case 3:
                return z + true;
            case 4:
                return z + true;
            case 5:
                return z + true;
            case 6:
                return z + true;
            case 7:
                return z + true;
            case 8:
                return z + true;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER:
                return z + true;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER:
                return z + true;
            default:
                return z + true;
        }
    }

    public final int J(boolean z) {
        int ordinal = ordinal();
        if (ordinal != 1) {
            if (ordinal == 3 || ordinal == 5 || ordinal == 8 || ordinal == 10) {
                return 30;
            }
            return 31;
        } else if (z) {
            return 29;
        } else {
            return 28;
        }
    }

    public final int K() {
        int ordinal = ordinal();
        if (ordinal == 1) {
            return 29;
        }
        if (ordinal == 3 || ordinal == 5 || ordinal == 8 || ordinal == 10) {
            return 30;
        }
        return 31;
    }

    public final l M() {
        int i = ((int) 1) + 12;
        return a[(i + ordinal()) % 12];
    }

    public final boolean e(p pVar) {
        if (pVar instanceof a) {
            if (pVar == a.MONTH_OF_YEAR) {
                return true;
            }
            return false;
        } else if (pVar == null || !pVar.p(this)) {
            return false;
        } else {
            return true;
        }
    }

    public final k g(k kVar) {
        if (C0010i.p(kVar).equals(u.d)) {
            return kVar.d((long) getValue(), a.MONTH_OF_YEAR);
        }
        throw new RuntimeException("Adjustment only supported on ISO date-time");
    }

    public final int getValue() {
        return ordinal() + 1;
    }

    public final int p(p pVar) {
        if (pVar == a.MONTH_OF_YEAR) {
            return getValue();
        }
        return j$.time.temporal.l.a(this, pVar);
    }

    public final t t(p pVar) {
        if (pVar == a.MONTH_OF_YEAR) {
            return pVar.m();
        }
        return j$.time.temporal.l.d(this, pVar);
    }
}
