package defpackage;

/* renamed from: bge  reason: default package */
/* compiled from: PG */
final class bge extends aub {
    public bge(aus aus) {
        super(aus);
    }

    /* access modifiers changed from: protected */
    public final String a() {
        return "INSERT OR IGNORE INTO `Dependency` (`work_spec_id`,`prerequisite_id`) VALUES (?,?)";
    }

    /* access modifiers changed from: protected */
    public final /* bridge */ /* synthetic */ void c(axc axc, Object obj) {
        bvj bvj = (bvj) obj;
        axc.g(1, (String) bvj.a);
        axc.g(2, (String) bvj.b);
    }
}
