package defpackage;

/* renamed from: eqb  reason: default package */
/* compiled from: PG */
public final class eqb extends htq implements hvb {
    public static final eqb b;
    private static volatile hvh c;
    public huf a = hvk.a;

    static {
        eqb eqb = new eqb();
        b = eqb;
        htq.z(eqb.class, eqb);
    }

    private eqb() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(b, "\u0004\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u001b", new Object[]{"a", eps.class});
        } else if (i2 == 3) {
            return new eqb();
        } else {
            if (i2 == 4) {
                return new htk((htq) b);
            }
            if (i2 == 5) {
                return b;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = c;
            if (hvh == null) {
                synchronized (eqb.class) {
                    hvh = c;
                    if (hvh == null) {
                        hvh = new htl(b);
                        c = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
