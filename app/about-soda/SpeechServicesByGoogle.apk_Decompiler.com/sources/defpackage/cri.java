package defpackage;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import java.util.concurrent.atomic.AtomicBoolean;

/* renamed from: cri  reason: default package */
/* compiled from: PG */
public final class cri extends BroadcastReceiver {
    final /* synthetic */ AtomicBoolean a;
    final /* synthetic */ Runnable b;
    final /* synthetic */ po c;

    public cri(AtomicBoolean atomicBoolean, Runnable runnable, po poVar) {
        this.a = atomicBoolean;
        this.b = runnable;
        this.c = poVar;
    }

    public final void onReceive(Context context, Intent intent) {
        if (this.a.compareAndSet(false, true)) {
            context.unregisterReceiver(this);
            this.b.run();
            this.c.c((Object) null);
        }
    }
}
