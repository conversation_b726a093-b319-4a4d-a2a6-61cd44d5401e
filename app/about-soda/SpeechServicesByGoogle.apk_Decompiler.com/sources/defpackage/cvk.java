package defpackage;

import j$.util.Collection;
import j$.util.Objects;
import j$.util.stream.Stream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/* renamed from: cvk  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvk implements hko {
    public final /* synthetic */ boolean a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    private final /* synthetic */ int d;

    public /* synthetic */ cvk(cwm cwm, ctg ctg, boolean z, int i) {
        this.d = i;
        this.c = cwm;
        this.b = ctg;
        this.a = z;
    }

    /* JADX WARNING: type inference failed for: r12v7, types: [java.util.concurrent.Future, java.lang.Object] */
    public final hme a(Object obj) {
        int i = this.d;
        if (i == 0) {
            ArrayList arrayList = new ArrayList();
            Iterator it = ((List) obj).iterator();
            while (true) {
                Object obj2 = this.b;
                if (!it.hasNext()) {
                    return cqh.U(arrayList).n(new ctw(6), ((cvy) obj2).e);
                }
                ctg ctg = (ctg) it.next();
                if (!ctg.e) {
                    Object obj3 = this.c;
                    cvy cvy = (cvy) obj2;
                    arrayList.add(cvy.q(cvy.c.g(ctg), new cvu(obj2, this.a, (Object) ctg, obj3, 0)));
                }
            }
        } else if (i == 1) {
            boolean booleanValue = ((Boolean) obj).booleanValue();
            Object obj4 = this.b;
            if (!booleanValue) {
                ((cvy) obj4).i.d(1036);
                return hfc.J(new IOException("Unable to update file group metadata"));
            }
            ? r12 = this.c;
            if (this.a) {
                new cxi((Object) ((cvy) obj4).i, (char[]) null).e(1072, (csx) hfc.S(r12));
            }
            return hfc.K((csx) hfc.S(r12));
        } else if (i == 2) {
            Void voidR = (Void) obj;
            if (!ikc.a.a().t()) {
                return hma.a;
            }
            Object obj5 = this.c;
            boolean z = this.a;
            cwm cwm = (cwm) this.b;
            cwm.l.d(1031);
            cvy cvy2 = cwm.c;
            return cvy2.q(cvy2.c.d(), gof.d(new cvk((Object) cvy2, z, obj5, 0)));
        } else if (i != 3) {
            gyo gyo = (gyo) obj;
            ((hdc) ((hdc) eyo.a.f()).j("com/google/android/libraries/speech/modeldownload/languagepacks/inject/LanguagePackDirGeneratorImpl", "getLanguagePackInfo", 92, "LanguagePackDirGeneratorImpl.java")).u("Found matching installed packs: %s", exo.a(gyo));
            gxv gxv = (gxv) Collection.EL.stream(gyo).collect(gvx.a(new eyh(5), new eyh(6)));
            Object obj6 = this.c;
            boolean containsKey = gxv.containsKey(obj6);
            Object obj7 = this.b;
            if (!containsKey) {
                eyo eyo = (eyo) obj7;
                exx exx = eyo.c;
                htk l = eyv.g.l();
                if (!l.b.B()) {
                    l.u();
                }
                eyv eyv = (eyv) l.b;
                obj6.getClass();
                eyv.a = 1 | eyv.a;
                eyv.b = (String) obj6;
                return ftd.L(exx.c((eyv) l.r()), new eyd(obj6, 5), eyo.d);
            } else if (!this.a) {
                return hfc.H(gxq.q(((eyo) obj7).b.e((exo) gxv.get(obj6))));
            } else {
                Stream stream = Collection.EL.stream(gxv.values());
                exq exq = ((eyo) obj7).b;
                Objects.requireNonNull(exq);
                Stream map = stream.map(new bof(exq, 11));
                int i2 = gxq.d;
                return hfc.H((Iterable) map.collect(gvx.a));
            }
        } else {
            Void voidR2 = (Void) obj;
            return ((cwm) this.c).c.g((ctg) this.b, this.a);
        }
    }

    public /* synthetic */ cvk(eyo eyo, String str, boolean z, int i) {
        this.d = i;
        this.b = eyo;
        this.c = str;
        this.a = z;
    }

    public /* synthetic */ cvk(Object obj, boolean z, Object obj2, int i) {
        this.d = i;
        this.b = obj;
        this.a = z;
        this.c = obj2;
    }
}
