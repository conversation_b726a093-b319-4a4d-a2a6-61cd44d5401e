package defpackage;

import android.content.Context;
import android.content.SharedPreferences;
import j$.nio.channels.DesugarChannels;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Executor;

/* renamed from: cxb  reason: default package */
/* compiled from: PG */
public final class cxb implements cvz {
    public final Executor a;
    private final Context b;
    private final cuk c;
    private final grh d;

    public cxb(Context context, cuk cuk, grh grh, Executor executor) {
        this.b = context;
        this.c = cuk;
        this.d = grh;
        this.a = executor;
    }

    public final hme a(csx csx) {
        cyh.d("%s: Adding file group %s", "SharedPreferencesFileGroupsMetadata", csx.c);
        csx j = cqx.j(csx, (cqx.c() / 1000) + csx.j);
        ArrayList arrayList = new ArrayList();
        arrayList.add(j);
        return m(arrayList);
    }

    public final hme b() {
        cqh.B(this.b, "gms_icing_mdd_groups", this.d).edit().clear().commit();
        cqh.B(this.b, "gms_icing_mdd_group_key_properties", this.d).edit().clear().commit();
        return k();
    }

    public final hme c() {
        return ftd.L(d(), new cwi(this, 16), this.a);
    }

    public final hme d() {
        ArrayList arrayList = new ArrayList();
        SharedPreferences B = cqh.B(this.b, "gms_icing_mdd_groups", this.d);
        SharedPreferences.Editor editor = null;
        for (String next : B.getAll().keySet()) {
            try {
                arrayList.add(cqx.d(next));
            } catch (czf e) {
                cyh.q(e, "Failed to deserialize groupKey:".concat(String.valueOf(next)));
                this.c.a();
                if (editor == null) {
                    editor = B.edit();
                }
                editor.remove(next);
                cyh.c("%s: Deleting null file group ", "SharedPreferencesFileGroupsMetadata");
            }
        }
        if (editor != null) {
            editor.commit();
        }
        return hfc.K(arrayList);
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x004b  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme e() {
        /*
            r8 = this;
            java.lang.String r0 = "FileGroupsMetadataUtil"
            android.content.Context r1 = r8.b
            grh r2 = r8.d
            java.io.File r1 = defpackage.cqx.e(r1, r2)
            java.io.FileInputStream r2 = new java.io.FileInputStream     // Catch:{ FileNotFoundException -> 0x005f }
            r2.<init>(r1)     // Catch:{ FileNotFoundException -> 0x005f }
            r3 = 0
            r4 = 1
            long r5 = r1.length()     // Catch:{ IllegalArgumentException -> 0x0050 }
            int r1 = (int) r5     // Catch:{ IllegalArgumentException -> 0x0050 }
            java.nio.ByteBuffer r1 = java.nio.ByteBuffer.allocate(r1)     // Catch:{ IllegalArgumentException -> 0x0050 }
            java.nio.channels.FileChannel r5 = r2.getChannel()     // Catch:{ IOException -> 0x003d }
            java.nio.channels.FileChannel r5 = j$.nio.channels.DesugarChannels.convertMaybeLegacyFileChannelFromLibrary(r5)     // Catch:{ IOException -> 0x003d }
            r5.read(r1)     // Catch:{ IOException -> 0x003d }
            r1.rewind()     // Catch:{ IOException -> 0x003d }
            java.lang.Class<csx> r5 = defpackage.csx.class
            csx r6 = defpackage.csx.w     // Catch:{ IOException -> 0x003d }
            r7 = 7
            java.lang.Object r6 = r6.C(r7)     // Catch:{ IOException -> 0x003d }
            hvh r6 = (defpackage.hvh) r6     // Catch:{ IOException -> 0x003d }
            java.util.List r1 = defpackage.cqh.Q(r1, r5, r6)     // Catch:{ IOException -> 0x003d }
            r2.close()     // Catch:{ IOException -> 0x003b }
            goto L_0x0049
        L_0x003b:
            r2 = move-exception
            goto L_0x0040
        L_0x003d:
            r1 = move-exception
            r2 = r1
            r1 = 0
        L_0x0040:
            java.lang.Object[] r4 = new java.lang.Object[r4]
            r4[r3] = r0
            java.lang.String r0 = "%s: IOException occurred while reading file groups."
            defpackage.cyh.j(r2, r0, r4)
        L_0x0049:
            if (r1 != 0) goto L_0x006c
            int r0 = defpackage.gxq.d
            gxq r1 = defpackage.hal.a
            goto L_0x006c
        L_0x0050:
            r1 = move-exception
            java.lang.Object[] r2 = new java.lang.Object[r4]
            r2[r3] = r0
            java.lang.String r0 = "%s: Exception while reading from stale groups into buffer."
            defpackage.cyh.j(r1, r0, r2)
            int r0 = defpackage.gxq.d
            gxq r1 = defpackage.hal.a
            goto L_0x006c
        L_0x005f:
            java.lang.String r0 = r1.getAbsolutePath()
            java.lang.String r1 = "File %s not found while reading."
            defpackage.cyh.c(r1, r0)
            int r0 = defpackage.gxq.d
            gxq r1 = defpackage.hal.a
        L_0x006c:
            hme r0 = defpackage.hfc.K(r1)
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cxb.e():hme");
    }

    public final hme f() {
        return hma.a;
    }

    public final hme g(ctg ctg) {
        Context context = this.b;
        return hfc.K((csx) cqh.D(cqh.B(context, "gms_icing_mdd_groups", this.d), cqx.f(ctg), (hvh) csx.w.C(7)));
    }

    public final hme h(ctg ctg) {
        Context context = this.b;
        return hfc.K((cth) cqh.D(cqh.B(context, "gms_icing_mdd_group_key_properties", this.d), cqx.f(ctg), (hvh) cth.b.C(7)));
    }

    public final hme i(ctg ctg) {
        Context context = this.b;
        grh grh = this.d;
        return hfc.K(Boolean.valueOf(cqh.I(cqh.B(context, "gms_icing_mdd_groups", grh), cqx.f(ctg))));
    }

    public final hme j(List list) {
        SharedPreferences.Editor edit = cqh.B(this.b, "gms_icing_mdd_groups", this.d).edit();
        Iterator it = list.iterator();
        while (it.hasNext()) {
            ctg ctg = (ctg) it.next();
            cyh.e("%s: Removing group %s %s", "SharedPreferencesFileGroupsMetadata", ctg.b, ctg.c);
            edit.remove(cqh.F(ctg));
        }
        return hfc.K(Boolean.valueOf(edit.commit()));
    }

    public final hme k() {
        n().delete();
        return hma.a;
    }

    public final hme l(ctg ctg, csx csx) {
        Context context = this.b;
        grh grh = this.d;
        return hfc.K(Boolean.valueOf(cqh.J(cqh.B(context, "gms_icing_mdd_groups", grh), cqx.f(ctg), csx)));
    }

    public final hme m(List list) {
        File n = n();
        try {
            FileOutputStream fileOutputStream = new FileOutputStream(n, true);
            try {
                ByteBuffer P = cqh.P(list);
                if (P != null) {
                    DesugarChannels.convertMaybeLegacyFileChannelFromLibrary(fileOutputStream.getChannel()).write(P);
                }
                fileOutputStream.close();
                return hfc.K(true);
            } catch (IOException unused) {
                cyh.f("IOException occurred while writing file groups.");
                return hfc.K(false);
            }
        } catch (FileNotFoundException unused2) {
            cyh.g("File %s not found while writing.", n.getAbsolutePath());
            return hfc.K(false);
        }
    }

    /* access modifiers changed from: package-private */
    public final File n() {
        return cqx.e(this.b, this.d);
    }
}
