package defpackage;

import java.util.concurrent.TimeUnit;

/* renamed from: bbu  reason: default package */
/* compiled from: PG */
public final class bbu extends bca {
    public bbu(Class cls, long j, TimeUnit timeUnit) {
        super(cls);
        bhe bhe = this.b;
        long millis = timeUnit.toMillis(j);
        if (millis < 900000) {
            bbk.a().f(bhe.a, "Interval duration lesser than minimum allowed value; Changed to 900000");
        }
        long q = jnu.q(millis, 900000);
        long q2 = jnu.q(millis, 900000);
        if (q < 900000) {
            bbk.a().f(bhe.a, "Interval duration lesser than minimum allowed value; Changed to 900000");
        }
        bhe.i = jnu.q(q, 900000);
        if (q2 < 300000) {
            bbk.a().f(bhe.a, "Flex duration lesser than minimum allowed value; Changed to 300000");
        }
        if (q2 > bhe.i) {
            bbk.a().f(bhe.a, a.au(q, "Flex duration greater than interval duration; Changed to "));
        }
        bhe.j = jnu.s(q2, 300000, bhe.i);
    }

    public final /* bridge */ /* synthetic */ bmu a() {
        bhe bhe = this.b;
        if (!bhe.r) {
            return new bmu(this.a, bhe, this.c);
        }
        throw new IllegalArgumentException("PeriodicWorkRequests cannot be expedited");
    }
}
