package defpackage;

import android.net.Uri;
import java.util.List;
import java.util.concurrent.Callable;

/* renamed from: cxu  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cxu implements hko {
    public final /* synthetic */ cxx a;
    public final /* synthetic */ String b;
    public final /* synthetic */ Uri c;
    public final /* synthetic */ cxw d;
    public final /* synthetic */ ctg e;
    public final /* synthetic */ int f;
    public final /* synthetic */ long g;
    public final /* synthetic */ String h;
    public final /* synthetic */ String i;
    public final /* synthetic */ int j;
    public final /* synthetic */ csz k;
    public final /* synthetic */ int l;
    public final /* synthetic */ List m;
    public final /* synthetic */ hse n;

    public /* synthetic */ cxu(cxx cxx, String str, Uri uri, cxw cxw, ctg ctg, int i2, long j2, String str2, String str3, int i3, csz csz, int i4, List list, hse hse) {
        this.a = cxx;
        this.b = str;
        this.c = uri;
        this.d = cxw;
        this.e = ctg;
        this.f = i2;
        this.g = j2;
        this.h = str2;
        this.i = str3;
        this.j = i3;
        this.k = csz;
        this.l = i4;
        this.m = list;
        this.n = hse;
    }

    public final hme a(Object obj) {
        String str;
        hme hme;
        grh grh = (grh) obj;
        if (grh.f()) {
            return (hme) grh.b();
        }
        hse hse = this.n;
        List list = this.m;
        int i2 = this.l;
        csz csz = this.k;
        int i3 = this.j;
        String str2 = this.i;
        String str3 = this.h;
        long j2 = this.g;
        int i4 = this.f;
        ctg ctg = this.e;
        cxw cxw = this.d;
        Uri uri = this.c;
        String str4 = this.b;
        cxx cxx = this.a;
        String str5 = str4;
        cxv cxv = new cxv(cxx, ctg, i4, j2, str3, uri, str2, i3, csz, i2, list, hse);
        hmf hmf = new hmf((Callable) new ctw(14));
        cxx cxx2 = cxx;
        Uri uri2 = uri;
        czw d2 = czw.e(hmf).g(cxv, cxx2.f).g(new cwq(cxw, uri2, 9), cxx2.f).d(Exception.class, new cwq(cxx2, cxw, 10), cxx2.f);
        if (!cqh.i()) {
            cxx2.g.put(uri2, d2);
            hme = hma.a;
            str = str5;
        } else {
            str = str5;
            hme = cxx2.h.e(str, d2);
        }
        czw g2 = czw.e(hme).g(new cwq(hmf, d2, 11), cxx2.f);
        g2.c(new ai((Object) cxx2, (Object) str, (Object) uri2, 16), cxx2.f);
        return g2;
    }
}
