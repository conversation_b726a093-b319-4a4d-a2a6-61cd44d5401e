# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: soda_api.proto
# Protobuf Python Version: 5.28.3
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    28,
    3,
    '',
    'soda_api.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0esoda_api.proto\x12\x12speech.soda.chrome\"\xb2\x05\n\x15\x45xtendedSodaConfigMsg\x12\x15\n\rchannel_count\x18\x01 \x01(\x05\x12\x13\n\x0bsample_rate\x18\x02 \x01(\x05\x12\x1b\n\x10max_buffer_bytes\x18\x04 \x01(\x05:\x01\x30\x12)\n\x1asimulate_realtime_testonly\x18\x05 \x01(\x08:\x05\x66\x61lse\x12 \n\x14\x63onfig_file_location\x18\x03 \x01(\tB\x02\x18\x01\x12\x0f\n\x07\x61pi_key\x18\x06 \x01(\t\x12\x1f\n\x17language_pack_directory\x18\x07 \x01(\t\x12X\n\x10recognition_mode\x18\x08 \x01(\x0e\x32\x39.speech.soda.chrome.ExtendedSodaConfigMsg.RecognitionMode:\x03IME\x12\'\n\x15reset_on_final_result\x18\t \x01(\x08:\x04trueB\x02\x18\x01\x12$\n\x16include_timing_metrics\x18\n \x01(\x08:\x04true\x12\x1d\n\x0e\x65nable_lang_id\x18\x0b \x01(\x08:\x05\x66\x61lse\x12\x1f\n\x11\x65nable_formatting\x18\x0c \x01(\x08:\x04true\x12.\n\x1f\x65nable_speaker_change_detection\x18\r \x01(\x08:\x05\x66\x61lse\x12\x1e\n\x0finclude_logging\x18\x0e \x01(\x08:\x05\x66\x61lse\x12=\n\x10multilang_config\x18\x0f \x01(\x0b\x32#.speech.soda.chrome.MultilangConfig\x12#\n\x14mask_offensive_words\x18\x10 \x01(\x08:\x05\x66\x61lse\"4\n\x0fRecognitionMode\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x07\n\x03IME\x10\x01\x12\x0b\n\x07\x43\x41PTION\x10\x02\"\xf4\x01\n\x0fMultilangConfig\x12r\n!multilang_language_pack_directory\x18\x01 \x03(\x0b\x32G.speech.soda.chrome.MultilangConfig.MultilangLanguagePackDirectoryEntry\x12&\n\x1erewind_when_switching_language\x18\x02 \x01(\x08\x1a\x45\n#MultilangLanguagePackDirectoryEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x8b\x01\n\rTimingMetrics\x12\x1e\n\x16\x61udio_start_epoch_usec\x18\x01 \x01(\x03\x12\x1d\n\x15\x61udio_start_time_usec\x18\x02 \x01(\x03\x12\x1e\n\x16\x65lapsed_wall_time_usec\x18\x03 \x01(\x03\x12\x1b\n\x13\x65vent_end_time_usec\x18\x04 \x01(\x03\"4\n\x0eHypothesisPart\x12\x0c\n\x04text\x18\x01 \x03(\t\x12\x14\n\x0c\x61lignment_ms\x18\x02 \x01(\x03\"\xf3\x04\n\x15SodaRecognitionResult\x12\x12\n\nhypothesis\x18\x01 \x03(\t\x12I\n\x0bresult_type\x18\x02 \x01(\x0e\x32\x34.speech.soda.chrome.SodaRecognitionResult.ResultType\x12\\\n\x0f\x65ndpoint_reason\x18\x03 \x01(\x0e\x32\x43.speech.soda.chrome.SodaRecognitionResult.FinalResultEndpointReason\x12\x39\n\x0etiming_metrics\x18\x04 \x01(\x0b\x32!.speech.soda.chrome.TimingMetrics\x12;\n\x0fhypothesis_part\x18\x05 \x03(\x0b\x32\".speech.soda.chrome.HypothesisPart\"?\n\nResultType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0b\n\x07PARTIAL\x10\x01\x12\t\n\x05\x46INAL\x10\x02\x12\x0c\n\x08PREFETCH\x10\x03\"\xe3\x01\n\x19\x46inalResultEndpointReason\x12\x14\n\x10\x45NDPOINT_UNKNOWN\x10\x00\x12\x1a\n\x16\x45NDPOINT_END_OF_SPEECH\x10\x01\x12\x1d\n\x19\x45NDPOINT_END_OF_UTTERANCE\x10\x02\x12\x19\n\x15\x45NDPOINT_END_OF_AUDIO\x10\x03\x12!\n\x1d\x45NDPOINT_ASR_RESET_BY_HOTWORD\x10\x04\x12\x1f\n\x1b\x45NDPOINT_ASR_RESET_EXTERNAL\x10\x05\x12\x16\n\x12\x45NDPOINT_ASR_ERROR\x10\x06\"\x8f\x02\n\x11SodaEndpointEvent\x12R\n\rendpoint_type\x18\x01 \x01(\x0e\x32\x32.speech.soda.chrome.SodaEndpointEvent.EndpointType:\x07UNKNOWN\x12\x39\n\x0etiming_metrics\x18\x02 \x01(\x0b\x32!.speech.soda.chrome.TimingMetrics\"k\n\x0c\x45ndpointType\x12\x13\n\x0fSTART_OF_SPEECH\x10\x00\x12\x11\n\rEND_OF_SPEECH\x10\x01\x12\x10\n\x0c\x45ND_OF_AUDIO\x10\x02\x12\x14\n\x10\x45ND_OF_UTTERANCE\x10\x03\x12\x0b\n\x07UNKNOWN\x10\x04\"O\n\x12SodaAudioLevelInfo\x12\x0b\n\x03rms\x18\x01 \x01(\x02\x12\x13\n\x0b\x61udio_level\x18\x02 \x01(\x02\x12\x17\n\x0f\x61udio_time_usec\x18\x03 \x01(\x03\"\xfa\x01\n\x0fSodaLangIdEvent\x12\x10\n\x08language\x18\x01 \x01(\t\x12\x18\n\x10\x63onfidence_level\x18\x02 \x01(\x05\x12N\n\x11\x61sr_switch_result\x18\x03 \x01(\x0e\x32\x33.speech.soda.chrome.SodaLangIdEvent.AsrSwitchResult\"k\n\x0f\x41srSwitchResult\x12\x15\n\x11\x44\x45\x46\x41ULT_NO_SWITCH\x10\x00\x12\x14\n\x10SWITCH_SUCCEEDED\x10\x01\x12\x11\n\rSWITCH_FAILED\x10\x02\x12\x18\n\x14SWITCH_SKIPPED_NO_LP\x10\x03\"\x94\x04\n\x0cSodaResponse\x12L\n\tsoda_type\x18\x01 \x01(\x0e\x32\x30.speech.soda.chrome.SodaResponse.SodaMessageType:\x07UNKNOWN\x12\x45\n\x12recognition_result\x18\x02 \x01(\x0b\x32).speech.soda.chrome.SodaRecognitionResult\x12=\n\x0e\x65ndpoint_event\x18\x03 \x01(\x0b\x32%.speech.soda.chrome.SodaEndpointEvent\x12@\n\x10\x61udio_level_info\x18\x04 \x01(\x0b\x32&.speech.soda.chrome.SodaAudioLevelInfo\x12\x39\n\x0clangid_event\x18\x05 \x01(\x0b\x32#.speech.soda.chrome.SodaLangIdEvent\x12\x11\n\tlog_lines\x18\x06 \x03(\t\"\x9f\x01\n\x0fSodaMessageType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0f\n\x0bRECOGNITION\x10\x01\x12\x08\n\x04STOP\x10\x02\x12\x0c\n\x08SHUTDOWN\x10\x03\x12\t\n\x05START\x10\x04\x12\x0c\n\x08\x45NDPOINT\x10\x05\x12\x0f\n\x0b\x41UDIO_LEVEL\x10\x06\x12\n\n\x06LANGID\x10\x07\x12 \n\x1cLOGS_ONLY_ARTIFICIAL_MESSAGE\x10\x08\x42\x04H\x03P\x01')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'soda_api_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'H\003P\001'
  _globals['_EXTENDEDSODACONFIGMSG'].fields_by_name['config_file_location']._loaded_options = None
  _globals['_EXTENDEDSODACONFIGMSG'].fields_by_name['config_file_location']._serialized_options = b'\030\001'
  _globals['_EXTENDEDSODACONFIGMSG'].fields_by_name['reset_on_final_result']._loaded_options = None
  _globals['_EXTENDEDSODACONFIGMSG'].fields_by_name['reset_on_final_result']._serialized_options = b'\030\001'
  _globals['_MULTILANGCONFIG_MULTILANGLANGUAGEPACKDIRECTORYENTRY']._loaded_options = None
  _globals['_MULTILANGCONFIG_MULTILANGLANGUAGEPACKDIRECTORYENTRY']._serialized_options = b'8\001'
  _globals['_EXTENDEDSODACONFIGMSG']._serialized_start=39
  _globals['_EXTENDEDSODACONFIGMSG']._serialized_end=729
  _globals['_EXTENDEDSODACONFIGMSG_RECOGNITIONMODE']._serialized_start=677
  _globals['_EXTENDEDSODACONFIGMSG_RECOGNITIONMODE']._serialized_end=729
  _globals['_MULTILANGCONFIG']._serialized_start=732
  _globals['_MULTILANGCONFIG']._serialized_end=976
  _globals['_MULTILANGCONFIG_MULTILANGLANGUAGEPACKDIRECTORYENTRY']._serialized_start=907
  _globals['_MULTILANGCONFIG_MULTILANGLANGUAGEPACKDIRECTORYENTRY']._serialized_end=976
  _globals['_TIMINGMETRICS']._serialized_start=979
  _globals['_TIMINGMETRICS']._serialized_end=1118
  _globals['_HYPOTHESISPART']._serialized_start=1120
  _globals['_HYPOTHESISPART']._serialized_end=1172
  _globals['_SODARECOGNITIONRESULT']._serialized_start=1175
  _globals['_SODARECOGNITIONRESULT']._serialized_end=1802
  _globals['_SODARECOGNITIONRESULT_RESULTTYPE']._serialized_start=1509
  _globals['_SODARECOGNITIONRESULT_RESULTTYPE']._serialized_end=1572
  _globals['_SODARECOGNITIONRESULT_FINALRESULTENDPOINTREASON']._serialized_start=1575
  _globals['_SODARECOGNITIONRESULT_FINALRESULTENDPOINTREASON']._serialized_end=1802
  _globals['_SODAENDPOINTEVENT']._serialized_start=1805
  _globals['_SODAENDPOINTEVENT']._serialized_end=2076
  _globals['_SODAENDPOINTEVENT_ENDPOINTTYPE']._serialized_start=1969
  _globals['_SODAENDPOINTEVENT_ENDPOINTTYPE']._serialized_end=2076
  _globals['_SODAAUDIOLEVELINFO']._serialized_start=2078
  _globals['_SODAAUDIOLEVELINFO']._serialized_end=2157
  _globals['_SODALANGIDEVENT']._serialized_start=2160
  _globals['_SODALANGIDEVENT']._serialized_end=2410
  _globals['_SODALANGIDEVENT_ASRSWITCHRESULT']._serialized_start=2303
  _globals['_SODALANGIDEVENT_ASRSWITCHRESULT']._serialized_end=2410
  _globals['_SODARESPONSE']._serialized_start=2413
  _globals['_SODARESPONSE']._serialized_end=2945
  _globals['_SODARESPONSE_SODAMESSAGETYPE']._serialized_start=2786
  _globals['_SODARESPONSE_SODAMESSAGETYPE']._serialized_end=2945
# @@protoc_insertion_point(module_scope)
