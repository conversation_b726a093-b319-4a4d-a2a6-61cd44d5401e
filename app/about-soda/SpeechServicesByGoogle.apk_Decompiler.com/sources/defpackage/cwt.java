package defpackage;

/* renamed from: cwt  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwt implements hko {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    private final /* synthetic */ int d;

    public /* synthetic */ cwt(ctf ctf, cxa cxa, ctj ctj, int i) {
        this.d = i;
        this.c = ctf;
        this.a = cxa;
        this.b = ctj;
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r6v26, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r12v8, resolved type: iai} */
    /* JADX WARNING: type inference failed for: r2v13, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v4, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v20, types: [java.lang.Object, cxa] */
    /* JADX WARNING: type inference failed for: r2v33, types: [java.lang.Object, cxa] */
    /* JADX WARNING: type inference failed for: r3v19, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v25, types: [java.lang.Object, cxa] */
    /* JADX WARNING: type inference failed for: r0v47, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v33, types: [dey, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v49, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v65, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v51, types: [hme, java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v69, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v70, types: [java.lang.Object, hko] */
    /* JADX WARNING: type inference failed for: r2v71, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v55, types: [java.lang.Object, hko] */
    /* JADX WARNING: type inference failed for: r2v72, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme a(java.lang.Object r18) {
        /*
            r17 = this;
            r1 = r17
            r0 = r18
            int r2 = r1.d
            r3 = 3
            r4 = 0
            r5 = 4
            r6 = 5
            r7 = 0
            r8 = 2
            r9 = 1
            switch(r2) {
                case 0: goto L_0x03cf;
                case 1: goto L_0x038c;
                case 2: goto L_0x0355;
                case 3: goto L_0x030e;
                case 4: goto L_0x02b4;
                case 5: goto L_0x0258;
                case 6: goto L_0x0225;
                case 7: goto L_0x0202;
                case 8: goto L_0x01e6;
                case 9: goto L_0x0147;
                case 10: goto L_0x0105;
                case 11: goto L_0x00d3;
                case 12: goto L_0x00c4;
                case 13: goto L_0x00af;
                case 14: goto L_0x004b;
                default: goto L_0x0010;
            }
        L_0x0010:
            java.lang.Object r2 = r1.b
            ghi r2 = (defpackage.ghi) r2
            fbb r0 = r2.d(r0)
            j$.time.Instant r4 = j$.time.Instant.now()
            long r4 = r4.toEpochMilli()
            java.lang.String r4 = java.lang.Long.toString(r4)
            dwj r5 = r0.c
            dxm r6 = defpackage.dwv.aj
            java.lang.String r10 = "timestamp"
            dxh r6 = r6.d(r10, r4)
            r5.a(r6)
            java.lang.Object r5 = r1.c
            android.content.Intent r5 = (android.content.Intent) r5
            java.lang.String r6 = r5.getAction()
            java.lang.String r10 = "calling_package"
            java.lang.String r10 = r5.getStringExtra(r10)
            if (r6 == 0) goto L_0x0409
            java.lang.String r11 = "android.speech.action.GET_LANGUAGE_DETAILS"
            boolean r6 = r6.equals(r11)
            if (r6 != 0) goto L_0x03ee
            goto L_0x0409
        L_0x004b:
            java.lang.Object r2 = r1.a
            gdn r0 = (defpackage.gdn) r0
            java.lang.String r3 = "$values"
            defpackage.jnu.e(r2, r3)
            java.lang.Object r3 = r1.c
            java.lang.String r4 = "$snapshot"
            defpackage.jnu.e(r3, r4)
            gcq r3 = (defpackage.gcq) r3
            gdv r4 = defpackage.ftc.v(r3)
            boolean r0 = r0.e(r2, r4)
            if (r0 == 0) goto L_0x00a5
            java.lang.Object r0 = r1.b
            gdx r0 = (defpackage.gdx) r0
            jjo r2 = r0.t
            java.lang.Object r2 = r2.a()
            java.lang.Boolean r2 = (java.lang.Boolean) r2
            boolean r2 = r2.booleanValue()
            if (r2 == 0) goto L_0x0083
            gda r0 = new gda
            r0.<init>((boolean) r9)
            hme r0 = defpackage.hfc.K(r0)
            goto L_0x00ae
        L_0x0083:
            jjk r0 = r0.n
            java.lang.Object r0 = r0.b()
            hsq r2 = r3.d
            java.lang.String r3 = r3.c
            gnk r0 = (defpackage.gnk) r0
            hme r0 = r0.i(r2, r3)
            fhd r2 = new fhd
            r3 = 13
            r2.<init>(r3)
            gqx r2 = defpackage.gof.b(r2)
            hld r3 = defpackage.hld.a
            hme r0 = defpackage.ftd.K(r0, r2, r3)
            goto L_0x00ae
        L_0x00a5:
            gda r0 = new gda
            r0.<init>((boolean) r7)
            hme r0 = defpackage.hfc.K(r0)
        L_0x00ae:
            return r0
        L_0x00af:
            java.lang.Void r0 = (java.lang.Void) r0
            java.lang.Object r0 = r1.c
            androidx.wear.ambient.AmbientModeSupport$AmbientController r0 = (androidx.wear.ambient.AmbientModeSupport.AmbientController) r0
            java.lang.Object r0 = r0.a
            fps r0 = (defpackage.fps) r0
            fpq r0 = r0.f
            java.lang.Object r2 = r1.a
            java.lang.Object r3 = r1.b
            hme r0 = r0.d(r3, r2)
            return r0
        L_0x00c4:
            java.lang.Object r0 = r1.a
            java.lang.Object r2 = r1.b
            java.lang.Object r3 = r1.c
            fps r3 = (defpackage.fps) r3
            fpq r3 = r3.f
            hme r0 = r3.d(r2, r0)
            return r0
        L_0x00d3:
            java.lang.Object r2 = r1.b
            java.lang.Object r3 = r1.a
            java.lang.Object r2 = defpackage.hfc.S(r2)
            java.lang.Object r5 = defpackage.hfc.S(r3)
            boolean r2 = r2.equals(r5)
            java.lang.Object r5 = r1.c
            if (r2 == 0) goto L_0x00ec
            hme r0 = defpackage.hfc.K(r18)
            goto L_0x0101
        L_0x00ec:
            ezm r0 = new ezm
            r0.<init>(r5, r3, r8, r4)
            hko r0 = defpackage.gof.d(r0)
            fpq r5 = (defpackage.fpq) r5
            java.util.concurrent.Executor r2 = r5.c
            hme r0 = defpackage.hke.g(r3, r0, r2)
            java.lang.Object r2 = r5.e
            monitor-enter(r2)
            monitor-exit(r2)     // Catch:{ all -> 0x0102 }
        L_0x0101:
            return r0
        L_0x0102:
            r0 = move-exception
            monitor-exit(r2)     // Catch:{ all -> 0x0102 }
            throw r0
        L_0x0105:
            java.lang.String r0 = (java.lang.String) r0
            java.lang.Object[] r2 = new java.lang.Object[r9]
            r2[r7] = r0
            java.lang.Object r0 = r1.a
            ezo r0 = (defpackage.ezo) r0
            android.content.Context r3 = r0.f
            r5 = 2132017312(0x7f1400a0, float:1.9672899E38)
            java.lang.String r2 = r3.getString(r5, r2)
            grh r2 = defpackage.grh.h(r2)
            java.lang.Object r3 = r1.c
            csj r3 = (defpackage.csj) r3
            r3.a = r2
            java.lang.String r2 = ""
            grh r2 = defpackage.grh.h(r2)
            r3.b = r2
            java.lang.Object r2 = r1.b
            exk r2 = (defpackage.exk) r2
            j$.util.Optional r2 = r2.a
            java.lang.Object r2 = r2.orElse(r4)
            csf r2 = (defpackage.csf) r2
            grh r2 = defpackage.grh.g(r2)
            r3.c = r2
            cto r0 = r0.b
            csk r2 = r3.a()
            hme r0 = r0.c(r2)
            return r0
        L_0x0147:
            java.util.List r0 = (java.util.List) r0
            java.lang.Object r2 = r1.b
            duw r2 = (defpackage.duw) r2
            boolean r3 = r2.e
            if (r3 != 0) goto L_0x0157
            java.lang.String r0 = ""
            gxq r0 = defpackage.gxq.q(r0)
        L_0x0157:
            gxl r10 = new gxl
            r10.<init>()
            java.util.Iterator r0 = r0.iterator()
        L_0x0160:
            java.lang.Object r3 = r1.c
            boolean r4 = r0.hasNext()
            if (r4 == 0) goto L_0x01ce
            java.lang.Object r4 = r1.a
            java.lang.Object r5 = r0.next()
            r7 = r5
            java.lang.String r7 = (java.lang.String) r7
            java.util.concurrent.ConcurrentMap r5 = defpackage.dve.c
            gri r6 = new gri
            r6.<init>(r4, r7)
            boolean r5 = r5.containsKey(r6)
            if (r5 != 0) goto L_0x0160
            boolean r5 = r2.c
            gef r6 = new gef
            java.lang.String r4 = (java.lang.String) r4
            r11 = r3
            dte r11 = (defpackage.dte) r11
            r6.<init>((defpackage.dte) r11, (java.lang.String) r4, (java.lang.String) r7, (boolean) r5)
            boolean r3 = r2.d
            if (r3 == 0) goto L_0x019d
            android.content.Context r3 = r11.c
            java.lang.String r4 = r2.a
            android.content.SharedPreferences r3 = defpackage.dvd.a(r3)
            java.lang.String r5 = ""
            java.lang.String r3 = r3.getString(r4, r5)
            goto L_0x019e
        L_0x019d:
            r3 = r7
        L_0x019e:
            hme r5 = r6.a(r3)
            hly r3 = defpackage.hly.q(r5)
            j$.util.Objects.requireNonNull(r6)
            cxr r4 = new cxr
            r8 = 12
            r4.<init>(r6, r8)
            hmi r6 = r11.d()
            hme r12 = defpackage.hke.g(r3, r4, r6)
            cvd r13 = new cvd
            r8 = 20
            r3 = r13
            r4 = r11
            r6 = r2
            r3.<init>((defpackage.dte) r4, (defpackage.hme) r5, (defpackage.duw) r6, (java.lang.String) r7, (int) r8)
            hmi r3 = r11.d()
            hme r3 = defpackage.hke.g(r12, r13, r3)
            r10.h(r3)
            goto L_0x0160
        L_0x01ce:
            gxq r0 = r10.g()
            ipt r0 = defpackage.hfc.Y(r0)
            dvq r2 = new dvq
            r2.<init>(r9)
            dte r3 = (defpackage.dte) r3
            hmi r3 = r3.d()
            hme r0 = r0.a(r2, r3)
            return r0
        L_0x01e6:
            java.util.Map r0 = (java.util.Map) r0
            java.util.Collection r2 = r0.values()
            ipt r2 = defpackage.hfc.Y(r2)
            java.lang.Object r3 = r1.c
            java.lang.Object r4 = r1.b
            cmj r6 = new cmj
            dhd r4 = (defpackage.dhd) r4
            r6.<init>((defpackage.dhd) r4, (defpackage.dey) r3, (java.util.Map) r0, (int) r5)
            java.lang.Object r0 = r1.a
            hme r0 = r2.a(r6, r0)
            return r0
        L_0x0202:
            java.lang.Throwable r0 = (java.lang.Throwable) r0
            java.lang.Throwable r0 = defpackage.cqh.am(r0)
            boolean r2 = r0 instanceof java.util.concurrent.CancellationException
            if (r2 != 0) goto L_0x0222
            java.lang.Object r2 = r1.c
            dbs r3 = new dbs
            r3.<init>(r8)
            dbw r2 = (defpackage.dbw) r2
            java.lang.Object r2 = r2.j
            cxk r2 = (defpackage.cxk) r2
            r2.a(r3)
            r0.getMessage()
            defpackage.dcq.a()
        L_0x0222:
            java.lang.Object r0 = r1.a
            return r0
        L_0x0225:
            grh r0 = (defpackage.grh) r0
            boolean r2 = r0.f()
            if (r2 == 0) goto L_0x024e
            java.lang.Object r2 = r1.a
            java.lang.Object r3 = r1.c
            java.lang.Object r4 = r1.b
            java.lang.String r5 = "%s: Cancel download file %s"
            java.lang.String r6 = "MddFileDownloader"
            defpackage.cyh.d(r5, r6, r3)
            java.lang.Object r0 = r0.b()
            hme r0 = (defpackage.hme) r0
            r0.cancel(r9)
            cxx r4 = (defpackage.cxx) r4
            android.net.Uri r3 = (android.net.Uri) r3
            java.lang.String r2 = (java.lang.String) r2
            hme r0 = r4.b(r2, r3)
            goto L_0x0257
        L_0x024e:
            java.lang.String r0 = "%s: stopDownloading on non-existent download"
            java.lang.String r2 = "MddFileDownloader"
            defpackage.cyh.m(r0, r2)
            hme r0 = defpackage.hma.a
        L_0x0257:
            return r0
        L_0x0258:
            ctl r0 = (defpackage.ctl) r0
            java.lang.Object r2 = r0.C(r6)
            htk r2 = (defpackage.htk) r2
            r2.x(r0)
            htq r3 = r2.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x026e
            r2.u()
        L_0x026e:
            java.lang.Object r3 = r1.c
            htq r4 = r2.b
            ctl r4 = (defpackage.ctl) r4
            ctl r5 = defpackage.ctl.h
            ctf r3 = (defpackage.ctf) r3
            int r5 = r3.h
            r4.c = r5
            int r5 = r4.a
            r5 = r5 | r8
            r4.a = r5
            ctf r4 = defpackage.ctf.CORRUPTED
            boolean r3 = r3.equals(r4)
            if (r3 == 0) goto L_0x02a3
            int r0 = r0.g
            int r0 = r0 + r9
            htq r3 = r2.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x0297
            r2.u()
        L_0x0297:
            htq r3 = r2.b
            ctl r3 = (defpackage.ctl) r3
            int r4 = r3.a
            r4 = r4 | 32
            r3.a = r4
            r3.g = r0
        L_0x02a3:
            java.lang.Object r0 = r1.b
            java.lang.Object r3 = r1.a
            htq r2 = r2.r()
            ctl r2 = (defpackage.ctl) r2
            ctj r0 = (defpackage.ctj) r0
            hme r0 = r3.h(r0, r2)
            return r0
        L_0x02b4:
            csi r0 = (defpackage.csi) r0
            csh r0 = r0.a
            java.lang.String r2 = "%s: reVerifyFile lost or corrupted code %s"
            java.lang.String r3 = "SharedFileManager"
            defpackage.cyh.h(r2, r3, r0)
            java.lang.Object r0 = r1.b
            htq r0 = (defpackage.htq) r0
            java.lang.Object r2 = r0.C(r6)
            htk r2 = (defpackage.htk) r2
            r2.x(r0)
            ctf r0 = defpackage.ctf.CORRUPTED
            htq r3 = r2.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x02d9
            r2.u()
        L_0x02d9:
            java.lang.Object r3 = r1.c
            java.lang.Object r4 = r1.a
            htq r5 = r2.b
            ctl r5 = (defpackage.ctl) r5
            ctl r6 = defpackage.ctl.h
            int r0 = r0.h
            r5.c = r0
            int r0 = r5.a
            r0 = r0 | r8
            r5.a = r0
            htq r0 = r2.r()
            ctl r0 = (defpackage.ctl) r0
            dbw r4 = (defpackage.dbw) r4
            java.lang.Object r2 = r4.j
            ctj r3 = (defpackage.ctj) r3
            hme r0 = r2.h(r3, r0)
            czw r0 = defpackage.czw.e(r0)
            bub r2 = new bub
            r3 = 11
            r2.<init>(r3)
            java.lang.Object r3 = r4.l
            czw r0 = r0.g(r2, r3)
            return r0
        L_0x030e:
            android.net.Uri r0 = (android.net.Uri) r0
            if (r0 == 0) goto L_0x0348
            java.lang.Object r2 = r1.b
            java.lang.Object r3 = r1.a
            ctl r2 = (defpackage.ctl) r2
            boolean r2 = r2.d
            if (r2 == 0) goto L_0x0336
            dbw r3 = (defpackage.dbw) r3
            java.lang.Object r2 = r3.c
            kjd r2 = (defpackage.kjd) r2
            boolean r0 = r2.j(r0)
            if (r0 == 0) goto L_0x0329
            goto L_0x0345
        L_0x0329:
            kml r0 = defpackage.csi.a()
            csh r2 = defpackage.csh.DOWNLOADED_FILE_NOT_FOUND_ERROR
            r0.b = r2
            csi r0 = r0.a()
            throw r0
        L_0x0336:
            java.lang.Object r2 = r1.c
            dbw r3 = (defpackage.dbw) r3
            java.lang.Object r3 = r3.c
            csv r2 = (defpackage.csv) r2
            java.lang.String r4 = r2.f
            kjd r3 = (defpackage.kjd) r3
            defpackage.cxt.c(r3, r2, r0, r4)
        L_0x0345:
            hme r0 = defpackage.hma.a
            return r0
        L_0x0348:
            kml r0 = defpackage.csi.a()
            csh r2 = defpackage.csh.DOWNLOADED_FILE_NOT_FOUND_ERROR
            r0.b = r2
            csi r0 = r0.a()
            throw r0
        L_0x0355:
            android.net.Uri r0 = (android.net.Uri) r0
            java.lang.Object r0 = r1.a
            ctf r2 = defpackage.ctf.DOWNLOAD_IN_PROGRESS
            htk r0 = (defpackage.htk) r0
            htq r3 = r0.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x0368
            r0.u()
        L_0x0368:
            java.lang.Object r3 = r1.c
            java.lang.Object r4 = r1.b
            htq r5 = r0.b
            ctl r5 = (defpackage.ctl) r5
            ctl r6 = defpackage.ctl.h
            int r2 = r2.h
            r5.c = r2
            int r2 = r5.a
            r2 = r2 | r8
            r5.a = r2
            htq r0 = r0.r()
            ctl r0 = (defpackage.ctl) r0
            dbw r4 = (defpackage.dbw) r4
            java.lang.Object r2 = r4.j
            ctj r3 = (defpackage.ctj) r3
            hme r0 = r2.h(r3, r0)
            return r0
        L_0x038c:
            ctl r0 = (defpackage.ctl) r0
            int r2 = r0.c
            ctf r2 = defpackage.ctf.b(r2)
            if (r2 != 0) goto L_0x0398
            ctf r2 = defpackage.ctf.NONE
        L_0x0398:
            ctf r4 = defpackage.ctf.DOWNLOAD_COMPLETE
            if (r2 == r4) goto L_0x039f
            hme r0 = defpackage.hma.a
            goto L_0x03ce
        L_0x039f:
            java.lang.Object r2 = r1.c
            java.lang.Object r4 = r1.b
            java.lang.Object r6 = r1.a
            dbw r6 = (defpackage.dbw) r6
            r7 = r4
            ctj r7 = (defpackage.ctj) r7
            hme r7 = r6.l(r7)
            czw r7 = defpackage.czw.e(r7)
            cwt r8 = new cwt
            htq r2 = (defpackage.htq) r2
            r8.<init>((defpackage.dbw) r6, (defpackage.htq) r0, (defpackage.htq) r2, (int) r3)
            java.lang.Object r2 = r6.l
            czw r2 = r7.g(r8, r2)
            cwt r3 = new cwt
            htq r4 = (defpackage.htq) r4
            r3.<init>((defpackage.dbw) r6, (defpackage.htq) r0, (defpackage.htq) r4, (int) r5)
            java.lang.Object r0 = r6.l
            java.lang.Class<csi> r4 = defpackage.csi.class
            czw r0 = r2.d(r4, r3, r0)
        L_0x03ce:
            return r0
        L_0x03cf:
            java.lang.Object r2 = r1.b
            java.lang.String r0 = (java.lang.String) r0
            ctj r2 = (defpackage.ctj) r2
            int r2 = r2.e
            int r2 = defpackage.a.x(r2)
            if (r2 != 0) goto L_0x03de
            goto L_0x03df
        L_0x03de:
            r9 = r2
        L_0x03df:
            java.lang.Object r2 = r1.c
            java.lang.Object r3 = r1.a
            csv r2 = (defpackage.csv) r2
            java.lang.String r2 = r2.f
            dbw r3 = (defpackage.dbw) r3
            hme r0 = r3.r(r9, r0, r2)
            return r0
        L_0x03ee:
            java.lang.String r6 = "com.google.recognition.extra.RETURN_OFFLINE_LANGUAGE_DETAILS"
            boolean r6 = r5.getBooleanExtra(r6, r7)
            if (r6 != 0) goto L_0x03f9
            java.lang.String r6 = "Offline not specified"
            goto L_0x040b
        L_0x03f9:
            if (r10 == 0) goto L_0x0406
            gyo r6 = r0.e
            boolean r6 = r6.contains(r10)
            if (r6 == 0) goto L_0x0406
            java.lang.String r6 = ""
            goto L_0x040b
        L_0x0406:
            java.lang.String r6 = "Offline language info not supported"
            goto L_0x040b
        L_0x0409:
            java.lang.String r6 = "Wrong action"
        L_0x040b:
            java.lang.Object r11 = r1.a
            boolean r12 = r6.isEmpty()
            if (r12 != 0) goto L_0x0451
            hca r3 = defpackage.fbb.a
            hco r3 = r3.g()
            hby r3 = (defpackage.hby) r3
            java.lang.String r7 = "LanguagePackDetailsReceiver.java"
            java.lang.String r8 = "com/google/android/libraries/speech/modelmanager/languagepack/legacy/LanguagePackDetailsReceiver"
            java.lang.String r9 = "onReceive"
            r10 = 127(0x7f, float:1.78E-43)
            hco r3 = r3.j(r8, r9, r10, r7)
            hby r3 = (defpackage.hby) r3
            java.lang.String r7 = "Error processing the broadcast: %s"
            r3.u(r7, r6)
            dwj r3 = r0.c
            dxl r7 = defpackage.dwv.ak
            r8 = 10
            dxh r7 = r7.c(r8)
            java.lang.String r8 = "timestamp"
            r7.d(r8, r4)
            r3.a(r7)
            ghj r11 = (defpackage.ghj) r11
            int r3 = r11.a
            android.os.Bundle r4 = r11.c
            ghj r7 = new ghj
            r7.<init>(r3, r6, r4)
            hme r3 = defpackage.hfc.K(r7)
            goto L_0x04cf
        L_0x0451:
            hca r6 = defpackage.fbb.a
            hco r6 = r6.f()
            hby r6 = (defpackage.hby) r6
            java.lang.String r12 = "LanguagePackDetailsReceiver.java"
            java.lang.String r13 = "com/google/android/libraries/speech/modelmanager/languagepack/legacy/LanguagePackDetailsReceiver"
            java.lang.String r14 = "onReceive"
            r15 = 135(0x87, float:1.89E-43)
            hco r6 = r6.j(r13, r14, r15, r12)
            hby r6 = (defpackage.hby) r6
            java.lang.String r12 = "Fetching language pack details"
            r6.r(r12)
            defpackage.fvf.aP(r10)
            bqo r6 = r0.f
            iai r12 = defpackage.iai.AMBIENT_CONTINUOUS
            huv r6 = r6.a
            boolean r13 = r6.containsKey(r10)
            if (r13 == 0) goto L_0x048a
            hua r12 = defpackage.bqo.c
            java.lang.Object r6 = r6.get(r10)
            java.lang.Integer r6 = (java.lang.Integer) r6
            java.lang.Object r6 = r12.a(r6)
            r12 = r6
            iai r12 = (defpackage.iai) r12
        L_0x048a:
            ezv r6 = r0.b
            grh r10 = defpackage.grh.h(r12)
            hau r13 = defpackage.hau.a
            j$.util.Optional r14 = j$.util.Optional.empty()
            hme r6 = r6.c(r10, r13, r14)
            ezv r10 = r0.b
            j$.util.Optional r13 = j$.util.Optional.empty()
            hme r13 = r10.b(r13)
            ezv r10 = r0.b
            hau r14 = defpackage.hau.a
            j$.util.Optional r15 = j$.util.Optional.empty()
            hme r14 = r10.a(r12, r14, r15)
            hme[] r3 = new defpackage.hme[r3]
            r3[r7] = r6
            r3[r9] = r13
            r3[r8] = r14
            bzl r3 = defpackage.ftd.ad(r3)
            faz r7 = new faz
            r15 = r11
            ghj r15 = (defpackage.ghj) r15
            r10 = r7
            r11 = r0
            r12 = r6
            r16 = r4
            r10.<init>(r11, r12, r13, r14, r15, r16)
            hmh r4 = r0.d
            hme r3 = r3.z(r7, r4)
        L_0x04cf:
            hme r0 = r2.e(r3, r0, r5)
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cwt.a(java.lang.Object):hme");
    }

    public /* synthetic */ cwt(dbw dbw, htq htq, htq htq2, int i) {
        this.d = i;
        this.a = dbw;
        this.b = htq;
        this.c = htq2;
    }

    public /* synthetic */ cwt(Object obj, Object obj2, Object obj3, int i) {
        this.d = i;
        this.b = obj;
        this.c = obj2;
        this.a = obj3;
    }

    public /* synthetic */ cwt(Object obj, Object obj2, Object obj3, int i, byte[] bArr) {
        this.d = i;
        this.c = obj;
        this.b = obj2;
        this.a = obj3;
    }

    public /* synthetic */ cwt(Object obj, Object obj2, Object obj3, int i, char[] cArr) {
        this.d = i;
        this.b = obj;
        this.a = obj2;
        this.c = obj3;
    }

    public /* synthetic */ cwt(Object obj, Object obj2, Object obj3, int i, short[] sArr) {
        this.d = i;
        this.a = obj;
        this.c = obj2;
        this.b = obj3;
    }
}
