package defpackage;

/* renamed from: eng  reason: default package */
/* compiled from: PG */
public final class eng implements enr {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/microphone/impl/audiosourcedataaccessor/AudioAdapterAudioSourceDataAccessor");
    public final ehg b;
    public final int c;
    public final ech d;
    public final grh e;
    public final dyt f;
    public final jqh g = new jqh();
    public final jqh h;
    public final eck i;
    public final dlv j;
    private final grh k;
    private final jqs l;
    private final emt m;
    private final jjo n = new jjw(new mq(this, 18));
    private final jjo o;
    private final jjo p;
    private final jjo q;
    private final jjo r;
    private final hme s;
    private final eej t;
    private final eoz u;

    public eng(ehg ehg, grh grh, int i2, eck eck, ech ech, grh grh2, dlv dlv, jqs jqs, emt emt, bzj bzj) {
        eel eel;
        this.b = ehg;
        this.k = grh;
        this.c = i2;
        this.i = eck;
        this.d = ech;
        this.e = grh2;
        this.j = dlv;
        this.l = jqs;
        this.m = emt;
        this.u = don.k(jqs);
        jqh jqh = new jqh();
        this.h = jqh;
        this.o = new jjw(new bes(this, bzj, 7, (byte[]) null));
        this.p = new jjw(new mq(this, 16));
        this.q = new jjw(new mq(this, 15));
        jjw jjw = new jjw(new mq(this, 17));
        this.r = jjw;
        this.s = jqw.w(jqh);
        grh grh3 = (grh) jjw.a();
        eci eci = ech.b;
        dyt dyt = (eci == null ? eci.e : eci).c;
        dyt = dyt == null ? dyt.l : dyt;
        jnu.d(dyt, "getAudioLibInputParams(...)");
        this.f = dyt;
        int ordinal = eck.a.ordinal();
        if (ordinal == 0) {
            eel = eel.SOURCE_BISTO;
        } else if (ordinal == 1) {
            eel = eel.SOURCE_SODA;
        } else if (ordinal == 2) {
            eel = eel.SOURCE_CAR;
        } else if (ordinal == 3) {
            eel = eel.SOURCE_GACS;
        } else if (ordinal == 4) {
            eel = eel.SOURCE_EMPTY;
        } else {
            throw new jjq();
        }
        this.t = dom.m(eel);
    }

    private final grh q() {
        return (grh) this.p.a();
    }

    public final int a() {
        evf a2;
        p(eaj.FAILED_TO_OPEN_AUDIO_SOURCE);
        Object m2 = m();
        jji.c(m2);
        evg evg = (evg) ((grh) m2).e();
        if (evg != null && (a2 = evg.a()) != null) {
            return a2.b();
        }
        throw new IllegalStateException("Source was expected to be opened by this time");
    }

    public final dyt b() {
        return this.f;
    }

    public final dyw c(eal eal, enk enk) {
        evf a2;
        ecn ecn;
        ecu ecu;
        dxx dxx;
        grh grh;
        Object m2 = m();
        jji.c(m2);
        evg evg = (evg) ((grh) m2).e();
        if (evg == null || (a2 = evg.a()) == null) {
            throw new IllegalStateException("Source was expected to be opened by this time");
        }
        if (a2 instanceof ecn) {
            ecn = (ecn) a2;
        } else {
            ecn = null;
        }
        if (a2 instanceof ecu) {
            ecu = (ecu) a2;
        } else {
            ecu = null;
        }
        if (ecn != null) {
            dxx = doe.h(ecn, enk.a());
        } else {
            dxx = null;
        }
        grh g2 = grh.g(dxx);
        grh g3 = doe.g(ecu, enk.a());
        grh g4 = grh.g((Object) null);
        htk l2 = eak.c.l();
        jnu.d(l2, "newBuilder(...)");
        dlv e2 = jnu.e(l2, "builder");
        e2.g(eal);
        eak e3 = e2.e();
        if (eal == eal.OPENED_SHARED) {
            grh = dom.n(q()).a(new ejr(wl.q, 3));
        } else {
            grh = q().a(new ejr(wl.r, 4));
        }
        return ejw.c(g2, g3, g4, e3, grh, (hme) dom.o((grh) this.q.a(), enk.a(), this.f).a());
    }

    public final eej d() {
        return this.t;
    }

    public final /* synthetic */ ekt e() {
        return null;
    }

    public final eqw g() {
        return (eqw) this.o.a();
    }

    public final hme h(eam eam) {
        jnu.e(eam, "stopListeningReason");
        return this.u.o(new ene(this, eam, (jlr) null, 1));
    }

    public final hme i(eal eal, enk enk) {
        jnu.e(eal, "success");
        return this.u.o(new enm(this, enk, eal, (jlr) null, 1));
    }

    public final hme j() {
        return this.s;
    }

    public final hme k(enk enk) {
        return this.u.o(new ene(this, enk, (jlr) null, 0));
    }

    /* JADX WARNING: Removed duplicated region for block: B:12:0x0030  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object l(defpackage.jlr r7) {
        /*
            r6 = this;
            boolean r0 = r7 instanceof defpackage.end
            if (r0 == 0) goto L_0x0013
            r0 = r7
            end r0 = (defpackage.end) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            end r0 = new end
            r0.<init>(r6, r7)
        L_0x0018:
            java.lang.Object r7 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            r3 = 1
            if (r2 == 0) goto L_0x0030
            if (r2 != r3) goto L_0x0028
            defpackage.jji.c(r7)
            goto L_0x00ab
        L_0x0028:
            java.lang.IllegalStateException r7 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r7.<init>(r0)
            throw r7
        L_0x0030:
            defpackage.jji.c(r7)
            java.lang.Object r7 = r6.m()
            defpackage.jji.c(r7)
            grh r7 = (defpackage.grh) r7
            java.lang.Object r7 = r7.e()
            evg r7 = (defpackage.evg) r7
            if (r7 != 0) goto L_0x004b
            eag r7 = defpackage.eag.FAILED_CLOSING_ABSENT_WRITEABLE_AUDIO_BUFFER_DATA
            eah r7 = defpackage.ejw.g(r7)
            return r7
        L_0x004b:
            ecj r2 = defpackage.ecj.c
            htk r2 = r2.l()
            java.lang.String r4 = "newBuilder(...)"
            defpackage.jnu.d(r2, r4)
            ech r4 = r6.d
            eci r4 = r4.b
            if (r4 != 0) goto L_0x005e
            eci r4 = defpackage.eci.e
        L_0x005e:
            java.lang.String r5 = "getStartListeningParams(...)"
            defpackage.jnu.d(r4, r5)
            java.lang.String r5 = "<this>"
            defpackage.jnu.e(r4, r5)
            int r5 = r4.a
            r5 = r5 & 4
            if (r5 == 0) goto L_0x0075
            ead r4 = r4.d
            if (r4 != 0) goto L_0x0076
            ead r4 = defpackage.ead.c
            goto L_0x0076
        L_0x0075:
            r4 = 0
        L_0x0076:
            if (r4 == 0) goto L_0x008f
            htq r5 = r2.b
            boolean r5 = r5.B()
            if (r5 != 0) goto L_0x0083
            r2.u()
        L_0x0083:
            htq r5 = r2.b
            ecj r5 = (defpackage.ecj) r5
            r5.b = r4
            int r4 = r5.a
            r4 = r4 | 2
            r5.a = r4
        L_0x008f:
            evf r7 = r7.a()
            r7.f()
            eck r7 = r6.i
            htq r2 = r2.r()
            ecj r2 = (defpackage.ecj) r2
            hme r7 = r7.b(r2)
            r0.c = r3
            java.lang.Object r7 = defpackage.jqw.x(r7, r0)
            if (r7 != r1) goto L_0x00ab
            return r1
        L_0x00ab:
            java.lang.String r0 = "await(...)"
            defpackage.jnu.d(r7, r0)
            return r7
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eng.l(jlr):java.lang.Object");
    }

    public final Object m() {
        return ((jju) this.n.a()).a;
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v1, resolved type: enk} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v22, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v23, resolved type: enk} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v24, resolved type: enk} */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:12:0x0034  */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x00ec  */
    /* JADX WARNING: Removed duplicated region for block: B:37:0x0106  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object n(defpackage.enk r8, defpackage.jlr r9) {
        /*
            r7 = this;
            boolean r0 = r9 instanceof defpackage.enf
            if (r0 == 0) goto L_0x0013
            r0 = r9
            enf r0 = (defpackage.enf) r0
            int r1 = r0.d
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.d = r1
            goto L_0x0018
        L_0x0013:
            enf r0 = new enf
            r0.<init>(r7, r9)
        L_0x0018:
            java.lang.Object r9 = r0.b
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.d
            r3 = 1
            if (r2 == 0) goto L_0x0034
            if (r2 != r3) goto L_0x002c
            java.lang.Object r8 = r0.a
            eng r0 = r0.e
            defpackage.jji.c(r9)
            goto L_0x00e5
        L_0x002c:
            java.lang.IllegalStateException r8 = new java.lang.IllegalStateException
            java.lang.String r9 = "call to 'resume' before 'invoke' with coroutine"
            r8.<init>(r9)
            throw r8
        L_0x0034:
            defpackage.jji.c(r9)
            hca r9 = a
            hco r9 = r9.f()
            hcr r2 = defpackage.hdg.a
            java.lang.String r4 = "ALT.AdapterSrcAccessor"
            hco r9 = r9.h(r2, r4)
            java.lang.String r2 = "startListeningInternal"
            r4 = 166(0xa6, float:2.33E-43)
            java.lang.String r5 = "com/google/android/libraries/search/audio/microphone/impl/audiosourcedataaccessor/AudioAdapterAudioSourceDataAccessor"
            java.lang.String r6 = "AudioAdapterAudioSourceDataAccessor.kt"
            hco r9 = r9.j(r5, r2, r4, r6)
            hby r9 = (defpackage.hby) r9
            java.lang.String r2 = r7.o()
            int r4 = r7.c
            java.lang.Integer r5 = new java.lang.Integer
            r5.<init>(r4)
            java.lang.String r4 = "#audio# %s session(%d) opening source, offset(%s)"
            r9.G(r4, r2, r5, r8)
            emt r9 = r7.m
            boolean r9 = r9.c()
            if (r9 != 0) goto L_0x00ae
            emt r8 = r7.m
            android.content.Context r9 = r8.b
            java.lang.String r0 = "appops"
            java.lang.Object r9 = r9.getSystemService(r0)
            r0 = r9
            android.app.AppOpsManager r0 = (android.app.AppOpsManager) r0
            if (r0 != 0) goto L_0x0096
            hca r8 = defpackage.emt.a
            hco r8 = r8.h()
            hby r8 = (defpackage.hby) r8
            java.lang.String r9 = "showDialog"
            r0 = 58
            java.lang.String r1 = "com/google/android/libraries/search/audio/microphone/accountability/RecordAudioOpChecker"
            java.lang.String r2 = "RecordAudioOpChecker.java"
            hco r8 = r8.j(r1, r9, r0, r2)
            hby r8 = (defpackage.hby) r8
            java.lang.String r9 = "#audio# Unable to get access to app ops manager"
            r8.r(r9)
            goto L_0x00a7
        L_0x0096:
            android.content.Context r8 = r8.b
            int r2 = android.os.Process.myUid()
            java.lang.String r3 = r8.getPackageName()
            r4 = 0
            r5 = 0
            java.lang.String r1 = "android:record_audio"
            int unused = r0.noteOp(r1, r2, r3, r4, r5)
        L_0x00a7:
            eaj r8 = defpackage.eaj.FAILED_OPENING_OP_NOT_ALLOWED
            dyw r8 = defpackage.ejw.a(r8)
            return r8
        L_0x00ae:
            eqw r9 = r7.g()
            if (r9 == 0) goto L_0x00b7
            r9.b()
        L_0x00b7:
            java.lang.Object r9 = r7.m()
            defpackage.jji.c(r9)
            grh r9 = (defpackage.grh) r9
            java.lang.Object r9 = r9.e()
            evg r9 = (defpackage.evg) r9
            if (r9 != 0) goto L_0x00cf
            eaj r8 = defpackage.eaj.FAILED_OPENING_ABSENT_WRITEABLE_AUDIO_BUFFER_DATA
            dyw r8 = defpackage.ejw.a(r8)
            return r8
        L_0x00cf:
            hme r9 = r9.b()
            java.lang.String r2 = "audioSourceOpeningStatus(...)"
            defpackage.jnu.d(r9, r2)
            r0.e = r7
            r0.a = r8
            r0.d = r3
            java.lang.Object r9 = defpackage.jqw.x(r9, r0)
            if (r9 == r1) goto L_0x0137
            r0 = r7
        L_0x00e5:
            eak r9 = (defpackage.eak) r9
            int r1 = r9.a
            r2 = 2
            if (r1 != r2) goto L_0x0106
            java.lang.Object r8 = r9.b
            java.lang.Integer r8 = (java.lang.Integer) r8
            int r8 = r8.intValue()
            eaj r8 = defpackage.eaj.b(r8)
            if (r8 != 0) goto L_0x00fc
            eaj r8 = defpackage.eaj.UNKNOWN_OPENING_FAILURE
        L_0x00fc:
            java.lang.String r9 = "getFailure(...)"
            defpackage.jnu.d(r8, r9)
            dyw r8 = defpackage.ejw.a(r8)
            return r8
        L_0x0106:
            eal r9 = defpackage.eal.OPENED
            dyw r8 = r0.c(r9, r8)
            grh r9 = r0.k
            boolean r9 = r9.f()
            if (r9 == 0) goto L_0x0136
            emt r9 = r0.m
            hme r1 = defpackage.hfc.K(r8)
            hme r2 = r0.s
            int r3 = r0.c
            ech r0 = r0.d
            int r0 = r0.c
            ewn r0 = defpackage.ewn.b(r0)
            if (r0 != 0) goto L_0x012a
            ewn r0 = defpackage.ewn.TAG_DO_NOT_USE
        L_0x012a:
            java.lang.String r4 = "getAttributionId(...)"
            defpackage.jnu.d(r0, r4)
            java.lang.String r0 = defpackage.fbi.k(r0)
            r9.a(r1, r2, r3, r0)
        L_0x0136:
            return r8
        L_0x0137:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eng.n(enk, jlr):java.lang.Object");
    }

    public final String o() {
        if (true != this.k.f()) {
            return "hotword";
        }
        return "audio request";
    }

    public final void p(eaj eaj) {
        Object obj;
        try {
            obj = (eak) this.g.A();
        } catch (Throwable th) {
            obj = jji.b(th);
        }
        if (true == (obj instanceof jjt)) {
            obj = null;
        }
        eak eak = (eak) obj;
        if (eak == null || eak.a != 1) {
            throw new elc(ejw.h(eaj));
        }
    }

    public final /* synthetic */ enr f() {
        return this;
    }
}
