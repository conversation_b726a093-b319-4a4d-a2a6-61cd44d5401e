package defpackage;

import android.app.Activity;
import android.media.audiofx.AcousticEchoCanceler;
import android.media.audiofx.NoiseSuppressor;
import com.android.car.ui.core.CarUiInstaller;
import j$.util.Collection;
import j$.util.function.Function$CC;
import java.util.Locale;
import java.util.Map;
import java.util.function.Function;

/* renamed from: bpf  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bpf implements Function {
    private final /* synthetic */ int a;

    public /* synthetic */ bpf(int i) {
        this.a = i;
    }

    public final /* synthetic */ Function andThen(Function function) {
        switch (this.a) {
            case 0:
                return Function$CC.$default$andThen(this, function);
            case 1:
                return Function$CC.$default$andThen(this, function);
            case 2:
                return Function$CC.$default$andThen(this, function);
            case 3:
                return Function$CC.$default$andThen(this, function);
            case 4:
                return Function$CC.$default$andThen(this, function);
            case 5:
                return Function$CC.$default$andThen(this, function);
            case 6:
                return Function$CC.$default$andThen(this, function);
            case 7:
                return Function$CC.$default$andThen(this, function);
            case 8:
                return Function$CC.$default$andThen(this, function);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return Function$CC.$default$andThen(this, function);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return Function$CC.$default$andThen(this, function);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return Function$CC.$default$andThen(this, function);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return Function$CC.$default$andThen(this, function);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return Function$CC.$default$andThen(this, function);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return Function$CC.$default$andThen(this, function);
            case 15:
                return Function$CC.$default$andThen(this, function);
            case 16:
                return Function$CC.$default$andThen(this, function);
            case 17:
                return Function$CC.$default$andThen(this, function);
            case 18:
                return Function$CC.$default$andThen(this, function);
            case 19:
                return Function$CC.$default$andThen(this, function);
            default:
                return Function$CC.$default$andThen(this, function);
        }
    }

    public final Object apply(Object obj) {
        switch (this.a) {
            case 0:
                bqh bqh = (bqh) obj;
                return String.format(Locale.getDefault(), "(%d, %d)", new Object[]{Integer.valueOf(bqh.a), Integer.valueOf(bqh.b)});
            case 1:
                int i = CarUiInstaller.a;
                if (obj instanceof Activity) {
                    return Activity.class;
                }
                return obj.getClass();
            case 2:
                return bzw.a((bsk) obj);
            case 3:
                return ((ifd) obj).c;
            case 4:
                return Collection.EL.stream(((bts) obj).b);
            case 5:
                return bzw.a((bsk) obj);
            case 6:
                return ((bty) obj).b;
            case 7:
                String str = (String) obj;
                if (str.equals("ar-xa")) {
                    return "ar";
                }
                return str;
            case 8:
                return ((iul) obj).d;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return ((brt) obj).d();
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                exo exo = (exo) obj;
                hdf hdf = byr.a;
                return exo;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                hdf hdf2 = byr.a;
                return ((byn) obj).a.a;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                byn byn = (byn) obj;
                hdf hdf3 = byr.a;
                return byn;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return ((exo) obj).a;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return NoiseSuppressor.create(((Integer) obj).intValue());
            case 15:
                return AcousticEchoCanceler.create(((Integer) obj).intValue());
            case 16:
                return ((exo) obj).b();
            case 17:
                hdf hdf4 = exy.a;
                return (exo) ((grh) ((Map.Entry) obj).getValue()).b();
            case 18:
                return ((exz) obj).d();
            case 19:
                return ((exz) obj).c();
            default:
                return ((exz) obj).f();
        }
    }

    public final /* synthetic */ Function compose(Function function) {
        switch (this.a) {
            case 0:
                return Function$CC.$default$compose(this, function);
            case 1:
                return Function$CC.$default$compose(this, function);
            case 2:
                return Function$CC.$default$compose(this, function);
            case 3:
                return Function$CC.$default$compose(this, function);
            case 4:
                return Function$CC.$default$compose(this, function);
            case 5:
                return Function$CC.$default$compose(this, function);
            case 6:
                return Function$CC.$default$compose(this, function);
            case 7:
                return Function$CC.$default$compose(this, function);
            case 8:
                return Function$CC.$default$compose(this, function);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return Function$CC.$default$compose(this, function);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return Function$CC.$default$compose(this, function);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return Function$CC.$default$compose(this, function);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return Function$CC.$default$compose(this, function);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return Function$CC.$default$compose(this, function);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return Function$CC.$default$compose(this, function);
            case 15:
                return Function$CC.$default$compose(this, function);
            case 16:
                return Function$CC.$default$compose(this, function);
            case 17:
                return Function$CC.$default$compose(this, function);
            case 18:
                return Function$CC.$default$compose(this, function);
            case 19:
                return Function$CC.$default$compose(this, function);
            default:
                return Function$CC.$default$compose(this, function);
        }
    }
}
