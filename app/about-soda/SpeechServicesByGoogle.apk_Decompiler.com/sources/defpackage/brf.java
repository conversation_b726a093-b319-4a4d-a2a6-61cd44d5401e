package defpackage;

import android.content.Context;
import androidx.wear.ambient.AmbientModeSupport;
import com.google.android.apps.speech.tts.googletts.GoogleTTSRoot_Application;

/* renamed from: brf  reason: default package */
/* compiled from: PG */
public class brf extends bwb {
    private boolean g = false;
    private final iie h = new iie(new AmbientModeSupport.AmbientController(this));

    public final iie a() {
        return this.h;
    }

    public void onCreate() {
        if (!this.g) {
            this.g = true;
            GoogleTTSRoot_Application googleTTSRoot_Application = (GoogleTTSRoot_Application) this;
            brc brc = (brc) bo();
            googleTTSRoot_Application.e = (gnk) brc.g.b();
            googleTTSRoot_Application.f = brc.o;
            googleTTSRoot_Application.a = (Context) brc.p.b();
            googleTTSRoot_Application.c = (bwm) brc.q.b();
            iit.a(brc.r);
            googleTTSRoot_Application.d = (bxc) brc.s.b();
            googleTTSRoot_Application.b = (bwn) brc.t.b();
            iit.a(brc.u);
            iit.a(brc.v);
        }
        super.onCreate();
    }
}
