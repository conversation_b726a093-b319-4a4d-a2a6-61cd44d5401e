package defpackage;

import android.content.Context;
import java.util.concurrent.ScheduledExecutorService;

/* renamed from: brn  reason: default package */
/* compiled from: PG */
public final class brn implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;

    public brn(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4) {
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
    }

    /* renamed from: a */
    public final brm b() {
        buf.a().intValue();
        return new brm((Context) this.a.b(), (fzt) this.b.b(), (ScheduledExecutorService) this.c.b(), iit.c(this.d));
    }
}
