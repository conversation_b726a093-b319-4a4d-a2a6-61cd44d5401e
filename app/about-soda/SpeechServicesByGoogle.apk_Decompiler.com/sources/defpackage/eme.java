package defpackage;

/* renamed from: eme  reason: default package */
/* compiled from: PG */
public final class eme implements iiu {
    private final jjk a;
    private final jjk b;

    public eme(jjk jjk, jjk jjk2) {
        this.a = jjk;
        this.b = jjk2;
    }

    /* renamed from: a */
    public final cyw b() {
        grh grh = (grh) ((iiv) this.a).a;
        cyw cyw = (cyw) this.b.b();
        jnu.e(grh, "audioResourcesLoggerOverride");
        jnu.e(cyw, "defaultAudioResourcesLogger");
        return (cyw) grh.d(cyw);
    }
}
