package androidx.preference;

import android.content.Context;
import android.content.res.TypedArray;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.AttributeSet;
import com.google.android.tts.R;

/* compiled from: PG */
public class EditTextPreference extends DialogPreference {
    private asi mOnBindEditTextListener;
    private String mText;

    public EditTextPreference(Context context) {
        this(context, (AttributeSet) null);
    }

    public asi getOnBindEditTextListener() {
        return this.mOnBindEditTextListener;
    }

    public String getText() {
        return this.mText;
    }

    /* access modifiers changed from: protected */
    public Object onGetDefaultValue(TypedArray typedArray, int i) {
        return typedArray.getString(i);
    }

    /* access modifiers changed from: protected */
    public void onRestoreInstanceState(Parcelable parcelable) {
        if (parcelable == null || !parcelable.getClass().equals(asj.class)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        asj asj = (asj) parcelable;
        super.onRestoreInstanceState(asj.getSuperState());
        setText(asj.a);
    }

    /* access modifiers changed from: protected */
    public Parcelable onSaveInstanceState() {
        Parcelable onSaveInstanceState = super.onSaveInstanceState();
        if (isPersistent()) {
            return onSaveInstanceState;
        }
        asj asj = new asj(onSaveInstanceState);
        asj.a = getText();
        return asj;
    }

    /* access modifiers changed from: protected */
    public void onSetInitialValue(Object obj) {
        setText(getPersistedString((String) obj));
    }

    public void setOnBindEditTextListener(asi asi) {
        this.mOnBindEditTextListener = asi;
    }

    public void setText(String str) {
        boolean shouldDisableDependents = shouldDisableDependents();
        this.mText = str;
        persistString(str);
        boolean shouldDisableDependents2 = shouldDisableDependents();
        if (shouldDisableDependents2 != shouldDisableDependents) {
            notifyDependencyChange(shouldDisableDependents2);
        }
        notifyChanged();
    }

    public boolean shouldDisableDependents() {
        if (TextUtils.isEmpty(this.mText) || super.shouldDisableDependents()) {
            return true;
        }
        return false;
    }

    public EditTextPreference(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, kq.h(context, R.attr.editTextPreferenceStyle, 16842898));
    }

    public EditTextPreference(Context context, AttributeSet attributeSet, int i) {
        this(context, attributeSet, i, 0);
    }

    public EditTextPreference(Context context, AttributeSet attributeSet, int i, int i2) {
        super(context, attributeSet, i, i2);
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, att.d, i, i2);
        if (kq.n(obtainStyledAttributes, 0, 0, false)) {
            if (asn.b == null) {
                asn.b = new asn(1);
            }
            setSummaryProvider(asn.b);
        }
        obtainStyledAttributes.recycle();
    }
}
