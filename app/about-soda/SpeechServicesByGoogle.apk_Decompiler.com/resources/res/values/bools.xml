<?xml version="1.0" encoding="utf-8"?>
<resources>
    <bool name="abc_action_bar_embed_tabs">true</bool>
    <bool name="car_ui_alert_dialog_force_dismiss_button">true</bool>
    <bool name="car_ui_clear_focus_area_history_when_rotating">true</bool>
    <bool name="car_ui_enable_focus_area_background_highlight">false</bool>
    <bool name="car_ui_enable_focus_area_foreground_highlight">false</bool>
    <bool name="car_ui_escrow_check_components_automatically">false</bool>
    <bool name="car_ui_focus_area_default_focus_overrides_history">false</bool>
    <bool name="car_ui_ime_wide_screen_aligned_left">true</bool>
    <bool name="car_ui_ime_wide_screen_allow_app_hide_content_area">true</bool>
    <bool name="car_ui_is_light_theme">false</bool>
    <bool name="car_ui_list_item_single_line_title">false</bool>
    <bool name="car_ui_omit_display_cut_out_insets">false</bool>
    <bool name="car_ui_preference_list_instant_change_callback">false</bool>
    <bool name="car_ui_preference_list_show_full_screen">true</bool>
    <bool name="car_ui_preference_show_chevron">false</bool>
    <bool name="car_ui_preference_two_action_switch_widget_focusable">false</bool>
    <bool name="car_ui_scrollbar_enable">true</bool>
    <bool name="car_ui_toolbar_logo_fills_nav_icon_space">true</bool>
    <bool name="car_ui_toolbar_menuitem_individual_click_listeners">false</bool>
    <bool name="car_ui_toolbar_nav_icon_reserve_space">true</bool>
    <bool name="car_ui_toolbar_show_logo">true</bool>
    <bool name="car_ui_toolbar_tab_flexible_layout">false</bool>
    <bool name="car_ui_toolbar_tabs_on_second_row">false</bool>
    <bool name="config_materialPreferenceIconSpaceReserved">false</bool>
    <bool name="enable_system_alarm_service_default">false</bool>
    <bool name="enable_system_foreground_service_default">true</bool>
    <bool name="enable_system_job_service_default">true</bool>
    <bool name="enable_tnt_components">false</bool>
    <bool name="gm_sys_typescale_body1_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_body2_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_button_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_caption_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_display1_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_display2_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_display3_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_headline1_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_headline2_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_headline3_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_headline4_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_headline5_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_headline6_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_overline_text_all_caps">true</bool>
    <bool name="gm_sys_typescale_subhead1_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_subhead2_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_subtitle1_text_all_caps">false</bool>
    <bool name="gm_sys_typescale_subtitle2_text_all_caps">false</bool>
    <bool name="settingslib_config_icon_space_reserved">true</bool>
    <bool name="workmanager_test_configuration">false</bool>
</resources>
