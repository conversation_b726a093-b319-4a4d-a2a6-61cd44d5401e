package defpackage;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Executor;

/* renamed from: esr  reason: default package */
/* compiled from: PG */
public final class esr {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/routing/registry/HotwordRoutesRegistry");
    private static final List e = jji.o(dzp.SODA_ROUTE, dzp.BLUETOOTH_AUDIO_ROUTE, dzp.HANDOVER_ROUTE, dzp.CAR_AUDIO_ROUTE);
    public final eoz b;
    public final cyw c;
    public final bzj d;
    private final eoz f;
    private final Executor g;
    private final hll h;
    private final jjo i = new jjw(new eoe(this, 6));
    private final dsy j;
    private final eoz k;

    public esr(dvx dvx, cyw cyw, eoz eoz, bzj bzj, dsy dsy, eoz eoz2, Executor executor, hll hll, eoz eoz3) {
        jnu.e(dvx, "listeningSessionAssembler");
        jnu.e(eoz, "tokenGenerator");
        jnu.e(executor, "lightweightExecutor");
        jnu.e(hll, "audioExecutionSequencer");
        this.c = cyw;
        this.b = eoz;
        this.d = bzj;
        this.j = dsy;
        this.f = eoz2;
        this.g = executor;
        this.h = hll;
        this.k = eoz3;
    }

    private final List e() {
        return (List) this.i.a();
    }

    private static final esq f(dzx dzx) {
        ekh ekh = new ekh(dzx);
        ejn a2 = ejn.a();
        htk l = eab.d.l();
        jnu.d(l, "newBuilder(...)");
        return new esq(ekh, -1, a2, jnu.e(l, "builder").z());
    }

    /* JADX WARNING: type inference failed for: r4v5, types: [elp, java.lang.Object] */
    /* JADX WARNING: Code restructure failed: missing block: B:48:0x01d4, code lost:
        r2 = r12.k.y(r2);
        ((defpackage.hby) r1.f().h(defpackage.hdg.a, "ALT.HotwordRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/HotwordRoutesRegistry", "startHotwordRouteConnection", 232, "HotwordRoutesRegistry.kt")).x("#audio# starting connection to hotword route(token(%d), %s)", r0, defpackage.fbi.r(r2.d.b));
        r1 = r12.h.b(defpackage.gof.c(new defpackage.ejj(r2, r12, 7, (byte[]) null)), r12.g);
        defpackage.jnu.d(r1, "submitAsync(...)");
        r3 = defpackage.eac.c.l();
        defpackage.jnu.d(r3, "newBuilder(...)");
        r3 = defpackage.jnu.e(r3, "builder");
        r4 = defpackage.ebw.c.l();
        defpackage.jnu.d(r4, "newBuilder(...)");
        r4 = defpackage.jnu.e(r4, "builder");
        r4.y(r0);
        r3.y(r4.x());
        r4 = new defpackage.esp(r2, r12.d.O(r3.x(), r2.d.a), r0, r13);
        r0 = r12.c;
        r13 = r13.c;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:49:0x0257, code lost:
        if (r13 != null) goto L_0x025b;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:50:0x0259, code lost:
        r13 = defpackage.ehg.c;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:51:0x025b, code lost:
        defpackage.jnu.d(r13, "getClientInfo(...)");
        r0.s(r13, r1, r4);
        e().add(r4);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:53:0x026b, code lost:
        return r4;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final synchronized defpackage.eso a(defpackage.eab r13) {
        /*
            r12 = this;
            monitor-enter(r12)
            java.lang.String r0 = "params"
            defpackage.jnu.e(r13, r0)     // Catch:{ all -> 0x0280 }
            eoz r0 = r12.b     // Catch:{ all -> 0x0280 }
            int r0 = r0.m()     // Catch:{ all -> 0x0280 }
            hca r1 = a     // Catch:{ all -> 0x0280 }
            hco r2 = r1.f()     // Catch:{ all -> 0x0280 }
            hcr r3 = defpackage.hdg.a     // Catch:{ all -> 0x0280 }
            java.lang.String r4 = "ALT.HotwordRoutesRegy"
            hco r2 = r2.h(r3, r4)     // Catch:{ all -> 0x0280 }
            java.lang.String r3 = "HotwordRoutesRegistry.kt"
            java.lang.String r4 = "com/google/android/libraries/search/audio/routing/registry/HotwordRoutesRegistry"
            java.lang.String r5 = "connectHotwordRoute$java_com_google_android_libraries_search_audio_routing_registry_hotword_routes_registry"
            r6 = 129(0x81, float:1.81E-43)
            hco r2 = r2.j(r4, r5, r6, r3)     // Catch:{ all -> 0x0280 }
            hby r2 = (defpackage.hby) r2     // Catch:{ all -> 0x0280 }
            java.lang.String r3 = defpackage.esx.m(r13)     // Catch:{ all -> 0x0280 }
            java.lang.String r4 = "#audio# updating hotword to route(token(%d), %s)"
            r2.x(r4, r0, r3)     // Catch:{ all -> 0x0280 }
            java.lang.String r2 = "params"
            defpackage.jnu.e(r13, r2)     // Catch:{ all -> 0x0280 }
            ehg r2 = r13.c     // Catch:{ all -> 0x0280 }
            if (r2 != 0) goto L_0x003c
            ehg r2 = defpackage.ehg.c     // Catch:{ all -> 0x0280 }
        L_0x003c:
            cyw r3 = r12.c     // Catch:{ all -> 0x0280 }
            java.lang.Object r4 = r3.d     // Catch:{ all -> 0x0280 }
            emd r4 = (defpackage.emd) r4     // Catch:{ all -> 0x0280 }
            java.lang.String r5 = "HOTWORD_ROUTE_UPDATE"
            r4.g(r2, r5)     // Catch:{ all -> 0x0280 }
            ehg r2 = r13.c     // Catch:{ all -> 0x0280 }
            if (r2 != 0) goto L_0x004d
            ehg r2 = defpackage.ehg.c     // Catch:{ all -> 0x0280 }
        L_0x004d:
            java.lang.Object r4 = r3.a     // Catch:{ all -> 0x0280 }
            java.lang.String r5 = "getClientInfo(...)"
            defpackage.jnu.d(r2, r5)     // Catch:{ all -> 0x0280 }
            r5 = 0
            r4.m(r2, r5)     // Catch:{ all -> 0x0280 }
            java.lang.Object r2 = r3.b     // Catch:{ all -> 0x0280 }
            eav r3 = defpackage.eav.n     // Catch:{ all -> 0x0280 }
            htk r3 = r3.l()     // Catch:{ all -> 0x0280 }
            java.lang.String r4 = "newBuilder(...)"
            defpackage.jnu.d(r3, r4)     // Catch:{ all -> 0x0280 }
            byw r3 = defpackage.jnu.e(r3, "builder")     // Catch:{ all -> 0x0280 }
            eay r4 = defpackage.eay.HOTWORD_UPDATE_AUDIO_ROUTE     // Catch:{ all -> 0x0280 }
            r3.o(r4)     // Catch:{ all -> 0x0280 }
            java.lang.String r4 = "value"
            defpackage.jnu.e(r13, r4)     // Catch:{ all -> 0x0280 }
            java.lang.Object r4 = r3.a     // Catch:{ all -> 0x0280 }
            r6 = r4
            htk r6 = (defpackage.htk) r6     // Catch:{ all -> 0x0280 }
            htq r6 = r6.b     // Catch:{ all -> 0x0280 }
            boolean r6 = r6.B()     // Catch:{ all -> 0x0280 }
            if (r6 != 0) goto L_0x0086
            r6 = r4
            htk r6 = (defpackage.htk) r6     // Catch:{ all -> 0x0280 }
            r6.u()     // Catch:{ all -> 0x0280 }
        L_0x0086:
            htk r4 = (defpackage.htk) r4     // Catch:{ all -> 0x0280 }
            htq r4 = r4.b     // Catch:{ all -> 0x0280 }
            eav r4 = (defpackage.eav) r4     // Catch:{ all -> 0x0280 }
            r13.getClass()     // Catch:{ all -> 0x0280 }
            r4.c = r13     // Catch:{ all -> 0x0280 }
            r6 = 205(0xcd, float:2.87E-43)
            r4.b = r6     // Catch:{ all -> 0x0280 }
            long r6 = (long) r0     // Catch:{ all -> 0x0280 }
            r3.r(r6)     // Catch:{ all -> 0x0280 }
            eav r3 = r3.l()     // Catch:{ all -> 0x0280 }
            eug r2 = (defpackage.eug) r2     // Catch:{ all -> 0x0280 }
            r2.d(r3)     // Catch:{ all -> 0x0280 }
            java.util.List r2 = e     // Catch:{ all -> 0x0280 }
            dzq r3 = r13.b     // Catch:{ all -> 0x0280 }
            if (r3 != 0) goto L_0x00aa
            dzq r3 = defpackage.dzq.c     // Catch:{ all -> 0x0280 }
        L_0x00aa:
            int r3 = r3.a     // Catch:{ all -> 0x0280 }
            dzp r3 = defpackage.dzp.a(r3)     // Catch:{ all -> 0x0280 }
            boolean r2 = r2.contains(r3)     // Catch:{ all -> 0x0280 }
            if (r2 == 0) goto L_0x00de
            esn r2 = new esn     // Catch:{ all -> 0x0280 }
            eaj r7 = defpackage.eaj.FAILED_TO_OPEN_AUDIO_SOURCE_DUE_TO_FAILED_ROUTING     // Catch:{ all -> 0x0280 }
            eag r8 = defpackage.eag.FAILED_CLOSING_AUDIO_SOURCE_DUE_TO_FAILED_ROUTING     // Catch:{ all -> 0x0280 }
            dzx r9 = defpackage.dzx.DISCONNECT_REASON_UNSUPPORTED_HOTWORD_ROUTE_PARAMS     // Catch:{ all -> 0x0280 }
            eaf r10 = defpackage.eaf.FAILED_ROUTING_DUE_TO_UNSUPPORTED_HOTWORD_ROUTE_PARAMS     // Catch:{ all -> 0x0280 }
            dzq r3 = r13.b     // Catch:{ all -> 0x0280 }
            if (r3 != 0) goto L_0x00c6
            dzq r3 = defpackage.dzq.c     // Catch:{ all -> 0x0280 }
        L_0x00c6:
            int r3 = r3.a     // Catch:{ all -> 0x0280 }
            dzp r3 = defpackage.dzp.a(r3)     // Catch:{ all -> 0x0280 }
            java.lang.String r4 = "hotword client doesn't support the "
            java.util.Objects.toString(r3)     // Catch:{ all -> 0x0280 }
            java.lang.String r3 = java.lang.String.valueOf(r3)     // Catch:{ all -> 0x0280 }
            java.lang.String r11 = r4.concat(r3)     // Catch:{ all -> 0x0280 }
            r6 = r2
            r6.<init>(r7, r8, r9, r10, r11)     // Catch:{ all -> 0x0280 }
            goto L_0x00df
        L_0x00de:
            r2 = r5
        L_0x00df:
            if (r2 != 0) goto L_0x0106
            hco r2 = r1.f()     // Catch:{ all -> 0x0280 }
            hcr r3 = defpackage.hdg.a     // Catch:{ all -> 0x0280 }
            java.lang.String r4 = "ALT.HotwordRoutesRegy"
            hco r2 = r2.h(r3, r4)     // Catch:{ all -> 0x0280 }
            java.lang.String r3 = "HotwordRoutesRegistry.kt"
            java.lang.String r4 = "com/google/android/libraries/search/audio/routing/registry/HotwordRoutesRegistry"
            java.lang.String r6 = "checkHotwordRouteUpdateConditionsOrFailedSession"
            r7 = 175(0xaf, float:2.45E-43)
            hco r2 = r2.j(r4, r6, r7, r3)     // Catch:{ all -> 0x0280 }
            hby r2 = (defpackage.hby) r2     // Catch:{ all -> 0x0280 }
            java.lang.String r3 = defpackage.esx.m(r13)     // Catch:{ all -> 0x0280 }
            java.lang.String r4 = "#audio# hotword route(token(%d), %s) OK to update"
            r2.x(r4, r0, r3)     // Catch:{ all -> 0x0280 }
            r3 = r5
            goto L_0x0161
        L_0x0106:
            hco r3 = r1.h()     // Catch:{ all -> 0x0280 }
            hcr r4 = defpackage.hdg.a     // Catch:{ all -> 0x0280 }
            java.lang.String r6 = "ALT.HotwordRoutesRegy"
            hco r3 = r3.h(r4, r6)     // Catch:{ all -> 0x0280 }
            java.lang.String r4 = "HotwordRoutesRegistry.kt"
            java.lang.String r6 = "com/google/android/libraries/search/audio/routing/registry/HotwordRoutesRegistry"
            java.lang.String r7 = "checkHotwordRouteUpdateConditionsOrFailedSession"
            r8 = 181(0xb5, float:2.54E-43)
            hco r3 = r3.j(r6, r7, r8, r4)     // Catch:{ all -> 0x0280 }
            hby r3 = (defpackage.hby) r3     // Catch:{ all -> 0x0280 }
            java.lang.Integer r4 = java.lang.Integer.valueOf(r0)     // Catch:{ all -> 0x0280 }
            java.lang.String r6 = defpackage.esx.m(r13)     // Catch:{ all -> 0x0280 }
            java.lang.String r7 = r2.e     // Catch:{ all -> 0x0280 }
            java.lang.String r8 = "#audio# updating hotword route(token(%d), %s) failed: %s"
            r3.G(r8, r4, r6, r7)     // Catch:{ all -> 0x0280 }
            dzx r3 = r2.c     // Catch:{ all -> 0x0280 }
            esq r3 = f(r3)     // Catch:{ all -> 0x0280 }
            r4 = 21
            esq r3 = defpackage.esq.g(r3, r0, r13, r4)     // Catch:{ all -> 0x0280 }
            cyw r4 = r12.c     // Catch:{ all -> 0x0280 }
            ehg r6 = r13.c     // Catch:{ all -> 0x0280 }
            if (r6 != 0) goto L_0x0143
            ehg r6 = defpackage.ehg.c     // Catch:{ all -> 0x0280 }
        L_0x0143:
            java.lang.String r7 = "getClientInfo(...)"
            defpackage.jnu.d(r6, r7)     // Catch:{ all -> 0x0280 }
            r4.r(r6)     // Catch:{ all -> 0x0280 }
            cyw r4 = r12.c     // Catch:{ all -> 0x0280 }
            ehg r6 = r13.c     // Catch:{ all -> 0x0280 }
            if (r6 != 0) goto L_0x0153
            ehg r6 = defpackage.ehg.c     // Catch:{ all -> 0x0280 }
        L_0x0153:
            java.lang.String r7 = "getClientInfo(...)"
            defpackage.jnu.d(r6, r7)     // Catch:{ all -> 0x0280 }
            eaf r2 = r2.d     // Catch:{ all -> 0x0280 }
            hme r2 = defpackage.eki.k(r2)     // Catch:{ all -> 0x0280 }
            r4.s(r6, r2, r3)     // Catch:{ all -> 0x0280 }
        L_0x0161:
            if (r3 != 0) goto L_0x027e
            hco r2 = r1.f()     // Catch:{ all -> 0x0280 }
            hcr r3 = defpackage.hdg.a     // Catch:{ all -> 0x0280 }
            java.lang.String r4 = "ALT.HotwordRoutesRegy"
            hco r2 = r2.h(r3, r4)     // Catch:{ all -> 0x0280 }
            java.lang.String r3 = "HotwordRoutesRegistry.kt"
            java.lang.String r4 = "com/google/android/libraries/search/audio/routing/registry/HotwordRoutesRegistry"
            java.lang.String r6 = "enforceConcurrencyStateOnNewHotwordRoute"
            r7 = 199(0xc7, float:2.79E-43)
            hco r2 = r2.j(r4, r6, r7, r3)     // Catch:{ all -> 0x0280 }
            hby r2 = (defpackage.hby) r2     // Catch:{ all -> 0x0280 }
            java.lang.String r3 = "#audio# enforcing concurrency state on a new hotword route"
            r2.r(r3)     // Catch:{ all -> 0x0280 }
            java.util.List r2 = r12.e()     // Catch:{ all -> 0x0280 }
            java.lang.Object r2 = defpackage.jji.u(r2)     // Catch:{ all -> 0x0280 }
            esp r2 = (defpackage.esp) r2     // Catch:{ all -> 0x0280 }
            if (r2 == 0) goto L_0x0195
            int r2 = r2.b     // Catch:{ all -> 0x0280 }
            dzx r3 = defpackage.dzx.DISCONNECT_REASON_CLIENT_REQUESTED_ROUTE_UPDATE     // Catch:{ all -> 0x0280 }
            r12.d(r2, r3)     // Catch:{ all -> 0x0280 }
        L_0x0195:
            dzq r2 = r13.b     // Catch:{ all -> 0x0280 }
            if (r2 != 0) goto L_0x019b
            dzq r2 = defpackage.dzq.c     // Catch:{ all -> 0x0280 }
        L_0x019b:
            java.lang.String r3 = "getAudioRouteType(...)"
            defpackage.jnu.d(r2, r3)     // Catch:{ all -> 0x0280 }
            int r3 = r2.a     // Catch:{ all -> 0x0280 }
            dzp r3 = defpackage.dzp.a(r3)     // Catch:{ all -> 0x0280 }
            int r3 = r3.ordinal()     // Catch:{ all -> 0x0280 }
            switch(r3) {
                case 0: goto L_0x026c;
                case 1: goto L_0x01cd;
                case 2: goto L_0x01ba;
                case 3: goto L_0x01b1;
                case 4: goto L_0x026c;
                case 5: goto L_0x026c;
                case 6: goto L_0x026c;
                case 7: goto L_0x026c;
                case 8: goto L_0x01cd;
                default: goto L_0x01ad;
            }     // Catch:{ all -> 0x0280 }
        L_0x01ad:
            jjq r13 = new jjq     // Catch:{ all -> 0x0280 }
            goto L_0x027a
        L_0x01b1:
            dsy r3 = r12.j     // Catch:{ all -> 0x0280 }
            ecg r4 = defpackage.ecg.BISTO     // Catch:{ all -> 0x0280 }
            eru r2 = r3.c(r2, r4)     // Catch:{ all -> 0x0280 }
            goto L_0x01d4
        L_0x01ba:
            erz r3 = new erz     // Catch:{ all -> 0x0280 }
            int r4 = r2.a     // Catch:{ all -> 0x0280 }
            r6 = 3
            if (r4 != r6) goto L_0x01c6
            java.lang.Object r2 = r2.b     // Catch:{ all -> 0x0280 }
            dzy r2 = (defpackage.dzy) r2     // Catch:{ all -> 0x0280 }
            goto L_0x01c8
        L_0x01c6:
            dzy r2 = defpackage.dzy.c     // Catch:{ all -> 0x0280 }
        L_0x01c8:
            r3.<init>(r2)     // Catch:{ all -> 0x0280 }
            r2 = r3
            goto L_0x01d4
        L_0x01cd:
            ery r2 = new ery     // Catch:{ all -> 0x0280 }
            gqd r3 = defpackage.gqd.a     // Catch:{ all -> 0x0280 }
            r2.<init>(r3)     // Catch:{ all -> 0x0280 }
        L_0x01d4:
            eoz r3 = r12.k     // Catch:{ all -> 0x0280 }
            ese r2 = r3.y(r2)     // Catch:{ all -> 0x0280 }
            hco r1 = r1.f()     // Catch:{ all -> 0x0280 }
            hcr r3 = defpackage.hdg.a     // Catch:{ all -> 0x0280 }
            java.lang.String r4 = "ALT.HotwordRoutesRegy"
            hco r1 = r1.h(r3, r4)     // Catch:{ all -> 0x0280 }
            java.lang.String r3 = "HotwordRoutesRegistry.kt"
            java.lang.String r4 = "com/google/android/libraries/search/audio/routing/registry/HotwordRoutesRegistry"
            java.lang.String r6 = "startHotwordRouteConnection"
            r7 = 232(0xe8, float:3.25E-43)
            hco r1 = r1.j(r4, r6, r7, r3)     // Catch:{ all -> 0x0280 }
            hby r1 = (defpackage.hby) r1     // Catch:{ all -> 0x0280 }
            ejn r3 = r2.d     // Catch:{ all -> 0x0280 }
            dzq r3 = r3.b     // Catch:{ all -> 0x0280 }
            java.lang.String r3 = defpackage.fbi.r(r3)     // Catch:{ all -> 0x0280 }
            java.lang.String r4 = "#audio# starting connection to hotword route(token(%d), %s)"
            r1.x(r4, r0, r3)     // Catch:{ all -> 0x0280 }
            hll r1 = r12.h     // Catch:{ all -> 0x0280 }
            ejj r3 = new ejj     // Catch:{ all -> 0x0280 }
            r4 = 7
            r3.<init>(r2, r12, r4, r5)     // Catch:{ all -> 0x0280 }
            hkn r3 = defpackage.gof.c(r3)     // Catch:{ all -> 0x0280 }
            java.util.concurrent.Executor r4 = r12.g     // Catch:{ all -> 0x0280 }
            hme r1 = r1.b(r3, r4)     // Catch:{ all -> 0x0280 }
            java.lang.String r3 = "submitAsync(...)"
            defpackage.jnu.d(r1, r3)     // Catch:{ all -> 0x0280 }
            eac r3 = defpackage.eac.c     // Catch:{ all -> 0x0280 }
            htk r3 = r3.l()     // Catch:{ all -> 0x0280 }
            java.lang.String r4 = "newBuilder(...)"
            defpackage.jnu.d(r3, r4)     // Catch:{ all -> 0x0280 }
            bzj r3 = defpackage.jnu.e(r3, "builder")     // Catch:{ all -> 0x0280 }
            ebw r4 = defpackage.ebw.c     // Catch:{ all -> 0x0280 }
            htk r4 = r4.l()     // Catch:{ all -> 0x0280 }
            java.lang.String r5 = "newBuilder(...)"
            defpackage.jnu.d(r4, r5)     // Catch:{ all -> 0x0280 }
            bzl r4 = defpackage.jnu.e(r4, "builder")     // Catch:{ all -> 0x0280 }
            r4.y(r0)     // Catch:{ all -> 0x0280 }
            ebw r4 = r4.x()     // Catch:{ all -> 0x0280 }
            r3.y(r4)     // Catch:{ all -> 0x0280 }
            eac r3 = r3.x()     // Catch:{ all -> 0x0280 }
            bzj r4 = r12.d     // Catch:{ all -> 0x0280 }
            ejn r5 = r2.d     // Catch:{ all -> 0x0280 }
            hme r5 = r5.a     // Catch:{ all -> 0x0280 }
            esa r3 = r4.O(r3, r5)     // Catch:{ all -> 0x0280 }
            esp r4 = new esp     // Catch:{ all -> 0x0280 }
            r4.<init>(r2, r3, r0, r13)     // Catch:{ all -> 0x0280 }
            cyw r0 = r12.c     // Catch:{ all -> 0x0280 }
            ehg r13 = r13.c     // Catch:{ all -> 0x0280 }
            if (r13 != 0) goto L_0x025b
            ehg r13 = defpackage.ehg.c     // Catch:{ all -> 0x0280 }
        L_0x025b:
            java.lang.String r2 = "getClientInfo(...)"
            defpackage.jnu.d(r13, r2)     // Catch:{ all -> 0x0280 }
            r0.s(r13, r1, r4)     // Catch:{ all -> 0x0280 }
            java.util.List r13 = r12.e()     // Catch:{ all -> 0x0280 }
            r13.add(r4)     // Catch:{ all -> 0x0280 }
            monitor-exit(r12)
            return r4
        L_0x026c:
            java.lang.String r13 = "Route("
            java.lang.String r0 = ") is unexpected"
            java.lang.IllegalStateException r1 = new java.lang.IllegalStateException     // Catch:{ all -> 0x0280 }
            java.lang.String r13 = defpackage.a.ao(r2, r13, r0)     // Catch:{ all -> 0x0280 }
            r1.<init>(r13)     // Catch:{ all -> 0x0280 }
            throw r1     // Catch:{ all -> 0x0280 }
        L_0x027a:
            r13.<init>()     // Catch:{ all -> 0x0280 }
            throw r13     // Catch:{ all -> 0x0280 }
        L_0x027e:
            monitor-exit(r12)
            return r3
        L_0x0280:
            r13 = move-exception
            monitor-exit(r12)     // Catch:{ all -> 0x0280 }
            throw r13
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.esr.a(eab):eso");
    }

    public final synchronized eso b(Integer num) {
        Object obj;
        int i2;
        ((hby) a.e().h(hdg.a, "ALT.HotwordRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/HotwordRoutesRegistry", "findHotwordRoute$java_com_google_android_libraries_search_audio_routing_registry_hotword_routes_registry", 302, "HotwordRoutesRegistry.kt")).u("#audio# find hotword route(%s)", esx.l(num));
        Iterator it = e().iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            esp esp = (esp) obj;
            if (num != null) {
                if (num.intValue() == esp.b) {
                    break;
                }
            } else {
                break;
            }
        }
        esp esp2 = (esp) obj;
        if (esp2 != null) {
            return esp2;
        }
        ((hby) a.e().h(hdg.a, "ALT.HotwordRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/HotwordRoutesRegistry", "findHotwordRoute$java_com_google_android_libraries_search_audio_routing_registry_hotword_routes_registry", 307, "HotwordRoutesRegistry.kt")).u("#audio# no hotword route(%s) found, inactive", esx.l(num));
        esq f2 = f(dzx.DISCONNECT_REASON_AUDIO_ROUTE_LOST);
        if (num != null) {
            i2 = num.intValue();
        } else {
            i2 = -1;
        }
        return esq.g(f2, i2, (eab) null, 29);
    }

    public final synchronized List c() {
        ArrayList arrayList;
        List<esp> e2 = e();
        arrayList = new ArrayList(jji.K(e2));
        for (esp esp : e2) {
            arrayList.add(esp.d);
        }
        return arrayList;
    }

    public final synchronized void d(int i2, dzx dzx) {
        Object obj;
        jnu.e(dzx, "reason");
        Iterator it = e().iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            if (((esp) obj).b == i2) {
                break;
            }
        }
        esp esp = (esp) obj;
        if (esp == null) {
            ((hby) a.f().h(hdg.a, "ALT.HotwordRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/HotwordRoutesRegistry", "disconnectHotwordRoute$java_com_google_android_libraries_search_audio_routing_registry_hotword_routes_registry", 269, "HotwordRoutesRegistry.kt")).x("#audio# no hotword route(%d) to disconnect(%s), skip", i2, dzx.name());
            return;
        }
        jji.N(e(), new eoh(i2, 7));
        ehg ehg = esp.c.c;
        if (ehg == null) {
            ehg = ehg.c;
        }
        jnu.d(ehg, "getClientInfo(...)");
        String name = dzx.name();
        int i3 = esp.b;
        String r = fbi.r(esp.d.b);
        ((hby) a.f().h(hdg.a, "ALT.HotwordRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/HotwordRoutesRegistry", "markDisconnected", 280, "HotwordRoutesRegistry.kt")).C("#audio# disconnecting(%s) hotword %s", name, "route(token(" + i3 + "), " + r + ")");
        eoz eoz = this.f;
        eow w = fbi.w(esp);
        eam g2 = eki.g(dzx);
        jnu.d(g2, "convertDisconnectReasonToStopListeningReason(...)");
        eoz.j(w, g2);
        this.c.r(ehg);
        fyz.c(this.h.b(gof.c(new ejj(esp, dzx, 6)), this.g), "Disconnecting the hotword route failed", new Object[0]);
    }
}
