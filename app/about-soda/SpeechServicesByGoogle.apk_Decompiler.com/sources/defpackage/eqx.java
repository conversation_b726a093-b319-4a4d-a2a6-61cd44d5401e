package defpackage;

/* renamed from: eqx  reason: default package */
/* compiled from: PG */
public final class eqx extends htq implements hvb {
    public static final eqx d;
    private static volatile hvh e;
    public int a;
    public ehg b;
    public dyt c;

    static {
        eqx eqx = new eqx();
        d = eqx;
        htq.z(eqx.class, eqx);
    }

    private eqx() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(d, "\u0004\u0002\u0000\u0001\u0001\u0002\u0002\u0000\u0000\u0000\u0001ဉ\u0000\u0002ဉ\u0001", new Object[]{"a", "b", "c"});
        } else if (i2 == 3) {
            return new eqx();
        } else {
            if (i2 == 4) {
                return new htk((htq) d);
            }
            if (i2 == 5) {
                return d;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = e;
            if (hvh == null) {
                synchronized (eqx.class) {
                    hvh = e;
                    if (hvh == null) {
                        hvh = new htl(d);
                        e = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
