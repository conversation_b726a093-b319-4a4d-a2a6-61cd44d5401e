package j$.time.format;

import j$.time.chrono.n;
import j$.time.temporal.p;
import java.util.Locale;

/* renamed from: j$.time.format.b  reason: case insensitive filesystem */
final class C0015b extends x {
    final /* synthetic */ w e;

    C0015b(w wVar) {
        this.e = wVar;
    }

    public final String d(n nVar, p pVar, long j, B b, Locale locale) {
        return this.e.a(j, b);
    }

    public final String e(p pVar, long j, B b, Locale locale) {
        return this.e.a(j, b);
    }
}
