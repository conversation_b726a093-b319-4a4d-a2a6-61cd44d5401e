package defpackage;

import com.google.android.libraries.performance.primes.transmitter.clearcut.ClearcutMetricSnapshotTransmitter;
import j$.util.Optional;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.concurrent.Executor;

/* renamed from: cnr  reason: default package */
/* compiled from: PG */
public final class cnr implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final /* synthetic */ int d;

    public cnr(jjk jjk, jjk jjk2, jjk jjk3, int i) {
        this.d = i;
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
    }

    /* JADX WARNING: type inference failed for: r1v67, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r1v79, types: [java.lang.Object, jjk] */
    public final /* synthetic */ Object b() {
        dom dom;
        Object obj;
        switch (this.d) {
            case 0:
                jjk jjk = this.c;
                return new coj(((con) jjk).b().a(((cns) this.a).a()), this.b);
            case 1:
                jjk jjk2 = this.c;
                return new coj(((con) jjk2).b().a(((cns) this.a).a()), this.b);
            case 2:
                jjk jjk3 = this.c;
                return new coj(((con) jjk3).b().a(((cns) this.a).a()), this.b);
            case 3:
                cqx cqx = (cqx) this.a.b();
                return new cyn((fps) this.b.b(), (Executor) this.c.b(), grq.a);
            case 4:
                jjk jjk4 = this.a;
                jjk jjk5 = this.c;
                return ((gih) jjk5).b().h(((cyu) this.b).a(), (kjd) jjk4.b());
            case 5:
                jjk jjk6 = this.a;
                jjk jjk7 = this.c;
                return ((gih) jjk7).b().h(((cyu) this.b).a(), (kjd) jjk6.b());
            case 6:
                jjk jjk8 = this.a;
                jjk jjk9 = this.c;
                return ((gih) jjk9).b().h(((cyu) this.b).a(), (kjd) jjk8.b());
            case 7:
                jjk jjk10 = this.a;
                jjk jjk11 = this.c;
                return ((gih) jjk11).b().h(((cyu) this.b).a(), (kjd) jjk10.b());
            case 8:
                jjk jjk12 = this.a;
                jjk jjk13 = this.c;
                return ((gih) jjk13).b().h(((cyv) this.b).b(), (kjd) jjk12.b());
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return new dji((hmi) this.c.b(), (dku) this.a.b(), this.b);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return new dkq((dku) this.b.b(), (dky) this.c.b(), this.a);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                Object obj2 = ((iiv) this.b).a;
                return new dmg((grh) obj2, this.a, ((dks) this.c).b());
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                jjk jjk14 = this.c;
                return new dnv(((dme) this.a).b(), iit.c(this.b), (Executor) jjk14.b());
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return new doc(iit.c(this.c), (grh) ((iiv) this.a).a, (Executor) this.b.b());
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                Executor executor = (Executor) this.c.b();
                dpe dpe = (dpe) this.a.b();
                cqx cqx2 = (cqx) this.b.b();
                return new dnk((byte[]) null);
            case 15:
                grh grh = (grh) ((iiv) this.c).a;
                if (((grh) ((iiv) this.a).a).f() || grh.f()) {
                    dom = (dom) this.b.b();
                } else {
                    dom = new dom((byte[]) null, (byte[]) null);
                }
                hzz.u(dom);
                return dom;
            case 16:
                grh grh2 = (grh) ((iiv) this.c).a;
                if (((grh) ((iiv) this.a).a).f() || grh2.f()) {
                    obj = new hbi((dmf) this.b.b());
                } else {
                    obj = hau.a;
                }
                hzz.u(obj);
                return obj;
            case 17:
                cqx cqx3 = (cqx) this.c.b();
                return new drx((Random) this.a.b(), (drl) this.b.b());
            case 18:
                jjk jjk15 = this.a;
                return new gef(((iim) this.b).a(), ((bqs) this.c).a(), ((dsl) jjk15).b(), new ClearcutMetricSnapshotTransmitter());
            case 19:
                Object obj3 = ((iiv) this.c).a;
                return new dwh(((dvz) this.b).b(), ((iir) this.a).a, (Map) obj3);
            default:
                fpi a2 = ((dwn) this.b).b();
                jjk jjk16 = this.a;
                jjk jjk17 = this.c;
                ihn c2 = iit.c(jjk16);
                dwr a3 = dws.a();
                a3.b = "SingletonLogger";
                Integer num = (Integer) ((Optional) ((iiv) jjk17).a).orElse(0);
                num.intValue();
                a3.d = grh.h(num);
                dws a4 = a3.a();
                ((cqx) a2.g.b()).getClass();
                grh grh3 = (grh) ((iiv) a2.e).a;
                grh3.getClass();
                grh grh4 = (grh) ((iiv) a2.a).a;
                grh4.getClass();
                Set set = (Set) ((iiv) a2.b).a;
                set.getClass();
                ? r1 = a2.f;
                Object obj4 = a2.c;
                Object obj5 = a2.d;
                Object b2 = r1.b();
                grh a5 = ((bqs) obj4).a();
                ((grh) ((iiv) obj5).a).getClass();
                c2.getClass();
                return new dwm(grh3, grh4, set, (alx) b2, a5, c2, a4);
        }
    }

    public cnr(jjk jjk, jjk jjk2, jjk jjk3, int i, float[] fArr) {
        this.d = i;
        this.b = jjk;
        this.c = jjk2;
        this.a = jjk3;
    }

    public cnr(jjk jjk, jjk jjk2, jjk jjk3, int i, short[] sArr) {
        this.d = i;
        this.b = jjk;
        this.a = jjk2;
        this.c = jjk3;
    }

    public cnr(jjk jjk, jjk jjk2, jjk jjk3, int i, short[][] sArr) {
        this.d = i;
        this.c = jjk;
        this.a = jjk2;
        this.b = jjk3;
    }

    public cnr(jjk jjk, jjk jjk2, jjk jjk3, int i, boolean[][] zArr) {
        this.d = i;
        this.a = jjk;
        this.c = jjk2;
        this.b = jjk3;
    }
}
