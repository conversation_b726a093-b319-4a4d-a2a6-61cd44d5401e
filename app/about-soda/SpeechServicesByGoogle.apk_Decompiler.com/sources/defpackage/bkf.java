package defpackage;

import android.os.Build;
import android.os.Bundle;
import android.view.View;
import android.view.WindowInsets;

/* renamed from: bkf  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bkf implements View.OnApplyWindowInsetsListener {
    private final /* synthetic */ int a;

    public /* synthetic */ bkf(int i) {
        this.a = i;
    }

    public final WindowInsets onApplyWindowInsets(View view, WindowInsets windowInsets) {
        if (this.a == 0) {
            return view.onApplyWindowInsets(new WindowInsets.Builder(windowInsets).setInsets(WindowInsets.Type.displayCutout(), dp$$ExternalSyntheticApiModelOutline0.m()).build());
        }
        if (Build.VERSION.SDK_INT < 30) {
            return view.onApplyWindowInsets(windowInsets);
        }
        if (!sk$$ExternalSyntheticApiModelOutline1.m(windowInsets, sk$$ExternalSyntheticApiModelOutline1.m())) {
            return view.onApplyWindowInsets(windowInsets);
        }
        new Bundle();
        throw null;
    }
}
