package defpackage;

/* renamed from: cns  reason: default package */
/* compiled from: PG */
public final class cns implements iiu {
    private final jjk a;
    private final jjk b;
    private final /* synthetic */ int c;

    public cns(jjk jjk, jjk jjk2, int i) {
        this.c = i;
        this.a = jjk;
        this.b = jjk2;
    }

    public final hmi a() {
        int i = this.c;
        if (i == 0) {
            hmi E = cqx.E(this.a, (grh) ((iiv) this.b).a);
            hzz.u(E);
            return E;
        } else if (i != 1) {
            hmi E2 = cqx.E(this.a, (grh) ((iiv) this.b).a);
            hzz.u(E2);
            return E2;
        } else {
            hmi E3 = cqx.E(this.a, (grh) ((iiv) this.b).a);
            hzz.u(E3);
            return E3;
        }
    }

    public final /* synthetic */ Object b() {
        int i = this.c;
        if (i == 0) {
            return a();
        }
        if (i != 1) {
            return a();
        }
        return a();
    }
}
