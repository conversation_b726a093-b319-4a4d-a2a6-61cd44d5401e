package defpackage;

import android.net.Uri;
import java.util.concurrent.Executor;

/* renamed from: cwe  reason: default package */
/* compiled from: PG */
public final class cwe implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;
    private final jjk e;
    private final jjk f;
    private final jjk g;
    private final jjk h;
    private final jjk i;
    private final jjk j;
    private final jjk k;

    public cwe(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, jjk jjk9, jjk jjk10, jjk jjk11) {
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
        this.e = jjk5;
        this.f = jjk6;
        this.g = jjk7;
        this.h = jjk8;
        this.i = jjk9;
        this.j = jjk10;
        this.k = jjk11;
    }

    /* renamed from: a */
    public final cwd b() {
        ((iim) this.a).a();
        cqh cqh = (cqh) this.k.b();
        return new cwd((cyk) this.b.b(), ((cxe) this.c).b(), (cws) this.d.b(), (cws) this.e.b(), (Uri) this.f.b(), (Uri) this.g.b(), ((cwp) this.h).b(), (kjd) this.i.b(), (Executor) this.j.b());
    }
}
