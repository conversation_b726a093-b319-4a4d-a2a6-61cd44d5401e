package defpackage;

import java.util.concurrent.Executor;

/* renamed from: eua  reason: default package */
/* compiled from: PG */
public final class eua {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender");
    public final jna b;
    public final grh c;
    public final Runnable d;
    public final Executor e;
    public final Executor f;
    public final jpp g = new jpp(false, jpt.a);
    private final hme h;
    private final hme i;
    private final jna j;
    private final hme k;
    private final jix l;
    private final jqs m;
    private final jpp n = new jpp(false, jpt.a);

    public eua(hme hme, jna jna, hme hme2, jna jna2, hme hme3, grh grh, Runnable runnable, jix jix, jqs jqs, Executor executor, Executor executor2) {
        this.h = hme;
        this.b = jna;
        this.i = hme2;
        this.j = jna2;
        this.k = hme3;
        this.c = grh;
        this.d = runnable;
        this.l = jix;
        this.m = jqs;
        this.e = executor;
        this.f = executor2;
        ((jiq) jix).e(new dqb(this, 17));
    }

    private final void l(eaj eaj) {
        htk l2 = etf.c.l();
        jnu.d(l2, "newBuilder(...)");
        dlv t = jnu.e(l2, "builder");
        htk l3 = etc.f.l();
        jnu.d(l3, "newBuilder(...)");
        bzl C = jnu.e(l3, "builder");
        htk l4 = dzh.c.l();
        jnu.d(l4, "newBuilder(...)");
        bzl n2 = jnu.e(l4, "builder");
        htk l5 = eak.c.l();
        jnu.d(l5, "newBuilder(...)");
        dlv e2 = jnu.e(l5, "builder");
        e2.f(eaj);
        n2.w(e2.e());
        C.L(n2.v());
        htk l6 = ebw.c.l();
        jnu.d(l6, "newBuilder(...)");
        bzl o = jnu.e(l6, "builder");
        o.y(((Number) ((grm) this.c).a).intValue());
        C.K(o.x());
        t.r(C.J());
        f(t.n());
        h();
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x003b  */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0074 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:29:0x0075  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0029  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object a(defpackage.jlr r9) {
        /*
            r8 = this;
            boolean r0 = r9 instanceof defpackage.etq
            if (r0 == 0) goto L_0x0013
            r0 = r9
            etq r0 = (defpackage.etq) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            etq r0 = new etq
            r0.<init>(r8, r9)
        L_0x0018:
            java.lang.Object r9 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            java.lang.String r3 = "handleAudioParams"
            java.lang.String r4 = "com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender"
            java.lang.String r5 = "StreamListeningSessionResponseSender.kt"
            java.lang.String r6 = "ALT.GrpcARCRespSender"
            r7 = 1
            if (r2 == 0) goto L_0x003b
            if (r2 != r7) goto L_0x0033
            eua r0 = r0.d
            defpackage.jji.c(r9)     // Catch:{ all -> 0x0031 }
            goto L_0x0064
        L_0x0031:
            r9 = move-exception
            goto L_0x006a
        L_0x0033:
            java.lang.IllegalStateException r9 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r9.<init>(r0)
            throw r9
        L_0x003b:
            defpackage.jji.c(r9)
            hca r9 = a
            hco r9 = r9.c()
            hcr r2 = defpackage.hdg.a
            hco r9 = r9.h(r2, r6)
            r2 = 184(0xb8, float:2.58E-43)
            hco r9 = r9.j(r4, r3, r2, r5)
            hby r9 = (defpackage.hby) r9
            java.lang.String r2 = "#audio# Handle audio params for the remote client"
            r9.r(r2)
            hme r9 = r8.k     // Catch:{ all -> 0x0068 }
            r0.d = r8     // Catch:{ all -> 0x0068 }
            r0.c = r7     // Catch:{ all -> 0x0068 }
            java.lang.Object r9 = defpackage.jqw.x(r9, r0)     // Catch:{ all -> 0x0068 }
            if (r9 == r1) goto L_0x0067
            r0 = r8
        L_0x0064:
            dyt r9 = (defpackage.dyt) r9     // Catch:{ all -> 0x0031 }
            goto L_0x006e
        L_0x0067:
            return r1
        L_0x0068:
            r9 = move-exception
            r0 = r8
        L_0x006a:
            java.lang.Object r9 = defpackage.jji.b(r9)
        L_0x006e:
            java.lang.Throwable r1 = defpackage.jju.a(r9)
            if (r1 != 0) goto L_0x0075
            return r9
        L_0x0075:
            hca r9 = a
            hco r9 = r9.g()
            hcr r2 = defpackage.hdg.a
            hco r9 = r9.h(r2, r6)
            hby r9 = (defpackage.hby) r9
            hco r9 = r9.i(r1)
            r1 = 190(0xbe, float:2.66E-43)
            hco r9 = r9.j(r4, r3, r1, r5)
            hby r9 = (defpackage.hby) r9
            java.lang.String r1 = "#audio# Fail while getting session's audio params, shutting down"
            r9.r(r1)
            eaj r9 = defpackage.eaj.FAILED_OPENING_ERROR_RETRIEVING_AUDIO_PARAMS
            r0.l(r9)
            r9 = 0
            return r9
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eua.a(jlr):java.lang.Object");
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x003b  */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0074 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:29:0x0075  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0029  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object b(defpackage.jlr r9) {
        /*
            r8 = this;
            boolean r0 = r9 instanceof defpackage.etr
            if (r0 == 0) goto L_0x0013
            r0 = r9
            etr r0 = (defpackage.etr) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            etr r0 = new etr
            r0.<init>(r8, r9)
        L_0x0018:
            java.lang.Object r9 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            java.lang.String r3 = "handleStartListeningResult"
            java.lang.String r4 = "com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender"
            java.lang.String r5 = "StreamListeningSessionResponseSender.kt"
            java.lang.String r6 = "ALT.GrpcARCRespSender"
            r7 = 1
            if (r2 == 0) goto L_0x003b
            if (r2 != r7) goto L_0x0033
            eua r0 = r0.d
            defpackage.jji.c(r9)     // Catch:{ all -> 0x0031 }
            goto L_0x0064
        L_0x0031:
            r9 = move-exception
            goto L_0x006a
        L_0x0033:
            java.lang.IllegalStateException r9 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r9.<init>(r0)
            throw r9
        L_0x003b:
            defpackage.jji.c(r9)
            hca r9 = a
            hco r9 = r9.c()
            hcr r2 = defpackage.hdg.a
            hco r9 = r9.h(r2, r6)
            r2 = 171(0xab, float:2.4E-43)
            hco r9 = r9.j(r4, r3, r2, r5)
            hby r9 = (defpackage.hby) r9
            java.lang.String r2 = "#audio# Handle start listening result for the remote client"
            r9.r(r2)
            hme r9 = r8.h     // Catch:{ all -> 0x0068 }
            r0.d = r8     // Catch:{ all -> 0x0068 }
            r0.c = r7     // Catch:{ all -> 0x0068 }
            java.lang.Object r9 = defpackage.jqw.x(r9, r0)     // Catch:{ all -> 0x0068 }
            if (r9 == r1) goto L_0x0067
            r0 = r8
        L_0x0064:
            ebg r9 = (defpackage.ebg) r9     // Catch:{ all -> 0x0031 }
            goto L_0x006e
        L_0x0067:
            return r1
        L_0x0068:
            r9 = move-exception
            r0 = r8
        L_0x006a:
            java.lang.Object r9 = defpackage.jji.b(r9)
        L_0x006e:
            java.lang.Throwable r1 = defpackage.jju.a(r9)
            if (r1 != 0) goto L_0x0075
            return r9
        L_0x0075:
            hca r9 = a
            hco r9 = r9.g()
            hcr r2 = defpackage.hdg.a
            hco r9 = r9.h(r2, r6)
            hby r9 = (defpackage.hby) r9
            hco r9 = r9.i(r1)
            r1 = 177(0xb1, float:2.48E-43)
            hco r9 = r9.j(r4, r3, r1, r5)
            hby r9 = (defpackage.hby) r9
            java.lang.String r1 = "#audio# Fail while getting session's start status, shutting down"
            r9.r(r1)
            eaj r9 = defpackage.eaj.FAILED_OPENING_ERROR_RETRIEVING_STATUS
            r0.l(r9)
            r9 = 0
            return r9
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eua.b(jlr):java.lang.Object");
    }

    public final Object c(dya dya, jna jna, jlr jlr) {
        Object d2 = jqw.d(new etu(dya, this, jna, (jlr) null), jlr);
        if (d2 == jlx.COROUTINE_SUSPENDED) {
            return d2;
        }
        return jkd.a;
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x003b  */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0075  */
    /* JADX WARNING: Removed duplicated region for block: B:30:0x0091  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0029  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object d(defpackage.hme r9, defpackage.jlr r10) {
        /*
            r8 = this;
            boolean r0 = r10 instanceof defpackage.etv
            if (r0 == 0) goto L_0x0013
            r0 = r10
            etv r0 = (defpackage.etv) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            etv r0 = new etv
            r0.<init>(r8, r10)
        L_0x0018:
            java.lang.Object r10 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            java.lang.String r3 = "sendAudioStartTime"
            java.lang.String r4 = "com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender"
            java.lang.String r5 = "StreamListeningSessionResponseSender.kt"
            java.lang.String r6 = "ALT.GrpcARCRespSender"
            r7 = 1
            if (r2 == 0) goto L_0x003b
            if (r2 != r7) goto L_0x0033
            eua r9 = r0.d
            defpackage.jji.c(r10)     // Catch:{ all -> 0x0031 }
            goto L_0x0062
        L_0x0031:
            r10 = move-exception
            goto L_0x0069
        L_0x0033:
            java.lang.IllegalStateException r9 = new java.lang.IllegalStateException
            java.lang.String r10 = "call to 'resume' before 'invoke' with coroutine"
            r9.<init>(r10)
            throw r9
        L_0x003b:
            defpackage.jji.c(r10)
            hca r10 = a
            hco r10 = r10.c()
            hcr r2 = defpackage.hdg.a
            hco r10 = r10.h(r2, r6)
            r2 = 322(0x142, float:4.51E-43)
            hco r10 = r10.j(r4, r3, r2, r5)
            hby r10 = (defpackage.hby) r10
            java.lang.String r2 = "#audio# sending audio start time to the remote client"
            r10.r(r2)
            r0.d = r8     // Catch:{ all -> 0x0066 }
            r0.c = r7     // Catch:{ all -> 0x0066 }
            java.lang.Object r10 = defpackage.jqw.x(r9, r0)     // Catch:{ all -> 0x0066 }
            if (r10 == r1) goto L_0x0065
            r9 = r8
        L_0x0062:
            dyu r10 = (defpackage.dyu) r10     // Catch:{ all -> 0x0031 }
            goto L_0x006d
        L_0x0065:
            return r1
        L_0x0066:
            r9 = move-exception
            r10 = r9
            r9 = r8
        L_0x0069:
            java.lang.Object r10 = defpackage.jji.b(r10)
        L_0x006d:
            java.lang.Throwable r0 = defpackage.jju.a(r10)
            java.lang.String r1 = "newBuilder(...)"
            if (r0 != 0) goto L_0x0091
            dyu r10 = (defpackage.dyu) r10
            etf r0 = defpackage.etf.c
            htk r0 = r0.l()
            defpackage.jnu.d(r0, r1)
            dlv r0 = defpackage.jnu.e(r0, "builder")
            r0.p(r10)
            etf r10 = r0.n()
            r9.f(r10)
            jkd r9 = defpackage.jkd.a
            return r9
        L_0x0091:
            hca r10 = a
            hco r10 = r10.h()
            hcr r2 = defpackage.hdg.a
            hco r10 = r10.h(r2, r6)
            hby r10 = (defpackage.hby) r10
            hco r10 = r10.i(r0)
            r0 = 329(0x149, float:4.61E-43)
            hco r10 = r10.j(r4, r3, r0, r5)
            hby r10 = (defpackage.hby) r10
            java.lang.String r0 = "#audio# unexpected fail while getting session's audio start time, send empty"
            r10.r(r0)
            etf r10 = defpackage.etf.c
            htk r10 = r10.l()
            defpackage.jnu.d(r10, r1)
            dlv r10 = defpackage.jnu.e(r10, "builder")
            dyu r0 = defpackage.dyu.c
            htk r0 = r0.l()
            htm r0 = (defpackage.htm) r0
            defpackage.jnu.d(r0, r1)
            dlv r0 = defpackage.jnu.e(r0, "builder")
            dyu r0 = r0.c()
            r10.p(r0)
            etf r10 = r10.n()
            r9.f(r10)
            jkd r9 = defpackage.jkd.a
            return r9
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eua.d(hme, jlr):java.lang.Object");
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x003b  */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0075  */
    /* JADX WARNING: Removed duplicated region for block: B:30:0x0091  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0029  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object e(defpackage.hme r9, defpackage.jlr r10) {
        /*
            r8 = this;
            boolean r0 = r10 instanceof defpackage.etw
            if (r0 == 0) goto L_0x0013
            r0 = r10
            etw r0 = (defpackage.etw) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            etw r0 = new etw
            r0.<init>(r8, r10)
        L_0x0018:
            java.lang.Object r10 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            java.lang.String r3 = "sendFirstByteReadResult"
            java.lang.String r4 = "com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender"
            java.lang.String r5 = "StreamListeningSessionResponseSender.kt"
            java.lang.String r6 = "ALT.GrpcARCRespSender"
            r7 = 1
            if (r2 == 0) goto L_0x003b
            if (r2 != r7) goto L_0x0033
            eua r9 = r0.d
            defpackage.jji.c(r10)     // Catch:{ all -> 0x0031 }
            goto L_0x0062
        L_0x0031:
            r10 = move-exception
            goto L_0x0069
        L_0x0033:
            java.lang.IllegalStateException r9 = new java.lang.IllegalStateException
            java.lang.String r10 = "call to 'resume' before 'invoke' with coroutine"
            r9.<init>(r10)
            throw r9
        L_0x003b:
            defpackage.jji.c(r10)
            hca r10 = a
            hco r10 = r10.c()
            hcr r2 = defpackage.hdg.a
            hco r10 = r10.h(r2, r6)
            r2 = 302(0x12e, float:4.23E-43)
            hco r10 = r10.j(r4, r3, r2, r5)
            hby r10 = (defpackage.hby) r10
            java.lang.String r2 = "#audio# Sending first byte read result to the remote client"
            r10.r(r2)
            r0.d = r8     // Catch:{ all -> 0x0066 }
            r0.c = r7     // Catch:{ all -> 0x0066 }
            java.lang.Object r10 = defpackage.jqw.x(r9, r0)     // Catch:{ all -> 0x0066 }
            if (r10 == r1) goto L_0x0065
            r9 = r8
        L_0x0062:
            dyv r10 = (defpackage.dyv) r10     // Catch:{ all -> 0x0031 }
            goto L_0x006d
        L_0x0065:
            return r1
        L_0x0066:
            r9 = move-exception
            r10 = r9
            r9 = r8
        L_0x0069:
            java.lang.Object r10 = defpackage.jji.b(r10)
        L_0x006d:
            java.lang.Throwable r0 = defpackage.jju.a(r10)
            java.lang.String r1 = "newBuilder(...)"
            if (r0 != 0) goto L_0x0091
            dyv r10 = (defpackage.dyv) r10
            etf r0 = defpackage.etf.c
            htk r0 = r0.l()
            defpackage.jnu.d(r0, r1)
            dlv r0 = defpackage.jnu.e(r0, "builder")
            r0.q(r10)
            etf r10 = r0.n()
            r9.f(r10)
            jkd r9 = defpackage.jkd.a
            return r9
        L_0x0091:
            hca r10 = a
            hco r10 = r10.h()
            hcr r2 = defpackage.hdg.a
            hco r10 = r10.h(r2, r6)
            hby r10 = (defpackage.hby) r10
            hco r10 = r10.i(r0)
            r0 = 309(0x135, float:4.33E-43)
            hco r10 = r10.j(r4, r3, r0, r5)
            hby r10 = (defpackage.hby) r10
            java.lang.String r0 = "#audio# Unexpected fail while getting session's first byte result, send empty"
            r10.r(r0)
            etf r10 = defpackage.etf.c
            htk r10 = r10.l()
            defpackage.jnu.d(r10, r1)
            dlv r10 = defpackage.jnu.e(r10, "builder")
            dyv r0 = defpackage.dyv.c
            htk r0 = r0.l()
            defpackage.jnu.d(r0, r1)
            bzj r0 = defpackage.jnu.e(r0, "builder")
            dyv r0 = r0.A()
            r10.q(r0)
            etf r10 = r10.n()
            r9.f(r10)
            jkd r9 = defpackage.jkd.a
            return r9
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eua.e(hme, jlr):java.lang.Object");
    }

    public final Object f(etf etf) {
        Object obj;
        if (this.g.b()) {
            return jji.b(new Throwable());
        }
        try {
            this.l.c(etf);
            obj = jkd.a;
        } catch (Throwable th) {
            obj = jji.b(th);
        }
        Throwable a2 = jju.a(obj);
        if (a2 == null) {
            return obj;
        }
        ((hby) ((hby) a.h().h(hdg.a, "ALT.GrpcARCRespSender")).i(a2).j("com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender", "sendNext-IoAF18A", 388, "StreamListeningSessionResponseSender.kt")).r("#audio# Cannot send audio session data to the remote client, shutting down");
        i(a2);
        return obj;
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x003b  */
    /* JADX WARNING: Removed duplicated region for block: B:26:0x0074  */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0096  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0029  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object g(defpackage.jlr r9) {
        /*
            r8 = this;
            boolean r0 = r9 instanceof defpackage.etx
            if (r0 == 0) goto L_0x0013
            r0 = r9
            etx r0 = (defpackage.etx) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            etx r0 = new etx
            r0.<init>(r8, r9)
        L_0x0018:
            java.lang.Object r9 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            java.lang.String r3 = "sendStopListeningStatus"
            java.lang.String r4 = "com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender"
            java.lang.String r5 = "StreamListeningSessionResponseSender.kt"
            java.lang.String r6 = "ALT.GrpcARCRespSender"
            r7 = 1
            if (r2 == 0) goto L_0x003b
            if (r2 != r7) goto L_0x0033
            eua r0 = r0.d
            defpackage.jji.c(r9)     // Catch:{ all -> 0x0031 }
            goto L_0x006c
        L_0x0031:
            r9 = move-exception
            goto L_0x0068
        L_0x0033:
            java.lang.IllegalStateException r9 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r9.<init>(r0)
            throw r9
        L_0x003b:
            defpackage.jji.c(r9)
            hca r9 = a
            hco r9 = r9.c()
            hcr r2 = defpackage.hdg.a
            hco r9 = r9.h(r2, r6)
            r2 = 198(0xc6, float:2.77E-43)
            hco r9 = r9.j(r4, r3, r2, r5)
            hby r9 = (defpackage.hby) r9
            java.lang.String r2 = "#audio# waiting for stop status before sending to the remote client"
            r9.r(r2)
            hme r9 = r8.i     // Catch:{ all -> 0x0066 }
            r0.d = r8     // Catch:{ all -> 0x0066 }
            r0.c = r7     // Catch:{ all -> 0x0066 }
            java.lang.Object r9 = defpackage.jqw.x(r9, r0)     // Catch:{ all -> 0x0066 }
            if (r9 == r1) goto L_0x0065
            r0 = r8
            goto L_0x006c
        L_0x0065:
            return r1
        L_0x0066:
            r9 = move-exception
            r0 = r8
        L_0x0068:
            java.lang.Object r9 = defpackage.jji.b(r9)
        L_0x006c:
            java.lang.Throwable r1 = defpackage.jju.a(r9)
            java.lang.String r2 = "newBuilder(...)"
            if (r1 != 0) goto L_0x0096
            etf r1 = defpackage.etf.c
            htk r1 = r1.l()
            defpackage.jnu.d(r1, r2)
            dlv r1 = defpackage.jnu.e(r1, "builder")
            jna r2 = r0.j
            java.lang.Object r9 = r2.a(r9)
            dzi r9 = (defpackage.dzi) r9
            r1.s(r9)
            etf r9 = r1.n()
            r0.f(r9)
            jkd r9 = defpackage.jkd.a
            return r9
        L_0x0096:
            hca r9 = a
            hco r9 = r9.h()
            hcr r7 = defpackage.hdg.a
            hco r9 = r9.h(r7, r6)
            hby r9 = (defpackage.hby) r9
            hco r9 = r9.i(r1)
            r1 = 205(0xcd, float:2.87E-43)
            hco r9 = r9.j(r4, r3, r1, r5)
            hby r9 = (defpackage.hby) r9
            java.lang.String r1 = "#audio# Unexpected fail while getting stop listening status, send failing"
            r9.r(r1)
            eah r9 = defpackage.eah.c
            htk r9 = r9.l()
            defpackage.jnu.d(r9, r2)
            dlv r9 = defpackage.jnu.e(r9, "builder")
            eag r1 = defpackage.eag.FAILED_CLOSING_GRPC_STOP_LISTENING_STATUS_NOT_RECEIVED
            r9.i(r1)
            eah r9 = r9.h()
            etf r1 = defpackage.etf.c
            htk r1 = r1.l()
            defpackage.jnu.d(r1, r2)
            dlv r1 = defpackage.jnu.e(r1, "builder")
            dzi r3 = defpackage.dzi.d
            htk r3 = r3.l()
            defpackage.jnu.d(r3, r2)
            bzj r2 = defpackage.jnu.e(r3, "builder")
            r2.s(r9)
            dzi r9 = r2.r()
            r1.s(r9)
            etf r9 = r1.n()
            r0.f(r9)
            jkd r9 = defpackage.jkd.a
            return r9
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eua.g(jlr):java.lang.Object");
    }

    public final void h() {
        if (!this.g.c()) {
            try {
                this.l.a();
            } catch (Throwable th) {
                jji.b(th);
            }
            j();
        }
    }

    public final void i(Throwable th) {
        if (!this.g.c()) {
            try {
                this.l.b(th);
            } catch (Throwable th2) {
                jji.b(th2);
            }
            j();
        }
    }

    public final void j() {
        ((hby) a.f().h(hdg.a, "ALT.GrpcARCRespSender").j("com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender", "shutdown", 347, "StreamListeningSessionResponseSender.kt")).r("#audio# the communication channel shut down");
        this.e.execute(gof.h(new dqb(this, 18)));
    }

    public final void k() {
        if (this.n.c()) {
            ((hby) a.c().h(hdg.a, "ALT.GrpcARCRespSender").j("com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender", "start", 86, "StreamListeningSessionResponseSender.kt")).r("#audio# Skipping sending due to already started");
            return;
        }
        ((hby) a.c().h(hdg.a, "ALT.GrpcARCRespSender").j("com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender", "start", 89, "StreamListeningSessionResponseSender.kt")).u("#audio# starting sending session(%s) info", ((grm) this.c).a);
        job.S(this.m, (jlv) null, (jqt) null, new etz(this, (jlr) null), 3);
    }
}
