package defpackage;

/* renamed from: bdq  reason: default package */
/* compiled from: PG */
public final class bdq extends jnv implements jmp {
    final /* synthetic */ Object a;
    final /* synthetic */ Object b;
    final /* synthetic */ Object c;
    private final /* synthetic */ int d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bdq(euz euz, eel eel, ebg ebg, int i) {
        super(0);
        this.d = i;
        this.c = euz;
        this.b = eel;
        this.a = ebg;
    }

    /* JADX WARNING: type inference failed for: r23v0, types: [java.util.Set] */
    /* JADX WARNING: type inference failed for: r2v27, types: [java.lang.Object, ebg] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final /* synthetic */ java.lang.Object a() {
        /*
            r25 = this;
            r1 = r25
            int r0 = r1.d
            r2 = 2
            if (r0 == 0) goto L_0x0228
            r3 = 1
            if (r0 == r3) goto L_0x0216
            r4 = 0
            if (r0 == r2) goto L_0x00ad
            r2 = 3
            if (r0 == r2) goto L_0x002e
            java.lang.Object r0 = r1.b
            kbo r0 = (defpackage.kbo) r0
            kgj r0 = r0.b
            defpackage.jnu.b(r0)
            java.lang.Object r2 = r1.a
            kbj r2 = (defpackage.kbj) r2
            kcd r2 = r2.i
            java.lang.Object r3 = r1.c
            kca r3 = (defpackage.kca) r3
            java.util.List r3 = r3.a()
            java.lang.String r2 = r2.c
            java.util.List r0 = r0.a(r3, r2)
            return r0
        L_0x002e:
            java.lang.Object r0 = r1.b
            eel r0 = (defpackage.eel) r0
            java.lang.String r0 = defpackage.esx.n(r0)
            java.lang.Object r2 = r1.a
            grh r2 = r2.a()
            java.lang.Object r2 = r2.b()
            dxx r2 = (defpackage.dxx) r2
            java.lang.Object r3 = r1.c
            euz r3 = (defpackage.euz) r3
            eui r5 = r3.a(r0)
            dyt r0 = r3.f
            int r6 = defpackage.evc.d(r0)
            byte[] r7 = new byte[r6]
            r8 = r4
        L_0x0053:
            int r0 = r2.a(r7, r8, r4, r6)     // Catch:{ all -> 0x005c }
            java.lang.Integer r0 = java.lang.Integer.valueOf(r0)     // Catch:{ all -> 0x005c }
            goto L_0x0061
        L_0x005c:
            r0 = move-exception
            java.lang.Object r0 = defpackage.jji.b(r0)
        L_0x0061:
            java.lang.Throwable r9 = defpackage.jju.a(r0)
            if (r9 != 0) goto L_0x0084
            java.lang.Number r0 = (java.lang.Number) r0
            int r0 = r0.intValue()
            if (r0 < 0) goto L_0x0080
            if (r0 <= 0) goto L_0x0053
            int r8 = r8 + r0
            hsq r0 = defpackage.hsq.s(r7, r4, r0)
            hme r0 = r5.c(r0)
            boolean r0 = r0.isCancelled()
            if (r0 == 0) goto L_0x0053
        L_0x0080:
            r5.a()
            goto L_0x00aa
        L_0x0084:
            hca r0 = defpackage.euz.a
            hco r0 = r0.h()
            hcr r2 = defpackage.hdg.a
            java.lang.String r4 = "ALT.SessionAudioStore"
            hco r0 = r0.h(r2, r4)
            java.lang.String r2 = "storeAudio"
            r4 = 122(0x7a, float:1.71E-43)
            java.lang.String r6 = "com/google/android/libraries/search/audio/store/session/SessionAudioStore"
            java.lang.String r7 = "SessionAudioStore.kt"
            hco r0 = r0.j(r6, r2, r4, r7)
            hby r0 = (defpackage.hby) r0
            int r2 = r3.g
            java.lang.String r3 = "#audio# cannot read session(%s) next audio data"
            r0.s(r3, r2)
            r5.a()
        L_0x00aa:
            jkd r0 = defpackage.jkd.a
            return r0
        L_0x00ad:
            java.lang.Object r0 = r1.c
            java.lang.Object r2 = r1.b
            java.lang.Object r5 = r1.a
            bdq r6 = new bdq
            r6.<init>((java.lang.Object) r5, (java.lang.Object) r2, (java.lang.Object) r0, (int) r4)
            bdm r2 = (defpackage.bdm) r2
            androidx.work.impl.WorkDatabase r0 = r2.d
            bhf r0 = r0.A()
            java.lang.Object r2 = r1.c
            java.lang.String r2 = (java.lang.String) r2
            java.util.List r2 = r0.e(r2)
            int r4 = r2.size()
            if (r4 > r3) goto L_0x020e
            java.lang.Object r2 = defpackage.jji.u(r2)
            bhc r2 = (defpackage.bhc) r2
            if (r2 != 0) goto L_0x00db
            r6.a()
            goto L_0x01ce
        L_0x00db:
            java.lang.String r3 = r2.a
            bhe r3 = r0.b(r3)
            if (r3 == 0) goto L_0x01e7
            boolean r3 = r3.d()
            if (r3 == 0) goto L_0x01df
            bbx r3 = r2.b
            bbx r4 = defpackage.bbx.CANCELLED
            if (r3 != r4) goto L_0x00f9
            java.lang.String r2 = r2.a
            r0.f(r2)
            r6.a()
            goto L_0x01ce
        L_0x00f9:
            java.lang.Object r0 = r1.a
            java.lang.String r4 = r2.a
            bmu r0 = (defpackage.bmu) r0
            java.lang.Object r0 = r0.b
            r3 = r0
            bhe r3 = (defpackage.bhe) r3
            r15 = 0
            r16 = 16777214(0xfffffe, float:2.3509884E-38)
            r5 = 0
            r6 = 0
            r7 = 0
            r8 = 0
            r9 = 0
            r11 = 0
            r12 = 0
            r13 = 0
            bhe r0 = defpackage.bhe.e(r3, r4, r5, r6, r7, r8, r9, r11, r12, r13, r15, r16)
            java.lang.Object r2 = r1.b
            bdm r2 = (defpackage.bdm) r2
            bcp r2 = r2.f
            java.lang.String r3 = "processor"
            defpackage.jnu.d(r2, r3)
            java.lang.Object r3 = r1.b
            bdm r3 = (defpackage.bdm) r3
            androidx.work.impl.WorkDatabase r3 = r3.d
            java.lang.String r4 = "workDatabase"
            defpackage.jnu.d(r3, r4)
            java.lang.Object r4 = r1.b
            bdm r4 = (defpackage.bdm) r4
            bam r4 = r4.c
            java.lang.String r5 = "configuration"
            defpackage.jnu.d(r4, r5)
            java.lang.Object r5 = r1.b
            bdm r5 = (defpackage.bdm) r5
            java.util.List r5 = r5.e
            java.lang.String r6 = "schedulers"
            defpackage.jnu.d(r5, r6)
            java.lang.Object r6 = r1.a
            java.lang.String r7 = r0.b
            bhf r8 = r3.A()
            bhe r8 = r8.b(r7)
            if (r8 == 0) goto L_0x01d1
            bbx r9 = r8.c
            boolean r9 = r9.a()
            if (r9 != 0) goto L_0x01ce
            boolean r9 = r8.d()
            boolean r10 = r0.d()
            r9 = r9 ^ r10
            if (r9 != 0) goto L_0x019f
            boolean r2 = r2.c(r7)
            if (r2 != 0) goto L_0x017d
            java.util.Iterator r9 = r5.iterator()
        L_0x016d:
            boolean r10 = r9.hasNext()
            if (r10 == 0) goto L_0x017d
            java.lang.Object r10 = r9.next()
            bcr r10 = (defpackage.bcr) r10
            r10.b(r7)
            goto L_0x016d
        L_0x017d:
            bmu r6 = (defpackage.bmu) r6
            java.lang.Object r6 = r6.a
            bdp r9 = new bdp
            r17 = r9
            r18 = r3
            r19 = r8
            r20 = r0
            r21 = r5
            r22 = r7
            r23 = r6
            r24 = r2
            r17.<init>(r18, r19, r20, r21, r22, r23, r24)
            r3.n(r9)
            if (r2 != 0) goto L_0x01ce
            defpackage.bct.a(r4, r3, r5)
            goto L_0x01ce
        L_0x019f:
            wl r2 = defpackage.wl.h
            java.lang.UnsupportedOperationException r3 = new java.lang.UnsupportedOperationException
            java.lang.StringBuilder r4 = new java.lang.StringBuilder
            java.lang.String r5 = "Can't update "
            r4.<init>(r5)
            java.lang.Object r5 = r2.a(r8)
            java.lang.String r5 = (java.lang.String) r5
            r4.append(r5)
            java.lang.String r5 = " Worker to "
            r4.append(r5)
            java.lang.Object r0 = r2.a(r0)
            java.lang.String r0 = (java.lang.String) r0
            r4.append(r0)
            java.lang.String r0 = " Worker. Update operation must preserve worker's type."
            r4.append(r0)
            java.lang.String r0 = r4.toString()
            r3.<init>(r0)
            throw r3
        L_0x01ce:
            jkd r0 = defpackage.jkd.a
            return r0
        L_0x01d1:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.String r2 = "Worker with "
            java.lang.String r3 = " doesn't exist"
            java.lang.String r2 = defpackage.a.ap(r7, r2, r3)
            r0.<init>(r2)
            throw r0
        L_0x01df:
            java.lang.UnsupportedOperationException r0 = new java.lang.UnsupportedOperationException
            java.lang.String r2 = "Can't update OneTimeWorker to Periodic Worker. Update operation must preserve worker's type."
            r0.<init>(r2)
            throw r0
        L_0x01e7:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            java.lang.String r4 = "WorkSpec with "
            r3.<init>(r4)
            java.lang.String r2 = r2.a
            r3.append(r2)
            java.lang.String r2 = ", that matches a name \""
            r3.append(r2)
            java.lang.Object r2 = r1.c
            java.lang.String r2 = (java.lang.String) r2
            r3.append(r2)
            java.lang.String r2 = "\", wasn't found"
            r3.append(r2)
            java.lang.String r2 = r3.toString()
            r0.<init>(r2)
            throw r0
        L_0x020e:
            java.lang.UnsupportedOperationException r0 = new java.lang.UnsupportedOperationException
            java.lang.String r2 = "Can't apply UPDATE policy to the chains of work."
            r0.<init>(r2)
            throw r0
        L_0x0216:
            java.lang.Object r0 = r1.c
            java.lang.Object r2 = r1.b
            java.lang.Object r3 = r1.a
            am r3 = (defpackage.am) r3
            cm r3 = r3.d
            android.view.ViewGroup r2 = (android.view.ViewGroup) r2
            r3.f(r2, r0)
            jkd r0 = defpackage.jkd.a
            return r0
        L_0x0228:
            java.lang.Object r0 = r1.b
            java.lang.Object r3 = r1.c
            java.lang.Object r4 = r1.a
            java.util.List r4 = defpackage.jji.m(r4)
            bcy r5 = new bcy
            java.lang.String r3 = (java.lang.String) r3
            bdm r0 = (defpackage.bdm) r0
            r5.<init>(r0, r3, r2, r4)
            defpackage.bic.a(r5)
            jkd r0 = defpackage.jkd.a
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bdq.a():java.lang.Object");
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bdq(Object obj, Object obj2, Object obj3, int i) {
        super(0);
        this.d = i;
        this.a = obj;
        this.b = obj2;
        this.c = obj3;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bdq(Object obj, Object obj2, Object obj3, int i, byte[] bArr) {
        super(0);
        this.d = i;
        this.b = obj;
        this.c = obj2;
        this.a = obj3;
    }
}
