package defpackage;

/* renamed from: cql  reason: default package */
/* compiled from: PG */
public final class cql {
    public static final cql a = new cql(0, 0, 0);
    public final long b;
    public final long c;
    public final long d;

    public cql() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof cql) {
            cql cql = (cql) obj;
            if (this.b == cql.b && this.c == cql.c && this.d == cql.d) {
                return true;
            }
            return false;
        }
        return false;
    }

    public final int hashCode() {
        long j = this.d;
        long j2 = this.b;
        long j3 = this.c;
        return ((int) (j ^ (j >>> 32))) ^ ((((((int) (j2 ^ (j2 >>> 32))) ^ 1000003) * 1000003) ^ ((int) ((j3 >>> 32) ^ j3))) * 1000003);
    }

    public final String toString() {
        return "SchedStat{cpuTimeNs=" + this.b + ", runDelayNs=" + this.c + ", runCount=" + this.d + "}";
    }

    public cql(long j, long j2, long j3) {
        this.b = j;
        this.c = j2;
        this.d = j3;
    }
}
