package defpackage;

import java.util.concurrent.Executor;

/* renamed from: etm  reason: default package */
/* compiled from: PG */
public final class etm extends esv {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl");
    public final Executor b;
    public final cyw c;
    private final eoz d;
    private final eoz e;
    private final dku f;
    private final bmu g;

    public etm(cyw cyw, Executor executor, dku dku, bmu bmu, eoz eoz, eoz eoz2) {
        this.c = cyw;
        this.b = executor;
        this.f = dku;
        this.g = bmu;
        this.d = eoz;
        this.e = eoz2;
    }

    /* JADX WARNING: type inference failed for: r1v7, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v8, types: [java.lang.Object, jjk] */
    public final synchronized void a(dzc dzc, jix jix) {
        hby hby = (hby) ((hby) a.f().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl", "activateAudioRequestClient", 90, "AudioServiceImpl.java");
        ehg ehg = dzc.b;
        if (ehg == null) {
            ehg = ehg.c;
        }
        hby.u("#audio# activate client(%s) remote request", fbi.s(ehg));
        ejl B = this.c.B(dzc);
        eoz eoz = this.e;
        int i = B.b;
        dza dza = B.a;
        dpz dpz = new dpz(this, B, 12);
        hme hme = ((eir) dza).a;
        hme.getClass();
        jqs jqs = (jqs) eoz.b.b();
        jqs.getClass();
        Executor executor = (Executor) eoz.a.b();
        executor.getClass();
        eto eto = new eto(i, hme, dpz, jix, jqs, executor);
        if (eto.g.c()) {
            ((hby) eto.a.c().h(hdg.a, "ALT.GrpcClientRespSndr").j("com/google/android/libraries/search/audio/service/impl/StreamAudioClientResponseSender", "start", 50, "StreamAudioClientResponseSender.kt")).r("#audio# skipping sending due to already started");
            return;
        }
        ((hby) eto.a.c().h(hdg.a, "ALT.GrpcClientRespSndr").j("com/google/android/libraries/search/audio/service/impl/StreamAudioClientResponseSender", "start", 53, "StreamAudioClientResponseSender.kt")).s("#audio# starting sending client(%d) info", eto.b);
        job.S(eto.e, (jlv) null, (jqt) null, new edc(eto, (jlr) null, 19), 3);
    }

    public final synchronized void b(esy esy, jix jix) {
        ebf ebf = esy.a;
        if (ebf == null) {
            ebf = ebf.c;
        }
        int i = ebf.b;
        ((hby) ((hby) a.f().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl", "deactivateClient", 108, "AudioServiceImpl.java")).s("#audio# deactivateClient, token=%d", i);
        this.c.J(i, dzf.DEACTIVATED_CLIENT_REQUESTED);
        jix.c(htc.a);
        jix.a();
    }

    public final synchronized void c(dzk dzk, jix jix) {
        ((hby) ((hby) a.f().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl", "getAudioRequestReadOnlyListeningSession", 357, "AudioServiceImpl.java")).s("#audio# get audio read only session(%s)", dzk.a);
        ebv z = ((cyw) this.f.a).z(dzk);
        dyy dyy = ((ejy) z).a;
        hme c2 = dyy.c();
        jna jna = ejw.a;
        hme a2 = ((dyx) dyy.d()).a();
        dyy dyy2 = ((ejy) z).a;
        this.g.v(c2, jna, a2, ejw.b, dyy2.b(), grh.h(Integer.valueOf(dzk.a)), new bpw(13), jix).k();
    }

    public final synchronized void d(etb etb, jix jix) {
        int i;
        ejm ejm;
        dzl dzl;
        grh grh = gqd.a;
        int i2 = etb.b;
        if (i2 == 2) {
            i = ((ebf) etb.c).b;
            ((hby) ((hby) a.f().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl", "startListening", 161, "AudioServiceImpl.java")).s("#audio# audio client(%d) start remote request", i);
        } else if (i2 == 4) {
            grh h = grh.h((dzl) etb.c);
            if (etb.b == 4) {
                dzl = (dzl) etb.c;
            } else {
                dzl = dzl.d;
            }
            ebf ebf = dzl.b;
            if (ebf == null) {
                ebf = ebf.c;
            }
            int i3 = ebf.b;
            ((hby) ((hby) a.f().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl", "startListening", 165, "AudioServiceImpl.java")).s("#audio# audio client(%d)'s route start remote request", i3);
            grh grh2 = h;
            i = i3;
            grh = grh2;
        } else {
            ((hby) ((hby) a.g().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl", "startListening", 167, "AudioServiceImpl.java")).r("#audio# not enough params for start remote request");
            jix.a();
            return;
        }
        if (grh.f()) {
            cyw cyw = this.c;
            Object b2 = grh.b();
            dze dze = etb.d;
            if (dze == null) {
                dze = dze.k;
            }
            ejm = cyw.E((dzl) b2, dze);
        } else {
            cyw cyw2 = this.c;
            dze dze2 = etb.d;
            if (dze2 == null) {
                dze2 = dze.k;
            }
            ejm = cyw2.D(i, dze2);
        }
        bmu bmu = this.g;
        dyy dyy = ejm.a;
        bmu.v(dyy.c(), ejw.a, ((dyx) dyy.d()).a(), ejw.b, dyy.b(), grh.h(Integer.valueOf(ejm.b)), new mp((Object) this, i, (Object) ejm, 5), jix).k();
    }

    public final synchronized void e(esz esz, jix jix) {
        ejp ejp;
        if ((esz.a & 2) != 0) {
            eac eac = esz.c;
            if (eac == null) {
                eac = eac.c;
            }
            hby hby = (hby) ((hby) a.f().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl", "startListeningForHotword", 245, "AudioServiceImpl.java");
            ebw ebw = eac.b;
            if (ebw == null) {
                ebw = ebw.c;
            }
            hby.s("#audio# hotword client's route(%d) start remote request", ebw.b);
            cyw cyw = this.c;
            ebn ebn = esz.b;
            if (ebn == null) {
                ebn = ebn.h;
            }
            ejp = cyw.I(eac, ebn);
        } else {
            ((hby) ((hby) a.f().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl", "startListeningForHotword", 252, "AudioServiceImpl.java")).r("#audio# hotword client start remote request");
            cyw cyw2 = this.c;
            ebn ebn2 = esz.b;
            if (ebn2 == null) {
                ebn2 = ebn.h;
            }
            ejp = cyw2.H(ebn2);
        }
        bmu bmu = this.g;
        ebl ebl = ejp.a;
        hme c2 = ebl.c();
        jna jna = ejw.c;
        hme a2 = ebl.a().a();
        jna jna2 = ejw.d;
        ebn ebn3 = esz.b;
        if (ebn3 == null) {
            ebn3 = ebn.h;
        }
        dyt dyt = ebn3.d;
        if (dyt == null) {
            dyt = dyt.l;
        }
        int i = ejp.b;
        bmu.v(c2, jna, a2, jna2, hfc.K(dyt), grh.h(Integer.valueOf(i)), new dpz(this, ejp, 13), jix).k();
    }

    public final synchronized void f(etd etd, jix jix) {
        ebf ebf = etd.b;
        if (ebf == null) {
            ebf = ebf.c;
        }
        int i = ebf.b;
        ebw ebw = etd.c;
        if (ebw == null) {
            ebw = ebw.c;
        }
        int i2 = ebw.b;
        ((hby) ((hby) a.f().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl", "stopListening", 206, "AudioServiceImpl.java")).s("#audio# stopListening sessionToken: %d", i2);
        hfc.T(this.c.C(i, i2).a.a().a(), gof.g(new etj(jix, i, i2)), this.b);
    }

    public final synchronized void g(ebw ebw, jix jix) {
        int i = ebw.b;
        ((hby) ((hby) a.f().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl", "stopListeningForHotword", 279, "AudioServiceImpl.java")).s("#audio# stopListeningForHotword sessionToken: %d", i);
        hfc.T(this.c.G(i).a.b().a(), gof.g(new etk(jix, i)), this.b);
    }

    public final synchronized void h(ebw ebw, jix jix) {
        int i = ebw.b;
        ((hby) ((hby) a.f().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl", "stopListeningForSeamlessMode", 315, "AudioServiceImpl.java")).s("#audio# stopListeningForSeamlessMode sessionToken: %d", i);
        ekf d2 = this.c.G(i).a.d();
        hfc.T(hfc.K((ebp) ((htk) d2.b).r()), gof.g(new etl(this, d2, jix, i, 0)), this.b);
    }

    public final synchronized void i(eth eth, jix jix) {
        ((hby) ((hby) a.f().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl", "updateHotwordRoute", 139, "AudioServiceImpl.java")).r("#audio# update hotword client's route remote request");
        eab eab = eth.a;
        if (eab == null) {
            eab = eab.d;
        }
        cyw cyw = this.c;
        eoz eoz = this.d;
        ejo F = cyw.F(eab);
        eoz.p(F.b, F.a.a(), new dpz(this, F, 14), jix).d();
    }

    public final synchronized void j(eti eti, jix jix) {
        ebf ebf = eti.a;
        if (ebf == null) {
            ebf = ebf.c;
        }
        int i = ebf.b;
        ((hby) ((hby) a.f().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl", "updateRoute", 118, "AudioServiceImpl.java")).s("#audio# update client's(%d) route remote request", i);
        cyw cyw = this.c;
        dzn dzn = eti.b;
        if (dzn == null) {
            dzn = dzn.c;
        }
        ejk A = cyw.A(i, dzn);
        this.d.p(A.b, A.a.a(), new mp((Object) this, i, (Object) A, 4), jix).d();
    }

    public final void k(jix jix) {
        jix.c(htc.a);
    }
}
