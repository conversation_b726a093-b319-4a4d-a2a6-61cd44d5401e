package defpackage;

/* renamed from: ete  reason: default package */
/* compiled from: PG */
public final class ete extends htq implements hvb {
    public static final ete c;
    private static volatile hvh d;
    public int a = 0;
    public Object b;

    static {
        ete ete = new ete();
        c = ete;
        htq.z(ete.class, ete);
    }

    private ete() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(c, "\u0004\u0003\u0001\u0000\u0002\u0004\u0003\u0000\u0000\u0000\u0002<\u0000\u0003<\u0000\u0004<\u0000", new Object[]{"b", "a", ebf.class, dzg.class, htc.class});
        } else if (i2 == 3) {
            return new ete();
        } else {
            if (i2 == 4) {
                return new htk((htq) c);
            }
            if (i2 == 5) {
                return c;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = d;
            if (hvh == null) {
                synchronized (ete.class) {
                    hvh = d;
                    if (hvh == null) {
                        hvh = new htl(c);
                        d = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
