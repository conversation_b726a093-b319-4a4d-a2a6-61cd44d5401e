package defpackage;

/* renamed from: cuj  reason: default package */
/* compiled from: PG */
public final class cuj {
    public final String a;
    private final grh b;
    private final grh c;

    public cuj() {
        throw null;
    }

    public static cui a() {
        cui cui = new cui((byte[]) null);
        cui.a = 1;
        return cui;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof cuj) {
            cuj cuj = (cuj) obj;
            if (!this.a.equals(cuj.a) || !this.b.equals(cuj.b) || !this.c.equals(cuj.c)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return ((((((this.a.hashCode() ^ 1000003) * 1000003) ^ **********) * 1000003) ^ **********) * 1000003) ^ 1237;
    }

    public final String toString() {
        grh grh = this.c;
        String valueOf = String.valueOf(this.b);
        String valueOf2 = String.valueOf(grh);
        return "RemoveFileGroupRequest{groupName=" + this.a + ", accountOptional=" + valueOf + ", variantIdOptional=" + valueOf2 + ", pendingOnly=false}";
    }

    public cuj(String str, grh grh, grh grh2) {
        this.a = str;
        this.b = grh;
        this.c = grh2;
    }
}
