package defpackage;

import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;

/* renamed from: bfx  reason: default package */
/* compiled from: PG */
public final class bfx {
    public static final String a = bbk.b("NetworkStateTracker");

    public static final bex a(ConnectivityManager connectivityManager) {
        boolean z;
        boolean z2;
        jnu.e(connectivityManager, "<this>");
        NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
        boolean z3 = true;
        if (activeNetworkInfo == null || !activeNetworkInfo.isConnected()) {
            z = false;
        } else {
            z = true;
        }
        jnu.e(connectivityManager, "<this>");
        try {
            jnu.e(connectivityManager, "<this>");
            Network activeNetwork = connectivityManager.getActiveNetwork();
            jnu.e(connectivityManager, "<this>");
            NetworkCapabilities networkCapabilities = connectivityManager.getNetworkCapabilities(activeNetwork);
            if (networkCapabilities != null) {
                z2 = networkCapabilities.hasCapability(16);
                boolean isActiveNetworkMetered = connectivityManager.isActiveNetworkMetered();
                if (activeNetworkInfo == null || activeNetworkInfo.isRoaming()) {
                    z3 = false;
                }
                return new bex(z, z2, isActiveNetworkMetered, z3);
            }
        } catch (SecurityException e) {
            bbk.a().d(a, "Unable to validate active network", e);
        }
        z2 = false;
        boolean isActiveNetworkMetered2 = connectivityManager.isActiveNetworkMetered();
        z3 = false;
        return new bex(z, z2, isActiveNetworkMetered2, z3);
    }
}
