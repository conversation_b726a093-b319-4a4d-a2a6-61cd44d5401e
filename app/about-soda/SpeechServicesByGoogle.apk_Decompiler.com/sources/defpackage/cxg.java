package defpackage;

/* renamed from: cxg  reason: default package */
/* compiled from: PG */
public final class cxg {
    public final ctg a;
    public final csx b;

    public cxg() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof cxg) {
            cxg cxg = (cxg) obj;
            if (!this.a.equals(cxg.a) || !this.b.equals(cxg.b)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        int i2;
        ctg ctg = this.a;
        if (ctg.B()) {
            i = ctg.i();
        } else {
            int i3 = ctg.memoizedHashCode;
            if (i3 == 0) {
                i3 = ctg.i();
                ctg.memoizedHashCode = i3;
            }
            i = i3;
        }
        csx csx = this.b;
        if (csx.B()) {
            i2 = csx.i();
        } else {
            int i4 = csx.memoizedHashCode;
            if (i4 == 0) {
                i4 = csx.i();
                csx.memoizedHashCode = i4;
            }
            i2 = i4;
        }
        return ((i ^ 1000003) * 1000003) ^ i2;
    }

    public final String toString() {
        csx csx = this.b;
        String obj = this.a.toString();
        String obj2 = csx.toString();
        return "GroupKeyAndGroup{groupKey=" + obj + ", dataFileGroup=" + obj2 + "}";
    }

    public cxg(ctg ctg, csx csx) {
        if (ctg != null) {
            this.a = ctg;
            if (csx != null) {
                this.b = csx;
                return;
            }
            throw new NullPointerException("Null dataFileGroup");
        }
        throw new NullPointerException("Null groupKey");
    }
}
