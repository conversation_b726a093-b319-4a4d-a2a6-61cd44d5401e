package defpackage;

import android.car.drivingstate.CarUxRestrictions;

/* renamed from: bmg  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bmg implements bny {
    public final /* synthetic */ bml a;

    public /* synthetic */ bmg(bml bml) {
        this.a = bml;
    }

    public final void a(CarUxRestrictions carUxRestrictions) {
        bml bml = this.a;
        boolean d = bml.d();
        bml.g = carUxRestrictions;
        if (bml.d() != d) {
            bml.c();
        }
    }
}
