package defpackage;

/* renamed from: eup  reason: default package */
/* compiled from: PG */
public final class eup {
    public final String a;
    public final int b;
    public final int c;
    public final int d;

    public eup(String str, int i, int i2, int i3) {
        this.a = str;
        this.b = i;
        this.c = i2;
        this.d = i3;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof eup)) {
            return false;
        }
        eup eup = (eup) obj;
        if (jnu.i(this.a, eup.a) && this.b == eup.b && this.c == eup.c && this.d == eup.d) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (((((this.a.hashCode() * 31) + this.b) * 31) + this.c) * 31) + this.d;
    }

    public final String toString() {
        return "Config(name=" + this.a + ", encodingFormat=" + this.b + ", channelCount=" + this.c + ", sampleRateHz=" + this.d + ")";
    }
}
