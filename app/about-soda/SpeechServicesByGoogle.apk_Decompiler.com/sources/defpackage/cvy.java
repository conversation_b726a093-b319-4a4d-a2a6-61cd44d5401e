package defpackage;

import android.content.Context;
import android.content.pm.PackageManager;
import android.net.Uri;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;

/* renamed from: cvy  reason: default package */
/* compiled from: PG */
public final class cvy {
    public final Context a;
    public final cuk b;
    public final cvz c;
    public final grh d;
    public final Executor e;
    public final grh f;
    public final grh g;
    public final cxz h;
    public final cyk i;
    public final dbw j;
    public final cqx k;
    public final bzj l = new bzj();
    public final kjd m;
    private final Executor n;

    public cvy(Context context, cyk cyk, cuk cuk, cvz cvz, dbw dbw, cqx cqx, grh grh, Executor executor, grh grh2, kjd kjd, grh grh3, cxz cxz, Executor executor2) {
        this.a = context;
        this.i = cyk;
        this.b = cuk;
        this.c = cvz;
        this.j = dbw;
        this.k = cqx;
        this.d = grh;
        this.e = executor;
        this.n = executor2;
        this.f = grh2;
        this.m = kjd;
        this.g = grh3;
        this.h = cxz;
    }

    public static void A(cyk cyk, csx csx, csv csv, int i2) {
        htk l2 = hij.j.l();
        if (!l2.b.B()) {
            l2.u();
        }
        hij hij = (hij) l2.b;
        hij.b = hfc.ay(i2);
        hij.a |= 1;
        String str = csx.c;
        if (!l2.b.B()) {
            l2.u();
        }
        htq htq = l2.b;
        hij hij2 = (hij) htq;
        str.getClass();
        hij2.a |= 2;
        hij2.c = str;
        int i3 = csx.e;
        if (!htq.B()) {
            l2.u();
        }
        htq htq2 = l2.b;
        hij hij3 = (hij) htq2;
        hij3.a |= 4;
        hij3.d = i3;
        long j2 = csx.r;
        if (!htq2.B()) {
            l2.u();
        }
        htq htq3 = l2.b;
        hij hij4 = (hij) htq3;
        hij4.a |= 128;
        hij4.h = j2;
        String str2 = csx.s;
        if (!htq3.B()) {
            l2.u();
        }
        htq htq4 = l2.b;
        hij hij5 = (hij) htq4;
        str2.getClass();
        hij5.a |= 256;
        hij5.i = str2;
        String str3 = csv.b;
        if (!htq4.B()) {
            l2.u();
        }
        hij hij6 = (hij) l2.b;
        str3.getClass();
        hij6.a |= 8;
        hij6.e = str3;
        cyk.a((hij) l2.r());
    }

    public static grh a(csx csx, csx csx2) {
        if (csx2.r != csx.r) {
            return grh.h(hix.NEW_BUILD_ID);
        }
        if (!csx2.s.equals(csx.s)) {
            return grh.h(hix.NEW_VARIANT_ID);
        }
        if (csx2.e != csx.e) {
            return grh.h(hix.NEW_VERSION_NUMBER);
        }
        if (!s(csx, csx2)) {
            return grh.h(hix.DIFFERENT_FILES);
        }
        if (csx2.j != csx.j) {
            return grh.h(hix.DIFFERENT_STALE_LIFETIME);
        }
        if (csx2.k != csx.k) {
            return grh.h(hix.DIFFERENT_EXPIRATION_DATE);
        }
        csz csz = csx2.l;
        if (csz == null) {
            csz = csz.f;
        }
        csz csz2 = csx.l;
        if (csz2 == null) {
            csz2 = csz.f;
        }
        if (!csz.equals(csz2)) {
            return grh.h(hix.DIFFERENT_DOWNLOAD_CONDITIONS);
        }
        int x = a.x(csx2.i);
        int i2 = 1;
        if (x == 0) {
            x = 1;
        }
        int x2 = a.x(csx.i);
        if (x2 == 0) {
            x2 = 1;
        }
        if (x != x2) {
            return grh.h(hix.DIFFERENT_ALLOWED_READERS);
        }
        int A = cqh.A(csx2.q);
        if (A == 0) {
            A = 1;
        }
        int A2 = cqh.A(csx.q);
        if (A2 != 0) {
            i2 = A2;
        }
        if (A != i2) {
            return grh.h(hix.DIFFERENT_DOWNLOAD_POLICY);
        }
        kbi kbi = csx2.u;
        if (kbi == null) {
            kbi = kbi.c;
        }
        kbi kbi2 = csx.u;
        if (kbi2 == null) {
            kbi2 = kbi.c;
        }
        if (!kbi.equals(kbi2)) {
            return grh.h(hix.DIFFERENT_EXPERIMENT_INFO);
        }
        return gqd.a;
    }

    public static boolean s(csx csx, csx csx2) {
        return csx.n.equals(csx2.n);
    }

    public static boolean u(ctl ctl, long j2) {
        if (j2 > ctl.e) {
            return true;
        }
        return false;
    }

    public static final void v(List list, ctg ctg) {
        cyh.i("%s downloadFileGroup %s %s can't finish!", "FileGroupManager", ctg.b, ctg.c);
        cry.b(list, ctg.b);
        cyh.g("%s: An unknown error has occurred during download", "FileGroupManager");
        kml a2 = csi.a();
        a2.b = csh.UNKNOWN_ERROR;
        throw a2.a();
    }

    public static void z(int i2, cyk cyk, csx csx) {
        cyk.e(i2, csx.c, csx.e, csx.r, csx.s);
    }

    /* access modifiers changed from: package-private */
    public final hme B(ctg ctg, csx csx, hko hko, cxi cxi) {
        boolean z;
        ctg ctg2 = ctg;
        csx csx2 = csx;
        cyh.e("%s: Verify group: %s, remove pending version: %s", "FileGroupManager", csx2.c, true);
        htk htk = (htk) ctg2.C(5);
        htk.x(ctg2);
        if (!htk.b.B()) {
            htk.u();
        }
        ctg ctg3 = (ctg) htk.b;
        ctg ctg4 = ctg.g;
        ctg3.a |= 8;
        ctg3.e = true;
        ctg ctg5 = (ctg) htk.r();
        htk htk2 = (htk) ctg2.C(5);
        htk2.x(ctg2);
        if (!htk2.b.B()) {
            htk2.u();
        }
        ctg ctg6 = (ctg) htk2.b;
        ctg6.a |= 8;
        ctg6.e = false;
        ctg ctg7 = (ctg) htk2.r();
        csw csw = csx2.b;
        if (csw == null) {
            csw = csw.i;
        }
        if ((csw.a & 4) != 0) {
            z = true;
        } else {
            z = false;
        }
        long c2 = cqx.c();
        csw csw2 = csx2.b;
        if (csw2 == null) {
            csw2 = csw.i;
        }
        htk htk3 = (htk) csw2.C(5);
        htk3.x(csw2);
        if (!htk3.b.B()) {
            htk3.u();
        }
        csw csw3 = (csw) htk3.b;
        csw3.a |= 4;
        csw3.d = c2;
        csw csw4 = (csw) htk3.r();
        htk htk4 = (htk) csx2.C(5);
        htk4.x(csx2);
        if (!htk4.b.B()) {
            htk4.u();
        }
        csx csx3 = (csx) htk4.b;
        csw4.getClass();
        csx3.b = csw4;
        csx3.a |= 1;
        return czw.e(h(csx2)).g(new dgq(this, cxi, csx, ctg7, hko, ctg5, (csx) htk4.r(), z, 1), this.e).g(new cvn((Object) this, (htq) csx2, 2), this.e);
    }

    public final gxv b(csx csx) {
        gxr gxr = new gxr();
        Uri i2 = cqx.i(this.a, this.f, csx);
        for (csv csv : csx.n) {
            gxr.d(csv, cqx.h(i2, csv));
        }
        return gxr.a();
    }

    public final gxv c(gxv gxv, gxv gxv2) {
        gxr gxr = new gxr();
        hbp k2 = gxv2.entrySet().iterator();
        while (k2.hasNext()) {
            Map.Entry entry = (Map.Entry) k2.next();
            if (entry.getValue() != null && gxv.containsKey(entry.getKey())) {
                Uri uri = (Uri) gxv.get(entry.getKey());
                Uri uri2 = (Uri) entry.getValue();
                try {
                    Uri a2 = czi.a(this.a, uri);
                    if (!this.m.j(uri) || !a2.toString().equals(uri2.toString())) {
                        cyh.i("%s verifyIsolatedFileUris unable to get isolated file uri! %s %s", "FileGroupManager", uri, uri2);
                    } else {
                        gxr.d((csv) entry.getKey(), uri);
                    }
                } catch (IOException unused) {
                    cyh.i("%s verifyIsolatedFileUris unable to get isolated file uri! %s %s", "FileGroupManager", uri, uri2);
                }
            }
        }
        return gxr.a();
    }

    public final hme d(csx csx) {
        if (!csx.m) {
            return hma.a;
        }
        try {
            cqx.K(this.a, this.f, csx, this.m);
            huf huf = csx.n;
            if (fvf.O(huf, new agz(3)).f()) {
                return hfc.J(new UnsupportedOperationException("Preserve File Paths is invalid with Android Blob Sharing"));
            }
            hme L = ftd.L(k(csx), new bsx(this, (List) huf, b(csx), 13), this.e);
            ftd.M(L, new ema(this, csx, 1), this.e);
            return L;
        } catch (IOException e2) {
            kml a2 = csi.a();
            a2.b = csh.UNABLE_TO_REMOVE_SYMLINK_STRUCTURE;
            a2.c = "Unable to cleanup symlink structure";
            a2.d = e2;
            return hfc.J(a2.a());
        }
    }

    public final hme e(ctg ctg, csz csz, hko hko) {
        AtomicReference atomicReference = new AtomicReference();
        return ftd.G(q(g(ctg, false), new cvj(this, ctg, atomicReference, csz, hko, 1)), Exception.class, new bsx(this, atomicReference, ctg, 12, (byte[]) null), this.e);
    }

    public final hme f(ctl ctl, csv csv, csx csx) {
        if (ctl.d) {
            return hfc.K(cvw.FILE_ALREADY_SHARED);
        }
        if (csv.n.isEmpty()) {
            return hfc.K(cvw.FILE_SHARING_CHECKSUM_NOT_PROVIDED);
        }
        Context context = this.a;
        String str = csv.n;
        kjd kjd = this.m;
        return p(ftd.J(new cyz(context, str, kjd, csv, csx), this.n), new amv(13));
    }

    public final hme g(ctg ctg, boolean z) {
        htk htk = (htk) ctg.C(5);
        htk.x(ctg);
        if (!htk.b.B()) {
            htk.u();
        }
        ctg ctg2 = (ctg) htk.b;
        ctg ctg3 = ctg.g;
        ctg2.a |= 8;
        ctg2.e = z;
        return this.c.g((ctg) htk.r());
    }

    public final hme h(csx csx) {
        return i(csx, false, false, 0, csx.n.size());
    }

    public final hme i(csx csx, boolean z, boolean z2, int i2, int i3) {
        if (i2 < i3) {
            csv csv = (csv) csx.n.get(i2);
            if (!cqx.p(csv)) {
                return czw.e(j(csv, csx)).g(new cvv(this, csv, csx, z, z2, i2, i3), this.e);
            }
            return i(csx, z, z2, i2 + 1, i3);
        } else if (z) {
            return hfc.K(cvx.FAILED);
        } else {
            if (z2) {
                return hfc.K(cvx.PENDING);
            }
            return hfc.K(cvx.DOWNLOADED);
        }
    }

    /* JADX WARNING: type inference failed for: r0v4, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme j(csv csv, csx csx) {
        int x = a.x(csx.i);
        if (x == 0) {
            x = 1;
        }
        ctj t = cqh.t(csv, x);
        dbw dbw = this.j;
        return czw.e(ftd.L(dbw.n(t), new bub(13), dbw.l)).d(cwz.class, new bpt(this, csx, 14, (byte[]) null), this.e);
    }

    /* access modifiers changed from: package-private */
    public final hme k(csx csx) {
        gxr gxr = new gxr();
        gxr gxr2 = new gxr();
        for (csv csv : csx.n) {
            if (cqx.p(csv)) {
                gxr.d(csv, Uri.parse(csv.c));
            } else {
                int x = a.x(csx.i);
                if (x == 0) {
                    x = 1;
                }
                gxr2.d(csv, cqh.t(csv, x));
            }
        }
        gxv a2 = gxr2.a();
        return czw.e(this.j.m(gyo.n(a2.values()))).f(new btb(a2, gxr, 6, (byte[]) null), this.e);
    }

    public final hme l(csx csx, csv csv, ctj ctj) {
        return ftd.G(this.j.n(ctj), cwz.class, new cvd(this, ctj, csx, csv, 9), this.e);
    }

    public final hme m(hko hko) {
        return q(this.c.d(), new cvp(this, (List) new ArrayList(), hko, 4));
    }

    public final hme n(ctg ctg, csi csi, long j2, String str) {
        htk l2 = hig.k.l();
        String str2 = ctg.b;
        if (!l2.b.B()) {
            l2.u();
        }
        htq htq = l2.b;
        hig hig = (hig) htq;
        str2.getClass();
        hig.a |= 1;
        hig.b = str2;
        String str3 = ctg.c;
        if (!htq.B()) {
            l2.u();
        }
        htq htq2 = l2.b;
        hig hig2 = (hig) htq2;
        str3.getClass();
        hig2.a |= 4;
        hig2.d = str3;
        if (!htq2.B()) {
            l2.u();
        }
        htq htq3 = l2.b;
        hig hig3 = (hig) htq3;
        hig3.a |= 64;
        hig3.h = j2;
        if (!htq3.B()) {
            l2.u();
        }
        hig hig4 = (hig) l2.b;
        str.getClass();
        hig4.a |= 128;
        hig4.i = str;
        cvz cvz = this.c;
        htk htk = (htk) ctg.C(5);
        htk.x(ctg);
        if (!htk.b.B()) {
            htk.u();
        }
        ctg ctg2 = (ctg) htk.b;
        ctg2.a |= 8;
        ctg2.e = false;
        return q(cvz.g((ctg) htk.r()), new bsx(this, l2, csi, 9, (byte[]) null));
    }

    /* JADX WARNING: type inference failed for: r2v3, types: [java.lang.Object, cxa] */
    /* JADX WARNING: type inference failed for: r0v3, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme o(csx csx, int i2, int i3) {
        int i4 = 1;
        if (i2 >= i3) {
            return hfc.K(true);
        }
        csv csv = (csv) csx.n.get(i2);
        if (cqx.p(csv)) {
            return o(csx, i2 + 1, i3);
        }
        int x = a.x(csx.i);
        if (x != 0) {
            i4 = x;
        }
        ctj t = cqh.t(csv, i4);
        dbw dbw = this.j;
        return q(ftd.L(dbw.j.e(t), new cwq(dbw, t, 5), dbw.l), new cyj(this, csx, i2, i3, 1));
    }

    public final hme p(hme hme, gqx gqx) {
        return ftd.K(hme, gqx, this.e);
    }

    public final hme q(hme hme, hko hko) {
        return ftd.L(hme, hko, this.e);
    }

    /* JADX WARNING: type inference failed for: r6v1, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme r(csx csx, csv csv, ctj ctj, long j2) {
        dbw dbw = this.j;
        return q(ftd.L(dbw.n(ctj), new cww(dbw, j2, ctj), dbw.l), new bsx(this, (htq) csv, (htq) csx, 10));
    }

    public final boolean t(String str) {
        try {
            this.a.getPackageManager().getApplicationInfo(str, 0);
            return true;
        } catch (PackageManager.NameNotFoundException unused) {
            return false;
        }
    }

    public final hme w(csx csx, csv csv, ctj ctj, ctl ctl, int i2) {
        return q(y(csx, csv, ctl, ctj, csv.n, csx.k, i2), new cvr(this, i2, csx, csv, ctj));
    }

    public final hme x(csx csx, csv csv, ctj ctj, ctl ctl, int i2) {
        csv csv2 = csv;
        String str = csv2.n;
        long j2 = csx.k;
        int x = a.x(ctj.e);
        if (x == 0) {
            x = 1;
        }
        Uri u = cqx.u(this.a, x, ctl.b, csv2.f, this.b, this.f, false);
        if (u != null) {
            czw e2 = czw.e(ftd.J(new cyy(this.a, str, this.m, u, csv, csx, 0), this.n));
            cvm cvm = r0;
            cvm cvm2 = new cvm(this, csx, csv, ctl, ctj, str, j2, i2);
            return e2.g(cvm, this.e);
        }
        cyh.g("%s: Failed to get file uri!", "FileGroupManager");
        throw new cza(28, "Failed to get local file uri");
    }

    /* access modifiers changed from: package-private */
    public final hme y(csx csx, csv csv, ctl ctl, ctj ctj, String str, long j2, int i2) {
        ctl ctl2 = ctl;
        long j3 = j2;
        if (!ctl2.d || u(ctl2, j3)) {
            csx csx2 = csx;
            csv csv2 = csv;
            int i3 = i2;
            long max = Math.max(j3, ctl2.e);
            Context context = this.a;
            kjd kjd = this.m;
            String str2 = str;
            long j4 = max;
            hme J2 = ftd.J(new cyx(context, str2, j4, kjd, csv, csx), this.n);
            cvh cvh = r0;
            cvh cvh2 = new cvh(this, ctj, str2, j4, csv, csx, i2);
            return q(J2, cvh);
        }
        A(this.i, csx, csv, i2);
        return hfc.K(true);
    }
}
