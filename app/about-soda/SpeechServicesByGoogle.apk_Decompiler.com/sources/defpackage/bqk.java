package defpackage;

/* renamed from: bqk  reason: default package */
/* compiled from: PG */
public final class bqk implements htu {
    static final htu a = new bqk(0);
    public static final htu b = new bqk(1);
    public static final htu c = new bqk(2);
    public static final htu d = new bqk(3);
    public static final htu e = new bqk(4);
    public static final htu f = new bqk(5);
    public static final htu g = new bqk(6);
    public static final htu h = new bqk(7);
    public static final htu i = new bqk(8);
    public static final htu j = new bqk(9);
    public static final htu k = new bqk(10);
    public static final htu l = new bqk(11);
    public static final htu m = new bqk(12);
    public static final htu n = new bqk(13);
    public static final htu o = new bqk(14);
    public static final htu p = new bqk(15);
    public static final htu q = new bqk(16);
    public static final htu r = new bqk(17);
    public static final htu s = new bqk(18);
    public static final htu t = new bqk(19);
    public static final htu u = new bqk(20);
    private final /* synthetic */ int v;

    private bqk(int i2) {
        this.v = i2;
    }

    public final boolean a(int i2) {
        cse cse;
        switch (this.v) {
            case 0:
                if (bql.b(i2) != null) {
                    return true;
                }
                break;
            case 1:
                return a.K(i2);
            case 2:
                return a.L(i2);
            case 3:
                if (btt.b(i2) != null) {
                    return true;
                }
                return false;
            case 4:
                if (adh.h(i2) != 0) {
                    return true;
                }
                return false;
            case 5:
                if (btu.b(i2) != null) {
                    return true;
                }
                return false;
            case 6:
                if (btv.b(i2) != null) {
                    return true;
                }
                return false;
            case 7:
                if (btw.b(i2) != null) {
                    return true;
                }
                return false;
            case 8:
                return a.L(i2);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                if (csd.b(i2) != null) {
                    return true;
                }
                return false;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                if (i2 == 0) {
                    cse = cse.BLOCK_DOWNLOAD_IN_LOW_STORAGE;
                } else if (i2 == 1) {
                    cse = cse.BLOCK_DOWNLOAD_LOWER_THRESHOLD;
                } else if (i2 != 2) {
                    cse = null;
                } else {
                    cse = cse.EXTREMELY_LOW_THRESHOLD;
                }
                if (cse != null) {
                    return true;
                }
                return false;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                if (css.b(i2) != null) {
                    return true;
                }
                return false;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return a.K(i2);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return a.K(i2);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return a.K(i2);
            case 15:
                return a.M(i2);
            case 16:
                return a.K(i2);
            case 17:
                return a.K(i2);
            case 18:
                return a.M(i2);
            case 19:
                return a.M(i2);
            default:
                if (ctf.b(i2) != null) {
                    return true;
                }
                break;
        }
        return false;
    }
}
