package defpackage;

import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import androidx.core.graphics.drawable.IconCompat;
import com.google.android.tts.R;
import java.util.ArrayList;
import java.util.concurrent.Callable;

/* renamed from: ctu  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ctu implements hko {
    public final /* synthetic */ cuf a;
    public final /* synthetic */ csk b;

    public /* synthetic */ ctu(cuf cuf, csk csk) {
        this.a = cuf;
        this.b = csk;
    }

    public final hme a(Object obj) {
        String str;
        Object obj2;
        boolean z;
        ki[] kiVarArr;
        cvc cvc = (cvc) obj;
        int b2 = cvc.b() - 1;
        if (b2 == 1) {
            return cvc.c();
        }
        if (b2 == 2) {
            return hfc.K(cvc.a());
        }
        csk csk = this.b;
        cuf cuf = this.a;
        csx d = cvc.d();
        Context context = cuf.a;
        ((NotificationManager) context.getSystemService(NotificationManager.class)).createNotificationChannel(new NotificationChannel("download-notification-channel-id", context.getResources().getString(R.string.mdd_download_notification_channel_name), 3));
        htk l = ctg.g.l();
        if (!l.b.B()) {
            l.u();
        }
        String str2 = csk.a;
        ctg ctg = (ctg) l.b;
        str2.getClass();
        ctg.a |= 1;
        ctg.b = str2;
        String packageName = cuf.a.getPackageName();
        if (!l.b.B()) {
            l.u();
        }
        ctg ctg2 = (ctg) l.b;
        packageName.getClass();
        ctg2.a |= 2;
        ctg2.c = packageName;
        ctg ctg3 = (ctg) l.r();
        cuv a2 = cuv.a(str2);
        csz csz = d.l;
        if (csz == null) {
            csz = csz.f;
        }
        int x = a.x(csz.c);
        if (x == 0) {
            x = 1;
        }
        if (csk.f.f()) {
            try {
                x = a.x(cqh.R((csf) csk.f.b()).c);
                if (x == 0) {
                    x = 1;
                }
            } catch (hui unused) {
            }
        }
        int i = x - 1;
        if (i == 0 || i == 2) {
            str = cuf.a.getResources().getString(R.string.mdd_notification_download_paused_wifi);
        } else {
            str = cuf.a.getResources().getString(R.string.mdd_notification_download_paused);
        }
        String str3 = str;
        sp spVar = new sp(cuf.a);
        String str4 = csk.a;
        Context context2 = cuf.a;
        int i2 = csk.h;
        grh grh = csk.d;
        cuv a3 = cuv.a(str4);
        sk d2 = cqh.d(context2);
        d2.f((String) grh.d(str4));
        d2.e((String) csk.e.d(csk.a));
        d2.g = null;
        d2.i(17301633);
        d2.g(true);
        d2.h(i2, 0, false);
        String str5 = csk.a;
        int i3 = csk.j;
        int hashCode = str5.hashCode();
        if (i3 == 2) {
            Context context3 = cuf.a;
            grh grh2 = cuf.f;
            String str6 = a3.a;
            Intent intent = new Intent(context3, (Class) grh2.b());
            intent.setPackage(context3.getPackageName());
            intent.putExtra("cancel-action", hashCode);
            intent.putExtra("key", str6);
            fvf.aw(true, "Cannot set any dangerous parts of intent to be mutable.");
            fvf.aw(true, "Cannot use Intent.FILL_IN_ACTION unless the action is marked as mutable.");
            fvf.aw(true, "Cannot use Intent.FILL_IN_DATA unless the data is marked as mutable.");
            fvf.aw(true, "Cannot use Intent.FILL_IN_CATEGORIES unless the category is marked as mutable.");
            fvf.aw(true, "Cannot use Intent.FILL_IN_CLIP_DATA unless the clip data is marked as mutable.");
            if (intent.getComponent() != null) {
                z = true;
            } else {
                z = false;
            }
            fvf.aw(z, "Must set component on Intent.");
            if (ewp.a(0, 1)) {
                fvf.aw(!ewp.a(1140850688, 67108864), "Cannot set mutability flags if PendingIntent.FLAG_IMMUTABLE is set.");
            } else {
                fvf.aw(ewp.a(1140850688, 67108864), "Must set PendingIntent.FLAG_IMMUTABLE for SDK >= 23 if no parts of intent are mutable.");
            }
            Intent intent2 = new Intent(intent);
            if (!ewp.a(1140850688, 67108864)) {
                if (intent2.getPackage() == null) {
                    intent2.setPackage(intent2.getComponent().getPackageName());
                }
                if (!ewp.a(0, 3) && intent2.getAction() == null) {
                    intent2.setAction("");
                }
                if (!ewp.a(0, 9) && intent2.getCategories() == null) {
                    intent2.addCategory("");
                }
                if (!ewp.a(0, 5) && intent2.getData() == null) {
                    intent2.setDataAndType(Uri.EMPTY, "*/*");
                }
                if (!ewp.a(0, 17) && intent2.getClipData() == null) {
                    intent2.setClipData(ewp.a);
                }
            }
            PendingIntent m = PendingIntent.getForegroundService(context3, hashCode, intent2, 1140850688);
            String string = context3.getResources().getString(R.string.mdd_notification_action_cancel);
            fvf.aP(m);
            IconCompat c = IconCompat.c(17301642);
            Bundle bundle = new Bundle();
            CharSequence c2 = sk.c(string);
            ArrayList arrayList = new ArrayList();
            ArrayList arrayList2 = new ArrayList();
            if (!arrayList.isEmpty()) {
                ki[] kiVarArr2 = (ki[]) arrayList.toArray(new ki[arrayList.size()]);
            }
            if (arrayList2.isEmpty()) {
                kiVarArr = null;
            } else {
                kiVarArr = (ki[]) arrayList2.toArray(new ki[arrayList2.size()]);
            }
            d2.b.add(new si(c, c2, m, bundle, kiVarArr));
            spVar.a(hashCode, d2.a());
        }
        cuc cuc = new cuc(cuf, a3, csk, d2, spVar, hashCode, str3);
        ((czp) cuf.e.b()).g(csk.a, cuc);
        try {
            if (csk.f.f()) {
                obj2 = grh.h(cqh.R((csf) csk.f.b()));
            } else {
                obj2 = gqd.a;
            }
            hmf hmf = new hmf((Callable) new ctw(1));
            czw f = czw.e(hmf).g(new bsx((Object) cuf, (Object) ctg3, obj2, 4), cuf.d).g(new bsx((Object) cuf, ctg3, (Object) csk, 5), cuf.d).f(new amv(12), cuf.d);
            hme L = ftd.L(cuf.i.e(a2.a, f), new bpt(hmf, f, 7, (byte[]) null), cuf.d);
            ftd.M(L, new cmk(cuc, 2), cuf.d);
            return L;
        } catch (hui e) {
            return hfc.J(e);
        }
    }
}
