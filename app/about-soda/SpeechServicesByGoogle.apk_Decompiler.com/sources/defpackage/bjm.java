package defpackage;

import android.os.SystemClock;
import android.view.ViewTreeObserver;
import com.android.car.ui.FocusParkingView;

/* renamed from: bjm  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bjm implements ViewTreeObserver.OnTouchModeChangeListener {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ bjm(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    public final void onTouchModeChanged(boolean z) {
        if (this.b != 0) {
            if (!z) {
                bjk bjk = (bjk) this.a;
                bjk.D = SystemClock.uptimeMillis() + bjk.C;
            }
        } else if (!z) {
            FocusParkingView focusParkingView = (FocusParkingView) this.a;
            focusParkingView.d = SystemClock.uptimeMillis() + focusParkingView.c;
        }
    }
}
