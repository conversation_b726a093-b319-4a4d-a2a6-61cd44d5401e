package defpackage;

import android.app.Application;
import android.bluetooth.BluetoothHeadset;
import android.content.Context;
import android.location.Location;
import android.location.LocationManager;
import android.os.Parcelable;
import android.text.TextUtils;
import android.util.Log;
import android.util.LruCache;
import android.view.Menu;
import android.view.MenuInflater;
import android.view.MenuItem;
import androidx.wear.ambient.AmbientMode;
import androidx.wear.ambient.AmbientModeSupport;
import j$.util.Optional;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/* renamed from: bmu  reason: default package */
/* compiled from: PG */
public final class bmu {
    public static bmu d;
    public final Object a;
    public final Object b;
    public final Object c;

    public bmu(BluetoothHeadset bluetoothHeadset, efw efw, List list) {
        jnu.e(list, "devices");
        this.a = bluetoothHeadset;
        this.c = efw;
        this.b = list;
    }

    private final Method N(Class cls) {
        Object obj;
        Method O = O(cls, (String) this.c, (Class[]) this.b);
        if (O == null || (obj = this.a) == null) {
            return O;
        }
        if (!((Class) obj).isAssignableFrom(O.getReturnType())) {
            return null;
        }
        return O;
    }

    private static Method O(Class cls, String str, Class[] clsArr) {
        if (cls == null) {
            return null;
        }
        try {
            if ((cls.getModifiers() & 1) == 0) {
                return O(cls.getSuperclass(), str, clsArr);
            }
            Method method = cls.getMethod(str, clsArr);
            try {
                if (1 != (method.getModifiers() & 1)) {
                    return null;
                }
            } catch (NoSuchMethodException unused) {
            }
            return method;
        } catch (NoSuchMethodException unused2) {
            return null;
        }
    }

    /* JADX WARNING: type inference failed for: r2v1, types: [java.lang.Object, java.lang.Runnable] */
    public final void A(AmbientMode.AmbientController ambientController) {
        ((CopyOnWriteArrayList) this.a).add(ambientController);
        this.b.run();
    }

    /* JADX WARNING: type inference failed for: r0v2, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v4, types: [java.lang.Object, java.lang.Runnable] */
    public final void B(AmbientMode.AmbientController ambientController) {
        ((CopyOnWriteArrayList) this.a).remove(ambientController);
        if (((vy) this.c.remove(ambientController)) == null) {
            this.b.run();
            return;
        }
        throw null;
    }

    public final Object C(Object obj, Object... objArr) {
        Method N;
        try {
            N = N(obj.getClass());
            if (N != null) {
                return N.invoke(obj, objArr);
            }
            Object obj2 = this.c;
            String valueOf = String.valueOf(obj);
            throw new AssertionError("Method " + ((String) obj2) + " not supported for object " + valueOf);
        } catch (IllegalAccessException e) {
            AssertionError assertionError = new AssertionError("Unexpectedly could not call: ".concat(N.toString()));
            assertionError.initCause(e);
            throw assertionError;
        } catch (InvocationTargetException e2) {
            Throwable targetException = e2.getTargetException();
            if (targetException instanceof RuntimeException) {
                throw ((RuntimeException) targetException);
            }
            AssertionError assertionError2 = new AssertionError("Unexpected exception");
            assertionError2.initCause(targetException);
            throw assertionError2;
        }
    }

    public final boolean D(Object obj) {
        if (N(obj.getClass()) != null) {
            return true;
        }
        return false;
    }

    public final void E(Object obj, Object... objArr) {
        try {
            Method N = N(obj.getClass());
            if (N != null) {
                try {
                    N.invoke(obj, objArr);
                } catch (IllegalAccessException unused) {
                }
            }
        } catch (InvocationTargetException e) {
            Throwable targetException = e.getTargetException();
            if (targetException instanceof RuntimeException) {
                throw ((RuntimeException) targetException);
            }
            AssertionError assertionError = new AssertionError("Unexpected exception");
            assertionError.initCause(targetException);
            throw assertionError;
        }
    }

    public final IllegalArgumentException F() {
        Object obj = this.a;
        Object obj2 = this.b;
        Object obj3 = this.c;
        String valueOf = String.valueOf(obj3);
        String valueOf2 = String.valueOf(obj2);
        String valueOf3 = String.valueOf(obj3);
        String valueOf4 = String.valueOf(obj);
        return new IllegalArgumentException("Multiple entries with same key: " + valueOf + "=" + valueOf2 + " and " + valueOf3 + "=" + valueOf4);
    }

    /* JADX WARNING: type inference failed for: r0v0, types: [java.lang.Object, jjk] */
    public final hme G(hme hme, hko hko) {
        Set<gke> set = (Set) this.a.b();
        gym i = gyo.i(set.size());
        for (gke gkb : set) {
            i.c(new gkb(hko, gkb, 0));
        }
        return ((bzl) this.c).F(new fpo((Object) hme, 19), i.g());
    }

    public final void H(hme hme, Object obj, gcc gcc, grh grh, Executor executor) {
        ftd.V();
        hfc.T(hme, gof.g(new gbz(this, obj, gcc, grh, 0)), executor);
    }

    public final void I(hme hme, Object obj) {
        H(hme, obj, gcc.a, gqd.a, hld.a);
    }

    public final void J(hme hme, Object obj) {
        H(hme, obj, gcc.b, gqd.a, hld.a);
    }

    /* JADX WARNING: type inference failed for: r3v0, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r6v1, types: [java.util.Map, java.lang.Object] */
    public final void K(Object obj, AmbientModeSupport.AmbientController ambientController) {
        fnk.c();
        a.w(obj, "Cannot subscribe with a null key");
        ov ovVar = new ov();
        synchronized (this.b) {
            gyf gyf = new gyf();
            gyi gyi = (gyi) this.a.get(obj);
            if (gyi != null) {
                gyf.b(gyi);
            }
            gyf.c(ambientController);
            this.a.put(obj, gyf.a());
        }
        ou ouVar = new ou(ovVar);
        while (ouVar.hasNext()) {
            ((gcb) ouVar.next()).a();
        }
    }

    /* JADX WARNING: type inference failed for: r2v0, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v1, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r10v2, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r10v9, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: Removed duplicated region for block: B:18:0x0063  */
    /* JADX WARNING: Removed duplicated region for block: B:19:0x006d  */
    /* JADX WARNING: Removed duplicated region for block: B:22:0x008a  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void L(java.lang.Object r9, androidx.wear.ambient.AmbientModeSupport.AmbientController r10) {
        /*
            r8 = this;
            defpackage.fnk.c()
            java.lang.String r0 = "Cannot unsubscribe from a null key"
            defpackage.a.w(r9, r0)
            ov r0 = new ov
            r0.<init>()
            java.lang.Object r1 = r8.b
            monitor-enter(r1)
            java.lang.Object r2 = r8.a     // Catch:{ all -> 0x00a6 }
            java.lang.Object r2 = r2.get(r9)     // Catch:{ all -> 0x00a6 }
            gyi r2 = (defpackage.gyi) r2     // Catch:{ all -> 0x00a6 }
            java.lang.String r3 = "Failed to remove a subscription key. State is corrupted."
            r4 = 1
            r5 = 0
            if (r2 == 0) goto L_0x0020
            r6 = r4
            goto L_0x0021
        L_0x0020:
            r6 = r5
        L_0x0021:
            defpackage.fvf.aG(r6, r3)     // Catch:{ all -> 0x00a6 }
            java.lang.Object r3 = r8.a     // Catch:{ all -> 0x00a6 }
            gyf r6 = new gyf     // Catch:{ all -> 0x00a6 }
            r6.<init>()     // Catch:{ all -> 0x00a6 }
            r6.b(r2)     // Catch:{ all -> 0x00a6 }
            int r2 = r2.a(r10)     // Catch:{ all -> 0x00a6 }
            int r2 = r2 + -1
            haf r7 = r6.a     // Catch:{ all -> 0x00a6 }
            j$.util.Objects.requireNonNull(r7)     // Catch:{ all -> 0x00a6 }
            if (r2 != 0) goto L_0x004d
            boolean r2 = r6.c     // Catch:{ all -> 0x00a6 }
            if (r2 != 0) goto L_0x004c
            hag r2 = new hag     // Catch:{ all -> 0x00a6 }
            haf r7 = r6.a     // Catch:{ all -> 0x00a6 }
            r2.<init>(r7)     // Catch:{ all -> 0x00a6 }
            r6.a = r2     // Catch:{ all -> 0x00a6 }
            r6.c = r4     // Catch:{ all -> 0x00a6 }
            r2 = r5
            goto L_0x005c
        L_0x004c:
            r2 = r5
        L_0x004d:
            boolean r4 = r6.b     // Catch:{ all -> 0x00a6 }
            if (r4 == 0) goto L_0x005c
            haf r4 = new haf     // Catch:{ all -> 0x00a6 }
            haf r7 = r6.a     // Catch:{ all -> 0x00a6 }
            r4.<init>((defpackage.haf) r7)     // Catch:{ all -> 0x00a6 }
            r6.a = r4     // Catch:{ all -> 0x00a6 }
            r6.c = r5     // Catch:{ all -> 0x00a6 }
        L_0x005c:
            r6.b = r5     // Catch:{ all -> 0x00a6 }
            defpackage.fvf.aP(r10)     // Catch:{ all -> 0x00a6 }
            if (r2 != 0) goto L_0x006d
            haf r2 = r6.a     // Catch:{ all -> 0x00a6 }
            int r4 = defpackage.fvf.aa(r10)     // Catch:{ all -> 0x00a6 }
            r2.f(r10, r4)     // Catch:{ all -> 0x00a6 }
            goto L_0x0075
        L_0x006d:
            haf r4 = r6.a     // Catch:{ all -> 0x00a6 }
            defpackage.fvf.aP(r10)     // Catch:{ all -> 0x00a6 }
            r4.o(r10, r2)     // Catch:{ all -> 0x00a6 }
        L_0x0075:
            gyi r10 = r6.a()     // Catch:{ all -> 0x00a6 }
            r3.put(r9, r10)     // Catch:{ all -> 0x00a6 }
            java.lang.Object r10 = r8.a     // Catch:{ all -> 0x00a6 }
            java.lang.Object r10 = r10.get(r9)     // Catch:{ all -> 0x00a6 }
            gyi r10 = (defpackage.gyi) r10     // Catch:{ all -> 0x00a6 }
            boolean r10 = r10.isEmpty()     // Catch:{ all -> 0x00a6 }
            if (r10 == 0) goto L_0x008f
            java.lang.Object r10 = r8.a     // Catch:{ all -> 0x00a6 }
            r10.remove(r9)     // Catch:{ all -> 0x00a6 }
        L_0x008f:
            monitor-exit(r1)     // Catch:{ all -> 0x00a6 }
            ou r9 = new ou
            r9.<init>(r0)
        L_0x0095:
            boolean r10 = r9.hasNext()
            if (r10 == 0) goto L_0x00a5
            java.lang.Object r10 = r9.next()
            gcb r10 = (defpackage.gcb) r10
            r10.b()
            goto L_0x0095
        L_0x00a5:
            return
        L_0x00a6:
            r9 = move-exception
            monitor-exit(r1)     // Catch:{ all -> 0x00a6 }
            throw r9
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bmu.L(java.lang.Object, androidx.wear.ambient.AmbientModeSupport$AmbientController):void");
    }

    /* JADX WARNING: type inference failed for: r0v0, types: [gsb, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v4, types: [gsb, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r9v1, types: [hme, java.lang.Object] */
    public final void M(cxi cxi, AmbientModeSupport.AmbientController ambientController, Parcelable parcelable) {
        boolean z;
        boolean z2;
        boolean z3;
        bw bwVar = (bw) this.b.a();
        boolean z4 = false;
        if (bwVar.Y() || bwVar.v) {
            z = false;
        } else {
            z = true;
        }
        fvf.aG(z, "Called when state-loss is possible.");
        gqa gqa = (gqa) this.a.a();
        gqa.f();
        pb pbVar = gqa.a;
        if (pbVar.a) {
            pc.b(pbVar);
        }
        int i = pbVar.d;
        int i2 = 0;
        while (true) {
            if (i2 >= i) {
                i2 = -1;
                break;
            } else if (pbVar.c[i2] == ambientController) {
                break;
            } else {
                i2++;
            }
        }
        if (i2 != -1) {
            z2 = true;
        } else {
            z2 = false;
        }
        fvf.aG(z2, "Callback not registered.");
        int a2 = gqa.a.a(i2);
        gqb gqb = new gqb(a2, parcelable, cxi.a);
        gqa.f();
        if (pc.a(gqa.a, a2) != null) {
            z3 = true;
        } else {
            z3 = false;
        }
        fvf.aG(z3, "Callback not registered.");
        if (gqa.c != null) {
            z4 = true;
        }
        fvf.aG(z4, "Listening outside of callback window.");
        fvf.aG(gqa.g, "Executing tasks from lifecycle methods is disallowed since it can result in unnecessary operations during configuration changes or other lifecycle transitions.");
        fvf.aG(!gqa.i, "Calling listen() from FutureCallbackRegistry callbacks is disallowed because hopping through the UI thread adds extra latency. Chain the new Future to the original Future using Futures.transformAsync instead.");
        gpw gpw = gqa.d;
        gqb.c.c(new ghg(gqb, 15), hld.a);
        gqa.b.add(gqb);
        gqb.c(gqa);
        if (!gqb.b()) {
            AmbientModeSupport.AmbientController ambientController2 = (AmbientModeSupport.AmbientController) pc.a(gqa.a, a2);
            gqa.h(gqb);
        }
    }

    public final String a() {
        String uuid = ((UUID) this.c).toString();
        jnu.d(uuid, "id.toString()");
        return uuid;
    }

    public final int b() {
        return ((int[]) this.a).length;
    }

    public final bmu c(int i) {
        int[] iArr = new int[i];
        int[] iArr2 = new int[i];
        int i2 = 0;
        int i3 = 0;
        while (i3 < i) {
            iArr[i3] = ((Random) this.b).nextInt(((int[]) this.a).length + 1);
            int i4 = i3 + 1;
            int nextInt = ((Random) this.b).nextInt(i4);
            iArr2[i3] = iArr2[nextInt];
            iArr2[nextInt] = i3;
            i3 = i4;
        }
        Arrays.sort(iArr);
        int[] iArr3 = new int[(((int[]) this.a).length + i)];
        int i5 = 0;
        int i6 = 0;
        while (true) {
            int[] iArr4 = (int[]) this.a;
            if (i2 >= iArr4.length + i) {
                return new bmu(iArr3, new Random(((Random) this.b).nextLong()));
            }
            if (i5 >= i || i6 != iArr[i5]) {
                int i7 = i6 + 1;
                int i8 = iArr4[i6];
                iArr3[i2] = i8;
                if (i8 >= 0) {
                    iArr3[i2] = i8 + i;
                }
                i6 = i7;
            } else {
                iArr3[i2] = iArr2[i5];
                i5++;
            }
            i2++;
        }
    }

    public final void d(Menu menu, MenuInflater menuInflater) {
        Iterator it = ((CopyOnWriteArrayList) this.a).iterator();
        while (it.hasNext()) {
            ((bw) ((AmbientMode.AmbientController) it.next()).a).S(menu, menuInflater);
        }
    }

    public final void e(Menu menu) {
        Iterator it = ((CopyOnWriteArrayList) this.a).iterator();
        while (it.hasNext()) {
            ((bw) ((AmbientMode.AmbientController) it.next()).a).U(menu);
        }
    }

    public final boolean f(MenuItem menuItem) {
        Iterator it = ((CopyOnWriteArrayList) this.a).iterator();
        while (it.hasNext()) {
            if (((bw) ((AmbientMode.AmbientController) it.next()).a).T(menuItem)) {
                return true;
            }
        }
        return false;
    }

    public final void g(qh qhVar) {
        ((ArrayList) this.a).clear();
        int size = qhVar.aI.size();
        for (int i = 0; i < size; i++) {
            qg qgVar = (qg) qhVar.aI.get(i);
            if (qgVar.M() == 3 || qgVar.N() == 3) {
                ((ArrayList) this.a).add(qgVar);
            }
        }
        qhVar.c();
    }

    public final boolean h(rg rgVar, qg qgVar, int i) {
        boolean z;
        boolean z2;
        ((qo) this.b).i = qgVar.M();
        ((qo) this.b).j = qgVar.N();
        ((qo) this.b).a = qgVar.j();
        qo qoVar = (qo) this.b;
        qoVar.b = qgVar.h();
        qoVar.g = false;
        qoVar.h = i;
        int i2 = qoVar.i;
        int i3 = qoVar.j;
        if (i2 != 3 || qgVar.X <= 0.0f) {
            z = false;
        } else {
            z = true;
        }
        if (i3 != 3 || qgVar.X <= 0.0f) {
            z2 = false;
        } else {
            z2 = true;
        }
        if (z && qgVar.u[0] == 4) {
            qoVar.i = 1;
        }
        if (z2 && qgVar.u[1] == 4) {
            qoVar.j = 1;
        }
        rgVar.a(qgVar, qoVar);
        qgVar.C(((qo) this.b).c);
        qgVar.x(((qo) this.b).d);
        qo qoVar2 = (qo) this.b;
        qgVar.F = qoVar2.f;
        qgVar.u(qoVar2.e);
        qo qoVar3 = (qo) this.b;
        qoVar3.h = 0;
        return qoVar3.g;
    }

    public final void i(qh qhVar, int i, int i2, int i3) {
        int i4 = qhVar.ac;
        int i5 = qhVar.ad;
        qhVar.B(0);
        qhVar.A(0);
        qhVar.C(i2);
        qhVar.x(i3);
        qhVar.B(i4);
        qhVar.A(i5);
        Object obj = this.c;
        ((qh) obj).b = i;
        ((qn) obj).T();
    }

    public final Location j(String str) {
        try {
            if (((LocationManager) this.b).isProviderEnabled(str)) {
                return ((LocationManager) this.b).getLastKnownLocation(str);
            }
            return null;
        } catch (Exception unused) {
            return null;
        }
    }

    /* JADX WARNING: type inference failed for: r0v0, types: [java.util.Map, java.lang.Object] */
    public final String k() {
        return dds.f(this.c.values(), new cwr(12));
    }

    /* JADX WARNING: type inference failed for: r1v0, types: [java.util.Map, java.lang.Object] */
    public final List l(ddk ddk) {
        ArrayList arrayList = new ArrayList();
        for (dhl dhl : this.c.values()) {
            if (dhl.b.equals(ddk)) {
                arrayList.add(dhl);
            }
        }
        return arrayList;
    }

    /* JADX WARNING: type inference failed for: r3v2, types: [java.util.Map, java.lang.Object] */
    public final void m(dih dih) {
        ddk ddk = dih.e;
        if (l(ddk).isEmpty()) {
            dih.f.run();
            this.a.remove(ddk);
        }
    }

    /* JADX WARNING: type inference failed for: r0v0, types: [java.util.Map, java.lang.Object] */
    public final boolean n(String str) {
        return this.c.containsKey(str);
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [hco, hdc] */
    /* JADX WARNING: type inference failed for: r1v2, types: [java.util.Map, java.lang.Object] */
    public final hme o(Object obj) {
        hmr hmr;
        ((hdc) dcs.a.l().j("com/google/android/libraries/micore/superpacks/base/TaskRunner", "cancel", 167, "TaskRunner.java")).u("Canceling task for: %s", q(obj));
        synchronized (this.a) {
            ddq ddq = (ddq) this.a.get(obj);
            if (ddq != null) {
                synchronized (ddq) {
                    hmr = ddq.b;
                    if (hmr == null) {
                        ddq.c.b();
                        ddq.b = new hmr();
                        hmr = ddq.b;
                    }
                }
                return hmr;
            }
            hme K = hfc.K((Object) null);
            return K;
        }
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [hco, hdc] */
    /* JADX WARNING: type inference failed for: r1v2, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v2, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r8v0, types: [hmh, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r11v4, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme p(Object obj, ddp ddp) {
        ddq ddq;
        ((hdc) dcs.a.l().j("com/google/android/libraries/micore/superpacks/base/TaskRunner", "execute", 118, "TaskRunner.java")).u("Executing task for %s", q(obj));
        synchronized (this.a) {
            ddq = (ddq) this.a.get(obj);
            if (ddq == null) {
                ddq = new ddq();
                this.a.put(obj, ddq);
                hme bf = this.b.bf(new ddo(this, ddp, ddq, obj, 0));
                ddq.a = hfc.Z(bf).b(new cvs(this, obj, ddq, bf, 3), this.b);
            }
        }
        return hfc.L(ddq.a);
    }

    public final String q(Object obj) {
        Object obj2 = this.c;
        String obj3 = obj.toString();
        if (obj2 != null) {
            return a.as(obj3, (String) obj2, ":");
        }
        return obj3;
    }

    public final void r(String str) {
        synchronized (this) {
            for (Map.Entry entry : ((LruCache) this.b).snapshot().entrySet()) {
                Object obj = ((kmx) entry.getValue()).b;
                if (obj == null || ((dci) ((dbl) obj).e()).a.equals(str)) {
                    ((LruCache) this.b).remove((File) entry.getKey());
                }
            }
        }
    }

    /* JADX WARNING: type inference failed for: r2v1, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v1, types: [hco, hdc] */
    /* JADX WARNING: Code restructure failed: missing block: B:7:0x001c, code lost:
        return null;
     */
    /* JADX WARNING: Removed duplicated region for block: B:14:0x0033  */
    /* JADX WARNING: Removed duplicated region for block: B:37:0x009f  */
    /* JADX WARNING: Removed duplicated region for block: B:41:0x00a5 A[SYNTHETIC, Splitter:B:41:0x00a5] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.dbl s(java.lang.String r9, int r10, java.io.File r11) {
        /*
            r8 = this;
            monitor-enter(r8)
            java.lang.Object r0 = r8.b     // Catch:{ all -> 0x00a8 }
            android.util.LruCache r0 = (android.util.LruCache) r0     // Catch:{ all -> 0x00a8 }
            java.lang.Object r0 = r0.get(r11)     // Catch:{ all -> 0x00a8 }
            kmx r0 = (defpackage.kmx) r0     // Catch:{ all -> 0x00a8 }
            boolean r1 = r11.exists()     // Catch:{ all -> 0x00a8 }
            r2 = 0
            if (r1 != 0) goto L_0x001d
            if (r0 == 0) goto L_0x001b
            java.lang.Object r9 = r8.b     // Catch:{ all -> 0x00a8 }
            android.util.LruCache r9 = (android.util.LruCache) r9     // Catch:{ all -> 0x00a8 }
            r9.remove(r11)     // Catch:{ all -> 0x00a8 }
        L_0x001b:
            monitor-exit(r8)     // Catch:{ all -> 0x00a8 }
            return r2
        L_0x001d:
            if (r0 == 0) goto L_0x0030
            long r3 = r11.lastModified()     // Catch:{ all -> 0x00a8 }
            long r5 = r0.a     // Catch:{ all -> 0x00a8 }
            int r1 = (r3 > r5 ? 1 : (r3 == r5 ? 0 : -1))
            if (r1 <= 0) goto L_0x0031
            java.lang.Object r0 = r8.b     // Catch:{ all -> 0x00a8 }
            android.util.LruCache r0 = (android.util.LruCache) r0     // Catch:{ all -> 0x00a8 }
            r0.remove(r11)     // Catch:{ all -> 0x00a8 }
        L_0x0030:
            r0 = r2
        L_0x0031:
            if (r0 != 0) goto L_0x009b
            hha r0 = new hha     // Catch:{ all -> 0x00a8 }
            r0.<init>()     // Catch:{ all -> 0x00a8 }
            java.io.FileInputStream r1 = new java.io.FileInputStream     // Catch:{ all -> 0x007c }
            r1.<init>(r11)     // Catch:{ all -> 0x007c }
            r0.c(r1)     // Catch:{ all -> 0x007c }
            java.lang.Object r2 = r8.a     // Catch:{ all -> 0x007c }
            java.lang.Object r2 = r2.get(r9)     // Catch:{ all -> 0x007c }
            daz r2 = (defpackage.daz) r2     // Catch:{ all -> 0x007c }
            if (r2 != 0) goto L_0x004c
            java.lang.Object r2 = r8.c     // Catch:{ all -> 0x007c }
        L_0x004c:
            hdf r3 = defpackage.dcs.a     // Catch:{ all -> 0x007c }
            hco r3 = r3.l()     // Catch:{ all -> 0x007c }
            java.lang.String r4 = "com/google/android/libraries/micore/superpacks/ManifestFileCache"
            java.lang.String r5 = "parseManifestFile"
            java.lang.String r6 = "ManifestFileCache.java"
            r7 = 137(0x89, float:1.92E-43)
            hco r3 = r3.j(r4, r5, r7, r6)     // Catch:{ all -> 0x007c }
            hdc r3 = (defpackage.hdc) r3     // Catch:{ all -> 0x007c }
            java.lang.String r4 = "Parsing manifest file %s with parser: %s"
            java.lang.String r5 = defpackage.dds.e(r11)     // Catch:{ all -> 0x007c }
            r3.C(r4, r5, r2)     // Catch:{ all -> 0x007c }
            daz r2 = (defpackage.daz) r2     // Catch:{ all -> 0x007c }
            dbl r9 = r2.a(r1, r9, r10)     // Catch:{ all -> 0x007c }
            kmx r10 = new kmx     // Catch:{ all -> 0x007c }
            long r1 = r11.lastModified()     // Catch:{ all -> 0x007c }
            r10.<init>((defpackage.dbl) r9, (long) r1)     // Catch:{ all -> 0x007c }
            r0.close()     // Catch:{ dbc -> 0x0089 }
            goto L_0x0093
        L_0x007c:
            r9 = move-exception
            java.lang.Class<dbc> r10 = defpackage.dbc.class
            java.lang.RuntimeException r9 = r0.b(r9, r10)     // Catch:{ all -> 0x0084 }
            throw r9     // Catch:{ all -> 0x0084 }
        L_0x0084:
            r9 = move-exception
            r0.close()     // Catch:{ dbc -> 0x0089 }
            throw r9     // Catch:{ dbc -> 0x0089 }
        L_0x0089:
            r9 = move-exception
            kmx r10 = new kmx     // Catch:{ all -> 0x00a8 }
            long r0 = r11.lastModified()     // Catch:{ all -> 0x00a8 }
            r10.<init>((defpackage.dbc) r9, (long) r0)     // Catch:{ all -> 0x00a8 }
        L_0x0093:
            r0 = r10
            java.lang.Object r9 = r8.b     // Catch:{ all -> 0x00a8 }
            android.util.LruCache r9 = (android.util.LruCache) r9     // Catch:{ all -> 0x00a8 }
            r9.put(r11, r0)     // Catch:{ all -> 0x00a8 }
        L_0x009b:
            java.lang.Object r9 = r0.c     // Catch:{ all -> 0x00a8 }
            if (r9 != 0) goto L_0x00a5
            java.lang.Object r9 = r0.b     // Catch:{ all -> 0x00a8 }
            monitor-exit(r8)     // Catch:{ all -> 0x00a8 }
            dbl r9 = (defpackage.dbl) r9
            return r9
        L_0x00a5:
            java.lang.Throwable r9 = (java.lang.Throwable) r9     // Catch:{ all -> 0x00a8 }
            throw r9     // Catch:{ all -> 0x00a8 }
        L_0x00a8:
            r9 = move-exception
            monitor-exit(r8)     // Catch:{ all -> 0x00a8 }
            throw r9
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bmu.s(java.lang.String, int, java.io.File):dbl");
    }

    public final void t(iat iat) {
        Optional optional;
        int J2;
        int J3;
        if (TextUtils.equals(iat.b, "SODA_CLEARCUT")) {
            try {
                hsq hsq = iat.c;
                hte a2 = hte.a();
                iep iep = iep.c;
                hsu k = hsq.k();
                htq n = iep.n();
                hvp b2 = hvj.a.b(n);
                b2.l(n, hsv.p(k), a2);
                b2.g(n);
                k.z(0);
                htq.D(n);
                iep iep2 = (iep) n;
                ieq ieq = iep2.b;
                if (ieq == null) {
                    ieq = ieq.d;
                }
                ieo ieo = ieq.b;
                if (ieo == null) {
                    ieo = ieo.b;
                }
                iec iec = ieo.a;
                if (iec == null) {
                    iec = iec.d;
                }
                iar b3 = iar.b(iec.a);
                if (b3 == null) {
                    b3 = iar.UNKNOWN_HOST_APP_ID;
                }
                int ordinal = b3.ordinal();
                if (ordinal == 8 || ordinal == 13 || ordinal == 14) {
                    ((hby) ((hby) ((hby) cnn.a.f()).g(30, TimeUnit.SECONDS)).j("com/google/android/libraries/assistant/soda/metrics/SodaMetricsUtils", "isMetricsLoggingSupported", 20, "SodaMetricsUtils.java")).s("Metrics logging not supported for host app id: %d", b3.D);
                    return;
                }
                ieq ieq2 = iep2.b;
                if (ieq2 == null) {
                    ieq2 = ieq.d;
                }
                ieo ieo2 = ieq2.b;
                if (ieo2 == null) {
                    ieo2 = ieo.b;
                }
                iec iec2 = ieo2.a;
                if (iec2 == null) {
                    iec2 = iec.d;
                }
                if (iec2 != null) {
                    iar b4 = iar.b(iec2.a);
                    if (b4 == null) {
                        b4 = iar.UNKNOWN_HOST_APP_ID;
                    }
                    if (!b4.equals(iar.ANDROID_VOICE_IME) && ((J3 = a.J(iec2.c)) == 0 || J3 != 4)) {
                        String a3 = fek.a(iec2);
                        if ((a3.startsWith("com.google") || a3.startsWith("com.android")) && !fek.a.containsKey(a3)) {
                            return;
                        }
                    }
                    Object obj = this.b;
                    cad cad = (cad) obj;
                    cab g = cad.g(iep2, cre.a((Context) this.c, (cqs) this.a));
                    g.i = iat.b;
                    ieq ieq3 = iep2.b;
                    if (ieq3 == null) {
                        ieq3 = ieq.d;
                    }
                    ieo ieo3 = ieq3.b;
                    if (ieo3 == null) {
                        ieo3 = ieo.b;
                    }
                    iec iec3 = ieo3.a;
                    if (iec3 == null) {
                        iec3 = iec.d;
                    }
                    if (iec3 == null) {
                        optional = Optional.empty();
                    } else {
                        iar b5 = iar.b(iec3.a);
                        if (b5 == null) {
                            b5 = iar.UNKNOWN_HOST_APP_ID;
                        }
                        if (b5.equals(iar.ANDROID_VOICE_IME) || ((J2 = a.J(iec3.c)) != 0 && J2 == 4)) {
                            optional = Optional.of(109599039);
                        } else {
                            optional = Optional.ofNullable((Integer) fek.a.get(fek.a(iec3)));
                        }
                    }
                    optional.ifPresent(new bme(g, 9));
                    g.b();
                }
            } catch (hui e) {
                e = e;
                if (e.a) {
                    e = new hui((IOException) e);
                }
                throw e;
            } catch (hvx e2) {
                throw e2.a();
            } catch (IOException e3) {
                if (e3.getCause() instanceof hui) {
                    throw ((hui) e3.getCause());
                }
                throw new hui(e3);
            } catch (RuntimeException e4) {
                if (e4.getCause() instanceof hui) {
                    throw ((hui) e4.getCause());
                }
                throw e4;
            } catch (hui e5) {
                throw e5;
            } catch (hui e6) {
                Log.w("SodaMetricsClearcut", "Failed to parse message as SodaExtension", e6);
            }
        }
    }

    /* JADX WARNING: type inference failed for: r1v0, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v2, types: [java.lang.Object, jjk] */
    public final fdk u(fey fey, fev fev, fdo fdo) {
        Object obj = this.a;
        Object b2 = this.c.b();
        fes a2 = ((fet) obj).b();
        hmi hmi = (hmi) this.b.b();
        hmi.getClass();
        return new fdk((cxp) b2, a2, hmi, fey, fev, fdo);
    }

    /* JADX WARNING: type inference failed for: r1v0, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r1v2, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r1v4, types: [java.lang.Object, jjk] */
    public final eua v(hme hme, jna jna, hme hme2, jna jna2, hme hme3, grh grh, Runnable runnable, jix jix) {
        hme.getClass();
        hme2.getClass();
        jqs jqs = (jqs) this.b.b();
        jqs.getClass();
        Executor executor = (Executor) this.a.b();
        executor.getClass();
        Executor executor2 = (Executor) this.c.b();
        executor2.getClass();
        return new eua(hme, jna, hme2, jna2, hme3, grh, runnable, jix, jqs, executor, executor2);
    }

    /* JADX WARNING: type inference failed for: r0v0, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r1v0, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v2, types: [java.lang.Object, jjk] */
    public final eny w(enr enr) {
        ? r0 = this.a;
        Object b2 = this.c.b();
        eks eks = (eks) r0.b();
        eks.getClass();
        jqs jqs = (jqs) this.b.b();
        jqs.getClass();
        return new eny(enr, gqd.a, (dlv) b2, eks, jqs);
    }

    public final String x() {
        Object obj = this.c;
        if (obj != null) {
            return ((dvo) obj).b;
        }
        Object obj2 = this.b;
        fvf.aP(obj2);
        return ((dug) obj2).e();
    }

    /* JADX WARNING: type inference failed for: r0v0, types: [gsb, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v0, types: [gsb, java.lang.Object] */
    public final File y() {
        return new File(((String) this.c.a()) + "/" + ((String) this.a.a()) + ".pb");
    }

    public final boolean z(dlc dlc) {
        if (!((ijj) this.b).a().booleanValue()) {
            return dlb.d((Context) this.c, dlc);
        }
        if (((dkq) this.a).c.a == 2) {
            return true;
        }
        return false;
    }

    public bmu(Context context, LocationManager locationManager) {
        this.a = new em();
        this.c = context;
        this.b = locationManager;
    }

    public bmu(Context context, dkq dkq, jjk jjk) {
        this.c = context;
        this.a = dkq;
        this.b = jjk;
    }

    public bmu(bmt bmt) {
        this.c = bmt.c;
        this.b = bmt.b;
        this.a = bmt.a;
    }

    public bmu(ceh ceh, AmbientMode.AmbientController ambientController, Runnable runnable, byte[] bArr) {
        this.c = ceh;
        this.b = ambientController;
        this.a = runnable;
    }

    public bmu(daz daz, Map map) {
        this.c = daz;
        this.a = map;
    }

    public bmu(dxv dxv, ihn ihn, Set set) {
        this.c = ihn;
        this.b = dxv;
        this.a = set;
    }

    public bmu(fdh fdh, Runnable runnable) {
        this.a = fdh;
        this.c = runnable;
        this.b = null;
    }

    public bmu(Object obj, Object obj2, Object obj3) {
        this.a = obj;
        this.c = obj2;
        this.b = obj3;
    }

    public bmu(Object obj, Object obj2, Object obj3, byte[] bArr) {
        this.c = obj;
        this.b = obj2;
        this.a = obj3;
    }

    public bmu(Object obj, Object obj2, Object obj3, char[] cArr) {
        this.c = obj;
        this.a = obj2;
        this.b = obj3;
    }

    public bmu(UUID uuid, bhe bhe, Set set) {
        jnu.e(uuid, "id");
        jnu.e(bhe, "workSpec");
        this.c = uuid;
        this.b = bhe;
        this.a = set;
    }

    public bmu(jjk jjk, jjk jjk2, jjk jjk3, short[] sArr) {
        this.b = jjk;
        this.c = jjk2;
        this.a = jjk3;
    }

    public bmu() {
        this(new Random());
    }

    public bmu(aba aba, gsb gsb, gsb gsb2, gpw gpw) {
        fvf.aG(((abf) aba).b == aaz.INITIALIZED, "FutureCallbackRegistry must be created in onCreate of the Activity/Fragment.");
        this.b = gsb;
        this.c = gpw;
        this.a = fvf.as(new dof(this, gsb2, 9, (byte[]) null));
        aba.b(new gpx(this));
    }

    public bmu(Context context) {
        bzz bzz = new bzz(context, "SODA_CLEARCUT");
        bzz.e = new czn(1, (byte[]) null);
        cad a2 = bzz.a();
        this.a = new cni();
        this.c = context;
        this.b = a2;
    }

    public bmu(hmh hmh, String str) {
        this.a = new HashMap();
        this.b = hmh;
        this.c = str;
    }

    public bmu(hsq hsq, String str, String str2) {
        this.b = hgv.e;
        this.c = fvf.as(new dof(this, hsq, 4, (byte[]) null));
        this.a = fvf.as(new dvu(this, str, str2, 1));
    }

    public bmu(jjk jjk, jjk jjk2, jjk jjk3, byte[] bArr) {
        jjk.getClass();
        this.c = jjk;
        jjk2.getClass();
        this.a = jjk2;
        jjk3.getClass();
        this.b = jjk3;
    }

    public bmu(jjk jjk, jjk jjk2, jjk jjk3, byte[] bArr, char[] cArr) {
        jjk.getClass();
        this.b = jjk;
        jjk2.getClass();
        this.a = jjk2;
        jjk3.getClass();
        this.c = jjk3;
    }

    public bmu(jjk jjk, jjk jjk2, jjk jjk3, char[] cArr, byte[] bArr) {
        jjk.getClass();
        this.c = jjk;
        jjk2.getClass();
        this.a = jjk2;
        jjk3.getClass();
        this.b = jjk3;
    }

    public bmu(qh qhVar) {
        this.a = new ArrayList();
        this.b = new qo();
        this.c = qhVar;
    }

    public bmu(byte[] bArr) {
        this.c = new gxl();
        this.b = new gxl();
        this.a = "DEFAULT";
    }

    public bmu(cxk cxk) {
        this.c = new HashMap();
        this.a = new HashMap();
        this.b = cxk;
    }

    public bmu(Runnable runnable) {
        this.a = new CopyOnWriteArrayList();
        this.c = new HashMap();
        this.b = runnable;
    }

    public bmu(Random random) {
        this(new int[0], random);
    }

    public bmu(Context context, grh grh, Executor executor) {
        this.a = executor;
        this.b = cyw.i(executor);
        this.c = new cyw(executor, (czd) new czl(grh, context, 0));
    }

    public bmu(dvo dvo, dug dug) {
        gxv gxv;
        String str;
        hsq hsq;
        dvo dvo2 = dvo;
        dug dug2 = dug;
        fvf.aF((dvo2 != null) ^ (dug2 != null));
        this.c = dvo2;
        this.b = dug2;
        if (dvo2 != null) {
            gxr h = gxv.h(dvo2.f.size() + 3);
            for (dvp dvp : dvo2.f) {
                int i = dvp.b;
                int i2 = i != 0 ? i != 2 ? i != 3 ? i != 4 ? i != 5 ? i != 6 ? 0 : 5 : 4 : 3 : 2 : 1 : 6;
                if (i2 != 0) {
                    int i3 = i2 - 1;
                    if (i3 == 0) {
                        h.d(dvp.d, Long.valueOf(i == 2 ? ((Long) dvp.c).longValue() : 0));
                    } else if (i3 == 1) {
                        h.d(dvp.d, Boolean.valueOf(i == 3 ? ((Boolean) dvp.c).booleanValue() : false));
                    } else if (i3 == 2) {
                        h.d(dvp.d, Double.valueOf(i == 4 ? ((Double) dvp.c).doubleValue() : 0.0d));
                    } else if (i3 == 3) {
                        String str2 = dvp.d;
                        if (i == 5) {
                            str = (String) dvp.c;
                        } else {
                            str = "";
                        }
                        h.d(str2, str);
                    } else if (i3 == 4) {
                        String str3 = dvp.d;
                        if (i == 6) {
                            hsq = (hsq) dvp.c;
                        } else {
                            hsq = hsq.b;
                        }
                        h.d(str3, hsq.y());
                    }
                } else {
                    throw null;
                }
            }
            h.d("__phenotype_server_token", dvo2.d);
            h.d("__phenotype_snapshot_token", dvo2.b);
            h.d("__phenotype_configuration_version", Long.valueOf(dvo2.e));
            gxv = h.a();
        } else {
            fvf.aP(dug);
            duf a2 = dug.a();
            gxr h2 = gxv.h(a2.b.size() + 3);
            hbp k = a2.b.iterator();
            while (k.hasNext()) {
                due due = (due) k.next();
                h2.d(due.c(), due.b());
            }
            h2.d("__phenotype_server_token", dug.d());
            h2.d("__phenotype_snapshot_token", dug.e());
            h2.d("__phenotype_configuration_version", Long.valueOf(dug2.b.d));
            gxv = h2.a();
        }
        this.a = gxv;
    }

    public bmu(ftc ftc, fnn fnn) {
        this.a = new HashMap();
        this.c = ftc;
        this.b = fnn;
    }

    public bmu(jjk jjk, jjk jjk2, jjk jjk3, char[] cArr) {
        jjk.getClass();
        this.c = jjk;
        jjk2.getClass();
        this.a = jjk2;
        jjk3.getClass();
        this.b = jjk3;
    }

    public bmu(grh grh) {
        this.b = new Object();
        this.a = new ot();
        new gxc();
        this.c = grh;
    }

    public bmu(daz daz, Map map, byte[] bArr) {
        this(daz, map);
        this.b = new LruCache(32);
    }

    public bmu(int[] iArr, Random random) {
        this.a = iArr;
        this.b = random;
        this.c = new int[iArr.length];
        for (int i = 0; i < iArr.length; i++) {
            ((int[]) this.c)[iArr[i]] = i;
        }
    }

    public bmu(bbn bbn) {
        this(bbn.a, bbn.b, bbn.c);
    }

    public bmu(jjk jjk, jjk jjk2, jjk jjk3, byte[] bArr, byte[] bArr2, byte[] bArr3, byte[] bArr4) {
        jjk.getClass();
        this.b = jjk;
        jjk2.getClass();
        this.c = jjk2;
        jjk3.getClass();
        this.a = jjk3;
    }

    public bmu(jjk jjk, jjk jjk2, jjk jjk3, byte[] bArr, byte[] bArr2, byte[] bArr3) {
        jjk.getClass();
        this.c = jjk;
        jjk2.getClass();
        this.a = jjk2;
        jjk3.getClass();
        this.b = jjk3;
    }

    public bmu(jjk jjk, jjk jjk2, jjk jjk3, byte[] bArr, byte[] bArr2) {
        jjk.getClass();
        this.b = jjk;
        jjk2.getClass();
        this.a = jjk2;
        jjk3.getClass();
        this.c = jjk3;
    }

    public bmu(ScheduledExecutorService scheduledExecutorService, frd frd) {
        frd frd2 = frd;
        fvf.as(new emh(this, 13));
        fvf.as(new evp(this, 5));
        fvf.as(new evv(this, 2));
        fvf.as(new ewa(this, 20));
        fvf.as(new ewf(this, 6));
        fvf.as(new ewf(this, 18));
        fvf.as(new ewg(this, 9));
        fvf.as(new ewh(this, 1));
        fvf.as(new ewh(this, 12));
        fvf.as(new ewi(this, 4));
        fvf.as(new evp(this, 14));
        fvf.as(new evv(this, 10));
        fvf.as(new ewb(this, 6));
        fvf.as(new ewh(this, 2));
        fvf.as(new ewi(this, 14));
        fvf.as(new ewj(this, 5));
        fvf.as(new ewj(this, 17));
        fvf.as(new ewl(this, 4));
        fvf.as(new ewl(this, 16));
        fvf.as(new evp(this, 4));
        fvf.as(new evp(this, 17));
        fvf.as(new evq(this, 8));
        fvf.as(new evq(this, 20));
        fvf.as(new evr(this, 11));
        fvf.as(new evs(this, 2));
        fvf.as(new evs(this, 14));
        fvf.as(new evt(this, 5));
        fvf.as(new evt(this, 17));
        fvf.as(new evu(this, 10));
        fvf.as(new evv(this, 0));
        fvf.as(new evv(this, 14));
        fvf.as(new evw(this, 5));
        fvf.as(new evw(this, 17));
        fvf.as(new evx(this, 8));
        fvf.as(new evx(this, 20));
        fvf.as(new evy(this, 11));
        fvf.as(new evz(this, 2));
        fvf.as(new evz(this, 16));
        fvf.as(new ewa(this, 7));
        fvf.as(new ewa(this, 19));
        fvf.as(new ewb(this, 11));
        fvf.as(new ewc(this, 2));
        fvf.as(new ewc(this, 14));
        fvf.as(new ewd(this, 5));
        fvf.as(new ewd(this, 17));
        fvf.as(new ewe(this, 8));
        fvf.as(new ewf(this, 0));
        fvf.as(new ewf(this, 3));
        fvf.as(new ewf(this, 4));
        fvf.as(new ewf(this, 5));
        fvf.as(new ewf(this, 7));
        fvf.as(new ewf(this, 8));
        fvf.as(new ewf(this, 9));
        fvf.as(new ewf(this, 10));
        fvf.as(new ewf(this, 12));
        fvf.as(new ewf(this, 13));
        fvf.as(new ewf(this, 14));
        fvf.as(new ewf(this, 15));
        fvf.as(new ewf(this, 16));
        fvf.as(new ewf(this, 17));
        fvf.as(new ewf(this, 19));
        fvf.as(new ewf(this, 20));
        fvf.as(new ewg(this, 1));
        fvf.as(new ewg(this, 2));
        fvf.as(new ewg(this, 3));
        fvf.as(new ewg(this, 4));
        fvf.as(new ewg(this, 5));
        fvf.as(new ewg(this, 6));
        fvf.as(new ewg(this, 7));
        fvf.as(new ewg(this, 8));
        fvf.as(new ewg(this, 10));
        fvf.as(new ewg(this, 11));
        fvf.as(new ewg(this, 13));
        fvf.as(new ewg(this, 14));
        fvf.as(new ewg(this, 15));
        fvf.as(new ewg(this, 16));
        fvf.as(new ewg(this, 17));
        fvf.as(new ewg(this, 18));
        fvf.as(new ewg(this, 19));
        fvf.as(new ewg(this, 20));
        fvf.as(new ewh(this, 0));
        fvf.as(new ewh(this, 3));
        fvf.as(new ewh(this, 4));
        fvf.as(new ewh(this, 5));
        fvf.as(new ewh(this, 6));
        fvf.as(new ewh(this, 7));
        fvf.as(new ewh(this, 8));
        fvf.as(new ewh(this, 9));
        fvf.as(new ewh(this, 10));
        fvf.as(new ewh(this, 11));
        fvf.as(new ewh(this, 14));
        fvf.as(new ewh(this, 15));
        fvf.as(new ewh(this, 16));
        fvf.as(new ewh(this, 17));
        fvf.as(new ewh(this, 18));
        fvf.as(new ewh(this, 19));
        fvf.as(new ewh(this, 20));
        fvf.as(new ewi(this, 1));
        fvf.as(new ewi(this, 0));
        fvf.as(new ewi(this, 2));
        fvf.as(new evt(this, 19));
        fvf.as(new evz(this, 4));
        fvf.as(new ewe(this, 10));
        fvf.as(new ewi(this, 8));
        fvf.as(new ewi(this, 19));
        fvf.as(new ewj(this, 9));
        fvf.as(new ewj(this, 20));
        fvf.as(new ewl(this, 6));
        fvf.as(new ewl(this, 17));
        fvf.as(new evp(this, 3));
        fvf.as(new evq(this, 4));
        fvf.as(new evq(this, 15));
        fvf.as(new evr(this, 5));
        fvf.as(new evr(this, 16));
        fvf.as(new evs(this, 6));
        fvf.as(new evs(this, 17));
        fvf.as(new evt(this, 7));
        fvf.as(new evt(this, 18));
        fvf.as(new evu(this, 9));
        fvf.as(new evu(this, 20));
        fvf.as(new evw(this, 1));
        fvf.as(new evw(this, 11));
        fvf.as(new evx(this, 0));
        fvf.as(new evx(this, 12));
        fvf.as(new evy(this, 2));
        fvf.as(new evy(this, 13));
        fvf.as(new evz(this, 3));
        fvf.as(new evz(this, 15));
        fvf.as(new ewa(this, 5));
        fvf.as(new ewa(this, 16));
        fvf.as(new ewb(this, 17));
        fvf.as(new ewc(this, 7));
        fvf.as(new ewc(this, 18));
        fvf.as(new ewd(this, 8));
        fvf.as(new ewd(this, 19));
        fvf.as(new ewe(this, 9));
        fvf.as(new ewf(this, 1));
        fvf.as(new ewf(this, 11));
        fvf.as(new ewg(this, 0));
        fvf.as(new ewg(this, 12));
        fvf.as(new ewh(this, 13));
        fvf.as(new ewi(this, 3));
        fvf.as(new ewi(this, 5));
        fvf.as(new ewi(this, 6));
        fvf.as(new ewi(this, 7));
        fvf.as(new ewi(this, 9));
        fvf.as(new ewi(this, 10));
        fvf.as(new ewi(this, 11));
        fvf.as(new ewi(this, 12));
        fvf.as(new ewi(this, 13));
        fvf.as(new ewi(this, 15));
        fvf.as(new ewi(this, 16));
        fvf.as(new ewi(this, 17));
        fvf.as(new ewi(this, 18));
        fvf.as(new ewi(this, 20));
        fvf.as(new ewj(this, 1));
        fvf.as(new ewj(this, 0));
        fvf.as(new ewj(this, 2));
        fvf.as(new ewj(this, 3));
        fvf.as(new ewj(this, 4));
        fvf.as(new ewj(this, 6));
        fvf.as(new ewj(this, 7));
        fvf.as(new ewj(this, 8));
        fvf.as(new ewj(this, 10));
        fvf.as(new ewj(this, 11));
        fvf.as(new ewj(this, 12));
        fvf.as(new ewj(this, 13));
        fvf.as(new ewj(this, 14));
        fvf.as(new ewj(this, 15));
        fvf.as(new ewj(this, 16));
        fvf.as(new ewj(this, 18));
        fvf.as(new ewj(this, 19));
        fvf.as(new ewk(this, 1));
        fvf.as(new ewk(this, 0));
        fvf.as(new ewk(this, 2));
        fvf.as(new ewk(this, 3));
        fvf.as(new ewl(this, 1));
        fvf.as(new ewl(this, 0));
        fvf.as(new ewl(this, 2));
        fvf.as(new ewl(this, 3));
        fvf.as(new ewl(this, 5));
        fvf.as(new ewl(this, 7));
        fvf.as(new ewl(this, 8));
        fvf.as(new ewl(this, 9));
        fvf.as(new ewl(this, 10));
        fvf.as(new ewl(this, 11));
        fvf.as(new ewl(this, 12));
        fvf.as(new ewl(this, 13));
        fvf.as(new ewl(this, 14));
        fvf.as(new ewl(this, 15));
        fvf.as(new emh(this, 14));
        fvf.as(new emh(this, 15));
        fvf.as(new emh(this, 16));
        fvf.as(new emh(this, 17));
        fvf.as(new emh(this, 18));
        fvf.as(new emh(this, 19));
        fvf.as(new emh(this, 20));
        fvf.as(new evp(this, 1));
        fvf.as(new evp(this, 0));
        fvf.as(new evp(this, 2));
        fvf.as(new evp(this, 6));
        fvf.as(new evp(this, 7));
        fvf.as(new evp(this, 8));
        fvf.as(new evp(this, 9));
        fvf.as(new evp(this, 10));
        fvf.as(new evp(this, 11));
        fvf.as(new evp(this, 12));
        fvf.as(new evp(this, 13));
        fvf.as(new evp(this, 15));
        fvf.as(new evp(this, 16));
        fvf.as(new evp(this, 18));
        fvf.as(new evp(this, 19));
        fvf.as(new evp(this, 20));
        fvf.as(new evq(this, 1));
        fvf.as(new evq(this, 0));
        fvf.as(new evq(this, 2));
        fvf.as(new evq(this, 3));
        fvf.as(new evq(this, 5));
        fvf.as(new evq(this, 6));
        fvf.as(new evq(this, 7));
        fvf.as(new evq(this, 9));
        fvf.as(new evq(this, 10));
        fvf.as(new evq(this, 11));
        fvf.as(new evq(this, 12));
        fvf.as(new evq(this, 13));
        fvf.as(new evq(this, 14));
        fvf.as(new evq(this, 16));
        fvf.as(new evq(this, 17));
        fvf.as(new evq(this, 18));
        fvf.as(new evq(this, 19));
        fvf.as(new evr(this, 1));
        fvf.as(new evr(this, 0));
        fvf.as(new evr(this, 2));
        fvf.as(new evr(this, 3));
        fvf.as(new evr(this, 4));
        fvf.as(new evr(this, 6));
        fvf.as(new evr(this, 7));
        fvf.as(new evr(this, 8));
        fvf.as(new evr(this, 9));
        fvf.as(new evr(this, 10));
        fvf.as(new evr(this, 12));
        fvf.as(new evr(this, 13));
        fvf.as(new evr(this, 14));
        fvf.as(new evr(this, 15));
        fvf.as(new evr(this, 17));
        fvf.as(new evr(this, 18));
        fvf.as(new evr(this, 19));
        fvf.as(new evr(this, 20));
        fvf.as(new evs(this, 1));
        fvf.as(new evs(this, 0));
        fvf.as(new evs(this, 3));
        fvf.as(new evs(this, 4));
        fvf.as(new evs(this, 5));
        fvf.as(new evs(this, 7));
        fvf.as(new evs(this, 8));
        fvf.as(new evs(this, 9));
        fvf.as(new evs(this, 10));
        fvf.as(new evs(this, 11));
        fvf.as(new evs(this, 12));
        fvf.as(new evs(this, 13));
        fvf.as(new evs(this, 15));
        fvf.as(new evs(this, 16));
        fvf.as(new evs(this, 18));
        fvf.as(new evs(this, 19));
        fvf.as(new evs(this, 20));
        fvf.as(new evt(this, 1));
        fvf.as(new evt(this, 0));
        fvf.as(new evt(this, 2));
        fvf.as(new evt(this, 3));
        fvf.as(new evt(this, 4));
        fvf.as(new evt(this, 6));
        fvf.as(new evt(this, 8));
        fvf.as(new evt(this, 9));
        fvf.as(new evt(this, 10));
        fvf.as(new evt(this, 11));
        fvf.as(new evt(this, 12));
        fvf.as(new evt(this, 13));
        fvf.as(new evt(this, 14));
        fvf.as(new evt(this, 15));
        fvf.as(new evt(this, 16));
        fvf.as(new evt(this, 20));
        fvf.as(new evu(this, 1));
        fvf.as(new evu(this, 0));
        fvf.as(new evu(this, 2));
        fvf.as(new evu(this, 3));
        fvf.as(new evu(this, 4));
        fvf.as(new evu(this, 5));
        fvf.as(new evu(this, 6));
        fvf.as(new evu(this, 7));
        fvf.as(new evu(this, 8));
        fvf.as(new evu(this, 11));
        fvf.as(new evu(this, 12));
        fvf.as(new evu(this, 13));
        fvf.as(new evu(this, 14));
        fvf.as(new evu(this, 15));
        fvf.as(new evu(this, 16));
        fvf.as(new evu(this, 17));
        fvf.as(new evu(this, 18));
        fvf.as(new evu(this, 19));
        fvf.as(new evv(this, 1));
        fvf.as(new evv(this, 3));
        fvf.as(new evv(this, 4));
        fvf.as(new evv(this, 5));
        fvf.as(new evv(this, 6));
        fvf.as(new evv(this, 7));
        fvf.as(new evv(this, 8));
        fvf.as(new evv(this, 9));
        fvf.as(new evv(this, 11));
        fvf.as(new evv(this, 12));
        fvf.as(new evv(this, 13));
        fvf.as(new evv(this, 15));
        fvf.as(new evv(this, 16));
        fvf.as(new evv(this, 17));
        fvf.as(new evv(this, 18));
        fvf.as(new evv(this, 19));
        fvf.as(new evv(this, 20));
        fvf.as(new evw(this, 0));
        fvf.as(new evw(this, 2));
        fvf.as(new evw(this, 3));
        fvf.as(new evw(this, 4));
        fvf.as(new evw(this, 6));
        fvf.as(new evw(this, 7));
        fvf.as(new evw(this, 8));
        fvf.as(new evw(this, 9));
        fvf.as(new evw(this, 10));
        fvf.as(new evw(this, 12));
        fvf.as(new evw(this, 13));
        fvf.as(new evw(this, 14));
        fvf.as(new evw(this, 15));
        fvf.as(new evw(this, 16));
        fvf.as(new evw(this, 18));
        fvf.as(new evw(this, 19));
        fvf.as(new evw(this, 20));
        fvf.as(new evx(this, 1));
        fvf.as(new evx(this, 2));
        fvf.as(new evx(this, 3));
        fvf.as(new evx(this, 4));
        fvf.as(new evx(this, 5));
        fvf.as(new evx(this, 6));
        fvf.as(new evx(this, 7));
        fvf.as(new evx(this, 9));
        fvf.as(new evx(this, 10));
        fvf.as(new evx(this, 11));
        fvf.as(new evx(this, 13));
        fvf.as(new evx(this, 14));
        fvf.as(new evx(this, 15));
        fvf.as(new evx(this, 16));
        fvf.as(new evx(this, 17));
        fvf.as(new evx(this, 18));
        fvf.as(new evx(this, 19));
        fvf.as(new evy(this, 1));
        fvf.as(new evy(this, 0));
        fvf.as(new evy(this, 3));
        fvf.as(new evy(this, 4));
        fvf.as(new evy(this, 5));
        fvf.as(new evy(this, 6));
        fvf.as(new evy(this, 7));
        fvf.as(new evy(this, 8));
        fvf.as(new evy(this, 9));
        fvf.as(new evy(this, 10));
        fvf.as(new evy(this, 12));
        fvf.as(new evy(this, 14));
        fvf.as(new evy(this, 15));
        fvf.as(new evy(this, 16));
        fvf.as(new evy(this, 17));
        fvf.as(new evy(this, 18));
        fvf.as(new evy(this, 19));
        fvf.as(new evy(this, 20));
        fvf.as(new evz(this, 1));
        fvf.as(new evz(this, 0));
        fvf.as(new evz(this, 5));
        fvf.as(new evz(this, 6));
        fvf.as(new evz(this, 7));
        fvf.as(new evz(this, 8));
        fvf.as(new evz(this, 9));
        fvf.as(new evz(this, 10));
        fvf.as(new evz(this, 11));
        fvf.as(new evz(this, 12));
        fvf.as(new evz(this, 13));
        fvf.as(new evz(this, 14));
        fvf.as(new evz(this, 17));
        fvf.as(new evz(this, 18));
        fvf.as(new evz(this, 19));
        fvf.as(new evz(this, 20));
        fvf.as(new ewa(this, 1));
        fvf.as(new ewa(this, 0));
        fvf.as(new ewa(this, 2));
        fvf.as(new ewa(this, 3));
        fvf.as(new ewa(this, 4));
        fvf.as(new ewa(this, 6));
        fvf.as(new ewa(this, 8));
        fvf.as(new ewa(this, 9));
        fvf.as(new ewa(this, 10));
        fvf.as(new ewa(this, 11));
        fvf.as(new ewa(this, 12));
        fvf.as(new ewa(this, 13));
        fvf.as(new ewa(this, 14));
        fvf.as(new ewa(this, 15));
        fvf.as(new ewa(this, 17));
        fvf.as(new ewa(this, 18));
        fvf.as(new ewb(this, 1));
        fvf.as(new ewb(this, 0));
        fvf.as(new ewb(this, 2));
        fvf.as(new ewb(this, 3));
        fvf.as(new ewb(this, 4));
        fvf.as(new ewb(this, 5));
        fvf.as(new ewb(this, 7));
        fvf.as(new ewb(this, 8));
        fvf.as(new ewb(this, 9));
        fvf.as(new ewb(this, 10));
        fvf.as(new ewb(this, 12));
        fvf.as(new ewb(this, 13));
        fvf.as(new ewb(this, 14));
        fvf.as(new ewb(this, 15));
        fvf.as(new ewb(this, 16));
        fvf.as(new ewb(this, 18));
        fvf.as(new ewb(this, 19));
        fvf.as(new ewb(this, 20));
        fvf.as(new ewc(this, 1));
        fvf.as(new ewc(this, 0));
        fvf.as(new ewc(this, 3));
        fvf.as(new ewc(this, 4));
        fvf.as(new ewc(this, 5));
        fvf.as(new ewc(this, 6));
        fvf.as(new ewc(this, 8));
        fvf.as(new ewc(this, 9));
        fvf.as(new ewc(this, 10));
        fvf.as(new ewc(this, 11));
        fvf.as(new ewc(this, 12));
        fvf.as(new ewc(this, 13));
        fvf.as(new ewc(this, 15));
        fvf.as(new ewc(this, 16));
        fvf.as(new ewc(this, 17));
        fvf.as(new ewc(this, 19));
        fvf.as(new ewc(this, 20));
        fvf.as(new ewd(this, 1));
        fvf.as(new ewd(this, 0));
        fvf.as(new ewd(this, 2));
        fvf.as(new ewd(this, 3));
        fvf.as(new ewd(this, 4));
        fvf.as(new ewd(this, 6));
        fvf.as(new ewd(this, 7));
        fvf.as(new ewd(this, 9));
        fvf.as(new ewd(this, 10));
        fvf.as(new ewd(this, 11));
        this.b = fvf.as(new ewd(this, 12));
        fvf.as(new ewd(this, 13));
        fvf.as(new ewd(this, 14));
        fvf.as(new ewd(this, 15));
        fvf.as(new ewd(this, 16));
        fvf.as(new ewd(this, 18));
        fvf.as(new ewd(this, 20));
        fvf.as(new ewe(this, 1));
        fvf.as(new ewe(this, 0));
        fvf.as(new ewe(this, 2));
        fvf.as(new ewe(this, 3));
        fvf.as(new ewe(this, 4));
        fvf.as(new ewe(this, 5));
        fvf.as(new ewe(this, 6));
        fvf.as(new ewe(this, 7));
        fvf.as(new ewe(this, 11));
        fvf.as(new ewe(this, 12));
        fvf.as(new ewe(this, 13));
        fvf.as(new ewe(this, 14));
        fvf.as(new ewe(this, 15));
        fvf.as(new ewe(this, 16));
        fvf.as(new ewe(this, 17));
        fvf.as(new ewe(this, 18));
        fvf.as(new ewe(this, 19));
        fvf.as(new ewe(this, 20));
        fvf.as(new ewf(this, 2));
        frc e = frc.e("googlequicksearchbox_android_deidentified");
        this.a = e;
        frc frc = e;
        frb d2 = e.d();
        if (d2 == null) {
            frc frc2 = e;
            this.c = frf.b(frd2, scheduledExecutorService, e, (Application) null);
            return;
        }
        this.c = d2;
        d2.a(frd2);
    }
}
