package defpackage;

import android.os.Looper;
import java.util.ArrayDeque;
import java.util.Iterator;
import java.util.concurrent.CopyOnWriteArraySet;

/* renamed from: afv  reason: default package */
/* compiled from: PG */
public final class afv {
    public final afm a;
    public final afq b;
    public final aft c;
    public final CopyOnWriteArraySet d;
    public boolean e;
    private final ArrayDeque f;
    private final ArrayDeque g;
    private final Object h;
    private boolean i;

    public afv(Looper looper, afm afm, aft aft) {
        this(new CopyOnWriteArraySet(), looper, afm, aft, true);
    }

    public final void a(Object obj) {
        synchronized (this.h) {
            if (!this.i) {
                this.d.add(new afu(obj));
            }
        }
    }

    public final void b() {
        f();
        if (!this.g.isEmpty()) {
            if (!this.b.b()) {
                afq afq = this.b;
                afq.g(afq.e(1));
            }
            boolean isEmpty = this.f.isEmpty();
            this.f.addAll(this.g);
            this.g.clear();
            if (isEmpty) {
                while (!this.f.isEmpty()) {
                    ((Runnable) this.f.peekFirst()).run();
                    this.f.removeFirst();
                }
            }
        }
    }

    public final void c(int i2, afs afs) {
        f();
        this.g.add(new mp((Object) new CopyOnWriteArraySet(this.d), i2, (Object) afs, 3));
    }

    public final void d() {
        f();
        synchronized (this.h) {
            this.i = true;
        }
        Iterator it = this.d.iterator();
        while (it.hasNext()) {
            ((afu) it.next()).a(this.c);
        }
        this.d.clear();
    }

    public final void e(int i2, afs afs) {
        c(i2, afs);
        b();
    }

    public final void f() {
        boolean z;
        if (this.e) {
            if (Thread.currentThread() == ((agf) this.b).b.getLooper().getThread()) {
                z = true;
            } else {
                z = false;
            }
            yi.h(z);
        }
    }

    public afv(CopyOnWriteArraySet copyOnWriteArraySet, Looper looper, afm afm, aft aft, boolean z) {
        this.a = afm;
        this.d = copyOnWriteArraySet;
        this.c = aft;
        this.h = new Object();
        this.f = new ArrayDeque();
        this.g = new ArrayDeque();
        this.b = afm.b(looper, new afr(this, 0));
        this.e = z;
    }
}
