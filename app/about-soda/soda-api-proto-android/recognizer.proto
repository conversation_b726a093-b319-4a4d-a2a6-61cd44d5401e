syntax = "proto2";

package greco;

import "net/proto2/bridge/proto/message_set.proto";
import "speech/decoder/common/alignment.proto";
import "speech/decoder/confidence/conf_feature.proto";
import "speech/grammar/proto/interpretation.proto";
import "speech/greco3/api/transliteration_script.proto";
import "speech/greco3/core/loggable.proto";
import "speech/greco3/hotword/hotword_feature.proto";
import "speech/multilang/language_decision/multilang_scores.proto";
import "speech/service/proto/alternates.proto";
import "speech/service/proto/augmentation_source.proto";
import "speech/service/s3/services/speakerid/frontend/confidence-interval-label.proto";

option objc_class_prefix = "GRC";
option cc_enable_arenas = true;
option optimize_for = CODE_SIZE;
option java_package = "com.google.speech.recognizer.api";

message SemanticResult {
    repeated speech.Interpretation interpretation = 1;
}

message Hypothesis {
    optional string text = 1;
    optional string prenorm_text = 12;
    optional string scrubbed_text = 13;
    optional float confidence = 2;
    optional bool accept = 11 [default = true];
    optional AlignmentProto state_align = 10;
    optional AlignmentProto phone_align = 3;
    optional AlignmentProto word_align = 4;
    optional AlignmentProto normalized_word_align = 21;
    repeated speech.WordConfFeature word_conf_feature = 5;
    optional speech.common.RecognitionClientAlternates alternates = 6;
    optional speech.service.augmentation.AugmentationSource augmentation_source = 14;
    optional int32 speech_end_time_usec = 15;
    optional float lm_cost_without_rescoring = 16;
    optional float lm_cost_without_jinn_boosting = 17;
    optional float lm_cost = 18;
    optional float am_cost = 19;
    optional float total_cost = 20;
    optional SemanticResult semantic_result = 7 [deprecated = true];
    
    extensions 1000 to max;
}

message RecognitionResult {
    optional int64 start_time_usec = 1;
    optional int64 end_time_usec = 2;
    repeated Hypothesis hypothesis = 3;
    
    optional NonspeechHypothesis nonspeech_hypothesis = 5;
    message NonspeechHypothesis {
        optional Hypothesis hypothesis = 1;
        optional int32 rank = 2;
    }
    
    repeated TransliteratedNbest transliteration = 12;
    optional bytes lattice_fst = 4;
    optional bytes detailed_lattice_fst = 8;
    repeated string phoneme = 6;
    optional bool rescoring_modified_lattice = 7 [deprecated = true];
    optional bool top_hypothesis_contains_hotword = 9;
    optional IntendedQueryResult intended_query_result = 10;
    repeated VerificationResult verification_result = 11;
    optional bool prepend_space = 13;
    
    extensions 1000 to max;
    
    extend .proto2.bridge.MessageSet {
        optional RecognitionResult message_set_extension = 110722326;
    }
}

message IntendedQueryResult {
    optional float score = 1;
    optional float semantic_filter_threshold = 2;
    optional float open_mic_threshold = 3;
    optional float ganache_open_mic_threshold = 5;
    
    optional IntendedQueryType type = 4;
    enum IntendedQueryType {
        UNKNOWN = 1;
        OPEN_MIC = 2;
    }
    
    optional bool supports_semantic_filter = 6;
    optional bool supports_magicmic = 7;
    optional string recognizer_language = 8;
    optional string recognizer_mode = 9;
    optional float acoustic_score = 10;
    optional bool supports_lph_filter = 11;
    optional float lph_filter_threshold = 12;
}

message TransliteratedNbest {
    optional TransliterationScript writing_system = 1;
    repeated Hypothesis hypothesis = 2;
}

message EndpointerEvent {
    optional EventType event_type = 1;
    enum EventType {
        START_OF_SPEECH = 0;
        END_OF_SPEECH = 1;
        END_OF_AUDIO = 2;
        END_OF_UTTERANCE = 3;
    }
    
    optional int64 time_usec = 2;
    optional int64 detected_time_usec = 3;
    
    optional EpType end_of_utterance_type = 4;
    enum EpType {
        ACOUSTIC_SILENCE = 1;
        DECODER_SILENCE = 2;
        ACOUSTIC_SILENCE_USING_DECODER = 3;
        FRAME_TIME = 4;
        EARLY_REJECT = 5;
        DECODER_ENDPOINTER_MODEL = 6;
    }
    
    optional int64 silence_threshold_msec = 5;
    optional int32 decoder_latency_msec = 6;
    
    extend Loggable {
        optional EndpointerEvent log_id = 29740232;
    }
}

message AudioEvent {
    optional bytes data = 1;
    
    extend Loggable {
        optional AudioEvent log_id = 29732080;
    }
}

message EncodedAudioEvent {
    optional bytes data = 1;
    
    extend Loggable {
        optional EncodedAudioEvent log_id = 68462908;
    }
}

message AudioFrameCount {
    optional int32 frames_seen = 1;
    optional int64 time_usec = 2;
}

message Prediction {
    optional string text = 1;
}

message PredictedRecognitionEvent {
    optional string text = 1 [deprecated = true];
    repeated Prediction prediction = 2;
    
    extend Loggable {
        optional PredictedRecognitionEvent log_id = 99882807;
    }
}

message PreambleEvent {
    optional bytes data = 1;
    
    extend Loggable {
        optional PreambleEvent log_id = 65825813;
    }
}

message AudioLevelEvent {
    optional float level = 1;
    optional int64 time_usec = 2;
    repeated float posteriors = 3;
    
    extend Loggable {
        optional AudioLevelEvent log_id = 30316041;
    }
}

message DecoderEvent {
    optional int64 end_time_usec = 1;
    optional int32 silence_suffix_frames = 2;
    optional int32 silence_suffix_ms = 3;
    optional int32 last_segment_frames = 8;
    optional int32 last_segment_ms = 9;
    optional int32 last_phone_to_end_frames = 10;
    optional int32 last_phone_to_end_ms = 11;
    optional bool contains_speech = 6;
    optional float sentence_end_cost = 7;
    optional int32 frames_processed = 5;
    optional string transcript = 12;
    optional int32 num_phones = 13;
    optional int32 num_hypotheses = 14 [default = -1];
    
    extend Loggable {
        optional DecoderEvent log_id = 85912388;
    }
}

message HotwordEvent {
    optional EventType event_type = 1;
    enum EventType {
        START_OF_HOTWORD = 0;
        END_OF_HOTWORD = 1;
    }
    
    optional int64 time_usec = 3;
    
    extend Loggable {
        optional HotwordEvent log_id = 32690191;
    }
}

message VerificationResult {
    optional bool speaker_verified = 1 [deprecated = true];
    optional string speaker_id = 2;
    optional float verification_score = 3;
    repeated speech.s3.ConfidenceIntervalLabel intervals_fired = 4;
    optional bool valid_response = 5 [default = true];
    
    extensions 128 to max;
}

message PartialPart {
    optional string text = 1;
    optional double stability = 2;
    optional SemanticResult semantic_result = 3;
}

message PartialResult {
    repeated PartialPart part = 1;
    optional int64 start_time_usec = 2;
    optional int64 end_time_usec = 3;
    optional int64 frame_step_msec = 16;
    optional AlignmentProto word_align = 4;
    optional AlignmentProto phone_align = 5;
    optional AlignmentProto state_align = 10;
    repeated speech.WordConfFeature word_conf_feature = 6;
    optional HotwordConfidenceFeature hotword_conf_feature = 7;
    optional float hotword_confidence = 8;
    optional bool hotword_low_confidence = 17 [default = false];
    optional bool hotword_fired = 9 [default = false];
    optional int32 hotword_firing_index = 18;
    optional int64 hotword_start_time_usec = 11;
    optional int64 hotword_end_time_usec = 12;
    optional bytes lattice_fst = 13;
    repeated Hypothesis hypothesis = 14;
    repeated TransliteratedNbest transliteration = 20;
    repeated VerificationResult verification_result = 15;
    optional IntendedQueryResult intended_query_result = 19;
}

message RecognitionEvent {
    optional EventType event_type = 1;
    enum EventType {
        RECOGNITION_RESULT = 0;
        RECOGNITION_COMPLETED = 1;
        RECOGNITION_PRELIMINARY_RESULT = 2;
        RECOGNITION_SUBMIT_RESULT = 3;
    }
    
    optional StatusCode status = 2;
    optional RecognitionResult result = 3;
    optional PartialResult partial_result = 4;
    optional RecognitionResult combined_result = 5;
    optional RecognitionResult prefetch_result = 7;
    optional int64 generation_time_ms = 6;
    
    optional DecoderStats decoder_stats = 10;
    message DecoderStats {
        optional int32 total_active_arcs = 1;
        optional int32 total_active_states = 2;
        optional int32 total_model_states = 4;
        
        repeated DecoderFrameStats frame_stats = 3;
        message DecoderFrameStats {
            optional int32 frame_active_arcs = 1;
            optional int32 frame_active_states = 2;
            optional int32 frame_private_states = 3;
            optional float frame_beam_threshold = 4;
            optional bool frame_beam_throttled = 5;
        }
    }
    
    optional int64 end_of_preamble_ms = 8;
    optional int64 end_of_preamble_frame = 9;
    optional int64 end_of_segment_flush_ms = 11;
    
    extend Loggable {
        optional RecognitionEvent log_id = 29732111;
    }
}

message MultiLangEvent {
    optional EventType event_type = 1;
    enum EventType {
        UNKNOWN_EVENT_TYPE = 0;
        FINAL = 1;
        IN_PROGRESS = 2;
    }
    
    optional StatusType status_type = 2;
    enum StatusType {
        UNKNOWN_STATUS_TYPE = 0;
        LANGUAGE_DETERMINED = 1;
        LANGUAGE_UNDETERMINED = 2;
    }
    
    optional PuntReason punt_reason = 6;
    enum PuntReason {
        UNKNOWN = 0;
        NONE = 1;
        DECISION_NOT_TRIGGERED = 2;
        NO_LANGID_EVENT = 3;
        INVALID_LANGID_EVENT = 4;
        MISSING_COMBINED_SCORES = 5;
    }
    
    optional string selected_language = 3;
    optional speech.multilang.CombinedMultilangScores combined_scores = 4;
    
    repeated RecognizerStatus recognizer_status = 5;
    message RecognizerStatus {
        optional string language = 1;
        optional bool has_text = 2;
        optional bool finished = 3;
    }
    
    extend Loggable {
        optional MultiLangEvent log_id = 240474159;
    }
}

enum StatusCode {
    STATUS_SUCCESS = 0;
    STATUS_INITIALIZATION_ERROR = 1;
    STATUS_RECOGNITION_ERROR = 2;
    STATUS_STREAM_BROKEN = 3;
    STATUS_CANCELED = 4;
}
