package defpackage;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;

/* renamed from: gng  reason: default package */
/* compiled from: PG */
public final class gng implements Application.ActivityLifecycleCallbacks {
    final /* synthetic */ Application.ActivityLifecycleCallbacks a;
    final /* synthetic */ gnk b;

    public gng(gnk gnk, Application.ActivityLifecycleCallbacks activityLifecycleCallbacks) {
        this.a = activityLifecycleCallbacks;
        this.b = gnk;
    }

    public final void onActivityCreated(Activity activity, Bundle bundle) {
        if (ftd.W(goq.a)) {
            this.a.onActivityCreated(activity, bundle);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityCreated"));
        try {
            this.a.onActivityCreated(activity, bundle);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onActivityDestroyed(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityDestroyed(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityDestroyed"));
        try {
            this.a.onActivityDestroyed(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onActivityPaused(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPaused(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPaused"));
        try {
            this.a.onActivityPaused(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPostCreated(Activity activity, Bundle bundle) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPostCreated(activity, bundle);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPostCreated"));
        try {
            this.a.onActivityPostCreated(activity, bundle);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPostDestroyed(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPostDestroyed(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPostDestroyed"));
        try {
            this.a.onActivityPostDestroyed(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPostPaused(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPostPaused(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPostPaused"));
        try {
            this.a.onActivityPostPaused(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPostResumed(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPostResumed(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPostResumed"));
        try {
            this.a.onActivityPostResumed(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPostSaveInstanceState(Activity activity, Bundle bundle) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPostSaveInstanceState(activity, bundle);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPostSaveInstanceState"));
        try {
            this.a.onActivityPostSaveInstanceState(activity, bundle);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPostStarted(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPostStarted(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPostStarted"));
        try {
            this.a.onActivityPostStarted(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPostStopped(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPostStopped(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPostStopped"));
        try {
            this.a.onActivityPostStopped(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPreCreated(Activity activity, Bundle bundle) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPreCreated(activity, bundle);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPreCreated"));
        try {
            this.a.onActivityPreCreated(activity, bundle);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPreDestroyed(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPreDestroyed(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPreDestroyed"));
        try {
            this.a.onActivityPreDestroyed(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPrePaused(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPrePaused(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPrePaused"));
        try {
            this.a.onActivityPrePaused(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPreResumed(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPreResumed(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPreResumed"));
        try {
            this.a.onActivityPreResumed(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPreSaveInstanceState(Activity activity, Bundle bundle) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPreSaveInstanceState(activity, bundle);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPreSaveInstanceState"));
        try {
            this.a.onActivityPreSaveInstanceState(activity, bundle);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPreStarted(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPreStarted(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPreStarted"));
        try {
            this.a.onActivityPreStarted(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public void onActivityPreStopped(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityPreStopped(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityPreStopped"));
        try {
            this.a.onActivityPreStopped(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onActivityResumed(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityResumed(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityResumed"));
        try {
            this.a.onActivityResumed(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onActivitySaveInstanceState(Activity activity, Bundle bundle) {
        if (ftd.W(goq.a)) {
            this.a.onActivitySaveInstanceState(activity, bundle);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivitySaveInstanceState"));
        try {
            this.a.onActivitySaveInstanceState(activity, bundle);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onActivityStarted(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityStarted(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityStarted"));
        try {
            this.a.onActivityStarted(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onActivityStopped(Activity activity) {
        if (ftd.W(goq.a)) {
            this.a.onActivityStopped(activity);
            return;
        }
        gmd d = this.b.d(String.valueOf(activity.getClass().getName()).concat("#onActivityStopped"));
        try {
            this.a.onActivityStopped(activity);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }
}
