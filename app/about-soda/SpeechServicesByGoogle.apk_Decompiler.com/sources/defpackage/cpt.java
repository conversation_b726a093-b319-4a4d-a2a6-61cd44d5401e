package defpackage;

import android.os.Build;

/* renamed from: cpt  reason: default package */
/* compiled from: PG */
public final class cpt {
    public static final int[] a = {19, 16, 13, 10, 0, -2, -4, -5, -6, -8};

    static {
        "ranchu".equals(Build.HARDWARE);
    }

    static int a(int i) {
        int i2;
        int i3 = 0;
        while (true) {
            int[] iArr = a;
            int length = iArr.length;
            i2 = 10;
            if (i3 >= 10) {
                break;
            }
            i2 = i3 + 1;
            if (i >= iArr[i3]) {
                break;
            }
            i3 = i2;
        }
        return i2;
    }
}
