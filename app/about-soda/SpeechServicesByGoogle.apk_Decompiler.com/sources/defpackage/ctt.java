package defpackage;

/* renamed from: ctt  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ctt implements hkn {
    public final /* synthetic */ cuf a;
    public final /* synthetic */ cuv b;
    public final /* synthetic */ ctg c;
    public final /* synthetic */ boolean d;
    public final /* synthetic */ csk e;
    public final /* synthetic */ String f;

    public /* synthetic */ ctt(cuf cuf, cuv cuv, ctg ctg, boolean z, csk csk, String str) {
        this.a = cuf;
        this.b = cuv;
        this.c = ctg;
        this.d = z;
        this.e = csk;
        this.f = str;
    }

    public final hme a() {
        cuf cuf = this.a;
        cyw cyw = cuf.i;
        cuv cuv = this.b;
        cyw cyw2 = cuf.j;
        String str = cuv.a;
        hme g = cyw.g(str);
        hme g2 = cyw2.g(str);
        return cqh.X(g, g2).o(new ctr(cuf, g, g2, this.c, this.d, this.e, this.f), cuf.d);
    }
}
