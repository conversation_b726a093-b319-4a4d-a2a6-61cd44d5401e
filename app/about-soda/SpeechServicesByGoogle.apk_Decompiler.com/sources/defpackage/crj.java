package defpackage;

import android.content.Context;
import android.os.UserManager;

/* renamed from: crj  reason: default package */
/* compiled from: PG */
public final class crj {
    public static final /* synthetic */ int a = 0;
    private static UserManager b = null;
    private static volatile boolean c = false;
    private static boolean d = false;

    private crj() {
    }

    public static Context a(Context context) {
        if (ag$$ExternalSyntheticApiModelOutline1.m(context)) {
            return context;
        }
        return ag$$ExternalSyntheticApiModelOutline1.m(context);
    }

    public static hme b(Context context, Runnable runnable) {
        if (!e(context)) {
            return kq.f(new bpv((Object) runnable, (Object) context, 2));
        }
        runnable.run();
        return hma.a;
    }

    public static synchronized void c() {
        synchronized (crj.class) {
            d = true;
        }
    }

    public static boolean d(Context context) {
        if (!f(context)) {
            return true;
        }
        return false;
    }

    public static boolean e(Context context) {
        if (f(context)) {
            return true;
        }
        return false;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:27:0x0067, code lost:
        return r3;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    private static boolean f(android.content.Context r5) {
        /*
            boolean r0 = c
            r1 = 1
            if (r0 == 0) goto L_0x0006
            return r1
        L_0x0006:
            java.lang.Class<crj> r0 = defpackage.crj.class
            monitor-enter(r0)
            boolean r2 = c     // Catch:{ all -> 0x0068 }
            if (r2 == 0) goto L_0x000f
            monitor-exit(r0)     // Catch:{ all -> 0x0068 }
            return r1
        L_0x000f:
            boolean r2 = d     // Catch:{ all -> 0x0068 }
            if (r2 == 0) goto L_0x005e
            android.content.Intent r2 = new android.content.Intent     // Catch:{ all -> 0x0068 }
            r2.<init>()     // Catch:{ all -> 0x0068 }
            java.lang.Class<com.google.android.libraries.directboot.DirectBootHelperService> r3 = com.google.android.libraries.directboot.DirectBootHelperService.class
            java.lang.String r3 = r3.getName()     // Catch:{ all -> 0x0068 }
            android.content.Intent r2 = r2.setClassName(r5, r3)     // Catch:{ all -> 0x0068 }
            android.content.pm.PackageManager r3 = r5.getPackageManager()     // Catch:{ all -> 0x0068 }
            r4 = 268435968(0x10000200, float:2.524509E-29)
            java.util.List r2 = r3.queryIntentServices(r2, r4)     // Catch:{ all -> 0x0068 }
            r3 = 0
            if (r2 == 0) goto L_0x0062
            boolean r4 = r2.isEmpty()     // Catch:{ all -> 0x0068 }
            if (r4 != 0) goto L_0x0062
            java.util.Iterator r2 = r2.iterator()     // Catch:{ all -> 0x0068 }
        L_0x003a:
            boolean r4 = r2.hasNext()     // Catch:{ all -> 0x0068 }
            if (r4 == 0) goto L_0x005c
            java.lang.Object r4 = r2.next()     // Catch:{ all -> 0x0068 }
            android.content.pm.ResolveInfo r4 = (android.content.pm.ResolveInfo) r4     // Catch:{ all -> 0x0068 }
            android.content.pm.ServiceInfo r4 = r4.serviceInfo     // Catch:{ all -> 0x0068 }
            boolean r4 = r4.directBootAware     // Catch:{ all -> 0x0068 }
            if (r4 == 0) goto L_0x003a
            java.lang.String r2 = "DirectBootUtils"
            java.lang.String r4 = "Falling back to user manager."
            android.util.Log.w(r2, r4)     // Catch:{ all -> 0x0068 }
            d = r3     // Catch:{ all -> 0x0068 }
            boolean r3 = g(r5)     // Catch:{ all -> 0x0068 }
            goto L_0x0062
        L_0x005c:
            r3 = r1
            goto L_0x0062
        L_0x005e:
            boolean r3 = g(r5)     // Catch:{ all -> 0x0068 }
        L_0x0062:
            if (r3 == 0) goto L_0x0066
            c = r1     // Catch:{ all -> 0x0068 }
        L_0x0066:
            monitor-exit(r0)     // Catch:{ all -> 0x0068 }
            return r3
        L_0x0068:
            r5 = move-exception
            monitor-exit(r0)     // Catch:{ all -> 0x0068 }
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.crj.f(android.content.Context):boolean");
    }

    /* JADX WARNING: Code restructure failed: missing block: B:13:0x0028, code lost:
        if (r2.isUserRunning(android.os.Process.myUserHandle()) != false) goto L_0x0038;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:16:0x0038, code lost:
        r0 = false;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    private static boolean g(android.content.Context r6) {
        /*
            r0 = 1
            r1 = r0
        L_0x0002:
            r2 = 2
            r3 = 0
            r4 = 0
            if (r1 > r2) goto L_0x0038
            android.os.UserManager r2 = b
            if (r2 != 0) goto L_0x0015
            java.lang.Class<android.os.UserManager> r2 = android.os.UserManager.class
            java.lang.Object r2 = r6.getSystemService(r2)
            android.os.UserManager r2 = (android.os.UserManager) r2
            b = r2
        L_0x0015:
            android.os.UserManager r2 = b
            if (r2 != 0) goto L_0x001a
            return r0
        L_0x001a:
            boolean r5 = r2.isUserUnlocked()     // Catch:{ NullPointerException -> 0x002b }
            if (r5 != 0) goto L_0x0039
            android.os.UserHandle r5 = android.os.Process.myUserHandle()     // Catch:{ NullPointerException -> 0x002b }
            boolean r6 = r2.isUserRunning(r5)     // Catch:{ NullPointerException -> 0x002b }
            if (r6 != 0) goto L_0x0038
            goto L_0x0039
        L_0x002b:
            r2 = move-exception
            java.lang.String r4 = "DirectBootUtils"
            java.lang.String r5 = "Failed to check if user is unlocked."
            android.util.Log.w(r4, r5, r2)
            b = r3
            int r1 = r1 + 1
            goto L_0x0002
        L_0x0038:
            r0 = r4
        L_0x0039:
            if (r0 == 0) goto L_0x003d
            b = r3
        L_0x003d:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.crj.g(android.content.Context):boolean");
    }
}
