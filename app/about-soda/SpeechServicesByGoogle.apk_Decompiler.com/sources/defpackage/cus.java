package defpackage;

import android.util.Pair;
import androidx.wear.ambient.AmbientLifecycleObserverKt;
import androidx.wear.ambient.AmbientModeSupport;
import java.io.File;
import java.util.concurrent.Executor;

/* renamed from: cus  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cus implements pq {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    public final /* synthetic */ Object d;
    public final /* synthetic */ Object e;
    private final /* synthetic */ int f;

    public /* synthetic */ cus(cyw cyw, cup cup, File file, String str, djc djc, int i) {
        this.f = i;
        this.a = cyw;
        this.b = cup;
        this.c = file;
        this.d = str;
        this.e = djc;
    }

    /* JADX WARNING: type inference failed for: r6v0, types: [java.lang.Object, djc] */
    /* JADX WARNING: type inference failed for: r0v16, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final Object a(po poVar) {
        if (this.f != 0) {
            ? r0 = this.c;
            jnu.e(r0, "$executor");
            r0.execute(new ww(this.e, this.d, this.b, (Object) poVar, 8));
            return jkd.a;
        }
        cur cur = new cur(poVar);
        Object obj = this.c;
        Object obj2 = this.d;
        ? r6 = this.e;
        Object obj3 = this.a;
        cyw cyw = (cyw) obj3;
        Object obj4 = cyw.c;
        cup cup = (cup) this.b;
        dis dis = new dis((diz) obj4, cup.b, (File) obj, (String) obj2, cur, r6);
        dis.l = (AmbientModeSupport.AmbientController) cyw.a;
        if (cuo.c == cup.c) {
            dis.h(dir.WIFI_OR_CELLULAR);
        } else {
            dis.h(dir.WIFI_ONLY);
        }
        int i = cup.d;
        if (i > 0) {
            dis.i = i;
        }
        gxq gxq = cup.e;
        for (int i2 = 0; i2 < ((hal) gxq).c; i2++) {
            Pair pair = (Pair) gxq.get(i2);
            dis.k.m((String) pair.first, (String) pair.second);
        }
        poVar.a(new ai(obj3, obj, obj2, 15, (int[]) null), hld.a);
        boolean e2 = dis.e();
        cyh.e("%s: Data download scheduled for file: %s enqueued: %s", "OffroadFileDownloader", cup.b, Boolean.valueOf(e2));
        if (!e2) {
            poVar.d(new IllegalStateException("Duplicate request for: ".concat(String.valueOf(cup.b))));
        }
        return "Data download scheduled for file ".concat(String.valueOf(cup.b));
    }

    public /* synthetic */ cus(Executor executor, AmbientLifecycleObserverKt ambientLifecycleObserverKt, String str, jmp jmp, abn abn, int i) {
        this.f = i;
        this.c = executor;
        this.a = ambientLifecycleObserverKt;
        this.e = str;
        this.d = jmp;
        this.b = abn;
    }
}
