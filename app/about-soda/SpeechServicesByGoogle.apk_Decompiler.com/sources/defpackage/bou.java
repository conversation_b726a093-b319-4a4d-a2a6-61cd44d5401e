package defpackage;

/* renamed from: bou  reason: default package */
/* compiled from: PG */
public final class bou extends htq implements hvb {
    public static final bou d;
    private static volatile hvh g;
    public int a;
    public int b;
    public bot c;
    private hhy e;
    private byte f = 2;

    static {
        bou bou = new bou();
        d = bou;
        htq.z(bou.class, bou);
    }

    private bou() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return Byte.valueOf(this.f);
        }
        byte b2 = 1;
        if (i2 == 2) {
            return new hvl(d, "\u0001\u0003\u0000\u0001\u0001\u0006\u0003\u0000\u0000\u0001\u0001᠌\u0000\u0003ဉ\u0003\u0006ᐉ\u0002", new Object[]{"a", "b", bqk.b, "c", "e"});
        } else if (i2 == 3) {
            return new bou();
        } else {
            if (i2 == 4) {
                return new htk((htq) d);
            }
            if (i2 == 5) {
                return d;
            }
            if (i2 != 6) {
                if (obj == null) {
                    b2 = 0;
                }
                this.f = b2;
                return null;
            }
            hvh hvh = g;
            if (hvh == null) {
                synchronized (bou.class) {
                    hvh = g;
                    if (hvh == null) {
                        hvh = new htl(d);
                        g = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
