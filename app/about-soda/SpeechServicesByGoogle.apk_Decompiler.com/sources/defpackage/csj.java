package defpackage;

/* renamed from: csj  reason: default package */
/* compiled from: PG */
public final class csj {
    public grh a;
    public grh b;
    public grh c;
    public grh d;
    public boolean e;
    public byte f;
    public int g;
    private String h;
    private grh i;
    private grh j;
    private grh k;
    private int l;

    public csj() {
        throw null;
    }

    public final csk a() {
        String str;
        int i2;
        if (this.f == 7 && (str = this.h) != null && (i2 = this.g) != 0) {
            return new csk(str, this.i, this.j, this.a, this.b, this.k, this.c, this.d, this.l, i2, this.e);
        }
        StringBuilder sb = new StringBuilder();
        if (this.h == null) {
            sb.append(" groupName");
        }
        if ((this.f & 1) == 0) {
            sb.append(" groupSizeBytes");
        }
        if (this.g == 0) {
            sb.append(" showNotifications");
        }
        if ((this.f & 2) == 0) {
            sb.append(" preserveZipDirectories");
        }
        if ((this.f & 4) == 0) {
            sb.append(" verifyIsolatedStructure");
        }
        throw new IllegalStateException("Missing required properties:".concat(sb.toString()));
    }

    public final void b(String str) {
        if (str != null) {
            this.h = str;
            return;
        }
        throw new NullPointerException("Null groupName");
    }

    public final void c(int i2) {
        this.l = i2;
        this.f = (byte) (this.f | 1);
    }

    public csj(byte[] bArr) {
        gqd gqd = gqd.a;
        this.i = gqd;
        this.j = gqd;
        this.a = gqd;
        this.b = gqd;
        this.k = gqd;
        this.c = gqd;
        this.d = gqd;
    }
}
