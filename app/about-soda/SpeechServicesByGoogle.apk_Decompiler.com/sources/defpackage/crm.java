package defpackage;

/* renamed from: crm  reason: default package */
/* compiled from: PG */
public final class crm extends hka {
    Object a;
    Runnable b;

    public crm(Object obj, Runnable runnable) {
        this.a = obj;
        this.b = runnable;
    }

    public final String a() {
        Object obj = this.a;
        if (obj == null) {
            return "";
        }
        return obj.toString();
    }

    /* access modifiers changed from: protected */
    public final void b() {
        this.a = null;
        if (this.b != null && isCancelled()) {
            this.b.run();
        }
        this.b = null;
    }
}
