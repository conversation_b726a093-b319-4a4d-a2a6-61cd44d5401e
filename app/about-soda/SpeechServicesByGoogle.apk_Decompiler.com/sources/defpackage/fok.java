package defpackage;

/* renamed from: fok  reason: default package */
/* compiled from: PG */
public final class fok implements fno {
    private final /* synthetic */ int a;

    public fok(int i) {
        this.a = i;
    }

    /* JADX WARNING: type inference failed for: r0v9, types: [java.lang.Object, fov] */
    /* JADX WARNING: type inference failed for: r2v1, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r10v9, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v3, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: Code restructure failed: missing block: B:18:0x0029, code lost:
        r1 = move-exception;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:19:0x002a, code lost:
        defpackage.jnu.y(r10, r0);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:20:0x002d, code lost:
        throw r1;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final /* synthetic */ java.lang.Object a(defpackage.fnn r10) {
        /*
            r9 = this;
            int r0 = r9.a
            if (r0 == 0) goto L_0x015e
            r1 = 1
            if (r0 == r1) goto L_0x013b
            r2 = 2
            r3 = 0
            r4 = 0
            if (r0 == r2) goto L_0x00bc
            r2 = 3
            if (r0 == r2) goto L_0x008b
            r2 = 4
            if (r0 == r2) goto L_0x002e
            foq r0 = new foq
            r0.<init>()
            java.io.InputStream r10 = r0.a(r10)
            hsu r0 = defpackage.hsu.K(r10)     // Catch:{ all -> 0x0027 }
            dug r0 = defpackage.dug.b(r0)     // Catch:{ all -> 0x0027 }
            defpackage.jnu.y(r10, r4)
            return r0
        L_0x0027:
            r0 = move-exception
            throw r0     // Catch:{ all -> 0x0029 }
        L_0x0029:
            r1 = move-exception
            defpackage.jnu.y(r10, r0)
            throw r1
        L_0x002e:
            java.util.ArrayDeque r0 = new java.util.ArrayDeque
            r0.<init>()
            java.lang.Object r2 = r10.d
            android.net.Uri r2 = (android.net.Uri) r2
            android.net.Uri$Builder r2 = r2.buildUpon()
            android.net.Uri$Builder r2 = r2.fragment(r4)
            android.net.Uri r2 = r2.build()
            java.lang.Object r10 = r10.f
            kjd r10 = (defpackage.kjd) r10
            java.lang.Iterable r2 = r10.d(r2)
            defpackage.fvf.U(r0, r2)
            r4 = 0
        L_0x0050:
            boolean r2 = r0.isEmpty()
            if (r2 != 0) goto L_0x0086
            java.lang.Object r2 = r0.remove()
            android.net.Uri r2 = (android.net.Uri) r2
            boolean r6 = r10.k(r2)
            if (r6 == 0) goto L_0x006a
            java.lang.Iterable r2 = r10.d(r2)
            defpackage.fvf.U(r0, r2)
            goto L_0x0050
        L_0x006a:
            boolean r6 = r10.j(r2)
            if (r6 == 0) goto L_0x0076
            long r6 = r10.c(r2)
            long r4 = r4 + r6
            goto L_0x0050
        L_0x0076:
            java.io.FileNotFoundException r10 = new java.io.FileNotFoundException
            java.lang.Object[] r0 = new java.lang.Object[r1]
            r0[r3] = r2
            java.lang.String r1 = "Child %s could not be opened"
            java.lang.String r0 = java.lang.String.format(r1, r0)
            r10.<init>(r0)
            throw r10
        L_0x0086:
            java.lang.Long r10 = java.lang.Long.valueOf(r4)
            return r10
        L_0x008b:
            foo r0 = new foo
            r0.<init>()
            r0.b()
            java.lang.Object r1 = r10.f
            java.lang.Object r10 = r10.e
            android.net.Uri r10 = (android.net.Uri) r10
            kjd r1 = (defpackage.kjd) r1
            java.lang.Object r10 = r1.e(r10, r0)
            java.io.File r10 = (java.io.File) r10
            r0 = 805306368(0x30000000, float:4.656613E-10)
            android.os.ParcelFileDescriptor r10 = android.os.ParcelFileDescriptor.open(r10, r0)
            r10.getFd()     // Catch:{ all -> 0x00b0 }
            if (r10 == 0) goto L_0x00af
            r10.close()
        L_0x00af:
            return r4
        L_0x00b0:
            r0 = move-exception
            if (r10 == 0) goto L_0x00bb
            r10.close()     // Catch:{ all -> 0x00b7 }
            goto L_0x00bb
        L_0x00b7:
            r10 = move-exception
            r0.addSuppressed(r10)
        L_0x00bb:
            throw r0
        L_0x00bc:
            java.lang.Object r0 = r10.a
            java.lang.Object r1 = r10.e
            android.net.Uri r1 = (android.net.Uri) r1
            java.io.OutputStream r0 = r0.q(r1)
            java.util.ArrayList r1 = new java.util.ArrayList
            r1.<init>()
            r1.add(r0)
            java.lang.Object r2 = r10.c
            boolean r2 = r2.isEmpty()
            if (r2 != 0) goto L_0x010c
            java.lang.Object r2 = r10.c
            java.lang.Object r5 = r10.d
            java.util.ArrayList r6 = new java.util.ArrayList
            r6.<init>()
            java.util.Iterator r2 = r2.iterator()
        L_0x00e3:
            boolean r7 = r2.hasNext()
            if (r7 == 0) goto L_0x00fc
            java.lang.Object r7 = r2.next()
            foz r7 = (defpackage.foz) r7
            r8 = r5
            android.net.Uri r8 = (android.net.Uri) r8
            foy r7 = r7.a(r8)
            if (r7 == 0) goto L_0x00e3
            r6.add(r7)
            goto L_0x00e3
        L_0x00fc:
            boolean r2 = r6.isEmpty()
            if (r2 != 0) goto L_0x0107
            fnm r4 = new fnm
            r4.<init>(r0, r6)
        L_0x0107:
            if (r4 == 0) goto L_0x010c
            r1.add(r4)
        L_0x010c:
            java.lang.Object r10 = r10.b
            java.util.Iterator r10 = r10.iterator()
            boolean r0 = r10.hasNext()
            if (r0 == 0) goto L_0x0131
            java.lang.Object r10 = r10.next()
            ftc r10 = (defpackage.ftc) r10
            java.lang.Object r10 = defpackage.fvf.S(r1)
            java.io.OutputStream r10 = (java.io.OutputStream) r10
            if (r10 == 0) goto L_0x0129
            r10.close()
        L_0x0129:
            foa r10 = new foa
            java.lang.String r0 = "wrapForAppend not supported by compress"
            r10.<init>(r0)
            throw r10
        L_0x0131:
            java.util.Collections.reverse(r1)
            java.lang.Object r10 = r1.get(r3)
            java.io.OutputStream r10 = (java.io.OutputStream) r10
            return r10
        L_0x013b:
            foq r0 = new foq
            r0.<init>()
            java.io.InputStream r10 = r0.a(r10)
            hsu r0 = defpackage.hsu.K(r10)     // Catch:{ all -> 0x0152 }
            dug r0 = defpackage.dug.b(r0)     // Catch:{ all -> 0x0152 }
            if (r10 == 0) goto L_0x0151
            r10.close()
        L_0x0151:
            return r0
        L_0x0152:
            r0 = move-exception
            if (r10 == 0) goto L_0x015d
            r10.close()     // Catch:{ all -> 0x0159 }
            goto L_0x015d
        L_0x0159:
            r10 = move-exception
            r0.addSuppressed(r10)
        L_0x015d:
            throw r0
        L_0x015e:
            boolean r0 = r10.b()
            if (r0 != 0) goto L_0x0172
            java.lang.Object r0 = r10.f
            java.lang.Object r10 = r10.d
            foj r1 = new foj
            android.net.Uri r10 = (android.net.Uri) r10
            kjd r0 = (defpackage.kjd) r0
            r1.<init>(r0, r10)
            return r1
        L_0x0172:
            java.lang.Object r10 = r10.d
            foa r0 = new foa
            java.lang.String r10 = java.lang.String.valueOf(r10)
            java.lang.String r10 = java.lang.String.valueOf(r10)
            java.lang.String r1 = "Transforms are not supported by this Opener: "
            java.lang.String r10 = r1.concat(r10)
            r0.<init>(r10)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.fok.a(fnn):java.lang.Object");
    }
}
