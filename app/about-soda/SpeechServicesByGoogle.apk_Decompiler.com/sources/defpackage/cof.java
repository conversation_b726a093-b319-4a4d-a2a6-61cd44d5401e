package defpackage;

import java.util.concurrent.TimeUnit;

/* renamed from: cof  reason: default package */
/* compiled from: PG */
public final class cof implements Runnable {
    public final Runnable a = this;
    public final /* synthetic */ Runnable b;
    public final /* synthetic */ hmr c;
    public final /* synthetic */ cog d;
    public final /* synthetic */ long e;
    public final /* synthetic */ TimeUnit f;
    public final /* synthetic */ coi g;

    public cof(coi coi, Runnable runnable, hmr hmr, cog cog, long j, TimeUnit timeUnit) {
        this.b = runnable;
        this.c = hmr;
        this.d = cog;
        this.e = j;
        this.f = timeUnit;
        this.g = coi;
    }

    public final void run() {
        this.g.execute(new cmp(this, 5));
    }
}
