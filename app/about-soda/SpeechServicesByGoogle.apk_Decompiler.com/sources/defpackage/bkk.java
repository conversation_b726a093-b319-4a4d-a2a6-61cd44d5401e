package defpackage;

import android.app.AlertDialog;
import android.app.Dialog;
import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.drawable.BitmapDrawable;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

/* renamed from: bkk  reason: default package */
/* compiled from: PG */
public abstract class bkk extends as implements DialogInterface.OnClickListener {
    protected CharSequence ag;
    protected CharSequence ah;
    protected CharSequence ai;
    protected CharSequence aj;
    protected int ak;
    protected BitmapDrawable al;
    public int am;

    /* access modifiers changed from: protected */
    public void B(View view) {
        int i;
        View h = bnv.h(view, 16908299);
        if (h != null) {
            CharSequence charSequence = this.aj;
            if (!TextUtils.isEmpty(charSequence)) {
                i = 0;
                if (h instanceof TextView) {
                    ((TextView) h).setText(charSequence);
                }
            } else {
                i = 8;
            }
            if (h.getVisibility() != i) {
                h.setVisibility(i);
            }
        }
    }

    /* access modifiers changed from: protected */
    public abstract void C(boolean z);

    /* access modifiers changed from: protected */
    public boolean D() {
        return false;
    }

    public final Dialog a(Bundle bundle) {
        bf activity = getActivity();
        this.am = -2;
        AlertDialog.Builder negativeButton = new AlertDialog.Builder(activity).setTitle(this.ag).setIcon(this.al).setPositiveButton(this.ah, this).setNegativeButton(this.ai, this);
        int i = this.ak;
        View view = null;
        if (i != 0) {
            view = LayoutInflater.from(activity).inflate(i, (ViewGroup) null);
        }
        if (view != null) {
            B(view);
            negativeButton.setView(view);
        } else {
            negativeButton.setMessage(this.aj);
        }
        E();
        AlertDialog create = negativeButton.create();
        if (D()) {
            create.setOnShowListener(new bkj(create));
        }
        return create;
    }

    public void onClick(DialogInterface dialogInterface, int i) {
        this.am = i;
    }

    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        if (bundle != null) {
            this.ag = bundle.getCharSequence("CarUiDialogFragment.title");
            this.ah = bundle.getCharSequence("CarUiDialogFragment.positiveText");
            this.ai = bundle.getCharSequence("CarUiDialogFragment.negativeText");
            this.aj = bundle.getCharSequence("CarUiDialogFragment.message");
            this.ak = bundle.getInt("CarUiDialogFragment.layout", 0);
            Bitmap bitmap = (Bitmap) bundle.getParcelable("CarUiDialogFragment.icon");
            if (bitmap != null) {
                this.al = new BitmapDrawable(getResources(), bitmap);
            }
        }
    }

    public final void onDismiss(DialogInterface dialogInterface) {
        boolean z;
        super.onDismiss(dialogInterface);
        if (this.am == -1) {
            z = true;
        } else {
            z = false;
        }
        C(z);
    }

    public void onSaveInstanceState(Bundle bundle) {
        super.onSaveInstanceState(bundle);
        bundle.putCharSequence("CarUiDialogFragment.title", this.ag);
        bundle.putCharSequence("CarUiDialogFragment.positiveText", this.ah);
        bundle.putCharSequence("CarUiDialogFragment.negativeText", this.ai);
        bundle.putCharSequence("CarUiDialogFragment.message", this.aj);
        bundle.putInt("CarUiDialogFragment.layout", this.ak);
        BitmapDrawable bitmapDrawable = this.al;
        if (bitmapDrawable != null) {
            bundle.putParcelable("CarUiDialogFragment.icon", bitmapDrawable.getBitmap());
        }
    }

    /* access modifiers changed from: protected */
    public void E() {
    }
}
