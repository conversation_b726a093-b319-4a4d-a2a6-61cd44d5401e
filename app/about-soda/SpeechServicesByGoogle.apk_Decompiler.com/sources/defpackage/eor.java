package defpackage;

import android.content.Context;
import j$.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.CancellationException;
import java.util.function.Predicate;

/* renamed from: eor  reason: default package */
/* compiled from: PG */
public final class eor {
    private static final hca a = hca.m("com/google/android/libraries/search/audio/microphone/registry/HotwordSessionsRegistry");
    private final elm b;
    private final emr c;
    private final jqs d;
    private final boolean e;
    private final List f = new ArrayList();
    private final eoz g;
    private final dsy h;
    private final cyw i;
    private final cyw j;

    public eor(dvx dvx, elm elm, emr emr, cyw cyw, cyw cyw2, eoz eoz, dsy dsy, jqs jqs, boolean z) {
        jnu.e(dvx, "audioSessionAssembler");
        jnu.e(elm, "micUpdateReporter");
        jnu.e(emr, "audioSessionToMicStateUpdater");
        jnu.e(eoz, "tokenGenerator");
        jnu.e(jqs, "lightweightScope");
        this.b = elm;
        this.c = emr;
        this.j = cyw;
        this.i = cyw2;
        this.g = eoz;
        this.h = dsy;
        this.d = jqs;
        this.e = z;
    }

    /* JADX WARNING: type inference failed for: r1v2, types: [elp, java.lang.Object] */
    private final void h(eon eon) {
        eop eop = (eop) eon;
        ehg ehg = eop.c.f;
        if (ehg == null) {
            ehg = ehg.c;
        }
        ehg ehg2 = ehg;
        cyw cyw = this.j;
        jnu.d(ehg2, "getClientInfo(...)");
        cyw.a.k(ehg2, eop.a, (long) eop.d, eop.b.b);
        ebl ebl = eop.a;
        ehg ehg3 = eop.c.f;
        if (ehg3 == null) {
            ehg3 = ehg.c;
        }
        Object obj = cyw.b;
        eug eug = (eug) obj;
        long j2 = (long) eop.d;
        ehg ehg4 = ehg3;
        hfc.T(ebl.c(), gof.g(new euf(eug, j2, ehg4, 0)), eug.b);
        hfc.T(ebl.a().a(), gof.g(new euf(eug, j2, ehg4, 1)), eug.b);
        Object obj2 = cyw.d;
        ebl ebl2 = eop.a;
        ehg ehg5 = eop.c.f;
        if (ehg5 == null) {
            ehg5 = ehg.c;
        }
        emd emd = (emd) obj2;
        ehg ehg6 = ehg5;
        hfc.T(ebl2.c(), gof.g(new emb(emd, (Predicate) new boa(17), ehg6, (Enum) eaj.FAILED_OPENING_HOTWORD_ERROR_RETRIEVING_STATUS, 3)), emd.c);
        hfc.T(ebl2.a().a(), gof.g(new emb(emd, (Predicate) new boa(18), ehg6, (Enum) eag.FAILED_CLOSING_HOTWORD_ERROR_RETRIEVING_STATUS, 4)), emd.c);
        ebl ebl3 = eop.a;
        hme c2 = ebl3.c();
        gqx b2 = gof.b(new dun(6));
        elm elm = this.b;
        elm.a(3, hke.f(c2, b2, elm.a), hke.f(ebl3.a().a(), gof.b(new dun(7)), elm.a));
        if (eop.e) {
            this.c.i(eop.a, -1, dom.m(eel.SOURCE_EMPTY));
        }
    }

    private static final eop i(eaj eaj, eag eag) {
        return new eop(new eka(eaj, eag), (eow) null, (ebn) null, 0, true, 14);
    }

    /* JADX WARNING: type inference failed for: r0v8, types: [elp, java.lang.Object] */
    public final synchronized ebk a(int i2, eam eam, boolean z) {
        Object obj;
        jnu.e(eam, "reason");
        Iterator it = this.f.iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            if (((eoo) obj).c == i2) {
                break;
            }
        }
        eoo eoo = (eoo) obj;
        if (eoo == null) {
            if (!z) {
                ((hby) a.f().h(hdg.a, "ALT.HotwordSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/HotwordSessionsRegistry", "stopHotwordSession$java_com_google_android_libraries_search_audio_microphone_registry_hotword_sessions_registry", 225, "HotwordSessionsRegistry.kt")).x("#audio# skipping hotword session(%d) stop(%s), inactive", i2, eam.name());
                cyw cyw = this.j;
                jnu.e(eam, "reason");
                htk l = eav.n.l();
                jnu.d(l, "newBuilder(...)");
                byw D = jnu.e(l, "builder");
                D.o(eay.HOTWORD_STOP_LISTENING);
                D.s((long) i2);
                D.t(eam);
                ((eug) cyw.b).d(D.l());
            }
            return eki.h(eag.FAILED_CLOSING_DUE_TO_INACTIVE_SESSION, eam);
        }
        jji.N(this.f, new eoh(i2, 2));
        ((hby) a.f().h(hdg.a, "ALT.HotwordSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/HotwordSessionsRegistry", "markStopped", 239, "HotwordSessionsRegistry.kt")).G("#audio# stopping(%s) hotword session(%d) for %s", eam.name(), Integer.valueOf(eoo.c), cqx.R(eoo.b));
        cyw cyw2 = this.j;
        jnu.e(eam, "reason");
        ehg ehg = eoo.b.f;
        if (ehg == null) {
            ehg = ehg.c;
        }
        ? r0 = cyw2.a;
        jnu.d(ehg, "getClientInfo(...)");
        r0.t(ehg, (long) eoo.c);
        cyw2.t(eay.HOTWORD_STOP_LISTENING, eoo, eam);
        eoo.d.r((CancellationException) null);
        return this.h.g(eoo.c, eam);
    }

    public final synchronized eon b(int i2) {
        Object obj;
        ((hby) a.e().h(hdg.a, "ALT.HotwordSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/HotwordSessionsRegistry", "findHotwordSession$java_com_google_android_libraries_search_audio_microphone_registry_hotword_sessions_registry", 256, "HotwordSessionsRegistry.kt")).s("#audio# find hotword session(%d)", i2);
        Iterator it = this.f.iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            if (((eoo) obj).c == i2) {
                break;
            }
        }
        eoo eoo = (eoo) obj;
        if (eoo != null) {
            return eoo;
        }
        ((hby) a.e().h(hdg.a, "ALT.HotwordSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/HotwordSessionsRegistry", "findHotwordSession$java_com_google_android_libraries_search_audio_microphone_registry_hotword_sessions_registry", 259, "HotwordSessionsRegistry.kt")).s("#audio# no hotword session(%d) found, it's inactive", i2);
        return eop.e(i(eaj.FAILED_OPENING_DUE_TO_INACTIVE_SESSION, eag.FAILED_CLOSING_DUE_TO_INACTIVE_SESSION), (eow) null, (ebn) null, i2, 23);
    }

    public final synchronized List c() {
        ArrayList arrayList;
        List<eon> list = this.f;
        arrayList = new ArrayList(jji.K(list));
        for (eon eon : list) {
            ehg ehg = eon.c().f;
            if (ehg == null) {
                ehg = ehg.c;
            }
            jnu.d(ehg, "getClientInfo(...)");
            arrayList.add(new eos(ehg, eon.a(), new eoe(eon, 2), eon.c()));
        }
        return arrayList;
    }

    public final synchronized void d(eow eow, eam eam) {
        jnu.e(eam, "reason");
        ArrayList<eoo> arrayList = new ArrayList<>();
        for (Object next : this.f) {
            if (((eoo) next).a.a == eow.a) {
                arrayList.add(next);
            }
        }
        if (arrayList.isEmpty()) {
            ((hby) a.e().h(hdg.a, "ALT.HotwordSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/HotwordSessionsRegistry", "stopRouteHotwordSessions", 175, "HotwordSessionsRegistry.kt")).C("#audio# no sessions left to stop(%s) for %s", eam.name(), eow.b());
            return;
        }
        for (eoo eoo : arrayList) {
            ebk unused = a(eoo.c, eam, false);
        }
    }

    /* JADX WARNING: type inference failed for: r3v0, types: [elp, java.lang.Object] */
    public final synchronized ekf e(int i2, eam eam) {
        Object obj;
        jnu.e(eam, "reason");
        Iterator it = this.f.iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            if (((eoo) obj).c == i2) {
                break;
            }
        }
        eoo eoo = (eoo) obj;
        if (eoo == null) {
            ((hby) a.f().h(hdg.a, "ALT.HotwordSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/HotwordSessionsRegistry", "stopHotwordSessionSeamlessly$java_com_google_android_libraries_search_audio_microphone_registry_hotword_sessions_registry", 193, "HotwordSessionsRegistry.kt")).x("#audio# skipping hotword session(%d) seamless stop(%s), inactive", i2, eam.name());
            cyw cyw = this.j;
            jnu.e(eam, "reason");
            htk l = eav.n.l();
            jnu.d(l, "newBuilder(...)");
            byw D = jnu.e(l, "builder");
            D.o(eay.HOTWORD_STOP_LISTENING_SEAMLESS);
            D.s((long) i2);
            D.t(eam);
            ((eug) cyw.b).d(D.l());
            eag eag = eag.FAILED_CLOSING_DUE_TO_INACTIVE_SESSION;
            htk l2 = ebr.c.l();
            jnu.d(l2, "newBuilder(...)");
            return eki.l(eag, jnu.e(l2, "builder").u(), eam);
        }
        cyw cyw2 = this.j;
        jnu.e(eam, "reason");
        ehg ehg = eoo.b.f;
        if (ehg == null) {
            ehg = ehg.c;
        }
        ? r3 = cyw2.a;
        jnu.d(ehg, "getClientInfo(...)");
        r3.t(ehg, (long) eoo.c);
        cyw2.t(eay.HOTWORD_STOP_LISTENING_SEAMLESS, eoo, eam);
        return this.h.j(i2, eam);
    }

    /* JADX WARNING: type inference failed for: r6v1, types: [elp, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v38, types: [java.util.Collection, java.lang.Object, java.lang.Iterable] */
    /* JADX WARNING: type inference failed for: r13v37, types: [java.util.Collection, java.lang.Object, java.lang.Iterable] */
    public final synchronized eon g(ebn ebn, eow eow, eoz eoz) {
        eol eol;
        jnu.e(ebn, "params");
        int m = this.g.m();
        ((hby) a.f().h(hdg.a, "ALT.HotwordSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/HotwordSessionsRegistry", "startHotwordSession$java_com_google_android_libraries_search_audio_microphone_registry_hotword_sessions_registry", 78, "HotwordSessionsRegistry.kt")).x("#audio# starting hotword session(%d) for %s", m, cqx.R(ebn));
        jnu.e(ebn, "params");
        ehg ehg = ebn.f;
        if (ehg == null) {
            ehg = ehg.c;
        }
        cyw cyw = this.j;
        jnu.d(ehg, "getClientInfo(...)");
        long j2 = (long) m;
        Duration ofNanos = Duration.ofNanos(cqx.F());
        jnu.d(ofNanos, "ofNanos(...)");
        cyw.a.s(ehg, j2, ofNanos);
        htk l = eav.n.l();
        jnu.d(l, "newBuilder(...)");
        byw D = jnu.e(l, "builder");
        D.o(eay.HOTWORD_START_LISTENING);
        jnu.e(ebn, "value");
        Object obj = D.a;
        if (!((htk) obj).b.B()) {
            ((htk) obj).u();
        }
        Object obj2 = cyw.b;
        eav eav = (eav) ((htk) obj).b;
        ebn.getClass();
        eav.c = ebn;
        eav.b = 202;
        D.r((long) eow.a);
        D.s(j2);
        ((eug) obj2).d(D.l());
        Object obj3 = cyw.d;
        ehg ehg2 = ebn.f;
        if (ehg2 == null) {
            ehg2 = ehg.c;
        }
        ((emd) obj3).g(ehg2, "HOTWORD");
        eop eop = null;
        if (eow.c) {
            eol = new eol(eaj.FAILED_OPENING_DUE_TO_INACTIVE_AUDIO_ROUTE_SESSION, eag.FAILED_CLOSING_DUE_TO_INACTIVE_AUDIO_ROUTE_SESSION, "the associated audio route is already inactive");
        } else {
            ? r1 = eoz.a;
            if (!(r1 instanceof Collection) || !r1.isEmpty()) {
                Iterator it = r1.iterator();
                while (true) {
                    if (!it.hasNext()) {
                        break;
                    } else if (!((Boolean) ((eos) it.next()).c.a()).booleanValue()) {
                        int A = a.A(ebn.g);
                        if (A != 0) {
                            if (A == 2) {
                                ? r13 = eoz.a;
                                if (!(r13 instanceof Collection) || !r13.isEmpty()) {
                                    Iterator it2 = r13.iterator();
                                    while (true) {
                                        if (it2.hasNext()) {
                                            if (((eos) it2.next()).a.a == 34 && this.e) {
                                                break;
                                            }
                                        } else {
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        eol = new eol(eaj.FAILED_OPENING_HOTWORD_DUE_TO_AUDIO_REQUEST_CLIENT_LISTENING, eag.FAILED_CLOSING_HOTWORD_DUE_TO_AUDIO_REQUEST_CLIENT_LISTENING, "not allowed while audio client is listening (concurrent mode insufficient)");
                    }
                }
            }
            cyw cyw2 = this.i;
            jnu.e(ebn, "params");
            eol j3 = cyw2.j();
            if (j3 == null) {
                dyt dyt = ebn.d;
                if (dyt == null) {
                    dyt = dyt.l;
                }
                Object obj4 = cyw2.b;
                int i2 = dyt.b;
                if (cqx.M((Context) ((dku) obj4).a)) {
                    if (i2 != 1999) {
                        j3 = new eol(eaj.FAILED_OPENING_HOTWORD_INCORRECT_AUDIO_SOURCE_TYPE, eag.FAILED_CLOSING_HOTWORD_INCORRECT_AUDIO_SOURCE_TYPE, a.am(i2, "audio source ", " is not allowed"));
                    }
                }
                j3 = null;
            }
            if (j3 == null) {
                if (!((emt) cyw2.d).c()) {
                    j3 = new eol(eaj.FAILED_OPENING_OP_NOT_ALLOWED, eag.FAILED_CLOSING_OP_NOT_ALLOWED, "record audio OP not allowed");
                } else {
                    j3 = null;
                }
            }
            if (j3 == null) {
                dyt dyt2 = ebn.d;
                if (dyt2 == null) {
                    dyt2 = dyt.l;
                }
                if (dyt2.b != 1999 || cqx.M((Context) cyw2.a)) {
                    eol = null;
                } else {
                    eol = new eol(eaj.FAILED_OPENING_PERMISSION_CAPTURE_AUDIO_HOTWORD_DENIED, eag.FAILED_CLOSING_PERMISSION_CAPTURE_AUDIO_HOTWORD_DENIED, "no CAPTURE_AUDIO_HOTWORD permission");
                }
            } else {
                eol = j3;
            }
        }
        if (eol == null) {
            ((hby) a.f().h(hdg.a, "ALT.HotwordSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/HotwordSessionsRegistry", "checkHotwordSessionStartConditions", 139, "HotwordSessionsRegistry.kt")).s("#audio# hotword session(%d) OK to start", m);
        } else {
            ((hby) a.h().h(hdg.a, "ALT.HotwordSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/HotwordSessionsRegistry", "checkHotwordSessionStartConditions", 145, "HotwordSessionsRegistry.kt")).G("#audio# hotword session(%d) start conditions failed for %s: %s", Integer.valueOf(m), cqx.R(ebn), eol.c);
            eop = eop.e(i(eol.a, eol.b), eow, ebn, m, 17);
            h(eop);
        }
        if (eop != null) {
            return eop;
        }
        ((hby) a.f().h(hdg.a, "ALT.HotwordSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/HotwordSessionsRegistry", "enforceConcurrencyStateOnNewHotwordSession", 159, "HotwordSessionsRegistry.kt")).r("#audio# enforcing concurrency state on a new hotword session");
        eoo eoo = (eoo) jji.u(this.f);
        if (eoo != null) {
            ebk unused = a(eoo.c, eam.NEW_HOTWORD_CLIENT_LISTENING, false);
        }
        emr emr = this.c;
        if (m == -1) {
            ((hby) emr.a.h().h(hdg.a, "ALT.SessionMicUpdater").j("com/google/android/libraries/search/audio/microphone/AudioSessionToMicStateUpdater", "reportUpcomingHotwordSession", 90, "AudioSessionToMicStateUpdater.kt")).r("#audio# unexpected inactive token, skip reporting");
        } else {
            eqr eqr = emr.b;
            htk l2 = eqp.e.l();
            jnu.d(l2, "newBuilder(...)");
            bzj ad = jnu.e(l2, "builder");
            ad.R(ept.CLIENT_TYPE_HOTWORD);
            ad.T(emr.g(m));
            ad.S(epq.REQUEST_OPEN_PENDING);
            eqr.d(ad.Q());
        }
        eop eop2 = new eop(this.h.h(eow.a(), new epl(m, ebn)), eow, ebn, m, false, 16);
        h(eop2);
        if (eop2.e) {
            return eop2;
        }
        cqx.Q(eop2, this.d, new eoq(this, m, 0));
        this.f.add(new eoo(eop2.a, eop2.b, eop2.c, eop2.d, eop2.e, cqx.P(eop2, this.d, new eoq(this, m, 2))));
        return eop2;
    }
}
