package defpackage;

import android.os.StrictMode;
import java.util.concurrent.ThreadFactory;

/* renamed from: cnt  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cnt implements ThreadFactory {
    public final /* synthetic */ ThreadFactory a;
    public final /* synthetic */ StrictMode.ThreadPolicy b;

    public /* synthetic */ cnt(ThreadFactory threadFactory, StrictMode.ThreadPolicy threadPolicy) {
        this.a = threadFactory;
        this.b = threadPolicy;
    }

    public final Thread newThread(Runnable runnable) {
        return this.a.newThread(new ckm((Object) this.b, (Object) runnable, 7));
    }
}
