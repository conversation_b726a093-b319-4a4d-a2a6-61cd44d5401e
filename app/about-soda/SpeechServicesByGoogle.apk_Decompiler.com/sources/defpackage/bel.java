package defpackage;

import android.content.Intent;
import java.util.Objects;

/* renamed from: bel  reason: default package */
/* compiled from: PG */
public final class bel implements Runnable {
    private final /* synthetic */ int a;
    private final Object b;

    public bel(apl apl, int i) {
        this.a = i;
        this.b = apl;
    }

    public final void run() {
        boolean isEmpty;
        boolean isEmpty2;
        if (this.a != 0) {
            ant ant = (ant) this.b;
            for (aoa aoa : ant.i) {
                aoa.k(true);
                aoa.i();
            }
            iqs iqs = ant.x;
            if (iqs.c != null) {
                iqs.c = null;
            }
            iqs.a = null;
            return;
        }
        bbk.a();
        bem.e();
        Object obj = this.b;
        synchronized (((bem) obj).g) {
            if (((bem) obj).h != null) {
                bbk.a();
                Objects.toString(((bem) obj).h);
                if (((Intent) ((bem) obj).g.remove(0)).equals(((bem) obj).h)) {
                    ((bem) obj).h = null;
                } else {
                    throw new IllegalStateException("Dequeue-d command is not the first.");
                }
            }
            Object obj2 = ((bem) obj).j.a;
            bee bee = ((bem) obj).f;
            synchronized (bee.d) {
                isEmpty = bee.c.isEmpty();
            }
            if (isEmpty) {
                if (((bem) obj).g.isEmpty()) {
                    synchronized (((bih) obj2).b) {
                        isEmpty2 = ((bih) obj2).a.isEmpty();
                    }
                    if (isEmpty2) {
                        bbk.a();
                        bek bek = ((bem) obj).i;
                        if (bek != null) {
                            bek.a();
                        }
                    }
                }
            }
            if (!((bem) obj).g.isEmpty()) {
                ((bem) obj).c();
            }
        }
    }

    public bel(bem bem, int i) {
        this.a = i;
        this.b = bem;
    }
}
