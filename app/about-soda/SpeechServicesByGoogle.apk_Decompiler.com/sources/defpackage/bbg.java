package defpackage;

import androidx.wear.ambient.AmbientLifecycleObserverKt;

/* renamed from: bbg  reason: default package */
/* compiled from: PG */
public final class bbg extends AmbientLifecycleObserverKt {
    public final bat a;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bbg() {
        super((byte[]) null);
        bat bat = bat.a;
        this.a = bat;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        return this.a.equals(((bbg) obj).a);
    }

    public final int hashCode() {
        return 3016889 + this.a.hashCode();
    }

    public final String toString() {
        return "Failure {mOutputData=" + this.a + '}';
    }
}
