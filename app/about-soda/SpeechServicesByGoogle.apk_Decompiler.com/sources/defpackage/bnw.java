package defpackage;

import android.car.drivingstate.CarUxRestrictions;
import android.car.drivingstate.CarUxRestrictionsManager;

/* renamed from: bnw  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bnw implements CarUxRestrictionsManager.OnUxRestrictionsChangedListener {
    public final /* synthetic */ bnz a;

    public /* synthetic */ bnw(bnz bnz) {
        this.a = bnz;
    }

    public final void onUxRestrictionsChanged(CarUxRestrictions carUxRestrictions) {
        bnz bnz = this.a;
        if (carUxRestrictions == null) {
            bnz.a = bnz.a();
        } else {
            bnz.a = carUxRestrictions;
        }
        for (bny a2 : bnz.b) {
            a2.a(bnz.a);
        }
    }
}
