package defpackage;

import android.content.Context;

/* renamed from: bcz  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bcz implements awo {
    public final /* synthetic */ Context a;

    public /* synthetic */ bcz(Context context) {
        this.a = context;
    }

    public final awp a(dtn dtn) {
        Context context = this.a;
        jnu.e(context, "$context");
        awn n = wc.n(context);
        n.a = dtn.a;
        n.b = (awm) dtn.e;
        n.c = true;
        n.d = true;
        return new axa().a(n.a());
    }
}
