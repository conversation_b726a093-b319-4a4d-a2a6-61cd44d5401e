<?xml version="1.0" encoding="utf-8"?>
<!-- Copyright (C) 2020 The Android Open Source Project

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

      http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->
<resources xmlns:tools="http://schemas.android.com/tools"
    tools:keep="
               @array/car_ui_*,
               @attr/CarUi*,
               @attr/actionEnabled,
               @attr/actionShown,
               @attr/activatable,
               @attr/activated,
               @attr/barrierAllowsGoneWidgets,
               @attr/barrierAllowsGoneWidgets,
               @attr/barrierDirection,
               @attr/barrierDirection,
               @attr/barrierMargin,
               @attr/barrierMargin,
               @attr/carUi*,
               @attr/car_ui_*,
               @attr/chainUseRtl,
               @attr/checkable,
               @attr/checked,
               @attr/constraintSet,
               @attr/constraint_referenced_ids,
               @attr/displayBehavior,
               @attr/enableDivider,
               @attr/flow_firstHorizontalBias,
               @attr/flow_firstHorizontalStyle,
               @attr/flow_firstVerticalBias,
               @attr/flow_firstVerticalStyle,
               @attr/flow_horizontalAlign,
               @attr/flow_horizontalBias,
               @attr/flow_horizontalGap,
               @attr/flow_horizontalStyle,
               @attr/flow_lastHorizontalBias,
               @attr/flow_lastHorizontalStyle,
               @attr/flow_lastVerticalBias,
               @attr/flow_lastVerticalStyle,
               @attr/flow_maxElementsWrap,
               @attr/flow_verticalAlign,
               @attr/flow_verticalBias,
               @attr/flow_verticalGap,
               @attr/flow_verticalStyle,
               @attr/flow_wrapMode,
               @attr/id,
               @attr/layoutDescription,
               @attr/layoutManager,
               @attr/layoutStyle,
               @attr/layout_constrainedHeight,
               @attr/layout_constrainedWidth,
               @attr/layout_constraintBaseline_creator,
               @attr/layout_constraintBaseline_toBaselineOf,
               @attr/layout_constraintBottom_creator,
               @attr/layout_constraintBottom_toBottomOf,
               @attr/layout_constraintBottom_toTopOf,
               @attr/layout_constraintCircle,
               @attr/layout_constraintCircleAngle,
               @attr/layout_constraintCircleRadius,
               @attr/layout_constraintDimensionRatio,
               @attr/layout_constraintEnd_toEndOf,
               @attr/layout_constraintEnd_toStartOf,
               @attr/layout_constraintGuide_begin,
               @attr/layout_constraintGuide_end,
               @attr/layout_constraintGuide_percent,
               @attr/layout_constraintHeight_default,
               @attr/layout_constraintHeight_max,
               @attr/layout_constraintHeight_min,
               @attr/layout_constraintHeight_percent,
               @attr/layout_constraintHorizontal_bias,
               @attr/layout_constraintHorizontal_chainStyle,
               @attr/layout_constraintHorizontal_weight,
               @attr/layout_constraintLeft_creator,
               @attr/layout_constraintLeft_toLeftOf,
               @attr/layout_constraintLeft_toRightOf,
               @attr/layout_constraintRight_creator,
               @attr/layout_constraintRight_toLeftOf,
               @attr/layout_constraintRight_toRightOf,
               @attr/layout_constraintStart_toEndOf,
               @attr/layout_constraintStart_toStartOf,
               @attr/layout_constraintTag,
               @attr/layout_constraintTop_creator,
               @attr/layout_constraintTop_toBottomOf,
               @attr/layout_constraintTop_toTopOf,
               @attr/layout_constraintVertical_bias,
               @attr/layout_constraintVertical_chainStyle,
               @attr/layout_constraintVertical_weight,
               @attr/layout_constraintWidth_default,
               @attr/layout_constraintWidth_max,
               @attr/layout_constraintWidth_min,
               @attr/layout_constraintWidth_percent,
               @attr/layout_editor_absoluteX,
               @attr/layout_editor_absoluteY,
               @attr/layout_goneMarginBottom,
               @attr/layout_goneMarginEnd,
               @attr/layout_goneMarginLeft,
               @attr/layout_goneMarginRight,
               @attr/layout_goneMarginStart,
               @attr/layout_goneMarginTop,
               @attr/layout_optimizationLevel,
               @attr/logo,
               @attr/menuItems,
               @attr/numOfColumns,
               @attr/onClick,
               @attr/preferenceStyle,
               @attr/reverseLayout,
               @attr/rotaryScrollEnabled,
               @attr/search,
               @attr/searchHint,
               @attr/secondaryActionIcon,
               @attr/secondaryActionStyle,
               @attr/secondaryActionText,
               @attr/settings,
               @attr/showBackground,
               @attr/showIconAndTitle,
               @attr/showMenuItemsWhileSearching,
               @attr/showTabsInSubpage,
               @attr/state_ux_restricted,
               @attr/tinted,
               @attr/title,
               @attr/uxRestrictions,
               @attr/visible,
               @attr/widgetLayout,
               @bool/car_ui_*,
               @color/car_ui_*,
               @dimen/car_ui_*,
               @dimen/wrap_content,
               @drawable/car_ui_*,
               @font/car_ui_*,
               @id/action_widget_container,
               @id/car_ui_*,
               @id/container,
               @id/list,
               @id/nested_recycler_view_layout,
               @id/radio_button,
               @id/recycler_view,
               @id/search,
               @id/seek_bar,
               @id/seek_bar_text_left,
               @id/seek_bar_text_right,
               @id/seek_bar_text_top,
               @id/seekbar,
               @id/seekbar_value,
               @id/spinner,
               @id/textbox,
               @id/title_template,
               @id/toolbar,
               @integer/car_ui_*,
               @layout/car_ui_*,
               @raw/car_ui_*,
               @string/car_ui_*,
               @style/*CarUi*,
               "
  />

