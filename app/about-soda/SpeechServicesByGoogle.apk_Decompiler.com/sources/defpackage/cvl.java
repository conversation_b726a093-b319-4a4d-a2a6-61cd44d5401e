package defpackage;

/* renamed from: cvl  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvl implements hko {
    public final /* synthetic */ cvy a;
    public final /* synthetic */ csx b;
    public final /* synthetic */ csv c;
    public final /* synthetic */ ctj d;
    public final /* synthetic */ long e;
    public final /* synthetic */ int f;

    public /* synthetic */ cvl(cvy cvy, int i, csx csx, csv csv, ctj ctj, long j) {
        this.a = cvy;
        this.f = i;
        this.b = csx;
        this.c = csv;
        this.d = ctj;
        this.e = j;
    }

    public final hme a(Object obj) {
        if (((Boolean) obj).booleanValue() || this.f == 6) {
            return hma.a;
        }
        long j = this.e;
        ctj ctj = this.d;
        csv csv = this.c;
        return this.a.r(this.b, csv, ctj, j);
    }
}
