package defpackage;

import android.app.AppOpsManager;
import android.content.Context;
import android.os.Build;
import android.os.Process;
import java.util.concurrent.Executor;

/* renamed from: emt  reason: default package */
/* compiled from: PG */
public final class emt {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/microphone/accountability/RecordAudioOpChecker");
    public final Context b;
    public final Executor c;

    public emt(Context context, Executor executor) {
        this.b = context;
        this.c = executor;
    }

    public final void a(hme hme, hme hme2, int i, String str) {
        hfc.T(hme, gof.g(new etl(this, i, str, hme2, 1)), this.c);
    }

    public final void b(String str) {
        if (Build.VERSION.SDK_INT >= 30) {
            AppOpsManager appOpsManager = (AppOpsManager) this.b.getSystemService("appops");
            if (appOpsManager == null) {
                ((hby) ((hby) a.h()).j("com/google/android/libraries/search/audio/microphone/accountability/RecordAudioOpChecker", "finishOp", 102, "RecordAudioOpChecker.java")).r("#audio# unable to get access to app ops manager for startOp");
                return;
            }
            appOpsManager.finishOp("android:record_audio", Process.myUid(), this.b.getPackageName(), str);
        }
    }

    public final boolean c() {
        if (!tt.b()) {
            return true;
        }
        AppOpsManager appOpsManager = (AppOpsManager) this.b.getSystemService("appops");
        if (appOpsManager == null) {
            ((hby) ((hby) a.h()).j("com/google/android/libraries/search/audio/microphone/accountability/RecordAudioOpChecker", "isRecordAudioOpAllowed", 44, "RecordAudioOpChecker.java")).r("#audio# Unable to get access to app ops manager");
            return true;
        }
        if (appOpsManager.unsafeCheckOpNoThrow("android:record_audio", Process.myUid(), this.b.getPackageName()) == 0) {
            return true;
        }
        return false;
    }
}
