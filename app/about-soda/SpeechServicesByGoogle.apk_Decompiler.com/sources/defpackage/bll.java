package defpackage;

import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;

/* renamed from: bll  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bll implements View.OnGenericMotionListener {
    public final /* synthetic */ ViewGroup a;

    public /* synthetic */ bll(ViewGroup viewGroup) {
        this.a = viewGroup;
    }

    public final boolean onGenericMotion(View view, MotionEvent motionEvent) {
        int i = blp.d;
        if (motionEvent.getAction() != 8 || motionEvent.getSource() != 4194304) {
            return false;
        }
        ViewGroup viewGroup = this.a;
        MotionEvent obtain = MotionEvent.obtain(motionEvent);
        obtain.setSource(8194);
        viewGroup.onGenericMotionEvent(obtain);
        return true;
    }
}
