package defpackage;

import android.bluetooth.BluetoothAdapter;

/* renamed from: ekj  reason: default package */
/* compiled from: PG */
public final class ekj extends jmi implements jne {
    final /* synthetic */ Object a;
    private /* synthetic */ Object b;
    private final /* synthetic */ int c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public ekj(egu egu, jlr jlr, int i) {
        super(2, jlr);
        this.c = i;
        this.a = egu;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        if (this.c != 0) {
            return ((ekj) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
        return ((ekj) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    public final Object bk(Object obj) {
        Object obj2;
        Object obj3 = null;
        if (this.c != 0) {
            jji.c(obj);
            jqs jqs = (jqs) this.b;
            boolean z = false;
            if (cqx.N(((egu) this.a).b)) {
                try {
                    BluetoothAdapter bluetoothAdapter = ((egu) this.a).o;
                    if (bluetoothAdapter != null) {
                        obj2 = Boolean.valueOf(bluetoothAdapter.isEnabled());
                    } else {
                        obj2 = null;
                    }
                } catch (Throwable th) {
                    obj2 = jji.b(th);
                }
                if (true != (obj2 instanceof jjt)) {
                    obj3 = obj2;
                }
                if (jnu.i(obj3, true) && ((egu) this.a).e.isBluetoothScoAvailableOffCall()) {
                    z = true;
                }
            }
            return Boolean.valueOf(z);
        }
        jji.c(obj);
        jqs jqs2 = (jqs) this.b;
        job.S(jqs2, (jlv) null, (jqt) null, new edc((ekp) this.a, (jlr) null, 9), 3);
        job.S(jqs2, (jlv) null, (jqt) null, new edc((ekp) this.a, (jlr) null, 10, (byte[]) null), 3);
        job.S(jqs2, (jlv) null, (jqt) null, new edc((ekp) this.a, (jlr) null, 11, (char[]) null), 3);
        return jkd.a;
    }

    public final jlr c(Object obj, jlr jlr) {
        if (this.c != 0) {
            ekj ekj = new ekj((egu) this.a, jlr, 1);
            ekj.b = obj;
            return ekj;
        }
        ekj ekj2 = new ekj((ekp) this.a, jlr, 0);
        ekj2.b = obj;
        return ekj2;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public ekj(ekp ekp, jlr jlr, int i) {
        super(2, jlr);
        this.c = i;
        this.a = ekp;
    }
}
