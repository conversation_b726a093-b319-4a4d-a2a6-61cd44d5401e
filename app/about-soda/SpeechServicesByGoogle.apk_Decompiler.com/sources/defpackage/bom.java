package defpackage;

import android.app.ActionBar;
import android.content.res.Resources;
import android.os.Build;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.widget.FrameLayout;
import android.widget.Toolbar;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import androidx.wear.ambient.AmbientModeSupport;
import com.google.android.material.appbar.AppBarLayout;
import com.google.android.material.appbar.CollapsingToolbarLayout;
import com.google.android.tts.R;

/* renamed from: bom  reason: default package */
/* compiled from: PG */
public class bom extends bf {
    private cey k;

    public final cey o() {
        if (this.k == null) {
            this.k = new cey(new AmbientModeSupport.AmbientController(this));
        }
        return this.k;
    }

    public void onBackPressed() {
        super.onBackPressed();
        if (bb().a() == 0) {
            finishAfterTransition();
        }
    }

    /* JADX WARNING: type inference failed for: r0v6, types: [java.lang.Object, jna] */
    /* JADX WARNING: type inference failed for: r0v9, types: [java.lang.Object, jna] */
    /* access modifiers changed from: protected */
    public void onCreate(Bundle bundle) {
        ki kiVar;
        if (Build.VERSION.SDK_INT >= 35) {
            wl wlVar = wl.b;
            nh nhVar = new nh(0, 0, wlVar);
            nh nhVar2 = new nh(mt.a, mt.b, wlVar);
            View decorView = getWindow().getDecorView();
            jnu.d(decorView, "window.decorView");
            ? r0 = nhVar.c;
            Resources resources = decorView.getResources();
            jnu.d(resources, "view.resources");
            boolean booleanValue = ((Boolean) r0.a(resources)).booleanValue();
            ? r02 = nhVar2.c;
            Resources resources2 = decorView.getResources();
            jnu.d(resources2, "view.resources");
            boolean booleanValue2 = ((Boolean) r02.a(resources2)).booleanValue();
            if (Build.VERSION.SDK_INT >= 30) {
                kiVar = new mx();
            } else if (Build.VERSION.SDK_INT >= 29) {
                kiVar = new mw();
            } else if (Build.VERSION.SDK_INT >= 28) {
                kiVar = new mv();
            } else {
                kiVar = new mu();
            }
            Window window = getWindow();
            jnu.d(window, "window");
            kiVar.m(nhVar, nhVar2, window, decorView, booleanValue, booleanValue2);
            Window window2 = getWindow();
            jnu.d(window2, "window");
            kiVar.l(window2);
            wa.k(findViewById(16908290), new bon((mr) this, 0));
        }
        super.onCreate(bundle);
        cey o = o();
        View inflate = getLayoutInflater().inflate(R.layout.collapsing_toolbar_base_layout, (ViewGroup) null, false);
        if (inflate instanceof CoordinatorLayout) {
            CoordinatorLayout coordinatorLayout = (CoordinatorLayout) inflate;
        }
        o.c = (CollapsingToolbarLayout) inflate.findViewById(R.id.collapsing_toolbar);
        o.d = (AppBarLayout) inflate.findViewById(R.id.app_bar);
        Object obj = o.c;
        if (obj != null) {
            ((CollapsingToolbarLayout) obj).a.F = 1.1f;
            if (Build.VERSION.SDK_INT >= 33) {
                fue fue = ((CollapsingToolbarLayout) o.c).a;
                fue.G = 3;
                yi yiVar = new yi();
                if (fue.H != yiVar) {
                    fue.H = yiVar;
                    fue.g(true);
                }
            }
        }
        Object obj2 = o.d;
        if (obj2 != null) {
            AppBarLayout.Behavior behavior = new AppBarLayout.Behavior();
            behavior.b = new ftc((byte[]) null);
            ((rz) ((AppBarLayout) obj2).getLayoutParams()).b(behavior);
        }
        o.a = (FrameLayout) inflate.findViewById(R.id.content_frame);
        o.b = (Toolbar) inflate.findViewById(R.id.action_bar);
        Object obj3 = o.e;
        AmbientModeSupport.AmbientController ambientController = (AmbientModeSupport.AmbientController) obj3;
        super.setActionBar((Toolbar) o.b);
        ActionBar actionBar = super.getActionBar();
        if (actionBar != null) {
            actionBar.setDisplayHomeAsUpEnabled(true);
            actionBar.setHomeButtonEnabled(true);
            actionBar.setDisplayShowTitleEnabled(true);
        }
        super.setContentView(inflate);
    }

    public final boolean onNavigateUp() {
        if (bb().a() > 0) {
            bb().Z();
        }
        if (bb().a() != 0) {
            return true;
        }
        finishAfterTransition();
        return true;
    }

    public final void setContentView(int i) {
        Object obj;
        cey cey = this.k;
        if (cey == null) {
            obj = (ViewGroup) findViewById(R.id.content_frame);
        } else {
            obj = cey.a;
        }
        if (obj != null) {
            ((ViewGroup) obj).removeAllViews();
        }
        LayoutInflater.from(this).inflate(i, (ViewGroup) obj);
    }

    public final void setTitle(int i) {
        setTitle(getText(i));
    }

    public final void setTitle(CharSequence charSequence) {
        cey o = o();
        Object obj = o.c;
        if (obj != null) {
            ((CollapsingToolbarLayout) obj).e(charSequence);
        }
        super.setTitle(charSequence);
    }

    public final void setContentView(View view) {
        Object obj;
        cey cey = this.k;
        if (cey == null) {
            obj = (ViewGroup) findViewById(R.id.content_frame);
        } else {
            obj = cey.a;
        }
        if (obj != null) {
            ((ViewGroup) obj).addView(view);
        }
    }

    public final void setContentView(View view, ViewGroup.LayoutParams layoutParams) {
        Object obj;
        cey cey = this.k;
        if (cey == null) {
            obj = (ViewGroup) findViewById(R.id.content_frame);
        } else {
            obj = cey.a;
        }
        if (obj != null) {
            ((ViewGroup) obj).addView(view, layoutParams);
        }
    }
}
