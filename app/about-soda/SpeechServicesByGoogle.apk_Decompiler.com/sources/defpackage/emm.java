package defpackage;

/* renamed from: emm  reason: default package */
/* compiled from: PG */
final class emm extends jmi implements jne {
    Object a;
    Object b;
    int c;
    final /* synthetic */ emr d;
    final /* synthetic */ int e;
    final /* synthetic */ eej f;
    final /* synthetic */ Object g;
    private final /* synthetic */ int h;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public emm(emr emr, int i, eej eej, dyy dyy, jlr jlr, int i2) {
        super(2, jlr);
        this.h = i2;
        this.d = emr;
        this.e = i;
        this.f = eej;
        this.g = dyy;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        if (this.h != 0) {
            return ((emm) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
        return ((emm) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX WARNING: type inference failed for: r5v1, types: [java.lang.Object, ebl] */
    /* JADX WARNING: type inference failed for: r6v0, types: [java.lang.Object, ebl] */
    /* JADX WARNING: type inference failed for: r5v6, types: [dyy, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r6v2, types: [dyy, java.lang.Object] */
    /* JADX WARNING: Code restructure failed: missing block: B:16:0x0064, code lost:
        if (r8.b(r5, (defpackage.grh) r3, (java.lang.String) r4, r7) != r0) goto L_0x0067;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:34:0x00c7, code lost:
        if (r8.c(r5, (defpackage.grh) r3, (java.lang.String) r4, r7) != r0) goto L_0x00ca;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r8) {
        /*
            r7 = this;
            int r0 = r7.h
            r1 = 3
            r2 = 0
            r3 = 2
            r4 = 1
            if (r0 == 0) goto L_0x006b
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r5 = r7.c
            if (r5 == 0) goto L_0x0024
            if (r5 == r4) goto L_0x001e
            if (r5 == r3) goto L_0x0016
            defpackage.jji.c(r8)
            goto L_0x0067
        L_0x0016:
            java.lang.Object r3 = r7.b
            java.lang.Object r4 = r7.a
            defpackage.jji.c(r8)
            goto L_0x0052
        L_0x001e:
            java.lang.Object r4 = r7.a
            defpackage.jji.c(r8)
            goto L_0x003c
        L_0x0024:
            defpackage.jji.c(r8)
            emr r8 = r7.d
            int r5 = r7.e
            eej r6 = r7.f
            java.lang.String r5 = r8.f(r5)
            r7.a = r5
            r7.c = r4
            java.lang.Object r8 = r8.a(r6, r7)
            if (r8 == r0) goto L_0x006a
            r4 = r5
        L_0x003c:
            emr r5 = r7.d
            java.lang.Object r6 = r7.g
            grh r8 = (defpackage.grh) r8
            r7.a = r4
            r7.b = r8
            r7.c = r3
            r3 = r4
            java.lang.String r3 = (java.lang.String) r3
            java.lang.Object r3 = r5.d(r6, r8, r3, r7)
            if (r3 == r0) goto L_0x006a
            r3 = r8
        L_0x0052:
            emr r8 = r7.d
            java.lang.Object r5 = r7.g
            r7.a = r2
            r7.b = r2
            r7.c = r1
            java.lang.String r4 = (java.lang.String) r4
            grh r3 = (defpackage.grh) r3
            java.lang.Object r8 = r8.b(r5, r3, r4, r7)
            if (r8 != r0) goto L_0x0067
            goto L_0x006a
        L_0x0067:
            jkd r8 = defpackage.jkd.a
            return r8
        L_0x006a:
            return r0
        L_0x006b:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r5 = r7.c
            if (r5 == 0) goto L_0x0087
            if (r5 == r4) goto L_0x0081
            if (r5 == r3) goto L_0x0079
            defpackage.jji.c(r8)
            goto L_0x00ca
        L_0x0079:
            java.lang.Object r3 = r7.b
            java.lang.Object r4 = r7.a
            defpackage.jji.c(r8)
            goto L_0x00b5
        L_0x0081:
            java.lang.Object r4 = r7.a
            defpackage.jji.c(r8)
            goto L_0x009f
        L_0x0087:
            defpackage.jji.c(r8)
            emr r8 = r7.d
            int r5 = r7.e
            eej r6 = r7.f
            java.lang.String r5 = r8.g(r5)
            r7.a = r5
            r7.c = r4
            java.lang.Object r8 = r8.a(r6, r7)
            if (r8 == r0) goto L_0x00cd
            r4 = r5
        L_0x009f:
            emr r5 = r7.d
            java.lang.Object r6 = r7.g
            grh r8 = (defpackage.grh) r8
            r7.a = r4
            r7.b = r8
            r7.c = r3
            r3 = r4
            java.lang.String r3 = (java.lang.String) r3
            java.lang.Object r3 = r5.e(r6, r8, r3, r7)
            if (r3 == r0) goto L_0x00cd
            r3 = r8
        L_0x00b5:
            emr r8 = r7.d
            java.lang.Object r5 = r7.g
            r7.a = r2
            r7.b = r2
            r7.c = r1
            java.lang.String r4 = (java.lang.String) r4
            grh r3 = (defpackage.grh) r3
            java.lang.Object r8 = r8.c(r5, r3, r4, r7)
            if (r8 != r0) goto L_0x00ca
            goto L_0x00cd
        L_0x00ca:
            jkd r8 = defpackage.jkd.a
            return r8
        L_0x00cd:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.emm.bk(java.lang.Object):java.lang.Object");
    }

    /* JADX WARNING: type inference failed for: r13v0, types: [java.lang.Object, ebl] */
    /* JADX WARNING: type inference failed for: r6v0, types: [dyy, java.lang.Object] */
    public final jlr c(Object obj, jlr jlr) {
        if (this.h != 0) {
            return new emm(this.d, this.e, this.f, (dyy) this.g, jlr, 1);
        }
        return new emm(this.d, this.e, this.f, (ebl) this.g, jlr, 0);
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public emm(emr emr, int i, eej eej, ebl ebl, jlr jlr, int i2) {
        super(2, jlr);
        this.h = i2;
        this.d = emr;
        this.e = i;
        this.f = eej;
        this.g = ebl;
    }
}
