package defpackage;

/* renamed from: bhl  reason: default package */
/* compiled from: PG */
final class bhl extends auv {
    public bhl(aus aus) {
        super(aus);
    }

    public final String a() {
        return "DELETE FROM workspec WHERE state IN (2, 3, 5) AND (SELECT COUNT(*)=0 FROM dependency WHERE     prerequisite_id=id AND     work_spec_id NOT IN         (SELECT id FROM workspec WHERE state IN (2, 3, 5)))";
    }
}
