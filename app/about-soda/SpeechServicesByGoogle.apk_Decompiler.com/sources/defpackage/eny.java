package defpackage;

/* renamed from: eny  reason: default package */
/* compiled from: PG */
public final class eny implements enr {
    private static final hca n = hca.m("com/google/android/libraries/search/audio/microphone/impl/audiosourcedataaccessor/RefAudioSourceDataAccessor");
    public final grh a;
    public final ekt b;
    public final ekt c;
    public final enr d;
    public final boolean e;
    public final hme f;
    public final grh g;
    public final dyt h;
    public final eej i;
    public final jqh j;
    public final eks k;
    public final eoz l;
    public final dlv m;
    private final enr o;
    private final jqs p;
    private final jjo q;

    public eny(enr enr, grh grh, dlv dlv, eks eks, jqs jqs) {
        this.o = enr;
        this.a = grh;
        this.m = dlv;
        this.k = eks;
        this.p = jqs;
        this.l = don.k(jqs);
        jqh jqh = new jqh();
        this.j = jqh;
        ekt c2 = eks.c("ref-source@");
        this.b = c2;
        jjw jjw = new jjw(new eoe(this, 1));
        this.q = jjw;
        ekt e2 = enr.e();
        this.c = e2 != null ? e2 : c2;
        enr f2 = enr.f();
        this.d = f2;
        this.e = jnu.i(enr, f2);
        this.f = jqw.w(jqh);
        this.g = (grh) jjw.a();
        this.h = enr.b();
        this.i = enr.d();
        job.S(jqs, (jlv) null, (jqt) null, new edc(this, (jlr) null, 13, (byte[]) null), 3);
    }

    public final int a() {
        return this.o.a();
    }

    public final dyt b() {
        return this.h;
    }

    /* JADX WARNING: Removed duplicated region for block: B:12:0x0033  */
    /* JADX WARNING: Removed duplicated region for block: B:17:0x0053  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object c(defpackage.jlr r6) {
        /*
            r5 = this;
            boolean r0 = r6 instanceof defpackage.enx
            if (r0 == 0) goto L_0x0013
            r0 = r6
            enx r0 = (defpackage.enx) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            enx r0 = new enx
            r0.<init>(r5, r6)
        L_0x0018:
            java.lang.Object r6 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            r3 = 1
            if (r2 == 0) goto L_0x0033
            if (r2 != r3) goto L_0x002b
            jqh r1 = r0.e
            eny r0 = r0.d
            defpackage.jji.c(r6)
            goto L_0x004d
        L_0x002b:
            java.lang.IllegalStateException r6 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r6.<init>(r0)
            throw r6
        L_0x0033:
            defpackage.jji.c(r6)
            jqh r6 = r5.j
            enr r2 = r5.d
            hme r2 = r2.j()
            r0.d = r5
            r0.e = r6
            r0.c = r3
            java.lang.Object r0 = defpackage.jqw.x(r2, r0)
            if (r0 == r1) goto L_0x0084
            r1 = r6
            r6 = r0
            r0 = r5
        L_0x004d:
            boolean r6 = r1.O(r6)
            if (r6 == 0) goto L_0x0081
            hca r6 = n
            hco r6 = r6.f()
            hcr r1 = defpackage.hdg.a
            java.lang.String r2 = "ALT.RefSrcDataAccessor"
            hco r6 = r6.h(r1, r2)
            java.lang.String r1 = "completeStoppingAfterOrigin"
            r2 = 135(0x87, float:1.89E-43)
            java.lang.String r3 = "com/google/android/libraries/search/audio/microphone/impl/audiosourcedataaccessor/RefAudioSourceDataAccessor"
            java.lang.String r4 = "RefAudioSourceDataAccessor.kt"
            hco r6 = r6.j(r3, r1, r2, r4)
            hby r6 = (defpackage.hby) r6
            ekt r1 = r0.b
            java.lang.String r1 = r1.b
            java.lang.String r2 = "#audio# source(%s) origin stopped"
            r6.u(r2, r1)
            eks r6 = r0.k
            ekt r1 = r0.c
            ekt r0 = r0.b
            r6.b(r1, r0)
        L_0x0081:
            jkd r6 = defpackage.jkd.a
            return r6
        L_0x0084:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eny.c(jlr):java.lang.Object");
    }

    public final eej d() {
        return this.i;
    }

    public final ekt e() {
        return this.c;
    }

    public final enr f() {
        return this.d;
    }

    public final hme h(eam eam) {
        jnu.e(eam, "stopListeningReason");
        return this.l.o(new egv(this, eam, (jlr) null, 2));
    }

    public final hme i(eal eal, enk enk) {
        jnu.e(eal, "success");
        return this.o.i(eal, enk);
    }

    public final hme j() {
        return this.f;
    }

    public final hme k(enk enk) {
        return this.l.o(new egv(this, enk, (jlr) null, 3));
    }
}
