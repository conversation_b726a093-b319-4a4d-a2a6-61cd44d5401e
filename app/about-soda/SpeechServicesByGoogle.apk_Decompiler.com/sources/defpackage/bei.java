package defpackage;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.PowerManager;
import androidx.work.impl.WorkDatabase;
import androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/* renamed from: bei  reason: default package */
/* compiled from: PG */
final class bei implements Runnable {
    final /* synthetic */ bem a;

    public bei(bem bem) {
        this.a = bem;
    }

    /* JADX WARNING: type inference failed for: r1v7, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v7, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v11, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r9v27, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final void run() {
        bem bem;
        bel bel;
        List<byw> list;
        WorkDatabase workDatabase;
        boolean z;
        synchronized (this.a.g) {
            bem bem2 = this.a;
            bem2.h = (Intent) bem2.g.get(0);
        }
        bem bem3 = this.a;
        Intent intent = bem3.h;
        if (intent != null) {
            String action = intent.getAction();
            int intExtra = bem3.h.getIntExtra("KEY_START_ID", 0);
            bbk.a();
            Objects.toString(this.a.h);
            PowerManager.WakeLock a2 = bil.a(this.a.b, action + " (" + intExtra + ")");
            try {
                bbk.a();
                Objects.toString(a2);
                a2.acquire();
                bem bem4 = this.a;
                bee bee = bem4.f;
                Intent intent2 = bem4.h;
                String action2 = intent2.getAction();
                if ("ACTION_CONSTRAINTS_CHANGED".equals(action2)) {
                    bbk.a();
                    Objects.toString(intent2);
                    int i = beg.a;
                    Context context = bee.b;
                    byw byw = new byw(bem4.e.j, (byte[]) null);
                    List<bhe> d = bem4.e.d.A().d();
                    int i2 = bef.a;
                    boolean z2 = false;
                    boolean z3 = false;
                    boolean z4 = false;
                    boolean z5 = false;
                    for (bhe bhe : d) {
                        baq baq = bhe.k;
                        z2 |= baq.f;
                        z3 |= baq.d;
                        z4 |= baq.g;
                        if (baq.b != bbl.NOT_REQUIRED) {
                            z = true;
                        } else {
                            z = false;
                        }
                        z5 |= z;
                        if (z2 && z3 && z4 && z5) {
                            break;
                        }
                    }
                    Intent intent3 = new Intent("androidx.work.impl.background.systemalarm.UpdateProxies");
                    intent3.setComponent(new ComponentName(context, ConstraintProxyUpdateReceiver.class));
                    intent3.putExtra("KEY_BATTERY_NOT_LOW_PROXY_ENABLED", z2).putExtra("KEY_BATTERY_CHARGING_PROXY_ENABLED", z3).putExtra("KEY_STORAGE_NOT_LOW_PROXY_ENABLED", z4).putExtra("KEY_NETWORK_STATE_PROXY_ENABLED", z5);
                    context.sendBroadcast(intent3);
                    ArrayList arrayList = new ArrayList(d.size());
                    long currentTimeMillis = System.currentTimeMillis();
                    for (bhe bhe2 : d) {
                        if (currentTimeMillis >= bhe2.a() && (!bhe2.b() || byw.V(bhe2))) {
                            arrayList.add(bhe2);
                        }
                    }
                    int size = arrayList.size();
                    for (int i3 = 0; i3 < size; i3++) {
                        bhe bhe3 = (bhe) arrayList.get(i3);
                        String str = bhe3.b;
                        Intent c = bee.c(context, wg.f(bhe3));
                        bbk.a();
                        bem4.j.c.execute(new bej(bem4, c, intExtra));
                    }
                } else if ("ACTION_RESCHEDULE".equals(action2)) {
                    bbk.a();
                    Objects.toString(intent2);
                    bem4.e.k();
                } else {
                    Bundle extras = intent2.getExtras();
                    String[] strArr = {"KEY_WORKSPEC_ID"};
                    if (extras != null) {
                        if (!extras.isEmpty()) {
                            if (extras.get(strArr[0]) != null) {
                                if ("ACTION_SCHEDULE_WORK".equals(action2)) {
                                    bgt e = bee.e(intent2);
                                    bbk.a();
                                    Objects.toString(e);
                                    e.toString();
                                    workDatabase = bem4.e.d;
                                    workDatabase.l();
                                    bhe b = workDatabase.A().b(e.a);
                                    if (b == null) {
                                        bbk.a().f(bee.a, a.ao(e, "Skipping scheduling ", " because it's no longer in the DB"));
                                    } else if (b.c.a()) {
                                        bbk.a().f(bee.a, a.ao(e, "Skipping scheduling ", "because it is finished."));
                                    } else {
                                        long a3 = b.a();
                                        if (!b.b()) {
                                            bbk.a();
                                            Objects.toString(e);
                                            bed.b(bee.b, workDatabase, e, a3);
                                        } else {
                                            bbk.a();
                                            Objects.toString(e);
                                            bed.b(bee.b, workDatabase, e, a3);
                                            bem4.j.c.execute(new bej(bem4, bee.b(bee.b), intExtra));
                                        }
                                        workDatabase.o();
                                    }
                                    workDatabase.m();
                                } else if ("ACTION_DELAY_MET".equals(action2)) {
                                    synchronized (bee.d) {
                                        bgt e2 = bee.e(intent2);
                                        bbk.a();
                                        Objects.toString(e2);
                                        if (!bee.c.containsKey(e2)) {
                                            beh beh = new beh(bee.b, intExtra, bem4, bee.e.I(e2));
                                            bee.c.put(e2, beh);
                                            String str2 = beh.c.a;
                                            beh.h = bil.a(beh.a, str2 + " (" + beh.b + ")");
                                            bbk.a();
                                            Objects.toString(beh.h);
                                            beh.h.acquire();
                                            bhe b2 = beh.d.e.d.A().b(str2);
                                            if (b2 == null) {
                                                beh.f.execute(new alr(beh, 14));
                                            } else {
                                                boolean b3 = b2.b();
                                                beh.i = b3;
                                                if (!b3) {
                                                    bbk.a();
                                                    beh.f.execute(new alr(beh, 15));
                                                } else {
                                                    beh.k = bfc.a(beh.l, b2, beh.j, beh);
                                                }
                                            }
                                        } else {
                                            bbk.a();
                                            Objects.toString(e2);
                                        }
                                    }
                                } else if ("ACTION_STOP_WORK".equals(action2)) {
                                    Bundle extras2 = intent2.getExtras();
                                    String string = extras2.getString("KEY_WORKSPEC_ID");
                                    if (extras2.containsKey("KEY_WORKSPEC_GENERATION")) {
                                        int i4 = extras2.getInt("KEY_WORKSPEC_GENERATION");
                                        list = new ArrayList<>(1);
                                        byw H = bee.e.H(new bgt(string, i4));
                                        if (H != null) {
                                            list.add(H);
                                        }
                                    } else {
                                        list = bee.e.e(string);
                                    }
                                    for (byw byw2 : list) {
                                        bbk.a();
                                        we.k(bem4.k, byw2);
                                        Context context2 = bee.b;
                                        WorkDatabase workDatabase2 = bem4.e.d;
                                        Object obj = byw2.a;
                                        int i5 = bed.a;
                                        bgo x = workDatabase2.x();
                                        bgn f = wf.f(x, (bgt) obj);
                                        if (f != null) {
                                            bed.a(context2, (bgt) obj, f.c);
                                            bbk.a();
                                            Objects.toString(obj);
                                            String str3 = ((bgt) obj).a;
                                            int i6 = ((bgt) obj).b;
                                            ((bgs) x).a.k();
                                            axc d2 = ((bgs) x).b.d();
                                            d2.g(1, str3);
                                            d2.e(2, (long) i6);
                                            try {
                                                ((bgs) x).a.l();
                                                d2.a();
                                                ((bgs) x).a.o();
                                                ((bgs) x).a.m();
                                                ((bgs) x).b.f(d2);
                                            } catch (Throwable th) {
                                                ((bgs) x).b.f(d2);
                                                throw th;
                                            }
                                        }
                                        bem4.a((bgt) byw2.a, false);
                                    }
                                } else if ("ACTION_EXECUTION_COMPLETED".equals(action2)) {
                                    bgt e3 = bee.e(intent2);
                                    boolean z6 = intent2.getExtras().getBoolean("KEY_NEEDS_RESCHEDULE");
                                    bbk.a();
                                    Objects.toString(intent2);
                                    bee.a(e3, z6);
                                } else {
                                    bbk a4 = bbk.a();
                                    String str4 = bee.a;
                                    Objects.toString(intent2);
                                    a4.f(str4, "Ignoring intent ".concat(String.valueOf(intent2)));
                                }
                            }
                        }
                    }
                    bbk.a().c(bee.a, "Invalid request for " + action2 + " , requires KEY_WORKSPEC_ID .");
                }
                bbk.a();
                Objects.toString(a2);
                a2.release();
                bem = this.a;
                bel = new bel(bem, 0);
            } catch (Throwable th2) {
                try {
                    bbk.a().d(bem.a, "Unexpected error in onHandleIntent", th2);
                    bbk.a();
                    Objects.toString(a2);
                    a2.release();
                    bem = this.a;
                    bel = new bel(bem, 0);
                } catch (Throwable th3) {
                    bbk.a();
                    Objects.toString(a2);
                    a2.release();
                    bem bem5 = this.a;
                    bem5.j.c.execute(new bel(bem5, 0));
                    throw th3;
                }
            }
            bem.j.c.execute(bel);
        }
    }
}
