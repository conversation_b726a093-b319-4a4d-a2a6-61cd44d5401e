package defpackage;

/* renamed from: enj  reason: default package */
/* compiled from: PG */
public final class enj implements enk {
    public final int a;

    public enj(int i) {
        this.a = i;
    }

    public final int a() {
        return this.a;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if ((obj instanceof enj) && this.a == ((enj) obj).a) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return this.a;
    }

    public final String toString() {
        return "RelativeOffset(bytes=" + this.a + ")";
    }
}
