package defpackage;

/* renamed from: emz  reason: default package */
/* compiled from: PG */
public final class emz implements dyy {
    private static final hca e = hca.m("com/google/android/libraries/search/audio/microphone/impl/GrpcAudioRequestListeningSessionImpl");
    public final hme a;
    public final hme b;
    public final hme c;
    public final esu d;
    private final ehg f;
    private final hme g;
    private final hme h;
    private final eoz i;

    public emz(ehg ehg, hme hme, hme hme2, hme hme3, hme hme4, hme hme5, esu esu, eoz eoz) {
        this.f = ehg;
        this.a = hme;
        this.b = hme2;
        this.g = hme3;
        this.h = hme4;
        this.c = hme5;
        this.d = esu;
        this.i = eoz;
    }

    public final dyx a() {
        ((hby) e.f().h(hdg.a, "ALT.GrpcARCSession").j("com/google/android/libraries/search/audio/microphone/impl/GrpcAudioRequestListeningSessionImpl", "stopListening", 48, "GrpcAudioRequestListeningSessionImpl.kt")).u("#audio# stopping audio client(%s) session remotely", fbi.s(this.f));
        hme L = hfc.L(this.i.o(new emy(this, (jlr) null)));
        jnu.d(L, "nonCancellationPropagating(...)");
        return new elh(L, 4);
    }

    public final hme b() {
        return this.g;
    }

    public final hme c() {
        return this.h;
    }

    public final /* synthetic */ Object d() {
        return new elh(this, 3);
    }
}
