package defpackage;

import java.util.HashMap;

/* renamed from: esv  reason: default package */
/* compiled from: PG */
public class esv implements ios {
    public /* synthetic */ void a(dzc dzc, jix jix) {
        throw null;
    }

    public /* synthetic */ void b(esy esy, jix jix) {
        throw null;
    }

    public /* synthetic */ void c(dzk dzk, jix jix) {
        throw null;
    }

    public /* synthetic */ void d(etb etb, jix jix) {
        throw null;
    }

    public /* synthetic */ void e(esz esz, jix jix) {
        throw null;
    }

    public /* synthetic */ void f(etd etd, jix jix) {
        throw null;
    }

    public /* synthetic */ void g(ebw ebw, jix jix) {
        throw null;
    }

    public /* synthetic */ void h(ebw ebw, jix jix) {
        throw null;
    }

    public /* synthetic */ void i(eth eth, jix jix) {
        throw null;
    }

    public /* synthetic */ void j(eti eti, jix jix) {
        throw null;
    }

    public /* synthetic */ void k(jix jix) {
        throw null;
    }

    public final ixj l() {
        itb itb = esx.a;
        if (itb == null) {
            synchronized (esx.class) {
                itb = esx.a;
                if (itb == null) {
                    ixj ixj = new ixj("com.google.android.libraries.search.audio.service.AudioService");
                    ixj.c(esx.f());
                    ixj.c(esx.i());
                    ixj.c(esx.a());
                    ixj.c(esx.c());
                    ixj.c(esx.k());
                    ixj.c(esx.j());
                    ixj.c(esx.e());
                    ixj.c(esx.g());
                    ixj.c(esx.h());
                    ixj.c(esx.d());
                    ixj.c(esx.b());
                    itb = new itb(ixj);
                    esx.a = itb;
                }
            }
        }
        HashMap hashMap = new HashMap();
        isa f = esx.f();
        jiw jiw = new jiw(new fbh(this, 0, 1), true);
        String str = itb.a;
        jfb.r(f, jiw, str, itb, hashMap);
        jfb.r(esx.i(), new jiw(new fbh(this, 1, 1), false), str, itb, hashMap);
        jfb.r(esx.a(), new jiw(new fbh(this, 2, 1), true), str, itb, hashMap);
        jfb.r(esx.c(), new jiw(new fbh(this, 3, 1), false), str, itb, hashMap);
        jfb.r(esx.k(), new jiw(new fbh(this, 4, 1), true), str, itb, hashMap);
        jfb.r(esx.j(), new jiw(new fbh(this, 5, 1), true), str, itb, hashMap);
        jfb.r(esx.e(), new jiw(new fbh(this, 6, 1), true), str, itb, hashMap);
        jfb.r(esx.g(), new jiw(new fbh(this, 7, 1), false), str, itb, hashMap);
        jfb.r(esx.h(), new jiw(new fbh(this, 8, 1), false), str, itb, hashMap);
        jfb.r(esx.d(), new jiw(new fbh(this, 9, 1), true), str, itb, hashMap);
        jfb.r(esx.b(), new jiw(new fbh(this, 10, 1), true), str, itb, hashMap);
        return jfb.w(str, itb, hashMap);
    }
}
