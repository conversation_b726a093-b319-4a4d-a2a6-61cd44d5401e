package defpackage;

import android.util.Log;
import java.util.logging.Level;
import java.util.logging.Logger;

/* renamed from: cok  reason: default package */
/* compiled from: PG */
final class cok implements Runnable {
    private static final Logger a = Logger.getLogger("ErrorLoggingExecutor");
    private final Runnable b;
    private final jjk c;

    public cok(Runnable runnable, jjk jjk) {
        this.b = runnable;
        this.c = jjk;
    }

    public final void run() {
        try {
            this.b.run();
        } catch (Throwable th) {
            if (((Boolean) ((grh) ((iiv) this.c).a).d(false)).booleanValue()) {
                Thread currentThread = Thread.currentThread();
                currentThread.getUncaughtExceptionHandler().uncaughtException(currentThread, th);
                return;
            }
            a.logp(Level.SEVERE, "com.google.android.libraries.concurrent.ExceptionHandlingExecutorFactory$ExceptionHandlingOrLoggingRunnable", "run", "Uncaught exception from runnable", th);
            Log.e("ErrorLoggingExecutor", "Uncaught exception from runnable", th);
        }
    }

    public final String toString() {
        return this.b.toString();
    }
}
