package defpackage;

import android.graphics.drawable.Drawable;

/* renamed from: bla  reason: default package */
/* compiled from: PG */
public final class bla extends blf {
    public Drawable a;
    public bjj b;
    public final bkx c;
    public final bky d;
    public boolean e;
    public boolean f = true;
    public boolean g;
    public bkz h;
    public ekf i;

    public bla(bkx bkx) {
        this.c = bkx;
        this.d = bky.STANDARD;
    }

    public final void a(boolean z) {
        if (z != this.e) {
            bkx bkx = this.c;
            if (bkx == bkx.CHECK_BOX || bkx == bkx.SWITCH || bkx == bkx.RADIO_BUTTON) {
                this.e = z;
                bkz bkz = this.h;
                if (bkz != null) {
                    bkz.a(this, z);
                }
            }
        }
    }

    public final void b(CharSequence charSequence) {
        this.b = new bjj(new byw(charSequence));
    }
}
