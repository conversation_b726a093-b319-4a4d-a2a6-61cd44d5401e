package defpackage;

/* renamed from: bgy  reason: default package */
/* compiled from: PG */
final class bgy extends aub {
    public bgy(aus aus) {
        super(aus);
    }

    /* access modifiers changed from: protected */
    public final String a() {
        return "INSERT OR REPLACE INTO `WorkProgress` (`work_spec_id`,`progress`) VALUES (?,?)";
    }

    /* access modifiers changed from: protected */
    public final /* bridge */ /* synthetic */ void c(axc axc, Object obj) {
        wg wgVar = (wg) obj;
        throw null;
    }
}
