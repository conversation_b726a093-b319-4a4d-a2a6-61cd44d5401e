package defpackage;

import android.view.View;

/* renamed from: bks  reason: default package */
/* compiled from: PG */
final class bks implements kn {
    final /* synthetic */ bku a;

    public bks(bku bku) {
        this.a = bku;
    }

    public final void a(View view) {
        bku bku = this.a;
        if (bku.ag == null) {
            bku.ag = view.getParent();
        }
        if (this.a.d.getChildLayoutPosition(view) == this.a.ah) {
            view.requestFocus();
        }
    }

    public final void b() {
    }
}
