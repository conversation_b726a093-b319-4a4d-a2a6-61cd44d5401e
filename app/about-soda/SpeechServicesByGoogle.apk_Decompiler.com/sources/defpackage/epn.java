package defpackage;

/* renamed from: epn  reason: default package */
/* compiled from: PG */
public final class epn {
    public final int a;
    public final Object b;
    public final epp c;
    public final enr d;

    public epn(int i, Object obj, epp epp, enr enr) {
        this.a = i;
        this.b = obj;
        this.c = epp;
        this.d = enr;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof epn)) {
            return false;
        }
        epn epn = (epn) obj;
        if (this.a == epn.a && jnu.i(this.b, epn.b) && jnu.i(this.c, epn.c) && jnu.i(this.d, epn.d)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        Object obj = this.b;
        if (obj == null) {
            i = 0;
        } else {
            htq htq = (htq) obj;
            if (htq.B()) {
                i = htq.i();
            } else {
                int i2 = htq.memoizedHashCode;
                if (i2 == 0) {
                    i2 = htq.i();
                    htq.memoizedHashCode = i2;
                }
                i = i2;
            }
        }
        return (((((this.a * 31) + i) * 31) + this.c.hashCode()) * 31) + this.d.hashCode();
    }

    public final String toString() {
        return "SourceData(sessionToken=" + this.a + ", sessionParams=" + this.b + ", route=" + this.c + ", sourceAccessor=" + this.d + ")";
    }
}
