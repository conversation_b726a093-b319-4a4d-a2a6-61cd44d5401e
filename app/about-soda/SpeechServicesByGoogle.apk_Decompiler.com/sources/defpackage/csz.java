package defpackage;

/* renamed from: csz  reason: default package */
/* compiled from: PG */
public final class csz extends htq implements hvb {
    public static final csz f;
    private static volatile hvh g;
    public int a;
    public int b;
    public int c;
    public long d;
    public int e;

    static {
        csz csz = new csz();
        f = csz;
        htq.z(csz.class, csz);
    }

    private csz() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(f, "\u0001\u0004\u0000\u0001\u0001\u0004\u0004\u0000\u0000\u0000\u0001᠌\u0000\u0002᠌\u0001\u0003᠌\u0003\u0004ဂ\u0002", new Object[]{"a", "b", bqk.t, "c", bqk.s, "e", bqk.r, "d"});
        } else if (i2 == 3) {
            return new csz();
        } else {
            if (i2 == 4) {
                return new htk((htq) f);
            }
            if (i2 == 5) {
                return f;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = g;
            if (hvh == null) {
                synchronized (csz.class) {
                    hvh = g;
                    if (hvh == null) {
                        hvh = new htl(f);
                        g = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
