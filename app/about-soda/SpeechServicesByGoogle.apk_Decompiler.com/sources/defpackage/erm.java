package defpackage;

/* renamed from: erm  reason: default package */
/* compiled from: PG */
public final class erm {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/policies/adapter/SourcePolicyControllerAdapter");
    public final ero b;
    private final boolean c;
    private final boolean d;
    private final boolean e;
    private final boolean f;

    public erm(boolean z, boolean z2, boolean z3, boolean z4, ero ero) {
        jnu.e(ero, "controller");
        this.c = z;
        this.d = z2;
        this.e = z3;
        this.f = z4;
        this.b = ero;
    }

    public final dze a(ejf ejf, dze dze) {
        ecc ecc;
        ewn ewn;
        jnu.e(dze, "params");
        if (!b(ejf)) {
            return dze;
        }
        hby hby = (hby) a.f().h(hdg.a, "ALT.SourcePolAdptr").j("com/google/android/libraries/search/audio/policies/adapter/SourcePolicyControllerAdapter", "updateAudioRequestMicInputParams", 76, "SourcePolicyControllerAdapter.kt");
        ehg ehg = ejf.b().b;
        if (ehg == null) {
            ehg = ehg.c;
        }
        String name = ehf.a(ehg.a).name();
        dzb b2 = dzb.b(ejf.b().c);
        if (b2 == null) {
            b2 = dzb.DEFAULT;
        }
        hby.C("#audio# consulting policy for mic input params client(%s) intent(%s)", name, b2.name());
        htk l = erb.f.l();
        jnu.d(l, "newBuilder(...)");
        erb u = jnu.e(l, "builder").u();
        int b3 = doe.b(dze.d);
        if (b3 != 0) {
            int i = b3 - 1;
            if (i == 0) {
                htk htk = (htk) u.C(5);
                htk.x(u);
                jnu.d(htk, "toBuilder(...)");
                dlv W = jnu.e(htk, "builder");
                if (dze.d == 12) {
                    ecc = (ecc) dze.e;
                } else {
                    ecc = ecc.c;
                }
                jnu.e(ecc, "value");
                htk htk2 = (htk) W.a;
                if (!htk2.b.B()) {
                    htk2.u();
                }
                erb erb = (erb) htk2.b;
                ecc.getClass();
                erb.c = ecc;
                erb.b = 3;
                u = W.u();
            } else if (i == 1) {
                htk htk3 = (htk) u.C(5);
                htk3.x(u);
                jnu.d(htk3, "toBuilder(...)");
                dlv W2 = jnu.e(htk3, "builder");
                if (dze.d == 14) {
                    ewn = ewn.b(((Integer) dze.e).intValue());
                    if (ewn == null) {
                        ewn = ewn.TAG_DO_NOT_USE;
                    }
                } else {
                    ewn = ewn.TAG_DO_NOT_USE;
                }
                jnu.e(ewn, "value");
                htk htk4 = (htk) W2.a;
                if (!htk4.b.B()) {
                    htk4.u();
                }
                erb erb2 = (erb) htk4.b;
                erb2.c = Integer.valueOf(ewn.a());
                erb2.b = 4;
                u = W2.u();
            } else if (i != 2) {
                throw new jjq();
            }
            ero ero = this.b;
            dzc b4 = ejf.b();
            jnu.e(u, "request");
            jnu.e(b4, "params");
            ero.a(b4, false);
            jnu.e(u, "request");
            htk l2 = dze.k.l();
            jnu.d(l2, "newBuilder(...)");
            cxi c2 = jnu.e(l2, "builder");
            htk l3 = dyt.l.l();
            jnu.d(l3, "newBuilder(...)");
            cxi f2 = jnu.e(l3, "builder");
            htk htk5 = (htk) f2.a;
            if (!htk5.b.B()) {
                htk5.u();
            }
            dyt dyt = (dyt) htk5.b;
            dyt.a |= 1;
            dyt.b = 6;
            htk htk6 = (htk) f2.a;
            if (!htk6.b.B()) {
                htk6.u();
            }
            dyt dyt2 = (dyt) htk6.b;
            dyt2.a |= 2;
            dyt2.c = 16000;
            htk htk7 = (htk) f2.a;
            if (!htk7.b.B()) {
                htk7.u();
            }
            dyt dyt3 = (dyt) htk7.b;
            dyt3.a |= 4;
            dyt3.d = 1;
            htk htk8 = (htk) f2.a;
            if (!htk8.b.B()) {
                htk8.u();
            }
            dyt dyt4 = (dyt) htk8.b;
            dyt4.a |= 8;
            dyt4.e = 2;
            f2.i(true);
            dyt h = f2.h();
            jnu.e(h, "value");
            htk htk9 = (htk) c2.a;
            if (!htk9.b.B()) {
                htk9.u();
            }
            dze dze2 = (dze) htk9.b;
            h.getClass();
            dze2.f = h;
            dze2.a |= 1;
            return c2.f();
        }
        throw null;
    }

    public final boolean b(ejf ejf) {
        ehg ehg = ejf.b().b;
        if (ehg == null) {
            ehg = ehg.c;
        }
        if (ehg.a != 39) {
            ehg ehg2 = ejf.b().b;
            if (ehg2 == null) {
                ehg2 = ehg.c;
            }
            if (ehg2.a != 41) {
                ehg ehg3 = ejf.b().b;
                if (ehg3 == null) {
                    ehg3 = ehg.c;
                }
                if (ehg3.a != 16) {
                    ehg ehg4 = ejf.b().b;
                    if (ehg4 == null) {
                        ehg4 = ehg.c;
                    }
                    if (ehg4.a != 19) {
                        ehg ehg5 = ejf.b().b;
                        if (ehg5 == null) {
                            ehg5 = ehg.c;
                        }
                        if (ehg5.a != 18) {
                            ehg ehg6 = ejf.b().b;
                            if (ehg6 == null) {
                                ehg6 = ehg.c;
                            }
                            if (ehg6.a != 6) {
                                ehg ehg7 = ejf.b().b;
                                if (ehg7 == null) {
                                    ehg7 = ehg.c;
                                }
                                if (ehg7.a != 5) {
                                    ehg ehg8 = ejf.b().b;
                                    if (ehg8 == null) {
                                        ehg8 = ehg.c;
                                    }
                                    if (ehg8.a != 43) {
                                        if (this.c) {
                                            ehg ehg9 = ejf.b().b;
                                            if (ehg9 == null) {
                                                ehg9 = ehg.c;
                                            }
                                            if (ehg9.a == 21) {
                                                return true;
                                            }
                                        }
                                        if (this.d) {
                                            ehg ehg10 = ejf.b().b;
                                            if (ehg10 == null) {
                                                ehg10 = ehg.c;
                                            }
                                            if (ehg10.a == 22) {
                                                return true;
                                            }
                                        }
                                        if (this.e) {
                                            ehg ehg11 = ejf.b().b;
                                            if (ehg11 == null) {
                                                ehg11 = ehg.c;
                                            }
                                            if (ehg11.a == 28) {
                                                return true;
                                            }
                                        }
                                        if (!this.f) {
                                            return false;
                                        }
                                        ehg ehg12 = ejf.b().b;
                                        if (ehg12 == null) {
                                            ehg12 = ehg.c;
                                        }
                                        if (ehg12.a == 23) {
                                            return true;
                                        }
                                        return false;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return true;
    }
}
