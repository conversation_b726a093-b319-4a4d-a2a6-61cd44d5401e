package defpackage;

import android.content.Context;
import java.io.IOException;
import java.util.ArrayList;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;

/* renamed from: cws  reason: default package */
/* compiled from: PG */
public final class cws implements cxa {
    public final Context a;
    public final cuk b;
    private final Executor c;
    private final fps d;

    public cws(Context context, cuk cuk, fps fps, Executor executor) {
        this.a = context;
        this.b = cuk;
        this.d = fps;
        this.c = executor;
    }

    public final hme a() {
        return this.d.b(new cwr(1), this.c);
    }

    public final hme b(cwf cwf, int i) {
        hme hme;
        if (i > cwf.d) {
            return hfc.K(true);
        }
        cwf a2 = cwf.a(i);
        int ordinal = a2.ordinal();
        if (ordinal == 1) {
            hme = czw.e(this.d.b(new cwn(this, 15), this.c)).f(new cwr(0), this.c).b(IOException.class, new cwn(this, 9), this.c);
        } else if (ordinal != 2) {
            String name = a2.name();
            hme = hfc.J(new UnsupportedOperationException("Upgrade to version " + name + "not supported!"));
        } else {
            hme = czw.e(this.d.b(new cwn(this, 12), this.c)).f(new cwh(20), this.c).b(IOException.class, new cwn(this, 13), this.c);
        }
        return ftd.L(hme, new fpe(this, i, cwf, 1), this.c);
    }

    public final hme c() {
        AtomicReference atomicReference = new AtomicReference(new ArrayList());
        return ftd.K(this.d.b(new btb(this, atomicReference, 11), this.c), new cwn(atomicReference, 14), this.c);
    }

    public final hme d() {
        if (cqh.v(this.a)) {
            cwf a2 = cwf.a(cqh.l());
            cwf u = cqh.u(this.a, this.b);
            int i = a2.d;
            int i2 = u.d;
            if (i == i2) {
                return hfc.K(true);
            }
            if (i < i2) {
                cyh.i("%s Cannot migrate back from value %s to %s. Clear everything!", "ProtoDataStoreSharedFilesMetadata", u, a2);
                cuk cuk = this.b;
                String valueOf = String.valueOf(u);
                String valueOf2 = String.valueOf(a2);
                new Exception("Downgraded file key from " + valueOf + " to " + valueOf2 + ".");
                cuk.a();
                cqh.w(this.a, a2);
                return hfc.K(false);
            }
            return czw.e(b(a2, i2 + 1)).d(Exception.class, new cwq(this, a2, 0), this.c).g(new cwq(this, a2, 2), this.c);
        }
        cyh.c("%s Device isn't migrated to new file key, clear and set migration.", "ProtoDataStoreSharedFilesMetadata");
        cqh.x(this.a);
        cqh.w(this.a, cwf.a(cqh.l()));
        return hfc.K(false);
    }

    public final hme e(ctj ctj) {
        return ftd.K(f(new hbi(ctj)), new cwn(ctj, 10), hld.a);
    }

    public final hme f(gyo gyo) {
        return ftd.K(this.d.a(), new btb(this, gyo, 9), hld.a);
    }

    public final hme g(ctj ctj) {
        return czw.e(this.d.b(new cwn(cqh.L(ctj, this.a, this.b), 11), this.c)).f(new cwh(18), this.c).b(IOException.class, new cwh(19), this.c);
    }

    public final hme h(ctj ctj, ctl ctl) {
        return czw.e(this.d.b(new btb(cqh.L(ctj, this.a, this.b), ctl, 10, (byte[]) null), this.c)).f(new cwh(16), this.c).b(IOException.class, new cwh(17), this.c);
    }

    public final void i(cwf cwf) {
        if (cqh.u(this.a, this.b).d != cwf.d && !cqh.w(this.a, cwf)) {
            cyh.f(String.valueOf(cwf));
            cuk cuk = this.b;
            new Exception(String.valueOf(cwf));
            cuk.a();
        }
    }
}
