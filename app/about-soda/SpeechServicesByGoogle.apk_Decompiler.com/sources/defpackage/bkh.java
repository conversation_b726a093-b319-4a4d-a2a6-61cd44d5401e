package defpackage;

import android.view.View;
import com.android.car.ui.baselayout.Insets;
import com.google.android.tts.R;

/* renamed from: bkh  reason: default package */
/* compiled from: PG */
final class bkh {
    public final View a;
    public final View b;
    public final View c;
    public final View d;
    public final View e;
    public final View f;
    public bjw g;
    public Insets h = new Insets();

    public bkh(View view, View view2) {
        this.a = view2;
        View i = bnv.i(view, R.id.car_ui_base_layout_content_container);
        this.b = i;
        View findViewWithTag = view.findViewWithTag("car_ui_left_inset");
        this.c = findViewWithTag;
        View findViewWithTag2 = view.findViewWithTag("car_ui_right_inset");
        this.d = findViewWithTag2;
        View findViewWithTag3 = view.findViewWithTag("car_ui_top_inset");
        this.e = findViewWithTag3;
        View findViewWithTag4 = view.findViewWithTag("car_ui_bottom_inset");
        this.f = findViewWithTag4;
        bkg bkg = new bkg(this);
        if (findViewWithTag != null) {
            findViewWithTag.addOnLayoutChangeListener(bkg);
        }
        if (findViewWithTag2 != null) {
            findViewWithTag2.addOnLayoutChangeListener(bkg);
        }
        if (findViewWithTag3 != null) {
            findViewWithTag3.addOnLayoutChangeListener(bkg);
        }
        if (findViewWithTag4 != null) {
            findViewWithTag4.addOnLayoutChangeListener(bkg);
        }
        view2.addOnLayoutChangeListener(bkg);
        i.addOnLayoutChangeListener(bkg);
    }

    public static int a(View view) {
        int[] iArr = new int[2];
        view.getLocationOnScreen(iArr);
        return iArr[1] + view.getHeight();
    }

    public static int b(View view) {
        int[] iArr = new int[2];
        view.getLocationOnScreen(iArr);
        return iArr[0];
    }

    public static int c(View view) {
        int[] iArr = new int[2];
        view.getLocationOnScreen(iArr);
        return iArr[0] + view.getWidth();
    }

    public static int d(View view) {
        int[] iArr = new int[2];
        view.getLocationOnScreen(iArr);
        return iArr[1];
    }
}
