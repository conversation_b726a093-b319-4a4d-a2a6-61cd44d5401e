package defpackage;

import android.app.Activity;
import android.app.Application;
import android.content.ComponentName;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import com.android.car.ui.CarUiLayoutInflaterFactory;
import com.android.car.ui.baselayout.Insets;
import com.android.car.ui.core.BaseLayoutController;
import com.android.car.ui.core.CarUiInstaller;
import com.google.android.tts.R;

/* renamed from: bjz  reason: default package */
/* compiled from: PG */
public final class bjz implements Application.ActivityLifecycleCallbacks {
    private Insets a = null;
    private boolean b = false;

    private static final boolean a(Activity activity) {
        return bnv.p(activity, R.attr.carUiActivity);
    }

    public final void onActivityCreated(Activity activity, Bundle bundle) {
        if (a(activity)) {
            Log.i("CarUiInstaller", "CarUiInstaller started for ".concat(String.valueOf(ComponentName.createRelative(activity, activity.getClass().getName()).flattenToShortString())));
            LayoutInflater from = LayoutInflater.from(activity);
            if (from.getFactory2() == null) {
                from.setFactory2(new CarUiLayoutInflaterFactory());
            } else if (!(from.getFactory2() instanceof CarUiLayoutInflaterFactory) && !(from.getFactory2() instanceof ds)) {
                throw new AssertionError(String.valueOf(String.valueOf(from.getFactory2())).concat(" must extend CarUiLayoutInflaterFactory"));
            }
            CarUiInstaller.a(activity.getClassLoader(), BaseLayoutController.class, "build", (Object) null, activity);
            if (bundle != null) {
                this.a = new Insets(bundle.getInt("CAR_UI_INSET_LEFT"), bundle.getInt("CAR_UI_INSET_TOP"), bundle.getInt("CAR_UI_INSET_RIGHT"), bundle.getInt("CAR_UI_INSET_BOTTOM"));
            }
            this.b = true;
        }
    }

    public final void onActivityDestroyed(Activity activity) {
        if (a(activity)) {
            CarUiInstaller.a(activity.getClassLoader(), BaseLayoutController.class, "destroy", (Object) null, activity);
        }
    }

    public final void onActivityPostStarted(Activity activity) {
        if (a(activity)) {
            Object a2 = CarUiInstaller.a(activity.getClassLoader(), BaseLayoutController.class, "getBaseLayoutController", (Object) null, activity);
            if (this.a != null && a2 != null && this.b) {
                CarUiInstaller.a(activity.getClassLoader(), BaseLayoutController.class, "dispatchNewInsets", a2, CarUiInstaller.b(activity.getClassLoader(), this.a));
                this.b = false;
            }
        }
    }

    public final void onActivitySaveInstanceState(Activity activity, Bundle bundle) {
        Object a2;
        if (a(activity) && (a2 = CarUiInstaller.a(activity.getClassLoader(), BaseLayoutController.class, "getBaseLayoutController", (Object) null, activity)) != null) {
            Object a3 = CarUiInstaller.a(activity.getClassLoader(), BaseLayoutController.class, "getInsets", a2, new Object[0]);
            bundle.putInt("CAR_UI_INSET_LEFT", ((Integer) CarUiInstaller.a(activity.getClassLoader(), Insets.class, "getLeft", a3, new Object[0])).intValue());
            bundle.putInt("CAR_UI_INSET_TOP", ((Integer) CarUiInstaller.a(activity.getClassLoader(), Insets.class, "getTop", a3, new Object[0])).intValue());
            bundle.putInt("CAR_UI_INSET_RIGHT", ((Integer) CarUiInstaller.a(activity.getClassLoader(), Insets.class, "getRight", a3, new Object[0])).intValue());
            bundle.putInt("CAR_UI_INSET_BOTTOM", ((Integer) CarUiInstaller.a(activity.getClassLoader(), Insets.class, "getBottom", a3, new Object[0])).intValue());
        }
    }

    public final void onActivityPaused(Activity activity) {
    }

    public final void onActivityResumed(Activity activity) {
    }

    public final void onActivityStarted(Activity activity) {
    }

    public final void onActivityStopped(Activity activity) {
    }
}
