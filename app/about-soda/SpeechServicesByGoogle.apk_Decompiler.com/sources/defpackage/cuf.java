package defpackage;

import android.content.Context;
import android.net.Uri;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;

/* renamed from: cuf  reason: default package */
/* compiled from: PG */
public final class cuf implements cto {
    public final Context a;
    public final grh b;
    public final cwm c;
    public final Executor d;
    public final grh e;
    public final grh f;
    public final hko g;
    public final cyk h;
    public final cyw i;
    public final cyw j;
    public final kjd k;
    private final List l;
    private final bzj m = new bzj();
    private final bmu n;

    public cuf(Context context, cyk cyk, cwm cwm, Executor executor, List list, grh grh, kjd kjd, grh grh2, grh grh3, bmu bmu) {
        this.a = context;
        this.h = cyk;
        this.l = list;
        this.b = grh;
        this.d = executor;
        this.c = cwm;
        this.k = kjd;
        this.e = grh2;
        this.f = grh3;
        this.n = bmu;
        this.g = new bub(5);
        this.j = cyw.i(executor);
        this.i = new cyw(executor, (czd) new czl(grh3, context, 1));
    }

    public static crv j(String str, int i2, int i3, String str2, hse hse) {
        htk l2 = crv.g.l();
        if (!l2.b.B()) {
            l2.u();
        }
        htq htq = l2.b;
        crv crv = (crv) htq;
        str.getClass();
        crv.a |= 1;
        crv.b = str;
        if (!htq.B()) {
            l2.u();
        }
        htq htq2 = l2.b;
        crv crv2 = (crv) htq2;
        crv2.a |= 4;
        crv2.d = i2;
        if (i3 > 0) {
            if (!htq2.B()) {
                l2.u();
            }
            crv crv3 = (crv) l2.b;
            crv3.a |= 8;
            crv3.e = i3;
        }
        if (str2 != null) {
            if (!l2.b.B()) {
                l2.u();
            }
            crv crv4 = (crv) l2.b;
            crv4.a |= 2;
            crv4.c = str2;
        }
        if (hse != null) {
            if (!l2.b.B()) {
                l2.u();
            }
            crv crv5 = (crv) l2.b;
            crv5.f = hse;
            crv5.a |= 16;
        }
        return (crv) l2.r();
    }

    public static hig k(crw crw) {
        htk l2 = hig.k.l();
        String str = crw.b;
        if (!l2.b.B()) {
            l2.u();
        }
        htq htq = l2.b;
        hig hig = (hig) htq;
        str.getClass();
        hig.a |= 1;
        hig.b = str;
        String str2 = crw.c;
        if (!htq.B()) {
            l2.u();
        }
        htq htq2 = l2.b;
        hig hig2 = (hig) htq2;
        str2.getClass();
        hig2.a |= 4;
        hig2.d = str2;
        int i2 = crw.e;
        if (!htq2.B()) {
            l2.u();
        }
        hig hig3 = (hig) l2.b;
        hig3.a |= 2;
        hig3.c = i2;
        int size = crw.g.size();
        if (!l2.b.B()) {
            l2.u();
        }
        htq htq3 = l2.b;
        hig hig4 = (hig) htq3;
        hig4.a |= 8;
        hig4.e = size;
        String str3 = crw.i;
        if (!htq3.B()) {
            l2.u();
        }
        htq htq4 = l2.b;
        hig hig5 = (hig) htq4;
        str3.getClass();
        hig5.a |= 128;
        hig5.i = str3;
        long j2 = crw.h;
        if (!htq4.B()) {
            l2.u();
        }
        hig hig6 = (hig) l2.b;
        hig6.a |= 64;
        hig6.h = j2;
        return (hig) l2.r();
    }

    public static grh o(ctg ctg, csx csx) {
        if (cqh.k()) {
            if ((ctg.a & 16) != 0) {
                return grh.h(ctg.f);
            }
            return gqd.a;
        } else if (csx != null) {
            return grh.h(csx.s);
        } else {
            return gqd.a;
        }
    }

    public static List p(kjd kjd, Uri uri, String str) {
        ArrayList arrayList = new ArrayList();
        for (Uri uri2 : kjd.d(uri)) {
            if (kjd.k(uri2)) {
                arrayList.addAll(p(kjd, uri2, str));
            } else {
                String path = uri2.getPath();
                if (path != null) {
                    htk l2 = crv.g.l();
                    String replaceFirst = path.replaceFirst(str, "");
                    if (!l2.b.B()) {
                        l2.u();
                    }
                    crv crv = (crv) l2.b;
                    replaceFirst.getClass();
                    crv.a |= 1;
                    crv.b = replaceFirst;
                    int c2 = (int) kjd.c(uri2);
                    if (!l2.b.B()) {
                        l2.u();
                    }
                    crv crv2 = (crv) l2.b;
                    crv2.a |= 4;
                    crv2.d = c2;
                    String uri3 = uri2.toString();
                    if (!l2.b.B()) {
                        l2.u();
                    }
                    crv crv3 = (crv) l2.b;
                    uri3.getClass();
                    crv3.a |= 2;
                    crv3.c = uri3;
                    arrayList.add((crv) l2.r());
                }
            }
        }
        return arrayList;
    }

    public static hme q(csx csx, grh grh, String str, int i2, boolean z, boolean z2, cwm cwm, Executor executor, kjd kjd) {
        hse hse;
        csx csx2 = csx;
        String str2 = str;
        int i3 = i2;
        boolean z3 = z2;
        cwm cwm2 = cwm;
        Executor executor2 = executor;
        if (csx2 == null) {
            return hfc.K((Object) null);
        }
        htk l2 = crw.n.l();
        String str3 = csx2.c;
        if (!l2.b.B()) {
            l2.u();
        }
        htq htq = l2.b;
        crw crw = (crw) htq;
        str3.getClass();
        crw.a |= 1;
        crw.b = str3;
        String str4 = csx2.d;
        if (!htq.B()) {
            l2.u();
        }
        htq htq2 = l2.b;
        crw crw2 = (crw) htq2;
        str4.getClass();
        crw2.a |= 2;
        crw2.c = str4;
        int i4 = csx2.e;
        if (!htq2.B()) {
            l2.u();
        }
        crw crw3 = (crw) l2.b;
        crw3.a |= 8;
        crw3.e = i4;
        hse hse2 = csx2.f;
        if (hse2 == null) {
            hse2 = hse.c;
        }
        if (!l2.b.B()) {
            l2.u();
        }
        htq htq3 = l2.b;
        crw crw4 = (crw) htq3;
        hse2.getClass();
        crw4.k = hse2;
        crw4.a |= 128;
        long j2 = csx2.r;
        if (!htq3.B()) {
            l2.u();
        }
        htq htq4 = l2.b;
        crw crw5 = (crw) htq4;
        crw5.a |= 32;
        crw5.h = j2;
        if (!htq4.B()) {
            l2.u();
        }
        htq htq5 = l2.b;
        crw crw6 = (crw) htq5;
        crw6.f = i3 - 1;
        crw6.a |= 16;
        huf huf = csx2.t;
        if (!htq5.B()) {
            l2.u();
        }
        crw crw7 = (crw) l2.b;
        huf huf2 = crw7.j;
        if (!huf2.c()) {
            crw7.j = htq.s(huf2);
        }
        hrz.g(huf, crw7.j);
        if (grh.f()) {
            Object b2 = grh.b();
            if (!l2.b.B()) {
                l2.u();
            }
            crw crw8 = (crw) l2.b;
            crw8.a |= 64;
            crw8.i = (String) b2;
        }
        if (str2 != null) {
            if (!l2.b.B()) {
                l2.u();
            }
            crw crw9 = (crw) l2.b;
            crw9.a |= 4;
            crw9.d = str2;
        }
        if ((csx2.a & 32) != 0) {
            hse hse3 = csx2.g;
            if (hse3 == null) {
                hse3 = hse.c;
            }
            if (!l2.b.B()) {
                l2.u();
            }
            crw crw10 = (crw) l2.b;
            hse3.getClass();
            crw10.l = hse3;
            crw10.a |= 256;
        }
        huf<csv> huf3 = csx2.n;
        hme hme = hma.a;
        if (i3 != 2) {
            for (csv csv : huf3) {
                String str5 = csv.b;
                int i5 = csv.d;
                int i6 = csv.i;
                if ((csv.a & 8192) != 0) {
                    hse = csv.p;
                    if (hse == null) {
                        hse = hse.c;
                    }
                } else {
                    hse = null;
                }
                l2.A(j(str5, i5, i6, (String) null, hse));
            }
        } else {
            if (csx2.m) {
                fvf.av(true);
                String uri = cqx.i(cwm2.b, cwm2.g, csx).toString();
                if (!l2.b.B()) {
                    l2.u();
                }
                crw crw11 = (crw) l2.b;
                uri.getClass();
                crw11.a |= 1024;
                crw11.m = uri;
            }
            cyh.d("%s: getDataFileUris %s", "MDDManager", csx2.c);
            boolean o = cqx.o(csx);
            gxr gxr = new gxr();
            if (o) {
                gxr.g(cwm2.c.b(csx));
            }
            hme = czw.e(czw.e(cwm.f()).g(new cwl(cwm2, o, z3, csx), cwm2.h).f(new cwg(cwm2, o, z3, gxr.a()), cwm2.h).f(new amv(19), cwm2.h)).g(new cvu((List) huf3, z, kjd, l2, 1), executor2);
        }
        return czw.e(hme).f(new brg(l2, 10), executor2).b(csi.class, new amv(10), executor2);
    }

    private final hme r(boolean z) {
        return czw.e(n()).g(new ctq(this, z, 1), this.d).g(new bpr(this, 10), this.d).g(new ctq(this, z, 0), this.d);
    }

    private final void s(int i2, hme hme, long j2, hig hig, cue cue, cud cud) {
        hme hme2 = hme;
        hme.c(gof.h(new ctv(this, j2, hig, hme, cud, cue, i2, 1)), hld.a);
    }

    public final hme a(crx crx) {
        long F = cqx.F();
        hme q = this.m.q(new ctp(this, crx, 5), this.d);
        htk l2 = hig.k.l();
        String str = crx.a.b;
        if (!l2.b.B()) {
            l2.u();
        }
        htq htq = l2.b;
        hig hig = (hig) htq;
        str.getClass();
        hig.a |= 1;
        hig.b = str;
        long j2 = crx.a.h;
        if (!htq.B()) {
            l2.u();
        }
        htq htq2 = l2.b;
        hig hig2 = (hig) htq2;
        hig2.a |= 64;
        hig2.h = j2;
        String str2 = crx.a.i;
        if (!htq2.B()) {
            l2.u();
        }
        htq htq3 = l2.b;
        hig hig3 = (hig) htq3;
        str2.getClass();
        hig3.a |= 128;
        hig3.i = str2;
        if (!htq3.B()) {
            l2.u();
        }
        htq htq4 = l2.b;
        hig hig4 = (hig) htq4;
        hig4.a |= 32;
        hig4.g = false;
        if (!htq4.B()) {
            l2.u();
        }
        htq htq5 = l2.b;
        hig hig5 = (hig) htq5;
        hig5.a |= 256;
        hig5.j = false;
        int i2 = crx.a.d;
        if (!htq5.B()) {
            l2.u();
        }
        htq htq6 = l2.b;
        hig hig6 = (hig) htq6;
        hig6.a |= 2;
        hig6.c = i2;
        String str3 = crx.a.c;
        if (!htq6.B()) {
            l2.u();
        }
        hig hig7 = (hig) l2.b;
        str3.getClass();
        hig7.a |= 4;
        hig7.d = str3;
        int size = crx.a.g.size();
        if (!l2.b.B()) {
            l2.u();
        }
        hig hig8 = (hig) l2.b;
        hig8.a |= 8;
        hig8.e = size;
        hig hig9 = (hig) l2.r();
        s(3, q, F, hig9, new ctx(hig9), new cty(0));
        return q;
    }

    public final hme b(csk csk) {
        return ftd.J(new ctp(this, csk, 0), this.d);
    }

    public final hme c(csk csk) {
        cyh.c("%s: downloadFileGroupWithForegroundService start.", "MobileDataDownload");
        if (!this.f.f()) {
            return hfc.J(new IllegalArgumentException("downloadFileGroupWithForegroundService: ForegroundDownloadService is not provided!"));
        }
        if (this.e.f()) {
            return ftd.J(new ctp(this, csk, 6), this.d);
        }
        kml a2 = csi.a();
        a2.b = csh.DOWNLOAD_MONITOR_NOT_PROVIDED_ERROR;
        a2.c = "downloadFileGroupWithForegroundService: Download Monitor is not provided!";
        return hfc.J(a2.a());
    }

    public final hme d(csp csp) {
        long F = cqx.F();
        hme q = this.m.q(new ctp(this, csp, 3), this.d);
        htk l2 = hig.k.l();
        if (!l2.b.B()) {
            l2.u();
        }
        String str = csp.a;
        htq htq = l2.b;
        hig hig = (hig) htq;
        str.getClass();
        hig.a |= 1;
        hig.b = str;
        if (!htq.B()) {
            l2.u();
        }
        htq htq2 = l2.b;
        hig hig2 = (hig) htq2;
        hig2.a |= 32;
        hig2.g = false;
        if (!htq2.B()) {
            l2.u();
        }
        htq htq3 = l2.b;
        hig hig3 = (hig) htq3;
        hig3.a |= 2;
        hig3.c = -1;
        if (!htq3.B()) {
            l2.u();
        }
        hig hig4 = (hig) l2.b;
        hig4.a |= 64;
        hig4.h = -1;
        s(4, q, F, (hig) l2.r(), new cts(), new cty(1));
        return q;
    }

    public final hme e(csr csr) {
        return this.m.q(new ctp(this, csr, 2), this.d);
    }

    /* JADX WARNING: Can't fix incorrect switch cases order */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme f(java.lang.String r6) {
        /*
            r5 = this;
            int r0 = r6.hashCode()
            r1 = 0
            r2 = 3
            r3 = 2
            r4 = 1
            switch(r0) {
                case -2105562759: goto L_0x002a;
                case -1202768674: goto L_0x0020;
                case -69128772: goto L_0x0016;
                case 437964371: goto L_0x000c;
                default: goto L_0x000b;
            }
        L_0x000b:
            goto L_0x0034
        L_0x000c:
            java.lang.String r0 = "MDD.WIFI.CHARGING.PERIODIC.TASK"
            boolean r0 = r6.equals(r0)
            if (r0 == 0) goto L_0x0034
            r0 = r2
            goto L_0x0035
        L_0x0016:
            java.lang.String r0 = "MDD.CHARGING.PERIODIC.TASK"
            boolean r0 = r6.equals(r0)
            if (r0 == 0) goto L_0x0034
            r0 = r4
            goto L_0x0035
        L_0x0020:
            java.lang.String r0 = "MDD.CELLULAR.CHARGING.PERIODIC.TASK"
            boolean r0 = r6.equals(r0)
            if (r0 == 0) goto L_0x0034
            r0 = r3
            goto L_0x0035
        L_0x002a:
            java.lang.String r0 = "MDD.MAINTENANCE.PERIODIC.GCM.TASK"
            boolean r0 = r6.equals(r0)
            if (r0 == 0) goto L_0x0034
            r0 = r1
            goto L_0x0035
        L_0x0034:
            r0 = -1
        L_0x0035:
            if (r0 == 0) goto L_0x0074
            if (r0 == r4) goto L_0x005e
            if (r0 == r3) goto L_0x0059
            if (r0 == r2) goto L_0x0054
            java.lang.String r0 = "%s: gcm task doesn't belong to MDD"
            java.lang.String r1 = "MobileDataDownload"
            defpackage.cyh.c(r0, r1)
            java.lang.String r0 = "Unknown task tag sent to MDD.handleTask() "
            java.lang.String r6 = r0.concat(r6)
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            r0.<init>(r6)
            hme r6 = defpackage.hfc.J(r0)
            return r6
        L_0x0054:
            hme r6 = r5.r(r4)
            return r6
        L_0x0059:
            hme r6 = r5.r(r1)
            return r6
        L_0x005e:
            hme r6 = r5.n()
            bpr r0 = new bpr
            r1 = 11
            r0.<init>(r5, r1)
            hko r0 = defpackage.gof.d(r0)
            java.util.concurrent.Executor r1 = r5.d
            hme r6 = defpackage.ftd.L(r6, r0, r1)
            return r6
        L_0x0074:
            bzj r6 = r5.m
            cwm r0 = r5.c
            j$.util.Objects.requireNonNull(r0)
            cpw r1 = new cpw
            r1.<init>(r0, r3)
            java.util.concurrent.Executor r0 = r5.d
            hme r6 = r6.q(r1, r0)
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cuf.f(java.lang.String):hme");
    }

    public final hme g(cuj cuj) {
        return this.m.q(new ctp(this, cuj, 4, (byte[]) null), this.d);
    }

    public final hme h() {
        return this.m.p(new bdr((Object) this, 5), this.d);
    }

    /* JADX WARNING: type inference failed for: r3v1, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r6v1, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final void i(String str) {
        cyh.d("%s: CancelForegroundDownload for key = %s", "MobileDataDownload", str);
        ftd.L(this.i.g(str), new bpr(str, 12), this.d);
        cyh.d("%s: CancelForegroundDownload for Uri = %s", "DownloaderImp", str);
        bmu bmu = this.n;
        ftd.L(ftd.L(((cyw) bmu.c).f(str), new cwq(bmu, str, 13, (byte[]) null), bmu.a), new cxr(str, 6), bmu.a);
    }

    public final hme l(ctg ctg, csx csx, boolean z, boolean z2, boolean z3) {
        String str;
        int i2;
        grh o = o(ctg, csx);
        if ((ctg.a & 4) != 0) {
            str = ctg.d;
        } else {
            str = null;
        }
        String str2 = str;
        if (true != z) {
            i2 = 3;
        } else {
            i2 = 2;
        }
        return ftd.K(q(csx, o, str2, i2, z2, z3, this.c, this.d, this.k), new brg(this, 7), this.d);
    }

    public final hme m(csk csk) {
        cuv a2 = cuv.a(csk.a);
        htk l2 = ctg.g.l();
        if (!l2.b.B()) {
            l2.u();
        }
        String str = csk.a;
        ctg ctg = (ctg) l2.b;
        str.getClass();
        ctg.a |= 1;
        ctg.b = str;
        String packageName = this.a.getPackageName();
        if (!l2.b.B()) {
            l2.u();
        }
        ctg ctg2 = (ctg) l2.b;
        packageName.getClass();
        ctg2.a |= 2;
        ctg2.c = packageName;
        return this.m.q(new ctt(this, a2, (ctg) l2.r(), csk.g.f(), csk, str), this.d);
    }

    public final hme n() {
        ArrayList arrayList = new ArrayList();
        for (csn a2 : this.l) {
            arrayList.add(a2.a(this));
        }
        return cqh.U(arrayList).n(new ctw(0), this.d);
    }
}
