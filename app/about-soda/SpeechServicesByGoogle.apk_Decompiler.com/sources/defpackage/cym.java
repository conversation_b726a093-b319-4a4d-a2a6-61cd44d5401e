package defpackage;

import java.util.HashSet;
import java.util.List;
import java.util.Map;

/* renamed from: cym  reason: default package */
/* compiled from: PG */
public final class cym implements fpk {
    private static String b(String str, String str2) {
        return str + "|" + str2;
    }

    public final /* bridge */ /* synthetic */ hva a(bzl bzl, hva hva) {
        bzl bzl2 = bzl;
        cti cti = (cti) hva;
        if (cti.d) {
            return cti;
        }
        HashSet hashSet = new HashSet();
        htk htk = (htk) cti.C(5);
        htk.x(cti);
        if (!htk.b.B()) {
            htk.u();
        }
        cti cti2 = (cti) htk.b;
        int i = 2;
        cti2.a |= 2;
        cti2.d = true;
        hbp k = bzl.H().entrySet().iterator();
        while (k.hasNext()) {
            List g = gry.c("|").g((CharSequence) ((Map.Entry) k.next()).getKey());
            if (g.size() >= 4) {
                String str = (String) g.get(0);
                String str2 = (String) g.get(1);
                int parseInt = Integer.parseInt((String) g.get(i));
                String str3 = str + "|" + str2 + "|" + parseInt;
                if (!hashSet.contains(str3)) {
                    hashSet.add(str3);
                    String b = b(str3, "w");
                    String b2 = b(str3, "c");
                    long I = bzl2.I(b);
                    long I2 = bzl2.I(b2);
                    htk l = ctb.h.l();
                    htk l2 = ctg.g.l();
                    if (!l2.b.B()) {
                        l2.u();
                    }
                    htq htq = l2.b;
                    ctg ctg = (ctg) htq;
                    str2.getClass();
                    ctg.a |= 1;
                    ctg.b = str2;
                    if (!htq.B()) {
                        l2.u();
                    }
                    ctg ctg2 = (ctg) l2.b;
                    str.getClass();
                    ctg2.a |= 2;
                    ctg2.c = str;
                    if (!l.b.B()) {
                        l.u();
                    }
                    ctb ctb = (ctb) l.b;
                    ctg ctg3 = (ctg) l2.r();
                    ctg3.getClass();
                    ctb.b = ctg3;
                    ctb.a |= 1;
                    if (!l.b.B()) {
                        l.u();
                    }
                    htq htq2 = l.b;
                    ctb ctb2 = (ctb) htq2;
                    ctb2.a |= 8;
                    ctb2.e = parseInt;
                    if (!htq2.B()) {
                        l.u();
                    }
                    htq htq3 = l.b;
                    ctb ctb3 = (ctb) htq3;
                    ctb3.a |= 16;
                    ctb3.f = I2;
                    if (!htq3.B()) {
                        l.u();
                    }
                    ctb ctb4 = (ctb) l.b;
                    ctb4.a |= 32;
                    ctb4.g = I;
                    if (!htk.b.B()) {
                        htk.u();
                    }
                    cti cti3 = (cti) htk.b;
                    ctb ctb5 = (ctb) l.r();
                    ctb5.getClass();
                    cti3.b();
                    cti3.c.add(ctb5);
                    bzl2 = bzl;
                    i = 2;
                }
            }
            bzl2 = bzl;
        }
        return (cti) htk.r();
    }
}
