package defpackage;

import androidx.wear.ambient.AmbientModeSupport;
import com.google.android.libraries.speech.modelmanager.languagepack.ui.DownloadActivity;
import com.google.protobuf.contrib.android.ProtoParsers$InternalDontUse;
import j$.util.Optional;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

/* renamed from: ety  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ety implements Runnable {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    private final /* synthetic */ int d;

    public /* synthetic */ ety(ezz ezz, Optional optional, hmi hmi, int i) {
        this.d = i;
        this.c = ezz;
        this.a = optional;
        this.b = hmi;
    }

    /* JADX WARNING: type inference failed for: r0v6, types: [java.lang.Object, ebg] */
    /* JADX WARNING: type inference failed for: r0v35, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v22, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v28, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v32, types: [java.lang.Object, gch] */
    /* JADX WARNING: type inference failed for: r0v126, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v168, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v183, types: [java.lang.Object, java.lang.Runnable] */
    public final void run() {
        Object obj;
        int i = 0;
        switch (this.d) {
            case 0:
                ((hby) eua.a.c().h(hdg.a, "ALT.GrpcARCRespSender").j("com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender$startInternal$1$4", "invokeSuspend$lambda$0", 109, "StreamListeningSessionResponseSender.kt")).r("#audio# sending MIC audio accessor to the remote client");
                dxx dxx = (dxx) this.b.a().b();
                int d2 = evc.d((dyt) this.c);
                byte[] bArr = new byte[d2];
                int i2 = 0;
                while (true) {
                    try {
                        obj = Integer.valueOf(dxx.a(bArr, i2, 0, d2));
                    } catch (Throwable th) {
                        obj = jji.b(th);
                    }
                    Object obj2 = this.a;
                    Throwable a2 = jju.a(obj);
                    if (a2 == null) {
                        int intValue = ((Number) obj).intValue();
                        if (intValue < 0) {
                            htk l = etf.c.l();
                            jnu.d(l, "newBuilder(...)");
                            dlv t = jnu.e(l, "builder");
                            htk l2 = dyc.g.l();
                            jnu.d(l2, "newBuilder(...)");
                            bzl y = jnu.e(l2, "builder");
                            htk l3 = dyi.c.l();
                            jnu.d(l3, "newBuilder(...)");
                            bzj F = jnu.e(l3, "builder");
                            F.D(dyj.SUCCESS);
                            y.f(F.C());
                            t.o(y.d());
                            ((eua) obj2).f(t.n());
                            return;
                        } else if (intValue > 0) {
                            i2 += intValue;
                            htk l4 = etf.c.l();
                            jnu.d(l4, "newBuilder(...)");
                            dlv t2 = jnu.e(l4, "builder");
                            htk l5 = dyc.g.l();
                            jnu.d(l5, "newBuilder(...)");
                            bzl y2 = jnu.e(l5, "builder");
                            htk l6 = dyb.c.l();
                            jnu.d(l6, "newBuilder(...)");
                            cxi g = jnu.e(l6, "builder");
                            g.k(hsq.s(bArr, 0, intValue));
                            y2.e(g.j());
                            t2.o(y2.d());
                            if (jju.a(((eua) obj2).f(t2.n())) != null) {
                                ((hby) eua.a.h().h(hdg.a, "ALT.GrpcARCRespSender").j("com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender", "sendAudioAccessorBlocking", 286, "StreamListeningSessionResponseSender.kt")).r("#audio# Stopping sending bytes to the remote client");
                                return;
                            }
                        }
                    } else {
                        ((hby) ((hby) eua.a.h().h(hdg.a, "ALT.GrpcARCRespSender")).i(a2).j("com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender", "sendAudioAccessorBlocking", 267, "StreamListeningSessionResponseSender.kt")).r("#audio# Cannot read next audio data on behalf of the remote client, shutting down");
                        ((eua) obj2).i(a2);
                        return;
                    }
                }
            case 1:
                Object obj3 = this.a;
                jnu.e(obj3, "$bufferToWrite");
                ecu ecu = (ecu) this.c;
                if (ecu.f.a == null) {
                    while (true) {
                        eem eem = (eem) this.b;
                        int i3 = eem.a;
                        if (i < i3) {
                            dyc d3 = ecu.g.d(eem.b(jnu.p(ecu.d, i3 - i), i, ecu.b), i, (byte[]) obj3);
                            ecu.i.b = ecu.g.a();
                            if (d3 != null) {
                                ecu.c(d3);
                            }
                            i += ecu.d;
                        } else {
                            ((hby) ((hby) ecu.a.e().h(hdg.a, "ALT.BytesReceiverBuffer")).g(1, TimeUnit.SECONDS).j("com/google/android/libraries/search/audio/audiobuffer/AudioBytesReceiverAudioBuffer", "writeToBufferInternal", 145, "AudioBytesReceiverAudioBuffer.kt")).A("#audio# writing to buffer(%s), written(%d)", ecu.h, ecu.g.a());
                            return;
                        }
                    }
                } else {
                    return;
                }
            case 2:
                Object obj4 = this.a;
                Object obj5 = this.c;
                ((ezz) obj5).l.D(new ejj(obj5, obj4, 9, (byte[]) null), this.b);
                return;
            case 3:
                ((fdj) this.c).a.c.e((fhp) this.b, (fhq) this.a);
                return;
            case 4:
                fer.b(this.c, (String) this.b, (Object[]) this.a);
                return;
            case 5:
                fhm fhm = (fhm) this.a;
                ((hby) ((hby) fhm.a.c()).j("com/google/android/libraries/speech/transcription/recognition/grpc/impl/RecognitionSession", "onError", 366, "RecognitionSession.java")).u("[%s] onError.throttle send error to client.", fhm.g);
                fhm.l.c((fgu) this.b);
                fhm.l.b();
                fhm.f.n(fhm.g, (fck) this.c);
                fhm.h = false;
                return;
            case 6:
                fyz.b(this.c, (String) this.b, (Object[]) this.a);
                return;
            case 7:
                fzz fzz = (fzz) this.c;
                boolean z = fzz.e;
                Object obj6 = this.a;
                if (z) {
                    Object obj7 = this.b;
                    if (fzz.c.remove(obj7)) {
                        fzw fzw = (fzw) fzz.b.a(((gab) obj7).a);
                        gmj T = ftd.T("onSuccess FuturesMixin", goq.a, gmm.a);
                        try {
                            fzw.c(((gab) obj7).d, obj6);
                            T.close();
                            return;
                        } catch (Throwable th2) {
                            th.addSuppressed(th2);
                            break;
                        }
                    } else {
                        return;
                    }
                } else {
                    return;
                }
            case 8:
                fzz fzz2 = (fzz) this.c;
                boolean z2 = fzz2.e;
                Object obj8 = this.a;
                if (z2) {
                    Object obj9 = this.b;
                    if (fzz2.c.remove(obj9)) {
                        fzw fzw2 = (fzw) fzz2.b.a(((gab) obj9).a);
                        gmj T2 = ftd.T("onFailure FuturesMixin", goq.a, gmm.a);
                        try {
                            fzw2.a(((gab) obj9).d, (Throwable) obj8);
                            T2.close();
                            return;
                        } catch (Throwable th3) {
                            th.addSuppressed(th3);
                            break;
                        }
                    } else {
                        return;
                    }
                } else {
                    return;
                }
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                ((gcm) ((gmg) this.a).b).a((gbt) this.c, new gbr(), this.b);
                return;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fnk.c();
                Object obj10 = this.b;
                Object obj11 = this.a;
                gcn gcn = (gcn) obj11;
                fvf.aG(!obj10.equals(gcn.d), "The same LoadTask was processed twice.");
                Object obj12 = this.c;
                iqs iqs = (iqs) obj12;
                fvf.aF(iqs.i().isDone());
                if (gcn.h.g(obj12) && !iqs.i().isCancelled()) {
                    try {
                        if (((gcf) obj10).a(((gcn) obj11).d)) {
                            ((iqs) obj12).j();
                        } else if (!((iqs) obj12).i().isCancelled()) {
                            int a3 = ((gcn) obj11).e.b.a(((gcf) obj10).e, ((iqs) obj12).h(), !((gcf) obj10).b()) - 1;
                            if (a3 == 0) {
                                ((iqs) obj12).j();
                                if (((gcf) obj10).b()) {
                                    gbw gbw = new gbw();
                                    gbw.addSuppressed(gol.c());
                                    ((gcn) obj11).g(gbw);
                                } else {
                                    ((gcn) obj11).c((gcf) obj10);
                                }
                            } else if (a3 != 1) {
                                ((gcn) obj11).i((gcf) obj10, (iqs) obj12);
                                if (((gcf) obj10).b()) {
                                    gbw gbw2 = new gbw();
                                    gbw2.addSuppressed(gol.c());
                                    ((gcn) obj11).g(gbw2);
                                } else {
                                    ((gcn) obj11).c((gcf) obj10);
                                }
                            } else {
                                ((gcn) obj11).i((gcf) obj10, (iqs) obj12);
                                if (((gcn) obj11).f.d && ((gcn) obj11).h()) {
                                    fvf.aG(((gcn) obj11).f.e.f(), "Completed load, fetch is still open, and the callbacks didn't receive data. This is an impossible state.");
                                    gcn.f((gbo) ((gcn) obj11).f.c);
                                    ((gcn) obj11).f = ((gcn) obj11).f.a(false);
                                }
                            }
                        }
                        if (((gcn) obj11).f.d && ((gcn) obj11).h()) {
                            fvf.aG(((gcn) obj11).f.e.f(), "Completed load, fetch is still open, and the callbacks didn't receive data. This is an impossible state.");
                            gcn.f((gbo) ((gcn) obj11).f.c);
                            ((gcn) obj11).f = ((gcn) obj11).f.a(false);
                            return;
                        }
                        return;
                    } catch (gck e) {
                        ((gcn) obj11).g(e.getCause());
                        return;
                    } catch (Throwable th4) {
                        gcn.b.execute(new fbr(th4, 17));
                        return;
                    }
                } else {
                    return;
                }
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                Object obj13 = this.c;
                Object obj14 = this.b;
                Object obj15 = this.a;
                ((gcn) obj15).a.execute(new ety(obj15, obj14, obj13, 10));
                return;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                Object obj16 = this.c;
                Object obj17 = this.a;
                gcn gcn2 = (gcn) obj17;
                dvc dvc = gcn2.g;
                Object obj18 = this.b;
                if (dvc.g(obj16)) {
                    fnk.c();
                    try {
                        hfc.S(((bzj) obj16).a);
                        Object obj19 = ((gcf) obj18).f.b;
                        if (!((gcf) obj18).a(((gcn) obj17).d)) {
                            ((gcn) obj17).d((gcf) obj18);
                            ((gcn) obj17).k.H(hfc.K((Object) null), ((gcf) obj18).f.b, gcc.a, grh.h(((gcn) obj17).j), ((gcn) obj17).a);
                            return;
                        } else if (((gcn) obj17).f.d && ((gcn) obj17).h()) {
                            fvf.aG(((gcn) obj17).f.e.f(), "Completed load, fetch is still open, and the callbacks didn't receive data. This is an impossible state.");
                            gcn.f((gbo) ((gcn) obj17).f.c);
                            ((gcn) obj17).f = ((gcn) obj17).f.a(false);
                            return;
                        } else {
                            return;
                        }
                    } catch (ExecutionException e2) {
                        ((gcn) obj17).g(e2.getCause());
                        return;
                    } catch (Throwable th5) {
                        gcn2.b.execute(new fbr(th5, 16));
                        return;
                    }
                } else {
                    return;
                }
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                Object obj20 = this.c;
                Object obj21 = this.b;
                Object obj22 = this.a;
                ((gcn) obj22).a.execute(new ety(obj22, obj21, obj20, 12));
                return;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                ((ghr) this.c).b((ftc) this.b, (irw) this.a);
                return;
            case 15:
                ((gjb) this.c).l((gjf) this.b, this.a);
                return;
            case 16:
                ProtoParsers$InternalDontUse protoParsers$InternalDontUse = (ProtoParsers$InternalDontUse) ((gqb) this.b).b;
                ((hby) ((hby) DownloadActivity.k.f()).j("com/google/android/libraries/speech/modelmanager/languagepack/ui/DownloadActivity$1", "onSuccess", 123, "DownloadActivity.java")).u("#onSuccess: %s", (ProtoParsers$InternalDontUse) this.a);
                ((DownloadActivity) ((AmbientModeSupport.AmbientController) this.c).a).finish();
                return;
            case 17:
                gqa gqa = (gqa) this.c;
                bw bwVar = gqa.c;
                if (bwVar == null) {
                    return;
                }
                if (bwVar.Y()) {
                    gqa.e = true;
                    return;
                } else if (!bwVar.v) {
                    if (gqa.b.remove(this.b)) {
                        this.a.run();
                        return;
                    }
                    return;
                } else {
                    return;
                }
            case 18:
                Object obj23 = this.b;
                int i4 = ((gqb) obj23).a;
                gqa gqa2 = (gqa) this.c;
                gqa2.a(new ety((AmbientModeSupport.AmbientController) pc.a(gqa2.a, i4), obj23, this.a, 20, (byte[]) null));
                return;
            case 19:
                Object obj24 = this.b;
                int i5 = ((gqb) obj24).a;
                gqa gqa3 = (gqa) this.c;
                gqa3.a(new ety((AmbientModeSupport.AmbientController) pc.a(gqa3.a, i5), obj24, this.a, 16, (byte[]) null));
                return;
            default:
                ProtoParsers$InternalDontUse protoParsers$InternalDontUse2 = (ProtoParsers$InternalDontUse) ((gqb) this.b).b;
                ((hby) ((hby) ((hby) DownloadActivity.k.h()).i((Throwable) this.a)).j("com/google/android/libraries/speech/modelmanager/languagepack/ui/DownloadActivity$1", "onFailure", 130, "DownloadActivity.java")).r("#onFailure");
                ((DownloadActivity) ((AmbientModeSupport.AmbientController) this.c).a).finish();
                return;
        }
        throw th;
        throw th;
    }

    public /* synthetic */ ety(gmg gmg, gbt gbt, gch gch, int i) {
        this.d = i;
        this.a = gmg;
        this.c = gbt;
        this.b = gch;
    }

    public /* synthetic */ ety(Object obj, Object obj2, Object obj3, int i) {
        this.d = i;
        this.a = obj;
        this.b = obj2;
        this.c = obj3;
    }

    public /* synthetic */ ety(Object obj, Object obj2, Object obj3, int i, byte[] bArr) {
        this.d = i;
        this.c = obj;
        this.b = obj2;
        this.a = obj3;
    }
}
