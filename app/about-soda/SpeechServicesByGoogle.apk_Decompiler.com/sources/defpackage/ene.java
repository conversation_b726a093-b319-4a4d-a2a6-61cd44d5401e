package defpackage;

/* renamed from: ene  reason: default package */
/* compiled from: PG */
public final class ene extends jmi implements jne {
    int a;
    final /* synthetic */ Object b;
    final /* synthetic */ Object c;
    private /* synthetic */ Object d;
    private final /* synthetic */ int e;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public ene(eng eng, eam eam, jlr jlr, int i) {
        super(2, jlr);
        this.e = i;
        this.c = eng;
        this.b = eam;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        int i = this.e;
        if (i == 0) {
            return ((ene) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        } else if (i == 1) {
            return ((ene) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        } else if (i != 2) {
            return ((ene) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        } else {
            return ((ene) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
    }

    /* JADX WARNING: type inference failed for: r2v1, types: [enk, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v9, types: [java.lang.Object, ebg] */
    /* JADX WARNING: Removed duplicated region for block: B:40:0x0145  */
    /* JADX WARNING: Removed duplicated region for block: B:84:0x0247  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r12) {
        /*
            r11 = this;
            int r0 = r11.e
            r1 = 1
            if (r0 == 0) goto L_0x024e
            java.lang.String r2 = "getAudioSourceClosingStatus(...)"
            java.lang.String r3 = "invokeSuspend"
            r4 = 2
            if (r0 == r1) goto L_0x014c
            if (r0 == r4) goto L_0x0065
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r11.a
            defpackage.jji.c(r12)
            if (r2 == 0) goto L_0x0018
            goto L_0x0062
        L_0x0018:
            java.lang.Object r12 = r11.d
            jqs r12 = (defpackage.jqs) r12
            hca r12 = defpackage.euz.a
            hco r12 = r12.c()
            hcr r2 = defpackage.hdg.a
            java.lang.String r4 = "ALT.SessionAudioStore"
            hco r12 = r12.h(r2, r4)
            java.lang.String r2 = "com/google/android/libraries/search/audio/store/session/SessionAudioStore$startStoring$lambda$5$$inlined$launchPropagatingLegacy$default$2"
            r4 = 82
            java.lang.String r5 = "SessionAudioStore.kt"
            hco r12 = r12.j(r2, r3, r4, r5)
            hby r12 = (defpackage.hby) r12
            java.lang.Object r2 = r11.c
            euz r2 = (defpackage.euz) r2
            int r2 = r2.g
            java.lang.String r3 = "#audio# starting storing session(%s) LOOPBACK audio..."
            r12.s(r3, r2)
            java.lang.Object r12 = r11.c
            java.lang.Object r2 = r11.b
            grh r2 = r2.d()
            java.lang.Object r2 = r2.b()
            dya r2 = (defpackage.dya) r2
            euz r12 = (defpackage.euz) r12
            java.lang.String r3 = "loopback"
            jqz r12 = r12.b(r3, r2)
            r11.a = r1
            jsg r12 = (defpackage.jsg) r12
            java.lang.Object r12 = r12.y(r11)
            if (r12 != r0) goto L_0x0062
            return r0
        L_0x0062:
            jkd r12 = defpackage.jkd.a
            return r12
        L_0x0065:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r5 = r11.a
            if (r5 == 0) goto L_0x0077
            if (r5 == r1) goto L_0x0072
            defpackage.jji.c(r12)
            goto L_0x013f
        L_0x0072:
            defpackage.jji.c(r12)
            goto L_0x00f0
        L_0x0077:
            defpackage.jji.c(r12)
            java.lang.Object r12 = r11.d
            jqs r12 = (defpackage.jqs) r12
            hca r12 = defpackage.eno.a
            hco r12 = r12.f()
            hcr r5 = defpackage.hdg.a
            java.lang.String r6 = "ALT.SrcAccessor"
            hco r12 = r12.h(r5, r6)
            java.lang.String r5 = "com/google/android/libraries/search/audio/microphone/impl/audiosourcedataaccessor/AudioSourceDataAccessorImpl$closeAudioSource$1"
            r6 = 145(0x91, float:2.03E-43)
            java.lang.String r7 = "AudioSourceDataAccessorImpl.kt"
            hco r12 = r12.j(r5, r3, r6, r7)
            hby r12 = (defpackage.hby) r12
            java.lang.Object r3 = r11.c
            eno r3 = (defpackage.eno) r3
            java.lang.String r3 = r3.h
            java.lang.String r5 = "#audio# closing audio source(%s)"
            r12.u(r5, r3)
            java.lang.Object r12 = r11.c
            eno r12 = (defpackage.eno) r12
            eqw r12 = r12.g()
            if (r12 == 0) goto L_0x00b0
            r12.a()
        L_0x00b0:
            java.lang.Object r12 = r11.c
            r3 = r12
            eno r3 = (defpackage.eno) r3
            jju r5 = r3.g
            if (r5 != 0) goto L_0x00fd
            jqh r12 = r3.j
            java.lang.Object r3 = r11.b
            dzi r4 = defpackage.dzi.d
            htk r4 = r4.l()
            java.lang.String r5 = "newBuilder(...)"
            defpackage.jnu.d(r4, r5)
            bzj r4 = defpackage.jnu.e(r4, "builder")
            eam r3 = (defpackage.eam) r3
            r4.t(r3)
            eag r3 = defpackage.eag.FAILED_CLOSING_WAS_NOT_OPENED
            eah r3 = defpackage.eki.d(r3)
            r4.s(r3)
            dzi r3 = r4.r()
            r12.O(r3)
            java.lang.Object r12 = r11.c
            r11.a = r1
            eno r12 = (defpackage.eno) r12
            jqh r12 = r12.j
            java.lang.Object r12 = r12.y(r11)
            if (r12 != r0) goto L_0x00f0
            goto L_0x014b
        L_0x00f0:
            dzi r12 = (defpackage.dzi) r12
            eah r12 = r12.b
            if (r12 != 0) goto L_0x00f8
            eah r12 = defpackage.eah.c
        L_0x00f8:
            r0 = r12
            defpackage.jnu.d(r0, r2)
            goto L_0x014b
        L_0x00fd:
            eno r12 = (defpackage.eno) r12     // Catch:{ all -> 0x0106 }
            eeh r12 = r12.c     // Catch:{ all -> 0x0106 }
            eah r12 = r12.a()     // Catch:{ all -> 0x0106 }
            goto L_0x010b
        L_0x0106:
            r12 = move-exception
            java.lang.Object r12 = defpackage.jji.b(r12)
        L_0x010b:
            java.lang.Object r1 = r11.b
            java.lang.Throwable r3 = defpackage.jju.a(r12)
            if (r3 != 0) goto L_0x011f
            eah r12 = (defpackage.eah) r12
            defpackage.jnu.b(r12)
            eam r1 = (defpackage.eam) r1
            dzi r12 = defpackage.ejw.j(r12, r1)
            goto L_0x0127
        L_0x011f:
            eag r12 = defpackage.eag.FAILED_CLOSING_ERROR_IN_GETTING_AUDIO_SOURCE_CLOSING_STATUS
            eam r1 = (defpackage.eam) r1
            dzi r12 = defpackage.ejw.k(r12, r1)
        L_0x0127:
            java.lang.Object r1 = r11.c
            eno r1 = (defpackage.eno) r1
            jqh r1 = r1.j
            r1.O(r12)
            java.lang.Object r12 = r11.c
            r11.a = r4
            eno r12 = (defpackage.eno) r12
            jqh r12 = r12.j
            java.lang.Object r12 = r12.y(r11)
            if (r12 != r0) goto L_0x013f
            goto L_0x014b
        L_0x013f:
            dzi r12 = (defpackage.dzi) r12
            eah r12 = r12.b
            if (r12 != 0) goto L_0x0147
            eah r12 = defpackage.eah.c
        L_0x0147:
            r0 = r12
            defpackage.jnu.d(r0, r2)
        L_0x014b:
            return r0
        L_0x014c:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r5 = r11.a
            java.lang.String r6 = "com/google/android/libraries/search/audio/microphone/impl/audiosourcedataaccessor/AudioAdapterAudioSourceDataAccessor$closeAudioSource$1"
            java.lang.String r7 = "AudioAdapterAudioSourceDataAccessor.kt"
            java.lang.String r8 = "ALT.AdapterSrcAccessor"
            if (r5 == 0) goto L_0x0165
            if (r5 == r1) goto L_0x015f
            defpackage.jji.c(r12)
            goto L_0x0241
        L_0x015f:
            defpackage.jji.c(r12)     // Catch:{ all -> 0x0163 }
            goto L_0x01aa
        L_0x0163:
            r12 = move-exception
            goto L_0x01ad
        L_0x0165:
            defpackage.jji.c(r12)
            java.lang.Object r12 = r11.d
            jqs r12 = (defpackage.jqs) r12
            hca r12 = defpackage.eng.a
            hco r12 = r12.f()
            hcr r5 = defpackage.hdg.a
            hco r12 = r12.h(r5, r8)
            r5 = 204(0xcc, float:2.86E-43)
            hco r12 = r12.j(r6, r3, r5, r7)
            hby r12 = (defpackage.hby) r12
            java.lang.Object r5 = r11.c
            eng r5 = (defpackage.eng) r5
            java.lang.String r9 = r5.o()
            int r5 = r5.c
            java.lang.String r10 = "#audio# %s session(%d) closing source"
            r12.A(r10, r9, r5)
            java.lang.Object r12 = r11.c
            eng r12 = (defpackage.eng) r12
            eqw r12 = r12.g()
            if (r12 == 0) goto L_0x019c
            r12.a()
        L_0x019c:
            java.lang.Object r12 = r11.c
            r11.a = r1     // Catch:{ all -> 0x0163 }
            eng r12 = (defpackage.eng) r12     // Catch:{ all -> 0x0163 }
            java.lang.Object r12 = r12.l(r11)     // Catch:{ all -> 0x0163 }
            if (r12 != r0) goto L_0x01aa
            goto L_0x024d
        L_0x01aa:
            eah r12 = (defpackage.eah) r12     // Catch:{ all -> 0x0163 }
            goto L_0x01b1
        L_0x01ad:
            java.lang.Object r12 = defpackage.jji.b(r12)
        L_0x01b1:
            java.lang.Throwable r1 = defpackage.jju.a(r12)
            if (r1 == 0) goto L_0x021f
            hca r12 = defpackage.eng.a
            hco r12 = r12.h()
            hcr r5 = defpackage.hdg.a
            hco r12 = r12.h(r5, r8)
            hby r12 = (defpackage.hby) r12
            hco r12 = r12.i(r1)
            r5 = 211(0xd3, float:2.96E-43)
            hco r12 = r12.j(r6, r3, r5, r7)
            hby r12 = (defpackage.hby) r12
            java.lang.String r3 = "#audio# closeAudioSource failed"
            r12.r(r3)
            boolean r12 = r1 instanceof defpackage.elc
            if (r12 == 0) goto L_0x0219
            elc r1 = (defpackage.elc) r1
            eak r12 = r1.a
            int r1 = r12.a
            if (r1 != r4) goto L_0x01f3
            java.lang.Object r12 = r12.b
            java.lang.Integer r12 = (java.lang.Integer) r12
            int r12 = r12.intValue()
            eaj r12 = defpackage.eaj.b(r12)
            if (r12 != 0) goto L_0x01f5
            eaj r12 = defpackage.eaj.UNKNOWN_OPENING_FAILURE
            goto L_0x01f5
        L_0x01f3:
            eaj r12 = defpackage.eaj.UNKNOWN_OPENING_FAILURE
        L_0x01f5:
            java.lang.String r1 = "getFailure(...)"
            defpackage.jnu.d(r12, r1)
            eag r1 = defpackage.eag.FAILED_CLOSING_ERROR_IN_GETTING_AUDIO_SOURCE_CLOSING_STATUS
            int r12 = r12.ordinal()
            r3 = 5
            if (r12 == r3) goto L_0x0212
            r3 = 49
            if (r12 == r3) goto L_0x020f
            r3 = 50
            if (r12 == r3) goto L_0x020c
            goto L_0x0214
        L_0x020c:
            eag r1 = defpackage.eag.FAILED_CLOSING_NO_AUDIO_ADAPTER_FOUND
            goto L_0x0214
        L_0x020f:
            eag r1 = defpackage.eag.FAILED_CLOSING_NO_CONNECTION_TO_AUDIO_ADAPTER_FOUND
            goto L_0x0214
        L_0x0212:
            eag r1 = defpackage.eag.FAILED_CLOSING_AUDIO_SOURCE_DUE_TO_FAILED_ROUTING
        L_0x0214:
            eah r12 = defpackage.ejw.g(r1)
            goto L_0x021f
        L_0x0219:
            eag r12 = defpackage.eag.FAILED_CLOSING_ERROR_IN_GETTING_AUDIO_SOURCE_CLOSING_STATUS
            eah r12 = defpackage.ejw.g(r12)
        L_0x021f:
            java.lang.Object r1 = r11.c
            java.lang.Object r3 = r11.b
            eah r12 = (defpackage.eah) r12
            eam r3 = (defpackage.eam) r3
            dzi r12 = defpackage.ejw.j(r12, r3)
            eng r1 = (defpackage.eng) r1
            jqh r1 = r1.h
            r1.O(r12)
            java.lang.Object r12 = r11.c
            r11.a = r4
            eng r12 = (defpackage.eng) r12
            jqh r12 = r12.h
            java.lang.Object r12 = r12.y(r11)
            if (r12 != r0) goto L_0x0241
            goto L_0x024d
        L_0x0241:
            dzi r12 = (defpackage.dzi) r12
            eah r12 = r12.b
            if (r12 != 0) goto L_0x0249
            eah r12 = defpackage.eah.c
        L_0x0249:
            r0 = r12
            defpackage.jnu.d(r0, r2)
        L_0x024d:
            return r0
        L_0x024e:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r11.a
            if (r2 == 0) goto L_0x025a
            defpackage.jji.c(r12)     // Catch:{ all -> 0x0258 }
            goto L_0x0270
        L_0x0258:
            r12 = move-exception
            goto L_0x0273
        L_0x025a:
            defpackage.jji.c(r12)
            java.lang.Object r12 = r11.d
            jqs r12 = (defpackage.jqs) r12
            java.lang.Object r12 = r11.b
            java.lang.Object r2 = r11.c
            r11.a = r1     // Catch:{ all -> 0x0258 }
            eng r12 = (defpackage.eng) r12     // Catch:{ all -> 0x0258 }
            java.lang.Object r12 = r12.n(r2, r11)     // Catch:{ all -> 0x0258 }
            if (r12 != r0) goto L_0x0270
            goto L_0x029b
        L_0x0270:
            dyw r12 = (defpackage.dyw) r12     // Catch:{ all -> 0x0258 }
            goto L_0x0277
        L_0x0273:
            java.lang.Object r12 = defpackage.jji.b(r12)
        L_0x0277:
            r0 = r12
            java.lang.Object r12 = r11.b
            boolean r1 = defpackage.jju.b(r0)
            if (r1 == 0) goto L_0x0290
            r1 = r0
            dyw r1 = (defpackage.dyw) r1
            java.lang.Object r1 = r1.f()
            dzh r1 = (defpackage.dzh) r1
            eak r1 = r1.b
            if (r1 != 0) goto L_0x0291
            eak r1 = defpackage.eak.c
            goto L_0x0291
        L_0x0290:
            r1 = r0
        L_0x0291:
            eng r12 = (defpackage.eng) r12
            jqh r12 = r12.g
            defpackage.jqw.k(r12, r1)
            defpackage.jji.c(r0)
        L_0x029b:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.ene.bk(java.lang.Object):java.lang.Object");
    }

    /* JADX WARNING: type inference failed for: r1v0, types: [enk, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v3, types: [java.lang.Object, ebg] */
    public final jlr c(Object obj, jlr jlr) {
        int i = this.e;
        if (i == 0) {
            ene ene = new ene((eng) this.b, (enk) this.c, jlr, 0);
            ene.d = obj;
            return ene;
        } else if (i == 1) {
            ene ene2 = new ene((eng) this.c, (eam) this.b, jlr, 1);
            ene2.d = obj;
            return ene2;
        } else if (i != 2) {
            ene ene3 = new ene(jlr, (euz) this.c, (ebg) this.b, 3);
            ene3.d = obj;
            return ene3;
        } else {
            ene ene4 = new ene((eno) this.c, (eam) this.b, jlr, 2);
            ene4.d = obj;
            return ene4;
        }
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public ene(eng eng, enk enk, jlr jlr, int i) {
        super(2, jlr);
        this.e = i;
        this.b = eng;
        this.c = enk;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public ene(eno eno, eam eam, jlr jlr, int i) {
        super(2, jlr);
        this.e = i;
        this.c = eno;
        this.b = eam;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public ene(jlr jlr, euz euz, ebg ebg, int i) {
        super(2, jlr);
        this.e = i;
        this.c = euz;
        this.b = ebg;
    }
}
