package defpackage;

/* renamed from: eof  reason: default package */
/* compiled from: PG */
public final class eof implements eoc {
    public final dyy a;
    public final eoa b;
    public final eow c;
    public final int d;
    public final dze e;
    public final boolean f;

    public eof(dyy dyy, eoa eoa, eow eow, int i, dze dze, boolean z) {
        jnu.e(dyy, "session");
        jnu.e(eoa, "client");
        jnu.e(eow, "route");
        jnu.e(dze, "params");
        this.a = dyy;
        this.b = eoa;
        this.c = eow;
        this.d = i;
        this.e = dze;
        this.f = z;
    }

    public static /* synthetic */ eof h(eof eof, eoa eoa, eow eow, int i, dze dze, int i2) {
        dyy dyy;
        if ((i2 & 1) != 0) {
            dyy = eof.a;
        } else {
            dyy = null;
        }
        dyy dyy2 = dyy;
        if ((i2 & 2) != 0) {
            eoa = eof.b;
        }
        eoa eoa2 = eoa;
        if ((i2 & 4) != 0) {
            eow = eof.c;
        }
        eow eow2 = eow;
        if ((i2 & 8) != 0) {
            i = eof.d;
        }
        int i3 = i;
        if ((i2 & 16) != 0) {
            dze = eof.e;
        }
        dze dze2 = dze;
        boolean z = eof.f;
        jnu.e(dyy2, "session");
        jnu.e(eoa2, "client");
        jnu.e(eow2, "route");
        jnu.e(dze2, "params");
        return new eof(dyy2, eoa2, eow2, i3, dze2, z);
    }

    public final int a() {
        return this.d;
    }

    public final dyy b() {
        return this.a;
    }

    public final dze c() {
        return this.e;
    }

    public final eoa d() {
        return this.b;
    }

    public final eow e() {
        return this.c;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof eof)) {
            return false;
        }
        eof eof = (eof) obj;
        if (jnu.i(this.a, eof.a) && jnu.i(this.b, eof.b) && jnu.i(this.c, eof.c) && this.d == eof.d && jnu.i(this.e, eof.e) && this.f == eof.f) {
            return true;
        }
        return false;
    }

    public final /* synthetic */ hme f() {
        return cqx.U(this);
    }

    public final /* synthetic */ Object g(jlr jlr) {
        return cqx.V(this, jlr);
    }

    public final int hashCode() {
        int i;
        int hashCode = (((this.a.hashCode() * 31) + this.b.hashCode()) * 31) + this.c.hashCode();
        dze dze = this.e;
        if (dze.B()) {
            i = dze.i();
        } else {
            int i2 = dze.memoizedHashCode;
            if (i2 == 0) {
                i2 = dze.i();
                dze.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (((((hashCode * 31) + this.d) * 31) + i) * 31) + a.f(this.f);
    }

    public final String toString() {
        return "AudioSessionDataSimple(session=" + this.a + ", client=" + this.b + ", route=" + this.c + ", sessionToken=" + this.d + ", params=" + this.e + ", isInactive=" + this.f + ")";
    }

    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public /* synthetic */ eof(defpackage.dyy r10, defpackage.eoa r11, defpackage.eow r12, int r13, defpackage.dze r14, boolean r15, int r16) {
        /*
            r9 = this;
            r0 = r16 & 2
            r1 = 0
            if (r0 == 0) goto L_0x000c
            eoa r0 = new eoa
            r0.<init>(r1)
            r4 = r0
            goto L_0x000d
        L_0x000c:
            r4 = r11
        L_0x000d:
            r0 = r16 & 4
            if (r0 == 0) goto L_0x0018
            eow r0 = new eow
            r0.<init>(r1)
            r5 = r0
            goto L_0x0019
        L_0x0018:
            r5 = r12
        L_0x0019:
            r0 = r16 & 8
            r1 = -1
            if (r0 == 0) goto L_0x0020
            r6 = r1
            goto L_0x0021
        L_0x0020:
            r6 = r13
        L_0x0021:
            r0 = r16 & 16
            if (r0 == 0) goto L_0x003a
            dze r0 = defpackage.dze.k
            htk r0 = r0.l()
            java.lang.String r2 = "newBuilder(...)"
            defpackage.jnu.d(r0, r2)
            cxi r0 = defpackage.jnu.e(r0, "builder")
            dze r0 = r0.f()
            r7 = r0
            goto L_0x003b
        L_0x003a:
            r7 = r14
        L_0x003b:
            r0 = r16 & 32
            if (r0 == 0) goto L_0x0046
            if (r6 != r1) goto L_0x0043
            r0 = 1
            goto L_0x0044
        L_0x0043:
            r0 = 0
        L_0x0044:
            r8 = r0
            goto L_0x0047
        L_0x0046:
            r8 = r15
        L_0x0047:
            r2 = r9
            r3 = r10
            r2.<init>(r3, r4, r5, r6, r7, r8)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eof.<init>(dyy, eoa, eow, int, dze, boolean, int):void");
    }
}
