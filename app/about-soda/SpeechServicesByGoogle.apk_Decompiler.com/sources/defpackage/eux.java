package defpackage;

/* renamed from: eux  reason: default package */
/* compiled from: PG */
public final class eux extends jmi implements jne {
    int a;
    final /* synthetic */ Object b;
    final /* synthetic */ Object c;
    final /* synthetic */ Object d;
    private /* synthetic */ Object e;
    private final /* synthetic */ int f;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eux(eua eua, ebg ebg, dyt dyt, jlr jlr, int i) {
        super(2, jlr);
        this.f = i;
        this.b = eua;
        this.c = ebg;
        this.d = dyt;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        if (this.f != 0) {
            return ((eux) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
        return ((eux) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX WARNING: type inference failed for: r3v4, types: [java.lang.Object, ebg] */
    public final Object bk(Object obj) {
        Object obj2;
        if (this.f != 0) {
            Object obj3 = jlx.COROUTINE_SUSPENDED;
            if (this.a != 0) {
                try {
                    jji.c(obj);
                } catch (Throwable th) {
                    obj2 = jji.b(th);
                }
            } else {
                jji.c(obj);
                jqs jqs = (jqs) this.e;
                Object obj4 = this.b;
                hme H = ftd.H(new ety(obj4, this.c, this.d, 0), ((eua) obj4).f);
                this.a = 1;
                obj = jqw.x(H, this);
                if (obj == obj3) {
                    return obj3;
                }
            }
            obj2 = (Void) obj;
            Throwable a2 = jju.a(obj2);
            if (a2 != null) {
                ((hby) ((hby) eua.a.g().h(hdg.a, "ALT.GrpcARCRespSender")).i(a2).j("com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender$startInternal$1$4", "invokeSuspend", 116, "StreamListeningSessionResponseSender.kt")).r("#audio# sending audio accessor failed");
            }
            return jkd.a;
        }
        Object obj5 = jlx.COROUTINE_SUSPENDED;
        if (this.a != 0) {
            jji.c(obj);
        } else {
            jji.c(obj);
            jqs jqs2 = (jqs) this.e;
            ((hby) euz.a.c().h(hdg.a, "ALT.SessionAudioStore").j("com/google/android/libraries/search/audio/store/session/SessionAudioStore$startStoring$lambda$5$$inlined$launchPropagatingLegacy$default$1", "invokeSuspend", 72, "SessionAudioStore.kt")).s("#audio# starting storing session(%s) MIC audio...", ((euz) this.b).g);
            Object obj6 = this.b;
            euz euz = (euz) obj6;
            jqz b2 = euz.b(esx.n((eel) this.c), (dya) this.d.b().b());
            this.a = 1;
            if (((jsg) b2).y(this) == obj5) {
                return obj5;
            }
        }
        return jkd.a;
    }

    /* JADX WARNING: type inference failed for: r6v0, types: [java.lang.Object, ebg] */
    /* JADX WARNING: type inference failed for: r3v1, types: [java.lang.Object, ebg] */
    public final jlr c(Object obj, jlr jlr) {
        if (this.f != 0) {
            eux eux = new eux((eua) this.b, (ebg) this.c, (dyt) this.d, jlr, 1);
            eux.e = obj;
            return eux;
        }
        Object obj2 = this.b;
        euz euz = (euz) obj2;
        jlr jlr2 = jlr;
        eux eux2 = new eux(jlr2, euz, (eel) this.c, (ebg) this.d, 0);
        eux2.e = obj;
        return eux2;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eux(jlr jlr, euz euz, eel eel, ebg ebg, int i) {
        super(2, jlr);
        this.f = i;
        this.b = euz;
        this.c = eel;
        this.d = ebg;
    }
}
