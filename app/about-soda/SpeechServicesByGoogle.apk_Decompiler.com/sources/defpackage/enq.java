package defpackage;

/* renamed from: enq  reason: default package */
/* compiled from: PG */
public final class enq implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;

    public enq(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4) {
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
    }

    /* renamed from: a */
    public final cyw b() {
        return new cyw(this.a, this.b, this.c, this.d, (byte[]) null);
    }
}
