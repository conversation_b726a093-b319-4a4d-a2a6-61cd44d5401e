package defpackage;

import android.view.View;
import j$.util.function.Predicate$CC;
import java.util.Locale;
import java.util.function.Predicate;

/* renamed from: boa  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class boa implements Predicate {
    private final /* synthetic */ int a;

    public /* synthetic */ boa(int i) {
        this.a = i;
    }

    public final /* synthetic */ Predicate and(Predicate predicate) {
        switch (this.a) {
            case 0:
                return Predicate$CC.$default$and(this, predicate);
            case 1:
                return Predicate$CC.$default$and(this, predicate);
            case 2:
                return Predicate$CC.$default$and(this, predicate);
            case 3:
                return Predicate$CC.$default$and(this, predicate);
            case 4:
                return Predicate$CC.$default$and(this, predicate);
            case 5:
                return Predicate$CC.$default$and(this, predicate);
            case 6:
                return Predicate$CC.$default$and(this, predicate);
            case 7:
                return Predicate$CC.$default$and(this, predicate);
            case 8:
                return Predicate$CC.$default$and(this, predicate);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return Predicate$CC.$default$and(this, predicate);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return Predicate$CC.$default$and(this, predicate);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return Predicate$CC.$default$and(this, predicate);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return Predicate$CC.$default$and(this, predicate);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return Predicate$CC.$default$and(this, predicate);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return Predicate$CC.$default$and(this, predicate);
            case 15:
                return Predicate$CC.$default$and(this, predicate);
            case 16:
                return Predicate$CC.$default$and(this, predicate);
            case 17:
                return Predicate$CC.$default$and(this, predicate);
            case 18:
                return Predicate$CC.$default$and(this, predicate);
            case 19:
                return Predicate$CC.$default$and(this, predicate);
            default:
                return Predicate$CC.$default$and(this, predicate);
        }
    }

    public final /* synthetic */ Predicate negate() {
        switch (this.a) {
            case 0:
                return Predicate$CC.$default$negate(this);
            case 1:
                return Predicate$CC.$default$negate(this);
            case 2:
                return Predicate$CC.$default$negate(this);
            case 3:
                return Predicate$CC.$default$negate(this);
            case 4:
                return Predicate$CC.$default$negate(this);
            case 5:
                return Predicate$CC.$default$negate(this);
            case 6:
                return Predicate$CC.$default$negate(this);
            case 7:
                return Predicate$CC.$default$negate(this);
            case 8:
                return Predicate$CC.$default$negate(this);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return Predicate$CC.$default$negate(this);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return Predicate$CC.$default$negate(this);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return Predicate$CC.$default$negate(this);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return Predicate$CC.$default$negate(this);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return Predicate$CC.$default$negate(this);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return Predicate$CC.$default$negate(this);
            case 15:
                return Predicate$CC.$default$negate(this);
            case 16:
                return Predicate$CC.$default$negate(this);
            case 17:
                return Predicate$CC.$default$negate(this);
            case 18:
                return Predicate$CC.$default$negate(this);
            case 19:
                return Predicate$CC.$default$negate(this);
            default:
                return Predicate$CC.$default$negate(this);
        }
    }

    public final /* synthetic */ Predicate or(Predicate predicate) {
        switch (this.a) {
            case 0:
                return Predicate$CC.$default$or(this, predicate);
            case 1:
                return Predicate$CC.$default$or(this, predicate);
            case 2:
                return Predicate$CC.$default$or(this, predicate);
            case 3:
                return Predicate$CC.$default$or(this, predicate);
            case 4:
                return Predicate$CC.$default$or(this, predicate);
            case 5:
                return Predicate$CC.$default$or(this, predicate);
            case 6:
                return Predicate$CC.$default$or(this, predicate);
            case 7:
                return Predicate$CC.$default$or(this, predicate);
            case 8:
                return Predicate$CC.$default$or(this, predicate);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return Predicate$CC.$default$or(this, predicate);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return Predicate$CC.$default$or(this, predicate);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return Predicate$CC.$default$or(this, predicate);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return Predicate$CC.$default$or(this, predicate);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return Predicate$CC.$default$or(this, predicate);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return Predicate$CC.$default$or(this, predicate);
            case 15:
                return Predicate$CC.$default$or(this, predicate);
            case 16:
                return Predicate$CC.$default$or(this, predicate);
            case 17:
                return Predicate$CC.$default$or(this, predicate);
            case 18:
                return Predicate$CC.$default$or(this, predicate);
            case 19:
                return Predicate$CC.$default$or(this, predicate);
            default:
                return Predicate$CC.$default$or(this, predicate);
        }
    }

    public final boolean test(Object obj) {
        switch (this.a) {
            case 0:
                return yi.B((View) obj);
            case 1:
                yh yhVar = (yh) obj;
                throw null;
            case 2:
                return yi.E((View) obj);
            case 3:
                return ((View) obj) instanceof boe;
            case 4:
                return yi.E((View) obj);
            case 5:
                View view = (View) obj;
                if (!view.isShown()) {
                    return true;
                }
                if (!(view instanceof boe) || ((boe) view).isLayoutCompleted()) {
                    return false;
                }
                return true;
            case 6:
                View view2 = (View) obj;
                if (!yi.C(view2) || !yi.x(view2)) {
                    return false;
                }
                return true;
            case 7:
                return yi.E((View) obj);
            case 8:
                return yi.E((View) obj);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return yi.E((View) obj);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return yi.E((View) obj);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                bjj bjj = (bjj) obj;
                int i = bjj.a;
                int i2 = bjj.b;
                return true;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return brv.j(Locale.forLanguageTag("en"), (String) ((bsk) obj).i().get(0));
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                exo exo = (exo) obj;
                hdf hdf = byr.a;
                if (!eyk.a.keySet().contains(exo.a.toLanguageTag())) {
                    return true;
                }
                ((hdc) ((hdc) byr.a.h()).j("com/google/android/apps/speech/tts/googletts/settings/asr/dataservice/impl/LanguagePackSettingsDataServiceImpl", "getSupportedLocalesSourceInternal", 148, "LanguagePackSettingsDataServiceImpl.java")).u("Omitting supported pack '%s' from settings because it is a locale alias", exo.b());
                return false;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                exo exo2 = (exo) obj;
                hdf hdf2 = byr.a;
                if (!eyk.a.keySet().contains(exo2.a.toLanguageTag())) {
                    return true;
                }
                ((hdc) ((hdc) byr.a.h()).j("com/google/android/apps/speech/tts/googletts/settings/asr/dataservice/impl/LanguagePackSettingsDataServiceImpl", "getInstalledLocalesSourceInternal", 230, "LanguagePackSettingsDataServiceImpl.java")).u("Omitting installed pack '%s' from settings because it is a locale alias", exo2.b());
                return false;
            case 15:
                eak eak = (eak) obj;
                if (eak.a == 2) {
                    eaj b = eaj.b(((Integer) eak.b).intValue());
                    if (b == null) {
                        b = eaj.UNKNOWN_OPENING_FAILURE;
                    }
                    if (b == eaj.FAILED_OPENING_GRPC_START_LISTENING_STATUS_NOT_RECEIVED) {
                        return true;
                    }
                }
                return false;
            case 16:
                eah eah = (eah) obj;
                if (eah.a == 2) {
                    eag b2 = eag.b(((Integer) eah.b).intValue());
                    if (b2 == null) {
                        b2 = eag.UNKNOWN_CLOSING_FAILURE;
                    }
                    if (b2 == eag.FAILED_CLOSING_GRPC_STOP_LISTENING_STATUS_NOT_RECEIVED) {
                        return true;
                    }
                }
                return false;
            case 17:
                eak eak2 = (eak) obj;
                return true;
            case 18:
                eah eah2 = (eah) obj;
                return true;
            case 19:
                eak eak3 = (eak) obj;
                return true;
            default:
                eah eah3 = (eah) obj;
                return true;
        }
    }
}
