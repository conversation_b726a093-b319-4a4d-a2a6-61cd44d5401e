package defpackage;

import android.os.IInterface;

/* renamed from: bpd  reason: default package */
/* compiled from: PG */
public final class bpd extends bow implements IInterface {
    final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bpd(po poVar, int i) {
        super("com.google.android.apps.aicore.aidl.IPrepareInferenceEngineCallback");
        this.b = i;
        this.a = poVar;
    }

    /* JADX WARNING: type inference failed for: r9v4, types: [android.os.IInterface] */
    /* access modifiers changed from: protected */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Unknown variable types count: 1 */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean y(int r7, android.os.Parcel r8, android.os.Parcel r9) {
        /*
            r6 = this;
            int r9 = r6.b
            r0 = 3
            r1 = 0
            r2 = 2
            r3 = 0
            r4 = 1
            if (r9 == 0) goto L_0x013b
            r5 = 4
            if (r9 == r4) goto L_0x00e4
            if (r9 == r2) goto L_0x0080
            if (r9 == r0) goto L_0x006a
            if (r9 == r5) goto L_0x0053
            if (r7 != r2) goto L_0x0052
            android.os.Parcelable$Creator r7 = com.google.android.gms.common.api.Status.CREATOR
            android.os.Parcelable r7 = defpackage.box.a(r8, r7)
            com.google.android.gms.common.api.Status r7 = (com.google.android.gms.common.api.Status) r7
            byte[] r9 = r8.createByteArray()
            defpackage.box.b(r8)
            boolean r8 = r7.b()
            if (r8 == 0) goto L_0x004a
            hte r8 = defpackage.hte.a()     // Catch:{ hui -> 0x0041 }
            dul r0 = defpackage.dul.d     // Catch:{ hui -> 0x0041 }
            int r1 = r9.length     // Catch:{ hui -> 0x0041 }
            htq r8 = defpackage.htq.o(r0, r9, r3, r1, r8)     // Catch:{ hui -> 0x0041 }
            defpackage.htq.D(r8)     // Catch:{ hui -> 0x0041 }
            dul r8 = (defpackage.dul) r8     // Catch:{ hui -> 0x0041 }
            java.lang.Object r9 = r6.a
            byw r9 = (defpackage.byw) r9
            defpackage.ke.m(r7, r8, r9)
            goto L_0x0051
        L_0x0041:
            r7 = move-exception
            java.lang.Object r8 = r6.a
            byw r8 = (defpackage.byw) r8
            r8.b(r7)
            goto L_0x0051
        L_0x004a:
            java.lang.Object r8 = r6.a
            byw r8 = (defpackage.byw) r8
            defpackage.ke.m(r7, r1, r8)
        L_0x0051:
            r3 = r4
        L_0x0052:
            return r3
        L_0x0053:
            if (r7 != r2) goto L_0x0069
            byte[] r7 = r8.createByteArray()
            defpackage.box.b(r8)
            cjs r8 = new cjs
            r8.<init>(r7)
            java.lang.Object r7 = r6.a
            ced r7 = (defpackage.ced) r7
            r7.a(r8)
            return r4
        L_0x0069:
            return r3
        L_0x006a:
            if (r7 != r4) goto L_0x007f
            android.os.Parcelable$Creator r7 = com.google.android.gms.common.api.Status.CREATOR
            android.os.Parcelable r7 = defpackage.box.a(r8, r7)
            com.google.android.gms.common.api.Status r7 = (com.google.android.gms.common.api.Status) r7
            defpackage.box.b(r8)
            java.lang.Object r8 = r6.a
            byw r8 = (defpackage.byw) r8
            defpackage.ke.l(r7, r8)
            return r4
        L_0x007f:
            return r3
        L_0x0080:
            if (r7 == r2) goto L_0x009f
            if (r7 == r0) goto L_0x0085
            goto L_0x00e3
        L_0x0085:
            int r7 = r8.readInt()
            defpackage.box.b(r8)
            bpp r8 = new bpp
            java.lang.String r9 = "Inference failed."
            r8.<init>(r2, r7, r9, r1)
            java.lang.Object r7 = r6.a
            byw r7 = (defpackage.byw) r7
            java.lang.Object r7 = r7.a
            po r7 = (defpackage.po) r7
            r7.d(r8)
            return r4
        L_0x009f:
            android.os.Parcelable$Creator r7 = defpackage.bpk.CREATOR
            android.os.Parcelable r7 = defpackage.box.a(r8, r7)
            bpk r7 = (defpackage.bpk) r7
            defpackage.box.b(r8)
            gxl r8 = new gxl
            r8.<init>()
            gxq r7 = r7.a
            int r9 = r7.size()
        L_0x00b5:
            if (r3 >= r9) goto L_0x00ca
            java.lang.Object r0 = r7.get(r3)
            bpi r0 = (defpackage.bpi) r0
            java.lang.String r0 = r0.a
            bqc r1 = new bqc
            r1.<init>(r0)
            r8.h(r1)
            int r3 = r3 + 1
            goto L_0x00b5
        L_0x00ca:
            gxq r7 = r8.g()
            bqe r8 = new bqe
            gxq r7 = defpackage.gxq.o(r7)
            r8.<init>(r7)
            java.lang.Object r7 = r6.a
            byw r7 = (defpackage.byw) r7
            java.lang.Object r7 = r7.a
            po r7 = (defpackage.po) r7
            r7.c(r8)
            r3 = r4
        L_0x00e3:
            return r3
        L_0x00e4:
            if (r7 == r2) goto L_0x0115
            if (r7 == r0) goto L_0x00e9
            goto L_0x013a
        L_0x00e9:
            int r7 = r8.readInt()
            java.lang.String r9 = r8.readString()
            defpackage.box.b(r8)
            if (r7 == r4) goto L_0x00fc
            if (r7 == r2) goto L_0x00f9
            goto L_0x00fe
        L_0x00f9:
            r3 = 604(0x25c, float:8.46E-43)
            goto L_0x00fe
        L_0x00fc:
            r3 = 601(0x259, float:8.42E-43)
        L_0x00fe:
            java.lang.Object r7 = r6.a
            java.lang.String r8 = java.lang.String.valueOf(r9)
            bpp r9 = new bpp
            java.lang.String r0 = "AiCore service is not connected. Service error: "
            java.lang.String r8 = r0.concat(r8)
            r9.<init>(r5, r3, r8, r1)
            bpz r7 = (defpackage.bpz) r7
            r7.a(r9)
            goto L_0x0139
        L_0x0115:
            android.os.IBinder r7 = r8.readStrongBinder()
            if (r7 != 0) goto L_0x011c
            goto L_0x012f
        L_0x011c:
            java.lang.String r9 = "com.google.android.apps.aicore.aidl.IAICoreService"
            android.os.IInterface r9 = r7.queryLocalInterface(r9)
            boolean r0 = r9 instanceof defpackage.boz
            if (r0 == 0) goto L_0x012a
            r1 = r9
            boz r1 = (defpackage.boz) r1
            goto L_0x012f
        L_0x012a:
            boz r1 = new boz
            r1.<init>(r7)
        L_0x012f:
            defpackage.box.b(r8)
            java.lang.Object r7 = r6.a
            bpz r7 = (defpackage.bpz) r7
            r7.d(r1)
        L_0x0139:
            r3 = r4
        L_0x013a:
            return r3
        L_0x013b:
            if (r7 == r2) goto L_0x0156
            if (r7 == r0) goto L_0x0140
            return r3
        L_0x0140:
            int r7 = r8.readInt()
            defpackage.box.b(r8)
            bpp r8 = new bpp
            java.lang.String r9 = "Preparation failed."
            r8.<init>(r0, r7, r9, r1)
            java.lang.Object r7 = r6.a
            po r7 = (defpackage.po) r7
            r7.d(r8)
            return r4
        L_0x0156:
            java.lang.Object r7 = r6.a
            po r7 = (defpackage.po) r7
            r7.c(r1)
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bpd.y(int, android.os.Parcel, android.os.Parcel):boolean");
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bpd(bpz bpz, int i) {
        super("com.google.android.apps.aicore.aidl.IAiCoreServiceProviderCallback");
        this.b = i;
        this.a = bpz;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bpd(byw byw, int i, char[] cArr) {
        super("com.google.android.apps.aicore.aidl.IRosieRobotResultCallback");
        this.b = i;
        this.a = byw;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bpd(byw byw, int i) {
        super("com.google.android.gms.common.api.internal.IStatusCallback");
        this.b = i;
        this.a = byw;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bpd(byw byw, int i, byte[] bArr) {
        super("com.google.android.gms.phenotype.internal.IGetStorageInfoCallbacks");
        this.b = i;
        this.a = byw;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bpd(ced ced, int i) {
        super("com.google.android.gms.phenotype.internal.IFlagUpdateListener");
        this.b = i;
        this.a = ced;
    }
}
