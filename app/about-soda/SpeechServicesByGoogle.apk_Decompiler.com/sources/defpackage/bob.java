package defpackage;

import android.view.View;
import android.view.ViewTreeObserver;

/* renamed from: bob  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bob implements ViewTreeObserver.OnGlobalLayoutListener {
    public final /* synthetic */ View a;
    public final /* synthetic */ boe b;
    public final /* synthetic */ ViewTreeObserver.OnGlobalLayoutListener[] c;
    public final /* synthetic */ Runnable[] d;
    public final /* synthetic */ Runnable[] e;

    public /* synthetic */ bob(View view, boe boe, ViewTreeObserver.OnGlobalLayoutListener[] onGlobalLayoutListenerArr, Runnable[] runnableArr, Runnable[] runnableArr2) {
        this.a = view;
        this.b = boe;
        this.c = onGlobalLayoutListenerArr;
        this.d = runnableArr;
        this.e = runnableArr2;
    }

    public final void onGlobalLayout() {
        View view = this.a;
        if (view.isShown()) {
            View t = yi.t(view);
            String.valueOf(t);
            if (yi.D(t)) {
                Runnable[] runnableArr = this.e;
                Runnable[] runnableArr2 = this.d;
                yi.u(this.b, this.c, runnableArr2, runnableArr);
            }
        }
    }
}
