package defpackage;

/* renamed from: etn  reason: default package */
/* compiled from: PG */
final class etn extends jme {
    /* synthetic */ Object a;
    final /* synthetic */ eto b;
    int c;
    eto d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public etn(eto eto, jlr jlr) {
        super(jlr);
        this.b = eto;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.c |= Integer.MIN_VALUE;
        return this.b.a(this);
    }
}
