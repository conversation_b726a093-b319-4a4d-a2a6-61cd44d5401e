package defpackage;

import android.content.ComponentName;
import android.content.Context;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.ProviderInfo;
import android.os.Build;
import android.os.Process;
import android.os.Trace;
import android.text.TextUtils;
import android.util.Log;
import com.google.android.tts.R;
import j$.util.Collection;
import j$.util.Objects;
import java.io.File;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Set;

/* renamed from: bke  reason: default package */
/* compiled from: PG */
public final class bke {
    private static bkd a;
    private static final Object b = new Object();
    private static Context c;

    private bke() {
    }

    public static bkd a(Context context) {
        bkd bkd;
        try {
            Trace.beginSection("car-ui-plugin-load");
            bkd bkd2 = a;
            if (bkd2 == null) {
                synchronized (b) {
                    bkd bkd3 = a;
                    if (bkd3 == null) {
                        b(context.getApplicationContext());
                        bkd = a;
                    } else {
                        bkd = bkd3;
                    }
                }
                bkd2 = bkd;
            }
            Trace.endSection();
            return bkd2;
        } catch (Throwable th) {
            Trace.endSection();
            throw th;
        }
    }

    private static void b(Context context) {
        ProviderInfo resolveContentProvider;
        int componentEnabledSetting;
        String str;
        boolean z;
        Class<?> cls;
        String str2;
        Set a2;
        ProviderInfo resolveContentProvider2;
        Class<bke> cls2 = bke.class;
        if (a == null) {
            PackageManager packageManager = context.getPackageManager();
            if (packageManager.hasSystemFeature("android.hardware.type.automotive") && (resolveContentProvider = packageManager.resolveContentProvider(context.getString(R.string.car_ui_plugin_package_provider_authority_name), 1049088)) != null && ((componentEnabledSetting = packageManager.getComponentEnabledSetting(new ComponentName(resolveContentProvider.packageName, resolveContentProvider.name))) != 0 ? componentEnabledSetting == 1 : resolveContentProvider.enabled)) {
                if (context.getPackageManager().hasSystemFeature("android.hardware.type.automotive") && (resolveContentProvider2 = context.getPackageManager().resolveContentProvider(context.getString(R.string.car_ui_plugin_package_provider_authority_name), 1049088)) != null) {
                    str = resolveContentProvider2.packageName;
                } else {
                    str = null;
                }
                try {
                    PackageInfo packageInfo = context.getPackageManager().getPackageInfo(str, 0);
                    Context applicationContext = context.getApplicationContext();
                    if (!(applicationContext instanceof bkc) || (a2 = ((bkc) applicationContext).a()) == null || !Collection.EL.stream(a2).anyMatch(new boa(1))) {
                        if (c == null) {
                            try {
                                c = context.createPackageContext(str, 3);
                            } catch (Exception e) {
                                Log.e("carui", "Could not load CarUi plugin", e);
                                a = new bki();
                                return;
                            }
                        }
                        try {
                            cls2.getClassLoader().loadClass("com.android.car.ui.plugin.PluginVersionProviderImpl");
                            cls = cls2.getClassLoader().loadClass("com.android.car.ui.pluginsupport.OemApiUtil");
                            z = true;
                        } catch (ClassNotFoundException unused) {
                            cls = null;
                            z = false;
                        }
                        if (!z) {
                            Log.w("carui", "loading using adapter classloader");
                            ApplicationInfo applicationInfo = context.getApplicationInfo();
                            ClassLoader classLoader = (ClassLoader) Objects.requireNonNull(cls2.getClassLoader());
                            ClassLoader classLoader2 = c.getClassLoader();
                            ArrayList arrayList = new ArrayList(3);
                            if (applicationInfo.nativeLibraryDir != null) {
                                arrayList.add(applicationInfo.nativeLibraryDir);
                            }
                            if ((applicationInfo.flags & 268435456) == 0) {
                                ArrayList<String> arrayList2 = new ArrayList<>();
                                if (Process.is64Bit()) {
                                    Collections.addAll(arrayList2, Build.SUPPORTED_64_BIT_ABIS);
                                } else {
                                    Collections.addAll(arrayList2, Build.SUPPORTED_32_BIT_ABIS);
                                }
                                for (String str3 : arrayList2) {
                                    arrayList.add(applicationInfo.sourceDir + "!/lib/" + str3);
                                }
                            }
                            if (arrayList.size() == 0) {
                                str2 = null;
                            } else {
                                str2 = TextUtils.join(File.pathSeparator, arrayList);
                            }
                            String str4 = applicationInfo.sourceDir;
                            if (applicationInfo.sharedLibraryFiles != null && applicationInfo.sharedLibraryFiles.length > 0) {
                                str4 = str4 + File.pathSeparator + TextUtils.join(File.pathSeparator, applicationInfo.sharedLibraryFiles);
                            }
                            try {
                                cls = new bka(str4, str2, classLoader, classLoader2).loadClass("com.android.car.ui.pluginsupport.OemApiUtil");
                            } catch (ClassNotFoundException e2) {
                                Log.e("carui", "Could not load oemApiUtilClass: ", e2);
                                a = new bki();
                                return;
                            }
                        }
                        try {
                            Method declaredMethod = cls.getDeclaredMethod("getPluginFactory", new Class[]{Context.class, String.class});
                            declaredMethod.setAccessible(true);
                            bkd bkd = (bkd) declaredMethod.invoke((Object) null, new Object[]{c, context.getPackageName()});
                            a = bkd;
                            if (bkd == null) {
                                Log.w("carui", "CarUi plugin loaded is null");
                                a = new bki();
                                return;
                            }
                            Log.i("carui", "Loaded plugin " + str + " version " + packageInfo.getLongVersionCode() + " for package " + context.getPackageName());
                        } catch (ReflectiveOperationException e3) {
                            Log.e("carui", "Could not invoke getPluginFactory: ", e3);
                            a = new bki();
                        }
                    } else {
                        Log.i("carui", "Package " + context.getPackageName() + " denied loading plugin " + str);
                        a = new bki();
                    }
                } catch (PackageManager.NameNotFoundException unused2) {
                    Log.e("carui", a.ap(str, "Could not load CarUi plugin, package ", " was not found."));
                    a = new bki();
                }
            } else {
                Log.i("carui", "CarUi plugin is disabled");
                a = new bki();
            }
        }
    }
}
