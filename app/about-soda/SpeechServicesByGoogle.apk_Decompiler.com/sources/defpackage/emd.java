package defpackage;

import android.content.Context;
import java.util.concurrent.Executor;
import java.util.function.Predicate;

/* renamed from: emd  reason: default package */
/* compiled from: PG */
public final class emd {
    public static final hdf a = hdf.q();
    public final ihn b;
    public final Executor c;
    public final gsb d;
    private final gsi e;

    public emd(ihn ihn, Executor executor, Context context, gsi gsi) {
        this.b = ihn;
        this.c = executor;
        this.e = gsi;
        this.d = fvf.as(new dtc(context, 9));
    }

    public final void a(hme hme, ejn ejn) {
        hfc.T(hme, gof.g(new emc((Object) this, (Object) gsa.b(this.e), (Object) ejn.b, 0)), this.c);
    }

    public final void b(hme hme, ejn ejn) {
        dzq dzq = ejn.b;
        hfc.T(hme, gof.g(new ema(this, dzq, 3)), this.c);
        hfc.T(ejn.a, gof.g(new ema(this, dzq, 4)), this.c);
    }

    public final void c() {
        ((fqy) ((emi) this.b.b()).c.a()).b((String) this.d.a());
    }

    public final void d(dyy dyy, ehg ehg, Predicate predicate, eaj eaj) {
        hfc.T(dyy.c(), gof.g(new emb(this, predicate, ehg, (Enum) eaj, 0)), this.c);
    }

    public final void e(dyy dyy, ehg ehg, Predicate predicate, eag eag) {
        hfc.T(((dyx) dyy.d()).a(), gof.g(new emb(this, predicate, ehg, (Enum) eag, 2)), this.c);
    }

    public final void f(ehg ehg, hme hme, dzq dzq) {
        hfc.T(hme, gof.g(new gbz(this, gsa.b(this.e), ehg, dzq, 1)), this.c);
    }

    public final void g(ehg ehg, String str) {
        if (ehg == null || ehf.a(ehg.a) == ehf.DETAILS_NOT_SET) {
            ((fqy) ((emi) this.b.b()).k.a()).b(str);
            ((hdc) ((hdc) a.h()).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl", "maybeLogClientInfoUndefined", 511, "MonitoringLoggerImpl.java")).u("#audio# Please specify the correct client info for %s as it will lead to failure in the future.", str);
        }
    }
}
