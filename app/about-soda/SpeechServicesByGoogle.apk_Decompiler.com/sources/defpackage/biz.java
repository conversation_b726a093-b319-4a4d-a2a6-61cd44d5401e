package defpackage;

/* renamed from: biz  reason: default package */
/* compiled from: PG */
public final class biz extends jme {
    /* synthetic */ Object a;
    int b;
    final /* synthetic */ bja c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public biz(bja bja, jlr jlr) {
        super(jlr);
        this.c = bja;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.b |= Integer.MIN_VALUE;
        return this.c.bK((Object) null, this);
    }
}
