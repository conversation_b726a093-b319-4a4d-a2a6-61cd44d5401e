<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:gravity="center_vertical" android:background="?android:attr/selectableItemBackground" android:paddingLeft="?android:attr/listPreferredItemPaddingLeft" android:paddingRight="?android:attr/listPreferredItemPaddingRight" android:clipToPadding="false" android:layout_width="match_parent" android:layout_height="wrap_content" android:baselineAligned="false" android:minHeight="?android:attr/listPreferredItemHeightSmall" android:paddingStart="?android:attr/listPreferredItemPaddingStart" android:paddingEnd="?android:attr/listPreferredItemPaddingEnd">
    <include layout="@layout/settingslib_icon_frame"/>
    <include layout="@layout/settingslib_preference_frame"/>
    <LinearLayout android:gravity="right|center_vertical|center_horizontal|center|end" android:orientation="vertical" android:id="@android:id/widget_frame" android:paddingLeft="16dp" android:paddingRight="0dp" android:layout_width="wrap_content" android:layout_height="match_parent" android:paddingStart="16dp" android:paddingEnd="0dp"/>
</LinearLayout>
