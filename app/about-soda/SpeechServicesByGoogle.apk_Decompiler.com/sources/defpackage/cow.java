package defpackage;

import android.os.SystemClock;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/* renamed from: cow  reason: default package */
/* compiled from: PG */
final class cow extends hka implements Runnable, hmg {
    final long a;
    final long b;
    final boolean c;
    final AtomicLong d = new AtomicLong(0);
    Runnable e;
    final /* synthetic */ coy f;

    public cow(coy coy, Runnable runnable, long j, long j2, boolean z) {
        this.f = coy;
        this.e = runnable;
        this.a = j;
        this.b = j2;
        this.c = z;
    }

    private final long d() {
        return Math.max(0, ((this.d.get() * this.b) + this.a) - SystemClock.elapsedRealtime());
    }

    public final /* synthetic */ int compareTo(Object obj) {
        return coy.a(this, (Delayed) obj);
    }

    public final long getDelay(TimeUnit timeUnit) {
        return timeUnit.convert(d(), TimeUnit.MILLISECONDS);
    }

    public final void run() {
        if (!isDone()) {
            this.d.incrementAndGet();
            try {
                this.e.run();
                if (this.c) {
                    coy coy = this.f;
                    coy.a.postDelayed(this, d());
                    return;
                }
                coy coy2 = this.f;
                coy2.a.postDelayed(this, this.b);
            } catch (Throwable th) {
                this.e = null;
                n(th);
            }
        }
    }
}
