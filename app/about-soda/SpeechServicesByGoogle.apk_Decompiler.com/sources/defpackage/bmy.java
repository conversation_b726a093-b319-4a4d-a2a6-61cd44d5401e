package defpackage;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;

/* renamed from: bmy  reason: default package */
/* compiled from: PG */
final class bmy implements TextWatcher {
    final /* synthetic */ bna a;

    public bmy(bna bna) {
        this.a = bna;
    }

    public final void afterTextChanged(Editable editable) {
        int i;
        String obj = editable.toString();
        if (true != TextUtils.isEmpty(obj)) {
            i = 0;
        } else {
            i = 8;
        }
        bna bna = this.a;
        bna.n.setVisibility(i);
        for (Object m : bna.r) {
            ag$$ExternalSyntheticApiModelOutline1.m(m).accept(obj);
        }
        for (bnn a2 : bna.t) {
            a2.a();
        }
    }

    public final void beforeTextChanged(CharSequence charSequence, int i, int i2, int i3) {
    }

    public final void onTextChanged(CharSequence charSequence, int i, int i2, int i3) {
    }
}
