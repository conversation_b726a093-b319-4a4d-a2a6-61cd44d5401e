package defpackage;

/* renamed from: csq  reason: default package */
/* compiled from: PG */
public final class csq {
    public boolean a;
    public grh b;
    public grh c;
    public grh d;
    public grh e;
    public boolean f;
    public boolean g;
    public byte h;

    public csq() {
        throw null;
    }

    public final void a(boolean z) {
        this.a = z;
        this.h = (byte) (this.h | 1);
    }

    public final void b(boolean z) {
        this.f = z;
        this.h = (byte) (this.h | 4);
    }

    public csq(byte[] bArr) {
        gqd gqd = gqd.a;
        this.b = gqd;
        this.c = gqd;
        this.d = gqd;
        this.e = gqd;
    }
}
