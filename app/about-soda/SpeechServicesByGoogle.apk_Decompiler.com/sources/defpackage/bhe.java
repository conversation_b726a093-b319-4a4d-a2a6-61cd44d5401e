package defpackage;

/* renamed from: bhe  reason: default package */
/* compiled from: PG */
public final class bhe {
    public static final String a = bbk.b("WorkSpec");
    public final String b;
    public bbx c;
    public String d;
    public String e;
    public bat f;
    public bat g;
    public long h;
    public long i;
    public long j;
    public baq k;
    public int l;
    public bak m;
    public long n;
    public long o;
    public long p;
    public long q;
    public boolean r;
    public bbt s;
    public int t;
    public final int u;
    public long v;
    public int w;
    public final int x;
    public String y;

    public bhe(String str, bbx bbx, String str2, String str3, bat bat, bat bat2, long j2, long j3, long j4, baq baq, int i2, bak bak, long j5, long j6, long j7, long j8, boolean z, bbt bbt, int i3, int i4, long j9, int i5, int i6, String str4) {
        bat bat3 = bat;
        bat bat4 = bat2;
        baq baq2 = baq;
        bak bak2 = bak;
        bbt bbt2 = bbt;
        jnu.e(str, "id");
        jnu.e(bbx, "state");
        jnu.e(str2, "workerClassName");
        jnu.e(str3, "inputMergerClassName");
        jnu.e(bat3, "input");
        jnu.e(bat4, "output");
        jnu.e(baq2, "constraints");
        jnu.e(bak2, "backoffPolicy");
        jnu.e(bbt2, "outOfQuotaPolicy");
        this.b = str;
        this.c = bbx;
        this.d = str2;
        this.e = str3;
        this.f = bat3;
        this.g = bat4;
        this.h = j2;
        this.i = j3;
        this.j = j4;
        this.k = baq2;
        this.l = i2;
        this.m = bak2;
        this.n = j5;
        this.o = j6;
        this.p = j7;
        this.q = j8;
        this.r = z;
        this.s = bbt2;
        this.t = i3;
        this.u = i4;
        this.v = j9;
        this.w = i5;
        this.x = i6;
        this.y = str4;
    }

    public static /* synthetic */ bhe e(bhe bhe, String str, bbx bbx, String str2, bat bat, int i2, long j2, int i3, int i4, long j3, int i5, int i6) {
        long j4;
        String str3;
        long j5;
        long j6;
        bhe bhe2 = bhe;
        int i7 = i6;
        String str4 = (i7 & 1) != 0 ? bhe2.b : str;
        bbx bbx2 = (i7 & 2) != 0 ? bhe2.c : bbx;
        String str5 = (i7 & 4) != 0 ? bhe2.d : str2;
        String str6 = (i7 & 8) != 0 ? bhe2.e : null;
        bat bat2 = (i7 & 16) != 0 ? bhe2.f : bat;
        bat bat3 = (i7 & 32) != 0 ? bhe2.g : null;
        long j7 = (i7 & 64) != 0 ? bhe2.h : 0;
        long j8 = (i7 & 128) != 0 ? bhe2.i : 0;
        long j9 = (i7 & 256) != 0 ? bhe2.j : 0;
        baq baq = (i7 & 512) != 0 ? bhe2.k : null;
        int i8 = (i7 & 1024) != 0 ? bhe2.l : i2;
        bak bak = (i7 & 2048) != 0 ? bhe2.m : null;
        if ((i7 & 4096) != 0) {
            str3 = str4;
            j4 = bhe2.n;
        } else {
            str3 = str4;
            j4 = 0;
        }
        long j10 = (i7 & 8192) != 0 ? bhe2.o : j2;
        long j11 = (i7 & 16384) != 0 ? bhe2.p : 0;
        long j12 = (32768 & i7) != 0 ? bhe2.q : 0;
        boolean z = (65536 & i7) != 0 ? bhe2.r : false;
        bbt bbt = (131072 & i7) != 0 ? bhe2.s : null;
        int i9 = (262144 & i7) != 0 ? bhe2.t : i3;
        int i10 = (524288 & i7) != 0 ? bhe2.u : i4;
        if ((1048576 & i7) != 0) {
            j5 = j8;
            j6 = bhe2.v;
        } else {
            j5 = j8;
            j6 = j3;
        }
        int i11 = (i7 & 2097152) != 0 ? bhe2.w : i5;
        String str7 = str3;
        jnu.e(str7, "id");
        jnu.e(bbx2, "state");
        jnu.e(str5, "workerClassName");
        jnu.e(str6, "inputMergerClassName");
        jnu.e(bat2, "input");
        jnu.e(bat3, "output");
        jnu.e(baq, "constraints");
        jnu.e(bak, "backoffPolicy");
        jnu.e(bbt, "outOfQuotaPolicy");
        return new bhe(str7, bbx2, str5, str6, bat2, bat3, j7, j5, j9, baq, i8, bak, j4, j10, j11, j12, z, bbt, i9, i10, j6, i11, bhe2.x, bhe2.y);
    }

    public final long a() {
        boolean c2 = c();
        return xm.w(c2, this.l, this.m, this.n, this.o, this.t, d(), this.h, this.j, this.i, this.v);
    }

    public final boolean b() {
        if (!jnu.i(baq.a, this.k)) {
            return true;
        }
        return false;
    }

    public final boolean c() {
        if (this.c != bbx.ENQUEUED || this.l <= 0) {
            return false;
        }
        return true;
    }

    public final boolean d() {
        if (this.i != 0) {
            return true;
        }
        return false;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof bhe)) {
            return false;
        }
        bhe bhe = (bhe) obj;
        if (jnu.i(this.b, bhe.b) && this.c == bhe.c && jnu.i(this.d, bhe.d) && jnu.i(this.e, bhe.e) && jnu.i(this.f, bhe.f) && jnu.i(this.g, bhe.g) && this.h == bhe.h && this.i == bhe.i && this.j == bhe.j && jnu.i(this.k, bhe.k) && this.l == bhe.l && this.m == bhe.m && this.n == bhe.n && this.o == bhe.o && this.p == bhe.p && this.q == bhe.q && this.r == bhe.r && this.s == bhe.s && this.t == bhe.t && this.u == bhe.u && this.v == bhe.v && this.w == bhe.w && this.x == bhe.x && jnu.i(this.y, bhe.y)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i2;
        int hashCode = (((((((((this.b.hashCode() * 31) + this.c.hashCode()) * 31) + this.d.hashCode()) * 31) + this.e.hashCode()) * 31) + this.f.hashCode()) * 31) + this.g.hashCode();
        int g2 = wg.g(this.h);
        int g3 = wg.g(this.i);
        int g4 = (((((((((((hashCode * 31) + g2) * 31) + g3) * 31) + wg.g(this.j)) * 31) + this.k.hashCode()) * 31) + this.l) * 31) + this.m.hashCode();
        int g5 = wg.g(this.n);
        int g6 = wg.g(this.o);
        int g7 = wg.g(this.p);
        int g8 = wg.g(this.q);
        int f2 = (((((((((((((g4 * 31) + g5) * 31) + g6) * 31) + g7) * 31) + g8) * 31) + a.f(this.r)) * 31) + this.s.hashCode()) * 31) + this.t;
        int g9 = (((((f2 * 31) + this.u) * 31) + wg.g(this.v)) * 31) + this.w;
        String str = this.y;
        if (str == null) {
            i2 = 0;
        } else {
            i2 = str.hashCode();
        }
        return (((g9 * 31) + this.x) * 31) + i2;
    }

    public final String toString() {
        return "{WorkSpec: " + this.b + '}';
    }

    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public /* synthetic */ bhe(java.lang.String r36, defpackage.bbx r37, java.lang.String r38, java.lang.String r39, defpackage.bat r40, defpackage.bat r41, long r42, long r44, long r46, defpackage.baq r48, int r49, defpackage.bak r50, long r51, long r53, long r55, long r57, boolean r59, defpackage.bbt r60, int r61, long r62, int r64, int r65, java.lang.String r66, int r67) {
        /*
            r35 = this;
            r0 = r67
            r1 = r0 & 2
            if (r1 == 0) goto L_0x000a
            bbx r1 = defpackage.bbx.ENQUEUED
            r4 = r1
            goto L_0x000c
        L_0x000a:
            r4 = r37
        L_0x000c:
            r1 = r0 & 8
            if (r1 == 0) goto L_0x001d
            java.lang.Class<androidx.work.OverwritingInputMerger> r1 = androidx.work.OverwritingInputMerger.class
            java.lang.String r1 = r1.getName()
            java.lang.String r2 = "OverwritingInputMerger::class.java.name"
            defpackage.jnu.d(r1, r2)
            r6 = r1
            goto L_0x001f
        L_0x001d:
            r6 = r39
        L_0x001f:
            r1 = r0 & 16
            if (r1 == 0) goto L_0x0027
            bat r1 = defpackage.bat.a
            r7 = r1
            goto L_0x0029
        L_0x0027:
            r7 = r40
        L_0x0029:
            r1 = r0 & 32
            if (r1 == 0) goto L_0x0031
            bat r1 = defpackage.bat.a
            r8 = r1
            goto L_0x0033
        L_0x0031:
            r8 = r41
        L_0x0033:
            r1 = r0 & 64
            r2 = 0
            if (r1 == 0) goto L_0x003b
            r9 = r2
            goto L_0x003d
        L_0x003b:
            r9 = r42
        L_0x003d:
            r1 = r0 & 128(0x80, float:1.794E-43)
            if (r1 == 0) goto L_0x0043
            r11 = r2
            goto L_0x0045
        L_0x0043:
            r11 = r44
        L_0x0045:
            r1 = r0 & 256(0x100, float:3.59E-43)
            if (r1 == 0) goto L_0x004b
            r13 = r2
            goto L_0x004d
        L_0x004b:
            r13 = r46
        L_0x004d:
            r1 = r0 & 512(0x200, float:7.175E-43)
            if (r1 == 0) goto L_0x0055
            baq r1 = defpackage.baq.a
            r15 = r1
            goto L_0x0057
        L_0x0055:
            r15 = r48
        L_0x0057:
            r1 = r0 & 1024(0x400, float:1.435E-42)
            r5 = 0
            if (r1 == 0) goto L_0x005f
            r16 = r5
            goto L_0x0061
        L_0x005f:
            r16 = r49
        L_0x0061:
            r1 = r0 & 2048(0x800, float:2.87E-42)
            if (r1 == 0) goto L_0x006a
            bak r1 = defpackage.bak.EXPONENTIAL
            r17 = r1
            goto L_0x006c
        L_0x006a:
            r17 = r50
        L_0x006c:
            r1 = r0 & 4096(0x1000, float:5.74E-42)
            if (r1 == 0) goto L_0x0073
            r18 = 30000(0x7530, double:1.4822E-319)
            goto L_0x0075
        L_0x0073:
            r18 = r51
        L_0x0075:
            r1 = r0 & 8192(0x2000, float:1.14794E-41)
            r20 = -1
            if (r1 == 0) goto L_0x007e
            r22 = r20
            goto L_0x0080
        L_0x007e:
            r22 = r53
        L_0x0080:
            r1 = r0 & 16384(0x4000, float:2.2959E-41)
            if (r1 == 0) goto L_0x0087
            r24 = r2
            goto L_0x0089
        L_0x0087:
            r24 = r55
        L_0x0089:
            r1 = 32768(0x8000, float:4.5918E-41)
            r1 = r1 & r0
            if (r1 == 0) goto L_0x0092
            r26 = r20
            goto L_0x0094
        L_0x0092:
            r26 = r57
        L_0x0094:
            r1 = 65536(0x10000, float:9.18355E-41)
            r1 = r1 & r0
            if (r1 == 0) goto L_0x009b
            r1 = r5
            goto L_0x009c
        L_0x009b:
            r1 = 1
        L_0x009c:
            r1 = r1 & r59
            r2 = 131072(0x20000, float:1.83671E-40)
            r2 = r2 & r0
            if (r2 == 0) goto L_0x00a8
            bbt r2 = defpackage.bbt.RUN_AS_NON_EXPEDITED_WORK_REQUEST
            r28 = r2
            goto L_0x00aa
        L_0x00a8:
            r28 = r60
        L_0x00aa:
            r2 = 262144(0x40000, float:3.67342E-40)
            r2 = r2 & r0
            if (r2 == 0) goto L_0x00b2
            r30 = r5
            goto L_0x00b4
        L_0x00b2:
            r30 = r61
        L_0x00b4:
            r2 = 1048576(0x100000, float:1.469368E-39)
            r2 = r2 & r0
            if (r2 == 0) goto L_0x00c1
            r2 = 9223372036854775807(0x7fffffffffffffff, double:NaN)
            r31 = r2
            goto L_0x00c3
        L_0x00c1:
            r31 = r62
        L_0x00c3:
            r2 = 2097152(0x200000, float:2.938736E-39)
            r2 = r2 & r0
            if (r2 == 0) goto L_0x00cb
            r33 = r5
            goto L_0x00cd
        L_0x00cb:
            r33 = r64
        L_0x00cd:
            r2 = 4194304(0x400000, float:5.877472E-39)
            r2 = r2 & r0
            if (r2 == 0) goto L_0x00d7
            r2 = -256(0xffffffffffffff00, float:NaN)
            r34 = r2
            goto L_0x00d9
        L_0x00d7:
            r34 = r65
        L_0x00d9:
            r2 = 8388608(0x800000, float:1.17549435E-38)
            r0 = r0 & r2
            if (r0 == 0) goto L_0x00e0
            r0 = 0
            goto L_0x00e2
        L_0x00e0:
            r0 = r66
        L_0x00e2:
            r29 = 0
            r2 = r35
            r3 = r36
            r5 = r38
            r20 = r22
            r22 = r24
            r24 = r26
            r26 = r1
            r27 = r28
            r28 = r30
            r30 = r31
            r32 = r33
            r33 = r34
            r34 = r0
            r2.<init>((java.lang.String) r3, (defpackage.bbx) r4, (java.lang.String) r5, (java.lang.String) r6, (defpackage.bat) r7, (defpackage.bat) r8, (long) r9, (long) r11, (long) r13, (defpackage.baq) r15, (int) r16, (defpackage.bak) r17, (long) r18, (long) r20, (long) r22, (long) r24, (boolean) r26, (defpackage.bbt) r27, (int) r28, (int) r29, (long) r30, (int) r32, (int) r33, (java.lang.String) r34)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bhe.<init>(java.lang.String, bbx, java.lang.String, java.lang.String, bat, bat, long, long, long, baq, int, bak, long, long, long, long, boolean, bbt, int, long, int, int, java.lang.String, int):void");
    }
}
