package defpackage;

/* renamed from: ctg  reason: default package */
/* compiled from: PG */
public final class ctg extends htq implements hvb {
    public static final ctg g;
    private static volatile hvh h;
    public int a;
    public String b = "";
    public String c = "";
    public String d = "";
    public boolean e;
    public String f = "";

    static {
        ctg ctg = new ctg();
        g = ctg;
        htq.z(ctg.class, ctg);
    }

    private ctg() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(g, "\u0001\u0005\u0000\u0001\u0001\u0006\u0005\u0000\u0000\u0000\u0001ဈ\u0000\u0002ဈ\u0001\u0004ဇ\u0003\u0005ဈ\u0002\u0006ဈ\u0004", new Object[]{"a", "b", "c", "e", "d", "f"});
        } else if (i2 == 3) {
            return new ctg();
        } else {
            if (i2 == 4) {
                return new htk((htq) g);
            }
            if (i2 == 5) {
                return g;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = h;
            if (hvh == null) {
                synchronized (ctg.class) {
                    hvh = h;
                    if (hvh == null) {
                        hvh = new htl(g);
                        h = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
