package defpackage;

/* renamed from: bfc  reason: default package */
/* compiled from: PG */
public final class bfc {
    public static final long a = 1000;

    static {
        bbk.b("WorkConstraintsTracker");
    }

    public static final jrz a(byw byw, bhe bhe, jqp jqp, bey bey) {
        jnu.e(byw, "<this>");
        jnu.e(bhe, "spec");
        jnu.e(jqp, "dispatcher");
        jsb jsb = new jsb();
        job.S(jqw.e(jqp.plus(jsb)), (jlv) null, (jqt) null, new bfb(byw, bhe, bey, (jlr) null, 0), 3);
        return jsb;
    }
}
