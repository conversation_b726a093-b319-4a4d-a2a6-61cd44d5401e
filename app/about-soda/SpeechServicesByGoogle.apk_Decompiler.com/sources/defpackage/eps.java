package defpackage;

/* renamed from: eps  reason: default package */
/* compiled from: PG */
public final class eps extends htq implements hvb {
    public static final eps f;
    private static volatile hvh g;
    public int a;
    public int b = 0;
    public Object c;
    public int d;
    public int e = -1;

    static {
        eps eps = new eps();
        f = eps;
        htq.z(eps.class, eps);
    }

    private eps() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(f, "\u0004\u0004\u0001\u0001\u0001\u0004\u0004\u0000\u0000\u0000\u0001᠌\u0000\u0002<\u0000\u0003<\u0000\u0004င\u0001", new Object[]{"c", "b", "a", "d", ebb.k, eak.class, eah.class, "e"});
        } else if (i2 == 3) {
            return new eps();
        } else {
            if (i2 == 4) {
                return new htk((htq) f);
            }
            if (i2 == 5) {
                return f;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = g;
            if (hvh == null) {
                synchronized (eps.class) {
                    hvh = g;
                    if (hvh == null) {
                        hvh = new htl(f);
                        g = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
