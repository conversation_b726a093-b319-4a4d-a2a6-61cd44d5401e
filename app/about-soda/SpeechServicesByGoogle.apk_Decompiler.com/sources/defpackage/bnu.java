package defpackage;

import android.app.AlertDialog;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.text.TextUtils;
import android.text.method.LinkMovementMethod;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import com.android.car.ui.FocusParkingView;
import com.android.car.ui.toolbar.TabLayout;
import com.google.android.tts.R;
import j$.util.DesugarCollections;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

/* renamed from: bnu  reason: default package */
/* compiled from: PG */
public final class bnu implements bnq {
    private final Set A = new HashSet();
    private final bml B;
    private final boolean C;
    private bnp D;
    private boolean E;
    private bmp F;
    private final bmv G;
    private List H;
    private final List I;

    /* renamed from: J  reason: collision with root package name */
    private final List f14J;
    private final boolean K;
    private final boolean L;
    private final bmj M;
    public final ViewGroup a;
    public final Context b;
    public final Set c = new HashSet();
    public final Set d = new HashSet();
    public List e;
    public final List f;
    public final bli g;
    public View[] h;
    public AlertDialog i;
    private final View j;
    private final ImageView k;
    private final ImageView l;
    private final ViewGroup m;
    private final ViewGroup n;
    private final bok o;
    private bjj p = new bjj(new byw((CharSequence) ""));
    private final bok q;
    private bjj r = new bjj(new byw((CharSequence) ""));
    private final ViewGroup s;
    private final TabLayout t;
    private final FrameLayout u;
    private bna v;
    private final CharSequence w;
    private final Set x = new HashSet();
    private final Set y = new HashSet();
    private final Set z = new HashSet();

    public bnu(Context context, View view) {
        new HashSet();
        this.D = bnp.HOME;
        this.E = false;
        this.F = bmp.DISABLED;
        this.G = bmv.DISABLED;
        this.H = Collections.emptyList();
        this.e = new ArrayList();
        ArrayList arrayList = new ArrayList();
        this.f = arrayList;
        this.I = new ArrayList();
        this.f14J = new ArrayList();
        this.M = new bnr(this);
        this.b = context;
        bmh bmh = new bmh(context);
        bmh.h = ((Context) bmh.a.get()).getDrawable(R.drawable.car_ui_icon_overflow_menu);
        bmh.g = ((Context) bmh.a.get()).getString(R.string.car_ui_toolbar_menu_item_overflow_title);
        bmh.i = new bns(this);
        this.B = bmh.a();
        this.C = context.getResources().getBoolean(R.bool.car_ui_toolbar_tabs_on_second_row);
        this.K = context.getResources().getBoolean(R.bool.car_ui_toolbar_nav_icon_reserve_space);
        context.getResources().getBoolean(R.bool.car_ui_toolbar_logo_fills_nav_icon_space);
        this.L = context.getResources().getBoolean(R.bool.car_ui_toolbar_show_logo);
        this.w = context.getString(R.string.car_ui_toolbar_default_search_hint);
        View h2 = bnv.h(view, R.id.car_ui_toolbar_background);
        this.j = h2;
        this.t = (TabLayout) bnv.i(view, R.id.car_ui_toolbar_tabs);
        this.k = (ImageView) bnv.i(view, R.id.car_ui_toolbar_nav_icon);
        this.l = (ImageView) bnv.i(view, R.id.car_ui_toolbar_logo);
        this.m = (ViewGroup) bnv.i(view, R.id.car_ui_toolbar_nav_icon_container);
        this.a = (ViewGroup) bnv.i(view, R.id.car_ui_toolbar_menu_items_container);
        this.n = (ViewGroup) bnv.i(view, R.id.car_ui_toolbar_title_container);
        this.q = (bok) bnv.i(view, R.id.car_ui_toolbar_subtitle);
        this.o = (bok) bnv.i(view, R.id.car_ui_toolbar_title);
        this.s = (ViewGroup) bnv.i(view, R.id.car_ui_toolbar_title_logo_container);
        ImageView imageView = (ImageView) bnv.i(view, R.id.car_ui_toolbar_title_logo);
        this.u = (FrameLayout) bnv.i(view, R.id.car_ui_toolbar_search_view_container);
        ProgressBar progressBar = (ProgressBar) bnv.i(view, R.id.car_ui_toolbar_progress_bar);
        if (h2 != null) {
            h2.setBackground(context.getDrawable(R.drawable.car_ui_toolbar_background));
        }
        this.g = new bli(arrayList);
        c();
    }

    public final bla a(bml bml) {
        bla bla;
        if (bml.a) {
            bla = new bla(bkx.SWITCH);
        } else {
            bla = new bla(bkx.NONE);
        }
        bla.a = bml.i;
        bla.g = bml.o;
        bla.a(bml.m);
        bla.f = bml.l;
        bla.b(bml.h);
        bla.i = new ekf(this, bml, (byte[]) null);
        return bla;
    }

    public final void b() {
        boolean z2;
        int i2;
        AlertDialog alertDialog = this.i;
        int i3 = 0;
        if (alertDialog == null) {
            z2 = false;
        } else {
            z2 = alertDialog.isShowing();
        }
        this.f.clear();
        for (bml bml : this.e) {
            if (bml.n) {
                this.f.add(a(bml));
            }
        }
        bji bji = new bji(this.b);
        bli bli = this.g;
        View inflate = LayoutInflater.from(bji.c).inflate(R.layout.car_ui_alert_dialog_list, (ViewGroup) null);
        RecyclerView recyclerView = (RecyclerView) bnv.i(inflate, R.id.list);
        recyclerView.ac(new LinearLayoutManager());
        recyclerView.ab(bli);
        recyclerView.setFocusable(false);
        bji.a.setView(inflate);
        View inflate2 = LayoutInflater.from(bji.c).inflate(R.layout.car_ui_alert_dialog_title_with_subtitle, (ViewGroup) null);
        TextView textView = (TextView) bnv.i(inflate2, R.id.car_ui_alert_title);
        TextView textView2 = (TextView) bnv.i(inflate2, R.id.car_ui_alert_subtitle);
        textView2.setMovementMethod(LinkMovementMethod.getInstance());
        vl.a(textView2, new alr(textView2, 16));
        ImageView imageView = (ImageView) bnv.i(inflate2, R.id.car_ui_alert_icon);
        textView.setText((CharSequence) null);
        if (true != TextUtils.isEmpty((CharSequence) null)) {
            i2 = 0;
        } else {
            i2 = 8;
        }
        textView.setVisibility(i2);
        textView2.setText((CharSequence) null);
        if (true == TextUtils.isEmpty((CharSequence) null)) {
            i3 = 8;
        }
        textView2.setVisibility(i3);
        imageView.setImageDrawable((Drawable) null);
        imageView.setVisibility(8);
        if (!TextUtils.isEmpty((CharSequence) null) || !TextUtils.isEmpty((CharSequence) null)) {
            bji.a.setCustomTitle(inflate2);
        }
        if (bji.c.getResources().getBoolean(R.bool.car_ui_alert_dialog_force_dismiss_button)) {
            bji.a.setNegativeButton(bji.c.getString(R.string.car_ui_alert_dialog_default_button), new bus(1));
        }
        bji.b = bji.a.create();
        bji.d = (ViewGroup) bji.b.getWindow().getDecorView().getRootView();
        bji.d.addView(new FocusParkingView(bji.c));
        bji.d.setOnApplyWindowInsetsListener(bji.e);
        bji.a.setOnDismissListener(bji.f);
        this.i = bji.b;
        if (z2) {
            this.g.notifyDataSetChanged();
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:114:0x0201, code lost:
        if (r2 > 0) goto L_0x0205;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void c() {
        /*
            r12 = this;
            bmp r0 = r12.F
            boolean r1 = r12.E
            if (r1 == 0) goto L_0x0015
            bnp r2 = r12.D
            bnp r3 = defpackage.bnp.HOME
            if (r2 != r3) goto L_0x000f
            bmp r0 = defpackage.bmp.DISABLED
            goto L_0x0015
        L_0x000f:
            bmp r2 = defpackage.bmp.DISABLED
            if (r0 != r2) goto L_0x0015
            bmp r0 = defpackage.bmp.BACK
        L_0x0015:
            bmv r2 = r12.G
            if (r1 == 0) goto L_0x002b
            bnp r1 = r12.D
            bnp r2 = defpackage.bnp.SEARCH
            if (r1 != r2) goto L_0x0022
            bmv r2 = defpackage.bmv.SEARCH
            goto L_0x002b
        L_0x0022:
            bnp r2 = defpackage.bnp.EDIT
            if (r1 != r2) goto L_0x0029
            bmv r2 = defpackage.bmv.EDIT
            goto L_0x002b
        L_0x0029:
            bmv r2 = defpackage.bmv.DISABLED
        L_0x002b:
            com.android.car.ui.toolbar.TabLayout r1 = r12.t
            java.util.List r1 = r1.b
            boolean r1 = r1.isEmpty()
            r3 = 1
            r1 = r1 ^ r3
            boolean r4 = r12.E
            r5 = 0
            if (r4 == 0) goto L_0x0043
            bnp r4 = r12.D
            bnp r6 = defpackage.bnp.HOME
            if (r4 == r6) goto L_0x0043
            bnp r1 = defpackage.bnp.SUBPAGE
            r1 = r5
        L_0x0043:
            bmv r4 = defpackage.bmv.DISABLED
            if (r2 == r4) goto L_0x0049
            r4 = r3
            goto L_0x004a
        L_0x0049:
            r4 = r5
        L_0x004a:
            bna r6 = r12.v
            r7 = 0
            r8 = 8
            if (r6 != 0) goto L_0x00a6
            if (r4 == 0) goto L_0x00a6
            android.content.Context r6 = r12.b
            bna r9 = new bna
            r9.<init>(r6)
            java.lang.CharSequence r6 = r12.w
            r9.l = r6
            boolean r10 = r9.m
            if (r10 != 0) goto L_0x0067
            android.widget.EditText r10 = r9.k
            r10.setHint(r6)
        L_0x0067:
            android.widget.ImageView r6 = r9.j
            r10 = 2131230926(0x7f0800ce, float:1.8077919E38)
            r6.setImageResource(r10)
            android.widget.EditText r6 = r9.k
            r6.setText(r7)
            android.widget.EditText r6 = r9.k
            android.text.Editable r10 = r6.getText()
            int r10 = r10.length()
            r6.setSelection(r10)
            java.util.Set r6 = r12.z
            java.util.Set r10 = r12.x
            r9.r = r10
            r9.t = r6
            java.util.Set r6 = r12.A
            java.util.Set r10 = r12.y
            r9.s = r10
            r9.u = r6
            r9.setVisibility(r8)
            android.widget.FrameLayout$LayoutParams r6 = new android.widget.FrameLayout$LayoutParams
            r10 = -1
            r6.<init>(r10, r10)
            android.widget.FrameLayout r10 = r12.u
            r10.addView(r9, r6)
            bne r6 = r9.i
            r6.c(r7)
            r12.v = r9
        L_0x00a6:
            java.util.List r6 = r12.I
            java.util.Iterator r6 = r6.iterator()
        L_0x00ac:
            boolean r9 = r6.hasNext()
            if (r9 == 0) goto L_0x00cf
            java.lang.Object r9 = r6.next()
            bmn r9 = (defpackage.bmn) r9
            bmv r10 = defpackage.bmv.SEARCH
            if (r2 != r10) goto L_0x00be
            r10 = r3
            goto L_0x00bf
        L_0x00be:
            r10 = r5
        L_0x00bf:
            boolean r11 = r9.a
            if (r10 == r11) goto L_0x00ac
            r9.a = r10
            bml r10 = r9.b
            boolean r10 = r10.c
            if (r10 == 0) goto L_0x00ac
            r9.a()
            goto L_0x00ac
        L_0x00cf:
            gp r6 = new gp
            r9 = 19
            r6.<init>(r12, r9, r7)
            int r9 = r0.ordinal()
            if (r9 == r3) goto L_0x00f1
            r10 = 2
            if (r9 == r10) goto L_0x00e8
            android.widget.ImageView r9 = r12.k
            r10 = 2131230916(0x7f0800c4, float:1.8077898E38)
            r9.setImageResource(r10)
            goto L_0x00f9
        L_0x00e8:
            android.widget.ImageView r9 = r12.k
            r10 = 2131230920(0x7f0800c8, float:1.8077906E38)
            r9.setImageResource(r10)
            goto L_0x00f9
        L_0x00f1:
            android.widget.ImageView r9 = r12.k
            r10 = 2131230918(0x7f0800c6, float:1.8077902E38)
            r9.setImageResource(r10)
        L_0x00f9:
            android.widget.ImageView r9 = r12.k
            bmp r10 = defpackage.bmp.DISABLED
            if (r0 == r10) goto L_0x0101
            r10 = r5
            goto L_0x0102
        L_0x0101:
            r10 = r8
        L_0x0102:
            r9.setVisibility(r10)
            android.widget.ImageView r9 = r12.l
            r10 = 4
            r9.setVisibility(r10)
            android.view.ViewGroup r9 = r12.s
            r9.setVisibility(r8)
            android.view.ViewGroup r9 = r12.m
            bmp r11 = defpackage.bmp.DISABLED
            if (r0 != r11) goto L_0x011d
            boolean r11 = r12.K
            if (r11 == 0) goto L_0x011b
            goto L_0x011e
        L_0x011b:
            r10 = r8
            goto L_0x011e
        L_0x011d:
            r10 = r5
        L_0x011e:
            r9.setVisibility(r10)
            bmp r9 = defpackage.bmp.DISABLED
            if (r0 == r9) goto L_0x012b
            android.view.ViewGroup r9 = r12.m
            r9.setOnClickListener(r6)
            goto L_0x0130
        L_0x012b:
            android.view.ViewGroup r6 = r12.m
            r6.setOnClickListener(r7)
        L_0x0130:
            android.view.ViewGroup r6 = r12.m
            boolean r9 = r6.hasOnClickListeners()
            r6.setClickable(r9)
            android.view.ViewGroup r6 = r12.s
            r6.setOnClickListener(r7)
            android.view.ViewGroup r6 = r12.s
            r6.setClickable(r5)
            android.view.ViewGroup r6 = r12.m
            bmp r9 = defpackage.bmp.DISABLED
            if (r0 == r9) goto L_0x0152
            android.content.Context r0 = r12.b
            r7 = 2132017235(0x7f140053, float:1.9672743E38)
            java.lang.String r7 = r0.getString(r7)
        L_0x0152:
            r6.setContentDescription(r7)
            android.view.ViewGroup r0 = r12.n
            if (r1 == 0) goto L_0x015d
            boolean r6 = r12.C
            if (r6 == 0) goto L_0x0161
        L_0x015d:
            if (r4 != 0) goto L_0x0161
            r6 = r5
            goto L_0x0162
        L_0x0161:
            r6 = r8
        L_0x0162:
            r0.setVisibility(r6)
            bok r0 = r12.q
            bjj r6 = r12.r
            java.lang.CharSequence r6 = r6.a()
            boolean r6 = android.text.TextUtils.isEmpty(r6)
            if (r3 == r6) goto L_0x0175
            r6 = r5
            goto L_0x0176
        L_0x0175:
            r6 = r8
        L_0x0176:
            r0.setVisibility(r6)
            com.android.car.ui.toolbar.TabLayout r0 = r12.t
            if (r1 == 0) goto L_0x0189
            bmv r1 = r12.G
            bmv r6 = defpackage.bmv.DISABLED
            if (r1 == r6) goto L_0x0187
            boolean r1 = r12.C
            if (r1 == 0) goto L_0x0189
        L_0x0187:
            r1 = r5
            goto L_0x018a
        L_0x0189:
            r1 = r8
        L_0x018a:
            r0.setVisibility(r1)
            bna r0 = r12.v
            if (r0 == 0) goto L_0x01db
            if (r4 == 0) goto L_0x01d8
            bmv r1 = defpackage.bmv.EDIT
            if (r2 != r1) goto L_0x0199
            r1 = r3
            goto L_0x019a
        L_0x0199:
            r1 = r5
        L_0x019a:
            boolean r2 = r0.v
            if (r1 == r2) goto L_0x01d2
            if (r1 == 0) goto L_0x01b5
            android.widget.EditText r2 = r0.k
            int r6 = r0.o
            int r7 = r0.q
            r2.setPaddingRelative(r6, r5, r7, r5)
            android.widget.EditText r2 = r0.k
            r6 = 6
            r2.setImeOptions(r6)
            android.widget.ImageView r2 = r0.j
            r2.setVisibility(r8)
            goto L_0x01c9
        L_0x01b5:
            android.widget.EditText r2 = r0.k
            int r6 = r0.p
            int r7 = r0.q
            r2.setPaddingRelative(r6, r5, r7, r5)
            android.widget.EditText r2 = r0.k
            r6 = 3
            r2.setImeOptions(r6)
            android.widget.ImageView r2 = r0.j
            r2.setVisibility(r5)
        L_0x01c9:
            r0.v = r1
            android.view.inputmethod.InputMethodManager r1 = r0.h
            android.widget.EditText r0 = r0.k
            r1.restartInput(r0)
        L_0x01d2:
            bna r0 = r12.v
            r0.setVisibility(r5)
            goto L_0x01db
        L_0x01d8:
            r0.setVisibility(r8)
        L_0x01db:
            android.view.ViewGroup r0 = r12.a
            if (r3 == r4) goto L_0x01e0
            r8 = r5
        L_0x01e0:
            r0.setVisibility(r8)
            bml r0 = r12.B
            if (r4 != 0) goto L_0x0204
            java.util.List r1 = r12.e
            java.util.Iterator r1 = r1.iterator()
            r2 = r5
        L_0x01ee:
            boolean r4 = r1.hasNext()
            if (r4 == 0) goto L_0x0201
            java.lang.Object r4 = r1.next()
            bml r4 = (defpackage.bml) r4
            boolean r4 = r4.n
            if (r4 == 0) goto L_0x01ee
            int r2 = r2 + 1
            goto L_0x01ee
        L_0x0201:
            if (r2 <= 0) goto L_0x0204
            goto L_0x0205
        L_0x0204:
            r3 = r5
        L_0x0205:
            r0.n = r3
            r0.c()
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bnu.c():void");
    }

    public final boolean isStateSet() {
        return this.E;
    }

    public final void setLogo(Drawable drawable) {
        if (this.L) {
            c();
        }
    }

    public final void setMenuItems(List list) {
        List<bml> emptyList = Collections.emptyList();
        ArrayList arrayList = new ArrayList();
        ArrayList arrayList2 = new ArrayList();
        int i2 = 0;
        AtomicInteger atomicInteger = new AtomicInteger(0);
        synchronized (this) {
            if (!emptyList.equals(this.H)) {
                for (bml bml : emptyList) {
                    if (bml.k == bmi.NEVER) {
                        arrayList2.add(bml);
                        bml.b(this.M);
                    } else {
                        arrayList.add(bml);
                    }
                }
                this.H = new ArrayList(emptyList);
                this.e = arrayList2;
                this.I.clear();
                this.a.removeAllViews();
                if (!arrayList2.isEmpty()) {
                    arrayList.add(this.B);
                    b();
                }
                View[] viewArr = new View[arrayList.size()];
                this.h = viewArr;
                while (i2 < arrayList.size()) {
                    bmn bmn = new bmn((bml) arrayList.get(i2), this.a);
                    this.I.add(bmn);
                    bnt bnt = new bnt(this, viewArr, i2, atomicInteger);
                    ayn ayn = new ayn(bmn.c.getContext());
                    ViewGroup viewGroup = bmn.c;
                    bmm bmm = new bmm(bmn, (Consumer) bnt);
                    Object obj = ayn.d;
                    ol olVar = (ol) ((om) ayn.a).c.a();
                    if (olVar == null) {
                        olVar = new ol();
                    }
                    olVar.a = (LayoutInflater) obj;
                    olVar.b = (Handler) ayn.c;
                    olVar.d = R.layout.car_ui_toolbar_menu_item;
                    olVar.c = viewGroup;
                    olVar.g = bmm;
                    olVar.f = null;
                    try {
                        ((om) ayn.a).b.put(olVar);
                        i2++;
                    } catch (InterruptedException e2) {
                        throw new RuntimeException("Failed to enqueue async inflate request", e2);
                    }
                }
                c();
            }
        }
    }

    public final void setNavButtonMode(bmp bmp) {
        if (bmp != this.F) {
            this.F = bmp;
            c();
        }
    }

    public final void setState(bnp bnp) {
        if (this.D != bnp || !this.E) {
            this.D = bnp;
            this.E = true;
            c();
        }
    }

    public final void setSubtitle(CharSequence charSequence) {
        bjj bjj = new bjj(new byw((CharSequence) ""));
        this.r = bjj;
        this.q.a(bjj);
        c();
    }

    public final void setTabs(List list) {
        this.f14J.clear();
        int i2 = -1;
        if (list != null && !list.isEmpty()) {
            if (list.size() > 0) {
                i2 = 0;
            } else {
                throw new IllegalArgumentException("Tab position is invalid: 0");
            }
        }
        TabLayout tabLayout = this.t;
        if (list == null) {
            tabLayout.b = Collections.emptyList();
        } else {
            tabLayout.b = DesugarCollections.unmodifiableList(new ArrayList(list));
        }
        tabLayout.c = i2;
        tabLayout.removeAllViews();
        for (int i3 = 0; i3 < tabLayout.b.size(); i3++) {
            tabLayout.addView(LayoutInflater.from(tabLayout.getContext()).inflate(tabLayout.a, tabLayout, false));
            tabLayout.a(i3);
        }
        c();
    }

    public final void setTitle(int i2) {
        setTitle((CharSequence) this.b.getString(R.string.voice_data_install_title));
    }

    public final void setNavButtonMode(bnk bnk) {
        bmp bmp;
        int ordinal = bnk.ordinal();
        if (ordinal != 0) {
            bmp = ordinal != 1 ? ordinal != 2 ? bmp.DISABLED : bmp.DOWN : bmp.CLOSE;
        } else {
            bmp = bmp.BACK;
        }
        setNavButtonMode(bmp);
    }

    public final void setTitle(CharSequence charSequence) {
        bjj bjj;
        if (charSequence == null) {
            bjj = new bjj(new byw((CharSequence) ""));
        } else {
            bjj = new bjj(new byw(charSequence));
        }
        this.p = bjj;
        this.o.a(bjj);
        c();
    }
}
