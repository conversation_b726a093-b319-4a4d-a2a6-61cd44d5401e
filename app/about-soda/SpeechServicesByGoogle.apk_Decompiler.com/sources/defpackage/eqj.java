package defpackage;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

/* renamed from: eqj  reason: default package */
/* compiled from: PG */
final class eqj extends jmi implements jna {
    final /* synthetic */ eql a;
    final /* synthetic */ eqq b;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eqj(eql eql, eqq eqq, jlr jlr) {
        super(1, jlr);
        this.a = eql;
        this.b = eqq;
    }

    public final /* bridge */ /* synthetic */ Object a(Object obj) {
        return new eqj(this.a, this.b, (jlr) obj).bk(jkd.a);
    }

    public final Object bk(Object obj) {
        eqq eqq;
        eqb eqb;
        eah eah;
        eak eak;
        jji.c(obj);
        eql eql = this.a;
        List list = eql.b;
        ArrayList arrayList = new ArrayList();
        Iterator it = list.iterator();
        while (true) {
            eqq = this.b;
            if (!it.hasNext()) {
                break;
            }
            Object next = it.next();
            if (!jnu.i(((eqp) next).c, eqq.e)) {
                arrayList.add(next);
            }
        }
        eql.b = arrayList;
        if (eqq.b == 2) {
            List list2 = eql.c;
            if (!(list2 instanceof Collection) || !list2.isEmpty()) {
                Iterator it2 = list2.iterator();
                while (true) {
                    if (it2.hasNext()) {
                        if (jnu.i(((eqq) it2.next()).e, eqq.e)) {
                            ((hby) eql.a.f().h(hdg.a, "ALT.MicStateReporter").j("com/google/android/libraries/search/audio/microphone/state/impl/MicStateReporterImpl", "applyToPartialUpdates", 358, "MicStateReporterImpl.kt")).u("#audio# skip update(id(%s)), already active", eqq.e);
                            htk l = eqb.b.l();
                            jnu.d(l, "newBuilder(...)");
                            eqb = jnu.e(l, "builder").N();
                            break;
                        }
                    } else {
                        break;
                    }
                }
            }
            eql.c = jji.x(eql.c, eqq);
            htk l2 = eqb.b.l();
            jnu.d(l2, "newBuilder(...)");
            byw ac = jnu.e(l2, "builder");
            ac.P();
            htk l3 = eps.f.l();
            jnu.d(l3, "newBuilder(...)");
            dlv Y = jnu.e(l3, "builder");
            ept b2 = ept.b(eqq.d);
            if (b2 == null) {
                b2 = ept.CLIENT_TYPE_UNKNOWN;
            }
            Y.z(b2);
            if ((eqq.a & 4) != 0) {
                Y.y(eqq.f);
            }
            if (eqq.b == 2) {
                eak = (eak) eqq.c;
            } else {
                eak = eak.c;
            }
            jnu.e(eak, "value");
            htk htk = (htk) Y.a;
            if (!htk.b.B()) {
                htk.u();
            }
            eps eps = (eps) htk.b;
            eak.getClass();
            eps.c = eak;
            eps.b = 2;
            ac.O(Y.x());
            eqb = ac.N();
        } else {
            List list3 = eql.c;
            if (!(list3 instanceof Collection) || !list3.isEmpty()) {
                Iterator it3 = list3.iterator();
                while (true) {
                    if (!it3.hasNext()) {
                        break;
                    } else if (jnu.i(((eqq) it3.next()).e, eqq.e)) {
                        List list4 = eql.c;
                        ArrayList arrayList2 = new ArrayList();
                        for (Object next2 : list4) {
                            if (!jnu.i(((eqq) next2).e, eqq.e)) {
                                arrayList2.add(next2);
                            }
                        }
                        eql.c = arrayList2;
                        htk l4 = eqb.b.l();
                        jnu.d(l4, "newBuilder(...)");
                        byw ac2 = jnu.e(l4, "builder");
                        ac2.P();
                        htk l5 = eps.f.l();
                        jnu.d(l5, "newBuilder(...)");
                        dlv Y2 = jnu.e(l5, "builder");
                        ept b3 = ept.b(eqq.d);
                        if (b3 == null) {
                            b3 = ept.CLIENT_TYPE_UNKNOWN;
                        }
                        Y2.z(b3);
                        if ((eqq.a & 4) != 0) {
                            Y2.y(eqq.f);
                        }
                        if (eqq.b == 3) {
                            eah = (eah) eqq.c;
                        } else {
                            eah = eah.c;
                        }
                        jnu.e(eah, "value");
                        htk htk2 = (htk) Y2.a;
                        if (!htk2.b.B()) {
                            htk2.u();
                        }
                        eps eps2 = (eps) htk2.b;
                        eah.getClass();
                        eps2.c = eah;
                        eps2.b = 3;
                        ac2.O(Y2.x());
                        eqb = ac2.N();
                    }
                }
            }
            ((hby) eql.a.f().h(hdg.a, "ALT.MicStateReporter").j("com/google/android/libraries/search/audio/microphone/state/impl/MicStateReporterImpl", "applyToPartialUpdates", 371, "MicStateReporterImpl.kt")).u("#audio# skip update(id(%s)), no active found", eqq.e);
            htk l6 = eqb.b.l();
            jnu.d(l6, "newBuilder(...)");
            eqb = jnu.e(l6, "builder").N();
        }
        huf huf = eqb.a;
        jnu.d(huf, "getDeltasList(...)");
        if (!huf.isEmpty()) {
            eql eql2 = this.a;
            eql2.c(eql2.b(), eqb);
        }
        return jkd.a;
    }
}
