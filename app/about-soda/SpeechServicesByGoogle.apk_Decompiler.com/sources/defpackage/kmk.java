package defpackage;

/* renamed from: kmk  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class kmk implements Runnable {
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v22, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v7, resolved type: java.lang.String} */
    /* JADX WARNING: type inference failed for: r5v8, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v10, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:175:0x0290 A[Catch:{ RuntimeException -> 0x029e }] */
    /* JADX WARNING: Removed duplicated region for block: B:212:0x0299 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:87:0x0151  */
    /* JADX WARNING: Removed duplicated region for block: B:88:0x0153  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void run() {
        /*
            r17 = this;
            java.lang.String r1 = "HttpFlagsLoader"
            kml r0 = new kml
            r0.<init>()
            org.chromium.net.impl.CronetLibraryLoader.d = r0
            long r2 = android.os.SystemClock.uptimeMillis()
            android.content.Context r4 = defpackage.jqw.c
            android.os.Bundle r0 = defpackage.kmt.a(r4)
            java.lang.String r5 = "android.net.http.ReadHttpFlags"
            r6 = 1
            boolean r0 = r0.getBoolean(r5, r6)
            java.lang.String r5 = "cr_HttpFlagsLoader"
            r7 = -1
            r9 = 0
            if (r0 != 0) goto L_0x002c
            java.lang.String r0 = org.chromium.net.impl.CronetLibraryLoader.a
            java.lang.Object[] r1 = new java.lang.Object[r9]
            java.lang.String r5 = "Not loading HTTP flags because they are disabled in the manifest"
            defpackage.jqw.H(r0, r5, r1)
            r0 = 0
            goto L_0x015a
        L_0x002c:
            android.content.pm.PackageManager r0 = r4.getPackageManager()     // Catch:{ RuntimeException -> 0x0146 }
            android.content.Intent r10 = new android.content.Intent     // Catch:{ RuntimeException -> 0x0146 }
            java.lang.String r11 = "android.net.http.FLAGS_FILE_PROVIDER"
            r10.<init>(r11)     // Catch:{ RuntimeException -> 0x0146 }
            r11 = 1048576(0x100000, float:1.469368E-39)
            android.content.pm.ResolveInfo r0 = r0.resolveService(r10, r11)     // Catch:{ RuntimeException -> 0x0146 }
            if (r0 != 0) goto L_0x0046
            java.lang.String r0 = "Unable to resolve the HTTP flags file provider package. This is expected if the host system is not set up to provide HTTP flags."
            android.util.Log.i(r5, r0)     // Catch:{ RuntimeException -> 0x0146 }
            r0 = 0
            goto L_0x004a
        L_0x0046:
            android.content.pm.ServiceInfo r0 = r0.serviceInfo     // Catch:{ RuntimeException -> 0x0146 }
            android.content.pm.ApplicationInfo r0 = r0.applicationInfo     // Catch:{ RuntimeException -> 0x0146 }
        L_0x004a:
            if (r0 != 0) goto L_0x004e
        L_0x004c:
            goto L_0x014c
        L_0x004e:
            java.lang.String r10 = "Found application exporting HTTP flags: %s"
            java.lang.String r11 = r0.packageName     // Catch:{ RuntimeException -> 0x0146 }
            java.lang.Object[] r12 = new java.lang.Object[r6]     // Catch:{ RuntimeException -> 0x0146 }
            r12[r9] = r11     // Catch:{ RuntimeException -> 0x0146 }
            defpackage.jqw.H(r1, r10, r12)     // Catch:{ RuntimeException -> 0x0146 }
            java.io.File r10 = new java.io.File     // Catch:{ RuntimeException -> 0x0146 }
            java.io.File r11 = new java.io.File     // Catch:{ RuntimeException -> 0x0146 }
            java.io.File r12 = new java.io.File     // Catch:{ RuntimeException -> 0x0146 }
            java.lang.String r0 = r0.deviceProtectedDataDir     // Catch:{ RuntimeException -> 0x0146 }
            r12.<init>(r0)     // Catch:{ RuntimeException -> 0x0146 }
            java.lang.String r0 = "app_httpflags"
            r11.<init>(r12, r0)     // Catch:{ RuntimeException -> 0x0146 }
            java.lang.String r0 = "flags.binarypb"
            r10.<init>(r11, r0)     // Catch:{ RuntimeException -> 0x0146 }
            java.lang.String r0 = "HTTP flags file path: %s"
            java.lang.String r11 = r10.getAbsolutePath()     // Catch:{ RuntimeException -> 0x0146 }
            java.lang.Object[] r12 = new java.lang.Object[r6]     // Catch:{ RuntimeException -> 0x0146 }
            r12[r9] = r11     // Catch:{ RuntimeException -> 0x0146 }
            defpackage.jqw.H(r1, r0, r12)     // Catch:{ RuntimeException -> 0x0146 }
            java.io.FileInputStream r11 = new java.io.FileInputStream     // Catch:{ FileNotFoundException -> 0x0124, IOException -> 0x011b }
            r11.<init>(r10)     // Catch:{ FileNotFoundException -> 0x0124, IOException -> 0x011b }
            kma r0 = defpackage.kma.DEFAULT_INSTANCE     // Catch:{ all -> 0x010f }
            hte r12 = defpackage.hte.a     // Catch:{ all -> 0x010f }
            hvj r12 = defpackage.hvj.a     // Catch:{ all -> 0x010f }
            hte r12 = defpackage.hte.a     // Catch:{ all -> 0x010f }
            int r13 = r11.read()     // Catch:{ hui -> 0x0103, IOException -> 0x00fc }
            if (r13 != r7) goto L_0x0092
            r0 = 0
            goto L_0x00b6
        L_0x0092:
            int r13 = defpackage.hsu.I(r13, r11)     // Catch:{ hui -> 0x0103, IOException -> 0x00fc }
            hry r14 = new hry     // Catch:{ all -> 0x010f }
            r14.<init>(r11, r13)     // Catch:{ all -> 0x010f }
            hsu r13 = defpackage.hsu.K(r14)     // Catch:{ all -> 0x010f }
            htq r0 = r0.n()     // Catch:{ all -> 0x010f }
            hvj r14 = defpackage.hvj.a     // Catch:{ hui -> 0x00f0, hvx -> 0x00ea, IOException -> 0x00d4, RuntimeException -> 0x00c3 }
            hvp r14 = r14.b(r0)     // Catch:{ hui -> 0x00f0, hvx -> 0x00ea, IOException -> 0x00d4, RuntimeException -> 0x00c3 }
            hsv r15 = defpackage.hsv.p(r13)     // Catch:{ hui -> 0x00f0, hvx -> 0x00ea, IOException -> 0x00d4, RuntimeException -> 0x00c3 }
            r14.l(r0, r15, r12)     // Catch:{ hui -> 0x00f0, hvx -> 0x00ea, IOException -> 0x00d4, RuntimeException -> 0x00c3 }
            r14.g(r0)     // Catch:{ hui -> 0x00f0, hvx -> 0x00ea, IOException -> 0x00d4, RuntimeException -> 0x00c3 }
            r13.z(r9)     // Catch:{ hui -> 0x00c0 }
        L_0x00b6:
            defpackage.htq.D(r0)     // Catch:{ all -> 0x010f }
            kma r0 = (defpackage.kma) r0     // Catch:{ all -> 0x010f }
            r11.close()     // Catch:{ FileNotFoundException -> 0x0124, IOException -> 0x011b }
            goto L_0x0138
        L_0x00c0:
            r0 = move-exception
            r12 = r0
            throw r12     // Catch:{ all -> 0x010f }
        L_0x00c3:
            r0 = move-exception
            java.lang.Throwable r12 = r0.getCause()     // Catch:{ all -> 0x010f }
            boolean r12 = r12 instanceof defpackage.hui     // Catch:{ all -> 0x010f }
            if (r12 == 0) goto L_0x00d3
            java.lang.Throwable r0 = r0.getCause()     // Catch:{ all -> 0x010f }
            hui r0 = (defpackage.hui) r0     // Catch:{ all -> 0x010f }
            throw r0     // Catch:{ all -> 0x010f }
        L_0x00d3:
            throw r0     // Catch:{ all -> 0x010f }
        L_0x00d4:
            r0 = move-exception
            java.lang.Throwable r12 = r0.getCause()     // Catch:{ all -> 0x010f }
            boolean r12 = r12 instanceof defpackage.hui     // Catch:{ all -> 0x010f }
            if (r12 == 0) goto L_0x00e4
            java.lang.Throwable r0 = r0.getCause()     // Catch:{ all -> 0x010f }
            hui r0 = (defpackage.hui) r0     // Catch:{ all -> 0x010f }
            throw r0     // Catch:{ all -> 0x010f }
        L_0x00e4:
            hui r12 = new hui     // Catch:{ all -> 0x010f }
            r12.<init>((java.io.IOException) r0)     // Catch:{ all -> 0x010f }
            throw r12     // Catch:{ all -> 0x010f }
        L_0x00ea:
            r0 = move-exception
            hui r0 = r0.a()     // Catch:{ all -> 0x010f }
            throw r0     // Catch:{ all -> 0x010f }
        L_0x00f0:
            r0 = move-exception
            boolean r12 = r0.a     // Catch:{ all -> 0x010f }
            if (r12 == 0) goto L_0x00fb
            hui r12 = new hui     // Catch:{ all -> 0x010f }
            r12.<init>((java.io.IOException) r0)     // Catch:{ all -> 0x010f }
            r0 = r12
        L_0x00fb:
            throw r0     // Catch:{ all -> 0x010f }
        L_0x00fc:
            r0 = move-exception
            hui r12 = new hui     // Catch:{ all -> 0x010f }
            r12.<init>((java.io.IOException) r0)     // Catch:{ all -> 0x010f }
            throw r12     // Catch:{ all -> 0x010f }
        L_0x0103:
            r0 = move-exception
            boolean r12 = r0.a     // Catch:{ all -> 0x010f }
            if (r12 == 0) goto L_0x010e
            hui r12 = new hui     // Catch:{ all -> 0x010f }
            r12.<init>((java.io.IOException) r0)     // Catch:{ all -> 0x010f }
            throw r12     // Catch:{ all -> 0x010f }
        L_0x010e:
            throw r0     // Catch:{ all -> 0x010f }
        L_0x010f:
            r0 = move-exception
            r12 = r0
            r11.close()     // Catch:{ all -> 0x0115 }
            goto L_0x011a
        L_0x0115:
            r0 = move-exception
            r11 = r0
            r12.addSuppressed(r11)     // Catch:{ FileNotFoundException -> 0x0124, IOException -> 0x011b }
        L_0x011a:
            throw r12     // Catch:{ FileNotFoundException -> 0x0124, IOException -> 0x011b }
        L_0x011b:
            r0 = move-exception
            java.lang.RuntimeException r1 = new java.lang.RuntimeException     // Catch:{ RuntimeException -> 0x0146 }
            java.lang.String r10 = "Unable to read HTTP flags file"
            r1.<init>(r10, r0)     // Catch:{ RuntimeException -> 0x0146 }
            throw r1     // Catch:{ RuntimeException -> 0x0146 }
        L_0x0124:
            java.lang.String r0 = "HTTP flags file `%s` is missing. This is expected if HTTP flags functionality is currently disabled in the host system."
            java.lang.String r10 = r10.getPath()     // Catch:{ RuntimeException -> 0x0146 }
            java.util.Locale r11 = java.util.Locale.US     // Catch:{ RuntimeException -> 0x0146 }
            java.lang.Object[] r12 = new java.lang.Object[r6]     // Catch:{ RuntimeException -> 0x0146 }
            r12[r9] = r10     // Catch:{ RuntimeException -> 0x0146 }
            java.lang.String r0 = java.lang.String.format(r11, r0, r12)     // Catch:{ RuntimeException -> 0x0146 }
            android.util.Log.i(r5, r0)     // Catch:{ RuntimeException -> 0x0146 }
            r0 = 0
        L_0x0138:
            if (r0 != 0) goto L_0x013c
            goto L_0x004c
        L_0x013c:
            java.lang.String r10 = "Successfully loaded HTTP flags: %s"
            java.lang.Object[] r11 = new java.lang.Object[r6]     // Catch:{ RuntimeException -> 0x0146 }
            r11[r9] = r0     // Catch:{ RuntimeException -> 0x0146 }
            defpackage.jqw.H(r1, r10, r11)     // Catch:{ RuntimeException -> 0x0146 }
            goto L_0x014d
        L_0x0146:
            r0 = move-exception
            java.lang.String r1 = "Unable to load HTTP flags file"
            android.util.Log.i(r5, r1, r0)
        L_0x014c:
            r0 = 0
        L_0x014d:
            kml r1 = org.chromium.net.impl.CronetLibraryLoader.d
            if (r0 == 0) goto L_0x0153
            r5 = r6
            goto L_0x0154
        L_0x0153:
            r5 = r9
        L_0x0154:
            java.lang.Boolean r5 = java.lang.Boolean.valueOf(r5)
            r1.b = r5
        L_0x015a:
            if (r0 != 0) goto L_0x0168
            kma r0 = defpackage.kma.DEFAULT_INSTANCE
            htk r0 = r0.l()
            htq r0 = r0.r()
            kma r0 = (defpackage.kma) r0
        L_0x0168:
            java.lang.String r1 = r4.getPackageName()
            java.lang.String r4 = "128.0.6557.4"
            int[] r4 = defpackage.bzj.W(r4)
            java.util.HashMap r5 = new java.util.HashMap
            r5.<init>()
            huv r0 = r0.flags_
            java.util.Map r0 = j$.util.DesugarCollections.unmodifiableMap(r0)
            java.util.Set r0 = r0.entrySet()
            java.util.Iterator r0 = r0.iterator()
        L_0x0185:
            boolean r10 = r0.hasNext()
            r13 = 2
            r14 = 3
            if (r10 == 0) goto L_0x02be
            java.lang.Object r10 = r0.next()
            java.util.Map$Entry r10 = (java.util.Map.Entry) r10
            java.lang.Object r15 = r10.getValue()     // Catch:{ RuntimeException -> 0x029e }
            kly r15 = (defpackage.kly) r15     // Catch:{ RuntimeException -> 0x029e }
            huf r15 = r15.constrainedValues_     // Catch:{ RuntimeException -> 0x029e }
            java.util.Iterator r15 = r15.iterator()     // Catch:{ RuntimeException -> 0x029e }
        L_0x019f:
            boolean r16 = r15.hasNext()     // Catch:{ RuntimeException -> 0x029e }
            if (r16 == 0) goto L_0x028c
            java.lang.Object r16 = r15.next()     // Catch:{ RuntimeException -> 0x029e }
            r11 = r16
            klx r11 = (defpackage.klx) r11     // Catch:{ RuntimeException -> 0x029e }
            int r12 = r11.bitField0_     // Catch:{ RuntimeException -> 0x029e }
            r12 = r12 & r6
            if (r12 == 0) goto L_0x01ba
            java.lang.String r12 = r11.appId_     // Catch:{ RuntimeException -> 0x029e }
            boolean r12 = r12.equals(r1)     // Catch:{ RuntimeException -> 0x029e }
            if (r12 == 0) goto L_0x019f
        L_0x01ba:
            int r12 = r11.bitField0_     // Catch:{ RuntimeException -> 0x029e }
            r12 = r12 & r13
            if (r12 == 0) goto L_0x01e7
            java.lang.String r12 = r11.minVersion_     // Catch:{ RuntimeException -> 0x029e }
            int[] r12 = defpackage.bzj.W(r12)     // Catch:{ RuntimeException -> 0x029e }
            r7 = r9
        L_0x01c6:
            int r9 = r4.length     // Catch:{ RuntimeException -> 0x029e }
            int r8 = r12.length     // Catch:{ RuntimeException -> 0x029e }
            int r13 = java.lang.Math.max(r9, r8)     // Catch:{ RuntimeException -> 0x029e }
            if (r7 >= r13) goto L_0x01e7
            if (r7 >= r9) goto L_0x01d3
            r9 = r4[r7]     // Catch:{ RuntimeException -> 0x029e }
            goto L_0x01d4
        L_0x01d3:
            r9 = 0
        L_0x01d4:
            if (r7 >= r8) goto L_0x01d9
            r8 = r12[r7]     // Catch:{ RuntimeException -> 0x029e }
            goto L_0x01da
        L_0x01d9:
            r8 = 0
        L_0x01da:
            if (r9 <= r8) goto L_0x01dd
            goto L_0x01e7
        L_0x01dd:
            if (r9 < r8) goto L_0x01e3
            int r7 = r7 + 1
            r13 = 2
            goto L_0x01c6
        L_0x01e3:
            r7 = -1
            r9 = 0
            r13 = 2
            goto L_0x019f
        L_0x01e7:
            int r7 = r11.valueCase_     // Catch:{ RuntimeException -> 0x029e }
            r8 = 7
            r9 = 6
            r12 = 5
            r13 = 4
            if (r7 == 0) goto L_0x0205
            if (r7 == r14) goto L_0x0203
            if (r7 == r13) goto L_0x0201
            if (r7 == r12) goto L_0x01ff
            if (r7 == r9) goto L_0x01fd
            if (r7 == r8) goto L_0x01fb
            r15 = 0
            goto L_0x0206
        L_0x01fb:
            r15 = r12
            goto L_0x0206
        L_0x01fd:
            r15 = r13
            goto L_0x0206
        L_0x01ff:
            r15 = r14
            goto L_0x0206
        L_0x0201:
            r15 = 2
            goto L_0x0206
        L_0x0203:
            r15 = r6
            goto L_0x0206
        L_0x0205:
            r15 = r9
        L_0x0206:
            int r9 = r15 + -1
            if (r15 == 0) goto L_0x028a
            if (r9 == 0) goto L_0x0276
            if (r9 == r6) goto L_0x0263
            r6 = 2
            if (r9 == r6) goto L_0x0251
            if (r9 == r14) goto L_0x0241
            if (r9 == r13) goto L_0x0232
            if (r9 != r12) goto L_0x021b
            r6 = 0
            r7 = 0
            goto L_0x028e
        L_0x021b:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException     // Catch:{ RuntimeException -> 0x029e }
            java.lang.String r1 = "Flag value uses unknown value type "
            java.lang.String r2 = defpackage.jqw.F(r15)     // Catch:{ RuntimeException -> 0x029e }
            java.util.Objects.toString(r2)     // Catch:{ RuntimeException -> 0x029e }
            java.lang.String r2 = defpackage.jqw.F(r15)     // Catch:{ RuntimeException -> 0x029e }
            java.lang.String r1 = r1.concat(r2)     // Catch:{ RuntimeException -> 0x029e }
            r0.<init>(r1)     // Catch:{ RuntimeException -> 0x029e }
            throw r0     // Catch:{ RuntimeException -> 0x029e }
        L_0x0232:
            bzj r6 = new bzj     // Catch:{ RuntimeException -> 0x029e }
            if (r7 != r8) goto L_0x023b
            java.lang.Object r7 = r11.value_     // Catch:{ RuntimeException -> 0x029e }
            hsq r7 = (defpackage.hsq) r7     // Catch:{ RuntimeException -> 0x029e }
            goto L_0x023d
        L_0x023b:
            hsq r7 = defpackage.hsq.b     // Catch:{ RuntimeException -> 0x029e }
        L_0x023d:
            r6.<init>((java.lang.Object) r7)     // Catch:{ RuntimeException -> 0x029e }
            goto L_0x0287
        L_0x0241:
            bzj r6 = new bzj     // Catch:{ RuntimeException -> 0x029e }
            java.lang.String r8 = ""
            r9 = 6
            if (r7 != r9) goto L_0x024d
            java.lang.Object r7 = r11.value_     // Catch:{ RuntimeException -> 0x029e }
            r8 = r7
            java.lang.String r8 = (java.lang.String) r8     // Catch:{ RuntimeException -> 0x029e }
        L_0x024d:
            r6.<init>((java.lang.Object) r8)     // Catch:{ RuntimeException -> 0x029e }
            goto L_0x0287
        L_0x0251:
            bzj r6 = new bzj     // Catch:{ RuntimeException -> 0x029e }
            if (r7 != r12) goto L_0x025e
            java.lang.Object r7 = r11.value_     // Catch:{ RuntimeException -> 0x029e }
            java.lang.Float r7 = (java.lang.Float) r7     // Catch:{ RuntimeException -> 0x029e }
            float r7 = r7.floatValue()     // Catch:{ RuntimeException -> 0x029e }
            goto L_0x025f
        L_0x025e:
            r7 = 0
        L_0x025f:
            r6.<init>((float) r7)     // Catch:{ RuntimeException -> 0x029e }
            goto L_0x0287
        L_0x0263:
            bzj r6 = new bzj     // Catch:{ RuntimeException -> 0x029e }
            if (r7 != r13) goto L_0x0270
            java.lang.Object r7 = r11.value_     // Catch:{ RuntimeException -> 0x029e }
            java.lang.Long r7 = (java.lang.Long) r7     // Catch:{ RuntimeException -> 0x029e }
            long r11 = r7.longValue()     // Catch:{ RuntimeException -> 0x029e }
            goto L_0x0272
        L_0x0270:
            r11 = 0
        L_0x0272:
            r6.<init>((long) r11)     // Catch:{ RuntimeException -> 0x029e }
            goto L_0x0287
        L_0x0276:
            bzj r6 = new bzj     // Catch:{ RuntimeException -> 0x029e }
            if (r7 != r14) goto L_0x0283
            java.lang.Object r7 = r11.value_     // Catch:{ RuntimeException -> 0x029e }
            java.lang.Boolean r7 = (java.lang.Boolean) r7     // Catch:{ RuntimeException -> 0x029e }
            boolean r7 = r7.booleanValue()     // Catch:{ RuntimeException -> 0x029e }
            goto L_0x0284
        L_0x0283:
            r7 = 0
        L_0x0284:
            r6.<init>((boolean) r7)     // Catch:{ RuntimeException -> 0x029e }
        L_0x0287:
            r7 = r6
            r6 = 0
            goto L_0x028e
        L_0x028a:
            r6 = 0
            throw r6     // Catch:{ RuntimeException -> 0x029e }
        L_0x028c:
            r6 = 0
            r7 = r6
        L_0x028e:
            if (r7 == 0) goto L_0x0299
            java.lang.Object r8 = r10.getKey()     // Catch:{ RuntimeException -> 0x029e }
            java.lang.String r8 = (java.lang.String) r8     // Catch:{ RuntimeException -> 0x029e }
            r5.put(r8, r7)     // Catch:{ RuntimeException -> 0x029e }
        L_0x0299:
            r6 = 1
            r7 = -1
            r9 = 0
            goto L_0x0185
        L_0x029e:
            r0 = move-exception
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.Object r2 = r10.getKey()
            java.lang.String r2 = (java.lang.String) r2
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            java.lang.String r4 = "Unable to resolve HTTP flag `"
            r3.<init>(r4)
            r3.append(r2)
            java.lang.String r2 = "`"
            r3.append(r2)
            java.lang.String r2 = r3.toString()
            r1.<init>(r2, r0)
            throw r1
        L_0x02be:
            bzj r0 = new bzj
            r0.<init>((java.lang.Object) r5)
            org.chromium.net.impl.CronetLibraryLoader.e = r0
            android.os.ConditionVariable r0 = org.chromium.net.impl.CronetLibraryLoader.c
            r0.open()
            bzj r0 = org.chromium.net.impl.CronetLibraryLoader.e
            java.util.Map r0 = r0.V()
            java.lang.String r1 = "Cronet_log_me"
            java.lang.Object r0 = r0.get(r1)
            bzj r0 = (defpackage.bzj) r0
            if (r0 == 0) goto L_0x02fc
            java.lang.String r1 = org.chromium.net.impl.CronetLibraryLoader.a
            java.lang.String r1 = java.lang.String.valueOf(r1)
            java.lang.String r0 = r0.aa()
            r4 = 1
            java.lang.Object[] r5 = new java.lang.Object[r4]
            r4 = 0
            r5[r4] = r0
            java.util.Locale r0 = java.util.Locale.US
            java.lang.String r6 = "HTTP flags log line: %s"
            java.lang.String r0 = java.lang.String.format(r0, r6, r5)
            java.lang.String r5 = "cr_"
            java.lang.String r1 = r5.concat(r1)
            android.util.Log.i(r1, r0)
            goto L_0x02fd
        L_0x02fc:
            r4 = 0
        L_0x02fd:
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            bzj r1 = org.chromium.net.impl.CronetLibraryLoader.e
            java.util.Map r1 = r1.V()
            java.util.Set r1 = r1.entrySet()
            java.util.Iterator r1 = r1.iterator()
        L_0x0310:
            boolean r5 = r1.hasNext()
            if (r5 == 0) goto L_0x0385
            java.lang.Object r5 = r1.next()
            java.util.Map$Entry r5 = (java.util.Map.Entry) r5
            android.util.Pair r6 = new android.util.Pair
            java.lang.Object r7 = r5.getKey()
            java.lang.String r7 = (java.lang.String) r7
            long r7 = defpackage.kog.a(r7)
            java.lang.Long r7 = java.lang.Long.valueOf(r7)
            java.lang.Object r5 = r5.getValue()
            bzj r5 = (defpackage.bzj) r5
            int r8 = r5.ac()
            r9 = -1
            int r8 = r8 + r9
            if (r8 == 0) goto L_0x036e
            r10 = 1
            if (r8 == r10) goto L_0x0368
            r10 = 2
            if (r8 == r10) goto L_0x0358
            if (r8 == r14) goto L_0x034f
            hsq r5 = r5.Z()
            byte[] r5 = r5.y()
            long r11 = defpackage.kog.b(r5)
            goto L_0x037a
        L_0x034f:
            java.lang.String r5 = r5.aa()
            long r11 = defpackage.kog.a(r5)
            goto L_0x037a
        L_0x0358:
            float r5 = r5.X()
            double r11 = (double) r5
            r15 = 4741671816366391296(0x41cdcd6500000000, double:1.0E9)
            double r11 = r11 * r15
            long r11 = java.lang.Math.round(r11)
            goto L_0x037a
        L_0x0368:
            r10 = 2
            long r11 = r5.Y()
            goto L_0x037a
        L_0x036e:
            r10 = 2
            boolean r5 = r5.ab()
            if (r5 == 0) goto L_0x0378
            r11 = 1
            goto L_0x037a
        L_0x0378:
            r11 = 0
        L_0x037a:
            java.lang.Long r5 = java.lang.Long.valueOf(r11)
            r6.<init>(r7, r5)
            r0.add(r6)
            goto L_0x0310
        L_0x0385:
            dfj r1 = new dfj
            r5 = 10
            r1.<init>(r5)
            java.util.Collections.sort(r0, r1)
            kml r1 = org.chromium.net.impl.CronetLibraryLoader.d
            java.util.ArrayList r5 = new java.util.ArrayList
            r5.<init>()
            r1.c = r5
            kml r1 = org.chromium.net.impl.CronetLibraryLoader.d
            java.util.ArrayList r5 = new java.util.ArrayList
            r5.<init>()
            r1.d = r5
            int r1 = r0.size()
            r9 = r4
        L_0x03a6:
            if (r9 >= r1) goto L_0x03c7
            java.lang.Object r4 = r0.get(r9)
            android.util.Pair r4 = (android.util.Pair) r4
            kml r5 = org.chromium.net.impl.CronetLibraryLoader.d
            java.lang.Object r5 = r5.c
            java.lang.Object r6 = r4.first
            java.lang.Long r6 = (java.lang.Long) r6
            r5.add(r6)
            kml r5 = org.chromium.net.impl.CronetLibraryLoader.d
            java.lang.Object r5 = r5.d
            java.lang.Object r4 = r4.second
            java.lang.Long r4 = (java.lang.Long) r4
            r5.add(r4)
            int r9 = r9 + 1
            goto L_0x03a6
        L_0x03c7:
            kml r0 = org.chromium.net.impl.CronetLibraryLoader.d
            long r4 = android.os.SystemClock.uptimeMillis()
            long r4 = r4 - r2
            int r1 = (int) r4
            r0.a = r1
            org.chromium.net.NetworkChangeNotifier.init()
            org.chromium.net.NetworkChangeNotifier r0 = org.chromium.net.NetworkChangeNotifier.a
            kld r1 = new kld
            r1.<init>()
            r2 = 1
            r0.g(r2, r1)
            android.os.ConditionVariable r0 = org.chromium.net.impl.CronetLibraryLoader.b
            r0.block()
            J.N.MROCxiBo()
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.kmk.run():void");
    }
}
