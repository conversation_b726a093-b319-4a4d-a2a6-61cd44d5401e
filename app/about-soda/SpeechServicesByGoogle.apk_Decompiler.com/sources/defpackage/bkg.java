package defpackage;

import android.view.View;
import com.android.car.ui.baselayout.Insets;

/* renamed from: bkg  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bkg implements View.OnLayoutChangeListener {
    public final /* synthetic */ bkh a;

    public /* synthetic */ bkg(bkh bkh) {
        this.a = bkh;
    }

    public final void onLayoutChange(View view, int i, int i2, int i3, int i4, int i5, int i6, int i7, int i8) {
        if (i != i5 || i2 != i6 || i3 != i7 || i4 != i8) {
            bkh bkh = this.a;
            int d = bkh.d(bkh.b) - bkh.d(bkh.a);
            View view2 = bkh.b;
            int max = Math.max(0, d);
            int b = bkh.b(view2) - bkh.b(bkh.a);
            View view3 = bkh.a;
            int max2 = Math.max(0, b);
            int c = bkh.c(view3) - bkh.c(bkh.b);
            View view4 = bkh.a;
            int max3 = Math.max(0, c);
            int a2 = bkh.a(view4) - bkh.a(bkh.b);
            View view5 = bkh.e;
            int max4 = Math.max(0, a2);
            if (view5 != null) {
                max += Math.max(0, bkh.a(view5) - bkh.d(bkh.b));
            }
            View view6 = bkh.f;
            if (view6 != null) {
                max4 += Math.max(0, bkh.a(bkh.b) - bkh.d(view6));
            }
            View view7 = bkh.c;
            if (view7 != null) {
                max2 += Math.max(0, bkh.c(view7) - bkh.b(bkh.b));
            }
            View view8 = bkh.d;
            if (view8 != null) {
                max3 += Math.max(0, bkh.c(bkh.b) - bkh.b(view8));
            }
            Insets insets = new Insets(max2, max, max3, max4);
            if (!insets.equals(bkh.h)) {
                bkh.h = insets;
                bjw bjw = bkh.g;
                if (bjw != null) {
                    bjw.a(insets);
                } else {
                    bkh.a.setPadding(insets.getLeft(), insets.getTop(), insets.getRight(), insets.getBottom());
                }
            }
        }
    }
}
