package defpackage;

import android.content.Context;
import android.content.Intent;
import android.os.PowerManager;
import androidx.wear.ambient.AmbientLifecycleObserverKt;
import androidx.work.impl.WorkDatabase;
import androidx.work.impl.foreground.SystemForegroundService;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/* renamed from: bcp  reason: default package */
/* compiled from: PG */
public final class bcp implements bga {
    public static final String a = bbk.b("Processor");
    public PowerManager.WakeLock b = null;
    public final Context c;
    public final WorkDatabase d;
    public final Map e = new HashMap();
    public final Map f = new HashMap();
    public final Map g = new HashMap();
    public final Set h = new HashSet();
    public final List i = new ArrayList();
    public final Object j = new Object();
    private final bam k;
    private final cyw l;

    public bcp(Context context, bam bam, cyw cyw, WorkDatabase workDatabase) {
        this.c = context;
        this.k = bam;
        this.l = cyw;
        this.d = workDatabase;
    }

    public static void f(eez eez, int i2) {
        if (eez != null) {
            ((jsg) eez.i).r(new bdo(i2));
            bbk.a();
            return;
        }
        bbk.a();
    }

    /* JADX WARNING: type inference failed for: r3v2, types: [java.util.concurrent.Executor, java.lang.Object] */
    private final void h(bgt bgt) {
        this.l.c.execute(new aku((Object) this, (Object) bgt, 5));
    }

    public final void a(bce bce) {
        synchronized (this.j) {
            this.i.add(bce);
        }
    }

    public final void b(bce bce) {
        synchronized (this.j) {
            this.i.remove(bce);
        }
    }

    public final boolean c(String str) {
        boolean z;
        synchronized (this.j) {
            if (e(str) != null) {
                z = true;
            } else {
                z = false;
            }
        }
        return z;
    }

    public final eez d(String str) {
        boolean z;
        eez eez = (eez) this.e.remove(str);
        if (eez != null) {
            z = true;
        } else {
            z = false;
        }
        if (!z) {
            eez = (eez) this.f.remove(str);
        }
        this.g.remove(str);
        if (z) {
            synchronized (this.j) {
                if (this.e.isEmpty()) {
                    Intent intent = new Intent(this.c, SystemForegroundService.class);
                    intent.setAction("ACTION_STOP_FOREGROUND");
                    try {
                        this.c.startService(intent);
                    } catch (Throwable th) {
                        bbk.a().d(a, "Unable to stop foreground service", th);
                    }
                    PowerManager.WakeLock wakeLock = this.b;
                    if (wakeLock != null) {
                        wakeLock.release();
                        this.b = null;
                    }
                }
            }
        }
        return eez;
    }

    public final eez e(String str) {
        eez eez = (eez) this.e.get(str);
        if (eez == null) {
            return (eez) this.f.get(str);
        }
        return eez;
    }

    /* JADX WARNING: type inference failed for: r1v12, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final boolean g(byw byw) {
        byw byw2 = byw;
        ArrayList arrayList = new ArrayList();
        Object obj = byw2.a;
        bgt bgt = (bgt) obj;
        String str = bgt.a;
        bhe bhe = (bhe) this.d.e(new cmj((Object) this, (Object) arrayList, (Object) str, 1));
        if (bhe == null) {
            bbk a2 = bbk.a();
            String str2 = a;
            Objects.toString(obj);
            a2.f(str2, "Didn't find WorkSpec for id ".concat(obj.toString()));
            h(bgt);
            return false;
        }
        synchronized (this.j) {
            if (c(str)) {
                Set set = (Set) this.g.get(str);
                if (((bgt) ((byw) set.iterator().next()).a).b == ((bgt) obj).b) {
                    set.add(byw2);
                    bbk.a();
                    Objects.toString(obj);
                } else {
                    h((bgt) obj);
                }
            } else if (bhe.u != ((bgt) obj).b) {
                h((bgt) obj);
            } else {
                eez eez = new eez(new fpi(this.c, this.k, this.l, (bga) this, this.d, bhe, (List) arrayList));
                hme b2 = AmbientLifecycleObserverKt.b(((jlm) ((cyw) eez.l).d).plus(new jsb()), new bas(eez, (jlr) null, 4, (byte[]) null));
                b2.c(new ai((Object) this, (Object) b2, (Object) eez, 7, (char[]) null), this.l.c);
                this.f.put(str, eez);
                HashSet hashSet = new HashSet();
                hashSet.add(byw2);
                this.g.put(str, hashSet);
                bbk.a();
                getClass().getSimpleName();
                Objects.toString(obj);
                return true;
            }
            return false;
        }
    }
}
