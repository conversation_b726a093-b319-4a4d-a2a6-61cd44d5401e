package defpackage;

import android.app.job.JobInfo;
import android.app.job.JobScheduler;
import android.content.Context;
import android.os.Build;
import java.util.List;

/* renamed from: ben  reason: default package */
/* compiled from: PG */
public final class ben {
    private static final String a = bbk.b("SystemJobScheduler");

    public static final JobScheduler a(Context context) {
        jnu.e(context, "<this>");
        Object systemService = context.getSystemService("jobscheduler");
        jnu.c(systemService, "null cannot be cast to non-null type android.app.job.JobScheduler");
        JobScheduler jobScheduler = (JobScheduler) systemService;
        if (Build.VERSION.SDK_INT < 34) {
            return jobScheduler;
        }
        jnu.e(jobScheduler, "jobScheduler");
        JobScheduler m = jobScheduler.forNamespace("androidx.work.systemjobscheduler");
        jnu.d(m, "jobScheduler.forNamespace(WORKMANAGER_NAMESPACE)");
        return m;
    }

    public static final List b(JobScheduler jobScheduler) {
        jnu.e(jobScheduler, "<this>");
        try {
            jnu.e(jobScheduler, "jobScheduler");
            List<JobInfo> allPendingJobs = jobScheduler.getAllPendingJobs();
            jnu.d(allPendingJobs, "jobScheduler.allPendingJobs");
            return allPendingJobs;
        } catch (Throwable th) {
            bbk.a().d(a, "getAllPendingJobs() is not reliable on this device.", th);
            return null;
        }
    }
}
