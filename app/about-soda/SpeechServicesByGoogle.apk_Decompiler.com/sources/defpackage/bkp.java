package defpackage;

/* renamed from: bkp  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bkp implements bkz {
    public final /* synthetic */ bkq a;
    public final /* synthetic */ String b;

    public /* synthetic */ bkp(bkq bkq, String str) {
        this.a = bkq;
        this.b = str;
    }

    public final void a(bla bla, boolean z) {
        bkq bkq = this.a;
        String str = this.b;
        if (z) {
            bkq.a.add(str);
        } else {
            bkq.a.remove(str);
        }
        if (bkq.b) {
            bkq.c();
        }
    }
}
