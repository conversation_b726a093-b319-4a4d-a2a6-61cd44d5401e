package defpackage;

/* renamed from: bgv  reason: default package */
/* compiled from: PG */
final class bgv extends aub {
    public bgv(aus aus) {
        super(aus);
    }

    /* access modifiers changed from: protected */
    public final String a() {
        return "INSERT OR IGNORE INTO `WorkName` (`name`,`work_spec_id`) VALUES (?,?)";
    }

    /* access modifiers changed from: protected */
    public final /* bridge */ /* synthetic */ void c(axc axc, Object obj) {
        bvj bvj = (bvj) obj;
        axc.g(1, (String) bvj.b);
        axc.g(2, (String) bvj.a);
    }
}
