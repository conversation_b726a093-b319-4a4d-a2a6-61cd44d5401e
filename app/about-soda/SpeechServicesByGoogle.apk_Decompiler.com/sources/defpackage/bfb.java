package defpackage;

/* renamed from: bfb  reason: default package */
/* compiled from: PG */
public final class bfb extends jmi implements jne {
    int a;
    final /* synthetic */ Object b;
    final /* synthetic */ Object c;
    final /* synthetic */ Object d;
    private final /* synthetic */ int e;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bfb(byw byw, bhe bhe, bey bey, jlr jlr, int i) {
        super(2, jlr);
        this.e = i;
        this.b = byw;
        this.c = bhe;
        this.d = bey;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        if (this.e != 0) {
            return ((bfb) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
        return ((bfb) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX WARNING: type inference failed for: r3v0, types: [bey, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r6v0, types: [bba, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v7, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: Code restructure failed: missing block: B:13:0x0059, code lost:
        if (r12 == r0) goto L_0x0072;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:4:0x000e, code lost:
        if (r2 != 1) goto L_0x0073;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r12) {
        /*
            r11 = this;
            int r0 = r11.e
            r1 = 1
            if (r0 == 0) goto L_0x0074
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r11.a
            defpackage.jji.c(r12)
            if (r2 == 0) goto L_0x0011
            if (r2 == r1) goto L_0x005c
            goto L_0x0073
        L_0x0011:
            java.lang.Object r12 = r11.c
            java.lang.Object r2 = r11.b
            java.lang.Object r6 = r11.d
            r11.a = r1
            eez r12 = (defpackage.eez) r12
            java.lang.Object r1 = r12.a
            int r3 = defpackage.bin.a
            r5 = r1
            bhe r5 = (defpackage.bhe) r5
            boolean r1 = r5.r
            if (r1 == 0) goto L_0x0057
            int r1 = android.os.Build.VERSION.SDK_INT
            r3 = 31
            if (r1 < r3) goto L_0x002d
            goto L_0x0057
        L_0x002d:
            java.lang.Object r1 = r12.l
            java.lang.Object r12 = r12.e
            cyw r1 = (defpackage.cyw) r1
            java.lang.Object r1 = r1.c
            java.lang.String r3 = "taskExecutor.mainThreadExecutor"
            defpackage.jnu.d(r1, r3)
            jqp r1 = defpackage.jnu.I(r1)
            biw r10 = new biw
            r7 = r12
            android.content.Context r7 = (android.content.Context) r7
            r4 = r2
            bbj r4 = (defpackage.bbj) r4
            r8 = 0
            r9 = 1
            r3 = r10
            r3.<init>((defpackage.bbj) r4, (defpackage.bhe) r5, (defpackage.bba) r6, (android.content.Context) r7, (defpackage.jlr) r8, (int) r9)
            java.lang.Object r12 = defpackage.jqw.o(r1, r10, r11)
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            if (r12 == r1) goto L_0x0059
            jkd r12 = defpackage.jkd.a
            goto L_0x0059
        L_0x0057:
            jkd r12 = defpackage.jkd.a
        L_0x0059:
            if (r12 != r0) goto L_0x005c
            goto L_0x0072
        L_0x005c:
            java.lang.String r12 = defpackage.bdy.a
            defpackage.bbk.a()
            java.lang.Object r12 = r11.b
            bbj r12 = (defpackage.bbj) r12
            hme r1 = r12.b()
            r2 = 2
            r11.a = r2
            java.lang.Object r12 = defpackage.bdy.a(r1, r12, r11)
            if (r12 != r0) goto L_0x0073
        L_0x0072:
            return r0
        L_0x0073:
            return r12
        L_0x0074:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r11.a
            if (r2 == 0) goto L_0x007e
            defpackage.jji.c(r12)
            goto L_0x009d
        L_0x007e:
            defpackage.jji.c(r12)
            java.lang.Object r12 = r11.b
            java.lang.Object r2 = r11.c
            java.lang.Object r3 = r11.d
            bhe r2 = (defpackage.bhe) r2
            byw r12 = (defpackage.byw) r12
            juo r12 = r12.U(r2)
            bfa r4 = new bfa
            r4.<init>(r3, r2)
            r11.a = r1
            java.lang.Object r12 = r12.a(r4, r11)
            if (r12 != r0) goto L_0x009d
            return r0
        L_0x009d:
            jkd r12 = defpackage.jkd.a
            return r12
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bfb.bk(java.lang.Object):java.lang.Object");
    }

    /* JADX WARNING: type inference failed for: r4v0, types: [bey, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v1, types: [bba, java.lang.Object] */
    public final jlr c(Object obj, jlr jlr) {
        if (this.e != 0) {
            Object obj2 = this.c;
            return new bfb((eez) obj2, (bbj) this.b, (bba) this.d, jlr, 1);
        }
        Object obj3 = this.b;
        return new bfb((byw) obj3, (bhe) this.c, (bey) this.d, jlr, 0);
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bfb(eez eez, bbj bbj, bba bba, jlr jlr, int i) {
        super(2, jlr);
        this.e = i;
        this.c = eez;
        this.b = bbj;
        this.d = bba;
    }
}
