package defpackage;

import android.media.AudioRecord;

/* renamed from: ekv  reason: default package */
/* compiled from: PG */
public final class ekv {
    private static final hca a = hca.m("com/google/android/libraries/search/audio/core/timestamp/AudioTimestampAdjuster");
    private final dyt b;
    private final int c;
    private final long d;
    private long e;
    private long f;
    private long g;
    private final dku h;

    public ekv(AudioRecord audioRecord, dyt dyt) {
        jnu.e(audioRecord, "audioRecord");
        jnu.e(dyt, "audioInputParams");
        dku dku = new dku(audioRecord);
        jnu.e(dyt, "audioInputParams");
        this.h = dku;
        this.b = dyt;
        this.c = 1000000000 / dyt.c;
        dys dys = dyt.i;
        long j = (long) (dys == null ? dys.c : dys).b;
        if (j < 50) {
            ((hby) ((hby) evc.a.h().h(hdg.a, "ALT.OptimizationUtils")).j("com/google/android/libraries/search/audio/utils/AudioParamsOptimizationUtils", "getTimestampPollingIntervalMillis", 97, "AudioParamsOptimizationUtils.java")).t("#audio# timestamp polling interval is smaller than min value(%s), using min value", 50);
            j = 50;
        }
        this.d = j;
        this.e = -j;
        this.f = cqx.F();
    }

    private final long b(long j) {
        return j * ((long) this.c);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:13:0x0041, code lost:
        if (((android.media.AudioRecord) r12.a).getTimestamp(r5, 1) == 0) goto L_0x0045;
     */
    /* JADX WARNING: Removed duplicated region for block: B:21:0x0074  */
    /* JADX WARNING: Removed duplicated region for block: B:25:0x00b2  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final synchronized defpackage.eku a(int r12) {
        /*
            r11 = this;
            monitor-enter(r11)
            r0 = 0
            if (r12 > 0) goto L_0x000d
            eku r12 = new eku     // Catch:{ all -> 0x00c4 }
            long r1 = r11.f     // Catch:{ all -> 0x00c4 }
            r12.<init>((long) r1, (android.media.AudioTimestamp) r0)     // Catch:{ all -> 0x00c4 }
            monitor-exit(r11)
            return r12
        L_0x000d:
            dyt r1 = r11.b     // Catch:{ all -> 0x00c4 }
            int r2 = r1.e     // Catch:{ all -> 0x00c4 }
            int r2 = defpackage.evc.a(r2)     // Catch:{ all -> 0x00c4 }
            int r1 = r1.d     // Catch:{ all -> 0x00c4 }
            int r1 = java.lang.Integer.bitCount(r1)     // Catch:{ all -> 0x00c4 }
            int r2 = r2 * r1
            int r12 = r12 / r2
            long r1 = (long) r12     // Catch:{ all -> 0x00c4 }
            long r3 = r11.g     // Catch:{ all -> 0x00c4 }
            long r3 = r3 + r1
            long r5 = android.os.SystemClock.elapsedRealtime()     // Catch:{ all -> 0x00c4 }
            long r7 = r11.e     // Catch:{ all -> 0x00c4 }
            long r7 = r5 - r7
            long r9 = r11.d     // Catch:{ all -> 0x00c4 }
            int r12 = (r7 > r9 ? 1 : (r7 == r9 ? 0 : -1))
            r7 = 1
            if (r12 < 0) goto L_0x0072
            r11.e = r5     // Catch:{ all -> 0x00c4 }
            dku r12 = r11.h     // Catch:{ all -> 0x00c4 }
            android.media.AudioTimestamp r5 = new android.media.AudioTimestamp     // Catch:{ all -> 0x00c4 }
            r5.<init>()     // Catch:{ all -> 0x00c4 }
            java.lang.Object r12 = r12.a     // Catch:{ RuntimeException -> 0x0044 }
            android.media.AudioRecord r12 = (android.media.AudioRecord) r12     // Catch:{ RuntimeException -> 0x0044 }
            int r12 = r12.getTimestamp(r5, r7)     // Catch:{ RuntimeException -> 0x0044 }
            if (r12 != 0) goto L_0x0044
            goto L_0x0045
        L_0x0044:
            r5 = r0
        L_0x0045:
            if (r5 != 0) goto L_0x0071
            hca r12 = a     // Catch:{ all -> 0x00c4 }
            hco r12 = r12.h()     // Catch:{ all -> 0x00c4 }
            hcr r5 = defpackage.hdg.a     // Catch:{ all -> 0x00c4 }
            java.lang.String r6 = "ALT.TimestampAdjuster"
            hco r12 = r12.h(r5, r6)     // Catch:{ all -> 0x00c4 }
            hby r12 = (defpackage.hby) r12     // Catch:{ all -> 0x00c4 }
            java.util.concurrent.TimeUnit r5 = java.util.concurrent.TimeUnit.SECONDS     // Catch:{ all -> 0x00c4 }
            hco r12 = r12.g(r7, r5)     // Catch:{ all -> 0x00c4 }
            java.lang.String r5 = "AudioTimestampAdjuster.kt"
            java.lang.String r6 = "com/google/android/libraries/search/audio/core/timestamp/AudioTimestampAdjuster"
            java.lang.String r8 = "getAndroidTimestampOrNull"
            r9 = 103(0x67, float:1.44E-43)
            hco r12 = r12.j(r6, r8, r9, r5)     // Catch:{ all -> 0x00c4 }
            hby r12 = (defpackage.hby) r12     // Catch:{ all -> 0x00c4 }
            java.lang.String r5 = "#audio# unable to retrieve audio timestamp"
            r12.r(r5)     // Catch:{ all -> 0x00c4 }
            goto L_0x0072
        L_0x0071:
            r0 = r5
        L_0x0072:
            if (r0 == 0) goto L_0x00b2
            long r1 = r0.framePosition     // Catch:{ all -> 0x00c4 }
            int r12 = (r1 > r3 ? 1 : (r1 == r3 ? 0 : -1))
            if (r12 == 0) goto L_0x00a7
            hca r12 = a     // Catch:{ all -> 0x00c4 }
            hco r12 = r12.c()     // Catch:{ all -> 0x00c4 }
            hcr r1 = defpackage.hdg.a     // Catch:{ all -> 0x00c4 }
            java.lang.String r2 = "ALT.TimestampAdjuster"
            hco r12 = r12.h(r1, r2)     // Catch:{ all -> 0x00c4 }
            hby r12 = (defpackage.hby) r12     // Catch:{ all -> 0x00c4 }
            java.util.concurrent.TimeUnit r1 = java.util.concurrent.TimeUnit.SECONDS     // Catch:{ all -> 0x00c4 }
            hco r12 = r12.g(r7, r1)     // Catch:{ all -> 0x00c4 }
            java.lang.String r1 = "AudioTimestampAdjuster.kt"
            java.lang.String r2 = "com/google/android/libraries/search/audio/core/timestamp/AudioTimestampAdjuster"
            java.lang.String r5 = "getTimestampElapsedNanos"
            r6 = 79
            hco r12 = r12.j(r2, r5, r6, r1)     // Catch:{ all -> 0x00c4 }
            r5 = r12
            hby r5 = (defpackage.hby) r5     // Catch:{ all -> 0x00c4 }
            long r7 = r0.framePosition     // Catch:{ all -> 0x00c4 }
            java.lang.String r6 = "#audio# timestamps: received(frame@%d) vs expected(frame@%d)"
            r9 = r3
            r5.y(r6, r7, r9)     // Catch:{ all -> 0x00c4 }
        L_0x00a7:
            long r1 = r0.framePosition     // Catch:{ all -> 0x00c4 }
            long r5 = r0.nanoTime     // Catch:{ all -> 0x00c4 }
            long r1 = r3 - r1
            long r1 = r11.b(r1)     // Catch:{ all -> 0x00c4 }
            goto L_0x00b8
        L_0x00b2:
            long r5 = r11.f     // Catch:{ all -> 0x00c4 }
            long r1 = r11.b(r1)     // Catch:{ all -> 0x00c4 }
        L_0x00b8:
            long r5 = r5 + r1
            r11.f = r5     // Catch:{ all -> 0x00c4 }
            r11.g = r3     // Catch:{ all -> 0x00c4 }
            eku r12 = new eku     // Catch:{ all -> 0x00c4 }
            r12.<init>((long) r5, (android.media.AudioTimestamp) r0)     // Catch:{ all -> 0x00c4 }
            monitor-exit(r11)
            return r12
        L_0x00c4:
            r12 = move-exception
            monitor-exit(r11)     // Catch:{ all -> 0x00c4 }
            throw r12
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.ekv.a(int):eku");
    }
}
