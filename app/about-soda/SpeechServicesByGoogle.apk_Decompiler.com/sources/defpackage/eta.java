package defpackage;

/* renamed from: eta  reason: default package */
/* compiled from: PG */
public final class eta extends htq implements hvb {
    public static final eta d;
    private static volatile hvh e;
    public int a;
    public ebp b;
    public ebr c;

    static {
        eta eta = new eta();
        d = eta;
        htq.z(eta.class, eta);
    }

    private eta() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(d, "\u0004\u0002\u0000\u0001\u0001\u0002\u0002\u0000\u0000\u0000\u0001ဉ\u0000\u0002ဉ\u0001", new Object[]{"a", "b", "c"});
        } else if (i2 == 3) {
            return new eta();
        } else {
            if (i2 == 4) {
                return new htk((htq) d);
            }
            if (i2 == 5) {
                return d;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = e;
            if (hvh == null) {
                synchronized (eta.class) {
                    hvh = e;
                    if (hvh == null) {
                        hvh = new htl(d);
                        e = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
