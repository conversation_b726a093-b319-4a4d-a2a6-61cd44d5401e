package defpackage;

/* renamed from: brk  reason: default package */
/* compiled from: PG */
public final class brk implements aer {
    public final /* synthetic */ brm a;
    private final po b;

    public brk(brm brm, po poVar) {
        this.a = brm;
        this.b = poVar;
    }

    public final void a(int i) {
        if (i == 1) {
            ((hby) ((hby) brm.a.c()).j("com/google/android/apps/speech/tts/googletts/audio/ExoPlayerDecoder$PlayerListener", "onPlaybackStateChanged", 397, "ExoPlayerDecoder.java")).r("ExoPlayer state changed to IDLE");
            this.b.c((Object) null);
            brm brm = this.a;
            brm.d.post(new bpu(this, 6));
        }
    }

    public final void b(aeo aeo) {
        String str;
        iff iff;
        hby hby = (hby) ((hby) ((hby) brm.a.h()).i(aeo)).j("com/google/android/apps/speech/tts/googletts/audio/ExoPlayerDecoder$PlayerListener", "onPlayerError", 407, "ExoPlayerDecoder.java");
        int i = aeo.a;
        switch (i) {
            case 1000:
                str = "ERROR_CODE_UNSPECIFIED";
                break;
            case 1001:
                str = "ERROR_CODE_REMOTE_ERROR";
                break;
            case 1002:
                str = "ERROR_CODE_BEHIND_LIVE_WINDOW";
                break;
            case 1003:
                str = "ERROR_CODE_TIMEOUT";
                break;
            case 1004:
                str = "ERROR_CODE_FAILED_RUNTIME_CHECK";
                break;
            default:
                switch (i) {
                    case 2000:
                        str = "ERROR_CODE_IO_UNSPECIFIED";
                        break;
                    case 2001:
                        str = "ERROR_CODE_IO_NETWORK_CONNECTION_FAILED";
                        break;
                    case 2002:
                        str = "ERROR_CODE_IO_NETWORK_CONNECTION_TIMEOUT";
                        break;
                    case 2003:
                        str = "ERROR_CODE_IO_INVALID_HTTP_CONTENT_TYPE";
                        break;
                    case 2004:
                        str = "ERROR_CODE_IO_BAD_HTTP_STATUS";
                        break;
                    case 2005:
                        str = "ERROR_CODE_IO_FILE_NOT_FOUND";
                        break;
                    case 2006:
                        str = "ERROR_CODE_IO_NO_PERMISSION";
                        break;
                    case 2007:
                        str = "ERROR_CODE_IO_CLEARTEXT_NOT_PERMITTED";
                        break;
                    case 2008:
                        str = "ERROR_CODE_IO_READ_POSITION_OUT_OF_RANGE";
                        break;
                    default:
                        switch (i) {
                            case 3001:
                                str = "ERROR_CODE_PARSING_CONTAINER_MALFORMED";
                                break;
                            case 3002:
                                str = "ERROR_CODE_PARSING_MANIFEST_MALFORMED";
                                break;
                            case 3003:
                                str = "ERROR_CODE_PARSING_CONTAINER_UNSUPPORTED";
                                break;
                            case 3004:
                                str = "ERROR_CODE_PARSING_MANIFEST_UNSUPPORTED";
                                break;
                            default:
                                switch (i) {
                                    case 4001:
                                        str = "ERROR_CODE_DECODER_INIT_FAILED";
                                        break;
                                    case 4002:
                                        str = "ERROR_CODE_DECODER_QUERY_FAILED";
                                        break;
                                    case 4003:
                                        str = "ERROR_CODE_DECODING_FAILED";
                                        break;
                                    case 4004:
                                        str = "ERROR_CODE_DECODING_FORMAT_EXCEEDS_CAPABILITIES";
                                        break;
                                    case 4005:
                                        str = "ERROR_CODE_DECODING_FORMAT_UNSUPPORTED";
                                        break;
                                    case 4006:
                                        str = "ERROR_CODE_DECODING_RESOURCES_RECLAIMED";
                                        break;
                                    default:
                                        switch (i) {
                                            case 5001:
                                                str = "ERROR_CODE_AUDIO_TRACK_INIT_FAILED";
                                                break;
                                            case 5002:
                                                str = "ERROR_CODE_AUDIO_TRACK_WRITE_FAILED";
                                                break;
                                            case 5003:
                                                str = "ERROR_CODE_AUDIO_TRACK_OFFLOAD_WRITE_FAILED";
                                                break;
                                            case 5004:
                                                str = "ERROR_CODE_AUDIO_TRACK_OFFLOAD_INIT_FAILED";
                                                break;
                                            default:
                                                switch (i) {
                                                    case 6000:
                                                        str = "ERROR_CODE_DRM_UNSPECIFIED";
                                                        break;
                                                    case 6001:
                                                        str = "ERROR_CODE_DRM_SCHEME_UNSUPPORTED";
                                                        break;
                                                    case 6002:
                                                        str = "ERROR_CODE_DRM_PROVISIONING_FAILED";
                                                        break;
                                                    case 6003:
                                                        str = "ERROR_CODE_DRM_CONTENT_ERROR";
                                                        break;
                                                    case 6004:
                                                        str = "ERROR_CODE_DRM_LICENSE_ACQUISITION_FAILED";
                                                        break;
                                                    case 6005:
                                                        str = "ERROR_CODE_DRM_DISALLOWED_OPERATION";
                                                        break;
                                                    case 6006:
                                                        str = "ERROR_CODE_DRM_SYSTEM_ERROR";
                                                        break;
                                                    default:
                                                        str = "invalid error code";
                                                        break;
                                                }
                                        }
                                }
                        }
                }
        }
        hby.u("ExoPlayer encountered error: %s", str);
        brm brm = this.a;
        int i2 = aeo.a;
        if (i2 != 1001) {
            if (i2 != 3003) {
                switch (i2) {
                    case 2001:
                    case 2002:
                    case 2004:
                        break;
                    case 2003:
                    case 2005:
                    case 2006:
                    case 2007:
                        break;
                    default:
                        iff = iff.COMPOSITION_STATUS_ERROR;
                        break;
                }
            }
            iff = iff.COMPOSITION_STATUS_INVALID_ARGUMENT_ERROR;
            brm.b(iff, "player_error");
        }
        iff = iff.COMPOSITION_STATUS_DEADLINE_EXCEEDED_ERROR;
        brm.b(iff, "player_error");
    }

    public final /* synthetic */ void d() {
    }

    public final /* synthetic */ void e() {
    }

    public final /* synthetic */ void f() {
    }

    public final /* synthetic */ void g() {
    }

    public final /* synthetic */ void h() {
    }

    public final /* synthetic */ void i() {
    }

    public final /* synthetic */ void j() {
    }

    public final /* synthetic */ void k() {
    }

    public final /* synthetic */ void l() {
    }

    public final /* synthetic */ void n() {
    }

    public final /* synthetic */ void o() {
    }

    public final /* synthetic */ void p() {
    }

    public final /* synthetic */ void c(aeo aeo) {
    }

    public final /* synthetic */ void m(int i) {
    }
}
