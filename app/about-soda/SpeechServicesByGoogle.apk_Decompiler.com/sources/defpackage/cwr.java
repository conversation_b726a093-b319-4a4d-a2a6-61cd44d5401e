package defpackage;

import android.app.ActivityManager;
import android.util.Log;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;

/* renamed from: cwr  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwr implements gqx {
    private final /* synthetic */ int a;

    public /* synthetic */ cwr(int i) {
        this.a = i;
    }

    public final Object apply(Object obj) {
        int i;
        grh grh;
        switch (this.a) {
            case 0:
                Void voidR = (Void) obj;
                return true;
            case 1:
                ctn ctn = (ctn) obj;
                htk htk = (htk) ctn.C(5);
                htk.x(ctn);
                if (!htk.a.B()) {
                    htk.b = htk.q();
                    return (ctn) htk.r();
                }
                throw new IllegalArgumentException("Default instance must be immutable.");
            case 2:
                HashMap hashMap = new HashMap();
                for (csx i2 : (Iterable) obj) {
                    cyd.g(hashMap, cyd.i(i2));
                }
                return hashMap;
            case 3:
                HashMap hashMap2 = new HashMap();
                for (cxg cxg : (Iterable) obj) {
                    cyc i3 = cyd.i(cxg.b);
                    HashSet hashSet = new HashSet();
                    csx csx = cxg.b;
                    kbi kbi = csx.u;
                    if (kbi == null) {
                        kbi = kbi.c;
                    }
                    if (true != cxg.a.e) {
                        i = 3;
                    } else {
                        i = 4;
                    }
                    Iterator it = kbi.a.iterator();
                    while (true) {
                        if (it.hasNext()) {
                            kbh kbh = (kbh) it.next();
                            int z = a.z(kbh.a);
                            if (z == 0) {
                                z = 1;
                                continue;
                            }
                            if (z == i) {
                                grh = grh.h(Integer.valueOf((int) kbh.b));
                            }
                        } else {
                            grh = gqd.a;
                        }
                    }
                    if (grh.f()) {
                        hashSet.add(grh.b());
                    } else if (Log.isLoggable("MDD", 5)) {
                        Log.w("MDD", "No download stage experiment ids found even though experiment info is set.");
                    }
                    kbi kbi2 = csx.u;
                    if (kbi2 == null) {
                        kbi2 = kbi.c;
                    }
                    if (kbi2.b.size() < 10) {
                        kbi kbi3 = csx.u;
                        if (kbi3 == null) {
                            kbi3 = kbi.c;
                        }
                        hashSet.addAll(kbi3.b);
                    } else {
                        kbi kbi4 = csx.u;
                        if (kbi4 == null) {
                            kbi4 = kbi.c;
                        }
                        cyh.n("Found more experiment ids than mendel experiment ids for logging than allowed. Found %d, limit is %d", Integer.valueOf(kbi4.b.size()), 10);
                    }
                    if (!hashSet.isEmpty()) {
                        cyd.g(hashMap2, i3).addAll(hashSet);
                    }
                }
                return hashMap2;
            case 4:
                hiu hiu = (hiu) obj;
                hii[] hiiArr = new hii[1];
                htk l = hii.u.l();
                if (!l.b.B()) {
                    l.u();
                }
                hii hii = (hii) l.b;
                hiu.getClass();
                hii.i = hiu;
                hii.b |= 8192;
                hiiArr[0] = (hii) l.r();
                return Arrays.asList(hiiArr);
            case 5:
                hir hir = (hir) obj;
                hii[] hiiArr2 = new hii[1];
                htk l2 = hii.u.l();
                if (!l2.b.B()) {
                    l2.u();
                }
                hii hii2 = (hii) l2.b;
                hir.getClass();
                hii2.k = hir;
                hii2.b |= 65536;
                hiiArr2[0] = (hii) l2.r();
                return Arrays.asList(hiiArr2);
            case 6:
                ArrayList arrayList = new ArrayList();
                for (cye cye : (List) obj) {
                    htk l3 = hii.u.l();
                    hin hin = cye.a;
                    if (!l3.b.B()) {
                        l3.u();
                    }
                    htq htq = l3.b;
                    hii hii3 = (hii) htq;
                    hii3.g = hin;
                    hii3.a |= Integer.MIN_VALUE;
                    hig hig = cye.b;
                    if (!htq.B()) {
                        l3.u();
                    }
                    hii hii4 = (hii) l3.b;
                    hii4.d = hig;
                    hii4.a |= 256;
                    arrayList.add((hii) l3.r());
                }
                return arrayList;
            case 7:
                ctk ctk = ((cti) obj).e;
                if (ctk == null) {
                    return ctk.d;
                }
                return ctk;
            case 8:
                Void voidR2 = (Void) obj;
                return new bbi();
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return ((deh) obj).h(true, false);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                ded ded = (ded) obj;
                if (ded != null) {
                    return ded.o().f(false);
                }
                return "null";
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                gri gri = (gri) obj;
                fvf.aP(gri);
                return gri.a;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                int i4 = dds.a;
                if (obj == null) {
                    return "";
                }
                return obj.toString();
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return ((djj) obj).a;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return grh.g(((ActivityManager.RunningAppProcessInfo) obj).importanceReasonComponent);
            case 15:
                kac kac = (kac) obj;
                htk l4 = kbg.d.l();
                if (!l4.b.B()) {
                    l4.u();
                }
                kbg kbg = (kbg) l4.b;
                kac.getClass();
                kbg.b = kac;
                kbg.a |= 1;
                htk l5 = kbf.e.l();
                if (!l5.b.B()) {
                    l5.u();
                }
                kbf kbf = (kbf) l5.b;
                kbf.a |= 2;
                kbf.d = true;
                kbf kbf2 = (kbf) l5.r();
                if (!l4.b.B()) {
                    l4.u();
                }
                kbg kbg2 = (kbg) l4.b;
                kbf2.getClass();
                kbg2.c = kbf2;
                kbg2.a |= 2;
                return (kbg) l4.r();
            case 16:
                ((hby) ((hby) ((hby) djs.a.h()).i((RuntimeException) obj)).j("com/google/android/libraries/performance/primes/metrics/memory/MemoryMetricServiceImpl", "record", 422, "MemoryMetricServiceImpl.java")).r("Metric extension provider failed.");
                return null;
            case 17:
                return Integer.valueOf(Integer.parseInt((String) obj));
            case 18:
                dly dly = (dly) obj;
                return false;
            case 19:
                Log.e("CheckboxChecker", "fetching usage reporting opt-in failed", (Throwable) obj);
                return true;
            default:
                return hnb.a((String) obj);
        }
    }
}
