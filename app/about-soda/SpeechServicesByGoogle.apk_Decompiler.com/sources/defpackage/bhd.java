package defpackage;

import java.util.List;

/* renamed from: bhd  reason: default package */
/* compiled from: PG */
public final class bhd {
    public final String a;
    public final bbx b;
    public final bat c;
    public final long d;
    public final long e;
    public final long f;
    public final baq g;
    public final int h;
    public final bak i;
    public final long j;
    public final long k;
    public final int l;
    public final int m;
    public final long n;
    public final int o;
    public final List p;
    public final List q;

    public bhd(String str, bbx bbx, bat bat, long j2, long j3, long j4, baq baq, int i2, bak bak, long j5, long j6, int i3, int i4, long j7, int i5, List list, List list2) {
        bak bak2 = bak;
        List list3 = list;
        List list4 = list2;
        jnu.e(str, "id");
        jnu.e(bbx, "state");
        jnu.e(bat, "output");
        jnu.e(bak2, "backoffPolicy");
        jnu.e(list3, "tags");
        jnu.e(list4, "progress");
        this.a = str;
        this.b = bbx;
        this.c = bat;
        this.d = j2;
        this.e = j3;
        this.f = j4;
        this.g = baq;
        this.h = i2;
        this.i = bak2;
        this.j = j5;
        this.k = j6;
        this.l = i3;
        this.m = i4;
        this.n = j7;
        this.o = i5;
        this.p = list3;
        this.q = list4;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof bhd)) {
            return false;
        }
        bhd bhd = (bhd) obj;
        if (jnu.i(this.a, bhd.a) && this.b == bhd.b && jnu.i(this.c, bhd.c) && this.d == bhd.d && this.e == bhd.e && this.f == bhd.f && jnu.i(this.g, bhd.g) && this.h == bhd.h && this.i == bhd.i && this.j == bhd.j && this.k == bhd.k && this.l == bhd.l && this.m == bhd.m && this.n == bhd.n && this.o == bhd.o && jnu.i(this.p, bhd.p) && jnu.i(this.q, bhd.q)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int hashCode = (((this.a.hashCode() * 31) + this.b.hashCode()) * 31) + this.c.hashCode();
        baq baq = this.g;
        long j2 = this.f;
        int v = (((((((((((hashCode * 31) + xm.v(this.d)) * 31) + xm.v(this.e)) * 31) + xm.v(j2)) * 31) + baq.hashCode()) * 31) + this.h) * 31) + this.i.hashCode();
        List list = this.p;
        long j3 = this.n;
        return (((((((((((((((v * 31) + xm.v(this.j)) * 31) + xm.v(this.k)) * 31) + this.l) * 31) + this.m) * 31) + xm.v(j3)) * 31) + this.o) * 31) + list.hashCode()) * 31) + this.q.hashCode();
    }

    public final String toString() {
        return "WorkInfoPojo(id=" + this.a + ", state=" + this.b + ", output=" + this.c + ", initialDelay=" + this.d + ", intervalDuration=" + this.e + ", flexDuration=" + this.f + ", constraints=" + this.g + ", runAttemptCount=" + this.h + ", backoffPolicy=" + this.i + ", backoffDelayDuration=" + this.j + ", lastEnqueueTime=" + this.k + ", periodCount=" + this.l + ", generation=" + this.m + ", nextScheduleTimeOverride=" + this.n + ", stopReason=" + this.o + ", tags=" + this.p + ", progress=" + this.q + ')';
    }
}
