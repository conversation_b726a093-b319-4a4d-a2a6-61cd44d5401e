package defpackage;

import android.content.Context;
import java.io.File;
import java.util.concurrent.atomic.AtomicBoolean;

/* renamed from: cpw  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cpw implements hkn {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ cpw(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [java.lang.Object, java.lang.Runnable] */
    /* JADX WARNING: type inference failed for: r0v23, types: [java.lang.Object, hkn] */
    /* JADX WARNING: type inference failed for: r0v26, types: [java.lang.Object, hkn] */
    /* JADX WARNING: type inference failed for: r0v29, types: [java.lang.Object, hkn] */
    /* JADX WARNING: type inference failed for: r1v20, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v21, types: [gsb, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v4, types: [gsb, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v19, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v49, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v32, types: [enl, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v62, types: [enl, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v64, types: [enl, java.lang.Object] */
    public final hme a() {
        String str;
        switch (this.b) {
            case 0:
                ? r0 = this.a;
                jnu.e(r0, "$task");
                r0.run();
                return hma.a;
            case 1:
                Object obj = this.a;
                return hke.g(hly.q(hfc.P(gof.c(new bta((btf) obj)), btf.b)), new bpr(obj, 3), btf.b);
            case 2:
                cyh.c("%s Running maintenance", "MDDManager");
                Object obj2 = this.a;
                cwm cwm = (cwm) obj2;
                return czw.e(cwm.f()).g(new cwi(obj2, 5), hld.a).g(new cwj(cwm), cwm.h);
            case 3:
                bzl bzl = (bzl) ((cuh) this.a).i.b();
                Context context = (Context) bzl.a;
                if (context.getPackageName().equals("com.google.android.gms")) {
                    str = "com.google.android.gms.icing.mdd";
                } else {
                    str = "com.google.android.gms.icing.mdd#".concat(String.valueOf(context.getPackageName()));
                }
                return cqx.a(new cjt((Context) bzl.a).k(str));
            case 4:
                return ((dbw) this.a).j();
            case 5:
                return ftd.K(this.a.a(), new cwr(6), hld.a);
            case 6:
                return ftd.K(this.a.a(), new cwr(5), hld.a);
            case 7:
                return ftd.K(this.a.a(), new cwr(4), hld.a);
            case 8:
                return ftd.K(this.a, new cyl(), hld.a);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fpi fpi = (fpi) this.a;
                grh grh = (grh) fpi.f.a();
                boolean f = grh.f();
                Object a2 = fpi.d.a();
                if (f) {
                    grh grh2 = (grh) a2;
                    if (grh2.f()) {
                        dmp dmp = new dmp((File) grh.b(), (String) grh2.b());
                        int a3 = dmp.a();
                        dmp.b().delete();
                        dmp.b = 0;
                        dmp.c = true;
                        if (a3 < ((dmq) fpi.e.b()).b) {
                            return hma.a;
                        }
                        Object obj3 = fpi.b;
                        ffg a4 = dma.a();
                        htk l = kbc.y.l();
                        htk l2 = kba.d.l();
                        if (!l2.b.B()) {
                            l2.u();
                        }
                        kba kba = (kba) l2.b;
                        kba.b = 6;
                        kba.a |= 1;
                        if (!l.b.B()) {
                            l.u();
                        }
                        kbc kbc = (kbc) l.b;
                        kba kba2 = (kba) l2.r();
                        kba2.getClass();
                        kbc.u = kba2;
                        kbc.a |= 8388608;
                        a4.i((kbc) l.r());
                        return ((cxm) obj3).b(a4.e());
                    }
                }
                return hma.a;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                dna dna = (dna) this.a;
                if (!dna.m() || dna.c.getAndSet(true)) {
                    return hma.a;
                }
                return dna.o(6, (dmo) dna.b.b(), ((dmq) dna.e.b()).e);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                dna dna2 = (dna) this.a;
                if (dna2.m()) {
                    fpi fpi2 = dna2.g;
                    if (!((AtomicBoolean) fpi2.a).getAndSet(false)) {
                        hme hme = hma.a;
                    } else {
                        hfc.P(new cpw(fpi2, 9), fpi2.c);
                    }
                }
                return hma.a;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                dox dox = (dox) ((dpc) this.a).a.b();
                return hma.a;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return ((dpt) this.a).c();
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return this.a.i(eal.OPENED_SEAMLESSLY, new enj(0));
            case 15:
                ? r02 = this.a;
                jnu.e(r02, "$source");
                return r02.k(new enj(0));
            case 16:
                return this.a.k(new enj(0));
            case 17:
                ezz ezz = (ezz) this.a;
                if (!ezz.i) {
                    ezz.i = true;
                    exo exo = ezz.b;
                    ((hby) ((hby) ezz.a.f().h(hdg.a, "ModelDownloadCallback")).j("com/google/android/libraries/speech/modelmanager/languagepack/ModelDownloadCallback", "pausedForConnectivity", 160, "ModelDownloadCallback.java")).A("Language pack %s[%s] download MDD paused for connectivity", exo.a, exo.b);
                    ezz.e("onSchedule", 4);
                    ezz.e.ifPresent(new ezx(2));
                }
                return hma.a;
            case 18:
                ezz ezz2 = (ezz) this.a;
                if (!ezz2.i) {
                    ezz2.i = true;
                    exo exo2 = ezz2.b;
                    ((hby) ((hby) ezz.a.f().h(hdg.a, "ModelDownloadCallback")).j("com/google/android/libraries/speech/modelmanager/languagepack/ModelDownloadCallback", "onComplete", 122, "ModelDownloadCallback.java")).A("Language pack %s[%s] download MDD complete", exo2.a, exo2.b);
                    ezz2.e("onComplete", 2);
                    ezz2.e.ifPresent(new ezx(0));
                }
                return hma.a;
            case 19:
                ezz ezz3 = (ezz) this.a;
                if (!ezz3.i) {
                    ezz3.i = true;
                    long j = ezz3.c;
                    ezz3.e("onSchedule: " + (j / 1000) + " sec timeout.", 1);
                    ezz3.e.ifPresent(new ezx(2));
                }
                return hma.a;
            default:
                return ((fka) this.a).c();
        }
    }
}
