package defpackage;

/* renamed from: csx  reason: default package */
/* compiled from: PG */
public final class csx extends htq implements hvb {
    public static final csx w;
    private static volatile hvh x;
    public int a;
    public csw b;
    public String c = "";
    public String d = "";
    public int e;
    public hse f;
    public hse g;
    public hse h;
    public int i;
    public long j;
    public long k;
    public csz l;
    public boolean m;
    public huf n;
    public int o;
    public huf p;
    public int q;
    public long r;
    public String s;
    public huf t;
    public kbi u;
    public String v;

    static {
        csx csx = new csx();
        w = csx;
        htq.z(csx.class, csx);
    }

    private csx() {
        hvk hvk = hvk.a;
        this.n = hvk;
        this.p = hvk;
        this.s = "";
        this.t = hvk;
        this.v = "";
    }

    /* access modifiers changed from: protected */
    public final Object a(int i2, Object obj) {
        int i3 = i2 - 1;
        if (i3 == 0) {
            return (byte) 1;
        }
        if (i3 == 2) {
            return new hvl(w, "\u0001\u0015\u0000\u0001\u0001年皤\u0015\u0000\u0003\u0000\u0001ဈ\u0001\u0002\u001b\u0003ဂ\t\u0006ဈ\u0002\nင\u0003\u000bဂ\n\f᠌\b\rဉ\u000b\u000eဇ\f\u0010င\r\u0011\u001b\u0013᠌\u000e\u0014ဉ\u0004\u0017ဂ\u000f\u0019\u001a\u001aဈ\u0010\u001bဉ\u0005\u001cဉ\u0011\u001dဉ\u0006Ϩဈ\u0013年皤ဉ\u0000", new Object[]{"a", "c", "n", csv.class, "j", "d", "e", "k", "i", bqk.p, "l", "m", "o", "p", cta.class, "q", dor.b, "f", "r", "t", "s", "g", "u", "h", "v", "b"});
        } else if (i3 == 3) {
            return new csx();
        } else {
            if (i3 == 4) {
                return new htk((htq) w);
            }
            if (i3 == 5) {
                return w;
            }
            if (i3 != 6) {
                return null;
            }
            hvh hvh = x;
            if (hvh == null) {
                synchronized (csx.class) {
                    hvh = x;
                    if (hvh == null) {
                        hvh = new htl(w);
                        x = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
