package defpackage;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.Executor;

/* renamed from: esl  reason: default package */
/* compiled from: PG */
public final class esl {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry");
    public final List b = new ArrayList();
    public final cyw c;
    private final eoz d;
    private final Executor e;
    private final hll f;
    private final boolean g;
    private final eoz h;
    private final dsy i;
    private final cyw j;
    private final eoz k;
    private final byw l;

    public esl(dvx dvx, cyw cyw, eoz eoz, byw byw, dsy dsy, cyw cyw2, eoz eoz2, dsy dsy2, doe doe, Executor executor, hll hll, eoz eoz3, boolean z) {
        jnu.e(dvx, "listeningSessionAssembler");
        jnu.e(eoz, "tokenGenerator");
        jnu.e(doe, "hotwordUriChecker");
        jnu.e(executor, "lightweightExecutor");
        jnu.e(hll, "audioExecutionSequencer");
        this.c = cyw;
        this.h = eoz;
        this.l = byw;
        this.i = dsy;
        this.j = cyw2;
        this.d = eoz2;
        this.e = executor;
        this.f = hll;
        this.k = eoz3;
        this.g = z;
        dsy2.i(new mz(this, 16));
    }

    private static final esj f(dzx dzx) {
        ekg ekg = new ekg(dzx);
        ejn a2 = ejn.a();
        esg esg = new esg((byte[]) null);
        htk l2 = dzn.c.l();
        jnu.d(l2, "newBuilder(...)");
        return new esj(ekg, -1, a2, esg, jnu.e(l2, "builder").j());
    }

    public final synchronized esh a(esg esg, Integer num) {
        Object obj;
        hca hca = a;
        ((hby) hca.e().h(hdg.a, "ALT.AudioRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry", "findAudioRoute$java_com_google_android_libraries_search_audio_routing_registry_audio_routes_registry", 495, "AudioRoutesRegistry.kt")).C("#audio# find audio route(%s) for %s", fbi.y(num), esg.a());
        if (esg.c) {
            ((hby) hca.e().h(hdg.a, "ALT.AudioRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry", "findAudioRoute$java_com_google_android_libraries_search_audio_routing_registry_audio_routes_registry", 499, "AudioRoutesRegistry.kt")).C("#audio# no audio route(%s) for %s, client inactive", fbi.y(num), esg.a());
            return esj.h(f(dzx.DISCONNECT_REASON_INACTIVE_CLIENT), fbi.x(num), esg, (dzn) null, 53);
        }
        Iterator it = this.b.iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            esi esi = (esi) obj;
            int i2 = esg.a;
            int i3 = esi.c.a;
            boolean z = true;
            if (num != null) {
                if (num.intValue() != esi.b) {
                    z = false;
                }
            }
            if (i2 == i3 && z) {
                break;
            }
        }
        esi esi2 = (esi) obj;
        if (esi2 != null) {
            return esi2;
        }
        ((hby) a.e().h(hdg.a, "ALT.AudioRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry", "findAudioRoute$java_com_google_android_libraries_search_audio_routing_registry_audio_routes_registry", 521, "AudioRoutesRegistry.kt")).C("#audio# no audio route(%s) for %s, inactive", fbi.y(num), esg.a());
        return esj.h(f(dzx.DISCONNECT_REASON_AUDIO_ROUTE_LOST), fbi.x(num), esg, (dzn) null, 53);
    }

    /* JADX WARNING: type inference failed for: r7v10, types: [elp, java.lang.Object] */
    public final synchronized void b(esg esg, int i2, dzx dzx) {
        Object obj;
        jnu.e(dzx, "reason");
        esk esk = new esk(i2, dzx, esg);
        if (esg.c) {
            esk.a();
            return;
        }
        Iterator it = this.b.iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            esi esi = (esi) obj;
            if (esi.c.a == esg.a && esi.b == i2) {
                break;
            }
        }
        esi esi2 = (esi) obj;
        if (esi2 == null) {
            esk.a();
            return;
        }
        jji.N(this.b, new eoh(i2, 6));
        String name = dzx.name();
        String r = fbi.r(esi2.d.b);
        ((hby) a.f().h(hdg.a, "ALT.AudioRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry", "markDisconnected", 464, "AudioRoutesRegistry.kt")).G("#audio# disconnecting(%s) audio %s for %s", name, "route(token(" + esi2.b + "), " + r + ")", esi2.c.a());
        eoz eoz = this.d;
        eow L = cqx.L(esi2);
        eam g2 = eki.g(dzx);
        jnu.d(g2, "convertDisconnectReasonToStopListeningReason(...)");
        eoz.i(L, g2);
        cyw cyw = this.c;
        esg esg2 = esi2.c;
        ehg ehg = esg2.b.b;
        if (ehg == null) {
            ehg = ehg.c;
        }
        ? r7 = cyw.a;
        jnu.d(ehg, "getClientInfo(...)");
        r7.o(ehg, Integer.valueOf(esg2.a));
        fyz.c(this.f.b(gof.c(new ejj(esi2, dzx, 4)), this.e), "Disconnecting the audio route failed", new Object[0]);
    }

    public final synchronized void c(esg esg, dzx dzx) {
        jnu.e(dzx, "reason");
        ArrayList<esi> arrayList = new ArrayList<>();
        for (Object next : this.b) {
            if (((esi) next).c.a == esg.a) {
                arrayList.add(next);
            }
        }
        if (arrayList.isEmpty()) {
            ((hby) a.e().h(hdg.a, "ALT.AudioRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry", "disconnectClientAudioRoutes$java_com_google_android_libraries_search_audio_routing_registry_audio_routes_registry", 403, "AudioRoutesRegistry.kt")).C("#audio# no routes left to disconnect(%s) for %s", dzx.name(), esg.a());
            return;
        }
        for (esi esi : arrayList) {
            b(esi.c, esi.b, dzx);
        }
    }

    public final synchronized void d(esg esg) {
        int m = this.h.m();
        ((hby) a.f().h(hdg.a, "ALT.AudioRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry", "initDefaultAudioClientRoute$java_com_google_android_libraries_search_audio_routing_registry_audio_routes_registry", 161, "AudioRoutesRegistry.kt")).x("#audio# initializing the default route(%d) for %s", m, esg.a());
        htk l2 = dzl.d.l();
        jnu.d(l2, "newBuilder(...)");
        dku h2 = jnu.e(l2, "builder");
        htk l3 = ebf.c.l();
        jnu.d(l3, "newBuilder(...)");
        byw B = jnu.e(l3, "builder");
        B.h(esg.a);
        h2.o(B.g());
        htk l4 = ebw.c.l();
        jnu.d(l4, "newBuilder(...)");
        bzl o = jnu.e(l4, "builder");
        o.y(m);
        h2.p(o.x());
        dzl n = h2.n();
        ery ery = new ery(gqd.a);
        erw L = this.l.L(n, ery.a().a);
        ebw ebw = n.c;
        if (ebw == null) {
            ebw = ebw.c;
        }
        int i2 = ebw.b;
        htk l5 = dzn.c.l();
        jnu.d(l5, "newBuilder(...)");
        dku f2 = jnu.e(l5, "builder");
        f2.k(ery.a().b);
        esi esi = new esi(ery, L, i2, esg, f2.j());
        this.c.m(esi);
        this.b.add(esi);
    }

    /* JADX WARNING: type inference failed for: r8v2, types: [elp, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v37, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r8v23, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r8v25, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r2v35, types: [ery] */
    /* JADX WARNING: type inference failed for: r2v36, types: [eru] */
    /* JADX WARNING: type inference failed for: r2v40, types: [eru] */
    /* JADX WARNING: type inference failed for: r2v41, types: [eru] */
    /* JADX WARNING: type inference failed for: r2v42, types: [eru] */
    /* JADX WARNING: type inference failed for: r3v36, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: Code restructure failed: missing block: B:84:0x02fd, code lost:
        r3 = r1.k.y(r2);
        ((defpackage.hby) a.f().h(defpackage.hdg.a, "ALT.AudioRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry", "startAudioRouteConnection", 380, "AudioRoutesRegistry.kt")).G("#audio# starting connection to audio route(token(%d), %s) for %s", java.lang.Integer.valueOf(r5), defpackage.fbi.r(r3.d.b), r26.a());
        r8 = r1.f.b(defpackage.gof.c(new defpackage.ejj(r3, r1, 5, (byte[]) null)), r1.e);
        defpackage.jnu.d(r8, "submitAsync(...)");
        r2 = defpackage.dzl.d.l();
        defpackage.jnu.d(r2, "newBuilder(...)");
        r2 = defpackage.jnu.e(r2, "builder");
        r4 = defpackage.ebf.c.l();
        defpackage.jnu.d(r4, "newBuilder(...)");
        r4 = defpackage.jnu.e(r4, "builder");
        r4.h(r0.a);
        r2.o(r4.g());
        r4 = defpackage.ebw.c.l();
        defpackage.jnu.d(r4, "newBuilder(...)");
        r4 = defpackage.jnu.e(r4, "builder");
        r4.y(r5);
        r2.p(r4.x());
        r2 = new defpackage.esi(r3, r1.l.L(r2.n(), r3.d.a), r5, r26, r27);
        r1.c.o(r8, r2);
        r1.b.add(r2);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:86:0x03b1, code lost:
        return r2;
     */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:44:0x014d  */
    /* JADX WARNING: Removed duplicated region for block: B:45:0x0172  */
    /* JADX WARNING: Removed duplicated region for block: B:47:0x01bb  */
    /* JADX WARNING: Removed duplicated region for block: B:90:0x03b6 A[DONT_GENERATE] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final synchronized defpackage.esh e(defpackage.esg r26, defpackage.dzn r27, defpackage.bzj r28) {
        /*
            r25 = this;
            r1 = r25
            r0 = r26
            r7 = r27
            monitor-enter(r25)
            java.lang.String r2 = "params"
            defpackage.jnu.e(r7, r2)     // Catch:{ all -> 0x03b8 }
            eoz r2 = r1.h     // Catch:{ all -> 0x03b8 }
            int r5 = r2.m()     // Catch:{ all -> 0x03b8 }
            hca r2 = a     // Catch:{ all -> 0x03b8 }
            hco r3 = r2.f()     // Catch:{ all -> 0x03b8 }
            hcr r4 = defpackage.hdg.a     // Catch:{ all -> 0x03b8 }
            java.lang.String r6 = "ALT.AudioRoutesRegy"
            hco r3 = r3.h(r4, r6)     // Catch:{ all -> 0x03b8 }
            java.lang.String r4 = "AudioRoutesRegistry.kt"
            java.lang.String r6 = "com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry"
            java.lang.String r8 = "connectAudioRoute$java_com_google_android_libraries_search_audio_routing_registry_audio_routes_registry"
            r9 = 196(0xc4, float:2.75E-43)
            hco r3 = r3.j(r6, r8, r9, r4)     // Catch:{ all -> 0x03b8 }
            hby r3 = (defpackage.hby) r3     // Catch:{ all -> 0x03b8 }
            java.lang.Integer r10 = java.lang.Integer.valueOf(r5)     // Catch:{ all -> 0x03b8 }
            java.lang.String r4 = defpackage.fbi.z(r27)     // Catch:{ all -> 0x03b8 }
            java.lang.String r6 = r26.a()     // Catch:{ all -> 0x03b8 }
            java.lang.String r8 = "#audio# updating to audio route(token(%d), %s) for %s"
            r3.G(r8, r10, r4, r6)     // Catch:{ all -> 0x03b8 }
            java.lang.String r3 = "params"
            defpackage.jnu.e(r7, r3)     // Catch:{ all -> 0x03b8 }
            dzc r3 = r0.b     // Catch:{ all -> 0x03b8 }
            ehg r3 = r3.b     // Catch:{ all -> 0x03b8 }
            if (r3 != 0) goto L_0x004c
            ehg r3 = defpackage.ehg.c     // Catch:{ all -> 0x03b8 }
        L_0x004c:
            cyw r4 = r1.c     // Catch:{ all -> 0x03b8 }
            java.lang.String r6 = "getClientInfo(...)"
            defpackage.jnu.d(r3, r6)     // Catch:{ all -> 0x03b8 }
            int r6 = r0.a     // Catch:{ all -> 0x03b8 }
            java.lang.Object r8 = r4.a     // Catch:{ all -> 0x03b8 }
            java.lang.Integer r6 = java.lang.Integer.valueOf(r6)     // Catch:{ all -> 0x03b8 }
            r8.m(r3, r6)     // Catch:{ all -> 0x03b8 }
            eav r3 = defpackage.eav.n     // Catch:{ all -> 0x03b8 }
            htk r3 = r3.l()     // Catch:{ all -> 0x03b8 }
            java.lang.String r6 = "newBuilder(...)"
            defpackage.jnu.d(r3, r6)     // Catch:{ all -> 0x03b8 }
            byw r3 = defpackage.jnu.e(r3, "builder")     // Catch:{ all -> 0x03b8 }
            eay r6 = defpackage.eay.AUDIO_REQUEST_UPDATE_AUDIO_ROUTE     // Catch:{ all -> 0x03b8 }
            r3.o(r6)     // Catch:{ all -> 0x03b8 }
            int r6 = r0.a     // Catch:{ all -> 0x03b8 }
            long r8 = (long) r6     // Catch:{ all -> 0x03b8 }
            r3.q(r8)     // Catch:{ all -> 0x03b8 }
            java.lang.String r6 = "value"
            defpackage.jnu.e(r7, r6)     // Catch:{ all -> 0x03b8 }
            java.lang.Object r6 = r3.a     // Catch:{ all -> 0x03b8 }
            r8 = r6
            htk r8 = (defpackage.htk) r8     // Catch:{ all -> 0x03b8 }
            htq r8 = r8.b     // Catch:{ all -> 0x03b8 }
            boolean r8 = r8.B()     // Catch:{ all -> 0x03b8 }
            if (r8 != 0) goto L_0x0090
            r8 = r6
            htk r8 = (defpackage.htk) r8     // Catch:{ all -> 0x03b8 }
            r8.u()     // Catch:{ all -> 0x03b8 }
        L_0x0090:
            htk r6 = (defpackage.htk) r6     // Catch:{ all -> 0x03b8 }
            htq r6 = r6.b     // Catch:{ all -> 0x03b8 }
            eav r6 = (defpackage.eav) r6     // Catch:{ all -> 0x03b8 }
            r27.getClass()     // Catch:{ all -> 0x03b8 }
            r6.c = r7     // Catch:{ all -> 0x03b8 }
            r8 = 204(0xcc, float:2.86E-43)
            r6.b = r8     // Catch:{ all -> 0x03b8 }
            dzc r6 = r0.b     // Catch:{ all -> 0x03b8 }
            ehg r6 = r6.b     // Catch:{ all -> 0x03b8 }
            if (r6 != 0) goto L_0x00a7
            ehg r6 = defpackage.ehg.c     // Catch:{ all -> 0x03b8 }
        L_0x00a7:
            java.lang.Object r4 = r4.b     // Catch:{ all -> 0x03b8 }
            r3.p(r6)     // Catch:{ all -> 0x03b8 }
            long r8 = (long) r5     // Catch:{ all -> 0x03b8 }
            r3.r(r8)     // Catch:{ all -> 0x03b8 }
            eav r3 = r3.l()     // Catch:{ all -> 0x03b8 }
            eug r4 = (defpackage.eug) r4     // Catch:{ all -> 0x03b8 }
            r4.d(r3)     // Catch:{ all -> 0x03b8 }
            dzq r3 = r7.b     // Catch:{ all -> 0x03b8 }
            if (r3 != 0) goto L_0x00bf
            dzq r3 = defpackage.dzq.c     // Catch:{ all -> 0x03b8 }
        L_0x00bf:
            int r3 = r3.a     // Catch:{ all -> 0x03b8 }
            r4 = 5
            r6 = 0
            if (r3 != r4) goto L_0x00da
            r3 = r28
            java.lang.Object r3 = r3.a     // Catch:{ all -> 0x03b8 }
            java.lang.Object r3 = defpackage.jji.u(r3)     // Catch:{ all -> 0x03b8 }
            ejn r3 = (defpackage.ejn) r3     // Catch:{ all -> 0x03b8 }
            if (r3 == 0) goto L_0x00d4
            dzq r3 = r3.b     // Catch:{ all -> 0x03b8 }
            goto L_0x00d5
        L_0x00d4:
            r3 = r6
        L_0x00d5:
            grh r3 = defpackage.grh.g(r3)     // Catch:{ all -> 0x03b8 }
            goto L_0x00e4
        L_0x00da:
            dzq r3 = r7.b     // Catch:{ all -> 0x03b8 }
            if (r3 != 0) goto L_0x00e0
            dzq r3 = defpackage.dzq.c     // Catch:{ all -> 0x03b8 }
        L_0x00e0:
            grh r3 = defpackage.grh.g(r3)     // Catch:{ all -> 0x03b8 }
        L_0x00e4:
            boolean r8 = r0.c     // Catch:{ all -> 0x03b8 }
            r14 = 2
            r15 = 1
            if (r8 == 0) goto L_0x00fd
            esn r8 = new esn     // Catch:{ all -> 0x03b8 }
            eaj r17 = defpackage.eaj.FAILED_OPENING_DUE_TO_INACTIVE_CLIENT     // Catch:{ all -> 0x03b8 }
            eag r18 = defpackage.eag.FAILED_CLOSING_DUE_TO_INACTIVE_CLIENT     // Catch:{ all -> 0x03b8 }
            dzx r19 = defpackage.dzx.DISCONNECT_REASON_INACTIVE_CLIENT     // Catch:{ all -> 0x03b8 }
            eaf r20 = defpackage.eaf.FAILED_ROUTING_DUE_TO_INACTIVE_CLIENT     // Catch:{ all -> 0x03b8 }
            java.lang.String r21 = "the associated audio client is already inactive"
            r16 = r8
            r16.<init>(r17, r18, r19, r20, r21)     // Catch:{ all -> 0x03b8 }
        L_0x00fb:
            r13 = r8
            goto L_0x014b
        L_0x00fd:
            boolean r8 = r3.f()     // Catch:{ all -> 0x03b8 }
            if (r8 != 0) goto L_0x0115
            esn r8 = new esn     // Catch:{ all -> 0x03b8 }
            eaj r17 = defpackage.eaj.FAILED_TO_OPEN_AUDIO_SOURCE_DUE_TO_FAILED_ROUTING     // Catch:{ all -> 0x03b8 }
            eag r18 = defpackage.eag.FAILED_CLOSING_AUDIO_SOURCE_DUE_TO_FAILED_ROUTING     // Catch:{ all -> 0x03b8 }
            dzx r19 = defpackage.dzx.DISCONNECT_REASON_ROUTE_NOT_MAPPED     // Catch:{ all -> 0x03b8 }
            eaf r20 = defpackage.eaf.FAILED_ROUTING_DUE_TO_HANDOVER_ROUTE_NOT_MAPPED     // Catch:{ all -> 0x03b8 }
            java.lang.String r21 = "the target audio route is unknown (handover failed?)"
            r16 = r8
            r16.<init>(r17, r18, r19, r20, r21)     // Catch:{ all -> 0x03b8 }
            goto L_0x00fb
        L_0x0115:
            dzc r8 = r0.b     // Catch:{ all -> 0x03b8 }
            int r8 = r8.c     // Catch:{ all -> 0x03b8 }
            dzb r8 = defpackage.dzb.b(r8)     // Catch:{ all -> 0x03b8 }
            if (r8 != 0) goto L_0x0121
            dzb r8 = defpackage.dzb.DEFAULT     // Catch:{ all -> 0x03b8 }
        L_0x0121:
            dzb r9 = defpackage.dzb.AMBIENT     // Catch:{ all -> 0x03b8 }
            if (r8 != r9) goto L_0x014a
            eoz r8 = r1.d     // Catch:{ all -> 0x03b8 }
            dzb[] r9 = new defpackage.dzb[r14]     // Catch:{ all -> 0x03b8 }
            dzb r11 = defpackage.dzb.DEFAULT     // Catch:{ all -> 0x03b8 }
            r12 = 0
            r9[r12] = r11     // Catch:{ all -> 0x03b8 }
            dzb r11 = defpackage.dzb.CONVERSATIONAL     // Catch:{ all -> 0x03b8 }
            r9[r15] = r11     // Catch:{ all -> 0x03b8 }
            boolean r8 = r8.k(r9)     // Catch:{ all -> 0x03b8 }
            if (r8 == 0) goto L_0x014a
            esn r8 = new esn     // Catch:{ all -> 0x03b8 }
            eaj r17 = defpackage.eaj.FAILED_OPENING_CONCURRENCY_CONFLICT     // Catch:{ all -> 0x03b8 }
            eag r18 = defpackage.eag.FAILED_CLOSING_CONCURRENCY_CONFLICT     // Catch:{ all -> 0x03b8 }
            dzx r19 = defpackage.dzx.DISCONNECT_REASON_CONCURRENCY_CONFLICT     // Catch:{ all -> 0x03b8 }
            eaf r20 = defpackage.eaf.FAILED_ROUTING_DUE_TO_HANDOVER_ROUTE_NOT_MAPPED     // Catch:{ all -> 0x03b8 }
            java.lang.String r21 = "ambient route cannot connect due to a conflict with another active session"
            r16 = r8
            r16.<init>(r17, r18, r19, r20, r21)     // Catch:{ all -> 0x03b8 }
            goto L_0x00fb
        L_0x014a:
            r13 = r6
        L_0x014b:
            if (r13 != 0) goto L_0x0172
            hco r8 = r2.f()     // Catch:{ all -> 0x03b8 }
            hcr r9 = defpackage.hdg.a     // Catch:{ all -> 0x03b8 }
            java.lang.String r10 = "ALT.AudioRoutesRegy"
            hco r8 = r8.h(r9, r10)     // Catch:{ all -> 0x03b8 }
            java.lang.String r9 = "AudioRoutesRegistry.kt"
            java.lang.String r10 = "com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry"
            java.lang.String r11 = "checkAudioRouteUpdateConditionsOrFailedSession"
            r12 = 276(0x114, float:3.87E-43)
            hco r8 = r8.j(r10, r11, r12, r9)     // Catch:{ all -> 0x03b8 }
            hby r8 = (defpackage.hby) r8     // Catch:{ all -> 0x03b8 }
            java.lang.String r9 = defpackage.fbi.z(r27)     // Catch:{ all -> 0x03b8 }
            java.lang.String r10 = "#audio# audio route(token(%d), %s) OK to update"
            r8.x(r10, r5, r9)     // Catch:{ all -> 0x03b8 }
            r8 = r6
            goto L_0x01b9
        L_0x0172:
            hco r8 = r2.h()     // Catch:{ all -> 0x03b8 }
            hcr r9 = defpackage.hdg.a     // Catch:{ all -> 0x03b8 }
            java.lang.String r11 = "ALT.AudioRoutesRegy"
            hco r8 = r8.h(r9, r11)     // Catch:{ all -> 0x03b8 }
            java.lang.String r9 = "AudioRoutesRegistry.kt"
            java.lang.String r11 = "com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry"
            java.lang.String r12 = "checkAudioRouteUpdateConditionsOrFailedSession"
            r14 = 282(0x11a, float:3.95E-43)
            hco r8 = r8.j(r11, r12, r14, r9)     // Catch:{ all -> 0x03b8 }
            hby r8 = (defpackage.hby) r8     // Catch:{ all -> 0x03b8 }
            java.lang.String r11 = defpackage.fbi.z(r27)     // Catch:{ all -> 0x03b8 }
            java.lang.String r12 = r26.a()     // Catch:{ all -> 0x03b8 }
            java.lang.String r14 = r13.e     // Catch:{ all -> 0x03b8 }
            java.lang.String r9 = "#audio# updating to route(token(%d), %s) failed for %s: %s"
            r4 = r13
            r13 = r14
            r8.H(r9, r10, r11, r12, r13)     // Catch:{ all -> 0x03b8 }
            dzx r8 = r4.c     // Catch:{ all -> 0x03b8 }
            esj r8 = f(r8)     // Catch:{ all -> 0x03b8 }
            r9 = 37
            esj r8 = defpackage.esj.h(r8, r5, r0, r7, r9)     // Catch:{ all -> 0x03b8 }
            cyw r9 = r1.c     // Catch:{ all -> 0x03b8 }
            r9.m(r8)     // Catch:{ all -> 0x03b8 }
            cyw r9 = r1.c     // Catch:{ all -> 0x03b8 }
            eaf r4 = r4.d     // Catch:{ all -> 0x03b8 }
            hme r4 = defpackage.eki.k(r4)     // Catch:{ all -> 0x03b8 }
            r9.o(r4, r8)     // Catch:{ all -> 0x03b8 }
        L_0x01b9:
            if (r8 != 0) goto L_0x03b6
            hco r2 = r2.f()     // Catch:{ all -> 0x03b8 }
            hcr r4 = defpackage.hdg.a     // Catch:{ all -> 0x03b8 }
            java.lang.String r8 = "ALT.AudioRoutesRegy"
            hco r2 = r2.h(r4, r8)     // Catch:{ all -> 0x03b8 }
            java.lang.String r4 = "AudioRoutesRegistry.kt"
            java.lang.String r8 = "com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry"
            java.lang.String r9 = "enforceConcurrencyStateOnNewAudioRoute"
            r10 = 303(0x12f, float:4.25E-43)
            hco r2 = r2.j(r8, r9, r10, r4)     // Catch:{ all -> 0x03b8 }
            hby r2 = (defpackage.hby) r2     // Catch:{ all -> 0x03b8 }
            java.lang.String r4 = r26.a()     // Catch:{ all -> 0x03b8 }
            java.lang.String r8 = "#audio# enforcing concurrency state on a new audio route of %s"
            r2.u(r8, r4)     // Catch:{ all -> 0x03b8 }
            java.util.List r2 = r1.b     // Catch:{ all -> 0x03b8 }
            java.util.Iterator r2 = r2.iterator()     // Catch:{ all -> 0x03b8 }
        L_0x01e4:
            boolean r4 = r2.hasNext()     // Catch:{ all -> 0x03b8 }
            if (r4 == 0) goto L_0x01fa
            java.lang.Object r4 = r2.next()     // Catch:{ all -> 0x03b8 }
            r8 = r4
            esi r8 = (defpackage.esi) r8     // Catch:{ all -> 0x03b8 }
            esg r8 = r8.c     // Catch:{ all -> 0x03b8 }
            int r8 = r8.a     // Catch:{ all -> 0x03b8 }
            int r9 = r0.a     // Catch:{ all -> 0x03b8 }
            if (r8 != r9) goto L_0x01e4
            goto L_0x01fb
        L_0x01fa:
            r4 = r6
        L_0x01fb:
            esi r4 = (defpackage.esi) r4     // Catch:{ all -> 0x03b8 }
            if (r4 == 0) goto L_0x0208
            esg r2 = r4.c     // Catch:{ all -> 0x03b8 }
            int r4 = r4.b     // Catch:{ all -> 0x03b8 }
            dzx r8 = defpackage.dzx.DISCONNECT_REASON_CLIENT_REQUESTED_ROUTE_UPDATE     // Catch:{ all -> 0x03b8 }
            r1.b(r2, r4, r8)     // Catch:{ all -> 0x03b8 }
        L_0x0208:
            java.lang.Object r2 = r3.b()     // Catch:{ all -> 0x03b8 }
            dzq r2 = (defpackage.dzq) r2     // Catch:{ all -> 0x03b8 }
            int r3 = r2.a     // Catch:{ all -> 0x03b8 }
            dzp r3 = defpackage.dzp.a(r3)     // Catch:{ all -> 0x03b8 }
            int r3 = r3.ordinal()     // Catch:{ all -> 0x03b8 }
            switch(r3) {
                case 0: goto L_0x0280;
                case 1: goto L_0x0273;
                case 2: goto L_0x0273;
                case 3: goto L_0x0263;
                case 4: goto L_0x0243;
                case 5: goto L_0x0239;
                case 6: goto L_0x022f;
                case 7: goto L_0x021f;
                case 8: goto L_0x0273;
                default: goto L_0x021b;
            }     // Catch:{ all -> 0x03b8 }
        L_0x021b:
            jjq r0 = new jjq     // Catch:{ all -> 0x03b8 }
            goto L_0x03b2
        L_0x021f:
            dsy r3 = r1.i     // Catch:{ all -> 0x03b8 }
            ecg r4 = defpackage.ecg.GACS     // Catch:{ all -> 0x03b8 }
            hca r8 = defpackage.evd.a     // Catch:{ all -> 0x03b8 }
            grh r8 = defpackage.evd.a(r27)     // Catch:{ all -> 0x03b8 }
            eru r2 = r3.d(r2, r4, r8)     // Catch:{ all -> 0x03b8 }
            goto L_0x02fd
        L_0x022f:
            dsy r3 = r1.i     // Catch:{ all -> 0x03b8 }
            ecg r4 = defpackage.ecg.CAR     // Catch:{ all -> 0x03b8 }
            eru r2 = r3.c(r2, r4)     // Catch:{ all -> 0x03b8 }
            goto L_0x02fd
        L_0x0239:
            dsy r3 = r1.i     // Catch:{ all -> 0x03b8 }
            ecg r4 = defpackage.ecg.SODA     // Catch:{ all -> 0x03b8 }
            eru r2 = r3.c(r2, r4)     // Catch:{ all -> 0x03b8 }
            goto L_0x02fd
        L_0x0243:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException     // Catch:{ all -> 0x03b8 }
            java.lang.String r2 = defpackage.fbi.p(r2)     // Catch:{ all -> 0x03b8 }
            java.lang.StringBuilder r3 = new java.lang.StringBuilder     // Catch:{ all -> 0x03b8 }
            r3.<init>()     // Catch:{ all -> 0x03b8 }
            java.lang.String r4 = "Route("
            r3.append(r4)     // Catch:{ all -> 0x03b8 }
            r3.append(r2)     // Catch:{ all -> 0x03b8 }
            java.lang.String r2 = ") is unexpected"
            r3.append(r2)     // Catch:{ all -> 0x03b8 }
            java.lang.String r2 = r3.toString()     // Catch:{ all -> 0x03b8 }
            r0.<init>(r2)     // Catch:{ all -> 0x03b8 }
            throw r0     // Catch:{ all -> 0x03b8 }
        L_0x0263:
            dsy r3 = r1.i     // Catch:{ all -> 0x03b8 }
            ecg r4 = defpackage.ecg.BISTO     // Catch:{ all -> 0x03b8 }
            hca r8 = defpackage.evd.a     // Catch:{ all -> 0x03b8 }
            grh r8 = defpackage.evd.a(r27)     // Catch:{ all -> 0x03b8 }
            eru r2 = r3.d(r2, r4, r8)     // Catch:{ all -> 0x03b8 }
            goto L_0x02fd
        L_0x0273:
            ery r2 = new ery     // Catch:{ all -> 0x03b8 }
            hca r3 = defpackage.evd.a     // Catch:{ all -> 0x03b8 }
            grh r3 = defpackage.evd.a(r27)     // Catch:{ all -> 0x03b8 }
            r2.<init>(r3)     // Catch:{ all -> 0x03b8 }
            goto L_0x02fd
        L_0x0280:
            cyw r3 = r1.j     // Catch:{ all -> 0x03b8 }
            dzc r4 = r0.b     // Catch:{ all -> 0x03b8 }
            elq r18 = new elq     // Catch:{ all -> 0x03b8 }
            ehg r4 = r4.b     // Catch:{ all -> 0x03b8 }
            if (r4 != 0) goto L_0x028c
            ehg r4 = defpackage.ehg.c     // Catch:{ all -> 0x03b8 }
        L_0x028c:
            r9 = r4
            java.lang.String r4 = "getClientInfo(...)"
            defpackage.jnu.d(r9, r4)     // Catch:{ all -> 0x03b8 }
            java.lang.Integer r10 = java.lang.Integer.valueOf(r5)     // Catch:{ all -> 0x03b8 }
            int r4 = r0.a     // Catch:{ all -> 0x03b8 }
            java.lang.Integer r12 = java.lang.Integer.valueOf(r4)     // Catch:{ all -> 0x03b8 }
            r13 = 4
            r11 = 0
            r8 = r18
            r8.<init>(r9, r10, r11, r12, r13)     // Catch:{ all -> 0x03b8 }
            int r4 = r2.a     // Catch:{ all -> 0x03b8 }
            if (r4 != r15) goto L_0x02ac
            java.lang.Object r2 = r2.b     // Catch:{ all -> 0x03b8 }
            dzu r2 = (defpackage.dzu) r2     // Catch:{ all -> 0x03b8 }
            goto L_0x02ae
        L_0x02ac:
            dzu r2 = defpackage.dzu.c     // Catch:{ all -> 0x03b8 }
        L_0x02ae:
            r19 = r2
            dzc r2 = r0.b     // Catch:{ all -> 0x03b8 }
            ehg r2 = r2.b     // Catch:{ all -> 0x03b8 }
            if (r2 != 0) goto L_0x02b8
            ehg r2 = defpackage.ehg.c     // Catch:{ all -> 0x03b8 }
        L_0x02b8:
            int r2 = r2.a     // Catch:{ all -> 0x03b8 }
            r4 = 41
            if (r2 != r4) goto L_0x02c5
            boolean r2 = r1.g     // Catch:{ all -> 0x03b8 }
            if (r2 == 0) goto L_0x02c5
            r20 = 2
            goto L_0x02c7
        L_0x02c5:
            r20 = r15
        L_0x02c7:
            egw r2 = new egw     // Catch:{ all -> 0x03b8 }
            r19.getClass()     // Catch:{ all -> 0x03b8 }
            java.lang.Object r4 = r3.b     // Catch:{ all -> 0x03b8 }
            java.lang.Object r8 = r3.d     // Catch:{ all -> 0x03b8 }
            java.lang.Object r4 = r4.b()     // Catch:{ all -> 0x03b8 }
            java.lang.Object r8 = r8.b()     // Catch:{ all -> 0x03b8 }
            r22 = r8
            jqs r22 = (defpackage.jqs) r22     // Catch:{ all -> 0x03b8 }
            r22.getClass()     // Catch:{ all -> 0x03b8 }
            java.lang.Object r8 = r3.c     // Catch:{ all -> 0x03b8 }
            java.lang.Object r8 = r8.b()     // Catch:{ all -> 0x03b8 }
            r23 = r8
            java.util.concurrent.Executor r23 = (java.util.concurrent.Executor) r23     // Catch:{ all -> 0x03b8 }
            r23.getClass()     // Catch:{ all -> 0x03b8 }
            java.lang.Object r3 = r3.a     // Catch:{ all -> 0x03b8 }
            eme r3 = (defpackage.eme) r3     // Catch:{ all -> 0x03b8 }
            cyw r24 = r3.b()     // Catch:{ all -> 0x03b8 }
            r21 = r4
            eix r21 = (defpackage.eix) r21     // Catch:{ all -> 0x03b8 }
            r17 = r2
            r17.<init>(r18, r19, r20, r21, r22, r23, r24)     // Catch:{ all -> 0x03b8 }
        L_0x02fd:
            eoz r3 = r1.k     // Catch:{ all -> 0x03b8 }
            ese r3 = r3.y(r2)     // Catch:{ all -> 0x03b8 }
            hca r2 = a     // Catch:{ all -> 0x03b8 }
            hco r2 = r2.f()     // Catch:{ all -> 0x03b8 }
            hcr r4 = defpackage.hdg.a     // Catch:{ all -> 0x03b8 }
            java.lang.String r8 = "ALT.AudioRoutesRegy"
            hco r2 = r2.h(r4, r8)     // Catch:{ all -> 0x03b8 }
            java.lang.String r4 = "AudioRoutesRegistry.kt"
            java.lang.String r8 = "com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry"
            java.lang.String r9 = "startAudioRouteConnection"
            r10 = 380(0x17c, float:5.32E-43)
            hco r2 = r2.j(r8, r9, r10, r4)     // Catch:{ all -> 0x03b8 }
            hby r2 = (defpackage.hby) r2     // Catch:{ all -> 0x03b8 }
            java.lang.Integer r4 = java.lang.Integer.valueOf(r5)     // Catch:{ all -> 0x03b8 }
            ejn r8 = r3.d     // Catch:{ all -> 0x03b8 }
            dzq r8 = r8.b     // Catch:{ all -> 0x03b8 }
            java.lang.String r8 = defpackage.fbi.r(r8)     // Catch:{ all -> 0x03b8 }
            java.lang.String r9 = r26.a()     // Catch:{ all -> 0x03b8 }
            java.lang.String r10 = "#audio# starting connection to audio route(token(%d), %s) for %s"
            r2.G(r10, r4, r8, r9)     // Catch:{ all -> 0x03b8 }
            hll r2 = r1.f     // Catch:{ all -> 0x03b8 }
            ejj r4 = new ejj     // Catch:{ all -> 0x03b8 }
            r8 = 5
            r4.<init>(r3, r1, r8, r6)     // Catch:{ all -> 0x03b8 }
            hkn r4 = defpackage.gof.c(r4)     // Catch:{ all -> 0x03b8 }
            java.util.concurrent.Executor r6 = r1.e     // Catch:{ all -> 0x03b8 }
            hme r8 = r2.b(r4, r6)     // Catch:{ all -> 0x03b8 }
            java.lang.String r2 = "submitAsync(...)"
            defpackage.jnu.d(r8, r2)     // Catch:{ all -> 0x03b8 }
            dzl r2 = defpackage.dzl.d     // Catch:{ all -> 0x03b8 }
            htk r2 = r2.l()     // Catch:{ all -> 0x03b8 }
            java.lang.String r4 = "newBuilder(...)"
            defpackage.jnu.d(r2, r4)     // Catch:{ all -> 0x03b8 }
            dku r2 = defpackage.jnu.e(r2, "builder")     // Catch:{ all -> 0x03b8 }
            ebf r4 = defpackage.ebf.c     // Catch:{ all -> 0x03b8 }
            htk r4 = r4.l()     // Catch:{ all -> 0x03b8 }
            java.lang.String r6 = "newBuilder(...)"
            defpackage.jnu.d(r4, r6)     // Catch:{ all -> 0x03b8 }
            byw r4 = defpackage.jnu.e(r4, "builder")     // Catch:{ all -> 0x03b8 }
            int r6 = r0.a     // Catch:{ all -> 0x03b8 }
            r4.h(r6)     // Catch:{ all -> 0x03b8 }
            ebf r4 = r4.g()     // Catch:{ all -> 0x03b8 }
            r2.o(r4)     // Catch:{ all -> 0x03b8 }
            ebw r4 = defpackage.ebw.c     // Catch:{ all -> 0x03b8 }
            htk r4 = r4.l()     // Catch:{ all -> 0x03b8 }
            java.lang.String r6 = "newBuilder(...)"
            defpackage.jnu.d(r4, r6)     // Catch:{ all -> 0x03b8 }
            bzl r4 = defpackage.jnu.e(r4, "builder")     // Catch:{ all -> 0x03b8 }
            r4.y(r5)     // Catch:{ all -> 0x03b8 }
            ebw r4 = r4.x()     // Catch:{ all -> 0x03b8 }
            r2.p(r4)     // Catch:{ all -> 0x03b8 }
            dzl r2 = r2.n()     // Catch:{ all -> 0x03b8 }
            byw r4 = r1.l     // Catch:{ all -> 0x03b8 }
            ejn r6 = r3.d     // Catch:{ all -> 0x03b8 }
            hme r6 = r6.a     // Catch:{ all -> 0x03b8 }
            erw r4 = r4.L(r2, r6)     // Catch:{ all -> 0x03b8 }
            esi r9 = new esi     // Catch:{ all -> 0x03b8 }
            r2 = r9
            r6 = r26
            r7 = r27
            r2.<init>(r3, r4, r5, r6, r7)     // Catch:{ all -> 0x03b8 }
            cyw r0 = r1.c     // Catch:{ all -> 0x03b8 }
            r0.o(r8, r9)     // Catch:{ all -> 0x03b8 }
            java.util.List r0 = r1.b     // Catch:{ all -> 0x03b8 }
            r0.add(r9)     // Catch:{ all -> 0x03b8 }
            monitor-exit(r25)
            return r9
        L_0x03b2:
            r0.<init>()     // Catch:{ all -> 0x03b8 }
            throw r0     // Catch:{ all -> 0x03b8 }
        L_0x03b6:
            monitor-exit(r25)
            return r8
        L_0x03b8:
            r0 = move-exception
            monitor-exit(r25)     // Catch:{ all -> 0x03b8 }
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.esl.e(esg, dzn, bzj):esh");
    }
}
