package defpackage;

import android.content.Context;
import androidx.work.WorkerParameters;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/* renamed from: baw  reason: default package */
/* compiled from: PG */
public final class baw extends bcb {
    public final List a = new CopyOnWriteArrayList();

    public final bbj a(Context context, String str, WorkerParameters workerParameters) {
        jnu.e(context, "appContext");
        jnu.e(str, "workerClassName");
        jnu.e(workerParameters, "workerParameters");
        for (bcb a2 : this.a) {
            try {
                bbj a3 = a2.a(context, str, workerParameters);
                if (a3 != null) {
                    return a3;
                }
            } catch (Throwable th) {
                bbk.a().d(bax.a, "Unable to instantiate a ListenableWorker (" + str + ')', th);
                throw th;
            }
        }
        return null;
    }
}
