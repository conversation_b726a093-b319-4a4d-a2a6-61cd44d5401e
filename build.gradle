// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {
        google()
        jcenter()
        
    }
    dependencies {
//        classpath 'com.android.tools.build:gradle:3.6.3'
        classpath 'com.android.tools.build:gradle:8.1.4'
        // classpath 'com.google.protobuf:protobuf-gradle-plugin:0.9.4'  // 暂时禁用
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
