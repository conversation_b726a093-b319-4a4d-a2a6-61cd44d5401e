package defpackage;

import java.util.concurrent.atomic.AtomicLong;

/* renamed from: czo  reason: default package */
/* compiled from: PG */
public final class czo implements fom {
    public final csl a;
    public final AtomicLong b = new AtomicLong();
    final /* synthetic */ czp c;
    private final String d;

    public czo(czp czp, String str, csl csl) {
        this.c = czp;
        this.d = str;
        this.a = csl;
    }

    public final void a(int i) {
        this.b.getAndAdd((long) i);
        cyh.l("%s: Received data for groupName = %s, len = %d, Counter = %d", "DownloadProgressMonitor", this.d, Integer.valueOf(i), Long.valueOf(this.b.get()));
    }

    public final void b() {
        synchronized (czp.class) {
            if (this.c.c.containsKey(this.d)) {
                this.c.a.execute(new cmp(this, 11));
            }
        }
    }
}
