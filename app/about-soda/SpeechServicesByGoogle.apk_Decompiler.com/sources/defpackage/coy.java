package defpackage;

import android.os.Handler;
import android.os.SystemClock;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.Delayed;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/* renamed from: coy  reason: default package */
/* compiled from: PG */
public final class coy extends hkb implements hmi {
    public static final /* synthetic */ int b = 0;
    public final Handler a;

    public coy(Handler handler) {
        this.a = handler;
    }

    public static int a(Delayed delayed, Delayed delayed2) {
        int i = (delayed2.getDelay(TimeUnit.MILLISECONDS) > delayed.getDelay(TimeUnit.MILLISECONDS) ? 1 : (delayed2.getDelay(TimeUnit.MILLISECONDS) == delayed.getDelay(TimeUnit.MILLISECONDS) ? 0 : -1));
        if (i > 0) {
            return -1;
        }
        if (i == 0) {
            return 0;
        }
        return 1;
    }

    private final hmg i(Runnable runnable, long j, long j2, TimeUnit timeUnit, boolean z) {
        TimeUnit timeUnit2 = timeUnit;
        long j3 = j;
        long millis = timeUnit2.toMillis(j);
        cow cow = new cow(this, runnable, millis + SystemClock.elapsedRealtime(), timeUnit2.toMillis(j2), z);
        this.a.postDelayed(cow, millis);
        j(cow, cow);
        return cow;
    }

    private final void j(hme hme, Runnable runnable) {
        hme.c(new ckm((Object) this, (Object) runnable, 11), hld.a);
    }

    public final boolean awaitTermination(long j, TimeUnit timeUnit) {
        throw new UnsupportedOperationException();
    }

    /* renamed from: b */
    public final hmg schedule(Runnable runnable, long j, TimeUnit timeUnit) {
        return schedule(Executors.callable(runnable), j, timeUnit);
    }

    public final hme bf(Callable callable) {
        hmr hmr = new hmr();
        this.a.post(new cov(hmr, callable));
        return hmr;
    }

    /* renamed from: c */
    public final hmg schedule(Callable callable, long j, TimeUnit timeUnit) {
        long millis = timeUnit.toMillis(j);
        cox cox = new cox(callable, SystemClock.elapsedRealtime() + millis);
        this.a.postDelayed(cox, millis);
        j(cox, cox);
        return cox;
    }

    /* renamed from: d */
    public final hmg scheduleAtFixedRate(Runnable runnable, long j, long j2, TimeUnit timeUnit) {
        return i(runnable, j, j2, timeUnit, true);
    }

    /* renamed from: e */
    public final hmg scheduleWithFixedDelay(Runnable runnable, long j, long j2, TimeUnit timeUnit) {
        return i(runnable, j, j2, timeUnit, false);
    }

    public final void execute(Runnable runnable) {
        this.a.post(runnable);
    }

    public final boolean isShutdown() {
        return false;
    }

    public final boolean isTerminated() {
        return false;
    }

    public final void shutdown() {
        throw new UnsupportedOperationException();
    }

    public final List shutdownNow() {
        throw new UnsupportedOperationException();
    }

    public final /* bridge */ /* synthetic */ Future submit(Callable callable) {
        return submit(callable);
    }
}
