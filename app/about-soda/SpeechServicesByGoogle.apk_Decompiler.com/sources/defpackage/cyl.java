package defpackage;

import java.util.List;

/* renamed from: cyl  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cyl implements gqx {
    public final Object apply(Object obj) {
        htk l = hir.e.l();
        long j = 0;
        long j2 = 0;
        for (ctb ctb : (List) obj) {
            htk l2 = hiq.e.l();
            htk l3 = hig.k.l();
            ctg ctg = ctb.b;
            if (ctg == null) {
                ctg = ctg.g;
            }
            String str = ctg.c;
            if (!l3.b.B()) {
                l3.u();
            }
            htq htq = l3.b;
            hig hig = (hig) htq;
            str.getClass();
            hig.a |= 4;
            hig.d = str;
            ctg ctg2 = ctb.b;
            if (ctg2 == null) {
                ctg2 = ctg.g;
            }
            String str2 = ctg2.b;
            if (!htq.B()) {
                l3.u();
            }
            htq htq2 = l3.b;
            hig hig2 = (hig) htq2;
            str2.getClass();
            hig2.a |= 1;
            hig2.b = str2;
            int i = ctb.e;
            if (!htq2.B()) {
                l3.u();
            }
            htq htq3 = l3.b;
            hig hig3 = (hig) htq3;
            hig3.a |= 2;
            hig3.c = i;
            long j3 = ctb.c;
            if (!htq3.B()) {
                l3.u();
            }
            htq htq4 = l3.b;
            hig hig4 = (hig) htq4;
            hig4.a |= 64;
            hig4.h = j3;
            String str3 = ctb.d;
            if (!htq4.B()) {
                l3.u();
            }
            hig hig5 = (hig) l3.b;
            str3.getClass();
            hig5.a |= 128;
            hig5.i = str3;
            hig hig6 = (hig) l3.r();
            if (!l2.b.B()) {
                l2.u();
            }
            htq htq5 = l2.b;
            hiq hiq = (hiq) htq5;
            hig6.getClass();
            hiq.b = hig6;
            hiq.a |= 1;
            long j4 = ctb.g;
            if (!htq5.B()) {
                l2.u();
            }
            htq htq6 = l2.b;
            hiq hiq2 = (hiq) htq6;
            hiq2.a |= 2;
            hiq2.c = j4;
            long j5 = ctb.f;
            if (!htq6.B()) {
                l2.u();
            }
            hiq hiq3 = (hiq) l2.b;
            hiq3.a |= 4;
            hiq3.d = j5;
            if (!l.b.B()) {
                l.u();
            }
            hir hir = (hir) l.b;
            hiq hiq4 = (hiq) l2.r();
            hiq4.getClass();
            huf huf = hir.b;
            if (!huf.c()) {
                hir.b = htq.s(huf);
            }
            hir.b.add(hiq4);
            j += ctb.g;
            j2 += ctb.f;
        }
        if (!l.b.B()) {
            l.u();
        }
        htq htq7 = l.b;
        hir hir2 = (hir) htq7;
        hir2.a |= 1;
        hir2.c = j;
        if (!htq7.B()) {
            l.u();
        }
        hir hir3 = (hir) l.b;
        hir3.a |= 2;
        hir3.d = j2;
        return (hir) l.r();
    }
}
