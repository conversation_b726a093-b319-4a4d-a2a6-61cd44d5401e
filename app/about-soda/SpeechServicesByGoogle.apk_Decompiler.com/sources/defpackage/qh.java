package defpackage;

import java.lang.ref.WeakReference;
import java.util.Arrays;
import java.util.HashSet;

/* renamed from: qh  reason: default package */
/* compiled from: PG */
public final class qh extends qn {
    public final qr a = new qr(this);
    public WeakReference aA = null;
    public WeakReference aB = null;
    public WeakReference aC = null;
    public WeakReference aD = null;
    final HashSet aE = new HashSet();
    public final qo aF = new qo();
    public rg aG = null;
    public final bmu aH = new bmu(this);
    public int ar;
    public int as;
    public int at = 0;
    public int au = 0;
    public qe[] av = new qe[4];
    public qe[] aw = new qe[4];
    public int ax = 257;
    public boolean ay = false;
    public boolean az = false;
    public int b;
    public boolean c = false;
    public final px d = new px();

    public static void X(qg qgVar, rg rgVar, qo qoVar) {
        boolean z;
        boolean z2;
        boolean z3;
        boolean z4;
        int i;
        int i2;
        if (rgVar != null) {
            if (qgVar.ah == 8 || (qgVar instanceof qj) || (qgVar instanceof qd)) {
                qoVar.c = 0;
                qoVar.d = 0;
                return;
            }
            qoVar.i = qgVar.M();
            qoVar.j = qgVar.N();
            qoVar.a = qgVar.j();
            qoVar.b = qgVar.h();
            qoVar.g = false;
            qoVar.h = 0;
            if (qoVar.i == 3) {
                z = true;
            } else {
                z = false;
            }
            if (qoVar.j == 3) {
                z2 = true;
            } else {
                z2 = false;
            }
            if (!z || qgVar.X <= 0.0f) {
                z3 = false;
            } else {
                z3 = true;
            }
            if (!z2 || qgVar.X <= 0.0f) {
                z4 = false;
            } else {
                z4 = true;
            }
            if (z && qgVar.F(0) && qgVar.s == 0 && !z3) {
                qoVar.i = 2;
                if (z2 && qgVar.t == 0) {
                    qoVar.i = 1;
                }
                z = false;
            }
            if (z2 && qgVar.F(1) && qgVar.t == 0 && !z4) {
                qoVar.j = 2;
                if (z && qgVar.s == 0) {
                    qoVar.j = 1;
                }
                z2 = false;
            }
            if (qgVar.e()) {
                qoVar.i = 1;
                z = false;
            }
            if (qgVar.f()) {
                qoVar.j = 1;
                z2 = false;
            }
            if (z3) {
                if (qgVar.u[0] == 4) {
                    qoVar.i = 1;
                } else if (!z2) {
                    if (qoVar.j == 1) {
                        i2 = qoVar.b;
                    } else {
                        qoVar.i = 2;
                        rgVar.a(qgVar, qoVar);
                        i2 = qoVar.d;
                    }
                    qoVar.i = 1;
                    qoVar.a = (int) (qgVar.X * ((float) i2));
                }
            }
            if (z4) {
                if (qgVar.u[1] == 4) {
                    qoVar.j = 1;
                } else if (!z) {
                    if (qoVar.i == 1) {
                        i = qoVar.a;
                    } else {
                        qoVar.j = 2;
                        rgVar.a(qgVar, qoVar);
                        i = qoVar.c;
                    }
                    qoVar.j = 1;
                    float f = (float) i;
                    if (qgVar.Y == -1) {
                        qoVar.b = (int) (f / qgVar.X);
                    } else {
                        qoVar.b = (int) (qgVar.X * f);
                    }
                }
            }
            rgVar.a(qgVar, qoVar);
            qgVar.C(qoVar.c);
            qgVar.x(qoVar.d);
            qgVar.F = qoVar.f;
            qgVar.u(qoVar.e);
            qoVar.h = 0;
            boolean z5 = qoVar.g;
        }
    }

    private final void Z(qf qfVar, qb qbVar) {
        px pxVar = this.d;
        pxVar.g(qbVar, pxVar.b(qfVar), 0, 5);
    }

    private final void aa(qf qfVar, qb qbVar) {
        px pxVar = this.d;
        pxVar.g(pxVar.b(qfVar), qbVar, 0, 5);
    }

    private final void ab() {
        this.at = 0;
        this.au = 0;
    }

    public final void D(boolean z, boolean z2) {
        super.D(z, z2);
        int size = this.aI.size();
        for (int i = 0; i < size; i++) {
            ((qg) this.aI.get(i)).D(z, z2);
        }
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r25v1, resolved type: java.util.ArrayList} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v5, resolved type: int[]} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r6v2, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v6, resolved type: int[]} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v4, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v25, resolved type: int[]} */
    /* JADX WARNING: type inference failed for: r2v10, types: [boolean] */
    /* JADX WARNING: type inference failed for: r2v11 */
    /* JADX WARNING: type inference failed for: r2v12 */
    /* JADX WARNING: type inference failed for: r25v30 */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:310:0x057a  */
    /* JADX WARNING: Removed duplicated region for block: B:327:0x05b1  */
    /* JADX WARNING: Removed duplicated region for block: B:333:0x05cc  */
    /* JADX WARNING: Removed duplicated region for block: B:335:0x05d5  */
    /* JADX WARNING: Removed duplicated region for block: B:341:0x05ed  */
    /* JADX WARNING: Removed duplicated region for block: B:349:0x0611  */
    /* JADX WARNING: Removed duplicated region for block: B:350:0x0613  */
    /* JADX WARNING: Removed duplicated region for block: B:359:0x0634  */
    /* JADX WARNING: Removed duplicated region for block: B:360:0x0636  */
    /* JADX WARNING: Removed duplicated region for block: B:363:0x063f  */
    /* JADX WARNING: Removed duplicated region for block: B:369:0x0659  */
    /* JADX WARNING: Removed duplicated region for block: B:541:0x093f  */
    /* JADX WARNING: Removed duplicated region for block: B:555:0x096e A[LOOP:42: B:554:0x096c->B:555:0x096e, LOOP_END] */
    /* JADX WARNING: Removed duplicated region for block: B:567:0x09d5  */
    /* JADX WARNING: Removed duplicated region for block: B:568:0x09e3  */
    /* JADX WARNING: Removed duplicated region for block: B:571:0x09f6  */
    /* JADX WARNING: Removed duplicated region for block: B:572:0x0a01  */
    /* JADX WARNING: Removed duplicated region for block: B:574:0x0a05  */
    /* JADX WARNING: Removed duplicated region for block: B:587:0x0a3e  */
    /* JADX WARNING: Removed duplicated region for block: B:589:0x0a43  */
    /* JADX WARNING: Removed duplicated region for block: B:590:0x0a45  */
    /* JADX WARNING: Removed duplicated region for block: B:594:0x0a54  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void T() {
        /*
            r27 = this;
            r7 = r27
            r8 = 0
            r7.Z = r8
            r7.aa = r8
            r7.ay = r8
            r7.az = r8
            java.util.ArrayList r0 = r7.aI
            int r9 = r0.size()
            int r0 = r27.j()
            int r0 = java.lang.Math.max(r8, r0)
            int r1 = r27.h()
            int r1 = java.lang.Math.max(r8, r1)
            int[] r2 = r7.aq
            r10 = 1
            r3 = r2[r10]
            r2 = r2[r8]
            int r4 = r7.b
            r12 = -1
            if (r4 != 0) goto L_0x0232
            int r4 = r7.ax
            boolean r4 = defpackage.ql.b(r4, r10)
            if (r4 == 0) goto L_0x0232
            rg r4 = r7.aG
            int r5 = r27.M()
            int r6 = r27.N()
            defpackage.qu.b = r8
            defpackage.qu.c = r8
            r27.t()
            java.util.ArrayList r13 = r7.aI
            int r14 = r13.size()
            r15 = r8
        L_0x004d:
            if (r15 >= r14) goto L_0x005b
            java.lang.Object r16 = r13.get(r15)
            qg r16 = (defpackage.qg) r16
            r16.t()
            int r15 = r15 + 1
            goto L_0x004d
        L_0x005b:
            boolean r15 = r7.c
            if (r5 != r10) goto L_0x0067
            int r5 = r27.j()
            r7.v(r8, r5)
            goto L_0x006e
        L_0x0067:
            qf r5 = r7.f39J
            r5.e(r8)
            r7.Z = r8
        L_0x006e:
            r5 = r8
            r16 = r5
            r17 = r16
        L_0x0073:
            r18 = 1056964608(0x3f000000, float:0.5)
            if (r5 >= r14) goto L_0x00d0
            java.lang.Object r19 = r13.get(r5)
            r11 = r19
            qg r11 = (defpackage.qg) r11
            boolean r8 = r11 instanceof defpackage.qj
            if (r8 == 0) goto L_0x00bd
            qj r11 = (defpackage.qj) r11
            int r8 = r11.ar
            if (r8 != r10) goto L_0x00cb
            int r8 = r11.b
            if (r8 == r12) goto L_0x0093
            r11.a(r8)
        L_0x0090:
            r16 = r10
            goto L_0x00cb
        L_0x0093:
            int r8 = r11.c
            if (r8 == r12) goto L_0x00a8
            boolean r8 = r27.e()
            if (r8 == 0) goto L_0x00a8
            int r8 = r27.j()
            int r12 = r11.c
            int r8 = r8 - r12
            r11.a(r8)
            goto L_0x0090
        L_0x00a8:
            boolean r8 = r27.e()
            if (r8 == 0) goto L_0x0090
            float r8 = r11.a
            int r12 = r27.j()
            float r12 = (float) r12
            float r8 = r8 * r12
            float r8 = r8 + r18
            int r8 = (int) r8
            r11.a(r8)
            goto L_0x0090
        L_0x00bd:
            boolean r8 = r11 instanceof defpackage.qd
            if (r8 == 0) goto L_0x00cb
            qd r11 = (defpackage.qd) r11
            int r8 = r11.a()
            if (r8 != 0) goto L_0x00cb
            r17 = r10
        L_0x00cb:
            int r5 = r5 + 1
            r8 = 0
            r12 = -1
            goto L_0x0073
        L_0x00d0:
            if (r16 == 0) goto L_0x00ee
            r5 = 0
        L_0x00d3:
            if (r5 >= r14) goto L_0x00ee
            java.lang.Object r8 = r13.get(r5)
            qg r8 = (defpackage.qg) r8
            boolean r11 = r8 instanceof defpackage.qj
            if (r11 == 0) goto L_0x00ea
            qj r8 = (defpackage.qj) r8
            int r11 = r8.ar
            if (r11 != r10) goto L_0x00ea
            r11 = 0
            defpackage.qu.a(r11, r8, r4, r15)
            goto L_0x00eb
        L_0x00ea:
            r11 = 0
        L_0x00eb:
            int r5 = r5 + 1
            goto L_0x00d3
        L_0x00ee:
            r11 = 0
            defpackage.qu.a(r11, r7, r4, r15)
            if (r17 == 0) goto L_0x010f
            r5 = r11
        L_0x00f5:
            if (r5 >= r14) goto L_0x010f
            java.lang.Object r8 = r13.get(r5)
            qg r8 = (defpackage.qg) r8
            boolean r12 = r8 instanceof defpackage.qd
            if (r12 == 0) goto L_0x010c
            qd r8 = (defpackage.qd) r8
            int r12 = r8.a()
            if (r12 != 0) goto L_0x010c
            defpackage.qu.d(r8, r4, r11, r15)
        L_0x010c:
            int r5 = r5 + 1
            goto L_0x00f5
        L_0x010f:
            if (r6 != r10) goto L_0x011b
            int r5 = r27.h()
            r7.w(r11, r5)
            r5 = r11
            r6 = r5
            goto L_0x0125
        L_0x011b:
            qf r5 = r7.K
            r5.e(r11)
            r7.aa = r11
            r5 = 0
            r6 = 0
            r11 = 0
        L_0x0125:
            if (r11 >= r14) goto L_0x017b
            java.lang.Object r8 = r13.get(r11)
            qg r8 = (defpackage.qg) r8
            boolean r12 = r8 instanceof defpackage.qj
            if (r12 == 0) goto L_0x016b
            qj r8 = (defpackage.qj) r8
            int r12 = r8.ar
            if (r12 != 0) goto L_0x0178
            int r5 = r8.b
            r12 = -1
            if (r5 == r12) goto L_0x0141
            r8.a(r5)
        L_0x013f:
            r5 = r10
            goto L_0x0178
        L_0x0141:
            int r5 = r8.c
            if (r5 == r12) goto L_0x0156
            boolean r5 = r27.f()
            if (r5 == 0) goto L_0x0156
            int r5 = r27.h()
            int r12 = r8.c
            int r5 = r5 - r12
            r8.a(r5)
            goto L_0x013f
        L_0x0156:
            boolean r5 = r27.f()
            if (r5 == 0) goto L_0x013f
            float r5 = r8.a
            int r12 = r27.h()
            float r12 = (float) r12
            float r5 = r5 * r12
            float r5 = r5 + r18
            int r5 = (int) r5
            r8.a(r5)
            goto L_0x013f
        L_0x016b:
            boolean r12 = r8 instanceof defpackage.qd
            if (r12 == 0) goto L_0x0178
            qd r8 = (defpackage.qd) r8
            int r8 = r8.a()
            if (r8 != r10) goto L_0x0178
            r6 = r10
        L_0x0178:
            int r11 = r11 + 1
            goto L_0x0125
        L_0x017b:
            if (r5 == 0) goto L_0x0196
            r5 = 0
        L_0x017e:
            if (r5 >= r14) goto L_0x0196
            java.lang.Object r8 = r13.get(r5)
            qg r8 = (defpackage.qg) r8
            boolean r11 = r8 instanceof defpackage.qj
            if (r11 == 0) goto L_0x0193
            qj r8 = (defpackage.qj) r8
            int r11 = r8.ar
            if (r11 != 0) goto L_0x0193
            defpackage.qu.b(r10, r8, r4)
        L_0x0193:
            int r5 = r5 + 1
            goto L_0x017e
        L_0x0196:
            r5 = 0
            defpackage.qu.b(r5, r7, r4)
            if (r6 == 0) goto L_0x01b7
            r5 = 0
        L_0x019d:
            if (r5 >= r14) goto L_0x01b7
            java.lang.Object r6 = r13.get(r5)
            qg r6 = (defpackage.qg) r6
            boolean r8 = r6 instanceof defpackage.qd
            if (r8 == 0) goto L_0x01b4
            qd r6 = (defpackage.qd) r6
            int r8 = r6.a()
            if (r8 != r10) goto L_0x01b4
            defpackage.qu.d(r6, r4, r10, r15)
        L_0x01b4:
            int r5 = r5 + 1
            goto L_0x019d
        L_0x01b7:
            r5 = 0
        L_0x01b8:
            if (r5 >= r14) goto L_0x01f0
            java.lang.Object r6 = r13.get(r5)
            qg r6 = (defpackage.qg) r6
            boolean r8 = r6.J()
            if (r8 == 0) goto L_0x01ed
            boolean r8 = defpackage.qu.c(r6)
            if (r8 == 0) goto L_0x01ed
            qo r8 = defpackage.qu.a
            X(r6, r4, r8)
            boolean r8 = r6 instanceof defpackage.qj
            if (r8 == 0) goto L_0x01e6
            r8 = r6
            qj r8 = (defpackage.qj) r8
            int r8 = r8.ar
            if (r8 != 0) goto L_0x01e1
            r8 = 0
            defpackage.qu.b(r8, r6, r4)
            goto L_0x01ed
        L_0x01e1:
            r8 = 0
            defpackage.qu.a(r8, r6, r4, r15)
            goto L_0x01ed
        L_0x01e6:
            r8 = 0
            defpackage.qu.a(r8, r6, r4, r15)
            defpackage.qu.b(r8, r6, r4)
        L_0x01ed:
            int r5 = r5 + 1
            goto L_0x01b8
        L_0x01f0:
            r4 = 0
        L_0x01f1:
            if (r4 >= r9) goto L_0x0232
            java.util.ArrayList r5 = r7.aI
            java.lang.Object r5 = r5.get(r4)
            qg r5 = (defpackage.qg) r5
            boolean r6 = r5.J()
            if (r6 == 0) goto L_0x022f
            boolean r6 = r5 instanceof defpackage.qj
            if (r6 != 0) goto L_0x022f
            boolean r6 = r5 instanceof defpackage.qd
            if (r6 != 0) goto L_0x022f
            boolean r6 = r5 instanceof defpackage.qm
            if (r6 != 0) goto L_0x022f
            boolean r6 = r5.G
            r6 = 0
            int r8 = r5.L(r6)
            int r6 = r5.L(r10)
            r11 = 3
            if (r8 != r11) goto L_0x0225
            int r8 = r5.s
            if (r8 == r10) goto L_0x0225
            if (r6 != r11) goto L_0x0225
            int r6 = r5.t
            if (r6 != r10) goto L_0x022f
        L_0x0225:
            qo r6 = new qo
            r6.<init>()
            rg r8 = r7.aG
            X(r5, r8, r6)
        L_0x022f:
            int r4 = r4 + 1
            goto L_0x01f1
        L_0x0232:
            r11 = 2
            if (r9 <= r11) goto L_0x05f6
            if (r2 == r11) goto L_0x0244
            if (r3 != r11) goto L_0x023b
            r3 = r11
            goto L_0x0244
        L_0x023b:
            r8 = r0
            r10 = r2
            r11 = r3
            r22 = r9
            r0 = 0
            r9 = r1
            goto L_0x0600
        L_0x0244:
            int r4 = r7.ax
            r5 = 1024(0x400, float:1.435E-42)
            boolean r4 = defpackage.ql.b(r4, r5)
            if (r4 == 0) goto L_0x05f6
            rg r4 = r7.aG
            java.util.ArrayList r5 = r7.aI
            int r6 = r5.size()
            r12 = 0
        L_0x0257:
            if (r12 >= r6) goto L_0x027f
            java.lang.Object r13 = r5.get(r12)
            qg r13 = (defpackage.qg) r13
            int r14 = r27.M()
            int r15 = r27.N()
            int r11 = r13.M()
            int r8 = r13.N()
            boolean r8 = defpackage.kf.k(r14, r15, r11, r8)
            if (r8 != 0) goto L_0x0276
            goto L_0x023b
        L_0x0276:
            boolean r8 = r13 instanceof defpackage.qi
            if (r8 == 0) goto L_0x027b
            goto L_0x023b
        L_0x027b:
            int r12 = r12 + 1
            r11 = 2
            goto L_0x0257
        L_0x027f:
            r8 = 0
            r10 = 0
            r11 = 0
            r12 = 0
            r13 = 0
            r14 = 0
            r15 = 0
        L_0x0286:
            if (r10 >= r6) goto L_0x0369
            java.lang.Object r21 = r5.get(r10)
            r22 = r9
            r9 = r21
            qg r9 = (defpackage.qg) r9
            r21 = r1
            int r1 = r27.M()
            r23 = r3
            int r3 = r27.N()
            r24 = r0
            int r0 = r9.M()
            r25 = r2
            int r2 = r9.N()
            boolean r0 = defpackage.kf.k(r1, r3, r0, r2)
            if (r0 != 0) goto L_0x02b5
            qo r0 = r7.aF
            X(r9, r4, r0)
        L_0x02b5:
            boolean r0 = r9 instanceof defpackage.qj
            if (r0 == 0) goto L_0x02d9
            r1 = r9
            qj r1 = (defpackage.qj) r1
            int r2 = r1.ar
            if (r2 != 0) goto L_0x02ca
            if (r12 != 0) goto L_0x02c7
            java.util.ArrayList r12 = new java.util.ArrayList
            r12.<init>()
        L_0x02c7:
            r12.add(r1)
        L_0x02ca:
            int r2 = r1.ar
            r3 = 1
            if (r2 != r3) goto L_0x02d9
            if (r8 != 0) goto L_0x02d6
            java.util.ArrayList r8 = new java.util.ArrayList
            r8.<init>()
        L_0x02d6:
            r8.add(r1)
        L_0x02d9:
            boolean r1 = r9 instanceof defpackage.qk
            if (r1 == 0) goto L_0x031d
            boolean r1 = r9 instanceof defpackage.qd
            if (r1 == 0) goto L_0x0306
            r1 = r9
            qd r1 = (defpackage.qd) r1
            int r2 = r1.a()
            if (r2 != 0) goto L_0x02f4
            if (r11 != 0) goto L_0x02f1
            java.util.ArrayList r11 = new java.util.ArrayList
            r11.<init>()
        L_0x02f1:
            r11.add(r1)
        L_0x02f4:
            int r2 = r1.a()
            r3 = 1
            if (r2 != r3) goto L_0x031d
            if (r13 != 0) goto L_0x0302
            java.util.ArrayList r13 = new java.util.ArrayList
            r13.<init>()
        L_0x0302:
            r13.add(r1)
            goto L_0x031d
        L_0x0306:
            r1 = r9
            qk r1 = (defpackage.qk) r1
            if (r11 != 0) goto L_0x0310
            java.util.ArrayList r11 = new java.util.ArrayList
            r11.<init>()
        L_0x0310:
            r11.add(r1)
            if (r13 != 0) goto L_0x031a
            java.util.ArrayList r13 = new java.util.ArrayList
            r13.<init>()
        L_0x031a:
            r13.add(r1)
        L_0x031d:
            qf r1 = r9.f39J
            qf r1 = r1.e
            if (r1 != 0) goto L_0x0339
            qf r1 = r9.L
            qf r1 = r1.e
            if (r1 != 0) goto L_0x0339
            if (r0 != 0) goto L_0x0339
            boolean r1 = r9 instanceof defpackage.qd
            if (r1 != 0) goto L_0x0339
            if (r14 != 0) goto L_0x0336
            java.util.ArrayList r14 = new java.util.ArrayList
            r14.<init>()
        L_0x0336:
            r14.add(r9)
        L_0x0339:
            qf r1 = r9.K
            qf r1 = r1.e
            if (r1 != 0) goto L_0x035b
            qf r1 = r9.M
            qf r1 = r1.e
            if (r1 != 0) goto L_0x035b
            qf r1 = r9.N
            qf r1 = r1.e
            if (r1 != 0) goto L_0x035b
            if (r0 != 0) goto L_0x035b
            boolean r0 = r9 instanceof defpackage.qd
            if (r0 != 0) goto L_0x035b
            if (r15 != 0) goto L_0x0358
            java.util.ArrayList r15 = new java.util.ArrayList
            r15.<init>()
        L_0x0358:
            r15.add(r9)
        L_0x035b:
            int r10 = r10 + 1
            r1 = r21
            r9 = r22
            r3 = r23
            r0 = r24
            r2 = r25
            goto L_0x0286
        L_0x0369:
            r24 = r0
            r21 = r1
            r25 = r2
            r23 = r3
            r22 = r9
            java.util.ArrayList r0 = new java.util.ArrayList
            r0.<init>()
            if (r8 == 0) goto L_0x038f
            int r1 = r8.size()
            r2 = 0
        L_0x037f:
            if (r2 >= r1) goto L_0x038f
            java.lang.Object r3 = r8.get(r2)
            qj r3 = (defpackage.qj) r3
            r4 = 0
            r9 = 0
            defpackage.kf.i(r3, r9, r0, r4)
            int r2 = r2 + 1
            goto L_0x037f
        L_0x038f:
            if (r11 == 0) goto L_0x03ad
            int r1 = r11.size()
            r2 = 0
        L_0x0396:
            if (r2 >= r1) goto L_0x03ad
            java.lang.Object r3 = r11.get(r2)
            qk r3 = (defpackage.qk) r3
            r4 = 0
            r8 = 0
            qz r9 = defpackage.kf.i(r3, r8, r0, r4)
            r3.T(r0, r8, r9)
            r9.b(r0)
            int r2 = r2 + 1
            goto L_0x0396
        L_0x03ad:
            r1 = 2
            qf r2 = r7.K(r1)
            java.util.HashSet r1 = r2.a
            if (r1 == 0) goto L_0x03ce
            java.util.Iterator r1 = r1.iterator()
        L_0x03ba:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L_0x03ce
            java.lang.Object r2 = r1.next()
            qf r2 = (defpackage.qf) r2
            qg r2 = r2.d
            r3 = 0
            r4 = 0
            defpackage.kf.i(r2, r4, r0, r3)
            goto L_0x03ba
        L_0x03ce:
            r1 = 4
            qf r1 = r7.K(r1)
            java.util.HashSet r1 = r1.a
            if (r1 == 0) goto L_0x03ef
            java.util.Iterator r1 = r1.iterator()
        L_0x03db:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L_0x03ef
            java.lang.Object r2 = r1.next()
            qf r2 = (defpackage.qf) r2
            qg r2 = r2.d
            r3 = 0
            r4 = 0
            defpackage.kf.i(r2, r4, r0, r3)
            goto L_0x03db
        L_0x03ef:
            r1 = 7
            qf r2 = r7.K(r1)
            java.util.HashSet r2 = r2.a
            if (r2 == 0) goto L_0x0410
            java.util.Iterator r2 = r2.iterator()
        L_0x03fc:
            boolean r3 = r2.hasNext()
            if (r3 == 0) goto L_0x0410
            java.lang.Object r3 = r2.next()
            qf r3 = (defpackage.qf) r3
            qg r3 = r3.d
            r4 = 0
            r8 = 0
            defpackage.kf.i(r3, r4, r0, r8)
            goto L_0x03fc
        L_0x0410:
            r4 = 0
            r8 = 0
            if (r14 == 0) goto L_0x0429
            int r2 = r14.size()
            r3 = r4
        L_0x0419:
            if (r3 >= r2) goto L_0x0429
            java.lang.Object r9 = r14.get(r3)
            qg r9 = (defpackage.qg) r9
            defpackage.kf.i(r9, r4, r0, r8)
            int r3 = r3 + 1
            r4 = 0
            r8 = 0
            goto L_0x0419
        L_0x0429:
            if (r12 == 0) goto L_0x0440
            int r2 = r12.size()
            r3 = 0
        L_0x0430:
            if (r3 >= r2) goto L_0x0440
            java.lang.Object r4 = r12.get(r3)
            qj r4 = (defpackage.qj) r4
            r8 = 0
            r9 = 1
            defpackage.kf.i(r4, r9, r0, r8)
            int r3 = r3 + 1
            goto L_0x0430
        L_0x0440:
            if (r13 == 0) goto L_0x045e
            int r2 = r13.size()
            r3 = 0
        L_0x0447:
            if (r3 >= r2) goto L_0x045e
            java.lang.Object r4 = r13.get(r3)
            qk r4 = (defpackage.qk) r4
            r8 = 0
            r9 = 1
            qz r10 = defpackage.kf.i(r4, r9, r0, r8)
            r4.T(r0, r9, r10)
            r10.b(r0)
            int r3 = r3 + 1
            goto L_0x0447
        L_0x045e:
            r2 = 3
            qf r3 = r7.K(r2)
            java.util.HashSet r2 = r3.a
            if (r2 == 0) goto L_0x047f
            java.util.Iterator r2 = r2.iterator()
        L_0x046b:
            boolean r3 = r2.hasNext()
            if (r3 == 0) goto L_0x047f
            java.lang.Object r3 = r2.next()
            qf r3 = (defpackage.qf) r3
            qg r3 = r3.d
            r4 = 0
            r8 = 1
            defpackage.kf.i(r3, r8, r0, r4)
            goto L_0x046b
        L_0x047f:
            r2 = 6
            qf r2 = r7.K(r2)
            java.util.HashSet r2 = r2.a
            if (r2 == 0) goto L_0x04a0
            java.util.Iterator r2 = r2.iterator()
        L_0x048c:
            boolean r3 = r2.hasNext()
            if (r3 == 0) goto L_0x04a0
            java.lang.Object r3 = r2.next()
            qf r3 = (defpackage.qf) r3
            qg r3 = r3.d
            r4 = 0
            r8 = 1
            defpackage.kf.i(r3, r8, r0, r4)
            goto L_0x048c
        L_0x04a0:
            r2 = 5
            qf r2 = r7.K(r2)
            java.util.HashSet r2 = r2.a
            if (r2 == 0) goto L_0x04c1
            java.util.Iterator r2 = r2.iterator()
        L_0x04ad:
            boolean r3 = r2.hasNext()
            if (r3 == 0) goto L_0x04c1
            java.lang.Object r3 = r2.next()
            qf r3 = (defpackage.qf) r3
            qg r3 = r3.d
            r4 = 0
            r8 = 1
            defpackage.kf.i(r3, r8, r0, r4)
            goto L_0x04ad
        L_0x04c1:
            qf r1 = r7.K(r1)
            java.util.HashSet r1 = r1.a
            if (r1 == 0) goto L_0x04e1
            java.util.Iterator r1 = r1.iterator()
        L_0x04cd:
            boolean r2 = r1.hasNext()
            if (r2 == 0) goto L_0x04e1
            java.lang.Object r2 = r1.next()
            qf r2 = (defpackage.qf) r2
            qg r2 = r2.d
            r3 = 0
            r4 = 1
            defpackage.kf.i(r2, r4, r0, r3)
            goto L_0x04cd
        L_0x04e1:
            r3 = 0
            r4 = 1
            if (r15 == 0) goto L_0x04fa
            int r1 = r15.size()
            r2 = 0
        L_0x04ea:
            if (r2 >= r1) goto L_0x04fa
            java.lang.Object r8 = r15.get(r2)
            qg r8 = (defpackage.qg) r8
            defpackage.kf.i(r8, r4, r0, r3)
            int r2 = r2 + 1
            r3 = 0
            r4 = 1
            goto L_0x04ea
        L_0x04fa:
            r1 = 0
        L_0x04fb:
            if (r1 >= r6) goto L_0x052c
            java.lang.Object r2 = r5.get(r1)
            qg r2 = (defpackage.qg) r2
            int[] r3 = r2.aq
            r4 = 0
            r8 = r3[r4]
            r9 = 3
            if (r8 != r9) goto L_0x0529
            r8 = 1
            r3 = r3[r8]
            if (r3 != r9) goto L_0x0529
            int r3 = r2.ao
            qz r3 = defpackage.kf.j(r0, r3)
            int r2 = r2.ap
            qz r2 = defpackage.kf.j(r0, r2)
            if (r3 == 0) goto L_0x0529
            if (r2 == 0) goto L_0x0529
            r3.c(r4, r2)
            r4 = 2
            r2.d = r4
            r0.remove(r3)
        L_0x0529:
            int r1 = r1 + 1
            goto L_0x04fb
        L_0x052c:
            int r1 = r0.size()
            r2 = 1
            if (r1 > r2) goto L_0x053d
        L_0x0533:
            r9 = r21
            r11 = r23
            r8 = r24
            r10 = r25
            goto L_0x05ff
        L_0x053d:
            int r1 = r27.M()
            r3 = 2
            if (r1 != r3) goto L_0x0572
            int r1 = r0.size()
            r3 = 0
            r4 = 0
            r5 = 0
        L_0x054b:
            if (r4 >= r1) goto L_0x0568
            java.lang.Object r6 = r0.get(r4)
            qz r6 = (defpackage.qz) r6
            int r8 = r6.d
            if (r8 == r2) goto L_0x0564
            px r2 = r7.d
            r8 = 0
            int r2 = r6.a(r2, r8)
            if (r2 <= r5) goto L_0x0561
            r3 = r6
        L_0x0561:
            if (r2 <= r5) goto L_0x0564
            r5 = r2
        L_0x0564:
            int r4 = r4 + 1
            r2 = 1
            goto L_0x054b
        L_0x0568:
            if (r3 == 0) goto L_0x0572
            r1 = 1
            r7.P(r1)
            r7.C(r5)
            goto L_0x0573
        L_0x0572:
            r3 = 0
        L_0x0573:
            int r1 = r27.N()
            r2 = 2
            if (r1 != r2) goto L_0x05a7
            int r1 = r0.size()
            r2 = 0
            r4 = 0
            r5 = 0
        L_0x0581:
            if (r4 >= r1) goto L_0x059d
            java.lang.Object r6 = r0.get(r4)
            qz r6 = (defpackage.qz) r6
            int r8 = r6.d
            if (r8 == 0) goto L_0x059a
            px r8 = r7.d
            r9 = 1
            int r8 = r6.a(r8, r9)
            if (r8 <= r5) goto L_0x0597
            r2 = r6
        L_0x0597:
            if (r8 <= r5) goto L_0x059a
            r5 = r8
        L_0x059a:
            int r4 = r4 + 1
            goto L_0x0581
        L_0x059d:
            if (r2 == 0) goto L_0x05a7
            r1 = 1
            r7.Q(r1)
            r7.x(r5)
            goto L_0x05a8
        L_0x05a7:
            r2 = 0
        L_0x05a8:
            if (r3 != 0) goto L_0x05ac
            if (r2 == 0) goto L_0x0533
        L_0x05ac:
            r0 = r25
            r1 = 2
            if (r0 != r1) goto L_0x05cc
            int r0 = r27.j()
            r1 = r24
            if (r1 >= r0) goto L_0x05c3
            if (r1 <= 0) goto L_0x05c3
            r7.C(r1)
            r2 = 1
            r7.ay = r2
            r0 = r1
            goto L_0x05c7
        L_0x05c3:
            int r0 = r27.j()
        L_0x05c7:
            r3 = r23
            r1 = 2
            r2 = 2
            goto L_0x05d3
        L_0x05cc:
            r1 = r24
            r2 = r0
            r0 = r1
            r3 = r23
            r1 = 2
        L_0x05d3:
            if (r3 != r1) goto L_0x05ed
            int r1 = r27.h()
            r4 = r21
            if (r4 >= r1) goto L_0x05e7
            if (r4 <= 0) goto L_0x05e7
            r7.x(r4)
            r1 = 1
            r7.az = r1
            r1 = r4
            goto L_0x05eb
        L_0x05e7:
            int r1 = r27.h()
        L_0x05eb:
            r3 = 2
            goto L_0x05f0
        L_0x05ed:
            r4 = r21
            r1 = r4
        L_0x05f0:
            r8 = r0
            r9 = r1
            r10 = r2
            r11 = r3
            r0 = 1
            goto L_0x0600
        L_0x05f6:
            r4 = r1
            r22 = r9
            r1 = r0
            r0 = r2
            r10 = r0
            r8 = r1
            r11 = r3
            r9 = r4
        L_0x05ff:
            r0 = 0
        L_0x0600:
            r12 = 64
            boolean r1 = r7.W(r12)
            if (r1 != 0) goto L_0x0613
            r1 = 128(0x80, float:1.794E-43)
            boolean r1 = r7.W(r1)
            if (r1 == 0) goto L_0x0611
            goto L_0x0613
        L_0x0611:
            r1 = 0
            goto L_0x0614
        L_0x0613:
            r1 = 1
        L_0x0614:
            px r2 = r7.d
            r3 = 0
            r2.f = r3
            r2.g = r3
            int r3 = r7.ax
            if (r3 == 0) goto L_0x0624
            if (r1 == 0) goto L_0x0624
            r1 = 1
            r2.g = r1
        L_0x0624:
            java.util.ArrayList r13 = r7.aI
            int r1 = r27.M()
            r2 = 2
            if (r1 == r2) goto L_0x0636
            int r1 = r27.N()
            if (r1 != r2) goto L_0x0634
            goto L_0x0636
        L_0x0634:
            r14 = 0
            goto L_0x0637
        L_0x0636:
            r14 = 1
        L_0x0637:
            r27.ab()
            r15 = r22
            r1 = 0
        L_0x063d:
            if (r1 >= r15) goto L_0x0653
            java.util.ArrayList r2 = r7.aI
            java.lang.Object r2 = r2.get(r1)
            qg r2 = (defpackage.qg) r2
            boolean r3 = r2 instanceof defpackage.qn
            if (r3 == 0) goto L_0x0650
            qn r2 = (defpackage.qn) r2
            r2.T()
        L_0x0650:
            int r1 = r1 + 1
            goto L_0x063d
        L_0x0653:
            r21 = r0
            r0 = 1
            r1 = 0
        L_0x0657:
            if (r0 == 0) goto L_0x0a4f
            r2 = 1
            int r6 = r1 + 1
            px r0 = r7.d     // Catch:{ Exception -> 0x0908 }
            r0.k()     // Catch:{ Exception -> 0x0908 }
            r27.ab()     // Catch:{ Exception -> 0x0908 }
            px r0 = r7.d     // Catch:{ Exception -> 0x0908 }
            r7.q(r0)     // Catch:{ Exception -> 0x0908 }
            r0 = 0
        L_0x066a:
            if (r0 >= r15) goto L_0x067c
            java.util.ArrayList r1 = r7.aI     // Catch:{ Exception -> 0x0908 }
            java.lang.Object r1 = r1.get(r0)     // Catch:{ Exception -> 0x0908 }
            qg r1 = (defpackage.qg) r1     // Catch:{ Exception -> 0x0908 }
            px r2 = r7.d     // Catch:{ Exception -> 0x0908 }
            r1.q(r2)     // Catch:{ Exception -> 0x0908 }
            int r0 = r0 + 1
            goto L_0x066a
        L_0x067c:
            px r0 = r7.d     // Catch:{ Exception -> 0x0908 }
            boolean r5 = r7.W(r12)     // Catch:{ Exception -> 0x0908 }
            r7.b(r0, r5)     // Catch:{ Exception -> 0x0908 }
            java.util.ArrayList r1 = r7.aI     // Catch:{ Exception -> 0x0908 }
            int r1 = r1.size()     // Catch:{ Exception -> 0x0908 }
            r2 = 0
            r3 = 0
        L_0x068d:
            if (r2 >= r1) goto L_0x06b9
            java.util.ArrayList r4 = r7.aI     // Catch:{ Exception -> 0x06ab }
            java.lang.Object r4 = r4.get(r2)     // Catch:{ Exception -> 0x06ab }
            qg r4 = (defpackage.qg) r4     // Catch:{ Exception -> 0x06ab }
            r12 = 0
            r4.y(r12, r12)     // Catch:{ Exception -> 0x06ab }
            r23 = r6
            r6 = 1
            r4.y(r6, r12)     // Catch:{ Exception -> 0x0905 }
            boolean r4 = r4 instanceof defpackage.qd     // Catch:{ Exception -> 0x0905 }
            r3 = r3 | r4
            int r2 = r2 + 1
            r6 = r23
            r12 = 64
            goto L_0x068d
        L_0x06ab:
            r0 = move-exception
            r23 = r6
        L_0x06ae:
            r26 = r9
            r25 = r13
            r9 = r23
            r3 = 0
            r20 = 3
            goto L_0x0911
        L_0x06b9:
            r23 = r6
            if (r3 == 0) goto L_0x0714
            r2 = 0
        L_0x06be:
            if (r2 >= r1) goto L_0x0714
            java.util.ArrayList r3 = r7.aI     // Catch:{ Exception -> 0x0905 }
            java.lang.Object r3 = r3.get(r2)     // Catch:{ Exception -> 0x0905 }
            qg r3 = (defpackage.qg) r3     // Catch:{ Exception -> 0x0905 }
            boolean r4 = r3 instanceof defpackage.qd     // Catch:{ Exception -> 0x0905 }
            if (r4 == 0) goto L_0x0711
            qd r3 = (defpackage.qd) r3     // Catch:{ Exception -> 0x0905 }
            r4 = 0
        L_0x06cf:
            int r6 = r3.as     // Catch:{ Exception -> 0x0905 }
            if (r4 >= r6) goto L_0x0711
            qg[] r6 = r3.ar     // Catch:{ Exception -> 0x0905 }
            r6 = r6[r4]     // Catch:{ Exception -> 0x0905 }
            boolean r12 = r3.b     // Catch:{ Exception -> 0x0905 }
            if (r12 != 0) goto L_0x06e4
            boolean r12 = r6.d()     // Catch:{ Exception -> 0x0905 }
            if (r12 != 0) goto L_0x06e4
            r24 = r3
            goto L_0x070c
        L_0x06e4:
            int r12 = r3.a     // Catch:{ Exception -> 0x0905 }
            r24 = r3
            if (r12 == 0) goto L_0x0707
            r3 = 1
            if (r12 != r3) goto L_0x06ef
            r12 = r3
            goto L_0x0708
        L_0x06ef:
            r3 = 2
            if (r12 == r3) goto L_0x06f6
            r3 = 3
            if (r12 != r3) goto L_0x070c
            goto L_0x06f7
        L_0x06f6:
            r3 = 3
        L_0x06f7:
            r12 = 1
            r6.y(r12, r12)     // Catch:{ Exception -> 0x06fc }
            goto L_0x070c
        L_0x06fc:
            r0 = move-exception
            r20 = r3
            r26 = r9
            r25 = r13
            r9 = r23
            goto L_0x07e5
        L_0x0707:
            r12 = 1
        L_0x0708:
            r3 = 0
            r6.y(r3, r12)     // Catch:{ Exception -> 0x0905 }
        L_0x070c:
            int r4 = r4 + 1
            r3 = r24
            goto L_0x06cf
        L_0x0711:
            int r2 = r2 + 1
            goto L_0x06be
        L_0x0714:
            java.util.HashSet r2 = r7.aE     // Catch:{ Exception -> 0x0905 }
            r2.clear()     // Catch:{ Exception -> 0x0905 }
            r2 = 0
        L_0x071a:
            if (r2 < r1) goto L_0x08d3
        L_0x071c:
            java.util.HashSet r2 = r7.aE     // Catch:{ Exception -> 0x0905 }
            int r2 = r2.size()     // Catch:{ Exception -> 0x0905 }
            if (r2 <= 0) goto L_0x0784
            java.util.HashSet r2 = r7.aE     // Catch:{ Exception -> 0x0905 }
            int r2 = r2.size()     // Catch:{ Exception -> 0x0905 }
            java.util.HashSet r3 = r7.aE     // Catch:{ Exception -> 0x0905 }
            java.util.Iterator r3 = r3.iterator()     // Catch:{ Exception -> 0x0905 }
        L_0x0730:
            boolean r4 = r3.hasNext()     // Catch:{ Exception -> 0x0905 }
            if (r4 == 0) goto L_0x0760
            java.lang.Object r4 = r3.next()     // Catch:{ Exception -> 0x0905 }
            qg r4 = (defpackage.qg) r4     // Catch:{ Exception -> 0x0905 }
            qm r4 = (defpackage.qm) r4     // Catch:{ Exception -> 0x0905 }
            java.util.HashSet r6 = r7.aE     // Catch:{ Exception -> 0x0905 }
            r24 = r3
            r12 = 0
        L_0x0743:
            int r3 = r4.as     // Catch:{ Exception -> 0x0905 }
            if (r12 >= r3) goto L_0x075d
            qg[] r3 = r4.ar     // Catch:{ Exception -> 0x0905 }
            r3 = r3[r12]     // Catch:{ Exception -> 0x0905 }
            boolean r3 = r6.contains(r3)     // Catch:{ Exception -> 0x0905 }
            if (r3 == 0) goto L_0x075a
            r4.b(r0, r5)     // Catch:{ Exception -> 0x0905 }
            java.util.HashSet r3 = r7.aE     // Catch:{ Exception -> 0x0905 }
            r3.remove(r4)     // Catch:{ Exception -> 0x0905 }
            goto L_0x0760
        L_0x075a:
            int r12 = r12 + 1
            goto L_0x0743
        L_0x075d:
            r3 = r24
            goto L_0x0730
        L_0x0760:
            java.util.HashSet r3 = r7.aE     // Catch:{ Exception -> 0x0905 }
            int r3 = r3.size()     // Catch:{ Exception -> 0x0905 }
            if (r2 != r3) goto L_0x071c
            java.util.HashSet r2 = r7.aE     // Catch:{ Exception -> 0x0905 }
            java.util.Iterator r2 = r2.iterator()     // Catch:{ Exception -> 0x0905 }
        L_0x076e:
            boolean r3 = r2.hasNext()     // Catch:{ Exception -> 0x0905 }
            if (r3 == 0) goto L_0x077e
            java.lang.Object r3 = r2.next()     // Catch:{ Exception -> 0x0905 }
            qg r3 = (defpackage.qg) r3     // Catch:{ Exception -> 0x0905 }
            r3.b(r0, r5)     // Catch:{ Exception -> 0x0905 }
            goto L_0x076e
        L_0x077e:
            java.util.HashSet r2 = r7.aE     // Catch:{ Exception -> 0x0905 }
            r2.clear()     // Catch:{ Exception -> 0x0905 }
            goto L_0x071c
        L_0x0784:
            boolean r2 = defpackage.px.a     // Catch:{ Exception -> 0x0905 }
            if (r2 == 0) goto L_0x07e8
            java.util.HashSet r12 = new java.util.HashSet     // Catch:{ Exception -> 0x07dc }
            r12.<init>()     // Catch:{ Exception -> 0x07dc }
            r2 = 0
        L_0x078e:
            if (r2 >= r1) goto L_0x07a4
            java.util.ArrayList r3 = r7.aI     // Catch:{ Exception -> 0x0905 }
            java.lang.Object r3 = r3.get(r2)     // Catch:{ Exception -> 0x0905 }
            qg r3 = (defpackage.qg) r3     // Catch:{ Exception -> 0x0905 }
            boolean r4 = r3.E()     // Catch:{ Exception -> 0x0905 }
            if (r4 != 0) goto L_0x07a1
            r12.add(r3)     // Catch:{ Exception -> 0x0905 }
        L_0x07a1:
            int r2 = r2 + 1
            goto L_0x078e
        L_0x07a4:
            int r1 = r27.M()     // Catch:{ Exception -> 0x07dc }
            r2 = 2
            if (r1 != r2) goto L_0x07ad
            r6 = 0
            goto L_0x07ae
        L_0x07ad:
            r6 = 1
        L_0x07ae:
            r24 = 0
            r1 = r27
            r2 = r27
            r20 = 3
            r3 = r0
            r4 = r12
            r25 = r13
            r13 = r5
            r5 = r6
            r26 = r9
            r9 = r23
            r6 = r24
            r1.p(r2, r3, r4, r5, r6)     // Catch:{ Exception -> 0x08d0 }
            java.util.Iterator r1 = r12.iterator()     // Catch:{ Exception -> 0x08d0 }
        L_0x07c9:
            boolean r2 = r1.hasNext()     // Catch:{ Exception -> 0x08d0 }
            if (r2 == 0) goto L_0x0832
            java.lang.Object r2 = r1.next()     // Catch:{ Exception -> 0x08d0 }
            qg r2 = (defpackage.qg) r2     // Catch:{ Exception -> 0x08d0 }
            defpackage.ql.a(r7, r0, r2)     // Catch:{ Exception -> 0x08d0 }
            r2.b(r0, r13)     // Catch:{ Exception -> 0x08d0 }
            goto L_0x07c9
        L_0x07dc:
            r0 = move-exception
            r26 = r9
            r25 = r13
            r9 = r23
            r20 = 3
        L_0x07e5:
            r3 = 0
            goto L_0x0911
        L_0x07e8:
            r26 = r9
            r25 = r13
            r9 = r23
            r20 = 3
            r13 = r5
            r2 = 0
        L_0x07f2:
            if (r2 >= r1) goto L_0x0832
            java.util.ArrayList r3 = r7.aI     // Catch:{ Exception -> 0x08d0 }
            java.lang.Object r3 = r3.get(r2)     // Catch:{ Exception -> 0x08d0 }
            qg r3 = (defpackage.qg) r3     // Catch:{ Exception -> 0x08d0 }
            boolean r4 = r3 instanceof defpackage.qh     // Catch:{ Exception -> 0x08d0 }
            if (r4 == 0) goto L_0x0823
            int[] r4 = r3.aq     // Catch:{ Exception -> 0x08d0 }
            r5 = 0
            r6 = r4[r5]     // Catch:{ Exception -> 0x08d0 }
            r5 = 1
            r4 = r4[r5]     // Catch:{ Exception -> 0x08d0 }
            r12 = 2
            if (r6 != r12) goto L_0x080f
            r3.P(r5)     // Catch:{ Exception -> 0x08d0 }
            r6 = r12
        L_0x080f:
            if (r4 != r12) goto L_0x0815
            r3.Q(r5)     // Catch:{ Exception -> 0x08d0 }
            r4 = r12
        L_0x0815:
            r3.b(r0, r13)     // Catch:{ Exception -> 0x08d0 }
            if (r6 != r12) goto L_0x081d
            r3.P(r12)     // Catch:{ Exception -> 0x08d0 }
        L_0x081d:
            if (r4 != r12) goto L_0x082f
            r3.Q(r12)     // Catch:{ Exception -> 0x08d0 }
            goto L_0x082f
        L_0x0823:
            defpackage.ql.a(r7, r0, r3)     // Catch:{ Exception -> 0x08d0 }
            boolean r4 = r3.E()     // Catch:{ Exception -> 0x08d0 }
            if (r4 != 0) goto L_0x082f
            r3.b(r0, r13)     // Catch:{ Exception -> 0x08d0 }
        L_0x082f:
            int r2 = r2 + 1
            goto L_0x07f2
        L_0x0832:
            int r1 = r7.at     // Catch:{ Exception -> 0x08d0 }
            if (r1 <= 0) goto L_0x083c
            r1 = 0
            r2 = 0
            defpackage.kf.l(r7, r0, r1, r2)     // Catch:{ Exception -> 0x08cd }
            goto L_0x083d
        L_0x083c:
            r1 = 0
        L_0x083d:
            int r2 = r7.au     // Catch:{ Exception -> 0x08cd }
            if (r2 <= 0) goto L_0x0845
            r2 = 1
            defpackage.kf.l(r7, r0, r1, r2)     // Catch:{ Exception -> 0x08d0 }
        L_0x0845:
            java.lang.ref.WeakReference r0 = r7.aA     // Catch:{ Exception -> 0x08d0 }
            if (r0 == 0) goto L_0x0865
            java.lang.Object r0 = r0.get()     // Catch:{ Exception -> 0x08d0 }
            if (r0 == 0) goto L_0x0865
            java.lang.ref.WeakReference r0 = r7.aA     // Catch:{ Exception -> 0x08d0 }
            java.lang.Object r0 = r0.get()     // Catch:{ Exception -> 0x08d0 }
            qf r0 = (defpackage.qf) r0     // Catch:{ Exception -> 0x08d0 }
            px r1 = r7.d     // Catch:{ Exception -> 0x08d0 }
            qf r2 = r7.K     // Catch:{ Exception -> 0x08d0 }
            qb r1 = r1.b(r2)     // Catch:{ Exception -> 0x08d0 }
            r7.aa(r0, r1)     // Catch:{ Exception -> 0x08d0 }
            r1 = 0
            r7.aA = r1     // Catch:{ Exception -> 0x08d0 }
        L_0x0865:
            java.lang.ref.WeakReference r0 = r7.aC     // Catch:{ Exception -> 0x08d0 }
            if (r0 == 0) goto L_0x0885
            java.lang.Object r0 = r0.get()     // Catch:{ Exception -> 0x08d0 }
            if (r0 == 0) goto L_0x0885
            java.lang.ref.WeakReference r0 = r7.aC     // Catch:{ Exception -> 0x08d0 }
            java.lang.Object r0 = r0.get()     // Catch:{ Exception -> 0x08d0 }
            qf r0 = (defpackage.qf) r0     // Catch:{ Exception -> 0x08d0 }
            px r1 = r7.d     // Catch:{ Exception -> 0x08d0 }
            qf r2 = r7.M     // Catch:{ Exception -> 0x08d0 }
            qb r1 = r1.b(r2)     // Catch:{ Exception -> 0x08d0 }
            r7.Z(r0, r1)     // Catch:{ Exception -> 0x08d0 }
            r1 = 0
            r7.aC = r1     // Catch:{ Exception -> 0x08d0 }
        L_0x0885:
            java.lang.ref.WeakReference r0 = r7.aB     // Catch:{ Exception -> 0x08d0 }
            if (r0 == 0) goto L_0x08a5
            java.lang.Object r0 = r0.get()     // Catch:{ Exception -> 0x08d0 }
            if (r0 == 0) goto L_0x08a5
            java.lang.ref.WeakReference r0 = r7.aB     // Catch:{ Exception -> 0x08d0 }
            java.lang.Object r0 = r0.get()     // Catch:{ Exception -> 0x08d0 }
            qf r0 = (defpackage.qf) r0     // Catch:{ Exception -> 0x08d0 }
            px r1 = r7.d     // Catch:{ Exception -> 0x08d0 }
            qf r2 = r7.f39J     // Catch:{ Exception -> 0x08d0 }
            qb r1 = r1.b(r2)     // Catch:{ Exception -> 0x08d0 }
            r7.aa(r0, r1)     // Catch:{ Exception -> 0x08d0 }
            r1 = 0
            r7.aB = r1     // Catch:{ Exception -> 0x08d0 }
        L_0x08a5:
            java.lang.ref.WeakReference r0 = r7.aD     // Catch:{ Exception -> 0x08d0 }
            if (r0 == 0) goto L_0x08c6
            java.lang.Object r0 = r0.get()     // Catch:{ Exception -> 0x08d0 }
            if (r0 == 0) goto L_0x08c6
            java.lang.ref.WeakReference r0 = r7.aD     // Catch:{ Exception -> 0x08d0 }
            java.lang.Object r0 = r0.get()     // Catch:{ Exception -> 0x08d0 }
            qf r0 = (defpackage.qf) r0     // Catch:{ Exception -> 0x08d0 }
            px r1 = r7.d     // Catch:{ Exception -> 0x08d0 }
            qf r2 = r7.L     // Catch:{ Exception -> 0x08d0 }
            qb r1 = r1.b(r2)     // Catch:{ Exception -> 0x08d0 }
            r7.Z(r0, r1)     // Catch:{ Exception -> 0x08d0 }
            r3 = 0
            r7.aD = r3     // Catch:{ Exception -> 0x0903 }
            goto L_0x08c7
        L_0x08c6:
            r3 = 0
        L_0x08c7:
            px r0 = r7.d     // Catch:{ Exception -> 0x0903 }
            r0.j()     // Catch:{ Exception -> 0x0903 }
            goto L_0x0926
        L_0x08cd:
            r0 = move-exception
            r3 = r1
            goto L_0x0911
        L_0x08d0:
            r0 = move-exception
            goto L_0x07e5
        L_0x08d3:
            r26 = r9
            r25 = r13
            r9 = r23
            r3 = 0
            r20 = 3
            r13 = r5
            java.util.ArrayList r4 = r7.aI     // Catch:{ Exception -> 0x0903 }
            java.lang.Object r4 = r4.get(r2)     // Catch:{ Exception -> 0x0903 }
            qg r4 = (defpackage.qg) r4     // Catch:{ Exception -> 0x0903 }
            boolean r5 = r4.E()     // Catch:{ Exception -> 0x0903 }
            if (r5 == 0) goto L_0x08f8
            boolean r5 = r4 instanceof defpackage.qm     // Catch:{ Exception -> 0x0903 }
            if (r5 == 0) goto L_0x08f5
            java.util.HashSet r5 = r7.aE     // Catch:{ Exception -> 0x0903 }
            r5.add(r4)     // Catch:{ Exception -> 0x0903 }
            goto L_0x08f8
        L_0x08f5:
            r4.b(r0, r13)     // Catch:{ Exception -> 0x0903 }
        L_0x08f8:
            int r2 = r2 + 1
            r23 = r9
            r5 = r13
            r13 = r25
            r9 = r26
            goto L_0x071a
        L_0x0903:
            r0 = move-exception
            goto L_0x0911
        L_0x0905:
            r0 = move-exception
            goto L_0x06ae
        L_0x0908:
            r0 = move-exception
            r26 = r9
            r25 = r13
            r3 = 0
            r20 = 3
            r9 = r6
        L_0x0911:
            r0.printStackTrace()
            java.io.PrintStream r1 = java.lang.System.out
            r0.toString()
            java.lang.String r0 = r0.toString()
            java.lang.String r2 = "EXCEPTION : "
            java.lang.String r0 = r2.concat(r0)
            r1.println(r0)
        L_0x0926:
            boolean[] r0 = defpackage.ql.a
            r1 = 2
            r2 = 0
            r0[r1] = r2
            r1 = 64
            boolean r0 = r7.W(r1)
            r7.R(r0)
            java.util.ArrayList r2 = r7.aI
            int r2 = r2.size()
            r4 = 0
            r5 = 0
        L_0x093d:
            if (r4 >= r2) goto L_0x095b
            java.util.ArrayList r6 = r7.aI
            java.lang.Object r6 = r6.get(r4)
            qg r6 = (defpackage.qg) r6
            r6.R(r0)
            int r12 = r6.k
            r13 = -1
            if (r12 != r13) goto L_0x0956
            int r6 = r6.l
            if (r6 == r13) goto L_0x0954
            goto L_0x0956
        L_0x0954:
            r6 = 0
            goto L_0x0957
        L_0x0956:
            r6 = 1
        L_0x0957:
            r5 = r5 | r6
            int r4 = r4 + 1
            goto L_0x093d
        L_0x095b:
            r13 = -1
            r0 = 8
            if (r14 == 0) goto L_0x09c5
            if (r9 >= r0) goto L_0x09c5
            boolean[] r2 = defpackage.ql.a
            r4 = 2
            boolean r2 = r2[r4]
            if (r2 == 0) goto L_0x09c5
            r2 = 0
            r4 = 0
            r6 = 0
        L_0x096c:
            if (r2 >= r15) goto L_0x0992
            java.util.ArrayList r12 = r7.aI
            java.lang.Object r12 = r12.get(r2)
            qg r12 = (defpackage.qg) r12
            int r1 = r12.Z
            int r17 = r12.j()
            int r1 = r1 + r17
            int r4 = java.lang.Math.max(r4, r1)
            int r1 = r12.aa
            int r12 = r12.h()
            int r1 = r1 + r12
            int r6 = java.lang.Math.max(r6, r1)
            int r2 = r2 + 1
            r1 = 64
            goto L_0x096c
        L_0x0992:
            int r1 = r7.ac
            int r1 = java.lang.Math.max(r1, r4)
            int r2 = r7.ad
            int r2 = java.lang.Math.max(r2, r6)
            r4 = 2
            if (r10 != r4) goto L_0x09b2
            int r6 = r27.j()
            if (r6 >= r1) goto L_0x09b2
            r7.C(r1)
            int[] r1 = r7.aq
            r5 = 0
            r1[r5] = r4
            r5 = 1
            r21 = 1
        L_0x09b2:
            if (r11 != r4) goto L_0x09c5
            int r1 = r27.h()
            if (r1 >= r2) goto L_0x09c5
            r7.x(r2)
            int[] r1 = r7.aq
            r2 = 1
            r1[r2] = r4
            r5 = 1
            r21 = 1
        L_0x09c5:
            int r1 = r7.ac
            int r2 = r27.j()
            int r1 = java.lang.Math.max(r1, r2)
            int r2 = r27.j()
            if (r1 <= r2) goto L_0x09e3
            r7.C(r1)
            int[] r1 = r7.aq
            r2 = 1
            r4 = 0
            r1[r4] = r2
            r18 = r2
            r21 = r18
            goto L_0x09e6
        L_0x09e3:
            r2 = 1
            r18 = r5
        L_0x09e6:
            int r1 = r7.ad
            int r4 = r27.h()
            int r1 = java.lang.Math.max(r1, r4)
            int r4 = r27.h()
            if (r1 <= r4) goto L_0x0a01
            r7.x(r1)
            int[] r1 = r7.aq
            r1[r2] = r2
            r1 = r2
            r21 = r1
            goto L_0x0a03
        L_0x0a01:
            r1 = r18
        L_0x0a03:
            if (r21 != 0) goto L_0x0a3e
            int[] r4 = r7.aq
            r5 = 0
            r6 = r4[r5]
            r12 = 2
            if (r6 != r12) goto L_0x0a1f
            if (r8 <= 0) goto L_0x0a1f
            int r6 = r27.j()
            if (r6 <= r8) goto L_0x0a1f
            r7.ay = r2
            r4[r5] = r2
            r7.C(r8)
            r1 = r2
            r21 = r1
        L_0x0a1f:
            int[] r4 = r7.aq
            r5 = r4[r2]
            r6 = 2
            if (r5 != r6) goto L_0x0a3b
            if (r26 <= 0) goto L_0x0a3b
            int r5 = r27.h()
            r12 = r26
            if (r5 <= r12) goto L_0x0a41
            r7.az = r2
            r4[r2] = r2
            r7.x(r12)
            r1 = 1
            r21 = 1
            goto L_0x0a41
        L_0x0a3b:
            r12 = r26
            goto L_0x0a41
        L_0x0a3e:
            r12 = r26
            r6 = 2
        L_0x0a41:
            if (r9 <= r0) goto L_0x0a45
            r0 = 0
            goto L_0x0a46
        L_0x0a45:
            r0 = 1
        L_0x0a46:
            r0 = r0 & r1
            r1 = r9
            r9 = r12
            r13 = r25
            r12 = 64
            goto L_0x0657
        L_0x0a4f:
            r1 = r13
            r7.aI = r1
            if (r21 == 0) goto L_0x0a5c
            int[] r0 = r7.aq
            r1 = 0
            r0[r1] = r10
            r1 = 1
            r0[r1] = r11
        L_0x0a5c:
            px r0 = r7.d
            androidx.wear.ambient.AmbientDelegate r0 = r0.j
            r7.S(r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.qh.T():void");
    }

    public final void U(int i) {
        this.ax = i;
        px.a = W(512);
    }

    /* JADX WARNING: Removed duplicated region for block: B:36:0x00d6  */
    /* JADX WARNING: Removed duplicated region for block: B:46:0x00fa  */
    /* JADX WARNING: Removed duplicated region for block: B:73:0x0129 A[SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean V(boolean r13, int r14) {
        /*
            r12 = this;
            qr r0 = r12.a
            qh r1 = r0.a
            r2 = 0
            int r1 = r1.L(r2)
            qh r3 = r0.a
            r4 = 1
            int r3 = r3.L(r4)
            qh r5 = r0.a
            int r6 = r5.k()
            int r5 = r5.l()
            if (r13 == 0) goto L_0x0082
            r13 = 2
            if (r1 == r13) goto L_0x0022
            if (r3 != r13) goto L_0x0082
            r3 = r13
        L_0x0022:
            java.util.ArrayList r7 = r0.e
            int r8 = r7.size()
            r9 = r2
        L_0x0029:
            if (r9 >= r8) goto L_0x0040
            java.lang.Object r10 = r7.get(r9)
            ra r10 = (defpackage.ra) r10
            int r11 = r10.f
            if (r11 != r14) goto L_0x003d
            boolean r10 = r10.e()
            if (r10 != 0) goto L_0x003d
            r7 = r2
            goto L_0x0041
        L_0x003d:
            int r9 = r9 + 1
            goto L_0x0029
        L_0x0040:
            r7 = r4
        L_0x0041:
            if (r14 != 0) goto L_0x0063
            if (r7 == 0) goto L_0x0082
            if (r1 != r13) goto L_0x0082
            qh r13 = r0.a
            r13.P(r4)
            qh r13 = r0.a
            int r7 = r0.a(r13, r2)
            r13.C(r7)
            qh r13 = r0.a
            qx r7 = r13.h
            qt r7 = r7.e
            int r13 = r13.j()
            r7.c(r13)
            goto L_0x0082
        L_0x0063:
            if (r7 == 0) goto L_0x0082
            if (r3 != r13) goto L_0x0082
            qh r13 = r0.a
            r13.Q(r4)
            qh r13 = r0.a
            int r7 = r0.a(r13, r4)
            r13.x(r7)
            qh r13 = r0.a
            qy r7 = r13.i
            qt r7 = r7.e
            int r13 = r13.h()
            r7.c(r13)
        L_0x0082:
            r13 = 4
            if (r14 != 0) goto L_0x00a6
            qh r5 = r0.a
            int[] r7 = r5.aq
            r7 = r7[r2]
            if (r7 == r4) goto L_0x008f
            if (r7 != r13) goto L_0x00b1
        L_0x008f:
            int r13 = r5.j()
            int r13 = r13 + r6
            qx r5 = r5.h
            qs r5 = r5.i
            r5.c(r13)
            qh r5 = r0.a
            qx r5 = r5.h
            qt r5 = r5.e
            int r13 = r13 - r6
            r5.c(r13)
            goto L_0x00c9
        L_0x00a6:
            qh r6 = r0.a
            int[] r7 = r6.aq
            r7 = r7[r4]
            if (r7 == r4) goto L_0x00b3
            if (r7 != r13) goto L_0x00b1
            goto L_0x00b3
        L_0x00b1:
            r13 = r2
            goto L_0x00ca
        L_0x00b3:
            int r13 = r6.h()
            int r13 = r13 + r5
            qy r6 = r6.i
            qs r6 = r6.i
            r6.c(r13)
            qh r6 = r0.a
            qy r6 = r6.i
            qt r6 = r6.e
            int r13 = r13 - r5
            r6.c(r13)
        L_0x00c9:
            r13 = r4
        L_0x00ca:
            r0.c()
            java.util.ArrayList r5 = r0.e
            int r6 = r5.size()
            r7 = r2
        L_0x00d4:
            if (r7 >= r6) goto L_0x00f1
            java.lang.Object r8 = r5.get(r7)
            ra r8 = (defpackage.ra) r8
            int r9 = r8.f
            if (r9 == r14) goto L_0x00e1
            goto L_0x00ee
        L_0x00e1:
            qg r9 = r8.d
            qh r10 = r0.a
            if (r9 != r10) goto L_0x00eb
            boolean r9 = r8.g
            if (r9 == 0) goto L_0x00ee
        L_0x00eb:
            r8.c()
        L_0x00ee:
            int r7 = r7 + 1
            goto L_0x00d4
        L_0x00f1:
            java.util.ArrayList r5 = r0.e
            int r6 = r5.size()
            r7 = r2
        L_0x00f8:
            if (r7 >= r6) goto L_0x0129
            java.lang.Object r8 = r5.get(r7)
            ra r8 = (defpackage.ra) r8
            int r9 = r8.f
            if (r9 == r14) goto L_0x0105
            goto L_0x0126
        L_0x0105:
            if (r13 != 0) goto L_0x010d
            qg r9 = r8.d
            qh r10 = r0.a
            if (r9 == r10) goto L_0x0126
        L_0x010d:
            qs r9 = r8.h
            boolean r9 = r9.i
            if (r9 != 0) goto L_0x0114
            goto L_0x012a
        L_0x0114:
            qs r9 = r8.i
            boolean r9 = r9.i
            if (r9 != 0) goto L_0x011b
            goto L_0x012a
        L_0x011b:
            boolean r9 = r8 instanceof defpackage.qp
            if (r9 != 0) goto L_0x0126
            qt r8 = r8.e
            boolean r8 = r8.i
            if (r8 != 0) goto L_0x0126
            goto L_0x012a
        L_0x0126:
            int r7 = r7 + 1
            goto L_0x00f8
        L_0x0129:
            r2 = r4
        L_0x012a:
            qh r13 = r0.a
            r13.P(r1)
            qh r13 = r0.a
            r13.Q(r3)
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.qh.V(boolean, int):boolean");
    }

    public final boolean W(int i) {
        if ((this.ax & i) == i) {
            return true;
        }
        return false;
    }

    /* access modifiers changed from: package-private */
    public final void a(qg qgVar, int i) {
        if (i == 0) {
            int i2 = this.at + 1;
            qe[] qeVarArr = this.aw;
            int length = qeVarArr.length;
            if (i2 >= length) {
                this.aw = (qe[]) Arrays.copyOf(qeVarArr, length + length);
            }
            qe[] qeVarArr2 = this.aw;
            int i3 = this.at;
            qeVarArr2[i3] = new qe(qgVar, 0, this.c);
            this.at = i3 + 1;
            return;
        }
        int i4 = this.au + 1;
        qe[] qeVarArr3 = this.av;
        int length2 = qeVarArr3.length;
        if (i4 >= length2) {
            this.av = (qe[]) Arrays.copyOf(qeVarArr3, length2 + length2);
        }
        qe[] qeVarArr4 = this.av;
        int i5 = this.au;
        qeVarArr4[i5] = new qe(qgVar, 1, this.c);
        this.au = i5 + 1;
    }

    public final void c() {
        this.a.b = true;
    }

    public final void s() {
        this.d.k();
        this.ar = 0;
        this.as = 0;
        super.s();
    }
}
