package defpackage;

import java.util.List;

/* renamed from: ekl  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ekl implements ecy {
    public final /* synthetic */ ekp a;
    public final /* synthetic */ ecy b;

    public /* synthetic */ ekl(ekp ekp, ecy ecy) {
        this.a = ekp;
        this.b = ecy;
    }

    public final /* synthetic */ hme a(dxy dxy) {
        return dlh.x(this, dxy);
    }

    public final dxz i(dxy dxy, int i) {
        dxz i2;
        ekp ekp = this.a;
        List list = ekp.d;
        ecy ecy = this.b;
        synchronized (list) {
            i2 = ecy.i(dxy, jnu.p(ekp.e.b, i));
            if (!ekp.f.b()) {
                ekp.d.add(i2.b);
            }
        }
        return i2;
    }

    public final /* synthetic */ dxz k(dxy dxy) {
        return doe.f(this, dxy);
    }
}
