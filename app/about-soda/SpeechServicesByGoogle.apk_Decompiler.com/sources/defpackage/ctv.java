package defpackage;

/* renamed from: ctv  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ctv implements Runnable {
    public final /* synthetic */ cuf a;
    public final /* synthetic */ hig b;
    public final /* synthetic */ hme c;
    public final /* synthetic */ cud d;
    public final /* synthetic */ cue e;
    public final /* synthetic */ long f;
    public final /* synthetic */ int g;
    private final /* synthetic */ int h;

    public /* synthetic */ ctv(cuf cuf, long j, hig hig, hme hme, cud cud, cue cue, int i, int i2) {
        this.h = i2;
        this.a = cuf;
        this.f = j;
        this.b = hig;
        this.c = hme;
        this.d = cud;
        this.e = cue;
        this.g = i;
    }

    /* JADX WARNING: type inference failed for: r1v7, types: [htq] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:14:0x003d  */
    /* JADX WARNING: Removed duplicated region for block: B:17:0x0045  */
    /* JADX WARNING: Removed duplicated region for block: B:18:0x0047  */
    /* JADX WARNING: Removed duplicated region for block: B:39:0x0074  */
    /* JADX WARNING: Removed duplicated region for block: B:42:0x009b  */
    /* JADX WARNING: Removed duplicated region for block: B:45:0x00b5  */
    /* JADX WARNING: Removed duplicated region for block: B:48:0x00cd  */
    /* JADX WARNING: Removed duplicated region for block: B:51:0x00e4  */
    /* JADX WARNING: Removed duplicated region for block: B:54:0x00f6  */
    /* JADX WARNING: Removed duplicated region for block: B:57:0x0117  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void run() {
        /*
            r14 = this;
            int r0 = r14.h
            if (r0 == 0) goto L_0x0026
            long r0 = r14.f
            long r2 = defpackage.cqx.F()
            long r11 = r2 - r0
            cuf r0 = r14.a
            hig r6 = r14.b
            hme r7 = r14.c
            cud r8 = r14.d
            cue r9 = r14.e
            int r10 = r14.g
            ctv r1 = new ctv
            r13 = 0
            r4 = r1
            r5 = r0
            r4.<init>((defpackage.cuf) r5, (defpackage.hig) r6, (defpackage.hme) r7, (defpackage.cud) r8, (defpackage.cue) r9, (int) r10, (long) r11, (int) r13)
            java.util.concurrent.Executor r0 = r0.d
            defpackage.ftd.H(r1, r0)
            return
        L_0x0026:
            cud r0 = r14.d
            hme r1 = r14.c
            r2 = 4
            r3 = 5
            java.lang.Object r1 = defpackage.hfc.S(r1)     // Catch:{ all -> 0x0037 }
            int r0 = r0.a(r1)     // Catch:{ all -> 0x0035 }
            goto L_0x0070
        L_0x0035:
            r0 = move-exception
            goto L_0x0039
        L_0x0037:
            r0 = move-exception
            r1 = 0
        L_0x0039:
            boolean r4 = r0 instanceof java.util.concurrent.ExecutionException
            if (r4 == 0) goto L_0x0041
            java.lang.Throwable r0 = r0.getCause()
        L_0x0041:
            boolean r4 = r0 instanceof java.util.concurrent.CancellationException
            if (r4 == 0) goto L_0x0047
            r0 = r3
            goto L_0x0070
        L_0x0047:
            boolean r4 = r0 instanceof java.lang.InterruptedException
            if (r4 == 0) goto L_0x004d
            r0 = 6
            goto L_0x0070
        L_0x004d:
            boolean r4 = r0 instanceof java.io.IOException
            if (r4 == 0) goto L_0x0053
            r0 = 7
            goto L_0x0070
        L_0x0053:
            boolean r4 = r0 instanceof java.lang.IllegalStateException
            if (r4 == 0) goto L_0x005a
            r0 = 8
            goto L_0x0070
        L_0x005a:
            boolean r4 = r0 instanceof java.lang.IllegalArgumentException
            if (r4 == 0) goto L_0x0061
            r0 = 9
            goto L_0x0070
        L_0x0061:
            boolean r4 = r0 instanceof java.lang.UnsupportedOperationException
            if (r4 == 0) goto L_0x0068
            r0 = 10
            goto L_0x0070
        L_0x0068:
            boolean r0 = r0 instanceof defpackage.csi
            if (r0 == 0) goto L_0x006f
            r0 = 11
            goto L_0x0070
        L_0x006f:
            r0 = r2
        L_0x0070:
            hig r4 = r14.b
            if (r1 == 0) goto L_0x008d
            cue r5 = r14.e
            java.lang.Object r3 = r4.C(r3)
            htk r3 = (defpackage.htk) r3
            r3.x(r4)
            hig r1 = r5.a(r1)
            r3.x(r1)
            htq r1 = r3.r()
            r4 = r1
            hig r4 = (defpackage.hig) r4
        L_0x008d:
            hio r1 = defpackage.hio.f
            htk r1 = r1.l()
            htq r3 = r1.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x009e
            r1.u()
        L_0x009e:
            int r3 = r14.g
            htq r5 = r1.b
            r6 = r5
            hio r6 = (defpackage.hio) r6
            int r3 = r3 + -2
            r6.b = r3
            int r3 = r6.a
            r3 = r3 | 1
            r6.a = r3
            boolean r3 = r5.B()
            if (r3 != 0) goto L_0x00b8
            r1.u()
        L_0x00b8:
            htq r3 = r1.b
            r5 = r3
            hio r5 = (defpackage.hio) r5
            int r0 = r0 + -2
            r5.c = r0
            int r0 = r5.a
            r0 = r0 | 2
            r5.a = r0
            boolean r0 = r3.B()
            if (r0 != 0) goto L_0x00d0
            r1.u()
        L_0x00d0:
            long r5 = r14.f
            htq r0 = r1.b
            r3 = r0
            hio r3 = (defpackage.hio) r3
            int r7 = r3.a
            r2 = r2 | r7
            r3.a = r2
            r3.e = r5
            boolean r0 = r0.B()
            if (r0 != 0) goto L_0x00e7
            r1.u()
        L_0x00e7:
            htq r0 = r1.b
            hio r0 = (defpackage.hio) r0
            r4.getClass()
            huf r2 = r0.d
            boolean r3 = r2.c()
            if (r3 != 0) goto L_0x00fc
            huf r2 = defpackage.htq.s(r2)
            r0.d = r2
        L_0x00fc:
            cuf r2 = r14.a
            huf r0 = r0.d
            r0.add(r4)
            htq r0 = r1.r()
            hio r0 = (defpackage.hio) r0
            hii r1 = defpackage.hii.u
            htk r1 = r1.l()
            htq r3 = r1.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x011a
            r1.u()
        L_0x011a:
            cyk r2 = r2.h
            htq r3 = r1.b
            hii r3 = (defpackage.hii) r3
            r0.getClass()
            r3.r = r0
            int r0 = r3.c
            r0 = r0 | 256(0x100, float:3.59E-43)
            r3.c = r0
            ikg r0 = defpackage.ikg.a
            ikh r0 = r0.a()
            long r3 = r0.a()
            int r0 = (int) r3
            long r3 = (long) r0
            r0 = 1110(0x456, float:1.555E-42)
            r2.i(r0, r1, r3)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.ctv.run():void");
    }

    public /* synthetic */ ctv(cuf cuf, hig hig, hme hme, cud cud, cue cue, int i, long j, int i2) {
        this.h = i2;
        this.a = cuf;
        this.b = hig;
        this.c = hme;
        this.d = cud;
        this.e = cue;
        this.g = i;
        this.f = j;
    }
}
