package defpackage;

/* renamed from: epp  reason: default package */
/* compiled from: PG */
public final class epp {
    public final int a;
    public final ejn b;

    public epp(int i, ejn ejn) {
        jnu.e(ejn, "routeData");
        this.a = i;
        this.b = ejn;
    }

    public final String a() {
        String r = fbi.r(this.b.b);
        return "route(token(" + this.a + "), " + r + ")";
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof epp)) {
            return false;
        }
        epp epp = (epp) obj;
        if (this.a == epp.a && jnu.i(this.b, epp.b)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (this.a * 31) + this.b.hashCode();
    }

    public final String toString() {
        return "SourceRouteData(routeToken=" + this.a + ", routeData=" + this.b + ")";
    }
}
