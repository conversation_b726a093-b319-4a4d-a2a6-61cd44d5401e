package defpackage;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

/* renamed from: cog  reason: default package */
/* compiled from: PG */
public final class cog extends hlq implements hmg {
    public volatile hmg a;

    public cog(hme hme, hmg hmg) {
        super(hme);
        this.a = hmg;
        hme.c(new cmp(this, 6), hld.a);
    }

    public final /* bridge */ /* synthetic */ int compareTo(Object obj) {
        return this.a.compareTo((Delayed) obj);
    }

    public final long getDelay(TimeUnit timeUnit) {
        return this.a.getDelay(timeUnit);
    }
}
