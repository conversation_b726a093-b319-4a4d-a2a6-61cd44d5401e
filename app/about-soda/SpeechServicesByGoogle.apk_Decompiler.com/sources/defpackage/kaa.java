package defpackage;

/* renamed from: kaa  reason: default package */
/* compiled from: PG */
public final class kaa extends htq implements hvb {
    public static final kaa f;
    private static volatile hvh g;
    public int a;
    public int b = 0;
    public Object c;
    public int d;
    public huf e = hvk.a;

    static {
        kaa kaa = new kaa();
        f = kaa;
        htq.z(kaa.class, kaa);
    }

    private kaa() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(f, "\u0001\u0004\u0001\u0001\u0001\u0004\u0004\u0000\u0001\u0000\u0001᠌\u0000\u00028\u0000\u0003<\u0000\u0004\u001b", new Object[]{"c", "b", "a", "d", jzz.a, jzx.class, "e", jzy.class});
        } else if (i2 == 3) {
            return new kaa();
        } else {
            if (i2 == 4) {
                return new htk((htq) f);
            }
            if (i2 == 5) {
                return f;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = g;
            if (hvh == null) {
                synchronized (kaa.class) {
                    hvh = g;
                    if (hvh == null) {
                        hvh = new htl(f);
                        g = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
