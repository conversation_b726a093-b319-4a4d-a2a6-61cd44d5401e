package j$.time;

import j$.desugar.sun.nio.fs.c;
import j$.time.temporal.ChronoUnit;
import j$.time.temporal.TemporalAmount;
import java.io.InvalidObjectException;
import java.io.ObjectInput;
import java.io.ObjectInputStream;
import java.io.ObjectOutput;
import java.io.Serializable;
import java.util.regex.Pattern;

public final class r implements TemporalAmount, Serializable {
    public static final r d = new r(0, 0, 0);
    private static final long serialVersionUID = -3587258372562876L;
    private final int a;
    private final int b;
    private final int c;

    static {
        Pattern.compile("([-+]?)P(?:([-+]?[0-9]+)Y)?(?:([-+]?[0-9]+)M)?(?:([-+]?[0-9]+)W)?(?:([-+]?[0-9]+)D)?", 2);
        c.a(new Object[]{ChronoUnit.YEARS, ChronoUnit.MONTHS, ChronoUnit.DAYS});
    }

    private r(int i, int i2, int i3) {
        this.a = i;
        this.b = i2;
        this.c = i3;
    }

    static r a(ObjectInput objectInput) {
        int readInt = objectInput.readInt();
        int readInt2 = objectInput.readInt();
        int readInt3 = objectInput.readInt();
        if ((readInt | readInt2 | readInt3) == 0) {
            return d;
        }
        return new r(readInt, readInt2, readInt3);
    }

    private void readObject(ObjectInputStream objectInputStream) {
        throw new InvalidObjectException("Deserialization via serialization delegate");
    }

    private Object writeReplace() {
        return new s((byte) 14, this);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:5:0x0009, code lost:
        r5 = (j$.time.r) r5;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean equals(java.lang.Object r5) {
        /*
            r4 = this;
            r0 = 1
            if (r4 != r5) goto L_0x0004
            return r0
        L_0x0004:
            boolean r1 = r5 instanceof j$.time.r
            r2 = 0
            if (r1 == 0) goto L_0x0020
            j$.time.r r5 = (j$.time.r) r5
            int r1 = r5.a
            int r3 = r4.a
            if (r3 != r1) goto L_0x001e
            int r1 = r4.b
            int r3 = r5.b
            if (r1 != r3) goto L_0x001e
            int r1 = r4.c
            int r5 = r5.c
            if (r1 != r5) goto L_0x001e
            goto L_0x001f
        L_0x001e:
            r0 = 0
        L_0x001f:
            return r0
        L_0x0020:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: j$.time.r.equals(java.lang.Object):boolean");
    }

    /* JADX WARNING: Code restructure failed: missing block: B:10:0x0042, code lost:
        r2 = (((long) r0) * 12) + ((long) r1);
        r7 = r7;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:11:0x004d, code lost:
        if (r2 == 0) goto L_0x0055;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:12:0x004f, code lost:
        r7 = r7.m(r2, j$.time.temporal.ChronoUnit.MONTHS);
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final j$.time.temporal.k g(j$.time.Instant r7) {
        /*
            r6 = this;
            java.lang.String r0 = "temporal"
            j$.util.Objects.requireNonNull(r7, r0)
            j$.time.temporal.r r0 = j$.time.temporal.l.e()
            java.lang.Object r0 = r7.F(r0)
            j$.time.chrono.n r0 = (j$.time.chrono.n) r0
            if (r0 == 0) goto L_0x0032
            j$.time.chrono.u r1 = j$.time.chrono.u.d
            boolean r1 = r1.equals(r0)
            if (r1 == 0) goto L_0x001a
            goto L_0x0032
        L_0x001a:
            j$.time.c r7 = new j$.time.c
            java.lang.String r0 = r0.l()
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            java.lang.String r2 = "Chronology mismatch, expected: ISO, actual: "
            r1.<init>(r2)
            r1.append(r0)
            java.lang.String r0 = r1.toString()
            r7.<init>(r0)
            throw r7
        L_0x0032:
            int r0 = r6.a
            int r1 = r6.b
            if (r1 != 0) goto L_0x0042
            if (r0 == 0) goto L_0x0055
            long r0 = (long) r0
            j$.time.temporal.ChronoUnit r2 = j$.time.temporal.ChronoUnit.YEARS
            j$.time.temporal.k r7 = r7.m(r0, r2)
            goto L_0x0055
        L_0x0042:
            long r2 = (long) r0
            r4 = 12
            long r2 = r2 * r4
            long r0 = (long) r1
            long r2 = r2 + r0
            r0 = 0
            int r4 = (r2 > r0 ? 1 : (r2 == r0 ? 0 : -1))
            if (r4 == 0) goto L_0x0055
            j$.time.temporal.ChronoUnit r0 = j$.time.temporal.ChronoUnit.MONTHS
            j$.time.temporal.k r7 = r7.m(r2, r0)
        L_0x0055:
            int r0 = r6.c
            if (r0 == 0) goto L_0x0062
            long r0 = (long) r0
            j$.time.temporal.ChronoUnit r2 = j$.time.temporal.ChronoUnit.DAYS
            j$.time.Instant r7 = (j$.time.Instant) r7
            j$.time.temporal.k r7 = r7.m(r0, r2)
        L_0x0062:
            return r7
        */
        throw new UnsupportedOperationException("Method not decompiled: j$.time.r.g(j$.time.Instant):j$.time.temporal.k");
    }

    public final int hashCode() {
        return Integer.rotateLeft(this.c, 16) + Integer.rotateLeft(this.b, 8) + this.a;
    }

    public final String toString() {
        if (this == d) {
            return "P0D";
        }
        StringBuilder sb = new StringBuilder("P");
        int i = this.a;
        if (i != 0) {
            sb.append(i);
            sb.append('Y');
        }
        int i2 = this.b;
        if (i2 != 0) {
            sb.append(i2);
            sb.append('M');
        }
        int i3 = this.c;
        if (i3 != 0) {
            sb.append(i3);
            sb.append('D');
        }
        return sb.toString();
    }

    /* access modifiers changed from: package-private */
    public final void writeExternal(ObjectOutput objectOutput) {
        objectOutput.writeInt(this.a);
        objectOutput.writeInt(this.b);
        objectOutput.writeInt(this.c);
    }
}
