package defpackage;

import android.net.Uri;
import android.os.Parcel;
import android.os.RemoteException;
import android.util.Log;
import com.google.android.apps.speech.tts.googletts.audio.AndroidDecoder;
import java.io.File;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.TimeoutException;

/* renamed from: brg  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class brg implements gqx {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ brg(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r2v21, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v22, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v100, types: [android.content.SharedPreferences, java.lang.Object] */
    public final Object apply(Object obj) {
        bty bty;
        boolean z;
        boolean z2 = false;
        switch (this.b) {
            case 0:
                IllegalArgumentException illegalArgumentException = (IllegalArgumentException) obj;
                AndroidDecoder androidDecoder = (AndroidDecoder) this.a;
                androidDecoder.jniDecoderStopped(androidDecoder.c, iff.COMPOSITION_STATUS_ERROR.g);
                androidDecoder.jniDecoderCompleted(androidDecoder.c);
                androidDecoder.directAudioBuffer.clear();
                return null;
            case 1:
                Object obj2 = this.a;
                boz boz = (boz) obj;
                String str = bqa.a;
                try {
                    boy a2 = ((bpq) obj2).a();
                    Parcel a3 = boz.a();
                    box.c(a3, a2);
                    Parcel b2 = boz.b(3, a3);
                    int readInt = b2.readInt();
                    b2.recycle();
                    return Integer.valueOf(readInt);
                } catch (RemoteException e) {
                    Log.e(bqa.a, "AiCore service failed to get feature status for ".concat(((bpq) obj2).a), e);
                    return 0;
                }
            case 2:
                TimeoutException timeoutException = (TimeoutException) obj;
                ((hby) ((hby) brm.a.h()).j("com/google/android/apps/speech/tts/googletts/audio/ExoPlayerDecoder", "decodeWithDataSource", 328, "ExoPlayerDecoder.java")).r("Decoder timed out");
                ((brm) this.a).b(iff.COMPOSITION_STATUS_DEADLINE_EXCEEDED_ERROR, "timeout");
                return null;
            case 3:
                HashMap hashMap = new HashMap();
                for (String str2 : (List) obj) {
                    Object obj3 = this.a;
                    ((hby) ((hby) btf.a.f()).j("com/google/android/apps/speech/tts/googletts/local/voicepack/VoiceDataDownloader", "getAvailableVoicesInfo", 509, "VoiceDataDownloader.java")).u("getAvailableVoicesInfo %s", str2);
                    btf btf = (btf) obj3;
                    Iterator it = ((btz) btf.r.b).b.iterator();
                    while (true) {
                        if (it.hasNext()) {
                            bty bty2 = (bty) it.next();
                            if (bty2.b.equals(str2)) {
                                bty = bty2;
                            }
                        } else {
                            bty = null;
                        }
                    }
                    fvf.aP(bty);
                    if (bty == null || (bty.a & 1) == 0) {
                        ((hby) ((hby) btf.a.f()).j("com/google/android/apps/speech/tts/googletts/local/voicepack/VoiceDataDownloader", "getAvailableVoicesInfo", 513, "VoiceDataDownloader.java")).u("Failed to get voice metadata for: %s", str2);
                    } else {
                        boolean z3 = !bzw.d(bty);
                        String e2 = grd.c(File.separatorChar).e(btf.f(btf.c), bty.b, new Object[0]);
                        long j = (long) bty.e;
                        Iterator it2 = bty.f.iterator();
                        while (true) {
                            if (!it2.hasNext()) {
                                z = true;
                            } else if (bzw.f((btx) it2.next())) {
                                z = false;
                            }
                        }
                        hashMap.put(str2, new bss(bty, j * 1024, z3, z, e2, gxq.o(brv.d(bty))));
                    }
                }
                return hashMap;
            case 4:
                Void voidR = (Void) obj;
                byr byr = (byr) this.a;
                byr.g.J(hma.a, "LanguagePackSettings:installedPacks");
                byr.g.J(hma.a, "LanguagePackSettings:supportedPacks");
                return true;
            case 5:
                byr byr2 = (byr) this.a;
                byr2.g.J(hma.a, "LanguagePackSettings:installedPacks");
                byr2.g.J(hma.a, "LanguagePackSettings:supportedPacks");
                return Boolean.valueOf(!((exo) obj).equals(exl.a));
            case 6:
                crw crw = (crw) obj;
                Object obj4 = this.a;
                if (crw != null) {
                    ((gxl) obj4).h(crw);
                }
                return obj4;
            case 7:
                crw crw2 = (crw) obj;
                if (crw2 != null) {
                    Object obj5 = this.a;
                    hig k = cuf.k(crw2);
                    htk l = hii.u.l();
                    htk l2 = his.c.l();
                    if (!l2.b.B()) {
                        l2.u();
                    }
                    his his = (his) l2.b;
                    k.getClass();
                    his.b = k;
                    his.a |= 1;
                    if (!l.b.B()) {
                        l.u();
                    }
                    cyk cyk = ((cuf) obj5).h;
                    hii hii = (hii) l.b;
                    his his2 = (his) l2.r();
                    his2.getClass();
                    hii.o = his2;
                    hii.c |= 1;
                    cyk.i(1071, l, (long) cqh.o());
                }
                return crw2;
            case 8:
                List<cxg> list = (List) obj;
                gxl gxl = new gxl();
                if (((csr) this.a).a) {
                    gxl.j(list);
                    return gxl.g();
                }
                for (cxg cxg : list) {
                    ctg ctg = cxg.a;
                    csx csx = cxg.b;
                    gxl.h(cxg);
                }
                return gxl.g();
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return new cxh((csx) this.a, (csx) obj);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                Void voidR2 = (Void) obj;
                return (crw) ((htk) this.a).r();
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                if (!((Boolean) obj).booleanValue()) {
                    ((cyk) ((dmd) this.a).a).d(1036);
                    cyh.g("%s: Failed to remove expired groups!", "ExpirationHandler");
                }
                return null;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                Uri uri = (Uri) obj;
                if (uri != null) {
                    this.a.add(uri);
                }
                return null;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                this.a.addAll((List) obj);
                return null;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                Void voidR3 = (Void) obj;
                return this.a;
            case 15:
                boolean booleanValue = ((Boolean) obj).booleanValue();
                Object obj6 = this.a;
                csw csw = ((csx) obj6).b;
                if (csw == null) {
                    csw = csw.i;
                }
                htk htk = (htk) csw.C(5);
                htk.x(csw);
                if (!htk.b.B()) {
                    htk.u();
                }
                csw csw2 = (csw) htk.b;
                csw2.a |= 64;
                csw2.h = booleanValue;
                csw csw3 = (csw) htk.r();
                htq htq = (htq) obj6;
                htk htk2 = (htk) htq.C(5);
                htk2.x(htq);
                if (!htk2.b.B()) {
                    htk2.u();
                }
                csx csx2 = (csx) htk2.b;
                csw3.getClass();
                csx2.b = csw3;
                csx2.a |= 1;
                return (csx) htk2.r();
            case 16:
                Iterator it3 = ((List) obj).iterator();
                while (true) {
                    Object obj7 = this.a;
                    if (!it3.hasNext()) {
                        return ((gym) obj7).g();
                    }
                    csx csx3 = ((cxg) it3.next()).b;
                    for (csv csv : csx3.n) {
                        int x = a.x(csx3.i);
                        if (x == 0) {
                            x = 1;
                        }
                        ((gym) obj7).c(cqh.t(csv, x));
                    }
                }
            case 17:
                if (((Boolean) obj).booleanValue() && ((Boolean) this.a).booleanValue()) {
                    z2 = true;
                }
                return Boolean.valueOf(z2);
            case 18:
                if (((Boolean) obj).booleanValue() && ((Boolean) this.a).booleanValue()) {
                    z2 = true;
                }
                return Boolean.valueOf(z2);
            case 19:
                return ((cwm) this.a).b((csx) obj);
            default:
                Void voidR4 = (Void) obj;
                boolean z4 = cwm.a;
                this.a.edit().putBoolean("mdd_migrated_to_offroad", true).commit();
                return null;
        }
    }
}
