package defpackage;

/* renamed from: erz  reason: default package */
/* compiled from: PG */
public final class erz implements erq {
    public po a;
    private final dzq b;
    private final hme c;

    public erz(dzy dzy) {
        htk l = dzq.c.l();
        if (!l.b.B()) {
            l.u();
        }
        dzq dzq = (dzq) l.b;
        dzy.getClass();
        dzq.b = dzy;
        dzq.a = 3;
        this.b = (dzq) l.r();
        this.c = kq.f(new bwl(this, 6));
    }

    public final ejn a() {
        gqd gqd = gqd.a;
        return new ejn(this.c, this.b, gqd, gqd);
    }

    public final synchronized hme b() {
        if (this.c.isDone()) {
            return eki.k(eaf.FAILED_ROUTING_DUE_TO_DISCONNECT_ALREADY_CALLED);
        }
        return eki.k(eaf.UPDATED);
    }

    public final synchronized hme c(dzx dzx) {
        if (this.c.isDone()) {
            return this.c;
        }
        htk l = dzm.c.l();
        if (!l.b.B()) {
            l.u();
        }
        dzm dzm = (dzm) l.b;
        dzm.b = dzx.H;
        dzm.a |= 1;
        this.a.c((dzm) l.r());
        return this.c;
    }

    public final /* synthetic */ ekt d() {
        return null;
    }

    public final /* synthetic */ erq e() {
        return this;
    }
}
