package defpackage;

import j$.util.DesugarTimeZone;
import java.util.GregorianCalendar;
import java.util.Random;
import java.util.TimeZone;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReference;

/* renamed from: cyn  reason: default package */
/* compiled from: PG */
public final class cyn implements cyi {
    public static final /* synthetic */ int d = 0;
    private static final TimeZone e = DesugarTimeZone.getTimeZone("UTC");
    public final Executor a;
    public final Random b;
    public final fps c;

    public cyn(fps fps, Executor executor, Random random) {
        this.c = fps;
        this.a = executor;
        this.b = random;
    }

    public static long e(long j) {
        GregorianCalendar gregorianCalendar = new GregorianCalendar(e);
        gregorianCalendar.setTimeInMillis(j);
        gregorianCalendar.set(11, 0);
        gregorianCalendar.set(12, 0);
        gregorianCalendar.set(13, 0);
        gregorianCalendar.set(14, 0);
        return gregorianCalendar.getTimeInMillis();
    }

    public final hme a() {
        int i = gxq.d;
        AtomicReference atomicReference = new AtomicReference(hal.a);
        return ftd.K(this.c.b(new cyg(atomicReference, 5), this.a), gof.b(new cyg(atomicReference, 6)), this.a);
    }

    public final hme b() {
        AtomicReference atomicReference = new AtomicReference(gqd.a);
        return ftd.K(this.c.b(new cyg(atomicReference, 3), hld.a), new cyg(atomicReference, 4), hld.a);
    }

    public final hme c() {
        return ftd.L(this.c.a(), new cxr(this, 4), this.a);
    }

    public final hme d(ctb ctb) {
        return this.c.b(new cyg(ctb, 7), this.a);
    }
}
