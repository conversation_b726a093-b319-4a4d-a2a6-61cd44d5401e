package defpackage;

import android.view.View;
import android.view.ViewGroup;

/* renamed from: bnd  reason: default package */
/* compiled from: PG */
public final class bnd implements View.OnAttachStateChangeListener {
    final /* synthetic */ Object a;
    final /* synthetic */ Object b;
    private final /* synthetic */ int c;

    public bnd(bl blVar, cb cbVar, int i) {
        this.c = i;
        this.a = blVar;
        this.b = cbVar;
    }

    /* JADX WARNING: type inference failed for: r0v0, types: [java.lang.Object, android.view.View$OnApplyWindowInsetsListener] */
    public final void onViewAttachedToWindow(View view) {
        if (this.c != 0) {
            cb cbVar = (cb) this.b;
            cbVar.d();
            cz.c((ViewGroup) cbVar.a.P.getParent(), ((bl) this.a).a).h();
            return;
        }
        ((bne) this.b).d.getRootView().setOnApplyWindowInsetsListener(this.a);
        ((bne) this.b).d.removeOnAttachStateChangeListener(this);
    }

    public bnd(bne bne, View.OnApplyWindowInsetsListener onApplyWindowInsetsListener, int i) {
        this.c = i;
        this.a = onApplyWindowInsetsListener;
        this.b = bne;
    }

    public final void onViewDetachedFromWindow(View view) {
    }
}
