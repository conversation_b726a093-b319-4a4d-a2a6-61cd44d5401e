package defpackage;

import android.os.Handler;
import android.os.Looper;
import java.util.concurrent.Executor;

/* renamed from: bpn  reason: default package */
/* compiled from: PG */
public final class bpn implements Executor {
    public static final bpn a = new bpn();
    private final Handler b = new Handler(Looper.getMainLooper());

    public final void execute(Runnable runnable) {
        this.b.post(runnable);
    }
}
