package defpackage;

import com.google.android.apps.speech.tts.googletts.audio.AndroidDecoder;
import java.util.concurrent.ExecutorService;

/* renamed from: bri  reason: default package */
/* compiled from: PG */
public final class bri implements iiu {
    private final jjk a;
    private final jjk b;

    public bri(jjk jjk, jjk jjk2) {
        this.a = jjk;
        this.b = jjk2;
    }

    /* renamed from: a */
    public final AndroidDecoder b() {
        brm a2 = ((brn) this.a).b();
        buf.c().intValue();
        return new AndroidDecoder(a2, (ExecutorService) this.b.b());
    }
}
