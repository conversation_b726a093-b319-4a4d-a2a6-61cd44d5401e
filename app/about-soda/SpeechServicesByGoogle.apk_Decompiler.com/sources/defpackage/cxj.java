package defpackage;

import android.accounts.AccountManager;
import android.content.Context;
import android.content.pm.ProviderInfo;
import android.net.Uri;
import androidx.wear.ambient.AmbientModeSupport;
import com.google.android.gms.common.api.Status;
import com.google.android.gms.common.api.internal.BasePendingResult;
import j$.util.DesugarCollections;
import j$.util.Optional;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.WeakHashMap;

/* renamed from: cxj  reason: default package */
/* compiled from: PG */
public final class cxj {
    public final Object a;
    public final Object b;

    public cxj(Uri uri, ProviderInfo providerInfo) {
        this.a = uri;
        this.b = providerInfo;
    }

    public static cxj b(long j) {
        return new cxj((Object) Long.TYPE, (Object) Long.valueOf(j));
    }

    /* JADX WARNING: Missing exception handler attribute for start block: B:24:0x0096 */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static final defpackage.kai d(boolean r7, defpackage.dlc r8) {
        /*
            kai r0 = defpackage.kai.h
            htk r0 = r0.l()
            long r1 = android.os.Process.getElapsedCpuTime()
            htq r3 = r0.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x0015
            r0.u()
        L_0x0015:
            htq r3 = r0.b
            r4 = r3
            kai r4 = (defpackage.kai) r4
            int r5 = r4.a
            r6 = 1
            r5 = r5 | r6
            r4.a = r5
            r4.b = r1
            boolean r1 = r3.B()
            if (r1 != 0) goto L_0x002b
            r0.u()
        L_0x002b:
            htq r1 = r0.b
            kai r1 = (defpackage.kai) r1
            int r2 = r1.a
            r2 = r2 | 2
            r1.a = r2
            r1.c = r7
            int r7 = java.lang.Thread.activeCount()
            htq r1 = r0.b
            boolean r1 = r1.B()
            if (r1 != 0) goto L_0x0046
            r0.u()
        L_0x0046:
            htq r1 = r0.b
            kai r1 = (defpackage.kai) r1
            int r2 = r1.a
            r2 = r2 | 4
            r1.a = r2
            r1.d = r7
            int r7 = android.os.Process.myPid()
            java.util.Locale r1 = java.util.Locale.US
            java.lang.Integer r2 = java.lang.Integer.valueOf(r7)
            java.lang.Object[] r3 = new java.lang.Object[r6]
            r4 = 0
            r3[r4] = r2
            java.lang.String r2 = "/proc/%d/oom_score_adj"
            java.lang.String r1 = java.lang.String.format(r1, r2, r3)
            android.os.StrictMode$ThreadPolicy r2 = android.os.StrictMode.allowThreadDiskReads()
            java.io.RandomAccessFile r3 = new java.io.RandomAccessFile     // Catch:{ IOException -> 0x0096 }
            java.lang.String r4 = "r"
            r3.<init>(r1, r4)     // Catch:{ IOException -> 0x0096 }
            java.lang.String r1 = r3.readLine()     // Catch:{ all -> 0x0089 }
            grh r1 = defpackage.grh.g(r1)     // Catch:{ all -> 0x0089 }
            cwr r4 = new cwr     // Catch:{ all -> 0x0089 }
            r5 = 17
            r4.<init>(r5)     // Catch:{ all -> 0x0089 }
            grh r1 = r1.a(r4)     // Catch:{ all -> 0x0089 }
            r3.close()     // Catch:{ IOException -> 0x0096 }
            goto L_0x0098
        L_0x0089:
            r1 = move-exception
            r3.close()     // Catch:{ all -> 0x008e }
            goto L_0x0092
        L_0x008e:
            r3 = move-exception
            r1.addSuppressed(r3)     // Catch:{ IOException -> 0x0096 }
        L_0x0092:
            throw r1     // Catch:{ IOException -> 0x0096 }
        L_0x0093:
            r7 = move-exception
            goto L_0x011a
        L_0x0096:
            gqd r1 = defpackage.gqd.a     // Catch:{ all -> 0x0093 }
        L_0x0098:
            android.os.StrictMode.setThreadPolicy(r2)
            boolean r2 = r1.f()
            if (r2 == 0) goto L_0x00c2
            java.lang.Object r1 = r1.b()
            java.lang.Integer r1 = (java.lang.Integer) r1
            int r1 = r1.intValue()
            htq r2 = r0.b
            boolean r2 = r2.B()
            if (r2 != 0) goto L_0x00b6
            r0.u()
        L_0x00b6:
            htq r2 = r0.b
            kai r2 = (defpackage.kai) r2
            int r3 = r2.a
            r3 = r3 | 16
            r2.a = r3
            r2.f = r1
        L_0x00c2:
            boolean r1 = r8.a
            if (r1 != 0) goto L_0x00c9
            gqd r7 = defpackage.gqd.a
            goto L_0x00e9
        L_0x00c9:
            gxq r8 = r8.a()
            dla r1 = new dla
            r1.<init>(r7)
            grh r7 = defpackage.fvf.O(r8, r1)
            cwr r8 = new cwr
            r1 = 14
            r8.<init>(r1)
            grh r7 = r7.a(r8)
            gqd r8 = defpackage.gqd.a
            java.lang.Object r7 = r7.d(r8)
            grh r7 = (defpackage.grh) r7
        L_0x00e9:
            boolean r8 = r7.f()
            if (r8 == 0) goto L_0x0113
            java.lang.Object r7 = r7.b()
            android.content.ComponentName r7 = (android.content.ComponentName) r7
            java.lang.String r7 = r7.flattenToString()
            htq r8 = r0.b
            boolean r8 = r8.B()
            if (r8 != 0) goto L_0x0104
            r0.u()
        L_0x0104:
            htq r8 = r0.b
            kai r8 = (defpackage.kai) r8
            r7.getClass()
            int r1 = r8.a
            r1 = r1 | 32
            r8.a = r1
            r8.g = r7
        L_0x0113:
            htq r7 = r0.r()
            kai r7 = (defpackage.kai) r7
            return r7
        L_0x011a:
            android.os.StrictMode.setThreadPolicy(r2)
            throw r7
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cxj.d(boolean, dlc):kai");
    }

    /* JADX WARNING: type inference failed for: r2v0, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v0, types: [java.util.Map, java.lang.Object] */
    public final void a(boolean z, Status status) {
        HashMap hashMap;
        HashMap hashMap2;
        synchronized (this.a) {
            hashMap = new HashMap(this.a);
        }
        synchronized (this.b) {
            hashMap2 = new HashMap(this.b);
        }
        for (Map.Entry entry : hashMap.entrySet()) {
            if (z || ((Boolean) entry.getValue()).booleanValue()) {
                ((BasePendingResult) entry.getKey()).j(status);
            }
        }
        for (Map.Entry entry2 : hashMap2.entrySet()) {
            if (z || ((Boolean) entry2.getValue()).booleanValue()) {
                ((byw) entry2.getKey()).d(new cck(status));
            }
        }
    }

    public final kai c() {
        dlc a2 = dlb.a((Context) this.a);
        return d(((bmu) this.b).z(a2), a2);
    }

    public final void e(File file, int i) {
        String str;
        File parentFile = file.getParentFile();
        fvf.aP(parentFile);
        dcd dcd = new dcd(parentFile.getName(), file.getName());
        Object obj = this.a;
        try {
            ddt c = ((dfl) ((AmbientModeSupport.AmbientController) obj).a).c(dcd);
            if (c != null) {
                str = ((dci) c).a;
            } else {
                str = null;
            }
            ((dfl) ((AmbientModeSupport.AmbientController) obj).a).f.f(str);
        } catch (IOException e) {
            ((hdc) ((hdc) ((hdc) dcs.a.g()).i(e)).j("com/google/android/libraries/micore/superpacks/gc/FileManager$FileRefTableListener", "onFileRefChanged", 1496, "FileManager.java")).u("Failed to delete released file %s", file);
        }
        Object obj2 = ((AmbientModeSupport.AmbientController) obj).a;
        ((dfl) obj2).g.a(new dfk(dcd, i, 0));
    }

    /* JADX WARNING: type inference failed for: r1v0, types: [java.util.Map, java.lang.Object] */
    public final boolean f(File file) {
        boolean containsKey;
        synchronized (this.b) {
            containsKey = this.b.containsKey(file);
        }
        return containsKey;
    }

    public final exo g(String str) {
        return (exo) ((HashMap) this.b).get(str);
    }

    public final Optional h(String str) {
        return (Optional) ((HashMap) this.a).get(str);
    }

    public final void i(String str) {
        ((HashMap) this.a).remove(str);
        ((HashMap) this.b).remove(str);
    }

    public cxj(bmu bmu, Context context) {
        this.b = bmu;
        this.a = context;
    }

    public cxj(Object obj, Object obj2) {
        this.a = obj;
        this.b = obj2;
    }

    public cxj() {
        this.a = DesugarCollections.synchronizedMap(new WeakHashMap());
        this.b = DesugarCollections.synchronizedMap(new WeakHashMap());
    }

    public cxj(Context context, byte[] bArr) {
        this.b = new dlm();
        this.a = context;
    }

    public cxj(bql bql, grh grh) {
        jnu.e(bql, "enqueueStatus");
        htk l = bqm.c.l();
        if (!l.b.B()) {
            l.u();
        }
        bqm bqm = (bqm) l.b;
        bqm.b = bql.f;
        bqm.a |= 1;
        this.b = (bqm) l.r();
        this.a = grh;
    }

    public cxj(char[] cArr) {
        this.a = new HashMap();
        this.b = new HashMap();
    }

    public cxj(Context context) {
        this.b = context.getPackageName();
        this.a = AccountManager.get(context);
    }

    public cxj(grh grh, gsb gsb) {
        this.a = grh;
        this.b = fvf.as(gsb);
    }

    public cxj(AmbientModeSupport.AmbientController ambientController) {
        this.b = new HashMap();
        this.a = ambientController;
    }

    public cxj(byte[] bArr) {
        this.b = new WeakHashMap();
        this.a = new dkg(this);
    }
}
