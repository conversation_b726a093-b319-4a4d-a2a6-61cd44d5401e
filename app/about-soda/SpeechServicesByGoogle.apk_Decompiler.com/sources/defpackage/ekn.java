package defpackage;

/* renamed from: ekn  reason: default package */
/* compiled from: PG */
final class ekn extends jme {
    /* synthetic */ Object a;
    final /* synthetic */ ekp b;
    int c;
    ekp d;
    jqh e;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public ekn(ekp ekp, jlr jlr) {
        super(jlr);
        this.b = ekp;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.c |= Integer.MIN_VALUE;
        return this.b.h(this);
    }
}
