package defpackage;

import androidx.work.CoroutineWorker;
import androidx.work.impl.workers.ConstraintTrackingWorker;
import java.util.concurrent.CancellationException;

/* renamed from: bas  reason: default package */
/* compiled from: PG */
public final class bas extends jmi implements jne {
    int a;
    final /* synthetic */ Object b;
    private final /* synthetic */ int c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bas(CoroutineWorker coroutineWorker, jlr jlr, int i) {
        super(2, jlr);
        this.c = i;
        this.b = coroutineWorker;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        int i = this.c;
        if (i == 0) {
            jlr c2 = c((jqs) obj, (jlr) obj2);
            jkd jkd = jkd.a;
            ((bas) c2).bk(jkd);
            return jkd;
        } else if (i == 1) {
            return ((bas) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        } else if (i == 2) {
            return ((bas) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        } else if (i == 3) {
            return ((bas) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        } else if (i != 4) {
            return ((bas) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        } else {
            return ((bas) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
    }

    /* JADX WARNING: type inference failed for: r3v4, types: [jlv, java.lang.Object] */
    public final Object bk(Object obj) {
        we weVar;
        int i = this.c;
        if (i != 0) {
            if (i == 1) {
                Object obj2 = jlx.COROUTINE_SUSPENDED;
                if (this.a != 0) {
                    jji.c(obj);
                } else {
                    jji.c(obj);
                    Object obj3 = this.b;
                    this.a = 1;
                    if (((auk) obj3).a(this) == obj2) {
                        return obj2;
                    }
                }
                return jkd.a;
            } else if (i == 2) {
                Object obj4 = jlx.COROUTINE_SUSPENDED;
                if (this.a != 0) {
                    jji.c(obj);
                } else {
                    jji.c(obj);
                    Object obj5 = this.b;
                    this.a = 1;
                    obj = ((CoroutineWorker) obj5).c(this);
                    if (obj == obj4) {
                        return obj4;
                    }
                }
                return obj;
            } else if (i == 3) {
                Object obj6 = jlx.COROUTINE_SUSPENDED;
                if (this.a != 0) {
                    jji.c(obj);
                } else {
                    jji.c(obj);
                    Object obj7 = this.b;
                    this.a = 1;
                    obj = ((eez) obj7).i(this);
                    if (obj == obj6) {
                        return obj6;
                    }
                }
                return obj;
            } else if (i != 4) {
                Object obj8 = jlx.COROUTINE_SUSPENDED;
                int i2 = this.a;
                jji.c(obj);
                if (i2 == 0) {
                    Object obj9 = this.b;
                    this.a = 1;
                    obj = ((ConstraintTrackingWorker) obj9).i(this);
                    if (obj == obj8) {
                        return obj8;
                    }
                }
                return obj;
            } else {
                Object obj10 = jlx.COROUTINE_SUSPENDED;
                if (this.a != 0) {
                    try {
                        jji.c(obj);
                    } catch (bdo e) {
                        weVar = new bdu(e.a);
                    } catch (CancellationException unused) {
                        weVar = new bds((byte[]) null);
                    } catch (Throwable th) {
                        bbk.a().d(bdy.a, "Unexpected error in WorkerWrapper", th);
                        weVar = new bds((byte[]) null);
                    }
                } else {
                    jji.c(obj);
                    Object obj11 = this.b;
                    ? r3 = ((eez) obj11).i;
                    bas bas = new bas((eez) obj11, (jlr) null, 3);
                    this.a = 1;
                    obj = jqw.o(r3, bas, this);
                    if (obj == obj10) {
                        return obj10;
                    }
                }
                weVar = (we) obj;
                eez eez = (eez) this.b;
                Object e2 = ((aus) eez.b).e(new bdv(weVar, eez));
                jnu.d(e2, "workDatabase.runInTransa…          }\n            )");
                return e2;
            }
        } else if (this.a != 0) {
            jji.c(obj);
            return obj;
        } else {
            jji.c(obj);
            this.a = 1;
            throw new IllegalStateException("Not implemented");
        }
    }

    public final jlr c(Object obj, jlr jlr) {
        int i = this.c;
        if (i == 0) {
            return new bas((CoroutineWorker) this.b, jlr, 0);
        }
        if (i == 1) {
            return new bas((auk) this.b, jlr, 1);
        }
        if (i == 2) {
            return new bas((CoroutineWorker) this.b, jlr, 2, (byte[]) null);
        }
        if (i == 3) {
            return new bas((eez) this.b, jlr, 3);
        }
        if (i != 4) {
            return new bas((ConstraintTrackingWorker) this.b, jlr, 5);
        }
        return new bas((eez) this.b, jlr, 4, (byte[]) null);
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bas(CoroutineWorker coroutineWorker, jlr jlr, int i, byte[] bArr) {
        super(2, jlr);
        this.c = i;
        this.b = coroutineWorker;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bas(ConstraintTrackingWorker constraintTrackingWorker, jlr jlr, int i) {
        super(2, jlr);
        this.c = i;
        this.b = constraintTrackingWorker;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bas(auk auk, jlr jlr, int i) {
        super(2, jlr);
        this.c = i;
        this.b = auk;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bas(eez eez, jlr jlr, int i) {
        super(2, jlr);
        this.c = i;
        this.b = eez;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bas(eez eez, jlr jlr, int i, byte[] bArr) {
        super(2, jlr);
        this.c = i;
        this.b = eez;
    }
}
