package j$.time.format;

import j$.time.ZoneId;
import j$.time.chrono.C0003b;
import j$.time.chrono.n;
import j$.time.temporal.l;
import j$.time.temporal.m;
import j$.time.temporal.p;
import j$.time.temporal.r;

final class t implements m {
    final /* synthetic */ C0003b a;
    final /* synthetic */ Object b;
    final /* synthetic */ n c;
    final /* synthetic */ ZoneId d;

    t(C0003b bVar, m mVar, n nVar, ZoneId zoneId) {
        this.a = bVar;
        this.b = mVar;
        this.c = nVar;
        this.d = zoneId;
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [j$.time.temporal.m, java.lang.Object] */
    public final long B(p pVar) {
        C0003b bVar = this.a;
        if (bVar == null || !pVar.B()) {
            return this.b.B(pVar);
        }
        return bVar.B(pVar);
    }

    /* JADX WARNING: type inference failed for: r0v3, types: [j$.time.temporal.m, java.lang.Object] */
    public final Object F(r rVar) {
        if (rVar == l.e()) {
            return this.c;
        }
        if (rVar == l.k()) {
            return this.d;
        }
        if (rVar == l.i()) {
            return this.b.F(rVar);
        }
        return rVar.a(this);
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [j$.time.temporal.m, java.lang.Object] */
    public final boolean e(p pVar) {
        C0003b bVar = this.a;
        if (bVar == null || !pVar.B()) {
            return this.b.e(pVar);
        }
        return bVar.e(pVar);
    }

    public final /* synthetic */ int p(p pVar) {
        return l.a(this, pVar);
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [j$.time.temporal.m, java.lang.Object] */
    public final j$.time.temporal.t t(p pVar) {
        C0003b bVar = this.a;
        if (bVar == null || !pVar.B()) {
            return this.b.t(pVar);
        }
        return bVar.t(pVar);
    }

    public final String toString() {
        String str;
        String valueOf = String.valueOf(this.b);
        String str2 = "";
        n nVar = this.c;
        if (nVar != null) {
            str = " with chronology ".concat(String.valueOf(nVar));
        } else {
            str = str2;
        }
        ZoneId zoneId = this.d;
        if (zoneId != null) {
            str2 = " with zone ".concat(String.valueOf(zoneId));
        }
        return valueOf + str + str2;
    }
}
