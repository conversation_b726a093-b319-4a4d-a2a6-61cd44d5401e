package defpackage;

/* renamed from: csu  reason: default package */
/* compiled from: PG */
public final class csu extends htq implements hvb {
    public static final csu b;
    private static volatile hvh d;
    public String a = "";
    private int c;

    static {
        csu csu = new csu();
        b = csu;
        htq.z(csu.class, csu);
    }

    private csu() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(b, "\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001ဈ\u0000", new Object[]{"c", "a"});
        } else if (i2 == 3) {
            return new csu();
        } else {
            if (i2 == 4) {
                return new htk((htq) b);
            }
            if (i2 == 5) {
                return b;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = d;
            if (hvh == null) {
                synchronized (csu.class) {
                    hvh = d;
                    if (hvh == null) {
                        hvh = new htl(b);
                        d = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
