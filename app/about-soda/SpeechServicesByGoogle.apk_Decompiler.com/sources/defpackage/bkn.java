package defpackage;

import java.util.List;

/* renamed from: bkn  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bkn implements bkz {
    public final /* synthetic */ bko a;
    public final /* synthetic */ bli b;
    public final /* synthetic */ List c;

    public /* synthetic */ bkn(bko bko, bli bli, List list) {
        this.a = bko;
        this.b = bli;
        this.c = list;
    }

    public final void a(bla bla, boolean z) {
        if (z) {
            List list = this.c;
            bko bko = this.a;
            bla bla2 = bko.a;
            if (bla2 != null) {
                bli bli = this.b;
                bla2.a(false);
                bli.notifyItemChanged(list.indexOf(bko.a));
            }
            bko.a = bla;
            bko.b = list.indexOf(bko.a);
            if (bko.c) {
                bko.c();
            }
        }
    }
}
