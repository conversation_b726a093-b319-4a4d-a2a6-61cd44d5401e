package j$.time.format;

import j$.time.chrono.n;
import j$.time.chrono.u;
import j$.time.temporal.l;
import j$.time.temporal.p;

final class o implements f {
    private final p a;
    private final B b;
    private final x c;
    private volatile j d;

    o(p pVar, B b2, x xVar) {
        this.a = pVar;
        this.b = b2;
        this.c = xVar;
    }

    public final boolean g(u uVar, StringBuilder sb) {
        String str;
        Long e = uVar.e(this.a);
        if (e == null) {
            return false;
        }
        n nVar = (n) uVar.d().F(l.e());
        if (nVar == null || nVar == u.d) {
            str = this.c.e(this.a, e.longValue(), this.b, uVar.c());
        } else {
            str = this.c.d(nVar, this.a, e.longValue(), this.b, uVar.c());
        }
        if (str == null) {
            if (this.d == null) {
                this.d = new j(this.a, 1, 19, A.NORMAL);
            }
            return this.d.g(uVar, sb);
        }
        sb.append(str);
        return true;
    }

    public final String toString() {
        B b2 = B.FULL;
        p pVar = this.a;
        B b3 = this.b;
        if (b3 == b2) {
            String valueOf = String.valueOf(pVar);
            return "Text(" + valueOf + ")";
        }
        String valueOf2 = String.valueOf(pVar);
        String valueOf3 = String.valueOf(b3);
        return "Text(" + valueOf2 + "," + valueOf3 + ")";
    }
}
