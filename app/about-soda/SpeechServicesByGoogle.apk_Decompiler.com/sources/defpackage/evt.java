package defpackage;

/* renamed from: evt  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class evt implements gsb {
    public final /* synthetic */ bmu a;
    private final /* synthetic */ int b;

    public /* synthetic */ evt(bmu bmu, int i) {
        this.b = i;
        this.a = bmu;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                fqy g = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/cross_device/timer_geller_share_total_count", new fqx[0]);
                g.c();
                return g;
            case 1:
                fqy g2 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/cross_device/timer_geller_share_completed_count", new fqx("task_status", String.class));
                g2.c();
                return g2;
            case 2:
                fqy g3 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/cross_device/whole_home_channel_close_reason", new fqx("reason", String.class));
                g3.c();
                return g3;
            case 3:
                fqy g4 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/cross_profile_counter", new fqx("app_version", String.class), new fqx("device_model", String.class), new fqx("manufacturer", String.class), new fqx("counter_name", String.class));
                g4.c();
                return g4;
            case 4:
                fqy g5 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/cross_profile_failure_message_counter", new fqx("app_version", String.class), new fqx("message", String.class));
                g5.c();
                return g5;
            case 5:
                fqy g6 = ((frc) this.a.a).g("/client_streamz/android_gsa/appintegration/query_completions", new fqx("service_name", String.class), new fqx("client_package", String.class), new fqx("client_version", String.class), new fqx("client_version_code", Integer.class), new fqx("agsa_source", String.class), new fqx("completion_type", String.class), new fqx("has_client_input", Boolean.class));
                g6.c();
                return g6;
            case 6:
                fqy g7 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/csl_callback", new fqx("app_version", String.class));
                g7.c();
                return g7;
            case 7:
                fqv c = ((frc) this.a.a).c("/client_streamz/android_gsa/assistant/tapas/monitoring/durations", new fqx("component", String.class), new fqx("label", String.class), new fqx("end_state", String.class));
                c.c();
                return c;
            case 8:
                fqv c2 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/csl_inference_latency", new fqx("app_version", String.class), new fqx("event_type", String.class));
                c2.c();
                return c2;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fqv c3 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/csl_time_to_detect_latency", new fqx("app_version", String.class), new fqx("event_type", String.class));
                c3.c();
                return c3;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fqy g8 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/data_updates", new fqx("app_version", String.class), new fqx("code_path", String.class), new fqx("content_type", String.class));
                g8.c();
                return g8;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                fqy g9 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/does_push_match_primary_account", new fqx("app_version", String.class), new fqx("account_match_status", String.class), new fqx("data_type", Integer.class), new fqx("is_bluechip", Boolean.class));
                g9.c();
                return g9;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                fqy g10 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/event_cards_after_dedup", new fqx("app_version", String.class), new fqx("has_wallet_event", Boolean.class), new fqx("has_gi_event", Boolean.class), new fqx("has_calendar_event_from_gi", Boolean.class));
                g10.c();
                return g10;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                fqy g11 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/event_reservation_dedup", new fqx("app_version", String.class), new fqx("has_wallet_event", Boolean.class), new fqx("has_gi_event", Boolean.class), new fqx("has_calendar_event", Boolean.class));
                g11.c();
                return g11;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                fqv c4 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/flight/boarding_pass_display_latency", new fqx[0]);
                c4.c();
                return c4;
            case 15:
                fqy g12 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/flight/flight_boarding_pass_status", new fqx("app_version", String.class), new fqx("flight_boarding_pass_status", String.class));
                g12.c();
                return g12;
            case 16:
                fqv c5 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/flight/flight_display_forward_interval", new fqx[0]);
                c5.c();
                return c5;
            case 17:
                fqy g13 = ((frc) this.a.a).g("/client_streamz/android_gsa/appintegration/show_voice_plate", new fqx("service_name", String.class), new fqx("client_package", String.class), new fqx("client_version", String.class), new fqx("client_version_code", Integer.class), new fqx("text_query", Boolean.class));
                g13.c();
                return g13;
            case 18:
                fqv c6 = ((frc) this.a.a).c("/client_streamz/android_gsa/assistant/tapas/monitoring/latencies", new fqx("component", String.class), new fqx("label", String.class));
                c6.c();
                return c6;
            case 19:
                fqy g14 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/surfaces/bisto/device_database_errors", new fqx("error_type", String.class), new fqx("method_name", String.class));
                g14.c();
                return g14;
            default:
                fqv c7 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/flight/flight_landing_baggage_claim_display_latency", new fqx("country", String.class), new fqx("airline", String.class), new fqx("destination_airport", String.class));
                c7.c();
                return c7;
        }
    }
}
