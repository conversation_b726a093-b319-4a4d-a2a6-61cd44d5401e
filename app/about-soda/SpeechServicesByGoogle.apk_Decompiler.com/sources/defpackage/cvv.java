package defpackage;

/* renamed from: cvv  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvv implements hko {
    public final /* synthetic */ cvy a;
    public final /* synthetic */ csv b;
    public final /* synthetic */ csx c;
    public final /* synthetic */ boolean d;
    public final /* synthetic */ boolean e;
    public final /* synthetic */ int f;
    public final /* synthetic */ int g;

    public /* synthetic */ cvv(cvy cvy, csv csv, csx csx, boolean z, boolean z2, int i, int i2) {
        this.a = cvy;
        this.b = csv;
        this.c = csx;
        this.d = z;
        this.e = z2;
        this.f = i;
        this.g = i2;
    }

    public final hme a(Object obj) {
        cvy cvy = this.a;
        csx csx = this.c;
        boolean z = this.d;
        boolean z2 = this.e;
        int i = this.f + 1;
        csv csv = this.b;
        int i2 = this.g;
        ctf ctf = (ctf) obj;
        if (ctf == ctf.DOWNLOAD_COMPLETE) {
            cyh.e("%s: File %s downloaded for group: %s", "FileGroupManager", csv.b, csx.c);
            return cvy.i(csx, z, z2, i, i2);
        } else if (ctf == ctf.SUBSCRIBED || ctf == ctf.DOWNLOAD_IN_PROGRESS) {
            cyh.e("%s: File %s not downloaded for group: %s", "FileGroupManager", csv.b, csx.c);
            return cvy.i(csx, z, true, i, i2);
        } else {
            cyh.e("%s: File %s not downloaded for group: %s", "FileGroupManager", csv.b, csx.c);
            return cvy.i(csx, true, z2, i, i2);
        }
    }
}
