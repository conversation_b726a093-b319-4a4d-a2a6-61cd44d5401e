package defpackage;

import android.os.StrictMode;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.ParseException;

/* renamed from: cqk  reason: default package */
/* compiled from: PG */
public final class cqk {
    private static final hca a = hca.m("com/google/android/libraries/concurrent/threadpool/ProcSchedStatUtils");

    public static cql a(File file) {
        cql cql;
        FileInputStream fileInputStream;
        Throwable th;
        if (file.isDirectory()) {
            return cql.a;
        }
        StrictMode.ThreadPolicy allowThreadDiskReads = StrictMode.allowThreadDiskReads();
        byte[] bArr = new byte[62];
        try {
            try {
                fileInputStream = new FileInputStream(file);
                int d = hgy.d(fileInputStream, bArr, 62);
                int i = 0;
                long j = -1;
                long j2 = -1;
                long j3 = 0;
                int i2 = 0;
                boolean z = false;
                while (true) {
                    if (i2 >= d) {
                        break;
                    }
                    int i3 = i2 + 1;
                    byte b = bArr[i2];
                    if (b != 32) {
                        if (b >= 48) {
                            if (b <= 57) {
                                if (j3 > 922337203685477580L) {
                                    break;
                                }
                                j3 = (j3 * 10) + ((long) (b - 48));
                                z = true;
                                i2 = i3;
                            } else {
                                break;
                            }
                        } else {
                            break;
                        }
                    } else if (!z) {
                        break;
                    } else {
                        if (i != 0) {
                            if (i != 1) {
                                break;
                            }
                            j2 = j3;
                        } else {
                            j = j3;
                        }
                        i++;
                        z = false;
                        i2 = i3;
                        j3 = 0;
                    }
                }
                if (i == 2) {
                    cql = new cql(j, j2, j3);
                    fileInputStream.close();
                    StrictMode.setThreadPolicy(allowThreadDiskReads);
                    return cql;
                }
                throw new ParseException("Failed to parse SchedStat", i);
            } catch (IOException | ParseException e) {
                e = e;
            } catch (Throwable th2) {
                th.addSuppressed(th2);
            }
        } catch (IOException | ParseException e2) {
            e = e2;
            File file2 = file;
            try {
                ((hby) ((hby) ((hby) a.h()).i(e)).j("com/google/android/libraries/concurrent/threadpool/ProcSchedStatUtils", "getThreadSchedStat", 87, "ProcSchedStatUtils.java")).u("Failed to read SchedStat for thread %s", file.getName());
                cql = cql.a;
                StrictMode.setThreadPolicy(allowThreadDiskReads);
                return cql;
            } catch (Throwable th3) {
                StrictMode.setThreadPolicy(allowThreadDiskReads);
                throw th3;
            }
        }
        throw th;
    }
}
