package defpackage;

/* renamed from: cua  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cua implements gqx {
    public final /* synthetic */ cuf a;
    public final /* synthetic */ boolean b;
    public final /* synthetic */ csk c;
    public final /* synthetic */ String d;

    public /* synthetic */ cua(cuf cuf, boolean z, csk csk, String str) {
        this.a = cuf;
        this.b = z;
        this.c = csk;
        this.d = str;
    }

    public final Object apply(Object obj) {
        crw crw = (crw) obj;
        csk csk = this.c;
        if (this.b) {
            try {
                ((csl) csk.g.b()).a(crw);
            } catch (Exception e) {
                cyh.o(e, "%s: Listener onComplete failed for group %s", "MobileDataDownload", crw.b);
            }
            ((czp) this.a.e.b()).i(this.d);
        }
        return crw;
    }
}
