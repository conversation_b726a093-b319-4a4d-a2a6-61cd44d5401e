package defpackage;

import android.os.Parcel;
import android.os.Parcelable;
import com.google.protobuf.contrib.android.ProtoParsers$InternalDontUse;
import java.util.ArrayList;
import java.util.Date;
import org.chromium.base.UnguessableToken;

/* renamed from: cht  reason: default package */
/* compiled from: PG */
public final class cht implements Parcelable.Creator {
    private final /* synthetic */ int a;

    public cht(int i) {
        this.a = i;
    }

    public final /* synthetic */ Object createFromParcel(Parcel parcel) {
        Parcel parcel2 = parcel;
        chu chu = null;
        switch (this.a) {
            case 0:
                int I = cgr.I(parcel);
                byte[] bArr = null;
                while (parcel.dataPosition() < I) {
                    int readInt = parcel.readInt();
                    int E = cgr.E(readInt);
                    if (E == 2) {
                        chu = (chu) cgr.M(parcel2, readInt, chu.CREATOR);
                    } else if (E != 3) {
                        cgr.U(parcel2, readInt);
                    } else {
                        bArr = cgr.W(parcel2, readInt);
                    }
                }
                cgr.S(parcel2, I);
                return new chs(chu, bArr);
            case 1:
                return new dcc(parcel.readInt(), parcel.readInt());
            case 2:
                String readString = parcel.readString();
                String readString2 = parcel.readString();
                int readInt2 = parcel.readInt();
                int readInt3 = parcel.readInt();
                int readInt4 = parcel.readInt();
                int readInt5 = parcel.readInt();
                int readInt6 = parcel.readInt();
                ArrayList<String> createStringArrayList = parcel.createStringArrayList();
                ddf j = ddg.j();
                j.a = readString;
                j.b = readString2;
                j.d(readInt2);
                j.c(readInt3);
                j.f(readInt4);
                j.b(readInt5);
                j.g(readInt6);
                if (createStringArrayList != null) {
                    int size = createStringArrayList.size();
                    for (int i = 0; i < size; i++) {
                        String str = createStringArrayList.get(i);
                        dct.f(str);
                        if (j.c == null) {
                            if (j.d == null) {
                                j.c = new gxl();
                            } else {
                                j.c = new gxl();
                                j.c.j(j.d);
                                j.d = null;
                            }
                        }
                        j.c.h(str);
                    }
                }
                return j.a();
            case 3:
                return ddt.c(ftd.o(parcel.readString()), parcel.readInt());
            case 4:
                gxq gxq = ded.o;
                String o = ftd.o(parcel.readString());
                String o2 = ftd.o(parcel.readString());
                long readLong = parcel.readLong();
                long readLong2 = parcel.readLong();
                int readInt7 = parcel.readInt();
                int readInt8 = parcel.readInt();
                ArrayList<String> createStringArrayList2 = parcel.createStringArrayList();
                ArrayList<String> createStringArrayList3 = parcel.createStringArrayList();
                String readString3 = parcel.readString();
                long readLong3 = parcel.readLong();
                String readString4 = parcel.readString();
                dec p = ded.p();
                p.a = (ddt) parcel2.readParcelable(ddt.class.getClassLoader());
                p.l(o);
                p.k(o2);
                p.m(readLong);
                p.g(readLong2);
                p.j(readInt7);
                p.h(readInt8);
                if (createStringArrayList2 != null) {
                    int size2 = createStringArrayList2.size();
                    for (int i2 = 0; i2 < size2; i2++) {
                        p.d(createStringArrayList2.get(i2));
                    }
                }
                if (createStringArrayList3 != null) {
                    p.f(createStringArrayList3);
                }
                p.c = readString3;
                p.i(new Date(readLong3));
                if (readString4 != null) {
                    p.e("label", readString4);
                }
                return p.a();
            case 5:
                int readInt9 = parcel.readInt();
                int readInt10 = parcel.readInt();
                int readInt11 = parcel.readInt();
                int readInt12 = parcel.readInt();
                int readInt13 = parcel.readInt();
                deg g = deh.g();
                g.f((ded) parcel2.readParcelable(ded.class.getClassLoader()));
                g.d(readInt9);
                g.c(readInt10);
                g.e(readInt11);
                g.b(readInt12);
                g.g(readInt13);
                return g.a();
            case 6:
                return new ewx(parcel2);
            case 7:
                return new fsw(parcel2);
            case 8:
                return fwm.a(parcel.readInt(), gba.a);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return new gab(parcel2);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return new gpb();
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return new gqb(parcel2);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                byte[] bArr2 = new byte[parcel.readInt()];
                parcel2.readByteArray(bArr2);
                return new ProtoParsers$InternalDontUse(bArr2, (hva) null);
            default:
                long readLong4 = parcel.readLong();
                long readLong5 = parcel.readLong();
                if (readLong4 == 0 || readLong5 == 0) {
                    return null;
                }
                return new UnguessableToken(readLong4, readLong5);
        }
    }

    public final /* synthetic */ Object[] newArray(int i) {
        switch (this.a) {
            case 0:
                return new chs[i];
            case 1:
                return new dck[i];
            case 2:
                return new ddg[i];
            case 3:
                return new ddt[i];
            case 4:
                return new ded[i];
            case 5:
                return new deh[i];
            case 6:
                return new ewx[i];
            case 7:
                return new fsw[i];
            case 8:
                return new fwm[i];
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return new gab[i];
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return new gpb[i];
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return new gqb[i];
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return new ProtoParsers$InternalDontUse[i];
            default:
                return new UnguessableToken[i];
        }
    }
}
