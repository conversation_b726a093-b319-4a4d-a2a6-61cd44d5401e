package defpackage;

import android.content.Context;
import androidx.work.WorkerParameters;

/* renamed from: bav  reason: default package */
/* compiled from: PG */
public final class bav extends bcb {
    public static final bav a = new bav();

    private bav() {
    }

    public final /* bridge */ /* synthetic */ bbj a(Context context, String str, WorkerParameters workerParameters) {
        jnu.e(context, "appContext");
        jnu.e(str, "workerClassName");
        jnu.e(workerParameters, "workerParameters");
        return null;
    }
}
