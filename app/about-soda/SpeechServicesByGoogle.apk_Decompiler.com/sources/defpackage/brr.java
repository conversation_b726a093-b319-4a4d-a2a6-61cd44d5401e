package defpackage;

import android.os.Bundle;
import java.util.List;
import java.util.Locale;
import java.util.Set;

/* renamed from: brr  reason: default package */
/* compiled from: PG */
public final class brr {
    private static final hca p = hca.m("com/google/android/apps/speech/tts/googletts/common/GoogleTTSRequest");
    public final CharSequence a;
    public String b;
    public String c;
    public final int d;
    public final int e;
    public final int f;
    public final Bundle g;
    public igw h;
    public String i;
    public final String j;
    public final List k;
    public final float l;
    public String m = null;
    public final boolean n;
    public boolean o;
    private String q;

    /* JADX WARNING: Removed duplicated region for block: B:23:0x00a4  */
    /* JADX WARNING: Removed duplicated region for block: B:37:0x014a  */
    /* JADX WARNING: Removed duplicated region for block: B:45:0x0161  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public brr(java.lang.CharSequence r15, java.lang.String r16, java.lang.String r17, java.lang.String r18, int r19, int r20, int r21, java.util.List r22, android.os.Bundle r23, float r24, java.util.Locale r25, java.lang.String r26) {
        /*
            r14 = this;
            r1 = r14
            r2 = r18
            r3 = r23
            r14.<init>()
            r4 = 0
            r1.m = r4
            r0 = r15
            r1.a = r0
            java.lang.String r0 = r15.toString()
            r5 = 1
            r6 = 0
            if (r0 == 0) goto L_0x002c
            java.lang.String r7 = "<speak"
            boolean r0 = r0.contains(r7)
            if (r0 == 0) goto L_0x002c
            iog r0 = defpackage.iog.a
            ioh r0 = r0.b()
            boolean r0 = r0.a()
            if (r0 == 0) goto L_0x002c
            r0 = r5
            goto L_0x002d
        L_0x002c:
            r0 = r6
        L_0x002d:
            r1.n = r0
            java.lang.String r0 = defpackage.ftd.t(r16)
            r1.b = r0
            java.lang.String r0 = "com.google.android.tts:TextProto"
            byte[] r0 = r3.getByteArray(r0)
            if (r0 == 0) goto L_0x0065
            hte r7 = defpackage.hte.a()     // Catch:{ hui -> 0x0050 }
            igw r8 = defpackage.igw.c     // Catch:{ hui -> 0x0050 }
            int r9 = r0.length     // Catch:{ hui -> 0x0050 }
            htq r0 = defpackage.htq.o(r8, r0, r6, r9, r7)     // Catch:{ hui -> 0x0050 }
            defpackage.htq.D(r0)     // Catch:{ hui -> 0x0050 }
            igw r0 = (defpackage.igw) r0     // Catch:{ hui -> 0x0050 }
            r1.h = r0     // Catch:{ hui -> 0x0050 }
            goto L_0x0065
        L_0x0050:
            r0 = move-exception
            r13 = r0
            hca r0 = p
            hco r7 = r0.g()
            java.lang.String r10 = "<init>"
            r11 = 167(0xa7, float:2.34E-43)
            java.lang.String r8 = "Could not parse text proto"
            java.lang.String r9 = "com/google/android/apps/speech/tts/googletts/common/GoogleTTSRequest"
            java.lang.String r12 = "GoogleTTSRequest.java"
            ((defpackage.hby) ((defpackage.hby) ((defpackage.hby) r7).i(r13)).j(r9, r10, r11, r12)).r(r8)
        L_0x0065:
            if (r25 == 0) goto L_0x0084
            java.lang.String r0 = r25.getLanguage()
            r7 = r16
            boolean r0 = r7.equalsIgnoreCase(r0)
            if (r0 == 0) goto L_0x0084
            boolean r0 = r17.isEmpty()
            if (r0 == 0) goto L_0x0084
            java.lang.String r0 = r25.getCountry()
            java.lang.String r0 = defpackage.ftd.t(r0)
            r1.c = r0
            goto L_0x008a
        L_0x0084:
            java.lang.String r0 = defpackage.ftd.t(r17)
            r1.c = r0
        L_0x008a:
            r7 = r19
            r1.d = r7
            r7 = r20
            r1.e = r7
            r7 = r21
            r1.f = r7
            r7 = r22
            r1.k = r7
            r1.g = r3
            r7 = r26
            r1.j = r7
            r1.q = r4
            if (r2 == 0) goto L_0x014a
            java.lang.String r0 = "-local"
            int r0 = r2.indexOf(r0)
            java.lang.String r7 = "-network"
            int r7 = r2.indexOf(r7)
            java.lang.String r8 = "-language"
            int r8 = r2.indexOf(r8)
            r9 = -1
            if (r0 == r9) goto L_0x00c1
            java.lang.String r0 = r2.substring(r6, r0)
            r1.i = r0
            goto L_0x014c
        L_0x00c1:
            if (r7 == r9) goto L_0x00cf
            java.lang.String r0 = r2.substring(r6, r7)
            r1.i = r0
            java.lang.String r0 = "NetworkFirst"
            r1.q = r0
            goto L_0x014c
        L_0x00cf:
            if (r8 == r9) goto L_0x014c
            java.lang.String r0 = "-"
            java.lang.String[] r0 = r2.split(r0)
            int r7 = r0.length
            r8 = 3
            java.lang.String r9 = "<init>"
            java.lang.String r10 = "com/google/android/apps/speech/tts/googletts/common/GoogleTTSRequest"
            java.lang.String r11 = "GoogleTTSRequest.java"
            if (r7 != r8) goto L_0x010b
            r6 = r0[r6]
            java.lang.String r6 = defpackage.ftd.t(r6)
            r1.b = r6
            r0 = r0[r5]
            java.lang.String r0 = defpackage.ftd.t(r0)
            r1.c = r0
            hca r0 = p
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            r5 = 202(0xca, float:2.83E-43)
            hco r0 = r0.j(r10, r9, r5, r11)
            hby r0 = (defpackage.hby) r0
            java.lang.String r5 = r1.b
            java.lang.String r6 = r1.c
            java.lang.String r7 = "Interpreting request for %s as request for %s-%s"
            r0.G(r7, r2, r5, r6)
            goto L_0x0147
        L_0x010b:
            r5 = 2
            if (r7 != r5) goto L_0x0132
            r0 = r0[r6]
            java.lang.String r0 = defpackage.ftd.t(r0)
            r1.b = r0
            java.lang.String r0 = ""
            r1.c = r0
            hca r0 = p
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            r5 = 208(0xd0, float:2.91E-43)
            hco r0 = r0.j(r10, r9, r5, r11)
            hby r0 = (defpackage.hby) r0
            java.lang.String r5 = r1.b
            java.lang.String r6 = "Interpreting request for %s as request for %s"
            r0.C(r6, r2, r5)
            goto L_0x0147
        L_0x0132:
            hca r0 = p
            hco r0 = r0.g()
            hby r0 = (defpackage.hby) r0
            r5 = 211(0xd3, float:2.96E-43)
            hco r0 = r0.j(r10, r9, r5, r11)
            hby r0 = (defpackage.hby) r0
            java.lang.String r5 = "Invalid voice name: %s"
            r0.u(r5, r2)
        L_0x0147:
            r1.i = r4
            goto L_0x014c
        L_0x014a:
            r1.i = r4
        L_0x014c:
            java.lang.String r0 = "com.google.android.tts:LoudnessGain"
            java.lang.String r2 = "0.0f"
            java.lang.String r0 = r3.getString(r0, r2)     // Catch:{ NumberFormatException -> 0x0159 }
            float r0 = java.lang.Float.parseFloat(r0)     // Catch:{ NumberFormatException -> 0x0159 }
            goto L_0x015a
        L_0x0159:
            r0 = 0
        L_0x015a:
            r2 = 1065353216(0x3f800000, float:1.0)
            int r3 = (r0 > r2 ? 1 : (r0 == r2 ? 0 : -1))
            if (r3 < 0) goto L_0x0161
            goto L_0x0163
        L_0x0161:
            r0 = r24
        L_0x0163:
            float r0 = java.lang.Math.max(r0, r2)
            r2 = 1134435369(0x439e1c29, float:316.22)
            float r0 = java.lang.Math.min(r0, r2)
            r1.l = r0
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.brr.<init>(java.lang.CharSequence, java.lang.String, java.lang.String, java.lang.String, int, int, int, java.util.List, android.os.Bundle, float, java.util.Locale, java.lang.String):void");
    }

    public static final int n(String str, Bundle bundle) {
        Object obj = bundle.get(str);
        if (obj instanceof Integer) {
            return ((Integer) obj).intValue();
        }
        return Integer.parseInt(bundle.getString(str));
    }

    public static final boolean o(String str, Bundle bundle) {
        if (bundle == null || bundle.get(str) == null) {
            return false;
        }
        return true;
    }

    private final String p() {
        String string = this.g.getString("com.google.android.tts:Mode");
        if (string == null) {
            return this.q;
        }
        return string;
    }

    private static final boolean q(String str, Bundle bundle) {
        Object obj = bundle.get(str);
        if (obj instanceof Boolean) {
            return ((Boolean) obj).booleanValue();
        }
        return Boolean.parseBoolean(bundle.getString(str));
    }

    public final float a() {
        float f2;
        float f3;
        float f4 = (float) this.e;
        if (f4 >= 100.0f) {
            if (f4 > 400.0f) {
                f4 = 400.0f;
            }
            f3 = ((f4 - 0.044921875f) / 300.0f) * 3.0f;
            f2 = 1.0f;
        } else {
            if (f4 < 20.0f) {
                f4 = 20.0f;
            }
            f2 = 0.5f;
            f3 = ((f4 - 0.21875f) / 80.0f) * 0.5f;
        }
        return f3 + f2;
    }

    public final int b() {
        String str;
        int i2;
        int i3;
        if (q("networkTts", this.g)) {
            str = "NetworkOnly";
        } else if (q("embeddedTts", this.g)) {
            str = "LocalOnly";
        } else if (!ftd.p(this.g.getString("com.google.android.tts:Mode"))) {
            str = this.g.getString("com.google.android.tts:Mode");
        } else {
            str = "LocalFirst";
        }
        String str2 = this.j;
        if (str2 != null) {
            i2 = str2.hashCode() + 37;
        } else {
            i2 = 1;
        }
        int hashCode = ((i2 * 37) + str.hashCode()) * 37;
        if (true != f()) {
            i3 = 4177;
        } else {
            i3 = 4159;
        }
        return ((((((hashCode + i3) * 37) + this.k.hashCode()) * 37) + this.e) * 37) + this.d;
    }

    public final String c() {
        return this.g.getString("com.google.android.tts:VuiId");
    }

    public final Locale d() {
        return new Locale(this.b, this.c);
    }

    public final boolean e() {
        return "x-detect".equals(this.g.getString("com.google.android.tts:LanguageDetection"));
    }

    public final boolean equals(Object obj) {
        if (!(obj instanceof brr)) {
            return false;
        }
        brr brr = (brr) obj;
        if (!this.a.toString().contentEquals(brr.a) || !this.b.equals(brr.b) || !this.c.equals(brr.c) || this.d != brr.d || this.e != brr.e || this.f != brr.f) {
            return false;
        }
        Bundle bundle = this.g;
        Bundle bundle2 = brr.g;
        Set<String> keySet = bundle.keySet();
        if (!keySet.containsAll(bundle2.keySet())) {
            return false;
        }
        for (String str : keySet) {
            if (!bundle.get(str).equals(bundle2.get(str))) {
            }
        }
        if (a.k(this.i, brr.i) && this.k == brr.k && this.l == brr.l) {
            return true;
        }
        return false;
        return false;
    }

    public final boolean f() {
        return "hol".equals(this.g.getString("com.google.android.tts:UseGoogleOnlyVoice"));
    }

    public final boolean g() {
        if (this.i != null) {
            return true;
        }
        return false;
    }

    public final boolean h() {
        String string = this.g.getString("com.google.android.tts:EnableEnUsVoiceSelectionFallback");
        if (string == null || !ftd.v("true", string)) {
            return false;
        }
        return true;
    }

    public final int hashCode() {
        int i2;
        int hashCode = ((((((((((((this.a.hashCode() + 31) * 31) + this.b.hashCode()) * 31) + this.c.hashCode()) * 31) + this.d) * 31) + this.e) * 31) + this.f) * 31) + this.g.size();
        String str = this.i;
        if (str == null) {
            i2 = 0;
        } else {
            i2 = str.hashCode();
        }
        return (((((hashCode * 31) + i2) * 31) + this.k.hashCode()) * 31) + Float.floatToRawIntBits(this.l);
    }

    public final boolean i() {
        String string = this.g.getString("com.google.android.tts:DisableInterLanguageFallback");
        if (string == null || !ftd.v("true", string)) {
            return false;
        }
        return true;
    }

    public final boolean j() {
        if ("LocalOnly".equals(p()) || q("embeddedTts", this.g)) {
            return true;
        }
        return false;
    }

    public final boolean k() {
        return "NetworkFirst".equals(p());
    }

    public final boolean l() {
        if ("NetworkOnly".equals(p()) || q("networkTts", this.g)) {
            return true;
        }
        return false;
    }

    public final boolean m() {
        String string = this.g.getString("com.google.android.tts:DisableVoiceSelectionFallback");
        if (string == null || !ftd.v("true", string)) {
            return false;
        }
        return true;
    }
}
