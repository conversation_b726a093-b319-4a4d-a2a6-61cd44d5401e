package defpackage;

/* renamed from: evy  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class evy implements gsb {
    public final /* synthetic */ bmu a;
    private final /* synthetic */ int b;

    public /* synthetic */ evy(bmu bmu, int i) {
        this.b = i;
        this.a = bmu;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                fqy g = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/tng_weather/trigger_status", new fqx("status", String.class), new fqx("action", String.class), new fqx("request_type", String.class));
                g.c();
                return g;
            case 1:
                fqy g2 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/tng_weather/send_status", new fqx("status", String.class), new fqx("action", String.class));
                g2.c();
                return g2;
            case 2:
                fqv c = ((frc) this.a.a).c("/client_streamz/android_gsa/assistant/verticals/memory/garbage_collection/duration", new fqx[0]);
                c.c();
                return c;
            case 3:
                fqy g3 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/update_action", new fqx("app_version", String.class), new fqx("code_path", String.class), new fqx("content_type", String.class), new fqx("action_type", String.class));
                g3.c();
                return g3;
            case 4:
                fqv c2 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/update_data_size", new fqx("app_version", String.class), new fqx("aiai_app_version", String.class), new fqx("update_status", String.class), new fqx("locus_id", String.class));
                c2.c();
                return c2;
            case 5:
                fqv c3 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/weather/air_quality_aqi_delta", new fqx("app_version", String.class), new fqx("location_history_enabled", Boolean.class), new fqx("waa_enabled", Boolean.class), new fqx("is_fine_location_used", Boolean.class), new fqx("is_the_same_station", Boolean.class), new fqx("age_in_hours", Integer.class), new fqx("freshness_delta_in_seconds", Integer.class));
                c3.c();
                return c3;
            case 6:
                fqy g4 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/air_quality_events", new fqx("status", String.class));
                g4.c();
                return g4;
            case 7:
                fqv c4 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/weather/air_quality_freshness", new fqx("app_version", String.class));
                c4.c();
                return c4;
            case 8:
                fqv c5 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/weather/air_quality_freshness_delta", new fqx("app_version", String.class), new fqx("location_history_enabled", Boolean.class), new fqx("waa_enabled", Boolean.class), new fqx("is_fine_location_used", Boolean.class), new fqx("is_the_same_station", Boolean.class), new fqx("age_in_hours", Integer.class));
                c5.c();
                return c5;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fqy g5 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/air_quality_multiple_cards", new fqx("app_version", String.class));
                g5.c();
                return g5;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fqy g6 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/air_quality_source_station_type", new fqx("station", String.class));
                g6.c();
                return g6;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                fqv c6 = ((frc) this.a.a).c("/client_streamz/android_gsa/assistant/context/fetch_latency", new fqx("client_id", String.class), new fqx("context_source", String.class));
                c6.c();
                return c6;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                fqy g7 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/card", new fqx("has_city_name", Boolean.class));
                g7.c();
                return g7;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                fqy g8 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/verticals/memory/garbage_collection/finished_count", new fqx("outcome", String.class));
                g8.c();
                return g8;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                fqy g9 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/card_push_count", new fqx("is_bluechip", Boolean.class), new fqx("has_weather", Boolean.class), new fqx("app_version", String.class), new fqx("location_history_enabled", Boolean.class), new fqx("waa_enabled", Boolean.class), new fqx("is_last_weather_card_expired", Boolean.class), new fqx("is_weather_enabled", Boolean.class));
                g9.c();
                return g9;
            case 15:
                fqy g10 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/correct_city_shown", new fqx("app_version", String.class), new fqx("is_bluechip", Boolean.class), new fqx("location_history_enabled", Boolean.class), new fqx("waa_enabled", Boolean.class), new fqx("is_correct", Boolean.class));
                g10.c();
                return g10;
            case 16:
                fqy g11 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/current_and_daily_forecast_mismatch", new fqx("source", Integer.class), new fqx("mismatch_fields", String.class));
                g11.c();
                return g11;
            case 17:
                fqy g12 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/current_weather_missing", new fqx("source", Integer.class));
                g12.c();
                return g12;
            case 18:
                fqy g13 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/current_weather_stale", new fqx("source", Integer.class), new fqx("hours", Integer.class));
                g13.c();
                return g13;
            case 19:
                fqy g14 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/current_weather_update_missing_location_permission", new fqx[0]);
                g14.c();
                return g14;
            default:
                fqy g15 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/current_weather_update_time_missing", new fqx("source", Integer.class));
                g15.c();
                return g15;
        }
    }
}
