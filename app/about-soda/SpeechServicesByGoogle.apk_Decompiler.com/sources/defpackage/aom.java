package defpackage;

import java.io.File;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/* renamed from: aom  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class aom implements Comparator {
    private final /* synthetic */ int a;

    public /* synthetic */ aom(int i) {
        this.a = i;
    }

    public final int compare(Object obj, Object obj2) {
        hah hah;
        switch (this.a) {
            case 0:
                return ((adv) obj2).i - ((adv) obj).i;
            case 1:
                byte[] bArr = (byte[]) obj;
                byte[] bArr2 = (byte[]) obj2;
                oz ozVar = ub.a;
                int length = bArr.length;
                int length2 = bArr2.length;
                if (length != length2) {
                    return length - length2;
                }
                for (int i = 0; i < bArr.length; i++) {
                    byte b = bArr[i];
                    byte b2 = bArr2[i];
                    if (b != b2) {
                        return b - b2;
                    }
                }
                return 0;
            case 2:
                Integer num = (Integer) obj;
                Integer num2 = (Integer) obj2;
                if (num.intValue() == -1) {
                    if (num2.intValue() == -1) {
                        return 0;
                    }
                    return -1;
                } else if (num2.intValue() == -1) {
                    return 1;
                } else {
                    return num.intValue() - num2.intValue();
                }
            case 3:
                return ((aos) ((List) obj).get(0)).compareTo((aos) ((List) obj2).get(0));
            case 4:
                return ((aor) Collections.max((List) obj)).compareTo((aor) Collections.max((List) obj2));
            case 5:
                List list = (List) obj;
                List list2 = (List) obj2;
                return gwo.b.d((apc) Collections.max(list, new aom(7)), (apc) Collections.max(list2, new aom(7)), new aom(7)).b(list.size(), list2.size()).d((apc) Collections.max(list, new aom(8)), (apc) Collections.max(list2, new aom(8)), new aom(8)).a();
            case 6:
                return ((aoz) ((List) obj).get(0)).compareTo((aoz) ((List) obj2).get(0));
            case 7:
                apc apc = (apc) obj;
                apc apc2 = (apc) obj2;
                gwo e = gwo.b.e(apc.h, apc2.h).b(apc.m, apc2.m).e(apc.n, apc2.n).e(apc.i, apc2.i).e(apc.e, apc2.e).e(apc.g, apc2.g).d(Integer.valueOf(apc.l), Integer.valueOf(apc2.l), haw.a).e(apc.o, apc2.o).e(apc.p, apc2.p);
                if (apc.o && apc.p) {
                    e = e.b(apc.q, apc2.q);
                }
                return e.a();
            case 8:
                apc apc3 = (apc) obj;
                apc apc4 = (apc) obj2;
                if (!apc3.e || !apc3.h) {
                    hah = new hax(apd.a);
                } else {
                    hah = apd.a;
                }
                gwo gwo = gwo.b;
                aov aov = apc3.f;
                return gwo.d(Integer.valueOf(apc3.k), Integer.valueOf(apc4.k), hah).d(Integer.valueOf(apc3.j), Integer.valueOf(apc4.j), hah).a();
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return ((apo) obj).a - ((apo) obj2).a;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return Float.compare(((apo) obj).c, ((apo) obj2).c);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return jji.aB((Integer) ((Map.Entry) obj).getKey(), (Integer) ((Map.Entry) obj2).getKey());
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return jji.aB((Integer) ((Map.Entry) obj).getKey(), (Integer) ((Map.Entry) obj2).getKey());
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return jji.aB(((avy) obj).a, ((avy) obj2).a);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return jji.aB(((awa) obj).a, ((awa) obj2).a);
            case 15:
                return ((String) ((bty) ((iul) obj).c).c.get(0)).compareTo((String) ((bty) ((iul) obj2).c).c.get(0));
            case 16:
                return cqh.F((ctg) obj).compareTo(cqh.F((ctg) obj2));
            case 17:
                return cqh.F(((cxg) obj).a).compareTo(cqh.F(((cxg) obj2).a));
            case 18:
                return cqh.F((ctj) obj).compareTo(cqh.F((ctj) obj2));
            case 19:
                return -Long.compare(((File) obj).lastModified(), ((File) obj2).lastModified());
            default:
                return ((ded) obj).o().compareTo(((ded) obj2).o());
        }
    }
}
