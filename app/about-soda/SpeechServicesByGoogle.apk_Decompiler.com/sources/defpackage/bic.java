package defpackage;

/* renamed from: bic  reason: default package */
/* compiled from: PG */
public final class bic {
    private static final String a = bbk.b("EnqueueRunnable");

    /* JADX WARNING: type inference failed for: r5v6, types: [java.util.Set, java.lang.Object] */
    /* JADX WARNING: Removed duplicated region for block: B:139:0x02ed  */
    /* JADX WARNING: Removed duplicated region for block: B:172:? A[RETURN, SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:88:0x01ad A[Catch:{ all -> 0x0325, all -> 0x02d9, all -> 0x02c6, all -> 0x0272, all -> 0x032d }] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static void a(defpackage.bcy r23) {
        /*
            r0 = r23
            java.util.HashSet r1 = new java.util.HashSet
            r1.<init>()
            java.util.List r2 = r0.d
            r1.addAll(r2)
            java.util.HashSet r2 = new java.util.HashSet
            r2.<init>()
            java.util.Iterator r3 = r1.iterator()
        L_0x0015:
            boolean r4 = r3.hasNext()
            if (r4 == 0) goto L_0x0036
            java.lang.Object r4 = r3.next()
            java.lang.String r4 = (java.lang.String) r4
            boolean r4 = r2.contains(r4)
            if (r4 != 0) goto L_0x0028
            goto L_0x0015
        L_0x0028:
            java.lang.IllegalStateException r1 = new java.lang.IllegalStateException
            java.lang.String r2 = "WorkContinuation has cycles ("
            java.lang.String r3 = ")"
            java.lang.String r0 = defpackage.a.ao(r0, r2, r3)
            r1.<init>(r0)
            throw r1
        L_0x0036:
            java.util.List r2 = r0.d
            r1.removeAll(r2)
            bdm r1 = r0.a
            androidx.work.impl.WorkDatabase r2 = r1.d
            r2.l()
            bam r1 = r1.c     // Catch:{ all -> 0x032d }
            java.lang.String r3 = "workDatabase"
            defpackage.jnu.e(r2, r3)     // Catch:{ all -> 0x032d }
            java.lang.String r3 = "configuration"
            defpackage.jnu.e(r1, r3)     // Catch:{ all -> 0x032d }
            r3 = 1
            bcy[] r4 = new defpackage.bcy[r3]     // Catch:{ all -> 0x032d }
            r5 = 0
            r4[r5] = r0     // Catch:{ all -> 0x032d }
            java.util.List r4 = defpackage.jji.p(r4)     // Catch:{ all -> 0x032d }
            r6 = r5
        L_0x0059:
            boolean r7 = r4.isEmpty()     // Catch:{ all -> 0x032d }
            if (r7 != 0) goto L_0x00a4
            java.lang.Object r7 = defpackage.jji.t(r4)     // Catch:{ all -> 0x032d }
            bcy r7 = (defpackage.bcy) r7     // Catch:{ all -> 0x032d }
            java.util.List r7 = r7.c     // Catch:{ all -> 0x032d }
            java.lang.String r8 = "current.work"
            defpackage.jnu.d(r7, r8)     // Catch:{ all -> 0x032d }
            boolean r8 = r7 instanceof java.util.Collection     // Catch:{ all -> 0x032d }
            if (r8 == 0) goto L_0x0078
            boolean r8 = r7.isEmpty()     // Catch:{ all -> 0x032d }
            if (r8 == 0) goto L_0x0078
            r8 = r5
            goto L_0x00a2
        L_0x0078:
            java.util.Iterator r7 = r7.iterator()     // Catch:{ all -> 0x032d }
            r8 = r5
        L_0x007d:
            boolean r9 = r7.hasNext()     // Catch:{ all -> 0x032d }
            if (r9 == 0) goto L_0x00a2
            java.lang.Object r9 = r7.next()     // Catch:{ all -> 0x032d }
            bmu r9 = (defpackage.bmu) r9     // Catch:{ all -> 0x032d }
            java.lang.Object r9 = r9.b     // Catch:{ all -> 0x032d }
            bhe r9 = (defpackage.bhe) r9     // Catch:{ all -> 0x032d }
            baq r9 = r9.k     // Catch:{ all -> 0x032d }
            boolean r9 = r9.b()     // Catch:{ all -> 0x032d }
            if (r9 == 0) goto L_0x007d
            int r8 = r8 + 1
            if (r8 < 0) goto L_0x009a
            goto L_0x007d
        L_0x009a:
            java.lang.ArithmeticException r0 = new java.lang.ArithmeticException     // Catch:{ all -> 0x032d }
            java.lang.String r1 = "Count overflow has happened."
            r0.<init>(r1)     // Catch:{ all -> 0x032d }
            throw r0     // Catch:{ all -> 0x032d }
        L_0x00a2:
            int r6 = r6 + r8
            goto L_0x0059
        L_0x00a4:
            if (r6 != 0) goto L_0x00a7
            goto L_0x00db
        L_0x00a7:
            bhf r4 = r2.A()     // Catch:{ all -> 0x032d }
            r7 = r4
            bhx r7 = (defpackage.bhx) r7     // Catch:{ all -> 0x032d }
            aus r7 = r7.a     // Catch:{ all -> 0x032d }
            java.lang.String r8 = "Select COUNT(*) FROM workspec WHERE LENGTH(content_uri_triggers)<>0 AND state NOT IN (2, 3, 5)"
            auu r8 = defpackage.auu.a(r8, r5)     // Catch:{ all -> 0x032d }
            r7.k()     // Catch:{ all -> 0x032d }
            bhx r4 = (defpackage.bhx) r4     // Catch:{ all -> 0x032d }
            aus r4 = r4.a     // Catch:{ all -> 0x032d }
            android.database.Cursor r4 = defpackage.vy.f(r4, r8, r5)     // Catch:{ all -> 0x032d }
            boolean r7 = r4.moveToFirst()     // Catch:{ all -> 0x0325 }
            if (r7 == 0) goto L_0x00cc
            int r7 = r4.getInt(r5)     // Catch:{ all -> 0x0325 }
            goto L_0x00cd
        L_0x00cc:
            r7 = r5
        L_0x00cd:
            r4.close()     // Catch:{ all -> 0x032d }
            r8.j()     // Catch:{ all -> 0x032d }
            int r1 = r1.l     // Catch:{ all -> 0x032d }
            int r1 = r7 + r6
            r4 = 8
            if (r1 > r4) goto L_0x02f9
        L_0x00db:
            java.util.HashSet r1 = new java.util.HashSet     // Catch:{ all -> 0x032d }
            r1.<init>()     // Catch:{ all -> 0x032d }
            bdm r4 = r0.a     // Catch:{ all -> 0x032d }
            java.util.List r6 = r0.c     // Catch:{ all -> 0x032d }
            java.lang.String[] r7 = new java.lang.String[r5]     // Catch:{ all -> 0x032d }
            java.lang.Object[] r1 = r1.toArray(r7)     // Catch:{ all -> 0x032d }
            java.lang.String[] r1 = (java.lang.String[]) r1     // Catch:{ all -> 0x032d }
            java.lang.String r7 = r0.b     // Catch:{ all -> 0x032d }
            int r8 = r0.f     // Catch:{ all -> 0x032d }
            bam r9 = r4.c     // Catch:{ all -> 0x032d }
            androidx.wear.ambient.AmbientModeSupport$AmbientCallback r9 = r9.q     // Catch:{ all -> 0x032d }
            long r9 = java.lang.System.currentTimeMillis()     // Catch:{ all -> 0x032d }
            androidx.work.impl.WorkDatabase r11 = r4.d     // Catch:{ all -> 0x032d }
            if (r1 == 0) goto L_0x0101
            int r12 = r1.length     // Catch:{ all -> 0x032d }
            if (r12 <= 0) goto L_0x0101
            r12 = r3
            goto L_0x0102
        L_0x0101:
            r12 = r5
        L_0x0102:
            if (r12 == 0) goto L_0x014a
            int r13 = r1.length     // Catch:{ all -> 0x032d }
            r15 = r3
            r14 = r5
            r16 = r14
            r17 = r16
        L_0x010b:
            if (r14 >= r13) goto L_0x014f
            r5 = r1[r14]     // Catch:{ all -> 0x032d }
            bhf r3 = r11.A()     // Catch:{ all -> 0x032d }
            bhe r3 = r3.b(r5)     // Catch:{ all -> 0x032d }
            if (r3 != 0) goto L_0x012e
            bbk r1 = defpackage.bbk.a()     // Catch:{ all -> 0x032d }
            java.lang.String r3 = a     // Catch:{ all -> 0x032d }
            java.lang.String r4 = "Prerequisite "
            java.lang.String r6 = " doesn't exist; not enqueuing"
            java.lang.String r4 = defpackage.a.ap(r5, r4, r6)     // Catch:{ all -> 0x032d }
            r1.c(r3, r4)     // Catch:{ all -> 0x032d }
        L_0x012a:
            r1 = 1
            r5 = 0
            goto L_0x02e3
        L_0x012e:
            bbx r3 = r3.c     // Catch:{ all -> 0x032d }
            bbx r5 = defpackage.bbx.SUCCEEDED     // Catch:{ all -> 0x032d }
            if (r3 != r5) goto L_0x0136
            r5 = 1
            goto L_0x0137
        L_0x0136:
            r5 = 0
        L_0x0137:
            r15 = r15 & r5
            bbx r5 = defpackage.bbx.FAILED     // Catch:{ all -> 0x032d }
            if (r3 != r5) goto L_0x013f
            r16 = 1
            goto L_0x0145
        L_0x013f:
            bbx r5 = defpackage.bbx.CANCELLED     // Catch:{ all -> 0x032d }
            if (r3 != r5) goto L_0x0145
            r17 = 1
        L_0x0145:
            int r14 = r14 + 1
            r3 = 1
            r5 = 0
            goto L_0x010b
        L_0x014a:
            r15 = 1
            r16 = 0
            r17 = 0
        L_0x014f:
            boolean r3 = android.text.TextUtils.isEmpty(r7)     // Catch:{ all -> 0x032d }
            if (r3 != 0) goto L_0x01a2
            if (r12 != 0) goto L_0x01a2
            bhf r5 = r11.A()     // Catch:{ all -> 0x032d }
            java.util.List r5 = r5.e(r7)     // Catch:{ all -> 0x032d }
            boolean r13 = r5.isEmpty()     // Catch:{ all -> 0x032d }
            if (r13 != 0) goto L_0x01a2
            r13 = 2
            if (r8 != r13) goto L_0x0183
            java.util.Iterator r8 = r5.iterator()     // Catch:{ all -> 0x032d }
        L_0x016c:
            boolean r13 = r8.hasNext()     // Catch:{ all -> 0x032d }
            if (r13 == 0) goto L_0x0183
            java.lang.Object r13 = r8.next()     // Catch:{ all -> 0x032d }
            bhc r13 = (defpackage.bhc) r13     // Catch:{ all -> 0x032d }
            bbx r13 = r13.b     // Catch:{ all -> 0x032d }
            bbx r14 = defpackage.bbx.ENQUEUED     // Catch:{ all -> 0x032d }
            if (r13 == r14) goto L_0x012a
            bbx r14 = defpackage.bbx.RUNNING     // Catch:{ all -> 0x032d }
            if (r13 != r14) goto L_0x016c
            goto L_0x012a
        L_0x0183:
            defpackage.xm.h(r7, r4)     // Catch:{ all -> 0x032d }
            bhf r8 = r11.A()     // Catch:{ all -> 0x032d }
            java.util.Iterator r5 = r5.iterator()     // Catch:{ all -> 0x032d }
        L_0x018e:
            boolean r13 = r5.hasNext()     // Catch:{ all -> 0x032d }
            if (r13 == 0) goto L_0x01a0
            java.lang.Object r13 = r5.next()     // Catch:{ all -> 0x032d }
            bhc r13 = (defpackage.bhc) r13     // Catch:{ all -> 0x032d }
            java.lang.String r13 = r13.a     // Catch:{ all -> 0x032d }
            r8.f(r13)     // Catch:{ all -> 0x032d }
            goto L_0x018e
        L_0x01a0:
            r5 = 1
            goto L_0x01a3
        L_0x01a2:
            r5 = 0
        L_0x01a3:
            java.util.Iterator r6 = r6.iterator()     // Catch:{ all -> 0x032d }
        L_0x01a7:
            boolean r8 = r6.hasNext()     // Catch:{ all -> 0x032d }
            if (r8 == 0) goto L_0x02e2
            java.lang.Object r8 = r6.next()     // Catch:{ all -> 0x032d }
            bmu r8 = (defpackage.bmu) r8     // Catch:{ all -> 0x032d }
            java.lang.Object r13 = r8.b     // Catch:{ all -> 0x032d }
            if (r12 == 0) goto L_0x01d9
            if (r15 != 0) goto L_0x01d9
            if (r16 == 0) goto L_0x01c5
            bbx r14 = defpackage.bbx.FAILED     // Catch:{ all -> 0x032d }
            r19 = r6
            r6 = r13
            bhe r6 = (defpackage.bhe) r6     // Catch:{ all -> 0x032d }
            r6.c = r14     // Catch:{ all -> 0x032d }
            goto L_0x01e0
        L_0x01c5:
            r19 = r6
            if (r17 == 0) goto L_0x01d1
            bbx r6 = defpackage.bbx.CANCELLED     // Catch:{ all -> 0x032d }
            r14 = r13
            bhe r14 = (defpackage.bhe) r14     // Catch:{ all -> 0x032d }
            r14.c = r6     // Catch:{ all -> 0x032d }
            goto L_0x01e0
        L_0x01d1:
            bbx r6 = defpackage.bbx.BLOCKED     // Catch:{ all -> 0x032d }
            r14 = r13
            bhe r14 = (defpackage.bhe) r14     // Catch:{ all -> 0x032d }
            r14.c = r6     // Catch:{ all -> 0x032d }
            goto L_0x01e0
        L_0x01d9:
            r19 = r6
            r6 = r13
            bhe r6 = (defpackage.bhe) r6     // Catch:{ all -> 0x032d }
            r6.o = r9     // Catch:{ all -> 0x032d }
        L_0x01e0:
            r6 = r13
            bhe r6 = (defpackage.bhe) r6     // Catch:{ all -> 0x032d }
            bbx r6 = r6.c     // Catch:{ all -> 0x032d }
            bbx r14 = defpackage.bbx.ENQUEUED     // Catch:{ all -> 0x032d }
            if (r6 != r14) goto L_0x01ed
            r6 = 1
            r18 = 0
            goto L_0x01f0
        L_0x01ed:
            r6 = 1
            r18 = 1
        L_0x01f0:
            r14 = r18 ^ 1
            r5 = r5 | r14
            bhf r6 = r11.A()     // Catch:{ all -> 0x032d }
            java.util.List r14 = r4.e     // Catch:{ all -> 0x032d }
            bhe r13 = (defpackage.bhe) r13     // Catch:{ all -> 0x032d }
            bhe r13 = defpackage.yh.o(r14, r13)     // Catch:{ all -> 0x032d }
            r14 = r6
            bhx r14 = (defpackage.bhx) r14     // Catch:{ all -> 0x032d }
            aus r14 = r14.a     // Catch:{ all -> 0x032d }
            r14.k()     // Catch:{ all -> 0x032d }
            r14 = r6
            bhx r14 = (defpackage.bhx) r14     // Catch:{ all -> 0x032d }
            aus r14 = r14.a     // Catch:{ all -> 0x032d }
            r14.l()     // Catch:{ all -> 0x032d }
            r14 = r6
            bhx r14 = (defpackage.bhx) r14     // Catch:{ all -> 0x02d9 }
            aub r14 = r14.b     // Catch:{ all -> 0x02d9 }
            r14.b(r13)     // Catch:{ all -> 0x02d9 }
            r13 = r6
            bhx r13 = (defpackage.bhx) r13     // Catch:{ all -> 0x02d9 }
            aus r13 = r13.a     // Catch:{ all -> 0x02d9 }
            r13.o()     // Catch:{ all -> 0x02d9 }
            bhx r6 = (defpackage.bhx) r6     // Catch:{ all -> 0x032d }
            aus r6 = r6.a     // Catch:{ all -> 0x032d }
            r6.m()     // Catch:{ all -> 0x032d }
            if (r12 == 0) goto L_0x027b
            int r13 = r1.length     // Catch:{ all -> 0x032d }
            r14 = 0
        L_0x022a:
            if (r14 >= r13) goto L_0x027b
            r6 = r1[r14]     // Catch:{ all -> 0x032d }
            r20 = r1
            bvj r1 = new bvj     // Catch:{ all -> 0x032d }
            r21 = r4
            java.lang.String r4 = r8.a()     // Catch:{ all -> 0x032d }
            r22 = r5
            r5 = 0
            r1.<init>((java.lang.String) r4, (java.lang.String) r6, (byte[]) r5, (byte[]) r5)     // Catch:{ all -> 0x032d }
            bgd r4 = r11.u()     // Catch:{ all -> 0x032d }
            r5 = r4
            bgf r5 = (defpackage.bgf) r5     // Catch:{ all -> 0x032d }
            aus r5 = r5.a     // Catch:{ all -> 0x032d }
            r5.k()     // Catch:{ all -> 0x032d }
            r5 = r4
            bgf r5 = (defpackage.bgf) r5     // Catch:{ all -> 0x032d }
            aus r5 = r5.a     // Catch:{ all -> 0x032d }
            r5.l()     // Catch:{ all -> 0x032d }
            r5 = r4
            bgf r5 = (defpackage.bgf) r5     // Catch:{ all -> 0x0272 }
            aub r5 = r5.b     // Catch:{ all -> 0x0272 }
            r5.b(r1)     // Catch:{ all -> 0x0272 }
            r1 = r4
            bgf r1 = (defpackage.bgf) r1     // Catch:{ all -> 0x0272 }
            aus r1 = r1.a     // Catch:{ all -> 0x0272 }
            r1.o()     // Catch:{ all -> 0x0272 }
            bgf r4 = (defpackage.bgf) r4     // Catch:{ all -> 0x032d }
            aus r1 = r4.a     // Catch:{ all -> 0x032d }
            r1.m()     // Catch:{ all -> 0x032d }
            int r14 = r14 + 1
            r1 = r20
            r4 = r21
            r5 = r22
            goto L_0x022a
        L_0x0272:
            r0 = move-exception
            bgf r4 = (defpackage.bgf) r4     // Catch:{ all -> 0x032d }
            aus r1 = r4.a     // Catch:{ all -> 0x032d }
            r1.m()     // Catch:{ all -> 0x032d }
            throw r0     // Catch:{ all -> 0x032d }
        L_0x027b:
            r20 = r1
            r21 = r4
            r22 = r5
            bhy r1 = r11.B()     // Catch:{ all -> 0x032d }
            java.lang.String r4 = r8.a()     // Catch:{ all -> 0x032d }
            java.lang.Object r5 = r8.a     // Catch:{ all -> 0x032d }
            defpackage.wg.e(r1, r4, r5)     // Catch:{ all -> 0x032d }
            if (r3 != 0) goto L_0x02cf
            bgu r1 = r11.y()     // Catch:{ all -> 0x032d }
            bvj r4 = new bvj     // Catch:{ all -> 0x032d }
            java.lang.String r5 = r8.a()     // Catch:{ all -> 0x032d }
            r6 = 0
            r4.<init>(r7, r5, r6)     // Catch:{ all -> 0x032d }
            r5 = r1
            bgw r5 = (defpackage.bgw) r5     // Catch:{ all -> 0x032d }
            aus r5 = r5.a     // Catch:{ all -> 0x032d }
            r5.k()     // Catch:{ all -> 0x032d }
            r5 = r1
            bgw r5 = (defpackage.bgw) r5     // Catch:{ all -> 0x032d }
            aus r5 = r5.a     // Catch:{ all -> 0x032d }
            r5.l()     // Catch:{ all -> 0x032d }
            r5 = r1
            bgw r5 = (defpackage.bgw) r5     // Catch:{ all -> 0x02c6 }
            aub r5 = r5.b     // Catch:{ all -> 0x02c6 }
            r5.b(r4)     // Catch:{ all -> 0x02c6 }
            r4 = r1
            bgw r4 = (defpackage.bgw) r4     // Catch:{ all -> 0x02c6 }
            aus r4 = r4.a     // Catch:{ all -> 0x02c6 }
            r4.o()     // Catch:{ all -> 0x02c6 }
            bgw r1 = (defpackage.bgw) r1     // Catch:{ all -> 0x032d }
            aus r1 = r1.a     // Catch:{ all -> 0x032d }
            r1.m()     // Catch:{ all -> 0x032d }
            goto L_0x02cf
        L_0x02c6:
            r0 = move-exception
            bgw r1 = (defpackage.bgw) r1     // Catch:{ all -> 0x032d }
            aus r1 = r1.a     // Catch:{ all -> 0x032d }
            r1.m()     // Catch:{ all -> 0x032d }
            throw r0     // Catch:{ all -> 0x032d }
        L_0x02cf:
            r6 = r19
            r1 = r20
            r4 = r21
            r5 = r22
            goto L_0x01a7
        L_0x02d9:
            r0 = move-exception
            bhx r6 = (defpackage.bhx) r6     // Catch:{ all -> 0x032d }
            aus r1 = r6.a     // Catch:{ all -> 0x032d }
            r1.m()     // Catch:{ all -> 0x032d }
            throw r0     // Catch:{ all -> 0x032d }
        L_0x02e2:
            r1 = 1
        L_0x02e3:
            r0.e = r1     // Catch:{ all -> 0x032d }
            r2.o()     // Catch:{ all -> 0x032d }
            r2.m()
            if (r5 == 0) goto L_0x02f8
            bdm r0 = r0.a
            bam r1 = r0.c
            androidx.work.impl.WorkDatabase r2 = r0.d
            java.util.List r0 = r0.e
            defpackage.bct.a(r1, r2, r0)
        L_0x02f8:
            return
        L_0x02f9:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException     // Catch:{ all -> 0x032d }
            java.lang.StringBuilder r1 = new java.lang.StringBuilder     // Catch:{ all -> 0x032d }
            r1.<init>()     // Catch:{ all -> 0x032d }
            java.lang.String r3 = "Too many workers with contentUriTriggers are enqueued:\ncontentUriTrigger workers limit: "
            r1.append(r3)     // Catch:{ all -> 0x032d }
            r1.append(r4)     // Catch:{ all -> 0x032d }
            java.lang.String r3 = ";\nalready enqueued count: "
            r1.append(r3)     // Catch:{ all -> 0x032d }
            r1.append(r7)     // Catch:{ all -> 0x032d }
            java.lang.String r3 = ";\ncurrent enqueue operation count: "
            r1.append(r3)     // Catch:{ all -> 0x032d }
            r1.append(r6)     // Catch:{ all -> 0x032d }
            java.lang.String r3 = ".\nTo address this issue you can: \n1. enqueue less workers or batch some of workers with content uri triggers together;\n2. increase limit via Configuration.Builder.setContentUriTriggerWorkersLimit;\nPlease beware that workers with content uri triggers immediately occupy slots in JobScheduler so no updates to content uris are missed."
            r1.append(r3)     // Catch:{ all -> 0x032d }
            java.lang.String r1 = r1.toString()     // Catch:{ all -> 0x032d }
            r0.<init>(r1)     // Catch:{ all -> 0x032d }
            throw r0     // Catch:{ all -> 0x032d }
        L_0x0325:
            r0 = move-exception
            r4.close()     // Catch:{ all -> 0x032d }
            r8.j()     // Catch:{ all -> 0x032d }
            throw r0     // Catch:{ all -> 0x032d }
        L_0x032d:
            r0 = move-exception
            r2.m()
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bic.a(bcy):void");
    }
}
