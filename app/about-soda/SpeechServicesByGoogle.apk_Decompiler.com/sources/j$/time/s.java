package j$.time;

import java.io.Externalizable;
import java.io.InvalidClassException;
import java.io.ObjectInput;
import java.io.ObjectOutput;
import java.io.Serializable;
import java.io.StreamCorruptedException;

final class s implements Externalizable {
    private static final long serialVersionUID = -7683839454370182990L;
    private byte a;
    private Object b;

    public s() {
    }

    static Serializable a(ObjectInput objectInput) {
        return b(objectInput.readByte(), objectInput);
    }

    private static Serializable b(byte b2, ObjectInput objectInput) {
        switch (b2) {
            case 1:
                Duration duration = Duration.ZERO;
                return Duration.ofSeconds(objectInput.readLong(), (long) objectInput.readInt());
            case 2:
                Instant instant = Instant.EPOCH;
                return Instant.ofEpochSecond(objectInput.readLong(), (long) objectInput.readInt());
            case 3:
                g gVar = g.d;
                return g.T(objectInput.readInt(), objectInput.readByte(), objectInput.readByte());
            case 4:
                return k.X(objectInput);
            case 5:
                i iVar = i.c;
                g gVar2 = g.d;
                return i.Q(g.T(objectInput.readInt(), objectInput.readByte(), objectInput.readByte()), k.X(objectInput));
            case 6:
                return ZonedDateTime.L(objectInput);
            case 7:
                int i = y.d;
                return ZoneId.J(objectInput.readUTF(), false);
            case 8:
                return x.T(objectInput);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER:
                return q.K(objectInput);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER:
                return p.K(objectInput);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER:
                int i2 = u.b;
                return u.I(objectInput.readInt());
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER:
                return w.M(objectInput);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER:
                return n.I(objectInput);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER:
                return r.a(objectInput);
            default:
                throw new StreamCorruptedException("Unknown serialized type");
        }
    }

    private Object readResolve() {
        return this.b;
    }

    public final void readExternal(ObjectInput objectInput) {
        byte readByte = objectInput.readByte();
        this.a = readByte;
        this.b = b(readByte, objectInput);
    }

    public final void writeExternal(ObjectOutput objectOutput) {
        byte b2 = this.a;
        Object obj = this.b;
        objectOutput.writeByte(b2);
        switch (b2) {
            case 1:
                ((Duration) obj).writeExternal(objectOutput);
                return;
            case 2:
                ((Instant) obj).N(objectOutput);
                return;
            case 3:
                ((g) obj).f0(objectOutput);
                return;
            case 4:
                ((k) obj).c0(objectOutput);
                return;
            case 5:
                ((i) obj).Z(objectOutput);
                return;
            case 6:
                ((ZonedDateTime) obj).N(objectOutput);
                return;
            case 7:
                ((y) obj).O(objectOutput);
                return;
            case 8:
                ((x) obj).U(objectOutput);
                return;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER:
                ((q) obj).writeExternal(objectOutput);
                return;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER:
                ((p) obj).writeExternal(objectOutput);
                return;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER:
                ((u) obj).M(objectOutput);
                return;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER:
                ((w) obj).P(objectOutput);
                return;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER:
                ((n) obj).J(objectOutput);
                return;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER:
                ((r) obj).writeExternal(objectOutput);
                return;
            default:
                throw new InvalidClassException("Unknown serialized type");
        }
    }

    s(byte b2, Object obj) {
        this.a = b2;
        this.b = obj;
    }
}
