package defpackage;

import android.content.SharedPreferences;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/* renamed from: cwi  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwi implements hko {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ cwi(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r2v14, types: [java.lang.Object, cxa] */
    /* JADX WARNING: type inference failed for: r1v26, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v33, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r10v76, types: [cuk, java.lang.Object] */
    public final hme a(Object obj) {
        switch (this.b) {
            case 0:
                Void voidR = (Void) obj;
                cwm cwm = (cwm) this.a;
                SharedPreferences B = cqh.B(cwm.b, "gms_icing_mdd_manager_metadata", cwm.g);
                if (B.getBoolean("mdd_migrated_to_offroad", false)) {
                    return hma.a;
                }
                cyh.c("%s Clearing MDD as device isn't migrated to offroad.", "MDDManager");
                return ftd.K(cwm.a(), new brg(B, 20), cwm.h);
            case 1:
                ArrayList arrayList = new ArrayList();
                for (cxg cxg : (List) obj) {
                    Object obj2 = this.a;
                    cwm cwm2 = (cwm) obj2;
                    arrayList.add(czw.e(cwm2.k.c(cxg.a.b)).g(new cwa(obj2, (Object) cxg, 19), cwm2.h).g(new cwa(obj2, (Object) cxg, 20), cwm2.h));
                }
                return cqh.U(arrayList).n(new ctw(13), hld.a);
            case 2:
                Void voidR2 = (Void) obj;
                Object obj3 = this.a;
                cwm cwm3 = (cwm) obj3;
                dbw dbw = cwm3.m;
                SharedPreferences B2 = cqh.B(dbw.a, "gms_icing_mdd_shared_file_manager_metadata", (grh) dbw.g);
                if (B2.contains("migrated_to_new_file_key")) {
                    if (B2.getBoolean("migrated_to_new_file_key", false)) {
                        cqh.x(dbw.a);
                    }
                    B2.edit().remove("migrated_to_new_file_key").commit();
                }
                return ftd.L(hfc.K(true), new cvi(obj3, 20), cwm3.h);
            case 3:
                Void voidR3 = (Void) obj;
                Object obj4 = this.a;
                cwm cwm4 = (cwm) obj4;
                return ftd.L(cwm4.e.d(), new cwi(obj4, 8), cwm4.h);
            case 4:
                Void voidR4 = (Void) obj;
                return ((cwm) this.a).d.f();
            case 5:
                Void voidR5 = (Void) obj;
                if (!ikc.a.a().g()) {
                    return hfc.K(-1);
                }
                return czw.e(((cwm) this.a).j.b()).b(IOException.class, new cwh(0), hld.a).f(new cwh(2), hld.a);
            case 6:
                Void voidR6 = (Void) obj;
                return ((cwm) this.a).d.b();
            case 7:
                Void voidR7 = (Void) obj;
                return ((cwm) this.a).d.c();
            case 8:
                if (((Boolean) obj).booleanValue()) {
                    return hma.a;
                }
                Object obj5 = this.a;
                cyh.m("%s Clearing MDD since FilesMetadata failed or needs migration.", "MDDManager");
                return ((cwm) obj5).a();
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                Void voidR8 = (Void) obj;
                Object obj6 = this.a;
                cwm cwm5 = (cwm) obj6;
                SharedPreferences B3 = cqh.B(cwm5.b, "gms_icing_mdd_manager_metadata", cwm5.g);
                if (!B3.contains("gms_icing_mdd_reset_trigger")) {
                    B3.edit().putInt("gms_icing_mdd_reset_trigger", cqh.p()).commit();
                }
                int i = B3.getInt("gms_icing_mdd_reset_trigger", 0);
                int p = cqh.p();
                if (i >= p) {
                    return hma.a;
                }
                B3.edit().putInt("gms_icing_mdd_reset_trigger", p).commit();
                cyh.c("%s Received reset trigger. Clearing all Mdd data.", "MDDManager");
                cwm5.l.d(1045);
                dbw dbw2 = cwm5.m;
                return ftd.L(ftd.L(dbw2.j.c(), new cwi(dbw2, 13), dbw2.l), new cwi(obj6, 10), cwm5.h);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                Void voidR9 = (Void) obj;
                Object obj7 = this.a;
                cwm cwm6 = (cwm) obj7;
                return ftd.L(cwm6.e.a(), new cwi(obj7, 6), cwm6.h);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                Void voidR10 = (Void) obj;
                cwh cwh = new cwh(13);
                cwo cwo = (cwo) this.a;
                return cwo.c.b(cwh, cwo.b);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                ctl ctl = (ctl) obj;
                if (ctl != null) {
                    return hfc.K(ctl);
                }
                cyh.h("%s: getSharedFile called on file that doesn't exist! Key = %s", "SharedFileManager", this.a);
                return hfc.J(new cwz());
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                List<ctj> list = (List) obj;
                ArrayList arrayList2 = new ArrayList();
                Object obj8 = this.a;
                try {
                    for (ctj i2 : list) {
                        arrayList2.add(((dbw) obj8).i(i2));
                    }
                } catch (Exception unused) {
                    ((dbw) obj8).d.a();
                }
                return cqh.U(arrayList2).o(new cpw(obj8, 4), ((dbw) obj8).l);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                if (((Boolean) obj).booleanValue()) {
                    return hfc.K(true);
                }
                cyh.h("%s: Unable to modify file subscription for key %s", "SharedFileManager", this.a);
                return hfc.K(false);
            case 15:
                if (((Boolean) obj).booleanValue()) {
                    return hfc.K(true);
                }
                cyh.h("%s: Unable to write back subscription for file entry with %s", "SharedFileManager", this.a);
                return hfc.K(false);
            case 16:
                List list2 = (List) obj;
                ArrayList arrayList3 = new ArrayList();
                Iterator it = list2.iterator();
                while (true) {
                    Object obj9 = this.a;
                    if (!it.hasNext()) {
                        return cqh.U(arrayList3).o(new ctp(list2, arrayList3, 11, (byte[]) null), ((cxb) obj9).a);
                    }
                    arrayList3.add(((cxb) obj9).g((ctg) it.next()));
                }
            case 17:
                ((csi) this.a).addSuppressed((IOException) obj);
                return hma.a;
            case 18:
                Void voidR11 = (Void) obj;
                return hfc.J((Throwable) this.a);
            case 19:
                if (((Boolean) obj).booleanValue()) {
                    return hma.a;
                }
                cyh.h("%s: Unable to write back download info for file entry with %s", "DownloaderCallbackImpl", this.a);
                kml a2 = csi.a();
                a2.b = csh.UNABLE_TO_UPDATE_FILE_STATE_ERROR;
                return hfc.J(a2.a());
            default:
                ((csi) this.a).addSuppressed((IOException) obj);
                return hma.a;
        }
    }
}
