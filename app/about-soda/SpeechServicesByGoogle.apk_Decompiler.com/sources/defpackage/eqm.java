package defpackage;

import android.media.AudioRecordingConfiguration;
import java.util.Arrays;

/* renamed from: eqm  reason: default package */
/* compiled from: PG */
public final class eqm {
    private final grh a;
    private final dku b;

    public eqm(grh grh, dku dku) {
        jnu.e(grh, "systemMicUseOwnerChecker");
        jnu.e(dku, "audioSessionIdStore");
        this.a = grh;
        this.b = dku;
    }

    public final eqf a(AudioRecordingConfiguration audioRecordingConfiguration, eqc... eqcArr) {
        jnu.e(audioRecordingConfiguration, "configuration");
        jnu.e(eqcArr, "owners");
        if (jji.ac(eqcArr, eqc.SELF) && this.b.s(audioRecordingConfiguration.getClientAudioSessionId())) {
            return eqe.b;
        }
        eqm eqm = (eqm) this.a.e();
        if (eqm != null) {
            return eqm.a(audioRecordingConfiguration, (eqc[]) Arrays.copyOf(eqcArr, eqcArr.length));
        }
        return eqe.a;
    }
}
