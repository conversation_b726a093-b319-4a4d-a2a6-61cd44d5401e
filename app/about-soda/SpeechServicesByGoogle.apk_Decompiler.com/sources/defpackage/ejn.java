package defpackage;

/* renamed from: ejn  reason: default package */
/* compiled from: PG */
public final class ejn {
    public final hme a;
    public final dzq b;
    public final grh c;
    public final grh d;

    public ejn() {
        throw null;
    }

    public static ejn a() {
        htk l = dzm.c.l();
        dzx dzx = dzx.UNKNOWN_DISCONNECT_REASON;
        if (!l.b.B()) {
            l.u();
        }
        dzm dzm = (dzm) l.b;
        dzm.b = dzx.H;
        dzm.a |= 1;
        hme K = hfc.K((dzm) l.r());
        dzq dzq = dzq.c;
        gqd gqd = gqd.a;
        return new ejn(K, dzq, gqd, gqd);
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof ejn) {
            ejn ejn = (ejn) obj;
            if (!this.a.equals(ejn.a) || !this.b.equals(ejn.b) || !this.c.equals(ejn.c) || !this.d.equals(ejn.d)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        int hashCode = this.a.hashCode() ^ 1000003;
        dzq dzq = this.b;
        if (dzq.B()) {
            i = dzq.i();
        } else {
            int i2 = dzq.memoizedHashCode;
            if (i2 == 0) {
                i2 = dzq.i();
                dzq.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (((((hashCode * 1000003) ^ i) * 1000003) ^ this.c.hashCode()) * 1000003) ^ this.d.hashCode();
    }

    public final String toString() {
        grh grh = this.d;
        grh grh2 = this.c;
        dzq dzq = this.b;
        String obj = this.a.toString();
        String obj2 = dzq.toString();
        String obj3 = grh2.toString();
        String obj4 = grh.toString();
        return "AudioRouteData{audioRouteDisconnectStatus=" + obj + ", audioRouteType=" + obj2 + ", handoffDataOptional=" + obj3 + ", routeRef=" + obj4 + "}";
    }

    public ejn(hme hme, dzq dzq, grh grh, grh grh2) {
        if (hme != null) {
            this.a = hme;
            if (dzq != null) {
                this.b = dzq;
                this.c = grh;
                this.d = grh2;
                return;
            }
            throw new NullPointerException("Null audioRouteType");
        }
        throw new NullPointerException("Null audioRouteDisconnectStatus");
    }
}
