package defpackage;

import android.content.Context;
import androidx.work.WorkerParameters;
import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicInteger;

/* renamed from: bbj  reason: default package */
/* compiled from: PG */
public abstract class bbj {
    public Context a;
    public WorkerParameters b;
    public final AtomicInteger c = new AtomicInteger(-256);
    public boolean d;

    public bbj(Context context, WorkerParameters workerParameters) {
        if (context == null) {
            throw new IllegalArgumentException("Application Context is null");
        } else if (workerParameters != null) {
            this.a = context;
            this.b = workerParameters;
        } else {
            throw new IllegalArgumentException("WorkerParameters is null");
        }
    }

    public hme a() {
        return kq.f(new bbf());
    }

    public abstract hme b();

    public final bat d() {
        return this.b.b;
    }

    public final UUID e() {
        return this.b.a;
    }

    public final Executor f() {
        return this.b.d;
    }

    public final void g(int i) {
        this.c.compareAndSet(-256, i);
    }

    public final boolean h() {
        if (this.c.get() != -256) {
            return true;
        }
        return false;
    }
}
