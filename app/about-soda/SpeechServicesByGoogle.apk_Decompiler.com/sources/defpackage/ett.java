package defpackage;

/* renamed from: ett  reason: default package */
/* compiled from: PG */
public final class ett extends jmi implements jnf {
    public /* synthetic */ Object a;
    public final /* synthetic */ eua b;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public ett(eua eua, jlr jlr) {
        super(3, jlr);
        this.b = eua;
    }

    public final Object bk(Object obj) {
        jji.c(obj);
        Throwable th = (Throwable) this.a;
        ((hby) ((hby) eua.a.g().h(hdg.a, "ALT.GrpcARCRespSender")).i(th).j("com/google/android/libraries/search/audio/service/impl/StreamListeningSessionResponseSender$sendAudioBytesReceiverRegistry$2$2", "invokeSuspend", 247, "StreamListeningSessionResponseSender.kt")).r("#audio# Fail while getting flow's session audio data, shutting down");
        this.b.i(th);
        return jkd.a;
    }
}
