package defpackage;

import java.util.Set;

/* renamed from: evd  reason: default package */
/* compiled from: PG */
public final class evd {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/utils/SeamlessUtils");
    public static final Set b = jji.aM(1, 16);

    public static final grh a(dzn dzn) {
        eaa eaa;
        ebh ebh;
        jnu.e(dzn, "audioRouteRequestParams");
        dzq dzq = dzn.b;
        if (dzq == null) {
            dzq = dzq.c;
        }
        if (dzq.a == 5) {
            eaa = (eaa) dzq.b;
        } else {
            eaa = eaa.c;
        }
        jnu.d(eaa, "getHandoverRoute(...)");
        jnu.e(eaa, "<this>");
        if ((eaa.a & 1) != 0) {
            ebh = eaa.b;
            if (ebh == null) {
                ebh = ebh.c;
            }
        } else {
            ebh = null;
        }
        return grh.g(ebh);
    }

    public static final ebh b(ejn ejn, dze dze) {
        ebh ebh;
        jnu.e(ejn, "audioRouteData");
        jnu.e(dze, "params");
        jnu.e(dze, "<this>");
        if (dze.b == 3) {
            ebh = (ebh) dze.c;
        } else {
            ebh = null;
        }
        if (ebh == null) {
            return (ebh) ejn.c.e();
        }
        return ebh;
    }
}
