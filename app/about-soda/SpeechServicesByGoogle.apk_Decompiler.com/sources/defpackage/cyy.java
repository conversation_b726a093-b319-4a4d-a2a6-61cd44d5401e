package defpackage;

import android.content.Context;
import android.net.Uri;
import androidx.wear.ambient.AmbientMode;

/* renamed from: cyy  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cyy implements hkn {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    public final /* synthetic */ Object d;
    public final /* synthetic */ Object e;
    public final /* synthetic */ Object f;
    private final /* synthetic */ int g;

    public /* synthetic */ cyy(Context context, String str, kjd kjd, Uri uri, csv csv, csx csx, int i) {
        this.g = i;
        this.a = context;
        this.b = str;
        this.c = kjd;
        this.d = uri;
        this.e = csv;
        this.f = csx;
    }

    /* JADX WARNING: type inference failed for: r12v1, types: [dey, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r11v1, types: [hmh] */
    /* JADX WARNING: type inference failed for: r2v20, types: [java.util.concurrent.Executor] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme a() {
        /*
            r16 = this;
            r1 = r16
            java.lang.String r2 = ""
            java.lang.String r3 = "AndroidSharingUtil"
            int r0 = r1.g
            r4 = 0
            r5 = 1
            if (r0 == 0) goto L_0x00ce
            if (r0 == r5) goto L_0x009b
            java.lang.Object r0 = r1.c
            r7 = r0
            dhd r7 = (defpackage.dhd) r7
            boolean r0 = r7.d
            if (r0 == 0) goto L_0x001b
            hme r0 = defpackage.hma.a
            goto L_0x009a
        L_0x001b:
            java.util.HashSet r8 = new java.util.HashSet
            r8.<init>()
            java.util.List r0 = r7.b
            gxq r0 = (defpackage.gxq) r0
            hbq r0 = r0.listIterator()
        L_0x0028:
            java.lang.Object r2 = r1.d
            boolean r3 = r0.hasNext()
            if (r3 == 0) goto L_0x005e
            java.lang.Object r3 = r0.next()
            java.util.Collection r3 = (java.util.Collection) r3
            java.util.Iterator r3 = r3.iterator()
        L_0x003a:
            boolean r6 = r3.hasNext()
            if (r6 == 0) goto L_0x0028
            java.lang.Object r6 = r3.next()
            deh r6 = (defpackage.deh) r6
            ded r6 = r6.f()
            ddc r6 = r6.o()
            r8.add(r6)
            dcd r6 = (defpackage.dcd) r6
            java.lang.String r6 = r6.a
            r9 = r2
            dfl r9 = (defpackage.dfl) r9
            boolean r6 = r9.n(r6)
            r4 = r4 | r6
            goto L_0x003a
        L_0x005e:
            if (r4 == 0) goto L_0x006f
            int r0 = r8.size()
            if (r0 > r5) goto L_0x0067
            goto L_0x006f
        L_0x0067:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.String r2 = "Only one file can be requested for a detached namespace."
            r0.<init>(r2)
            throw r0
        L_0x006f:
            java.lang.Object r0 = r1.e
            java.lang.Object r3 = r1.f
            java.lang.Object r12 = r1.a
            java.lang.Object r15 = r1.b
            r9 = r4 ^ 1
            eez r4 = r7.f
            java.lang.String r5 = r7.a
            java.util.Set r6 = r7.c
            hme r4 = r4.c(r5, r8, r6)
            dgq r5 = new dgq
            r13 = r3
            androidx.wear.ambient.AmbientMode$AmbientController r13 = (androidx.wear.ambient.AmbientMode.AmbientController) r13
            r14 = r0
            kli r14 = (defpackage.kli) r14
            r10 = r2
            dfl r10 = (defpackage.dfl) r10
            r0 = 2
            r6 = r5
            r11 = r15
            r2 = r15
            r15 = r0
            r6.<init>((defpackage.dhd) r7, (java.util.Set) r8, (boolean) r9, (defpackage.dfl) r10, (defpackage.hmh) r11, (defpackage.dey) r12, (androidx.wear.ambient.AmbientMode.AmbientController) r13, (defpackage.kli) r14, (int) r15)
            hme r0 = defpackage.hke.g(r4, r5, r2)
        L_0x009a:
            return r0
        L_0x009b:
            char r0 = java.io.File.separatorChar
            grd r0 = defpackage.grd.c(r0)
            java.lang.Object r2 = r1.a
            btf r2 = (defpackage.btf) r2
            android.content.Context r2 = r2.c
            java.lang.Object r3 = r1.d
            java.lang.Object r6 = r1.e
            java.lang.Object r7 = r1.b
            java.lang.Object r8 = r1.f
            java.lang.String r2 = defpackage.btf.f(r2)
            bty r8 = (defpackage.bty) r8
            java.lang.String r7 = (java.lang.String) r7
            btw r6 = (defpackage.btw) r6
            btv r3 = (defpackage.btv) r3
            java.lang.String r3 = defpackage.btf.e(r8, r7, r6, r3)
            java.lang.Object[] r5 = new java.lang.Object[r5]
            r5[r4] = r3
            java.lang.Object r3 = r1.c
            java.lang.String r0 = r0.e(r2, r3, r5)
            hme r0 = defpackage.hfc.K(r0)
            return r0
        L_0x00ce:
            java.lang.Object r6 = r1.f
            java.lang.Object r7 = r1.e
            java.lang.Object r0 = r1.d
            java.lang.Object r8 = r1.c
            java.lang.Object r9 = r1.b
            java.lang.Object r10 = r1.a
            r11 = 3
            r12 = 2
            android.content.Context r10 = (android.content.Context) r10     // Catch:{ foa -> 0x01a7, fnx -> 0x017f, fny -> 0x0157, IOException -> 0x012e }
            java.lang.String r9 = (java.lang.String) r9     // Catch:{ foa -> 0x01a7, fnx -> 0x017f, fny -> 0x0157, IOException -> 0x012e }
            android.net.Uri r9 = defpackage.cqx.t(r10, r9)     // Catch:{ foa -> 0x01a7, fnx -> 0x017f, fny -> 0x0157, IOException -> 0x012e }
            foq r10 = new foq     // Catch:{ foa -> 0x01a7, fnx -> 0x017f, fny -> 0x0157, IOException -> 0x012e }
            r10.<init>()     // Catch:{ foa -> 0x01a7, fnx -> 0x017f, fny -> 0x0157, IOException -> 0x012e }
            r13 = r8
            kjd r13 = (defpackage.kjd) r13     // Catch:{ foa -> 0x01a7, fnx -> 0x017f, fny -> 0x0157, IOException -> 0x012e }
            android.net.Uri r0 = (android.net.Uri) r0     // Catch:{ foa -> 0x01a7, fnx -> 0x017f, fny -> 0x0157, IOException -> 0x012e }
            java.lang.Object r0 = r13.e(r0, r10)     // Catch:{ foa -> 0x01a7, fnx -> 0x017f, fny -> 0x0157, IOException -> 0x012e }
            r10 = r0
            java.io.InputStream r10 = (java.io.InputStream) r10     // Catch:{ foa -> 0x01a7, fnx -> 0x017f, fny -> 0x0157, IOException -> 0x012e }
            fou r0 = new fou     // Catch:{ all -> 0x0120 }
            r0.<init>()     // Catch:{ all -> 0x0120 }
            kjd r8 = (defpackage.kjd) r8     // Catch:{ all -> 0x0120 }
            java.lang.Object r0 = r8.e(r9, r0)     // Catch:{ all -> 0x0120 }
            r8 = r0
            java.io.OutputStream r8 = (java.io.OutputStream) r8     // Catch:{ all -> 0x0120 }
            defpackage.hgy.a(r10, r8)     // Catch:{ all -> 0x0112 }
            if (r8 == 0) goto L_0x010b
            r8.close()     // Catch:{ all -> 0x0120 }
        L_0x010b:
            if (r10 == 0) goto L_0x01db
            r10.close()     // Catch:{ foa -> 0x01a7, fnx -> 0x017f, fny -> 0x0157, IOException -> 0x012e }
            goto L_0x01db
        L_0x0112:
            r0 = move-exception
            r9 = r0
            if (r8 == 0) goto L_0x011f
            r8.close()     // Catch:{ all -> 0x011a }
            goto L_0x011f
        L_0x011a:
            r0 = move-exception
            r8 = r0
            r9.addSuppressed(r8)     // Catch:{ all -> 0x0120 }
        L_0x011f:
            throw r9     // Catch:{ all -> 0x0120 }
        L_0x0120:
            r0 = move-exception
            r8 = r0
            if (r10 == 0) goto L_0x012d
            r10.close()     // Catch:{ all -> 0x0128 }
            goto L_0x012d
        L_0x0128:
            r0 = move-exception
            r9 = r0
            r8.addSuppressed(r9)     // Catch:{ foa -> 0x01a7, fnx -> 0x017f, fny -> 0x0157, IOException -> 0x012e }
        L_0x012d:
            throw r8     // Catch:{ foa -> 0x01a7, fnx -> 0x017f, fny -> 0x0157, IOException -> 0x012e }
        L_0x012e:
            csv r7 = (defpackage.csv) r7
            java.lang.String r0 = r7.b
            csx r6 = (defpackage.csx) r6
            java.lang.String r2 = r6.c
            java.lang.Object[] r8 = new java.lang.Object[r11]
            r8[r4] = r3
            r8[r5] = r0
            r8[r12] = r2
            java.lang.String r0 = "%s: Failed to copy to the blobstore after download for file %s, file group %s"
            defpackage.cyh.i(r0, r8)
            java.lang.String r0 = r7.b
            java.lang.String r2 = r6.c
            java.lang.Object[] r3 = new java.lang.Object[r12]
            r3[r4] = r0
            r3[r5] = r2
            java.lang.String r0 = "Error while copying file %s, group %s, to the shared blob storage"
            java.lang.String r2 = java.lang.String.format(r0, r3)
            r4 = 22
            goto L_0x01db
        L_0x0157:
            csv r7 = (defpackage.csv) r7
            java.lang.String r0 = r7.b
            csx r6 = (defpackage.csx) r6
            java.lang.String r2 = r6.c
            java.lang.Object[] r8 = new java.lang.Object[r11]
            r8[r4] = r3
            r8[r5] = r0
            r8[r12] = r2
            java.lang.String r0 = "%s: Malformed lease uri file %s, file group %s"
            defpackage.cyh.i(r0, r8)
            java.lang.String r0 = r7.b
            java.lang.String r2 = r6.c
            java.lang.Object[] r3 = new java.lang.Object[r12]
            r3[r4] = r0
            r3[r5] = r2
            java.lang.String r0 = "Malformed blob Uri for file %s, group %s"
            java.lang.String r2 = java.lang.String.format(r0, r3)
            r4 = 17
            goto L_0x01db
        L_0x017f:
            csv r7 = (defpackage.csv) r7
            java.lang.String r0 = r7.b
            csx r6 = (defpackage.csx) r6
            java.lang.String r2 = r6.c
            java.lang.Object[] r8 = new java.lang.Object[r11]
            r8[r4] = r3
            r8[r5] = r0
            r8[r12] = r2
            java.lang.String r0 = "%s: Failed to share after download for file %s, file group %s due to LimitExceededException"
            defpackage.cyh.i(r0, r8)
            java.lang.String r0 = r7.b
            java.lang.String r2 = r6.c
            java.lang.Object[] r3 = new java.lang.Object[r12]
            r3[r4] = r0
            r3[r5] = r2
            java.lang.String r0 = "System limit exceeded for file %s, group %s"
            java.lang.String r2 = java.lang.String.format(r0, r3)
            r4 = 25
            goto L_0x01db
        L_0x01a7:
            r0 = move-exception
            java.lang.String r8 = r0.getMessage()
            boolean r8 = android.text.TextUtils.isEmpty(r8)
            if (r8 == 0) goto L_0x01b3
            goto L_0x01b7
        L_0x01b3:
            java.lang.String r2 = r0.getMessage()
        L_0x01b7:
            csv r7 = (defpackage.csv) r7
            java.lang.String r0 = r7.b
            csx r6 = (defpackage.csx) r6
            java.lang.String r6 = r6.c
            r7 = 4
            java.lang.Object[] r7 = new java.lang.Object[r7]
            r7[r4] = r3
            r7[r5] = r0
            r7[r12] = r6
            r7[r11] = r2
            java.lang.String r0 = "%s: Failed to share after download for file %s, file group %s. UnsupportedFileStorageOperation was thrown with message \"%s\""
            defpackage.cyh.l(r0, r7)
            java.lang.String r0 = "UnsupportedFileStorageOperation was thrown: "
            java.lang.String r2 = java.lang.String.valueOf(r2)
            java.lang.String r2 = r0.concat(r2)
            r4 = 24
        L_0x01db:
            if (r4 != 0) goto L_0x01e0
            hme r0 = defpackage.hma.a
            return r0
        L_0x01e0:
            cza r0 = new cza
            r0.<init>(r4, r2)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cyy.a():hme");
    }

    public /* synthetic */ cyy(btf btf, String str, bty bty, String str2, btw btw, btv btv, int i) {
        this.g = i;
        this.a = btf;
        this.c = str;
        this.f = bty;
        this.b = str2;
        this.e = btw;
        this.d = btv;
    }

    public /* synthetic */ cyy(dhd dhd, dfl dfl, hmh hmh, dey dey, AmbientMode.AmbientController ambientController, kli kli, int i) {
        this.g = i;
        this.c = dhd;
        this.d = dfl;
        this.b = hmh;
        this.a = dey;
        this.f = ambientController;
        this.e = kli;
    }
}
