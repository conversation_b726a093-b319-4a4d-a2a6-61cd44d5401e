package defpackage;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/* renamed from: bec  reason: default package */
/* compiled from: PG */
public final class bec {
    public final bxq a;
    private final bbv b;
    private final long c;
    private final Object d = new Object();
    private final Map e = new LinkedHashMap();

    public bec(bbv bbv, bxq bxq) {
        long millis = TimeUnit.MINUTES.toMillis(90);
        this.b = bbv;
        this.a = bxq;
        this.c = millis;
    }

    public final void a(byw byw) {
        Runnable runnable;
        jnu.e(byw, "token");
        synchronized (this.d) {
            runnable = (Runnable) this.e.remove(byw);
        }
        if (runnable != null) {
            this.b.a(runnable);
        }
    }

    public final void b(byw byw) {
        aku aku = new aku((Object) this, (Object) byw, 7, (char[]) null);
        synchronized (this.d) {
            Runnable runnable = (Runnable) this.e.put(byw, aku);
        }
        this.b.b(this.c, aku);
    }
}
