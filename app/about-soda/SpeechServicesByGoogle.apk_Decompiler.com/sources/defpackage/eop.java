package defpackage;

/* renamed from: eop  reason: default package */
/* compiled from: PG */
public final class eop implements eon {
    public final ebl a;
    public final eow b;
    public final ebn c;
    public final int d;
    public final boolean e;

    public eop(ebl ebl, eow eow, ebn ebn, int i, boolean z) {
        jnu.e(ebl, "session");
        jnu.e(eow, "route");
        jnu.e(ebn, "params");
        this.a = ebl;
        this.b = eow;
        this.c = ebn;
        this.d = i;
        this.e = z;
    }

    public static /* synthetic */ eop e(eop eop, eow eow, ebn ebn, int i, int i2) {
        ebl ebl;
        if ((i2 & 1) != 0) {
            ebl = eop.a;
        } else {
            ebl = null;
        }
        ebl ebl2 = ebl;
        if ((i2 & 2) != 0) {
            eow = eop.b;
        }
        eow eow2 = eow;
        if ((i2 & 4) != 0) {
            ebn = eop.c;
        }
        ebn ebn2 = ebn;
        boolean z = eop.e;
        jnu.e(ebl2, "session");
        jnu.e(eow2, "route");
        jnu.e(ebn2, "params");
        return new eop(ebl2, eow2, ebn2, i, z);
    }

    public final int a() {
        return this.d;
    }

    public final ebl b() {
        return this.a;
    }

    public final ebn c() {
        return this.c;
    }

    public final eow d() {
        return this.b;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof eop)) {
            return false;
        }
        eop eop = (eop) obj;
        if (jnu.i(this.a, eop.a) && jnu.i(this.b, eop.b) && jnu.i(this.c, eop.c) && this.d == eop.d && this.e == eop.e) {
            return true;
        }
        return false;
    }

    public final /* synthetic */ hme f() {
        return cqx.S(this);
    }

    public final /* synthetic */ Object g(jlr jlr) {
        return cqx.T(this, jlr);
    }

    public final int hashCode() {
        int i;
        int hashCode = (this.a.hashCode() * 31) + this.b.hashCode();
        ebn ebn = this.c;
        if (ebn.B()) {
            i = ebn.i();
        } else {
            int i2 = ebn.memoizedHashCode;
            if (i2 == 0) {
                i2 = ebn.i();
                ebn.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (((((hashCode * 31) + i) * 31) + this.d) * 31) + a.f(this.e);
    }

    public final String toString() {
        return "HotwordSessionDataSimple(session=" + this.a + ", route=" + this.b + ", params=" + this.c + ", sessionToken=" + this.d + ", isInactive=" + this.e + ")";
    }

    /* JADX WARNING: type inference failed for: r9v7, types: [htq, java.lang.Object] */
    /* JADX WARNING: Illegal instructions before constructor call */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Unknown variable types count: 1 */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public /* synthetic */ eop(defpackage.ebl r8, defpackage.eow r9, defpackage.ebn r10, int r11, boolean r12, int r13) {
        /*
            r7 = this;
            r0 = r13 & 2
            if (r0 == 0) goto L_0x000a
            eow r9 = new eow
            r0 = 0
            r9.<init>(r0)
        L_0x000a:
            r3 = r9
            r9 = r13 & 4
            if (r9 == 0) goto L_0x002b
            ebn r9 = defpackage.ebn.h
            htk r9 = r9.l()
            java.lang.String r10 = "newBuilder(...)"
            defpackage.jnu.d(r9, r10)
            java.lang.String r10 = "builder"
            defpackage.jnu.e(r9, r10)
            htq r9 = r9.r()
            java.lang.String r10 = "build(...)"
            defpackage.jnu.d(r9, r10)
            r10 = r9
            ebn r10 = (defpackage.ebn) r10
        L_0x002b:
            r4 = r10
            r9 = r13 & 8
            r10 = -1
            if (r9 == 0) goto L_0x0033
            r5 = r10
            goto L_0x0034
        L_0x0033:
            r5 = r11
        L_0x0034:
            r9 = r13 & 16
            if (r9 == 0) goto L_0x003d
            if (r5 != r10) goto L_0x003c
            r12 = 1
            goto L_0x003d
        L_0x003c:
            r12 = 0
        L_0x003d:
            r6 = r12
            r1 = r7
            r2 = r8
            r1.<init>(r2, r3, r4, r5, r6)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eop.<init>(ebl, eow, ebn, int, boolean, int):void");
    }
}
