package defpackage;

import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;

/* renamed from: bji  reason: default package */
/* compiled from: PG */
public final class bji {
    public final AlertDialog.Builder a;
    public AlertDialog b;
    public final Context c;
    public ViewGroup d;
    public final View.OnApplyWindowInsetsListener e = new bkf(1);
    public final DialogInterface.OnDismissListener f = new bjh(this);

    public bji(Context context) {
        this.a = new AlertDialog.Builder(context, 0);
        InputMethodManager inputMethodManager = (InputMethodManager) context.getSystemService("input_method");
        this.c = context;
    }
}
