package defpackage;

import android.os.Build;
import android.os.Trace;
import androidx.wear.ambient.AmbientLifecycleObserverKt;

/* renamed from: bdx  reason: default package */
/* compiled from: PG */
public final class bdx extends jnv implements jna {
    final /* synthetic */ bbj a;
    final /* synthetic */ boolean b;
    final /* synthetic */ String c;
    final /* synthetic */ eez d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bdx(bbj bbj, boolean z, String str, eez eez) {
        super(1);
        this.a = bbj;
        this.b = z;
        this.c = str;
        this.d = eez;
    }

    public final /* bridge */ /* synthetic */ Object a(Object obj) {
        String str;
        Throwable th = (Throwable) obj;
        if (th instanceof bdo) {
            this.a.g(((bdo) th).a);
        }
        if (this.b && (str = this.c) != null) {
            eez eez = this.d;
            AmbientLifecycleObserverKt ambientLifecycleObserverKt = ((bam) eez.c).p;
            int hashCode = ((bhe) eez.a).hashCode();
            if (Build.VERSION.SDK_INT >= 29) {
                Trace.endAsyncSection(wd.o(str), hashCode);
            } else {
                String o = wd.o(str);
                try {
                    if (wd.c == null) {
                        wd.c = Trace.class.getMethod("asyncTraceEnd", new Class[]{Long.TYPE, String.class, Integer.TYPE});
                    }
                    wd.c.invoke((Object) null, new Object[]{Long.valueOf(wd.a), o, Integer.valueOf(hashCode)});
                } catch (Exception e) {
                    wd.s(e);
                }
            }
        }
        return jkd.a;
    }
}
