package defpackage;

/* renamed from: csb  reason: default package */
/* compiled from: PG */
public final class csb extends htq implements hvb {
    public static final csb h;
    private static volatile hvh i;
    public int a;
    public String b = "";
    public String c = "";
    public int d;
    public ihf e;
    public String f = "";
    public hse g;

    static {
        csb csb = new csb();
        h = csb;
        htq.z(csb.class, csb);
    }

    private csb() {
        hvk hvk = hvk.a;
    }

    /* access modifiers changed from: protected */
    public final Object a(int i2, Object obj) {
        int i3 = i2 - 1;
        if (i3 == 0) {
            return (byte) 1;
        }
        if (i3 == 2) {
            return new hvl(h, "\u0001\u0006\u0000\u0001\u0002\u0015\u0006\u0000\u0000\u0000\u0002ဈ\u0001\u0004င\u0002\u0007ဈ\u0000\u000bဉ\u0005\u000eဈ\u0006\u0015ဉ\r", new Object[]{"a", "c", "d", "b", "e", "f", "g"});
        } else if (i3 == 3) {
            return new csb();
        } else {
            if (i3 == 4) {
                return new htk((htq) h);
            }
            if (i3 == 5) {
                return h;
            }
            if (i3 != 6) {
                return null;
            }
            hvh hvh = i;
            if (hvh == null) {
                synchronized (csb.class) {
                    hvh = i;
                    if (hvh == null) {
                        hvh = new htl(h);
                        i = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
