package defpackage;

/* renamed from: elw  reason: default package */
/* compiled from: PG */
public final class elw extends jmi implements jne {
    int a;
    final /* synthetic */ ehg b;
    final /* synthetic */ Integer c;
    final /* synthetic */ dzq d;
    final /* synthetic */ hme e;
    final /* synthetic */ Object f;
    private /* synthetic */ Object g;
    private final /* synthetic */ int h;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public elw(cyw cyw, ehg ehg, Integer num, dzq dzq, hme hme, jlr jlr, int i) {
        super(2, jlr);
        this.h = i;
        this.f = cyw;
        this.b = ehg;
        this.c = num;
        this.d = dzq;
        this.e = hme;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        int i = this.h;
        if (i == 0) {
            return ((elw) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        } else if (i != 1) {
            return ((elw) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        } else {
            return ((elw) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v5, resolved type: eaf} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v22, resolved type: dzx} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v37, resolved type: eaf} */
    /* JADX WARNING: type inference failed for: r0v15, types: [elp, java.lang.Object] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r7) {
        /*
            r6 = this;
            int r0 = r6.h
            java.lang.String r1 = "audioRouteType"
            java.lang.String r2 = "clientInfo"
            java.lang.String r3 = "getOrDefault(...)"
            r4 = 1
            if (r0 == 0) goto L_0x00d1
            if (r0 == r4) goto L_0x005a
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r1 = r6.a
            if (r1 == 0) goto L_0x0019
            defpackage.jji.c(r7)     // Catch:{ all -> 0x0017 }
            goto L_0x002b
        L_0x0017:
            r7 = move-exception
            goto L_0x0038
        L_0x0019:
            defpackage.jji.c(r7)
            java.lang.Object r7 = r6.g
            jqs r7 = (defpackage.jqs) r7
            hme r7 = r6.e
            r6.a = r4     // Catch:{ all -> 0x0017 }
            java.lang.Object r7 = defpackage.jqw.x(r7, r6)     // Catch:{ all -> 0x0017 }
            if (r7 != r0) goto L_0x002b
            goto L_0x0059
        L_0x002b:
            dzo r7 = (defpackage.dzo) r7     // Catch:{ all -> 0x0017 }
            int r7 = r7.b     // Catch:{ all -> 0x0017 }
            eaf r7 = defpackage.eaf.b(r7)     // Catch:{ all -> 0x0017 }
            if (r7 != 0) goto L_0x003c
            eaf r7 = defpackage.eaf.UNKNOWN_ROUTING_STATUS     // Catch:{ all -> 0x0017 }
            goto L_0x003c
        L_0x0038:
            java.lang.Object r7 = defpackage.jji.b(r7)
        L_0x003c:
            boolean r0 = r7 instanceof defpackage.jjt
            eaf r1 = defpackage.eaf.FAILED_GETTING_ROUTING_STATUS_FUTURE
            if (r4 != r0) goto L_0x0043
            r7 = r1
        L_0x0043:
            defpackage.jnu.d(r7, r3)
            java.lang.Object r0 = r6.f
            ehg r1 = r6.b
            java.lang.Integer r2 = r6.c
            dzq r3 = r6.d
            cyw r0 = (defpackage.cyw) r0
            java.lang.Object r0 = r0.a
            eaf r7 = (defpackage.eaf) r7
            r0.q(r1, r2, r3, r7)
            jkd r0 = defpackage.jkd.a
        L_0x0059:
            return r0
        L_0x005a:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r5 = r6.a
            if (r5 == 0) goto L_0x0066
            defpackage.jji.c(r7)     // Catch:{ all -> 0x0064 }
            goto L_0x0078
        L_0x0064:
            r7 = move-exception
            goto L_0x0085
        L_0x0066:
            defpackage.jji.c(r7)
            java.lang.Object r7 = r6.g
            jqs r7 = (defpackage.jqs) r7
            hme r7 = r6.e
            r6.a = r4     // Catch:{ all -> 0x0064 }
            java.lang.Object r7 = defpackage.jqw.x(r7, r6)     // Catch:{ all -> 0x0064 }
            if (r7 != r0) goto L_0x0078
            goto L_0x00d0
        L_0x0078:
            dzm r7 = (defpackage.dzm) r7     // Catch:{ all -> 0x0064 }
            int r7 = r7.b     // Catch:{ all -> 0x0064 }
            dzx r7 = defpackage.dzx.b(r7)     // Catch:{ all -> 0x0064 }
            if (r7 != 0) goto L_0x0089
            dzx r7 = defpackage.dzx.UNKNOWN_DISCONNECT_REASON     // Catch:{ all -> 0x0064 }
            goto L_0x0089
        L_0x0085:
            java.lang.Object r7 = defpackage.jji.b(r7)
        L_0x0089:
            boolean r0 = r7 instanceof defpackage.jjt
            dzx r5 = defpackage.dzx.FAILED_GETTING_DISCONNECT_REASON
            if (r4 != r0) goto L_0x0090
            r7 = r5
        L_0x0090:
            defpackage.jnu.d(r7, r3)
            java.lang.Object r0 = r6.f
            ehg r3 = r6.b
            java.lang.Integer r4 = r6.c
            dzq r5 = r6.d
            dzx r7 = (defpackage.dzx) r7
            defpackage.jnu.e(r3, r2)
            defpackage.jnu.e(r5, r1)
            java.lang.String r1 = "reason"
            defpackage.jnu.e(r7, r1)
            dxl r1 = defpackage.dwt.d
            java.lang.String r2 = "AUDIO_ROUTE_DISCONNECT_DONE"
            defpackage.jnu.d(r1, r2)
            int r7 = r7.H
            java.lang.String r2 = "disconnect_route_reason"
            dxh r7 = r1.a(r7, r2)
            java.lang.String r1 = "route_disconnect"
            java.lang.String r2 = defpackage.elx.B(r4)
            r7.d(r1, r2)
            gnk r1 = defpackage.elx.a
            jyl r2 = defpackage.elx.y(r3, r5)
            r7.g(r1, r2)
            elx r0 = (defpackage.elx) r0
            r0.x(r7, r3)
            jkd r0 = defpackage.jkd.a
        L_0x00d0:
            return r0
        L_0x00d1:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r5 = r6.a
            if (r5 == 0) goto L_0x00dd
            defpackage.jji.c(r7)     // Catch:{ all -> 0x00db }
            goto L_0x00ef
        L_0x00db:
            r7 = move-exception
            goto L_0x00fc
        L_0x00dd:
            defpackage.jji.c(r7)
            java.lang.Object r7 = r6.g
            jqs r7 = (defpackage.jqs) r7
            hme r7 = r6.e
            r6.a = r4     // Catch:{ all -> 0x00db }
            java.lang.Object r7 = defpackage.jqw.x(r7, r6)     // Catch:{ all -> 0x00db }
            if (r7 != r0) goto L_0x00ef
            goto L_0x0143
        L_0x00ef:
            dzo r7 = (defpackage.dzo) r7     // Catch:{ all -> 0x00db }
            int r7 = r7.b     // Catch:{ all -> 0x00db }
            eaf r7 = defpackage.eaf.b(r7)     // Catch:{ all -> 0x00db }
            if (r7 != 0) goto L_0x0100
            eaf r7 = defpackage.eaf.UNKNOWN_ROUTING_STATUS     // Catch:{ all -> 0x00db }
            goto L_0x0100
        L_0x00fc:
            java.lang.Object r7 = defpackage.jji.b(r7)
        L_0x0100:
            boolean r0 = r7 instanceof defpackage.jjt
            eaf r5 = defpackage.eaf.FAILED_GETTING_ROUTING_STATUS_FUTURE
            if (r4 != r0) goto L_0x0107
            r7 = r5
        L_0x0107:
            defpackage.jnu.d(r7, r3)
            java.lang.Object r0 = r6.f
            ehg r3 = r6.b
            java.lang.Integer r4 = r6.c
            dzq r5 = r6.d
            eaf r7 = (defpackage.eaf) r7
            defpackage.jnu.e(r3, r2)
            defpackage.jnu.e(r5, r1)
            java.lang.String r1 = "status"
            defpackage.jnu.e(r7, r1)
            dxl r1 = defpackage.dwt.b
            java.lang.String r2 = "AUDIO_ROUTE_CONNECT_DONE"
            defpackage.jnu.d(r1, r2)
            dxh r7 = defpackage.elx.E(r1, r7)
            java.lang.String r1 = "route_connect"
            java.lang.String r2 = defpackage.elx.B(r4)
            r7.d(r1, r2)
            gnk r1 = defpackage.elx.a
            jyl r2 = defpackage.elx.y(r3, r5)
            r7.g(r1, r2)
            elx r0 = (defpackage.elx) r0
            r0.x(r7, r3)
            jkd r0 = defpackage.jkd.a
        L_0x0143:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.elw.bk(java.lang.Object):java.lang.Object");
    }

    public final jlr c(Object obj, jlr jlr) {
        int i = this.h;
        if (i == 0) {
            elw elw = new elw((elx) this.f, this.b, this.c, this.d, this.e, jlr, 0);
            elw.g = obj;
            return elw;
        } else if (i != 1) {
            elw elw2 = new elw((cyw) this.f, this.b, this.c, this.d, this.e, jlr, 2);
            elw2.g = obj;
            return elw2;
        } else {
            elw elw3 = new elw((elx) this.f, this.b, this.c, this.d, this.e, jlr, 1, (byte[]) null);
            elw3.g = obj;
            return elw3;
        }
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public elw(elx elx, ehg ehg, Integer num, dzq dzq, hme hme, jlr jlr, int i) {
        super(2, jlr);
        this.h = i;
        this.f = elx;
        this.b = ehg;
        this.c = num;
        this.d = dzq;
        this.e = hme;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public elw(elx elx, ehg ehg, Integer num, dzq dzq, hme hme, jlr jlr, int i, byte[] bArr) {
        super(2, jlr);
        this.h = i;
        this.f = elx;
        this.b = ehg;
        this.c = num;
        this.d = dzq;
        this.e = hme;
    }
}
