package defpackage;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver;
import java.util.concurrent.TimeUnit;

/* renamed from: bid  reason: default package */
/* compiled from: PG */
public final class bid implements Runnable {
    private static final String a = bbk.b("ForceStopRunnable");
    private static final long b = TimeUnit.DAYS.toMillis(3650);
    private final Context c;
    private final bdm d;
    private int e = 0;
    private final byw f;

    public bid(Context context, bdm bdm) {
        this.c = context.getApplicationContext();
        this.d = bdm;
        this.f = bdm.l;
    }

    public static void a(Context context) {
        int i;
        AlarmManager alarmManager = (AlarmManager) context.getSystemService("alarm");
        if (Build.VERSION.SDK_INT >= 31) {
            i = 167772160;
        } else {
            i = 134217728;
        }
        PendingIntent b2 = b(context, i);
        long currentTimeMillis = System.currentTimeMillis() + b;
        if (alarmManager != null) {
            alarmManager.setExact(0, currentTimeMillis, b2);
        }
    }

    private static PendingIntent b(Context context, int i) {
        Intent intent = new Intent();
        intent.setComponent(new ComponentName(context, ForceStopRunnable$BroadcastReceiver.class));
        intent.setAction("ACTION_FORCE_STOP_RESCHEDULE");
        return PendingIntent.getBroadcast(context, -1, intent, i);
    }

    /* JADX INFO: finally extract failed */
    /* JADX WARNING: Can't wrap try/catch for region: R(6:7|8|(7:10|(1:12)|197|13|(1:15)(1:16)|17|(4:20|(1:199)(6:22|(1:24)|25|(1:27)(1:28)|29|200)|198|18))|30|31|(16:32|33|34|35|(2:38|36)|39|40|(1:42)(1:43)|44|(3:48|(4:51|(2:53|203)(2:54|204)|201|49)|202)|55|(2:56|(2:58|(2:205|60))(2:206|61))|62|(8:64|65|66|(2:69|67)|207|70|71|72)|76|(20:77|78|(3:80|(2:83|81)|208)|84|85|86|87|88|89|90|91|92|93|94|95|96|(1:(1:99)(3:100|102|(6:107|108|(1:110)(1:111)|112|(3:(1:115)|116|(5:120|(1:122)|212|(3:125|(1:130)(2:190|129)|123)|209))(1:(2:191|132))|(2:134|135)(1:192))(2:193|106)))|101|102|(0)(0)))) */
    /* JADX WARNING: Code restructure failed: missing block: B:6:0x0021, code lost:
        if (r5 == false) goto L_0x0403;
     */
    /* JADX WARNING: Missing exception handler attribute for start block: B:7:0x0025 */
    /* JADX WARNING: Removed duplicated region for block: B:107:0x02ab A[SYNTHETIC, Splitter:B:107:0x02ab] */
    /* JADX WARNING: Removed duplicated region for block: B:193:0x0289 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:7:0x0025 A[LOOP:0: B:7:0x0025->B:189:0x0025, LOOP_LABEL: LOOP:0: B:7:0x0025->B:189:0x0025, LOOP_START, SYNTHETIC, Splitter:B:7:0x0025] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void run() {
        /*
            r17 = this;
            r1 = r17
            java.lang.String r2 = "last_force_stop_ms"
            java.lang.String r3 = "reschedule_needed"
            java.lang.String r4 = "context"
            bdm r5 = r1.d     // Catch:{ all -> 0x040a }
            bam r5 = r5.c     // Catch:{ all -> 0x040a }
            java.lang.String r6 = r5.i     // Catch:{ all -> 0x040a }
            boolean r6 = android.text.TextUtils.isEmpty(r6)     // Catch:{ all -> 0x040a }
            if (r6 == 0) goto L_0x0018
            defpackage.bbk.a()     // Catch:{ all -> 0x040a }
            goto L_0x0025
        L_0x0018:
            android.content.Context r6 = r1.c     // Catch:{ all -> 0x040a }
            boolean r5 = defpackage.big.a(r6, r5)     // Catch:{ all -> 0x040a }
            defpackage.bbk.a()     // Catch:{ all -> 0x040a }
            if (r5 != 0) goto L_0x0025
            goto L_0x0403
        L_0x0025:
            android.content.Context r5 = r1.c     // Catch:{ SQLiteException -> 0x03e6 }
            defpackage.jnu.e(r5, r4)     // Catch:{ SQLiteException -> 0x03e6 }
            java.io.File r6 = defpackage.we.j(r5)     // Catch:{ SQLiteException -> 0x03e6 }
            boolean r6 = r6.exists()     // Catch:{ SQLiteException -> 0x03e6 }
            r7 = 3
            r8 = 0
            if (r6 == 0) goto L_0x011e
            defpackage.bbk.a()     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r6 = defpackage.bdb.a     // Catch:{ SQLiteException -> 0x03e6 }
            defpackage.jnu.e(r5, r4)     // Catch:{ SQLiteException -> 0x03e6 }
            java.io.File r6 = defpackage.we.j(r5)     // Catch:{ SQLiteException -> 0x03e6 }
            defpackage.jnu.e(r5, r4)     // Catch:{ SQLiteException -> 0x03e6 }
            java.io.File r9 = new java.io.File     // Catch:{ SQLiteException -> 0x03e6 }
            defpackage.jnu.e(r5, r4)     // Catch:{ SQLiteException -> 0x03e6 }
            java.io.File r5 = r5.getNoBackupFilesDir()     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r10 = "context.noBackupFilesDir"
            defpackage.jnu.d(r5, r10)     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r10 = "androidx.work.workdb"
            r9.<init>(r5, r10)     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String[] r5 = defpackage.bdb.b     // Catch:{ SQLiteException -> 0x03e6 }
            int r10 = r5.length     // Catch:{ SQLiteException -> 0x03e6 }
            int r10 = defpackage.jji.e(r7)     // Catch:{ SQLiteException -> 0x03e6 }
            r11 = 16
            int r10 = defpackage.jnu.o(r10, r11)     // Catch:{ SQLiteException -> 0x03e6 }
            java.util.LinkedHashMap r11 = new java.util.LinkedHashMap     // Catch:{ SQLiteException -> 0x03e6 }
            r11.<init>(r10)     // Catch:{ SQLiteException -> 0x03e6 }
            r10 = r8
        L_0x006b:
            if (r10 >= r7) goto L_0x00a8
            r12 = r5[r10]     // Catch:{ SQLiteException -> 0x03e6 }
            java.io.File r13 = new java.io.File     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r14 = r6.getPath()     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r14 = java.lang.String.valueOf(r14)     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r15 = java.lang.String.valueOf(r12)     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r14 = r14.concat(r15)     // Catch:{ SQLiteException -> 0x03e6 }
            r13.<init>(r14)     // Catch:{ SQLiteException -> 0x03e6 }
            java.io.File r14 = new java.io.File     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r15 = r9.getPath()     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r15 = java.lang.String.valueOf(r15)     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r12 = java.lang.String.valueOf(r12)     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r12 = r15.concat(r12)     // Catch:{ SQLiteException -> 0x03e6 }
            r14.<init>(r12)     // Catch:{ SQLiteException -> 0x03e6 }
            jjs r12 = new jjs     // Catch:{ SQLiteException -> 0x03e6 }
            r12.<init>(r13, r14)     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.Object r13 = r12.a     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.Object r12 = r12.b     // Catch:{ SQLiteException -> 0x03e6 }
            r11.put(r13, r12)     // Catch:{ SQLiteException -> 0x03e6 }
            int r10 = r10 + 1
            goto L_0x006b
        L_0x00a8:
            jjs r5 = new jjs     // Catch:{ SQLiteException -> 0x03e6 }
            r5.<init>(r6, r9)     // Catch:{ SQLiteException -> 0x03e6 }
            boolean r6 = r11.isEmpty()     // Catch:{ SQLiteException -> 0x03e6 }
            if (r6 == 0) goto L_0x00b8
            java.util.Map r5 = defpackage.jji.f(r5)     // Catch:{ SQLiteException -> 0x03e6 }
            goto L_0x00c5
        L_0x00b8:
            java.util.LinkedHashMap r6 = new java.util.LinkedHashMap     // Catch:{ SQLiteException -> 0x03e6 }
            r6.<init>(r11)     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.Object r9 = r5.a     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.Object r5 = r5.b     // Catch:{ SQLiteException -> 0x03e6 }
            r6.put(r9, r5)     // Catch:{ SQLiteException -> 0x03e6 }
            r5 = r6
        L_0x00c5:
            java.util.Set r5 = r5.entrySet()     // Catch:{ SQLiteException -> 0x03e6 }
            java.util.Iterator r5 = r5.iterator()     // Catch:{ SQLiteException -> 0x03e6 }
        L_0x00cd:
            boolean r6 = r5.hasNext()     // Catch:{ SQLiteException -> 0x03e6 }
            if (r6 == 0) goto L_0x011e
            java.lang.Object r6 = r5.next()     // Catch:{ SQLiteException -> 0x03e6 }
            java.util.Map$Entry r6 = (java.util.Map.Entry) r6     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.Object r9 = r6.getKey()     // Catch:{ SQLiteException -> 0x03e6 }
            java.io.File r9 = (java.io.File) r9     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.Object r6 = r6.getValue()     // Catch:{ SQLiteException -> 0x03e6 }
            java.io.File r6 = (java.io.File) r6     // Catch:{ SQLiteException -> 0x03e6 }
            boolean r10 = r9.exists()     // Catch:{ SQLiteException -> 0x03e6 }
            if (r10 == 0) goto L_0x00cd
            boolean r10 = r6.exists()     // Catch:{ SQLiteException -> 0x03e6 }
            if (r10 == 0) goto L_0x0107
            bbk r10 = defpackage.bbk.a()     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r11 = defpackage.bdb.a     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r12 = "Over-writing contents of "
            java.util.Objects.toString(r6)     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r13 = java.lang.String.valueOf(r6)     // Catch:{ SQLiteException -> 0x03e6 }
            java.lang.String r12 = r12.concat(r13)     // Catch:{ SQLiteException -> 0x03e6 }
            r10.f(r11, r12)     // Catch:{ SQLiteException -> 0x03e6 }
        L_0x0107:
            boolean r10 = r9.renameTo(r6)     // Catch:{ SQLiteException -> 0x03e6 }
            if (r10 == 0) goto L_0x0114
            java.util.Objects.toString(r9)     // Catch:{ SQLiteException -> 0x03e6 }
            java.util.Objects.toString(r6)     // Catch:{ SQLiteException -> 0x03e6 }
            goto L_0x011a
        L_0x0114:
            java.util.Objects.toString(r9)     // Catch:{ SQLiteException -> 0x03e6 }
            java.util.Objects.toString(r6)     // Catch:{ SQLiteException -> 0x03e6 }
        L_0x011a:
            defpackage.bbk.a()     // Catch:{ SQLiteException -> 0x03e6 }
            goto L_0x00cd
        L_0x011e:
            defpackage.bbk.a()     // Catch:{ all -> 0x040a }
            android.content.Context r6 = r1.c     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bdm r9 = r1.d     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            androidx.work.impl.WorkDatabase r9 = r9.d     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            int r10 = defpackage.bep.a     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            android.app.job.JobScheduler r10 = defpackage.ben.a(r6)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.util.List r6 = defpackage.bep.e(r6, r10)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bgo r11 = r9.x()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.lang.String r12 = "SELECT DISTINCT work_spec_id FROM SystemIdInfo"
            auu r12 = defpackage.auu.a(r12, r8)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r13 = r11
            bgs r13 = (defpackage.bgs) r13     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            aus r13 = r13.a     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r13.k()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bgs r11 = (defpackage.bgs) r11     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            aus r11 = r11.a     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            android.database.Cursor r11 = defpackage.vy.f(r11, r12, r8)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.util.ArrayList r13 = new java.util.ArrayList     // Catch:{ all -> 0x0383 }
            int r14 = r11.getCount()     // Catch:{ all -> 0x0383 }
            r13.<init>(r14)     // Catch:{ all -> 0x0383 }
        L_0x0154:
            boolean r14 = r11.moveToNext()     // Catch:{ all -> 0x0383 }
            if (r14 == 0) goto L_0x0162
            java.lang.String r14 = r11.getString(r8)     // Catch:{ all -> 0x0383 }
            r13.add(r14)     // Catch:{ all -> 0x0383 }
            goto L_0x0154
        L_0x0162:
            r11.close()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r12.j()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            if (r6 == 0) goto L_0x016f
            int r11 = r6.size()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            goto L_0x0170
        L_0x016f:
            r11 = r8
        L_0x0170:
            java.util.HashSet r12 = new java.util.HashSet     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r12.<init>(r11)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            if (r6 == 0) goto L_0x01a1
            boolean r11 = r6.isEmpty()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            if (r11 != 0) goto L_0x01a1
            java.util.Iterator r6 = r6.iterator()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
        L_0x0181:
            boolean r11 = r6.hasNext()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            if (r11 == 0) goto L_0x01a1
            java.lang.Object r11 = r6.next()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            android.app.job.JobInfo r11 = (android.app.job.JobInfo) r11     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bgt r14 = defpackage.bep.a(r11)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            if (r14 == 0) goto L_0x0199
            java.lang.String r11 = r14.a     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r12.add(r11)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            goto L_0x0181
        L_0x0199:
            int r11 = r11.getId()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            defpackage.bep.f(r10, r11)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            goto L_0x0181
        L_0x01a1:
            java.util.Iterator r6 = r13.iterator()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
        L_0x01a5:
            boolean r10 = r6.hasNext()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            if (r10 == 0) goto L_0x01bc
            java.lang.Object r10 = r6.next()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.lang.String r10 = (java.lang.String) r10     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            boolean r10 = r12.contains(r10)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            if (r10 != 0) goto L_0x01a5
            defpackage.bbk.a()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r6 = 1
            goto L_0x01bd
        L_0x01bc:
            r6 = r8
        L_0x01bd:
            r10 = -1
            if (r6 == 0) goto L_0x01e9
            r9.l()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bhf r12 = r9.A()     // Catch:{ all -> 0x01e3 }
            java.util.Iterator r13 = r13.iterator()     // Catch:{ all -> 0x01e3 }
        L_0x01cc:
            boolean r14 = r13.hasNext()     // Catch:{ all -> 0x01e3 }
            if (r14 == 0) goto L_0x01dc
            java.lang.Object r14 = r13.next()     // Catch:{ all -> 0x01e3 }
            java.lang.String r14 = (java.lang.String) r14     // Catch:{ all -> 0x01e3 }
            r12.l(r14, r10)     // Catch:{ all -> 0x01e3 }
            goto L_0x01cc
        L_0x01dc:
            r9.o()     // Catch:{ all -> 0x01e3 }
            r9.m()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            goto L_0x01e9
        L_0x01e3:
            r0 = move-exception
            r6 = r0
            r9.m()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            throw r6     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
        L_0x01e9:
            bdm r9 = r1.d     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            androidx.work.impl.WorkDatabase r9 = r9.d     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bhf r12 = r9.A()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bgx r13 = r9.z()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r9.l()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.util.List r14 = r12.c()     // Catch:{ all -> 0x037d }
            boolean r15 = r14.isEmpty()     // Catch:{ all -> 0x037d }
            if (r15 != 0) goto L_0x022a
            java.util.Iterator r14 = r14.iterator()     // Catch:{ all -> 0x037d }
        L_0x0206:
            boolean r16 = r14.hasNext()     // Catch:{ all -> 0x037d }
            if (r16 == 0) goto L_0x022a
            java.lang.Object r16 = r14.next()     // Catch:{ all -> 0x037d }
            r7 = r16
            bhe r7 = (defpackage.bhe) r7     // Catch:{ all -> 0x037d }
            bbx r5 = defpackage.bbx.ENQUEUED     // Catch:{ all -> 0x037d }
            java.lang.String r8 = r7.b     // Catch:{ all -> 0x037d }
            r12.m(r5, r8)     // Catch:{ all -> 0x037d }
            java.lang.String r5 = r7.b     // Catch:{ all -> 0x037d }
            r8 = -512(0xfffffffffffffe00, float:NaN)
            r12.j(r5, r8)     // Catch:{ all -> 0x037d }
            java.lang.String r5 = r7.b     // Catch:{ all -> 0x037d }
            r12.l(r5, r10)     // Catch:{ all -> 0x037d }
            r7 = 3
            r8 = 0
            goto L_0x0206
        L_0x022a:
            r5 = r13
            bhb r5 = (defpackage.bhb) r5     // Catch:{ all -> 0x037d }
            aus r5 = r5.a     // Catch:{ all -> 0x037d }
            r5.k()     // Catch:{ all -> 0x037d }
            r5 = r13
            bhb r5 = (defpackage.bhb) r5     // Catch:{ all -> 0x037d }
            auv r5 = r5.b     // Catch:{ all -> 0x037d }
            axc r5 = r5.d()     // Catch:{ all -> 0x037d }
            r7 = r13
            bhb r7 = (defpackage.bhb) r7     // Catch:{ all -> 0x0373 }
            aus r7 = r7.a     // Catch:{ all -> 0x0373 }
            r7.l()     // Catch:{ all -> 0x0373 }
            r5.a()     // Catch:{ all -> 0x0368 }
            r7 = r13
            bhb r7 = (defpackage.bhb) r7     // Catch:{ all -> 0x0368 }
            aus r7 = r7.a     // Catch:{ all -> 0x0368 }
            r7.o()     // Catch:{ all -> 0x0368 }
            r7 = r13
            bhb r7 = (defpackage.bhb) r7     // Catch:{ all -> 0x0373 }
            aus r7 = r7.a     // Catch:{ all -> 0x0373 }
            r7.m()     // Catch:{ all -> 0x0373 }
            bhb r13 = (defpackage.bhb) r13     // Catch:{ all -> 0x037d }
            auv r7 = r13.b     // Catch:{ all -> 0x037d }
            r7.f(r5)     // Catch:{ all -> 0x037d }
            r9.o()     // Catch:{ all -> 0x037d }
            r9.m()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            if (r15 == 0) goto L_0x026a
            if (r6 == 0) goto L_0x0268
            goto L_0x026a
        L_0x0268:
            r5 = 0
            goto L_0x026b
        L_0x026a:
            r5 = 1
        L_0x026b:
            bdm r6 = r1.d     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            byw r6 = r6.l     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.lang.Object r6 = r6.a     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            androidx.work.impl.WorkDatabase r6 = (androidx.work.impl.WorkDatabase) r6     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bgh r6 = r6.v()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.lang.Long r6 = r6.a(r3)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r7 = 0
            if (r6 == 0) goto L_0x02ab
            long r9 = r6.longValue()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r11 = 1
            int r6 = (r9 > r11 ? 1 : (r9 == r11 ? 0 : -1))
            if (r6 != 0) goto L_0x02ab
            defpackage.bbk.a()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bdm r5 = r1.d     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r5.k()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bdm r5 = r1.d     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            byw r5 = r5.l     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bgg r6 = new bgg     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.lang.Long r7 = java.lang.Long.valueOf(r7)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r6.<init>(r3, r7)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.lang.Object r5 = r5.a     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            androidx.work.impl.WorkDatabase r5 = (androidx.work.impl.WorkDatabase) r5     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bgh r5 = r5.v()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r5.b(r6)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            goto L_0x0403
        L_0x02ab:
            int r6 = android.os.Build.VERSION.SDK_INT     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            r9 = 31
            if (r6 < r9) goto L_0x02b4
            r6 = 570425344(0x22000000, float:1.7347235E-18)
            goto L_0x02b6
        L_0x02b4:
            r6 = 536870912(0x20000000, float:1.0842022E-19)
        L_0x02b6:
            android.content.Context r9 = r1.c     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            android.app.PendingIntent r6 = b(r9, r6)     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            int r9 = android.os.Build.VERSION.SDK_INT     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            r10 = 30
            if (r9 < r10) goto L_0x0315
            if (r6 == 0) goto L_0x02c7
            r6.cancel()     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
        L_0x02c7:
            android.content.Context r6 = r1.c     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            java.lang.String r9 = "activity"
            java.lang.Object r6 = r6.getSystemService(r9)     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            android.app.ActivityManager r6 = (android.app.ActivityManager) r6     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            r9 = 0
            r10 = 0
            java.util.List r6 = r6.getHistoricalProcessExitReasons(r9, r10, r10)     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            if (r6 == 0) goto L_0x031d
            boolean r9 = r6.isEmpty()     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            if (r9 != 0) goto L_0x031d
            byw r9 = r1.f     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            java.lang.Object r9 = r9.a     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            androidx.work.impl.WorkDatabase r9 = (androidx.work.impl.WorkDatabase) r9     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            bgh r9 = r9.v()     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            java.lang.Long r9 = r9.a(r2)     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            if (r9 == 0) goto L_0x02f3
            long r7 = r9.longValue()     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
        L_0x02f3:
            int r9 = r6.size()     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            if (r10 >= r9) goto L_0x031d
            java.lang.Object r9 = r6.get(r10)     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            android.app.ApplicationExitInfo r9 = defpackage.sk$$ExternalSyntheticApiModelOutline1.m((java.lang.Object) r9)     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            int r11 = defpackage.sk$$ExternalSyntheticApiModelOutline1.m((android.app.ApplicationExitInfo) r9)     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            r12 = 10
            if (r11 != r12) goto L_0x0312
            long r11 = defpackage.sk$$ExternalSyntheticApiModelOutline1.m((android.app.ApplicationExitInfo) r9)     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            int r9 = (r11 > r7 ? 1 : (r11 == r7 ? 0 : -1))
            if (r9 < 0) goto L_0x0312
            goto L_0x033e
        L_0x0312:
            int r10 = r10 + 1
            goto L_0x02f3
        L_0x0315:
            if (r6 != 0) goto L_0x031d
            android.content.Context r5 = r1.c     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            a(r5)     // Catch:{ SecurityException -> 0x0331, IllegalArgumentException -> 0x032f }
            goto L_0x033e
        L_0x031d:
            if (r5 == 0) goto L_0x0403
            defpackage.bbk.a()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bdm r5 = r1.d     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bam r6 = r5.c     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            androidx.work.impl.WorkDatabase r7 = r5.d     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.util.List r5 = r5.e     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            defpackage.bct.a(r6, r7, r5)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            goto L_0x0403
        L_0x032f:
            r0 = move-exception
            goto L_0x0332
        L_0x0331:
            r0 = move-exception
        L_0x0332:
            r5 = r0
            bbk r6 = defpackage.bbk.a()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.lang.String r7 = a     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.lang.String r8 = "Ignoring exception"
            r6.g(r7, r8, r5)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
        L_0x033e:
            defpackage.bbk.a()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bdm r5 = r1.d     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r5.k()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            byw r5 = r1.f     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bdm r6 = r1.d     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bam r6 = r6.c     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            androidx.wear.ambient.AmbientModeSupport$AmbientCallback r6 = r6.q     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            long r6 = java.lang.System.currentTimeMillis()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bgg r8 = new bgg     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.lang.Long r6 = java.lang.Long.valueOf(r6)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r8.<init>(r2, r6)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            java.lang.Object r5 = r5.a     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            androidx.work.impl.WorkDatabase r5 = (androidx.work.impl.WorkDatabase) r5     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            bgh r5 = r5.v()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r5.b(r8)     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            goto L_0x0403
        L_0x0368:
            r0 = move-exception
            r6 = r0
            r7 = r13
            bhb r7 = (defpackage.bhb) r7     // Catch:{ all -> 0x0373 }
            aus r7 = r7.a     // Catch:{ all -> 0x0373 }
            r7.m()     // Catch:{ all -> 0x0373 }
            throw r6     // Catch:{ all -> 0x0373 }
        L_0x0373:
            r0 = move-exception
            r6 = r0
            bhb r13 = (defpackage.bhb) r13     // Catch:{ all -> 0x037d }
            auv r7 = r13.b     // Catch:{ all -> 0x037d }
            r7.f(r5)     // Catch:{ all -> 0x037d }
            throw r6     // Catch:{ all -> 0x037d }
        L_0x037d:
            r0 = move-exception
            r5 = r0
            r9.m()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            throw r5     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
        L_0x0383:
            r0 = move-exception
            r5 = r0
            r11.close()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            r12.j()     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
            throw r5     // Catch:{ SQLiteAccessPermException -> 0x039a, SQLiteCantOpenDatabaseException -> 0x0398, SQLiteConstraintException -> 0x0396, SQLiteDatabaseCorruptException -> 0x0394, SQLiteDatabaseLockedException -> 0x0392, SQLiteDiskIOException -> 0x0390, SQLiteFullException -> 0x038e, SQLiteTableLockedException -> 0x038c }
        L_0x038c:
            r0 = move-exception
            goto L_0x039b
        L_0x038e:
            r0 = move-exception
            goto L_0x039b
        L_0x0390:
            r0 = move-exception
            goto L_0x039b
        L_0x0392:
            r0 = move-exception
            goto L_0x039b
        L_0x0394:
            r0 = move-exception
            goto L_0x039b
        L_0x0396:
            r0 = move-exception
            goto L_0x039b
        L_0x0398:
            r0 = move-exception
            goto L_0x039b
        L_0x039a:
            r0 = move-exception
        L_0x039b:
            r5 = r0
            int r6 = r1.e     // Catch:{ all -> 0x040a }
            r7 = 1
            int r6 = r6 + r7
            r1.e = r6     // Catch:{ all -> 0x040a }
            r7 = 3
            if (r6 < r7) goto L_0x03d8
            android.content.Context r2 = r1.c     // Catch:{ all -> 0x040a }
            java.lang.Class<android.os.UserManager> r3 = android.os.UserManager.class
            java.lang.Object r2 = r2.getSystemService(r3)     // Catch:{ all -> 0x040a }
            android.os.UserManager r2 = (android.os.UserManager) r2     // Catch:{ all -> 0x040a }
            boolean r2 = r2.isUserUnlocked()     // Catch:{ all -> 0x040a }
            if (r2 == 0) goto L_0x03b8
            java.lang.String r2 = "The file system on the device is in a bad state. WorkManager cannot access the app's internal data store."
            goto L_0x03ba
        L_0x03b8:
            java.lang.String r2 = "WorkManager can't be accessed from direct boot, because credential encrypted storage isn't accessible.\nDon't access or initialise WorkManager from directAware components. See https://developer.android.com/training/articles/direct-boot"
        L_0x03ba:
            bbk r3 = defpackage.bbk.a()     // Catch:{ all -> 0x040a }
            java.lang.String r4 = a     // Catch:{ all -> 0x040a }
            r3.d(r4, r2, r5)     // Catch:{ all -> 0x040a }
            java.lang.IllegalStateException r3 = new java.lang.IllegalStateException     // Catch:{ all -> 0x040a }
            r3.<init>(r2, r5)     // Catch:{ all -> 0x040a }
            bdm r2 = r1.d     // Catch:{ all -> 0x040a }
            bam r2 = r2.c     // Catch:{ all -> 0x040a }
            um r2 = r2.f     // Catch:{ all -> 0x040a }
            if (r2 == 0) goto L_0x03d7
            defpackage.bbk.a()     // Catch:{ all -> 0x040a }
            r2.a(r3)     // Catch:{ all -> 0x040a }
            goto L_0x0403
        L_0x03d7:
            throw r3     // Catch:{ all -> 0x040a }
        L_0x03d8:
            defpackage.bbk.a()     // Catch:{ all -> 0x040a }
            int r5 = r1.e     // Catch:{ all -> 0x040a }
            long r5 = (long) r5
            r7 = 300(0x12c, double:1.48E-321)
            long r5 = r5 * r7
            java.lang.Thread.sleep(r5)     // Catch:{ InterruptedException -> 0x0025 }
            goto L_0x0025
        L_0x03e6:
            r0 = move-exception
            r2 = r0
            java.lang.String r3 = "Unexpected SQLite exception during migrations"
            bbk r4 = defpackage.bbk.a()     // Catch:{ all -> 0x040a }
            java.lang.String r5 = a     // Catch:{ all -> 0x040a }
            r4.c(r5, r3)     // Catch:{ all -> 0x040a }
            java.lang.IllegalStateException r4 = new java.lang.IllegalStateException     // Catch:{ all -> 0x040a }
            r4.<init>(r3, r2)     // Catch:{ all -> 0x040a }
            bdm r2 = r1.d     // Catch:{ all -> 0x040a }
            bam r2 = r2.c     // Catch:{ all -> 0x040a }
            um r2 = r2.f     // Catch:{ all -> 0x040a }
            if (r2 == 0) goto L_0x0409
            r2.a(r4)     // Catch:{ all -> 0x040a }
        L_0x0403:
            bdm r2 = r1.d
            r2.j()
            return
        L_0x0409:
            throw r4     // Catch:{ all -> 0x040a }
        L_0x040a:
            r0 = move-exception
            r2 = r0
            bdm r3 = r1.d
            r3.j()
            throw r2
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bid.run():void");
    }
}
