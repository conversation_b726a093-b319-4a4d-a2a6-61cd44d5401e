package defpackage;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.concurrent.ThreadFactory;

/* renamed from: cpj  reason: default package */
/* compiled from: PG */
public final class cpj implements ThreadFactory {
    public final HashSet a = new HashSet();
    private final ThreadFactory b;

    public cpj(ThreadFactory threadFactory) {
        this.b = threadFactory;
    }

    public final ArrayList a() {
        ArrayList arrayList;
        synchronized (this.a) {
            arrayList = new ArrayList(this.a);
        }
        return arrayList;
    }

    public final Thread newThread(Runnable runnable) {
        Thread newThread = this.b.newThread(new ckm((Object) this, (Object) runnable, 13));
        if (newThread != null) {
            synchronized (this.a) {
                this.a.add(newThread);
            }
        }
        return newThread;
    }
}
