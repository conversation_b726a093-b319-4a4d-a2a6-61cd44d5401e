<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt">
    <item android:state_selected="true" android:color="?attr/colorOnSurfaceVariant" android:alpha="1"/>
    <item android:state_focused="true" android:color="?attr/colorOnSurfaceVariant" android:alpha="1"/>
    <item android:color="?attr/colorHairline" android:alpha="1" android:state_hovered="true"/>
    <item android:color="@android:color/transparent" android:alpha="1"/>
</selector>
