package defpackage;

/* renamed from: bgs  reason: default package */
/* compiled from: PG */
public final class bgs implements bgo {
    public final aus a;
    public final auv b;
    public final auv c;
    private final aub d;

    public bgs(aus aus) {
        this.a = aus;
        this.d = new bgp(aus);
        this.b = new bgq(aus);
        this.c = new bgr(aus);
    }

    public final void a(bgn bgn) {
        this.a.k();
        this.a.l();
        try {
            this.d.b(bgn);
            this.a.o();
        } finally {
            this.a.m();
        }
    }
}
