package defpackage;

import android.app.Application;
import java.util.concurrent.ScheduledExecutorService;

/* renamed from: emi  reason: default package */
/* compiled from: PG */
public final class emi {
    public final frc a;
    public final gsb b = fvf.as(new dtc(this, 17));
    public final gsb c = fvf.as(new dtc(this, 18));
    public final gsb d = fvf.as(new emh(this, 0));
    public final gsb e = fvf.as(new emh(this, 4));
    public final gsb f = fvf.as(new emh(this, 1));
    public final gsb g = fvf.as(new emh(this, 5));
    public final gsb h = fvf.as(new emh(this, 6));
    public final gsb i = fvf.as(new emh(this, 7));
    public final gsb j = fvf.as(new emh(this, 8));
    public final gsb k = fvf.as(new emh(this, 9));
    public final gsb l = fvf.as(new emh(this, 10));
    public final gsb m = fvf.as(new dtc(this, 11));
    public final gsb n = fvf.as(new dtc(this, 13));
    public final gsb o;
    public final gsb p;
    private final frb q;
    private final gsb r = fvf.as(new dtc(this, 19));
    private final gsb s = fvf.as(new dtc(this, 20));
    private final gsb t = fvf.as(new emh(this, 2));
    private final gsb u = fvf.as(new emh(this, 3));
    private final gsb v = fvf.as(new emh(this, 11));
    private final gsb w = fvf.as(new emh(this, 12));

    public emi(ScheduledExecutorService scheduledExecutorService, frd frd, Application application) {
        fvf.as(new dtc(this, 10));
        fvf.as(new dtc(this, 12));
        fvf.as(new dtc(this, 14));
        this.o = fvf.as(new dtc(this, 15));
        this.p = fvf.as(new dtc(this, 16));
        frc e2 = frc.e("audio_library_android");
        this.a = e2;
        frb frb = e2.a;
        if (frb == null) {
            this.q = frf.b(frd, scheduledExecutorService, e2, application);
            return;
        }
        this.q = frb;
        ((frf) frb).b = frd;
    }

    public final void a(String str, String str2) {
        ((fqy) this.r.a()).b(str, str2);
    }

    public final void b(String str, String str2, String str3) {
        ((fqy) this.s.a()).b(str, str2, str3);
    }

    public final void c(String str, String str2, String str3, String str4) {
        ((fqy) this.t.a()).b(str, str2, str3, str4);
    }

    public final void d(String str, String str2, String str3, boolean z) {
        ((fqy) this.u.a()).b(str, str2, str3, Boolean.valueOf(z));
    }

    public final void e(String str, String str2, String str3, String str4) {
        ((fqy) this.v.a()).b(str, str2, str3, str4);
    }

    public final void f(String str, String str2, boolean z, String str3) {
        ((fqy) this.w.a()).b(str, str2, Boolean.valueOf(z), str3);
    }
}
