<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:gravity="center_vertical" android:paddingRight="?android:attr/scrollbarSize" android:layout_width="match_parent" android:layout_height="wrap_content" android:baselineAligned="false" android:minHeight="?android:attr/listPreferredItemHeight">
    <TextView android:textAppearance="?android:attr/textAppearanceMediumInverse" android:textColor="?android:attr/textColorPrimary" android:ellipsize="marquee" android:id="@+id/license" android:fadingEdge="horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginLeft="15dp" android:layout_marginTop="6dp" android:layout_marginRight="6dp" android:layout_marginBottom="6dp" android:singleLine="false" android:layout_marginStart="15dp" android:layout_marginEnd="6dp"/>
</LinearLayout>
