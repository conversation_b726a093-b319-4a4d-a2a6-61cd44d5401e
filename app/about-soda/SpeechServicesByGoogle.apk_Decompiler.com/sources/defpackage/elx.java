package defpackage;

import j$.time.Duration;

/* renamed from: elx  reason: default package */
/* compiled from: PG */
public final class elx implements elp {
    public static final gnk a = jyl.j;
    private final elo b;
    private final jqs c;

    public elx(elo elo, jqs jqs) {
        jnu.e(jqs, "lightweightScope");
        this.b = elo;
        this.c = jqs;
    }

    public static final jyl A(ehg ehg, jyh jyh, eam eam, Integer num) {
        htk l = jyl.i.l();
        jnu.d(l, "newBuilder(...)");
        dlv ak = jnu.e(l, "builder");
        ak.Q(dvx.g(ehg));
        ak.R(jyh);
        htk l2 = jyi.e.l();
        jnu.d(l2, "newBuilder(...)");
        dlv aj = jnu.e(l2, "builder");
        if (num != null) {
            num.intValue();
            aj.N(num.intValue());
        }
        jnu.e(eam, "value");
        htk htk = (htk) aj.a;
        if (!htk.b.B()) {
            htk.u();
        }
        jyi jyi = (jyi) htk.b;
        jyi.b = eam.s;
        jyi.a |= 1;
        ak.S(aj.M());
        return ak.O();
    }

    public static final String B(Integer num) {
        String num2;
        if (num == null || (num2 = num.toString()) == null) {
            return "hotword";
        }
        return num2;
    }

    public static final dxh C(dxm dxm, eak eak) {
        eal eal;
        int i = eak.a;
        if (i == 2) {
            eaj b2 = eaj.b(((Integer) eak.b).intValue());
            if (b2 == null) {
                b2 = eaj.UNKNOWN_OPENING_FAILURE;
            }
            return dxm.c(b2.ag, "start_listening_failure");
        }
        if (i == 1) {
            eal = eal.b(((Integer) eak.b).intValue());
            if (eal == null) {
                eal = eal.UNKNOWN_OPENING_SUCCESS;
            }
        } else {
            eal = eal.UNKNOWN_OPENING_SUCCESS;
        }
        return dxm.c(eal.e, "start_listening_success");
    }

    public static final dxh D(dxl dxl, eah eah) {
        eai eai;
        int i = eah.a;
        if (i == 2) {
            eag b2 = eag.b(((Integer) eah.b).intValue());
            if (b2 == null) {
                b2 = eag.UNKNOWN_CLOSING_FAILURE;
            }
            return dxl.a(b2.L, "stop_listening_failure");
        }
        if (i == 1) {
            eai = eai.b(((Integer) eah.b).intValue());
            if (eai == null) {
                eai = eai.UNKNOWN_CLOSING_SUCCESS;
            }
        } else {
            eai = eai.UNKNOWN_CLOSING_SUCCESS;
        }
        return dxl.a(eai.d, "stop_listening_success");
    }

    public static final dxh E(dxl dxl, eaf eaf) {
        return dxl.a(eaf.z, "update_route_status");
    }

    public static final void F(dxh dxh, long j) {
        dxh.d("listening", String.valueOf(j));
    }

    private static final jyl G(int i) {
        htk l = jyl.i.l();
        jnu.d(l, "newBuilder(...)");
        dlv ak = jnu.e(l, "builder");
        htk l2 = jyf.e.l();
        jnu.d(l2, "newBuilder(...)");
        dlv al = jnu.e(l2, "builder");
        htk htk = (htk) al.a;
        if (!htk.b.B()) {
            htk.u();
        }
        jyf jyf = (jyf) htk.b;
        jyf.a |= 2;
        jyf.c = i;
        ak.P(al.T());
        return ak.O();
    }

    private static final void H(dxh dxh, String str) {
        dxh.d("buffer", str);
    }

    public static final jyl y(ehg ehg, dzq dzq) {
        htk l = jyl.i.l();
        jnu.d(l, "newBuilder(...)");
        dlv ak = jnu.e(l, "builder");
        ak.Q(dvx.g(ehg));
        htk l2 = jyk.c.l();
        jnu.d(l2, "newBuilder(...)");
        jnu.e(l2, "builder");
        jyj h = dvx.h(dzq);
        jnu.e(h, "value");
        if (!l2.b.B()) {
            l2.u();
        }
        jyk jyk = (jyk) l2.b;
        jyk.b = h.j;
        jyk.a |= 1;
        htq o = l2.r();
        jnu.d(o, "build(...)");
        jyk jyk2 = (jyk) o;
        jnu.e(jyk2, "value");
        htk htk = (htk) ak.a;
        if (!htk.b.B()) {
            htk.u();
        }
        jyl jyl = (jyl) htk.b;
        jyk2.getClass();
        jyl.e = jyk2;
        jyl.a |= 8;
        return ak.O();
    }

    public static final jyl z(ehg ehg, jyh jyh, dzq dzq, ebh ebh) {
        htk l = jyl.i.l();
        jnu.d(l, "newBuilder(...)");
        dlv ak = jnu.e(l, "builder");
        ak.Q(dvx.g(ehg));
        ak.R(jyh);
        htk l2 = jyi.e.l();
        jnu.d(l2, "newBuilder(...)");
        dlv aj = jnu.e(l2, "builder");
        if (ebh != null) {
            aj.N(ebh.a);
        }
        jyj h = dvx.h(dzq);
        jnu.e(h, "value");
        htk htk = (htk) aj.a;
        if (!htk.b.B()) {
            htk.u();
        }
        jyi jyi = (jyi) htk.b;
        jyi.c = h.j;
        jyi.a |= 2;
        ak.S(aj.M());
        return ak.O();
    }

    public final void a(String str, int i) {
        jnu.e(str, "shortIdentity");
        dxl dxl = dwt.p;
        jnu.d(dxl, "AUDIO_BUFFER_CLOSED");
        dxh c2 = dxl.c(1);
        H(c2, str);
        c2.g(a, G(i));
        htk l = ehg.c.l();
        jnu.d(l, "newBuilder(...)");
        x(c2, jnu.e(l, "builder").o());
    }

    public final void b(String str, int i) {
        jnu.e(str, "shortIdentity");
        dxh a2 = dwt.l.a();
        H(a2, str);
        a2.g(a, G(i));
        htk l = ehg.c.l();
        jnu.d(l, "newBuilder(...)");
        x(a2, jnu.e(l, "builder").o());
    }

    public final void c(String str, int i) {
        jnu.e(str, "shortIdentity");
        dxh a2 = dwt.o.a();
        H(a2, str);
        a2.g(a, G(i));
        htk l = ehg.c.l();
        jnu.d(l, "newBuilder(...)");
        x(a2, jnu.e(l, "builder").o());
    }

    public final void d(String str) {
        jnu.e(str, "shortIdentity");
        dxh a2 = dwt.j.a();
        H(a2, str);
        htk l = ehg.c.l();
        jnu.d(l, "newBuilder(...)");
        x(a2, jnu.e(l, "builder").o());
    }

    public final void e(String str, int i) {
        jnu.e(str, "shortIdentity");
        dxh a2 = dwt.n.a();
        H(a2, str);
        a2.g(a, G(i));
        htk l = ehg.c.l();
        jnu.d(l, "newBuilder(...)");
        x(a2, jnu.e(l, "builder").o());
    }

    public final void f(String str, Integer num) {
        dxh a2 = dwt.m.a();
        H(a2, str);
        if (num != null) {
            gnk gnk = a;
            htk l = jyl.i.l();
            jnu.d(l, "newBuilder(...)");
            dlv ak = jnu.e(l, "builder");
            htk l2 = jyf.e.l();
            jnu.d(l2, "newBuilder(...)");
            dlv al = jnu.e(l2, "builder");
            Object obj = al.a;
            int intValue = num.intValue();
            htk htk = (htk) obj;
            if (!htk.b.B()) {
                htk.u();
            }
            jyf jyf = (jyf) htk.b;
            jyf.a |= 1;
            jyf.b = intValue;
            ak.P(al.T());
            a2.g(gnk, ak.O());
        }
        htk l3 = ehg.c.l();
        jnu.d(l3, "newBuilder(...)");
        x(a2, jnu.e(l3, "builder").o());
    }

    public final void g(String str, int i) {
        jnu.e(str, "shortIdentity");
        dxh a2 = dwt.k.a();
        H(a2, str);
        gnk gnk = a;
        htk l = jyl.i.l();
        jnu.d(l, "newBuilder(...)");
        dlv ak = jnu.e(l, "builder");
        htk l2 = jyf.e.l();
        jnu.d(l2, "newBuilder(...)");
        dlv al = jnu.e(l2, "builder");
        htk htk = (htk) al.a;
        if (!htk.b.B()) {
            htk.u();
        }
        jyf jyf = (jyf) htk.b;
        jyf.a |= 4;
        jyf.d = i;
        ak.P(al.T());
        a2.g(gnk, ak.O());
        htk l3 = ehg.c.l();
        jnu.d(l3, "newBuilder(...)");
        x(a2, jnu.e(l3, "builder").o());
    }

    public final void h(ehg ehg, int i, dym dym) {
        jnu.e(ehg, "clientInfo");
        jnu.e(dym, "params");
        dxh a2 = dwt.q.a();
        a2.d("focus_acquire", String.valueOf(i));
        gnk gnk = a;
        htk l = jyl.i.l();
        jnu.d(l, "newBuilder(...)");
        dlv ak = jnu.e(l, "builder");
        ak.Q(dvx.g(ehg));
        htk l2 = jyd.c.l();
        jnu.d(l2, "newBuilder(...)");
        jnu.e(l2, "builder");
        htk htk = (htk) dym.C(5);
        htk.x(dym);
        jnu.d(htk, "toBuilder(...)");
        jnu.e(htk, "builder");
        if ((dym.a & 16) != 0) {
            if (!htk.b.B()) {
                htk.u();
            }
            dym dym2 = (dym) htk.b;
            dym2.a &= -3;
            dym2.c = 3;
        }
        if (!htk.b.B()) {
            htk.u();
        }
        dym dym3 = (dym) htk.b;
        dym3.e = null;
        dym3.a &= -9;
        htq o = htk.r();
        jnu.d(o, "build(...)");
        dym dym4 = (dym) o;
        jnu.e(dym4, "value");
        if (!l2.b.B()) {
            l2.u();
        }
        jyd jyd = (jyd) l2.b;
        dym4.getClass();
        jyd.b = dym4;
        jyd.a |= 1;
        htq o2 = l2.r();
        jnu.d(o2, "build(...)");
        jyd jyd2 = (jyd) o2;
        jnu.e(jyd2, "value");
        htk htk2 = (htk) ak.a;
        if (!htk2.b.B()) {
            htk2.u();
        }
        jyl jyl = (jyl) htk2.b;
        jyd2.getClass();
        jyl.g = jyd2;
        jyl.a |= 64;
        a2.g(gnk, ak.O());
        x(a2, ehg);
    }

    public final void i(ehg ehg, int i, dyl dyl) {
        jnu.e(dyl, "reason");
        dxh a2 = dwt.s.a();
        a2.d("focus_release", String.valueOf(i));
        gnk gnk = a;
        htk l = jyl.i.l();
        jnu.d(l, "newBuilder(...)");
        dlv ak = jnu.e(l, "builder");
        ak.Q(dvx.g(ehg));
        htk l2 = jye.c.l();
        jnu.d(l2, "newBuilder(...)");
        jnu.e(l2, "builder");
        jnu.e(dyl, "value");
        if (!l2.b.B()) {
            l2.u();
        }
        jye jye = (jye) l2.b;
        jye.b = dyl.e;
        jye.a |= 1;
        htq o = l2.r();
        jnu.d(o, "build(...)");
        jye jye2 = (jye) o;
        jnu.e(jye2, "value");
        htk htk = (htk) ak.a;
        if (!htk.b.B()) {
            htk.u();
        }
        jyl jyl = (jyl) htk.b;
        jye2.getClass();
        jyl.h = jye2;
        jyl.a |= 128;
        a2.g(gnk, ak.O());
        x(a2, ehg);
    }

    public final void j(ehg ehg) {
        jnu.e(ehg, "clientInfo");
        x(dwt.x.a(), ehg);
    }

    public final void k(ehg ehg, ebl ebl, long j, ejn ejn) {
        ehg ehg2 = ehg;
        jnu.e(ehg, "clientInfo");
        jnu.e(ebl, "hotwordListeningSession");
        ejn ejn2 = ejn;
        jnu.e(ejn2, "routeData");
        ehg ehg3 = ehg;
        long j2 = j;
        job.S(this.c, (jlv) null, (jqt) null, new elu(this, ehg3, j2, ejn2, ebl, (jlr) null), 3);
        job.S(this.c, (jlv) null, (jqt) null, new elv(this, ehg3, j2, ebl, (jlr) null, 0), 3);
    }

    public final void l(ehg ehg, dyy dyy, long j, dze dze, ejn ejn) {
        ehg ehg2 = ehg;
        jnu.e(ehg, "clientInfo");
        jnu.e(dyy, "audioRequestListeningSession");
        dze dze2 = dze;
        jnu.e(dze2, "params");
        ejn ejn2 = ejn;
        jnu.e(ejn2, "routeData");
        ehg ehg3 = ehg;
        long j2 = j;
        job.S(this.c, (jlv) null, (jqt) null, new elt(this, ehg3, j2, ejn2, dze2, dyy, (jlr) null), 3);
        job.S(this.c, (jlv) null, (jqt) null, new elv(this, ehg3, j2, dyy, (jlr) null, 1), 3);
    }

    public final void m(ehg ehg, Integer num) {
        jnu.e(ehg, "clientInfo");
        dxh a2 = dwt.a.a();
        a2.d("route_connect", B(num));
        x(a2, ehg);
    }

    public final void n(ehg ehg, Integer num, dzq dzq, hme hme) {
        jnu.e(ehg, "clientInfo");
        jnu.e(dzq, "audioRouteType");
        jnu.e(hme, "disconnectStatus");
        job.S(this.c, (jlv) null, (jqt) null, new elw(this, ehg, num, dzq, hme, (jlr) null, 1, (byte[]) null), 3);
    }

    public final void o(ehg ehg, Integer num) {
        jnu.e(ehg, "clientInfo");
        dxh a2 = dwt.c.a();
        a2.d("route_disconnect", B(num));
        x(a2, ehg);
    }

    public final void p(ehg ehg, hme hme, hme hme2, Integer num, dzq dzq) {
        jnu.e(ehg, "clientInfo");
        jnu.e(hme, "connectStatus");
        jnu.e(hme2, "disconnectStatus");
        jnu.e(dzq, "audioRouteType");
        job.S(this.c, (jlv) null, (jqt) null, new elw(this, ehg, num, dzq, hme, (jlr) null, 0), 3);
        n(ehg, num, dzq, hme2);
    }

    public final void q(ehg ehg, Integer num, dzq dzq, eaf eaf) {
        jnu.e(ehg, "clientInfo");
        jnu.e(eaf, "status");
        dxl dxl = dwt.v;
        jnu.d(dxl, "AUDIO_ROUTE_INNER_CONNECT_DONE");
        dxh E = E(dxl, eaf);
        E.d("route_inner_connect", B(num));
        E.g(a, y(ehg, dzq));
        x(E, ehg);
    }

    public final void r(ehg ehg, Integer num) {
        jnu.e(ehg, "clientInfo");
        dxh a2 = dwt.u.a();
        a2.d("route_inner_connect", B(num));
        x(a2, ehg);
    }

    public final void s(ehg ehg, long j, Duration duration) {
        jnu.e(ehg, "clientInfo");
        jnu.e(duration, "elapsedTime");
        dxh a2 = dwt.e.a();
        F(a2, j);
        a2.f(duration.toNanos());
        x(a2, ehg);
    }

    public final void t(ehg ehg, long j) {
        jnu.e(ehg, "clientInfo");
        dxh a2 = dwt.h.a();
        F(a2, j);
        x(a2, ehg);
    }

    public final void u(elq elq, eak eak) {
        jnu.e(eak, "status");
        dxm dxm = dwt.w;
        jnu.d(dxm, "ZLM_AUDIO_SOURCE_OPEN_DONE");
        dxh C = C(dxm, eak);
        Integer num = elq.c;
        if (num != null) {
            C.d("listening", num.toString());
        }
        x(C, elq.a);
    }

    public final void v(ehg ehg, int i, eec eec) {
        jnu.e(ehg, "clientInfo");
        job.S(this.c, (jlv) null, (jqt) null, new els(this, ehg, i, eec, (jlr) null, 1, (byte[]) null), 3);
        job.S(this.c, (jlv) null, (jqt) null, new els(this, ehg, i, eec, (jlr) null, 0), 3);
    }

    public final void w(ehg ehg, long j) {
        jnu.e(ehg, "clientInfo");
        dxh a2 = dwt.g.a();
        F(a2, j);
        x(a2, ehg);
    }

    public final void x(dxh dxh, ehg ehg) {
        this.b.a(ehg);
    }
}
