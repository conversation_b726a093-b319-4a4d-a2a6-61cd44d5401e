package defpackage;

/* renamed from: epw  reason: default package */
/* compiled from: PG */
public final class epw implements epy {
    private final String a;

    public epw() {
        this((char[]) null);
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if ((obj instanceof epw) && jnu.i(this.a, ((epw) obj).a)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return this.a.hashCode();
    }

    public final String toString() {
        return "Occupied(appOwner=" + this.a + ")";
    }

    public epw(byte[] bArr) {
        this.a = "";
    }

    public /* synthetic */ epw(char[] cArr) {
        this((byte[]) null);
    }
}
