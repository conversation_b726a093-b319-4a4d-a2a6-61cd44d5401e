<?xml version="1.0" encoding="utf-8"?>
<set xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt">
    <set android:ordering="sequentially">
        <objectAnimator android:interpolator="@interpolator/fast_out_slow_in" android:duration="166" android:valueFrom="0" android:valueTo="0" android:valueType="floatType" android:propertyName="scaleX"/>
        <objectAnimator android:interpolator="@interpolator/fast_out_slow_in" android:duration="16" android:valueFrom="0" android:valueTo="1.5" android:valueType="floatType" android:propertyName="scaleX"/>
        <objectAnimator android:interpolator="@interpolator/fast_out_slow_in" android:duration="316" android:valueFrom="1.5" android:valueTo="1" android:valueType="floatType" android:propertyName="scaleX"/>
    </set>
    <set android:ordering="sequentially">
        <objectAnimator android:interpolator="@interpolator/fast_out_slow_in" android:duration="166" android:valueFrom="0" android:valueTo="0" android:valueType="floatType" android:propertyName="scaleY"/>
        <objectAnimator android:interpolator="@interpolator/fast_out_slow_in" android:duration="16" android:valueFrom="0" android:valueTo="1.5" android:valueType="floatType" android:propertyName="scaleY"/>
        <objectAnimator android:interpolator="@interpolator/fast_out_slow_in" android:duration="316" android:valueFrom="1.5" android:valueTo="1" android:valueType="floatType" android:propertyName="scaleY"/>
    </set>
</set>
