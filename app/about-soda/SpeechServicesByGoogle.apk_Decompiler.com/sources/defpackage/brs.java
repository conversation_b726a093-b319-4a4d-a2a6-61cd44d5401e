package defpackage;

/* renamed from: brs  reason: default package */
/* compiled from: PG */
public final class brs {
    public final int a;
    public final int b;
    public final int c;
    public final int d;
    public final double e;

    public brs() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof brs) {
            brs brs = (brs) obj;
            if (this.a == brs.a && this.b == brs.b && this.c == brs.c && this.d == brs.d) {
                if (Double.doubleToLongBits(this.e) == Double.doubleToLongBits(brs.e)) {
                    return true;
                }
            }
        }
        return false;
    }

    public final int hashCode() {
        return ((int) ((Double.doubleToLongBits(this.e) >>> 32) ^ Double.doubleToLongBits(this.e))) ^ ((((((((this.a ^ 1000003) * 1000003) ^ this.b) * 1000003) ^ this.c) * 1000003) ^ this.d) * 1000003);
    }

    public final String toString() {
        return "InternalTimepoint{sentenceIndex=" + this.a + ", sayIndex=" + this.b + ", startChar=" + this.c + ", endChar=" + this.d + ", startTime=" + this.e + "}";
    }

    public brs(int i, int i2, int i3, int i4, double d2) {
        this.a = i;
        this.b = i2;
        this.c = i3;
        this.d = i4;
        this.e = d2;
    }
}
