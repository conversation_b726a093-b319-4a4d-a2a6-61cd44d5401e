package defpackage;

/* renamed from: bfh  reason: default package */
/* compiled from: PG */
public final class bfh extends bfd {
    public bfh(bft bft) {
        super(bft);
    }

    public final boolean b(bhe bhe) {
        jnu.e(bhe, "workSpec");
        if (bhe.k.b == bbl.CONNECTED) {
            return true;
        }
        return false;
    }

    public final int d() {
        return 7;
    }

    public final /* bridge */ /* synthetic */ boolean e(Object obj) {
        bex bex = (bex) obj;
        jnu.e(bex, "value");
        if (!bex.a || !bex.b) {
            return true;
        }
        return false;
    }
}
