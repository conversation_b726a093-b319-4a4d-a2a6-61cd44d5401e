package defpackage;

import android.content.Context;
import j$.util.Optional;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;

/* renamed from: coa  reason: default package */
/* compiled from: PG */
public final class coa implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;
    private final jjk e;
    private final jjk f;
    private final jjk g;
    private final /* synthetic */ int h;

    public coa(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, int i) {
        this.h = i;
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
        this.e = jjk5;
        this.f = jjk6;
        this.g = jjk7;
    }

    public final /* synthetic */ Object b() {
        switch (this.h) {
            case 0:
                int max = Math.max(2, Runtime.getRuntime().availableProcessors() - 2);
                Integer.valueOf(max).getClass();
                cpk a2 = ((cps) this.c).b();
                cpp a3 = ((cob) this.g).b();
                cqn cqn = new cqn("Lite", max, ((Boolean) ((grh) ((iiv) this.e).a).d(false)).booleanValue());
                return new coi(hfc.E(coo.a((grh) ((iiv) this.d).a, cqx.C(cqn, a2, cqx.D(cqn.a, new cnt(new cnu((ThreadFactory) this.a.b(), 2), cqx.A())), cqx.J((bzl) this.f.b(), cqn), a3))), (hmi) this.b.b());
            case 1:
                return new cmg(((iim) this.e).a(), (hmh) this.d.b(), (hmi) this.g.b(), ((bqt) this.a).b(), (Optional) ((iiv) this.b).a, (Optional) ((iiv) this.f).a, ((bqt) this.c).b());
            case 2:
                jjk jjk = this.a;
                Context a4 = ((iim) this.e).a();
                Object obj = ((iiv) this.d).a;
                ihn c2 = iit.c(this.b);
                jjk jjk2 = this.f;
                grh a5 = ((bqs) this.g).a();
                cqh cqh = (cqh) jjk2.b();
                gsb as = fvf.as(new gdc((Executor) (ScheduledExecutorService) jjk.b(), c2, a4, a5, (kjd) this.c.b(), (grh) obj, 1));
                hzz.u(as);
                return as;
            case 3:
                jjk jjk3 = this.a;
                jjk jjk4 = this.d;
                jjk jjk5 = this.e;
                ihn c3 = iit.c(this.b);
                cqx cqx = (cqx) jjk5.b();
                return new dml(((dme) this.g).b(), ((iim) jjk4).a(), (hmi) jjk3.b(), c3, this.c, this.f);
            case 4:
                jjk jjk6 = this.a;
                return new dqn(((dme) this.g).b(), ((iim) this.e).a(), (dkq) jjk6.b(), (Executor) this.b.b(), iit.c(this.d), ((drn) this.c).b(), this.f);
            case 5:
                jjk jjk7 = this.g;
                jjk jjk8 = this.f;
                jjk jjk9 = this.b;
                return new egu(((iim) this.a).a(), (Executor) jjk7.b(), (jqs) this.c.b(), ((ehe) this.e).b(), ((gbb) this.d).b(), iit.c(jjk9), (gnk) jjk8.b());
            case 6:
                return new eyg(((eze) this.c).b(), (ezo) this.f.b(), (Optional) ((iiv) this.d).a, (hmh) this.g.b(), (exy) this.a.b(), (dwj) this.e.b(), ((inb) this.b).a());
            case 7:
                jjk jjk10 = this.d;
                jjk jjk11 = this.g;
                jjk jjk12 = this.f;
                jjk jjk13 = this.b;
                jjk jjk14 = this.c;
                return new fpi(this.e, this.a, jjk14, jjk13, jjk12, jjk11, jjk10);
            case 8:
                jjk jjk15 = this.a;
                return new fxq(this.b, (ixj) this.e.b(), (fps) this.d.b(), (cqx) this.g.b(), jjk15, ((gbg) this.f).a().intValue(), (Executor) this.c.b());
            default:
                return new gop((grh) ((iiv) this.b).a, (grh) ((iiv) this.e).a, (grh) ((iiv) this.a).a, (grh) ((iiv) this.c).a, (Set) ((iiv) this.f).a, (grh) ((iiv) this.d).a, (grh) ((iiv) this.g).a);
        }
    }

    public coa(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, int i, byte[] bArr) {
        this.h = i;
        this.e = jjk;
        this.d = jjk2;
        this.g = jjk3;
        this.a = jjk4;
        this.b = jjk5;
        this.f = jjk6;
        this.c = jjk7;
    }

    public coa(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, int i, char[] cArr) {
        this.h = i;
        this.e = jjk;
        this.a = jjk2;
        this.c = jjk3;
        this.b = jjk4;
        this.d = jjk5;
        this.g = jjk6;
        this.f = jjk7;
    }

    public coa(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, int i, float[] fArr) {
        this.h = i;
        this.c = jjk;
        this.f = jjk2;
        this.d = jjk3;
        this.g = jjk4;
        this.a = jjk5;
        this.e = jjk6;
        this.b = jjk7;
    }

    public coa(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, int i, int[] iArr) {
        this.h = i;
        this.g = jjk;
        this.e = jjk2;
        this.a = jjk3;
        this.b = jjk4;
        this.d = jjk5;
        this.c = jjk6;
        this.f = jjk7;
    }

    public coa(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, int i, short[] sArr) {
        this.h = i;
        this.g = jjk;
        this.d = jjk2;
        this.a = jjk3;
        this.b = jjk4;
        this.c = jjk5;
        this.e = jjk6;
        this.f = jjk7;
    }

    public coa(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, int i, boolean[] zArr) {
        this.h = i;
        this.a = jjk;
        this.g = jjk2;
        this.c = jjk3;
        this.e = jjk4;
        this.d = jjk5;
        this.b = jjk6;
        this.f = jjk7;
    }

    public coa(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, int i, byte[][] bArr) {
        this.h = i;
        this.e = jjk;
        this.a = jjk2;
        this.c = jjk3;
        this.b = jjk4;
        this.f = jjk5;
        this.g = jjk6;
        this.d = jjk7;
    }

    public coa(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, int i, char[][] cArr) {
        this.h = i;
        this.b = jjk;
        this.e = jjk2;
        this.d = jjk3;
        this.g = jjk4;
        this.a = jjk5;
        this.f = jjk6;
        this.c = jjk7;
    }

    public coa(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, int i, short[][] sArr) {
        this.h = i;
        this.b = jjk;
        this.e = jjk2;
        this.a = jjk3;
        this.c = jjk4;
        this.f = jjk5;
        this.d = jjk6;
        this.g = jjk7;
    }
}
