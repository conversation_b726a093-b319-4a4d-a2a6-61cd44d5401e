package defpackage;

import androidx.wear.ambient.AmbientLifecycleObserverKt;

/* renamed from: bbi  reason: default package */
/* compiled from: PG */
public final class bbi extends AmbientLifecycleObserverKt {
    public final bat a;

    public bbi(bat bat) {
        super((byte[]) null);
        this.a = bat;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        return this.a.equals(((bbi) obj).a);
    }

    public final int hashCode() {
        return 3016951 + this.a.hashCode();
    }

    public final String toString() {
        return "Success {mOutputData=" + this.a + '}';
    }

    public bbi() {
        this(bat.a);
    }
}
