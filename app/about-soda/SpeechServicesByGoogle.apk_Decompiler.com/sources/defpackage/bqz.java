package defpackage;

import android.os.Bundle;
import androidx.wear.ambient.AmbientDelegate;

/* renamed from: bqz  reason: default package */
/* compiled from: PG */
public final class bqz implements ggn, ihr, iio {
    public final bc a;
    public iiz b;
    public iiz c;
    public iiz d;
    public final brc e;
    public final bqx f;
    private iiz g;
    private iiz h;
    private final bqz i;

    public bqz() {
        throw null;
    }

    public final Bundle a() {
        Bundle bundle = this.a.l;
        if (bundle == null) {
            return new Bundle();
        }
        return bundle;
    }

    public final byu b() {
        return new byu((fzv) this.c.b(), (bym) this.e.C.b());
    }

    public final gpk c() {
        return new gpk(new bzl((Object) (gnk) this.e.g.b()));
    }

    public final AmbientDelegate d() {
        return new AmbientDelegate(this.e, this.i);
    }

    public final ixj e() {
        return this.f.f();
    }

    public bqz(brc brc, bqx bqx, bc bcVar) {
        this.i = this;
        this.e = brc;
        this.f = bqx;
        this.a = bcVar;
        iiu a2 = iiv.a(bcVar);
        this.g = a2;
        this.b = iit.d(new fxu(a2, brc.i, 12));
        iiz iiz = bqw.a;
        this.h = iiz;
        this.c = iit.d(new fxu(this.g, iiz, 3));
        this.d = ijd.a(new gbh(this.g, 12));
    }
}
