package defpackage;

import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;

/* renamed from: cyq  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cyq implements gqx {
    public final /* synthetic */ Set a;
    public final /* synthetic */ ctj b;
    public final /* synthetic */ AtomicLong c;
    public final /* synthetic */ Set d;
    public final /* synthetic */ boolean e;
    public final /* synthetic */ cys f;
    public final /* synthetic */ cxg g;
    public final /* synthetic */ Set h;

    public /* synthetic */ cyq(Set set, ctj ctj, AtomicLong atomicLong, Set set2, boolean z, cys cys, cxg cxg, Set set3) {
        this.a = set;
        this.b = ctj;
        this.c = atomicLong;
        this.d = set2;
        this.e = z;
        this.f = cys;
        this.g = cxg;
        this.h = set3;
    }

    public final Object apply(Object obj) {
        Long l = (Long) obj;
        Set set = this.a;
        ctj ctj = this.b;
        if (!set.contains(ctj)) {
            this.c.getAndAdd(l.longValue());
            set.add(ctj);
        }
        cys cys = this.f;
        boolean z = this.e;
        Set set2 = this.d;
        if (!set2.contains(ctj)) {
            if (z) {
                cys.b += l.longValue();
            }
            cys.a += l.longValue();
            set2.add(ctj);
        }
        if (!this.g.a.e) {
            return null;
        }
        Set set3 = this.h;
        fvf.aP(set3);
        if (set3.contains(ctj)) {
            return null;
        }
        if (z) {
            cys.d += l.longValue();
            cys.f++;
        }
        cys.c += l.longValue();
        set3.add(ctj);
        return null;
    }
}
