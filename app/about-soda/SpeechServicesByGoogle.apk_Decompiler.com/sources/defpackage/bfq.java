package defpackage;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;

/* renamed from: bfq  reason: default package */
/* compiled from: PG */
public final class bfq extends BroadcastReceiver {
    final /* synthetic */ bfr a;

    public bfq(bfr bfr) {
        this.a = bfr;
    }

    public final void onReceive(Context context, Intent intent) {
        jnu.e(context, "context");
        jnu.e(intent, "intent");
        this.a.c(intent);
    }
}
