package j$.time.temporal;

import j$.time.chrono.C0010i;
import j$.time.chrono.u;

/* 'enum' modifier removed */
final class c extends f {
    c() {
        super("QUARTER_OF_YEAR", 1);
    }

    public final long g(m mVar) {
        if (p(mVar)) {
            return (mVar.B(a.MONTH_OF_YEAR) + 2) / 3;
        }
        throw new RuntimeException("Unsupported field: QuarterOfYear");
    }

    public final t m() {
        return t.j(1, 4);
    }

    public final boolean p(m mVar) {
        if (mVar.e(a.MONTH_OF_YEAR)) {
            p pVar = h.a;
            if (C0010i.p(mVar).equals(u.d)) {
                return true;
            }
        }
        return false;
    }

    public final k s(k kVar, long j) {
        long g = g(kVar);
        m().b(j, this);
        a aVar = a.MONTH_OF_YEAR;
        return kVar.d(((j - g) * 3) + kVar.B(aVar), aVar);
    }

    public final t t(m mVar) {
        if (p(mVar)) {
            return m();
        }
        throw new RuntimeException("Unsupported field: QuarterOfYear");
    }

    public final String toString() {
        return "QuarterOfYear";
    }
}
