package defpackage;

import android.content.res.ColorStateList;
import android.graphics.PorterDuff;
import android.graphics.drawable.Drawable;
import android.os.Parcelable;
import android.support.v7.widget.AppCompatButton;
import android.text.TextUtils;
import android.util.Log;
import android.view.accessibility.AccessibilityEvent;
import android.view.accessibility.AccessibilityNodeInfo;
import android.widget.Button;
import android.widget.Checkable;
import android.widget.CompoundButton;
import java.util.Iterator;
import java.util.LinkedHashSet;

/* renamed from: fsq  reason: default package */
/* compiled from: PG */
public class fsq extends AppCompatButton implements Checkable, fvx {
    private static final int[] b = {16842911};
    private static final int[] c = {16842912};
    private final fsr d;
    private final LinkedHashSet e = new LinkedHashSet();
    private final PorterDuff.Mode f;
    private final ColorStateList g;
    private Drawable h;
    private final int i;
    private int j;
    private int k;
    private final int l;
    private boolean m = false;
    private boolean n = false;
    private final int o;

    /* JADX WARNING: Code restructure failed: missing block: B:4:0x005e, code lost:
        r8 = defpackage.ke.h(r8, (r11 = r6.getResourceId(10, 0)));
     */
    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public fsq(android.content.Context r24, android.util.AttributeSet r25) {
        /*
            r23 = this;
            r0 = r23
            r1 = r25
            r2 = 2130969503(0x7f04039f, float:1.754769E38)
            r3 = 2132084687(0x7f1507cf, float:1.9809552E38)
            r4 = r24
            android.content.Context r4 = defpackage.fwl.a(r4, r1, r2, r3)
            r0.<init>(r4, r1, r2)
            java.util.LinkedHashSet r4 = new java.util.LinkedHashSet
            r4.<init>()
            r0.e = r4
            r4 = 0
            r0.m = r4
            r0.n = r4
            android.content.Context r5 = r23.getContext()
            int[] r6 = defpackage.fst.a
            android.content.res.TypedArray r6 = defpackage.ful.c(r5, r1, r6, r2, r3)
            r7 = 12
            int r7 = r6.getDimensionPixelSize(r7, r4)
            r0.l = r7
            r8 = 15
            r9 = -1
            int r8 = r6.getInt(r8, r9)
            android.graphics.PorterDuff$Mode r10 = android.graphics.PorterDuff.Mode.SRC_IN
            android.graphics.PorterDuff$Mode r8 = defpackage.a.e(r8, r10)
            r0.f = r8
            android.content.Context r8 = r23.getContext()
            r10 = 14
            android.content.res.ColorStateList r8 = defpackage.ftc.o(r8, r6, r10)
            r0.g = r8
            android.content.Context r8 = r23.getContext()
            r10 = 10
            boolean r11 = r6.hasValue(r10)
            if (r11 == 0) goto L_0x0064
            int r11 = r6.getResourceId(r10, r4)
            if (r11 == 0) goto L_0x0064
            android.graphics.drawable.Drawable r8 = defpackage.ke.h(r8, r11)
            if (r8 != 0) goto L_0x0068
        L_0x0064:
            android.graphics.drawable.Drawable r8 = r6.getDrawable(r10)
        L_0x0068:
            r0.h = r8
            r8 = 11
            r10 = 1
            int r8 = r6.getInteger(r8, r10)
            r0.o = r8
            r8 = 13
            int r8 = r6.getDimensionPixelSize(r8, r4)
            r0.i = r8
            r8 = 17
            int r8 = r6.getResourceId(r8, r4)
            r11 = 0
            if (r8 != 0) goto L_0x0085
            goto L_0x00a5
        L_0x0085:
            android.content.res.Resources r12 = r5.getResources()
            java.lang.String r12 = r12.getResourceTypeName(r8)
            java.lang.String r13 = "xml"
            boolean r12 = j$.util.Objects.equals(r12, r13)
            if (r12 != 0) goto L_0x0096
            goto L_0x00a5
        L_0x0096:
            kml r12 = new kml
            r12.<init>(r5, r8)
            int r8 = r12.a
            if (r8 != 0) goto L_0x00a0
            goto L_0x00a5
        L_0x00a0:
            iul r11 = new iul
            r11.<init>(r12)
        L_0x00a5:
            if (r11 == 0) goto L_0x00aa
            java.lang.Object r1 = r11.d
            goto L_0x00b4
        L_0x00aa:
            gai r1 = defpackage.fvm.f(r5, r1, r2, r3)
            fvm r2 = new fvm
            r2.<init>(r1)
            r1 = r2
        L_0x00b4:
            fsr r2 = new fsr
            fvm r1 = (defpackage.fvm) r1
            r2.<init>(r0, r1)
            r0.d = r2
            int r1 = r6.getDimensionPixelOffset(r10, r4)
            r2.d = r1
            r1 = 2
            int r3 = r6.getDimensionPixelOffset(r1, r4)
            r2.e = r3
            r3 = 3
            int r3 = r6.getDimensionPixelOffset(r3, r4)
            r2.f = r3
            r3 = 4
            int r3 = r6.getDimensionPixelOffset(r3, r4)
            r2.g = r3
            r3 = 8
            boolean r5 = r6.hasValue(r3)
            if (r5 == 0) goto L_0x00f0
            int r3 = r6.getDimensionPixelSize(r3, r9)
            r2.h = r3
            fvm r5 = r2.b
            float r3 = (float) r3
            fvm r3 = r5.b(r3)
            r2.c(r3)
        L_0x00f0:
            r3 = 20
            int r3 = r6.getDimensionPixelSize(r3, r4)
            r2.i = r3
            r3 = 7
            int r3 = r6.getInt(r3, r9)
            android.graphics.PorterDuff$Mode r5 = android.graphics.PorterDuff.Mode.SRC_IN
            android.graphics.PorterDuff$Mode r3 = defpackage.a.e(r3, r5)
            r2.j = r3
            fsq r3 = r2.a
            android.content.Context r3 = r3.getContext()
            r5 = 6
            android.content.res.ColorStateList r3 = defpackage.ftc.o(r3, r6, r5)
            r2.k = r3
            fsq r3 = r2.a
            android.content.Context r3 = r3.getContext()
            r5 = 19
            android.content.res.ColorStateList r3 = defpackage.ftc.o(r3, r6, r5)
            r2.l = r3
            fsq r3 = r2.a
            android.content.Context r3 = r3.getContext()
            r5 = 16
            android.content.res.ColorStateList r3 = defpackage.ftc.o(r3, r6, r5)
            r2.m = r3
            r3 = 5
            boolean r3 = r6.getBoolean(r3, r4)
            r2.p = r3
            r3 = 9
            int r3 = r6.getDimensionPixelSize(r3, r4)
            r2.s = r3
            r3 = 21
            boolean r3 = r6.getBoolean(r3, r10)
            r2.q = r3
            fsq r3 = r2.a
            int r3 = r3.getPaddingStart()
            fsq r5 = r2.a
            int r5 = r5.getPaddingTop()
            fsq r8 = r2.a
            int r8 = r8.getPaddingEnd()
            fsq r12 = r2.a
            int r12 = r12.getPaddingBottom()
            boolean r13 = r6.hasValue(r4)
            if (r13 == 0) goto L_0x016a
            r2.b()
            r22 = r7
            goto L_0x023a
        L_0x016a:
            fvi r13 = new fvi
            fvm r14 = r2.b
            r13.<init>((defpackage.fvm) r14)
            iul r14 = r2.t
            if (r14 == 0) goto L_0x0178
            r13.q(r14)
        L_0x0178:
            zj r14 = r2.c
            if (r14 == 0) goto L_0x017f
            r13.j(r14)
        L_0x017f:
            fsq r14 = r2.a
            android.content.Context r14 = r14.getContext()
            r13.i(r14)
            android.content.res.ColorStateList r14 = r2.k
            r13.setTintList(r14)
            android.graphics.PorterDuff$Mode r14 = r2.j
            if (r14 == 0) goto L_0x0194
            r13.setTintMode(r14)
        L_0x0194:
            int r14 = r2.i
            float r14 = (float) r14
            android.content.res.ColorStateList r15 = r2.l
            r13.o(r14)
            r13.n(r15)
            fvi r14 = new fvi
            fvm r15 = r2.b
            r14.<init>((defpackage.fvm) r15)
            iul r15 = r2.t
            if (r15 == 0) goto L_0x01ad
            r14.q(r15)
        L_0x01ad:
            zj r15 = r2.c
            if (r15 == 0) goto L_0x01b4
            r14.j(r15)
        L_0x01b4:
            r14.setTint(r4)
            int r15 = r2.i
            float r15 = (float) r15
            r14.o(r15)
            android.content.res.ColorStateList r15 = android.content.res.ColorStateList.valueOf(r4)
            r14.n(r15)
            fvi r15 = new fvi
            fvm r10 = r2.b
            r15.<init>((defpackage.fvm) r10)
            r2.n = r15
            iul r10 = r2.t
            if (r10 == 0) goto L_0x01d8
            android.graphics.drawable.Drawable r15 = r2.n
            fvi r15 = (defpackage.fvi) r15
            r15.q(r10)
        L_0x01d8:
            zj r10 = r2.c
            if (r10 == 0) goto L_0x01e3
            android.graphics.drawable.Drawable r15 = r2.n
            fvi r15 = (defpackage.fvi) r15
            r15.j(r10)
        L_0x01e3:
            fsq r10 = r2.a
            android.graphics.drawable.Drawable r15 = r2.n
            r15.setTint(r9)
            android.graphics.drawable.RippleDrawable r9 = new android.graphics.drawable.RippleDrawable
            android.content.res.ColorStateList r15 = r2.m
            android.content.res.ColorStateList r15 = defpackage.fux.a(r15)
            android.graphics.drawable.LayerDrawable r0 = new android.graphics.drawable.LayerDrawable
            android.graphics.drawable.Drawable[] r1 = new android.graphics.drawable.Drawable[r1]
            r1[r4] = r14
            r14 = 1
            r1[r14] = r13
            r0.<init>(r1)
            android.graphics.drawable.InsetDrawable r1 = new android.graphics.drawable.InsetDrawable
            int r13 = r2.d
            int r14 = r2.f
            int r4 = r2.e
            r22 = r7
            int r7 = r2.g
            r16 = r1
            r17 = r0
            r18 = r13
            r19 = r14
            r20 = r4
            r21 = r7
            r16.<init>(r17, r18, r19, r20, r21)
            android.graphics.drawable.Drawable r0 = r2.n
            r9.<init>(r15, r1, r0)
            r2.r = r9
            android.graphics.drawable.LayerDrawable r0 = r2.r
            super.setBackgroundDrawable(r0)
            fvi r0 = r2.a()
            if (r0 == 0) goto L_0x023a
            int r1 = r2.s
            float r1 = (float) r1
            r0.k(r1)
            fsq r1 = r2.a
            int[] r1 = r1.getDrawableState()
            r0.setState(r1)
        L_0x023a:
            fsq r0 = r2.a
            int r1 = r2.d
            int r3 = r3 + r1
            int r1 = r2.f
            int r5 = r5 + r1
            int r1 = r2.e
            int r8 = r8 + r1
            int r1 = r2.g
            int r12 = r12 + r1
            r0.setPaddingRelative(r3, r5, r8, r12)
            if (r11 == 0) goto L_0x0276
            zj r0 = new zj
            r0.<init>()
            boolean r1 = r23.f()
            r3 = 1
            if (r3 == r1) goto L_0x025c
            r1 = 1056964608(0x3f000000, float:0.5)
            goto L_0x025f
        L_0x025c:
            r1 = 1061997773(0x3f4ccccd, float:0.8)
        L_0x025f:
            r0.c(r1)
            r1 = 1145569280(0x44480000, float:800.0)
            r0.e(r1)
            r2.c = r0
            iul r0 = r2.t
            if (r0 == 0) goto L_0x0270
            r2.d()
        L_0x0270:
            r2.t = r11
            r2.d()
            goto L_0x0277
        L_0x0276:
            r3 = 1
        L_0x0277:
            r6.recycle()
            r0 = r23
            r1 = r22
            r0.setCompoundDrawablePadding(r1)
            android.graphics.drawable.Drawable r1 = r0.h
            if (r1 == 0) goto L_0x0287
            r4 = r3
            goto L_0x0288
        L_0x0287:
            r4 = 0
        L_0x0288:
            r0.g(r4)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.fsq.<init>(android.content.Context, android.util.AttributeSet):void");
    }

    private final void a() {
        if (j()) {
            setCompoundDrawablesRelative(this.h, (Drawable) null, (Drawable) null, (Drawable) null);
        } else if (i()) {
            setCompoundDrawablesRelative((Drawable) null, (Drawable) null, this.h, (Drawable) null);
        } else if (k()) {
            setCompoundDrawablesRelative((Drawable) null, this.h, (Drawable) null, (Drawable) null);
        }
    }

    private final void g(boolean z) {
        Drawable drawable = this.h;
        if (drawable != null) {
            Drawable mutate = drawable.mutate();
            this.h = mutate;
            mutate.setTintList(this.g);
            PorterDuff.Mode mode = this.f;
            if (mode != null) {
                this.h.setTintMode(mode);
            }
            int i2 = this.i;
            if (i2 == 0) {
                i2 = this.h.getIntrinsicWidth();
            }
            int i3 = this.i;
            if (i3 == 0) {
                i3 = this.h.getIntrinsicHeight();
            }
            Drawable drawable2 = this.h;
            int i4 = this.j;
            int i5 = this.k;
            drawable2.setBounds(i4, i5, i2 + i4, i3 + i5);
            this.h.setVisible(true, z);
        }
        if (z) {
            a();
            return;
        }
        Drawable[] compoundDrawablesRelative = getCompoundDrawablesRelative();
        Drawable drawable3 = compoundDrawablesRelative[0];
        Drawable drawable4 = compoundDrawablesRelative[1];
        Drawable drawable5 = compoundDrawablesRelative[2];
        if ((j() && drawable3 != this.h) || ((i() && drawable5 != this.h) || (k() && drawable4 != this.h))) {
            a();
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:46:0x00d4 A[ADDED_TO_REGION] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    private final void h(int r9, int r10) {
        /*
            r8 = this;
            android.graphics.drawable.Drawable r0 = r8.h
            if (r0 == 0) goto L_0x0140
            android.text.Layout r0 = r8.getLayout()
            if (r0 != 0) goto L_0x000c
            goto L_0x0140
        L_0x000c:
            boolean r0 = r8.j()
            r1 = 2
            r2 = 1
            r3 = 0
            if (r0 != 0) goto L_0x00a2
            boolean r0 = r8.i()
            if (r0 == 0) goto L_0x001d
            goto L_0x00a2
        L_0x001d:
            boolean r9 = r8.k()
            if (r9 == 0) goto L_0x0140
            r8.j = r3
            int r9 = r8.o
            r0 = 16
            if (r9 != r0) goto L_0x0031
            r8.k = r3
            r8.g(r3)
            return
        L_0x0031:
            int r9 = r8.i
            if (r9 != 0) goto L_0x003b
            android.graphics.drawable.Drawable r9 = r8.h
            int r9 = r9.getIntrinsicHeight()
        L_0x003b:
            int r0 = r8.getLineCount()
            if (r0 <= r2) goto L_0x004a
            android.text.Layout r0 = r8.getLayout()
            int r0 = r0.getHeight()
            goto L_0x0084
        L_0x004a:
            android.text.TextPaint r0 = r8.getPaint()
            java.lang.CharSequence r2 = r8.getText()
            java.lang.String r2 = r2.toString()
            android.text.method.TransformationMethod r4 = r8.getTransformationMethod()
            if (r4 == 0) goto L_0x0068
            android.text.method.TransformationMethod r4 = r8.getTransformationMethod()
            java.lang.CharSequence r2 = r4.getTransformation(r2, r8)
            java.lang.String r2 = r2.toString()
        L_0x0068:
            android.graphics.Rect r4 = new android.graphics.Rect
            r4.<init>()
            int r5 = r2.length()
            r0.getTextBounds(r2, r3, r5, r4)
            int r0 = r4.height()
            android.text.Layout r2 = r8.getLayout()
            int r2 = r2.getHeight()
            int r0 = java.lang.Math.min(r0, r2)
        L_0x0084:
            int r10 = r10 - r0
            int r0 = r8.getPaddingTop()
            int r10 = r10 - r0
            int r10 = r10 - r9
            int r9 = r8.l
            int r0 = r8.getPaddingBottom()
            int r10 = r10 - r9
            int r10 = r10 - r0
            int r10 = r10 / r1
            int r9 = java.lang.Math.max(r3, r10)
            int r10 = r8.k
            if (r10 == r9) goto L_0x0140
            r8.k = r9
            r8.g(r3)
            return
        L_0x00a2:
            r8.k = r3
            int r10 = r8.getTextAlignment()
            r0 = 3
            r4 = 4
            if (r10 == r2) goto L_0x00bc
            r5 = 6
            if (r10 == r5) goto L_0x00b9
            if (r10 == r0) goto L_0x00b9
            if (r10 == r4) goto L_0x00b6
            android.text.Layout$Alignment r10 = android.text.Layout.Alignment.ALIGN_NORMAL
            goto L_0x00d0
        L_0x00b6:
            android.text.Layout$Alignment r10 = android.text.Layout.Alignment.ALIGN_CENTER
            goto L_0x00d0
        L_0x00b9:
            android.text.Layout$Alignment r10 = android.text.Layout.Alignment.ALIGN_OPPOSITE
            goto L_0x00d0
        L_0x00bc:
            int r10 = r8.getGravity()
            r5 = 8388615(0x800007, float:1.1754953E-38)
            r10 = r10 & r5
            if (r10 == r2) goto L_0x00b6
            r5 = 5
            if (r10 == r5) goto L_0x00b9
            r5 = 8388613(0x800005, float:1.175495E-38)
            if (r10 == r5) goto L_0x00b9
            android.text.Layout$Alignment r10 = android.text.Layout.Alignment.ALIGN_NORMAL
        L_0x00d0:
            int r5 = r8.o
            if (r5 == r2) goto L_0x013b
            if (r5 == r0) goto L_0x013b
            if (r5 != r1) goto L_0x00dc
            android.text.Layout$Alignment r0 = android.text.Layout.Alignment.ALIGN_NORMAL
            if (r10 == r0) goto L_0x013b
        L_0x00dc:
            int r0 = r8.o
            if (r0 != r4) goto L_0x00e4
            android.text.Layout$Alignment r0 = android.text.Layout.Alignment.ALIGN_OPPOSITE
            if (r10 == r0) goto L_0x013b
        L_0x00e4:
            int r0 = r8.i
            if (r0 != 0) goto L_0x00ee
            android.graphics.drawable.Drawable r0 = r8.h
            int r0 = r0.getIntrinsicWidth()
        L_0x00ee:
            int r1 = r8.getLineCount()
            r5 = 0
            r6 = r3
        L_0x00f4:
            if (r6 >= r1) goto L_0x0105
            android.text.Layout r7 = r8.getLayout()
            float r7 = r7.getLineWidth(r6)
            float r5 = java.lang.Math.max(r5, r7)
            int r6 = r6 + 1
            goto L_0x00f4
        L_0x0105:
            double r5 = (double) r5
            double r5 = java.lang.Math.ceil(r5)
            int r1 = (int) r5
            int r9 = r9 - r1
            int r1 = r8.getPaddingEnd()
            int r9 = r9 - r1
            int r9 = r9 - r0
            int r0 = r8.l
            int r1 = r8.getPaddingStart()
            int r9 = r9 - r0
            int r9 = r9 - r1
            android.text.Layout$Alignment r0 = android.text.Layout.Alignment.ALIGN_CENTER
            if (r10 != r0) goto L_0x0120
            int r9 = r9 / 2
        L_0x0120:
            int r10 = r8.getLayoutDirection()
            if (r10 == r2) goto L_0x0128
            r10 = r3
            goto L_0x0129
        L_0x0128:
            r10 = r2
        L_0x0129:
            int r0 = r8.o
            if (r0 == r4) goto L_0x012e
            r2 = r3
        L_0x012e:
            if (r10 == r2) goto L_0x0131
            int r9 = -r9
        L_0x0131:
            int r10 = r8.j
            if (r10 == r9) goto L_0x0140
            r8.j = r9
            r8.g(r3)
            return
        L_0x013b:
            r8.j = r3
            r8.g(r3)
        L_0x0140:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.fsq.h(int, int):void");
    }

    private final boolean i() {
        int i2 = this.o;
        if (i2 == 3 || i2 == 4) {
            return true;
        }
        return false;
    }

    private final boolean j() {
        int i2 = this.o;
        if (i2 == 1 || i2 == 2) {
            return true;
        }
        return false;
    }

    private final boolean k() {
        int i2 = this.o;
        if (i2 == 16 || i2 == 32) {
            return true;
        }
        return false;
    }

    private final boolean l() {
        fsr fsr = this.d;
        if (fsr == null || fsr.o) {
            return false;
        }
        return true;
    }

    /* access modifiers changed from: package-private */
    public final String b() {
        Class cls;
        if (!TextUtils.isEmpty((CharSequence) null)) {
            return null;
        }
        if (true != f()) {
            cls = Button.class;
        } else {
            cls = CompoundButton.class;
        }
        return cls.getName();
    }

    public final void c(fvm fvm) {
        if (l()) {
            this.d.c(fvm);
            return;
        }
        throw new IllegalStateException("Attempted to set ShapeAppearanceModel on a MaterialButton which has an overwritten background.");
    }

    public final void d(ColorStateList colorStateList) {
        if (l()) {
            fsr fsr = this.d;
            if (fsr.k != colorStateList) {
                fsr.k = colorStateList;
                if (fsr.a() != null) {
                    fsr.a().setTintList(fsr.k);
                    return;
                }
                return;
            }
            return;
        }
        he heVar = this.a;
        if (heVar != null) {
            if (heVar.a == null) {
                heVar.a = new lu();
            }
            lu luVar = heVar.a;
            luVar.a = colorStateList;
            luVar.d = true;
            heVar.a();
        }
    }

    public final void e(PorterDuff.Mode mode) {
        if (l()) {
            fsr fsr = this.d;
            if (fsr.j != mode) {
                fsr.j = mode;
                if (fsr.a() != null && fsr.j != null) {
                    fsr.a().setTintMode(fsr.j);
                    return;
                }
                return;
            }
            return;
        }
        he heVar = this.a;
        if (heVar != null) {
            if (heVar.a == null) {
                heVar.a = new lu();
            }
            lu luVar = heVar.a;
            luVar.b = mode;
            luVar.c = true;
            heVar.a();
        }
    }

    public final boolean f() {
        fsr fsr = this.d;
        if (fsr == null || !fsr.p) {
            return false;
        }
        return true;
    }

    public final ColorStateList getBackgroundTintList() {
        lu luVar;
        if (l()) {
            return this.d.k;
        }
        he heVar = this.a;
        if (heVar == null || (luVar = heVar.a) == null) {
            return null;
        }
        return luVar.a;
    }

    public final PorterDuff.Mode getBackgroundTintMode() {
        lu luVar;
        if (l()) {
            return this.d.j;
        }
        he heVar = this.a;
        if (heVar == null || (luVar = heVar.a) == null) {
            return null;
        }
        return luVar.b;
    }

    public final boolean isChecked() {
        return this.m;
    }

    /* access modifiers changed from: protected */
    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        if (l()) {
            fvf.d(this, this.d.a());
        }
    }

    /* access modifiers changed from: protected */
    public final int[] onCreateDrawableState(int i2) {
        int[] onCreateDrawableState = super.onCreateDrawableState(i2 + 2);
        if (f()) {
            mergeDrawableStates(onCreateDrawableState, b);
        }
        if (this.m) {
            mergeDrawableStates(onCreateDrawableState, c);
        }
        return onCreateDrawableState;
    }

    public final void onInitializeAccessibilityEvent(AccessibilityEvent accessibilityEvent) {
        super.onInitializeAccessibilityEvent(accessibilityEvent);
        accessibilityEvent.setClassName(b());
        accessibilityEvent.setChecked(this.m);
    }

    public final void onInitializeAccessibilityNodeInfo(AccessibilityNodeInfo accessibilityNodeInfo) {
        super.onInitializeAccessibilityNodeInfo(accessibilityNodeInfo);
        accessibilityNodeInfo.setClassName(b());
        accessibilityNodeInfo.setCheckable(f());
        accessibilityNodeInfo.setChecked(this.m);
        accessibilityNodeInfo.setClickable(isClickable());
    }

    /* access modifiers changed from: protected */
    public final void onLayout(boolean z, int i2, int i3, int i4, int i5) {
        super.onLayout(z, i2, i3, i4, i5);
        h(getMeasuredWidth(), getMeasuredHeight());
    }

    public final void onRestoreInstanceState(Parcelable parcelable) {
        if (!(parcelable instanceof fsp)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        fsp fsp = (fsp) parcelable;
        super.onRestoreInstanceState(fsp.d);
        setChecked(fsp.a);
    }

    public final Parcelable onSaveInstanceState() {
        fsp fsp = new fsp(super.onSaveInstanceState());
        fsp.a = this.m;
        return fsp;
    }

    /* access modifiers changed from: protected */
    public final void onTextChanged(CharSequence charSequence, int i2, int i3, int i4) {
        super.onTextChanged(charSequence, i2, i3, i4);
        h(getMeasuredWidth(), getMeasuredHeight());
    }

    public final boolean performClick() {
        if (this.d.q) {
            toggle();
        }
        return super.performClick();
    }

    public final void refreshDrawableState() {
        super.refreshDrawableState();
        if (this.h != null) {
            if (this.h.setState(getDrawableState())) {
                invalidate();
            }
        }
    }

    public final void setBackground(Drawable drawable) {
        setBackgroundDrawable(drawable);
    }

    public final void setBackgroundColor(int i2) {
        if (l()) {
            fsr fsr = this.d;
            if (fsr.a() != null) {
                fsr.a().setTint(i2);
                return;
            }
            return;
        }
        super.setBackgroundColor(i2);
    }

    public final void setBackgroundDrawable(Drawable drawable) {
        if (!l()) {
            super.setBackgroundDrawable(drawable);
        } else if (drawable != getBackground()) {
            Log.w("MaterialButton", "MaterialButton manages its own background to control elevation, shape, color and states. Consider using backgroundTint, shapeAppearance and other attributes where available. A custom background will ignore these attributes and you should consider handling interaction states such as pressed, focused and disabled");
            this.d.b();
            super.setBackgroundDrawable(drawable);
        } else {
            getBackground().setState(drawable.getState());
        }
    }

    public final void setBackgroundResource(int i2) {
        Drawable drawable;
        if (i2 != 0) {
            drawable = ke.h(getContext(), i2);
        } else {
            drawable = null;
        }
        setBackgroundDrawable(drawable);
    }

    public final void setBackgroundTintList(ColorStateList colorStateList) {
        d(colorStateList);
    }

    public final void setBackgroundTintMode(PorterDuff.Mode mode) {
        e(mode);
    }

    public final void setChecked(boolean z) {
        if (f() && isEnabled() && this.m != z) {
            this.m = z;
            refreshDrawableState();
            if (getParent() instanceof fss) {
                fss fss = (fss) getParent();
                throw null;
            } else if (!this.n) {
                this.n = true;
                Iterator it = this.e.iterator();
                while (it.hasNext()) {
                    ((fso) it.next()).a();
                }
                this.n = false;
            }
        }
    }

    public final void setElevation(float f2) {
        super.setElevation(f2);
        if (l()) {
            this.d.a().k(f2);
        }
    }

    public final void setTextAlignment(int i2) {
        super.setTextAlignment(i2);
        h(getMeasuredWidth(), getMeasuredHeight());
    }

    public final void toggle() {
        setChecked(!this.m);
    }
}
