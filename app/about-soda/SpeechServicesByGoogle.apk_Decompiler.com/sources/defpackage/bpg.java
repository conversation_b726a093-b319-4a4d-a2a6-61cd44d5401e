package defpackage;

import android.os.Parcel;
import android.os.Parcelable;
import android.util.Log;
import j$.util.Collection;
import j$.util.stream.Collectors;

/* renamed from: bpg  reason: default package */
/* compiled from: PG */
public final class bpg extends cgf {
    public static final Parcelable.Creator CREATOR = new bph();
    public final int A;
    public final int B;
    public final long C;
    public final long D;
    public final int E;
    public final int F;
    public final int G;
    public final int H;
    public final int I;

    /* renamed from: J  reason: collision with root package name */
    public final int f15J;
    public final int K;
    public final int a;
    public final int b;
    public final int c;
    public final int d;
    public final long e;
    public final int f;
    public final int g;
    public final int h;
    public final long i;
    public final long j;
    public final long k;
    public final long l;
    public final long m;
    public final long n;
    public final int o;
    public final int p;
    public final int q;
    public final int r;
    public final boolean s;
    public final int t;
    public final long u;
    public final double v;
    public final double w;
    public final int x;
    public final float y;
    public final byte[] z;

    public bpg(int i2, int i3, int i4, int i5, long j2, int i6, int i7, int i8, long j3, long j4, long j5, long j6, long j7, long j8, int i9, int i10, int i11, int i12, boolean z2, int i13, long j9, double d2, double d3, int i14, float f2, byte[] bArr, int i15, int i16, long j10, long j11, int i17, int i18, int i19, int i20, int i21, int i22, int i23) {
        this.a = i2;
        this.b = i3;
        this.c = i4;
        this.d = i5;
        this.e = j2;
        this.f = i6;
        this.g = i7;
        this.h = i8;
        this.i = j3;
        this.j = j4;
        this.k = j5;
        this.l = j6;
        this.m = j7;
        this.n = j8;
        this.o = i9;
        this.p = i10;
        this.q = i11;
        this.r = i12;
        this.s = z2;
        this.t = i13;
        this.u = j9;
        this.v = d2;
        this.w = d3;
        this.x = i14;
        this.y = f2;
        this.z = bArr;
        this.A = i15;
        this.B = i16;
        this.C = j10;
        this.D = j11;
        this.E = i17;
        this.F = i18;
        this.G = i19;
        this.H = i20;
        this.I = i21;
        this.f15J = i22;
        this.K = i23;
    }

    public final String toString() {
        bqi bqi;
        gxr gxr = new gxr();
        gxr.d("customerId", Integer.valueOf(this.a));
        gxr.d("featureType", Integer.valueOf(this.b));
        gxr.d("featureVariant", Integer.valueOf(this.c));
        gxr.d("status", Integer.valueOf(this.d));
        gxr.d("inferenceLatencyTotalMillis", Long.valueOf(this.e));
        gxr.d("numInputTokens", Integer.valueOf(this.f));
        gxr.d("numOutputTokens", Integer.valueOf(this.g));
        gxr.d("numDecodeSteps", Integer.valueOf(this.h));
        gxr.d("inferenceServiceStartLatencyMillis", Long.valueOf(this.i));
        gxr.d("inferenceApiCallHandlingLatencyMillis", Long.valueOf(this.j));
        gxr.d("inferenceInputSafetyCheckLatencyMillis", Long.valueOf(this.k));
        gxr.d("inferenceInputEncodingLatencyMillis", Long.valueOf(this.l));
        gxr.d("inferenceOverallOutputLatencyMillis", Long.valueOf(this.m));
        gxr.d("inferenceOutputSafetyCheckLatencyMillis", Long.valueOf(this.n));
        gxr.d("inputSafetyCheckPolicyViolationType", Integer.valueOf(this.o));
        gxr.d("inputSafetyCheckSuggestedActionType", Integer.valueOf(this.p));
        gxr.d("outputSafetyCheckPolicyViolationType", Integer.valueOf(this.q));
        gxr.d("outputSafetyCheckSuggestedActionType", Integer.valueOf(this.r));
        gxr.d("isModelLoaded", Boolean.valueOf(this.s));
        gxr.d("featureId", Integer.valueOf(this.t));
        gxr.d("modelInferenceLatencyMillis", Long.valueOf(this.u));
        gxr.d("outputTokensPerSecond", Double.valueOf(this.v));
        gxr.d("inputTokensPerSecond", Double.valueOf(this.w));
        gxr.d("numSamples", Integer.valueOf(this.x));
        gxr.d("cannedResponsesRatio", Float.valueOf(this.y));
        try {
            byte[] bArr = this.z;
            htq o2 = htq.o(bqi.e, bArr, 0, bArr.length, hte.a());
            htq.D(o2);
            bqi = (bqi) o2;
        } catch (hui e2) {
            Log.e("InferenceEventTraceResult", "Failed to parse SpeculativeDecodeStatistics proto", e2);
            bqi = bqi.e;
        }
        StringBuilder sb = new StringBuilder("{drafter_id: ");
        sb.append(bqi.a);
        sb.append(", drafter_time: ");
        htb htb = bqi.c;
        if (htb == null) {
            htb = htb.c;
        }
        sb.append(htb.a);
        sb.append(", acceptance_rate: ");
        sb.append(bqi.d);
        sb.append(", drafter_guesses_per_position: ");
        sb.append((String) Collection.EL.stream(bqi.b).map(new bpf(0)).collect(Collectors.joining(", ")));
        sb.append("}");
        gxr.d("speculativeDecodeStatistics", sb.toString());
        gxr.d("numSuffixScoreFiltered", Integer.valueOf(this.A));
        gxr.d("numPostDeduped", Integer.valueOf(this.B));
        gxr.d("inferenceStatefulSuspensionLatencyMillis", Long.valueOf(this.C));
        gxr.d("inferenceStatelessSuspensionLatencyMillis", Long.valueOf(this.D));
        gxr.d("topCannedResponseIndex", Integer.valueOf(this.E));
        gxr.d("inferenceStatefulSuspensionCount", Integer.valueOf(this.F));
        gxr.d("inferenceStatefulResumptionCount", Integer.valueOf(this.G));
        gxr.d("inferenceStatelessSuspensionCount", Integer.valueOf(this.H));
        gxr.d("inferenceStatelessResumptionCount", Integer.valueOf(this.I));
        gxr.d("requestKind", Integer.valueOf(this.f15J));
        gxr.d("initialQueuePosition", Integer.valueOf(this.K));
        return "InferenceEventTraceResult\n".concat(fvf.v(gxr.b()));
    }

    public final void writeToParcel(Parcel parcel, int i2) {
        int i3 = this.a;
        int i4 = cgr.i(parcel);
        cgr.o(parcel, 1, i3);
        cgr.o(parcel, 2, this.b);
        cgr.o(parcel, 3, this.c);
        cgr.o(parcel, 4, this.d);
        cgr.p(parcel, 5, this.e);
        cgr.o(parcel, 6, this.f);
        cgr.o(parcel, 7, this.g);
        cgr.o(parcel, 8, this.h);
        cgr.p(parcel, 9, this.i);
        cgr.p(parcel, 10, this.j);
        cgr.p(parcel, 11, this.k);
        cgr.p(parcel, 12, this.l);
        cgr.p(parcel, 13, this.m);
        cgr.p(parcel, 14, this.n);
        cgr.o(parcel, 15, this.o);
        cgr.o(parcel, 16, this.p);
        cgr.o(parcel, 17, this.q);
        cgr.o(parcel, 18, this.r);
        cgr.l(parcel, 19, this.s);
        cgr.o(parcel, 20, this.t);
        cgr.p(parcel, 21, this.u);
        cgr.m(parcel, 22, this.v);
        cgr.m(parcel, 23, this.w);
        cgr.o(parcel, 24, this.x);
        cgr.n(parcel, 25, 4);
        parcel.writeFloat(this.y);
        cgr.r(parcel, 26, this.z);
        cgr.o(parcel, 27, this.A);
        cgr.o(parcel, 28, this.B);
        cgr.p(parcel, 29, this.C);
        cgr.p(parcel, 30, this.D);
        cgr.o(parcel, 31, this.E);
        cgr.o(parcel, 32, this.F);
        cgr.o(parcel, 33, this.G);
        cgr.o(parcel, 34, this.H);
        cgr.o(parcel, 35, this.I);
        cgr.o(parcel, 36, this.f15J);
        cgr.o(parcel, 37, this.K);
        cgr.k(parcel, i4);
    }
}
