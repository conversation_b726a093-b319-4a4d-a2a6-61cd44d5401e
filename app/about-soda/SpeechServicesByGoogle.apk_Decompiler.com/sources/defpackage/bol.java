package defpackage;

import android.content.Context;
import android.util.AttributeSet;
import j$.util.Objects;
import java.util.Collections;
import java.util.List;

/* renamed from: bol  reason: default package */
/* compiled from: PG */
public final class bol extends bok {
    public List a = Collections.emptyList();
    public vl b;

    public bol(Context context, AttributeSet attributeSet) {
        super(context, attributeSet);
    }

    public final void a(bjj bjj) {
        this.a = Collections.singletonList((bjj) Objects.requireNonNull(bjj));
        if (this.b == null) {
            this.b = vl.a(this, new alr(this, 20));
        }
        setText(bjj.a());
    }

    /* JADX WARNING: Code restructure failed: missing block: B:20:0x0067, code lost:
        r2 = 0;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.CharSequence b(java.lang.CharSequence r15) {
        /*
            r14 = this;
            android.text.Layout r0 = r14.getLayout()
            java.lang.Object r0 = j$.util.Objects.requireNonNull(r0)
            android.text.Layout r0 = (android.text.Layout) r0
            int r0 = r0.getWidth()
            int r8 = r15.length()
            r9 = 0
            r1 = r9
            r10 = r1
            r11 = r10
            r12 = r11
        L_0x0017:
            if (r10 != 0) goto L_0x006d
            float r6 = (float) r0
            android.text.TextPaint r1 = r14.getPaint()
            r5 = 1
            r7 = 0
            r2 = r15
            r3 = r11
            r4 = r8
            int r1 = r1.breakText(r2, r3, r4, r5, r6, r7)
            int r1 = r1 + r11
            java.lang.String r2 = "\n"
            int r2 = android.text.TextUtils.indexOf(r15, r2, r11, r1)
            r3 = -1
            if (r2 == r3) goto L_0x0037
            int r2 = r2 + 1
            int r1 = java.lang.Math.min(r1, r2)
        L_0x0037:
            r2 = 1
            int r12 = r12 + r2
            r4 = 2147483647(0x7fffffff, float:NaN)
            if (r12 == r4) goto L_0x0042
            int r4 = r8 + -1
            if (r1 <= r4) goto L_0x0043
        L_0x0042:
            r10 = r2
        L_0x0043:
            if (r10 != 0) goto L_0x0069
            char r2 = r15.charAt(r1)
            boolean r2 = java.lang.Character.isWhitespace(r2)
            if (r2 != 0) goto L_0x0069
            if (r1 <= 0) goto L_0x0069
            r2 = r9
        L_0x0052:
            int r4 = r1 - r2
            int r4 = r4 + r3
            char r4 = r15.charAt(r4)
            boolean r4 = java.lang.Character.isWhitespace(r4)
            if (r4 != 0) goto L_0x0068
            int r2 = r2 + 1
            int r4 = r1 - r2
            if (r4 == r11) goto L_0x0067
            if (r2 < r1) goto L_0x0052
        L_0x0067:
            r2 = r9
        L_0x0068:
            int r1 = r1 - r2
        L_0x0069:
            r13 = r11
            r11 = r1
            r1 = r13
            goto L_0x0017
        L_0x006d:
            android.text.SpannableStringBuilder r10 = new android.text.SpannableStringBuilder
            r10.<init>()
            java.lang.CharSequence r2 = r15.subSequence(r9, r1)
            r10.append(r2)
            java.util.Scanner r2 = new java.util.Scanner
            java.lang.CharSequence r1 = r15.subSequence(r1, r8)
            java.lang.String r1 = r1.toString()
            r2.<init>(r1)
            boolean r1 = r2.hasNextLine()
            if (r1 == 0) goto L_0x00b1
            float r0 = (float) r0
            java.lang.String r1 = r2.nextLine()
            android.text.TextPaint r2 = r14.getPaint()
            android.text.TextUtils$TruncateAt r3 = android.text.TextUtils.TruncateAt.END
            java.lang.CharSequence r0 = android.text.TextUtils.ellipsize(r1, r2, r0, r3)
            r10.append(r0)
            boolean r0 = r15 instanceof android.text.Spanned
            if (r0 == 0) goto L_0x00b1
            r2 = r15
            android.text.Spanned r2 = (android.text.Spanned) r2
            int r4 = r10.length()
            java.lang.Class<java.lang.Object> r5 = java.lang.Object.class
            r7 = 0
            r3 = 0
            r6 = r10
            android.text.TextUtils.copySpansFrom(r2, r3, r4, r5, r6, r7)
        L_0x00b1:
            return r10
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bol.b(java.lang.CharSequence):java.lang.CharSequence");
    }
}
