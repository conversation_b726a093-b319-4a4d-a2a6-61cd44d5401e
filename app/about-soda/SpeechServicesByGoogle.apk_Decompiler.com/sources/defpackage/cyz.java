package defpackage;

import android.content.Context;

/* renamed from: cyz  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cyz implements hkn {
    public final /* synthetic */ Context a;
    public final /* synthetic */ String b;
    public final /* synthetic */ csv c;
    public final /* synthetic */ csx d;
    public final /* synthetic */ kjd e;

    public /* synthetic */ cyz(Context context, String str, kjd kjd, csv csv, csx csx) {
        this.a = context;
        this.b = str;
        this.e = kjd;
        this.c = csv;
        this.d = csx;
    }

    /* JADX WARNING: Removed duplicated region for block: B:14:0x009a  */
    /* JADX WARNING: Removed duplicated region for block: B:16:0x00a3  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme a() {
        /*
            r12 = this;
            java.lang.String r0 = ""
            java.lang.String r1 = "AndroidSharingUtil"
            android.content.Context r2 = r12.a
            java.lang.String r3 = r12.b
            kjd r4 = r12.e
            csv r5 = r12.c
            csx r6 = r12.d
            r7 = 3
            r8 = 2
            r9 = 1
            r10 = 0
            android.net.Uri r2 = defpackage.cqx.t(r2, r3)     // Catch:{ foa -> 0x0067, fny -> 0x0040, IOException -> 0x001c }
            boolean r1 = r4.j(r2)     // Catch:{ foa -> 0x0067, fny -> 0x0040, IOException -> 0x001c }
            goto L_0x0098
        L_0x001c:
            java.lang.String r0 = r5.b
            java.lang.String r2 = r6.c
            java.lang.Object[] r3 = new java.lang.Object[r7]
            r3[r10] = r1
            r3[r9] = r0
            r3[r8] = r2
            java.lang.String r0 = "%s: Failed to check existence in the shared storage for file %s, file group %s"
            defpackage.cyh.i(r0, r3)
            java.lang.String r0 = r5.b
            java.lang.String r1 = r6.c
            java.lang.Object[] r2 = new java.lang.Object[r8]
            r2[r10] = r0
            r2[r9] = r1
            java.lang.String r0 = "Error while checking if file %s, group %s, exists in the shared blob storage."
            java.lang.String r0 = java.lang.String.format(r0, r2)
            r1 = 19
            goto L_0x0063
        L_0x0040:
            java.lang.String r0 = r5.b
            java.lang.String r2 = r6.c
            java.lang.Object[] r3 = new java.lang.Object[r7]
            r3[r10] = r1
            r3[r9] = r0
            r3[r8] = r2
            java.lang.String r0 = "%s: Malformed lease uri file %s, file group %s"
            defpackage.cyh.i(r0, r3)
            java.lang.String r0 = r5.b
            java.lang.String r1 = r6.c
            java.lang.Object[] r2 = new java.lang.Object[r8]
            r2[r10] = r0
            r2[r9] = r1
            java.lang.String r0 = "Malformed blob Uri for file %s, group %s"
            java.lang.String r0 = java.lang.String.format(r0, r2)
            r1 = 17
        L_0x0063:
            r11 = r10
            r10 = r1
            r1 = r11
            goto L_0x0098
        L_0x0067:
            r2 = move-exception
            java.lang.String r3 = r2.getMessage()
            boolean r3 = android.text.TextUtils.isEmpty(r3)
            if (r3 == 0) goto L_0x0073
            goto L_0x0077
        L_0x0073:
            java.lang.String r0 = r2.getMessage()
        L_0x0077:
            java.lang.String r2 = r5.b
            java.lang.String r3 = r6.c
            r4 = 4
            java.lang.Object[] r4 = new java.lang.Object[r4]
            r4[r10] = r1
            r4[r9] = r2
            r4[r8] = r3
            r4[r7] = r0
            java.lang.String r1 = "%s: Failed to share for file %s, file group %s. UnsupportedFileStorageOperation was thrown with message \"%s\""
            defpackage.cyh.l(r1, r4)
            java.lang.String r1 = "UnsupportedFileStorageOperation was thrown: "
            java.lang.String r0 = java.lang.String.valueOf(r0)
            java.lang.String r0 = r1.concat(r0)
            r1 = 24
            goto L_0x0063
        L_0x0098:
            if (r10 != 0) goto L_0x00a3
            java.lang.Boolean r0 = java.lang.Boolean.valueOf(r1)
            hme r0 = defpackage.hfc.K(r0)
            return r0
        L_0x00a3:
            cza r1 = new cza
            r1.<init>(r10, r0)
            throw r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cyz.a():hme");
    }
}
