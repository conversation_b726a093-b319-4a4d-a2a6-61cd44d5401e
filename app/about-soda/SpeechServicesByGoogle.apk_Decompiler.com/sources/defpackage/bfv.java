package defpackage;

import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.os.Build;
import java.util.Objects;

/* renamed from: bfv  reason: default package */
/* compiled from: PG */
public final class bfv extends ConnectivityManager.NetworkCallback {
    final /* synthetic */ bfw a;

    public bfv(bfw bfw) {
        this.a = bfw;
    }

    public final void onCapabilitiesChanged(Network network, NetworkCapabilities networkCapabilities) {
        bex bex;
        jnu.e(network, "network");
        jnu.e(networkCapabilities, "capabilities");
        bbk.a();
        String str = bfx.a;
        Objects.toString(networkCapabilities);
        if (Build.VERSION.SDK_INT >= 28) {
            jnu.e(networkCapabilities, "<this>");
            bex = new bex(networkCapabilities.hasCapability(12), networkCapabilities.hasCapability(16), !networkCapabilities.hasCapability(11), networkCapabilities.hasCapability(18));
        } else {
            bex = bfx.a(this.a.e);
        }
        this.a.f(bex);
    }

    public final void onLost(Network network) {
        jnu.e(network, "network");
        bbk.a();
        String str = bfx.a;
        bfw bfw = this.a;
        bfw.f(bfx.a(bfw.e));
    }
}
