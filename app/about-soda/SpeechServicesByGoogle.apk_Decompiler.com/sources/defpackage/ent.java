package defpackage;

/* renamed from: ent  reason: default package */
/* compiled from: PG */
public final class ent implements enr {
    private final enr a;
    private final jjo b;

    public ent(enw enw, enr enr) {
        this.a = enr;
        this.b = new jjw(new mq(enw, 20));
    }

    private final enk c(enk enk) {
        if (enk instanceof eni) {
            return enk;
        }
        if (enk instanceof enj) {
            enk enk2 = (enk) this.b.a();
            if (enk2 instanceof eni) {
                return new eni(((eni) enk2).a + ((enj) enk).a);
            }
            if (enk2 instanceof enj) {
                enj enj = (enj) enk2;
                jnu.e(enj, "other");
                return new enj(((enj) enk).a + enj.a);
            }
            throw new jjq();
        }
        throw new jjq();
    }

    public final int a() {
        return this.a.a();
    }

    public final dyt b() {
        return ((eny) this.a).h;
    }

    public final eej d() {
        return ((eny) this.a).i;
    }

    public final ekt e() {
        return ((eny) this.a).c;
    }

    public final enr f() {
        return ((eny) this.a).d;
    }

    public final hme h(eam eam) {
        jnu.e(eam, "stopListeningReason");
        return this.a.h(eam);
    }

    public final hme i(eal eal, enk enk) {
        jnu.e(eal, "success");
        return this.a.i(eal, c(enk));
    }

    public final hme j() {
        return ((eny) this.a).f;
    }

    public final hme k(enk enk) {
        return this.a.k(c(enk));
    }
}
