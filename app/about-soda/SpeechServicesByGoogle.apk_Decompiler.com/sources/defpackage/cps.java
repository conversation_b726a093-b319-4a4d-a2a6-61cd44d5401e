package defpackage;

/* renamed from: cps  reason: default package */
/* compiled from: PG */
public final class cps implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;

    public cps(jjk jjk, jjk jjk2, jjk jjk3) {
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
    }

    /* renamed from: a */
    public final cpk b() {
        return new cpk((grh) ((iiv) this.a).a, ((bqs) this.b).a(), (hmi) this.c.b());
    }
}
