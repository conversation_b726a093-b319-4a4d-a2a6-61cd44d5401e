package defpackage;

import android.content.ContentProviderClient;
import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.PorterDuff;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Parcelable;
import android.util.AttributeSet;
import android.util.Base64;
import android.util.Log;
import android.util.SparseIntArray;
import android.view.MenuItem;
import android.view.SubMenu;
import android.view.View;
import android.view.ViewGroup;
import androidx.wear.ambient.AmbientMode;
import com.google.android.tts.R;
import j$.util.DesugarCollections;
import j$.util.Objects;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.atomic.AtomicReferenceFieldUpdater;

/* renamed from: a  reason: default package */
/* compiled from: PG */
public class a {
    public a() {
    }

    public static /* synthetic */ int A(int i) {
        if (i == 0) {
            return 1;
        }
        if (i != 1) {
            return 0;
        }
        return 2;
    }

    public static /* synthetic */ int B(int i) {
        return i - 1;
    }

    public static /* synthetic */ int C(int i) {
        if (i == 0) {
            return 6;
        }
        int i2 = 1;
        if (i != 1) {
            i2 = 2;
            if (i != 2) {
                i2 = 3;
                if (i != 3) {
                    i2 = 4;
                    if (i != 4) {
                        i2 = 5;
                        if (i != 5) {
                            return 0;
                        }
                    }
                }
            }
        }
        return i2;
    }

    public static /* synthetic */ int D(int i) {
        switch (i) {
            case 0:
                return 1;
            case 1:
                return 2;
            case 2:
                return 3;
            case 3:
                return 4;
            case 4:
                return 5;
            case 5:
                return 6;
            case 6:
                return 7;
            case 7:
                return 8;
            case 8:
                return 9;
            default:
                return 0;
        }
    }

    public static /* synthetic */ int E(int i) {
        return i - 2;
    }

    public static /* synthetic */ int F(int i) {
        switch (i) {
            case 0:
                return 1;
            case 1:
                return 2;
            case 2:
                return 3;
            case 3:
                return 4;
            case 4:
                return 5;
            case 5:
                return 6;
            case 6:
                return 7;
            case 7:
                return 8;
            default:
                return 0;
        }
    }

    public static /* synthetic */ int G(int i) {
        switch (i) {
            case 0:
                return 1;
            case 1:
                return 2;
            case 2:
                return 3;
            case 3:
                return 4;
            case 4:
                return 5;
            case 5:
                return 6;
            case 6:
                return 7;
            case 7:
                return 8;
            case 8:
                return 9;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return 10;
            default:
                return 0;
        }
    }

    public static /* synthetic */ int H(int i) {
        switch (i) {
            case 0:
                return 1;
            case 1:
                return 2;
            case 2:
                return 3;
            case 3:
                return 4;
            case 4:
                return 5;
            case 5:
                return 6;
            case 6:
                return 7;
            case 7:
                return 8;
            case 8:
                return 9;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return 10;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return 11;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return 12;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return 13;
            default:
                return 0;
        }
    }

    public static /* synthetic */ void I(int i) {
        if (i == 0) {
            throw null;
        }
    }

    public static /* synthetic */ int J(int i) {
        if (i == 0) {
            return 1;
        }
        if (i == 1) {
            return 2;
        }
        if (i == 2) {
            return 3;
        }
        if (i == 3) {
            return 4;
        }
        if (i == 4) {
            return 5;
        }
        if (i != 5) {
            return 0;
        }
        return 6;
    }

    public static /* synthetic */ boolean K(int i) {
        if (A(i) != 0) {
            return true;
        }
        return false;
    }

    public static /* synthetic */ boolean L(int i) {
        if (z(i) != 0) {
            return true;
        }
        return false;
    }

    public static /* synthetic */ boolean M(int i) {
        if (x(i) != 0) {
            return true;
        }
        return false;
    }

    public static /* synthetic */ boolean N(int i) {
        if (J(i) != 0) {
            return true;
        }
        return false;
    }

    public static /* synthetic */ boolean O(int i) {
        if (y(i) != 0) {
            return true;
        }
        return false;
    }

    public static /* synthetic */ void P(int i) {
        if (i == 0) {
            throw null;
        }
    }

    public static Object Q(Bundle bundle, String str, Class cls) {
        if (Build.VERSION.SDK_INT >= 34) {
            return bundle.getParcelable(str, cls);
        }
        Parcelable parcelable = bundle.getParcelable(str);
        if (cls.isInstance(parcelable)) {
            return parcelable;
        }
        return null;
    }

    public static cx R(int i) {
        if (i == 0) {
            return cx.VISIBLE;
        }
        if (i == 4) {
            return cx.INVISIBLE;
        }
        if (i == 8) {
            return cx.GONE;
        }
        throw new IllegalArgumentException(ak(i, "Unknown visibility "));
    }

    public static cx S(View view) {
        jnu.e(view, "<this>");
        if (view.getAlpha() == 0.0f && view.getVisibility() == 0) {
            return cx.INVISIBLE;
        }
        return R(view.getVisibility());
    }

    public static cz T(ViewGroup viewGroup, a aVar) {
        jnu.e(viewGroup, "container");
        jnu.e(aVar, "factory");
        Object tag = viewGroup.getTag(R.id.special_effects_controller_view_tag);
        if (tag instanceof cz) {
            return (cz) tag;
        }
        jnu.e(viewGroup, "container");
        cz czVar = new cz(viewGroup);
        viewGroup.setTag(R.id.special_effects_controller_view_tag, czVar);
        return czVar;
    }

    public static uz U(uu uuVar) {
        return uuVar.a();
    }

    public static /* synthetic */ String a(int i) {
        switch (i) {
            case 2:
                return "LEFT";
            case 3:
                return "TOP";
            case 4:
                return "RIGHT";
            case 5:
                return "BOTTOM";
            case 6:
                return "BASELINE";
            case 7:
                return "CENTER";
            case 8:
                return "CENTER_X";
            default:
                return "CENTER_Y";
        }
    }

    public static /* synthetic */ String aB(String str, String str2) {
        return str + str2;
    }

    private static int aD(Context context, int i) {
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(16973825, new int[]{i});
        int resourceId = obtainStyledAttributes.getResourceId(0, -1);
        obtainStyledAttributes.recycle();
        return resourceId;
    }

    public static void ab(int i) {
        if (i < 0) {
            throw new IllegalArgumentException();
        }
    }

    public static void ac(Object obj) {
        obj.getClass();
    }

    public static Object ad(ExecutorService executorService, Callable callable, int i) {
        try {
            return executorService.submit(callable).get((long) i, TimeUnit.MILLISECONDS);
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        } catch (InterruptedException e2) {
            throw e2;
        } catch (TimeoutException unused) {
            throw new InterruptedException("timeout");
        }
    }

    public static uh[] ae(List list) {
        return (uh[]) list.get(0);
    }

    public static /* synthetic */ List af(Object[] objArr) {
        ArrayList arrayList = new ArrayList(1);
        arrayList.add(Objects.requireNonNull(objArr[0]));
        return DesugarCollections.unmodifiableList(arrayList);
    }

    public static void ag(ContentProviderClient contentProviderClient) {
        if (contentProviderClient != null) {
            contentProviderClient.release();
        }
    }

    public static Handler ah(Looper looper) {
        if (Build.VERSION.SDK_INT >= 28) {
            return Handler.createAsync(looper);
        }
        Class<Handler> cls = Handler.class;
        try {
            return cls.getDeclaredConstructor(new Class[]{Looper.class, Handler.Callback.class, Boolean.TYPE}).newInstance(new Object[]{looper, null, true});
        } catch (IllegalAccessException | InstantiationException | NoSuchMethodException e) {
            Log.w("HandlerCompat", "Unable to invoke Handler(Looper, Callback, boolean) constructor", e);
            return new Handler(looper);
        } catch (InvocationTargetException e2) {
            Throwable cause = e2.getCause();
            if (cause instanceof RuntimeException) {
                throw ((RuntimeException) cause);
            } else if (cause instanceof Error) {
                throw ((Error) cause);
            } else {
                throw new RuntimeException(cause);
            }
        }
    }

    /* JADX WARNING: Missing exception handler attribute for start block: B:64:0x00cb */
    /* JADX WARNING: Removed duplicated region for block: B:23:0x0047 A[RETURN] */
    /* JADX WARNING: Removed duplicated region for block: B:24:0x0048  */
    /* JADX WARNING: Removed duplicated region for block: B:64:0x00cb A[SYNTHETIC, Splitter:B:64:0x00cb] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static defpackage.bxq ai(android.content.Context r7, defpackage.bc r8, boolean r9, boolean r10) {
        /*
            ay r0 = r8.S
            r1 = 0
            if (r0 != 0) goto L_0x0007
            r0 = r1
            goto L_0x0009
        L_0x0007:
            int r0 = r0.f
        L_0x0009:
            r2 = 1
            if (r10 == 0) goto L_0x0018
            if (r9 == 0) goto L_0x0013
            int r9 = r8.i()
            goto L_0x001e
        L_0x0013:
            int r9 = r8.j()
            goto L_0x0024
        L_0x0018:
            if (r9 == 0) goto L_0x0020
            int r9 = r8.g()
        L_0x001e:
            r10 = r2
            goto L_0x0025
        L_0x0020:
            int r9 = r8.h()
        L_0x0024:
            r10 = r1
        L_0x0025:
            r3 = r10
            r8.s(r1, r1, r1, r1)
            android.view.ViewGroup r4 = r8.O
            r5 = 0
            if (r4 == 0) goto L_0x003c
            r6 = 2131428010(0x7f0b02aa, float:1.8477652E38)
            java.lang.Object r4 = r4.getTag(r6)
            if (r4 == 0) goto L_0x003c
            android.view.ViewGroup r4 = r8.O
            r4.setTag(r6, r5)
        L_0x003c:
            android.view.ViewGroup r4 = r8.O
            if (r4 == 0) goto L_0x0048
            android.animation.LayoutTransition r4 = r4.getLayoutTransition()
            if (r4 != 0) goto L_0x0047
            goto L_0x0048
        L_0x0047:
            return r5
        L_0x0048:
            r8.onCreateAnimation(r0, r10, r9)
            r8.onCreateAnimator(r0, r10, r9)
            if (r9 != 0) goto L_0x00aa
            if (r0 == 0) goto L_0x00ab
            r8 = 4097(0x1001, float:5.741E-42)
            if (r0 == r8) goto L_0x00a0
            r8 = 8194(0x2002, float:1.1482E-41)
            if (r0 == r8) goto L_0x0096
            r8 = 8197(0x2005, float:1.1486E-41)
            if (r0 == r8) goto L_0x0084
            r8 = 4099(0x1003, float:5.744E-42)
            if (r0 == r8) goto L_0x007a
            r8 = 4100(0x1004, float:5.745E-42)
            if (r0 == r8) goto L_0x0068
            r1 = -1
            goto L_0x00ab
        L_0x0068:
            if (r10 == 0) goto L_0x0072
            r8 = 16842936(0x10100b8, float:2.3694074E-38)
            int r1 = aD(r7, r8)
            goto L_0x00ab
        L_0x0072:
            r8 = 16842937(0x10100b9, float:2.3694076E-38)
            int r1 = aD(r7, r8)
            goto L_0x00ab
        L_0x007a:
            if (r2 == r3) goto L_0x0080
            r1 = 2130837510(0x7f020006, float:1.7279976E38)
            goto L_0x00ab
        L_0x0080:
            r1 = 2130837509(0x7f020005, float:1.7279974E38)
            goto L_0x00ab
        L_0x0084:
            if (r10 == 0) goto L_0x008e
            r8 = 16842938(0x10100ba, float:2.369408E-38)
            int r1 = aD(r7, r8)
            goto L_0x00ab
        L_0x008e:
            r8 = 16842939(0x10100bb, float:2.3694082E-38)
            int r1 = aD(r7, r8)
            goto L_0x00ab
        L_0x0096:
            if (r2 == r3) goto L_0x009c
            r1 = 2130837508(0x7f020004, float:1.7279972E38)
            goto L_0x00ab
        L_0x009c:
            r1 = 2130837507(0x7f020003, float:1.727997E38)
            goto L_0x00ab
        L_0x00a0:
            if (r2 == r3) goto L_0x00a6
            r1 = 2130837512(0x7f020008, float:1.727998E38)
            goto L_0x00ab
        L_0x00a6:
            r1 = 2130837511(0x7f020007, float:1.7279978E38)
            goto L_0x00ab
        L_0x00aa:
            r1 = r9
        L_0x00ab:
            if (r1 == 0) goto L_0x00e7
            android.content.res.Resources r8 = r7.getResources()
            java.lang.String r8 = r8.getResourceTypeName(r1)
            java.lang.String r9 = "anim"
            boolean r8 = r9.equals(r8)
            if (r8 == 0) goto L_0x00cb
            android.view.animation.Animation r9 = android.view.animation.AnimationUtils.loadAnimation(r7, r1)     // Catch:{ NotFoundException -> 0x00c9, RuntimeException -> 0x00cb }
            if (r9 == 0) goto L_0x00e7
            bxq r10 = new bxq     // Catch:{ NotFoundException -> 0x00c9, RuntimeException -> 0x00cb }
            r10.<init>((android.view.animation.Animation) r9)     // Catch:{ NotFoundException -> 0x00c9, RuntimeException -> 0x00cb }
            return r10
        L_0x00c9:
            r7 = move-exception
            throw r7
        L_0x00cb:
            android.animation.Animator r9 = android.animation.AnimatorInflater.loadAnimator(r7, r1)     // Catch:{ RuntimeException -> 0x00d7 }
            if (r9 == 0) goto L_0x00e7
            bxq r10 = new bxq     // Catch:{ RuntimeException -> 0x00d7 }
            r10.<init>((android.animation.Animator) r9)     // Catch:{ RuntimeException -> 0x00d7 }
            return r10
        L_0x00d7:
            r9 = move-exception
            if (r8 != 0) goto L_0x00e6
            android.view.animation.Animation r7 = android.view.animation.AnimationUtils.loadAnimation(r7, r1)
            if (r7 == 0) goto L_0x00e7
            bxq r8 = new bxq
            r8.<init>((android.view.animation.Animation) r7)
            return r8
        L_0x00e6:
            throw r9
        L_0x00e7:
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.a.ai(android.content.Context, bc, boolean, boolean):bxq");
    }

    public static /* synthetic */ String ak(int i, String str) {
        return str + i;
    }

    public static /* synthetic */ String al(int i, String str, SparseIntArray sparseIntArray) {
        return str + Integer.toHexString(i) + "   " + sparseIntArray.get(i);
    }

    public static /* synthetic */ String am(int i, String str, String str2) {
        return str + i + str2;
    }

    public static /* synthetic */ String an(String str, String str2) {
        return str2 + str;
    }

    public static /* synthetic */ String ao(Object obj, String str, String str2) {
        return str + obj + str2;
    }

    public static /* synthetic */ String ap(String str, String str2, String str3) {
        return str2 + str + str3;
    }

    public static /* synthetic */ String aq(String str, AttributeSet attributeSet, String str2) {
        return attributeSet.getPositionDescription() + str2 + str;
    }

    public static /* synthetic */ String ar(Object obj, Object obj2, String str, String str2) {
        return str + obj2 + str2 + obj;
    }

    public static /* synthetic */ String as(String str, String str2, String str3) {
        return str2 + str3 + str;
    }

    public static /* synthetic */ String au(long j, String str) {
        return str + j;
    }

    public static /* synthetic */ String av(String str, String str2, String str3, String str4, String str5) {
        return str3 + str2 + str4 + str + str5;
    }

    public static /* synthetic */ String aw(int i, int i2, String str, String str2) {
        return str + i2 + str2 + i;
    }

    public static /* synthetic */ String ay(Object obj, Class cls, String str) {
        String obj2 = cls.toString();
        String valueOf = String.valueOf(obj.getClass());
        return str + obj2 + ", but the wrapper available is of type: " + valueOf + ". Does your peer's @Inject constructor reference the wrong wrapper class?";
    }

    public static /* synthetic */ String az(bf bfVar, Class cls) {
        String obj = cls.toString();
        String valueOf = String.valueOf(bfVar.getClass());
        return "Attempt to inject a Activity wrapper of type " + obj + ", but the wrapper available is of type: " + valueOf + ". Does your peer's @Inject constructor reference the wrong wrapper class?";
    }

    public static /* synthetic */ void b() {
        throw new UnsupportedOperationException("Operation is not supported for read-only collection");
    }

    public static /* synthetic */ boolean c() {
        throw new UnsupportedOperationException("Operation is not supported for read-only collection");
    }

    public static /* synthetic */ boolean d() {
        throw new UnsupportedOperationException("Operation is not supported for read-only collection");
    }

    public static /* synthetic */ PorterDuff.Mode e(int i, PorterDuff.Mode mode) {
        if (i == 3) {
            return PorterDuff.Mode.SRC_OVER;
        }
        if (i == 5) {
            return PorterDuff.Mode.SRC_IN;
        }
        if (i == 9) {
            return PorterDuff.Mode.SRC_ATOP;
        }
        switch (i) {
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return PorterDuff.Mode.MULTIPLY;
            case 15:
                return PorterDuff.Mode.SCREEN;
            case 16:
                return PorterDuff.Mode.ADD;
            default:
                return mode;
        }
    }

    public static /* synthetic */ int f(boolean z) {
        if (z) {
            return 1231;
        }
        return 1237;
    }

    public static /* synthetic */ boolean g(AtomicReferenceFieldUpdater atomicReferenceFieldUpdater, Object obj, Object obj2, Object obj3) {
        while (!atomicReferenceFieldUpdater.compareAndSet(obj, obj2, obj3)) {
            if (atomicReferenceFieldUpdater.get(obj) != obj2) {
                return false;
            }
        }
        return true;
    }

    public static /* synthetic */ Object h(Future future) {
        Object obj;
        boolean z = false;
        while (true) {
            try {
                obj = future.get();
                break;
            } catch (InterruptedException unused) {
                z = true;
            } catch (Throwable th) {
                if (z) {
                    Thread.currentThread().interrupt();
                }
                throw th;
            }
        }
        if (z) {
            Thread.currentThread().interrupt();
        }
        return obj;
    }

    public static /* synthetic */ boolean i(AtomicReference atomicReference, Object obj) {
        while (!atomicReference.compareAndSet((Object) null, obj)) {
            if (atomicReference.get() != null) {
                return false;
            }
        }
        return true;
    }

    public static /* synthetic */ void j(boolean z) {
        if (z) {
            throw new IllegalArgumentException("Peered fragments cannot be retained, to avoid memory leaks. If you need a retained fragment, you should subclass Fragment directly. See http://go/tiktok-conformance-violations/FRAGMENT_SET_RETAIN_INSTANCE");
        }
    }

    public static /* synthetic */ boolean k(Object obj, Object obj2) {
        if (obj == obj2) {
            return true;
        }
        if (obj == null) {
            return false;
        }
        if (obj.equals(obj2)) {
            return true;
        }
        return false;
    }

    public static /* synthetic */ boolean l(AtomicReference atomicReference, Object obj, Object obj2) {
        while (!atomicReference.compareAndSet(obj, obj2)) {
            if (atomicReference.get() != obj) {
                return false;
            }
        }
        return true;
    }

    public static /* synthetic */ Set m(grh grh, jjk jjk) {
        if (grh.f()) {
            return new hbi((dmf) jjk.b());
        }
        return hau.a;
    }

    public static /* synthetic */ hme n(jna jna, Object obj) {
        return (hme) jna.a(obj);
    }

    public static /* synthetic */ hxr o(int i) {
        hxr b = hxr.b(i);
        if (b == null) {
            return hxr.FEATURE_UNSPECIFIED;
        }
        return b;
    }

    public static /* synthetic */ hva s(grh grh) {
        try {
            byte[] decode = Base64.decode("CAU", 3);
            htb htb = htb.c;
            int length = decode.length;
            hte hte = hte.a;
            hvj hvj = hvj.a;
            htq o = htq.o(htb, decode, 0, length, hte.a);
            htq.D(o);
            return (hva) grh.d((htb) o);
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e2) {
            throw new RuntimeException(e2);
        }
    }

    public static /* synthetic */ hva t(grh grh) {
        try {
            byte[] decode = Base64.decode("CICjBQ", 3);
            htb htb = htb.c;
            int length = decode.length;
            hte hte = hte.a;
            hvj hvj = hvj.a;
            htq o = htq.o(htb, decode, 0, length, hte.a);
            htq.D(o);
            return (hva) grh.d((htb) o);
        } catch (RuntimeException e) {
            throw e;
        } catch (Exception e2) {
            throw new RuntimeException(e2);
        }
    }

    public static /* synthetic */ void v(Object obj, String str) {
        if (obj == null) {
            throw new NullPointerException(str);
        }
    }

    public static /* synthetic */ void w(Object obj, Object obj2) {
        if (obj == null) {
            throw new NullPointerException((String) obj2);
        }
    }

    public static /* synthetic */ int x(int i) {
        if (i == 0) {
            return 1;
        }
        if (i == 1) {
            return 2;
        }
        if (i != 2) {
            return 0;
        }
        return 3;
    }

    public static /* synthetic */ int y(int i) {
        if (i == 0) {
            return 1;
        }
        if (i == 1) {
            return 2;
        }
        if (i == 2) {
            return 3;
        }
        if (i == 3) {
            return 4;
        }
        if (i != 4) {
            return 0;
        }
        return 5;
    }

    public static /* synthetic */ int z(int i) {
        if (i == 0) {
            return 1;
        }
        if (i == 1) {
            return 2;
        }
        if (i == 2) {
            return 3;
        }
        if (i != 3) {
            return 0;
        }
        return 4;
    }

    public View V(MenuItem menuItem) {
        throw null;
    }

    public void W(SubMenu subMenu) {
        throw null;
    }

    public boolean X() {
        throw null;
    }

    public boolean Y() {
        throw null;
    }

    public boolean Z() {
        throw null;
    }

    public boolean aa() {
        throw null;
    }

    public void aj(AmbientMode.AmbientController ambientController) {
        throw null;
    }

    public a(char[] cArr) {
    }
}
