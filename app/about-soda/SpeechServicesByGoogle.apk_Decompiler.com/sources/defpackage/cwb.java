package defpackage;

import android.net.Uri;
import java.io.IOException;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.Executor;

/* renamed from: cwb  reason: default package */
/* compiled from: PG */
public final class cwb implements cvz {
    public final cwo a;
    public final Executor b;
    private final cxb c;
    private final cwo d;
    private final Uri e;
    private final Uri f;
    private final cyk g;
    private final cxi h;
    private final kjd i;

    public cwb(cyk cyk, cxb cxb, cwo cwo, cwo cwo2, Uri uri, Uri uri2, cxi cxi, kjd kjd, Executor executor) {
        this.g = cyk;
        this.c = cxb;
        this.d = cwo2;
        this.a = cwo;
        this.e = uri;
        this.f = uri2;
        this.h = cxi;
        this.i = kjd;
        this.b = executor;
    }

    private static hme q() {
        return hfc.J(new IllegalStateException("Migration flag had unexpected state"));
    }

    private final void r(Uri uri) {
        if (this.i.j(uri)) {
            this.i.h(uri);
        }
    }

    public final hme a(csx csx) {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.c.a(csx);
        }
        if (ordinal == 2) {
            return ftd.L(n(this.c.a(csx)), new cvn((Object) this, (htq) csx, 10), this.b);
        }
        if (ordinal != 3) {
            return q();
        }
        return this.d.a(csx);
    }

    public final hme b() {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.c.b();
        }
        if (ordinal == 2) {
            return ftd.L(n(this.c.b()), new cvi(this, 14), this.b);
        }
        if (ordinal != 3) {
            return q();
        }
        return this.d.b();
    }

    public final hme c() {
        aom aom = new aom(17);
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.c.c();
        }
        if (ordinal == 2) {
            return ftd.L(n(this.c.c()), new cvn((Object) this, (Object) aom, 11), this.b);
        }
        if (ordinal != 3) {
            return q();
        }
        return this.d.c();
    }

    public final hme d() {
        aom aom = new aom(16);
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.c.d();
        }
        if (ordinal == 2) {
            return ftd.L(n(this.c.d()), new cvn((Object) this, (Object) aom, 15), this.b);
        }
        if (ordinal != 3) {
            return q();
        }
        return this.d.d();
    }

    public final hme e() {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.c.e();
        }
        if (ordinal == 2) {
            return ftd.L(n(this.c.e()), new cvi(this, 13), this.b);
        }
        if (ordinal != 3) {
            return q();
        }
        return this.d.e();
    }

    public final hme f() {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            try {
                r(this.e);
                r(this.f);
                return hma.a;
            } catch (IOException e2) {
                return hfc.J(e2);
            } catch (Throwable th) {
                r(this.f);
                throw th;
            }
        } else if (ordinal == 2) {
            try {
                r(this.f);
                return hma.a;
            } catch (IOException e3) {
                return hfc.J(e3);
            }
        } else if (ordinal != 3) {
            return q();
        } else {
            try {
                r(this.e);
                return hma.a;
            } catch (IOException e4) {
                return hfc.J(e4);
            }
        }
    }

    public final hme g(ctg ctg) {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.c.g(ctg);
        }
        if (ordinal == 2) {
            return ftd.L(n(this.c.g(ctg)), new cwa((Object) this, (htq) ctg, 6), this.b);
        }
        if (ordinal != 3) {
            return q();
        }
        return this.d.g(ctg);
    }

    public final hme h(ctg ctg) {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.c.h(ctg);
        }
        if (ordinal == 2) {
            return ftd.L(n(this.c.h(ctg)), new cvn((Object) this, (htq) ctg, 18), this.b);
        }
        if (ordinal != 3) {
            return q();
        }
        return this.d.h(ctg);
    }

    public final hme i(ctg ctg) {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.c.i(ctg);
        }
        if (ordinal == 2) {
            return ftd.L(n(this.c.i(ctg)), new cvn((Object) this, (htq) ctg, 16), this.b);
        }
        if (ordinal != 3) {
            return q();
        }
        return this.d.i(ctg);
    }

    public final hme j(List list) {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.c.j(list);
        }
        if (ordinal == 2) {
            return ftd.L(n(this.c.j(list)), new cwa((Object) this, (Object) list, 3), this.b);
        }
        if (ordinal != 3) {
            return q();
        }
        return this.d.j(list);
    }

    public final hme k() {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.c.k();
        }
        if (ordinal == 2) {
            return ftd.L(n(this.c.k()), new cvi(this, 15), this.b);
        }
        if (ordinal != 3) {
            return q();
        }
        return this.d.k();
    }

    public final hme l(ctg ctg, csx csx) {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.c.l(ctg, csx);
        }
        if (ordinal == 2) {
            return ftd.L(n(this.c.l(ctg, csx)), new cvp((Object) this, ctg, csx, 14), this.b);
        }
        if (ordinal != 3) {
            return q();
        }
        return this.d.l(ctg, csx);
    }

    public final hme m(List list) {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.c.m(list);
        }
        if (ordinal == 2) {
            return ftd.L(n(this.c.m(list)), new cvn((Object) this, (Object) list, 12), this.b);
        }
        if (ordinal != 3) {
            return q();
        }
        return this.d.m(list);
    }

    public final hme n(hme hme) {
        return ftd.F(ftd.K(hme, new amv(15), this.b), Exception.class, new amv(16), this.b);
    }

    public final hme o(cze cze, cze cze2, int i2) {
        int q = cqh.q();
        if (cyh.b((long) q)) {
            if (cze.equals(cze2)) {
                this.g.c(1107, q);
            } else {
                this.g.c(i2, q);
            }
        }
        if (cze.a) {
            return hfc.K(cze.a());
        }
        return hfc.J((Throwable) cze.b());
    }

    public final hme p(cze cze, cze cze2, Comparator comparator, int i2) {
        int q = cqh.q();
        if (cyh.b((long) q)) {
            if (cze.d(cze, cze2, comparator)) {
                this.g.c(1106, q);
            } else {
                this.g.c(i2, q);
            }
        }
        if (cze.a) {
            List list = (List) cze.a();
            fvf.aP(list);
            return hfc.K(list);
        }
        Object b2 = cze.b();
        fvf.aP(b2);
        return hfc.J((Throwable) b2);
    }
}
