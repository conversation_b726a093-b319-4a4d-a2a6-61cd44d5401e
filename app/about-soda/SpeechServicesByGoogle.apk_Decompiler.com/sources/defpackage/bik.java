package defpackage;

import java.util.Set;

/* renamed from: bik  reason: default package */
/* compiled from: PG */
public final class bik implements Runnable {
    private final bcp a;
    private final boolean b;
    private final int c;
    private final byw d;

    public bik(bcp bcp, byw byw, boolean z, int i) {
        jnu.e(bcp, "processor");
        jnu.e(byw, "token");
        this.a = bcp;
        this.d = byw;
        this.b = z;
        this.c = i;
    }

    public final void run() {
        eez d2;
        if (this.b) {
            bcp bcp = this.a;
            byw byw = this.d;
            int i = this.c;
            String str = ((bgt) byw.a).a;
            synchronized (bcp.j) {
                d2 = bcp.d(str);
            }
            bcp.f(d2, i);
        } else {
            bcp bcp2 = this.a;
            byw byw2 = this.d;
            int i2 = this.c;
            String str2 = ((bgt) byw2.a).a;
            synchronized (bcp2.j) {
                if (bcp2.e.get(str2) != null) {
                    bbk.a();
                } else {
                    Set set = (Set) bcp2.g.get(str2);
                    if (set != null) {
                        if (set.contains(byw2)) {
                            eez d3 = bcp2.d(str2);
                            bcp.f(d3, i2);
                        }
                    }
                }
            }
        }
        bbk.a();
        bbk.b("StopWorkRunnable");
        Object obj = this.d.a;
    }
}
