package defpackage;

/* renamed from: cuc  reason: default package */
/* compiled from: PG */
public final class cuc implements csl {
    final /* synthetic */ cuv a;
    public final /* synthetic */ csk b;
    public final /* synthetic */ sk c;
    public final /* synthetic */ sp d;
    public final /* synthetic */ int e;
    public final /* synthetic */ String f;
    final /* synthetic */ cuf g;

    public cuc(cuf cuf, cuv cuv, csk csk, sk skVar, sp spVar, int i, String str) {
        this.a = cuv;
        this.b = csk;
        this.c = skVar;
        this.d = spVar;
        this.e = i;
        this.f = str;
        this.g = cuf;
    }

    public final void a(crw crw) {
        ftd.J(new ctp(this, crw, 7), this.g.d);
    }

    public final void b(Throwable th) {
        ftd.J(new ctp(this, th, 8, (byte[]) null), this.g.d);
    }

    public final void c(long j) {
        ftd.L(this.g.i.g(this.a.a), new fxo(this, j, 1), this.g.d);
    }

    public final void d() {
        ftd.L(this.g.i.f(this.a.a), new bpr(this, 13), this.g.d);
    }
}
