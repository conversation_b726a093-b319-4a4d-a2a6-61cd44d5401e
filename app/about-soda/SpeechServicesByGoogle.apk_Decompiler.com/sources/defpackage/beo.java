package defpackage;

import android.content.ComponentName;
import android.content.Context;
import androidx.work.impl.background.systemjob.SystemJobService;

/* renamed from: beo  reason: default package */
/* compiled from: PG */
final class beo {
    public final ComponentName a;
    public final boolean b;

    static {
        bbk.b("SystemJobInfoConverter");
    }

    public beo(Context context, boolean z) {
        this.a = new ComponentName(context.getApplicationContext(), SystemJobService.class);
        this.b = z;
    }
}
