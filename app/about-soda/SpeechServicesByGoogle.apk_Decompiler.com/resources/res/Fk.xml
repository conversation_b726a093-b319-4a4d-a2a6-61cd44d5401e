<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:layout_width="match_parent" android:layout_height="wrap_content">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center_horizontal" android:id="@+id/transcription_voice_ime_recognizer" android:background="@color/transcription_voice_ime_color_bg" android:layout_width="match_parent" android:layout_height="wrap_content" android:keepScreenOn="true">
        <ImageView android:id="@+id/transcription_prev_ime" android:background="?android:attr/selectableItemBackgroundBorderless" android:paddingTop="@dimen/transcription_intent_api_material_small_margin" android:paddingBottom="@dimen/transcription_intent_api_material_large_margin" android:focusable="true" android:clickable="true" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/quantum_gm_ic_arrow_back_black_24" android:scaleType="center" android:tint="@color/agsa_color_on_surface" android:contentDescription="@string/transcription_app_voice_ime_hint_prev_ime" android:paddingStart="@dimen/transcription_intent_api_material_small_margin" android:paddingEnd="@dimen/transcription_intent_api_material_large_margin" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
        <TextView android:textSize="@dimen/transcription_intent_api_material_state_text_size" android:textColor="@color/agsa_color_on_surface" android:ellipsize="end" android:gravity="center" android:id="@+id/transcription_voice_ime_text" android:paddingTop="@dimen/transcription_intent_api_material_small_margin" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:minLines="1" android:importantForAccessibility="no" android:paddingStart="@dimen/transcription_voice_ime_text_padding_start" android:paddingEnd="@dimen/transcription_voice_ime_text_padding_end" app:fontFamily="@font/google_sans" app:layout_constraintBottom_toTopOf="@+id/transcription_voice_ime_error_text" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
        <TextView android:textSize="@dimen/transcription_voice_ime_error_text_size" android:textColor="@color/agsa_color_on_surface" android:ellipsize="end" android:gravity="center" android:id="@+id/transcription_voice_ime_error_text" android:padding="@dimen/transcription_intent_api_material_small_margin" android:visibility="visible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:maxLines="1" android:minLines="1" android:importantForAccessibility="no" app:fontFamily="@font/google_sans" app:layout_constraintBottom_toTopOf="@+id/transcription_voice_ime_mic_container" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/transcription_voice_ime_text"/>
        <ImageView android:id="@+id/transcription_voice_ime_settings_button" android:background="?android:attr/selectableItemBackgroundBorderless" android:padding="@dimen/transcription_intent_api_material_large_margin" android:focusable="true" android:clickable="true" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/quantum_gm_ic_settings_black_24" android:tint="@color/agsa_color_on_surface" android:contentDescription="@string/transcription_app_voice_ime_hint_settings" app:layout_constraintBottom_toTopOf="@+id/transcription_voice_ime_google_logo" app:layout_constraintEnd_toStartOf="@+id/transcription_voice_ime_mic_container" app:layout_constraintHorizontal_chainStyle="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/transcription_voice_ime_error_text"/>
        <FrameLayout android:id="@+id/transcription_voice_ime_mic_container" android:layout_width="@dimen/transcription_intent_api_material_mic_area_size" android:layout_height="@dimen/transcription_intent_api_material_mic_area_size" app:layout_constraintBottom_toTopOf="@+id/transcription_voice_ime_google_logo" app:layout_constraintEnd_toStartOf="@+id/transcription_voice_ime_undo_button" app:layout_constraintHorizontal_chainStyle="0" app:layout_constraintStart_toEndOf="@+id/transcription_voice_ime_settings_button" app:layout_constraintTop_toBottomOf="@+id/transcription_voice_ime_error_text">
            <include layout="@layout/transcription_intent_api_material_mic_indicator"/>
        </FrameLayout>
        <com.google.android.libraries.speech.transcription.voiceime.BackspaceView android:id="@+id/transcription_voice_ime_undo_button" android:background="?android:attr/selectableItemBackgroundBorderless" android:padding="@dimen/transcription_intent_api_material_large_margin" android:focusable="true" android:clickable="true" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/quantum_gm_ic_backspace_black_24" android:tint="@color/agsa_color_on_surface" android:contentDescription="@string/transcription_app_voice_ime_hint_remove_last_word" app:layout_constraintBottom_toTopOf="@+id/transcription_voice_ime_google_logo" app:layout_constraintEnd_toEndOf="0" app:layout_constraintHorizontal_chainStyle="0" app:layout_constraintStart_toEndOf="@+id/transcription_voice_ime_mic_container" app:layout_constraintTop_toBottomOf="@+id/transcription_voice_ime_error_text"/>
        <ImageView android:id="@+id/transcription_voice_ime_google_logo" android:paddingTop="@dimen/transcription_voice_ime_google_logo_padding_top" android:paddingBottom="@dimen/transcription_voice_ime_google_logo_padding_bottom" android:focusable="false" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/googlelogo_dark_color_84x28" android:scaleType="center" android:tint="@color/transcription_intent_api_google_logo_tint" android:contentDescription="@string/transcription_app_common_google_logo" android:importantForAccessibility="no" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/transcription_voice_ime_mic_container"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>
