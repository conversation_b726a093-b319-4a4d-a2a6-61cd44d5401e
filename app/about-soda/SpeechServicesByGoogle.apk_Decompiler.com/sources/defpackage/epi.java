package defpackage;

/* renamed from: epi  reason: default package */
/* compiled from: PG */
public final class epi {
    public final enr a;
    public final epp b;
    public final epl c;

    public epi(enr enr, epp epp, epl epl) {
        this.a = enr;
        this.b = epp;
        this.c = epl;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof epi)) {
            return false;
        }
        epi epi = (epi) obj;
        if (jnu.i(this.a, epi.a) && jnu.i(this.b, epi.b) && jnu.i(this.c, epi.c)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (((this.a.hashCode() * 31) + this.b.hashCode()) * 31) + this.c.hashCode();
    }

    public final String toString() {
        return "HotwordSourceData(sourceAccessor=" + this.a + ", route=" + this.b + ", session=" + this.c + ")";
    }
}
