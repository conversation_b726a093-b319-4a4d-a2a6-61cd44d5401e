package defpackage;

import java.util.List;

/* renamed from: bhf  reason: default package */
/* compiled from: PG */
public interface bhf {
    bbx a(String str);

    bhe b(String str);

    List c();

    List d();

    List e(String str);

    void f(String str);

    void g(String str, int i);

    void h(String str, long j);

    void i(String str, bat bat);

    void j(String str, int i);

    List k();

    void l(String str, long j);

    void m(bbx bbx, String str);
}
