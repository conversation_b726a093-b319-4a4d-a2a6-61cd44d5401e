<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt">
    <android.support.v7.widget.RecyclerView android:id="@+id/car_ui_recycler_view" android:tag="RecyclerView" android:paddingLeft="112dp" android:scrollbars="vertical" android:layout_width="match_parent" android:layout_height="match_parent"/>
    <androidx.constraintlayout.widget.ConstraintLayout android:gravity="center" android:layout_gravity="left" android:id="@+id/car_ui_scroll_bar" android:layout_width="112dp" android:layout_height="match_parent">
        <ImageView android:id="@+id/car_ui_scrollbar_page_up" android:background="@drawable/car_ui_recyclerview_button_ripple_background_private" android:focusable="false" android:layout_width="76dp" android:layout_height="76dp" android:layout_marginTop="15dp" android:src="@drawable/car_ui_recyclerview_ic_up_private" android:scaleType="centerInside" android:hapticFeedbackEnabled="false" android:contentDescription="Scroll up" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintRight_toRightOf="0" app:layout_constraintTop_toTopOf="0"/>
        <View android:layout_gravity="center_horizontal" android:id="@+id/car_ui_scrollbar_thumb" android:background="@drawable/car_ui_recyclerview_scrollbar_thumb_private" android:layout_width="7dp" android:layout_height="0dp" app:layout_constraintBottom_toBottomOf="@+id/car_ui_scrollbar_track" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintRight_toRightOf="0" app:layout_constraintTop_toTopOf="@+id/car_ui_scrollbar_track"/>
        <View android:id="@+id/car_ui_scrollbar_track" android:layout_width="0dp" android:layout_height="0dp" android:layout_marginTop="16dp" android:layout_marginBottom="16dp" app:layout_constraintBottom_toTopOf="@+id/car_ui_scrollbar_page_down" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/car_ui_scrollbar_page_up"/>
        <ImageView android:id="@+id/car_ui_scrollbar_page_down" android:background="@drawable/car_ui_recyclerview_button_ripple_background_private" android:focusable="false" android:layout_width="76dp" android:layout_height="76dp" android:layout_marginBottom="15dp" android:src="@drawable/car_ui_recyclerview_ic_down_private" android:scaleType="centerInside" android:hapticFeedbackEnabled="false" android:contentDescription="Scroll down" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintRight_toRightOf="0"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</merge>
