package defpackage;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;

/* renamed from: cwo  reason: default package */
/* compiled from: PG */
public final class cwo implements cvz {
    public final cuk a;
    public final Executor b;
    public final fps c;

    public cwo(cuk cuk, fps fps, Executor executor) {
        this.a = cuk;
        this.c = fps;
        this.b = executor;
    }

    public final hme a(csx csx) {
        cyh.d("%s: Adding file group %s", "ProtoDataStoreFileGroupsMetadata", csx.c);
        csx j = cqx.j(csx, (cqx.c() / 1000) + csx.j);
        ArrayList arrayList = new ArrayList();
        arrayList.add(j);
        return m(arrayList);
    }

    public final hme b() {
        return czw.e(k()).g(new cwi(this, 11), this.b);
    }

    public final hme c() {
        ArrayList arrayList = new ArrayList();
        return czw.e(this.c.b(new cwn(arrayList, 8), this.b)).f(new cwn(arrayList, 2), this.b);
    }

    public final hme d() {
        ArrayList arrayList = new ArrayList();
        return czw.e(this.c.b(new btb(this, arrayList, 7), this.b)).f(new cwn(arrayList, 5), this.b);
    }

    public final hme e() {
        return ftd.K(this.c.a(), new cwh(7), this.b);
    }

    public final hme f() {
        return hma.a;
    }

    public final hme g(ctg ctg) {
        return ftd.K(this.c.a(), new cwn(cqx.f(ctg), 6), this.b);
    }

    public final hme h(ctg ctg) {
        return ftd.K(this.c.a(), new cwn(cqx.f(ctg), 3), this.b);
    }

    public final hme i(ctg ctg) {
        return czw.e(this.c.b(new cwn(cqx.f(ctg), 7), this.b)).f(new cwh(14), this.b).b(IOException.class, new cwh(15), this.b);
    }

    public final hme j(List list) {
        return czw.e(this.c.b(new cwn(list, 4), this.b)).f(new cwh(5), this.b).b(IOException.class, new cwh(6), this.b);
    }

    public final hme k() {
        return this.c.b(new cwh(8), this.b);
    }

    public final hme l(ctg ctg, csx csx) {
        return czw.e(this.c.b(new btb(cqx.f(ctg), csx, 8, (byte[]) null), this.b)).f(new cwh(10), this.b).b(IOException.class, new cwh(11), this.b);
    }

    public final hme m(List list) {
        return czw.e(this.c.b(new cwn(list, 0), this.b)).f(new cwh(9), this.b).b(IOException.class, new cwh(12), this.b);
    }
}
