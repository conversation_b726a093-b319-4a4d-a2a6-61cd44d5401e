package defpackage;

/* renamed from: eom  reason: default package */
/* compiled from: PG */
public final class eom extends jme {
    public /* synthetic */ Object a;
    final /* synthetic */ eon b;
    public int c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eom(eon eon, jlr jlr) {
        super(jlr);
        this.b = eon;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.c |= Integer.MIN_VALUE;
        return cqx.T(this.b, this);
    }
}
