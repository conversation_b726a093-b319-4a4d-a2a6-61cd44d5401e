package defpackage;

/* renamed from: bpx  reason: default package */
/* compiled from: PG */
public final class bpx {
    public final Object a;
    public final awh b;

    public bpx() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof bpx) {
            bpx bpx = (bpx) obj;
            if (!this.a.equals(bpx.a) || !this.b.equals(bpx.b)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return ((this.a.hashCode() ^ 1000003) * 1000003) ^ this.b.hashCode();
    }

    public final String toString() {
        awh awh = this.b;
        String obj = this.a.toString();
        String obj2 = awh.toString();
        return "InferenceServiceContext{getService=" + obj + ", disconnectSignal=" + obj2 + "}";
    }

    public bpx(Object obj, awh awh) {
        if (obj != null) {
            this.a = obj;
            this.b = awh;
            return;
        }
        throw new NullPointerException("Null getService");
    }
}
