package defpackage;

import android.net.Uri;
import java.io.IOException;
import java.io.OutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

/* renamed from: cxy  reason: default package */
/* compiled from: PG */
public final class cxy implements fno {
    private final Uri a;

    public cxy(Uri uri) {
        this.a = uri;
    }

    public final /* bridge */ /* synthetic */ Object a(fnn fnn) {
        OutputStream outputStream;
        Object obj = fnn.f;
        try {
            foq foq = new foq();
            foq.a = true;
            ZipInputStream zipInputStream = new ZipInputStream(foq.a(fnn));
            while (true) {
                try {
                    ZipEntry nextEntry = zipInputStream.getNextEntry();
                    if (nextEntry != null) {
                        Uri build = this.a.buildUpon().appendPath(fbi.j(nextEntry)).build();
                        if (nextEntry.isDirectory()) {
                            ((kjd) obj).f(build);
                        } else {
                            outputStream = (OutputStream) ((kjd) obj).e(build, new fou());
                            hgy.a(zipInputStream, outputStream);
                            if (outputStream != null) {
                                outputStream.close();
                            }
                        }
                    } else {
                        zipInputStream.close();
                        return null;
                    }
                } catch (Throwable th) {
                    zipInputStream.close();
                    throw th;
                }
            }
            throw th;
        } catch (IOException e) {
            ((kjd) obj).l(this.a);
            throw e;
        } catch (Throwable th2) {
            th.addSuppressed(th2);
        }
    }
}
