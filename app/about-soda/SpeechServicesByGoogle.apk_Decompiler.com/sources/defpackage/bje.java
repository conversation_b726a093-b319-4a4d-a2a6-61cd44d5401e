package defpackage;

/* renamed from: bje  reason: default package */
/* compiled from: PG */
public final class bje {
    public static final String a = bbk.b("ConstraintTrkngWrkr");

    /* JADX WARNING: Removed duplicated region for block: B:12:0x002f  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static final java.lang.Object a(defpackage.byw r4, defpackage.bhe r5, defpackage.jlr r6) {
        /*
            boolean r0 = r6 instanceof defpackage.bjc
            if (r0 == 0) goto L_0x0013
            r0 = r6
            bjc r0 = (defpackage.bjc) r0
            int r1 = r0.b
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.b = r1
            goto L_0x0018
        L_0x0013:
            bjc r0 = new bjc
            r0.<init>(r6)
        L_0x0018:
            java.lang.Object r6 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.b
            r3 = 1
            if (r2 == 0) goto L_0x002f
            if (r2 != r3) goto L_0x0027
            defpackage.jji.c(r6)
            goto L_0x0051
        L_0x0027:
            java.lang.IllegalStateException r4 = new java.lang.IllegalStateException
            java.lang.String r5 = "call to 'resume' before 'invoke' with coroutine"
            r4.<init>(r5)
            throw r4
        L_0x002f:
            defpackage.jji.c(r6)
            juo r4 = r4.U(r5)
            bjd r6 = new bjd
            r2 = 0
            r6.<init>(r5, r2)
            juy r5 = new juy
            r2 = 2
            r5.<init>(r4, r6, r2)
            bjb r4 = new bjb
            r6 = 0
            r4.<init>(r5, r6)
            r0.b = r3
            java.lang.Object r6 = defpackage.jqw.B(r4, r0)
            if (r6 != r1) goto L_0x0051
            return r1
        L_0x0051:
            ber r6 = (defpackage.ber) r6
            int r4 = r6.a
            java.lang.Integer r5 = new java.lang.Integer
            r5.<init>(r4)
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bje.a(byw, bhe, jlr):java.lang.Object");
    }
}
