package defpackage;

import android.app.Activity;
import android.view.View;
import com.android.car.ui.baselayout.Insets;

/* renamed from: bjx  reason: default package */
/* compiled from: PG */
public final class bjx implements bjw {
    public bjw a;
    public Insets b = new Insets();
    private final Activity c;
    private final View d;

    public bjx(Activity activity, View view) {
        this.c = activity;
        this.d = view;
    }

    public final void a(Insets insets) {
        boolean z;
        if (!this.b.equals(insets)) {
            this.b = insets;
            bjw bjw = this.a;
            if (bjw != null) {
                bjw.a(insets);
                return;
            }
            Activity activity = this.c;
            if (activity instanceof bjw) {
                ((bjw) activity).a(insets);
                z = true;
            } else {
                z = false;
            }
            Activity activity2 = this.c;
            if (activity2 instanceof bf) {
                for (bc bcVar : ((bf) activity2).bb().j()) {
                    if (bcVar instanceof bjw) {
                        ((bjw) bcVar).a(insets);
                        z = true;
                    }
                }
            }
            if (!z) {
                this.d.setPadding(insets.getLeft(), insets.getTop(), insets.getRight(), insets.getBottom());
            }
        }
    }
}
