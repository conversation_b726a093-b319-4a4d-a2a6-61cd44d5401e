package defpackage;

import java.util.ArrayList;
import java.util.List;

/* renamed from: eul  reason: default package */
/* compiled from: PG */
public final class eul implements eui {
    final /* synthetic */ eum a;
    private final eui b;
    private final euh c;
    private final List d;

    public eul(eum eum, ebd ebd) {
        eun eun;
        jnu.e(ebd, "metadata");
        this.a = eum;
        gnk gnk = elf.a;
        jnu.d(gnk, "storingDestination");
        ele b2 = ele.b(((eld) ftc.aR(ebd, gnk)).b);
        int ordinal = (b2 == null ? ele.MODE_DEFAULT_MEMORY : b2).ordinal();
        euh euh = null;
        if (ordinal == 0 || ordinal == 1) {
            euo euo = eum.b;
            jnu.e(ebd, "metadata");
            eun = new eun(euo, ebd, euo.e.n());
        } else if (ordinal == 2) {
            eun = null;
        } else {
            throw new jjq();
        }
        this.b = eun;
        if (eum.c.b) {
            gnk gnk2 = elf.a;
            jnu.d(gnk2, "storingDestination");
            ele b3 = ele.b(((eld) ftc.aR(ebd, gnk2)).b);
            int ordinal2 = (b3 == null ? ele.MODE_DEFAULT_MEMORY : b3).ordinal();
            if (ordinal2 != 0) {
                if (ordinal2 == 1 || ordinal2 == 2) {
                    euv euv = eum.c;
                    jnu.e(ebd, "metadata");
                    euh = euv.b ? new euu(euv, ebd) : euv.a;
                } else {
                    throw new jjq();
                }
            }
        }
        this.c = euh;
        this.d = jji.o(eun, euh);
    }

    private static final hme d(List list) {
        ArrayList arrayList = new ArrayList();
        for (Object next : list) {
            if (next != null) {
                arrayList.add(next);
            }
        }
        ArrayList arrayList2 = new ArrayList();
        for (Object next2 : arrayList) {
            if (!((hme) next2).isCancelled()) {
                arrayList2.add(next2);
            }
        }
        if (arrayList2.isEmpty()) {
            return hfc.I();
        }
        return ftd.aa(arrayList2).A(new euk(0), hld.a);
    }

    public final hme a() {
        hme hme;
        List<euh> list = this.d;
        ArrayList arrayList = new ArrayList(jji.K(list));
        for (euh euh : list) {
            if (euh != null) {
                hme = euh.a();
            } else {
                hme = null;
            }
            arrayList.add(hme);
        }
        return d(arrayList);
    }

    public final hme b(dyc dyc) {
        hme hme;
        jnu.e(dyc, "audioData");
        List<euh> list = this.d;
        ArrayList arrayList = new ArrayList(jji.K(list));
        for (euh euh : list) {
            if (euh != null) {
                hme = euh.b(dyc);
            } else {
                hme = null;
            }
            arrayList.add(hme);
        }
        return d(arrayList);
    }

    public final hme c(hsq hsq) {
        hme hme;
        jnu.e(hsq, "bytes");
        List<euh> list = this.d;
        ArrayList arrayList = new ArrayList(jji.K(list));
        for (euh euh : list) {
            if (euh != null) {
                hme = euh.c(hsq);
            } else {
                hme = null;
            }
            arrayList.add(hme);
        }
        return d(arrayList);
    }
}
