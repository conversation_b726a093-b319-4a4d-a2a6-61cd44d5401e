package defpackage;

/* renamed from: cqz  reason: default package */
/* compiled from: PG */
public final class cqz {
    public static final /* synthetic */ int a = 0;

    static {
        int[] a2 = hws.a();
        int i = 0;
        int i2 = 0;
        int i3 = 0;
        while (i2 < 82) {
            int i4 = a2[i2];
            int i5 = i4 - 1;
            if (i4 != 0) {
                i3 = Math.max(i3, i5);
                i2++;
            } else {
                throw null;
            }
        }
        itt[] ittArr = new itt[(i3 + 1)];
        int[] a3 = hws.a();
        while (i < 82) {
            int i6 = a3[i];
            int i7 = i6 - 1;
            if (i6 != 0) {
                if (!(i7 == 17 || i7 == 27 || i7 == 78 || i7 == 82)) {
                    switch (i7) {
                        case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                        case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                        case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                        case 15:
                            break;
                        default:
                            switch (i7) {
                                case 19:
                                case 20:
                                case 21:
                                    break;
                                default:
                                    switch (i7) {
                                        case 31:
                                        case 32:
                                        case 33:
                                        case 34:
                                        case 35:
                                        case 36:
                                        case 37:
                                        case 38:
                                        case 39:
                                        case 40:
                                        case 41:
                                        case 42:
                                        case 43:
                                        case 44:
                                        case 45:
                                        case 46:
                                        case 47:
                                        case 48:
                                        case 49:
                                            break;
                                        default:
                                            switch (i7) {
                                                case 56:
                                                case 57:
                                                case 58:
                                                case 59:
                                                case 60:
                                                case 61:
                                                case 62:
                                                case 63:
                                                case 64:
                                                case 65:
                                                case 66:
                                                case 67:
                                                case 68:
                                                case 69:
                                                case 70:
                                                case 71:
                                                case 72:
                                                case 73:
                                                    break;
                                                default:
                                                    continue;
                                                    continue;
                                                    continue;
                                            }
                                    }
                            }
                    }
                }
                itt itt = new itt();
                ittArr[i7] = itt;
                int i8 = gxq.d;
                itt.a = hal.a;
                i++;
            } else {
                throw null;
            }
        }
    }
}
