package defpackage;

import android.os.Binder;
import android.os.Build;
import androidx.preference.Preference;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/* renamed from: cny  reason: default package */
/* compiled from: PG */
public final class cny extends ThreadPoolExecutor {
    public cny(TimeUnit timeUnit, BlockingQueue blockingQueue, ThreadFactory threadFactory) {
        super(0, Preference.DEFAULT_ORDER, 60, timeUnit, blockingQueue, threadFactory);
    }

    /* access modifiers changed from: protected */
    public final void afterExecute(Runnable runnable, Throwable th) {
        super.afterExecute(runnable, th);
        if (Build.VERSION.SDK_INT < 31) {
            Binder.flushPendingCommands();
        }
    }
}
