package defpackage;

/* renamed from: eml  reason: default package */
/* compiled from: PG */
final class eml extends jme {
    /* synthetic */ Object a;
    final /* synthetic */ emr b;
    int c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eml(emr emr, jlr jlr) {
        super(jlr);
        this.b = emr;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.c |= Integer.MIN_VALUE;
        return this.b.a((eej) null, this);
    }
}
