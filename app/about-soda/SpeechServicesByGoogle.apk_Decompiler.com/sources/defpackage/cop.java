package defpackage;

import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

/* renamed from: cop  reason: default package */
/* compiled from: PG */
public abstract class cop extends hlr implements hmi {
    protected cop() {
    }

    /* renamed from: b */
    public final hmg schedule(Runnable runnable, long j, TimeUnit timeUnit) {
        return h().b(runnable, j, timeUnit);
    }

    /* renamed from: c */
    public final hmg schedule(Callable callable, long j, TimeUnit timeUnit) {
        return h().c(callable, j, timeUnit);
    }

    /* renamed from: d */
    public final hmg scheduleAtFixedRate(Runnable runnable, long j, long j2, TimeUnit timeUnit) {
        return h().d(runnable, j, j2, timeUnit);
    }

    /* renamed from: e */
    public final hmg scheduleWithFixedDelay(Runnable runnable, long j, long j2, TimeUnit timeUnit) {
        return h().e(runnable, j, j2, timeUnit);
    }

    /* access modifiers changed from: protected */
    public /* bridge */ /* synthetic */ hmh f() {
        throw null;
    }

    /* access modifiers changed from: protected */
    public abstract hmi h();
}
