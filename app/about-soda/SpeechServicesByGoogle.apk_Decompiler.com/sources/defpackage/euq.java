package defpackage;

import android.util.Log;
import java.io.RandomAccessFile;

/* renamed from: euq  reason: default package */
/* compiled from: PG */
public final class euq extends jnv implements jna {
    public static final euq a = new euq(0);
    public static final euq b = new euq(1);
    public static final euq c = new euq(2);
    public static final euq d = new euq(3);
    public static final euq e = new euq(4);
    public static final euq f = new euq(5);
    public static final euq g = new euq(6);
    public static final euq h = new euq(7);
    public static final euq i = new euq(8);
    public static final euq j = new euq(9);
    public static final euq k = new euq(10);
    public static final euq l = new euq(11);
    public static final euq m = new euq(12);
    public static final euq n = new euq(13);
    public static final euq o = new euq(14);
    public static final euq p = new euq(15);
    public static final euq q = new euq(16);
    public static final euq r = new euq(17);
    private final /* synthetic */ int s;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public euq(int i2) {
        super(1);
        this.s = i2;
    }

    public final /* synthetic */ Object a(Object obj) {
        boolean z = true;
        switch (this.s) {
            case 0:
                RandomAccessFile randomAccessFile = (RandomAccessFile) obj;
                jnu.e(randomAccessFile, "$this$runOpsSafely");
                randomAccessFile.getFD().sync();
                randomAccessFile.close();
                return jkd.a;
            case 1:
                dyc dyc = (dyc) obj;
                jnu.e(dyc, "it");
                htk l2 = etf.c.l();
                jnu.d(l2, "newBuilder(...)");
                dlv t = jnu.e(l2, "builder");
                jnu.e(dyc, "value");
                htk htk = (htk) t.a;
                if (!htk.b.B()) {
                    htk.u();
                }
                etf etf = (etf) htk.b;
                dyc.getClass();
                etf.b = dyc;
                etf.a = 10;
                return t.n();
            case 2:
                RandomAccessFile randomAccessFile2 = (RandomAccessFile) obj;
                jnu.e(randomAccessFile2, "$this$runOpsSafely");
                randomAccessFile2.getFD().sync();
                randomAccessFile2.close();
                return jkd.a;
            case 3:
                gxq gxq = (gxq) obj;
                jnu.b(gxq);
                return fvf.Y(gxq);
            case 4:
                exo exo = (exo) obj;
                jnu.e(exo, "it");
                return hfc.K(exo);
            case 5:
                jnu.e((Exception) obj, "it");
                return null;
            case 6:
                String str = (String) obj;
                jnu.e(str, "it");
                return str;
            case 7:
                ccx ccx = (ccx) obj;
                jnu.e(ccx, "e");
                return Integer.valueOf(Log.w("ConfigurationUpdater", "Failed to update shared storage snapshot. GMS Core version too old.", ccx));
            case 8:
                dtv dtv = (dtv) obj;
                jnu.e(dtv, "e");
                if (dtv.a == 29501) {
                    return null;
                }
                throw dtv;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                hnh hnh = (hnh) obj;
                jnu.e(hnh, "it");
                return hnh.f;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                ccx ccx2 = (ccx) obj;
                jnu.e(ccx2, "e");
                return Integer.valueOf(Log.w("RegisterInternal", "GMS Core version too old", ccx2));
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                jnu.e((Exception) obj, "it");
                return false;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                Void voidR = (Void) obj;
                return hfc.K(true);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                if (obj != null) {
                    z = false;
                }
                return Boolean.valueOf(z);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                String str2 = (String) obj;
                jnu.e(str2, "line");
                return str2;
            case 15:
                jlt jlt = (jlt) obj;
                if (jlt instanceof jqp) {
                    return (jqp) jlt;
                }
                return null;
            case 16:
                jlt jlt2 = (jlt) obj;
                if (jlt2 instanceof jrr) {
                    return (jrr) jlt2;
                }
                return null;
            default:
                Throwable th = (Throwable) obj;
                return null;
        }
    }
}
