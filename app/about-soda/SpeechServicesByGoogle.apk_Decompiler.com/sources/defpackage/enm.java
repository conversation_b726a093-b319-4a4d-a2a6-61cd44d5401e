package defpackage;

/* renamed from: enm  reason: default package */
/* compiled from: PG */
final class enm extends jmi implements jne {
    final /* synthetic */ eal a;
    final /* synthetic */ Object b;
    final /* synthetic */ Object c;
    private final /* synthetic */ int d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public enm(eng eng, enk enk, eal eal, jlr jlr, int i) {
        super(2, jlr);
        this.d = i;
        this.c = eng;
        this.b = enk;
        this.a = eal;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        if (this.d != 0) {
            return ((enm) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
        return ((enm) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX WARNING: type inference failed for: r5v8, types: [enk, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v17, types: [enk, java.lang.Object] */
    public final Object bk(Object obj) {
        if (this.d != 0) {
            jji.c(obj);
            eng eng = (eng) this.c;
            ((hby) eng.a.f().h(hdg.a, "ALT.AdapterSrcAccessor").j("com/google/android/libraries/search/audio/microphone/impl/audiosourcedataaccessor/AudioAdapterAudioSourceDataAccessor$copyStarted$1", "invokeSuspend", 238, "AudioAdapterAudioSourceDataAccessor.kt")).G("#audio# copying %s session(%d) source, offset(%s)", eng.o(), new Integer(eng.c), this.b);
            ((eng) this.c).p(eaj.FAILED_OPENING_NO_SOURCE_TO_COPY);
            ? r5 = this.b;
            return ((eng) this.c).c(this.a, r5);
        }
        jji.c(obj);
        ((hby) eno.a.f().h(hdg.a, "ALT.SrcAccessor").j("com/google/android/libraries/search/audio/microphone/impl/audiosourcedataaccessor/AudioSourceDataAccessorImpl$copyStarted$1", "invokeSuspend", 175, "AudioSourceDataAccessorImpl.kt")).C("#audio# copying audio source(%s), offset(%s)", ((eno) this.b).h, this.c);
        ((eno) this.b).l(eaj.FAILED_OPENING_NO_SOURCE_TO_COPY);
        ? r52 = this.c;
        return ((eno) this.b).c(this.a, r52);
    }

    /* JADX WARNING: type inference failed for: r2v0, types: [enk, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v1, types: [enk, java.lang.Object] */
    public final jlr c(Object obj, jlr jlr) {
        if (this.d != 0) {
            return new enm((eng) this.c, (enk) this.b, this.a, jlr, 1);
        }
        return new enm((eno) this.b, (enk) this.c, this.a, jlr, 0);
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public enm(eno eno, enk enk, eal eal, jlr jlr, int i) {
        super(2, jlr);
        this.d = i;
        this.b = eno;
        this.c = enk;
        this.a = eal;
    }
}
