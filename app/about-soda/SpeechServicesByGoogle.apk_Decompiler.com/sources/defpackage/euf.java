package defpackage;

/* renamed from: euf  reason: default package */
/* compiled from: PG */
public final class euf implements hls {
    final /* synthetic */ long a;
    final /* synthetic */ ehg b;
    final /* synthetic */ eug c;
    private final /* synthetic */ int d;

    public euf(eug eug, long j, ehg ehg, int i) {
        this.d = i;
        this.a = j;
        this.b = ehg;
        this.c = eug;
    }

    public final void a(Throwable th) {
        int i = this.d;
        if (i == 0) {
            ((hby) ((hby) eug.a.h().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$4", "onFailure", 352, "AudioEventsHolderImpl.java")).t("#audio# cannot retrieve hotword client session(token(%d)) start listening status", this.a);
            htk l = ebo.c.l();
            htk l2 = eak.c.l();
            eaj eaj = eaj.FAILED_OPENING_ERROR_RETRIEVING_STATUS;
            if (!l2.b.B()) {
                l2.u();
            }
            eak eak = (eak) l2.b;
            eak.b = Integer.valueOf(eaj.ag);
            eak.a = 2;
            eak eak2 = (eak) l2.r();
            if (!l.b.B()) {
                l.u();
            }
            ebo ebo = (ebo) l.b;
            eak2.getClass();
            ebo.b = eak2;
            ebo.a |= 1;
            ebo ebo2 = (ebo) l.r();
            htk l3 = eax.h.l();
            if (!l3.b.B()) {
                l3.u();
            }
            htq htq = l3.b;
            eax eax = (eax) htq;
            ebo2.getClass();
            eax.c = ebo2;
            eax.b = 100;
            long j = this.a;
            if (!htq.B()) {
                l3.u();
            }
            htq htq2 = l3.b;
            eax eax2 = (eax) htq2;
            eax2.a = 1 | eax2.a;
            eax2.d = j;
            ehg ehg = this.b;
            if (ehf.a(ehg.a) != ehf.DETAILS_NOT_SET) {
                if (!htq2.B()) {
                    l3.u();
                }
                eax eax3 = (eax) l3.b;
                ehg.getClass();
                eax3.f = ehg;
                eax3.a |= 4;
            }
            this.c.f((eax) l3.r());
        } else if (i != 1) {
            htk l4 = eaw.h.l();
            htk l5 = eba.c.l();
            if (!l5.b.B()) {
                l5.u();
            }
            eba eba = (eba) l5.b;
            eba.b = 2;
            eba.a |= 1;
            eba eba2 = (eba) l5.r();
            if (!l4.b.B()) {
                l4.u();
            }
            htq htq3 = l4.b;
            eaw eaw = (eaw) htq3;
            eba2.getClass();
            eaw.c = eba2;
            eaw.b = 2;
            ehg ehg2 = this.b;
            if (ehf.a(ehg2.a) != ehf.DETAILS_NOT_SET) {
                if (!htq3.B()) {
                    l4.u();
                }
                eaw eaw2 = (eaw) l4.b;
                ehg2.getClass();
                eaw2.d = ehg2;
                eaw2.a |= 1;
            }
            this.c.e((eaw) l4.r());
        } else {
            ((hby) ((hby) eug.a.h().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$3", "onFailure", 297, "AudioEventsHolderImpl.java")).t("#audio# cannot retrieve hotword client session(token(%d)) stop listening status", this.a);
            htk l6 = ebp.d.l();
            htk l7 = eah.c.l();
            eag eag = eag.FAILED_CLOSING_ERROR_RETRIEVING_STATUS;
            if (!l7.b.B()) {
                l7.u();
            }
            eah eah = (eah) l7.b;
            eah.b = Integer.valueOf(eag.L);
            eah.a = 2;
            eah eah2 = (eah) l7.r();
            if (!l6.b.B()) {
                l6.u();
            }
            ebp ebp = (ebp) l6.b;
            eah2.getClass();
            ebp.b = eah2;
            ebp.a |= 1;
            ebp ebp2 = (ebp) l6.r();
            htk l8 = eax.h.l();
            if (!l8.b.B()) {
                l8.u();
            }
            htq htq4 = l8.b;
            eax eax4 = (eax) htq4;
            ebp2.getClass();
            eax4.c = ebp2;
            eax4.b = 101;
            long j2 = this.a;
            if (!htq4.B()) {
                l8.u();
            }
            htq htq5 = l8.b;
            eax eax5 = (eax) htq5;
            eax5.a = 1 | eax5.a;
            eax5.d = j2;
            ehg ehg3 = this.b;
            if (ehf.a(ehg3.a) != ehf.DETAILS_NOT_SET) {
                if (!htq5.B()) {
                    l8.u();
                }
                eax eax6 = (eax) l8.b;
                ehg3.getClass();
                eax6.f = ehg3;
                eax6.a |= 4;
            }
            this.c.f((eax) l8.r());
        }
    }

    public final /* synthetic */ void b(Object obj) {
        String str;
        eal eal;
        eaj eaj;
        String str2;
        eai eai;
        eag eag;
        int i = this.d;
        if (i == 0) {
            ebj ebj = (ebj) obj;
            Object f = ebj.f();
            ebo ebo = (ebo) f;
            eak eak = ebo.b;
            if (eak == null) {
                eak = eak.c;
            }
            if (eak.a == 2) {
                eak eak2 = ebo.b;
                if (eak2 == null) {
                    eak2 = eak.c;
                }
                if (eak2.a == 2) {
                    eaj = eaj.b(((Integer) eak2.b).intValue());
                    if (eaj == null) {
                        eaj = eaj.UNKNOWN_OPENING_FAILURE;
                    }
                } else {
                    eaj = eaj.UNKNOWN_OPENING_FAILURE;
                }
                str = eaj.name();
            } else {
                eak eak3 = ebo.b;
                if (eak3 == null) {
                    eak3 = eak.c;
                }
                if (eak3.a == 1) {
                    eal = eal.b(((Integer) eak3.b).intValue());
                    if (eal == null) {
                        eal = eal.UNKNOWN_OPENING_SUCCESS;
                    }
                } else {
                    eal = eal.UNKNOWN_OPENING_SUCCESS;
                }
                str = eal.name();
            }
            ((hby) ((hby) eug.a.f().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$4", "onSuccess", 336, "AudioEventsHolderImpl.java")).z("#audio# hotword client session(token(%d)) start listening status(%s)", this.a, str);
            htk l = eax.h.l();
            if (!l.b.B()) {
                l.u();
            }
            htq htq = l.b;
            eax eax = (eax) htq;
            f.getClass();
            eax.c = f;
            eax.b = 100;
            long j = this.a;
            if (!htq.B()) {
                l.u();
            }
            htq htq2 = l.b;
            eax eax2 = (eax) htq2;
            eax2.a = 1 | eax2.a;
            eax2.d = j;
            ehg ehg = this.b;
            if (ehf.a(ehg.a) != ehf.DETAILS_NOT_SET) {
                if (!htq2.B()) {
                    l.u();
                }
                eax eax3 = (eax) l.b;
                ehg.getClass();
                eax3.f = ehg;
                eax3.a |= 4;
            }
            this.c.f((eax) l.r());
            eug eug = this.c;
            long j2 = this.a;
            ehg ehg2 = this.b;
            if (ebj.c().f()) {
                hfc.T((hme) ebj.c().b(), gof.g(new euf(eug, j2, ehg2, 2)), eug.b);
            }
        } else if (i != 1) {
            dyv dyv = (dyv) obj;
            htk l2 = eaw.h.l();
            htk l3 = eba.c.l();
            if (!l3.b.B()) {
                l3.u();
            }
            eba eba = (eba) l3.b;
            eba.b = 1;
            eba.a |= 1;
            eba eba2 = (eba) l3.r();
            if (!l2.b.B()) {
                l2.u();
            }
            htq htq3 = l2.b;
            eaw eaw = (eaw) htq3;
            eba2.getClass();
            eaw.c = eba2;
            eaw.b = 2;
            long j3 = this.a;
            if (!htq3.B()) {
                l2.u();
            }
            htq htq4 = l2.b;
            eaw eaw2 = (eaw) htq4;
            eaw2.a = 2 | eaw2.a;
            eaw2.e = j3;
            ehg ehg3 = this.b;
            if (ehf.a(ehg3.a) != ehf.DETAILS_NOT_SET) {
                if (!htq4.B()) {
                    l2.u();
                }
                eaw eaw3 = (eaw) l2.b;
                ehg3.getClass();
                eaw3.d = ehg3;
                eaw3.a |= 1;
            }
            this.c.e((eaw) l2.r());
        } else {
            ebp ebp = (ebp) obj;
            eah eah = ebp.b;
            if (eah == null) {
                eah = eah.c;
            }
            if (eah.a == 2) {
                eah eah2 = ebp.b;
                if (eah2 == null) {
                    eah2 = eah.c;
                }
                if (eah2.a == 2) {
                    eag = eag.b(((Integer) eah2.b).intValue());
                    if (eag == null) {
                        eag = eag.UNKNOWN_CLOSING_FAILURE;
                    }
                } else {
                    eag = eag.UNKNOWN_CLOSING_FAILURE;
                }
                str2 = eag.name();
            } else {
                eah eah3 = ebp.b;
                if (eah3 == null) {
                    eah3 = eah.c;
                }
                if (eah3.a == 1) {
                    eai = eai.b(((Integer) eah3.b).intValue());
                    if (eai == null) {
                        eai = eai.UNKNOWN_CLOSING_SUCCESS;
                    }
                } else {
                    eai = eai.UNKNOWN_CLOSING_SUCCESS;
                }
                str2 = eai.name();
            }
            ((hby) ((hby) eug.a.f().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$3", "onSuccess", 282, "AudioEventsHolderImpl.java")).z("#audio# hotword client session(token(%d)) stop listening status(%s)", this.a, str2);
            htk l4 = eax.h.l();
            if (!l4.b.B()) {
                l4.u();
            }
            htq htq5 = l4.b;
            eax eax4 = (eax) htq5;
            ebp.getClass();
            eax4.c = ebp;
            eax4.b = 101;
            long j4 = this.a;
            if (!htq5.B()) {
                l4.u();
            }
            htq htq6 = l4.b;
            eax eax5 = (eax) htq6;
            eax5.a |= 1;
            eax5.d = j4;
            ehg ehg4 = this.b;
            if (ehf.a(ehg4.a) != ehf.DETAILS_NOT_SET) {
                if (!htq6.B()) {
                    l4.u();
                }
                eax eax6 = (eax) l4.b;
                ehg4.getClass();
                eax6.f = ehg4;
                eax6.a |= 4;
            }
            this.c.f((eax) l4.r());
        }
    }
}
