package defpackage;

import android.car.Car;
import android.car.drivingstate.CarUxRestrictions;
import android.car.drivingstate.CarUxRestrictionsManager;
import android.content.Context;
import android.os.Build;
import android.os.Handler;
import android.util.Log;
import java.util.Collections;
import java.util.Set;
import java.util.WeakHashMap;

/* renamed from: bnz  reason: default package */
/* compiled from: PG */
public final class bnz {
    private static bnz d;
    public CarUxRestrictions a = a();
    public final Set b = Collections.newSetFromMap(new WeakHashMap());
    public final CarUxRestrictionsManager.OnUxRestrictionsChangedListener c;

    private bnz(Context context) {
        bnw bnw = new bnw(this);
        this.c = bnw;
        try {
            if (Build.VERSION.SDK_INT >= 30) {
                Car.createCar(context, (Handler) null, 0, new bnx(this));
            } else {
                d(Car.createCar(context), bnw);
            }
        } catch (RuntimeException e) {
            Log.w("CarUxRestrictionsUtil", "Unable to connect to car service, assuming unrestricted", e);
            this.c.onUxRestrictionsChanged(new CarUxRestrictions.Builder(false, 0, 0).build());
        }
    }

    public static CarUxRestrictions a() {
        return new CarUxRestrictions.Builder(true, 511, 0).build();
    }

    public static bnz b(Context context) {
        if (d == null) {
            d = new bnz(context);
        }
        return d;
    }

    public static void d(Car car, CarUxRestrictionsManager.OnUxRestrictionsChangedListener onUxRestrictionsChangedListener) {
        try {
            CarUxRestrictionsManager carUxRestrictionsManager = (CarUxRestrictionsManager) car.getCarManager("uxrestriction");
            carUxRestrictionsManager.registerListener(onUxRestrictionsChangedListener);
            onUxRestrictionsChangedListener.onUxRestrictionsChanged(carUxRestrictionsManager.getCurrentCarUxRestrictions());
        } catch (NullPointerException e) {
            Log.e("CarUxRestrictionsUtil", "Car not connected", e);
        }
    }

    public final void c(bny bny) {
        this.b.add(bny);
        bny.a(this.a);
    }

    public final void e(bny bny) {
        this.b.remove(bny);
    }
}
