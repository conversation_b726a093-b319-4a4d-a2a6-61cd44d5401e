package defpackage;

/* renamed from: ctr  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ctr implements hkn {
    public final /* synthetic */ cuf a;
    public final /* synthetic */ hme b;
    public final /* synthetic */ hme c;
    public final /* synthetic */ ctg d;
    public final /* synthetic */ boolean e;
    public final /* synthetic */ csk f;
    public final /* synthetic */ String g;

    public /* synthetic */ ctr(cuf cuf, hme hme, hme hme2, ctg ctg, boolean z, csk csk, String str) {
        this.a = cuf;
        this.b = hme;
        this.c = hme2;
        this.d = ctg;
        this.e = z;
        this.f = csk;
        this.g = str;
    }

    public final hme a() {
        hme hme = this.b;
        if (((grh) hfc.S(hme)).f()) {
            return hfc.K(new cuz((hme) ((grh) hfc.S(hme)).b()));
        }
        hme hme2 = this.c;
        if (((grh) hfc.S(hme2)).f()) {
            return hfc.K(new cuz((hme) ((grh) hfc.S(hme2)).b()));
        }
        String str = this.g;
        csk csk = this.f;
        boolean z = this.e;
        ctg ctg = this.d;
        cuf cuf = this.a;
        return ftd.L(ftd.L(cuf.c.e(ctg, false), new bpt(cuf, ctg, 9, (byte[]) null), cuf.d), new ctz(cuf, ctg, z, csk, str), cuf.d);
    }
}
