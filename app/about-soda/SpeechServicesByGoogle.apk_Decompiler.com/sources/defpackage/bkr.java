package defpackage;

import android.content.DialogInterface;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.Drawable;
import android.os.Bundle;
import androidx.preference.DialogPreference;

/* renamed from: bkr  reason: default package */
/* compiled from: PG */
public abstract class bkr extends bkk implements DialogInterface.OnClickListener {
    private DialogPreference an;

    public final DialogPreference F() {
        if (this.an == null) {
            this.an = (DialogPreference) ((asg) getTargetFragment()).aW(this.l.getString("key"));
        }
        return this.an;
    }

    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        bc targetFragment = getTargetFragment();
        if (targetFragment instanceof asg) {
            asg asg = (asg) targetFragment;
            String string = this.l.getString("key");
            if (bundle == null) {
                DialogPreference dialogPreference = (DialogPreference) asg.aW(string);
                this.an = dialogPreference;
                this.ag = dialogPreference.getDialogTitle();
                this.ah = this.an.getPositiveButtonText();
                this.ai = this.an.getNegativeButtonText();
                this.aj = this.an.getDialogMessage();
                this.ak = this.an.getDialogLayoutResource();
                Drawable dialogIcon = this.an.getDialogIcon();
                if (dialogIcon == null || (dialogIcon instanceof BitmapDrawable)) {
                    this.al = (BitmapDrawable) dialogIcon;
                    return;
                }
                Bitmap createBitmap = Bitmap.createBitmap(dialogIcon.getIntrinsicWidth(), dialogIcon.getIntrinsicHeight(), Bitmap.Config.ARGB_8888);
                Canvas canvas = new Canvas(createBitmap);
                dialogIcon.setBounds(0, 0, canvas.getWidth(), canvas.getHeight());
                dialogIcon.draw(canvas);
                this.al = new BitmapDrawable(getResources(), createBitmap);
                return;
            }
            return;
        }
        throw new IllegalStateException("Target fragment must implement TargetFragment interface");
    }
}
