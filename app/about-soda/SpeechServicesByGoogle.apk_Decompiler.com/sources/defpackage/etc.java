package defpackage;

/* renamed from: etc  reason: default package */
/* compiled from: PG */
public final class etc extends htq implements hvb {
    public static final etc f;
    private static volatile hvh g;
    public int a;
    public dzh b;
    public ebw c;
    public dyt d;
    public boolean e;

    static {
        etc etc = new etc();
        f = etc;
        htq.z(etc.class, etc);
    }

    private etc() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(f, "\u0004\u0004\u0000\u0001\u0001\u0005\u0004\u0000\u0000\u0000\u0001ဉ\u0000\u0003ဉ\u0001\u0004ဉ\u0002\u0005ဇ\u0003", new Object[]{"a", "b", "c", "d", "e"});
        } else if (i2 == 3) {
            return new etc();
        } else {
            if (i2 == 4) {
                return new htk((htq) f);
            }
            if (i2 == 5) {
                return f;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = g;
            if (hvh == null) {
                synchronized (etc.class) {
                    hvh = g;
                    if (hvh == null) {
                        hvh = new htl(f);
                        g = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
