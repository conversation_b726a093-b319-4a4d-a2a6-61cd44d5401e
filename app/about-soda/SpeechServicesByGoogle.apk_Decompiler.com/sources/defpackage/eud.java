package defpackage;

import java.util.concurrent.Executor;

/* renamed from: eud  reason: default package */
/* compiled from: PG */
public final class eud {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/service/impl/StreamRouteSessionResponseSender");
    public final int b;
    public final Runnable c;
    public final jix d;
    public final jpp e = new jpp(false, jpt.a);
    private final hme f;
    private final jqs g;
    private final Executor h;
    private final jpp i = new jpp(false, jpt.a);

    public eud(int i2, hme hme, Runnable runnable, jix jix, jqs jqs, Executor executor) {
        this.b = i2;
        this.f = hme;
        this.c = runnable;
        this.d = jix;
        this.g = jqs;
        this.h = executor;
        ((jiq) jix).e(new dqb(this, 20));
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x003b  */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0078  */
    /* JADX WARNING: Removed duplicated region for block: B:31:0x00cf  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0029  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object a(defpackage.jlr r10) {
        /*
            r9 = this;
            boolean r0 = r10 instanceof defpackage.euc
            if (r0 == 0) goto L_0x0013
            r0 = r10
            euc r0 = (defpackage.euc) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            euc r0 = new euc
            r0.<init>(r9, r10)
        L_0x0018:
            java.lang.Object r10 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            java.lang.String r3 = "sendDisconnectStatus"
            java.lang.String r4 = "com/google/android/libraries/search/audio/service/impl/StreamRouteSessionResponseSender"
            java.lang.String r5 = "StreamRouteSessionResponseSender.kt"
            java.lang.String r6 = "ALT.GrpcRouteRespSender"
            r7 = 1
            if (r2 == 0) goto L_0x003b
            if (r2 != r7) goto L_0x0033
            eud r0 = r0.d
            defpackage.jji.c(r10)     // Catch:{ all -> 0x0031 }
            goto L_0x0066
        L_0x0031:
            r10 = move-exception
            goto L_0x006c
        L_0x0033:
            java.lang.IllegalStateException r10 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r10.<init>(r0)
            throw r10
        L_0x003b:
            defpackage.jji.c(r10)
            hca r10 = a
            hco r10 = r10.c()
            hcr r2 = defpackage.hdg.a
            hco r10 = r10.h(r2, r6)
            r2 = 71
            hco r10 = r10.j(r4, r3, r2, r5)
            hby r10 = (defpackage.hby) r10
            int r2 = r9.b
            java.lang.String r8 = "#audio# sending route(%d) disconnect status once done"
            r10.s(r8, r2)
            hme r10 = r9.f     // Catch:{ all -> 0x006a }
            r0.d = r9     // Catch:{ all -> 0x006a }
            r0.c = r7     // Catch:{ all -> 0x006a }
            java.lang.Object r10 = defpackage.jqw.x(r10, r0)     // Catch:{ all -> 0x006a }
            if (r10 == r1) goto L_0x0069
            r0 = r9
        L_0x0066:
            dzm r10 = (defpackage.dzm) r10     // Catch:{ all -> 0x0031 }
            goto L_0x0070
        L_0x0069:
            return r1
        L_0x006a:
            r10 = move-exception
            r0 = r9
        L_0x006c:
            java.lang.Object r10 = defpackage.jji.b(r10)
        L_0x0070:
            java.lang.Throwable r1 = defpackage.jju.a(r10)
            java.lang.String r2 = "newBuilder(...)"
            if (r1 == 0) goto L_0x00af
            hca r10 = a
            hco r10 = r10.h()
            hcr r7 = defpackage.hdg.a
            hco r10 = r10.h(r7, r6)
            hby r10 = (defpackage.hby) r10
            hco r10 = r10.i(r1)
            r1 = 78
            hco r10 = r10.j(r4, r3, r1, r5)
            hby r10 = (defpackage.hby) r10
            int r1 = r0.b
            java.lang.String r3 = "#audio# unexpected fail while getting route(%d) disconnect status, sending failure"
            r10.s(r3, r1)
            dzm r10 = defpackage.dzm.c
            htk r10 = r10.l()
            defpackage.jnu.d(r10, r2)
            dku r10 = defpackage.jnu.e(r10, "builder")
            dzx r1 = defpackage.dzx.FAILED_GETTING_DISCONNECT_REASON
            r10.m(r1)
            dzm r10 = r10.l()
        L_0x00af:
            dzm r10 = (defpackage.dzm) r10
            etg r1 = defpackage.etg.c
            htk r1 = r1.l()
            defpackage.jnu.d(r1, r2)
            dlv r1 = defpackage.jnu.e(r1, "builder")
            java.lang.String r2 = "value"
            defpackage.jnu.e(r10, r2)
            java.lang.Object r2 = r1.a
            htk r2 = (defpackage.htk) r2
            htq r3 = r2.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x00d2
            r2.u()
        L_0x00d2:
            htq r2 = r2.b
            etg r2 = (defpackage.etg) r2
            r10.getClass()
            r2.b = r10
            r10 = 3
            r2.a = r10
            etg r10 = r1.m()
            r0.b(r10)
            jkd r10 = defpackage.jkd.a
            return r10
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eud.a(jlr):java.lang.Object");
    }

    public final Object b(etg etg) {
        Object obj;
        if (this.e.b()) {
            return jji.b(new Throwable());
        }
        try {
            this.d.c(etg);
            obj = jkd.a;
        } catch (Throwable th) {
            obj = jji.b(th);
        }
        Throwable a2 = jju.a(obj);
        if (a2 == null) {
            return obj;
        }
        ((hby) ((hby) a.h().h(hdg.a, "ALT.GrpcRouteRespSender")).i(a2).j("com/google/android/libraries/search/audio/service/impl/StreamRouteSessionResponseSender", "sendNext-IoAF18A", 114, "StreamRouteSessionResponseSender.kt")).s("#audio# cannot send route(%d) data to the remote client, shutting down", this.b);
        if (!this.e.c()) {
            try {
                this.d.b(a2);
            } catch (Throwable th2) {
                jji.b(th2);
            }
            c();
        }
        return obj;
    }

    public final void c() {
        ((hby) a.f().h(hdg.a, "ALT.GrpcRouteRespSender").j("com/google/android/libraries/search/audio/service/impl/StreamRouteSessionResponseSender", "shutdown", 88, "StreamRouteSessionResponseSender.kt")).s("#audio# the route's(%d) communication channel shut down", this.b);
        this.h.execute(gof.h(new dqb(this, 19)));
    }

    public final void d() {
        if (this.i.c()) {
            ((hby) a.c().h(hdg.a, "ALT.GrpcRouteRespSender").j("com/google/android/libraries/search/audio/service/impl/StreamRouteSessionResponseSender", "start", 50, "StreamRouteSessionResponseSender.kt")).r("#audio# skipping sending due to already started");
            return;
        }
        ((hby) a.c().h(hdg.a, "ALT.GrpcRouteRespSender").j("com/google/android/libraries/search/audio/service/impl/StreamRouteSessionResponseSender", "start", 53, "StreamRouteSessionResponseSender.kt")).s("#audio# starting sending route(%d) info", this.b);
        job.S(this.g, (jlv) null, (jqt) null, new jur(this, (jlr) null, 1), 3);
    }
}
