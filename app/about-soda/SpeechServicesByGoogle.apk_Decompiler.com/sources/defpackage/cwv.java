package defpackage;

import java.util.List;

/* renamed from: cwv  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwv implements hko {
    public final /* synthetic */ ctj a;
    public final /* synthetic */ csv b;
    public final /* synthetic */ csy c;
    public final /* synthetic */ ctg d;
    public final /* synthetic */ int e;
    public final /* synthetic */ long f;
    public final /* synthetic */ String g;
    public final /* synthetic */ csz h;
    public final /* synthetic */ int i;
    public final /* synthetic */ List j;
    public final /* synthetic */ Object k;
    public final /* synthetic */ Object l;
    public final /* synthetic */ dbw m;
    private final /* synthetic */ int n;

    public /* synthetic */ cwv(dbw dbw, ctj ctj, String str, csv csv, csy csy, ctg ctg, int i2, long j2, String str2, csz csz, int i3, List list, hse hse, int i4) {
        this.n = i4;
        this.m = dbw;
        this.a = ctj;
        this.k = str;
        this.b = csv;
        this.c = csy;
        this.d = ctg;
        this.e = i2;
        this.f = j2;
        this.g = str2;
        this.h = csz;
        this.i = i3;
        this.j = list;
        this.l = hse;
    }

    /* JADX WARNING: type inference failed for: r2v5, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v6, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v11, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r12v2, types: [java.lang.Object, cxa] */
    /* JADX WARNING: type inference failed for: r23v1, types: [java.util.concurrent.Executor] */
    /* JADX WARNING: type inference failed for: r13v6, types: [java.lang.Object, cxa] */
    /* JADX WARNING: type inference failed for: r15v6, types: [cuk, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r28v1, types: [java.util.concurrent.Executor] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme a(java.lang.Object r35) {
        /*
            r34 = this;
            r0 = r34
            int r1 = r0.n
            if (r1 == 0) goto L_0x0124
            r1 = r35
            java.lang.Boolean r1 = (java.lang.Boolean) r1
            java.lang.Object r1 = r0.l
            java.lang.Object r1 = defpackage.hfc.S(r1)
            r10 = r1
            android.net.Uri r10 = (android.net.Uri) r10
            dbw r1 = r0.m
            java.lang.Object r3 = r1.f
            grh r3 = (defpackage.grh) r3
            boolean r3 = r3.f()
            csv r4 = r0.b
            ctj r5 = r0.a
            ctg r6 = r0.d
            int r7 = r0.e
            long r8 = r0.f
            java.lang.String r15 = r0.g
            csz r13 = r0.h
            int r12 = r0.i
            java.util.List r11 = r0.j
            java.lang.Object r14 = r0.k
            if (r3 == 0) goto L_0x00c5
            csy r3 = r0.c
            if (r3 != 0) goto L_0x0039
            goto L_0x00c5
        L_0x0039:
            android.content.Context r2 = r1.a
            r17 = r13
            java.lang.Object r13 = r1.j
            r19 = r11
            java.lang.Object r11 = r1.c
            r21 = r15
            java.lang.Object r15 = r1.d
            cxq r29 = new cxq
            r22 = r12
            int r12 = r5.e
            int r12 = defpackage.a.x(r12)
            if (r12 != 0) goto L_0x0056
            r23 = 1
            goto L_0x0058
        L_0x0056:
            r23 = r12
        L_0x0058:
            java.lang.Object r12 = r1.f
            grh r12 = (defpackage.grh) r12
            java.lang.Object r12 = r12.b()
            r18 = r12
            cum r18 = (defpackage.cum) r18
            java.lang.Object r12 = r1.i
            r24 = r14
            java.lang.Object r14 = r1.g
            java.lang.Object r0 = r1.m
            r35 = r5
            java.lang.Object r5 = r1.l
            r28 = r5
            r27 = r0
            cqh r27 = (defpackage.cqh) r27
            r26 = r14
            grh r26 = (defpackage.grh) r26
            r20 = r12
            cyk r20 = (defpackage.cyk) r20
            r14 = r11
            kjd r14 = (defpackage.kjd) r14
            r0 = r24
            r30 = r19
            r11 = r29
            r31 = r22
            r12 = r2
            r2 = r17
            r32 = r21
            r16 = r4
            r17 = r23
            r19 = r3
            r21 = r6
            r22 = r7
            r23 = r8
            r25 = r32
            r11.<init>(r12, r13, r14, r15, r16, r17, r18, r19, r20, r21, r22, r23, r25, r26, r27, r28)
            r1.p(r6, r10)
            java.lang.Object r1 = r1.h
            r5 = r35
            java.lang.String r4 = r5.d
            java.lang.String r11 = r3.b
            int r12 = r3.c
            r3 = r1
            cxx r3 = (defpackage.cxx) r3
            r17 = r0
            hse r17 = (defpackage.hse) r17
            r5 = r6
            r6 = r7
            r7 = r8
            r9 = r32
            r13 = r2
            r14 = r29
            r15 = r31
            r16 = r30
            hme r0 = r3.c(r4, r5, r6, r7, r9, r10, r11, r12, r13, r14, r15, r16, r17)
            goto L_0x0123
        L_0x00c5:
            r30 = r11
            r31 = r12
            r2 = r13
            r0 = r14
            r32 = r15
            java.lang.Object r12 = r1.j
            java.lang.Object r3 = r1.c
            cxs r24 = new cxs
            int r11 = r5.e
            int r11 = defpackage.a.x(r11)
            if (r11 != 0) goto L_0x00dd
            r15 = 1
            goto L_0x00de
        L_0x00dd:
            r15 = r11
        L_0x00de:
            java.lang.Object r11 = r1.i
            java.lang.Object r13 = r1.m
            java.lang.Object r14 = r1.l
            r22 = r13
            cqh r22 = (defpackage.cqh) r22
            r16 = r11
            cyk r16 = (defpackage.cyk) r16
            r13 = r3
            kjd r13 = (defpackage.kjd) r13
            r11 = r24
            r3 = r14
            r14 = r4
            r17 = r6
            r18 = r7
            r19 = r8
            r21 = r32
            r23 = r3
            r11.<init>(r12, r13, r14, r15, r16, r17, r18, r19, r21, r22, r23)
            r1.p(r6, r10)
            java.lang.Object r1 = r1.h
            java.lang.String r5 = r5.d
            java.lang.String r11 = r4.c
            int r12 = r4.d
            r3 = r1
            cxx r3 = (defpackage.cxx) r3
            r17 = r0
            hse r17 = (defpackage.hse) r17
            r4 = r5
            r5 = r6
            r6 = r7
            r7 = r8
            r9 = r32
            r13 = r2
            r14 = r24
            r15 = r31
            r16 = r30
            hme r0 = r3.c(r4, r5, r6, r7, r9, r10, r11, r12, r13, r14, r15, r16, r17)
        L_0x0123:
            return r0
        L_0x0124:
            r0 = r35
            ctl r0 = (defpackage.ctl) r0
            int r1 = r0.c
            ctf r1 = defpackage.ctf.b(r1)
            if (r1 != 0) goto L_0x0132
            ctf r1 = defpackage.ctf.NONE
        L_0x0132:
            ctf r2 = defpackage.ctf.DOWNLOAD_COMPLETE
            if (r1 != r2) goto L_0x013a
            hme r0 = defpackage.hma.a
            goto L_0x01be
        L_0x013a:
            r1 = r34
            ctj r8 = r1.a
            r2 = 5
            java.lang.Object r2 = r0.C(r2)
            r4 = r2
            htk r4 = (defpackage.htk) r4
            r4.x(r0)
            int r0 = r8.e
            int r0 = defpackage.a.x(r0)
            if (r0 != 0) goto L_0x0153
            r2 = 1
            goto L_0x0154
        L_0x0153:
            r2 = r0
        L_0x0154:
            java.lang.Object r0 = r1.l
            java.util.List r9 = r1.j
            int r10 = r1.i
            csz r11 = r1.h
            java.lang.String r12 = r1.g
            long r13 = r1.f
            int r15 = r1.e
            ctg r7 = r1.d
            csy r6 = r1.c
            csv r5 = r1.b
            java.lang.Object r3 = r1.k
            r16 = r7
            dbw r7 = r1.m
            java.lang.String r1 = r5.f
            java.lang.String r3 = (java.lang.String) r3
            hme r19 = r7.r(r2, r3, r1)
            czw r1 = defpackage.czw.e(r19)
            cwt r3 = new cwt
            r17 = 2
            r18 = 0
            r2 = r3
            r33 = r3
            r3 = r7
            r21 = r5
            r5 = r8
            r20 = r6
            r6 = r17
            r30 = r9
            r9 = r7
            r7 = r18
            r2.<init>((java.lang.Object) r3, (java.lang.Object) r4, (java.lang.Object) r5, (int) r6, (char[]) r7)
            java.lang.Object r2 = r9.l
            r3 = r33
            czw r1 = r1.g(r3, r2)
            cwv r2 = new cwv
            r31 = r0
            hse r31 = (defpackage.hse) r31
            r32 = 1
            r17 = r2
            r18 = r9
            r22 = r8
            r23 = r16
            r24 = r15
            r25 = r13
            r27 = r12
            r28 = r11
            r29 = r10
            r17.<init>((defpackage.dbw) r18, (defpackage.hme) r19, (defpackage.csy) r20, (defpackage.csv) r21, (defpackage.ctj) r22, (defpackage.ctg) r23, (int) r24, (long) r25, (java.lang.String) r27, (defpackage.csz) r28, (int) r29, (java.util.List) r30, (defpackage.hse) r31, (int) r32)
            java.lang.Object r0 = r9.l
            czw r0 = r1.g(r2, r0)
        L_0x01be:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cwv.a(java.lang.Object):hme");
    }

    public /* synthetic */ cwv(dbw dbw, hme hme, csy csy, csv csv, ctj ctj, ctg ctg, int i2, long j2, String str, csz csz, int i3, List list, hse hse, int i4) {
        this.n = i4;
        this.m = dbw;
        this.l = hme;
        this.c = csy;
        this.b = csv;
        this.a = ctj;
        this.d = ctg;
        this.e = i2;
        this.f = j2;
        this.g = str;
        this.h = csz;
        this.i = i3;
        this.j = list;
        this.k = hse;
    }
}
