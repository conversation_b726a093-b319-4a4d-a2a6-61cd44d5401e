package defpackage;

import androidx.wear.ambient.SharedLibraryVersion;
import androidx.work.impl.WorkDatabase;
import java.util.List;
import java.util.Set;

/* renamed from: bdp  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bdp implements Runnable {
    public final /* synthetic */ WorkDatabase a;
    public final /* synthetic */ bhe b;
    public final /* synthetic */ bhe c;
    public final /* synthetic */ List d;
    public final /* synthetic */ String e;
    public final /* synthetic */ Set f;
    public final /* synthetic */ boolean g;

    public /* synthetic */ bdp(WorkDatabase workDatabase, bhe bhe, bhe bhe2, List list, String str, Set set, boolean z) {
        this.a = workDatabase;
        this.b = bhe;
        this.c = bhe2;
        this.d = list;
        this.e = str;
        this.f = set;
        this.g = z;
    }

    public final void run() {
        aua aua;
        axc d2;
        WorkDatabase workDatabase = this.a;
        jnu.e(workDatabase, "$workDatabase");
        List list = this.d;
        jnu.e(list, "$schedulers");
        String str = this.e;
        jnu.e(str, "$workSpecId");
        bhf A = workDatabase.A();
        bhy B = workDatabase.B();
        bhe bhe = this.b;
        bbx bbx = bhe.c;
        int i = bhe.l;
        long j = bhe.o;
        int i2 = bhe.t;
        int i3 = bhe.u + 1;
        long j2 = bhe.v;
        int i4 = bhe.w;
        bhe bhe2 = this.c;
        bhe e2 = bhe.e(bhe2, (String) null, bbx, (String) null, (bat) null, i, j, i2, i3, j2, i4, 12835837);
        bhe bhe3 = bhe2;
        if (bhe3.w == 1) {
            e2.v = bhe3.v;
            e2.w++;
        }
        bhe o = yh.o(list, e2);
        bhx bhx = (bhx) A;
        bhx.a.k();
        bhx.a.l();
        try {
            aua = ((bhx) A).c;
            d2 = aua.d();
            d2.g(1, o.b);
            d2.e(2, (long) xm.m(o.c));
            d2.g(3, o.d);
            d2.g(4, o.e);
            d2.c(5, SharedLibraryVersion.a(o.f));
            d2.c(6, SharedLibraryVersion.a(o.g));
            d2.e(7, o.h);
            d2.e(8, o.i);
            d2.e(9, o.j);
            d2.e(10, (long) o.l);
            d2.e(11, (long) xm.j(o.m));
            d2.e(12, o.n);
            d2.e(13, o.o);
            d2.e(14, o.p);
            d2.e(15, o.q);
            d2.e(16, o.r ? 1 : 0);
            d2.e(17, (long) xm.l(o.s));
            d2.e(18, (long) o.t);
            d2.e(19, (long) o.u);
            d2.e(20, o.v);
            d2.e(21, (long) o.w);
            d2.e(22, (long) o.x);
            String str2 = o.y;
            if (str2 == null) {
                d2.f(23);
            } else {
                d2.g(23, str2);
            }
            baq baq = o.k;
            d2.e(24, (long) xm.k(baq.b));
            d2.c(25, xm.t(baq.c));
            d2.e(26, baq.d ? 1 : 0);
            d2.e(27, baq.e ? 1 : 0);
            d2.e(28, baq.f ? 1 : 0);
            d2.e(29, baq.g ? 1 : 0);
            d2.e(30, baq.h);
            d2.e(31, baq.i);
            d2.c(32, xm.u(baq.j));
            d2.g(33, o.b);
            d2.a();
            aua.f(d2);
            ((bhx) A).a.o();
            bhx.a.m();
            bib bib = (bib) B;
            bib.a.k();
            axc d3 = bib.c.d();
            d3.g(1, str);
            try {
                ((bib) B).a.l();
                d3.a();
                ((bib) B).a.o();
                ((bib) B).a.m();
                boolean z = this.g;
                Set set = this.f;
                bib.c.f(d3);
                wg.e(B, str, set);
                if (!z) {
                    A.l(str, -1);
                    workDatabase.z().a(str);
                }
            } catch (Throwable th) {
                bib.c.f(d3);
                throw th;
            }
        } catch (Throwable th2) {
            bhx.a.m();
            throw th2;
        }
    }
}
