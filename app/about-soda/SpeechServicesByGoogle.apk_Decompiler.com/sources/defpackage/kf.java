package defpackage;

import android.view.View;
import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.ConcurrentModificationException;

/* renamed from: kf  reason: default package */
/* compiled from: PG */
public class kf {
    public kf() {
    }

    public static void a(View view, float f) {
        try {
            view.setFrameContentVelocity(f);
        } catch (LinkageError unused) {
        }
    }

    public static int b(la laVar, ka kaVar, View view, View view2, kl klVar, boolean z) {
        if (klVar.ao() == 0 || laVar.a() == 0 || view == null || view2 == null) {
            return 0;
        }
        if (!z) {
            return Math.abs(kl.bk(view) - kl.bk(view2)) + 1;
        }
        return Math.min(kaVar.k(), kaVar.a(view2) - kaVar.d(view));
    }

    public static int c(la laVar, ka kaVar, View view, View view2, kl klVar, boolean z, boolean z2) {
        int i;
        if (klVar.ao() == 0 || laVar.a() == 0 || view == null || view2 == null) {
            return 0;
        }
        int min = Math.min(kl.bk(view), kl.bk(view2));
        int max = Math.max(kl.bk(view), kl.bk(view2));
        if (z2) {
            i = Math.max(0, (laVar.a() - max) - 1);
        } else {
            i = Math.max(0, min);
        }
        if (!z) {
            return i;
        }
        return Math.round((((float) i) * (((float) Math.abs(kaVar.a(view2) - kaVar.d(view))) / ((float) (Math.abs(kl.bk(view) - kl.bk(view2)) + 1)))) + ((float) (kaVar.j() - kaVar.d(view))));
    }

    public static int d(la laVar, ka kaVar, View view, View view2, kl klVar, boolean z) {
        if (klVar.ao() == 0 || laVar.a() == 0 || view == null || view2 == null) {
            return 0;
        }
        if (!z) {
            return laVar.a();
        }
        return (int) ((((float) (kaVar.a(view2) - kaVar.d(view))) / ((float) (Math.abs(kl.bk(view) - kl.bk(view2)) + 1))) * ((float) laVar.a()));
    }

    public static int e(ov ovVar, int i) {
        try {
            return pd.a(ovVar.a, ovVar.c, i);
        } catch (IndexOutOfBoundsException unused) {
            throw new ConcurrentModificationException();
        }
    }

    public static int f(ov ovVar, Object obj, int i) {
        int i2 = ovVar.c;
        if (i2 == 0) {
            return -1;
        }
        int e = e(ovVar, i);
        if (e < 0 || jnu.i(obj, ovVar.b[e])) {
            return e;
        }
        int i3 = e + 1;
        while (i3 < i2 && ovVar.a[i3] == i) {
            if (jnu.i(obj, ovVar.b[i3])) {
                return i3;
            }
            i3++;
        }
        int i4 = e - 1;
        while (i4 >= 0 && ovVar.a[i4] == i) {
            if (jnu.i(obj, ovVar.b[i4])) {
                return i4;
            }
            i4--;
        }
        return ~i3;
    }

    public static int g(ov ovVar) {
        return f(ovVar, (Object) null, 0);
    }

    public static void h(ov ovVar, int i) {
        ovVar.a = new int[i];
        ovVar.b = new Object[i];
    }

    public static qz i(qg qgVar, int i, ArrayList arrayList, qz qzVar) {
        int i2;
        int i3;
        int i4;
        if (i == 0) {
            i2 = qgVar.ao;
        } else {
            i2 = qgVar.ap;
        }
        if (i2 != -1 && (qzVar == null || i2 != qzVar.c)) {
            int i5 = 0;
            while (true) {
                if (i5 >= arrayList.size()) {
                    break;
                }
                qz qzVar2 = (qz) arrayList.get(i5);
                if (qzVar2.c == i2) {
                    if (qzVar != null) {
                        qzVar.c(i, qzVar2);
                        arrayList.remove(qzVar);
                    }
                    qzVar = qzVar2;
                } else {
                    i5++;
                }
            }
        } else if (i2 != -1) {
            return qzVar;
        }
        if (qzVar == null) {
            if (qgVar instanceof qk) {
                qk qkVar = (qk) qgVar;
                int i6 = 0;
                while (true) {
                    if (i6 >= qkVar.as) {
                        i4 = -1;
                        break;
                    }
                    qg qgVar2 = qkVar.ar[i6];
                    if (i == 0) {
                        i4 = qgVar2.ao;
                        if (i4 != -1) {
                            break;
                        }
                    } else {
                        i4 = qgVar2.ap;
                        if (i4 != -1) {
                            break;
                        }
                    }
                    i6++;
                }
                if (i4 != -1) {
                    int i7 = 0;
                    while (true) {
                        if (i7 >= arrayList.size()) {
                            break;
                        }
                        qz qzVar3 = (qz) arrayList.get(i7);
                        if (qzVar3.c == i4) {
                            qzVar = qzVar3;
                            break;
                        }
                        i7++;
                    }
                }
            }
            if (qzVar == null) {
                qzVar = new qz(i);
            }
            arrayList.add(qzVar);
        }
        if (qzVar.d(qgVar)) {
            if (qgVar instanceof qj) {
                qj qjVar = (qj) qgVar;
                qf qfVar = qjVar.d;
                if (qjVar.ar == 0) {
                    i3 = 1;
                } else {
                    i3 = 0;
                }
                qfVar.c(i3, arrayList, qzVar);
            }
            if (i == 0) {
                qgVar.ao = qzVar.c;
                qgVar.f39J.c(0, arrayList, qzVar);
                qgVar.L.c(0, arrayList, qzVar);
            } else {
                qgVar.ap = qzVar.c;
                qgVar.K.c(1, arrayList, qzVar);
                qgVar.N.c(1, arrayList, qzVar);
                qgVar.M.c(1, arrayList, qzVar);
            }
            qgVar.Q.c(i, arrayList, qzVar);
        }
        return qzVar;
    }

    public static qz j(ArrayList arrayList, int i) {
        int size = arrayList.size();
        for (int i2 = 0; i2 < size; i2++) {
            qz qzVar = (qz) arrayList.get(i2);
            if (i == qzVar.c) {
                return qzVar;
            }
        }
        return null;
    }

    public static boolean k(int i, int i2, int i3, int i4) {
        boolean z;
        boolean z2;
        if (i3 == 1 || i3 == 2 || (i3 == 4 && i != 2)) {
            z = true;
        } else {
            z = false;
        }
        if (i4 == 1 || i4 == 2 || (i4 == 4 && i2 != 2)) {
            z2 = true;
        } else {
            z2 = false;
        }
        if (z || z2) {
            return true;
        }
        return false;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:164:0x029b, code lost:
        if (r4.d == r7) goto L_0x029f;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:59:0x0115, code lost:
        if (r6.d == r5) goto L_0x0119;
     */
    /* JADX WARNING: Removed duplicated region for block: B:193:0x031c  */
    /* JADX WARNING: Removed duplicated region for block: B:202:0x0346  */
    /* JADX WARNING: Removed duplicated region for block: B:203:0x034a  */
    /* JADX WARNING: Removed duplicated region for block: B:206:0x0352  */
    /* JADX WARNING: Removed duplicated region for block: B:237:0x0453 A[ADDED_TO_REGION] */
    /* JADX WARNING: Removed duplicated region for block: B:259:0x04b9  */
    /* JADX WARNING: Removed duplicated region for block: B:299:0x054a  */
    /* JADX WARNING: Removed duplicated region for block: B:319:0x05ac  */
    /* JADX WARNING: Removed duplicated region for block: B:320:0x05ae  */
    /* JADX WARNING: Removed duplicated region for block: B:325:0x05c5  */
    /* JADX WARNING: Removed duplicated region for block: B:327:0x05cd A[ADDED_TO_REGION] */
    /* JADX WARNING: Removed duplicated region for block: B:395:0x071e  */
    /* JADX WARNING: Removed duplicated region for block: B:396:0x0720  */
    /* JADX WARNING: Removed duplicated region for block: B:399:0x0729  */
    /* JADX WARNING: Removed duplicated region for block: B:400:0x072c  */
    /* JADX WARNING: Removed duplicated region for block: B:403:0x0732  */
    /* JADX WARNING: Removed duplicated region for block: B:404:0x0735  */
    /* JADX WARNING: Removed duplicated region for block: B:406:0x0739  */
    /* JADX WARNING: Removed duplicated region for block: B:411:0x0748  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static void l(defpackage.qh r40, defpackage.px r41, java.util.ArrayList r42, int r43) {
        /*
            r0 = r40
            r10 = r41
            r11 = r42
            r12 = 2
            if (r43 != 0) goto L_0x0012
            int r1 = r0.at
            qe[] r2 = r0.aw
            r14 = r1
            r15 = r2
            r16 = 0
            goto L_0x001a
        L_0x0012:
            int r1 = r0.au
            qe[] r2 = r0.av
            r14 = r1
            r15 = r2
            r16 = r12
        L_0x001a:
            r9 = 0
        L_0x001b:
            if (r9 >= r14) goto L_0x0777
            r1 = r15[r9]
            boolean r2 = r1.t
            r3 = 3
            r8 = 8
            r17 = 0
            r7 = 1
            if (r2 != 0) goto L_0x016c
            int r2 = r1.o
            int r2 = r2 + r2
            qg r5 = r1.a
            r6 = r5
            r18 = 0
        L_0x0031:
            if (r18 != 0) goto L_0x0128
            int r18 = r2 + 1
            int r13 = r1.i
            int r13 = r13 + r7
            r1.i = r13
            qg[] r13 = r5.an
            int r4 = r1.o
            r13[r4] = r17
            qg[] r13 = r5.am
            r13[r4] = r17
            int r13 = r5.ah
            if (r13 == r8) goto L_0x00f9
            int r13 = r1.l
            int r13 = r13 + r7
            r1.l = r13
            int r4 = r5.L(r4)
            if (r4 == r3) goto L_0x0065
            int r4 = r1.m
            int r13 = r1.o
            if (r13 != 0) goto L_0x005e
            int r13 = r5.j()
            goto L_0x0062
        L_0x005e:
            int r13 = r5.h()
        L_0x0062:
            int r4 = r4 + r13
            r1.m = r4
        L_0x0065:
            int r4 = r1.m
            qf[] r13 = r5.R
            r13 = r13[r2]
            int r13 = r13.b()
            int r4 = r4 + r13
            r1.m = r4
            qf[] r13 = r5.R
            r13 = r13[r18]
            int r13 = r13.b()
            int r4 = r4 + r13
            r1.m = r4
            int r4 = r1.n
            qf[] r13 = r5.R
            r13 = r13[r2]
            int r13 = r13.b()
            int r4 = r4 + r13
            r1.n = r4
            qf[] r13 = r5.R
            r13 = r13[r18]
            int r13 = r13.b()
            int r4 = r4 + r13
            r1.n = r4
            qg r4 = r1.b
            if (r4 != 0) goto L_0x009b
            r1.b = r5
        L_0x009b:
            r1.d = r5
            int[] r4 = r5.aq
            int r13 = r1.o
            r4 = r4[r13]
            if (r4 != r3) goto L_0x00f9
            int[] r4 = r5.u
            r4 = r4[r13]
            if (r4 == 0) goto L_0x00b0
            if (r4 == r3) goto L_0x00b0
            if (r4 != r12) goto L_0x00f7
            r4 = r12
        L_0x00b0:
            int r12 = r1.j
            int r12 = r12 + r7
            r1.j = r12
            float[] r12 = r5.al
            r12 = r12[r13]
            r13 = 0
            int r22 = (r12 > r13 ? 1 : (r12 == r13 ? 0 : -1))
            if (r22 <= 0) goto L_0x00c3
            float r13 = r1.k
            float r13 = r13 + r12
            r1.k = r13
        L_0x00c3:
            int r13 = r5.ah
            if (r13 == r8) goto L_0x00e5
            if (r4 == 0) goto L_0x00cb
            if (r4 != r3) goto L_0x00e5
        L_0x00cb:
            r4 = 0
            int r12 = (r12 > r4 ? 1 : (r12 == r4 ? 0 : -1))
            if (r12 >= 0) goto L_0x00d3
            r1.q = r7
            goto L_0x00d5
        L_0x00d3:
            r1.r = r7
        L_0x00d5:
            java.util.ArrayList r4 = r1.h
            if (r4 != 0) goto L_0x00e0
            java.util.ArrayList r4 = new java.util.ArrayList
            r4.<init>()
            r1.h = r4
        L_0x00e0:
            java.util.ArrayList r4 = r1.h
            r4.add(r5)
        L_0x00e5:
            qg r4 = r1.f
            if (r4 != 0) goto L_0x00eb
            r1.f = r5
        L_0x00eb:
            qg r4 = r1.g
            if (r4 == 0) goto L_0x00f5
            int r12 = r1.o
            qg[] r4 = r4.am
            r4[r12] = r5
        L_0x00f5:
            r1.g = r5
        L_0x00f7:
            int r4 = r1.o
        L_0x00f9:
            if (r6 == r5) goto L_0x0101
            qg[] r4 = r6.an
            int r6 = r1.o
            r4[r6] = r5
        L_0x0101:
            qf[] r4 = r5.R
            r4 = r4[r18]
            qf r4 = r4.e
            if (r4 == 0) goto L_0x0117
            qg r4 = r4.d
            qf[] r6 = r4.R
            r6 = r6[r2]
            qf r6 = r6.e
            if (r6 == 0) goto L_0x0117
            qg r6 = r6.d
            if (r6 == r5) goto L_0x0119
        L_0x0117:
            r4 = r17
        L_0x0119:
            if (r4 == 0) goto L_0x011e
            r18 = 0
            goto L_0x0120
        L_0x011e:
            r18 = r7
        L_0x0120:
            if (r4 != 0) goto L_0x0123
            r4 = r5
        L_0x0123:
            r6 = r5
            r12 = 2
            r5 = r4
            goto L_0x0031
        L_0x0128:
            qg r4 = r1.b
            if (r4 == 0) goto L_0x0139
            int r6 = r1.m
            qf[] r4 = r4.R
            r4 = r4[r2]
            int r4 = r4.b()
            int r6 = r6 - r4
            r1.m = r6
        L_0x0139:
            qg r4 = r1.d
            if (r4 == 0) goto L_0x014c
            int r2 = r2 + 1
            int r6 = r1.m
            qf[] r4 = r4.R
            r2 = r4[r2]
            int r2 = r2.b()
            int r6 = r6 - r2
            r1.m = r6
        L_0x014c:
            r1.c = r5
            int r2 = r1.o
            if (r2 != 0) goto L_0x015b
            boolean r2 = r1.p
            if (r2 == 0) goto L_0x015b
            qg r2 = r1.c
            r1.e = r2
            goto L_0x015f
        L_0x015b:
            qg r2 = r1.a
            r1.e = r2
        L_0x015f:
            boolean r2 = r1.r
            if (r2 == 0) goto L_0x0169
            boolean r2 = r1.q
            if (r2 == 0) goto L_0x0169
            r2 = r7
            goto L_0x016a
        L_0x0169:
            r2 = 0
        L_0x016a:
            r1.s = r2
        L_0x016c:
            r1.t = r7
            if (r11 == 0) goto L_0x0181
            qg r2 = r1.a
            boolean r2 = r11.contains(r2)
            if (r2 == 0) goto L_0x0179
            goto L_0x0181
        L_0x0179:
            r27 = r9
            r30 = r14
            r31 = r15
            goto L_0x076a
        L_0x0181:
            qg r12 = r1.a
            qg r13 = r1.c
            qg r6 = r1.b
            qg r5 = r1.d
            qg r2 = r1.e
            float r4 = r1.k
            qg r8 = r1.f
            qg r8 = r1.g
            int[] r8 = r0.aq
            r8 = r8[r43]
            if (r43 != 0) goto L_0x01b9
            int r3 = r2.aj
            if (r3 != 0) goto L_0x019e
            r23 = r7
            goto L_0x01a0
        L_0x019e:
            r23 = 0
        L_0x01a0:
            if (r3 != r7) goto L_0x01a6
            r21 = r7
            r7 = 2
            goto L_0x01a9
        L_0x01a6:
            r7 = 2
            r21 = 0
        L_0x01a9:
            if (r3 != r7) goto L_0x01ad
            r3 = 1
            goto L_0x01ae
        L_0x01ad:
            r3 = 0
        L_0x01ae:
            r25 = r4
            r27 = r9
            r7 = r12
            r26 = r23
            r4 = 0
            r23 = r21
            goto L_0x01d9
        L_0x01b9:
            r7 = 2
            int r3 = r2.ak
            r7 = 1
            if (r3 != 0) goto L_0x01c2
            r23 = 1
            goto L_0x01c4
        L_0x01c2:
            r23 = 0
        L_0x01c4:
            r25 = r4
            r4 = 2
            if (r3 != r7) goto L_0x01cb
            r7 = 1
            goto L_0x01cc
        L_0x01cb:
            r7 = 0
        L_0x01cc:
            if (r3 != r4) goto L_0x01d0
            r3 = 1
            goto L_0x01d1
        L_0x01d0:
            r3 = 0
        L_0x01d1:
            r27 = r9
            r26 = r23
            r4 = 0
            r23 = r7
            r7 = r12
        L_0x01d9:
            if (r4 != 0) goto L_0x02b3
            int r4 = r16 + 1
            qf[] r9 = r7.R
            r9 = r9[r16]
            r11 = 1
            if (r11 == r3) goto L_0x01e7
            r29 = 4
            goto L_0x01e9
        L_0x01e7:
            r29 = 1
        L_0x01e9:
            int r11 = r9.b()
            r30 = r14
            int[] r14 = r7.aq
            r14 = r14[r43]
            r31 = r15
            r15 = 3
            if (r14 != r15) goto L_0x0200
            int[] r14 = r7.u
            r14 = r14[r43]
            if (r14 != 0) goto L_0x0200
            r14 = 1
            goto L_0x0201
        L_0x0200:
            r14 = 0
        L_0x0201:
            qf r15 = r9.e
            if (r15 == 0) goto L_0x020d
            if (r7 == r12) goto L_0x020d
            int r32 = r15.b()
            int r11 = r11 + r32
        L_0x020d:
            if (r3 == 0) goto L_0x0215
            if (r7 == r12) goto L_0x0215
            if (r7 == r6) goto L_0x0215
            r29 = 8
        L_0x0215:
            if (r15 == 0) goto L_0x0251
            if (r7 != r6) goto L_0x0226
            r32 = r2
            qb r2 = r9.h
            qb r15 = r15.h
            r33 = r12
            r12 = 6
            r10.g(r2, r15, r11, r12)
            goto L_0x0233
        L_0x0226:
            r32 = r2
            r33 = r12
            qb r2 = r9.h
            qb r12 = r15.h
            r15 = 8
            r10.g(r2, r12, r11, r15)
        L_0x0233:
            if (r14 == 0) goto L_0x0239
            if (r3 != 0) goto L_0x0239
            r29 = 5
        L_0x0239:
            if (r7 != r6) goto L_0x0245
            if (r3 == 0) goto L_0x0245
            boolean[] r2 = r7.T
            boolean r2 = r2[r43]
            if (r2 == 0) goto L_0x0245
            r2 = 5
            goto L_0x0247
        L_0x0245:
            r2 = r29
        L_0x0247:
            qb r12 = r9.h
            qf r9 = r9.e
            qb r9 = r9.h
            r10.m(r12, r9, r11, r2)
            goto L_0x0255
        L_0x0251:
            r32 = r2
            r33 = r12
        L_0x0255:
            r2 = 2
            if (r8 != r2) goto L_0x0287
            int r2 = r7.ah
            r9 = 8
            if (r2 == r9) goto L_0x0275
            int[] r2 = r7.aq
            r2 = r2[r43]
            r9 = 3
            if (r2 != r9) goto L_0x0275
            qf[] r2 = r7.R
            r9 = r2[r4]
            qb r9 = r9.h
            r2 = r2[r16]
            qb r2 = r2.h
            r11 = 5
            r12 = 0
            r10.g(r9, r2, r12, r11)
            goto L_0x0276
        L_0x0275:
            r12 = 0
        L_0x0276:
            qf[] r2 = r7.R
            r2 = r2[r16]
            qb r2 = r2.h
            qf[] r9 = r0.R
            r9 = r9[r16]
            qb r9 = r9.h
            r11 = 8
            r10.g(r2, r9, r12, r11)
        L_0x0287:
            qf[] r2 = r7.R
            r2 = r2[r4]
            qf r2 = r2.e
            if (r2 == 0) goto L_0x029d
            qg r2 = r2.d
            qf[] r4 = r2.R
            r4 = r4[r16]
            qf r4 = r4.e
            if (r4 == 0) goto L_0x029d
            qg r4 = r4.d
            if (r4 == r7) goto L_0x029f
        L_0x029d:
            r2 = r17
        L_0x029f:
            if (r2 == 0) goto L_0x02a3
            r4 = 0
            goto L_0x02a4
        L_0x02a3:
            r4 = 1
        L_0x02a4:
            if (r2 == 0) goto L_0x02a7
            r7 = r2
        L_0x02a7:
            r11 = r42
            r14 = r30
            r15 = r31
            r2 = r32
            r12 = r33
            goto L_0x01d9
        L_0x02b3:
            r32 = r2
            r33 = r12
            r30 = r14
            r31 = r15
            if (r5 == 0) goto L_0x0318
            int r2 = r16 + 1
            qf[] r4 = r13.R
            r4 = r4[r2]
            qf r4 = r4.e
            if (r4 == 0) goto L_0x0318
            qf[] r4 = r5.R
            r4 = r4[r2]
            int[] r7 = r5.aq
            r7 = r7[r43]
            r9 = 3
            if (r7 != r9) goto L_0x02ee
            int[] r7 = r5.u
            r7 = r7[r43]
            if (r7 != 0) goto L_0x02ee
            if (r3 != 0) goto L_0x02ee
            qf r7 = r4.e
            qg r9 = r7.d
            if (r9 != r0) goto L_0x02ee
            qb r9 = r4.h
            qb r7 = r7.h
            int r11 = r4.b()
            int r11 = -r11
            r12 = 5
            r10.m(r9, r7, r11, r12)
            goto L_0x0304
        L_0x02ee:
            r12 = 5
            if (r3 == 0) goto L_0x0304
            qf r7 = r4.e
            qg r9 = r7.d
            if (r9 != r0) goto L_0x0304
            qb r9 = r4.h
            qb r7 = r7.h
            int r11 = r4.b()
            int r11 = -r11
            r14 = 4
            r10.m(r9, r7, r11, r14)
        L_0x0304:
            qb r7 = r4.h
            qf[] r9 = r13.R
            r2 = r9[r2]
            qf r2 = r2.e
            qb r2 = r2.h
            int r4 = r4.b()
            int r4 = -r4
            r9 = 6
            r10.h(r7, r2, r4, r9)
            goto L_0x0319
        L_0x0318:
            r12 = 5
        L_0x0319:
            r11 = 2
            if (r8 != r11) goto L_0x0333
            int r2 = r16 + 1
            qf[] r4 = r0.R
            r4 = r4[r2]
            qb r4 = r4.h
            qf[] r7 = r13.R
            r2 = r7[r2]
            qb r7 = r2.h
            int r2 = r2.b()
            r8 = 8
            r10.g(r4, r7, r2, r8)
        L_0x0333:
            java.util.ArrayList r2 = r1.h
            if (r2 == 0) goto L_0x0451
            int r4 = r2.size()
            r7 = 1
            if (r4 <= r7) goto L_0x0451
            boolean r7 = r1.q
            if (r7 == 0) goto L_0x034a
            boolean r7 = r1.s
            if (r7 != 0) goto L_0x034a
            int r7 = r1.j
            float r7 = (float) r7
            goto L_0x034c
        L_0x034a:
            r7 = r25
        L_0x034c:
            r14 = r17
            r8 = 0
            r9 = 0
        L_0x0350:
            if (r8 >= r4) goto L_0x0451
            java.lang.Object r15 = r2.get(r8)
            qg r15 = (defpackage.qg) r15
            float[] r11 = r15.al
            r11 = r11[r43]
            r20 = 0
            int r22 = (r11 > r20 ? 1 : (r11 == r20 ? 0 : -1))
            if (r22 >= 0) goto L_0x037d
            boolean r11 = r1.s
            if (r11 == 0) goto L_0x0379
            int r11 = r16 + 1
            qf[] r12 = r15.R
            r11 = r12[r11]
            qb r11 = r11.h
            r12 = r12[r16]
            qb r12 = r12.h
            r0 = 0
            r15 = 4
            r10.m(r11, r12, r0, r15)
            r15 = r0
            goto L_0x0396
        L_0x0379:
            r0 = 4
            r11 = 1065353216(0x3f800000, float:1.0)
            goto L_0x037e
        L_0x037d:
            r0 = 4
        L_0x037e:
            r20 = 0
            int r22 = (r11 > r20 ? 1 : (r11 == r20 ? 0 : -1))
            if (r22 != 0) goto L_0x03a0
            int r11 = r16 + 1
            qf[] r12 = r15.R
            r11 = r12[r11]
            qb r11 = r11.h
            r12 = r12[r16]
            qb r12 = r12.h
            r0 = 8
            r15 = 0
            r10.m(r11, r12, r15, r0)
        L_0x0396:
            r34 = r2
            r19 = r4
            r37 = r7
            r20 = 0
            goto L_0x0443
        L_0x03a0:
            r0 = 0
            if (r14 == 0) goto L_0x0436
            int r19 = r16 + 1
            qf[] r14 = r14.R
            r0 = r14[r16]
            qb r0 = r0.h
            r14 = r14[r19]
            qb r14 = r14.h
            qf[] r12 = r15.R
            r34 = r2
            r2 = r12[r16]
            qb r2 = r2.h
            r12 = r12[r19]
            qb r12 = r12.h
            r19 = r4
            pw r4 = r41.a()
            r35 = r15
            r15 = 0
            r4.b = r15
            int r20 = (r7 > r15 ? 1 : (r7 == r15 ? 0 : -1))
            r15 = -1082130432(0xffffffffbf800000, float:-1.0)
            if (r20 == 0) goto L_0x0416
            int r20 = (r9 > r11 ? 1 : (r9 == r11 ? 0 : -1))
            if (r20 != 0) goto L_0x03d1
            goto L_0x0416
        L_0x03d1:
            r20 = 0
            int r36 = (r9 > r20 ? 1 : (r9 == r20 ? 0 : -1))
            if (r36 != 0) goto L_0x03e6
            pv r2 = r4.e
            r9 = 1065353216(0x3f800000, float:1.0)
            r2.g(r0, r9)
            pv r0 = r4.e
            r0.g(r14, r15)
        L_0x03e3:
            r37 = r7
            goto L_0x0432
        L_0x03e6:
            r15 = 1065353216(0x3f800000, float:1.0)
            if (r22 != 0) goto L_0x03f7
            pv r0 = r4.e
            r0.g(r2, r15)
            pv r0 = r4.e
            r2 = -1082130432(0xffffffffbf800000, float:-1.0)
            r0.g(r12, r2)
            goto L_0x03e3
        L_0x03f7:
            float r9 = r9 / r7
            float r22 = r11 / r7
            r37 = r7
            pv r7 = r4.e
            r7.g(r0, r15)
            pv r0 = r4.e
            r7 = -1082130432(0xffffffffbf800000, float:-1.0)
            r0.g(r14, r7)
            pv r0 = r4.e
            float r9 = r9 / r22
            r0.g(r12, r9)
            pv r0 = r4.e
            float r7 = -r9
            r0.g(r2, r7)
            goto L_0x0432
        L_0x0416:
            r37 = r7
            r20 = 0
            pv r7 = r4.e
            r9 = 1065353216(0x3f800000, float:1.0)
            r7.g(r0, r9)
            pv r0 = r4.e
            r7 = -1082130432(0xffffffffbf800000, float:-1.0)
            r0.g(r14, r7)
            pv r0 = r4.e
            r0.g(r12, r9)
            pv r0 = r4.e
            r0.g(r2, r7)
        L_0x0432:
            r10.e(r4)
            goto L_0x0440
        L_0x0436:
            r34 = r2
            r19 = r4
            r37 = r7
            r35 = r15
            r20 = 0
        L_0x0440:
            r9 = r11
            r14 = r35
        L_0x0443:
            int r8 = r8 + 1
            r4 = r19
            r2 = r34
            r7 = r37
            r11 = 2
            r12 = 5
            r0 = r40
            goto L_0x0350
        L_0x0451:
            if (r6 == 0) goto L_0x04b1
            if (r6 == r5) goto L_0x0457
            if (r3 == 0) goto L_0x04b1
        L_0x0457:
            int r0 = r16 + 1
            r11 = r33
            qf[] r1 = r11.R
            r1 = r1[r16]
            qf[] r2 = r13.R
            r2 = r2[r0]
            qf r1 = r1.e
            if (r1 == 0) goto L_0x046b
            qb r1 = r1.h
            r3 = r1
            goto L_0x046d
        L_0x046b:
            r3 = r17
        L_0x046d:
            qf r1 = r2.e
            if (r1 == 0) goto L_0x0475
            qb r1 = r1.h
            r7 = r1
            goto L_0x0477
        L_0x0475:
            r7 = r17
        L_0x0477:
            qf[] r1 = r6.R
            r1 = r1[r16]
            if (r5 == 0) goto L_0x0481
            qf[] r2 = r5.R
            r2 = r2[r0]
        L_0x0481:
            if (r3 == 0) goto L_0x04ad
            if (r7 == 0) goto L_0x04ad
            if (r43 != 0) goto L_0x048c
            r0 = r32
            float r0 = r0.ae
            goto L_0x0490
        L_0x048c:
            r0 = r32
            float r0 = r0.af
        L_0x0490:
            int r4 = r1.b()
            int r8 = r2.b()
            qb r9 = r1.h
            qb r11 = r2.h
            r12 = 7
            r1 = r41
            r2 = r9
            r14 = r5
            r5 = r0
            r0 = r6
            r6 = r7
            r7 = r11
            r15 = r27
            r9 = r12
            r1.d(r2, r3, r4, r5, r6, r7, r8, r9)
            goto L_0x05ba
        L_0x04ad:
            r14 = r5
            r0 = r6
            goto L_0x05ba
        L_0x04b1:
            r14 = r5
            r0 = r6
            r15 = r27
            r11 = r33
            if (r26 == 0) goto L_0x05c5
            if (r0 == 0) goto L_0x05bd
            int r2 = r1.j
            if (r2 <= 0) goto L_0x04c5
            int r1 = r1.i
            if (r1 != r2) goto L_0x04c5
            r12 = 1
            goto L_0x04c6
        L_0x04c5:
            r12 = 0
        L_0x04c6:
            r8 = r0
            r9 = r8
        L_0x04c8:
            if (r9 == 0) goto L_0x05b8
            qg[] r1 = r9.an
            r1 = r1[r43]
            r7 = r1
        L_0x04cf:
            if (r7 == 0) goto L_0x04dc
            int r1 = r7.ah
            r6 = 8
            if (r1 != r6) goto L_0x04de
            qg[] r1 = r7.an
            r7 = r1[r43]
            goto L_0x04cf
        L_0x04dc:
            r6 = 8
        L_0x04de:
            if (r7 != 0) goto L_0x04ef
            if (r9 != r14) goto L_0x04e3
            goto L_0x04ef
        L_0x04e3:
            r19 = r7
            r20 = r8
            r18 = r12
            r27 = r15
        L_0x04eb:
            r15 = 5
            r12 = r9
            goto L_0x05a6
        L_0x04ef:
            int r1 = r16 + 1
            qf[] r2 = r9.R
            r2 = r2[r16]
            qb r3 = r2.h
            qf r4 = r2.e
            if (r4 == 0) goto L_0x04fe
            qb r4 = r4.h
            goto L_0x0500
        L_0x04fe:
            r4 = r17
        L_0x0500:
            if (r8 == r9) goto L_0x0509
            qf[] r4 = r8.R
            r4 = r4[r1]
            qb r4 = r4.h
            goto L_0x0518
        L_0x0509:
            if (r9 != r0) goto L_0x0518
            qf[] r4 = r11.R
            r4 = r4[r16]
            qf r4 = r4.e
            if (r4 == 0) goto L_0x0516
            qb r4 = r4.h
            goto L_0x0518
        L_0x0516:
            r4 = r17
        L_0x0518:
            int r2 = r2.b()
            qf[] r5 = r9.R
            r5 = r5[r1]
            int r5 = r5.b()
            if (r7 == 0) goto L_0x0531
            qf[] r6 = r7.R
            r6 = r6[r16]
            r19 = r7
            qb r7 = r6.h
        L_0x052e:
            r27 = r15
            goto L_0x0542
        L_0x0531:
            r19 = r7
            qf[] r6 = r13.R
            r6 = r6[r1]
            qf r6 = r6.e
            if (r6 == 0) goto L_0x053e
            qb r7 = r6.h
            goto L_0x052e
        L_0x053e:
            r27 = r15
            r7 = r17
        L_0x0542:
            qf[] r15 = r9.R
            r15 = r15[r1]
            qb r15 = r15.h
            if (r6 == 0) goto L_0x054f
            int r6 = r6.b()
            int r5 = r5 + r6
        L_0x054f:
            qf[] r6 = r8.R
            r6 = r6[r1]
            int r6 = r6.b()
            int r2 = r2 + r6
            if (r3 == 0) goto L_0x05a0
            if (r4 == 0) goto L_0x05a0
            if (r7 == 0) goto L_0x05a0
            if (r15 == 0) goto L_0x05a0
            if (r9 != r0) goto L_0x056a
            qf[] r2 = r0.R
            r2 = r2[r16]
            int r2 = r2.b()
        L_0x056a:
            r6 = r2
            if (r9 != r14) goto L_0x0578
            qf[] r2 = r14.R
            r1 = r2[r1]
            int r1 = r1.b()
            r20 = r1
            goto L_0x057a
        L_0x0578:
            r20 = r5
        L_0x057a:
            r5 = 1
            if (r5 == r12) goto L_0x0580
            r22 = 5
            goto L_0x0582
        L_0x0580:
            r22 = 8
        L_0x0582:
            r24 = 1056964608(0x3f000000, float:0.5)
            r1 = r41
            r2 = r3
            r3 = r4
            r4 = r6
            r28 = r5
            r5 = r24
            r18 = 8
            r6 = r7
            r7 = r15
            r15 = r8
            r8 = r20
            r18 = r12
            r20 = r15
            r15 = 5
            r12 = r9
            r9 = r22
            r1.d(r2, r3, r4, r5, r6, r7, r8, r9)
            goto L_0x05a6
        L_0x05a0:
            r20 = r8
            r18 = r12
            goto L_0x04eb
        L_0x05a6:
            int r1 = r12.ah
            r9 = 8
            if (r1 == r9) goto L_0x05ae
            r8 = r12
            goto L_0x05b0
        L_0x05ae:
            r8 = r20
        L_0x05b0:
            r12 = r18
            r9 = r19
            r15 = r27
            goto L_0x04c8
        L_0x05b8:
            r27 = r15
        L_0x05ba:
            r6 = r0
            goto L_0x070e
        L_0x05bd:
            r27 = r15
            r9 = 8
            r15 = 5
            r12 = r17
            goto L_0x05cb
        L_0x05c5:
            r27 = r15
            r9 = 8
            r15 = 5
            r12 = r0
        L_0x05cb:
            if (r23 == 0) goto L_0x070a
            if (r0 == 0) goto L_0x070a
            int r18 = r16 + 1
            int r2 = r1.j
            if (r2 <= 0) goto L_0x05db
            int r1 = r1.i
            if (r1 != r2) goto L_0x05db
            r8 = 1
            goto L_0x05dc
        L_0x05db:
            r8 = 0
        L_0x05dc:
            r6 = r0
            r7 = r6
        L_0x05de:
            if (r7 == 0) goto L_0x06b3
            qg[] r1 = r7.an
            r1 = r1[r43]
        L_0x05e4:
            if (r1 == 0) goto L_0x05ef
            int r2 = r1.ah
            if (r2 != r9) goto L_0x05ef
            qg[] r1 = r1.an
            r1 = r1[r43]
            goto L_0x05e4
        L_0x05ef:
            if (r7 == r0) goto L_0x0696
            if (r7 == r14) goto L_0x0696
            if (r1 == 0) goto L_0x0696
            if (r1 != r14) goto L_0x05fa
            r5 = r17
            goto L_0x05fb
        L_0x05fa:
            r5 = r1
        L_0x05fb:
            qf[] r1 = r7.R
            r1 = r1[r16]
            qb r2 = r1.h
            qf r3 = r1.e
            qf[] r3 = r6.R
            r3 = r3[r18]
            qb r3 = r3.h
            int r1 = r1.b()
            qf[] r4 = r7.R
            r4 = r4[r18]
            int r4 = r4.b()
            if (r5 == 0) goto L_0x062e
            qf[] r9 = r5.R
            r9 = r9[r16]
            qb r15 = r9.h
            r19 = r5
            qf r5 = r9.e
            if (r5 == 0) goto L_0x0626
            qb r5 = r5.h
            goto L_0x0628
        L_0x0626:
            r5 = r17
        L_0x0628:
            r39 = r9
            r9 = r5
            r5 = r39
            goto L_0x0647
        L_0x062e:
            r19 = r5
            qf[] r5 = r14.R
            r9 = r5[r16]
            if (r9 == 0) goto L_0x0639
            qb r5 = r9.h
            goto L_0x063b
        L_0x0639:
            r5 = r17
        L_0x063b:
            qf[] r15 = r7.R
            r15 = r15[r18]
            qb r15 = r15.h
            r39 = r15
            r15 = r5
            r5 = r9
            r9 = r39
        L_0x0647:
            if (r5 == 0) goto L_0x064e
            int r5 = r5.b()
            int r4 = r4 + r5
        L_0x064e:
            r20 = r4
            qf[] r4 = r6.R
            r4 = r4[r18]
            int r4 = r4.b()
            int r4 = r4 + r1
            r5 = 1
            if (r5 == r8) goto L_0x065f
            r22 = 4
            goto L_0x0661
        L_0x065f:
            r22 = 8
        L_0x0661:
            if (r2 == 0) goto L_0x0686
            if (r3 == 0) goto L_0x0686
            if (r15 == 0) goto L_0x0686
            if (r9 == 0) goto L_0x0686
            r24 = 1056964608(0x3f000000, float:0.5)
            r1 = r41
            r28 = r5
            r5 = r24
            r24 = r6
            r6 = r15
            r15 = r7
            r7 = r9
            r38 = r8
            r8 = r20
            r29 = r12
            r12 = 8
            r20 = 4
            r9 = r22
            r1.d(r2, r3, r4, r5, r6, r7, r8, r9)
            goto L_0x0693
        L_0x0686:
            r28 = r5
            r24 = r6
            r15 = r7
            r38 = r8
            r29 = r12
            r12 = 8
            r20 = 4
        L_0x0693:
            r7 = r19
            goto L_0x06a3
        L_0x0696:
            r24 = r6
            r15 = r7
            r38 = r8
            r29 = r12
            r20 = 4
            r28 = 1
            r12 = r9
            r7 = r1
        L_0x06a3:
            int r1 = r15.ah
            if (r1 == r12) goto L_0x06a9
            r6 = r15
            goto L_0x06ab
        L_0x06a9:
            r6 = r24
        L_0x06ab:
            r9 = r12
            r12 = r29
            r8 = r38
            r15 = 5
            goto L_0x05de
        L_0x06b3:
            r29 = r12
            qf[] r1 = r0.R
            r1 = r1[r16]
            qf[] r2 = r11.R
            r2 = r2[r16]
            qf r2 = r2.e
            qf[] r3 = r14.R
            r11 = r3[r18]
            qf[] r3 = r13.R
            r3 = r3[r18]
            qf r12 = r3.e
            if (r2 == 0) goto L_0x06f8
            if (r0 == r14) goto L_0x06da
            qb r3 = r1.h
            qb r2 = r2.h
            int r1 = r1.b()
            r4 = 5
            r10.m(r3, r2, r1, r4)
            goto L_0x06f8
        L_0x06da:
            if (r12 == 0) goto L_0x06f8
            qb r3 = r1.h
            qb r4 = r2.h
            int r5 = r1.b()
            qb r6 = r11.h
            qb r7 = r12.h
            int r8 = r11.b()
            r9 = 5
            r15 = 1056964608(0x3f000000, float:0.5)
            r1 = r41
            r2 = r3
            r3 = r4
            r4 = r5
            r5 = r15
            r1.d(r2, r3, r4, r5, r6, r7, r8, r9)
        L_0x06f8:
            if (r12 == 0) goto L_0x070c
            if (r0 == r14) goto L_0x070c
            qb r0 = r11.h
            qb r1 = r12.h
            int r2 = r11.b()
            int r2 = -r2
            r3 = 5
            r10.m(r0, r1, r2, r3)
            goto L_0x070c
        L_0x070a:
            r29 = r12
        L_0x070c:
            r6 = r29
        L_0x070e:
            if (r26 != 0) goto L_0x0712
            if (r23 == 0) goto L_0x076a
        L_0x0712:
            if (r6 == 0) goto L_0x076a
            if (r6 == r14) goto L_0x076a
            int r0 = r16 + 1
            qf[] r1 = r6.R
            r2 = r1[r16]
            if (r14 != 0) goto L_0x0720
            r5 = r6
            goto L_0x0721
        L_0x0720:
            r5 = r14
        L_0x0721:
            qf[] r3 = r5.R
            r3 = r3[r0]
            qf r4 = r2.e
            if (r4 == 0) goto L_0x072c
            qb r4 = r4.h
            goto L_0x072e
        L_0x072c:
            r4 = r17
        L_0x072e:
            qf r7 = r3.e
            if (r7 == 0) goto L_0x0735
            qb r7 = r7.h
            goto L_0x0737
        L_0x0735:
            r7 = r17
        L_0x0737:
            if (r13 == r5) goto L_0x0746
            qf[] r7 = r13.R
            r7 = r7[r0]
            qf r7 = r7.e
            if (r7 == 0) goto L_0x0744
            qb r7 = r7.h
            goto L_0x0746
        L_0x0744:
            r7 = r17
        L_0x0746:
            if (r6 != r5) goto L_0x074a
            r3 = r1[r0]
        L_0x074a:
            if (r4 == 0) goto L_0x076a
            if (r7 == 0) goto L_0x076a
            int r6 = r2.b()
            qf[] r1 = r5.R
            r0 = r1[r0]
            int r8 = r0.b()
            qb r2 = r2.h
            qb r0 = r3.h
            r9 = 5
            r5 = 1056964608(0x3f000000, float:0.5)
            r1 = r41
            r3 = r4
            r4 = r6
            r6 = r7
            r7 = r0
            r1.d(r2, r3, r4, r5, r6, r7, r8, r9)
        L_0x076a:
            int r9 = r27 + 1
            r12 = 2
            r0 = r40
            r11 = r42
            r14 = r30
            r15 = r31
            goto L_0x001b
        L_0x0777:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.kf.l(qh, px, java.util.ArrayList, int):void");
    }

    public kf(qg qgVar) {
        new WeakReference(qgVar);
        px.o(qgVar.f39J);
        px.o(qgVar.K);
        px.o(qgVar.L);
        px.o(qgVar.M);
        px.o(qgVar.N);
    }
}
