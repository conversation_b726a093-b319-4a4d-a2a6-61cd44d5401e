package defpackage;

import java.util.Arrays;

/* renamed from: ake  reason: default package */
/* compiled from: PG */
public final class ake {
    public final long a;
    public final aex b;
    public final int c;
    public final ank d;
    public final long e;
    public final aex f;
    public final int g;
    public final ank h;
    public final long i;
    public final long j;

    public ake(long j2, aex aex, int i2, ank ank, long j3, aex aex2, int i3, ank ank2, long j4, long j5) {
        this.a = j2;
        this.b = aex;
        this.c = i2;
        this.d = ank;
        this.e = j3;
        this.f = aex2;
        this.g = i3;
        this.h = ank2;
        this.i = j4;
        this.j = j5;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj != null && getClass() == obj.getClass()) {
            ake ake = (ake) obj;
            if (this.a == ake.a && this.c == ake.c && this.e == ake.e && this.g == ake.g && this.i == ake.i && this.j == ake.j && a.k(this.b, ake.b) && a.k(this.d, ake.d) && a.k(this.f, ake.f) && a.k(this.h, ake.h)) {
                return true;
            }
            return false;
        }
        return false;
    }

    public final int hashCode() {
        return Arrays.hashCode(new Object[]{Long.valueOf(this.a), this.b, Integer.valueOf(this.c), this.d, Long.valueOf(this.e), this.f, Integer.valueOf(this.g), this.h, Long.valueOf(this.i), Long.valueOf(this.j)});
    }
}
