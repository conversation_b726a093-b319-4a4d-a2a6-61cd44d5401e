package defpackage;

import android.content.Context;
import android.content.Intent;
import androidx.work.impl.background.systemalarm.SystemAlarmService;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/* renamed from: bee  reason: default package */
/* compiled from: PG */
public final class bee implements bce {
    public static final String a = bbk.b("CommandHandler");
    public final Context b;
    public final Map c = new HashMap();
    public final Object d = new Object();
    public final bxq e;

    public bee(Context context, bxq bxq) {
        this.b = context;
        this.e = bxq;
    }

    static Intent b(Context context) {
        Intent intent = new Intent(context, SystemAlarmService.class);
        intent.setAction("ACTION_CONSTRAINTS_CHANGED");
        return intent;
    }

    static Intent c(Context context, bgt bgt) {
        Intent intent = new Intent(context, SystemAlarmService.class);
        intent.setAction("ACTION_DELAY_MET");
        f(intent, bgt);
        return intent;
    }

    public static Intent d(Context context, bgt bgt) {
        Intent intent = new Intent(context, SystemAlarmService.class);
        intent.setAction("ACTION_SCHEDULE_WORK");
        f(intent, bgt);
        return intent;
    }

    static bgt e(Intent intent) {
        return new bgt(intent.getStringExtra("KEY_WORKSPEC_ID"), intent.getIntExtra("KEY_WORKSPEC_GENERATION", 0));
    }

    public static void f(Intent intent, bgt bgt) {
        intent.putExtra("KEY_WORKSPEC_ID", bgt.a);
        intent.putExtra("KEY_WORKSPEC_GENERATION", bgt.b);
    }

    public final void a(bgt bgt, boolean z) {
        synchronized (this.d) {
            beh beh = (beh) this.c.remove(bgt);
            this.e.H(bgt);
            if (beh != null) {
                bbk.a();
                Objects.toString(beh.c);
                beh.a();
                if (z) {
                    beh.g.execute(new bej(beh.d, d(beh.a, beh.c), beh.b));
                }
                if (beh.i) {
                    beh.g.execute(new bej(beh.d, b(beh.a), beh.b));
                }
            }
        }
    }
}
