package defpackage;

import androidx.preference.Preference;

/* renamed from: cqn  reason: default package */
/* compiled from: PG */
public final class cqn {
    public final String a;
    public final int b;
    public final boolean c;

    public cqn() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof cqn) {
            cqn cqn = (cqn) obj;
            if (this.a.equals(cqn.a) && this.b == cqn.b && this.c == cqn.c) {
                return true;
            }
            return false;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        int hashCode = this.a.hashCode() ^ 1000003;
        if (true != this.c) {
            i = 1237;
        } else {
            i = 1231;
        }
        return (((((((((hashCode * 1000003) ^ this.b) * 1000003) ^ Preference.DEFAULT_ORDER) * 1000003) ^ Preference.DEFAULT_ORDER) * 1000003) ^ 1231) * 1000003) ^ i;
    }

    public final String toString() {
        return "ThreadPoolConfig{name=" + this.a + ", numThreads=" + this.b + ", maxThreadSize=2147483647, maxQueueSize=2147483647, enableStats=true, enabledMetrics=" + this.c + "}";
    }

    public cqn(String str, int i, boolean z) {
        this.a = str;
        this.b = i;
        this.c = z;
    }
}
