package defpackage;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Executor;

/* renamed from: epe  reason: default package */
/* compiled from: PG */
public final class epe {
    private static final hca f = hca.m("com/google/android/libraries/search/audio/microphone/source/registry/AudioSourceDataRegistry");
    public final Executor a;
    public final hll b;
    public final Set c = new LinkedHashSet();
    public final cxi d;
    public final cyw e;
    private final emt g;
    private final emr h;
    private final jqs i;
    private final boolean j;
    private final List k = new ArrayList();
    private final dou l;
    private final cxp m;
    private final fnn n;
    private final bmu o;
    private final bmu p;
    private final byw q;

    public epe(emt emt, bmu bmu, fnn fnn, emr emr, dou dou, cxp cxp, Executor executor, jqs jqs, cyw cyw, hll hll, bmu bmu2, boolean z, cxi cxi, byw byw) {
        jnu.e(emr, "audioSessionToMicStateUpdater");
        jnu.e(executor, "lightweightExecutor");
        jnu.e(jqs, "lightweightScope");
        jnu.e(hll, "audioExecutionSequencer");
        jnu.e(byw, "sessionAudioStoreFactory");
        this.g = emt;
        this.p = bmu;
        this.n = fnn;
        this.h = emr;
        this.l = dou;
        this.m = cxp;
        this.a = executor;
        this.i = jqs;
        this.e = cyw;
        this.b = hll;
        this.o = bmu2;
        this.j = z;
        this.d = cxi;
        this.q = byw;
    }

    public final synchronized dyx a(int i2, eam eam) {
        Object obj;
        jnu.e(eam, "reason");
        Iterator it = this.k.iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            if (((epc) obj).c.a == i2) {
                break;
            }
        }
        epc epc = (epc) obj;
        if (epc == null) {
            ((hby) f.f().h(hdg.a, "ALT.AudioSrcDataRegy").j("com/google/android/libraries/search/audio/microphone/source/registry/AudioSourceDataRegistry", "stopAudioSource$java_com_google_android_libraries_search_audio_microphone_source_registry_audio_source_data_registry", 419, "AudioSourceDataRegistry.kt")).x("#audio# no audio source session(%d) to stop(%s), skipping", i2, eam.name());
            return new ekd(eag.FAILED_CLOSING_NO_AUDIO_SOURCE, eam);
        }
        jji.N(this.k, new eoh(i2, 3));
        ((hby) f.f().h(hdg.a, "ALT.AudioSrcDataRegy").j("com/google/android/libraries/search/audio/microphone/source/registry/AudioSourceDataRegistry", "markStopped", 429, "AudioSourceDataRegistry.kt")).A("#audio# stopping(%s) audio source session(%d)", eam.name(), epc.c.a);
        hme b2 = this.b.b(gof.c(new ejj(epc, eam, 2)), this.a);
        jnu.d(b2, "submitAsync(...)");
        return new eke(b2, eam, this.a);
    }

    public final synchronized void b(jna jna) {
        this.c.add(jna);
    }

    /* JADX WARNING: type inference failed for: r5v8, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r2v17, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r3v20, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v30, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r3v24, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r5v27, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r5v31, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r1v84, types: [java.lang.Object, java.lang.Iterable] */
    /* JADX WARNING: type inference failed for: r3v36, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v58, types: [java.lang.Object, java.lang.Iterable] */
    /* JADX WARNING: type inference failed for: r1v100, types: [java.lang.Object, jna] */
    /* JADX WARNING: Code restructure failed: missing block: B:36:0x016a, code lost:
        if (r12 == r6) goto L_0x016c;
     */
    /* JADX WARNING: Removed duplicated region for block: B:151:0x04c2 A[Catch:{ all -> 0x039d, all -> 0x09f4 }] */
    /* JADX WARNING: Removed duplicated region for block: B:316:0x0917 A[Catch:{ all -> 0x039d, all -> 0x09f4 }] */
    /* JADX WARNING: Removed duplicated region for block: B:320:0x096a A[Catch:{ all -> 0x039d, all -> 0x09f4 }] */
    /* JADX WARNING: Removed duplicated region for block: B:59:0x02b2 A[Catch:{ all -> 0x039d, all -> 0x09f4 }] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final synchronized defpackage.dyy c(defpackage.epb r34, defpackage.epp r35, defpackage.epg r36, defpackage.dsy r37) {
        /*
            r33 = this;
            r8 = r33
            r9 = r34
            r10 = r35
            r11 = r36
            r7 = r37
            monitor-enter(r33)
            hca r1 = f     // Catch:{ all -> 0x09f4 }
            hco r2 = r1.f()     // Catch:{ all -> 0x09f4 }
            hcr r3 = defpackage.hdg.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "ALT.AudioSrcDataRegy"
            hco r2 = r2.h(r3, r4)     // Catch:{ all -> 0x09f4 }
            java.lang.String r3 = "AudioSourceDataRegistry.kt"
            java.lang.String r4 = "com/google/android/libraries/search/audio/microphone/source/registry/AudioSourceDataRegistry"
            java.lang.String r5 = "startAudioSessionSource$java_com_google_android_libraries_search_audio_microphone_source_registry_audio_source_data_registry"
            r6 = 124(0x7c, float:1.74E-43)
            hco r2 = r2.j(r4, r5, r6, r3)     // Catch:{ all -> 0x09f4 }
            hby r2 = (defpackage.hby) r2     // Catch:{ all -> 0x09f4 }
            int r3 = r11.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "#audio# starting audio source session(%d) on %s for %s"
            java.lang.Integer r3 = java.lang.Integer.valueOf(r3)     // Catch:{ all -> 0x09f4 }
            java.lang.String r5 = r35.a()     // Catch:{ all -> 0x09f4 }
            java.lang.String r6 = r34.a()     // Catch:{ all -> 0x09f4 }
            r2.G(r4, r3, r5, r6)     // Catch:{ all -> 0x09f4 }
            hco r1 = r1.f()     // Catch:{ all -> 0x09f4 }
            hcr r2 = defpackage.hdg.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r3 = "ALT.AudioSrcDataRegy"
            hco r1 = r1.h(r2, r3)     // Catch:{ all -> 0x09f4 }
            java.lang.String r2 = "AudioSourceDataRegistry.kt"
            java.lang.String r3 = "com/google/android/libraries/search/audio/microphone/source/registry/AudioSourceDataRegistry"
            java.lang.String r4 = "enforceConcurrencyStateOnNewAudioSource"
            r5 = 204(0xcc, float:2.86E-43)
            hco r1 = r1.j(r3, r4, r5, r2)     // Catch:{ all -> 0x09f4 }
            hby r1 = (defpackage.hby) r1     // Catch:{ all -> 0x09f4 }
            java.lang.String r2 = "#audio# enforcing concurrency state on a new audio source of %s"
            java.lang.String r3 = r34.a()     // Catch:{ all -> 0x09f4 }
            r1.u(r2, r3)     // Catch:{ all -> 0x09f4 }
            java.util.List r1 = r8.k     // Catch:{ all -> 0x09f4 }
            java.util.Iterator r1 = r1.iterator()     // Catch:{ all -> 0x09f4 }
        L_0x0063:
            boolean r2 = r1.hasNext()     // Catch:{ all -> 0x09f4 }
            if (r2 == 0) goto L_0x0079
            java.lang.Object r2 = r1.next()     // Catch:{ all -> 0x09f4 }
            r3 = r2
            epc r3 = (defpackage.epc) r3     // Catch:{ all -> 0x09f4 }
            epb r3 = r3.b     // Catch:{ all -> 0x09f4 }
            int r3 = r3.a     // Catch:{ all -> 0x09f4 }
            int r4 = r9.a     // Catch:{ all -> 0x09f4 }
            if (r3 != r4) goto L_0x0063
            goto L_0x007a
        L_0x0079:
            r2 = 0
        L_0x007a:
            epc r2 = (defpackage.epc) r2     // Catch:{ all -> 0x09f4 }
            if (r2 == 0) goto L_0x0087
            epg r1 = r2.c     // Catch:{ all -> 0x09f4 }
            int r1 = r1.a     // Catch:{ all -> 0x09f4 }
            eam r2 = defpackage.eam.UNEXPECTED_AUDIO_SOURCE_OPENED     // Catch:{ all -> 0x09f4 }
            r8.a(r1, r2)     // Catch:{ all -> 0x09f4 }
        L_0x0087:
            ejn r1 = r10.b     // Catch:{ all -> 0x09f4 }
            dze r2 = r11.b     // Catch:{ all -> 0x09f4 }
            hca r3 = defpackage.evd.a     // Catch:{ all -> 0x09f4 }
            ebh r1 = defpackage.evd.b(r1, r2)     // Catch:{ all -> 0x09f4 }
            r13 = 14
            if (r1 != 0) goto L_0x0098
        L_0x0095:
            r2 = 0
            goto L_0x02b0
        L_0x0098:
            java.lang.Object r2 = r7.b     // Catch:{ all -> 0x09f4 }
            java.util.Iterator r2 = r2.iterator()     // Catch:{ all -> 0x09f4 }
        L_0x009e:
            boolean r3 = r2.hasNext()     // Catch:{ all -> 0x09f4 }
            if (r3 == 0) goto L_0x01e5
            java.lang.Object r3 = r2.next()     // Catch:{ all -> 0x09f4 }
            r4 = r3
            epn r4 = (defpackage.epn) r4     // Catch:{ all -> 0x09f4 }
            dze r5 = r11.b     // Catch:{ all -> 0x09f4 }
            dyt r5 = r5.f     // Catch:{ all -> 0x09f4 }
            if (r5 != 0) goto L_0x00b3
            dyt r5 = defpackage.dyt.l     // Catch:{ all -> 0x09f4 }
        L_0x00b3:
            java.lang.String r6 = "getAudioLibInputParams(...)"
            defpackage.jnu.d(r5, r6)     // Catch:{ all -> 0x09f4 }
            enr r4 = r4.d     // Catch:{ all -> 0x09f4 }
            java.lang.String r6 = "expectedParams"
            defpackage.jnu.e(r5, r6)     // Catch:{ all -> 0x09f4 }
            r6 = r4
            eny r6 = (defpackage.eny) r6     // Catch:{ all -> 0x09f4 }
            grh r6 = r6.g     // Catch:{ all -> 0x09f4 }
            boolean r6 = r6.f()     // Catch:{ all -> 0x09f4 }
            if (r6 != 0) goto L_0x00ec
            hca r3 = defpackage.evd.a     // Catch:{ all -> 0x09f4 }
            hco r3 = r3.h()     // Catch:{ all -> 0x09f4 }
            hcr r4 = defpackage.hdg.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r5 = "ALT.SeamlessUtils"
            hco r3 = r3.h(r4, r5)     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "SeamlessUtils.kt"
            java.lang.String r5 = "com/google/android/libraries/search/audio/utils/SeamlessUtils"
            java.lang.String r6 = "canHandoff"
            r14 = 54
            hco r3 = r3.j(r5, r6, r14, r4)     // Catch:{ all -> 0x09f4 }
            hby r3 = (defpackage.hby) r3     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "#audio# can't handoff, no handover handler found"
            r3.r(r4)     // Catch:{ all -> 0x09f4 }
            goto L_0x009e
        L_0x00ec:
            r6 = r4
            eny r6 = (defpackage.eny) r6     // Catch:{ all -> 0x09f4 }
            grh r6 = r6.g     // Catch:{ all -> 0x09f4 }
            java.lang.Object r6 = r6.b()     // Catch:{ all -> 0x09f4 }
            ens r6 = (defpackage.ens) r6     // Catch:{ all -> 0x09f4 }
            int r6 = r6.b     // Catch:{ all -> 0x09f4 }
            int r14 = r1.a     // Catch:{ all -> 0x09f4 }
            if (r6 == r14) goto L_0x0120
            hca r3 = defpackage.evd.a     // Catch:{ all -> 0x09f4 }
            hco r3 = r3.h()     // Catch:{ all -> 0x09f4 }
            hcr r4 = defpackage.hdg.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r5 = "ALT.SeamlessUtils"
            hco r3 = r3.h(r4, r5)     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "SeamlessUtils.kt"
            java.lang.String r5 = "com/google/android/libraries/search/audio/utils/SeamlessUtils"
            java.lang.String r15 = "canHandoff"
            r12 = 62
            hco r3 = r3.j(r5, r15, r12, r4)     // Catch:{ all -> 0x09f4 }
            hby r3 = (defpackage.hby) r3     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "#audio# can't handoff, id mismatch: current(%d), expected(%d)"
            r3.v(r4, r6, r14)     // Catch:{ all -> 0x09f4 }
            goto L_0x009e
        L_0x0120:
            eny r4 = (defpackage.eny) r4     // Catch:{ all -> 0x09f4 }
            dyt r4 = r4.h     // Catch:{ all -> 0x09f4 }
            int r6 = r4.c     // Catch:{ all -> 0x09f4 }
            int r12 = r5.c     // Catch:{ all -> 0x09f4 }
            if (r6 == r12) goto L_0x014d
            hca r3 = defpackage.evd.a     // Catch:{ all -> 0x09f4 }
            hco r3 = r3.h()     // Catch:{ all -> 0x09f4 }
            hcr r4 = defpackage.hdg.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r5 = "ALT.SeamlessUtils"
            hco r3 = r3.h(r4, r5)     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "SeamlessUtils.kt"
            java.lang.String r5 = "com/google/android/libraries/search/audio/utils/SeamlessUtils"
            java.lang.String r14 = "canHandoff"
            r15 = 71
            hco r3 = r3.j(r5, r14, r15, r4)     // Catch:{ all -> 0x09f4 }
            hby r3 = (defpackage.hby) r3     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "#audio# can't handoff, sample rate mismatch: current(%d Hz), expected(%d Hz)"
            r3.v(r4, r6, r12)     // Catch:{ all -> 0x09f4 }
            goto L_0x009e
        L_0x014d:
            int r6 = r4.d     // Catch:{ all -> 0x09f4 }
            int r12 = r5.d     // Catch:{ all -> 0x09f4 }
            java.util.Set r14 = defpackage.evd.b     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r15 = java.lang.Integer.valueOf(r12)     // Catch:{ all -> 0x09f4 }
            boolean r14 = r14.contains(r15)     // Catch:{ all -> 0x09f4 }
            if (r14 == 0) goto L_0x016a
            java.util.Set r14 = defpackage.evd.b     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r15 = java.lang.Integer.valueOf(r6)     // Catch:{ all -> 0x09f4 }
            boolean r14 = r14.contains(r15)     // Catch:{ all -> 0x09f4 }
            if (r14 != 0) goto L_0x016c
            goto L_0x01c2
        L_0x016a:
            if (r12 != r6) goto L_0x01c2
        L_0x016c:
            int r6 = r4.e     // Catch:{ all -> 0x09f4 }
            int r12 = r5.e     // Catch:{ all -> 0x09f4 }
            if (r6 == r12) goto L_0x0195
            hca r3 = defpackage.evd.a     // Catch:{ all -> 0x09f4 }
            hco r3 = r3.h()     // Catch:{ all -> 0x09f4 }
            hcr r4 = defpackage.hdg.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r5 = "ALT.SeamlessUtils"
            hco r3 = r3.h(r4, r5)     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "SeamlessUtils.kt"
            java.lang.String r5 = "com/google/android/libraries/search/audio/utils/SeamlessUtils"
            java.lang.String r14 = "canHandoff"
            r15 = 95
            hco r3 = r3.j(r5, r14, r15, r4)     // Catch:{ all -> 0x09f4 }
            hby r3 = (defpackage.hby) r3     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "#audio# can't handoff, encoding mismatch: current(%d), expected(%d)"
            r3.v(r4, r6, r12)     // Catch:{ all -> 0x09f4 }
            goto L_0x009e
        L_0x0195:
            boolean r6 = r4.k     // Catch:{ all -> 0x09f4 }
            boolean r12 = r5.k     // Catch:{ all -> 0x09f4 }
            if (r6 == r12) goto L_0x01e6
            hca r3 = defpackage.evd.a     // Catch:{ all -> 0x09f4 }
            hco r3 = r3.h()     // Catch:{ all -> 0x09f4 }
            hcr r6 = defpackage.hdg.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r12 = "ALT.SeamlessUtils"
            hco r3 = r3.h(r6, r12)     // Catch:{ all -> 0x09f4 }
            java.lang.String r6 = "SeamlessUtils.kt"
            java.lang.String r12 = "com/google/android/libraries/search/audio/utils/SeamlessUtils"
            java.lang.String r14 = "canHandoff"
            r15 = 105(0x69, float:1.47E-43)
            hco r3 = r3.j(r12, r14, r15, r6)     // Catch:{ all -> 0x09f4 }
            hby r3 = (defpackage.hby) r3     // Catch:{ all -> 0x09f4 }
            boolean r4 = r4.k     // Catch:{ all -> 0x09f4 }
            boolean r5 = r5.k     // Catch:{ all -> 0x09f4 }
            java.lang.String r6 = "#audio# can't handoff, push mechanism flag mismatch: current(%b), expected(%b)"
            r3.F(r6, r4, r5)     // Catch:{ all -> 0x09f4 }
            goto L_0x009e
        L_0x01c2:
            hca r3 = defpackage.evd.a     // Catch:{ all -> 0x09f4 }
            hco r3 = r3.h()     // Catch:{ all -> 0x09f4 }
            hcr r4 = defpackage.hdg.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r5 = "ALT.SeamlessUtils"
            hco r3 = r3.h(r4, r5)     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "SeamlessUtils.kt"
            java.lang.String r5 = "com/google/android/libraries/search/audio/utils/SeamlessUtils"
            java.lang.String r14 = "canHandoff"
            r15 = 83
            hco r3 = r3.j(r5, r14, r15, r4)     // Catch:{ all -> 0x09f4 }
            hby r3 = (defpackage.hby) r3     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "#audio# can't handoff, channel config mismatch: current(%d), expected(%d)"
            r3.v(r4, r6, r12)     // Catch:{ all -> 0x09f4 }
            goto L_0x009e
        L_0x01e5:
            r3 = 0
        L_0x01e6:
            epn r3 = (defpackage.epn) r3     // Catch:{ all -> 0x09f4 }
            if (r3 != 0) goto L_0x020f
            hca r1 = f     // Catch:{ all -> 0x09f4 }
            hco r1 = r1.f()     // Catch:{ all -> 0x09f4 }
            hcr r2 = defpackage.hdg.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r3 = "ALT.AudioSrcDataRegy"
            hco r1 = r1.h(r2, r3)     // Catch:{ all -> 0x09f4 }
            java.lang.String r2 = "AudioSourceDataRegistry.kt"
            java.lang.String r3 = "com/google/android/libraries/search/audio/microphone/source/registry/AudioSourceDataRegistry"
            java.lang.String r4 = "maybeHandoff"
            r5 = 231(0xe7, float:3.24E-43)
            hco r1 = r1.j(r3, r4, r5, r2)     // Catch:{ all -> 0x09f4 }
            hby r1 = (defpackage.hby) r1     // Catch:{ all -> 0x09f4 }
            int r2 = r11.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r3 = "#audio# session(%d) seamless start failed, starting normally"
            r1.s(r3, r2)     // Catch:{ all -> 0x09f4 }
            goto L_0x0095
        L_0x020f:
            hca r2 = f     // Catch:{ all -> 0x09f4 }
            hco r2 = r2.f()     // Catch:{ all -> 0x09f4 }
            hcr r4 = defpackage.hdg.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r5 = "ALT.AudioSrcDataRegy"
            hco r2 = r2.h(r4, r5)     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "AudioSourceDataRegistry.kt"
            java.lang.String r5 = "com/google/android/libraries/search/audio/microphone/source/registry/AudioSourceDataRegistry"
            java.lang.String r6 = "maybeHandoff"
            r12 = 235(0xeb, float:3.3E-43)
            hco r2 = r2.j(r5, r6, r12, r4)     // Catch:{ all -> 0x09f4 }
            hby r2 = (defpackage.hby) r2     // Catch:{ all -> 0x09f4 }
            int r4 = r11.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r5 = "#audio# starting session(%d) seamlessly"
            r2.s(r5, r4)     // Catch:{ all -> 0x09f4 }
            int r1 = r1.b     // Catch:{ all -> 0x09f4 }
            enu r2 = new enu     // Catch:{ all -> 0x09f4 }
            r2.<init>(r1)     // Catch:{ all -> 0x09f4 }
            enr r1 = r3.d     // Catch:{ all -> 0x09f4 }
            ent r4 = new ent     // Catch:{ all -> 0x09f4 }
            r4.<init>(r2, r1)     // Catch:{ all -> 0x09f4 }
            java.lang.Object r1 = r7.c     // Catch:{ all -> 0x09f4 }
            if (r1 == 0) goto L_0x024d
            int r2 = r3.a     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r2 = java.lang.Integer.valueOf(r2)     // Catch:{ all -> 0x09f4 }
            r1.a(r2)     // Catch:{ all -> 0x09f4 }
        L_0x024d:
            hll r1 = r8.b     // Catch:{ all -> 0x09f4 }
            cpw r2 = new cpw     // Catch:{ all -> 0x09f4 }
            r2.<init>(r4, r13)     // Catch:{ all -> 0x09f4 }
            hkn r2 = defpackage.gof.c(r2)     // Catch:{ all -> 0x09f4 }
            java.util.concurrent.Executor r3 = r8.a     // Catch:{ all -> 0x09f4 }
            hme r1 = r1.b(r2, r3)     // Catch:{ all -> 0x09f4 }
            java.lang.String r2 = "submitAsync(...)"
            defpackage.jnu.d(r1, r2)     // Catch:{ all -> 0x09f4 }
            emt r2 = r8.g     // Catch:{ all -> 0x09f4 }
            hme r3 = r4.j()     // Catch:{ all -> 0x09f4 }
            int r5 = r11.a     // Catch:{ all -> 0x09f4 }
            dze r6 = r11.b     // Catch:{ all -> 0x09f4 }
            int r12 = r6.d     // Catch:{ all -> 0x09f4 }
            if (r12 != r13) goto L_0x0282
            java.lang.Object r6 = r6.e     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r6 = (java.lang.Integer) r6     // Catch:{ all -> 0x09f4 }
            int r6 = r6.intValue()     // Catch:{ all -> 0x09f4 }
            ewn r6 = defpackage.ewn.b(r6)     // Catch:{ all -> 0x09f4 }
            if (r6 != 0) goto L_0x0284
            ewn r6 = defpackage.ewn.TAG_DO_NOT_USE     // Catch:{ all -> 0x09f4 }
            goto L_0x0284
        L_0x0282:
            ewn r6 = defpackage.ewn.TAG_DO_NOT_USE     // Catch:{ all -> 0x09f4 }
        L_0x0284:
            java.lang.String r12 = "getAttributionId(...)"
            defpackage.jnu.d(r6, r12)     // Catch:{ all -> 0x09f4 }
            java.lang.String r6 = defpackage.fbi.k(r6)     // Catch:{ all -> 0x09f4 }
            r2.a(r1, r3, r5, r6)     // Catch:{ all -> 0x09f4 }
            cyw r2 = r8.e     // Catch:{ all -> 0x09f4 }
            ehg r3 = r9.b     // Catch:{ all -> 0x09f4 }
            eej r5 = r4.d()     // Catch:{ all -> 0x09f4 }
            hme r5 = r5.b()     // Catch:{ all -> 0x09f4 }
            java.lang.String r6 = "clientInfo"
            defpackage.jnu.e(r3, r6)     // Catch:{ all -> 0x09f4 }
            java.lang.String r6 = "startResult"
            defpackage.jnu.e(r1, r6)     // Catch:{ all -> 0x09f4 }
            java.lang.String r6 = "SEAMLESS"
            r2.v(r6, r3, r5, r1)     // Catch:{ all -> 0x09f4 }
            eph r2 = new eph     // Catch:{ all -> 0x09f4 }
            r2.<init>(r4, r1)     // Catch:{ all -> 0x09f4 }
        L_0x02b0:
            if (r2 != 0) goto L_0x0919
            epd r12 = new epd     // Catch:{ all -> 0x09f4 }
            r6 = 0
            r1 = r12
            r2 = r33
            r3 = r35
            r4 = r36
            r5 = r34
            r1.<init>((defpackage.epe) r2, (defpackage.epp) r3, (defpackage.epg) r4, (defpackage.epb) r5, (int) r6)     // Catch:{ all -> 0x09f4 }
            boolean r1 = r8.j     // Catch:{ all -> 0x09f4 }
            r5 = 4
            r2 = 1
            if (r1 == 0) goto L_0x04bf
            ehg r1 = r9.b     // Catch:{ all -> 0x09f4 }
            int r1 = r1.a     // Catch:{ all -> 0x09f4 }
            r6 = 15
            if (r1 != r6) goto L_0x04bf
            java.lang.Object r1 = r7.b     // Catch:{ all -> 0x09f4 }
            java.util.Iterator r1 = r1.iterator()     // Catch:{ all -> 0x09f4 }
        L_0x02d5:
            boolean r6 = r1.hasNext()     // Catch:{ all -> 0x09f4 }
            if (r6 == 0) goto L_0x0425
            java.lang.Object r6 = r1.next()     // Catch:{ all -> 0x09f4 }
            r7 = r6
            epn r7 = (defpackage.epn) r7     // Catch:{ all -> 0x09f4 }
            ejn r14 = r10.b     // Catch:{ all -> 0x09f4 }
            dze r15 = r11.b     // Catch:{ all -> 0x09f4 }
            java.lang.String r3 = "expectedRoute"
            defpackage.jnu.e(r14, r3)     // Catch:{ all -> 0x09f4 }
            java.lang.String r3 = "expectedParams"
            defpackage.jnu.e(r15, r3)     // Catch:{ all -> 0x09f4 }
            java.lang.String r3 = "sourceToShare"
            defpackage.jnu.e(r7, r3)     // Catch:{ all -> 0x09f4 }
            epp r3 = r7.c     // Catch:{ all -> 0x09f4 }
            ejn r3 = r3.b     // Catch:{ all -> 0x09f4 }
            dzq r3 = r3.b     // Catch:{ all -> 0x09f4 }
            int r3 = r3.a     // Catch:{ all -> 0x09f4 }
            dzp r3 = defpackage.dzp.a(r3)     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "getAudioRouteCase(...)"
            defpackage.jnu.d(r3, r4)     // Catch:{ all -> 0x09f4 }
            dzq r4 = r14.b     // Catch:{ all -> 0x09f4 }
            int r4 = r4.a     // Catch:{ all -> 0x09f4 }
            dzp r4 = defpackage.dzp.a(r4)     // Catch:{ all -> 0x09f4 }
            if (r4 == r3) goto L_0x0313
            r3 = 7
            goto L_0x03d8
        L_0x0313:
            dzp r4 = defpackage.dzp.BUILTIN_AUDIO_ROUTE     // Catch:{ all -> 0x09f4 }
            if (r3 == r4) goto L_0x031a
            r3 = 3
            goto L_0x03d8
        L_0x031a:
            int r3 = r15.b     // Catch:{ all -> 0x09f4 }
            dzd r3 = defpackage.dzd.a(r3)     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "getOptionalAudioInputModeCase(...)"
            defpackage.jnu.d(r3, r4)     // Catch:{ all -> 0x09f4 }
            java.util.Set r4 = defpackage.eve.a     // Catch:{ all -> 0x09f4 }
            dzd r4 = defpackage.dzd.OPTIONALAUDIOINPUTMODE_NOT_SET     // Catch:{ all -> 0x09f4 }
            if (r3 == r4) goto L_0x032e
        L_0x032b:
            r3 = 6
            goto L_0x03d8
        L_0x032e:
            java.lang.Object r3 = r7.b     // Catch:{ all -> 0x09f4 }
            ebn r3 = (defpackage.ebn) r3     // Catch:{ all -> 0x09f4 }
            int r3 = r3.b     // Catch:{ all -> 0x09f4 }
            ebm r3 = defpackage.ebm.a(r3)     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "getOptionalAudioInputModeCase(...)"
            defpackage.jnu.d(r3, r4)     // Catch:{ all -> 0x09f4 }
            ebm r4 = defpackage.ebm.OPTIONALAUDIOINPUTMODE_NOT_SET     // Catch:{ all -> 0x09f4 }
            if (r3 == r4) goto L_0x0342
            goto L_0x032b
        L_0x0342:
            int r3 = r15.a     // Catch:{ all -> 0x09f4 }
            r4 = r3 & 2
            if (r4 == 0) goto L_0x034b
            r3 = r5
            goto L_0x03d8
        L_0x034b:
            r3 = r3 & 4
            if (r3 == 0) goto L_0x0352
            r3 = 5
            goto L_0x03d8
        L_0x0352:
            enr r3 = r7.d     // Catch:{ all -> 0x09f4 }
            eny r3 = (defpackage.eny) r3     // Catch:{ all -> 0x09f4 }
            dyt r3 = r3.h     // Catch:{ all -> 0x09f4 }
            dyt r4 = r15.f     // Catch:{ all -> 0x09f4 }
            if (r4 != 0) goto L_0x035e
            dyt r4 = defpackage.dyt.l     // Catch:{ all -> 0x09f4 }
        L_0x035e:
            java.lang.String r14 = "getAudioLibInputParams(...)"
            defpackage.jnu.d(r4, r14)     // Catch:{ all -> 0x09f4 }
            int r14 = r3.b     // Catch:{ all -> 0x09f4 }
            r15 = 1999(0x7cf, float:2.801E-42)
            if (r14 != r15) goto L_0x036a
            r14 = 6
        L_0x036a:
            int r15 = r4.b     // Catch:{ all -> 0x09f4 }
            if (r14 == r15) goto L_0x0372
            r3 = 8
            goto L_0x03d8
        L_0x0372:
            int r14 = r3.c     // Catch:{ all -> 0x09f4 }
            int r15 = r4.c     // Catch:{ all -> 0x09f4 }
            if (r14 == r15) goto L_0x037b
            r3 = 10
            goto L_0x03d8
        L_0x037b:
            int r14 = r3.e     // Catch:{ all -> 0x09f4 }
            int r15 = r4.e     // Catch:{ all -> 0x09f4 }
            if (r14 == r15) goto L_0x0384
            r3 = 11
            goto L_0x03d8
        L_0x0384:
            boolean r15 = r3.k     // Catch:{ all -> 0x09f4 }
            boolean r13 = r4.k     // Catch:{ all -> 0x09f4 }
            if (r15 == r13) goto L_0x038d
            r3 = 9
            goto L_0x03d8
        L_0x038d:
            int r13 = r4.d     // Catch:{ all -> 0x09f4 }
            int r15 = r3.d     // Catch:{ all -> 0x09f4 }
            if (r13 != r15) goto L_0x0394
            goto L_0x03c9
        L_0x0394:
            int r13 = defpackage.evc.b(r14)     // Catch:{ all -> 0x039d }
            java.lang.Integer r13 = java.lang.Integer.valueOf(r13)     // Catch:{ all -> 0x039d }
            goto L_0x03a3
        L_0x039d:
            r0 = move-exception
            r13 = r0
            java.lang.Object r13 = defpackage.jji.b(r13)     // Catch:{ all -> 0x09f4 }
        L_0x03a3:
            java.lang.Throwable r13 = defpackage.jju.a(r13)     // Catch:{ all -> 0x09f4 }
            if (r13 == 0) goto L_0x03ac
        L_0x03a9:
            r3 = 12
            goto L_0x03d8
        L_0x03ac:
            java.util.Set r13 = defpackage.eve.a     // Catch:{ all -> 0x09f4 }
            int r4 = r4.d     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r4 = java.lang.Integer.valueOf(r4)     // Catch:{ all -> 0x09f4 }
            boolean r4 = r13.contains(r4)     // Catch:{ all -> 0x09f4 }
            if (r4 == 0) goto L_0x03a9
            java.util.Set r4 = defpackage.eve.a     // Catch:{ all -> 0x09f4 }
            int r3 = r3.d     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r3 = java.lang.Integer.valueOf(r3)     // Catch:{ all -> 0x09f4 }
            boolean r3 = r4.contains(r3)     // Catch:{ all -> 0x09f4 }
            if (r3 != 0) goto L_0x03c9
            goto L_0x03a9
        L_0x03c9:
            enr r3 = r7.d     // Catch:{ all -> 0x09f4 }
            eny r3 = (defpackage.eny) r3     // Catch:{ all -> 0x09f4 }
            hme r3 = r3.f     // Catch:{ all -> 0x09f4 }
            boolean r3 = r3.isDone()     // Catch:{ all -> 0x09f4 }
            if (r3 == 0) goto L_0x03d7
            r3 = 2
            goto L_0x03d8
        L_0x03d7:
            r3 = r2
        L_0x03d8:
            if (r3 != r2) goto L_0x03db
            goto L_0x0426
        L_0x03db:
            hca r4 = f     // Catch:{ all -> 0x09f4 }
            hco r4 = r4.c()     // Catch:{ all -> 0x09f4 }
            hcr r6 = defpackage.hdg.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r13 = "ALT.AudioSrcDataRegy"
            hco r4 = r4.h(r6, r13)     // Catch:{ all -> 0x09f4 }
            java.lang.String r6 = "AudioSourceDataRegistry.kt"
            java.lang.String r13 = "com/google/android/libraries/search/audio/microphone/source/registry/AudioSourceDataRegistry"
            java.lang.String r14 = "maybeShareHotword"
            r15 = 284(0x11c, float:3.98E-43)
            hco r4 = r4.j(r13, r14, r15, r6)     // Catch:{ all -> 0x09f4 }
            hby r4 = (defpackage.hby) r4     // Catch:{ all -> 0x09f4 }
            int r6 = r7.a     // Catch:{ all -> 0x09f4 }
            switch(r3) {
                case 2: goto L_0x041a;
                case 3: goto L_0x0417;
                case 4: goto L_0x0414;
                case 5: goto L_0x0411;
                case 6: goto L_0x040e;
                case 7: goto L_0x040b;
                case 8: goto L_0x0408;
                case 9: goto L_0x0405;
                case 10: goto L_0x0402;
                case 11: goto L_0x03ff;
                default: goto L_0x03fc;
            }     // Catch:{ all -> 0x09f4 }
        L_0x03fc:
            java.lang.String r3 = "CHANNELS_INCOMPATIBLE"
            goto L_0x041c
        L_0x03ff:
            java.lang.String r3 = "ENCODING_INCOMPATIBLE"
            goto L_0x041c
        L_0x0402:
            java.lang.String r3 = "RATE_INCOMPATIBLE"
            goto L_0x041c
        L_0x0405:
            java.lang.String r3 = "BUFFERS_INCOMPATIBLE"
            goto L_0x041c
        L_0x0408:
            java.lang.String r3 = "SOURCE_INCOMPATIBLE"
            goto L_0x041c
        L_0x040b:
            java.lang.String r3 = "ROUTES_INCOMPATIBLE"
            goto L_0x041c
        L_0x040e:
            java.lang.String r3 = "INPUT_MODE_INCOMPATIBLE"
            goto L_0x041c
        L_0x0411:
            java.lang.String r3 = "TALKBACK_MUTING_REQUESTED"
            goto L_0x041c
        L_0x0414:
            java.lang.String r3 = "EMULATED_MODE_REQUESTED"
            goto L_0x041c
        L_0x0417:
            java.lang.String r3 = "ROUTE_NOT_BUILTIN"
            goto L_0x041c
        L_0x041a:
            java.lang.String r3 = "SOURCE_ALREADY_STOPPED"
        L_0x041c:
            java.lang.String r7 = "#audio# not sharing hotword session(%d, %s)"
            r4.x(r7, r6, r3)     // Catch:{ all -> 0x09f4 }
            r13 = 14
            goto L_0x02d5
        L_0x0425:
            r6 = 0
        L_0x0426:
            epn r6 = (defpackage.epn) r6     // Catch:{ all -> 0x09f4 }
            if (r6 != 0) goto L_0x044e
            hca r1 = f     // Catch:{ all -> 0x09f4 }
            hco r1 = r1.f()     // Catch:{ all -> 0x09f4 }
            hcr r3 = defpackage.hdg.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "ALT.AudioSrcDataRegy"
            hco r1 = r1.h(r3, r4)     // Catch:{ all -> 0x09f4 }
            java.lang.String r3 = "AudioSourceDataRegistry.kt"
            java.lang.String r4 = "com/google/android/libraries/search/audio/microphone/source/registry/AudioSourceDataRegistry"
            java.lang.String r6 = "maybeShareHotword"
            r7 = 288(0x120, float:4.04E-43)
            hco r1 = r1.j(r4, r6, r7, r3)     // Catch:{ all -> 0x09f4 }
            hby r1 = (defpackage.hby) r1     // Catch:{ all -> 0x09f4 }
            int r3 = r11.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "#audio# no shareable hotword source for(%d)"
            r1.s(r4, r3)     // Catch:{ all -> 0x09f4 }
            goto L_0x04bf
        L_0x044e:
            hca r1 = f     // Catch:{ all -> 0x09f4 }
            hco r1 = r1.f()     // Catch:{ all -> 0x09f4 }
            hcr r3 = defpackage.hdg.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "ALT.AudioSrcDataRegy"
            hco r1 = r1.h(r3, r4)     // Catch:{ all -> 0x09f4 }
            java.lang.String r3 = "AudioSourceDataRegistry.kt"
            java.lang.String r4 = "com/google/android/libraries/search/audio/microphone/source/registry/AudioSourceDataRegistry"
            java.lang.String r7 = "maybeShareHotword"
            r13 = 292(0x124, float:4.09E-43)
            hco r1 = r1.j(r4, r7, r13, r3)     // Catch:{ all -> 0x09f4 }
            hby r1 = (defpackage.hby) r1     // Catch:{ all -> 0x09f4 }
            int r3 = r11.a     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "#audio# sharing hotword source for session(%d)"
            r1.s(r4, r3)     // Catch:{ all -> 0x09f4 }
            bmu r1 = r8.o     // Catch:{ all -> 0x09f4 }
            eoe r3 = new eoe     // Catch:{ all -> 0x09f4 }
            r3.<init>(r6, r5)     // Catch:{ all -> 0x09f4 }
            env r4 = new env     // Catch:{ all -> 0x09f4 }
            r4.<init>(r3)     // Catch:{ all -> 0x09f4 }
            enr r3 = r6.d     // Catch:{ all -> 0x09f4 }
            ent r6 = new ent     // Catch:{ all -> 0x09f4 }
            r6.<init>(r4, r3)     // Catch:{ all -> 0x09f4 }
            eny r1 = r1.w(r6)     // Catch:{ all -> 0x09f4 }
            java.lang.Object r3 = r12.a(r1)     // Catch:{ all -> 0x09f4 }
            emt r4 = r8.g     // Catch:{ all -> 0x09f4 }
            hme r6 = r1.f     // Catch:{ all -> 0x09f4 }
            int r7 = r11.a     // Catch:{ all -> 0x09f4 }
            dze r13 = r11.b     // Catch:{ all -> 0x09f4 }
            int r14 = r13.d     // Catch:{ all -> 0x09f4 }
            r15 = 14
            if (r14 != r15) goto L_0x04ab
            java.lang.Object r13 = r13.e     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r13 = (java.lang.Integer) r13     // Catch:{ all -> 0x09f4 }
            int r13 = r13.intValue()     // Catch:{ all -> 0x09f4 }
            ewn r13 = defpackage.ewn.b(r13)     // Catch:{ all -> 0x09f4 }
            if (r13 != 0) goto L_0x04ad
            ewn r13 = defpackage.ewn.TAG_DO_NOT_USE     // Catch:{ all -> 0x09f4 }
            goto L_0x04ad
        L_0x04ab:
            ewn r13 = defpackage.ewn.TAG_DO_NOT_USE     // Catch:{ all -> 0x09f4 }
        L_0x04ad:
            java.lang.String r14 = "getAttributionId(...)"
            defpackage.jnu.d(r13, r14)     // Catch:{ all -> 0x09f4 }
            java.lang.String r13 = defpackage.fbi.k(r13)     // Catch:{ all -> 0x09f4 }
            r4.a(r3, r6, r7, r13)     // Catch:{ all -> 0x09f4 }
            eph r4 = new eph     // Catch:{ all -> 0x09f4 }
            r4.<init>(r1, r3)     // Catch:{ all -> 0x09f4 }
            goto L_0x04c0
        L_0x04bf:
            r4 = 0
        L_0x04c0:
            if (r4 != 0) goto L_0x0917
            ejn r1 = r10.b     // Catch:{ all -> 0x09f4 }
            dzq r1 = r1.b     // Catch:{ all -> 0x09f4 }
            int r3 = r1.a     // Catch:{ all -> 0x09f4 }
            dzp r3 = defpackage.dzp.a(r3)     // Catch:{ all -> 0x09f4 }
            int r3 = r3.ordinal()     // Catch:{ all -> 0x09f4 }
            switch(r3) {
                case 0: goto L_0x06ab;
                case 1: goto L_0x06ab;
                case 2: goto L_0x06ab;
                case 3: goto L_0x05ff;
                case 4: goto L_0x05df;
                case 5: goto L_0x05a2;
                case 6: goto L_0x0565;
                case 7: goto L_0x04d7;
                case 8: goto L_0x06ab;
                default: goto L_0x04d3;
            }     // Catch:{ all -> 0x09f4 }
        L_0x04d3:
            jjq r1 = new jjq     // Catch:{ all -> 0x09f4 }
            goto L_0x0913
        L_0x04d7:
            cxp r3 = r8.m     // Catch:{ all -> 0x09f4 }
            ehg r4 = r9.b     // Catch:{ all -> 0x09f4 }
            int r5 = r9.a     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r5 = java.lang.Integer.valueOf(r5)     // Catch:{ all -> 0x09f4 }
            grh r26 = defpackage.grh.g(r5)     // Catch:{ all -> 0x09f4 }
            int r5 = r11.a     // Catch:{ all -> 0x09f4 }
            dou r6 = r8.l     // Catch:{ all -> 0x09f4 }
            ejn r7 = r10.b     // Catch:{ all -> 0x09f4 }
            ecg r13 = defpackage.ecg.GACS     // Catch:{ all -> 0x09f4 }
            eck r28 = r6.d(r13, r7)     // Catch:{ all -> 0x09f4 }
            int r6 = r1.a     // Catch:{ all -> 0x09f4 }
            r7 = 8
            if (r6 != r7) goto L_0x04fc
            java.lang.Object r1 = r1.b     // Catch:{ all -> 0x09f4 }
            dzz r1 = (defpackage.dzz) r1     // Catch:{ all -> 0x09f4 }
            goto L_0x04fe
        L_0x04fc:
            dzz r1 = defpackage.dzz.c     // Catch:{ all -> 0x09f4 }
        L_0x04fe:
            java.lang.String r6 = "getGacsAudioRoute(...)"
            defpackage.jnu.d(r1, r6)     // Catch:{ all -> 0x09f4 }
            dze r6 = r11.b     // Catch:{ all -> 0x09f4 }
            java.lang.String r7 = "<this>"
            defpackage.jnu.e(r1, r7)     // Catch:{ all -> 0x09f4 }
            java.lang.String r7 = "audioRequestMicInputParams"
            defpackage.jnu.e(r6, r7)     // Catch:{ all -> 0x09f4 }
            int r7 = r1.a     // Catch:{ all -> 0x09f4 }
            r2 = r2 & r7
            if (r2 == 0) goto L_0x0526
            ead r1 = r1.b     // Catch:{ all -> 0x09f4 }
            if (r1 != 0) goto L_0x051a
            ead r1 = defpackage.ead.c     // Catch:{ all -> 0x09f4 }
        L_0x051a:
            java.lang.String r2 = "getPrecachedBufferId(...)"
            defpackage.jnu.d(r1, r2)     // Catch:{ all -> 0x09f4 }
            ech r1 = defpackage.doe.m(r1, r6)     // Catch:{ all -> 0x09f4 }
        L_0x0523:
            r29 = r1
            goto L_0x0559
        L_0x0526:
            ech r1 = defpackage.ech.d     // Catch:{ all -> 0x09f4 }
            htk r1 = r1.l()     // Catch:{ all -> 0x09f4 }
            java.lang.String r2 = "newBuilder(...)"
            defpackage.jnu.d(r1, r2)     // Catch:{ all -> 0x09f4 }
            bzl r1 = defpackage.jnu.e(r1, "builder")     // Catch:{ all -> 0x09f4 }
            eci r2 = defpackage.eci.e     // Catch:{ all -> 0x09f4 }
            htk r2 = r2.l()     // Catch:{ all -> 0x09f4 }
            java.lang.String r7 = "newBuilder(...)"
            defpackage.jnu.d(r2, r7)     // Catch:{ all -> 0x09f4 }
            bzl r2 = defpackage.jnu.e(r2, "builder")     // Catch:{ all -> 0x09f4 }
            dyt r6 = r6.f     // Catch:{ all -> 0x09f4 }
            if (r6 != 0) goto L_0x054a
            dyt r6 = defpackage.dyt.l     // Catch:{ all -> 0x09f4 }
        L_0x054a:
            r2.r(r6)     // Catch:{ all -> 0x09f4 }
            eci r2 = r2.q()     // Catch:{ all -> 0x09f4 }
            r1.u(r2)     // Catch:{ all -> 0x09f4 }
            ech r1 = r1.s()     // Catch:{ all -> 0x09f4 }
            goto L_0x0523
        L_0x0559:
            r24 = r3
            r25 = r4
            r27 = r5
            eng r1 = r24.b(r25, r26, r27, r28, r29)     // Catch:{ all -> 0x09f4 }
            goto L_0x08ff
        L_0x0565:
            cxp r2 = r8.m     // Catch:{ all -> 0x09f4 }
            ehg r3 = r9.b     // Catch:{ all -> 0x09f4 }
            int r4 = r9.a     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r4 = java.lang.Integer.valueOf(r4)     // Catch:{ all -> 0x09f4 }
            grh r4 = defpackage.grh.g(r4)     // Catch:{ all -> 0x09f4 }
            int r5 = r11.a     // Catch:{ all -> 0x09f4 }
            dou r6 = r8.l     // Catch:{ all -> 0x09f4 }
            ejn r7 = r10.b     // Catch:{ all -> 0x09f4 }
            ecg r13 = defpackage.ecg.CAR     // Catch:{ all -> 0x09f4 }
            eck r6 = r6.d(r13, r7)     // Catch:{ all -> 0x09f4 }
            int r7 = r1.a     // Catch:{ all -> 0x09f4 }
            r13 = 7
            if (r7 != r13) goto L_0x0589
            java.lang.Object r1 = r1.b     // Catch:{ all -> 0x09f4 }
            dzw r1 = (defpackage.dzw) r1     // Catch:{ all -> 0x09f4 }
            goto L_0x058b
        L_0x0589:
            dzw r1 = defpackage.dzw.b     // Catch:{ all -> 0x09f4 }
        L_0x058b:
            ead r1 = r1.a     // Catch:{ all -> 0x09f4 }
            if (r1 != 0) goto L_0x0591
            ead r1 = defpackage.ead.c     // Catch:{ all -> 0x09f4 }
        L_0x0591:
            java.lang.String r7 = "getPrecachedBufferId(...)"
            defpackage.jnu.d(r1, r7)     // Catch:{ all -> 0x09f4 }
            dze r7 = r11.b     // Catch:{ all -> 0x09f4 }
            ech r7 = defpackage.doe.m(r1, r7)     // Catch:{ all -> 0x09f4 }
            eng r1 = r2.b(r3, r4, r5, r6, r7)     // Catch:{ all -> 0x09f4 }
            goto L_0x08ff
        L_0x05a2:
            cxp r2 = r8.m     // Catch:{ all -> 0x09f4 }
            ehg r3 = r9.b     // Catch:{ all -> 0x09f4 }
            int r4 = r9.a     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r4 = java.lang.Integer.valueOf(r4)     // Catch:{ all -> 0x09f4 }
            grh r4 = defpackage.grh.g(r4)     // Catch:{ all -> 0x09f4 }
            int r5 = r11.a     // Catch:{ all -> 0x09f4 }
            dou r6 = r8.l     // Catch:{ all -> 0x09f4 }
            ejn r7 = r10.b     // Catch:{ all -> 0x09f4 }
            ecg r13 = defpackage.ecg.SODA     // Catch:{ all -> 0x09f4 }
            eck r6 = r6.d(r13, r7)     // Catch:{ all -> 0x09f4 }
            int r7 = r1.a     // Catch:{ all -> 0x09f4 }
            r13 = 6
            if (r7 != r13) goto L_0x05c6
            java.lang.Object r1 = r1.b     // Catch:{ all -> 0x09f4 }
            eae r1 = (defpackage.eae) r1     // Catch:{ all -> 0x09f4 }
            goto L_0x05c8
        L_0x05c6:
            eae r1 = defpackage.eae.b     // Catch:{ all -> 0x09f4 }
        L_0x05c8:
            ead r1 = r1.a     // Catch:{ all -> 0x09f4 }
            if (r1 != 0) goto L_0x05ce
            ead r1 = defpackage.ead.c     // Catch:{ all -> 0x09f4 }
        L_0x05ce:
            java.lang.String r7 = "getPrecachedBufferId(...)"
            defpackage.jnu.d(r1, r7)     // Catch:{ all -> 0x09f4 }
            dze r7 = r11.b     // Catch:{ all -> 0x09f4 }
            ech r7 = defpackage.doe.m(r1, r7)     // Catch:{ all -> 0x09f4 }
            eng r1 = r2.b(r3, r4, r5, r6, r7)     // Catch:{ all -> 0x09f4 }
            goto L_0x08ff
        L_0x05df:
            java.lang.IllegalStateException r2 = new java.lang.IllegalStateException     // Catch:{ all -> 0x09f4 }
            java.lang.String r1 = defpackage.fbi.p(r1)     // Catch:{ all -> 0x09f4 }
            java.lang.StringBuilder r3 = new java.lang.StringBuilder     // Catch:{ all -> 0x09f4 }
            r3.<init>()     // Catch:{ all -> 0x09f4 }
            java.lang.String r4 = "Route("
            r3.append(r4)     // Catch:{ all -> 0x09f4 }
            r3.append(r1)     // Catch:{ all -> 0x09f4 }
            java.lang.String r1 = ") is unexpected"
            r3.append(r1)     // Catch:{ all -> 0x09f4 }
            java.lang.String r1 = r3.toString()     // Catch:{ all -> 0x09f4 }
            r2.<init>(r1)     // Catch:{ all -> 0x09f4 }
            throw r2     // Catch:{ all -> 0x09f4 }
        L_0x05ff:
            cxp r2 = r8.m     // Catch:{ all -> 0x09f4 }
            ehg r3 = r9.b     // Catch:{ all -> 0x09f4 }
            int r4 = r9.a     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r4 = java.lang.Integer.valueOf(r4)     // Catch:{ all -> 0x09f4 }
            grh r4 = defpackage.grh.g(r4)     // Catch:{ all -> 0x09f4 }
            int r6 = r11.a     // Catch:{ all -> 0x09f4 }
            dou r7 = r8.l     // Catch:{ all -> 0x09f4 }
            ejn r13 = r10.b     // Catch:{ all -> 0x09f4 }
            ecg r14 = defpackage.ecg.BISTO     // Catch:{ all -> 0x09f4 }
            eck r21 = r7.d(r14, r13)     // Catch:{ all -> 0x09f4 }
            int r7 = r1.a     // Catch:{ all -> 0x09f4 }
            if (r7 != r5) goto L_0x0622
            java.lang.Object r1 = r1.b     // Catch:{ all -> 0x09f4 }
            dzr r1 = (defpackage.dzr) r1     // Catch:{ all -> 0x09f4 }
            goto L_0x0624
        L_0x0622:
            dzr r1 = defpackage.dzr.d     // Catch:{ all -> 0x09f4 }
        L_0x0624:
            java.lang.String r5 = "getBistoAudioRoute(...)"
            defpackage.jnu.d(r1, r5)     // Catch:{ all -> 0x09f4 }
            dze r5 = r11.b     // Catch:{ all -> 0x09f4 }
            java.lang.String r7 = "<this>"
            defpackage.jnu.e(r1, r7)     // Catch:{ all -> 0x09f4 }
            java.lang.String r7 = "audioRequestMicInputParams"
            defpackage.jnu.e(r5, r7)     // Catch:{ all -> 0x09f4 }
            int r7 = r1.a     // Catch:{ all -> 0x09f4 }
            r13 = 2
            r7 = r7 & r13
            if (r7 == 0) goto L_0x064d
            ead r1 = r1.c     // Catch:{ all -> 0x09f4 }
            if (r1 != 0) goto L_0x0641
            ead r1 = defpackage.ead.c     // Catch:{ all -> 0x09f4 }
        L_0x0641:
            java.lang.String r7 = "getPrecachedBufferId(...)"
            defpackage.jnu.d(r1, r7)     // Catch:{ all -> 0x09f4 }
            ech r1 = defpackage.doe.m(r1, r5)     // Catch:{ all -> 0x09f4 }
        L_0x064a:
            r22 = r1
            goto L_0x069d
        L_0x064d:
            ebs r1 = defpackage.dlh.A(r1)     // Catch:{ all -> 0x09f4 }
            grh r1 = defpackage.grh.g(r1)     // Catch:{ all -> 0x09f4 }
            java.lang.String r7 = "audioRequestMicInputParams"
            defpackage.jnu.e(r5, r7)     // Catch:{ all -> 0x09f4 }
            dyt r7 = r5.f     // Catch:{ all -> 0x09f4 }
            if (r7 != 0) goto L_0x0660
            dyt r7 = defpackage.dyt.l     // Catch:{ all -> 0x09f4 }
        L_0x0660:
            java.lang.String r13 = "getAudioLibInputParams(...)"
            defpackage.jnu.d(r7, r13)     // Catch:{ all -> 0x09f4 }
            ech r1 = defpackage.doe.n(r1, r7)     // Catch:{ all -> 0x09f4 }
            r7 = 5
            java.lang.Object r7 = r1.C(r7)     // Catch:{ all -> 0x09f4 }
            htk r7 = (defpackage.htk) r7     // Catch:{ all -> 0x09f4 }
            r7.x(r1)     // Catch:{ all -> 0x09f4 }
            java.lang.String r1 = "toBuilder(...)"
            defpackage.jnu.d(r7, r1)     // Catch:{ all -> 0x09f4 }
            bzl r1 = defpackage.jnu.e(r7, "builder")     // Catch:{ all -> 0x09f4 }
            int r7 = r5.d     // Catch:{ all -> 0x09f4 }
            r13 = 14
            if (r7 != r13) goto L_0x0693
            java.lang.Object r5 = r5.e     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r5 = (java.lang.Integer) r5     // Catch:{ all -> 0x09f4 }
            int r5 = r5.intValue()     // Catch:{ all -> 0x09f4 }
            ewn r5 = defpackage.ewn.b(r5)     // Catch:{ all -> 0x09f4 }
            if (r5 != 0) goto L_0x0695
            ewn r5 = defpackage.ewn.TAG_DO_NOT_USE     // Catch:{ all -> 0x09f4 }
            goto L_0x0695
        L_0x0693:
            ewn r5 = defpackage.ewn.TAG_DO_NOT_USE     // Catch:{ all -> 0x09f4 }
        L_0x0695:
            r1.t(r5)     // Catch:{ all -> 0x09f4 }
            ech r1 = r1.s()     // Catch:{ all -> 0x09f4 }
            goto L_0x064a
        L_0x069d:
            r17 = r2
            r18 = r3
            r19 = r4
            r20 = r6
            eng r1 = r17.b(r18, r19, r20, r21, r22)     // Catch:{ all -> 0x09f4 }
            goto L_0x08ff
        L_0x06ab:
            fnn r1 = r8.n     // Catch:{ all -> 0x09f4 }
            dze r3 = r11.b     // Catch:{ all -> 0x09f4 }
            ehg r4 = r9.b     // Catch:{ all -> 0x09f4 }
            ejn r6 = r10.b     // Catch:{ all -> 0x09f4 }
            int r7 = r10.a     // Catch:{ all -> 0x09f4 }
            int r13 = r11.a     // Catch:{ all -> 0x09f4 }
            int r14 = r9.a     // Catch:{ all -> 0x09f4 }
            elq r15 = new elq     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r7 = java.lang.Integer.valueOf(r7)     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r13 = java.lang.Integer.valueOf(r13)     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r14 = java.lang.Integer.valueOf(r14)     // Catch:{ all -> 0x09f4 }
            r15.<init>(r4, r7, r13, r14)     // Catch:{ all -> 0x09f4 }
            dyt r7 = r3.f     // Catch:{ all -> 0x09f4 }
            if (r7 != 0) goto L_0x06d0
            dyt r7 = defpackage.dyt.l     // Catch:{ all -> 0x09f4 }
        L_0x06d0:
            int r7 = r7.a     // Catch:{ all -> 0x09f4 }
            r7 = r7 & 256(0x100, float:3.59E-43)
            if (r7 == 0) goto L_0x06e6
            ekw r7 = new ekw     // Catch:{ all -> 0x09f4 }
            dyt r13 = r3.f     // Catch:{ all -> 0x09f4 }
            if (r13 != 0) goto L_0x06de
            dyt r13 = defpackage.dyt.l     // Catch:{ all -> 0x09f4 }
        L_0x06de:
            r7.<init>(r13)     // Catch:{ all -> 0x09f4 }
            grh r7 = defpackage.grh.h(r7)     // Catch:{ all -> 0x09f4 }
            goto L_0x06e8
        L_0x06e6:
            gqd r7 = defpackage.gqd.a     // Catch:{ all -> 0x09f4 }
        L_0x06e8:
            java.lang.Object r13 = r1.c     // Catch:{ all -> 0x09f4 }
            dyt r14 = r3.f     // Catch:{ all -> 0x09f4 }
            if (r14 != 0) goto L_0x06f0
            dyt r14 = defpackage.dyt.l     // Catch:{ all -> 0x09f4 }
        L_0x06f0:
            gxl r5 = new gxl     // Catch:{ all -> 0x09f4 }
            r5.<init>()     // Catch:{ all -> 0x09f4 }
            java.lang.Object r2 = r1.f     // Catch:{ all -> 0x09f4 }
            edv r10 = new edv     // Catch:{ all -> 0x09f4 }
            r3.getClass()     // Catch:{ all -> 0x09f4 }
            cxi r2 = (defpackage.cxi) r2     // Catch:{ all -> 0x09f4 }
            java.lang.Object r2 = r2.a     // Catch:{ all -> 0x09f4 }
            java.lang.Object r2 = r2.b()     // Catch:{ all -> 0x09f4 }
            dku r2 = (defpackage.dku) r2     // Catch:{ all -> 0x09f4 }
            r10.<init>(r3, r2)     // Catch:{ all -> 0x09f4 }
            r5.h(r10)     // Catch:{ all -> 0x09f4 }
            java.lang.Object r2 = r1.b     // Catch:{ all -> 0x09f4 }
            dyt r10 = r3.f     // Catch:{ all -> 0x09f4 }
            if (r10 != 0) goto L_0x0714
            dyt r10 = defpackage.dyt.l     // Catch:{ all -> 0x09f4 }
        L_0x0714:
            bzl r2 = (defpackage.bzl) r2     // Catch:{ all -> 0x09f4 }
            eds r2 = r2.o(r10)     // Catch:{ all -> 0x09f4 }
            r5.h(r2)     // Catch:{ all -> 0x09f4 }
            java.lang.Object r2 = r1.a     // Catch:{ all -> 0x09f4 }
            dyt r10 = r3.f     // Catch:{ all -> 0x09f4 }
            if (r10 != 0) goto L_0x0725
            dyt r10 = defpackage.dyt.l     // Catch:{ all -> 0x09f4 }
        L_0x0725:
            dsy r2 = (defpackage.dsy) r2     // Catch:{ all -> 0x09f4 }
            edo r2 = r2.b(r10)     // Catch:{ all -> 0x09f4 }
            r5.h(r2)     // Catch:{ all -> 0x09f4 }
            gxq r2 = r5.g()     // Catch:{ all -> 0x09f4 }
            dou r13 = (defpackage.dou) r13     // Catch:{ all -> 0x09f4 }
            efo r27 = r13.f(r14, r7, r2)     // Catch:{ all -> 0x09f4 }
            java.lang.Object r2 = r1.d     // Catch:{ all -> 0x09f4 }
            ees r5 = new ees     // Catch:{ all -> 0x09f4 }
            int r10 = r4.a     // Catch:{ all -> 0x09f4 }
            ehf r10 = defpackage.ehf.a(r10)     // Catch:{ all -> 0x09f4 }
            r5.<init>(r10)     // Catch:{ all -> 0x09f4 }
            r5 = r2
            efc r5 = (defpackage.efc) r5     // Catch:{ all -> 0x09f4 }
            eeh r5 = r5.b()     // Catch:{ all -> 0x09f4 }
            if (r5 != 0) goto L_0x07ba
            int r10 = r3.b     // Catch:{ all -> 0x09f4 }
            r13 = 5
            if (r10 != r13) goto L_0x0777
            r10 = r2
            efc r10 = (defpackage.efc) r10     // Catch:{ all -> 0x09f4 }
            grh r10 = r10.a     // Catch:{ all -> 0x09f4 }
            boolean r10 = r10.f()     // Catch:{ all -> 0x09f4 }
            if (r10 == 0) goto L_0x0777
            r5 = r2
            efc r5 = (defpackage.efc) r5     // Catch:{ all -> 0x09f4 }
            grh r5 = r5.a     // Catch:{ all -> 0x09f4 }
            java.lang.Object r5 = r5.b()     // Catch:{ all -> 0x09f4 }
            eer r5 = (defpackage.eer) r5     // Catch:{ all -> 0x09f4 }
            int r10 = r3.b     // Catch:{ all -> 0x09f4 }
            r13 = 5
            if (r10 != r13) goto L_0x0772
            java.lang.Object r10 = r3.c     // Catch:{ all -> 0x09f4 }
            java.lang.String r10 = (java.lang.String) r10     // Catch:{ all -> 0x09f4 }
        L_0x0772:
            eeh r5 = r5.a()     // Catch:{ all -> 0x09f4 }
            goto L_0x07ba
        L_0x0777:
            int r10 = r3.a     // Catch:{ all -> 0x09f4 }
            r13 = 2
            r10 = r10 & r13
            if (r10 == 0) goto L_0x0791
            boolean r10 = r3.g     // Catch:{ all -> 0x09f4 }
            if (r10 == 0) goto L_0x0791
            r5 = r2
            efc r5 = (defpackage.efc) r5     // Catch:{ all -> 0x09f4 }
            cxk r5 = r5.d     // Catch:{ all -> 0x09f4 }
            dyt r10 = r3.f     // Catch:{ all -> 0x09f4 }
            if (r10 != 0) goto L_0x078c
            dyt r10 = defpackage.dyt.l     // Catch:{ all -> 0x09f4 }
        L_0x078c:
            eeu r5 = r5.h(r10)     // Catch:{ all -> 0x09f4 }
            goto L_0x07ba
        L_0x0791:
            int r10 = r3.b     // Catch:{ all -> 0x09f4 }
            r13 = 13
            if (r10 != r13) goto L_0x079c
            java.lang.Object r10 = r3.c     // Catch:{ all -> 0x09f4 }
            ebx r10 = (defpackage.ebx) r10     // Catch:{ all -> 0x09f4 }
            goto L_0x079e
        L_0x079c:
            ebx r10 = defpackage.ebx.c     // Catch:{ all -> 0x09f4 }
        L_0x079e:
            int r10 = r10.a     // Catch:{ all -> 0x09f4 }
            r14 = 1
            r10 = r10 & r14
            if (r10 == 0) goto L_0x07ba
            r5 = r2
            efc r5 = (defpackage.efc) r5     // Catch:{ all -> 0x09f4 }
            cxk r5 = r5.c     // Catch:{ all -> 0x09f4 }
            int r10 = r3.b     // Catch:{ all -> 0x09f4 }
            if (r10 != r13) goto L_0x07b2
            java.lang.Object r10 = r3.c     // Catch:{ all -> 0x09f4 }
            ebx r10 = (defpackage.ebx) r10     // Catch:{ all -> 0x09f4 }
            goto L_0x07b4
        L_0x07b2:
            ebx r10 = defpackage.ebx.c     // Catch:{ all -> 0x09f4 }
        L_0x07b4:
            java.lang.String r10 = r10.b     // Catch:{ all -> 0x09f4 }
            eff r5 = r5.g(r10)     // Catch:{ all -> 0x09f4 }
        L_0x07ba:
            if (r5 == 0) goto L_0x07c1
            defpackage.efc.a(r7)     // Catch:{ all -> 0x09f4 }
            goto L_0x08a5
        L_0x07c1:
            efc r2 = (defpackage.efc) r2     // Catch:{ all -> 0x09f4 }
            eez r2 = r2.b     // Catch:{ all -> 0x09f4 }
            eeq r5 = defpackage.eeq.j     // Catch:{ all -> 0x09f4 }
            htk r5 = r5.l()     // Catch:{ all -> 0x09f4 }
            dyt r10 = r3.f     // Catch:{ all -> 0x09f4 }
            if (r10 != 0) goto L_0x07d1
            dyt r10 = defpackage.dyt.l     // Catch:{ all -> 0x09f4 }
        L_0x07d1:
            htq r13 = r5.b     // Catch:{ all -> 0x09f4 }
            boolean r13 = r13.B()     // Catch:{ all -> 0x09f4 }
            if (r13 != 0) goto L_0x07dc
            r5.u()     // Catch:{ all -> 0x09f4 }
        L_0x07dc:
            htq r13 = r5.b     // Catch:{ all -> 0x09f4 }
            eeq r13 = (defpackage.eeq) r13     // Catch:{ all -> 0x09f4 }
            r10.getClass()     // Catch:{ all -> 0x09f4 }
            r13.d = r10     // Catch:{ all -> 0x09f4 }
            int r10 = r13.a     // Catch:{ all -> 0x09f4 }
            r14 = 1
            r10 = r10 | r14
            r13.a = r10     // Catch:{ all -> 0x09f4 }
            int r10 = r3.b     // Catch:{ all -> 0x09f4 }
            r13 = 10
            if (r10 != r13) goto L_0x0812
            java.lang.Object r10 = r3.c     // Catch:{ all -> 0x09f4 }
            java.lang.Boolean r10 = (java.lang.Boolean) r10     // Catch:{ all -> 0x09f4 }
            boolean r10 = r10.booleanValue()     // Catch:{ all -> 0x09f4 }
            htq r13 = r5.b     // Catch:{ all -> 0x09f4 }
            boolean r13 = r13.B()     // Catch:{ all -> 0x09f4 }
            if (r13 != 0) goto L_0x0804
            r5.u()     // Catch:{ all -> 0x09f4 }
        L_0x0804:
            htq r13 = r5.b     // Catch:{ all -> 0x09f4 }
            eeq r13 = (defpackage.eeq) r13     // Catch:{ all -> 0x09f4 }
            int r14 = r13.a     // Catch:{ all -> 0x09f4 }
            r18 = 4
            r14 = r14 | 4
            r13.a = r14     // Catch:{ all -> 0x09f4 }
            r13.f = r10     // Catch:{ all -> 0x09f4 }
        L_0x0812:
            int r10 = r3.b     // Catch:{ all -> 0x09f4 }
            r13 = 17
            if (r10 != r13) goto L_0x0831
            htq r10 = r5.b     // Catch:{ all -> 0x09f4 }
            boolean r10 = r10.B()     // Catch:{ all -> 0x09f4 }
            if (r10 != 0) goto L_0x0823
            r5.u()     // Catch:{ all -> 0x09f4 }
        L_0x0823:
            htq r10 = r5.b     // Catch:{ all -> 0x09f4 }
            eeq r10 = (defpackage.eeq) r10     // Catch:{ all -> 0x09f4 }
            int r13 = r10.a     // Catch:{ all -> 0x09f4 }
            r14 = 8
            r13 = r13 | r14
            r10.a = r13     // Catch:{ all -> 0x09f4 }
            r13 = 1
            r10.g = r13     // Catch:{ all -> 0x09f4 }
        L_0x0831:
            int r10 = r3.d     // Catch:{ all -> 0x09f4 }
            int r13 = defpackage.doe.b(r10)     // Catch:{ all -> 0x09f4 }
            if (r13 == 0) goto L_0x0911
            r14 = 1
            if (r13 != r14) goto L_0x085f
            r13 = 12
            if (r10 != r13) goto L_0x0845
            java.lang.Object r10 = r3.e     // Catch:{ all -> 0x09f4 }
            ecc r10 = (defpackage.ecc) r10     // Catch:{ all -> 0x09f4 }
            goto L_0x0847
        L_0x0845:
            ecc r10 = defpackage.ecc.c     // Catch:{ all -> 0x09f4 }
        L_0x0847:
            htq r13 = r5.b     // Catch:{ all -> 0x09f4 }
            boolean r13 = r13.B()     // Catch:{ all -> 0x09f4 }
            if (r13 != 0) goto L_0x0852
            r5.u()     // Catch:{ all -> 0x09f4 }
        L_0x0852:
            htq r13 = r5.b     // Catch:{ all -> 0x09f4 }
            eeq r13 = (defpackage.eeq) r13     // Catch:{ all -> 0x09f4 }
            r10.getClass()     // Catch:{ all -> 0x09f4 }
            r13.c = r10     // Catch:{ all -> 0x09f4 }
            r10 = 4
            r13.b = r10     // Catch:{ all -> 0x09f4 }
            goto L_0x089b
        L_0x085f:
            int r13 = defpackage.doe.b(r10)     // Catch:{ all -> 0x09f4 }
            if (r13 == 0) goto L_0x090f
            r14 = 2
            if (r13 != r14) goto L_0x089b
            r13 = 14
            if (r10 != r13) goto L_0x087d
            java.lang.Object r10 = r3.e     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r10 = (java.lang.Integer) r10     // Catch:{ all -> 0x09f4 }
            int r10 = r10.intValue()     // Catch:{ all -> 0x09f4 }
            ewn r10 = defpackage.ewn.b(r10)     // Catch:{ all -> 0x09f4 }
            if (r10 != 0) goto L_0x087f
            ewn r10 = defpackage.ewn.TAG_DO_NOT_USE     // Catch:{ all -> 0x09f4 }
            goto L_0x087f
        L_0x087d:
            ewn r10 = defpackage.ewn.TAG_DO_NOT_USE     // Catch:{ all -> 0x09f4 }
        L_0x087f:
            htq r13 = r5.b     // Catch:{ all -> 0x09f4 }
            boolean r13 = r13.B()     // Catch:{ all -> 0x09f4 }
            if (r13 != 0) goto L_0x088a
            r5.u()     // Catch:{ all -> 0x09f4 }
        L_0x088a:
            htq r13 = r5.b     // Catch:{ all -> 0x09f4 }
            eeq r13 = (defpackage.eeq) r13     // Catch:{ all -> 0x09f4 }
            int r10 = r10.a()     // Catch:{ all -> 0x09f4 }
            java.lang.Integer r10 = java.lang.Integer.valueOf(r10)     // Catch:{ all -> 0x09f4 }
            r13.c = r10     // Catch:{ all -> 0x09f4 }
            r10 = 6
            r13.b = r10     // Catch:{ all -> 0x09f4 }
        L_0x089b:
            htq r5 = r5.r()     // Catch:{ all -> 0x09f4 }
            eeq r5 = (defpackage.eeq) r5     // Catch:{ all -> 0x09f4 }
            eeh r5 = r2.a(r5, r7, r15)     // Catch:{ all -> 0x09f4 }
        L_0x08a5:
            efh r26 = defpackage.dom.k(r6, r5)     // Catch:{ all -> 0x09f4 }
            java.lang.Object r1 = r1.e     // Catch:{ all -> 0x09f4 }
            dyt r2 = r3.f     // Catch:{ all -> 0x09f4 }
            if (r2 != 0) goto L_0x08b1
            dyt r2 = defpackage.dyt.l     // Catch:{ all -> 0x09f4 }
        L_0x08b1:
            r28 = r2
            eno r2 = new eno     // Catch:{ all -> 0x09f4 }
            r4.getClass()     // Catch:{ all -> 0x09f4 }
            r28.getClass()     // Catch:{ all -> 0x09f4 }
            r3 = r1
            cyw r3 = (defpackage.cyw) r3     // Catch:{ all -> 0x09f4 }
            java.lang.Object r3 = r3.d     // Catch:{ all -> 0x09f4 }
            r5 = r1
            cyw r5 = (defpackage.cyw) r5     // Catch:{ all -> 0x09f4 }
            java.lang.Object r5 = r5.a     // Catch:{ all -> 0x09f4 }
            java.lang.Object r3 = r3.b()     // Catch:{ all -> 0x09f4 }
            java.lang.Object r5 = r5.b()     // Catch:{ all -> 0x09f4 }
            r31 = r5
            jqs r31 = (defpackage.jqs) r31     // Catch:{ all -> 0x09f4 }
            r31.getClass()     // Catch:{ all -> 0x09f4 }
            r5 = r1
            cyw r5 = (defpackage.cyw) r5     // Catch:{ all -> 0x09f4 }
            java.lang.Object r5 = r5.b     // Catch:{ all -> 0x09f4 }
            java.lang.Object r5 = r5.b()     // Catch:{ all -> 0x09f4 }
            cqx r5 = (defpackage.cqx) r5     // Catch:{ all -> 0x09f4 }
            r5.getClass()     // Catch:{ all -> 0x09f4 }
            cyw r1 = (defpackage.cyw) r1     // Catch:{ all -> 0x09f4 }
            java.lang.Object r1 = r1.c     // Catch:{ all -> 0x09f4 }
            iiv r1 = (defpackage.iiv) r1     // Catch:{ all -> 0x09f4 }
            java.lang.Object r1 = r1.a     // Catch:{ all -> 0x09f4 }
            r32 = r1
            bzj r32 = (defpackage.bzj) r32     // Catch:{ all -> 0x09f4 }
            r32.getClass()     // Catch:{ all -> 0x09f4 }
            gqd r29 = defpackage.gqd.a     // Catch:{ all -> 0x09f4 }
            r30 = r3
            dlv r30 = (defpackage.dlv) r30     // Catch:{ all -> 0x09f4 }
            r24 = r2
            r25 = r4
            r24.<init>(r25, r26, r27, r28, r29, r30, r31, r32)     // Catch:{ all -> 0x09f4 }
            r1 = r2
        L_0x08ff:
            bmu r2 = r8.o     // Catch:{ all -> 0x09f4 }
            eny r1 = r2.w(r1)     // Catch:{ all -> 0x09f4 }
            eph r2 = new eph     // Catch:{ all -> 0x09f4 }
            java.lang.Object r3 = r12.a(r1)     // Catch:{ all -> 0x09f4 }
            r2.<init>(r1, r3)     // Catch:{ all -> 0x09f4 }
            goto L_0x0919
        L_0x090f:
            r1 = 0
            throw r1     // Catch:{ all -> 0x09f4 }
        L_0x0911:
            r1 = 0
            throw r1     // Catch:{ all -> 0x09f4 }
        L_0x0913:
            r1.<init>()     // Catch:{ all -> 0x09f4 }
            throw r1     // Catch:{ all -> 0x09f4 }
        L_0x0917:
            r10 = r4
            goto L_0x091a
        L_0x0919:
            r10 = r2
        L_0x091a:
            jqs r1 = r8.i     // Catch:{ all -> 0x09f4 }
            java.util.concurrent.Executor r2 = r8.a     // Catch:{ all -> 0x09f4 }
            bub r3 = new bub     // Catch:{ all -> 0x09f4 }
            r4 = 18
            r3.<init>(r4)     // Catch:{ all -> 0x09f4 }
            hko r3 = defpackage.gof.d(r3)     // Catch:{ all -> 0x09f4 }
            hme r4 = r10.b     // Catch:{ all -> 0x09f4 }
            java.lang.Class<java.lang.Throwable> r5 = java.lang.Throwable.class
            hme r2 = defpackage.hjl.g(r4, r5, r3, r2)     // Catch:{ all -> 0x09f4 }
            edc r3 = new edc     // Catch:{ all -> 0x09f4 }
            r4 = 14
            r5 = 0
            r3.<init>((defpackage.hme) r2, (defpackage.jlr) r5, (int) r4, (byte[]) r5)     // Catch:{ all -> 0x09f4 }
            jqz r3 = defpackage.job.R(r1, r3)     // Catch:{ all -> 0x09f4 }
            mz r1 = new mz     // Catch:{ all -> 0x09f4 }
            r1.<init>(r2, r4)     // Catch:{ all -> 0x09f4 }
            r3.w(r1)     // Catch:{ all -> 0x09f4 }
            jqs r12 = r8.i     // Catch:{ all -> 0x09f4 }
            enr r13 = r10.a     // Catch:{ all -> 0x09f4 }
            hme r19 = r13.j()     // Catch:{ all -> 0x09f4 }
            epj r14 = new epj     // Catch:{ all -> 0x09f4 }
            r6 = 0
            r7 = 1
            r1 = r14
            r2 = r33
            r4 = r19
            r5 = r13
            r1.<init>((defpackage.epe) r2, (defpackage.jqz) r3, (defpackage.hme) r4, (defpackage.enr) r5, (defpackage.jlr) r6, (int) r7)     // Catch:{ all -> 0x09f4 }
            hme r1 = defpackage.jqw.z(r12, r14)     // Catch:{ all -> 0x09f4 }
            bmu r2 = r8.p     // Catch:{ all -> 0x09f4 }
            int r15 = r9.a     // Catch:{ all -> 0x09f4 }
            int r3 = r11.a     // Catch:{ all -> 0x09f4 }
            dze r4 = r11.b     // Catch:{ all -> 0x09f4 }
            dyt r4 = r4.f     // Catch:{ all -> 0x09f4 }
            if (r4 != 0) goto L_0x096c
            dyt r4 = defpackage.dyt.l     // Catch:{ all -> 0x09f4 }
        L_0x096c:
            java.lang.String r5 = "getAudioLibInputParams(...)"
            defpackage.jnu.d(r4, r5)     // Catch:{ all -> 0x09f4 }
            dyt r5 = r13.b()     // Catch:{ all -> 0x09f4 }
            dyt r17 = defpackage.eve.a(r4, r5)     // Catch:{ all -> 0x09f4 }
            hme r18 = defpackage.ejw.f(r1)     // Catch:{ all -> 0x09f4 }
            ehg r1 = r9.b     // Catch:{ all -> 0x09f4 }
            emv r4 = new emv     // Catch:{ all -> 0x09f4 }
            r17.getClass()     // Catch:{ all -> 0x09f4 }
            r19.getClass()     // Catch:{ all -> 0x09f4 }
            r1.getClass()     // Catch:{ all -> 0x09f4 }
            java.lang.Object r5 = r2.b     // Catch:{ all -> 0x09f4 }
            java.lang.Object r5 = r5.b()     // Catch:{ all -> 0x09f4 }
            r21 = r5
            cyw r21 = (defpackage.cyw) r21     // Catch:{ all -> 0x09f4 }
            r21.getClass()     // Catch:{ all -> 0x09f4 }
            java.lang.Object r5 = r2.a     // Catch:{ all -> 0x09f4 }
            java.lang.Object r2 = r2.c     // Catch:{ all -> 0x09f4 }
            emf r5 = (defpackage.emf) r5     // Catch:{ all -> 0x09f4 }
            emd r22 = r5.b()     // Catch:{ all -> 0x09f4 }
            java.lang.Object r2 = r2.b()     // Catch:{ all -> 0x09f4 }
            r23 = r2
            hmi r23 = (defpackage.hmi) r23     // Catch:{ all -> 0x09f4 }
            r23.getClass()     // Catch:{ all -> 0x09f4 }
            r14 = r4
            r16 = r3
            r20 = r1
            r14.<init>(r15, r16, r17, r18, r19, r20, r21, r22, r23)     // Catch:{ all -> 0x09f4 }
            emr r1 = r8.h     // Catch:{ all -> 0x09f4 }
            int r2 = r11.a     // Catch:{ all -> 0x09f4 }
            enr r3 = r10.a     // Catch:{ all -> 0x09f4 }
            eej r3 = r3.d()     // Catch:{ all -> 0x09f4 }
            r1.h(r4, r2, r3)     // Catch:{ all -> 0x09f4 }
            java.util.List r1 = r8.k     // Catch:{ all -> 0x09f4 }
            enr r2 = r10.a     // Catch:{ all -> 0x09f4 }
            epc r3 = new epc     // Catch:{ all -> 0x09f4 }
            r5 = r35
            r3.<init>(r2, r9, r5, r11)     // Catch:{ all -> 0x09f4 }
            hme r12 = r4.a     // Catch:{ all -> 0x09f4 }
            byw r9 = r8.q     // Catch:{ all -> 0x09f4 }
            epb r2 = r3.b     // Catch:{ all -> 0x09f4 }
            enr r5 = r3.a     // Catch:{ all -> 0x09f4 }
            eej r5 = r5.d()     // Catch:{ all -> 0x09f4 }
            hme r11 = r5.b()     // Catch:{ all -> 0x09f4 }
            enr r5 = r3.a     // Catch:{ all -> 0x09f4 }
            epg r6 = r3.c     // Catch:{ all -> 0x09f4 }
            dyt r13 = r5.b()     // Catch:{ all -> 0x09f4 }
            int r14 = r6.a     // Catch:{ all -> 0x09f4 }
            ehg r10 = r2.b     // Catch:{ all -> 0x09f4 }
            euz r2 = r9.K(r10, r11, r12, r13, r14)     // Catch:{ all -> 0x09f4 }
            r2.c()     // Catch:{ all -> 0x09f4 }
            r1.add(r3)     // Catch:{ all -> 0x09f4 }
            monitor-exit(r33)
            return r4
        L_0x09f4:
            r0 = move-exception
            r1 = r0
            monitor-exit(r33)     // Catch:{ all -> 0x09f4 }
            throw r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.epe.c(epb, epp, epg, dsy):dyy");
    }
}
