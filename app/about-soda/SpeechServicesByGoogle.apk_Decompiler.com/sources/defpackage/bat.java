package defpackage;

import androidx.wear.ambient.AmbientModeSupport;
import j$.util.DesugarCollections;
import j$.util.Objects;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/* renamed from: bat  reason: default package */
/* compiled from: PG */
public final class bat {
    public static final bat a = AmbientModeSupport.AmbientCallback.a(new LinkedHashMap());
    public final Map b;

    public bat(bat bat) {
        jnu.e(bat, "other");
        this.b = new HashMap(bat.b);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:118:0x01ee, code lost:
        r2 = move-exception;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:120:?, code lost:
        defpackage.jnu.y(r3, r13);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:121:0x01f2, code lost:
        throw r2;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:25:0x005e, code lost:
        r3 = move-exception;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:27:?, code lost:
        defpackage.jnu.y(r13, r2);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:28:0x0062, code lost:
        throw r3;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static final defpackage.bat a(byte[] r13) {
        /*
            java.lang.String r0 = "Error in Data#fromByteArray: "
            java.lang.String r1 = "bytes"
            defpackage.jnu.e(r13, r1)
            int r1 = r13.length
            r2 = 10240(0x2800, float:1.4349E-41)
            if (r1 > r2) goto L_0x020e
            if (r1 != 0) goto L_0x0012
            bat r13 = a
            goto L_0x020d
        L_0x0012:
            java.util.LinkedHashMap r1 = new java.util.LinkedHashMap
            r1.<init>()
            java.io.ByteArrayInputStream r2 = new java.io.ByteArrayInputStream     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            r2.<init>(r13)     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            r13 = 2
            byte[] r3 = new byte[r13]     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            r2.read(r3)     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            r4 = 0
            byte r5 = r3[r4]     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            r6 = -84
            r7 = 1
            if (r5 != r6) goto L_0x0032
            byte r3 = r3[r7]     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            r5 = -19
            if (r3 != r5) goto L_0x0032
            r3 = r7
            goto L_0x0033
        L_0x0032:
            r3 = r4
        L_0x0033:
            r2.reset()     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            r5 = 0
            if (r3 == 0) goto L_0x0063
            java.io.ObjectInputStream r13 = new java.io.ObjectInputStream     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            r13.<init>(r2)     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            int r2 = r13.readInt()     // Catch:{ all -> 0x005c }
        L_0x0042:
            if (r4 >= r2) goto L_0x0057
            java.lang.String r3 = r13.readUTF()     // Catch:{ all -> 0x005c }
            java.lang.String r6 = "readUTF()"
            defpackage.jnu.d(r3, r6)     // Catch:{ all -> 0x005c }
            java.lang.Object r6 = r13.readObject()     // Catch:{ all -> 0x005c }
            r1.put(r3, r6)     // Catch:{ all -> 0x005c }
            int r4 = r4 + 1
            goto L_0x0042
        L_0x0057:
            defpackage.jnu.y(r13, r5)     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            goto L_0x0208
        L_0x005c:
            r2 = move-exception
            throw r2     // Catch:{ all -> 0x005e }
        L_0x005e:
            r3 = move-exception
            defpackage.jnu.y(r13, r2)     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            throw r3     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
        L_0x0063:
            java.io.DataInputStream r3 = new java.io.DataInputStream     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            r3.<init>(r2)     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            short r2 = r3.readShort()     // Catch:{ all -> 0x01ec }
            r6 = -21521(0xffffffffffffabef, float:NaN)
            if (r2 != r6) goto L_0x01e0
            short r2 = r3.readShort()     // Catch:{ all -> 0x01ec }
            if (r2 != r7) goto L_0x01d4
            int r2 = r3.readInt()     // Catch:{ all -> 0x01ec }
            r6 = r4
        L_0x007b:
            if (r6 >= r2) goto L_0x01d0
            byte r8 = r3.readByte()     // Catch:{ all -> 0x01ec }
            if (r8 != 0) goto L_0x0086
            r8 = r5
            goto L_0x01b4
        L_0x0086:
            if (r8 != r7) goto L_0x0092
            boolean r8 = r3.readBoolean()     // Catch:{ all -> 0x01ec }
            java.lang.Boolean r8 = java.lang.Boolean.valueOf(r8)     // Catch:{ all -> 0x01ec }
            goto L_0x01b4
        L_0x0092:
            if (r8 != r13) goto L_0x009e
            byte r8 = r3.readByte()     // Catch:{ all -> 0x01ec }
            java.lang.Byte r8 = java.lang.Byte.valueOf(r8)     // Catch:{ all -> 0x01ec }
            goto L_0x01b4
        L_0x009e:
            r9 = 3
            if (r8 != r9) goto L_0x00ab
            int r8 = r3.readInt()     // Catch:{ all -> 0x01ec }
            java.lang.Integer r8 = java.lang.Integer.valueOf(r8)     // Catch:{ all -> 0x01ec }
            goto L_0x01b4
        L_0x00ab:
            r9 = 4
            if (r8 != r9) goto L_0x00b8
            long r8 = r3.readLong()     // Catch:{ all -> 0x01ec }
            java.lang.Long r8 = java.lang.Long.valueOf(r8)     // Catch:{ all -> 0x01ec }
            goto L_0x01b4
        L_0x00b8:
            r9 = 5
            if (r8 != r9) goto L_0x00c5
            float r8 = r3.readFloat()     // Catch:{ all -> 0x01ec }
            java.lang.Float r8 = java.lang.Float.valueOf(r8)     // Catch:{ all -> 0x01ec }
            goto L_0x01b4
        L_0x00c5:
            r9 = 6
            if (r8 != r9) goto L_0x00d2
            double r8 = r3.readDouble()     // Catch:{ all -> 0x01ec }
            java.lang.Double r8 = java.lang.Double.valueOf(r8)     // Catch:{ all -> 0x01ec }
            goto L_0x01b4
        L_0x00d2:
            r9 = 7
            if (r8 != r9) goto L_0x00db
            java.lang.String r8 = r3.readUTF()     // Catch:{ all -> 0x01ec }
            goto L_0x01b4
        L_0x00db:
            r9 = 8
            if (r8 != r9) goto L_0x00fa
            int r8 = r3.readInt()     // Catch:{ all -> 0x01ec }
            java.lang.Boolean[] r9 = new java.lang.Boolean[r8]     // Catch:{ all -> 0x01ec }
            r10 = r4
        L_0x00e6:
            if (r10 >= r8) goto L_0x00f5
            boolean r11 = r3.readBoolean()     // Catch:{ all -> 0x01ec }
            java.lang.Boolean r11 = java.lang.Boolean.valueOf(r11)     // Catch:{ all -> 0x01ec }
            r9[r10] = r11     // Catch:{ all -> 0x01ec }
            int r10 = r10 + 1
            goto L_0x00e6
        L_0x00f5:
            r8 = r9
            java.io.Serializable r8 = (java.io.Serializable) r8     // Catch:{ all -> 0x01ec }
            goto L_0x01b4
        L_0x00fa:
            r9 = 9
            if (r8 != r9) goto L_0x0119
            int r8 = r3.readInt()     // Catch:{ all -> 0x01ec }
            java.lang.Byte[] r9 = new java.lang.Byte[r8]     // Catch:{ all -> 0x01ec }
            r10 = r4
        L_0x0105:
            if (r10 >= r8) goto L_0x0114
            byte r11 = r3.readByte()     // Catch:{ all -> 0x01ec }
            java.lang.Byte r11 = java.lang.Byte.valueOf(r11)     // Catch:{ all -> 0x01ec }
            r9[r10] = r11     // Catch:{ all -> 0x01ec }
            int r10 = r10 + 1
            goto L_0x0105
        L_0x0114:
            r8 = r9
            java.io.Serializable r8 = (java.io.Serializable) r8     // Catch:{ all -> 0x01ec }
            goto L_0x01b4
        L_0x0119:
            r9 = 10
            if (r8 != r9) goto L_0x0138
            int r8 = r3.readInt()     // Catch:{ all -> 0x01ec }
            java.lang.Integer[] r9 = new java.lang.Integer[r8]     // Catch:{ all -> 0x01ec }
            r10 = r4
        L_0x0124:
            if (r10 >= r8) goto L_0x0133
            int r11 = r3.readInt()     // Catch:{ all -> 0x01ec }
            java.lang.Integer r11 = java.lang.Integer.valueOf(r11)     // Catch:{ all -> 0x01ec }
            r9[r10] = r11     // Catch:{ all -> 0x01ec }
            int r10 = r10 + 1
            goto L_0x0124
        L_0x0133:
            r8 = r9
            java.io.Serializable r8 = (java.io.Serializable) r8     // Catch:{ all -> 0x01ec }
            goto L_0x01b4
        L_0x0138:
            r9 = 11
            if (r8 != r9) goto L_0x0156
            int r8 = r3.readInt()     // Catch:{ all -> 0x01ec }
            java.lang.Long[] r9 = new java.lang.Long[r8]     // Catch:{ all -> 0x01ec }
            r10 = r4
        L_0x0143:
            if (r10 >= r8) goto L_0x0152
            long r11 = r3.readLong()     // Catch:{ all -> 0x01ec }
            java.lang.Long r11 = java.lang.Long.valueOf(r11)     // Catch:{ all -> 0x01ec }
            r9[r10] = r11     // Catch:{ all -> 0x01ec }
            int r10 = r10 + 1
            goto L_0x0143
        L_0x0152:
            r8 = r9
            java.io.Serializable r8 = (java.io.Serializable) r8     // Catch:{ all -> 0x01ec }
            goto L_0x01b4
        L_0x0156:
            r9 = 12
            if (r8 != r9) goto L_0x0174
            int r8 = r3.readInt()     // Catch:{ all -> 0x01ec }
            java.lang.Float[] r9 = new java.lang.Float[r8]     // Catch:{ all -> 0x01ec }
            r10 = r4
        L_0x0161:
            if (r10 >= r8) goto L_0x0170
            float r11 = r3.readFloat()     // Catch:{ all -> 0x01ec }
            java.lang.Float r11 = java.lang.Float.valueOf(r11)     // Catch:{ all -> 0x01ec }
            r9[r10] = r11     // Catch:{ all -> 0x01ec }
            int r10 = r10 + 1
            goto L_0x0161
        L_0x0170:
            r8 = r9
            java.io.Serializable r8 = (java.io.Serializable) r8     // Catch:{ all -> 0x01ec }
            goto L_0x01b4
        L_0x0174:
            r9 = 13
            if (r8 != r9) goto L_0x0192
            int r8 = r3.readInt()     // Catch:{ all -> 0x01ec }
            java.lang.Double[] r9 = new java.lang.Double[r8]     // Catch:{ all -> 0x01ec }
            r10 = r4
        L_0x017f:
            if (r10 >= r8) goto L_0x018e
            double r11 = r3.readDouble()     // Catch:{ all -> 0x01ec }
            java.lang.Double r11 = java.lang.Double.valueOf(r11)     // Catch:{ all -> 0x01ec }
            r9[r10] = r11     // Catch:{ all -> 0x01ec }
            int r10 = r10 + 1
            goto L_0x017f
        L_0x018e:
            r8 = r9
            java.io.Serializable r8 = (java.io.Serializable) r8     // Catch:{ all -> 0x01ec }
            goto L_0x01b4
        L_0x0192:
            r9 = 14
            if (r8 != r9) goto L_0x01c4
            int r8 = r3.readInt()     // Catch:{ all -> 0x01ec }
            java.lang.String[] r9 = new java.lang.String[r8]     // Catch:{ all -> 0x01ec }
            r10 = r4
        L_0x019d:
            if (r10 >= r8) goto L_0x01b1
            java.lang.String r11 = r3.readUTF()     // Catch:{ all -> 0x01ec }
            java.lang.String r12 = "androidx.work.Data-95ed6082-b8e9-46e8-a73f-ff56f00f5d9d"
            boolean r12 = defpackage.jnu.i(r11, r12)     // Catch:{ all -> 0x01ec }
            if (r7 != r12) goto L_0x01ac
            r11 = r5
        L_0x01ac:
            r9[r10] = r11     // Catch:{ all -> 0x01ec }
            int r10 = r10 + 1
            goto L_0x019d
        L_0x01b1:
            r8 = r9
            java.io.Serializable r8 = (java.io.Serializable) r8     // Catch:{ all -> 0x01ec }
        L_0x01b4:
            java.lang.String r9 = r3.readUTF()     // Catch:{ all -> 0x01ec }
            java.lang.String r10 = "key"
            defpackage.jnu.d(r9, r10)     // Catch:{ all -> 0x01ec }
            r1.put(r9, r8)     // Catch:{ all -> 0x01ec }
            int r6 = r6 + 1
            goto L_0x007b
        L_0x01c4:
            java.lang.IllegalStateException r13 = new java.lang.IllegalStateException     // Catch:{ all -> 0x01ec }
            java.lang.String r2 = "Unsupported type "
            java.lang.String r2 = defpackage.a.ak(r8, r2)     // Catch:{ all -> 0x01ec }
            r13.<init>(r2)     // Catch:{ all -> 0x01ec }
            throw r13     // Catch:{ all -> 0x01ec }
        L_0x01d0:
            defpackage.jnu.y(r3, r5)     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            goto L_0x0208
        L_0x01d4:
            java.lang.String r13 = "Unsupported version number: "
            java.lang.String r13 = defpackage.a.ak(r2, r13)     // Catch:{ all -> 0x01ec }
            java.lang.IllegalStateException r2 = new java.lang.IllegalStateException     // Catch:{ all -> 0x01ec }
            r2.<init>(r13)     // Catch:{ all -> 0x01ec }
            throw r2     // Catch:{ all -> 0x01ec }
        L_0x01e0:
            java.lang.String r13 = "Magic number doesn't match: "
            java.lang.String r13 = defpackage.a.ak(r2, r13)     // Catch:{ all -> 0x01ec }
            java.lang.IllegalStateException r2 = new java.lang.IllegalStateException     // Catch:{ all -> 0x01ec }
            r2.<init>(r13)     // Catch:{ all -> 0x01ec }
            throw r2     // Catch:{ all -> 0x01ec }
        L_0x01ec:
            r13 = move-exception
            throw r13     // Catch:{ all -> 0x01ee }
        L_0x01ee:
            r2 = move-exception
            defpackage.jnu.y(r3, r13)     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
            throw r2     // Catch:{ IOException -> 0x01fe, ClassNotFoundException -> 0x01f3 }
        L_0x01f3:
            r13 = move-exception
            java.lang.String r2 = defpackage.bau.a
            bbk r3 = defpackage.bbk.a()
            r3.d(r2, r0, r13)
            goto L_0x0208
        L_0x01fe:
            r13 = move-exception
            java.lang.String r2 = defpackage.bau.a
            bbk r3 = defpackage.bbk.a()
            r3.d(r2, r0, r13)
        L_0x0208:
            bat r13 = new bat
            r13.<init>((java.util.Map) r1)
        L_0x020d:
            return r13
        L_0x020e:
            java.lang.IllegalStateException r13 = new java.lang.IllegalStateException
            java.lang.String r0 = "Data cannot occupy more than 10240 bytes when serialized"
            r13.<init>(r0)
            throw r13
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bat.a(byte[]):bat");
    }

    public final String b(String str) {
        Object obj = this.b.get(str);
        if (obj instanceof String) {
            return (String) obj;
        }
        return null;
    }

    public final Map c() {
        Map unmodifiableMap = DesugarCollections.unmodifiableMap(this.b);
        jnu.d(unmodifiableMap, "unmodifiableMap(values)");
        return unmodifiableMap;
    }

    public final boolean d(String str, Class cls) {
        Object obj = this.b.get(str);
        if (obj == null || !cls.isAssignableFrom(obj.getClass())) {
            return false;
        }
        return true;
    }

    /* JADX WARNING: Removed duplicated region for block: B:28:0x0067 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:31:0x002f A[SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean equals(java.lang.Object r8) {
        /*
            r7 = this;
            r0 = 1
            if (r7 != r8) goto L_0x0004
            return r0
        L_0x0004:
            r1 = 0
            if (r8 == 0) goto L_0x0069
            java.lang.Class r2 = r7.getClass()
            java.lang.Class r3 = r8.getClass()
            boolean r2 = defpackage.jnu.i(r2, r3)
            if (r2 != 0) goto L_0x0016
            goto L_0x0069
        L_0x0016:
            bat r8 = (defpackage.bat) r8
            java.util.Map r2 = r7.b
            java.util.Map r3 = r8.b
            java.util.Set r2 = r2.keySet()
            java.util.Set r3 = r3.keySet()
            boolean r3 = defpackage.jnu.i(r2, r3)
            if (r3 != 0) goto L_0x002b
            return r1
        L_0x002b:
            java.util.Iterator r2 = r2.iterator()
        L_0x002f:
            boolean r3 = r2.hasNext()
            if (r3 == 0) goto L_0x0068
            java.lang.Object r3 = r2.next()
            java.lang.String r3 = (java.lang.String) r3
            java.util.Map r4 = r7.b
            java.lang.Object r4 = r4.get(r3)
            java.util.Map r5 = r8.b
            java.lang.Object r3 = r5.get(r3)
            if (r4 == 0) goto L_0x0065
            if (r3 != 0) goto L_0x004c
            goto L_0x0065
        L_0x004c:
            boolean r5 = r4 instanceof java.lang.Object[]
            if (r5 == 0) goto L_0x005e
            r5 = r4
            java.lang.Object[] r5 = (java.lang.Object[]) r5
            boolean r6 = r3 instanceof java.lang.Object[]
            if (r6 == 0) goto L_0x005e
            java.lang.Object[] r3 = (java.lang.Object[]) r3
            boolean r3 = defpackage.jji.T(r5, r3)
            goto L_0x0062
        L_0x005e:
            boolean r3 = defpackage.jnu.i(r4, r3)
        L_0x0062:
            if (r3 != 0) goto L_0x002f
            goto L_0x0067
        L_0x0065:
            if (r4 == r3) goto L_0x002f
        L_0x0067:
            return r1
        L_0x0068:
            return r0
        L_0x0069:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bat.equals(java.lang.Object):boolean");
    }

    public final int hashCode() {
        int i;
        int i2 = 0;
        for (Map.Entry entry : this.b.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof Object[]) {
                i = Objects.hashCode(entry.getKey()) ^ Arrays.deepHashCode((Object[]) value);
            } else {
                i = entry.hashCode();
            }
            i2 += i;
        }
        return i2 * 31;
    }

    public final String toString() {
        return "Data {" + jji.M(this.b.entrySet(), (CharSequence) null, (CharSequence) null, (CharSequence) null, wl.g, 31) + "}";
    }

    public bat(Map map) {
        this.b = new HashMap(map);
    }
}
