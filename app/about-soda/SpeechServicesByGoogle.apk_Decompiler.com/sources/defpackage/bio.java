package defpackage;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import androidx.work.impl.foreground.SystemForegroundService;
import java.util.UUID;

/* renamed from: bio  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bio implements jmp {
    public final /* synthetic */ bip a;
    public final /* synthetic */ UUID b;
    public final /* synthetic */ baz c;
    public final /* synthetic */ Context d;

    public /* synthetic */ bio(bip bip, UUID uuid, baz baz, Context context) {
        this.a = bip;
        this.b = uuid;
        this.c = baz;
        this.d = context;
    }

    public final Object a() {
        baz baz;
        UUID uuid = this.b;
        bip bip = this.a;
        bhf bhf = bip.b;
        String uuid2 = uuid.toString();
        bhe b2 = bhf.b(uuid2);
        if (b2 == null || b2.c.a()) {
            throw new IllegalStateException("Calls to setForegroundAsync() must complete before a ListenableWorker signals completion of work by returning an instance of Result.");
        }
        bga bga = bip.a;
        synchronized (((bcp) bga).j) {
            bbk.a().e(bcp.a, a.ap(uuid2, "Moving WorkSpec (", ") to the foreground"));
            eez eez = (eez) ((bcp) bga).f.remove(uuid2);
            baz = this.c;
            if (eez != null) {
                if (((bcp) bga).b == null) {
                    ((bcp) bga).b = bil.a(((bcp) bga).c, "ProcessorForegroundLck");
                    ((bcp) bga).b.acquire();
                }
                ((bcp) bga).e.put(uuid2, eez);
                Context context = ((bcp) bga).c;
                bgt h = eez.h();
                Intent intent = new Intent(context, SystemForegroundService.class);
                intent.setAction("ACTION_START_FOREGROUND");
                intent.putExtra("KEY_WORKSPEC_ID", h.a);
                intent.putExtra("KEY_GENERATION", h.b);
                intent.putExtra("KEY_NOTIFICATION_ID", baz.a);
                intent.putExtra("KEY_FOREGROUND_SERVICE_TYPE", baz.b);
                intent.putExtra("KEY_NOTIFICATION", baz.c);
                ComponentName unused = ((bcp) bga).c.startForegroundService(intent);
            }
        }
        Context context2 = this.d;
        bgt f = wg.f(b2);
        Intent intent2 = new Intent(context2, SystemForegroundService.class);
        intent2.setAction("ACTION_NOTIFY");
        intent2.putExtra("KEY_NOTIFICATION_ID", baz.a);
        intent2.putExtra("KEY_FOREGROUND_SERVICE_TYPE", baz.b);
        intent2.putExtra("KEY_NOTIFICATION", baz.c);
        intent2.putExtra("KEY_WORKSPEC_ID", f.a);
        intent2.putExtra("KEY_GENERATION", f.b);
        context2.startService(intent2);
        return null;
    }
}
