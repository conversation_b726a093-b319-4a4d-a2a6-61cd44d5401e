package defpackage;

import java.util.Map;

/* renamed from: cob  reason: default package */
/* compiled from: PG */
public final class cob implements iiu {
    private final jjk a;
    private final jjk b;

    public cob(jjk jjk, jjk jjk2) {
        this.a = jjk;
        this.b = jjk2;
    }

    /* renamed from: a */
    public final cpp b() {
        cpp cpp;
        Map map = (Map) ((iiv) this.a).a;
        int size = map.size();
        jjk jjk = this.b;
        if (size == 0) {
            cpp = ((cpq) jjk).b();
        } else if (size == 1) {
            cpp = (cpp) fvf.T(map.keySet());
        } else {
            throw new IllegalArgumentException("More than 1 ThreadMonitoringConfiguration: ".concat(String.valueOf(jjk.toString())));
        }
        hzz.u(cpp);
        return cpp;
    }
}
