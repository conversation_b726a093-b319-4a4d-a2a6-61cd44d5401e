package defpackage;

import android.car.drivingstate.CarUxRestrictions;
import android.content.Context;
import android.graphics.drawable.Drawable;
import android.widget.Toast;
import com.google.android.tts.R;
import java.lang.ref.WeakReference;

/* renamed from: bml  reason: default package */
/* compiled from: PG */
public final class bml {
    public final boolean a;
    public final boolean b;
    public final boolean c;
    public final boolean d;
    public final boolean e;
    public final int f;
    public CarUxRestrictions g;
    public final CharSequence h;
    public final Drawable i;
    public final bmk j;
    public final bmi k;
    public final boolean l;
    public boolean m;
    public boolean n;
    public boolean o;
    private final WeakReference p;
    private WeakReference q = new WeakReference((Object) null);
    private final int r;
    private final bny s;

    public bml(bmh bmh) {
        bmg bmg = new bmg(this);
        this.s = bmg;
        WeakReference weakReference = bmh.a;
        this.p = weakReference;
        this.f = bmh.f;
        this.a = bmh.m;
        this.b = bmh.p;
        this.h = bmh.g;
        this.i = bmh.h;
        this.j = bmh.i;
        this.k = bmh.j;
        this.l = true;
        this.m = bmh.n;
        this.n = bmh.o;
        this.o = bmh.q;
        this.c = bmh.r;
        this.d = bmh.l;
        this.e = bmh.k;
        this.r = bmh.t;
        bnz.b((Context) weakReference.get()).c(bmg);
    }

    public final void a() {
        if (this.l && this.n) {
            if (!d()) {
                if (this.b) {
                    this.o = !this.o;
                    c();
                }
                if (this.a) {
                    this.m = !this.m;
                    c();
                }
                bmk bmk = this.j;
                if (bmk != null) {
                    bmk.a(this);
                    return;
                }
                return;
            }
            Toast.makeText((Context) this.p.get(), R.string.car_ui_restricted_while_driving, 1).show();
        }
    }

    public final void b(bmj bmj) {
        this.q = new WeakReference(bmj);
    }

    public final void c() {
        bmj bmj = (bmj) this.q.get();
        if (bmj != null) {
            bmj.onMenuItemChanged(this);
        }
    }

    public final boolean d() {
        CarUxRestrictions carUxRestrictions = this.g;
        if (carUxRestrictions == null) {
            return true;
        }
        if ((carUxRestrictions.getActiveRestrictions() & this.r) != 0) {
            return true;
        }
        return false;
    }
}
