package defpackage;

/* renamed from: eum  reason: default package */
/* compiled from: PG */
public final class eum {
    public static final euj a = new euj();
    public final euo b;
    public final euv c;

    public eum(euo euo, euv euv) {
        jnu.e(euo, "memoryStore");
        jnu.e(euv, "persistentStore");
        this.b = euo;
        this.c = euv;
    }

    public final boolean a(eld eld) {
        jnu.e(eld, "storingDestination");
        ele b2 = ele.b(eld.b);
        if (b2 == null) {
            b2 = ele.MODE_DEFAULT_MEMORY;
        }
        int ordinal = b2.ordinal();
        if (ordinal == 0 || ordinal == 1) {
            return true;
        }
        if (ordinal == 2) {
            return this.c.b;
        }
        throw new jjq();
    }
}
