package defpackage;

/* renamed from: eow  reason: default package */
/* compiled from: PG */
public final class eow {
    public final int a;
    public final ejn b;
    public final boolean c;

    public eow() {
        this((byte[]) null);
    }

    public final epp a() {
        return new epp(this.a, this.b);
    }

    public final String b() {
        String r = fbi.r(this.b.b);
        return "route(token(" + this.a + "), " + r + ")";
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof eow)) {
            return false;
        }
        eow eow = (eow) obj;
        if (this.a == eow.a && jnu.i(this.b, eow.b) && this.c == eow.c) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (((this.a * 31) + this.b.hashCode()) * 31) + a.f(this.c);
    }

    public final String toString() {
        return "SessionRouteData(routeToken=" + this.a + ", routeData=" + this.b + ", isInactive=" + this.c + ")";
    }

    public eow(int i, ejn ejn, boolean z) {
        jnu.e(ejn, "routeData");
        this.a = i;
        this.b = ejn;
        this.c = z;
    }

    public /* synthetic */ eow(byte[] bArr) {
        this(-1, ejn.a(), true);
    }
}
