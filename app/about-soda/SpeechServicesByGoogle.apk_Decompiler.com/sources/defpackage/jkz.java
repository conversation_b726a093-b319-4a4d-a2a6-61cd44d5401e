package defpackage;

import java.util.ConcurrentModificationException;
import java.util.ListIterator;
import java.util.NoSuchElementException;

/* renamed from: jkz  reason: default package */
/* compiled from: PG */
final class jkz implements ListIterator, joc {
    private int a;
    private int b = -1;
    private int c;
    private final /* synthetic */ int d;
    private final jkl e;

    public jkz(jky jky, int i, int i2) {
        this.d = i2;
        this.e = jky;
        this.a = i;
        jky jky2 = jky;
        this.c = jky.modCount;
    }

    private final void a() {
        if (((jla) this.e).modCount != this.c) {
            throw new ConcurrentModificationException();
        }
    }

    private final void b() {
        if (((jky) this.e).d.modCount != this.c) {
            throw new ConcurrentModificationException();
        }
    }

    public final void add(Object obj) {
        if (this.d != 0) {
            b();
            int i = this.a;
            this.a = i + 1;
            ((jky) this.e).add(i, obj);
            this.b = -1;
            this.c = ((jky) this.e).modCount;
            return;
        }
        a();
        int i2 = this.a;
        this.a = i2 + 1;
        ((jla) this.e).add(i2, obj);
        this.b = -1;
        this.c = ((jla) this.e).modCount;
    }

    public final boolean hasNext() {
        if (this.d != 0) {
            if (this.a < ((jky) this.e).c) {
                return true;
            }
            return false;
        }
        if (this.a < ((jla) this.e).c) {
            return true;
        }
        return false;
    }

    public final boolean hasPrevious() {
        if (this.d != 0) {
            if (this.a > 0) {
                return true;
            }
            return false;
        } else if (this.a > 0) {
            return true;
        } else {
            return false;
        }
    }

    public final Object next() {
        if (this.d != 0) {
            b();
            jkl jkl = this.e;
            int i = this.a;
            jky jky = (jky) jkl;
            if (i < jky.c) {
                this.a = i + 1;
                this.b = i;
                return jky.a[jky.b + i];
            }
            throw new NoSuchElementException();
        }
        a();
        jkl jkl2 = this.e;
        int i2 = this.a;
        jla jla = (jla) jkl2;
        if (i2 < jla.c) {
            this.a = i2 + 1;
            this.b = i2;
            return jla.b[i2];
        }
        throw new NoSuchElementException();
    }

    public final int nextIndex() {
        if (this.d != 0) {
            return this.a;
        }
        return this.a;
    }

    public final Object previous() {
        if (this.d != 0) {
            b();
            int i = this.a;
            if (i > 0) {
                int i2 = i - 1;
                this.a = i2;
                this.b = i2;
                jky jky = (jky) this.e;
                return jky.a[jky.b + i2];
            }
            throw new NoSuchElementException();
        }
        a();
        int i3 = this.a;
        if (i3 > 0) {
            int i4 = i3 - 1;
            this.a = i4;
            this.b = i4;
            return ((jla) this.e).b[i4];
        }
        throw new NoSuchElementException();
    }

    public final int previousIndex() {
        int i = this.d;
        return this.a - 1;
    }

    public final void remove() {
        if (this.d != 0) {
            b();
            int i = this.b;
            if (i != -1) {
                this.e.b(i);
                this.a = this.b;
                this.b = -1;
                this.c = ((jky) this.e).modCount;
                return;
            }
            throw new IllegalStateException("Call next() or previous() before removing element from the iterator.");
        }
        a();
        int i2 = this.b;
        if (i2 != -1) {
            this.e.b(i2);
            this.a = this.b;
            this.b = -1;
            this.c = ((jla) this.e).modCount;
            return;
        }
        throw new IllegalStateException("Call next() or previous() before removing element from the iterator.");
    }

    public final void set(Object obj) {
        if (this.d != 0) {
            b();
            int i = this.b;
            if (i != -1) {
                ((jky) this.e).set(i, obj);
                return;
            }
            throw new IllegalStateException("Call next() or previous() before replacing element from the iterator.");
        }
        a();
        int i2 = this.b;
        if (i2 != -1) {
            ((jla) this.e).set(i2, obj);
            return;
        }
        throw new IllegalStateException("Call next() or previous() before replacing element from the iterator.");
    }

    public jkz(jla jla, int i, int i2) {
        this.d = i2;
        this.e = jla;
        this.a = i;
        jla jla2 = jla;
        this.c = jla.modCount;
    }
}
