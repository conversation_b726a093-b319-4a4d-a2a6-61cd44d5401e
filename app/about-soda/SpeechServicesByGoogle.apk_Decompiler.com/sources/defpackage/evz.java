package defpackage;

/* renamed from: evz  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class evz implements gsb {
    public final /* synthetic */ bmu a;
    private final /* synthetic */ int b;

    public /* synthetic */ evz(bmu bmu, int i) {
        this.b = i;
        this.a = bmu;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                fqy g = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/drawable_is_null", new fqx("size", Integer.class));
                g.c();
                return g;
            case 1:
                fqy g2 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/daily_forecast_invalid", new fqx("source", Integer.class), new fqx("empty_list", Boolean.class), new fqx("missing_start_date", Boolean.class), new fqx("missing_high_temperature", Boolean.class), new fqx("missing_low_temperature", Boolean.class), new fqx("missing_icon_url", Boolean.class));
                g2.c();
                return g2;
            case 2:
                fqy g3 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/context/fetch_status_count", new fqx("client_id", String.class), new fqx("caching_strategy", String.class), new fqx("status", String.class));
                g3.c();
                return g3;
            case 3:
                fqy g4 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/verticals/memory/garbage_collection/started_count", new fqx[0]);
                g4.c();
                return g4;
            case 4:
                fqy g5 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/surfaces/bisto/first_stage_hotword_triggered", new fqx("model_id", String.class));
                g5.c();
                return g5;
            case 5:
                fqy g6 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/expired_weather_cards", new fqx("count", Integer.class));
                g6.c();
                return g6;
            case 6:
                fqy g7 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/feature_type", new fqx("feature_type", Integer.class));
                g7.c();
                return g7;
            case 7:
                fqy g8 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/hourly_forecast_cards_icons_mismatch", new fqx[0]);
                g8.c();
                return g8;
            case 8:
                fqy g9 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/hourly_forecast_data_invalid", new fqx("empty_list", Boolean.class), new fqx("missing_utc_date", Boolean.class), new fqx("missing_start_hour", Boolean.class), new fqx("missing_temperature", Boolean.class), new fqx("missing_icon_url", Boolean.class));
                g9.c();
                return g9;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fqy g10 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/receive_status", new fqx("status", String.class), new fqx("action", String.class), new fqx("data_origin", String.class));
                g10.c();
                return g10;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fqy g11 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/refreshed_immediately", new fqx[0]);
                g11.c();
                return g11;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                fqy g12 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/send_status", new fqx("status", String.class), new fqx("action", String.class));
                g12.c();
                return g12;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                fqv c = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/weather/sno_update_delta", new fqx("app_version", String.class));
                c.c();
                return c;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                fqy g13 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/trigger_status", new fqx("status", String.class), new fqx("action", String.class), new fqx("request_type", String.class));
                g13.c();
                return g13;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                fqy g14 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/weather_card_wrapper_cache", new fqx[0]);
                g14.c();
                return g14;
            case 15:
                fqy g15 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/verticals/memory/image_loading/failure_count", new fqx[0]);
                g15.c();
                return g15;
            case 16:
                fqy g16 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/context/invalidation_flow_setup_count", new fqx("client_id", String.class), new fqx("call_site", String.class), new fqx("from_process", String.class), new fqx("to_process", String.class));
                g16.c();
                return g16;
            case 17:
                fqv c2 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/weather/weather_temperature_current_to_hourly_forecast_delta", new fqx("staleness_in_hours", Integer.class), new fqx("hourly_forecast_enabled", Boolean.class), new fqx("waa_enabled", Boolean.class));
                c2.c();
                return c2;
            case 18:
                fqv c3 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/weather/weather_temperature_delta", new fqx("app_version", String.class), new fqx("is_bluechip", Boolean.class), new fqx("location_history_enabled", Boolean.class), new fqx("waa_enabled", Boolean.class), new fqx("is_fine_location_used", Boolean.class), new fqx("age_in_hours", Integer.class));
                c3.c();
                return c3;
            case 19:
                fqy g17 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/weather_unit_different", new fqx("app_version", String.class), new fqx("is_different", Boolean.class));
                g17.c();
                return g17;
            default:
                fqy g18 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/weather/zero_update_time", new fqx[0]);
                g18.c();
                return g18;
        }
    }
}
