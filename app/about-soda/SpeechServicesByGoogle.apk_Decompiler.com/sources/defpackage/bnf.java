package defpackage;

import java.util.function.Consumer;

/* renamed from: bnf  reason: default package */
/* compiled from: PG */
public final class bnf {
    public Consumer a = null;
    public boolean b = true;

    public bnf() {
    }

    /* JADX WARNING: type inference failed for: r0v2, types: [java.util.function.Consumer, java.lang.Object] */
    public bnf(bng bng) {
        this.a = bng.d;
        this.b = bng.a;
    }
}
