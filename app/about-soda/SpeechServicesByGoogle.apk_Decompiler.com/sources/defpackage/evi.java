package defpackage;

/* renamed from: evi  reason: default package */
/* compiled from: PG */
public final class evi extends htq implements hvb {
    public static final evi d;
    private static volatile hvh e;
    public int a;
    public int b;
    public long c;

    static {
        evi evi = new evi();
        d = evi;
        htq.z(evi.class, evi);
    }

    private evi() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(d, "\u0004\u0002\u0000\u0001\u0001\u0002\u0002\u0000\u0000\u0000\u0001᠌\u0000\u0002ဂ\u0001", new Object[]{"a", "b", ebb.l, "c"});
        } else if (i2 == 3) {
            return new evi();
        } else {
            if (i2 == 4) {
                return new htk((htq) d);
            }
            if (i2 == 5) {
                return d;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = e;
            if (hvh == null) {
                synchronized (evi.class) {
                    hvh = e;
                    if (hvh == null) {
                        hvh = new htl(d);
                        e = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
