package defpackage;

/* renamed from: cst  reason: default package */
/* compiled from: PG */
public final class cst extends htq implements hvb {
    public static final cst b;
    private static volatile hvh d;
    public int a;
    private int c;

    static {
        cst cst = new cst();
        b = cst;
        htq.z(cst.class, cst);
    }

    private cst() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(b, "\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001᠌\u0000", new Object[]{"c", "a", bqk.l});
        } else if (i2 == 3) {
            return new cst();
        } else {
            if (i2 == 4) {
                return new htk((htq) b);
            }
            if (i2 == 5) {
                return b;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = d;
            if (hvh == null) {
                synchronized (cst.class) {
                    hvh = d;
                    if (hvh == null) {
                        hvh = new htl(b);
                        d = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
