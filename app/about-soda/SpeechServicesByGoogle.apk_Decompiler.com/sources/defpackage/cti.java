package defpackage;

/* renamed from: cti  reason: default package */
/* compiled from: PG */
public final class cti extends htq implements hvb {
    public static final cti f;
    private static volatile hvh g;
    public int a;
    public hvw b;
    public huf c = hvk.a;
    public boolean d;
    public ctk e;

    static {
        cti cti = new cti();
        f = cti;
        htq.z(cti.class, cti);
    }

    private cti() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(f, "\u0001\u0004\u0000\u0001\u0001\u0004\u0004\u0000\u0001\u0000\u0001ဉ\u0000\u0002\u001b\u0003ဇ\u0001\u0004ဉ\u0002", new Object[]{"a", "b", "c", ctb.class, "d", "e"});
        } else if (i2 == 3) {
            return new cti();
        } else {
            if (i2 == 4) {
                return new htk((htq) f);
            }
            if (i2 == 5) {
                return f;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = g;
            if (hvh == null) {
                synchronized (cti.class) {
                    hvh = g;
                    if (hvh == null) {
                        hvh = new htl(f);
                        g = hvh;
                    }
                }
            }
            return hvh;
        }
    }

    public final void b() {
        huf huf = this.c;
        if (!huf.c()) {
            this.c = htq.s(huf);
        }
    }
}
