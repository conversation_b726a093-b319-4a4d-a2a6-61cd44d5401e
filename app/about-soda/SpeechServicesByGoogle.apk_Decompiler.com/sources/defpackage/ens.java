package defpackage;

import java.util.concurrent.Executor;

/* renamed from: ens  reason: default package */
/* compiled from: PG */
public final class ens {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/microphone/impl/audiosourcedataaccessor/HotwordHandoverHandler");
    public final int b;
    public final hme c = kq.f(new bwl(this, 4));
    public po d;

    public ens(int i, hme hme, Executor executor) {
        this.b = i;
        hfc.T(hme, gof.g(new cmk(this, 7)), executor);
    }
}
