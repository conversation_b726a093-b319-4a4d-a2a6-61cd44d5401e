package defpackage;

/* renamed from: elj  reason: default package */
/* compiled from: PG */
public final class elj {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/extensions/externallisteningsession/impl/ExternalListeningSessionProviderImpl");
    public final dou b;
    public final cxk c;
    public final dou d;
    private final jqs e;
    private final dku f;

    public elj(dou dou, cxk cxk, dou dou2, dku dku, jqs jqs) {
        jnu.e(dku, "externalSessionFactory");
        jnu.e(jqs, "blockingScope");
        this.b = dou;
        this.c = cxk;
        this.d = dou2;
        this.f = dku;
        this.e = jqs;
    }

    /* JADX WARNING: type inference failed for: r0v6, types: [java.lang.Object, jjk] */
    public final dyy a(eeh eeh, dyt dyt) {
        jnz jnz = new jnz();
        hme f2 = kq.f(new bwl(jnz, 3));
        eoz k = don.k(this.e);
        mz mzVar = new mz(jnz, 9);
        return new eli(k.o(new egr(this, eeh, dyt, (jna) mzVar, (jlr) null, 4)), f2, dyt, new epd(k, this, eeh, (jna) mzVar, 1), (hmi) ((dku) this.f.a).a.b());
    }
}
