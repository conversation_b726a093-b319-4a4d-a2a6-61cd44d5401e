package defpackage;

/* renamed from: bpw  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bpw implements Runnable {
    private final /* synthetic */ int a;

    public /* synthetic */ bpw(int i) {
        this.a = i;
    }

    public final void run() {
        switch (this.a) {
            case 0:
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
            case 8:
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                throw new RuntimeException("Someone quit the @LightweightExecutor looper");
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                dtp.e();
                return;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                ((hby) ((hby) fdz.a.c()).j("com/google/android/libraries/speech/transcription/recognition/RecognitionClient$SpeechAndMicrophoneListener", "onHeartbeat", 308, "RecognitionClient.java")).r("#onHeartbeat");
                return;
            case 15:
                ((hby) ((hby) fhb.a.h().h(hdg.a, "GoogleAsrService")).j("com/google/android/libraries/speech/transcription/recognition/grpc/impl/GoogleAsrServiceImpl", "queryServiceStatus", 115, "GoogleAsrServiceImpl.java")).r("#queryServiceStatus cancelled by client.");
                return;
            case 16:
                ((hby) ((hby) fhb.a.h().h(hdg.a, "GoogleAsrService")).j("com/google/android/libraries/speech/transcription/recognition/grpc/impl/GoogleAsrServiceImpl", "downloadModel", 187, "GoogleAsrServiceImpl.java")).r("#downloadModel cancelled by client.");
                return;
            case 17:
                ((hby) ((hby) fhb.a.h().h(hdg.a, "GoogleAsrService")).j("com/google/android/libraries/speech/transcription/recognition/grpc/impl/GoogleAsrServiceImpl", "checkModelAvailability", 156, "GoogleAsrServiceImpl.java")).r("#checkModelAvailability cancelled by client.");
                return;
            case 18:
                Throwable th = new Throwable();
                th.fillInStackTrace();
                ((hby) ((hby) ((hby) fzy.a.g()).i(th)).j("com/google/apps/tiktok/concurrent/futuresmixin/FuturesMixinImpl$1", "run", 236, "FuturesMixinImpl.java")).r("b/66999648 detected");
                return;
            case 19:
                return;
            default:
                ((hby) ((hby) gjb.a.f()).j("com/google/apps/tiktok/sync/impl/SyncManagerImpl", "sync", 184, "SyncManagerImpl.java")).r("#sync() complete");
                return;
        }
    }
}
