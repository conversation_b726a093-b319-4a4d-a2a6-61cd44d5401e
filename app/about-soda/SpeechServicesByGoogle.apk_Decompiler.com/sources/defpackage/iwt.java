package defpackage;

import androidx.preference.Preference;
import java.lang.Thread;
import java.net.SocketAddress;
import java.nio.charset.Charset;
import java.util.Collections;
import java.util.IdentityHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Set;
import java.util.concurrent.ScheduledExecutorService;
import java.util.logging.Logger;

/* renamed from: iwt  reason: default package */
/* compiled from: PG */
public final class iwt implements jex, iyk {
    public static final Logger a = Logger.getLogger(iwt.class.getName());
    public final int b;
    public final boolean c;
    public jey d;
    public ior e;
    public jbu f;
    public boolean g;
    public final Set h = Collections.newSetFromMap(new IdentityHashMap());
    public List i;
    public final Thread.UncaughtExceptionHandler j = new jde(1);
    public final izw k = new iwn(this);
    private final iql l;
    private final SocketAddress m;
    private final String n;
    private final String o;
    private int p;
    private jcf q;
    private ScheduledExecutorService r;
    private boolean s;
    private itg t;
    private ior u;

    public iwt(SocketAddress socketAddress, String str, String str2, ior ior, boolean z) {
        this.m = socketAddress;
        this.b = Preference.DEFAULT_ORDER;
        this.n = str;
        this.o = izr.e("inprocess", str2);
        a.w(ior, "eagAttrs");
        ior ior2 = ior.a;
        kls kls = new kls(ior.a);
        kls.b(izn.a, isu.PRIVACY_AND_INTEGRITY);
        kls.b(izn.b, ior);
        kls.b(iqb.a, socketAddress);
        kls.b(iqb.b, socketAddress);
        this.u = kls.a();
        this.l = iql.a(getClass(), socketAddress.toString());
        this.c = z;
    }

    public static int a(irw irw) {
        Charset charset = iqn.a;
        byte[][] l2 = irw.l();
        long j2 = 0;
        for (int i2 = 0; i2 < l2.length; i2 += 2) {
            j2 += (long) (l2[i2].length + 32 + l2[i2 + 1].length);
        }
        return (int) Math.min(j2, 2147483647L);
    }

    public static itg e(itg itg, boolean z) {
        if (itg == null) {
            return null;
        }
        itg e2 = itg.b(itg.n.r).e(itg.o);
        if (z) {
            return e2.d(itg.p);
        }
        return e2;
    }

    public final synchronized ixz b(isa isa, irw irw, iov iov, ipb[] ipbArr) {
        int a2;
        jfg g2 = jfg.g(ipbArr, this.u);
        itg itg = this.t;
        if (itg != null) {
            return new iwo(g2, itg);
        }
        irw.g(izr.j, this.o);
        if (this.p == Integer.MAX_VALUE || (a2 = a(irw)) <= this.p) {
            return new iws(this, isa, irw, iov, this.n, g2).a;
        }
        return new iwo(g2, itg.g.e(String.format(Locale.US, "Request metadata larger than %d: %d", new Object[]{Integer.valueOf(this.p), Integer.valueOf(a2)})));
    }

    public final iql c() {
        return this.l;
    }

    public final synchronized Runnable d(jbu jbu) {
        iwj iwj;
        this.f = jbu;
        int i2 = iwj.c;
        SocketAddress socketAddress = this.m;
        if (socketAddress instanceof iwf) {
            iwj = ((iwf) socketAddress).a();
        } else if (!(socketAddress instanceof iwm)) {
            iwj = null;
        } else {
            iwm iwm = (iwm) socketAddress;
            throw null;
        }
        if (iwj != null) {
            this.p = Preference.DEFAULT_ORDER;
            jcf jcf = iwj.b;
            this.q = jcf;
            this.r = (ScheduledExecutorService) jcf.a();
            this.i = iwj.a;
            this.d = iwj.a(this);
        }
        if (this.d == null) {
            itg e2 = itg.k.e("Could not find server: ".concat(String.valueOf(String.valueOf(this.m))));
            this.t = e2;
            return new hpj(this, e2, 15);
        }
        ior ior = ior.a;
        kls kls = new kls(ior.a);
        kls.b(iqb.a, this.m);
        kls.b(iqb.b, this.m);
        ior a2 = kls.a();
        this.d.c();
        this.e = a2;
        jbu jbu2 = this.f;
        ior ior2 = this.u;
        jbu2.e();
        this.u = ior2;
        this.f.b();
        return null;
    }

    public final synchronized void f() {
        o(itg.k.e("InProcessTransport shutdown by the server-side"));
    }

    public final synchronized void g(itg itg) {
        if (!this.g) {
            this.g = true;
            this.f.c(itg);
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:14:0x0024, code lost:
        return;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final synchronized void h() {
        /*
            r2 = this;
            monitor-enter(r2)
            boolean r0 = r2.s     // Catch:{ all -> 0x0025 }
            if (r0 == 0) goto L_0x0006
            goto L_0x0023
        L_0x0006:
            r0 = 1
            r2.s = r0     // Catch:{ all -> 0x0025 }
            java.util.concurrent.ScheduledExecutorService r0 = r2.r     // Catch:{ all -> 0x0025 }
            if (r0 == 0) goto L_0x0015
            jcf r1 = r2.q     // Catch:{ all -> 0x0025 }
            r1.b(r0)     // Catch:{ all -> 0x0025 }
            r0 = 0
            r2.r = r0     // Catch:{ all -> 0x0025 }
        L_0x0015:
            jbu r0 = r2.f     // Catch:{ all -> 0x0025 }
            r0.d()     // Catch:{ all -> 0x0025 }
            jey r0 = r2.d     // Catch:{ all -> 0x0025 }
            if (r0 == 0) goto L_0x0023
            r0.b()     // Catch:{ all -> 0x0025 }
            monitor-exit(r2)
            return
        L_0x0023:
            monitor-exit(r2)
            return
        L_0x0025:
            r0 = move-exception
            monitor-exit(r2)     // Catch:{ all -> 0x0025 }
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.iwt.h():void");
    }

    /* JADX WARNING: Code restructure failed: missing block: B:11:0x0019, code lost:
        return;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final synchronized void o(defpackage.itg r2) {
        /*
            r1 = this;
            monitor-enter(r1)
            boolean r0 = r1.g     // Catch:{ all -> 0x001a }
            if (r0 == 0) goto L_0x0006
            goto L_0x0018
        L_0x0006:
            r1.t = r2     // Catch:{ all -> 0x001a }
            r1.g(r2)     // Catch:{ all -> 0x001a }
            java.util.Set r2 = r1.h     // Catch:{ all -> 0x001a }
            boolean r2 = r2.isEmpty()     // Catch:{ all -> 0x001a }
            if (r2 == 0) goto L_0x0018
            r1.h()     // Catch:{ all -> 0x001a }
            monitor-exit(r1)
            return
        L_0x0018:
            monitor-exit(r1)
            return
        L_0x001a:
            r2 = move-exception
            monitor-exit(r1)     // Catch:{ all -> 0x001a }
            throw r2
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.iwt.o(itg):void");
    }

    /* JADX WARNING: Code restructure failed: missing block: B:10:0x0019, code lost:
        ((defpackage.iws) r0.get(r2)).a.c(r5);
        r2 = r2 + 1;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:11:0x0027, code lost:
        return;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:8:0x0012, code lost:
        r1 = r0.size();
        r2 = 0;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:9:0x0017, code lost:
        if (r2 >= r1) goto L_0x0027;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void p(defpackage.itg r5) {
        /*
            r4 = this;
            monitor-enter(r4)
            r4.o(r5)     // Catch:{ all -> 0x0028 }
            boolean r0 = r4.s     // Catch:{ all -> 0x0028 }
            if (r0 == 0) goto L_0x000a
            monitor-exit(r4)     // Catch:{ all -> 0x0028 }
            return
        L_0x000a:
            java.util.ArrayList r0 = new java.util.ArrayList     // Catch:{ all -> 0x0028 }
            java.util.Set r1 = r4.h     // Catch:{ all -> 0x0028 }
            r0.<init>(r1)     // Catch:{ all -> 0x0028 }
            monitor-exit(r4)     // Catch:{ all -> 0x0028 }
            int r1 = r0.size()
            r2 = 0
        L_0x0017:
            if (r2 >= r1) goto L_0x0027
            java.lang.Object r3 = r0.get(r2)
            iws r3 = (defpackage.iws) r3
            iwp r3 = r3.a
            r3.c(r5)
            int r2 = r2 + 1
            goto L_0x0017
        L_0x0027:
            return
        L_0x0028:
            r5 = move-exception
            monitor-exit(r4)     // Catch:{ all -> 0x0028 }
            throw r5
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.iwt.p(itg):void");
    }

    public final ScheduledExecutorService r() {
        return this.r;
    }

    public final String toString() {
        grg r2 = ftd.r(this);
        r2.f("logId", this.l.a);
        r2.b("address", this.m);
        return r2.toString();
    }
}
