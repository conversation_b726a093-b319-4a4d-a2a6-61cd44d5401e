package defpackage;

/* renamed from: csg  reason: default package */
/* compiled from: PG */
public final class csg extends htq implements hvb {
    public static final csg a;
    private static volatile hvh b;

    static {
        csg csg = new csg();
        a = csg;
        htq.z(csg.class, csg);
    }

    private csg() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(a, "\u0001\u0000", (Object[]) null);
        }
        if (i2 == 3) {
            return new csg();
        }
        if (i2 == 4) {
            return new htk((htq) a);
        }
        if (i2 == 5) {
            return a;
        }
        if (i2 != 6) {
            return null;
        }
        hvh hvh = b;
        if (hvh == null) {
            synchronized (csg.class) {
                hvh = b;
                if (hvh == null) {
                    hvh = new htl(a);
                    b = hvh;
                }
            }
        }
        return hvh;
    }
}
