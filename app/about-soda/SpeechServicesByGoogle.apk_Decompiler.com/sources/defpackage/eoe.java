package defpackage;

import android.util.Base64;
import j$.time.ZoneId;
import j$.time.format.DateTimeFormatter;
import j$.util.concurrent.ConcurrentHashMap;
import java.io.File;

/* renamed from: eoe  reason: default package */
/* compiled from: PG */
public final class eoe extends jnv implements jmp {
    final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eoe(Object obj, int i) {
        super(0);
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [eoc, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v11, types: [eon, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v17, types: [enr, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v25, types: [enr, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v73, types: [gge, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v75, types: [gge, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v77, types: [gge, java.lang.Object] */
    public final /* synthetic */ Object a() {
        String str;
        hsq hsq;
        switch (this.b) {
            case 0:
                return Boolean.valueOf(((dyx) this.a.b().d()).a().isDone());
            case 1:
                Object obj = this.a;
                return ((eny) obj).a.a(new ejr(new mz(obj, 13), 9));
            case 2:
                return Boolean.valueOf(this.a.b().a().a().isDone());
            case 3:
                return Integer.valueOf(this.a.a());
            case 4:
                return Integer.valueOf(((epn) this.a).d.a());
            case 5:
                return Integer.valueOf(this.a.a());
            case 6:
                esp[] espArr = new esp[1];
                esr esr = (esr) this.a;
                int m = esr.b.m();
                ((hby) esr.a.f().h(hdg.a, "ALT.HotwordRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/HotwordRoutesRegistry", "createHotwordDefaultRoute", 99, "HotwordRoutesRegistry.kt")).s("#audio# initializing the default route(%s) for hotword", m);
                ery ery = new ery(gqd.a);
                htk l = eac.c.l();
                jnu.d(l, "newBuilder(...)");
                bzj m2 = jnu.e(l, "builder");
                htk l2 = ebw.c.l();
                jnu.d(l2, "newBuilder(...)");
                bzl o = jnu.e(l2, "builder");
                o.y(m);
                m2.y(o.x());
                eac x = m2.x();
                esa O = esr.d.O(x, ery.a().a);
                ebw ebw = x.b;
                if (ebw == null) {
                    ebw = ebw.c;
                }
                int i = ebw.b;
                htk l3 = eab.d.l();
                jnu.d(l3, "newBuilder(...)");
                bzj p = jnu.e(l3, "builder");
                dzq dzq = ery.a().b;
                htk htk = (htk) p.a;
                if (!htk.b.B()) {
                    htk.u();
                }
                eab eab = (eab) htk.b;
                eab.b = dzq;
                eab.a = 1 | eab.a;
                espArr[0] = new esp(ery, O, i, p.z());
                return jji.p(espArr);
            case 7:
                eus eus = (eus) this.a;
                if (eus.d) {
                    File externalCacheDir = eus.c.getExternalCacheDir();
                    if (externalCacheDir != null) {
                        str = String.valueOf(externalCacheDir.getAbsolutePath()).concat("/audio_library/recordings");
                    } else {
                        str = null;
                    }
                    if (str == null) {
                        Object obj2 = this.a;
                        ((hby) eus.a.f().h(hdg.a, "ALT.AudioFileWriter").j("com/google/android/libraries/search/audio/store/persistent/AudioFileWriter$fileNameWithoutExtension$2", "invoke", 79, "AudioFileWriter.kt")).r("#audio# no external storage available, falling back to internal");
                        str = evo.a(((eus) obj2).c).getAbsolutePath();
                    }
                } else {
                    str = evo.a(eus.c).getAbsolutePath();
                }
                String format = cqx.G().atZone(ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("yyyyMMdd'T'HHmmssSSS"));
                jnu.d(format, "format(...)");
                String a2 = eus.b.a(((eus) this.a).e.a, "");
                return a.as(format + "-" + a2, str, "/");
            case 8:
                return String.valueOf(((eus) this.a).c()).concat("_tmp.wav");
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return String.valueOf(((eus) this.a).c()).concat(".wav");
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return String.valueOf(((eus) this.a).c()).concat(".timestamps");
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return String.valueOf(((eus) this.a).c()).concat("_tmp.timestamps");
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return this.a.getViewModelStore$ar$class_merging$ar$class_merging();
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return this.a.getDefaultViewModelProviderFactory();
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return this.a.getDefaultViewModelCreationExtras();
            case 15:
                return hsq.q(Base64.decode((String) this.a, 3));
            case 16:
                return new ConcurrentHashMap(((iir) ((gnk) this.a).c).a.size(), 0.75f, 4);
            case 17:
                gdx gdx = (gdx) this.a;
                return Boolean.valueOf(gdx.c.contains(gdx.s));
            case 18:
                gcs gcs = (gcs) this.a;
                if (gcs.b == 5) {
                    hsq = (hsq) gcs.c;
                } else {
                    hsq = hsq.b;
                }
                jnu.d(hsq, "getBytesValue(...)");
                return hsq;
            case 19:
                Object a3 = ((due) this.a).a();
                if (a3 instanceof hsq) {
                    return (hsq) a3;
                }
                jnu.c(a3, "null cannot be cast to non-null type kotlin.ByteArray");
                return hsq.q((byte[]) a3);
            default:
                return this.a;
        }
    }
}
