package j$.time.format;

import j$.time.chrono.C0010i;
import j$.time.g;
import j$.time.temporal.p;
import j$.util.Objects;

final class m extends j {
    static final g h = g.T(2000, 1, 1);
    private final g g;

    /* synthetic */ m(p pVar, g gVar, int i) {
        this(pVar, 2, 2, gVar, i);
    }

    /* access modifiers changed from: package-private */
    public final long b(u uVar, long j) {
        int i;
        long abs = Math.abs(j);
        g gVar = this.g;
        if (gVar != null) {
            i = C0010i.p(uVar.d()).r(gVar).p(this.a);
        } else {
            i = 0;
        }
        long j2 = (long) i;
        long[] jArr = j.f;
        if (j >= j2) {
            long j3 = jArr[this.b];
            if (j < j2 + j3) {
                return abs % j3;
            }
        }
        return abs % jArr[this.c];
    }

    /* access modifiers changed from: package-private */
    public final j c() {
        if (this.e == -1) {
            return this;
        }
        return new m(this.a, this.b, this.c, this.g, -1);
    }

    /* access modifiers changed from: package-private */
    public final j d(int i) {
        int i2 = this.b;
        int i3 = this.c;
        return new m(this.a, i2, i3, this.g, this.e + i);
    }

    public final String toString() {
        String valueOf = String.valueOf(this.a);
        Object obj = this.g;
        if (obj == null) {
            obj = Objects.requireNonNull(0, "defaultObj");
        }
        String valueOf2 = String.valueOf(obj);
        return "ReducedValue(" + valueOf + "," + this.b + "," + this.c + "," + valueOf2 + ")";
    }

    m(p pVar, g gVar) {
        this(pVar, 2, 2, gVar, 0);
        if (gVar == null) {
            long j = (long) 0;
            if (!pVar.m().i(j)) {
                throw new IllegalArgumentException("The base value must be within the range of the field");
            } else if (j + j.f[2] > 2147483647L) {
                throw new RuntimeException("Unable to add printer-parser as the range exceeds the capacity of an int");
            }
        }
    }

    private m(p pVar, int i, int i2, g gVar, int i3) {
        super(pVar, i, i2, A.NOT_NEGATIVE, i3);
        this.g = gVar;
    }
}
