package defpackage;

/* renamed from: evx  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class evx implements gsb {
    public final /* synthetic */ bmu a;
    private final /* synthetic */ int b;

    public /* synthetic */ evx(bmu bmu, int i) {
        this.b = i;
        this.a = bmu;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                fqy g = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/verticals/memory/backfill/started_count", new fqx[0]);
                g.c();
                return g;
            case 1:
                fqy g2 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/location_triggering_eligibility", new fqx("app_version", String.class), new fqx("type", String.class), new fqx("dau", Boolean.class));
                g2.c();
                return g2;
            case 2:
                fqy g3 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/location_update", new fqx("app_version", String.class), new fqx("source", String.class), new fqx("update_model", String.class), new fqx("dau", Boolean.class));
                g3.c();
                return g3;
            case 3:
                fqv c = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/odlh/commute_distinct_card_interval", new fqx("app_version", String.class), new fqx("client_type", String.class));
                c.c();
                return c;
            case 4:
                fqy g4 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/odlh/commute_duplicate_card_hidden", new fqx("app_version", String.class), new fqx("client_type", String.class), new fqx("card_version", String.class));
                g4.c();
                return g4;
            case 5:
                fqy g5 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/odlh/version_display", new fqx("app_version", String.class), new fqx("client_type", String.class), new fqx("card_type", String.class), new fqx("card_version", String.class));
                g5.c();
                return g5;
            case 6:
                fqv c2 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/pcp/display_validity_change_to_client_notification", new fqx("client_type", String.class), new fqx("data_type", String.class));
                c2.c();
                return c2;
            case 7:
                fqy g6 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/push_message_count", new fqx("data_type", Integer.class));
                g6.c();
                return g6;
            case 8:
                fqv c3 = ((frc) this.a.a).c("/client_streamz/android_gsa/assistant/appactions/clock_inventory_data_fetch_latency", new fqx("android_version", Integer.class), new fqx("source", String.class));
                c3.c();
                return c3;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fqy g7 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/setting/shared_preferences_pds_consistent", new fqx("feature_type", String.class), new fqx("is_consistent", Boolean.class));
                g7.c();
                return g7;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fqv c4 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/smartspace_card_storage_read_latency", new fqx("app_version", String.class));
                c4.c();
                return c4;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                fqv c5 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/smartspace_card_storage_write_latency", new fqx("is_smartspace_chip", Boolean.class), new fqx("app_version", String.class));
                c5.c();
                return c5;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                fqy g8 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/verticals/memory/entity/image_entity_builder/assist_data_too_large_count", new fqx("app_package", String.class));
                g8.c();
                return g8;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                fqy g9 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/tng_weather/air_quality_events", new fqx("status", String.class));
                g9.c();
                return g9;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                fqv c6 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/tng_weather/air_quality_freshness", new fqx("app_version", String.class));
                c6.c();
                return c6;
            case 15:
                fqy g10 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/tng_weather/air_quality_source_station_type", new fqx("station", String.class));
                g10.c();
                return g10;
            case 16:
                fqy g11 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/tng_weather/current_and_daily_forecast_mismatch", new fqx("source", Integer.class), new fqx("mismatch_fields", String.class));
                g11.c();
                return g11;
            case 17:
                fqy g12 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/tng_weather/daily_forecast_invalid", new fqx("source", Integer.class), new fqx("empty_list", Boolean.class), new fqx("missing_start_date", Boolean.class), new fqx("missing_high_temperature", Boolean.class), new fqx("missing_low_temperature", Boolean.class), new fqx("missing_icon_url", Boolean.class));
                g12.c();
                return g12;
            case 18:
                fqy g13 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/tng_weather/hourly_forecast_data_invalid", new fqx("empty_list", Boolean.class), new fqx("missing_utc_date", Boolean.class), new fqx("missing_start_hour", Boolean.class), new fqx("missing_temperature", Boolean.class), new fqx("missing_icon_url", Boolean.class));
                g13.c();
                return g13;
            case 19:
                fqy g14 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/tng_weather/receive_status", new fqx("status", String.class), new fqx("action", String.class), new fqx("data_origin", String.class));
                g14.c();
                return g14;
            default:
                fqv c7 = ((frc) this.a.a).c("/client_streamz/android_gsa/assistant/appactions/clock_inventory_fetch_latency", new fqx("android_version", Integer.class));
                c7.c();
                return c7;
        }
    }
}
