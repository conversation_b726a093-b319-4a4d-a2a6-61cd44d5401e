package defpackage;

import android.content.Context;
import android.net.Uri;
import android.system.ErrnoException;
import android.system.Os;
import java.io.IOException;

/* renamed from: czi  reason: default package */
/* compiled from: PG */
public final class czi {
    public static Uri a(Context context, Uri uri) {
        try {
            String readlink = Os.readlink(ftc.L(uri, context).getAbsolutePath());
            if (readlink != null) {
                fnr fnr = new fnr(context);
                fnr.b(readlink);
                return fnr.a();
            }
            throw new IOException("Unable to read symlink");
        } catch (ErrnoException | fny e) {
            throw new IOException("Unable to read symlink", e);
        }
    }

    public static void b(Context context, Uri uri, Uri uri2) {
        try {
            Os.symlink(ftc.L(uri2, context).getAbsolutePath(), ftc.L(uri, context).getAbsolutePath());
        } catch (ErrnoException | fny e) {
            throw new IOException("Unable to create symlink", e);
        }
    }
}
