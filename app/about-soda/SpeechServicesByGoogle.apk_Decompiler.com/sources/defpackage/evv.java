package defpackage;

/* renamed from: evv  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class evv implements gsb {
    public final /* synthetic */ bmu a;
    private final /* synthetic */ int b;

    public /* synthetic */ evv(bmu bmu, int i) {
        this.b = i;
        this.a = bmu;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                fqy g = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/apa/runtime/interactor_exit_info_counter", new fqx("exit_info_status", String.class));
                g.c();
                return g;
            case 1:
                fqy g2 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/habits/media_suggestions_count", new fqx("app_version", String.class), new fqx("num_suggestions", Integer.class), new fqx("num_supported_suggestions", Integer.class), new fqx("has_assistant_settings_suggestion", Boolean.class), new fqx("dau", Boolean.class));
                g2.c();
                return g2;
            case 2:
                fqy g3 = ((frc) this.a.a).g("/client_streamz/android_gsa/aap/default_request_counter", new fqx[0]);
                g3.c();
                return g3;
            case 3:
                fqy g4 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/habits/profiles", new fqx("app_version", String.class), new fqx("key", String.class), new fqx("is_present", Boolean.class));
                g4.c();
                return g4;
            case 4:
                fqy g5 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/habits/profiles_dau", new fqx("app_version", String.class), new fqx("key", String.class), new fqx("present_frequency", Integer.class));
                g5.c();
                return g5;
            case 5:
                fqv c = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/headphone/app_headphone_use_duration", new fqx("app_name", String.class));
                c.c();
                return c;
            case 6:
                fqy g6 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/headphone/app_suggestion", new fqx("app_name", String.class), new fqx("suggestion_source", String.class));
                g6.c();
                return g6;
            case 7:
                fqy g7 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/headphone/connect_counter", new fqx("is_device_locked", Boolean.class), new fqx("has_active_media_session", Boolean.class), new fqx("is_launcher_on_foreground", Boolean.class), new fqx("is_music_active", Boolean.class), new fqx("is_phone_call_ongoing", Boolean.class), new fqx("headset_type", String.class));
                g7.c();
                return g7;
            case 8:
                fqv c2 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/headphone/connection_latency", new fqx("last_triggering_source", String.class));
                c2.c();
                return c2;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fqv c3 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/headphone/headphone_disconnection_latency", new fqx("last_triggering_source", String.class));
                c3.c();
                return c3;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fqy g8 = ((frc) this.a.a).g("/client_streamz/android_gsa/account_id/missing_in_intent_extra_and_data", new fqx[0]);
                g8.c();
                return g8;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                fqy g9 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/headphone/local_cache_availability_counter", new fqx("is_cache_available", Boolean.class));
                g9.c();
                return g9;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                fqy g10 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/headphone/logging_connection", new fqx("connection_detected", Boolean.class), new fqx("successfully_logged", Boolean.class));
                g10.c();
                return g10;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                fqv c4 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/headphone/media_local_cache_refresh_interval", new fqx("app_package", String.class));
                c4.c();
                return c4;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                fqv c5 = ((frc) this.a.a).c("/client_streamz/android_gsa/assistant/appactions/clock_context_conversion_latency", new fqx("android_version", Integer.class), new fqx("source", String.class), new fqx("entity_type", String.class));
                c5.c();
                return c5;
            case 15:
                fqv c6 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/headphone/media_session_age", new fqx("is_user_unlocked", Boolean.class));
                c6.c();
                return c6;
            case 16:
                fqv c7 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/headphone/model_generation_interval", new fqx[0]);
                c7.c();
                return c7;
            case 17:
                fqv c8 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/headphone/on_device_app_suggestion_scores", new fqx("app_position", String.class), new fqx("device_type", String.class));
                c8.c();
                return c8;
            case 18:
                fqy g11 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/headphone/on_device_model_generation_conditions", new fqx("is_v1_model_generation_enabled", Boolean.class), new fqx("is_usage_stats_mgr_present", Boolean.class), new fqx("is_pr_bit_and_waa_enabled", Boolean.class), new fqx("has_headphone_connections", Boolean.class), new fqx("has_app_events", Boolean.class));
                g11.c();
                return g11;
            case 19:
                fqy g12 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/headphone/on_device_model_requested", new fqx("is_habits_unfiltered_apps_present", Boolean.class), new fqx("is_on_device_unfiltered_apps_present", Boolean.class), new fqx("is_habits_profile_present", Boolean.class), new fqx("is_on_device_profile_present", Boolean.class), new fqx("is_top_app_same_in_habits", Boolean.class), new fqx("is_top_supported_app_same_in_habits", Boolean.class), new fqx("is_device_model_v1", Boolean.class));
                g12.c();
                return g12;
            default:
                fqv c9 = ((frc) this.a.a).c("/client_streamz/android_gsa/smartspace/headphone/persistent_media_recommendation_card_send_out_interval", new fqx[0]);
                c9.c();
                return c9;
        }
    }
}
