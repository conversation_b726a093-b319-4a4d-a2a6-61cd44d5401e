package defpackage;

import android.os.SystemClock;

/* renamed from: crr  reason: default package */
/* compiled from: PG */
final class crr extends ism {
    final /* synthetic */ crs a;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public crr(crs crs, ftc ftc) {
        super(ftc);
        this.a = crs;
    }

    public final void a(itg itg, irw irw) {
        cru cru = this.a.a;
        synchronized (cru.a) {
            if (!cru.c) {
                cru.c = true;
                crt crt = cru.b;
                if (crt == null) {
                    cru.d.s = itg.n.r;
                    djl.a().d(cru.d);
                } else {
                    synchronized (crt.a) {
                        dpp dpp = crt.b;
                        int i = crt.d;
                        int i2 = crt.e;
                        dpp.c = SystemClock.elapsedRealtime() - dpp.a;
                        dpp.d = i;
                        dpp.e = i2;
                        crt.b.s = itg.n.r;
                        djl.a().d(crt.b);
                        crt.c = true;
                    }
                }
            }
        }
        super.a(itg, irw);
    }
}
