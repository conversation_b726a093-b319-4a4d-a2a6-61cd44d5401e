<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:theme="@style/Theme.IntentApi.Material" android:orientation="vertical" android:id="@+id/transcription_intent_api_main" android:layout_width="match_parent" android:layout_height="match_parent" android:importantForAccessibility="no">
    <ScrollView android:layout_width="match_parent" android:layout_height="match_parent">
        <android.support.constraint.ConstraintLayout android:id="@+id/transcription_intent_api_recognizer" android:layout_width="match_parent" android:layout_height="match_parent" android:layout_margin="24dp" android:keepScreenOn="true">
            <ImageView android:id="@+id/transcription_google_logo" android:focusable="false" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/googlelogo_dark_color_84x28" android:scaleType="center" android:tint="@color/transcription_intent_api_google_logo_tint" android:contentDescription="@string/transcription_app_common_google_logo" android:importantForAccessibility="no" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintRight_toRightOf="0" app:layout_constraintTop_toTopOf="0"/>
            <FrameLayout android:id="@+id/transcription_intent_api_mic_container" android:layout_width="@dimen/transcription_intent_api_material_mic_area_size" android:layout_height="@dimen/transcription_intent_api_material_mic_area_size" android:layout_marginTop="@dimen/transcription_intent_api_material_large_margin" android:importantForAccessibility="noHideDescendants" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintRight_toRightOf="0" app:layout_constraintTop_toBottomOf="@+id/transcription_google_logo">
                <include layout="@layout/transcription_intent_api_material_mic_indicator"/>
            </FrameLayout>
            <android.support.v7.widget.AppCompatTextView android:textSize="@dimen/transcription_intent_api_material_state_text_size" android:textColor="@color/agsa_color_on_surface" android:gravity="center_horizontal" android:id="@+id/transcription_intent_api_text" android:visibility="visible" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginTop="@dimen/transcription_intent_api_material_small_margin" android:maxLines="2" android:minLines="1" android:importantForAccessibility="no" android:textAlignment="center" app:fontFamily="@font/google_sans" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintRight_toRightOf="0" app:layout_constraintTop_toBottomOf="@+id/transcription_intent_api_mic_container"/>
            <android.support.v7.widget.AppCompatButton android:enabled="false" android:textColor="@color/agsa_color_primary" android:id="@+id/transcription_retry_button" android:background="@drawable/transcription_intent_api_custom_bordered_button" android:paddingLeft="@dimen/transcription_intent_api_material_retry_button_left_right_padding" android:paddingTop="@dimen/transcription_intent_api_material_retry_button_top_bottom_padding" android:paddingRight="@dimen/transcription_intent_api_material_retry_button_left_right_padding" android:paddingBottom="@dimen/transcription_intent_api_material_retry_button_top_bottom_padding" android:visibility="invisible" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/transcription_app_try_again" android:textAllCaps="false" android:fontFamily="@font/google_sans_medium" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintRight_toRightOf="0" app:layout_constraintTop_toBottomOf="@+id/transcription_intent_api_text" style="@style/TextAppearance.AppCompat.Button"/>
            <android.support.v7.widget.AppCompatTextView android:textColor="@color/agsa_color_on_surface_variant" android:id="@+id/transcription_intent_api_language" android:paddingBottom="25dp" android:layout_width="wrap_content" android:layout_height="wrap_content" android:importantForAccessibility="no" android:accessibilityTraversalBefore="@+id/transcription_retry_button" app:fontFamily="@font/google_sans" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintRight_toRightOf="0" app:layout_constraintTop_toBottomOf="@+id/transcription_retry_button" style="@style/TextAppearance.AppCompat.Caption"/>
        </android.support.constraint.ConstraintLayout>
    </ScrollView>
</FrameLayout>
