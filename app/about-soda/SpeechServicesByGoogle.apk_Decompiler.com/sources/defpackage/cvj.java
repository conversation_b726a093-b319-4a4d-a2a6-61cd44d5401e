package defpackage;

import androidx.wear.ambient.AmbientDelegate;
import java.io.IOException;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicReference;

/* renamed from: cvj  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvj implements hko {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    public final /* synthetic */ Object d;
    public final /* synthetic */ Object e;
    private final /* synthetic */ int f;

    public /* synthetic */ cvj(cvy cvy, csv csv, csx csx, ctl ctl, ctj ctj, int i) {
        this.f = i;
        this.d = cvy;
        this.b = csv;
        this.c = csx;
        this.a = ctl;
        this.e = ctj;
    }

    /* JADX WARNING: type inference failed for: r2v1, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r15v6, types: [java.lang.Object, hko] */
    public final hme a(Object obj) {
        int A;
        int A2;
        int i = this.f;
        boolean z = false;
        if (i == 0) {
            cvw cvw = (cvw) obj;
            Object name = cvw.name();
            csv csv = (csv) this.b;
            Object obj2 = csv.b;
            csx csx = (csx) this.c;
            cyh.e("%s: Android sharing CASE: %s for file %s, filegroup %s", "FileGroupManager", name, obj2, csx.c);
            ctl ctl = (ctl) hfc.S(this.d);
            int ordinal = cvw.ordinal();
            Object obj3 = this.a;
            Object obj4 = this.e;
            if (ordinal == 1) {
                cvy cvy = (cvy) obj3;
                return cvy.q(cvy.y(csx, csv, ctl, (ctj) obj4, ctl.f, csx.k, 3), new bub(8));
            } else if (ordinal != 3) {
                if (ordinal == 4) {
                    ctf b2 = ctf.b(ctl.c);
                    if (b2 == null) {
                        b2 = ctf.NONE;
                    }
                    if (b2 == ctf.DOWNLOAD_COMPLETE && (A = a.A(csv.l)) != 0 && A == 2) {
                        return ((cvy) obj3).x(csx, csv, (ctj) obj4, ctl, 6);
                    }
                }
                cyh.e("%s: File couldn't be shared before download %s, filegroup %s", "FileGroupManager", csv.b, csx.c);
                return hma.a;
            } else {
                return ((cvy) obj3).w(csx, csv, (ctj) obj4, ctl, 4);
            }
        } else if (i == 1) {
            csx csx2 = (csx) obj;
            Object obj5 = this.d;
            Object obj6 = this.c;
            Object obj7 = this.e;
            if (csx2 == null) {
                cvy cvy2 = (cvy) obj7;
                return cvy2.q(cvy2.g((ctg) obj6, true), new bpt(obj6, obj5, 15));
            }
            ((AtomicReference) obj5).set(csx2);
            csw csw = csx2.b;
            if (csw == null) {
                csw = csw.i;
            }
            int i2 = csw.f + 1;
            htk htk = (htk) csx2.C(5);
            htk.x(csx2);
            htk htk2 = (htk) csw.C(5);
            htk2.x(csw);
            if (!htk2.b.B()) {
                htk2.u();
            }
            csw csw2 = (csw) htk2.b;
            csw2.a |= 16;
            csw2.f = i2;
            if (!htk.b.B()) {
                htk.u();
            }
            csx csx3 = (csx) htk.b;
            csw csw3 = (csw) htk2.r();
            csw3.getClass();
            csx3.b = csw3;
            csx3.a |= 1;
            csx csx4 = (csx) htk.r();
            if ((csw.a & 8) != 0) {
                z = true;
            }
            boolean z2 = !z;
            if (!z) {
                long c2 = cqx.c();
                csw csw4 = csx4.b;
                if (csw4 == null) {
                    csw4 = csw.i;
                }
                htk htk3 = (htk) csw4.C(5);
                htk3.x(csw4);
                if (!htk3.b.B()) {
                    htk3.u();
                }
                csw csw5 = (csw) htk3.b;
                csw5.a = 8 | csw5.a;
                csw5.e = c2;
                csw csw6 = (csw) htk3.r();
                htk htk4 = (htk) csx4.C(5);
                htk4.x(csx4);
                if (!htk4.b.B()) {
                    htk4.u();
                }
                csx csx5 = (csx) htk4.b;
                csw6.getClass();
                csx5.b = csw6;
                csx5.a |= 1;
                csx4 = (csx) htk4.r();
            }
            hme K = hfc.K(csx4);
            if (!z) {
                ArrayList arrayList = new ArrayList();
                for (csv j : csx4.n) {
                    arrayList.add(((cvy) obj7).j(j, csx4));
                }
                bzj W = cqh.W(arrayList);
                bdr bdr = new bdr((Object) arrayList, 6);
                cvy cvy3 = (cvy) obj7;
                K = czw.e(W.n(bdr, cvy3.e)).f(new brg(csx4, 15), cvy3.e);
            }
            cvy cvy4 = (cvy) obj7;
            return czw.e(czw.e(K).g(new bpt(obj7, obj6, 16, (byte[]) null), cvy4.e).g(new cvk(obj7, z2, (Object) K, 1), cvy4.e)).d(IOException.class, new bub(6), cvy4.e).g(new cvd(cvy4, (csz) this.b, (ctg) obj6, (hko) this.a, 2), cvy4.e);
        } else if (i != 2) {
            Void voidR = (Void) obj;
            ((dcm) ((AmbientDelegate) this.a).c).a();
            Object obj8 = this.c;
            Object obj9 = this.b;
            ded ded = (ded) this.e;
            ((kli) this.d).c(ded, (ddc) obj9, (ddc) obj8);
            return kli.e(ded);
        } else {
            cvw cvw2 = (cvw) obj;
            Object name2 = cvw2.name();
            csv csv2 = (csv) this.b;
            Object obj10 = csv2.b;
            csx csx6 = (csx) this.c;
            cyh.e("%s: Android sharing CASE: %s for file %s, filegroup %s", "FileGroupManager", name2, obj10, csx6.c);
            int ordinal2 = cvw2.ordinal();
            Object obj11 = this.d;
            Object obj12 = this.a;
            Object obj13 = this.e;
            if (ordinal2 == 1) {
                long j2 = csx6.k;
                ctl ctl2 = (ctl) obj12;
                if (!cvy.u(ctl2, j2)) {
                    return hma.a;
                }
                cyh.e("%s: File already shared after downloaded but lease has to be updated for file %s, filegroup %s", "FileGroupManager", csv2.b, csx6.c);
                cvy cvy5 = (cvy) obj11;
                long j3 = j2;
                ctj ctj = (ctj) obj13;
                return cvy5.q(cvy5.y(csx6, csv2, ctl2, ctj, ctl2.f, j3, 27), new cvq(cvy5, csx6, csv2, ctj, j3));
            } else if (ordinal2 == 3) {
                return ((cvy) obj11).w(csx6, csv2, (ctj) obj13, (ctl) obj12, 5);
            } else if (ordinal2 == 4 && (A2 = a.A(csv2.l)) != 0 && A2 == 2) {
                return ((cvy) obj11).x(csx6, csv2, (ctj) obj13, (ctl) obj12, 7);
            } else {
                int A3 = a.A(csv2.l);
                if (A3 != 0 && A3 == 2) {
                    cvy.A(((cvy) obj11).i, csx6, csv2, 16);
                }
                cyh.e("%s: File couldn't be shared after download %s, filegroup %s", "FileGroupManager", csv2.b, csx6.c);
                return ((cvy) obj11).r(csx6, csv2, (ctj) obj13, csx6.k);
            }
        }
    }

    public /* synthetic */ cvj(cvy cvy, csv csv, csx csx, hme hme, ctj ctj, int i) {
        this.f = i;
        this.a = cvy;
        this.b = csv;
        this.c = csx;
        this.d = hme;
        this.e = ctj;
    }

    public /* synthetic */ cvj(cvy cvy, ctg ctg, AtomicReference atomicReference, csz csz, hko hko, int i) {
        this.f = i;
        this.e = cvy;
        this.c = ctg;
        this.d = atomicReference;
        this.b = csz;
        this.a = hko;
    }

    public /* synthetic */ cvj(kli kli, AmbientDelegate ambientDelegate, ded ded, ddc ddc, ddc ddc2, int i) {
        this.f = i;
        this.d = kli;
        this.a = ambientDelegate;
        this.e = ded;
        this.b = ddc;
        this.c = ddc2;
    }
}
