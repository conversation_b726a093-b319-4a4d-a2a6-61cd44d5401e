<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_focused="true" android:state_pressed="true">
        <shape android:shape="rectangle">
            <corners android:radius="24dp"/>
            <solid android:color="@color/car_ui_rotary_focus_pressed_fill_color"/>
            <stroke android:width="@dimen/car_ui_rotary_focus_pressed_stroke_width" android:color="@color/car_ui_rotary_focus_pressed_stroke_color"/>
        </shape>
    </item>
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <corners android:radius="24dp"/>
            <solid android:color="@color/car_ui_rotary_focus_fill_color"/>
            <stroke android:width="@dimen/car_ui_rotary_focus_stroke_width" android:color="@color/car_ui_rotary_focus_stroke_color"/>
        </shape>
    </item>
    <item>
        <ripple android:color="?android:attr/colorControlHighlight"/>
    </item>
</selector>
