package defpackage;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.preference.ListPreference;
import androidx.preference.Preference;
import com.android.car.ui.FocusArea;
import com.android.car.ui.baselayout.Insets;
import com.android.car.ui.recyclerview.CarUiRecyclerView;
import com.google.android.tts.R;
import java.util.ArrayList;

/* renamed from: bko  reason: default package */
/* compiled from: PG */
public final class bko extends bc implements bjw {
    public bla a;
    public int b = -1;
    public boolean c;
    private ListPreference d;

    public final void a(Insets insets) {
        View requireView = requireView();
        bnv.i(requireView, R.id.list).setPadding(0, insets.getTop(), 0, insets.getBottom());
        requireView.setPadding(insets.getLeft(), 0, insets.getRight(), 0);
        FocusArea focusArea = (FocusArea) requireView.findViewById(R.id.car_ui_focus_area);
        if (focusArea != null) {
            focusArea.c(0, insets.getTop(), 0, insets.getBottom());
            focusArea.b(0, insets.getTop(), 0, insets.getBottom());
        }
    }

    public final void c() {
        ListPreference listPreference;
        if (this.b >= 0 && (listPreference = this.d) != null) {
            String charSequence = listPreference.getEntryValues()[this.b].toString();
            if (this.d.callChangeListener(charSequence)) {
                this.d.setValue(charSequence);
            }
        }
    }

    public final View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        return layoutInflater.inflate(R.layout.car_ui_list_preference, viewGroup, false);
    }

    public final void onStart() {
        Insets I;
        super.onStart();
        if ((getTargetFragment() instanceof bku) && (I = ((bku) getTargetFragment()).I()) != null) {
            a(I);
        }
    }

    public final void onStop() {
        super.onStop();
        if (!this.c) {
            c();
        }
    }

    public final void onViewCreated(View view, Bundle bundle) {
        CarUiRecyclerView carUiRecyclerView = (CarUiRecyclerView) bnv.i(view, R.id.list);
        this.c = getResources().getBoolean(R.bool.car_ui_preference_list_instant_change_callback);
        carUiRecyclerView.setClipToPadding(false);
        String string = requireArguments().getString("key");
        asg asg = (asg) getTargetFragment();
        if (string == null) {
            throw new IllegalStateException("ListPreference key not found in Fragment arguments");
        } else if (asg != null) {
            Preference aW = asg.aW(string);
            if (aW instanceof ListPreference) {
                ListPreference listPreference = (ListPreference) aW;
                this.d = listPreference;
                CharSequence[] entries = listPreference.getEntries();
                CharSequence[] entryValues = this.d.getEntryValues();
                if (entries == null || entryValues == null) {
                    throw new IllegalStateException("ListPreference requires an entries array and an entryValues array.");
                }
                if (entries.length == entryValues.length) {
                    ListPreference listPreference2 = this.d;
                    this.b = listPreference2.findIndexOfValue(listPreference2.getValue());
                    ArrayList arrayList = new ArrayList();
                    bli bli = new bli(arrayList);
                    for (int i = 0; i < entries.length; i++) {
                        String charSequence = entries[i].toString();
                        bla bla = new bla(bkx.RADIO_BUTTON);
                        bla.b(charSequence);
                        if (i == this.b) {
                            bla.a(true);
                            this.a = bla;
                        }
                        bla.h = new bkn(this, bli, arrayList);
                        arrayList.add(bla);
                    }
                    carUiRecyclerView.setAdapter(bli);
                    carUiRecyclerView.scrollToPosition(this.b);
                    carUiRecyclerView.post(new aku((Object) this, (Object) carUiRecyclerView, 11));
                    return;
                }
                throw new IllegalStateException("ListPreference entries array length does not match entryValues array length.");
            }
            throw new IllegalStateException("Cannot use ListPreferenceFragment with a preference that is not of type ListPreference");
        } else {
            throw new IllegalStateException("Target fragment must be registered before displaying ListPreference screen.");
        }
    }
}
