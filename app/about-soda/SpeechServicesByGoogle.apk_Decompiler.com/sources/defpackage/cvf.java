package defpackage;

import android.content.Context;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

/* renamed from: cvf  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvf implements gqx {
    public final /* synthetic */ List a;
    public final /* synthetic */ Set b;
    public final /* synthetic */ dmd c;

    public /* synthetic */ cvf(dmd dmd, List list, Set set) {
        this.c = dmd;
        this.a = list;
        this.b = set;
    }

    /* JADX WARNING: type inference failed for: r3v2, types: [cuk, java.lang.Object] */
    public final Object apply(Object obj) {
        List list = this.a;
        list.addAll((List) obj);
        Iterator it = list.iterator();
        while (true) {
            Set set = this.b;
            if (!it.hasNext()) {
                return set;
            }
            csx csx = (csx) it.next();
            for (csv csv : csx.n) {
                dmd dmd = this.c;
                int x = a.x(csx.i);
                if (x == 0) {
                    x = 1;
                }
                Object obj2 = dmd.h;
                ? r3 = dmd.i;
                htk l = ctj.g.l();
                String k = cqx.k(csv);
                int ordinal = cqh.u((Context) obj2, r3).ordinal();
                if (ordinal == 0) {
                    int i = x - 1;
                    String str = csv.c;
                    if (!l.b.B()) {
                        l.u();
                    }
                    htq htq = l.b;
                    ctj ctj = (ctj) htq;
                    str.getClass();
                    ctj.a = 1 | ctj.a;
                    ctj.b = str;
                    int i2 = csv.d;
                    if (!htq.B()) {
                        l.u();
                    }
                    htq htq2 = l.b;
                    ctj ctj2 = (ctj) htq2;
                    ctj2.a = 2 | ctj2.a;
                    ctj2.c = i2;
                    if (!htq2.B()) {
                        l.u();
                    }
                    htq htq3 = l.b;
                    ctj ctj3 = (ctj) htq3;
                    k.getClass();
                    ctj3.a |= 4;
                    ctj3.d = k;
                    if (!htq3.B()) {
                        l.u();
                    }
                    ctj ctj4 = (ctj) l.b;
                    ctj4.e = i;
                    ctj4.a |= 8;
                } else if (ordinal == 1) {
                    int i3 = x - 1;
                    String str2 = csv.c;
                    if (!l.b.B()) {
                        l.u();
                    }
                    htq htq4 = l.b;
                    ctj ctj5 = (ctj) htq4;
                    str2.getClass();
                    ctj5.a = 1 | ctj5.a;
                    ctj5.b = str2;
                    int i4 = csv.d;
                    if (!htq4.B()) {
                        l.u();
                    }
                    htq htq5 = l.b;
                    ctj ctj6 = (ctj) htq5;
                    ctj6.a = 2 | ctj6.a;
                    ctj6.c = i4;
                    if (!htq5.B()) {
                        l.u();
                    }
                    htq htq6 = l.b;
                    ctj ctj7 = (ctj) htq6;
                    k.getClass();
                    ctj7.a |= 4;
                    ctj7.d = k;
                    if (!htq6.B()) {
                        l.u();
                    }
                    ctj ctj8 = (ctj) l.b;
                    ctj8.e = i3;
                    ctj8.a |= 8;
                    if ((csv.a & 32) != 0) {
                        ihf ihf = csv.g;
                        if (ihf == null) {
                            ihf = ihf.b;
                        }
                        if (!l.b.B()) {
                            l.u();
                        }
                        ctj ctj9 = (ctj) l.b;
                        ihf.getClass();
                        ctj9.f = ihf;
                        ctj9.a |= 16;
                    }
                } else if (ordinal == 2) {
                    int i5 = x - 1;
                    if (!l.b.B()) {
                        l.u();
                    }
                    htq htq7 = l.b;
                    ctj ctj10 = (ctj) htq7;
                    k.getClass();
                    ctj10.a |= 4;
                    ctj10.d = k;
                    if (!htq7.B()) {
                        l.u();
                    }
                    ctj ctj11 = (ctj) l.b;
                    ctj11.e = i5;
                    ctj11.a |= 8;
                }
                set.add((ctj) l.r());
            }
        }
    }
}
