package defpackage;

import android.app.Activity;
import java.util.Set;

/* renamed from: bqx  reason: default package */
/* compiled from: PG */
public final class bqx implements ggj, ggr, gnm, ihq, iif, iio {
    public iiz a;
    public iiz b;
    public final brc c;
    private final Activity d;
    private iiz e;
    private iiz f;
    private iiz g;
    private iiz h;
    private iiz i;
    private iiz j;
    private iiz k;
    private iiz l;
    private iiz m;
    private iiz n;
    private iiz o;
    private iiz p;
    private iiz q;
    private iiz r;
    private iiz s;
    private iiz t;
    private iiz u;
    private iiz v;
    private iiz w;
    private final bqy x;
    private final bqx y;

    public bqx() {
        throw null;
    }

    public final Activity a() {
        Activity activity = this.d;
        iil.c(activity);
        return activity;
    }

    public final gnk b() {
        return (gnk) this.c.g.b();
    }

    public final Set c() {
        return hau.a;
    }

    public final bqr d() {
        return new bqr(this.c, this.x, this.y);
    }

    public final awh e() {
        return (awh) this.w.b();
    }

    public final ixj f() {
        return new ixj((Object) new iiw(gxv.m(true, true, true)), (Object) new bqv(this.c), (byte[]) null);
    }

    public bqx(brc brc, bqy bqy, Activity activity, gge gge) {
        this.y = this;
        this.c = brc;
        this.x = bqy;
        this.d = activity;
        this.a = ijd.a(new ekx(brc.z, 13));
        iiu c2 = iiv.c(activity);
        this.e = c2;
        iil iil = new iil(c2);
        this.f = iil;
        iit.d(new gfa(iil, brc.k, brc.h, 0));
        this.g = new glz(this.e, 8);
        iiu c3 = iiv.c(gge);
        this.h = c3;
        this.i = ijd.a(new fxu(this.g, c3, 19));
        this.j = new drj(this.e, 2);
        iiz iiz = brc.x;
        iiz iiz2 = this.j;
        iiz iiz3 = this.i;
        gfs gfs = new gfs(iiz, iiz2, iiz3);
        this.k = gfs;
        this.l = new gft(iiz2, iiz3, gfs);
        this.m = bqw.a;
        this.n = iit.d(new foh(ijc.a, 4));
        this.o = iit.d(new foh(this.i, 6));
        this.p = bqw.a;
        iis iis = new iis();
        this.q = iis;
        this.r = iit.d(new fwz(this.i, this.n, this.o, this.p, iis, 1));
        iiz d2 = iit.d(new fwz(this.i, this.n, this.o, this.p, this.q, 0));
        this.s = d2;
        fxa fxa = new fxa(this.m, this.r, d2);
        this.t = fxa;
        fww fww = new fww(fxa);
        this.u = fww;
        foh foh = new foh(fww, 2);
        this.v = foh;
        new bqs(foh, 1, (byte[]) null);
        iis.a((iis) this.q, iit.d(new fxu(this.i, this.l, 18, (boolean[]) null)));
        this.w = iit.d(new fxu(this.f, ijc.a, 20));
        this.b = ijd.a(new gbh(this.i, 11));
    }
}
