package defpackage;

/* renamed from: bpl  reason: default package */
/* compiled from: PG */
public final class bpl {
    public final boz a;
    public final awh b;

    public bpl() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof bpl) {
            bpl bpl = (bpl) obj;
            if (!this.a.equals(bpl.a) || !this.b.equals(bpl.b)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return ((this.a.hashCode() ^ 1000003) * 1000003) ^ this.b.hashCode();
    }

    public final String toString() {
        awh awh = this.b;
        String obj = this.a.toString();
        String obj2 = awh.toString();
        return "ServiceContext{service=" + obj + ", disconnectSignal=" + obj2 + "}";
    }

    public bpl(boz boz, awh awh) {
        if (boz != null) {
            this.a = boz;
            if (awh != null) {
                this.b = awh;
                return;
            }
            throw new NullPointerException("Null disconnectSignal");
        }
        throw new NullPointerException("Null service");
    }
}
