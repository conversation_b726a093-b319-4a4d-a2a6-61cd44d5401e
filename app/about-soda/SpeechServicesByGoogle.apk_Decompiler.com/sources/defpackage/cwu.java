package defpackage;

import android.net.Uri;
import java.util.List;

/* renamed from: cwu  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwu implements hko {
    public final /* synthetic */ hme a;
    public final /* synthetic */ hme b;
    public final /* synthetic */ hme c;
    public final /* synthetic */ hme d;
    public final /* synthetic */ hme e;
    public final /* synthetic */ csv f;
    public final /* synthetic */ ctg g;
    public final /* synthetic */ ctj h;
    public final /* synthetic */ csz i;
    public final /* synthetic */ int j;
    public final /* synthetic */ List k;
    public final /* synthetic */ hse l;
    public final /* synthetic */ dbw m;

    public /* synthetic */ cwu(dbw dbw, hme hme, hme hme2, hme hme3, hme hme4, hme hme5, csv csv, ctg ctg, ctj ctj, csz csz, int i2, List list, hse hse) {
        this.m = dbw;
        this.a = hme;
        this.b = hme2;
        this.c = hme3;
        this.d = hme4;
        this.e = hme5;
        this.f = csv;
        this.g = ctg;
        this.h = ctj;
        this.i = csz;
        this.j = i2;
        this.k = list;
        this.l = hse;
    }

    /* JADX WARNING: type inference failed for: r2v6, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme a(Object obj) {
        Void voidR = (Void) obj;
        ctl ctl = (ctl) hfc.S(this.a);
        csy csy = (csy) hfc.S(this.b);
        String str = (String) hfc.S(this.c);
        Uri uri = (Uri) hfc.S(this.d);
        csx csx = (csx) hfc.S(this.e);
        ctf b2 = ctf.b(ctl.c);
        if (b2 == null) {
            b2 = ctf.NONE;
        }
        csv csv = this.f;
        cyh.e("%s: startDownload status %s for %s", "SharedFileManager", b2, csv.c);
        int i2 = ctl.c;
        ctf b3 = ctf.b(i2);
        if (b3 == null) {
            b3 = ctf.NONE;
        }
        ctg ctg = this.g;
        dbw dbw = this.m;
        if (b3 == ctf.DOWNLOAD_COMPLETE) {
            if (((grh) dbw.e).f()) {
                ((czp) ((grh) dbw.e).b()).h(ctg.b, (long) csv.d);
            }
            return hma.a;
        }
        ctf b4 = ctf.b(i2);
        if (b4 == null) {
            b4 = ctf.NONE;
        }
        hse hse = this.l;
        List list = this.k;
        int i3 = this.j;
        csz csz = this.i;
        ctj ctj = this.h;
        if (b4 == ctf.DOWNLOAD_IN_PROGRESS) {
            hme a2 = ((cxx) dbw.h).a(ctj.d, uri);
            cwy cwy = r3;
            dbw dbw2 = dbw;
            cwy cwy2 = new cwy(dbw, ctg, uri, ctj, str, csx, csv, csy, csz, i3, list, hse);
            return ftd.L(a2, cwy, dbw2.l);
        }
        return dbw.k(ctj, str, csx.e, csx.r, csx.s, ctg, csv, csy, csz, i3, list, hse);
    }
}
