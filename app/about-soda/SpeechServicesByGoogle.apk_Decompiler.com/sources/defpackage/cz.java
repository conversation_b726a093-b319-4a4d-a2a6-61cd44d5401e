package defpackage;

import android.view.View;
import android.view.ViewGroup;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;
import java.util.Map;
import java.util.Objects;

/* renamed from: cz  reason: default package */
/* compiled from: PG */
public final class cz {
    public final ViewGroup a;
    public final List b = new ArrayList();
    public final List c = new ArrayList();
    public boolean d;
    public boolean e;
    private boolean f;

    public cz(ViewGroup viewGroup) {
        jnu.e(viewGroup, "container");
        this.a = viewGroup;
    }

    public static final cz c(ViewGroup viewGroup, bw bwVar) {
        jnu.e(viewGroup, "container");
        a aj = bwVar.aj();
        jnu.d(aj, "fragmentManager.specialEffectsControllerFactory");
        return a.T(viewGroup, aj);
    }

    private final void k() {
        for (cy cyVar : this.b) {
            if (cyVar.b == cw.ADDING) {
                cyVar.g(a.R(cyVar.c.requireView().getVisibility()), cw.NONE);
            }
        }
    }

    private final void l(Map map, View view) {
        String f2 = wa.f(view);
        if (f2 != null) {
            map.put(f2, view);
        }
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            int childCount = viewGroup.getChildCount();
            for (int i = 0; i < childCount; i++) {
                View childAt = viewGroup.getChildAt(i);
                if (childAt.getVisibility() == 0) {
                    jnu.d(childAt, "child");
                    l(map, childAt);
                }
            }
        }
    }

    private static void m(ot otVar, Collection collection) {
        jji.Q(otVar.entrySet(), new mz(collection, 1));
    }

    public final cy a(bc bcVar) {
        Object obj;
        Iterator it = this.b.iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            cy cyVar = (cy) obj;
            if (jnu.i(cyVar.c, bcVar) && !cyVar.d) {
                break;
            }
        }
        return (cy) obj;
    }

    public final cy b(bc bcVar) {
        Object obj;
        Iterator it = this.c.iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            cy cyVar = (cy) obj;
            if (jnu.i(cyVar.c, bcVar) && !cyVar.d) {
                break;
            }
        }
        return (cy) obj;
    }

    public final void d(cy cyVar) {
        jnu.e(cyVar, "operation");
        if (cyVar.h) {
            cyVar.a.a(cyVar.c.requireView(), this.a);
            cyVar.h();
        }
    }

    public final void e(List list) {
        ArrayList arrayList = new ArrayList();
        Iterator it = list.iterator();
        while (it.hasNext()) {
            jji.J(arrayList, ((cy) it.next()).i);
        }
        List B = jji.B(jji.G(arrayList));
        int size = B.size();
        for (int i = 0; i < size; i++) {
            ((cu) B.get(i)).b(this.a);
        }
        int size2 = list.size();
        for (int i2 = 0; i2 < size2; i2++) {
            d((cy) list.get(i2));
        }
        List B2 = jji.B(list);
        int size3 = B2.size();
        for (int i3 = 0; i3 < size3; i3++) {
            cy cyVar = (cy) B2.get(i3);
            if (cyVar.i.isEmpty()) {
                cyVar.a();
            }
        }
    }

    public final void f(cx cxVar, cw cwVar, cb cbVar) {
        synchronized (this.b) {
            bc bcVar = cbVar.a;
            jnu.d(bcVar, "fragmentStateManager.fragment");
            cy a2 = a(bcVar);
            if (a2 == null) {
                bc bcVar2 = cbVar.a;
                if (bcVar2.s) {
                    jnu.d(bcVar2, "fragmentStateManager.fragment");
                    a2 = b(bcVar2);
                } else {
                    a2 = null;
                }
            }
            if (a2 != null) {
                a2.g(cxVar, cwVar);
                return;
            }
            cv cvVar = new cv(cxVar, cwVar, cbVar);
            this.b.add(cvVar);
            cvVar.c(new aj((Object) this, (Object) cvVar, 5));
            cvVar.c(new aj((Object) this, (Object) cvVar, 6));
        }
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v4, resolved type: cy} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v5, resolved type: cy} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v24, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r11v3, resolved type: cy} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v28, resolved type: cy} */
    /* JADX WARNING: Code restructure failed: missing block: B:246:0x05ad, code lost:
        return;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:75:0x0163, code lost:
        if (r8 == r11) goto L_0x0165;
     */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:231:0x056f A[LOOP:23: B:229:0x0569->B:231:0x056f, LOOP_END] */
    /* JADX WARNING: Removed duplicated region for block: B:238:0x0586  */
    /* JADX WARNING: Removed duplicated region for block: B:239:0x058f  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void g() {
        /*
            r29 = this;
            r1 = r29
            boolean r0 = r1.e
            if (r0 == 0) goto L_0x0007
            return
        L_0x0007:
            android.view.ViewGroup r0 = r1.a
            boolean r0 = r0.isAttachedToWindow()
            r2 = 0
            if (r0 != 0) goto L_0x0016
            r29.h()
            r1.d = r2
            return
        L_0x0016:
            java.util.List r3 = r1.b
            monitor-enter(r3)
            java.util.List r0 = r1.c     // Catch:{ all -> 0x05ae }
            java.util.List r0 = defpackage.jji.D(r0)     // Catch:{ all -> 0x05ae }
            java.util.List r4 = r1.c     // Catch:{ all -> 0x05ae }
            r4.clear()     // Catch:{ all -> 0x05ae }
            java.util.Iterator r0 = r0.iterator()     // Catch:{ all -> 0x05ae }
        L_0x0028:
            boolean r4 = r0.hasNext()     // Catch:{ all -> 0x05ae }
            r5 = 2
            r6 = 1
            if (r4 == 0) goto L_0x007f
            java.lang.Object r4 = r0.next()     // Catch:{ all -> 0x05ae }
            cy r4 = (defpackage.cy) r4     // Catch:{ all -> 0x05ae }
            boolean r7 = r1.f     // Catch:{ all -> 0x05ae }
            if (r7 == 0) goto L_0x0047
            boolean r5 = defpackage.bw.V(r5)     // Catch:{ all -> 0x05ae }
            if (r5 == 0) goto L_0x0043
            java.util.Objects.toString(r4)     // Catch:{ all -> 0x05ae }
        L_0x0043:
            r4.a()     // Catch:{ all -> 0x05ae }
            goto L_0x0073
        L_0x0047:
            boolean r5 = defpackage.bw.V(r5)     // Catch:{ all -> 0x05ae }
            if (r5 == 0) goto L_0x0050
            java.util.Objects.toString(r4)     // Catch:{ all -> 0x05ae }
        L_0x0050:
            android.view.ViewGroup r5 = r1.a     // Catch:{ all -> 0x05ae }
            java.util.List r7 = r1.b     // Catch:{ all -> 0x05ae }
            boolean r7 = r7.isEmpty()     // Catch:{ all -> 0x05ae }
            if (r7 != 0) goto L_0x0062
            bc r7 = r4.c     // Catch:{ all -> 0x05ae }
            boolean r7 = r7.s     // Catch:{ all -> 0x05ae }
            if (r7 == 0) goto L_0x0062
            r7 = r6
            goto L_0x0063
        L_0x0062:
            r7 = r2
        L_0x0063:
            java.lang.String r8 = "container"
            defpackage.jnu.e(r5, r8)     // Catch:{ all -> 0x05ae }
            boolean r8 = r4.d     // Catch:{ all -> 0x05ae }
            if (r8 != 0) goto L_0x0073
            if (r7 == 0) goto L_0x0070
            r4.f = r6     // Catch:{ all -> 0x05ae }
        L_0x0070:
            r4.e(r5)     // Catch:{ all -> 0x05ae }
        L_0x0073:
            r1.f = r2     // Catch:{ all -> 0x05ae }
            boolean r5 = r4.e     // Catch:{ all -> 0x05ae }
            if (r5 != 0) goto L_0x0028
            java.util.List r5 = r1.c     // Catch:{ all -> 0x05ae }
            r5.add(r4)     // Catch:{ all -> 0x05ae }
            goto L_0x0028
        L_0x007f:
            java.util.List r0 = r1.b     // Catch:{ all -> 0x05ae }
            boolean r0 = r0.isEmpty()     // Catch:{ all -> 0x05ae }
            if (r0 != 0) goto L_0x05ac
            r29.k()     // Catch:{ all -> 0x05ae }
            java.util.List r0 = r1.b     // Catch:{ all -> 0x05ae }
            java.util.List r0 = defpackage.jji.D(r0)     // Catch:{ all -> 0x05ae }
            boolean r4 = r0.isEmpty()     // Catch:{ all -> 0x05ae }
            if (r4 == 0) goto L_0x0098
            monitor-exit(r3)
            return
        L_0x0098:
            java.util.List r4 = r1.b     // Catch:{ all -> 0x05ae }
            r4.clear()     // Catch:{ all -> 0x05ae }
            java.util.List r4 = r1.c     // Catch:{ all -> 0x05ae }
            r4.addAll(r0)     // Catch:{ all -> 0x05ae }
            boolean r4 = r1.d     // Catch:{ all -> 0x05ae }
            java.util.Iterator r7 = r0.iterator()     // Catch:{ all -> 0x05ae }
        L_0x00a8:
            boolean r8 = r7.hasNext()     // Catch:{ all -> 0x05ae }
            if (r8 == 0) goto L_0x00cb
            java.lang.Object r8 = r7.next()     // Catch:{ all -> 0x05ae }
            r10 = r8
            cy r10 = (defpackage.cy) r10     // Catch:{ all -> 0x05ae }
            bc r11 = r10.c     // Catch:{ all -> 0x05ae }
            android.view.View r11 = r11.P     // Catch:{ all -> 0x05ae }
            java.lang.String r12 = "operation.fragment.mView"
            defpackage.jnu.d(r11, r12)     // Catch:{ all -> 0x05ae }
            cx r11 = defpackage.a.S(r11)     // Catch:{ all -> 0x05ae }
            cx r12 = defpackage.cx.VISIBLE     // Catch:{ all -> 0x05ae }
            if (r11 != r12) goto L_0x00a8
            cx r10 = r10.a     // Catch:{ all -> 0x05ae }
            if (r10 == r12) goto L_0x00a8
            goto L_0x00cc
        L_0x00cb:
            r8 = 0
        L_0x00cc:
            r10 = r8
            cy r10 = (defpackage.cy) r10     // Catch:{ all -> 0x05ae }
            int r7 = r0.size()     // Catch:{ all -> 0x05ae }
            java.util.ListIterator r7 = r0.listIterator(r7)     // Catch:{ all -> 0x05ae }
        L_0x00d7:
            boolean r8 = r7.hasPrevious()     // Catch:{ all -> 0x05ae }
            if (r8 == 0) goto L_0x00fa
            java.lang.Object r8 = r7.previous()     // Catch:{ all -> 0x05ae }
            r11 = r8
            cy r11 = (defpackage.cy) r11     // Catch:{ all -> 0x05ae }
            bc r12 = r11.c     // Catch:{ all -> 0x05ae }
            android.view.View r12 = r12.P     // Catch:{ all -> 0x05ae }
            java.lang.String r13 = "operation.fragment.mView"
            defpackage.jnu.d(r12, r13)     // Catch:{ all -> 0x05ae }
            cx r12 = defpackage.a.S(r12)     // Catch:{ all -> 0x05ae }
            cx r13 = defpackage.cx.VISIBLE     // Catch:{ all -> 0x05ae }
            if (r12 == r13) goto L_0x00d7
            cx r11 = r11.a     // Catch:{ all -> 0x05ae }
            if (r11 != r13) goto L_0x00d7
            goto L_0x00fb
        L_0x00fa:
            r8 = 0
        L_0x00fb:
            r11 = r8
            cy r11 = (defpackage.cy) r11     // Catch:{ all -> 0x05ae }
            boolean r7 = defpackage.bw.V(r5)     // Catch:{ all -> 0x05ae }
            if (r7 == 0) goto L_0x010a
            java.util.Objects.toString(r10)     // Catch:{ all -> 0x05ae }
            java.util.Objects.toString(r11)     // Catch:{ all -> 0x05ae }
        L_0x010a:
            java.util.ArrayList r15 = new java.util.ArrayList     // Catch:{ all -> 0x05ae }
            r15.<init>()     // Catch:{ all -> 0x05ae }
            java.util.ArrayList r7 = new java.util.ArrayList     // Catch:{ all -> 0x05ae }
            r7.<init>()     // Catch:{ all -> 0x05ae }
            java.lang.Object r8 = defpackage.jji.v(r0)     // Catch:{ all -> 0x05ae }
            cy r8 = (defpackage.cy) r8     // Catch:{ all -> 0x05ae }
            bc r8 = r8.c     // Catch:{ all -> 0x05ae }
            java.util.Iterator r12 = r0.iterator()     // Catch:{ all -> 0x05ae }
        L_0x0120:
            boolean r13 = r12.hasNext()     // Catch:{ all -> 0x05ae }
            if (r13 == 0) goto L_0x0144
            java.lang.Object r13 = r12.next()     // Catch:{ all -> 0x05ae }
            cy r13 = (defpackage.cy) r13     // Catch:{ all -> 0x05ae }
            bc r13 = r13.c     // Catch:{ all -> 0x05ae }
            ay r13 = r13.S     // Catch:{ all -> 0x05ae }
            ay r14 = r8.S     // Catch:{ all -> 0x05ae }
            int r2 = r14.b     // Catch:{ all -> 0x05ae }
            r13.b = r2     // Catch:{ all -> 0x05ae }
            int r2 = r14.c     // Catch:{ all -> 0x05ae }
            r13.c = r2     // Catch:{ all -> 0x05ae }
            int r2 = r14.d     // Catch:{ all -> 0x05ae }
            r13.d = r2     // Catch:{ all -> 0x05ae }
            int r2 = r14.e     // Catch:{ all -> 0x05ae }
            r13.e = r2     // Catch:{ all -> 0x05ae }
            r2 = 0
            goto L_0x0120
        L_0x0144:
            java.util.Iterator r2 = r0.iterator()     // Catch:{ all -> 0x05ae }
        L_0x0148:
            boolean r8 = r2.hasNext()     // Catch:{ all -> 0x05ae }
            if (r8 == 0) goto L_0x0177
            java.lang.Object r8 = r2.next()     // Catch:{ all -> 0x05ae }
            cy r8 = (defpackage.cy) r8     // Catch:{ all -> 0x05ae }
            ae r12 = new ae     // Catch:{ all -> 0x05ae }
            r12.<init>(r8, r4)     // Catch:{ all -> 0x05ae }
            r15.add(r12)     // Catch:{ all -> 0x05ae }
            an r12 = new an     // Catch:{ all -> 0x05ae }
            if (r4 == 0) goto L_0x0163
            if (r8 != r10) goto L_0x0167
            goto L_0x0165
        L_0x0163:
            if (r8 != r11) goto L_0x0167
        L_0x0165:
            r13 = r6
            goto L_0x0168
        L_0x0167:
            r13 = 0
        L_0x0168:
            r12.<init>(r8, r4, r13)     // Catch:{ all -> 0x05ae }
            r7.add(r12)     // Catch:{ all -> 0x05ae }
            aj r12 = new aj     // Catch:{ all -> 0x05ae }
            r12.<init>((java.lang.Object) r1, (java.lang.Object) r8, (int) r6)     // Catch:{ all -> 0x05ae }
            r8.c(r12)     // Catch:{ all -> 0x05ae }
            goto L_0x0148
        L_0x0177:
            java.util.ArrayList r2 = new java.util.ArrayList     // Catch:{ all -> 0x05ae }
            r2.<init>()     // Catch:{ all -> 0x05ae }
            java.util.Iterator r7 = r7.iterator()     // Catch:{ all -> 0x05ae }
        L_0x0180:
            boolean r8 = r7.hasNext()     // Catch:{ all -> 0x05ae }
            if (r8 == 0) goto L_0x0197
            java.lang.Object r8 = r7.next()     // Catch:{ all -> 0x05ae }
            r12 = r8
            an r12 = (defpackage.an) r12     // Catch:{ all -> 0x05ae }
            boolean r12 = r12.b()     // Catch:{ all -> 0x05ae }
            if (r12 != 0) goto L_0x0180
            r2.add(r8)     // Catch:{ all -> 0x05ae }
            goto L_0x0180
        L_0x0197:
            java.util.ArrayList r14 = new java.util.ArrayList     // Catch:{ all -> 0x05ae }
            r14.<init>()     // Catch:{ all -> 0x05ae }
            java.util.Iterator r2 = r2.iterator()     // Catch:{ all -> 0x05ae }
        L_0x01a0:
            boolean r7 = r2.hasNext()     // Catch:{ all -> 0x05ae }
            if (r7 == 0) goto L_0x01b7
            java.lang.Object r7 = r2.next()     // Catch:{ all -> 0x05ae }
            r8 = r7
            an r8 = (defpackage.an) r8     // Catch:{ all -> 0x05ae }
            cm r8 = r8.a()     // Catch:{ all -> 0x05ae }
            if (r8 == 0) goto L_0x01a0
            r14.add(r7)     // Catch:{ all -> 0x05ae }
            goto L_0x01a0
        L_0x01b7:
            java.util.Iterator r2 = r14.iterator()     // Catch:{ all -> 0x05ae }
            r12 = 0
        L_0x01bc:
            boolean r7 = r2.hasNext()     // Catch:{ all -> 0x05ae }
            if (r7 == 0) goto L_0x01fd
            java.lang.Object r7 = r2.next()     // Catch:{ all -> 0x05ae }
            an r7 = (defpackage.an) r7     // Catch:{ all -> 0x05ae }
            cm r8 = r7.a()     // Catch:{ all -> 0x05ae }
            if (r12 == 0) goto L_0x01fb
            if (r8 != r12) goto L_0x01d1
            goto L_0x01fb
        L_0x01d1:
            java.lang.StringBuilder r0 = new java.lang.StringBuilder     // Catch:{ all -> 0x05ae }
            r0.<init>()     // Catch:{ all -> 0x05ae }
            java.lang.String r2 = "Mixing framework transitions and AndroidX transitions is not allowed. Fragment "
            r0.append(r2)     // Catch:{ all -> 0x05ae }
            cy r2 = r7.a     // Catch:{ all -> 0x05ae }
            bc r2 = r2.c     // Catch:{ all -> 0x05ae }
            r0.append(r2)     // Catch:{ all -> 0x05ae }
            java.lang.String r2 = " returned Transition "
            r0.append(r2)     // Catch:{ all -> 0x05ae }
            java.lang.Object r2 = r7.b     // Catch:{ all -> 0x05ae }
            r0.append(r2)     // Catch:{ all -> 0x05ae }
            java.lang.String r2 = " which uses a different Transition type than other Fragments."
            r0.append(r2)     // Catch:{ all -> 0x05ae }
            java.lang.String r0 = r0.toString()     // Catch:{ all -> 0x05ae }
            java.lang.IllegalArgumentException r2 = new java.lang.IllegalArgumentException     // Catch:{ all -> 0x05ae }
            r2.<init>(r0)     // Catch:{ all -> 0x05ae }
            throw r2     // Catch:{ all -> 0x05ae }
        L_0x01fb:
            r12 = r8
            goto L_0x01bc
        L_0x01fd:
            if (r12 != 0) goto L_0x0205
            r23 = r0
            r22 = r15
            goto L_0x0452
        L_0x0205:
            java.util.ArrayList r13 = new java.util.ArrayList     // Catch:{ all -> 0x05ae }
            r13.<init>()     // Catch:{ all -> 0x05ae }
            java.util.ArrayList r2 = new java.util.ArrayList     // Catch:{ all -> 0x05ae }
            r2.<init>()     // Catch:{ all -> 0x05ae }
            ot r8 = new ot     // Catch:{ all -> 0x05ae }
            r8.<init>()     // Catch:{ all -> 0x05ae }
            java.util.ArrayList r7 = new java.util.ArrayList     // Catch:{ all -> 0x05ae }
            r7.<init>()     // Catch:{ all -> 0x05ae }
            java.util.ArrayList r16 = new java.util.ArrayList     // Catch:{ all -> 0x05ae }
            r16.<init>()     // Catch:{ all -> 0x05ae }
            ot r6 = new ot     // Catch:{ all -> 0x05ae }
            r6.<init>()     // Catch:{ all -> 0x05ae }
            ot r9 = new ot     // Catch:{ all -> 0x05ae }
            r9.<init>()     // Catch:{ all -> 0x05ae }
            java.util.Iterator r18 = r14.iterator()     // Catch:{ all -> 0x05ae }
            r19 = r7
            r20 = r16
            r16 = 0
        L_0x0232:
            boolean r7 = r18.hasNext()     // Catch:{ all -> 0x05ae }
            if (r7 == 0) goto L_0x03f1
            java.lang.Object r7 = r18.next()     // Catch:{ all -> 0x05ae }
            an r7 = (defpackage.an) r7     // Catch:{ all -> 0x05ae }
            boolean r21 = r7.c()     // Catch:{ all -> 0x05ae }
            if (r21 == 0) goto L_0x03d1
            if (r10 == 0) goto L_0x03d1
            if (r11 == 0) goto L_0x03d1
            java.lang.Object r7 = r7.d     // Catch:{ all -> 0x05ae }
            java.lang.Object r7 = r12.a(r7)     // Catch:{ all -> 0x05ae }
            java.lang.Object r7 = r12.c(r7)     // Catch:{ all -> 0x05ae }
            bc r5 = r11.c     // Catch:{ all -> 0x05ae }
            java.util.ArrayList r5 = r5.m()     // Catch:{ all -> 0x05ae }
            r22 = r15
            bc r15 = r10.c     // Catch:{ all -> 0x05ae }
            java.util.ArrayList r15 = r15.m()     // Catch:{ all -> 0x05ae }
            r23 = r0
            bc r0 = r10.c     // Catch:{ all -> 0x05ae }
            java.util.ArrayList r0 = r0.n()     // Catch:{ all -> 0x05ae }
            r24 = r12
            int r12 = r0.size()     // Catch:{ all -> 0x05ae }
            r26 = r2
            r25 = r14
            r14 = 0
        L_0x0273:
            if (r14 >= r12) goto L_0x028e
            java.lang.Object r2 = r0.get(r14)     // Catch:{ all -> 0x05ae }
            int r2 = r5.indexOf(r2)     // Catch:{ all -> 0x05ae }
            r19 = r0
            r0 = -1
            if (r2 == r0) goto L_0x0289
            java.lang.Object r0 = r15.get(r14)     // Catch:{ all -> 0x05ae }
            r5.set(r2, r0)     // Catch:{ all -> 0x05ae }
        L_0x0289:
            int r14 = r14 + 1
            r0 = r19
            goto L_0x0273
        L_0x028e:
            bc r0 = r11.c     // Catch:{ all -> 0x05ae }
            java.util.ArrayList r0 = r0.n()     // Catch:{ all -> 0x05ae }
            if (r4 != 0) goto L_0x02a8
            bc r2 = r10.c     // Catch:{ all -> 0x05ae }
            bc r12 = r11.c     // Catch:{ all -> 0x05ae }
            ki r2 = r2.A()     // Catch:{ all -> 0x05ae }
            ki r12 = r12.z()     // Catch:{ all -> 0x05ae }
            jjs r14 = new jjs     // Catch:{ all -> 0x05ae }
            r14.<init>(r2, r12)     // Catch:{ all -> 0x05ae }
            goto L_0x02b9
        L_0x02a8:
            bc r2 = r10.c     // Catch:{ all -> 0x05ae }
            bc r12 = r11.c     // Catch:{ all -> 0x05ae }
            ki r2 = r2.z()     // Catch:{ all -> 0x05ae }
            ki r12 = r12.A()     // Catch:{ all -> 0x05ae }
            jjs r14 = new jjs     // Catch:{ all -> 0x05ae }
            r14.<init>(r2, r12)     // Catch:{ all -> 0x05ae }
        L_0x02b9:
            java.lang.Object r2 = r14.b     // Catch:{ all -> 0x05ae }
            java.lang.Object r12 = r14.a     // Catch:{ all -> 0x05ae }
            ki r12 = (defpackage.ki) r12     // Catch:{ all -> 0x05ae }
            ki r2 = (defpackage.ki) r2     // Catch:{ all -> 0x05ae }
            int r14 = r5.size()     // Catch:{ all -> 0x05ae }
            r15 = 0
        L_0x02c6:
            if (r15 >= r14) goto L_0x02f0
            r19 = r14
            java.lang.Object r14 = r5.get(r15)     // Catch:{ all -> 0x05ae }
            r27 = r4
            java.lang.String r4 = "exitingNames[i]"
            defpackage.jnu.d(r14, r4)     // Catch:{ all -> 0x05ae }
            java.lang.String r14 = (java.lang.String) r14     // Catch:{ all -> 0x05ae }
            java.lang.Object r4 = r0.get(r15)     // Catch:{ all -> 0x05ae }
            r28 = r13
            java.lang.String r13 = "enteringNames[i]"
            defpackage.jnu.d(r4, r13)     // Catch:{ all -> 0x05ae }
            java.lang.String r4 = (java.lang.String) r4     // Catch:{ all -> 0x05ae }
            r8.put(r14, r4)     // Catch:{ all -> 0x05ae }
            int r15 = r15 + 1
            r14 = r19
            r4 = r27
            r13 = r28
            goto L_0x02c6
        L_0x02f0:
            r27 = r4
            r28 = r13
            r4 = 2
            boolean r13 = defpackage.bw.V(r4)     // Catch:{ all -> 0x05ae }
            if (r13 == 0) goto L_0x031b
            int r4 = r0.size()     // Catch:{ all -> 0x05ae }
            r13 = 0
        L_0x0300:
            if (r13 >= r4) goto L_0x030b
            java.lang.Object r14 = r0.get(r13)     // Catch:{ all -> 0x05ae }
            java.lang.String r14 = (java.lang.String) r14     // Catch:{ all -> 0x05ae }
            int r13 = r13 + 1
            goto L_0x0300
        L_0x030b:
            int r4 = r5.size()     // Catch:{ all -> 0x05ae }
            r13 = 0
        L_0x0310:
            if (r13 >= r4) goto L_0x031b
            java.lang.Object r14 = r5.get(r13)     // Catch:{ all -> 0x05ae }
            java.lang.String r14 = (java.lang.String) r14     // Catch:{ all -> 0x05ae }
            int r13 = r13 + 1
            goto L_0x0310
        L_0x031b:
            bc r4 = r10.c     // Catch:{ all -> 0x05ae }
            java.lang.String r13 = "firstOut.fragment.mView"
            android.view.View r4 = r4.P     // Catch:{ all -> 0x05ae }
            defpackage.jnu.d(r4, r13)     // Catch:{ all -> 0x05ae }
            r1.l(r6, r4)     // Catch:{ all -> 0x05ae }
            r6.a(r5)     // Catch:{ all -> 0x05ae }
            if (r12 == 0) goto L_0x0338
            r4 = 2
            boolean r0 = defpackage.bw.V(r4)     // Catch:{ all -> 0x05ae }
            if (r0 == 0) goto L_0x0336
            java.util.Objects.toString(r10)     // Catch:{ all -> 0x05ae }
        L_0x0336:
            r0 = 0
            throw r0     // Catch:{ all -> 0x05ae }
        L_0x0338:
            java.util.Set r4 = r6.keySet()     // Catch:{ all -> 0x05ae }
            r8.a(r4)     // Catch:{ all -> 0x05ae }
            bc r4 = r11.c     // Catch:{ all -> 0x05ae }
            java.lang.String r12 = "lastIn.fragment.mView"
            android.view.View r4 = r4.P     // Catch:{ all -> 0x05ae }
            defpackage.jnu.d(r4, r12)     // Catch:{ all -> 0x05ae }
            r1.l(r9, r4)     // Catch:{ all -> 0x05ae }
            r9.a(r0)     // Catch:{ all -> 0x05ae }
            java.util.Collection r4 = r8.values()     // Catch:{ all -> 0x05ae }
            r9.a(r4)     // Catch:{ all -> 0x05ae }
            if (r2 == 0) goto L_0x0363
            r2 = 2
            boolean r0 = defpackage.bw.V(r2)     // Catch:{ all -> 0x05ae }
            if (r0 == 0) goto L_0x0361
            java.util.Objects.toString(r11)     // Catch:{ all -> 0x05ae }
        L_0x0361:
            r2 = 0
            throw r2     // Catch:{ all -> 0x05ae }
        L_0x0363:
            r2 = 0
            cm r4 = defpackage.ce.a     // Catch:{ all -> 0x05ae }
            int r4 = r8.f     // Catch:{ all -> 0x05ae }
            r12 = -1
            int r4 = r4 + r12
        L_0x036a:
            if (r4 < 0) goto L_0x037e
            java.lang.Object r12 = r8.g(r4)     // Catch:{ all -> 0x05ae }
            java.lang.String r12 = (java.lang.String) r12     // Catch:{ all -> 0x05ae }
            boolean r12 = r9.containsKey(r12)     // Catch:{ all -> 0x05ae }
            if (r12 != 0) goto L_0x037b
            r8.e(r4)     // Catch:{ all -> 0x05ae }
        L_0x037b:
            int r4 = r4 + -1
            goto L_0x036a
        L_0x037e:
            java.util.Set r4 = r8.keySet()     // Catch:{ all -> 0x05ae }
            m(r6, r4)     // Catch:{ all -> 0x05ae }
            java.util.Collection r4 = r8.values()     // Catch:{ all -> 0x05ae }
            m(r9, r4)     // Catch:{ all -> 0x05ae }
            boolean r4 = r8.isEmpty()     // Catch:{ all -> 0x05ae }
            if (r4 == 0) goto L_0x03ca
            java.lang.StringBuilder r4 = new java.lang.StringBuilder     // Catch:{ all -> 0x05ae }
            r4.<init>()     // Catch:{ all -> 0x05ae }
            java.lang.String r12 = "Ignoring shared elements transition "
            r4.append(r12)     // Catch:{ all -> 0x05ae }
            r4.append(r7)     // Catch:{ all -> 0x05ae }
            java.lang.String r7 = " between "
            r4.append(r7)     // Catch:{ all -> 0x05ae }
            r4.append(r10)     // Catch:{ all -> 0x05ae }
            java.lang.String r7 = " and "
            r4.append(r7)     // Catch:{ all -> 0x05ae }
            r4.append(r11)     // Catch:{ all -> 0x05ae }
            java.lang.String r7 = " as there are no matching elements in both the entering and exiting fragment. In order to run a SharedElementTransition, both fragments involved must have the element."
            r4.append(r7)     // Catch:{ all -> 0x05ae }
            java.lang.String r4 = r4.toString()     // Catch:{ all -> 0x05ae }
            java.lang.String r7 = "FragmentManager"
            android.util.Log.i(r7, r4)     // Catch:{ all -> 0x05ae }
            r28.clear()     // Catch:{ all -> 0x05ae }
            r26.clear()     // Catch:{ all -> 0x05ae }
            r19 = r0
            r16 = r2
            r20 = r5
            goto L_0x03e0
        L_0x03ca:
            r19 = r0
            r20 = r5
            r16 = r7
            goto L_0x03e0
        L_0x03d1:
            r23 = r0
            r26 = r2
            r27 = r4
            r24 = r12
            r28 = r13
            r25 = r14
            r22 = r15
            r2 = 0
        L_0x03e0:
            r15 = r22
            r0 = r23
            r12 = r24
            r14 = r25
            r2 = r26
            r4 = r27
            r13 = r28
            r5 = 2
            goto L_0x0232
        L_0x03f1:
            r23 = r0
            r26 = r2
            r27 = r4
            r24 = r12
            r28 = r13
            r25 = r14
            r22 = r15
            if (r16 != 0) goto L_0x041b
            boolean r0 = r25.isEmpty()     // Catch:{ all -> 0x05ae }
            if (r0 != 0) goto L_0x0452
            java.util.Iterator r0 = r25.iterator()     // Catch:{ all -> 0x05ae }
        L_0x040b:
            boolean r2 = r0.hasNext()     // Catch:{ all -> 0x05ae }
            if (r2 == 0) goto L_0x0452
            java.lang.Object r2 = r0.next()     // Catch:{ all -> 0x05ae }
            an r2 = (defpackage.an) r2     // Catch:{ all -> 0x05ae }
            java.lang.Object r2 = r2.b     // Catch:{ all -> 0x05ae }
            if (r2 == 0) goto L_0x040b
        L_0x041b:
            am r0 = new am     // Catch:{ all -> 0x05ae }
            r7 = r0
            r2 = r8
            r8 = r25
            r4 = r9
            r9 = r10
            r10 = r11
            r11 = r24
            r12 = r16
            r13 = r28
            r5 = r25
            r14 = r26
            r15 = r2
            r16 = r19
            r17 = r20
            r18 = r6
            r19 = r4
            r20 = r27
            r7.<init>(r8, r9, r10, r11, r12, r13, r14, r15, r16, r17, r18, r19, r20)     // Catch:{ all -> 0x05ae }
            java.util.Iterator r2 = r5.iterator()     // Catch:{ all -> 0x05ae }
        L_0x0440:
            boolean r4 = r2.hasNext()     // Catch:{ all -> 0x05ae }
            if (r4 == 0) goto L_0x0452
            java.lang.Object r4 = r2.next()     // Catch:{ all -> 0x05ae }
            an r4 = (defpackage.an) r4     // Catch:{ all -> 0x05ae }
            cy r4 = r4.a     // Catch:{ all -> 0x05ae }
            r4.d(r0)     // Catch:{ all -> 0x05ae }
            goto L_0x0440
        L_0x0452:
            java.util.ArrayList r0 = new java.util.ArrayList     // Catch:{ all -> 0x05ae }
            r0.<init>()     // Catch:{ all -> 0x05ae }
            java.util.ArrayList r2 = new java.util.ArrayList     // Catch:{ all -> 0x05ae }
            r2.<init>()     // Catch:{ all -> 0x05ae }
            java.util.Iterator r4 = r22.iterator()     // Catch:{ all -> 0x05ae }
        L_0x0460:
            boolean r5 = r4.hasNext()     // Catch:{ all -> 0x05ae }
            if (r5 == 0) goto L_0x0474
            java.lang.Object r5 = r4.next()     // Catch:{ all -> 0x05ae }
            ae r5 = (defpackage.ae) r5     // Catch:{ all -> 0x05ae }
            cy r5 = r5.a     // Catch:{ all -> 0x05ae }
            java.util.List r5 = r5.i     // Catch:{ all -> 0x05ae }
            defpackage.jji.J(r2, r5)     // Catch:{ all -> 0x05ae }
            goto L_0x0460
        L_0x0474:
            boolean r2 = r2.isEmpty()     // Catch:{ all -> 0x05ae }
            java.util.Iterator r4 = r22.iterator()     // Catch:{ all -> 0x05ae }
            r5 = 0
        L_0x047d:
            boolean r6 = r4.hasNext()     // Catch:{ all -> 0x05ae }
            if (r6 == 0) goto L_0x04cc
            java.lang.Object r6 = r4.next()     // Catch:{ all -> 0x05ae }
            ae r6 = (defpackage.ae) r6     // Catch:{ all -> 0x05ae }
            android.view.ViewGroup r7 = r1.a     // Catch:{ all -> 0x05ae }
            cy r8 = r6.a     // Catch:{ all -> 0x05ae }
            java.lang.String r9 = "context"
            android.content.Context r7 = r7.getContext()     // Catch:{ all -> 0x05ae }
            defpackage.jnu.d(r7, r9)     // Catch:{ all -> 0x05ae }
            bxq r7 = r6.a(r7)     // Catch:{ all -> 0x05ae }
            if (r7 == 0) goto L_0x047d
            java.lang.Object r7 = r7.b     // Catch:{ all -> 0x05ae }
            if (r7 != 0) goto L_0x04a4
            r0.add(r6)     // Catch:{ all -> 0x05ae }
            goto L_0x047d
        L_0x04a4:
            bc r7 = r8.c     // Catch:{ all -> 0x05ae }
            java.util.List r9 = r8.i     // Catch:{ all -> 0x05ae }
            boolean r9 = r9.isEmpty()     // Catch:{ all -> 0x05ae }
            if (r9 != 0) goto L_0x04b9
            r9 = 2
            boolean r6 = defpackage.bw.V(r9)     // Catch:{ all -> 0x05ae }
            if (r6 == 0) goto L_0x047d
            java.util.Objects.toString(r7)     // Catch:{ all -> 0x05ae }
            goto L_0x047d
        L_0x04b9:
            cx r5 = r8.a     // Catch:{ all -> 0x05ae }
            cx r7 = defpackage.cx.GONE     // Catch:{ all -> 0x05ae }
            if (r5 != r7) goto L_0x04c2
            r8.h()     // Catch:{ all -> 0x05ae }
        L_0x04c2:
            ag r5 = new ag     // Catch:{ all -> 0x05ae }
            r5.<init>(r6)     // Catch:{ all -> 0x05ae }
            r8.d(r5)     // Catch:{ all -> 0x05ae }
            r5 = 1
            goto L_0x047d
        L_0x04cc:
            java.util.Iterator r0 = r0.iterator()     // Catch:{ all -> 0x05ae }
        L_0x04d0:
            boolean r4 = r0.hasNext()     // Catch:{ all -> 0x05ae }
            if (r4 == 0) goto L_0x0504
            java.lang.Object r4 = r0.next()     // Catch:{ all -> 0x05ae }
            ae r4 = (defpackage.ae) r4     // Catch:{ all -> 0x05ae }
            cy r6 = r4.a     // Catch:{ all -> 0x05ae }
            bc r7 = r6.c     // Catch:{ all -> 0x05ae }
            if (r2 != 0) goto L_0x04ed
            r8 = 2
            boolean r4 = defpackage.bw.V(r8)     // Catch:{ all -> 0x05ae }
            if (r4 == 0) goto L_0x04d0
            java.util.Objects.toString(r7)     // Catch:{ all -> 0x05ae }
            goto L_0x04d0
        L_0x04ed:
            if (r5 == 0) goto L_0x04fa
            r8 = 2
            boolean r4 = defpackage.bw.V(r8)     // Catch:{ all -> 0x05ae }
            if (r4 == 0) goto L_0x04d0
            java.util.Objects.toString(r7)     // Catch:{ all -> 0x05ae }
            goto L_0x04d0
        L_0x04fa:
            r8 = 2
            ad r7 = new ad     // Catch:{ all -> 0x05ae }
            r7.<init>(r4)     // Catch:{ all -> 0x05ae }
            r6.d(r7)     // Catch:{ all -> 0x05ae }
            goto L_0x04d0
        L_0x0504:
            java.util.Iterator r0 = r23.iterator()     // Catch:{ all -> 0x05ae }
        L_0x0508:
            r2 = 1
        L_0x0509:
            boolean r4 = r0.hasNext()     // Catch:{ all -> 0x05ae }
            if (r4 == 0) goto L_0x053e
            java.lang.Object r2 = r0.next()     // Catch:{ all -> 0x05ae }
            cy r2 = (defpackage.cy) r2     // Catch:{ all -> 0x05ae }
            java.util.List r4 = r2.i     // Catch:{ all -> 0x05ae }
            boolean r4 = r4.isEmpty()     // Catch:{ all -> 0x05ae }
            if (r4 != 0) goto L_0x053c
            java.util.List r2 = r2.i     // Catch:{ all -> 0x05ae }
            boolean r4 = r2.isEmpty()     // Catch:{ all -> 0x05ae }
            if (r4 == 0) goto L_0x0526
            goto L_0x0508
        L_0x0526:
            java.util.Iterator r2 = r2.iterator()     // Catch:{ all -> 0x05ae }
        L_0x052a:
            boolean r4 = r2.hasNext()     // Catch:{ all -> 0x05ae }
            if (r4 == 0) goto L_0x0508
            java.lang.Object r4 = r2.next()     // Catch:{ all -> 0x05ae }
            cu r4 = (defpackage.cu) r4     // Catch:{ all -> 0x05ae }
            boolean r4 = r4.e()     // Catch:{ all -> 0x05ae }
            if (r4 != 0) goto L_0x052a
        L_0x053c:
            r2 = 0
            goto L_0x0509
        L_0x053e:
            if (r2 == 0) goto L_0x0563
            java.util.ArrayList r0 = new java.util.ArrayList     // Catch:{ all -> 0x05ae }
            r0.<init>()     // Catch:{ all -> 0x05ae }
            java.util.Iterator r2 = r23.iterator()     // Catch:{ all -> 0x05ae }
        L_0x0549:
            boolean r4 = r2.hasNext()     // Catch:{ all -> 0x05ae }
            if (r4 == 0) goto L_0x055b
            java.lang.Object r4 = r2.next()     // Catch:{ all -> 0x05ae }
            cy r4 = (defpackage.cy) r4     // Catch:{ all -> 0x05ae }
            java.util.List r4 = r4.i     // Catch:{ all -> 0x05ae }
            defpackage.jji.J(r0, r4)     // Catch:{ all -> 0x05ae }
            goto L_0x0549
        L_0x055b:
            boolean r0 = r0.isEmpty()     // Catch:{ all -> 0x05ae }
            if (r0 != 0) goto L_0x0563
            r0 = 1
            goto L_0x0564
        L_0x0563:
            r0 = 0
        L_0x0564:
            java.util.Iterator r2 = r23.iterator()     // Catch:{ all -> 0x05ae }
            r4 = 1
        L_0x0569:
            boolean r5 = r2.hasNext()     // Catch:{ all -> 0x05ae }
            if (r5 == 0) goto L_0x057b
            java.lang.Object r5 = r2.next()     // Catch:{ all -> 0x05ae }
            cy r5 = (defpackage.cy) r5     // Catch:{ all -> 0x05ae }
            bc r5 = r5.c     // Catch:{ all -> 0x05ae }
            boolean r5 = r5.s     // Catch:{ all -> 0x05ae }
            r4 = r4 & r5
            goto L_0x0569
        L_0x057b:
            if (r4 == 0) goto L_0x0581
            if (r0 != 0) goto L_0x0581
            r6 = 1
            goto L_0x0582
        L_0x0581:
            r6 = 0
        L_0x0582:
            r1.f = r6     // Catch:{ all -> 0x05ae }
            if (r4 != 0) goto L_0x058f
            r2 = r23
            r1.j(r2)     // Catch:{ all -> 0x05ae }
            r1.e(r2)     // Catch:{ all -> 0x05ae }
            goto L_0x05a9
        L_0x058f:
            r2 = r23
            if (r0 == 0) goto L_0x05a9
            r1.j(r2)     // Catch:{ all -> 0x05ae }
            int r0 = r2.size()     // Catch:{ all -> 0x05ae }
            r4 = 0
        L_0x059b:
            if (r4 >= r0) goto L_0x05a9
            java.lang.Object r5 = r2.get(r4)     // Catch:{ all -> 0x05ae }
            cy r5 = (defpackage.cy) r5     // Catch:{ all -> 0x05ae }
            r1.d(r5)     // Catch:{ all -> 0x05ae }
            int r4 = r4 + 1
            goto L_0x059b
        L_0x05a9:
            r0 = 0
            r1.d = r0     // Catch:{ all -> 0x05ae }
        L_0x05ac:
            monitor-exit(r3)
            return
        L_0x05ae:
            r0 = move-exception
            monitor-exit(r3)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cz.g():void");
    }

    public final void h() {
        List list = this.b;
        boolean isAttachedToWindow = this.a.isAttachedToWindow();
        synchronized (list) {
            k();
            j(this.b);
            for (cy cyVar : jji.D(this.c)) {
                if (bw.V(2)) {
                    if (!isAttachedToWindow) {
                        StringBuilder sb = new StringBuilder();
                        sb.append("Container ");
                        sb.append(this.a);
                        sb.append(" is not attached to window. ");
                    }
                    Objects.toString(cyVar);
                }
                cyVar.e(this.a);
            }
            for (cy cyVar2 : jji.D(this.b)) {
                if (bw.V(2)) {
                    if (!isAttachedToWindow) {
                        StringBuilder sb2 = new StringBuilder();
                        sb2.append("Container ");
                        sb2.append(this.a);
                        sb2.append(" is not attached to window. ");
                    }
                    Objects.toString(cyVar2);
                }
                cyVar2.e(this.a);
            }
        }
    }

    public final void i() {
        bc bcVar;
        Object obj;
        synchronized (this.b) {
            k();
            List list = this.b;
            ListIterator listIterator = list.listIterator(list.size());
            while (true) {
                bcVar = null;
                if (!listIterator.hasPrevious()) {
                    obj = null;
                    break;
                }
                obj = listIterator.previous();
                cy cyVar = (cy) obj;
                View view = cyVar.c.P;
                jnu.d(view, "operation.fragment.mView");
                cx S = a.S(view);
                cx cxVar = cyVar.a;
                cx cxVar2 = cx.VISIBLE;
                if (cxVar == cxVar2 && S != cxVar2) {
                    break;
                }
            }
            cy cyVar2 = (cy) obj;
            if (cyVar2 != null) {
                bcVar = cyVar2.c;
            }
            boolean z = false;
            if (bcVar != null) {
                ay ayVar = bcVar.S;
                if (ayVar != null) {
                    z = ayVar.s;
                }
            }
            this.e = z;
        }
    }

    public final void j(List list) {
        int size = list.size();
        for (int i = 0; i < size; i++) {
            ((cy) list.get(i)).b();
        }
        ArrayList arrayList = new ArrayList();
        Iterator it = list.iterator();
        while (it.hasNext()) {
            jji.J(arrayList, ((cy) it.next()).i);
        }
        List B = jji.B(jji.G(arrayList));
        int size2 = B.size();
        for (int i2 = 0; i2 < size2; i2++) {
            cu cuVar = (cu) B.get(i2);
            ViewGroup viewGroup = this.a;
            jnu.e(viewGroup, "container");
            if (!cuVar.h) {
                cuVar.d(viewGroup);
            }
            cuVar.h = true;
        }
    }
}
