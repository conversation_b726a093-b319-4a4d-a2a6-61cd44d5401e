package defpackage;

/* renamed from: cvt  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvt implements hko {
    public final /* synthetic */ cvy a;
    public final /* synthetic */ csv b;
    public final /* synthetic */ csx c;
    public final /* synthetic */ long d;
    public final /* synthetic */ int e;

    public /* synthetic */ cvt(cvy cvy, csv csv, csx csx, int i, long j) {
        this.a = cvy;
        this.b = csv;
        this.c = csx;
        this.e = i;
        this.d = j;
    }

    public final hme a(Object obj) {
        boolean booleanValue = ((Boolean) obj).booleanValue();
        cvy cvy = this.a;
        csx csx = this.c;
        csv csv = this.b;
        if (!booleanValue) {
            cyh.i("%s: Failed to set new state for file %s, filegroup %s", "FileGroupManager", csv.b, csx.c);
            cvy.A(cvy.i, csx, csv, 15);
            return hfc.K(false);
        }
        cyk cyk = cvy.i;
        htk l = hij.j.l();
        if (!l.b.B()) {
            l.u();
        }
        int i = this.e;
        hij hij = (hij) l.b;
        hij.b = hfc.ay(i);
        hij.a |= 1;
        String str = csx.c;
        if (!l.b.B()) {
            l.u();
        }
        htq htq = l.b;
        hij hij2 = (hij) htq;
        str.getClass();
        hij2.a = 2 | hij2.a;
        hij2.c = str;
        int i2 = csx.e;
        if (!htq.B()) {
            l.u();
        }
        htq htq2 = l.b;
        hij hij3 = (hij) htq2;
        hij3.a |= 4;
        hij3.d = i2;
        long j = csx.r;
        if (!htq2.B()) {
            l.u();
        }
        htq htq3 = l.b;
        hij hij4 = (hij) htq3;
        hij4.a |= 128;
        hij4.h = j;
        String str2 = csx.s;
        if (!htq3.B()) {
            l.u();
        }
        htq htq4 = l.b;
        hij hij5 = (hij) htq4;
        str2.getClass();
        hij5.a |= 256;
        hij5.i = str2;
        String str3 = csv.b;
        if (!htq4.B()) {
            l.u();
        }
        htq htq5 = l.b;
        hij hij6 = (hij) htq5;
        str3.getClass();
        hij6.a |= 8;
        hij6.e = str3;
        if (!htq5.B()) {
            l.u();
        }
        htq htq6 = l.b;
        hij hij7 = (hij) htq6;
        hij7.a |= 16;
        hij7.f = true;
        if (!htq6.B()) {
            l.u();
        }
        long j2 = this.d;
        hij hij8 = (hij) l.b;
        hij8.a |= 32;
        hij8.g = j2;
        cyk.a((hij) l.r());
        return hfc.K(true);
    }
}
