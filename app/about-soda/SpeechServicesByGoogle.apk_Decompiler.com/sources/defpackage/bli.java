package defpackage;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import com.google.android.tts.R;
import java.util.List;

/* renamed from: bli  reason: default package */
/* compiled from: PG */
public final class bli extends kc {
    public int a = -1;
    private final List b;

    public bli(List list) {
        this.b = list;
    }

    public final int getItemCount() {
        if (this.a == -1) {
            return this.b.size();
        }
        return Math.min(this.b.size(), this.a);
    }

    public final int getItemViewType(int i) {
        if (this.b.get(i) instanceof bla) {
            bkx bkx = ((bla) this.b.get(i)).c;
            return 1;
        } else if (this.b.get(i) instanceof blc) {
            return 0;
        } else {
            throw new IllegalStateException("Unknown view type.");
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:37:0x00c1, code lost:
        if (r0 != false) goto L_0x00c3;
     */
    /* JADX WARNING: Removed duplicated region for block: B:41:0x00ed  */
    /* JADX WARNING: Removed duplicated region for block: B:60:0x018a  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void onBindViewHolder(defpackage.ld r9, int r10) {
        /*
            r8 = this;
            int r0 = r9.getItemViewType()
            java.lang.String r1 = "Incorrect view holder type for list item."
            r2 = 0
            if (r0 == 0) goto L_0x01d0
            r3 = 2
            r4 = 1
            if (r0 == r4) goto L_0x0018
            if (r0 != r3) goto L_0x0010
            goto L_0x0018
        L_0x0010:
            java.lang.IllegalStateException r9 = new java.lang.IllegalStateException
            java.lang.String r10 = "Unknown item view type."
            r9.<init>(r10)
            throw r9
        L_0x0018:
            boolean r0 = r9 instanceof defpackage.blh
            if (r0 == 0) goto L_0x01ca
            java.util.List r0 = r8.b
            java.lang.Object r10 = r0.get(r10)
            blf r10 = (defpackage.blf) r10
            boolean r0 = r10 instanceof defpackage.bla
            if (r0 == 0) goto L_0x01c2
            blh r9 = (defpackage.blh) r9
            bla r10 = (defpackage.bla) r10
            bjj r0 = r10.b
            r1 = 0
            r5 = 8
            if (r0 == 0) goto L_0x003e
            bok r6 = r9.d
            r6.a(r0)
            bok r0 = r9.d
            r0.setVisibility(r1)
            goto L_0x0043
        L_0x003e:
            bok r0 = r9.d
            r0.setVisibility(r5)
        L_0x0043:
            bok r0 = r9.e
            r0.setVisibility(r5)
            android.widget.ImageView r0 = r9.f
            r0.setVisibility(r5)
            android.widget.ImageView r0 = r9.g
            r0.setVisibility(r5)
            android.widget.ImageView r0 = r9.h
            r0.setVisibility(r5)
            android.graphics.drawable.Drawable r0 = r10.a
            if (r0 == 0) goto L_0x0093
            android.view.ViewGroup r6 = r9.i
            r6.setVisibility(r1)
            bky r6 = r10.d
            int r6 = r6.ordinal()
            if (r6 == 0) goto L_0x0088
            if (r6 == r4) goto L_0x007d
            if (r6 == r3) goto L_0x006d
            goto L_0x0098
        L_0x006d:
            android.widget.ImageView r6 = r9.h
            r6.setVisibility(r1)
            android.widget.ImageView r6 = r9.h
            r6.setImageDrawable(r0)
            android.widget.ImageView r0 = r9.h
            r0.setClipToOutline(r4)
            goto L_0x0098
        L_0x007d:
            android.widget.ImageView r6 = r9.f
            r6.setVisibility(r1)
            android.widget.ImageView r6 = r9.f
            r6.setImageDrawable(r0)
            goto L_0x0098
        L_0x0088:
            android.widget.ImageView r6 = r9.g
            r6.setVisibility(r1)
            android.widget.ImageView r6 = r9.g
            r6.setImageDrawable(r0)
            goto L_0x0098
        L_0x0093:
            android.view.ViewGroup r0 = r9.i
            r0.setVisibility(r5)
        L_0x0098:
            boolean r0 = r9.p
            if (r0 == 0) goto L_0x00a8
            android.view.View r0 = r9.a
            boolean r6 = r0 instanceof com.android.car.ui.SecureView
            if (r6 == 0) goto L_0x00c3
            com.android.car.ui.SecureView r0 = (com.android.car.ui.SecureView) r0
            r0.a()
            goto L_0x00ca
        L_0x00a8:
            android.view.View r0 = r9.b
            boolean r6 = r0 instanceof com.android.car.ui.SecureView
            if (r6 == 0) goto L_0x00b5
            com.android.car.ui.SecureView r0 = (com.android.car.ui.SecureView) r0
            r0.a()
            r0 = r1
            goto L_0x00b6
        L_0x00b5:
            r0 = r4
        L_0x00b6:
            android.view.View r6 = r9.c
            boolean r7 = r6 instanceof com.android.car.ui.SecureView
            if (r7 == 0) goto L_0x00c3
            com.android.car.ui.SecureView r6 = (com.android.car.ui.SecureView) r6
            r6.a()
            if (r0 == 0) goto L_0x00ca
        L_0x00c3:
            java.lang.String r0 = "carui"
            java.lang.String r6 = "List item doesn't have a SecureView, but security was requested!"
            android.util.Log.w(r0, r6)
        L_0x00ca:
            android.view.View r0 = r9.k
            r0.setVisibility(r5)
            android.widget.Switch r0 = r9.l
            r0.setVisibility(r5)
            android.widget.CheckBox r0 = r9.m
            r0.setVisibility(r5)
            android.widget.RadioButton r0 = r9.n
            r0.setVisibility(r5)
            android.widget.ImageView r0 = r9.o
            r0.setVisibility(r5)
            ekf r0 = r10.i
            bkx r6 = r10.c
            int r6 = r6.ordinal()
            if (r6 == 0) goto L_0x018a
            if (r6 == r4) goto L_0x0184
            if (r6 == r3) goto L_0x017e
            r3 = 3
            if (r6 == r3) goto L_0x0178
            r3 = 4
            if (r6 == r3) goto L_0x0144
            r3 = 5
            if (r6 != r3) goto L_0x013c
            if (r0 == 0) goto L_0x00fd
            goto L_0x00fe
        L_0x00fd:
            r4 = r1
        L_0x00fe:
            android.widget.ImageView r3 = r9.o
            r3.setVisibility(r1)
            android.widget.ImageView r3 = r9.o
            android.view.View r6 = r9.itemView
            android.content.Context r6 = r6.getContext()
            r7 = 2131230940(0x7f0800dc, float:1.8077947E38)
            android.graphics.drawable.Drawable r6 = r6.getDrawable(r7)
            r3.setImageDrawable(r6)
            android.view.ViewGroup r3 = r9.j
            r3.setVisibility(r1)
            android.view.View r3 = r9.a
            r3.setVisibility(r1)
            android.view.View r1 = r9.a
            gp r3 = new gp
            r6 = 14
            r3.<init>(r0, r6, r2)
            r1.setOnClickListener(r3)
            android.view.View r0 = r9.a
            r0.setClickable(r4)
            android.view.View r0 = r9.b
            r0.setVisibility(r5)
            android.view.View r0 = r9.c
            r0.setVisibility(r5)
            goto L_0x01b3
        L_0x013c:
            java.lang.IllegalStateException r9 = new java.lang.IllegalStateException
            java.lang.String r10 = "Unknown secondary action type."
            r9.<init>(r10)
            throw r9
        L_0x0144:
            if (r0 == 0) goto L_0x0147
            goto L_0x0148
        L_0x0147:
            r4 = r1
        L_0x0148:
            android.widget.ImageView r3 = r9.o
            r3.setVisibility(r1)
            android.widget.ImageView r3 = r9.o
            r3.setImageDrawable(r2)
            android.view.ViewGroup r3 = r9.j
            r3.setVisibility(r1)
            android.view.View r3 = r9.a
            r3.setVisibility(r1)
            android.view.View r1 = r9.a
            gp r3 = new gp
            r6 = 15
            r3.<init>(r0, r6, r2)
            r1.setOnClickListener(r3)
            android.view.View r0 = r9.a
            r0.setClickable(r4)
            android.view.View r0 = r9.b
            r0.setVisibility(r5)
            android.view.View r0 = r9.c
            r0.setVisibility(r5)
            goto L_0x01b3
        L_0x0178:
            android.widget.RadioButton r1 = r9.n
            r9.b(r10, r1, r0)
            goto L_0x01b3
        L_0x017e:
            android.widget.CheckBox r1 = r9.m
            r9.b(r10, r1, r0)
            goto L_0x01b3
        L_0x0184:
            android.widget.Switch r1 = r9.l
            r9.b(r10, r1, r0)
            goto L_0x01b3
        L_0x018a:
            if (r0 == 0) goto L_0x018d
            goto L_0x018e
        L_0x018d:
            r4 = r1
        L_0x018e:
            android.view.ViewGroup r3 = r9.j
            r3.setVisibility(r5)
            android.view.View r3 = r9.a
            r3.setVisibility(r1)
            android.view.View r1 = r9.a
            gp r3 = new gp
            r6 = 13
            r3.<init>(r0, r6, r2)
            r1.setOnClickListener(r3)
            android.view.View r0 = r9.a
            r0.setClickable(r4)
            android.view.View r0 = r9.b
            r0.setVisibility(r5)
            android.view.View r0 = r9.c
            r0.setVisibility(r5)
        L_0x01b3:
            android.view.View r0 = r9.itemView
            boolean r1 = r10.g
            r0.setActivated(r1)
            android.view.View r0 = r9.itemView
            boolean r10 = r10.f
            r9.a(r0, r10)
            return
        L_0x01c2:
            java.lang.IllegalStateException r9 = new java.lang.IllegalStateException
            java.lang.String r10 = "Expected item to be bound to viewHolder to be instance of CarUiContentListItem."
            r9.<init>(r10)
            throw r9
        L_0x01ca:
            java.lang.IllegalStateException r9 = new java.lang.IllegalStateException
            r9.<init>(r1)
            throw r9
        L_0x01d0:
            boolean r0 = r9 instanceof defpackage.blg
            if (r0 == 0) goto L_0x01ef
            java.util.List r0 = r8.b
            java.lang.Object r10 = r0.get(r10)
            blf r10 = (defpackage.blf) r10
            boolean r0 = r10 instanceof defpackage.blc
            if (r0 != 0) goto L_0x01e8
            java.lang.IllegalStateException r9 = new java.lang.IllegalStateException
            java.lang.String r10 = "Expected item to be bound to viewHolder to be instance of CarUiHeaderListItem."
            r9.<init>(r10)
            throw r9
        L_0x01e8:
            blg r9 = (defpackage.blg) r9
            blc r10 = (defpackage.blc) r10
            android.widget.TextView r9 = r9.a
            throw r2
        L_0x01ef:
            java.lang.IllegalStateException r9 = new java.lang.IllegalStateException
            r9.<init>(r1)
            throw r9
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bli.onBindViewHolder(ld, int):void");
    }

    public final ld onCreateViewHolder(ViewGroup viewGroup, int i) {
        LayoutInflater from = LayoutInflater.from(viewGroup.getContext());
        boolean z = false;
        if (i == 0) {
            return new blg(from.inflate(R.layout.car_ui_header_list_item, viewGroup, false));
        }
        if (i == 1 || i == 2) {
            View inflate = from.inflate(R.layout.car_ui_list_item_2, viewGroup, false);
            if (i == 1) {
                z = true;
            }
            return new blh(inflate, z);
        }
        throw new IllegalStateException("Unknown item type.");
    }
}
