package defpackage;

import android.view.View;
import com.google.android.libraries.speech.transcription.voiceime.BackspaceView;

/* renamed from: bre  reason: default package */
/* compiled from: PG */
public final class bre implements fjb, fjc, iio {
    private final View a;
    private final brc b;
    private final bqz c;

    public bre() {
        throw null;
    }

    public final fja a() {
        fka fka = (fka) this.b.L.b();
        View view = this.a;
        if (view instanceof BackspaceView) {
            BackspaceView backspaceView = (BackspaceView) view;
            hzz.u(backspaceView);
            bqz bqz = this.c;
            gfq gfq = (gfq) bqz.f.b.b();
            return new fja(fka, backspaceView, (gfq) ((grm) grh.h((gfq) bqz.d.b())).a, (gnk) this.b.g.b());
        }
        throw new IllegalStateException(a.ay(view, fja.class, "Attempt to inject a ActivityAgnosticView wrapper of type "));
    }

    public bre(brc brc, bqz bqz, View view) {
        this.b = brc;
        this.c = bqz;
        this.a = view;
    }

    public final void b() {
    }
}
