package defpackage;

/* renamed from: elt  reason: default package */
/* compiled from: PG */
final class elt extends jmi implements jne {
    int a;
    final /* synthetic */ elx b;
    final /* synthetic */ ehg c;
    final /* synthetic */ long d;
    final /* synthetic */ ejn e;
    final /* synthetic */ dze f;
    final /* synthetic */ dyy g;
    private /* synthetic */ Object h;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public elt(elx elx, ehg ehg, long j, ejn ejn, dze dze, dyy dyy, jlr jlr) {
        super(2, jlr);
        this.b = elx;
        this.c = ehg;
        this.d = j;
        this.e = ejn;
        this.f = dze;
        this.g = dyy;
    }

    public final /* bridge */ /* synthetic */ Object b(Object obj, Object obj2) {
        return ((elt) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:12:0x0034, code lost:
        if (r13 != r0) goto L_0x0036;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:26:0x00b8, code lost:
        if (r13 == r0) goto L_0x00ba;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:27:0x00ba, code lost:
        return r0;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r13) {
        /*
            r12 = this;
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r1 = r12.a
            r2 = 0
            r3 = 1
            if (r1 == 0) goto L_0x001a
            if (r1 == r3) goto L_0x0012
            defpackage.jji.c(r13)     // Catch:{ all -> 0x000f }
            goto L_0x00bb
        L_0x000f:
            r13 = move-exception
            goto L_0x00be
        L_0x0012:
            java.lang.Object r1 = r12.h
            jqs r1 = (defpackage.jqs) r1
            defpackage.jji.c(r13)     // Catch:{ all -> 0x0039 }
            goto L_0x0036
        L_0x001a:
            defpackage.jji.c(r13)
            java.lang.Object r13 = r12.h
            jqs r13 = (defpackage.jqs) r13
            dyy r1 = r12.g
            hme r1 = r1.c()     // Catch:{ all -> 0x0039 }
            java.lang.String r4 = "getStartListeningResult(...)"
            defpackage.jnu.d(r1, r4)     // Catch:{ all -> 0x0039 }
            r12.h = r13     // Catch:{ all -> 0x0039 }
            r12.a = r3     // Catch:{ all -> 0x0039 }
            java.lang.Object r13 = defpackage.jqw.x(r1, r12)     // Catch:{ all -> 0x0039 }
            if (r13 == r0) goto L_0x00ba
        L_0x0036:
            dyw r13 = (defpackage.dyw) r13     // Catch:{ all -> 0x0039 }
            goto L_0x003e
        L_0x0039:
            r13 = move-exception
            java.lang.Object r13 = defpackage.jji.b(r13)
        L_0x003e:
            eaj r1 = defpackage.eaj.UNKNOWN_OPENING_FAILURE
            dyw r1 = defpackage.ejw.a(r1)
            boolean r4 = r13 instanceof defpackage.jjt
            if (r3 != r4) goto L_0x0049
            r13 = r1
        L_0x0049:
            elx r1 = r12.b
            ehg r4 = r12.c
            long r5 = r12.d
            dyw r13 = (defpackage.dyw) r13
            java.lang.Object r7 = r13.f()
            java.lang.String r8 = "<get-startListeningStatus>(...)"
            defpackage.jnu.d(r7, r8)
            ejn r8 = r12.e
            hca r9 = defpackage.evd.a
            ejn r9 = r12.e
            dze r10 = r12.f
            ebh r9 = defpackage.evd.b(r9, r10)
            java.lang.String r10 = "clientInfo"
            defpackage.jnu.e(r4, r10)
            java.lang.String r10 = "status"
            defpackage.jnu.e(r7, r10)
            dxm r10 = defpackage.dwt.f
            java.lang.String r11 = "AUDIO_START_LISTENING_DONE"
            defpackage.jnu.d(r10, r11)
            dzh r7 = (defpackage.dzh) r7
            eak r7 = r7.b
            if (r7 != 0) goto L_0x007f
            eak r7 = defpackage.eak.c
        L_0x007f:
            dzq r8 = r8.b
            java.lang.String r11 = "getAudioSourceOpeningStatus(...)"
            defpackage.jnu.d(r7, r11)
            dxh r7 = defpackage.elx.C(r10, r7)
            defpackage.elx.F(r7, r5)
            gnk r5 = defpackage.elx.a
            jyh r6 = defpackage.jyh.AUDIO_REQUEST
            jyl r6 = defpackage.elx.z(r4, r6, r8, r9)
            r7.g(r5, r6)
            r1.x(r7, r4)
            grh r1 = r13.c()
            boolean r1 = r1.f()
            if (r1 == 0) goto L_0x00d3
            grh r13 = r13.c()     // Catch:{ all -> 0x000f }
            java.lang.Object r13 = r13.b()     // Catch:{ all -> 0x000f }
            hme r13 = (defpackage.hme) r13     // Catch:{ all -> 0x000f }
            r12.h = r2     // Catch:{ all -> 0x000f }
            r1 = 2
            r12.a = r1     // Catch:{ all -> 0x000f }
            java.lang.Object r13 = defpackage.jqw.x(r13, r12)     // Catch:{ all -> 0x000f }
            if (r13 != r0) goto L_0x00bb
        L_0x00ba:
            return r0
        L_0x00bb:
            dyv r13 = (defpackage.dyv) r13     // Catch:{ all -> 0x000f }
            goto L_0x00c2
        L_0x00be:
            java.lang.Object r13 = defpackage.jji.b(r13)
        L_0x00c2:
            boolean r0 = r13 instanceof defpackage.jjt
            if (r3 != r0) goto L_0x00c7
            goto L_0x00c8
        L_0x00c7:
            r2 = r13
        L_0x00c8:
            dyv r2 = (defpackage.dyv) r2
            elx r13 = r12.b
            ehg r0 = r12.c
            long r1 = r12.d
            r13.w(r0, r1)
        L_0x00d3:
            jkd r13 = defpackage.jkd.a
            return r13
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.elt.bk(java.lang.Object):java.lang.Object");
    }

    public final jlr c(Object obj, jlr jlr) {
        elt elt = new elt(this.b, this.c, this.d, this.e, this.f, this.g, jlr);
        elt.h = obj;
        return elt;
    }
}
