package com.google.speech.recognizer;

import android.util.Log;

import com.google.protobuf.Any;
import com.google.protobuf.InvalidProtocolBufferException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.ByteBuffer;
import java.nio.charset.Charset;
import java.util.logging.Logger;

public class AbstractRecognizer {

    private static final Logger logger = Logger.getLogger(AbstractRecognizer.class.getName());
    static public String TAG = "AbstractRecognizer";

    public InputStream reader;
    private long nativeObj;

    // 添加结果回调支持
    protected RecognitionResultCallback recognitionCallback;

    private static FileOutputStream lhandleEndpointerEvent;
    private static FileOutputStream lhandleRecognitionEvent;

    private native int nativeCancel(final long nativeObj);
    private native long nativeConstruct();
    private native void nativeDelete(final long nativeObj);
    private native int nativeInitFromProto(final long nativeObj, final long resourceNativeObj, final byte[] config);
    private native byte[] nativeRun(final long nativeObj, final byte[] params);
    private native void nativeInit(final long nativeObj);
    private native boolean nativeAddAudio(final long nativeObj, final ByteBuffer audioBuffer, final int length);

    public AbstractRecognizer() {
        this.nativeObj = this.nativeConstruct();
    }

    /**
     * 设置识别结果回调
     * @param callback 回调接口
     */
    public void setRecognitionCallback(RecognitionResultCallback callback) {
        this.recognitionCallback = callback;
    }

    /**
     * 获取 native 对象指针（用于 nativeAddAudio）
     * @return native 对象指针
     */
    public long getNativeObj() {
        return this.nativeObj;
    }

    public static String print(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        sb.append("[ ");
        for (byte b : bytes) {
            sb.append(String.format("0x%02X ", b));
        }
        sb.append("]");
        return sb.toString();
    }

    private final void validate() {
        if (this.nativeObj != 0L) {
            return;
        }
        throw new IllegalStateException("recognizer is not initialized");
    }

    public final byte[] run(final byte[] params) {
        this.validate();
        final byte[] nativeRun = this.nativeRun(this.nativeObj, params);

        Log.d(TAG + " - nativeRun", bytesToHex(nativeRun));
        Log.d(TAG, new String(nativeRun, Charset.forName("UTF-8")));

        // 直接在这里处理返回的数据，因为handleRecognitionEvent可能没有被调用
        if (nativeRun != null && nativeRun.length > 0) {
            Log.d(TAG, "Processing nativeRun result with SODA parser, length: " + nativeRun.length);

            // 使用我们的SODA响应解析器
            if (recognitionCallback != null) {
                // 使用SodaCleanParser替代已删除的SodaResponseParser
                SodaCleanParser.parseSodaResponse(nativeRun, recognitionCallback);
            } else {
                // 如果没有设置回调，使用默认的日志输出
                SodaCleanParser.parseSodaResponse(nativeRun, new DefaultRecognitionCallback());
            }
        }

        return nativeRun;
    }

    public final int init(final byte[] array, final ResourceManager resourceManager) {
        this.validate();
        return this.nativeInitFromProto(
                this.nativeObj,
                resourceManager.nativeObj,
                array
        );
    }

    public final void delete() {
        synchronized (this) {
            if (this.nativeObj != 0L) {
                this.nativeDelete(this.nativeObj);
                this.nativeObj = 0L;
            }
        }
    }

    public final int cancel() {
        this.validate();
        return this.nativeCancel(this.nativeObj);
    }

    @Override
    protected void finalize() {
        this.delete();
    }

    protected void handleAudioLevelEvent(final byte[] array) {
        // 注意：由于使用protobuf-javalite，UnknownFieldSet不可用
        // 这里只做简单的日志记录
        Log.d(TAG, "AudioLevelEvent received, length: " + array.length);

        // 可以尝试使用SodaCleanParser解析
        try {
            SodaCleanParser.parseSodaResponse(array, new RecognitionResultCallback() {
                @Override
                public void onFinalResult(String hypothesis, String endpointReason) {}
                @Override
                public void onPartialResult(String hypothesis) {}
                @Override
                public void onEndpointEvent(String endpointType) {}
                @Override
                public void onAudioLevelInfo(float rms, float audioLevel) {
                    Log.d(TAG, "Audio level - RMS: " + rms + ", Level: " + audioLevel);
                }
                @Override
                public void onLanguageIdEvent(String language, int confidenceLevel) {}
                @Override
                public void onParseError(String error, byte[] rawData) {
                    Log.d(TAG, "AudioLevelEvent parse error: " + error);
                }
            });
        } catch (Exception e) {
            Log.d(TAG, "AudioLevelEvent processing error: " + e.getMessage());
        }
    }

    protected void handleEndpointerEvent(final byte[] array) {
        //Log.d(TAG, bytesToHex(array));

        // 只有在 FileOutputStream 不为空时才写入
        try {
            if (lhandleEndpointerEvent != null) {
                lhandleEndpointerEvent.write("-next-".getBytes());
                lhandleEndpointerEvent.write(array);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 注意：由于使用protobuf-javalite，UnknownFieldSet不可用
        Log.d(TAG, "EndpointerEvent received, length: " + array.length);

        try {
            Any any = Any.parseFrom(array);
            Log.d(TAG, "EndpointerEvent Any type: " + any.getTypeUrl());
        } catch (InvalidProtocolBufferException e) {
            Log.d(TAG, "EndpointerEvent Any parse error: " + e.getMessage());
        }

        // 尝试使用SodaCleanParser解析
        try {
            SodaCleanParser.parseSodaResponse(array, new RecognitionResultCallback() {
                @Override
                public void onFinalResult(String hypothesis, String endpointReason) {}
                @Override
                public void onPartialResult(String hypothesis) {}
                @Override
                public void onEndpointEvent(String endpointType) {
                    Log.d(TAG, "Endpoint event: " + endpointType);
                }
                @Override
                public void onAudioLevelInfo(float rms, float audioLevel) {}
                @Override
                public void onLanguageIdEvent(String language, int confidenceLevel) {}
                @Override
                public void onParseError(String error, byte[] rawData) {
                    Log.d(TAG, "EndpointerEvent parse error: " + error);
                }
            });
        } catch (Exception e) {
            Log.d(TAG, "EndpointerEvent processing error: " + e.getMessage());
        }
    }

    protected void handleHotwordEvent(final byte[] array) {
        // 注意：由于使用protobuf-javalite，UnknownFieldSet不可用
        Log.d(TAG, "HotwordEvent received, length: " + array.length);

        // 简单的十六进制转储用于调试
        if (array.length > 0) {
            StringBuilder hex = new StringBuilder();
            for (int i = 0; i < Math.min(array.length, 50); i++) {
                hex.append(String.format("%02X ", array[i]));
            }
            Log.d(TAG, "HotwordEvent hex: " + hex.toString());
        }
    }

    protected void handleRecognitionEvent(final byte[] array) {
        Log.d(TAG + " - handleRecognitionEvent chris  ", bytesToHex(array));

        // 保存原始数据到文件（如果配置了）
        try {
            if (lhandleRecognitionEvent != null) {
                lhandleRecognitionEvent.write("-next-".getBytes());
                lhandleRecognitionEvent.write(array);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 使用新的SODA响应解析器（基于Python版本逻辑）
        Log.v(TAG, "Processing recognition event, data length: " + array.length);
        if (recognitionCallback != null) {
            // 使用SodaCleanParser替代已删除的SodaResponseParser
            SodaCleanParser.parseSodaResponse(array, recognitionCallback);
        } else {
            // 如果没有设置回调，使用默认的日志输出
            SodaCleanParser.parseSodaResponse(array, new DefaultRecognitionCallback());
        }

        // 注意：由于使用protobuf-javalite，UnknownFieldSet不可用
        // 原有的调试功能已被移除

        // 额外尝试直接 UTF-8 解码
        String utf8Decoded = new String(array, Charset.forName("UTF-8"));
        //Log.d(TAG, "UTF8 decode fallback: " + utf8Decoded);
    }

    /**
     * 注意：由于使用protobuf-javalite，UnknownFieldSet不可用
     * 这个方法已被禁用，保留只是为了编译兼容性
     */
    private void dumpUnknownFields(String label, Object set) {
        // 这个方法不再使用，因为protobuf-javalite不支持UnknownFieldSet
        Log.d(TAG, label + " - UnknownFieldSet dump not available in protobuf-javalite");
    }

    public static String bytesToHex(byte[] in) {
        final StringBuilder builder = new StringBuilder();
        for (byte b : in) {
            builder.append(String.format("%02x ", b));
        }
        return builder.toString();
    }

    /**
     * 默认的识别结果回调实现，输出到日志
     */
    private static class DefaultRecognitionCallback implements RecognitionResultCallback {
        @Override
        public void onFinalResult(String hypothesis, String endpointReason) {
            Log.i(TAG, String.format("[FINAL] %s (reason: %s)", hypothesis, endpointReason));
        }

        @Override
        public void onPartialResult(String hypothesis) {
            Log.d(TAG, String.format("[PARTIAL] %s", hypothesis));
        }

        @Override
        public void onEndpointEvent(String endpointType) {
            Log.d(TAG, String.format("[ENDPOINT] %s", endpointType));
        }

        @Override
        public void onAudioLevelInfo(float rms, float audioLevel) {
            Log.v(TAG, String.format("[AUDIO] RMS: %.3f, Level: %.3f", rms, audioLevel));
        }

        @Override
        public void onLanguageIdEvent(String language, int confidenceLevel) {
            Log.d(TAG, String.format("[LANGID] %s (confidence: %d)", language, confidenceLevel));
        }

        @Override
        public void onParseError(String error, byte[] rawData) {
            Log.e(TAG, String.format("[ERROR] %s (data length: %d)", error, rawData != null ? rawData.length : 0));
        }
    }

    protected int read(final byte[] buffer) {
        if (buffer.length > 0) {
            int bytesRead = 0;
            try {
                bytesRead = this.reader.read(buffer);
            } catch (IOException e) {
                e.printStackTrace();
            }
            return bytesRead == -1 ? 0 : bytesRead;
        }
        return -1;
    }

    protected void setAudioReader(InputStream audioStream) {
        reader = audioStream;
    }

    public void setLogFile(File sdcard) {
        try {
            //lhandleEndpointerEvent = new FileOutputStream(new File(sdcard.getAbsolutePath() + String.format("/Android/asr/endpointer_%d.bin", this.nativeObj)));
            //lhandleRecognitionEvent = new FileOutputStream(new File(sdcard.getAbsolutePath() + String.format("/Android/asr/recognition_%d.bin", this.nativeObj)));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
