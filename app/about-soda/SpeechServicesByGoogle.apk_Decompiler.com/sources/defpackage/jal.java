package defpackage;

import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/* renamed from: jal  reason: default package */
/* compiled from: PG */
public final class jal {
    private static final long a = TimeUnit.SECONDS.toNanos(1);

    public static Boolean a(Map map, String str) {
        if (!map.containsKey(str)) {
            return null;
        }
        Object obj = map.get(str);
        if (obj instanceof Boolean) {
            return (Boolean) obj;
        }
        throw new ClassCastException(String.format("value '%s' for key '%s' in '%s' is not Boolean", new Object[]{obj, str, map}));
    }

    public static Double b(Map map, String str) {
        if (!map.containsKey(str)) {
            return null;
        }
        Object obj = map.get(str);
        if (obj instanceof Double) {
            return (Double) obj;
        }
        if (obj instanceof String) {
            try {
                return Double.valueOf(Double.parseDouble((String) obj));
            } catch (NumberFormatException unused) {
                throw new IllegalArgumentException(String.format("value '%s' for key '%s' is not a double", new Object[]{obj, str}));
            }
        } else {
            throw new IllegalArgumentException(String.format("value '%s' for key '%s' in '%s' is not a number", new Object[]{obj, str, map}));
        }
    }

    public static Integer c(Map map, String str) {
        if (!map.containsKey(str)) {
            return null;
        }
        Object obj = map.get(str);
        if (obj instanceof Double) {
            Double d = (Double) obj;
            int intValue = d.intValue();
            if (((double) intValue) == d.doubleValue()) {
                return Integer.valueOf(intValue);
            }
            Objects.toString(d);
            throw new ClassCastException("Number expected to be integer: ".concat(String.valueOf(d)));
        } else if (obj instanceof String) {
            try {
                return Integer.valueOf(Integer.parseInt((String) obj));
            } catch (NumberFormatException unused) {
                throw new IllegalArgumentException(String.format("value '%s' for key '%s' is not an integer", new Object[]{obj, str}));
            }
        } else {
            throw new IllegalArgumentException(String.format("value '%s' for key '%s' is not an integer", new Object[]{obj, str}));
        }
    }

    /* JADX WARNING: Can't wrap try/catch for region: R(3:82|83|84) */
    /* JADX WARNING: Code restructure failed: missing block: B:84:0x0135, code lost:
        throw new java.text.ParseException("Duration value is out of range.", 0);
     */
    /* JADX WARNING: Missing exception handler attribute for start block: B:82:0x012e */
    /* JADX WARNING: Unknown top exception splitter block from list: {B:76:0x010e=Splitter:B:76:0x010e, B:82:0x012e=Splitter:B:82:0x012e} */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static java.lang.Long d(java.util.Map r14, java.lang.String r15) {
        /*
            java.lang.String r14 = e(r14, r15)
            if (r14 != 0) goto L_0x0008
            r14 = 0
            return r14
        L_0x0008:
            boolean r15 = r14.isEmpty()     // Catch:{ ParseException -> 0x014e }
            java.lang.String r0 = "Invalid duration string: "
            r1 = 0
            if (r15 != 0) goto L_0x0144
            int r15 = r14.length()     // Catch:{ ParseException -> 0x014e }
            r2 = -1
            int r15 = r15 + r2
            char r15 = r14.charAt(r15)     // Catch:{ ParseException -> 0x014e }
            r3 = 115(0x73, float:1.61E-43)
            if (r15 != r3) goto L_0x0144
            char r15 = r14.charAt(r1)     // Catch:{ ParseException -> 0x014e }
            r3 = 45
            r4 = 1
            if (r15 != r3) goto L_0x002e
            java.lang.String r14 = r14.substring(r4)     // Catch:{ ParseException -> 0x014e }
            r15 = r4
            goto L_0x002f
        L_0x002e:
            r15 = r1
        L_0x002f:
            int r3 = r14.length()     // Catch:{ ParseException -> 0x014e }
            int r3 = r3 + r2
            java.lang.String r3 = r14.substring(r1, r3)     // Catch:{ ParseException -> 0x014e }
            java.lang.String r5 = ""
            r6 = 46
            int r6 = r3.indexOf(r6)     // Catch:{ ParseException -> 0x014e }
            if (r6 == r2) goto L_0x004c
            int r2 = r6 + 1
            java.lang.String r5 = r3.substring(r2)     // Catch:{ ParseException -> 0x014e }
            java.lang.String r3 = r3.substring(r1, r6)     // Catch:{ ParseException -> 0x014e }
        L_0x004c:
            long r2 = java.lang.Long.parseLong(r3)     // Catch:{ ParseException -> 0x014e }
            boolean r6 = r5.isEmpty()     // Catch:{ ParseException -> 0x014e }
            if (r6 == 0) goto L_0x0058
            r7 = r1
            goto L_0x0089
        L_0x0058:
            r6 = r1
            r7 = r6
        L_0x005a:
            r8 = 9
            if (r6 >= r8) goto L_0x0089
            int r7 = r7 * 10
            int r8 = r5.length()     // Catch:{ ParseException -> 0x014e }
            if (r6 >= r8) goto L_0x0086
            char r8 = r5.charAt(r6)     // Catch:{ ParseException -> 0x014e }
            r9 = 48
            if (r8 < r9) goto L_0x007e
            char r8 = r5.charAt(r6)     // Catch:{ ParseException -> 0x014e }
            r9 = 57
            if (r8 > r9) goto L_0x007e
            char r8 = r5.charAt(r6)     // Catch:{ ParseException -> 0x014e }
            int r8 = r8 + -48
            int r7 = r7 + r8
            goto L_0x0086
        L_0x007e:
            java.text.ParseException r14 = new java.text.ParseException     // Catch:{ ParseException -> 0x014e }
            java.lang.String r15 = "Invalid nanoseconds."
            r14.<init>(r15, r1)     // Catch:{ ParseException -> 0x014e }
            throw r14     // Catch:{ ParseException -> 0x014e }
        L_0x0086:
            int r6 = r6 + 1
            goto L_0x005a
        L_0x0089:
            r5 = 0
            int r8 = (r2 > r5 ? 1 : (r2 == r5 ? 0 : -1))
            if (r8 < 0) goto L_0x0136
            if (r15 == 0) goto L_0x0093
            long r2 = -r2
            int r7 = -r7
        L_0x0093:
            long r14 = (long) r7
            long r8 = a     // Catch:{ IllegalArgumentException -> 0x012e }
            long r10 = -r8
            int r0 = (r14 > r10 ? 1 : (r14 == r10 ? 0 : -1))
            if (r0 <= 0) goto L_0x009f
            int r0 = (r14 > r8 ? 1 : (r14 == r8 ? 0 : -1))
            if (r0 < 0) goto L_0x00a7
        L_0x009f:
            long r10 = r14 / r8
            long r2 = defpackage.hfc.an(r2, r10)     // Catch:{ IllegalArgumentException -> 0x012e }
            long r14 = r14 % r8
            int r7 = (int) r14     // Catch:{ IllegalArgumentException -> 0x012e }
        L_0x00a7:
            int r14 = (r2 > r5 ? 1 : (r2 == r5 ? 0 : -1))
            if (r14 <= 0) goto L_0x00b3
            if (r7 >= 0) goto L_0x00b3
            long r14 = (long) r7     // Catch:{ IllegalArgumentException -> 0x012e }
            long r14 = r14 + r8
            r10 = -1
            long r2 = r2 + r10
            int r7 = (int) r14     // Catch:{ IllegalArgumentException -> 0x012e }
        L_0x00b3:
            int r14 = (r2 > r5 ? 1 : (r2 == r5 ? 0 : -1))
            r10 = 1
            if (r14 >= 0) goto L_0x00bf
            if (r7 <= 0) goto L_0x00bf
            long r14 = (long) r7     // Catch:{ IllegalArgumentException -> 0x012e }
            long r14 = r14 - r8
            long r2 = r2 + r10
            int r7 = (int) r14     // Catch:{ IllegalArgumentException -> 0x012e }
        L_0x00bf:
            r14 = -315576000000(0xffffffb686346200, double:NaN)
            int r14 = (r2 > r14 ? 1 : (r2 == r14 ? 0 : -1))
            if (r14 < 0) goto L_0x0113
            r14 = 315576000000(0x4979cb9e00, double:1.55915260252E-312)
            int r14 = (r2 > r14 ? 1 : (r2 == r14 ? 0 : -1))
            if (r14 > 0) goto L_0x0113
            long r14 = (long) r7     // Catch:{ IllegalArgumentException -> 0x012e }
            r12 = -999999999(0xffffffffc4653601, double:NaN)
            int r0 = (r14 > r12 ? 1 : (r14 == r12 ? 0 : -1))
            if (r0 < 0) goto L_0x0113
            int r0 = (r14 > r8 ? 1 : (r14 == r8 ? 0 : -1))
            if (r0 >= 0) goto L_0x0113
            int r0 = (r2 > r5 ? 1 : (r2 == r5 ? 0 : -1))
            if (r0 < 0) goto L_0x00e3
            if (r7 >= 0) goto L_0x00e7
        L_0x00e3:
            if (r0 > 0) goto L_0x0113
            if (r7 > 0) goto L_0x0113
        L_0x00e7:
            java.util.concurrent.TimeUnit r0 = java.util.concurrent.TimeUnit.SECONDS     // Catch:{ IllegalArgumentException -> 0x012e }
            long r2 = r0.toNanos(r2)     // Catch:{ IllegalArgumentException -> 0x012e }
            long r7 = r2 + r14
            long r14 = r14 ^ r2
            long r2 = r2 ^ r7
            int r0 = (r2 > r5 ? 1 : (r2 == r5 ? 0 : -1))
            if (r0 < 0) goto L_0x00f7
            r0 = r4
            goto L_0x00f8
        L_0x00f7:
            r0 = r1
        L_0x00f8:
            int r14 = (r14 > r5 ? 1 : (r14 == r5 ? 0 : -1))
            if (r14 >= 0) goto L_0x00fd
            r1 = r4
        L_0x00fd:
            r14 = r1 | r0
            if (r14 == 0) goto L_0x0102
            goto L_0x010e
        L_0x0102:
            r14 = 63
            long r14 = r7 >>> r14
            long r14 = r14 ^ r10
            r0 = 9223372036854775807(0x7fffffffffffffff, double:NaN)
            long r7 = r14 + r0
        L_0x010e:
            java.lang.Long r14 = java.lang.Long.valueOf(r7)     // Catch:{ ParseException -> 0x014e }
            return r14
        L_0x0113:
            java.lang.IllegalArgumentException r14 = new java.lang.IllegalArgumentException     // Catch:{ IllegalArgumentException -> 0x012e }
            java.lang.String r15 = "Duration is not valid. See proto definition for valid values. Seconds (%s) must be in range [-315,576,000,000, +315,576,000,000]. Nanos (%s) must be in range [-999,999,999, +999,999,999]. Nanos must have the same sign as seconds"
            java.lang.Long r0 = java.lang.Long.valueOf(r2)     // Catch:{ IllegalArgumentException -> 0x012e }
            java.lang.Integer r2 = java.lang.Integer.valueOf(r7)     // Catch:{ IllegalArgumentException -> 0x012e }
            r3 = 2
            java.lang.Object[] r3 = new java.lang.Object[r3]     // Catch:{ IllegalArgumentException -> 0x012e }
            r3[r1] = r0     // Catch:{ IllegalArgumentException -> 0x012e }
            r3[r4] = r2     // Catch:{ IllegalArgumentException -> 0x012e }
            java.lang.String r15 = java.lang.String.format(r15, r3)     // Catch:{ IllegalArgumentException -> 0x012e }
            r14.<init>(r15)     // Catch:{ IllegalArgumentException -> 0x012e }
            throw r14     // Catch:{ IllegalArgumentException -> 0x012e }
        L_0x012e:
            java.text.ParseException r14 = new java.text.ParseException     // Catch:{ ParseException -> 0x014e }
            java.lang.String r15 = "Duration value is out of range."
            r14.<init>(r15, r1)     // Catch:{ ParseException -> 0x014e }
            throw r14     // Catch:{ ParseException -> 0x014e }
        L_0x0136:
            java.text.ParseException r15 = new java.text.ParseException     // Catch:{ ParseException -> 0x014e }
            java.lang.String r14 = java.lang.String.valueOf(r14)     // Catch:{ ParseException -> 0x014e }
            java.lang.String r14 = r0.concat(r14)     // Catch:{ ParseException -> 0x014e }
            r15.<init>(r14, r1)     // Catch:{ ParseException -> 0x014e }
            throw r15     // Catch:{ ParseException -> 0x014e }
        L_0x0144:
            java.text.ParseException r15 = new java.text.ParseException     // Catch:{ ParseException -> 0x014e }
            java.lang.String r14 = r0.concat(r14)     // Catch:{ ParseException -> 0x014e }
            r15.<init>(r14, r1)     // Catch:{ ParseException -> 0x014e }
            throw r15     // Catch:{ ParseException -> 0x014e }
        L_0x014e:
            r14 = move-exception
            java.lang.RuntimeException r15 = new java.lang.RuntimeException
            r15.<init>(r14)
            throw r15
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.jal.d(java.util.Map, java.lang.String):java.lang.Long");
    }

    public static String e(Map map, String str) {
        if (!map.containsKey(str)) {
            return null;
        }
        Object obj = map.get(str);
        if (obj instanceof String) {
            return (String) obj;
        }
        throw new ClassCastException(String.format("value '%s' for key '%s' in '%s' is not String", new Object[]{obj, str, map}));
    }

    public static List f(Map map, String str) {
        if (!map.containsKey(str)) {
            return null;
        }
        Object obj = map.get(str);
        if (obj instanceof List) {
            return (List) obj;
        }
        throw new ClassCastException(String.format("value '%s' for key '%s' in '%s' is not List", new Object[]{obj, str, map}));
    }

    public static List g(Map map, String str) {
        List f = f(map, str);
        if (f == null) {
            return null;
        }
        j(f);
        return f;
    }

    public static List h(Map map, String str) {
        List f = f(map, str);
        if (f == null) {
            return null;
        }
        int i = 0;
        while (i < f.size()) {
            if (f.get(i) instanceof String) {
                i++;
            } else {
                throw new ClassCastException(String.format(Locale.US, "value '%s' for idx %d in '%s' is not string", new Object[]{f.get(i), Integer.valueOf(i), f}));
            }
        }
        return f;
    }

    public static Map i(Map map, String str) {
        if (!map.containsKey(str)) {
            return null;
        }
        Object obj = map.get(str);
        if (obj instanceof Map) {
            return (Map) obj;
        }
        throw new ClassCastException(String.format("value '%s' for key '%s' in '%s' is not object", new Object[]{obj, str, map}));
    }

    public static void j(List list) {
        int i = 0;
        while (i < list.size()) {
            if (list.get(i) instanceof Map) {
                i++;
            } else {
                throw new ClassCastException(String.format(Locale.US, "value %s for idx %d in %s is not object", new Object[]{list.get(i), Integer.valueOf(i), list}));
            }
        }
    }
}
