package defpackage;

import android.content.Context;
import androidx.work.impl.background.systemalarm.RescheduleReceiver;

/* renamed from: bcw  reason: default package */
/* compiled from: PG */
final class bcw extends jmi implements jne {
    /* synthetic */ boolean a;
    final /* synthetic */ Context b;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bcw(Context context, jlr jlr) {
        super(2, jlr);
        this.b = context;
    }

    public final /* bridge */ /* synthetic */ Object b(Object obj, Object obj2) {
        Boolean bool = (Boolean) obj;
        bool.booleanValue();
        return ((bcw) c(bool, (jlr) obj2)).bk(jkd.a);
    }

    public final Object bk(Object obj) {
        jji.c(obj);
        bif.a(this.b, RescheduleReceiver.class, this.a);
        return jkd.a;
    }

    public final jlr c(Object obj, jlr jlr) {
        bcw bcw = new bcw(this.b, jlr);
        bcw.a = ((Boolean) obj).booleanValue();
        return bcw;
    }
}
