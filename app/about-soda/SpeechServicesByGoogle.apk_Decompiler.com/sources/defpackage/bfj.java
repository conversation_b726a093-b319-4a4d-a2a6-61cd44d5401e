package defpackage;

/* renamed from: bfj  reason: default package */
/* compiled from: PG */
public final class bfj extends bfd {
    static {
        bbk.b("NetworkNotRoamingCtrlr");
    }

    public bfj(bft bft) {
        super(bft);
    }

    public final boolean b(bhe bhe) {
        jnu.e(bhe, "workSpec");
        if (bhe.k.b == bbl.NOT_ROAMING) {
            return true;
        }
        return false;
    }

    public final int d() {
        return 7;
    }

    public final /* bridge */ /* synthetic */ boolean e(Object obj) {
        bex bex = (bex) obj;
        jnu.e(bex, "value");
        if (!bex.a || !bex.d) {
            return true;
        }
        return false;
    }
}
