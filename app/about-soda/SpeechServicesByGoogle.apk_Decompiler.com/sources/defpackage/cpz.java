package defpackage;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.LockSupport;

/* renamed from: cpz  reason: default package */
/* compiled from: PG */
public final class cpz extends AtomicInteger {
    public final cqg a;
    public final long b;
    public final AtomicReference c = new AtomicReference();
    private final hme d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public cpz(long j, cqg cqg, hme hme) {
        super(cqa.a(cqe.a(j), false, false));
        jnu.e(cqg, "threadIdentifier");
        this.a = cqg;
        this.d = hme;
        this.b = cqe.c(j);
    }

    public final void a() {
        int i;
        do {
            i = get();
        } while (!compareAndSet(i, cqa.e(i, 0, false, true, 3)));
        if (cqa.d(i)) {
            cqg cqg = this.a;
            if (cqg.d) {
                cqg.e = cqg.c;
            } else {
                throw new IllegalStateException("Could not have been boosted while not holding a lock.");
            }
        }
        for (cpy cpy = (cpy) this.c.getAndSet(cpx.a); cpy != null; cpy = cpy.b) {
            LockSupport.unpark(cpy.a.a);
        }
    }

    public final /* bridge */ byte byteValue() {
        throw new UnsupportedOperationException();
    }

    public final /* bridge */ short shortValue() {
        throw new UnsupportedOperationException();
    }

    public final String toString() {
        return "Booster[Thread=[" + this.a.a + ", future=[" + this.d + "]]";
    }
}
