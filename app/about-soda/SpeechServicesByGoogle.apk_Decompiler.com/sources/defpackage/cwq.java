package defpackage;

import android.content.Context;
import android.content.SharedPreferences;
import android.net.Uri;
import android.os.SystemClock;
import android.util.Log;
import androidx.wear.ambient.AmbientDelegate;
import com.google.android.libraries.performance.primes.transmitter.clearcut.ClearcutMetricSnapshotTransmitter;
import j$.util.Collection;
import j$.util.stream.Stream;
import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;

/* renamed from: cwq  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwq implements hko {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    private final /* synthetic */ int c;

    public /* synthetic */ cwq(Object obj, Object obj2, int i) {
        this.c = i;
        this.a = obj;
        this.b = obj2;
    }

    /* JADX WARNING: type inference failed for: r2v12, types: [cuk, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r15v0, types: [cuk, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v12, types: [java.lang.Object, cxa] */
    /* JADX WARNING: type inference failed for: r3v18, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v16, types: [java.lang.Object, cxa] */
    /* JADX WARNING: type inference failed for: r1v33, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r15v1, types: [cuk, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v42, types: [java.lang.Object, cxa] */
    /* JADX WARNING: type inference failed for: r2v24, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r15v2, types: [cuk, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v32, types: [cxw, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v26, types: [cxw, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v77, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v37, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v30, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v100, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme a(Object obj) {
        int i;
        int i2;
        int i3;
        csi csi;
        Object obj2;
        boolean z = true;
        switch (this.c) {
            case 0:
                ((cws) this.a).i((cwf) this.b);
                return hfc.J((Exception) obj);
            case 1:
                Void voidR = (Void) obj;
                htq htq = (htq) this.b;
                htk htk = (htk) htq.C(5);
                htk.x(htq);
                if (!htk.b.B()) {
                    htk.u();
                }
                Object obj3 = this.a;
                ctg ctg = (ctg) htk.b;
                ctg ctg2 = ctg.g;
                ctg.a |= 8;
                ctg.e = false;
                ctg ctg3 = (ctg) htk.r();
                cvy cvy = ((cwm) obj3).c;
                return cvy.q(cvy.c.g(ctg3), new bsx(cvy, (htq) ctg3, htq, 17));
            case 2:
                ((cws) this.a).i((cwf) this.b);
                return hfc.K((Boolean) obj);
            case 3:
                cyh.h("%s: Start download called on file that doesn't exist. Key = %s!", "SharedFileManager", this.b);
                ((dbw) this.a).d.a();
                kml a2 = csi.a();
                a2.b = csh.SHARED_FILE_NOT_FOUND_ERROR;
                a2.d = (cwz) obj;
                return hfc.J(a2.a());
            case 4:
                ctl ctl = (ctl) obj;
                if (ctl == null) {
                    cyh.g("%s: Unable to read sharedFile from shared preferences.", "SharedFileManager");
                    return hfc.J(new cwz());
                }
                ctf b2 = ctf.b(ctl.c);
                if (b2 == null) {
                    b2 = ctf.NONE;
                }
                if (b2 != ctf.DOWNLOAD_COMPLETE) {
                    Object obj4 = this.b;
                    Object obj5 = this.a;
                    ctj ctj = (ctj) obj4;
                    int x = a.x(ctj.e);
                    if (x == 0) {
                        i = 1;
                    } else {
                        i = x;
                    }
                    dbw dbw = (dbw) obj5;
                    Uri u = cqx.u(dbw.a, i, ctl.b, ctj.d, dbw.d, (grh) dbw.g, false);
                    if (u != null) {
                        ((cxx) dbw.h).d(ctj.d, u);
                    }
                    ctf b3 = ctf.b(ctl.c);
                    if (b3 == null) {
                        b3 = ctf.NONE;
                    }
                    if (b3 == ctf.DOWNLOAD_IN_PROGRESS) {
                        ? r5 = dbw.j;
                        htk htk2 = (htk) ctl.C(5);
                        htk2.x(ctl);
                        ctf ctf = ctf.SUBSCRIBED;
                        if (!htk2.b.B()) {
                            htk2.u();
                        }
                        ctl ctl2 = (ctl) htk2.b;
                        ctl2.c = ctf.h;
                        ctl2.a |= 2;
                        return ftd.L(r5.h(ctj, (ctl) htk2.r()), new bub(12), dbw.l);
                    }
                }
                return hma.a;
            case 5:
                if (((ctl) obj) != null) {
                    return hfc.K(true);
                }
                Object obj6 = this.b;
                dbw dbw2 = (dbw) this.a;
                SharedPreferences B = cqh.B(dbw2.a, "gms_icing_mdd_shared_file_manager_metadata", (grh) dbw2.g);
                long j = B.getLong("next_file_name_v2", System.currentTimeMillis());
                if (!B.edit().putLong("next_file_name_v2", 1 + j).commit()) {
                    cyh.h("%s: Unable to update file name %s", "SharedFileManager", obj6);
                    return hfc.K(false);
                }
                String au = a.au(j, "datadownloadfile_");
                htk l = ctl.h.l();
                ctf ctf2 = ctf.SUBSCRIBED;
                if (!l.b.B()) {
                    l.u();
                }
                htq htq2 = l.b;
                ctl ctl3 = (ctl) htq2;
                ctl3.c = ctf2.h;
                ctl3.a = 2 | ctl3.a;
                if (!htq2.B()) {
                    l.u();
                }
                ctl ctl4 = (ctl) l.b;
                ctl4.a |= 1;
                ctl4.b = au;
                return ftd.L(dbw2.j.h((ctj) obj6, (ctl) l.r()), new cwi(obj6, 15), dbw2.l);
            case 6:
                ctl ctl5 = (ctl) obj;
                Object obj7 = this.b;
                if (ctl5 == null) {
                    cyh.h("%s: No file entry with key %s", "SharedFileManager", obj7);
                    return hfc.K(false);
                }
                Object obj8 = this.a;
                ctj ctj2 = (ctj) obj7;
                int x2 = a.x(ctj2.e);
                if (x2 == 0) {
                    i2 = 1;
                } else {
                    i2 = x2;
                }
                dbw dbw3 = (dbw) obj8;
                Uri u2 = cqx.u(dbw3.a, i2, ctl5.b, ctj2.d, dbw3.d, (grh) dbw3.g, false);
                if (u2 != null) {
                    ((cxx) dbw3.h).d(ctj2.d, u2);
                }
                return ftd.L(dbw3.j.g(ctj2), new cwi(obj7, 14), dbw3.l);
            case 7:
                gxv gxv = (gxv) obj;
                gxr gxr = new gxr();
                hbp k = ((gyo) this.a).iterator();
                while (k.hasNext()) {
                    ctj ctj3 = (ctj) k.next();
                    if (!gxv.containsKey(ctj3)) {
                        cyh.h("%s: getOnDeviceUris called on file that doesn't exist. Key = %s!", "SharedFileManager", ctj3);
                        return hfc.J(new cwz());
                    }
                    Object obj9 = this.b;
                    ctl ctl6 = (ctl) gxv.get(ctj3);
                    int x3 = a.x(ctj3.e);
                    if (x3 == 0) {
                        i3 = 1;
                    } else {
                        i3 = x3;
                    }
                    dbw dbw4 = (dbw) obj9;
                    Uri u3 = cqx.u(dbw4.a, i3, ctl6.b, ctl6.f, dbw4.d, (grh) dbw4.g, ctl6.d);
                    if (u3 != null) {
                        gxr.d(ctj3, u3);
                    }
                }
                return hfc.K(gxr.a());
            case 8:
                Void voidR2 = (Void) obj;
                cxq cxq = (cxq) this.a;
                String str = cxq.d.f;
                kjd kjd = cxq.o;
                Object obj10 = this.b;
                if (!cxt.d(kjd, (Uri) obj10, str)) {
                    cyh.h("%s: Final file checksum verification failed. %s.", "DeltaFileDownloaderCallbackImpl", obj10);
                    kml a3 = csi.a();
                    a3.b = csh.FINAL_FILE_CHECKSUM_MISMATCH_ERROR;
                    return hfc.J(a3.a());
                }
                return cxs.c(ctf.DOWNLOAD_COMPLETE, cxq.d, cxq.n, cxq.b, cxq.l);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                Void voidR3 = (Void) obj;
                return this.a.a((Uri) this.b);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                Exception exc = (Exception) obj;
                if (exc instanceof csi) {
                    csi = (csi) exc;
                } else {
                    kml a4 = csi.a();
                    a4.d = exc;
                    a4.b = csh.UNKNOWN_ERROR;
                    csi = a4.a();
                }
                return ftd.L(this.b.b(csi), new cxr(exc, 2), ((cxx) this.a).f);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                Void voidR4 = (Void) obj;
                ((hmf) this.a).run();
                return this.b;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                Object obj11 = this.b;
                Object obj12 = (Set) ((Map) obj).get(obj11);
                if (obj12 == null) {
                    obj12 = hau.a;
                }
                return ((cyd) this.a).f(gxv.j(obj11, obj12));
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                boolean booleanValue = ((Boolean) obj).booleanValue();
                Object obj13 = this.a;
                bmu bmu = (bmu) this.b;
                if (booleanValue) {
                    obj2 = bmu.c;
                } else {
                    obj2 = bmu.b;
                }
                return ((cyw) obj2).g((String) obj13);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                dbl dbl = (dbl) obj;
                Object obj14 = this.a;
                long elapsedRealtime = SystemClock.elapsedRealtime();
                String str2 = ((dci) obj14).a;
                Object obj15 = this.b;
                dbw dbw5 = (dbw) obj15;
                hme P = hfc.P(new dps(dbw5, str2, elapsedRealtime, 1), dbw5.g);
                hme b4 = hfc.Z(P).b(new dbr(obj15, (Object) str2, (Object) P, 0), dbw5.g);
                if (((cxk) dbw5.j).b()) {
                    return b4;
                }
                return hfc.Z(b4).b(new dbr(obj15, P, str2, 2, (byte[]) null), dbw5.g);
            case 15:
                dea dea = (dea) obj;
                AmbientDelegate ambientDelegate = (AmbientDelegate) this.b;
                ded f = ((dgo) ambientDelegate.b).c().f();
                ddc o = f.o();
                ddc a5 = dct.a(o, f.k());
                kli kli = (kli) this.a;
                int a6 = ((dfl) kli.c).a(a5);
                if (a6 <= kli.a) {
                    ((hdc) ((hdc) dcs.c.f()).j("com/google/android/libraries/micore/superpacks/packs/FetchPipeline", "fetchOnePackMaybeRetry", 319, "FetchPipeline.java")).G("Retrying fetching pack %s after validation failure, counts: %d, maxAllowed: %d", o.e(), Integer.valueOf(a6), Integer.valueOf(kli.a));
                    ((dfl) kli.c).o(a5, dhk.INVALID_PACK, false);
                    return kli.j(ambientDelegate);
                }
                ((hdc) ((hdc) dcs.c.f()).j("com/google/android/libraries/micore/superpacks/packs/FetchPipeline", "fetchOnePackMaybeRetry", 334, "FetchPipeline.java")).x("Max validation retry count of %d met for pack %s, failing fetch", a6, o.e());
                return hfc.J(dea);
            case 16:
                dsd dsd = (dsd) obj;
                htk htk3 = (htk) dsd.C(5);
                htk3.x(dsd);
                htm htm = (htm) htk3;
                if (!htm.b.B()) {
                    htm.u();
                }
                Object obj16 = this.b;
                Object obj17 = this.a;
                dsd dsd2 = (dsd) htm.b;
                dsd dsd3 = dsd.c;
                obj17.getClass();
                dsd2.b = (kbc) obj17;
                dsd2.a |= 1;
                gef gef = (gef) obj16;
                return ((ClearcutMetricSnapshotTransmitter) gef.d).a((Context) gef.c, (dsd) htm.r());
            case 17:
                Void voidR5 = (Void) obj;
                ConcurrentMap concurrentMap = duy.a;
                gxl gxl = new gxl();
                dte dte = (dte) this.b;
                gxl.h(dte.c);
                int i4 = crj.a;
                gxl.h(crj.a(dte.c));
                gxq g = gxl.g();
                int i5 = ((hal) g).c;
                for (int i6 = 0; i6 < i5; i6++) {
                    Object obj18 = this.a;
                    File file = new File(String.valueOf(((Context) g.get(i6)).getFilesDir()) + "/phenotype/shared/" + ((String) obj18));
                    Log.i("PhenotypeAccountStore", "Removing snapshots for removed user");
                    if (file.exists()) {
                        z = duy.a(file);
                    }
                }
                if (z) {
                    return hma.a;
                }
                return hfc.J(new IOException("Unable to remove snapshots for removed user"));
            case 18:
                Stream stream = Collection.EL.stream((gyo) obj);
                Object obj19 = this.b;
                Object obj20 = this.a;
                Stream map = stream.map(new ezk(obj20, obj19, 0));
                int i7 = gxq.d;
                return ftd.aa((gxq) map.collect(gvx.a)).z(new dvq(3), ((ezl) obj20).e);
            case 19:
                Void voidR6 = (Void) obj;
                exo exo = (exo) this.b;
                String a7 = ezh.a(exo.a.toLanguageTag(), exo.b);
                cui a8 = cuj.a();
                a8.b(a7);
                cuj a9 = a8.a();
                ((hdc) ((hdc) ezo.a.f()).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackSourceImpl", "deletePackage", 341, "ZipfileLanguagePackSourceImpl.java")).u("MDD.removeFileGroup(%s)", a7);
                return ((ezo) this.a).b.g(a9);
            default:
                boolean booleanValue2 = ((Boolean) obj).booleanValue();
                Object obj21 = this.b;
                if (booleanValue2) {
                    ((hdc) ((hdc) ezo.a.f()).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackSourceImpl", "deletePackage", 348, "ZipfileLanguagePackSourceImpl.java")).u("Zipfile LP successfully removed from MDD: %s", obj21);
                } else {
                    ((hdc) ((hdc) ezo.a.h()).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackSourceImpl", "deletePackage", 350, "ZipfileLanguagePackSourceImpl.java")).u("Zipfile LP not successfully removed from MDD. Did FileGroup exist? Descriptor: %s", obj21);
                }
                ezo ezo = (ezo) this.a;
                return ezo.c.b(ezo.b);
        }
    }

    public /* synthetic */ cwq(Object obj, Object obj2, int i, byte[] bArr) {
        this.c = i;
        this.b = obj;
        this.a = obj2;
    }
}
