package defpackage;

import j$.util.function.IntPredicate$CC;
import java.util.function.IntPredicate;

/* renamed from: bog  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bog implements IntPredicate {
    public final /* synthetic */ int a;

    public /* synthetic */ bog(int i) {
        this.a = i;
    }

    public final /* synthetic */ IntPredicate and(IntPredicate intPredicate) {
        return IntPredicate$CC.$default$and(this, intPredicate);
    }

    public final /* synthetic */ IntPredicate negate() {
        return IntPredicate$CC.$default$negate(this);
    }

    public final /* synthetic */ IntPredicate or(IntPredicate intPredicate) {
        return IntPredicate$CC.$default$or(this, intPredicate);
    }

    public final boolean test(int i) {
        if (this.a == i) {
            return true;
        }
        return false;
    }
}
