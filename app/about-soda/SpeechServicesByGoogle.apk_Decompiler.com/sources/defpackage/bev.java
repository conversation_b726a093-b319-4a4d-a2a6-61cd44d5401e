package defpackage;

import android.net.NetworkRequest;

/* renamed from: bev  reason: default package */
/* compiled from: PG */
final class bev extends jmi implements jne {
    int a;
    final /* synthetic */ baq b;
    final /* synthetic */ bew c;
    private /* synthetic */ Object d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bev(baq baq, bew bew, jlr jlr) {
        super(2, jlr);
        this.b = baq;
        this.c = bew;
    }

    public final /* bridge */ /* synthetic */ Object b(Object obj, Object obj2) {
        return ((bev) c((jty) obj, (jlr) obj2)).bk(jkd.a);
    }

    public final Object bk(Object obj) {
        jlx jlx = jlx.COROUTINE_SUSPENDED;
        int i = this.a;
        jji.c(obj);
        if (i == 0) {
            jty jty = (jty) this.d;
            NetworkRequest a2 = this.b.a();
            if (a2 == null) {
                jty.n((Throwable) null);
                return jkd.a;
            }
            beu beu = new beu(job.S(jty, (jlv) null, (jqt) null, new bet(this.c, jty, (jlr) null, 0), 3), jty);
            bbk.a();
            long j = bfc.a;
            this.c.a.registerNetworkCallback(a2, beu);
            bes bes = new bes(this.c, beu, 0);
            this.a = 1;
            if (job.aa(jty, bes, this) == jlx) {
                return jlx;
            }
        }
        return jkd.a;
    }

    public final jlr c(Object obj, jlr jlr) {
        bev bev = new bev(this.b, this.c, jlr);
        bev.d = obj;
        return bev;
    }
}
