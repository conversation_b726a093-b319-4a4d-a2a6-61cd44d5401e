package defpackage;

import androidx.work.impl.WorkDatabase_Impl;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;

/* renamed from: bdk  reason: default package */
/* compiled from: PG */
public final class bdk extends aut {
    public final /* synthetic */ WorkDatabase_Impl a;

    public bdk(WorkDatabase_Impl workDatabase_Impl) {
        this.a = workDatabase_Impl;
    }

    public final void a(awl awl) {
        awl.g("CREATE TABLE IF NOT EXISTS `Dependency` (`work_spec_id` TEXT NOT NULL, `prerequisite_id` TEXT NOT NULL, PRIMARY KEY(`work_spec_id`, `prerequisite_id`), FOREIGN KEY(`work_spec_id`) REFERENCES `WorkSpec`(`id`) ON UPDATE CASCADE ON DELETE CASCADE , FOREIGN KEY(`prerequisite_id`) REFERENCES `WorkSpec`(`id`) ON UPDATE CASCADE ON DELETE CASCADE )");
        awl.g("CREATE INDEX IF NOT EXISTS `index_Dependency_work_spec_id` ON `Dependency` (`work_spec_id`)");
        awl.g("CREATE INDEX IF NOT EXISTS `index_Dependency_prerequisite_id` ON `Dependency` (`prerequisite_id`)");
        awl.g("CREATE TABLE IF NOT EXISTS `WorkSpec` (`id` TEXT NOT NULL, `state` INTEGER NOT NULL, `worker_class_name` TEXT NOT NULL, `input_merger_class_name` TEXT NOT NULL, `input` BLOB NOT NULL, `output` BLOB NOT NULL, `initial_delay` INTEGER NOT NULL, `interval_duration` INTEGER NOT NULL, `flex_duration` INTEGER NOT NULL, `run_attempt_count` INTEGER NOT NULL, `backoff_policy` INTEGER NOT NULL, `backoff_delay_duration` INTEGER NOT NULL, `last_enqueue_time` INTEGER NOT NULL DEFAULT -1, `minimum_retention_duration` INTEGER NOT NULL, `schedule_requested_at` INTEGER NOT NULL, `run_in_foreground` INTEGER NOT NULL, `out_of_quota_policy` INTEGER NOT NULL, `period_count` INTEGER NOT NULL DEFAULT 0, `generation` INTEGER NOT NULL DEFAULT 0, `next_schedule_time_override` INTEGER NOT NULL DEFAULT 9223372036854775807, `next_schedule_time_override_generation` INTEGER NOT NULL DEFAULT 0, `stop_reason` INTEGER NOT NULL DEFAULT -256, `trace_tag` TEXT, `required_network_type` INTEGER NOT NULL, `required_network_request` BLOB NOT NULL DEFAULT x'', `requires_charging` INTEGER NOT NULL, `requires_device_idle` INTEGER NOT NULL, `requires_battery_not_low` INTEGER NOT NULL, `requires_storage_not_low` INTEGER NOT NULL, `trigger_content_update_delay` INTEGER NOT NULL, `trigger_max_content_delay` INTEGER NOT NULL, `content_uri_triggers` BLOB NOT NULL, PRIMARY KEY(`id`))");
        awl.g("CREATE INDEX IF NOT EXISTS `index_WorkSpec_schedule_requested_at` ON `WorkSpec` (`schedule_requested_at`)");
        awl.g("CREATE INDEX IF NOT EXISTS `index_WorkSpec_last_enqueue_time` ON `WorkSpec` (`last_enqueue_time`)");
        awl.g("CREATE TABLE IF NOT EXISTS `WorkTag` (`tag` TEXT NOT NULL, `work_spec_id` TEXT NOT NULL, PRIMARY KEY(`tag`, `work_spec_id`), FOREIGN KEY(`work_spec_id`) REFERENCES `WorkSpec`(`id`) ON UPDATE CASCADE ON DELETE CASCADE )");
        awl.g("CREATE INDEX IF NOT EXISTS `index_WorkTag_work_spec_id` ON `WorkTag` (`work_spec_id`)");
        awl.g("CREATE TABLE IF NOT EXISTS `SystemIdInfo` (`work_spec_id` TEXT NOT NULL, `generation` INTEGER NOT NULL DEFAULT 0, `system_id` INTEGER NOT NULL, PRIMARY KEY(`work_spec_id`, `generation`), FOREIGN KEY(`work_spec_id`) REFERENCES `WorkSpec`(`id`) ON UPDATE CASCADE ON DELETE CASCADE )");
        awl.g("CREATE TABLE IF NOT EXISTS `WorkName` (`name` TEXT NOT NULL, `work_spec_id` TEXT NOT NULL, PRIMARY KEY(`name`, `work_spec_id`), FOREIGN KEY(`work_spec_id`) REFERENCES `WorkSpec`(`id`) ON UPDATE CASCADE ON DELETE CASCADE )");
        awl.g("CREATE INDEX IF NOT EXISTS `index_WorkName_work_spec_id` ON `WorkName` (`work_spec_id`)");
        awl.g("CREATE TABLE IF NOT EXISTS `WorkProgress` (`work_spec_id` TEXT NOT NULL, `progress` BLOB NOT NULL, PRIMARY KEY(`work_spec_id`), FOREIGN KEY(`work_spec_id`) REFERENCES `WorkSpec`(`id`) ON UPDATE CASCADE ON DELETE CASCADE )");
        awl.g("CREATE TABLE IF NOT EXISTS `Preference` (`key` TEXT NOT NULL, `long_value` INTEGER, PRIMARY KEY(`key`))");
        awl.g("CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)");
        awl.g("INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '86254750241babac4b8d52996a675549')");
    }

    public final ipt b(awl awl) {
        awl awl2 = awl;
        HashMap hashMap = new HashMap(2);
        hashMap.put("work_spec_id", new avy("work_spec_id", "TEXT", true, 1, (String) null, 1));
        hashMap.put("prerequisite_id", new avy("prerequisite_id", "TEXT", true, 2, (String) null, 1));
        HashSet hashSet = new HashSet(2);
        hashSet.add(new avz("WorkSpec", "CASCADE", "CASCADE", Arrays.asList(new String[]{"work_spec_id"}), Arrays.asList(new String[]{"id"})));
        hashSet.add(new avz("WorkSpec", "CASCADE", "CASCADE", Arrays.asList(new String[]{"prerequisite_id"}), Arrays.asList(new String[]{"id"})));
        HashSet hashSet2 = new HashSet(2);
        hashSet2.add(new awa("index_Dependency_work_spec_id", false, Arrays.asList(new String[]{"work_spec_id"}), Arrays.asList(new String[]{"ASC"})));
        hashSet2.add(new awa("index_Dependency_prerequisite_id", false, Arrays.asList(new String[]{"prerequisite_id"}), Arrays.asList(new String[]{"ASC"})));
        awb awb = new awb("Dependency", hashMap, hashSet, hashSet2);
        awb a2 = awb.a(awl2, "Dependency");
        if (!wb.q(awb, a2)) {
            return new ipt(false, a.ar(a2, awb, "Dependency(androidx.work.impl.model.Dependency).\n Expected:\n", "\n Found:\n"));
        }
        HashMap hashMap2 = new HashMap(32);
        hashMap2.put("id", new avy("id", "TEXT", true, 1, (String) null, 1));
        hashMap2.put("state", new avy("state", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("worker_class_name", new avy("worker_class_name", "TEXT", true, 0, (String) null, 1));
        hashMap2.put("input_merger_class_name", new avy("input_merger_class_name", "TEXT", true, 0, (String) null, 1));
        hashMap2.put("input", new avy("input", "BLOB", true, 0, (String) null, 1));
        hashMap2.put("output", new avy("output", "BLOB", true, 0, (String) null, 1));
        hashMap2.put("initial_delay", new avy("initial_delay", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("interval_duration", new avy("interval_duration", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("flex_duration", new avy("flex_duration", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("run_attempt_count", new avy("run_attempt_count", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("backoff_policy", new avy("backoff_policy", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("backoff_delay_duration", new avy("backoff_delay_duration", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("last_enqueue_time", new avy("last_enqueue_time", "INTEGER", true, 0, "-1", 1));
        hashMap2.put("minimum_retention_duration", new avy("minimum_retention_duration", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("schedule_requested_at", new avy("schedule_requested_at", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("run_in_foreground", new avy("run_in_foreground", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("out_of_quota_policy", new avy("out_of_quota_policy", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("period_count", new avy("period_count", "INTEGER", true, 0, "0", 1));
        hashMap2.put("generation", new avy("generation", "INTEGER", true, 0, "0", 1));
        hashMap2.put("next_schedule_time_override", new avy("next_schedule_time_override", "INTEGER", true, 0, "9223372036854775807", 1));
        hashMap2.put("next_schedule_time_override_generation", new avy("next_schedule_time_override_generation", "INTEGER", true, 0, "0", 1));
        hashMap2.put("stop_reason", new avy("stop_reason", "INTEGER", true, 0, "-256", 1));
        hashMap2.put("trace_tag", new avy("trace_tag", "TEXT", false, 0, (String) null, 1));
        hashMap2.put("required_network_type", new avy("required_network_type", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("required_network_request", new avy("required_network_request", "BLOB", true, 0, "x''", 1));
        hashMap2.put("requires_charging", new avy("requires_charging", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("requires_device_idle", new avy("requires_device_idle", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("requires_battery_not_low", new avy("requires_battery_not_low", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("requires_storage_not_low", new avy("requires_storage_not_low", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("trigger_content_update_delay", new avy("trigger_content_update_delay", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("trigger_max_content_delay", new avy("trigger_max_content_delay", "INTEGER", true, 0, (String) null, 1));
        hashMap2.put("content_uri_triggers", new avy("content_uri_triggers", "BLOB", true, 0, (String) null, 1));
        HashSet hashSet3 = new HashSet(0);
        HashSet hashSet4 = new HashSet(2);
        hashSet4.add(new awa("index_WorkSpec_schedule_requested_at", false, Arrays.asList(new String[]{"schedule_requested_at"}), Arrays.asList(new String[]{"ASC"})));
        hashSet4.add(new awa("index_WorkSpec_last_enqueue_time", false, Arrays.asList(new String[]{"last_enqueue_time"}), Arrays.asList(new String[]{"ASC"})));
        awb awb2 = new awb("WorkSpec", hashMap2, hashSet3, hashSet4);
        awb a3 = awb.a(awl2, "WorkSpec");
        if (!wb.q(awb2, a3)) {
            return new ipt(false, a.ar(a3, awb2, "WorkSpec(androidx.work.impl.model.WorkSpec).\n Expected:\n", "\n Found:\n"));
        }
        HashMap hashMap3 = new HashMap(2);
        hashMap3.put("tag", new avy("tag", "TEXT", true, 1, (String) null, 1));
        hashMap3.put("work_spec_id", new avy("work_spec_id", "TEXT", true, 2, (String) null, 1));
        HashSet hashSet5 = new HashSet(1);
        hashSet5.add(new avz("WorkSpec", "CASCADE", "CASCADE", Arrays.asList(new String[]{"work_spec_id"}), Arrays.asList(new String[]{"id"})));
        HashSet hashSet6 = new HashSet(1);
        hashSet6.add(new awa("index_WorkTag_work_spec_id", false, Arrays.asList(new String[]{"work_spec_id"}), Arrays.asList(new String[]{"ASC"})));
        awb awb3 = new awb("WorkTag", hashMap3, hashSet5, hashSet6);
        awb a4 = awb.a(awl2, "WorkTag");
        if (!wb.q(awb3, a4)) {
            return new ipt(false, a.ar(a4, awb3, "WorkTag(androidx.work.impl.model.WorkTag).\n Expected:\n", "\n Found:\n"));
        }
        HashMap hashMap4 = new HashMap(3);
        hashMap4.put("work_spec_id", new avy("work_spec_id", "TEXT", true, 1, (String) null, 1));
        hashMap4.put("generation", new avy("generation", "INTEGER", true, 2, "0", 1));
        hashMap4.put("system_id", new avy("system_id", "INTEGER", true, 0, (String) null, 1));
        HashSet hashSet7 = new HashSet(1);
        hashSet7.add(new avz("WorkSpec", "CASCADE", "CASCADE", Arrays.asList(new String[]{"work_spec_id"}), Arrays.asList(new String[]{"id"})));
        awb awb4 = new awb("SystemIdInfo", hashMap4, hashSet7, new HashSet(0));
        awb a5 = awb.a(awl2, "SystemIdInfo");
        if (!wb.q(awb4, a5)) {
            return new ipt(false, a.ar(a5, awb4, "SystemIdInfo(androidx.work.impl.model.SystemIdInfo).\n Expected:\n", "\n Found:\n"));
        }
        HashMap hashMap5 = new HashMap(2);
        hashMap5.put("name", new avy("name", "TEXT", true, 1, (String) null, 1));
        hashMap5.put("work_spec_id", new avy("work_spec_id", "TEXT", true, 2, (String) null, 1));
        HashSet hashSet8 = new HashSet(1);
        hashSet8.add(new avz("WorkSpec", "CASCADE", "CASCADE", Arrays.asList(new String[]{"work_spec_id"}), Arrays.asList(new String[]{"id"})));
        HashSet hashSet9 = new HashSet(1);
        hashSet9.add(new awa("index_WorkName_work_spec_id", false, Arrays.asList(new String[]{"work_spec_id"}), Arrays.asList(new String[]{"ASC"})));
        awb awb5 = new awb("WorkName", hashMap5, hashSet8, hashSet9);
        awb a6 = awb.a(awl2, "WorkName");
        if (!wb.q(awb5, a6)) {
            return new ipt(false, a.ar(a6, awb5, "WorkName(androidx.work.impl.model.WorkName).\n Expected:\n", "\n Found:\n"));
        }
        HashMap hashMap6 = new HashMap(2);
        hashMap6.put("work_spec_id", new avy("work_spec_id", "TEXT", true, 1, (String) null, 1));
        hashMap6.put("progress", new avy("progress", "BLOB", true, 0, (String) null, 1));
        HashSet hashSet10 = new HashSet(1);
        hashSet10.add(new avz("WorkSpec", "CASCADE", "CASCADE", Arrays.asList(new String[]{"work_spec_id"}), Arrays.asList(new String[]{"id"})));
        awb awb6 = new awb("WorkProgress", hashMap6, hashSet10, new HashSet(0));
        awb a7 = awb.a(awl2, "WorkProgress");
        if (!wb.q(awb6, a7)) {
            return new ipt(false, a.ar(a7, awb6, "WorkProgress(androidx.work.impl.model.WorkProgress).\n Expected:\n", "\n Found:\n"));
        }
        HashMap hashMap7 = new HashMap(2);
        hashMap7.put("key", new avy("key", "TEXT", true, 1, (String) null, 1));
        hashMap7.put("long_value", new avy("long_value", "INTEGER", false, 0, (String) null, 1));
        awb awb7 = new awb("Preference", hashMap7, new HashSet(0), new HashSet(0));
        awb a8 = awb.a(awl2, "Preference");
        if (!wb.q(awb7, a8)) {
            return new ipt(false, a.ar(a8, awb7, "Preference(androidx.work.impl.model.Preference).\n Expected:\n", "\n Found:\n"));
        }
        return new ipt(true, (String) null);
    }
}
