package defpackage;

import android.os.Parcel;
import android.os.Parcelable;
import j$.util.Objects;

/* renamed from: bpi  reason: default package */
/* compiled from: PG */
public final class bpi extends cgf {
    public static final Parcelable.Creator CREATOR = new aqu(17);
    public final String a;
    public final int b;

    public bpi(String str, int i) {
        this.a = str;
        this.b = i;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof bpi)) {
            return false;
        }
        bpi bpi = (bpi) obj;
        if (this.b != bpi.b || !Objects.equals(this.a, bpi.a)) {
            return false;
        }
        return true;
    }

    public final int hashCode() {
        return Objects.hash(this.a, Integer.valueOf(this.b));
    }

    public final void writeToParcel(Parcel parcel, int i) {
        String str = this.a;
        int i2 = cgr.i(parcel);
        cgr.y(parcel, 1, str);
        cgr.o(parcel, 2, this.b);
        cgr.k(parcel, i2);
    }
}
