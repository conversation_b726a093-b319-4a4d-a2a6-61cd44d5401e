package defpackage;

import android.util.Log;

/* renamed from: crc  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class crc implements ckp {
    public final /* synthetic */ String a;
    private final /* synthetic */ int b;

    public /* synthetic */ crc(String str, int i) {
        this.b = i;
        this.a = str;
    }

    public final void b(Exception exc) {
        if (this.b != 0) {
            boolean z = crd.a;
            Log.w("CBVerifier", String.format("Committing phenotypeflags for %s failed. %s", new Object[]{this.a, exc}));
            return;
        }
        boolean z2 = crd.a;
        Log.w("CBVerifier", String.format("Fail to register phenotypeflags for %s. %s", new Object[]{this.a, exc}));
    }
}
