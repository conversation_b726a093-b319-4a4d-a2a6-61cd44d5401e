package defpackage;

import android.view.View;
import android.view.ViewGroup;
import androidx.coordinatorlayout.widget.CoordinatorLayout;
import com.google.android.material.appbar.AppBarLayout;
import j$.util.Objects;

/* renamed from: bon  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bon implements vk {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public bon(ViewGroup viewGroup, int i) {
        this.b = i;
        this.a = viewGroup;
    }

    public final xn a(View view, xn xnVar) {
        boolean z;
        xn xnVar2;
        int i = this.b;
        boolean z2 = true;
        if (i == 0) {
            tg f = xnVar.f(143);
            view.setPadding(f.b, sk$$ExternalSyntheticApiModelOutline1.m(((mr) this.a).getWindow().getDecorView().getRootWindowInsets(), 1).top, f.d, f.e);
            ViewGroup viewGroup = (ViewGroup) view;
            viewGroup.setClipToPadding(false);
            viewGroup.setClipChildren(false);
            return xn.a;
        } else if (i != 1) {
            AppBarLayout appBarLayout = (AppBarLayout) this.a;
            if (true != appBarLayout.getFitsSystemWindows()) {
                xnVar2 = null;
            } else {
                xnVar2 = xnVar;
            }
            if (!Objects.equals(appBarLayout.c, xnVar2)) {
                appBarLayout.c = xnVar2;
                appBarLayout.k();
                appBarLayout.requestLayout();
            }
            return xnVar;
        } else {
            CoordinatorLayout coordinatorLayout = (CoordinatorLayout) this.a;
            if (!Objects.equals(coordinatorLayout.e, xnVar)) {
                coordinatorLayout.e = xnVar;
                if (xnVar.d() > 0) {
                    z = true;
                } else {
                    z = false;
                }
                coordinatorLayout.f = z;
                if (z || coordinatorLayout.getBackground() != null) {
                    z2 = false;
                }
                coordinatorLayout.setWillNotDraw(z2);
                if (!xnVar.r()) {
                    int childCount = coordinatorLayout.getChildCount();
                    for (int i2 = 0; i2 < childCount; i2++) {
                        View childAt = coordinatorLayout.getChildAt(i2);
                        int[] iArr = wj.a;
                        if (childAt.getFitsSystemWindows() && ((rz) childAt.getLayoutParams()).a != null && xnVar.r()) {
                            break;
                        }
                    }
                }
                coordinatorLayout.requestLayout();
            }
            return xnVar;
        }
    }

    public /* synthetic */ bon(mr mrVar, int i) {
        this.b = i;
        this.a = mrVar;
    }
}
