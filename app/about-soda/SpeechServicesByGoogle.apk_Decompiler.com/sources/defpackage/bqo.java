package defpackage;

@Deprecated
/* renamed from: bqo  reason: default package */
/* compiled from: PG */
public final class bqo extends htq implements hvb {
    public static final bqo b;
    public static final hua c = new hua(iai.l, iai.UNKNOWN);
    private static volatile hvh d;
    public huv a = huv.a;

    static {
        bqo bqo = new bqo();
        b = bqo;
        htq.z(bqo.class, bqo);
    }

    private bqo() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(b, "\u0004\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001࠲", new Object[]{"a", bqn.a, hna.n});
        } else if (i2 == 3) {
            return new bqo();
        } else {
            if (i2 == 4) {
                return new htk((htq) b);
            }
            if (i2 == 5) {
                return b;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = d;
            if (hvh == null) {
                synchronized (bqo.class) {
                    hvh = d;
                    if (hvh == null) {
                        hvh = new htl(b);
                        d = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
