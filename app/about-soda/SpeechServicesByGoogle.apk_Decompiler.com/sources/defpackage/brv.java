package defpackage;

import android.text.TextUtils;
import j$.util.DesugarCollections;
import j$.util.concurrent.ConcurrentHashMap;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.MissingResourceException;

/* renamed from: brv  reason: default package */
/* compiled from: PG */
public final class brv {
    public static final Map a = new ConcurrentHashMap();
    private static final hca b = hca.m("com/google/android/apps/speech/tts/googletts/common/locales/LocalesHelper");
    private static final gxv c = haq.a(2, new Object[]{"cmn-cn", "zh-CN", "cmn-tw", "zh-TW"});
    private static final Map d;
    private static final Map e;
    private static final Map f = new ConcurrentHashMap();
    private static final Map g = new ConcurrentHashMap();

    static {
        fvf.am("cmn-cn", "zh-CN");
        fvf.am("cmn-tw", "zh-TW");
        HashMap hashMap = new HashMap();
        for (String str : Locale.getISOLanguages()) {
            try {
                if (!str.equals("zz")) {
                    hashMap.put(b(new Locale(str)), str);
                }
            } catch (MissingResourceException unused) {
            }
        }
        d = DesugarCollections.unmodifiableMap(hashMap);
        HashMap hashMap2 = new HashMap();
        for (String str2 : Locale.getISOCountries()) {
            try {
                if (!str2.equalsIgnoreCase("ZZ")) {
                    hashMap2.put(new Locale("", str2).getISO3Country(), str2);
                }
            } catch (MissingResourceException unused2) {
            }
        }
        e = DesugarCollections.unmodifiableMap(hashMap2);
        new ConcurrentHashMap();
    }

    public static brt a(String str) {
        if (str == null) {
            ((hby) ((hby) b.g()).j("com/google/android/apps/speech/tts/googletts/common/locales/LocalesHelper", "createFromString", 150, "LocalesHelper.java")).u("Invalid locale string: %s", (Object) null);
            return null;
        }
        String[] k = k(str);
        int length = k.length;
        if (length == 1) {
            return new brt(k[0]);
        }
        if (length == 2) {
            return new brt(k[0], k[1]);
        }
        if (length == 3) {
            return new brt(k[0], k[1], k[2]);
        }
        ((hby) ((hby) b.g()).j("com/google/android/apps/speech/tts/googletts/common/locales/LocalesHelper", "createFromString", 164, "LocalesHelper.java")).u("Invalid locale string: %s", str);
        return null;
    }

    public static String b(Locale locale) {
        if (locale.getLanguage().equalsIgnoreCase("yue")) {
            return "yue";
        }
        String iSO3Language = locale.getISO3Language();
        if (iSO3Language.isEmpty()) {
            ((hby) ((hby) b.h()).j("com/google/android/apps/speech/tts/googletts/common/locales/LocalesHelper", "safeGetISO3Language", 255, "LocalesHelper.java")).u("Locale#getISO3Language() for %s is empty", locale);
        }
        return iSO3Language;
    }

    public static String c(String str) {
        String str2 = (String) c.get(str.toLowerCase(Locale.US));
        if (str2 != null) {
            return str2;
        }
        return str;
    }

    public static List d(bty bty) {
        if (!bty.c.isEmpty()) {
            ArrayList arrayList = new ArrayList(bty.c.size());
            for (String str : bty.c) {
                brt a2 = a(str);
                if (a2 != null) {
                    arrayList.add(a2);
                } else {
                    throw new IllegalStateException("Error converting to locale: ".concat(String.valueOf(str)));
                }
            }
            return arrayList;
        }
        throw new IllegalStateException("Every voice needs to support at least one locale");
    }

    public static Locale e(String str) {
        if (str.startsWith("cmn")) {
            return Locale.forLanguageTag(str.replaceFirst("cmn", "zh"));
        }
        return Locale.forLanguageTag(str);
    }

    public static Locale f(String str, String str2) {
        if (!str2.isEmpty()) {
            return new Locale.Builder().setLanguage(str).setRegion(str2).build();
        }
        return Locale.forLanguageTag(str.replace('_', '-'));
    }

    public static Locale g(String str) {
        Map map = f;
        Locale locale = (Locale) map.get(str);
        if (locale != null) {
            return locale;
        }
        String[] k = k(str);
        int length = k.length;
        if (length == 1) {
            locale = new Locale(k[0]);
            map.put(str, locale);
        } else if (length == 2) {
            locale = new Locale(k[0], k[1]);
            map.put(str, locale);
        } else if (length == 3) {
            locale = new Locale(k[0], k[1], k[2]);
            map.put(str, locale);
        } else if (length >= 4) {
            locale = new Locale(k[0], k[1], k[2]);
            map.put(str, locale);
        }
        if (locale != null) {
            return locale;
        }
        throw new IllegalArgumentException("Invalid locale: ".concat(String.valueOf(str)));
    }

    public static Locale h(Locale locale) {
        String str;
        String str2;
        String language = locale.getLanguage();
        if (!TextUtils.isEmpty(language) && (str2 = (String) d.get(language)) != null) {
            language = str2;
        }
        String country = locale.getCountry();
        if (!TextUtils.isEmpty(country) && (str = (String) e.get(country)) != null) {
            country = str;
        }
        return new Locale(language, country, locale.getVariant());
    }

    public static boolean i(Locale locale, Locale locale2) {
        if (!locale.getCountry().equals(locale2.getCountry()) || !locale.getLanguage().equals(locale2.getLanguage())) {
            return false;
        }
        return true;
    }

    public static boolean j(Locale locale, String str) {
        Locale.getDefault();
        try {
            Locale g2 = g(str);
            if (locale == null) {
                return false;
            }
            if (locale.getCountry().isEmpty()) {
                return locale.getLanguage().equals(g2.getLanguage());
            }
            return i(locale, g2);
        } catch (IllegalArgumentException unused) {
            ((hby) ((hby) b.h()).j("com/google/android/apps/speech/tts/googletts/common/locales/LocalesHelper", "localesEqualMaybeOnlyLanguage", 358, "LocalesHelper.java")).r("Unable to determine locale from strings.");
            return false;
        }
    }

    public static String[] k(String str) {
        Map map = g;
        String[] strArr = (String[]) map.get(str);
        if (strArr != null) {
            return strArr;
        }
        String[] split = str.split("-|_");
        map.put(str, split);
        return split;
    }
}
