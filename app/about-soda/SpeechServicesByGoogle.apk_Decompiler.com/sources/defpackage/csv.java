package defpackage;

/* renamed from: csv  reason: default package */
/* compiled from: PG */
public final class csv extends htq implements hvb {
    public static final csv q;
    private static volatile hvh r;
    public int a;
    public String b = "";
    public String c = "";
    public int d;
    public int e;
    public String f = "";
    public ihf g;
    public String h = "";
    public int i;
    public ihf j;
    public huf k = hvk.a;
    public int l;
    public int m;
    public String n = "";
    public String o = "";
    public hse p;

    static {
        csv csv = new csv();
        q = csv;
        htq.z(csv.class, csv);
    }

    private csv() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i2, Object obj) {
        int i3 = i2 - 1;
        if (i3 == 0) {
            return (byte) 1;
        }
        if (i3 == 2) {
            return new hvl(q, "\u0001\u000f\u0000\u0001\u0002\u0015\u000f\u0000\u0001\u0000\u0002ဈ\u0001\u0004င\u0002\u0005ဈ\u0004\u0007ဈ\u0000\u000bဉ\u0005\fဉ\b\r\u001b\u000eဈ\u0006\u000f᠌\u0003\u0010င\u0007\u0011᠌\t\u0012᠌\n\u0013ဈ\u000b\u0014ဈ\f\u0015ဉ\r", new Object[]{"a", "c", "d", "f", "b", "g", "j", "k", csy.class, "h", "e", bqk.o, "i", "l", bqk.n, "m", bqk.m, "n", "o", "p"});
        } else if (i3 == 3) {
            return new csv();
        } else {
            if (i3 == 4) {
                return new htk((htq) q);
            }
            if (i3 == 5) {
                return q;
            }
            if (i3 != 6) {
                return null;
            }
            hvh hvh = r;
            if (hvh == null) {
                synchronized (csv.class) {
                    hvh = r;
                    if (hvh == null) {
                        hvh = new htl(q);
                        r = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
