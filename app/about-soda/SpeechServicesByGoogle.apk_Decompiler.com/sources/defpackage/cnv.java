package defpackage;

import java.util.concurrent.ThreadFactory;

/* renamed from: cnv  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cnv implements ThreadFactory {
    private final /* synthetic */ int a;

    public /* synthetic */ cnv(int i) {
        this.a = i;
    }

    public final Thread newThread(Runnable runnable) {
        int i = this.a;
        if (i == 0) {
            return new Thread(runnable);
        }
        if (i != 1) {
            return new Thread(runnable, "ProcessStablePhenotypeFlag");
        }
        return new ui(runnable);
    }
}
