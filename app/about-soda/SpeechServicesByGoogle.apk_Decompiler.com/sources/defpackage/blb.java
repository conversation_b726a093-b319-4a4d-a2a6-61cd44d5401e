package defpackage;

import android.support.v7.widget.GridLayoutManager;

/* renamed from: blb  reason: default package */
/* compiled from: PG */
public final class blb implements bld {
    public int a = 1;
    public boolean b = false;
    public jk c = new ji();
    private int d = 1;

    public static blb d(kl klVar) {
        if (klVar == null) {
            return null;
        }
        if (klVar instanceof GridLayoutManager) {
            blb blb = new blb();
            GridLayoutManager gridLayoutManager = (GridLayoutManager) klVar;
            blb.e(gridLayoutManager.b);
            blb.b = gridLayoutManager.m;
            blb.c = gridLayoutManager.g;
            return blb;
        }
        throw new AssertionError("GridLayoutManager required.");
    }

    public final int a() {
        return 1;
    }

    public final int b() {
        return this.a;
    }

    public final int c() {
        return this.d;
    }

    public final void e(int i) {
        if (i > 0) {
            this.d = i;
            return;
        }
        throw new AssertionError("Span count must be bigger than 0");
    }

    public final boolean f() {
        return this.b;
    }
}
