package defpackage;

import androidx.work.WorkerParameters;

/* renamed from: bdw  reason: default package */
/* compiled from: PG */
public final class bdw extends jme {
    public /* synthetic */ Object a;
    public int b;
    public WorkerParameters c;
    final /* synthetic */ eez d;
    public eez e;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bdw(eez eez, jlr jlr) {
        super(jlr);
        this.d = eez;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.b |= Integer.MIN_VALUE;
        return this.d.i(this);
    }
}
