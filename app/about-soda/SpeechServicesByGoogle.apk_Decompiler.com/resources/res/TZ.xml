<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt">
    <item android:state_pressed="true" android:drawable="@drawable/coloredicons_cic_create_300_36"/>
    <item android:state_focused="true" android:drawable="@drawable/coloredicons_cic_create_300_36"/>
    <item android:drawable="@drawable/coloredicons_cic_create_300_36" android:state_hovered="true"/>
    <item android:drawable="@drawable/coloredicons_cic_create_36"/>
</selector>
