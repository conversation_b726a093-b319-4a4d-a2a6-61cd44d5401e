package defpackage;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;

/* renamed from: eks  reason: default package */
/* compiled from: PG */
public final class eks {
    private static final hca a = hca.m("com/google/android/libraries/search/audio/core/ref/RefManagerImpl");
    private final Map b = new LinkedHashMap();
    private final eoz c;

    public eks(eoz eoz) {
        jnu.e(eoz, "tokenGenerator");
        this.c = eoz;
    }

    private final int e(ekt ekt) {
        Set set = (Set) this.b.get(ekt.b);
        if (set != null) {
            return set.size();
        }
        return 0;
    }

    public final synchronized ekr a(ekt ekt, ekt ekt2) {
        ekr ekr;
        jnu.e(ekt, "key");
        jnu.e(ekt2, "ref");
        Map map = this.b;
        String str = ekt.b;
        Object obj = map.get(str);
        if (obj == null) {
            obj = new LinkedHashSet();
            map.put(str, obj);
        }
        String str2 = ekt2.b;
        jnu.d(str2, "getValue(...)");
        ekr = new ekr(!((Set) obj).add(str2), e(ekt));
        ((hby) a.f().h(hdg.a, "ALT.RefManager").j("com/google/android/libraries/search/audio/core/ref/RefManagerImpl", "addRef", 68, "RefManager.kt")).G("#audio# referencing(%s) to(%s), (%s)", ekt2.b, ekt.b, ekr);
        return ekr;
    }

    public final synchronized ekr b(ekt ekt, ekt ekt2) {
        boolean z;
        ekr ekr;
        jnu.e(ekt, "key");
        jnu.e(ekt2, "ref");
        Set set = (Set) this.b.get(ekt.b);
        if (set != null) {
            z = set.remove(ekt2.b);
        } else {
            z = false;
        }
        ekr = new ekr(z, e(ekt));
        ((hby) a.f().h(hdg.a, "ALT.RefManager").j("com/google/android/libraries/search/audio/core/ref/RefManagerImpl", "removeRef", 76, "RefManager.kt")).G("#audio# de-referencing(%s) from(%s), (%s)", ekt2.b, ekt.b, ekr);
        return ekr;
    }

    public final ekt c(String str) {
        htk l = ekt.c.l();
        jnu.d(l, "newBuilder(...)");
        cxi l2 = jnu.e(l, "builder");
        String format = String.format("%016X", Arrays.copyOf(new Object[]{Long.valueOf(this.c.n())}, 1));
        jnu.d(format, "format(...)");
        l2.m(str.concat(String.valueOf(format)));
        return l2.l();
    }

    public final synchronized boolean d(ekt ekt, ekt ekt2) {
        Set set;
        jnu.e(ekt, "key");
        set = (Set) this.b.get(ekt.b);
        if (set == null) {
            set = jks.a;
        }
        return set.contains(ekt2.b);
    }
}
