package defpackage;

/* renamed from: esk  reason: default package */
/* compiled from: PG */
final class esk extends jnv implements jmp {
    final /* synthetic */ int a;
    final /* synthetic */ dzx b;
    final /* synthetic */ esg c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public esk(int i, dzx dzx, esg esg) {
        super(0);
        this.a = i;
        this.b = dzx;
        this.c = esg;
    }

    public final /* bridge */ /* synthetic */ Object a() {
        ((hby) esl.a.f().h(hdg.a, "ALT.AudioRoutesRegy").j("com/google/android/libraries/search/audio/routing/registry/AudioRoutesRegistry$disconnectAudioRoute$logAudioRouteAbsence$1", "invoke", 438, "AudioRoutesRegistry.kt")).G("#audio# no audio route(%d) to disconnect(%s) for %s, skip", Integer.valueOf(this.a), this.b.name(), this.c.a());
        return jkd.a;
    }
}
