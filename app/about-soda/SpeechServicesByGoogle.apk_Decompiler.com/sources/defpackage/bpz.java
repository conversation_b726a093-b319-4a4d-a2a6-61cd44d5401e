package defpackage;

import android.content.ComponentName;
import android.content.Intent;
import android.content.ServiceConnection;

/* renamed from: bpz  reason: default package */
/* compiled from: PG */
public final class bpz implements ServiceConnection {
    public final Object a = new Object();
    public hmr b;
    final /* synthetic */ bqa c;
    protected awh d = new awh();

    public bpz(bqa bqa) {
        this.c = bqa;
        b();
    }

    public final void a(bpp bpp) {
        synchronized (this.a) {
            this.b.n(bpp);
        }
        this.c.c();
    }

    /* access modifiers changed from: package-private */
    public final void b() {
        synchronized (this.a) {
            this.b = new hmr();
            this.d = new awh();
        }
    }

    public final boolean c(Intent intent) {
        return this.c.e.bindService(intent, this, 1);
    }

    public final void d(boz boz) {
        synchronized (this.a) {
            this.b.m(new bpl(boz, this.d));
        }
    }

    public final void onBindingDied(ComponentName componentName) {
        synchronized (this.a) {
            this.d.f();
            a(new bpp(4, 603, "AiCore service binding died.", (Throwable) null));
        }
        this.c.c();
    }

    public final void onNullBinding(ComponentName componentName) {
        a(new bpp(4, 605, "AiCore service returns null on binding.", (Throwable) null));
    }

    /* JADX WARNING: type inference failed for: r4v5, types: [android.os.IInterface] */
    /* JADX WARNING: type inference failed for: r2v7, types: [bpa] */
    /* JADX WARNING: type inference failed for: r2v9, types: [bpa] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Unknown variable types count: 1 */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void onServiceConnected(android.content.ComponentName r4, android.os.IBinder r5) {
        /*
            r3 = this;
            android.content.ComponentName r0 = defpackage.bqa.b
            boolean r0 = defpackage.a.k(r4, r0)
            r1 = 4
            r2 = 0
            if (r0 == 0) goto L_0x0043
            if (r5 != 0) goto L_0x000d
            goto L_0x0020
        L_0x000d:
            java.lang.String r4 = "com.google.android.apps.aicore.aidl.IAiCoreServiceProvider"
            android.os.IInterface r4 = r5.queryLocalInterface(r4)
            boolean r0 = r4 instanceof defpackage.bpa
            if (r0 == 0) goto L_0x001b
            r2 = r4
            bpa r2 = (defpackage.bpa) r2
            goto L_0x0020
        L_0x001b:
            bpa r2 = new bpa
            r2.<init>(r5)
        L_0x0020:
            bpd r4 = new bpd     // Catch:{ RemoteException -> 0x0036, RuntimeException -> 0x0034 }
            r5 = 1
            r4.<init>((defpackage.bpz) r3, (int) r5)     // Catch:{ RemoteException -> 0x0036, RuntimeException -> 0x0034 }
            android.os.Parcel r5 = r2.a()     // Catch:{ RemoteException -> 0x0036, RuntimeException -> 0x0034 }
            int r0 = defpackage.box.a     // Catch:{ RemoteException -> 0x0036, RuntimeException -> 0x0034 }
            r5.writeStrongBinder(r4)     // Catch:{ RemoteException -> 0x0036, RuntimeException -> 0x0034 }
            r4 = 2
            r2.d(r4, r5)     // Catch:{ RemoteException -> 0x0036, RuntimeException -> 0x0034 }
            return
        L_0x0034:
            r4 = move-exception
            goto L_0x0037
        L_0x0036:
            r4 = move-exception
        L_0x0037:
            bpp r5 = new bpp
            r0 = 6
            java.lang.String r2 = "AiCore service is not connected."
            r5.<init>(r1, r0, r2, r4)
            r3.a(r5)
            return
        L_0x0043:
            android.content.ComponentName r0 = defpackage.bqa.c
            boolean r0 = defpackage.a.k(r4, r0)
            if (r0 == 0) goto L_0x0065
            if (r5 != 0) goto L_0x004e
            goto L_0x0061
        L_0x004e:
            java.lang.String r4 = "com.google.android.apps.aicore.aidl.IAICoreService"
            android.os.IInterface r4 = r5.queryLocalInterface(r4)
            boolean r0 = r4 instanceof defpackage.boz
            if (r0 == 0) goto L_0x005c
            r2 = r4
            boz r2 = (defpackage.boz) r2
            goto L_0x0061
        L_0x005c:
            boz r2 = new boz
            r2.<init>(r5)
        L_0x0061:
            r3.d(r2)
            return
        L_0x0065:
            java.lang.String r4 = java.lang.String.valueOf(r4)
            java.lang.String r4 = java.lang.String.valueOf(r4)
            bpp r5 = new bpp
            java.lang.String r0 = "AiCore service is not connected. Unknown component "
            java.lang.String r4 = r0.concat(r4)
            r0 = 0
            r5.<init>(r1, r0, r4, r2)
            r3.a(r5)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bpz.onServiceConnected(android.content.ComponentName, android.os.IBinder):void");
    }

    public final void onServiceDisconnected(ComponentName componentName) {
        synchronized (this.a) {
            this.d.f();
            this.b.n(new bpp(4, 602, "AiCore service disconnected.", (Throwable) null));
            b();
        }
    }
}
