package defpackage;

import android.content.Context;
import android.net.Uri;
import java.io.IOException;
import java.util.concurrent.Executor;

/* renamed from: cxq  reason: default package */
/* compiled from: PG */
public final class cxq implements cxw {
    public final Context a;
    public final cxa b;
    public final cuk c;
    public final csv d;
    public final cum e;
    public final csy f;
    public final ctg g;
    public final int h;
    public final long i;
    public final String j;
    public final grh k;
    public final Executor l;
    public final cyk m;
    public final int n;
    public final kjd o;
    private final cqh p;

    public cxq(Context context, cxa cxa, kjd kjd, cuk cuk, csv csv, int i2, cum cum, csy csy, cyk cyk, ctg ctg, int i3, long j2, String str, grh grh, cqh cqh, Executor executor) {
        this.a = context;
        this.b = cxa;
        this.o = kjd;
        this.c = cuk;
        this.d = csv;
        this.n = i2;
        this.e = cum;
        this.f = csy;
        this.m = cyk;
        this.g = ctg;
        this.h = i3;
        this.i = j2;
        this.j = str;
        this.k = grh;
        this.p = cqh;
        this.l = executor;
    }

    public final hme a(Uri uri) {
        cyh.d("%s: Successfully downloaded delta file %s", "DeltaFileDownloaderCallbackImpl", uri);
        if (!cxt.d(this.o, uri, this.f.d)) {
            cyh.i("%s: Downloaded delta file at uri = %s, checksum = %s verification failed", "DeltaFileDownloaderCallbackImpl", uri, this.f.d);
            kml a2 = csi.a();
            a2.b = csh.DOWNLOADED_FILE_CHECKSUM_MISMATCH_ERROR;
            csi a3 = a2.a();
            cxa cxa = this.b;
            csv csv = this.d;
            int i2 = this.n;
            kjd kjd = this.o;
            csy csy = this.f;
            return czw.e(cxs.d(cxa, csv, i2, kjd, uri, csy.d, this.m, this.l)).d(IOException.class, new cwi(a3, 17), this.l).g(new cwi(a3, 18), this.l);
        }
        Uri r = cqh.r(uri);
        htk l2 = ctj.g.l();
        csu csu = this.f.f;
        if (csu == null) {
            csu = csu.b;
        }
        String str = csu.a;
        if (!l2.b.B()) {
            l2.u();
        }
        htq htq = l2.b;
        ctj ctj = (ctj) htq;
        str.getClass();
        ctj.a |= 4;
        ctj.d = str;
        int i3 = this.n;
        if (!htq.B()) {
            l2.u();
        }
        ctj ctj2 = (ctj) l2.b;
        ctj2.e = i3 - 1;
        ctj2.a |= 8;
        ctj ctj3 = (ctj) l2.r();
        return ftd.L(ftd.L(this.b.e(ctj3), new cvd((Object) this, (Object) ctj3, (Object) r, (Object) uri, 15, (byte[]) null), this.l), new cwq(this, r, 8), this.l);
    }

    public final hme b(csi csi) {
        cyh.d("%s: Failed to download file(delta) %s", "DeltaFileDownloaderCallbackImpl", this.d.f);
        if (csi.a.equals(csh.DOWNLOADED_FILE_CHECKSUM_MISMATCH_ERROR)) {
            return cxs.c(ctf.CORRUPTED, this.d, this.n, this.b, this.l);
        }
        return cxs.c(ctf.DOWNLOAD_FAILED, this.d, this.n, this.b, this.l);
    }
}
