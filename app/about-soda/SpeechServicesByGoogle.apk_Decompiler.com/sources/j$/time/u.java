package j$.time;

import j$.time.chrono.C0010i;
import j$.time.format.A;
import j$.time.format.s;
import j$.time.temporal.ChronoUnit;
import j$.time.temporal.TemporalUnit;
import j$.time.temporal.a;
import j$.time.temporal.k;
import j$.time.temporal.l;
import j$.time.temporal.n;
import j$.time.temporal.p;
import j$.time.temporal.r;
import j$.time.temporal.t;
import java.io.DataOutput;
import java.io.InvalidObjectException;
import java.io.ObjectInputStream;
import java.io.Serializable;

public final class u implements k, n, Comparable, Serializable {
    public static final /* synthetic */ int b = 0;
    private static final long serialVersionUID = -23038383694477807L;
    private final int a;

    static {
        s sVar = new s();
        sVar.p(a.YEAR, 4, 10, A.EXCEEDS_PAD);
        sVar.x();
    }

    private u(int i) {
        this.a = i;
    }

    public static u I(int i) {
        a.YEAR.I((long) i);
        return new u(i);
    }

    private void readObject(ObjectInputStream objectInputStream) {
        throw new InvalidObjectException("Deserialization via serialization delegate");
    }

    private Object writeReplace() {
        return new s((byte) 11, this);
    }

    public final long B(p pVar) {
        if (!(pVar instanceof a)) {
            return pVar.g(this);
        }
        int i = t.a[((a) pVar).ordinal()];
        int i2 = this.a;
        int i3 = 1;
        if (i == 1) {
            if (i2 < 1) {
                i2 = 1 - i2;
            }
            return (long) i2;
        } else if (i == 2) {
            return (long) i2;
        } else {
            if (i == 3) {
                if (i2 < 1) {
                    i3 = 0;
                }
                return (long) i3;
            }
            throw new RuntimeException("Unsupported field: ".concat(String.valueOf(pVar)));
        }
    }

    public final Object F(r rVar) {
        if (rVar == l.e()) {
            return j$.time.chrono.u.d;
        }
        if (rVar == l.i()) {
            return ChronoUnit.YEARS;
        }
        return l.c(this, rVar);
    }

    /* renamed from: J */
    public final u f(long j, TemporalUnit temporalUnit) {
        if (!(temporalUnit instanceof ChronoUnit)) {
            return (u) temporalUnit.m(this, j);
        }
        int i = t.b[((ChronoUnit) temporalUnit).ordinal()];
        if (i == 1) {
            return K(j);
        }
        if (i == 2) {
            return K(j$.nio.file.attribute.a.f(j, (long) 10));
        }
        if (i == 3) {
            return K(j$.nio.file.attribute.a.f(j, (long) 100));
        }
        if (i == 4) {
            return K(j$.nio.file.attribute.a.f(j, (long) 1000));
        }
        if (i == 5) {
            a aVar = a.ERA;
            return d(j$.nio.file.attribute.a.e(B(aVar), j), aVar);
        }
        throw new RuntimeException("Unsupported unit: ".concat(String.valueOf(temporalUnit)));
    }

    public final u K(long j) {
        if (j == 0) {
            return this;
        }
        return I(a.YEAR.F(((long) this.a) + j));
    }

    /* renamed from: L */
    public final u d(long j, p pVar) {
        if (!(pVar instanceof a)) {
            return (u) pVar.s(this, j);
        }
        a aVar = (a) pVar;
        aVar.I(j);
        int i = t.a[aVar.ordinal()];
        int i2 = this.a;
        if (i == 1) {
            if (i2 < 1) {
                j = 1 - j;
            }
            return I((int) j);
        } else if (i == 2) {
            return I((int) j);
        } else {
            if (i != 3) {
                throw new RuntimeException("Unsupported field: ".concat(String.valueOf(pVar)));
            } else if (B(a.ERA) == j) {
                return this;
            } else {
                return I(1 - i2);
            }
        }
    }

    /* access modifiers changed from: package-private */
    public final void M(DataOutput dataOutput) {
        dataOutput.writeInt(this.a);
    }

    public final int compareTo(Object obj) {
        return this.a - ((u) obj).a;
    }

    public final boolean e(p pVar) {
        if (pVar instanceof a) {
            if (pVar == a.YEAR || pVar == a.YEAR_OF_ERA || pVar == a.ERA) {
                return true;
            }
            return false;
        } else if (pVar == null || !pVar.p(this)) {
            return false;
        } else {
            return true;
        }
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof u) || this.a != ((u) obj).a) {
            return false;
        }
        return true;
    }

    public final k g(k kVar) {
        if (C0010i.p(kVar).equals(j$.time.chrono.u.d)) {
            return kVar.d((long) this.a, a.YEAR);
        }
        throw new RuntimeException("Adjustment only supported on ISO date-time");
    }

    public final int hashCode() {
        return this.a;
    }

    public final k m(long j, ChronoUnit chronoUnit) {
        if (j == Long.MIN_VALUE) {
            return f(Long.MAX_VALUE, chronoUnit).f(1, chronoUnit);
        }
        return f(-j, chronoUnit);
    }

    public final int p(p pVar) {
        return t(pVar).a(B(pVar), pVar);
    }

    public final k s(g gVar) {
        return (u) C0010i.a(gVar, this);
    }

    public final t t(p pVar) {
        long j;
        if (pVar != a.YEAR_OF_ERA) {
            return l.d(this, pVar);
        }
        if (this.a <= 0) {
            j = 1000000000;
        } else {
            j = 999999999;
        }
        return t.j(1, j);
    }

    public final String toString() {
        return Integer.toString(this.a);
    }
}
