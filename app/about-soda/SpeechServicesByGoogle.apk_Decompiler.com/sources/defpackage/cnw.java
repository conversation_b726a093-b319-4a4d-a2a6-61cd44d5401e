package defpackage;

import android.os.Build;
import android.os.Looper;

/* renamed from: cnw  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cnw implements Runnable {
    public final /* synthetic */ hmr a;

    public /* synthetic */ cnw(hmr hmr) {
        this.a = hmr;
    }

    /* JADX INFO: finally extract failed */
    public final void run() {
        hmr hmr = this.a;
        try {
            Looper.prepare();
            hmr.m(Looper.myLooper());
            Looper.loop();
            if (!"robolectric".equals(Build.FINGERPRINT)) {
                fnk.e(new bpw(11));
            }
        } catch (Throwable th) {
            hmr.m(Looper.myLooper());
            throw th;
        }
    }
}
