package defpackage;

import java.util.List;

/* renamed from: cvu  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvu implements hko {
    public final /* synthetic */ boolean a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    public final /* synthetic */ Object d;
    private final /* synthetic */ int e;

    public /* synthetic */ cvu(Object obj, boolean z, Object obj2, Object obj3, int i) {
        this.e = i;
        this.b = obj;
        this.a = z;
        this.c = obj2;
        this.d = obj3;
    }

    /* JADX WARNING: type inference failed for: r0v8, types: [java.lang.Object, hko] */
    /* JADX WARNING: type inference failed for: r0v31, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: Removed duplicated region for block: B:79:0x01c5  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme a(java.lang.Object r12) {
        /*
            r11 = this;
            int r0 = r11.e
            r1 = 0
            r2 = 1
            if (r0 == 0) goto L_0x00f2
            if (r0 == r2) goto L_0x0042
            java.lang.Throwable r12 = (java.lang.Throwable) r12
            java.lang.Object r0 = r11.c
            boolean r2 = r11.a
            if (r2 == 0) goto L_0x001c
            java.lang.Object r2 = r11.d
            java.lang.Object r3 = r11.b
            dhp r3 = (defpackage.dhp) r3
            r4 = r0
            ddk r4 = (defpackage.ddk) r4
            r3.a(r4, r2)
        L_0x001c:
            hdf r2 = defpackage.dcs.a
            hco r2 = r2.g()
            hdc r2 = (defpackage.hdc) r2
            hco r12 = r2.i(r12)
            hdc r12 = (defpackage.hdc) r12
            java.lang.String r2 = "onStartJob"
            r3 = 109(0x6d, float:1.53E-43)
            java.lang.String r4 = "com/google/android/libraries/micore/superpacks/scheduling/DownloadJob"
            java.lang.String r5 = "DownloadJob.java"
            hco r12 = r12.j(r4, r2, r3, r5)
            hdc r12 = (defpackage.hdc) r12
            java.lang.String r2 = "DownloadJob#onStartJob: failure for %s"
            r12.u(r2, r0)
            hme r12 = defpackage.hfc.K(r1)
            return r12
        L_0x0042:
            gxv r12 = (defpackage.gxv) r12
            java.lang.Object r0 = r11.d
            java.lang.Object r2 = r11.c
            java.util.Iterator r0 = r0.iterator()
        L_0x004c:
            boolean r3 = r0.hasNext()
            if (r3 == 0) goto L_0x00ef
            java.lang.Object r3 = r0.next()
            csv r3 = (defpackage.csv) r3
            boolean r4 = r12.containsKey(r3)
            if (r4 != 0) goto L_0x0074
            kml r12 = defpackage.csi.a()
            csh r0 = defpackage.csh.DOWNLOADED_FILE_NOT_FOUND_ERROR
            r12.b = r0
            java.lang.String r0 = "getDataFileUris() resolved to null"
            r12.c = r0
            csi r12 = r12.a()
            hme r12 = defpackage.hfc.J(r12)
            goto L_0x00f1
        L_0x0074:
            java.lang.Object r4 = r11.b
            boolean r5 = r11.a
            java.lang.Object r6 = r12.get(r3)
            android.net.Uri r6 = (android.net.Uri) r6
            if (r5 != 0) goto L_0x00b8
            r5 = r2
            kjd r5 = (defpackage.kjd) r5     // Catch:{ IOException -> 0x00db }
            boolean r5 = r5.k(r6)     // Catch:{ IOException -> 0x00db }
            if (r5 == 0) goto L_0x00b8
            java.lang.String r3 = r6.getPath()     // Catch:{ IOException -> 0x00db }
            if (r3 == 0) goto L_0x004c
            r5 = r2
            kjd r5 = (defpackage.kjd) r5     // Catch:{ IOException -> 0x00db }
            java.util.List r3 = defpackage.cuf.p(r5, r6, r3)     // Catch:{ IOException -> 0x00db }
            r5 = r4
            htk r5 = (defpackage.htk) r5     // Catch:{ IOException -> 0x00db }
            htq r5 = r5.b     // Catch:{ IOException -> 0x00db }
            boolean r5 = r5.B()     // Catch:{ IOException -> 0x00db }
            if (r5 != 0) goto L_0x00a7
            r5 = r4
            htk r5 = (defpackage.htk) r5     // Catch:{ IOException -> 0x00db }
            r5.u()     // Catch:{ IOException -> 0x00db }
        L_0x00a7:
            htk r4 = (defpackage.htk) r4     // Catch:{ IOException -> 0x00db }
            htq r4 = r4.b     // Catch:{ IOException -> 0x00db }
            crw r4 = (defpackage.crw) r4     // Catch:{ IOException -> 0x00db }
            crw r5 = defpackage.crw.n     // Catch:{ IOException -> 0x00db }
            r4.b()     // Catch:{ IOException -> 0x00db }
            huf r4 = r4.g     // Catch:{ IOException -> 0x00db }
            defpackage.hrz.g(r3, r4)     // Catch:{ IOException -> 0x00db }
            goto L_0x004c
        L_0x00b8:
            java.lang.String r5 = r3.b     // Catch:{ IOException -> 0x00db }
            int r7 = r3.d     // Catch:{ IOException -> 0x00db }
            int r8 = r3.i     // Catch:{ IOException -> 0x00db }
            java.lang.String r9 = r6.toString()     // Catch:{ IOException -> 0x00db }
            int r10 = r3.a     // Catch:{ IOException -> 0x00db }
            r10 = r10 & 8192(0x2000, float:1.14794E-41)
            if (r10 == 0) goto L_0x00cf
            hse r3 = r3.p     // Catch:{ IOException -> 0x00db }
            if (r3 != 0) goto L_0x00d0
            hse r3 = defpackage.hse.c     // Catch:{ IOException -> 0x00db }
            goto L_0x00d0
        L_0x00cf:
            r3 = r1
        L_0x00d0:
            crv r3 = defpackage.cuf.j(r5, r7, r8, r9, r3)     // Catch:{ IOException -> 0x00db }
            htk r4 = (defpackage.htk) r4     // Catch:{ IOException -> 0x00db }
            r4.A(r3)     // Catch:{ IOException -> 0x00db }
            goto L_0x004c
        L_0x00db:
            r3 = move-exception
            java.lang.String r4 = java.lang.String.valueOf(r6)
            java.lang.String r4 = java.lang.String.valueOf(r4)
            java.lang.String r5 = "Failed to list files under directory:"
            java.lang.String r4 = r5.concat(r4)
            defpackage.cyh.q(r3, r4)
            goto L_0x004c
        L_0x00ef:
            hme r12 = defpackage.hma.a
        L_0x00f1:
            return r12
        L_0x00f2:
            csx r12 = (defpackage.csx) r12
            if (r12 == 0) goto L_0x01d6
            int r0 = r12.q
            int r0 = defpackage.cqh.A(r0)
            if (r0 != 0) goto L_0x00ff
            goto L_0x0103
        L_0x00ff:
            if (r0 == r2) goto L_0x0103
            goto L_0x01d6
        L_0x0103:
            csz r0 = r12.l
            if (r0 != 0) goto L_0x0109
            csz r0 = defpackage.csz.f
        L_0x0109:
            int r0 = r0.c
            int r0 = defpackage.a.x(r0)
            r3 = 3
            r4 = 2
            r5 = 0
            if (r0 != 0) goto L_0x0115
            goto L_0x011a
        L_0x0115:
            if (r0 != r4) goto L_0x011a
        L_0x0117:
            r0 = r2
            goto L_0x01a0
        L_0x011a:
            csz r0 = r12.l
            if (r0 != 0) goto L_0x0120
            csz r0 = defpackage.csz.f
        L_0x0120:
            int r0 = r0.c
            int r0 = defpackage.a.x(r0)
            if (r0 != 0) goto L_0x012b
        L_0x0128:
            r0 = r5
            goto L_0x01a0
        L_0x012b:
            if (r0 != r3) goto L_0x0128
            long r6 = defpackage.cqx.c()
            csw r0 = r12.b
            if (r0 != 0) goto L_0x0137
            csw r0 = defpackage.csw.i
        L_0x0137:
            long r8 = r0.c
            long r6 = r6 - r8
            csz r0 = r12.l
            if (r0 != 0) goto L_0x0140
            csz r0 = defpackage.csz.f
        L_0x0140:
            r8 = 1000(0x3e8, double:4.94E-321)
            long r6 = r6 / r8
            long r8 = r0.d
            int r0 = (r6 > r8 ? 1 : (r6 == r8 ? 0 : -1))
            if (r0 <= 0) goto L_0x0128
            r0 = 5
            java.lang.Object r6 = r12.C(r0)
            htk r6 = (defpackage.htk) r6
            r6.x(r12)
            csz r12 = r12.l
            if (r12 != 0) goto L_0x0159
            csz r12 = defpackage.csz.f
        L_0x0159:
            java.lang.Object r0 = r12.C(r0)
            htk r0 = (defpackage.htk) r0
            r0.x(r12)
            htq r12 = r0.b
            boolean r12 = r12.B()
            if (r12 != 0) goto L_0x016d
            r0.u()
        L_0x016d:
            htq r12 = r0.b
            csz r12 = (defpackage.csz) r12
            r12.c = r2
            int r7 = r12.a
            r7 = r7 | r4
            r12.a = r7
            htq r12 = r6.b
            boolean r12 = r12.B()
            if (r12 != 0) goto L_0x0183
            r6.u()
        L_0x0183:
            htq r12 = r6.b
            csx r12 = (defpackage.csx) r12
            htq r0 = r0.r()
            csz r0 = (defpackage.csz) r0
            r0.getClass()
            r12.l = r0
            int r0 = r12.a
            r0 = r0 | 2048(0x800, float:2.87E-42)
            r12.a = r0
            htq r12 = r6.r()
            csx r12 = (defpackage.csx) r12
            goto L_0x0117
        L_0x01a0:
            boolean r6 = r11.a
            java.lang.String r7 = r12.c
            java.lang.Boolean r8 = java.lang.Boolean.valueOf(r0)
            java.lang.Object[] r3 = new java.lang.Object[r3]
            java.lang.String r9 = "FileGroupManager"
            r3[r5] = r9
            r3[r2] = r7
            r3[r4] = r8
            java.lang.String r2 = "%s: Try to download pending file group: %s, download over cellular = %s"
            defpackage.cyh.e(r2, r3)
            if (r6 != 0) goto L_0x01c1
            if (r0 == 0) goto L_0x01bc
            goto L_0x01c1
        L_0x01bc:
            hme r12 = defpackage.hfc.K(r1)
            goto L_0x01da
        L_0x01c1:
            csz r12 = r12.l
            if (r12 != 0) goto L_0x01c7
            csz r12 = defpackage.csz.f
        L_0x01c7:
            java.lang.Object r0 = r11.d
            java.lang.Object r1 = r11.c
            java.lang.Object r2 = r11.b
            cvy r2 = (defpackage.cvy) r2
            ctg r1 = (defpackage.ctg) r1
            hme r12 = r2.e(r1, r12, r0)
            goto L_0x01da
        L_0x01d6:
            hme r12 = defpackage.hfc.K(r1)
        L_0x01da:
            return r12
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cvu.a(java.lang.Object):hme");
    }

    public /* synthetic */ cvu(List list, boolean z, kjd kjd, htk htk, int i) {
        this.e = i;
        this.d = list;
        this.a = z;
        this.c = kjd;
        this.b = htk;
    }
}
