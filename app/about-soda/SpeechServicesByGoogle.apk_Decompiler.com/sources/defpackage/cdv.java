package defpackage;

import android.app.ActivityManager;
import android.app.Application;
import android.app.PendingIntent;
import android.content.Context;
import android.content.pm.PackageManager;
import android.os.Handler;
import android.os.HandlerThread;
import android.os.Looper;
import android.os.Message;
import android.os.SystemClock;
import android.util.Log;
import androidx.wear.ambient.AmbientMode;
import com.google.android.gms.common.api.GoogleApiActivity;
import com.google.android.gms.common.api.Status;
import j$.util.Objects;
import j$.util.concurrent.ConcurrentHashMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/* renamed from: cdv  reason: default package */
/* compiled from: PG */
public final class cdv implements Handler.Callback {
    public static final Status a = new Status(4, "Sign-out occurred while this API call was in progress.");
    public static final Status b = new Status(4, "The user must be signed in to make this API call.");
    public static final Object c = new Object();
    private static cdv p;
    public long d = 10000;
    public boolean e = false;
    public final Context f;
    public final cbi g;
    public final cfn h;
    public final AtomicInteger i = new AtomicInteger(1);
    public final AtomicInteger j = new AtomicInteger(0);
    public final Map k = new ConcurrentHashMap(5, 0.75f, 1);
    public cdp l = null;
    public final Set m = new ov();
    public final Handler n;
    public volatile boolean o = true;
    private cgc q;
    private final Set r = new ov();
    private cgk s;

    private cdv(Context context, Looper looper, cbi cbi) {
        this.f = context;
        fzt fzt = new fzt(looper, (Handler.Callback) this);
        this.n = fzt;
        this.g = cbi;
        this.h = new cfn((cbj) cbi);
        PackageManager packageManager = context.getPackageManager();
        if (cgo.b == null) {
            cgo.b = Boolean.valueOf(packageManager.hasSystemFeature("android.hardware.type.automotive"));
        }
        if (cgo.b.booleanValue()) {
            this.o = false;
        }
        fzt.sendMessage(fzt.obtainMessage(6));
    }

    public static Status a(cdf cdf, cbe cbe) {
        Object obj = cdf.a.a;
        String valueOf = String.valueOf(cbe);
        return new Status(17, "API: " + ((String) obj) + " is not available on this device. Connection failed with: " + valueOf, cbe.d, cbe);
    }

    public static cdv c(Context context) {
        cdv cdv;
        HandlerThread handlerThread;
        synchronized (c) {
            if (p == null) {
                synchronized (cfj.a) {
                    handlerThread = cfj.b;
                    if (handlerThread == null) {
                        cfj.b = new HandlerThread("GoogleApiHandler", 9);
                        cfj.b.start();
                        handlerThread = cfj.b;
                    }
                }
                p = new cdv(context.getApplicationContext(), handlerThread.getLooper(), cbi.a);
            }
            cdv = p;
        }
        return cdv;
    }

    private final cds j(ccm ccm) {
        Map map = this.k;
        cdf cdf = ccm.e;
        cds cds = (cds) map.get(cdf);
        if (cds == null) {
            cds = new cds(this, ccm);
            this.k.put(cdf, cds);
        }
        if (cds.p()) {
            this.r.add(cdf);
        }
        cds.d();
        return cds;
    }

    private final void k() {
        cgc cgc = this.q;
        if (cgc != null) {
            if (cgc.a > 0 || g()) {
                l().a(cgc);
            }
            this.q = null;
        }
    }

    private final cgk l() {
        if (this.s == null) {
            this.s = new cgk(this.f, cge.b);
        }
        return this.s;
    }

    /* access modifiers changed from: package-private */
    public final cds b(cdf cdf) {
        return (cds) this.k.get(cdf);
    }

    public final void d(cbe cbe, int i2) {
        if (!h(cbe, i2)) {
            Handler handler = this.n;
            handler.sendMessage(handler.obtainMessage(5, i2, 0, cbe));
        }
    }

    public final void e() {
        Handler handler = this.n;
        handler.sendMessage(handler.obtainMessage(3));
    }

    public final void f(cdp cdp) {
        synchronized (c) {
            if (this.l != cdp) {
                this.l = cdp;
                this.m.clear();
            }
            this.m.addAll(cdp.e);
        }
    }

    /* access modifiers changed from: package-private */
    public final boolean g() {
        if (this.e) {
            return false;
        }
        cgb cgb = cga.a().a;
        if (cgb != null && !cgb.b) {
            return false;
        }
        int b2 = this.h.b(203400000);
        if (b2 == -1 || b2 == 0) {
            return true;
        }
        return false;
    }

    /* access modifiers changed from: package-private */
    public final boolean h(cbe cbe, int i2) {
        PendingIntent pendingIntent;
        Context context = this.f;
        if (cgr.d(context)) {
            return false;
        }
        cbi cbi = this.g;
        if (cbe.a()) {
            pendingIntent = cbe.d;
        } else {
            pendingIntent = cbi.h(context, cbe.c, (String) null);
        }
        if (pendingIntent == null) {
            return false;
        }
        cbi.d(context, cbe.c, PendingIntent.getActivity(context, 0, GoogleApiActivity.a(context, pendingIntent, i2, true), cib.a | 134217728));
        return true;
    }

    /* JADX WARNING: type inference failed for: r1v31, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v54, types: [java.util.Map, java.lang.Object] */
    public final boolean handleMessage(Message message) {
        Status status;
        cbg[] b2;
        long j2 = 300000;
        cds cds = null;
        switch (message.what) {
            case 1:
                if (true == ((Boolean) message.obj).booleanValue()) {
                    j2 = 10000;
                }
                this.d = j2;
                this.n.removeMessages(12);
                for (cdf obtainMessage : this.k.keySet()) {
                    Handler handler = this.n;
                    handler.sendMessageDelayed(handler.obtainMessage(12, obtainMessage), this.d);
                }
                break;
            case 2:
                ke keVar = (ke) message.obj;
                throw null;
            case 3:
                for (cds cds2 : this.k.values()) {
                    cds2.c();
                    cds2.d();
                }
                break;
            case 4:
            case 8:
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                dvc dvc = (dvc) message.obj;
                cds cds3 = (cds) this.k.get(((ccm) dvc.b).e);
                if (cds3 == null) {
                    cds3 = j((ccm) dvc.b);
                }
                if (cds3.p() && this.j.get() != dvc.a) {
                    ((cde) dvc.c).d(a);
                    cds3.n();
                    break;
                } else {
                    cds3.e((cde) dvc.c);
                    break;
                }
                break;
            case 5:
                int i2 = message.arg1;
                cbe cbe = (cbe) message.obj;
                Iterator it = this.k.values().iterator();
                while (true) {
                    if (it.hasNext()) {
                        cds cds4 = (cds) it.next();
                        if (cds4.e == i2) {
                            cds = cds4;
                        }
                    }
                }
                if (cds != null) {
                    if (cbe.c != 13) {
                        cds.f(a(cds.c, cbe));
                        break;
                    } else {
                        AtomicBoolean atomicBoolean = cca.c;
                        cds.f(new Status(17, "Error resolution was canceled by the user, original error message: CANCELED: " + cbe.e));
                        break;
                    }
                } else {
                    Log.wtf("GoogleApiManager", a.am(i2, "Could not find API instance ", " while trying to fail enqueued calls."), new Exception());
                    break;
                }
            case 6:
                if (this.f.getApplicationContext() instanceof Application) {
                    Application application = (Application) this.f.getApplicationContext();
                    synchronized (cdg.a) {
                        cdg cdg = cdg.a;
                        if (!cdg.e) {
                            application.registerActivityLifecycleCallbacks(cdg);
                            application.registerComponentCallbacks(cdg.a);
                            cdg.a.e = true;
                        }
                    }
                    cdg cdg2 = cdg.a;
                    AmbientMode.AmbientController ambientController = new AmbientMode.AmbientController((Object) this);
                    synchronized (cdg2) {
                        cdg2.d.add(ambientController);
                    }
                    cdg cdg3 = cdg.a;
                    if (!cdg3.c.get()) {
                        ActivityManager.RunningAppProcessInfo runningAppProcessInfo = new ActivityManager.RunningAppProcessInfo();
                        ActivityManager.getMyMemoryState(runningAppProcessInfo);
                        if (!cdg3.c.getAndSet(true) && runningAppProcessInfo.importance > 100) {
                            cdg3.b.set(true);
                        }
                    }
                    if (!cdg3.b.get()) {
                        this.d = 300000;
                        break;
                    }
                }
                break;
            case 7:
                j((ccm) message.obj);
                break;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                if (this.k.containsKey(message.obj)) {
                    cds cds5 = (cds) this.k.get(message.obj);
                    cgr.ad(cds5.i.n);
                    if (cds5.f) {
                        cds5.d();
                        break;
                    }
                }
                break;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                ou ouVar = new ou((ov) this.r);
                while (ouVar.hasNext()) {
                    cds cds6 = (cds) this.k.remove((cdf) ouVar.next());
                    if (cds6 != null) {
                        cds6.n();
                    }
                }
                this.r.clear();
                break;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                if (this.k.containsKey(message.obj)) {
                    cds cds7 = (cds) this.k.get(message.obj);
                    cgr.ad(cds7.i.n);
                    if (cds7.f) {
                        cds7.o();
                        cdv cdv = cds7.i;
                        if (cdv.g.e(cdv.f) == 18) {
                            status = new Status(21, "Connection timed out waiting for Google Play services update to complete.");
                        } else {
                            status = new Status(22, "API failed to connect while resuming due to an unknown error.");
                        }
                        cds7.f(status);
                        cds7.b.i("Timing out connection while resuming.");
                        break;
                    }
                }
                break;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                if (this.k.containsKey(message.obj)) {
                    cds cds8 = (cds) this.k.get(message.obj);
                    cgr.ad(cds8.i.n);
                    if (cds8.b.j() && cds8.d.isEmpty()) {
                        cxj cxj = cds8.j;
                        if (cxj.a.isEmpty() && cxj.b.isEmpty()) {
                            cds8.b.i("Timing out service connection.");
                            break;
                        } else {
                            cds8.m();
                            break;
                        }
                    }
                }
                break;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                ke keVar2 = (ke) message.obj;
                throw null;
            case 15:
                cdt cdt = (cdt) message.obj;
                if (this.k.containsKey(cdt.a)) {
                    cds cds9 = (cds) this.k.get(cdt.a);
                    if (cds9.g.contains(cdt) && !cds9.f) {
                        if (cds9.b.j()) {
                            cds9.g();
                            break;
                        } else {
                            cds9.d();
                            break;
                        }
                    }
                }
                break;
            case 16:
                cdt cdt2 = (cdt) message.obj;
                if (this.k.containsKey(cdt2.a)) {
                    cds cds10 = (cds) this.k.get(cdt2.a);
                    if (cds10.g.remove(cdt2)) {
                        cds10.i.n.removeMessages(15, cdt2);
                        cds10.i.n.removeMessages(16, cdt2);
                        cbg cbg = cdt2.b;
                        ArrayList arrayList = new ArrayList(cds10.a.size());
                        for (cde cde : cds10.a) {
                            if ((cde instanceof ccy) && (b2 = ((ccy) cde).b(cds10)) != null) {
                                int i3 = 0;
                                while (true) {
                                    if (i3 <= 0) {
                                        if (!a.k(b2[i3], cbg)) {
                                            i3++;
                                        } else if (i3 >= 0) {
                                            arrayList.add(cde);
                                        }
                                    }
                                }
                            }
                        }
                        int size = arrayList.size();
                        for (int i4 = 0; i4 < size; i4++) {
                            cde cde2 = (cde) arrayList.get(i4);
                            cds10.a.remove(cde2);
                            cde2.e(new ccx(cbg));
                        }
                        break;
                    }
                }
                break;
            case 17:
                k();
                break;
            case 18:
                cef cef = (cef) message.obj;
                if (cef.c != 0) {
                    cgc cgc = this.q;
                    if (cgc != null) {
                        List list = cgc.b;
                        if (cgc.a != cef.b || (list != null && list.size() >= cef.d)) {
                            this.n.removeMessages(17);
                            k();
                        } else {
                            cgc cgc2 = this.q;
                            cfw cfw = cef.a;
                            if (cgc2.b == null) {
                                cgc2.b = new ArrayList();
                            }
                            cgc2.b.add(cfw);
                        }
                    }
                    if (this.q == null) {
                        ArrayList arrayList2 = new ArrayList();
                        arrayList2.add(cef.a);
                        this.q = new cgc(cef.b, arrayList2);
                        Handler handler2 = this.n;
                        handler2.sendMessageDelayed(handler2.obtainMessage(17), cef.c);
                        break;
                    }
                } else {
                    l().a(new cgc(cef.b, Arrays.asList(new cfw[]{cef.a})));
                    break;
                }
                break;
            case 19:
                this.e = false;
                break;
            default:
                Log.w("GoogleApiManager", "Unknown message id: " + message.what);
                return false;
        }
        return true;
    }

    public final void i(byw byw, int i2, ccm ccm) {
        long j2;
        long j3;
        if (i2 != 0) {
            cdf cdf = ccm.e;
            cee cee = null;
            if (g()) {
                cgb cgb = cga.a().a;
                boolean z = true;
                if (cgb != null) {
                    if (cgb.b) {
                        boolean z2 = cgb.c;
                        cds b2 = b(cdf);
                        if (b2 != null) {
                            ccj ccj = b2.b;
                            if (ccj instanceof cex) {
                                cex cex = (cex) ccj;
                                if (cex.y() && !cex.k()) {
                                    cfc b3 = cee.b(b2, cex, i2);
                                    if (b3 != null) {
                                        b2.h++;
                                        z = b3.c;
                                    }
                                }
                            }
                        }
                        z = z2;
                    }
                }
                if (z) {
                    j2 = System.currentTimeMillis();
                } else {
                    j2 = 0;
                }
                if (z) {
                    j3 = SystemClock.elapsedRealtime();
                } else {
                    j3 = 0;
                }
                cee = new cee(this, i2, cdf, j2, j3);
            }
            if (cee != null) {
                Object obj = byw.a;
                Handler handler = this.n;
                Objects.requireNonNull(handler);
                ((cks) obj).d(new cdr(handler, 0), cee);
            }
        }
    }
}
