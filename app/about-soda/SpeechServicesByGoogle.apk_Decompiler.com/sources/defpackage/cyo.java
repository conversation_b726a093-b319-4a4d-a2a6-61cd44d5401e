package defpackage;

/* renamed from: cyo  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cyo implements hkn {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;
    private final /* synthetic */ int c;

    public /* synthetic */ cyo(Object obj, int i, int i2) {
        this.c = i2;
        this.b = obj;
        this.a = i;
    }

    /* JADX WARNING: type inference failed for: r3v0, types: [cvz, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v2, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v1, types: [cvz, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v6, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme a() {
        int i = this.c;
        if (i == 0) {
            Object obj = this.b;
            eix eix = (eix) obj;
            return czw.e(eix.c.c()).g(new fxy(obj, this.a, 1), eix.g);
        } else if (i != 1) {
            return ((fzr) this.b).d(this.a);
        } else {
            cyw cyw = (cyw) this.b;
            return ftd.L(cyw.b.c(), new cyf(cyw, this.a), cyw.d);
        }
    }
}
