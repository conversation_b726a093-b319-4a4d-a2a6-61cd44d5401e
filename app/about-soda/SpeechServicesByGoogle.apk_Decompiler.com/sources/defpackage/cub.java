package defpackage;

/* renamed from: cub  reason: default package */
/* compiled from: PG */
final class cub implements hls {
    final /* synthetic */ boolean a;
    final /* synthetic */ String b;
    final /* synthetic */ cuf c;

    public cub(cuf cuf, boolean z, String str) {
        this.a = z;
        this.b = str;
        this.c = cuf;
    }

    public final void a(Throwable th) {
        if (this.a) {
            ((czp) this.c.e.b()).i(this.b);
        }
    }

    public final /* bridge */ /* synthetic */ void b(Object obj) {
        crw crw = (crw) obj;
    }
}
