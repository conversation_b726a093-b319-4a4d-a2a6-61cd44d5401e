package defpackage;

import com.google.android.gms.common.api.internal.BasePendingResult;
import j$.util.Objects;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.Executor;
import java.util.concurrent.Future;

/* renamed from: cvs  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvs implements hkn {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    public final /* synthetic */ Object d;
    private final /* synthetic */ int e;

    public /* synthetic */ cvs(bmu bmu, hme hme, hme hme2, dxu dxu, int i) {
        this.e = i;
        this.a = bmu;
        this.c = hme;
        this.d = hme2;
        this.b = dxu;
    }

    /* JADX WARNING: type inference failed for: r2v5, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r5v4, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v11, types: [hco, hdc] */
    /* JADX WARNING: type inference failed for: r2v13, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v19, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v22, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v12, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v15, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v33, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v36, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v49, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v52, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v33, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v30, types: [java.lang.Object, hko] */
    /* JADX WARNING: type inference failed for: r3v30, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v36, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v67, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v37, types: [java.util.concurrent.Future, java.lang.Object] */
    public final hme a() {
        boolean z;
        hnf hnf;
        byte[] bArr;
        int i = 0;
        switch (this.e) {
            case 0:
                Object obj = this.b;
                Object obj2 = this.a;
                cvy cvy = (cvy) obj2;
                return cvy.q(ftd.J(new ctp(obj2, obj, 10), cvy.e), new cvd(obj2, (ctg) obj, this.c, this.d, 4));
            case 1:
                Object obj3 = this.d;
                Object obj4 = this.c;
                Object obj5 = this.b;
                Object obj6 = this.a;
                cvs cvs = new cvs(obj6, obj5, obj4, obj3, 0);
                cvy cvy2 = (cvy) obj6;
                return cvy2.l.q(cvs, cvy2.e);
            case 2:
                hme K = hfc.K(true);
                while (true) {
                    ? r2 = this.c;
                    Object obj7 = this.b;
                    if (i >= r2.size()) {
                        return ftd.K(K, new brg(this.a, 17), ((cwd) obj7).c);
                    }
                    K = ftd.L(K, new cvp(obj7, (htq) (ctj) r2.get(i), (htq) (ctl) hfc.S((Future) this.d.get(i)), 18), ((cwd) obj7).c);
                    i++;
                }
            case 3:
                Object obj8 = this.d;
                Object obj9 = this.a;
                bmu bmu = (bmu) obj9;
                ((hdc) dcs.a.l().j("com/google/android/libraries/micore/superpacks/base/TaskRunner", "execute", 147, "TaskRunner.java")).u("Task completed for: %s", bmu.q(obj8));
                Object obj10 = bmu.a;
                Object obj11 = this.b;
                synchronized (obj10) {
                    ((bmu) obj9).a.remove(obj8);
                }
                synchronized (obj11) {
                    hmr hmr = ((ddq) obj11).b;
                    if (hmr != null) {
                        hmr.m((Object) null);
                    }
                }
                return this.c;
            case 4:
                ((eez) this.b).f((String) this.d, (dhd) this.a);
                return this.c;
            case 5:
                Object obj12 = this.d;
                try {
                    Map map = (Map) ((grh) hfc.S(this.a)).e();
                    if (map != null) {
                        htk htk = (htk) obj12;
                        long j = ((jzw) htk.b).b;
                        for (Map.Entry entry : map.entrySet()) {
                            Integer num = (Integer) entry.getKey();
                            num.intValue();
                            long longValue = ((Long) entry.getValue()).longValue() - j;
                            if (!htk.b.B()) {
                                htk.u();
                            }
                            jzw jzw = (jzw) htk.b;
                            huv huv = jzw.v;
                            if (!huv.b) {
                                jzw.v = huv.a();
                            }
                            jzw.v.put(num, Long.valueOf(longValue));
                        }
                    }
                } catch (Exception e2) {
                    ((hby) ((hby) ((hby) djs.a.h()).i(e2)).j("com/google/android/libraries/performance/primes/metrics/startup/StartupMetricRecordingService", "setCustomTimestamps", 'v', "StartupMetricRecordingService.java")).r("Failed to get custom timestamps future");
                }
                Object obj13 = this.b;
                ffg a2 = dma.a();
                htk l = kbc.y.l();
                htk l2 = jzs.f.l();
                long leastSignificantBits = UUID.randomUUID().getLeastSignificantBits();
                if (!l2.b.B()) {
                    l2.u();
                }
                htq htq = l2.b;
                jzs jzs = (jzs) htq;
                jzs.a |= 1;
                jzs.b = leastSignificantBits;
                if (!htq.B()) {
                    l2.u();
                }
                htq htq2 = l2.b;
                jzs jzs2 = (jzs) htq2;
                jzs2.c = 2;
                jzs2.a = 2 | jzs2.a;
                if (!htq2.B()) {
                    l2.u();
                }
                jzs jzs3 = (jzs) l2.b;
                jzw jzw2 = (jzw) ((htk) obj12).r();
                jzw2.getClass();
                jzs3.e = jzw2;
                jzs3.a |= 16;
                if (!l.b.B()) {
                    l.u();
                }
                cxm cxm = ((dqf) obj13).c;
                ? r1 = this.c;
                kbc kbc = (kbc) l.b;
                jzs jzs4 = (jzs) l2.r();
                jzs4.getClass();
                kbc.l = jzs4;
                kbc.a |= 2048;
                a2.i((kbc) l.r());
                a2.d = (jzf) ((grh) hfc.S(r1)).e();
                a2.c = null;
                a2.h(true);
                return cxm.b(a2.e());
            case 6:
                hva hva = (hva) hfc.S(this.c);
                cab cab = (cab) hfc.S(this.d);
                Object obj14 = this.a;
                bmu bmu2 = (bmu) obj14;
                cab.e(((Integer) ((grm) ((dxv) bmu2.b).e).a).intValue());
                hbp k = ((dxu) this.b).b.iterator();
                while (k.hasNext()) {
                    Integer num2 = (Integer) k.next();
                    num2.intValue();
                    if (cab.c == null) {
                        cab.c = new ArrayList();
                    }
                    cab.c.add(num2);
                }
                cab.o = (cre) ((grm) ((dxv) bmu2.b).f).a;
                ccr b2 = cab.b();
                Objects.requireNonNull(b2);
                crm crm = new crm(b2, new cmp(b2, 10));
                crk crk = new crk(crm);
                synchronized (((BasePendingResult) b2).c) {
                    cgr.ae(!((BasePendingResult) b2).g, "Result has already been consumed.");
                    ccw ccw = ((BasePendingResult) b2).j;
                    cgr.ae(true, "Cannot set callbacks if then() has been called.");
                    synchronized (((BasePendingResult) b2).c) {
                        z = ((BasePendingResult) b2).h;
                    }
                    if (!z) {
                        if (((BasePendingResult) b2).n()) {
                            ((BasePendingResult) b2).d.a(crk, ((BasePendingResult) b2).i());
                        } else {
                            ((BasePendingResult) b2).e = crk;
                        }
                    }
                }
                return ftd.K(gpa.g(crm).h(new dun(3), hld.a).e(Throwable.class, new dun(4), hld.a), new cyg(obj14, 19), hld.a);
            case 7:
                String str = (String) hfc.S(this.c);
                String str2 = (String) hfc.S(this.d);
                Object obj15 = this.a;
                bmu bmu3 = (bmu) obj15;
                Object obj16 = ((grm) ((dxv) bmu3.b).d).a;
                dxu dxu = (dxu) this.b;
                jyo jyo = dxu.a;
                htk l3 = hwv.d.l();
                if (!l3.b.B()) {
                    l3.u();
                }
                hwv hwv = (hwv) l3.b;
                jyo.getClass();
                hwv.b = jyo;
                hwv.a = 5;
                hme K2 = hfc.K(l3.r());
                hme K3 = ftd.K(K2, new cyg(obj15, 18), hld.a);
                hme K4 = ftd.K(K2, new dun(5), hld.a);
                return ftd.ae(K3, K4).B(new cvs(bmu3, K4, K3, dxu, 6), hld.a);
            case 8:
                Object obj17 = this.b;
                fpn fpn = new fpn(obj17, 4);
                ? r12 = this.d;
                ? r22 = this.a;
                hme g = hke.g(this.c, fpn, hld.a);
                hme g2 = hke.g(g, r22, r12);
                return hke.g(g2, gof.d(new cwt(obj17, (Object) g, (Object) g2, 11, (byte[]) null)), hld.a);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return hke.g(this.c, gof.d(new cwt(this.b, this.a, this.d, 12, (byte[]) null)), hld.a);
            default:
                ? r0 = this.a;
                if (r0 != 0) {
                    hnf = (hnf) hfc.S(r0);
                } else {
                    hnf = null;
                }
                ? r13 = this.c;
                if (r13 != 0) {
                    bArr = (byte[]) hfc.S(r13);
                } else {
                    bArr = null;
                }
                if (hnf == null) {
                    return hfc.K((Object) null);
                }
                Object obj18 = this.d;
                Object obj19 = this.b;
                if (bArr == null) {
                    return dtw.g(((dtw) ((cxp) obj19).a).a.i((String) obj18, hnf));
                }
                cjt cjt = ((dtw) ((cxp) obj19).a).a;
                String str3 = (String) obj18;
                cks i2 = cjt.i(str3, hnf);
                fih fih = new fih(cjt, str3, bArr);
                Executor executor = ckv.a;
                ckw ckw = new ckw();
                ckw ckw2 = (ckw) i2;
                ckw2.f.f(new ckk(executor, fih, ckw));
                ckw2.g();
                return dtw.g(ckw);
        }
    }

    public /* synthetic */ cvs(bmu bmu, Object obj, ddq ddq, hme hme, int i) {
        this.e = i;
        this.a = bmu;
        this.d = obj;
        this.b = ddq;
        this.c = hme;
    }

    public /* synthetic */ cvs(cwd cwd, List list, List list2, Boolean bool, int i) {
        this.e = i;
        this.b = cwd;
        this.c = list;
        this.d = list2;
        this.a = bool;
    }

    public /* synthetic */ cvs(Object obj, hme hme, hko hko, Executor executor, int i) {
        this.e = i;
        this.b = obj;
        this.c = hme;
        this.a = hko;
        this.d = executor;
    }

    public /* synthetic */ cvs(Object obj, Object obj2, Object obj3, hme hme, int i) {
        this.e = i;
        this.b = obj;
        this.d = obj2;
        this.a = obj3;
        this.c = hme;
    }

    public /* synthetic */ cvs(Object obj, Object obj2, Object obj3, Object obj4, int i) {
        this.e = i;
        this.a = obj;
        this.b = obj2;
        this.c = obj3;
        this.d = obj4;
    }
}
