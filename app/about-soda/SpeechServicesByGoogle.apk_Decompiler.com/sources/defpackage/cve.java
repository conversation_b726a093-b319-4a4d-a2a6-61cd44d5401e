package defpackage;

import j$.util.Collection;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;

/* renamed from: cve  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cve implements gqx {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    private final /* synthetic */ int d;

    public /* synthetic */ cve(byr byr, hme hme, String str, int i) {
        this.d = i;
        this.c = byr;
        this.a = hme;
        this.b = str;
    }

    /* JADX WARNING: type inference failed for: r0v6, types: [java.util.concurrent.Future, java.lang.Object] */
    public final Object apply(Object obj) {
        boolean z;
        int i = this.d;
        if (i != 0) {
            if (i == 1) {
                ? r0 = this.a;
                ext ext = (ext) obj;
                gxv gxv = haq.a;
                try {
                    gxv = (gxv) hfc.S(r0);
                } catch (ExecutionException e) {
                    ((hdc) ((hdc) ((hdc) byr.a.h()).i(e)).j("com/google/android/apps/speech/tts/googletts/settings/asr/dataservice/impl/LanguagePackSettingsDataServiceImpl", "getSupportedLocalesSource", 114, "LanguagePackSettingsDataServiceImpl.java")).r("Language names could not be read properly! The settings page may not have readable language names.");
                }
                Object obj2 = this.b;
                Object obj3 = this.c;
                gxv gxv2 = (gxv) Collection.EL.stream(fbi.e(gyo.n(ext.a))).filter(new bod(ext, 8)).filter(new boa(13)).collect(gvx.a(new bpf(13), new ezk((Object) gxv, (Object) ext, 1)));
                ((byr) obj3).f.a(dwv.ax.b("timestamp", (String) obj2).b());
                return gxv2;
            } else if (i == 2) {
                String str = (String) obj;
                return cyd.e(((cyd) this.a).a.a(), "mdd_" + ((cyc) this.c).a + "_" + str, (int[]) this.b, str);
            } else if (i != 3) {
                int max = Math.max(((ixj) ((kjd) this.a).a).j(gba.a) + 1, Math.max(((fyo) this.b).b, ((ixj) this.c).m()));
                if (max > 0) {
                    z = true;
                } else {
                    z = false;
                }
                fvf.aF(z);
                htk l = fyo.d.l();
                if (!l.b.B()) {
                    l.u();
                }
                fyo fyo = (fyo) l.b;
                fyo.a = 1 | fyo.a;
                fyo.b = max;
                return (fyo) l.r();
            } else {
                crw crw = (crw) obj;
                Object obj4 = this.c;
                if (crw == null) {
                    ((hdc) ((hdc) ezo.a.h()).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackSourceImpl", "getPackage", 393, "ZipfileLanguagePackSourceImpl.java")).u("LanguagePack %s is not known by MDD. Caller probably made an error.", obj4);
                    gqd gqd = gqd.a;
                    return new exr((exo) obj4, gqd, gqd);
                }
                Object obj5 = this.b;
                Object obj6 = this.a;
                ((hdc) ((hdc) ezo.a.f()).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackSourceImpl", "getPackage", 398, "ZipfileLanguagePackSourceImpl.java")).u("MDD.getFileGroup(%s) complete", obj5);
                ezo ezo = (ezo) obj6;
                ezo.h.a(dwv.X);
                return ezo.j((exo) obj4, crw);
            }
        } else if (((Boolean) obj).booleanValue()) {
            ((AtomicInteger) this.b).getAndIncrement();
            return null;
        } else {
            Object obj7 = this.c;
            ((cyk) ((dmd) this.a).a).d(1036);
            cyh.h("%s: Unsubscribe from file %s failed!", "ExpirationHandler", obj7);
            return null;
        }
    }

    public /* synthetic */ cve(ezo ezo, exo exo, String str, int i) {
        this.d = i;
        this.a = ezo;
        this.c = exo;
        this.b = str;
    }

    public /* synthetic */ cve(Object obj, Object obj2, Object obj3, int i) {
        this.d = i;
        this.a = obj;
        this.b = obj2;
        this.c = obj3;
    }
}
