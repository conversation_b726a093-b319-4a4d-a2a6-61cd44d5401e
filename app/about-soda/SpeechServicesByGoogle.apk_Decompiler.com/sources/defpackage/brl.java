package defpackage;

import java.nio.ByteBuffer;

/* renamed from: brl  reason: default package */
/* compiled from: PG */
final class brl implements brj {
    public final void a() {
        throw null;
    }

    public final void b(iff iff) {
        throw new IllegalStateException("No decoder callback is set, cannot call stop callback");
    }

    public final boolean c(Byte<PERSON>uffer byteBuffer) {
        throw new IllegalStateException("No decoder callback is set, cannot call audioAvailable callback");
    }

    public final void d() {
        throw new IllegalStateException("No decoder callback is set, cannot call start callback");
    }
}
