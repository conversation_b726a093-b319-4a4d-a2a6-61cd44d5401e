package defpackage;

import java.util.concurrent.ThreadFactory;

/* renamed from: cqr  reason: default package */
/* compiled from: PG */
public final class cqr implements ThreadFactory {
    public final cqp a;
    private final ThreadFactory b;

    public cqr(ThreadFactory threadFactory, cqp cqp) {
        this.b = threadFactory;
        this.a = cqp;
    }

    public final Thread newThread(Runnable runnable) {
        return this.b.newThread(new ckm((Object) this, (Object) runnable, 17));
    }
}
