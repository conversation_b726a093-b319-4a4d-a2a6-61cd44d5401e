package defpackage;

import j$.util.Optional;

/* renamed from: epd  reason: default package */
/* compiled from: PG */
public final class epd extends jnv implements jna {
    final /* synthetic */ Object a;
    final /* synthetic */ Object b;
    final /* synthetic */ Object c;
    final /* synthetic */ Object d;
    private final /* synthetic */ int e;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public epd(eoz eoz, elj elj, eeh eeh, jna jna, int i) {
        super(1);
        this.e = i;
        this.a = eoz;
        this.c = elj;
        this.d = eeh;
        this.b = jna;
    }

    /* JADX WARNING: type inference failed for: r1v7, types: [java.lang.Object, jqs] */
    /* JADX WARNING: type inference failed for: r2v11, types: [eeh, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r4v2, types: [java.lang.Object, jna] */
    public final /* synthetic */ Object a(Object obj) {
        Optional optional;
        int i = this.e;
        if (i == 0) {
            enl enl = (enl) obj;
            jnu.e(enl, "source");
            for (jna a2 : ((epe) this.a).c) {
                a2.a(new epf(((epp) this.b).a, (epg) this.c, enl.b()));
            }
            hme b2 = ((epe) this.a).b.b(gof.c(new cpw(enl, 15)), ((epe) this.a).a);
            jnu.d(b2, "submitAsync(...)");
            Object obj2 = this.a;
            Object obj3 = this.d;
            hme b3 = enl.d().b();
            Object obj4 = this.b;
            ehg ehg = ((epb) obj3).b;
            jnu.e(ehg, "clientInfo");
            jnu.e(b2, "startResult");
            ejn ejn = ((epp) obj4).b;
            cyw cyw = ((epe) obj2).e;
            ((emd) cyw.d).f(ehg, b2, ejn.b);
            cyw.v("AUDIO", ehg, b3, b2);
            job.S(cyw.c, (jlv) null, (jqt) null, new ely(cyw, b3, b2, (jlr) null, 1, (byte[]) null), 3);
            return b2;
        } else if (i != 1) {
            Void voidR = (Void) obj;
            jnu.d(Optional.empty(), "empty(...)");
            ((Optional) this.b).isPresent();
            htk l = csf.d.l();
            jnu.d(l, "newBuilder(...)");
            jnu.e(l, "builder");
            Object obj5 = ((Optional) this.b).get();
            if (!l.b.B()) {
                l.u();
            }
            csf csf = (csf) l.b;
            csf.c = ((csd) obj5).d;
            csf.a |= 2;
            cse cse = cse.BLOCK_DOWNLOAD_LOWER_THRESHOLD;
            jnu.e(cse, "value");
            if (!l.b.B()) {
                l.u();
            }
            csf csf2 = (csf) l.b;
            csf2.b = cse.d;
            csf2.a |= 1;
            htq o = l.r();
            jnu.d(o, "build(...)");
            Optional of = Optional.of((csf) o);
            Object obj6 = this.a;
            Object obj7 = this.d;
            exk exk = new exk(of);
            grh grh = (grh) this.c;
            if (grh.f()) {
                optional = Optional.of(grh.b());
            } else {
                optional = Optional.empty();
            }
            return ((exx) obj6).b.i((exo) obj7, exk, optional);
        } else {
            eam eam = (eam) obj;
            jnu.e(eam, "it");
            ((eoz) this.a).o(new egr((elj) this.c, (eeh) this.d, eam, (jna) this.b, (jlr) null, 3));
            return jkd.a;
        }
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public epd(epe epe, epp epp, epg epg, epb epb, int i) {
        super(1);
        this.e = i;
        this.a = epe;
        this.b = epp;
        this.c = epg;
        this.d = epb;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public epd(exx exx, exo exo, Optional optional, grh grh, int i) {
        super(1);
        this.e = i;
        this.a = exx;
        this.d = exo;
        this.b = optional;
        this.c = grh;
    }
}
