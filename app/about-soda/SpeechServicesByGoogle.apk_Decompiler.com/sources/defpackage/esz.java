package defpackage;

/* renamed from: esz  reason: default package */
/* compiled from: PG */
public final class esz extends htq implements hvb {
    public static final esz d;
    private static volatile hvh e;
    public int a;
    public ebn b;
    public eac c;

    static {
        esz esz = new esz();
        d = esz;
        htq.z(esz.class, esz);
    }

    private esz() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(d, "\u0004\u0002\u0000\u0001\u0001\u0002\u0002\u0000\u0000\u0000\u0001ဉ\u0000\u0002ဉ\u0001", new Object[]{"a", "b", "c"});
        } else if (i2 == 3) {
            return new esz();
        } else {
            if (i2 == 4) {
                return new htk((htq) d);
            }
            if (i2 == 5) {
                return d;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = e;
            if (hvh == null) {
                synchronized (esz.class) {
                    hvh = e;
                    if (hvh == null) {
                        hvh = new htl(d);
                        e = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
