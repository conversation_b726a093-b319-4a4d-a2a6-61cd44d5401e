package defpackage;

/* renamed from: cyb  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cyb implements grj {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ cyb(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r0v16, types: [java.util.Collection, java.lang.Object] */
    /* JADX WARNING: Can't fix incorrect switch cases order */
    /* JADX WARNING: Code restructure failed: missing block: B:116:0x0176, code lost:
        if (r13.canBeSpatialized((android.media.AudioAttributes) r0.b.a, r1.build()) != false) goto L_0x0178;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean a(java.lang.Object r13) {
        /*
            r12 = this;
            int r0 = r12.b
            if (r0 == 0) goto L_0x017e
            r1 = 4
            r2 = 12
            r3 = 3
            r4 = 2
            r5 = 0
            r6 = 1
            if (r0 == r6) goto L_0x006c
            if (r0 == r4) goto L_0x0061
            if (r0 == r3) goto L_0x0037
            if (r0 == r1) goto L_0x0025
            java.util.zip.ZipEntry r13 = (java.util.zip.ZipEntry) r13
            java.lang.Object r0 = r12.a
            j$.util.stream.Stream r0 = j$.util.Collection.EL.stream(r0)
            bod r1 = new bod
            r1.<init>(r13, r2)
            boolean r13 = r0.anyMatch(r1)
            return r13
        L_0x0025:
            java.lang.Exception r13 = (java.lang.Exception) r13
            java.lang.Object r0 = r12.a
            monitor-enter(r0)
            r13 = r0
            dgl r13 = (defpackage.dgl) r13     // Catch:{ all -> 0x0034 }
            hmr r13 = r13.f     // Catch:{ all -> 0x0034 }
            monitor-exit(r0)     // Catch:{ all -> 0x0034 }
            if (r13 != 0) goto L_0x0033
            return r6
        L_0x0033:
            return r5
        L_0x0034:
            r13 = move-exception
            monitor-exit(r0)     // Catch:{ all -> 0x0034 }
            throw r13
        L_0x0037:
            ctb r13 = (defpackage.ctb) r13
            int r0 = defpackage.cyn.d
            java.lang.Object r0 = r12.a
            ctb r0 = (defpackage.ctb) r0
            ctg r1 = r0.b
            if (r1 != 0) goto L_0x0045
            ctg r1 = defpackage.ctg.g
        L_0x0045:
            ctg r2 = r13.b
            if (r2 != 0) goto L_0x004b
            ctg r2 = defpackage.ctg.g
        L_0x004b:
            boolean r1 = r1.equals(r2)
            if (r1 == 0) goto L_0x0060
            int r1 = r0.e
            int r2 = r13.e
            if (r1 != r2) goto L_0x0060
            long r0 = r0.c
            long r2 = r13.c
            int r13 = (r0 > r2 ? 1 : (r0 == r2 ? 0 : -1))
            if (r13 != 0) goto L_0x0060
            return r6
        L_0x0060:
            return r5
        L_0x0061:
            csx r13 = (defpackage.csx) r13
            java.lang.Object r0 = r12.a
            java.lang.String r0 = (java.lang.String) r0
            boolean r13 = defpackage.cyd.h(r13, r0)
            return r13
        L_0x006c:
            java.lang.Object r0 = r12.a
            r7 = r0
            apd r7 = (defpackage.apd) r7
            java.lang.Object r7 = r7.b
            adv r13 = (defpackage.adv) r13
            monitor-enter(r7)
            r8 = r0
            apd r8 = (defpackage.apd) r8     // Catch:{ all -> 0x017b }
            aov r8 = r8.d     // Catch:{ all -> 0x017b }
            boolean r8 = r8.p     // Catch:{ all -> 0x017b }
            if (r8 == 0) goto L_0x0178
            r8 = r0
            apd r8 = (defpackage.apd) r8     // Catch:{ all -> 0x017b }
            boolean r8 = r8.c     // Catch:{ all -> 0x017b }
            if (r8 != 0) goto L_0x0178
            int r8 = r13.B     // Catch:{ all -> 0x017b }
            if (r8 <= r4) goto L_0x0178
            java.lang.String r8 = r13.n     // Catch:{ all -> 0x017b }
            r9 = -1
            r10 = 32
            if (r8 != 0) goto L_0x0092
            goto L_0x00dd
        L_0x0092:
            int r11 = r8.hashCode()     // Catch:{ all -> 0x017b }
            switch(r11) {
                case -2123537834: goto L_0x00b8;
                case 187078296: goto L_0x00ae;
                case 187078297: goto L_0x00a4;
                case 1504578661: goto L_0x009a;
                default: goto L_0x0099;
            }
        L_0x0099:
            goto L_0x00c2
        L_0x009a:
            java.lang.String r11 = "audio/eac3"
            boolean r8 = r8.equals(r11)
            if (r8 == 0) goto L_0x00c2
            r8 = r6
            goto L_0x00c3
        L_0x00a4:
            java.lang.String r11 = "audio/ac4"
            boolean r8 = r8.equals(r11)
            if (r8 == 0) goto L_0x00c2
            r8 = r3
            goto L_0x00c3
        L_0x00ae:
            java.lang.String r11 = "audio/ac3"
            boolean r8 = r8.equals(r11)
            if (r8 == 0) goto L_0x00c2
            r8 = r5
            goto L_0x00c3
        L_0x00b8:
            java.lang.String r11 = "audio/eac3-joc"
            boolean r8 = r8.equals(r11)
            if (r8 == 0) goto L_0x00c2
            r8 = r4
            goto L_0x00c3
        L_0x00c2:
            r8 = r9
        L_0x00c3:
            if (r8 == 0) goto L_0x00cc
            if (r8 == r6) goto L_0x00cc
            if (r8 == r4) goto L_0x00cc
            if (r8 == r3) goto L_0x00cc
            goto L_0x00dd
        L_0x00cc:
            int r3 = defpackage.agh.a     // Catch:{ all -> 0x017b }
            if (r3 < r10) goto L_0x0178
            r3 = r0
            apd r3 = (defpackage.apd) r3     // Catch:{ all -> 0x017b }
            aoy r3 = r3.e     // Catch:{ all -> 0x017b }
            if (r3 == 0) goto L_0x0178
            boolean r3 = r3.b     // Catch:{ all -> 0x017b }
            if (r3 != 0) goto L_0x00dd
            goto L_0x0178
        L_0x00dd:
            int r3 = defpackage.agh.a     // Catch:{ all -> 0x017b }
            if (r3 < r10) goto L_0x0179
            r3 = r0
            apd r3 = (defpackage.apd) r3     // Catch:{ all -> 0x017b }
            aoy r3 = r3.e     // Catch:{ all -> 0x017b }
            if (r3 == 0) goto L_0x0179
            boolean r8 = r3.b     // Catch:{ all -> 0x017b }
            if (r8 == 0) goto L_0x0179
            android.media.Spatializer r3 = r3.a     // Catch:{ all -> 0x017b }
            boolean r3 = defpackage.xt$$ExternalSyntheticApiModelOutline6.m((android.media.Spatializer) r3)     // Catch:{ all -> 0x017b }
            if (r3 == 0) goto L_0x0179
            r3 = r0
            apd r3 = (defpackage.apd) r3     // Catch:{ all -> 0x017b }
            aoy r3 = r3.e     // Catch:{ all -> 0x017b }
            android.media.Spatializer r3 = r3.a     // Catch:{ all -> 0x017b }
            boolean r3 = r3.isEnabled()     // Catch:{ all -> 0x017b }
            if (r3 == 0) goto L_0x0179
            r3 = r0
            apd r3 = (defpackage.apd) r3     // Catch:{ all -> 0x017b }
            aoy r3 = r3.e     // Catch:{ all -> 0x017b }
            apd r0 = (defpackage.apd) r0     // Catch:{ all -> 0x017b }
            adj r0 = r0.f     // Catch:{ all -> 0x017b }
            java.lang.String r8 = "audio/eac3-joc"
            java.lang.String r11 = r13.n     // Catch:{ all -> 0x017b }
            boolean r8 = r8.equals(r11)     // Catch:{ all -> 0x017b }
            if (r8 == 0) goto L_0x011c
            int r8 = r13.B     // Catch:{ all -> 0x017b }
            r11 = 16
            if (r8 != r11) goto L_0x011c
            r8 = r2
            goto L_0x011e
        L_0x011c:
            int r8 = r13.B     // Catch:{ all -> 0x017b }
        L_0x011e:
            r11 = 6396(0x18fc, float:8.963E-42)
            switch(r8) {
                case 1: goto L_0x0143;
                case 2: goto L_0x0142;
                case 3: goto L_0x013f;
                case 4: goto L_0x013c;
                case 5: goto L_0x0139;
                case 6: goto L_0x0136;
                case 7: goto L_0x0133;
                case 8: goto L_0x0131;
                case 9: goto L_0x0123;
                case 10: goto L_0x0129;
                case 11: goto L_0x0123;
                case 12: goto L_0x0125;
                default: goto L_0x0123;
            }     // Catch:{ all -> 0x017b }
        L_0x0123:
            r1 = r5
            goto L_0x0143
        L_0x0125:
            r1 = 743676(0xb58fc, float:1.042112E-39)
            goto L_0x0143
        L_0x0129:
            int r1 = defpackage.agh.a     // Catch:{ all -> 0x017b }
            if (r1 < r10) goto L_0x0131
            r1 = 737532(0xb40fc, float:1.033502E-39)
            goto L_0x0143
        L_0x0131:
            r1 = r11
            goto L_0x0143
        L_0x0133:
            r1 = 1276(0x4fc, float:1.788E-42)
            goto L_0x0143
        L_0x0136:
            r1 = 252(0xfc, float:3.53E-43)
            goto L_0x0143
        L_0x0139:
            r1 = 220(0xdc, float:3.08E-43)
            goto L_0x0143
        L_0x013c:
            r1 = 204(0xcc, float:2.86E-43)
            goto L_0x0143
        L_0x013f:
            r1 = 28
            goto L_0x0143
        L_0x0142:
            r1 = r2
        L_0x0143:
            if (r1 != 0) goto L_0x0146
            goto L_0x0179
        L_0x0146:
            android.media.AudioFormat$Builder r2 = new android.media.AudioFormat$Builder     // Catch:{ all -> 0x017b }
            r2.<init>()     // Catch:{ all -> 0x017b }
            android.media.AudioFormat$Builder r2 = r2.setEncoding(r4)     // Catch:{ all -> 0x017b }
            android.media.AudioFormat$Builder r1 = r2.setChannelMask(r1)     // Catch:{ all -> 0x017b }
            int r13 = r13.C     // Catch:{ all -> 0x017b }
            if (r13 == r9) goto L_0x015a
            r1.setSampleRate(r13)     // Catch:{ all -> 0x017b }
        L_0x015a:
            android.media.Spatializer r13 = r3.a     // Catch:{ all -> 0x017b }
            byw r2 = r0.b     // Catch:{ all -> 0x017b }
            if (r2 != 0) goto L_0x0168
            byw r2 = new byw     // Catch:{ all -> 0x017b }
            r3 = 0
            r2.<init>((char[]) r3, (byte[]) r3, (byte[]) r3)     // Catch:{ all -> 0x017b }
            r0.b = r2     // Catch:{ all -> 0x017b }
        L_0x0168:
            byw r0 = r0.b     // Catch:{ all -> 0x017b }
            java.lang.Object r0 = r0.a     // Catch:{ all -> 0x017b }
            android.media.AudioFormat r1 = r1.build()     // Catch:{ all -> 0x017b }
            android.media.AudioAttributes r0 = (android.media.AudioAttributes) r0     // Catch:{ all -> 0x017b }
            boolean r13 = r13.canBeSpatialized(r0, r1)     // Catch:{ all -> 0x017b }
            if (r13 == 0) goto L_0x0179
        L_0x0178:
            r5 = r6
        L_0x0179:
            monitor-exit(r7)     // Catch:{ all -> 0x017b }
            return r5
        L_0x017b:
            r13 = move-exception
            monitor-exit(r7)     // Catch:{ all -> 0x017b }
            throw r13
        L_0x017e:
            cxg r13 = (defpackage.cxg) r13
            csx r13 = r13.b
            java.lang.Object r0 = r12.a
            java.lang.String r0 = (java.lang.String) r0
            boolean r13 = defpackage.cyd.h(r13, r0)
            return r13
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cyb.a(java.lang.Object):boolean");
    }
}
