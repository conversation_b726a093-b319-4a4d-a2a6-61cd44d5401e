package defpackage;

import android.content.Context;
import android.net.Uri;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.Executor;

/* renamed from: cxx  reason: default package */
/* compiled from: PG */
public final class cxx {
    public final Context a;
    public final gsb b;
    public final czs c;
    public final grh d;
    public final cyi e;
    public final Executor f;
    final HashMap g = new HashMap();
    public final cyw h;
    public final kjd i;

    public cxx(Context context, gsb gsb, kjd kjd, czs czs, grh grh, cyi cyi, Executor executor) {
        this.a = context;
        this.b = gsb;
        this.i = kjd;
        this.c = czs;
        this.d = grh;
        this.e = cyi;
        this.f = executor;
        this.h = cyw.i(executor);
    }

    public final hme a(String str, Uri uri) {
        if (!cqh.i()) {
            return hfc.K(grh.g((hme) this.g.get(uri)));
        }
        return this.h.g(str);
    }

    public final hme b(String str, Uri uri) {
        if (cqh.i()) {
            return this.h.h(str);
        }
        hme hme = (hme) this.g.remove(uri);
        if (hme != null) {
            return hme;
        }
        return hma.a;
    }

    public final hme c(String str, ctg ctg, int i2, long j, String str2, Uri uri, String str3, int i3, csz csz, cxw cxw, int i4, List list, hse hse) {
        String str4 = str;
        Uri uri2 = uri;
        cxu cxu = r0;
        hme a2 = a(str4, uri2);
        cxu cxu2 = new cxu(this, str4, uri2, cxw, ctg, i2, j, str2, str3, i3, csz, i4, list, hse);
        return ftd.L(a2, cxu, this.f);
    }

    public final void d(String str, Uri uri) {
        ftd.L(a(str, uri), new cwt((Object) this, (Object) uri, (Object) str, 6), this.f);
    }
}
