package defpackage;

/* renamed from: crk  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class crk implements ccv {
    public final /* synthetic */ crm a;

    public /* synthetic */ crk(crm crm) {
        this.a = crm;
    }

    public final void a(ccu ccu) {
        int i = ccu.a().f;
        crm crm = this.a;
        if (i == 16) {
            crm.cancel(false);
        } else if (ccu.a().b()) {
            crm.m(ccu);
        } else if (ccu.a().h != null) {
            crm.n(new cct(ccu.a()));
        } else {
            crm.n(new cck(ccu.a()));
        }
    }
}
