package defpackage;

import android.support.v7.widget.GridLayoutManager;
import java.util.HashSet;

/* renamed from: bln  reason: default package */
/* compiled from: PG */
final class bln extends GridLayoutManager {
    final /* synthetic */ blp H;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bln(blp blp, int i, int i2, boolean z) {
        super(i, i2, z);
        this.H = blp;
    }

    public final void p(la laVar) {
        super.p(laVar);
        for (Runnable run : new HashSet(this.H.b)) {
            run.run();
        }
    }
}
