<?xml version="1.0" encoding="utf-8"?>
<com.android.car.ui.FocusArea xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:layout_width="match_parent" android:layout_height="@dimen/car_ui_toolbar_first_row_height">
    <androidx.constraintlayout.widget.ConstraintLayout android:id="@+id/car_ui_toolbar_background" android:tag="carUiToolbar" android:layout_width="match_parent" android:layout_height="match_parent" style="@style/Widget.CarUi.Toolbar.Container">
        <androidx.constraintlayout.widget.Guideline android:orientation="vertical" android:id="@+id/car_ui_toolbar_start_guideline" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintGuide_begin="@dimen/car_ui_toolbar_start_inset"/>
        <androidx.constraintlayout.widget.Guideline android:orientation="horizontal" android:id="@+id/car_ui_toolbar_top_guideline" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintGuide_begin="@dimen/car_ui_toolbar_top_inset"/>
        <androidx.constraintlayout.widget.Guideline android:orientation="vertical" android:id="@+id/car_ui_toolbar_end_guideline" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintGuide_end="@dimen/car_ui_toolbar_end_inset"/>
        <androidx.constraintlayout.widget.Guideline android:orientation="horizontal" android:id="@+id/car_ui_toolbar_bottom_guideline" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintGuide_end="@dimen/car_ui_toolbar_bottom_inset"/>
        <FrameLayout android:id="@+id/car_ui_toolbar_nav_icon_container" android:layout_width="@dimen/car_ui_toolbar_margin" android:layout_height="0dp" app:layout_constraintBottom_toTopOf="@+id/car_ui_toolbar_bottom_guideline" app:layout_constraintHorizontal_bias="0" app:layout_constraintStart_toEndOf="@+id/car_ui_toolbar_start_guideline" app:layout_constraintTop_toTopOf="@+id/car_ui_toolbar_top_guideline" style="@style/Widget.CarUi.Toolbar.NavIconContainer">
            <ImageView android:layout_gravity="center" android:id="@+id/car_ui_toolbar_nav_icon" android:layout_width="@dimen/car_ui_toolbar_nav_icon_size" android:layout_height="@dimen/car_ui_toolbar_nav_icon_size" android:scaleType="fitXY" style="@style/Widget.CarUi.Toolbar.NavIcon"/>
            <ImageView android:layout_gravity="center" android:id="@+id/car_ui_toolbar_logo" android:layout_width="@dimen/car_ui_toolbar_logo_size" android:layout_height="@dimen/car_ui_toolbar_logo_size" android:scaleType="fitXY"/>
        </FrameLayout>
        <FrameLayout android:id="@+id/car_ui_toolbar_title_logo_container" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintBottom_toTopOf="@+id/car_ui_toolbar_bottom_guideline" app:layout_constraintStart_toEndOf="@+id/car_ui_toolbar_nav_icon_container" app:layout_constraintTop_toTopOf="@+id/car_ui_toolbar_top_guideline" style="@style/Widget.CarUi.Toolbar.LogoContainer">
            <ImageView android:layout_gravity="center" android:id="@+id/car_ui_toolbar_title_logo" android:layout_width="@dimen/car_ui_toolbar_logo_size" android:layout_height="@dimen/car_ui_toolbar_logo_size" android:scaleType="fitXY" style="@style/Widget.CarUi.Toolbar.Logo"/>
        </FrameLayout>
        <LinearLayout android:orientation="vertical" android:id="@+id/car_ui_toolbar_title_container" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_marginStart="@dimen/car_ui_toolbar_title_margin_start" app:layout_constraintBottom_toTopOf="@+id/car_ui_toolbar_bottom_guideline" app:layout_constraintEnd_toStartOf="@+id/car_ui_toolbar_menu_items_container" app:layout_constraintStart_toEndOf="@+id/car_ui_toolbar_title_logo_container" app:layout_constraintTop_toTopOf="@+id/car_ui_toolbar_top_guideline" app:layout_goneMarginStart="@dimen/car_ui_toolbar_title_no_logo_margin_start">
            <TextView android:id="@+id/car_ui_toolbar_title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:singleLine="true" style="@style/Widget.CarUi.Toolbar.Title"/>
            <TextView android:id="@+id/car_ui_toolbar_subtitle" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" style="@style/Widget.CarUi.Toolbar.Subtitle"/>
        </LinearLayout>
        <com.android.car.ui.toolbar.TabLayout android:id="@+id/car_ui_toolbar_tabs" android:layout_width="wrap_content" android:layout_height="0dp" app:layout_constraintBottom_toTopOf="@+id/car_ui_toolbar_bottom_guideline" app:layout_constraintEnd_toStartOf="@+id/car_ui_toolbar_menu_items_container" app:layout_constraintHorizontal_bias="0" app:layout_constraintStart_toEndOf="@+id/car_ui_toolbar_title_logo_container" app:layout_constraintTop_toTopOf="@+id/car_ui_toolbar_top_guideline"/>
        <LinearLayout android:orientation="horizontal" android:id="@+id/car_ui_toolbar_menu_items_container" android:layout_width="wrap_content" android:layout_height="0dp" app:layout_constraintBottom_toTopOf="@+id/car_ui_toolbar_bottom_guideline" app:layout_constraintEnd_toStartOf="@+id/car_ui_toolbar_end_guideline" app:layout_constraintTop_toTopOf="@+id/car_ui_toolbar_top_guideline" style="@style/Widget.CarUi.Toolbar.MenuItem.Container"/>
        <FrameLayout android:id="@+id/car_ui_toolbar_search_view_container" android:layout_width="0dp" android:layout_height="@dimen/car_ui_toolbar_search_height" app:layout_constraintBottom_toTopOf="@+id/car_ui_toolbar_bottom_guideline" app:layout_constraintEnd_toStartOf="@+id/car_ui_toolbar_menu_items_container" app:layout_constraintStart_toEndOf="@+id/car_ui_toolbar_nav_icon_container" app:layout_constraintTop_toTopOf="@+id/car_ui_toolbar_top_guideline"/>
        <View android:id="@+id/car_ui_toolbar_row_separator" android:layout_width="match_parent" android:layout_height="@dimen/car_ui_toolbar_separator_height" app:layout_constraintBottom_toTopOf="@+id/car_ui_toolbar_bottom_guideline" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" style="@style/Widget.CarUi.Toolbar.SeparatorView"/>
        <ProgressBar android:id="@+id/car_ui_toolbar_progress_bar" android:visibility="gone" android:layout_width="match_parent" android:layout_height="wrap_content" android:indeterminate="true" app:layout_constraintBottom_toTopOf="@+id/car_ui_toolbar_row_separator" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" style="@style/Widget.CarUi.Toolbar.ProgressBar"/>
        <View android:id="@+id/car_ui_toolbar_bottom_styleable" android:layout_width="match_parent" android:layout_height="@dimen/car_ui_toolbar_bottom_view_height" app:layout_constraintBottom_toTopOf="@+id/car_ui_toolbar_progress_bar" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" style="@style/Widget.CarUi.Toolbar.BottomView"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.android.car.ui.FocusArea>
