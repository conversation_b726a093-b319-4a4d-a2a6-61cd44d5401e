package defpackage;

import android.os.SystemClock;
import java.util.concurrent.Callable;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

/* renamed from: cox  reason: default package */
/* compiled from: PG */
final class cox extends hka implements Runnable, hmg {
    private Callable a;
    private final long b;

    public cox(Callable callable, long j) {
        this.a = callable;
        this.b = j;
    }

    public final /* synthetic */ int compareTo(Object obj) {
        return coy.a(this, (Delayed) obj);
    }

    public final long getDelay(TimeUnit timeUnit) {
        return Math.max(0, TimeUnit.MILLISECONDS.convert(this.b - SystemClock.elapsedRealtime(), timeUnit));
    }

    public final void run() {
        if (!isDone()) {
            try {
                Callable callable = this.a;
                this.a = null;
                m(callable.call());
            } catch (Throwable th) {
                n(th);
                throw new RuntimeException(th);
            }
        }
    }
}
