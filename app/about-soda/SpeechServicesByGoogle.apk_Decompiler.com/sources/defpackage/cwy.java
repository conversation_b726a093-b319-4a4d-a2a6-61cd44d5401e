package defpackage;

import android.net.Uri;
import java.util.List;

/* renamed from: cwy  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwy implements hko {
    public final /* synthetic */ ctg a;
    public final /* synthetic */ Uri b;
    public final /* synthetic */ ctj c;
    public final /* synthetic */ String d;
    public final /* synthetic */ csx e;
    public final /* synthetic */ csv f;
    public final /* synthetic */ csy g;
    public final /* synthetic */ csz h;
    public final /* synthetic */ int i;
    public final /* synthetic */ List j;
    public final /* synthetic */ hse k;
    public final /* synthetic */ dbw l;

    public /* synthetic */ cwy(dbw dbw, ctg ctg, Uri uri, ctj ctj, String str, csx csx, csv csv, csy csy, csz csz, int i2, List list, hse hse) {
        this.l = dbw;
        this.a = ctg;
        this.b = uri;
        this.c = ctj;
        this.d = str;
        this.e = csx;
        this.f = csv;
        this.g = csy;
        this.h = csz;
        this.i = i2;
        this.j = list;
        this.k = hse;
    }

    public final hme a(Object obj) {
        grh grh = (grh) obj;
        boolean f2 = grh.f();
        dbw dbw = this.l;
        ctg ctg = this.a;
        if (f2) {
            dbw.p(ctg, this.b);
            return (hme) grh.b();
        }
        hse hse = this.k;
        List list = this.j;
        int i2 = this.i;
        csz csz = this.h;
        csy csy = this.g;
        csv csv = this.f;
        csx csx = this.e;
        return dbw.k(this.c, this.d, csx.e, csx.r, csx.s, ctg, csv, csy, csz, i2, list, hse);
    }
}
