package defpackage;

import java.util.concurrent.atomic.AtomicReference;

/* renamed from: ekw  reason: default package */
/* compiled from: PG */
public final class ekw {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/core/timestamp/TimestampProvider");
    public final int b;
    public final int c;
    public final AtomicReference d;
    private final int e;

    public ekw(dyt dyt) {
        jnu.e(dyt, "audioLibInputParams");
        int bitCount = Integer.bitCount(dyt.d);
        this.e = bitCount;
        this.b = evc.a(dyt.e) * bitCount;
        this.c = ********** / dyt.c;
        htk l = dyh.c.l();
        jnu.d(l, "newBuilder(...)");
        byw k = jnu.e(l, "builder");
        htk l2 = dyd.a.l();
        jnu.d(l2, "newBuilder(...)");
        k.E(jnu.e(l2, "builder").C());
        this.d = new AtomicReference(k.D());
    }

    public final void a(dyh dyh) {
        jnu.e(dyh, "audioTimestamp");
        this.d.set(dyh);
    }
}
