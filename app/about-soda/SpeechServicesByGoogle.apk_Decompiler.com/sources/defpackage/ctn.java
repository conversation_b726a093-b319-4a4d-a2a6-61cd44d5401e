package defpackage;

/* renamed from: ctn  reason: default package */
/* compiled from: PG */
public final class ctn extends htq implements hvb {
    public static final ctn b;
    private static volatile hvh c;
    public huv a = huv.a;

    static {
        ctn ctn = new ctn();
        b = ctn;
        htq.z(ctn.class, ctn);
    }

    private ctn() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(b, "\u0001\u0001\u0000\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u00012", new Object[]{"a", ctm.a});
        } else if (i2 == 3) {
            return new ctn();
        } else {
            if (i2 == 4) {
                return new htk((htq) b);
            }
            if (i2 == 5) {
                return b;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = c;
            if (hvh == null) {
                synchronized (ctn.class) {
                    hvh = c;
                    if (hvh == null) {
                        hvh = new htl(b);
                        c = hvh;
                    }
                }
            }
            return hvh;
        }
    }

    public final huv b() {
        huv huv = this.a;
        if (!huv.b) {
            this.a = huv.a();
        }
        return this.a;
    }
}
