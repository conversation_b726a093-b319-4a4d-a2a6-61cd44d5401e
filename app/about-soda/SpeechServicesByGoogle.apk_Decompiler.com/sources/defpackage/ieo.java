package defpackage;

/* renamed from: ieo  reason: default package */
/* compiled from: PG */
public final class ieo extends htq implements hvb {
    public static final ieo b;
    private static volatile hvh d;
    public iec a;
    private int c;

    static {
        ieo ieo = new ieo();
        b = ieo;
        htq.z(ieo.class, ieo);
    }

    private ieo() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(b, "\u0001\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001ဉ\u0000", new Object[]{"c", "a"});
        } else if (i2 == 3) {
            return new ieo();
        } else {
            if (i2 == 4) {
                return new htk((htq) b);
            }
            if (i2 == 5) {
                return b;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = d;
            if (hvh == null) {
                synchronized (ieo.class) {
                    hvh = d;
                    if (hvh == null) {
                        hvh = new htl(b);
                        d = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
