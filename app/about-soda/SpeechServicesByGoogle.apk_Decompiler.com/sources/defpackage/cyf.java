package defpackage;

import java.util.ArrayList;
import java.util.List;

/* renamed from: cyf  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cyf implements hko {
    public final /* synthetic */ int a;
    public final /* synthetic */ cyw b;

    public /* synthetic */ cyf(cyw cyw, int i) {
        this.b = cyw;
        this.a = i;
    }

    /* JADX WARNING: type inference failed for: r5v4, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r6v22, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme a(Object obj) {
        hme hme;
        csw csw;
        ArrayList arrayList = new ArrayList();
        for (cxg cxg : (List) obj) {
            ctg ctg = cxg.a;
            csx csx = cxg.b;
            htk l = hig.k.l();
            String str = ctg.b;
            if (!l.b.B()) {
                l.u();
            }
            htq htq = l.b;
            hig hig = (hig) htq;
            str.getClass();
            hig.a |= 1;
            hig.b = str;
            String str2 = ctg.c;
            if (!htq.B()) {
                l.u();
            }
            htq htq2 = l.b;
            hig hig2 = (hig) htq2;
            str2.getClass();
            hig2.a |= 4;
            hig2.d = str2;
            int i = csx.e;
            if (!htq2.B()) {
                l.u();
            }
            hig hig3 = (hig) l.b;
            hig3.a |= 2;
            hig3.c = i;
            int size = csx.n.size();
            if (!l.b.B()) {
                l.u();
            }
            hig hig4 = (hig) l.b;
            hig4.a |= 8;
            hig4.e = size;
            int i2 = 0;
            for (csv n : csx.n) {
                if (cqx.n(n)) {
                    i2++;
                }
            }
            if (!l.b.B()) {
                l.u();
            }
            hig hig5 = (hig) l.b;
            hig5.a |= 16;
            hig5.f = i2;
            boolean z = !ctg.d.isEmpty();
            if (!l.b.B()) {
                l.u();
            }
            htq htq3 = l.b;
            hig hig6 = (hig) htq3;
            hig6.a |= 32;
            hig6.g = z;
            long j = csx.r;
            if (!htq3.B()) {
                l.u();
            }
            htq htq4 = l.b;
            hig hig7 = (hig) htq4;
            hig7.a |= 64;
            hig7.h = j;
            String str3 = csx.s;
            if (!htq4.B()) {
                l.u();
            }
            hig hig8 = (hig) l.b;
            str3.getClass();
            hig8.a |= 128;
            hig8.i = str3;
            hig hig9 = (hig) l.r();
            htk l2 = hin.f.l();
            if (!l2.b.B()) {
                l2.u();
            }
            int i3 = this.a;
            hin hin = (hin) l2.b;
            hin.a |= 8;
            hin.e = i3;
            csw csw2 = csx.b;
            if (csw2 == null) {
                csw2 = csw.i;
            }
            if ((csw2.a & 2) != 0) {
                csw csw3 = csx.b;
                if (csw3 == null) {
                    csw3 = csw.i;
                }
                long j2 = csw3.c / 1000;
                if (!l2.b.B()) {
                    l2.u();
                }
                hin hin2 = (hin) l2.b;
                hin2.a |= 2;
                hin2.c = j2;
            } else {
                if (!l2.b.B()) {
                    l2.u();
                }
                hin hin3 = (hin) l2.b;
                hin3.a |= 2;
                hin3.c = -1;
            }
            cyw cyw = this.b;
            if (ctg.e) {
                if (!l2.b.B()) {
                    l2.u();
                }
                htq htq5 = l2.b;
                hin hin4 = (hin) htq5;
                hin4.b = a.E(3);
                hin4.a |= 1;
                csw csw4 = csx.b;
                if (csw4 == null) {
                    csw = csw.i;
                } else {
                    csw = csw4;
                }
                if ((csw.a & 4) != 0) {
                    if (csw4 == null) {
                        csw4 = csw.i;
                    }
                    long j3 = csw4.d / 1000;
                    if (!htq5.B()) {
                        l2.u();
                    }
                    hin hin5 = (hin) l2.b;
                    hin5.a |= 4;
                    hin5.d = j3;
                } else {
                    if (!htq5.B()) {
                        l2.u();
                    }
                    hin hin6 = (hin) l2.b;
                    hin6.a |= 4;
                    hin6.d = -1;
                }
                hme = hfc.K((hin) l2.r());
            } else {
                if (!l2.b.B()) {
                    l2.u();
                }
                hin hin7 = (hin) l2.b;
                hin7.a |= 4;
                hin7.d = -1;
                hme = ftd.K(((cvy) cyw.a).h(csx), new cyg(l2, 1), cyw.d);
            }
            arrayList.add(ftd.K(hme, new cyg(hig9, 0), cyw.d));
        }
        return hfc.H(arrayList);
    }
}
