package defpackage;

import java.util.concurrent.Executor;

/* renamed from: evb  reason: default package */
/* compiled from: PG */
public final class evb implements dxy {
    final /* synthetic */ Executor a;
    final /* synthetic */ jty b;

    public evb(jty jty, Executor executor) {
        this.b = jty;
        this.a = executor;
    }

    public final Executor a() {
        return this.a;
    }

    public final void b(dyc dyc) {
        jnu.e(dyc, "audioData");
        this.b.g(dyc);
        if (dyc.b == 2) {
            this.b.n((Throwable) null);
        }
    }
}
