package defpackage;

import android.accounts.Account;
import android.accounts.AccountManager;
import java.util.ArrayList;

/* renamed from: cwj  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwj implements hko {
    public final /* synthetic */ cwm a;

    public /* synthetic */ cwj(cwm cwm) {
        this.a = cwm;
    }

    /* JADX WARNING: type inference failed for: r2v37, types: [cyi, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r8v5, types: [cvz, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r10v2, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v41, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme a(Object obj) {
        hme hme;
        hme hme2;
        Integer num = (Integer) obj;
        ArrayList arrayList = new ArrayList();
        cyh.c("%s checkResetTrigger", "MDDManager");
        cwm cwm = this.a;
        arrayList.add(ftd.L(cwm.f(), new cwi(cwm, 9), cwm.h));
        if (ikc.a.a().p()) {
            cvy cvy = cwm.c;
            arrayList.add(cvy.m(new cvi(cvy, 10)));
        }
        if (ikc.a.a().s()) {
            cvy cvy2 = cwm.c;
            arrayList.add(cvy2.q(cvy2.c.d(), new cvi(cvy2, 5)));
        }
        if (ikc.a.a().r()) {
            cvy cvy3 = cwm.c;
            if (!cvy3.d.f()) {
                hme2 = hma.a;
            } else {
                try {
                    cxj cxj = (cxj) cvy3.d.b();
                    gxq p = gxq.p(((AccountManager) cxj.a).getAccountsByTypeForPackage("com.google", (String) cxj.b));
                    gym gym = new gym();
                    int i = ((hal) p).c;
                    for (int i2 = 0; i2 < i; i2++) {
                        Account account = (Account) p.get(i2);
                        if (!(account.name == null || account.type == null)) {
                            gym.c(account.type + ":" + account.name);
                        }
                    }
                    hme2 = cvy3.q(cvy3.c.d(), new bpt(cvy3, gym.g(), 13));
                } catch (RuntimeException e) {
                    hme2 = hfc.J(e);
                }
            }
            arrayList.add(hme2);
        }
        if (cqh.j()) {
            cvy cvy4 = cwm.c;
            arrayList.add(cvy4.m(new cvi(cvy4, 8)));
        }
        if (ikc.a.a().u()) {
            dmd dmd = cwm.p;
            arrayList.add(ftd.L(ftd.L(dmd.e.e(), new bpr(dmd, 17), dmd.c), new bpr(dmd, 15), dmd.c));
            cwm.l.d(1053);
        }
        cyw cyw = cwm.n;
        arrayList.add(((cyk) cyw.c).b(1046, new cpw(new cyo(cyw, num.intValue(), 1), 5), (int) ikg.a.a().b()));
        eix eix = cwm.q;
        arrayList.add(((cyk) eix.h).b(1057, new cpw(new cyo(eix, num.intValue(), 0), 7), (int) ikg.a.a().g()));
        cxk cxk = cwm.o;
        if (!cqh.n()) {
            hme = hma.a;
        } else {
            hme = ((cyk) cxk.a).b(1058, new cpw(new cpw(cxk.b.a(), 8), 6), (int) ikg.a.a().e());
        }
        arrayList.add(hme);
        if (cwm.i.f()) {
            cvy cvy5 = cwm.c;
            arrayList.add(cvy5.q(cvy5.c.d(), new cvi(cvy5, 9)));
        }
        cqh.B(cwm.b, "gms_icing_mdd_manager_metadata", cwm.g).edit().remove("gms_icing_mdd_manager_ph_config_version").remove("gms_icing_mdd_manager_ph_config_version_timestamp").commit();
        if (!ikc.a.a().e()) {
            arrayList.add(cqh.V(czw.e(cwm.d.c()).f(new cwh(3), cwm.h).g(new cwi(cwm, 1), cwm.h), czw.e(cwm.d.e()).f(new cwn(cwm, 1), cwm.h).g(new bub(9), cwm.h)).n(new ctw(12), hld.a));
        }
        return cqh.U(arrayList).n(new ctw(10), cwm.h);
    }
}
