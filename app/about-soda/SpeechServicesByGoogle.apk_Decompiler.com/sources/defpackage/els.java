package defpackage;

/* renamed from: els  reason: default package */
/* compiled from: PG */
final class els extends jmi implements jne {
    int a;
    final /* synthetic */ elx b;
    final /* synthetic */ ehg c;
    final /* synthetic */ int d;
    final /* synthetic */ eec e;
    private /* synthetic */ Object f;
    private final /* synthetic */ int g;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public els(elx elx, ehg ehg, int i, eec eec, jlr jlr, int i2) {
        super(2, jlr);
        this.g = i2;
        this.b = elx;
        this.c = ehg;
        this.d = i;
        this.e = eec;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        if (this.g != 0) {
            return ((els) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
        return ((els) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    public final Object bk(Object obj) {
        Object obj2;
        Object obj3;
        int i = 1;
        if (this.g != 0) {
            Object obj4 = jlx.COROUTINE_SUSPENDED;
            if (this.a != 0) {
                try {
                    jji.c(obj);
                } catch (Throwable th) {
                    obj3 = jji.b(th);
                }
            } else {
                jji.c(obj);
                jqs jqs = (jqs) this.f;
                hme a2 = this.e.a();
                jnu.d(a2, "getAcquireAudioFocusStatus(...)");
                this.a = 1;
                obj = jqw.x(a2, this);
                if (obj == obj4) {
                    return obj4;
                }
            }
            obj3 = (dyk) obj;
            Object n = eki.n(1);
            if (true == (obj3 instanceof jjt)) {
                obj3 = n;
            }
            elx elx = this.b;
            ehg ehg = this.c;
            int i2 = this.d;
            dyk dyk = (dyk) obj3;
            jnu.b(dyk);
            jnu.e(ehg, "clientInfo");
            jnu.e(dyk, "status");
            dxl dxl = dwt.r;
            jnu.d(dxl, "AUDIO_FOCUS_ACQUIRE_DONE");
            int e2 = dnk.e(dyk.b);
            if (e2 != 0) {
                i = e2;
            }
            dxh a3 = dxl.a(i - 1, "audio_focus_acquire_status");
            a3.d("focus_acquire", String.valueOf(i2));
            elx.x(a3, ehg);
            return jkd.a;
        }
        Object obj5 = jlx.COROUTINE_SUSPENDED;
        if (this.a != 0) {
            try {
                jji.c(obj);
            } catch (Throwable th2) {
                obj2 = jji.b(th2);
            }
        } else {
            jji.c(obj);
            jqs jqs2 = (jqs) this.f;
            hme b2 = this.e.b();
            jnu.d(b2, "getReleaseAudioFocusStatus(...)");
            this.a = 1;
            obj = jqw.x(b2, this);
            if (obj == obj5) {
                return obj5;
            }
        }
        obj2 = (dyn) obj;
        Object o = eki.o(1);
        if (true == (obj2 instanceof jjt)) {
            obj2 = o;
        }
        elx elx2 = this.b;
        ehg ehg2 = this.c;
        int i3 = this.d;
        dyn dyn = (dyn) obj2;
        jnu.b(dyn);
        jnu.e(ehg2, "clientInfo");
        jnu.e(dyn, "status");
        dxl dxl2 = dwt.t;
        jnu.d(dxl2, "AUDIO_FOCUS_RELEASE_DONE");
        int c2 = dnk.c(dyn.b);
        if (c2 != 0) {
            i = c2;
        }
        dxh a4 = dxl2.a(i - 1, "audio_focus_release_status");
        a4.d("focus_release", String.valueOf(i3));
        elx2.x(a4, ehg2);
        return jkd.a;
    }

    public final jlr c(Object obj, jlr jlr) {
        Object obj2 = obj;
        if (this.g != 0) {
            els els = new els(this.b, this.c, this.d, this.e, jlr, 1, (byte[]) null);
            els.f = obj2;
            return els;
        }
        els els2 = new els(this.b, this.c, this.d, this.e, jlr, 0);
        els2.f = obj2;
        return els2;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public els(elx elx, ehg ehg, int i, eec eec, jlr jlr, int i2, byte[] bArr) {
        super(2, jlr);
        this.g = i2;
        this.b = elx;
        this.c = ehg;
        this.d = i;
        this.e = eec;
    }
}
