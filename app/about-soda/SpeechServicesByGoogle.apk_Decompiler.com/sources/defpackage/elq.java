package defpackage;

/* renamed from: elq  reason: default package */
/* compiled from: PG */
public final class elq {
    public final ehg a;
    public final Integer b;
    public final Integer c;
    public final Integer d;

    public elq(ehg ehg, Integer num, Integer num2, Integer num3) {
        jnu.e(ehg, "clientInfo");
        this.a = ehg;
        this.b = num;
        this.c = num2;
        this.d = num3;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof elq)) {
            return false;
        }
        elq elq = (elq) obj;
        if (jnu.i(this.a, elq.a) && jnu.i(this.b, elq.b) && jnu.i(this.c, elq.c) && jnu.i(this.d, elq.d)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        int i2;
        ehg ehg = this.a;
        if (ehg.B()) {
            i = ehg.i();
        } else {
            int i3 = ehg.memoizedHashCode;
            if (i3 == 0) {
                i3 = ehg.i();
                ehg.memoizedHashCode = i3;
            }
            i = i3;
        }
        int hashCode = (i * 31) + this.b.hashCode();
        Integer num = this.c;
        int i4 = 0;
        if (num == null) {
            i2 = 0;
        } else {
            i2 = num.hashCode();
        }
        int i5 = ((hashCode * 31) + i2) * 31;
        Integer num2 = this.d;
        if (num2 != null) {
            i4 = num2.hashCode();
        }
        return i5 + i4;
    }

    public final String toString() {
        return "AudioLoggingMetadata(clientInfo=" + this.a + ", routeToken=" + this.b + ", sessionToken=" + this.c + ", clientToken=" + this.d + ")";
    }

    /* JADX INFO: this call moved to the top of the method (can break code semantics) */
    public /* synthetic */ elq(ehg ehg, Integer num, Integer num2, Integer num3, int i) {
        this(ehg, num, (i & 4) != 0 ? null : num2, (i & 8) != 0 ? null : num3);
    }
}
