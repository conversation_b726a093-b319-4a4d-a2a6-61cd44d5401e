package defpackage;

/* renamed from: eni  reason: default package */
/* compiled from: PG */
public final class eni implements enk {
    public final int a;

    public eni(int i) {
        this.a = i;
    }

    public final int a() {
        return this.a;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if ((obj instanceof eni) && this.a == ((eni) obj).a) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return this.a;
    }

    public final String toString() {
        return "AbsoluteOffset(bytes=" + this.a + ")";
    }
}
