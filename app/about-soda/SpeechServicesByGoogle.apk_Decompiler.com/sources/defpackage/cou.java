package defpackage;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.concurrent.locks.AbstractOwnableSynchronizer;

/* renamed from: cou  reason: default package */
/* compiled from: PG */
public final class cou extends Exception {
    public cou(Thread thread) {
        this(thread, (Throwable) null);
    }

    static Thread a(Object obj) {
        if (!(obj instanceof AbstractOwnableSynchronizer)) {
            return null;
        }
        try {
            Method declaredMethod = AbstractOwnableSynchronizer.class.getDeclaredMethod("getExclusiveOwnerThread", (Class[]) null);
            declaredMethod.setAccessible(true);
            return (Thread) declaredMethod.invoke(obj, (Object[]) null);
        } catch (ClassCastException | IllegalAccessException | NoSuchMethodException | InvocationTargetException unused) {
            return null;
        }
    }

    public final synchronized Throwable fillInStackTrace() {
        return this;
    }

    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public cou(java.lang.Thread r7, java.lang.Throwable r8) {
        /*
            r6 = this;
            java.lang.Object r0 = java.util.concurrent.locks.LockSupport.getBlocker(r7)
            java.lang.Thread$State r1 = r7.getState()
            java.lang.String r2 = java.lang.String.valueOf(r7)
            java.lang.String r3 = ""
            if (r1 != 0) goto L_0x0012
            r1 = r3
            goto L_0x001c
        L_0x0012:
            java.lang.String r1 = r1.toString()
            java.lang.String r4 = " in state "
            java.lang.String r1 = r4.concat(r1)
        L_0x001c:
            if (r0 != 0) goto L_0x0020
            r4 = r3
            goto L_0x002a
        L_0x0020:
            java.lang.String r4 = r0.toString()
            java.lang.String r5 = " blocked on "
            java.lang.String r4 = r5.concat(r4)
        L_0x002a:
            java.lang.Thread r0 = a(r0)
            java.lang.StringBuilder r5 = new java.lang.StringBuilder
            r5.<init>()
            r5.append(r2)
            r5.append(r1)
            r5.append(r4)
            if (r0 != 0) goto L_0x003f
            goto L_0x0041
        L_0x003f:
            java.lang.String r3 = " (see cause for owner state)"
        L_0x0041:
            r5.append(r3)
            java.lang.String r0 = r5.toString()
            java.lang.StackTraceElement[] r1 = r7.getStackTrace()
            java.lang.Object r7 = java.util.concurrent.locks.LockSupport.getBlocker(r7)
            java.lang.Thread r7 = a(r7)
            if (r7 == 0) goto L_0x005c
            cou r2 = new cou
            r2.<init>(r7, r8)
            r8 = r2
        L_0x005c:
            r6.<init>(r0)
            if (r8 == 0) goto L_0x0064
            r6.initCause(r8)
        L_0x0064:
            r6.setStackTrace(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cou.<init>(java.lang.Thread, java.lang.Throwable):void");
    }
}
