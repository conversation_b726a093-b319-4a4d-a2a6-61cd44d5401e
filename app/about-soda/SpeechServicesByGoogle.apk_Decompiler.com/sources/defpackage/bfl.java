package defpackage;

/* renamed from: bfl  reason: default package */
/* compiled from: PG */
public final class bfl extends bfd {
    public bfl(bft bft) {
        super(bft);
    }

    public final boolean b(bhe bhe) {
        jnu.e(bhe, "workSpec");
        return bhe.k.g;
    }

    public final int d() {
        return 9;
    }

    public final /* bridge */ /* synthetic */ boolean e(Object obj) {
        if (!((Boolean) obj).booleanValue()) {
            return true;
        }
        return false;
    }
}
