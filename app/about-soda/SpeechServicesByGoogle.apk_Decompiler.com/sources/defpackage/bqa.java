package defpackage;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import java.util.concurrent.Executor;

/* renamed from: bqa  reason: default package */
/* compiled from: PG */
public final class bqa implements bpm {
    public static final String a = "bqa";
    public static final ComponentName b;
    public static final ComponentName c;
    public final hmh d;
    protected final Context e;
    public final Executor f;
    private final Object g = new Object();
    private bpz h;

    static {
        ComponentName componentName = new ComponentName("com.google.android.aicore", "com.google.android.apps.aicore.service.multiuser.AiCoreMultiUserService");
        b = componentName;
        c = new ComponentName(componentName.getPackageName(), "com.google.android.apps.aicore.service.AiCoreService");
    }

    public bqa(bpo bpo) {
        this.e = bpo.a;
        this.d = hfc.E(bpo.b);
        this.f = bpo.c;
    }

    public final hme a() {
        bpz bpz;
        hmr hmr;
        synchronized (this.g) {
            bpz = this.h;
            if (bpz == null) {
                bpz = new bpz(this);
                this.h = bpz;
                Intent intent = new Intent();
                intent.setComponent(b);
                if (!bpz.c(intent)) {
                    bpz.c.e.unbindService(bpz);
                    Intent intent2 = new Intent();
                    intent2.setComponent(c);
                    if (!bpz.c(intent2)) {
                        bpz.a(new bpp(4, 601, "AiCore service failed to bind.", (Throwable) null));
                    }
                }
            }
        }
        synchronized (bpz.a) {
            if (bpz.b.isCancelled()) {
                bpz.b();
            }
            hmr = bpz.b;
        }
        return hmr;
    }

    public final hme b() {
        return hke.f(hly.q(a()), new amv(2), hld.a);
    }

    public final void c() {
        synchronized (this.g) {
            bpz bpz = this.h;
            if (bpz != null) {
                this.e.unbindService(bpz);
                this.h = null;
            }
        }
    }

    public final void close() {
        c();
    }
}
