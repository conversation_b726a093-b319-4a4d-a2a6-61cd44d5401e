package defpackage;

/* renamed from: eph  reason: default package */
/* compiled from: PG */
final class eph {
    public final enr a;
    public final hme b;

    public eph(enr enr, hme hme) {
        jnu.e(hme, "startListeningResult");
        this.a = enr;
        this.b = hme;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof eph)) {
            return false;
        }
        eph eph = (eph) obj;
        if (jnu.i(this.a, eph.a) && jnu.i(this.b, eph.b)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (this.a.hashCode() * 31) + this.b.hashCode();
    }

    public final String toString() {
        return "AudioSourceStartedData(sourceAccessor=" + this.a + ", startListeningResult=" + this.b + ")";
    }
}
