package defpackage;

/* renamed from: eqd  reason: default package */
/* compiled from: PG */
public final class eqd implements eqf {
    public final String a = "";

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if ((obj instanceof eqd) && jnu.i(this.a, ((eqd) obj).a)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return this.a.hashCode();
    }

    public final String toString() {
        return "NotOwned(actualOwnerApp=" + this.a + ")";
    }
}
