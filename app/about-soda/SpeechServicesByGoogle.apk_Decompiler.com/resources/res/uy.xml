<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:gravity="center_vertical" android:orientation="horizontal" android:layout_width="match_parent" android:layout_height="wrap_content" android:minHeight="?android:attr/listPreferredItemHeightSmall" android:paddingStart="?android:attr/listPreferredItemPaddingStart" android:paddingEnd="?android:attr/listPreferredItemPaddingEnd">
    <com.android.car.ui.uxr.DrawableStateImageView android:id="@android:id/icon" android:layout_width="@dimen/car_ui_preference_icon_size" android:layout_height="@dimen/car_ui_preference_icon_size" android:layout_marginTop="@dimen/car_ui_preference_content_margin_top" android:layout_marginBottom="@dimen/car_ui_preference_content_margin_bottom" android:scaleType="fitCenter" android:tint="@color/car_ui_preference_icon_color" android:layout_marginEnd="@dimen/car_ui_preference_icon_margin_end"/>
    <RelativeLayout android:layout_width="0dp" android:layout_height="wrap_content" android:layout_marginTop="@dimen/car_ui_preference_content_margin_top" android:layout_marginBottom="@dimen/car_ui_preference_content_margin_bottom" android:layout_weight="1">
        <com.android.car.ui.uxr.DrawableStateTextView android:textAppearance="@style/TextAppearance.CarUi.PreferenceTitle" android:id="@android:id/title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:singleLine="true"/>
        <com.android.car.ui.uxr.DrawableStateTextView android:textAppearance="@style/TextAppearance.CarUi.PreferenceSummary" android:id="@android:id/summary" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_below="@android:id/title" android:layout_alignStart="@android:id/title"/>
        <androidx.preference.UnPressableLinearLayout android:clipChildren="false" android:clipToPadding="false" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_below="@android:id/summary" android:layout_alignStart="@android:id/title">
            <SeekBar android:id="@+id/seekbar" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_weight="1" style="@style/Widget.CarUi.SeekbarPreference.Seekbar"/>
            <TextView android:id="@+id/seekbar_value" android:visibility="gone" android:layout_width="0dp" android:layout_height="0dp"/>
        </androidx.preference.UnPressableLinearLayout>
    </RelativeLayout>
</LinearLayout>
