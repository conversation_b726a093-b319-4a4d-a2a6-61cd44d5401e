package defpackage;

/* renamed from: eqi  reason: default package */
/* compiled from: PG */
final class eqi extends jmi implements jna {
    final /* synthetic */ epz a;
    final /* synthetic */ eqa b;
    final /* synthetic */ eqb c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eqi(epz epz, eqa eqa, eqb eqb, jlr jlr) {
        super(1, jlr);
        this.a = epz;
        this.b = eqa;
        this.c = eqb;
    }

    public final /* bridge */ /* synthetic */ Object a(Object obj) {
        return new eqi(this.a, this.b, this.c, (jlr) obj).bk(jkd.a);
    }

    public final Object bk(Object obj) {
        jji.c(obj);
        this.a.a();
        return jkd.a;
    }
}
