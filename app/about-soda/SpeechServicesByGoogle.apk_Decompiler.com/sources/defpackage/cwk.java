package defpackage;

/* renamed from: cwk  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwk implements hko {
    public final /* synthetic */ cwm a;
    public final /* synthetic */ csx b;
    public final /* synthetic */ ctg c;
    public final /* synthetic */ hko d;

    public /* synthetic */ cwk(cwm cwm, csx csx, ctg ctg, hko hko) {
        this.a = cwm;
        this.b = csx;
        this.c = ctg;
        this.d = hko;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:131:0x01f8, code lost:
        defpackage.cyh.i("%s Delta File of Datafile details missing in added group = %s, file id = %s, delta file UrlToDownload = %s.", "DataFileGroupValidator", r8, r10.b, r15.b);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:133:0x0215, code lost:
        if (defpackage.cqx.p(r10) == false) goto L_0x023e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:135:0x0221, code lost:
        if (defpackage.ikc.a.a().n() != false) goto L_0x023e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:136:0x0223, code lost:
        defpackage.cyh.i("%s File detected as sideloaded, but sideloading is not enabled. group = %s, file id = %s, file url = %s", "DataFileGroupValidator", r0.c, r10.b, r10.c);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:138:0x0241, code lost:
        defpackage.cyh.i("%s File details missing in added group = %s, file id = %s", "DataFileGroupValidator", r0.c, r10.b);
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme a(java.lang.Object r19) {
        /*
            r18 = this;
            r1 = r18
            java.lang.String r2 = "MDDManager"
            java.lang.String r3 = "%s %s"
            r0 = r19
            java.lang.Void r0 = (java.lang.Void) r0
            csx r0 = r1.b
            java.lang.String r4 = r0.c
            boolean r4 = r4.isEmpty()
            hko r5 = r1.d
            cwm r6 = r1.a
            java.lang.String r7 = "DataFileGroupValidator"
            r8 = 0
            if (r4 == 0) goto L_0x0022
            java.lang.String r2 = "%s Group name missing in added group"
            defpackage.cyh.g(r2, r7)
            goto L_0x02e6
        L_0x0022:
            java.lang.String r4 = r0.c
            java.lang.String r9 = "|"
            boolean r4 = r4.contains(r9)
            if (r4 == 0) goto L_0x0035
            java.lang.String r2 = r0.c
            java.lang.String r3 = "%s Group name = %s contains '|'"
            defpackage.cyh.h(r3, r7, r2)
            goto L_0x02e6
        L_0x0035:
            java.lang.String r4 = r0.d
            boolean r4 = r4.contains(r9)
            if (r4 == 0) goto L_0x0046
            java.lang.String r2 = r0.d
            java.lang.String r3 = "%s Owner package = %s contains '|'"
            defpackage.cyh.h(r3, r7, r2)
            goto L_0x02e6
        L_0x0046:
            huf r4 = r0.n
            java.util.Iterator r4 = r4.iterator()
        L_0x004c:
            boolean r10 = r4.hasNext()
            r12 = 2
            r13 = 1
            if (r10 == 0) goto L_0x0257
            java.lang.Object r10 = r4.next()
            csv r10 = (defpackage.csv) r10
            java.lang.String r14 = r10.b
            boolean r14 = r14.isEmpty()
            if (r14 != 0) goto L_0x0241
            java.lang.String r14 = r10.b
            boolean r14 = r14.contains(r9)
            if (r14 != 0) goto L_0x0241
            boolean r14 = defpackage.cqx.l(r10)
            if (r14 == 0) goto L_0x007f
            int r14 = r10.a
            r14 = r14 & 64
            if (r14 == 0) goto L_0x008f
            java.lang.String r14 = r10.h
            boolean r14 = r14.isEmpty()
            if (r14 != 0) goto L_0x008f
            goto L_0x008d
        L_0x007f:
            int r14 = r10.a
            r14 = r14 & 16
            if (r14 == 0) goto L_0x008f
            java.lang.String r14 = r10.f
            boolean r14 = r14.isEmpty()
            if (r14 != 0) goto L_0x008f
        L_0x008d:
            r14 = r13
            goto L_0x0090
        L_0x008f:
            r14 = r8
        L_0x0090:
            int r15 = r10.e
            int r15 = defpackage.a.A(r15)
            if (r15 != 0) goto L_0x0099
            r15 = r13
        L_0x0099:
            int r15 = r15 + -1
            if (r15 == 0) goto L_0x00a0
            r15 = r14 ^ 1
            goto L_0x00a1
        L_0x00a0:
            r15 = r14
        L_0x00a1:
            boolean r16 = defpackage.cqx.l(r10)
            if (r16 == 0) goto L_0x00ab
            if (r14 != 0) goto L_0x00ab
            r14 = r13
            goto L_0x00ac
        L_0x00ab:
            r14 = r8
        L_0x00ac:
            r14 = r14 | r15
            int r15 = r10.m
            int r15 = defpackage.a.A(r15)
            if (r15 != 0) goto L_0x00b7
        L_0x00b5:
            r15 = r13
            goto L_0x00c3
        L_0x00b7:
            if (r15 != r12) goto L_0x00b5
            java.lang.String r15 = r10.n
            boolean r15 = r15.isEmpty()
            if (r15 != 0) goto L_0x00c2
            goto L_0x00b5
        L_0x00c2:
            r15 = r8
        L_0x00c3:
            java.lang.String r11 = r10.c
            boolean r11 = r11.isEmpty()
            if (r11 != 0) goto L_0x0241
            java.lang.String r11 = r10.c
            boolean r11 = r11.contains(r9)
            if (r11 != 0) goto L_0x0241
            int r11 = r10.d
            if (r11 < 0) goto L_0x0241
            if (r14 == 0) goto L_0x0241
            if (r15 == 0) goto L_0x0241
            java.lang.String r11 = defpackage.cqx.k(r10)
            boolean r11 = r11.contains(r9)
            if (r11 != 0) goto L_0x0241
            int r11 = r10.a
            r11 = r11 & 32
            r14 = 4
            if (r11 == 0) goto L_0x0175
            ihf r11 = r10.g
            if (r11 != 0) goto L_0x00f2
            ihf r11 = defpackage.ihf.b
        L_0x00f2:
            boolean r11 = defpackage.cqx.b(r11)
            if (r11 == 0) goto L_0x02e6
            java.lang.String r11 = r0.c
            boolean r15 = defpackage.cqx.l(r10)
            if (r15 == 0) goto L_0x0158
            ikc r15 = defpackage.ikc.a
            ikd r15 = r15.a()
            boolean r15 = r15.o()
            if (r15 != 0) goto L_0x0115
            java.lang.String r2 = r10.b
            java.lang.String r3 = "Feature enableZipFolder is not enabled. Group = %s, file id = %s"
            defpackage.cyh.h(r3, r11, r2)
            goto L_0x02e6
        L_0x0115:
            ihf r15 = r10.g
            if (r15 != 0) goto L_0x011b
            ihf r15 = defpackage.ihf.b
        L_0x011b:
            huf r15 = r15.a
            int r15 = r15.size()
            if (r15 <= r13) goto L_0x012c
            java.lang.String r2 = r10.b
            java.lang.String r3 = "Download zip folder transform cannot not be applied with other transforms. Group = %s, file id = %s"
            defpackage.cyh.h(r3, r11, r2)
            goto L_0x02e6
        L_0x012c:
            ihf r15 = r10.g
            if (r15 != 0) goto L_0x0132
            ihf r15 = defpackage.ihf.b
        L_0x0132:
            huf r15 = r15.a
            java.lang.Object r15 = r15.get(r8)
            ihe r15 = (defpackage.ihe) r15
            int r8 = r15.a
            if (r8 != r14) goto L_0x0143
            java.lang.Object r8 = r15.b
            ihg r8 = (defpackage.ihg) r8
            goto L_0x0145
        L_0x0143:
            ihg r8 = defpackage.ihg.c
        L_0x0145:
            java.lang.String r15 = "*"
            java.lang.String r8 = r8.b
            boolean r8 = r15.equals(r8)
            if (r8 != 0) goto L_0x0158
            java.lang.String r2 = r10.b
            java.lang.String r3 = "Download zip folder transform can only have * as target. Group = %s, file id = %s"
            defpackage.cyh.h(r3, r11, r2)
            goto L_0x02e6
        L_0x0158:
            int r8 = r10.e
            int r8 = defpackage.a.A(r8)
            if (r8 != 0) goto L_0x0161
            goto L_0x0163
        L_0x0161:
            if (r8 == r12) goto L_0x0175
        L_0x0163:
            int r8 = r10.a
            r8 = r8 & 64
            if (r8 == 0) goto L_0x016a
            goto L_0x0175
        L_0x016a:
            java.lang.String r2 = r0.c
            java.lang.String r3 = r10.b
            java.lang.String r4 = "Download checksum must be provided. Group = %s, file id = %s"
            defpackage.cyh.h(r4, r2, r3)
            goto L_0x02e6
        L_0x0175:
            int r8 = r10.a
            r8 = r8 & 256(0x100, float:3.59E-43)
            if (r8 == 0) goto L_0x0187
            ihf r8 = r10.j
            if (r8 != 0) goto L_0x0181
            ihf r8 = defpackage.ihf.b
        L_0x0181:
            boolean r8 = defpackage.cqx.b(r8)
            if (r8 == 0) goto L_0x02e6
        L_0x0187:
            java.lang.String r8 = r0.c
            huf r11 = r10.k
            java.util.Iterator r11 = r11.iterator()
        L_0x018f:
            boolean r15 = r11.hasNext()
            if (r15 == 0) goto L_0x0211
            java.lang.Object r15 = r11.next()
            csy r15 = (defpackage.csy) r15
            java.lang.String r14 = r15.b
            boolean r14 = r14.isEmpty()
            if (r14 != 0) goto L_0x01f8
            java.lang.String r14 = r15.b
            boolean r14 = r14.contains(r9)
            if (r14 != 0) goto L_0x01f8
            int r14 = r15.a
            r14 = r14 & r12
            if (r14 == 0) goto L_0x01f8
            int r14 = r15.c
            if (r14 < 0) goto L_0x01f8
            java.lang.String r14 = r15.d
            boolean r14 = r14.isEmpty()
            if (r14 != 0) goto L_0x01f8
            java.lang.String r14 = r15.d
            boolean r14 = r14.contains(r9)
            if (r14 != 0) goto L_0x01f8
            int r14 = r15.a
            r17 = r14 & 8
            if (r17 == 0) goto L_0x01f8
            int r12 = r15.e
            int r12 = defpackage.a.A(r12)
            if (r12 != 0) goto L_0x01d3
            goto L_0x01f8
        L_0x01d3:
            if (r12 == r13) goto L_0x01f8
            r12 = r14 & 16
            if (r12 == 0) goto L_0x01f8
            csu r12 = r15.f
            if (r12 != 0) goto L_0x01df
            csu r12 = defpackage.csu.b
        L_0x01df:
            java.lang.String r12 = r12.a
            boolean r12 = r12.isEmpty()
            if (r12 != 0) goto L_0x01f8
            csu r12 = r15.f
            if (r12 != 0) goto L_0x01ed
            csu r12 = defpackage.csu.b
        L_0x01ed:
            java.lang.String r12 = r12.a
            boolean r12 = r12.contains(r9)
            if (r12 != 0) goto L_0x01f8
            r12 = 2
            r14 = 4
            goto L_0x018f
        L_0x01f8:
            java.lang.String r2 = r10.b
            java.lang.String r3 = r15.b
            r4 = 4
            java.lang.Object[] r4 = new java.lang.Object[r4]
            r5 = 0
            r4[r5] = r7
            r4[r13] = r8
            r5 = 2
            r4[r5] = r2
            r2 = 3
            r4[r2] = r3
            java.lang.String r2 = "%s Delta File of Datafile details missing in added group = %s, file id = %s, delta file UrlToDownload = %s."
            defpackage.cyh.i(r2, r4)
            goto L_0x02e6
        L_0x0211:
            boolean r8 = defpackage.cqx.p(r10)
            if (r8 == 0) goto L_0x023e
            ikc r8 = defpackage.ikc.a
            ikd r8 = r8.a()
            boolean r8 = r8.n()
            if (r8 != 0) goto L_0x023e
            java.lang.String r2 = r0.c
            java.lang.String r3 = r10.b
            java.lang.String r4 = r10.c
            r5 = 4
            java.lang.Object[] r5 = new java.lang.Object[r5]
            r8 = 0
            r5[r8] = r7
            r5[r13] = r2
            r2 = 2
            r5[r2] = r3
            r2 = 3
            r5[r2] = r4
            java.lang.String r2 = "%s File detected as sideloaded, but sideloading is not enabled. group = %s, file id = %s, file url = %s"
            defpackage.cyh.i(r2, r5)
            goto L_0x02e6
        L_0x023e:
            r8 = 0
            goto L_0x004c
        L_0x0241:
            java.lang.String r2 = r0.c
            java.lang.String r3 = r10.b
            r4 = 3
            java.lang.Object[] r4 = new java.lang.Object[r4]
            r5 = 0
            r4[r5] = r7
            r4[r13] = r2
            r2 = 2
            r4[r2] = r3
            java.lang.String r2 = "%s File details missing in added group = %s, file id = %s"
            defpackage.cyh.i(r2, r4)
            goto L_0x02e6
        L_0x0257:
            r4 = 0
        L_0x0258:
            huf r8 = r0.n
            int r8 = r8.size()
            if (r4 >= r8) goto L_0x02a7
            int r8 = r4 + 1
            r9 = r8
        L_0x0263:
            huf r10 = r0.n
            int r10 = r10.size()
            if (r9 >= r10) goto L_0x02a5
            huf r10 = r0.n
            java.lang.Object r10 = r10.get(r4)
            csv r10 = (defpackage.csv) r10
            java.lang.String r10 = r10.b
            huf r11 = r0.n
            java.lang.Object r11 = r11.get(r9)
            csv r11 = (defpackage.csv) r11
            java.lang.String r11 = r11.b
            boolean r10 = r10.equals(r11)
            if (r10 == 0) goto L_0x02a2
            java.lang.String r2 = r0.c
            huf r3 = r0.n
            java.lang.Object r3 = r3.get(r4)
            csv r3 = (defpackage.csv) r3
            java.lang.String r3 = r3.b
            r4 = 3
            java.lang.Object[] r4 = new java.lang.Object[r4]
            r5 = 0
            r4[r5] = r7
            r4[r13] = r2
            r2 = 2
            r4[r2] = r3
            java.lang.String r2 = "%s Repeated file id in added group = %s, file id = %s"
            defpackage.cyh.i(r2, r4)
            goto L_0x02e6
        L_0x02a2:
            int r9 = r9 + 1
            goto L_0x0263
        L_0x02a5:
            r4 = r8
            goto L_0x0258
        L_0x02a7:
            csz r4 = r0.l
            if (r4 != 0) goto L_0x02ad
            csz r4 = defpackage.csz.f
        L_0x02ad:
            int r4 = r4.c
            int r4 = defpackage.a.x(r4)
            if (r4 != 0) goto L_0x02b6
            goto L_0x02cd
        L_0x02b6:
            r8 = 3
            if (r4 != r8) goto L_0x02cd
            csz r4 = r0.l
            if (r4 != 0) goto L_0x02bf
            csz r4 = defpackage.csz.f
        L_0x02bf:
            long r8 = r4.d
            r10 = 0
            int r4 = (r8 > r10 ? 1 : (r8 == r10 ? 0 : -1))
            if (r4 > 0) goto L_0x02cd
            java.lang.String r2 = "%s For DOWNLOAD_FIRST_ON_WIFI_THEN_ON_ANY_NETWORK policy, the download_first_on_wifi_period_secs must be > 0"
            defpackage.cyh.g(r2, r7)
            goto L_0x02e6
        L_0x02cd:
            android.content.Context r4 = r6.b
            boolean r4 = defpackage.cqh.v(r4)
            if (r4 != 0) goto L_0x0300
            int r4 = r0.i
            int r4 = defpackage.a.x(r4)
            if (r4 != 0) goto L_0x02de
            goto L_0x0300
        L_0x02de:
            r8 = 3
            if (r4 != r8) goto L_0x0300
            java.lang.String r2 = "%s For AllowedReaders ALL_APPS policy, the device should be migrated to new key"
            defpackage.cyh.g(r2, r7)
        L_0x02e6:
            cyk r8 = r6.l
            java.lang.String r10 = r0.c
            int r11 = r0.e
            long r12 = r0.r
            java.lang.String r14 = r0.s
            r9 = 1020(0x3fc, float:1.43E-42)
            r8.e(r9, r10, r11, r12, r14)
            r0 = 0
            java.lang.Boolean r0 = java.lang.Boolean.valueOf(r0)
            hme r0 = defpackage.hfc.K(r0)
            goto L_0x04e2
        L_0x0300:
            huf r4 = r0.n
            java.util.Iterator r7 = r4.iterator()
        L_0x0306:
            boolean r8 = r7.hasNext()
            r9 = 5
            if (r8 == 0) goto L_0x03bf
            java.lang.Object r8 = r7.next()
            csv r8 = (defpackage.csv) r8
            int r8 = r8.e
            int r8 = defpackage.a.A(r8)
            if (r8 == 0) goto L_0x0306
            r10 = 2
            if (r8 != r10) goto L_0x0306
            int r7 = r4.size()
            gxl r7 = defpackage.gxq.g(r7)
            java.util.Iterator r4 = r4.iterator()
        L_0x032a:
            boolean r8 = r4.hasNext()
            if (r8 == 0) goto L_0x03ba
            java.lang.Object r8 = r4.next()
            csv r8 = (defpackage.csv) r8
            int r10 = r8.e
            int r10 = defpackage.a.A(r10)
            if (r10 != 0) goto L_0x033f
            r10 = r13
        L_0x033f:
            int r10 = r10 + -1
            if (r10 == 0) goto L_0x03b5
            java.lang.Object r10 = r8.C(r9)
            htk r10 = (defpackage.htk) r10
            r10.x(r8)
            java.lang.String r11 = r8.c
            java.security.MessageDigest r12 = defpackage.cxt.b()
            if (r12 != 0) goto L_0x0357
            java.lang.String r11 = ""
            goto L_0x0368
        L_0x0357:
            byte[] r11 = r11.getBytes()
            int r14 = r11.length
            r15 = 0
            r12.update(r11, r15, r14)
            byte[] r11 = r12.digest()
            java.lang.String r11 = defpackage.cxt.a(r11)
        L_0x0368:
            boolean r8 = defpackage.cqx.l(r8)
            if (r8 == 0) goto L_0x0386
            htq r8 = r10.b
            boolean r8 = r8.B()
            if (r8 != 0) goto L_0x0379
            r10.u()
        L_0x0379:
            htq r8 = r10.b
            csv r8 = (defpackage.csv) r8
            int r12 = r8.a
            r12 = r12 | 64
            r8.a = r12
            r8.h = r11
            goto L_0x039d
        L_0x0386:
            htq r8 = r10.b
            boolean r8 = r8.B()
            if (r8 != 0) goto L_0x0391
            r10.u()
        L_0x0391:
            htq r8 = r10.b
            csv r8 = (defpackage.csv) r8
            int r12 = r8.a
            r12 = r12 | 16
            r8.a = r12
            r8.f = r11
        L_0x039d:
            htq r8 = r10.b
            csv r8 = (defpackage.csv) r8
            java.lang.String r11 = r8.b
            java.lang.String r8 = r8.f
            java.lang.String r12 = "FileId %s does not have checksum. Generated checksum from url %s"
            defpackage.cyh.d(r12, r11, r8)
            htq r8 = r10.r()
            csv r8 = (defpackage.csv) r8
            r7.h(r8)
            goto L_0x032a
        L_0x03b5:
            r7.h(r8)
            goto L_0x032a
        L_0x03ba:
            gxq r4 = r7.g()
            goto L_0x03c3
        L_0x03bf:
            gxq r4 = defpackage.gxq.o(r4)
        L_0x03c3:
            java.lang.Object r7 = r0.C(r9)
            htk r7 = (defpackage.htk) r7
            r7.x(r0)
            htq r0 = r7.b
            boolean r0 = r0.B()
            if (r0 != 0) goto L_0x03d7
            r7.u()
        L_0x03d7:
            htq r0 = r7.b
            csx r0 = (defpackage.csx) r0
            hvk r8 = defpackage.hvk.a
            r0.n = r8
            htq r0 = r7.b
            boolean r0 = r0.B()
            if (r0 != 0) goto L_0x03ea
            r7.u()
        L_0x03ea:
            htq r0 = r7.b
            csx r0 = (defpackage.csx) r0
            huf r8 = r0.n
            boolean r9 = r8.c()
            if (r9 != 0) goto L_0x03fc
            huf r8 = defpackage.htq.s(r8)
            r0.n = r8
        L_0x03fc:
            huf r0 = r0.n
            defpackage.hrz.g(r4, r0)
            htq r0 = r7.r()
            csx r0 = (defpackage.csx) r0
            cvy r4 = r6.c     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            long r7 = defpackage.cqx.g(r0)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            boolean r7 = defpackage.cqx.q(r7)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            ctg r8 = r1.c
            java.lang.String r9 = "FileGroupManager"
            if (r7 != 0) goto L_0x04ac
            java.lang.String r7 = r8.c     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            boolean r7 = r4.t(r7)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            if (r7 == 0) goto L_0x048b
            r7 = 0
            hme r7 = defpackage.hfc.K(r7)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            ikc r9 = defpackage.ikc.a     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            ikd r9 = r9.a()     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            boolean r9 = r9.h()     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            if (r9 == 0) goto L_0x0452
            csz r9 = r0.l     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            if (r9 != 0) goto L_0x0436
            csz r9 = defpackage.csz.f     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
        L_0x0436:
            int r9 = r9.e     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            int r9 = defpackage.a.A(r9)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            if (r9 != 0) goto L_0x043f
            goto L_0x0452
        L_0x043f:
            r10 = 2
            if (r9 != r10) goto L_0x0452
            cvz r7 = r4.c     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            hme r7 = r7.h(r8)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            cvp r9 = new cvp     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            r10 = 0
            r9.<init>((java.lang.Object) r4, (defpackage.ctg) r8, (defpackage.csx) r0, (int) r10)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            hme r7 = r4.q(r7, r9)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
        L_0x0452:
            czw r7 = defpackage.czw.e(r7)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            cvp r9 = new cvp     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            r10 = 2
            r9.<init>((java.lang.Object) r4, (defpackage.ctg) r8, (defpackage.csx) r0, (int) r10)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            java.util.concurrent.Executor r10 = r4.e     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            czw r7 = r7.g(r9, r10)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            cvp r9 = new cvp     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            r10 = 3
            r9.<init>((java.lang.Object) r4, (defpackage.ctg) r8, (defpackage.csx) r0, (int) r10)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            java.util.concurrent.Executor r0 = r4.e     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            czw r0 = r7.g(r9, r0)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            czw r0 = defpackage.czw.e(r0)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            cvp r4 = new cvp     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            r7 = 19
            r4.<init>((java.lang.Object) r6, (defpackage.ctg) r8, (java.lang.Object) r5, (int) r7)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            java.util.concurrent.Executor r5 = r6.h     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            czw r0 = r0.g(r4, r5)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            cwh r4 = new cwh     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            r4.<init>(r13)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            java.util.concurrent.Executor r5 = r6.h     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            czw r0 = r0.f(r4, r5)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            goto L_0x04e2
        L_0x048b:
            java.lang.String r5 = "%s: Trying to add group %s for uninstalled app %s."
            java.lang.String r7 = r8.b     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            java.lang.String r8 = r8.c     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            r10 = 3
            java.lang.Object[] r10 = new java.lang.Object[r10]     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            r11 = 0
            r10[r11] = r9     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            r10[r13] = r7     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            r7 = 2
            r10[r7] = r8     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            defpackage.cyh.i(r5, r10)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            cyk r4 = r4.i     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            r5 = 1042(0x412, float:1.46E-42)
            defpackage.cvy.z(r5, r4, r0)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            cxf r0 = new cxf     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            r0.<init>()     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            throw r0     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
        L_0x04ac:
            java.lang.String r5 = "%s: Trying to add expired group %s."
            java.lang.String r7 = r8.b     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            defpackage.cyh.h(r5, r9, r7)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            cyk r4 = r4.i     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            r5 = 1048(0x418, float:1.469E-42)
            defpackage.cvy.z(r5, r4, r0)     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            cvg r0 = new cvg     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            r0.<init>()     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
            throw r0     // Catch:{ cvg -> 0x04d6, cxf -> 0x04d4, cux -> 0x04d2, IOException -> 0x04c0 }
        L_0x04c0:
            r0 = move-exception
            java.lang.Class r4 = r0.getClass()
            defpackage.cyh.h(r3, r2, r4)
            cuk r2 = r6.f
            r2.a()
            hme r0 = defpackage.hfc.J(r0)
            goto L_0x04e2
        L_0x04d2:
            r0 = move-exception
            goto L_0x04d7
        L_0x04d4:
            r0 = move-exception
            goto L_0x04d7
        L_0x04d6:
            r0 = move-exception
        L_0x04d7:
            java.lang.Class r4 = r0.getClass()
            defpackage.cyh.n(r3, r2, r4)
            hme r0 = defpackage.hfc.J(r0)
        L_0x04e2:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cwk.a(java.lang.Object):hme");
    }
}
