<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:orientation="horizontal" android:layout_width="match_parent" android:layout_height="match_parent">
    <RelativeLayout android:gravity="bottom" android:layout_gravity="bottom" android:id="@+id/car_ui_ime_carboard_area" android:background="@drawable/car_ui_ime_wide_screen_background" android:paddingBottom="@dimen/car_ui_ime_wide_screen_keyboard_area_padding_bottom" android:layout_width="@dimen/car_ui_ime_wide_screen_keyboard_width" android:layout_height="match_parent" android:paddingStart="@dimen/car_ui_ime_wide_screen_keyboard_area_padding_start" android:paddingEnd="@dimen/car_ui_ime_wide_screen_keyboard_area_padding_end">
        <RelativeLayout android:id="@+id/car_ui_imeWideScreenInputArea" android:layout_width="match_parent" android:layout_height="@dimen/car_ui_ime_wide_screen_input_area_height" android:layout_marginTop="@dimen/car_ui_ime_wide_screen_input_area_padding_top" android:layout_alignParentTop="true">
            <ImageView android:id="@+id/car_ui_closeKeyboard" android:layout_width="@dimen/car_ui_primary_icon_size" android:layout_height="@dimen/car_ui_primary_icon_size" android:layout_alignParentLeft="true" android:layout_centerVertical="true" style="@style/Widget.CarUi.Toolbar.NavIcon"/>
            <FrameLayout android:orientation="vertical" android:id="@+id/car_ui_fullscreenArea" android:background="@drawable/car_ui_ime_wide_screen_input_area_background" android:paddingLeft="@dimen/car_ui_ime_wide_screen_input_padding_start" android:layout_width="match_parent" android:layout_height="match_parent" android:layout_toRightOf="@+id/car_ui_closeKeyboard" android:layout_centerVertical="true">
                <FrameLayout android:gravity="left|center_vertical|center_horizontal|center" android:id="@+id/car_ui_inputExtractEditTextContainer" android:scrollbars="vertical" android:layout_width="match_parent" android:layout_height="match_parent" android:minLines="1" android:layout_weight="1" android:inputType="text" android:paddingEnd="@dimen/car_ui_ime_wide_screen_input_area_padding_end" android:backgroundTint="@drawable/car_ui_ime_wide_screen_input_area_tint_color"/>
                <RelativeLayout android:background="@android:color/transparent" android:layout_width="match_parent" android:layout_height="match_parent">
                    <ImageView android:gravity="center" android:layout_gravity="center" android:id="@+id/car_ui_wideScreenExtractedTextIcon" android:layout_width="@dimen/car_ui_primary_icon_size" android:layout_height="@dimen/car_ui_primary_icon_size" android:layout_alignParentLeft="true" android:layout_centerVertical="true"/>
                    <ImageView android:gravity="center" android:layout_gravity="center" android:id="@+id/car_ui_wideScreenClearData" android:background="@drawable/car_ui_icon_close" android:layout_width="@dimen/car_ui_primary_icon_size" android:layout_height="@dimen/car_ui_primary_icon_size" android:layout_alignParentRight="true" android:layout_centerVertical="true"/>
                    <ImageView android:gravity="center" android:layout_gravity="center" android:id="@+id/car_ui_wideScreenError" android:background="@drawable/car_ui_icon_error" android:visibility="gone" android:layout_width="@dimen/car_ui_primary_icon_size" android:layout_height="@dimen/car_ui_primary_icon_size" android:layout_alignParentRight="true" android:layout_centerVertical="true"/>
                </RelativeLayout>
            </FrameLayout>
        </RelativeLayout>
        <TextView android:textSize="@dimen/car_ui_ime_wide_screen_error_text_size" android:textColor="@color/car_ui_ime_wide_screen_error_text_color" android:id="@+id/car_ui_wideScreenErrorMessage" android:paddingLeft="@dimen/car_ui_ime_wide_screen_error_text_padding_start" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_below="@+id/car_ui_imeWideScreenInputArea" android:layout_alignParentLeft="true"/>
        <FrameLayout android:id="@+id/car_ui_wideScreenInputArea" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_alignParentBottom="true"/>
    </RelativeLayout>
    <View android:background="@color/car_ui_ime_wide_screen_divider_color" android:layout_width="@dimen/car_ui_ime_wide_screen_divider_width" android:layout_height="match_parent"/>
    <SurfaceView android:id="@+id/car_ui_ime_surface" android:background="@drawable/car_ui_ime_wide_screen_content_area_background" android:paddingTop="@dimen/car_ui_ime_wide_screen_input_area_padding_top" android:paddingBottom="@dimen/car_ui_ime_wide_screen_keyboard_area_padding_bottom" android:focusable="false" android:visibility="gone" android:layout_width="match_parent" android:layout_height="match_parent"/>
    <RelativeLayout android:id="@+id/car_ui_contentAreaAutomotive" android:background="@drawable/car_ui_ime_wide_screen_no_content_background" android:visibility="gone" android:layout_width="match_parent" android:layout_height="match_parent">
        <com.android.car.ui.recyclerview.CarUiRecyclerView android:id="@+id/car_ui_wideScreenSearchResultList" android:paddingTop="@dimen/car_ui_ime_wide_screen_input_area_padding_top" android:visibility="gone" android:scrollbars="vertical" android:layout_width="match_parent" android:layout_height="match_parent" android:requiresFadingEdge="vertical"/>
        <TextView android:textSize="@dimen/car_ui_ime_wide_screen_description_title_text_size" android:textColor="@color/car_ui_ime_wide_screen_description_title_color" android:id="@+id/car_ui_wideScreenDescriptionTitle" android:paddingLeft="@dimen/car_ui_ime_wide_screen_description_title_padding_left" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/car_ui_ime_wide_screen_description_title_margin_top" android:layout_alignParentLeft="true"/>
        <TextView android:textSize="@dimen/car_ui_ime_wide_screen_description_text_size" android:textColor="@color/car_ui_ime_wide_screen_description_color" android:id="@+id/car_ui_wideScreenDescription" android:paddingLeft="@dimen/car_ui_ime_wide_screen_description_title_padding_left" android:paddingTop="@dimen/car_ui_ime_wide_screen_description_padding_top" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_below="@+id/car_ui_wideScreenDescriptionTitle" android:layout_alignParentLeft="true"/>
        <Button android:theme="@style/Theme.DeviceDefault" android:textSize="@dimen/car_ui_ime_wide_screen_action_button_text_size" android:layout_gravity="center" android:id="@+id/car_ui_inputExtractActionAutomotive" android:visibility="gone" android:layout_width="wrap_content" android:layout_height="@dimen/car_ui_ime_wide_screen_action_button_height" android:layout_marginLeft="@dimen/car_ui_ime_wide_screen_action_button_margin_left" android:layout_marginBottom="@dimen/car_ui_ime_wide_screen_action_button_margin_bottom" android:layout_alignParentBottom="true"/>
    </RelativeLayout>
</LinearLayout>
