package defpackage;

import android.support.v7.widget.LinearLayoutManager;

/* renamed from: ble  reason: default package */
/* compiled from: PG */
public final class ble implements bld {
    public int a = 1;
    public boolean b = false;

    public static ble d(kl klVar) {
        int i;
        if (klVar == null) {
            return null;
        }
        if (klVar instanceof LinearLayoutManager) {
            ble ble = new ble();
            LinearLayoutManager linearLayoutManager = (LinearLayoutManager) klVar;
            if (linearLayoutManager.k != 0) {
                i = 1;
            } else {
                i = 0;
            }
            ble.a = i;
            ble.b = linearLayoutManager.m;
            return ble;
        }
        throw new AssertionError("LinearLayoutManager required.");
    }

    public final int a() {
        return 0;
    }

    public final int b() {
        return this.a;
    }

    public final int c() {
        return 1;
    }

    public final boolean f() {
        return this.b;
    }
}
