package defpackage;

/* renamed from: crv  reason: default package */
/* compiled from: PG */
public final class crv extends htq implements hvb {
    public static final crv g;
    private static volatile hvh h;
    public int a;
    public String b = "";
    public String c = "";
    public int d;
    public int e;
    public hse f;

    static {
        crv crv = new crv();
        g = crv;
        htq.z(crv.class, crv);
    }

    private crv() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(g, "\u0001\u0005\u0000\u0001\u0001\u0005\u0005\u0000\u0000\u0000\u0001ဈ\u0000\u0002ဈ\u0001\u0003င\u0002\u0004င\u0003\u0005ဉ\u0004", new Object[]{"a", "b", "c", "d", "e", "f"});
        } else if (i2 == 3) {
            return new crv();
        } else {
            if (i2 == 4) {
                return new htk((htq) g);
            }
            if (i2 == 5) {
                return g;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = h;
            if (hvh == null) {
                synchronized (crv.class) {
                    hvh = h;
                    if (hvh == null) {
                        hvh = new htl(g);
                        h = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
