package defpackage;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/* renamed from: cwh  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwh implements gqx {
    private final /* synthetic */ int a;

    public /* synthetic */ cwh(int i) {
        this.a = i;
    }

    public final Object apply(Object obj) {
        switch (this.a) {
            case 0:
                IOException iOException = (IOException) obj;
                boolean z = cwm.a;
                cyh.p("Failed to update days since last maintenance", new Object[0]);
                return grh.h(-1);
            case 1:
                Void voidR = (Void) obj;
                return true;
            case 2:
                grh grh = (grh) obj;
                boolean z2 = cwm.a;
                if (!grh.f()) {
                    return -1;
                }
                Integer num = (Integer) grh.b();
                if (num.intValue() < 0) {
                    return -1;
                }
                return num;
            case 3:
                ArrayList arrayList = new ArrayList();
                for (cxg cxg : (List) obj) {
                    csw csw = cxg.b.b;
                    if (csw == null) {
                        csw = csw.i;
                    }
                    if (!csw.g) {
                        arrayList.add(cxg);
                    }
                }
                return arrayList;
            case 4:
                Void voidR2 = (Void) obj;
                cwm.a = true;
                return null;
            case 5:
                Void voidR3 = (Void) obj;
                return true;
            case 6:
                IOException iOException2 = (IOException) obj;
                return false;
            case 7:
                return ((cte) obj).c;
            case 8:
                cte cte = (cte) obj;
                htk htk = (htk) cte.C(5);
                htk.x(cte);
                if (!htk.b.B()) {
                    htk.u();
                }
                cte cte2 = cte.d;
                ((cte) htk.b).c = hvk.a;
                return (cte) htk.r();
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                Void voidR4 = (Void) obj;
                return true;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                Void voidR5 = (Void) obj;
                return true;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                IOException iOException3 = (IOException) obj;
                return false;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                IOException iOException4 = (IOException) obj;
                return false;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                cte cte3 = (cte) obj;
                htk htk2 = (htk) cte3.C(5);
                htk2.x(cte3);
                if (!htk2.a.B()) {
                    htk2.b = htk2.q();
                    return (cte) htk2.r();
                }
                throw new IllegalArgumentException("Default instance must be immutable.");
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                Void voidR6 = (Void) obj;
                return true;
            case 15:
                IOException iOException5 = (IOException) obj;
                return false;
            case 16:
                Void voidR7 = (Void) obj;
                return true;
            case 17:
                IOException iOException6 = (IOException) obj;
                return false;
            case 18:
                Void voidR8 = (Void) obj;
                return true;
            case 19:
                IOException iOException7 = (IOException) obj;
                return false;
            default:
                Void voidR9 = (Void) obj;
                return true;
        }
    }
}
