package defpackage;

import java.util.Objects;

/* renamed from: bjd  reason: default package */
/* compiled from: PG */
final class bjd extends jmi implements jne {
    final /* synthetic */ bhe a;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bjd(bhe bhe, jlr jlr) {
        super(2, jlr);
        this.a = bhe;
    }

    public final /* bridge */ /* synthetic */ Object b(Object obj, Object obj2) {
        return ((bjd) c((wf) obj, (jlr) obj2)).bk(jkd.a);
    }

    public final Object bk(Object obj) {
        jji.c(obj);
        String str = bje.a;
        bbk.a();
        Objects.toString(this.a);
        return jkd.a;
    }

    public final jlr c(Object obj, jlr jlr) {
        return new bjd(this.a, jlr);
    }
}
