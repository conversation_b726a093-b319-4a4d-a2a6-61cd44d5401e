package defpackage;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

/* renamed from: bfy  reason: default package */
/* compiled from: PG */
public final class bfy extends bfr {
    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bfy(Context context, cyw cyw) {
        super(context, cyw);
        jnu.e(context, "context");
    }

    public final IntentFilter a() {
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("android.intent.action.DEVICE_STORAGE_OK");
        intentFilter.addAction("android.intent.action.DEVICE_STORAGE_LOW");
        return intentFilter;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:11:0x0031, code lost:
        if (r0.equals("android.intent.action.DEVICE_STORAGE_OK") == false) goto L_0x0039;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final /* bridge */ /* synthetic */ java.lang.Object b() {
        /*
            r5 = this;
            android.content.IntentFilter r0 = r5.a()
            android.content.Context r1 = r5.a
            r2 = 0
            android.content.Intent r0 = r1.registerReceiver(r2, r0)
            r1 = 1
            if (r0 == 0) goto L_0x003a
            java.lang.String r2 = r0.getAction()
            if (r2 != 0) goto L_0x0015
            goto L_0x003a
        L_0x0015:
            java.lang.String r0 = r0.getAction()
            r2 = 0
            if (r0 == 0) goto L_0x0039
            int r3 = r0.hashCode()
            r4 = -1181163412(0xffffffffb998e06c, float:-2.9158907E-4)
            if (r3 == r4) goto L_0x0034
            r4 = -730838620(0xffffffffd47049a4, float:-4.12811054E12)
            if (r3 == r4) goto L_0x002b
            goto L_0x0039
        L_0x002b:
            java.lang.String r3 = "android.intent.action.DEVICE_STORAGE_OK"
            boolean r0 = r0.equals(r3)
            if (r0 != 0) goto L_0x003a
            goto L_0x0039
        L_0x0034:
            java.lang.String r1 = "android.intent.action.DEVICE_STORAGE_LOW"
            r0.equals(r1)
        L_0x0039:
            r1 = r2
        L_0x003a:
            java.lang.Boolean r0 = java.lang.Boolean.valueOf(r1)
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bfy.b():java.lang.Object");
    }

    public final void c(Intent intent) {
        jnu.e(intent, "intent");
        if (intent.getAction() != null) {
            bbk.a();
            int i = bfz.a;
            intent.getAction();
            String action = intent.getAction();
            if (action != null) {
                int hashCode = action.hashCode();
                if (hashCode != -1181163412) {
                    if (hashCode == -730838620 && action.equals("android.intent.action.DEVICE_STORAGE_OK")) {
                        f(true);
                    }
                } else if (action.equals("android.intent.action.DEVICE_STORAGE_LOW")) {
                    f(false);
                }
            }
        }
    }
}
