package defpackage;

/* renamed from: cyg  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cyg implements gqx {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ cyg(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r12v105, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v57, resolved type: dum} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r12v109, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v46, resolved type: dum} */
    /* JADX WARNING: type inference failed for: r0v50, types: [java.lang.Object, java.util.concurrent.ConcurrentMap] */
    /* JADX WARNING: type inference failed for: r0v65, types: [java.lang.Object, ihn] */
    /* JADX WARNING: type inference failed for: r12v123, types: [java.util.Set, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v71, types: [java.lang.Object, jna] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object apply(java.lang.Object r12) {
        /*
            r11 = this;
            int r0 = r11.b
            r1 = 2
            r2 = 4
            r3 = 3
            r4 = 0
            r5 = 0
            r6 = 5
            r7 = 1
            switch(r0) {
                case 0: goto L_0x04b0;
                case 1: goto L_0x045b;
                case 2: goto L_0x03cd;
                case 3: goto L_0x0358;
                case 4: goto L_0x0349;
                case 5: goto L_0x0319;
                case 6: goto L_0x030a;
                case 7: goto L_0x024e;
                case 8: goto L_0x0224;
                case 9: goto L_0x0207;
                case 10: goto L_0x01e5;
                case 11: goto L_0x01cc;
                case 12: goto L_0x01aa;
                case 13: goto L_0x019b;
                case 14: goto L_0x0113;
                case 15: goto L_0x00f9;
                case 16: goto L_0x00dc;
                case 17: goto L_0x0043;
                case 18: goto L_0x0030;
                case 19: goto L_0x0013;
                default: goto L_0x000c;
            }
        L_0x000c:
            java.lang.Object r0 = r11.a
            java.lang.Object r12 = r0.a(r12)
            return r12
        L_0x0013:
            java.lang.Void r12 = (java.lang.Void) r12
            java.lang.Object r12 = r11.a
            bmu r12 = (defpackage.bmu) r12
            java.lang.Object r12 = r12.a
            java.util.Iterator r12 = r12.iterator()
        L_0x001f:
            boolean r0 = r12.hasNext()
            if (r0 == 0) goto L_0x002f
            java.lang.Object r0 = r12.next()
            dxf r0 = (defpackage.dxf) r0
            r0.c()
            goto L_0x001f
        L_0x002f:
            return r4
        L_0x0030:
            hva r12 = (defpackage.hva) r12
            java.lang.Object r0 = r11.a
            bmu r0 = (defpackage.bmu) r0
            java.lang.Object r0 = r0.c
            java.lang.Object r0 = r0.b()
            cad r0 = (defpackage.cad) r0
            cab r12 = r0.f(r12)
            return r12
        L_0x0043:
            dul r12 = (defpackage.dul) r12
            hoj r0 = new hoj
            r0.<init>()
            android.os.StrictMode$ThreadPolicy r1 = android.os.StrictMode.getThreadPolicy()
            android.os.StrictMode$ThreadPolicy$Builder r2 = new android.os.StrictMode$ThreadPolicy$Builder
            r2.<init>(r1)
            android.os.StrictMode$ThreadPolicy$Builder r2 = r2.permitDiskWrites()
            android.os.StrictMode$ThreadPolicy r2 = r2.build()
            android.os.StrictMode.setThreadPolicy(r2)
            java.lang.Object r2 = r11.a
            java.lang.Object r3 = defpackage.dvr.a     // Catch:{ IOException -> 0x00d1 }
            monitor-enter(r3)     // Catch:{ IOException -> 0x00d1 }
            r6 = r2
            dvr r6 = (defpackage.dvr) r6     // Catch:{ all -> 0x00cc }
            gsb r6 = r6.e     // Catch:{ all -> 0x00cc }
            java.lang.Object r6 = r6.a()     // Catch:{ all -> 0x00cc }
            kjd r6 = (defpackage.kjd) r6     // Catch:{ all -> 0x00cc }
            r8 = r2
            dvr r8 = (defpackage.dvr) r8     // Catch:{ all -> 0x00cc }
            android.net.Uri r8 = r8.g     // Catch:{ all -> 0x00cc }
            dui r9 = r12.b     // Catch:{ all -> 0x00cc }
            if (r9 != 0) goto L_0x0079
            dui r9 = defpackage.dui.k     // Catch:{ all -> 0x00cc }
        L_0x0079:
            fot r10 = new fot     // Catch:{ all -> 0x00cc }
            r10.<init>(r9)     // Catch:{ all -> 0x00cc }
            hoj[] r9 = new defpackage.hoj[r7]     // Catch:{ all -> 0x00cc }
            r9[r5] = r0     // Catch:{ all -> 0x00cc }
            r10.a = r9     // Catch:{ all -> 0x00cc }
            r6.e(r8, r10)     // Catch:{ all -> 0x00cc }
            dui r6 = r12.b     // Catch:{ all -> 0x00cc }
            if (r6 != 0) goto L_0x008d
            dui r6 = defpackage.dui.k     // Catch:{ all -> 0x00cc }
        L_0x008d:
            r8 = r2
            dvr r8 = (defpackage.dvr) r8     // Catch:{ all -> 0x00cc }
            r8.h = r6     // Catch:{ all -> 0x00cc }
            monitor-exit(r3)     // Catch:{ all -> 0x00cc }
            java.lang.Object r3 = defpackage.dvr.b     // Catch:{ IOException -> 0x00d1 }
            monitor-enter(r3)     // Catch:{ IOException -> 0x00d1 }
            r6 = r2
            dvr r6 = (defpackage.dvr) r6     // Catch:{ all -> 0x00c9 }
            gsb r6 = r6.e     // Catch:{ all -> 0x00c9 }
            java.lang.Object r6 = r6.a()     // Catch:{ all -> 0x00c9 }
            kjd r6 = (defpackage.kjd) r6     // Catch:{ all -> 0x00c9 }
            r8 = r2
            dvr r8 = (defpackage.dvr) r8     // Catch:{ all -> 0x00c9 }
            android.net.Uri r8 = r8.i     // Catch:{ all -> 0x00c9 }
            duj r9 = r12.c     // Catch:{ all -> 0x00c9 }
            if (r9 != 0) goto L_0x00ac
            duj r9 = defpackage.duj.j     // Catch:{ all -> 0x00c9 }
        L_0x00ac:
            fot r10 = new fot     // Catch:{ all -> 0x00c9 }
            r10.<init>(r9)     // Catch:{ all -> 0x00c9 }
            hoj[] r7 = new defpackage.hoj[r7]     // Catch:{ all -> 0x00c9 }
            r7[r5] = r0     // Catch:{ all -> 0x00c9 }
            r10.a = r7     // Catch:{ all -> 0x00c9 }
            r6.e(r8, r10)     // Catch:{ all -> 0x00c9 }
            duj r12 = r12.c     // Catch:{ all -> 0x00c9 }
            if (r12 != 0) goto L_0x00c0
            duj r12 = defpackage.duj.j     // Catch:{ all -> 0x00c9 }
        L_0x00c0:
            dvr r2 = (defpackage.dvr) r2     // Catch:{ all -> 0x00c9 }
            r2.j = r12     // Catch:{ all -> 0x00c9 }
            monitor-exit(r3)     // Catch:{ all -> 0x00c9 }
            android.os.StrictMode.setThreadPolicy(r1)
            return r4
        L_0x00c9:
            r12 = move-exception
            monitor-exit(r3)     // Catch:{ all -> 0x00c9 }
            throw r12     // Catch:{ IOException -> 0x00d1 }
        L_0x00cc:
            r12 = move-exception
            monitor-exit(r3)     // Catch:{ all -> 0x00cc }
            throw r12     // Catch:{ IOException -> 0x00d1 }
        L_0x00cf:
            r12 = move-exception
            goto L_0x00d8
        L_0x00d1:
            r12 = move-exception
            java.lang.RuntimeException r0 = new java.lang.RuntimeException     // Catch:{ all -> 0x00cf }
            r0.<init>(r12)     // Catch:{ all -> 0x00cf }
            throw r0     // Catch:{ all -> 0x00cf }
        L_0x00d8:
            android.os.StrictMode.setThreadPolicy(r1)
            throw r12
        L_0x00dc:
            dup r12 = (defpackage.dup) r12
            java.util.concurrent.ConcurrentMap r0 = defpackage.duy.a
            java.lang.Object r0 = r11.a
            dum r1 = defpackage.dum.d
            r0.getClass()
            huv r12 = r12.a
            boolean r2 = r12.containsKey(r0)
            if (r2 == 0) goto L_0x00f6
            java.lang.Object r12 = r12.get(r0)
            r1 = r12
            dum r1 = (defpackage.dum) r1
        L_0x00f6:
            java.lang.String r12 = r1.c
            return r12
        L_0x00f9:
            dup r12 = (defpackage.dup) r12
            java.util.concurrent.ConcurrentMap r0 = defpackage.duy.a
            dum r0 = defpackage.dum.d
            huv r12 = r12.a
            java.lang.Object r1 = r11.a
            boolean r2 = r12.containsKey(r1)
            if (r2 == 0) goto L_0x0110
            java.lang.Object r12 = r12.get(r1)
            r0 = r12
            dum r0 = (defpackage.dum) r0
        L_0x0110:
            huf r12 = r0.b
            return r12
        L_0x0113:
            dup r12 = (defpackage.dup) r12
            java.util.concurrent.ConcurrentMap r0 = defpackage.duy.a
            dup r0 = defpackage.dup.b
            htk r0 = r0.l()
            huv r12 = r12.a
            java.util.Map r12 = java.util.Collections.unmodifiableMap(r12)
            java.util.Set r12 = r12.entrySet()
            java.util.Iterator r12 = r12.iterator()
        L_0x012b:
            boolean r1 = r12.hasNext()
            if (r1 == 0) goto L_0x0194
            java.lang.Object r1 = r11.a
            java.lang.Object r2 = r12.next()
            java.util.Map$Entry r2 = (java.util.Map.Entry) r2
            java.lang.Object r3 = r2.getValue()
            dum r3 = (defpackage.dum) r3
            dum r4 = defpackage.dum.d
            htk r4 = r4.l()
            java.lang.String r5 = r3.c
            boolean r5 = r5.equals(r1)
            if (r5 != 0) goto L_0x0168
            java.lang.String r5 = r3.c
            htq r6 = r4.b
            boolean r6 = r6.B()
            if (r6 != 0) goto L_0x015a
            r4.u()
        L_0x015a:
            htq r6 = r4.b
            dum r6 = (defpackage.dum) r6
            r5.getClass()
            int r8 = r6.a
            r8 = r8 | r7
            r6.a = r8
            r6.c = r5
        L_0x0168:
            huf r3 = r3.b
            java.util.Iterator r3 = r3.iterator()
        L_0x016e:
            boolean r5 = r3.hasNext()
            if (r5 == 0) goto L_0x0184
            java.lang.Object r5 = r3.next()
            java.lang.String r5 = (java.lang.String) r5
            boolean r6 = r5.equals(r1)
            if (r6 != 0) goto L_0x016e
            r4.F(r5)
            goto L_0x016e
        L_0x0184:
            java.lang.Object r1 = r2.getKey()
            java.lang.String r1 = (java.lang.String) r1
            htq r2 = r4.r()
            dum r2 = (defpackage.dum) r2
            r0.G(r1, r2)
            goto L_0x012b
        L_0x0194:
            htq r12 = r0.r()
            dup r12 = (defpackage.dup) r12
            return r12
        L_0x019b:
            java.lang.String r12 = (java.lang.String) r12
            java.lang.Object r0 = r11.a
            alx r0 = (defpackage.alx) r0
            java.lang.Object r0 = r0.c
            java.lang.Object r12 = r0.get(r12)
            duq r12 = (defpackage.duq) r12
            return r12
        L_0x01aa:
            iih r12 = (defpackage.iih) r12
            java.lang.Object r12 = r12.a
            clp r12 = (defpackage.clp) r12
            cll r0 = r12.a
            defpackage.cgr.ah(r0)
            cll r12 = r12.a
            int r12 = r12.a
            if (r12 == r7) goto L_0x01bd
            if (r12 != r3) goto L_0x01be
        L_0x01bd:
            r5 = r7
        L_0x01be:
            java.lang.Object r12 = r11.a
            java.lang.Boolean r0 = java.lang.Boolean.valueOf(r5)
            dsi r12 = (defpackage.dsi) r12
            java.util.concurrent.atomic.AtomicReference r12 = r12.a
            r12.set(r0)
            return r0
        L_0x01cc:
            java.lang.String r12 = (java.lang.String) r12
            java.lang.ProcessBuilder r0 = new java.lang.ProcessBuilder     // Catch:{ IOException -> 0x01de }
            java.lang.String r1 = "/system/bin/trigger_perfetto"
            java.lang.String[] r12 = new java.lang.String[]{r1, r12}     // Catch:{ IOException -> 0x01de }
            r0.<init>(r12)     // Catch:{ IOException -> 0x01de }
            java.lang.Process r4 = r0.start()     // Catch:{ IOException -> 0x01de }
            goto L_0x01e4
        L_0x01de:
            java.lang.Object r12 = r11.a
            dmj r12 = (defpackage.dmj) r12
            r12.b = r7
        L_0x01e4:
            return r4
        L_0x01e5:
            java.lang.Throwable r12 = (java.lang.Throwable) r12
            java.lang.Object r12 = r11.a
            czz r12 = (defpackage.czz) r12
            java.util.Set r12 = r12.a
            java.util.Iterator r12 = r12.iterator()
        L_0x01f1:
            boolean r0 = r12.hasNext()
            if (r0 == 0) goto L_0x0201
            java.lang.Object r0 = r12.next()
            dag r0 = (defpackage.dag) r0
            r0.b()
            goto L_0x01f1
        L_0x0201:
            bbg r12 = new bbg
            r12.<init>()
            return r12
        L_0x0207:
            androidx.wear.ambient.AmbientLifecycleObserverKt r12 = (androidx.wear.ambient.AmbientLifecycleObserverKt) r12
            java.lang.Object r0 = r11.a
            czz r0 = (defpackage.czz) r0
            java.util.Set r0 = r0.a
            java.util.Iterator r0 = r0.iterator()
        L_0x0213:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L_0x0223
            java.lang.Object r1 = r0.next()
            dag r1 = (defpackage.dag) r1
            r1.a()     // Catch:{ Exception -> 0x0213 }
            goto L_0x0213
        L_0x0223:
            return r12
        L_0x0224:
            android.net.Uri r12 = (android.net.Uri) r12
            java.lang.Object r0 = r11.a
            if (r12 == 0) goto L_0x0247
            eix r0 = (defpackage.eix) r0     // Catch:{ IOException -> 0x0239 }
            java.lang.Object r0 = r0.b     // Catch:{ IOException -> 0x0239 }
            kjd r0 = (defpackage.kjd) r0     // Catch:{ IOException -> 0x0239 }
            long r2 = r0.c(r12)     // Catch:{ IOException -> 0x0239 }
            java.lang.Long r12 = java.lang.Long.valueOf(r2)     // Catch:{ IOException -> 0x0239 }
            goto L_0x024d
        L_0x0239:
            r0 = move-exception
            java.lang.Object[] r1 = new java.lang.Object[r1]
            java.lang.String r2 = "StorageLogger"
            r1[r5] = r2
            r1[r7] = r12
            java.lang.String r12 = "%s: Failed to call mobstore fileSize on uri %s!"
            defpackage.cyh.j(r0, r12, r1)
        L_0x0247:
            r0 = 0
            java.lang.Long r12 = java.lang.Long.valueOf(r0)
        L_0x024d:
            return r12
        L_0x024e:
            cti r12 = (defpackage.cti) r12
            int r0 = defpackage.cyn.d
            huf r0 = r12.c
            cyb r1 = new cyb
            java.lang.Object r2 = r11.a
            r1.<init>(r2, r3)
            java.util.Iterator r0 = r0.iterator()
            int r0 = defpackage.fvf.F(r0, r1)
            r1 = -1
            if (r0 != r1) goto L_0x0290
            java.lang.Object r0 = r12.C(r6)
            htk r0 = (defpackage.htk) r0
            r0.x(r12)
            htq r12 = r0.b
            boolean r12 = r12.B()
            if (r12 != 0) goto L_0x027a
            r0.u()
        L_0x027a:
            htq r12 = r0.b
            cti r12 = (defpackage.cti) r12
            r2.getClass()
            r12.b()
            huf r12 = r12.c
            r12.add(r2)
            htq r12 = r0.r()
            cti r12 = (defpackage.cti) r12
            goto L_0x0309
        L_0x0290:
            huf r1 = r12.c
            java.lang.Object r1 = r1.get(r0)
            ctb r1 = (defpackage.ctb) r1
            java.lang.Object r3 = r1.C(r6)
            htk r3 = (defpackage.htk) r3
            r3.x(r1)
            long r4 = r1.f
            ctb r2 = (defpackage.ctb) r2
            long r7 = r2.f
            long r4 = r4 + r7
            htq r7 = r3.b
            boolean r7 = r7.B()
            if (r7 != 0) goto L_0x02b3
            r3.u()
        L_0x02b3:
            htq r7 = r3.b
            r8 = r7
            ctb r8 = (defpackage.ctb) r8
            int r9 = r8.a
            r9 = r9 | 16
            r8.a = r9
            r8.f = r4
            long r4 = r1.g
            long r1 = r2.g
            long r4 = r4 + r1
            boolean r1 = r7.B()
            if (r1 != 0) goto L_0x02ce
            r3.u()
        L_0x02ce:
            htq r1 = r3.b
            ctb r1 = (defpackage.ctb) r1
            int r2 = r1.a
            r2 = r2 | 32
            r1.a = r2
            r1.g = r4
            htq r1 = r3.r()
            ctb r1 = (defpackage.ctb) r1
            java.lang.Object r2 = r12.C(r6)
            htk r2 = (defpackage.htk) r2
            r2.x(r12)
            htq r12 = r2.b
            boolean r12 = r12.B()
            if (r12 != 0) goto L_0x02f4
            r2.u()
        L_0x02f4:
            htq r12 = r2.b
            cti r12 = (defpackage.cti) r12
            r1.getClass()
            r12.b()
            huf r12 = r12.c
            r12.set(r0, r1)
            htq r12 = r2.r()
            cti r12 = (defpackage.cti) r12
        L_0x0309:
            return r12
        L_0x030a:
            java.lang.Void r12 = (java.lang.Void) r12
            int r12 = defpackage.cyn.d
            java.lang.Object r12 = r11.a
            java.util.concurrent.atomic.AtomicReference r12 = (java.util.concurrent.atomic.AtomicReference) r12
            java.lang.Object r12 = r12.get()
            java.util.List r12 = (java.util.List) r12
            return r12
        L_0x0319:
            cti r12 = (defpackage.cti) r12
            int r0 = defpackage.cyn.d
            huf r0 = r12.c
            java.lang.Object r1 = r11.a
            java.util.concurrent.atomic.AtomicReference r1 = (java.util.concurrent.atomic.AtomicReference) r1
            r1.set(r0)
            java.lang.Object r0 = r12.C(r6)
            htk r0 = (defpackage.htk) r0
            r0.x(r12)
            htq r12 = r0.b
            boolean r12 = r12.B()
            if (r12 != 0) goto L_0x033a
            r0.u()
        L_0x033a:
            htq r12 = r0.b
            cti r12 = (defpackage.cti) r12
            hvk r1 = defpackage.hvk.a
            r12.c = r1
            htq r12 = r0.r()
            cti r12 = (defpackage.cti) r12
            return r12
        L_0x0349:
            java.lang.Void r12 = (java.lang.Void) r12
            int r12 = defpackage.cyn.d
            java.lang.Object r12 = r11.a
            java.util.concurrent.atomic.AtomicReference r12 = (java.util.concurrent.atomic.AtomicReference) r12
            java.lang.Object r12 = r12.get()
            grh r12 = (defpackage.grh) r12
            return r12
        L_0x0358:
            cti r12 = (defpackage.cti) r12
            long r0 = defpackage.cqx.c()
            java.lang.Long r2 = java.lang.Long.valueOf(r0)
            hvw r3 = r12.b
            if (r3 != 0) goto L_0x0368
            hvw r3 = defpackage.hvw.c
        L_0x0368:
            long r3 = defpackage.hwr.a(r3)
            java.lang.Long r5 = java.lang.Long.valueOf(r3)
            java.lang.Object r6 = r12.C(r6)
            htk r6 = (defpackage.htk) r6
            r6.x(r12)
            r2.getClass()
            hvw r8 = defpackage.hwr.b(r0)
            htq r9 = r6.b
            boolean r9 = r9.B()
            if (r9 != 0) goto L_0x038b
            r6.u()
        L_0x038b:
            htq r9 = r6.b
            cti r9 = (defpackage.cti) r9
            r8.getClass()
            r9.b = r8
            int r8 = r9.a
            r8 = r8 | r7
            r9.a = r8
            htq r6 = r6.r()
            cti r6 = (defpackage.cti) r6
            int r12 = r12.a
            r12 = r12 & r7
            if (r12 == 0) goto L_0x03cc
            java.lang.Object r12 = r11.a
            r2.getClass()
            long r0 = defpackage.cyn.e(r0)
            r5.getClass()
            long r2 = defpackage.cyn.e(r3)
            long r0 = r0 - r2
            java.util.concurrent.TimeUnit r2 = java.util.concurrent.TimeUnit.MILLISECONDS
            long r0 = r2.toDays(r0)
            int r0 = defpackage.hfc.ae(r0)
            java.lang.Integer r0 = java.lang.Integer.valueOf(r0)
            grh r0 = defpackage.grh.h(r0)
            java.util.concurrent.atomic.AtomicReference r12 = (java.util.concurrent.atomic.AtomicReference) r12
            r12.set(r0)
        L_0x03cc:
            return r6
        L_0x03cd:
            cti r12 = (defpackage.cti) r12
            ctk r0 = r12.e
            if (r0 != 0) goto L_0x03d5
            ctk r0 = defpackage.ctk.d
        L_0x03d5:
            int r0 = r0.a
            r0 = r0 & r7
            if (r0 == 0) goto L_0x03dc
            goto L_0x045a
        L_0x03dc:
            java.lang.Object r0 = r11.a
            cyn r0 = (defpackage.cyn) r0
            java.util.Random r0 = r0.b
            long r3 = r0.nextLong()
            java.lang.Object r0 = r12.C(r6)
            htk r0 = (defpackage.htk) r0
            r0.x(r12)
            ctk r12 = r12.e
            if (r12 != 0) goto L_0x03f5
            ctk r12 = defpackage.ctk.d
        L_0x03f5:
            java.lang.Object r5 = r12.C(r6)
            htk r5 = (defpackage.htk) r5
            r5.x(r12)
            htq r12 = r5.b
            boolean r12 = r12.B()
            if (r12 != 0) goto L_0x0409
            r5.u()
        L_0x0409:
            htq r12 = r5.b
            ctk r12 = (defpackage.ctk) r12
            int r6 = r12.a
            r6 = r6 | r7
            r12.a = r6
            r12.b = r3
            long r3 = defpackage.cqx.c()
            hvw r12 = defpackage.hwr.b(r3)
            htq r3 = r5.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x0427
            r5.u()
        L_0x0427:
            htq r3 = r5.b
            ctk r3 = (defpackage.ctk) r3
            r12.getClass()
            r3.c = r12
            int r12 = r3.a
            r12 = r12 | r1
            r3.a = r12
            htq r12 = r0.b
            boolean r12 = r12.B()
            if (r12 != 0) goto L_0x0440
            r0.u()
        L_0x0440:
            htq r12 = r0.b
            cti r12 = (defpackage.cti) r12
            htq r1 = r5.r()
            ctk r1 = (defpackage.ctk) r1
            r1.getClass()
            r12.e = r1
            int r1 = r12.a
            r1 = r1 | r2
            r12.a = r1
            htq r12 = r0.r()
            cti r12 = (defpackage.cti) r12
        L_0x045a:
            return r12
        L_0x045b:
            cvx r12 = (defpackage.cvx) r12
            java.lang.Object r0 = r11.a
            cvx r1 = defpackage.cvx.DOWNLOADED
            if (r12 == r1) goto L_0x0488
            cvx r1 = defpackage.cvx.PENDING
            if (r12 != r1) goto L_0x0468
            goto L_0x0488
        L_0x0468:
            r12 = r0
            htk r12 = (defpackage.htk) r12
            htq r1 = r12.b
            boolean r1 = r1.B()
            if (r1 != 0) goto L_0x0476
            r12.u()
        L_0x0476:
            htq r12 = r12.b
            hin r12 = (defpackage.hin) r12
            hin r1 = defpackage.hin.f
            int r1 = defpackage.a.E(r6)
            r12.b = r1
            int r1 = r12.a
            r1 = r1 | r7
            r12.a = r1
            goto L_0x04a7
        L_0x0488:
            r12 = r0
            htk r12 = (defpackage.htk) r12
            htq r1 = r12.b
            boolean r1 = r1.B()
            if (r1 != 0) goto L_0x0496
            r12.u()
        L_0x0496:
            htq r12 = r12.b
            hin r12 = (defpackage.hin) r12
            hin r1 = defpackage.hin.f
            int r1 = defpackage.a.E(r2)
            r12.b = r1
            int r1 = r12.a
            r1 = r1 | r7
            r12.a = r1
        L_0x04a7:
            htk r0 = (defpackage.htk) r0
            htq r12 = r0.r()
            hin r12 = (defpackage.hin) r12
            return r12
        L_0x04b0:
            hin r12 = (defpackage.hin) r12
            java.lang.Object r0 = r11.a
            cye r1 = new cye
            hig r0 = (defpackage.hig) r0
            r1.<init>(r12, r0)
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cyg.apply(java.lang.Object):java.lang.Object");
    }
}
