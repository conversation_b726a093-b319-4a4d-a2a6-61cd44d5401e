package defpackage;

import android.database.Cursor;
import java.util.concurrent.Callable;

/* renamed from: bhp  reason: default package */
/* compiled from: PG */
public final class bhp implements Callable {
    final /* synthetic */ auu a;
    final /* synthetic */ bhx b;

    public bhp(bhx bhx, auu auu) {
        this.b = bhx;
        this.a = auu;
    }

    /* renamed from: a */
    public final Boolean call() {
        boolean z;
        boolean z2 = false;
        Cursor f = vy.f(this.b.a, this.a, false);
        try {
            if (f.moveToFirst()) {
                if (f.getInt(0) != 0) {
                    z2 = true;
                }
                z = Boolean.valueOf(z2);
            } else {
                z = false;
            }
            return z;
        } finally {
            f.close();
        }
    }

    /* access modifiers changed from: protected */
    public final void finalize() {
        this.a.j();
    }
}
