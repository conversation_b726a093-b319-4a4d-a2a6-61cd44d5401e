package defpackage;

import java.util.concurrent.atomic.AtomicBoolean;

/* renamed from: cro  reason: default package */
/* compiled from: PG */
public final class cro {
    private static final AtomicBoolean a = new AtomicBoolean(false);

    public static void a() {
        if (a.compareAndSet(false, true)) {
            hte a2 = hte.a();
            a.w(a2, "newRegistry");
            jhz.a = a2;
        }
    }
}
