package defpackage;

/* renamed from: emf  reason: default package */
/* compiled from: PG */
public final class emf implements iiu {
    private final jjk a;
    private final jjk b;

    public emf(jjk jjk, jjk jjk2) {
        this.a = jjk;
        this.b = jjk2;
    }

    /* renamed from: a */
    public final emd b() {
        grh grh = (grh) ((iiv) this.a).a;
        emd emd = (emd) this.b.b();
        jnu.e(grh, "monitoringLoggerOverride");
        jnu.e(emd, "defaultMonitoringLogger");
        return (emd) grh.d(emd);
    }
}
