package defpackage;

/* renamed from: cyc  reason: default package */
/* compiled from: PG */
public final class cyc {
    public final Long a;

    public cyc(Long l) {
        this.a = l;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof cyc) {
            return this.a.equals(((cyc) obj).a);
        }
        return false;
    }

    public final int hashCode() {
        return this.a.hashCode();
    }
}
