package j$.time.format;

import j$.time.chrono.n;
import j$.time.chrono.u;
import j$.time.temporal.a;
import j$.time.temporal.h;
import j$.time.temporal.m;
import j$.util.Objects;
import java.io.IOException;
import java.util.HashMap;
import java.util.Locale;

public final class DateTimeFormatter {
    public static final DateTimeFormatter e;
    private final e a;
    private final Locale b;
    private final y c;
    private final u d;

    static {
        s sVar = new s();
        a aVar = a.YEAR;
        A a2 = A.EXCEEDS_PAD;
        sVar.p(aVar, 4, 10, a2);
        sVar.e('-');
        a aVar2 = a.MONTH_OF_YEAR;
        sVar.o(aVar2, 2);
        sVar.e('-');
        a aVar3 = a.DAY_OF_MONTH;
        sVar.o(aVar3, 2);
        z zVar = z.STRICT;
        u uVar = u.d;
        DateTimeFormatter y = sVar.y(zVar, uVar);
        s sVar2 = new s();
        sVar2.t();
        sVar2.a(y);
        sVar2.i();
        sVar2.y(zVar, uVar);
        s sVar3 = new s();
        sVar3.t();
        sVar3.a(y);
        sVar3.s();
        sVar3.i();
        sVar3.y(zVar, uVar);
        s sVar4 = new s();
        a aVar4 = a.HOUR_OF_DAY;
        sVar4.o(aVar4, 2);
        sVar4.e(':');
        a aVar5 = a.MINUTE_OF_HOUR;
        sVar4.o(aVar5, 2);
        sVar4.s();
        sVar4.e(':');
        a aVar6 = a.SECOND_OF_MINUTE;
        sVar4.o(aVar6, 2);
        sVar4.s();
        sVar4.b(a.NANO_OF_SECOND, 0, 9, true);
        DateTimeFormatter y2 = sVar4.y(zVar, (u) null);
        s sVar5 = new s();
        sVar5.t();
        sVar5.a(y2);
        sVar5.i();
        sVar5.y(zVar, (u) null);
        s sVar6 = new s();
        sVar6.t();
        sVar6.a(y2);
        sVar6.s();
        sVar6.i();
        sVar6.y(zVar, (u) null);
        s sVar7 = new s();
        sVar7.t();
        sVar7.a(y);
        sVar7.e('T');
        sVar7.a(y2);
        DateTimeFormatter y3 = sVar7.y(zVar, uVar);
        s sVar8 = new s();
        sVar8.t();
        sVar8.a(y3);
        sVar8.v();
        sVar8.i();
        sVar8.w();
        DateTimeFormatter y4 = sVar8.y(zVar, uVar);
        s sVar9 = new s();
        sVar9.a(y4);
        sVar9.s();
        sVar9.e('[');
        sVar9.u();
        sVar9.q();
        sVar9.e(']');
        sVar9.y(zVar, uVar);
        s sVar10 = new s();
        sVar10.a(y3);
        sVar10.s();
        sVar10.i();
        sVar10.s();
        sVar10.e('[');
        sVar10.u();
        sVar10.q();
        sVar10.e(']');
        sVar10.y(zVar, uVar);
        s sVar11 = new s();
        sVar11.t();
        sVar11.p(aVar, 4, 10, a2);
        sVar11.e('-');
        sVar11.o(a.DAY_OF_YEAR, 3);
        sVar11.s();
        sVar11.i();
        sVar11.y(zVar, uVar);
        s sVar12 = new s();
        sVar12.t();
        sVar12.p(h.c, 4, 10, a2);
        sVar12.f("-W");
        sVar12.o(h.b, 2);
        sVar12.e('-');
        a aVar7 = a.DAY_OF_WEEK;
        sVar12.o(aVar7, 1);
        sVar12.s();
        sVar12.i();
        sVar12.y(zVar, uVar);
        s sVar13 = new s();
        sVar13.t();
        sVar13.c();
        e = sVar13.y(zVar, (u) null);
        s sVar14 = new s();
        sVar14.t();
        sVar14.o(aVar, 4);
        sVar14.o(aVar2, 2);
        sVar14.o(aVar3, 2);
        sVar14.s();
        sVar14.v();
        sVar14.h("+HHMMss", "Z");
        sVar14.w();
        sVar14.y(zVar, uVar);
        HashMap hashMap = new HashMap();
        hashMap.put(1L, "Mon");
        hashMap.put(2L, "Tue");
        hashMap.put(3L, "Wed");
        hashMap.put(4L, "Thu");
        hashMap.put(5L, "Fri");
        hashMap.put(6L, "Sat");
        u uVar2 = uVar;
        hashMap.put(7L, "Sun");
        HashMap hashMap2 = new HashMap();
        hashMap2.put(1L, "Jan");
        hashMap2.put(2L, "Feb");
        hashMap2.put(3L, "Mar");
        hashMap2.put(4L, "Apr");
        hashMap2.put(5L, "May");
        hashMap2.put(6L, "Jun");
        hashMap2.put(7L, "Jul");
        hashMap2.put(8L, "Aug");
        hashMap2.put(9L, "Sep");
        hashMap2.put(10L, "Oct");
        hashMap2.put(11L, "Nov");
        hashMap2.put(12L, "Dec");
        s sVar15 = new s();
        sVar15.t();
        sVar15.v();
        sVar15.s();
        sVar15.k(aVar7, hashMap);
        sVar15.f(", ");
        sVar15.r();
        sVar15.p(aVar3, 1, 2, A.NOT_NEGATIVE);
        sVar15.e(' ');
        sVar15.k(aVar2, hashMap2);
        sVar15.e(' ');
        sVar15.o(aVar, 4);
        sVar15.e(' ');
        sVar15.o(aVar4, 2);
        sVar15.e(':');
        sVar15.o(aVar5, 2);
        sVar15.s();
        sVar15.e(':');
        sVar15.o(aVar6, 2);
        sVar15.r();
        sVar15.e(' ');
        sVar15.h("+HHMM", "GMT");
        sVar15.y(z.SMART, uVar2);
    }

    DateTimeFormatter(e eVar, Locale locale, z zVar, u uVar) {
        y yVar = y.a;
        this.a = (e) Objects.requireNonNull(eVar, "printerParser");
        this.b = (Locale) Objects.requireNonNull(locale, "locale");
        this.c = (y) Objects.requireNonNull(yVar, "decimalStyle");
        z zVar2 = (z) Objects.requireNonNull(zVar, "resolverStyle");
        this.d = uVar;
    }

    public static DateTimeFormatter ofPattern(String str) {
        s sVar = new s();
        sVar.j(str);
        return sVar.x();
    }

    public final String a(m mVar) {
        StringBuilder sb = new StringBuilder(32);
        Objects.requireNonNull(mVar, "temporal");
        Objects.requireNonNull(sb, "appendable");
        try {
            this.a.g(new u(mVar, this), sb);
            return sb.toString();
        } catch (IOException e2) {
            throw new RuntimeException(e2.getMessage(), e2);
        }
    }

    public final n b() {
        return this.d;
    }

    public final y c() {
        return this.c;
    }

    public final Locale d() {
        return this.b;
    }

    /* access modifiers changed from: package-private */
    public final e e() {
        return this.a.a();
    }

    public final String toString() {
        String eVar = this.a.toString();
        if (eVar.startsWith("[")) {
            return eVar;
        }
        return eVar.substring(1, eVar.length() - 1);
    }
}
