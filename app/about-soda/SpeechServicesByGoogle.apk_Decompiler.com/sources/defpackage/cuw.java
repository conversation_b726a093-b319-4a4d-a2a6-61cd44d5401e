package defpackage;

import android.app.Service;
import com.google.android.libraries.mdi.download.foreground.sting.ForegroundDownloadService;

/* renamed from: cuw  reason: default package */
/* compiled from: PG */
public abstract class cuw extends Service implements iip {
    private volatile iij a;
    private final Object b = new Object();
    private boolean c = false;

    public final Object bo() {
        if (this.a == null) {
            synchronized (this.b) {
                if (this.a == null) {
                    this.a = new iij(this);
                }
            }
        }
        return this.a.bo();
    }

    public final void onCreate() {
        if (!this.c) {
            this.c = true;
            ((ForegroundDownloadService) this).a = (cto) ((bra) bo()).b.m.b();
        }
        super.onCreate();
    }
}
