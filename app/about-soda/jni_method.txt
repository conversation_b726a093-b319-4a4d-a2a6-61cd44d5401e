com.google.android.libraries.assistant.soda.AudioInput:
  - nativeCacheDirectBufferAddress

com.google.android.libraries.assistant.soda.Soda:
  - nativeAddAudio
  - nativeAddTimestampedAudio
  - nativeAddTimestampedLoopbackAudio
  - nativeCancelAsr
  - nativeCollectDebugInfo
  - nativeConstruct
  - nativeCreateSharedResources
  - nativeDelete
  - nativeDeleteDiarizationProcessor
  - nativeDeleteSharedResources
  - nativeDeleteSharedResourcesIfDiarizationProcessorAbsent
  - nativeEnrollForVoiceMatch
  - nativeExecuteTasks
  - nativeGetSpeakerIdEnrollmentInfo
  - nativeInit
  - nativeLogEvents
  - nativePushAudio
  - nativeRequiresReinitialization
  - nativeSetApaLogger
  - nativeSetDataProvider
  - nativeSetTransportFactory
  - nativeStartAsr
  - nativeStartCapture
  - nativeStopCapture
  - nativeUpdateContext
  - nativeUpdateRecognitionContext
  - nativeUpdateRuntime

com.google.android.libraries.assistant.soda.data.SodaDataProviderJni:
  - nativeHandleDataResponse

com.google.android.libraries.assistant.soda.s3client.SodaTransportSession:
  - nativeHandleError
  - nativeHandleResponse
  - nativeHandleState

com.google.android.libraries.assistant.speakerid.SodaEnrollmentManagerImpl:
  - nativeConstruct
  - nativeDelete
  - nativeGetEnrollmentAudio
  - nativeGetSpeakerInfo
  - nativeGetVoiceMatchModels
  - nativeProcessRequest

com.google.common.jni.initgoogle.InitGoogle:
  - initGoogle

com.google.speech.context.AbstractContextCompiler:
  - nativeCompile
  - nativeConstruct
  - nativeDelete
  - nativeInitFromProto

com.google.speech.grammar.AbstractGrammarCompiler:
  - nativeCompile
  - nativeConstruct
  - nativeDelete
  - nativeInitFromProto
  - nativeReadCache
  - nativeWriteCache
  - nativeWriteClgFst
  - nativeWriteOutputSymbolsToFile
  - nativeWriteSemanticFst
  - nativeWriteSemanticSymbolsToFile

com.google.speech.grammar.pumpkin.ActionFrameManager:
  - nativeCombine
  - nativeCreate
  - nativeCreateFromMMap
  - nativeDelete
  - nativeLoadActionFrame

com.google.speech.grammar.pumpkin.ActionFrame:
  - nativeDelete

com.google.speech.grammar.pumpkin.Tagger:
  - nativeConstruct
  - nativeDelete
  - nativeTag
  - nativeTagAndGetNBest

com.google.speech.grammar.pumpkin.UserValidators:
  - nativeAddGoldmineValidator
  - nativeAddJavaValidator
  - nativeAddMapBasedValidator
  - nativeAddUserValidator
  - nativeCreate
  - nativeDelete
  - nativeSetContacts

com.google.speech.recognizer.AbstractRecognizer:
  - nativeCancel
  - nativeConstruct
  - nativeDelete
  - nativeInit
  - nativeInitFromProto
  - nativeRun

com.google.speech.recognizer.ResourceManager:
  - nativeConstruct
  - nativeDelete
  - nativeInitFromProto
