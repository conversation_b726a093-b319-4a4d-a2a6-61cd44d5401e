package defpackage;

import j$.time.Duration;
import j$.time.Instant;
import j$.time.ZoneId;
import j$.time.format.DateTimeFormatter;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.Locale;

/* renamed from: esx  reason: default package */
/* compiled from: PG */
public final class esx {
    public static volatile itb a;
    private static volatile isa b;
    private static volatile isa c;
    private static volatile isa d;
    private static volatile isa e;
    private static volatile isa f;
    private static volatile isa g;
    private static volatile isa h;
    private static volatile isa i;
    private static volatile isa j;
    private static volatile isa k;
    private static volatile isa l;

    private esx() {
    }

    public static isa a() {
        isa isa = d;
        if (isa == null) {
            synchronized (esx.class) {
                isa = d;
                if (isa == null) {
                    irx a2 = isa.a();
                    a2.c = irz.SERVER_STREAMING;
                    a2.d = isa.c("com.google.android.libraries.search.audio.service.AudioService", "ActivateAudioRequestClient");
                    a2.b();
                    dzc dzc = dzc.d;
                    hte hte = jhz.a;
                    a2.a = new jhy(dzc);
                    a2.b = new jhy(ete.c);
                    isa = a2.a();
                    d = isa;
                }
            }
        }
        return isa;
    }

    public static isa b() {
        isa isa = l;
        if (isa == null) {
            synchronized (esx.class) {
                isa = l;
                if (isa == null) {
                    irx a2 = isa.a();
                    a2.c = irz.SERVER_STREAMING;
                    a2.d = isa.c("com.google.android.libraries.search.audio.service.AudioService", "Connect");
                    a2.b();
                    htc htc = htc.a;
                    hte hte = jhz.a;
                    a2.a = new jhy(htc);
                    a2.b = new jhy(htc.a);
                    isa = a2.a();
                    l = isa;
                }
            }
        }
        return isa;
    }

    public static isa c() {
        isa isa = e;
        if (isa == null) {
            synchronized (esx.class) {
                isa = e;
                if (isa == null) {
                    irx a2 = isa.a();
                    a2.c = irz.UNARY;
                    a2.d = isa.c("com.google.android.libraries.search.audio.service.AudioService", "DeactivateClient");
                    a2.b();
                    esy esy = esy.b;
                    hte hte = jhz.a;
                    a2.a = new jhy(esy);
                    a2.b = new jhy(htc.a);
                    isa = a2.a();
                    e = isa;
                }
            }
        }
        return isa;
    }

    public static isa d() {
        isa isa = k;
        if (isa == null) {
            synchronized (esx.class) {
                isa = k;
                if (isa == null) {
                    irx a2 = isa.a();
                    a2.c = irz.SERVER_STREAMING;
                    a2.d = isa.c("com.google.android.libraries.search.audio.service.AudioService", "GetAudioRequestReadOnlyListeningSession");
                    a2.b();
                    dzk dzk = dzk.b;
                    hte hte = jhz.a;
                    a2.a = new jhy(dzk);
                    a2.b = new jhy(etf.c);
                    isa = a2.a();
                    k = isa;
                }
            }
        }
        return isa;
    }

    public static isa e() {
        isa isa = h;
        if (isa == null) {
            synchronized (esx.class) {
                isa = h;
                if (isa == null) {
                    irx a2 = isa.a();
                    a2.c = irz.SERVER_STREAMING;
                    a2.d = isa.c("com.google.android.libraries.search.audio.service.AudioService", "StartListeningForHotword");
                    a2.b();
                    esz esz = esz.d;
                    hte hte = jhz.a;
                    a2.a = new jhy(esz);
                    a2.b = new jhy(etf.c);
                    isa = a2.a();
                    h = isa;
                }
            }
        }
        return isa;
    }

    public static isa f() {
        isa isa = b;
        if (isa == null) {
            synchronized (esx.class) {
                isa = b;
                if (isa == null) {
                    irx a2 = isa.a();
                    a2.c = irz.SERVER_STREAMING;
                    a2.d = isa.c("com.google.android.libraries.search.audio.service.AudioService", "StartListening");
                    a2.b();
                    etb etb = etb.e;
                    hte hte = jhz.a;
                    a2.a = new jhy(etb);
                    a2.b = new jhy(etf.c);
                    isa = a2.a();
                    b = isa;
                }
            }
        }
        return isa;
    }

    public static isa g() {
        isa isa = i;
        if (isa == null) {
            synchronized (esx.class) {
                isa = i;
                if (isa == null) {
                    irx a2 = isa.a();
                    a2.c = irz.UNARY;
                    a2.d = isa.c("com.google.android.libraries.search.audio.service.AudioService", "StopListeningForHotword");
                    a2.b();
                    ebw ebw = ebw.c;
                    hte hte = jhz.a;
                    a2.a = new jhy(ebw);
                    a2.b = new jhy(ebp.d);
                    isa = a2.a();
                    i = isa;
                }
            }
        }
        return isa;
    }

    public static isa h() {
        isa isa = j;
        if (isa == null) {
            synchronized (esx.class) {
                isa = j;
                if (isa == null) {
                    irx a2 = isa.a();
                    a2.c = irz.UNARY;
                    a2.d = isa.c("com.google.android.libraries.search.audio.service.AudioService", "StopListeningForSeamlessMode");
                    a2.b();
                    ebw ebw = ebw.c;
                    hte hte = jhz.a;
                    a2.a = new jhy(ebw);
                    a2.b = new jhy(eta.d);
                    isa = a2.a();
                    j = isa;
                }
            }
        }
        return isa;
    }

    public static isa i() {
        isa isa = c;
        if (isa == null) {
            synchronized (esx.class) {
                isa = c;
                if (isa == null) {
                    irx a2 = isa.a();
                    a2.c = irz.UNARY;
                    a2.d = isa.c("com.google.android.libraries.search.audio.service.AudioService", "StopListening");
                    a2.b();
                    etd etd = etd.d;
                    hte hte = jhz.a;
                    a2.a = new jhy(etd);
                    a2.b = new jhy(dzi.d);
                    isa = a2.a();
                    c = isa;
                }
            }
        }
        return isa;
    }

    public static isa j() {
        isa isa = g;
        if (isa == null) {
            synchronized (esx.class) {
                isa = g;
                if (isa == null) {
                    irx a2 = isa.a();
                    a2.c = irz.SERVER_STREAMING;
                    a2.d = isa.c("com.google.android.libraries.search.audio.service.AudioService", "UpdateHotwordRoute");
                    a2.b();
                    eth eth = eth.b;
                    hte hte = jhz.a;
                    a2.a = new jhy(eth);
                    a2.b = new jhy(etg.c);
                    isa = a2.a();
                    g = isa;
                }
            }
        }
        return isa;
    }

    public static isa k() {
        isa isa = f;
        if (isa == null) {
            synchronized (esx.class) {
                isa = f;
                if (isa == null) {
                    irx a2 = isa.a();
                    a2.c = irz.SERVER_STREAMING;
                    a2.d = isa.c("com.google.android.libraries.search.audio.service.AudioService", "UpdateRoute");
                    a2.b();
                    eti eti = eti.c;
                    hte hte = jhz.a;
                    a2.a = new jhy(eti);
                    a2.b = new jhy(etg.c);
                    isa = a2.a();
                    f = isa;
                }
            }
        }
        return isa;
    }

    public static final String l(Integer num) {
        if (num == null) {
            return "token N/A";
        }
        return num.toString();
    }

    public static final String m(eab eab) {
        dzq dzq = eab.b;
        if (dzq == null) {
            dzq = dzq.c;
        }
        jnu.d(dzq, "getAudioRouteType(...)");
        return fbi.r(dzq);
    }

    public static final String n(eel eel) {
        switch (eel.ordinal()) {
            case 0:
                return "unknown";
            case 1:
                return "builtin";
            case 2:
                return "zlm";
            case 3:
                return "sync";
            case 4:
                return "dsp";
            case 5:
                return "soda";
            case 6:
                return "bisto";
            case 7:
                return "car";
            case 8:
                return "uri";
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return "pfd";
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return "voicedsp";
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return "gacs";
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return "hotword";
            default:
                throw new jjq();
        }
    }

    public static final Object o(RandomAccessFile randomAccessFile, jna jna) {
        try {
            jna.a(randomAccessFile);
            return jkd.a;
        } catch (Throwable th) {
            return jji.b(th);
        }
    }

    public static hme p(euh euh, dyc dyc) {
        dyb dyb;
        jnu.e(dyc, "audioData");
        if (dyc.b == 1) {
            dyb = (dyb) dyc.c;
        } else {
            dyb = dyb.c;
        }
        hsq hsq = dyb.b;
        jnu.d(hsq, "getBytes(...)");
        return euh.c(hsq);
    }

    public static String q(eao eao) {
        int i2;
        String str;
        eav eav;
        eax eax;
        dzh dzh;
        eaj eaj;
        eal eal;
        dzi dzi;
        String str2;
        dzi dzi2;
        eag eag;
        eai eai;
        dyk dyk;
        dyn dyn;
        dyn dyn2;
        eap eap;
        dzo dzo;
        dzm dzm;
        ebo ebo;
        String str3;
        eaj eaj2;
        ebp ebp;
        String str4;
        ebp ebp2;
        eag eag2;
        eaz eaz;
        dzo dzo2;
        dzm dzm2;
        eaw eaw;
        eaq eaq;
        eba eba;
        ean ean;
        Object obj;
        ear ear;
        Object obj2;
        eat eat;
        int i3 = eao.b;
        if (i3 == 0) {
            i2 = 4;
        } else if (i3 == 2) {
            i2 = 1;
        } else if (i3 == 3) {
            i2 = 2;
        } else if (i3 != 4) {
            i2 = 0;
        } else {
            i2 = 3;
        }
        if (i2 != 0) {
            int i4 = i2 - 1;
            if (i4 != 0) {
                String str5 = "null";
                str = "";
                if (i4 == 1) {
                    if (i3 == 3) {
                        eax = (eax) eao.c;
                    } else {
                        eax = eax.h;
                    }
                    int i5 = eax.b;
                    int e2 = dvx.e(i5);
                    int i6 = e2 - 1;
                    if (e2 != 0) {
                        switch (i6) {
                            case 0:
                                if (i5 == 1) {
                                    dzh = (dzh) eax.c;
                                } else {
                                    dzh = dzh.c;
                                }
                                eak eak = dzh.b;
                                if (eak == null) {
                                    eak = eak.c;
                                }
                                if (eak.a != 1) {
                                    Locale locale = Locale.US;
                                    if (eak.a == 2) {
                                        eaj = eaj.b(((Integer) eak.b).intValue());
                                        if (eaj == null) {
                                            eaj = eaj.UNKNOWN_OPENING_FAILURE;
                                        }
                                    } else {
                                        eaj = eaj.UNKNOWN_OPENING_FAILURE;
                                    }
                                    str = String.format(locale, "StartListeningResult failure: %s, sessionToken: %d", new Object[]{eaj.name(), Long.valueOf(eax.d)});
                                    break;
                                } else {
                                    Locale locale2 = Locale.US;
                                    if (eak.a == 1) {
                                        eal = eal.b(((Integer) eak.b).intValue());
                                        if (eal == null) {
                                            eal = eal.UNKNOWN_OPENING_SUCCESS;
                                        }
                                    } else {
                                        eal = eal.UNKNOWN_OPENING_SUCCESS;
                                    }
                                    str = String.format(locale2, "StartListeningResult success: %s, sessionToken: %d", new Object[]{eal.name(), Long.valueOf(eax.d)});
                                    break;
                                }
                            case 1:
                                if (i5 == 2) {
                                    dzi = (dzi) eax.c;
                                } else {
                                    dzi = dzi.d;
                                }
                                eah eah = dzi.b;
                                if (eah == null) {
                                    eah = eah.c;
                                }
                                Locale locale3 = Locale.US;
                                if (eah.a == 1) {
                                    Locale locale4 = Locale.US;
                                    if (eah.a == 1) {
                                        eai = eai.b(((Integer) eah.b).intValue());
                                        if (eai == null) {
                                            eai = eai.UNKNOWN_CLOSING_SUCCESS;
                                        }
                                    } else {
                                        eai = eai.UNKNOWN_CLOSING_SUCCESS;
                                    }
                                    str2 = String.format(locale4, "success: %s", new Object[]{eai.name()});
                                } else {
                                    Locale locale5 = Locale.US;
                                    if (eah.a == 2) {
                                        eag = eag.b(((Integer) eah.b).intValue());
                                        if (eag == null) {
                                            eag = eag.UNKNOWN_CLOSING_FAILURE;
                                        }
                                    } else {
                                        eag = eag.UNKNOWN_CLOSING_FAILURE;
                                    }
                                    str2 = String.format(locale5, "failure: %s", new Object[]{eag.name()});
                                }
                                if (eax.b == 2) {
                                    dzi2 = (dzi) eax.c;
                                } else {
                                    dzi2 = dzi.d;
                                }
                                eam b2 = eam.b(dzi2.c);
                                if (b2 == null) {
                                    b2 = eam.UNSET;
                                }
                                str = String.format(locale3, "StopListeningResult %s, StopListeningReason: %s, sessionToken: %d", new Object[]{str2, b2.name(), Long.valueOf(eax.d)});
                                break;
                            case 2:
                                Locale locale6 = Locale.US;
                                if (eax.b == 5) {
                                    dyk = (dyk) eax.c;
                                } else {
                                    dyk = dyk.c;
                                }
                                int e3 = dnk.e(dyk.b);
                                if (e3 == 0) {
                                    e3 = 1;
                                }
                                String d2 = dnk.d(e3);
                                eeg eeg = eax.e;
                                if (eeg == null) {
                                    eeg = eeg.c;
                                }
                                str = String.format(locale6, "AudioFocusAcquiringStatus: %s, audioFocusToken: %d", new Object[]{d2, Integer.valueOf(eeg.b)});
                                break;
                            case 3:
                                Locale locale7 = Locale.US;
                                if (eax.b == 6) {
                                    dyn = (dyn) eax.c;
                                } else {
                                    dyn = dyn.d;
                                }
                                int c2 = dnk.c(dyn.b);
                                if (c2 == 0) {
                                    c2 = 1;
                                }
                                String b3 = dnk.b(c2);
                                if (eax.b == 6) {
                                    dyn2 = (dyn) eax.c;
                                } else {
                                    dyn2 = dyn.d;
                                }
                                dyl b4 = dyl.b(dyn2.c);
                                if (b4 == null) {
                                    b4 = dyl.REASON_UNKNOWN;
                                }
                                String name = b4.name();
                                eeg eeg2 = eax.e;
                                if (eeg2 == null) {
                                    eeg2 = eeg.c;
                                }
                                str = String.format(locale7, "AudioFocusReleasingStatus: %s, AudioFocusReleasingReason: %s, audioFocusToken: %d", new Object[]{b3, name, Integer.valueOf(eeg2.b)});
                                break;
                            case 4:
                                if (i5 == 8) {
                                    eap = (eap) eax.c;
                                } else {
                                    eap = eap.e;
                                }
                                if (eap.b != 2) {
                                    Locale locale8 = Locale.US;
                                    if (eap.b == 1) {
                                        dzo = (dzo) eap.c;
                                    } else {
                                        dzo = dzo.c;
                                    }
                                    eaf b5 = eaf.b(dzo.b);
                                    if (b5 == null) {
                                        b5 = eaf.UNKNOWN_ROUTING_STATUS;
                                    }
                                    String name2 = b5.name();
                                    Long valueOf = Long.valueOf(eax.g);
                                    dzq dzq = eap.d;
                                    if (dzq == null) {
                                        dzq = dzq.c;
                                    }
                                    str = String.format(locale8, "UpdateRoutingStatus: %s, clientToken: %s, route: %s", new Object[]{name2, valueOf, fbi.t(dzq)});
                                    break;
                                } else {
                                    Locale locale9 = Locale.US;
                                    if (eap.b == 2) {
                                        dzm = (dzm) eap.c;
                                    } else {
                                        dzm = dzm.c;
                                    }
                                    dzx b6 = dzx.b(dzm.b);
                                    if (b6 == null) {
                                        b6 = dzx.UNKNOWN_DISCONNECT_REASON;
                                    }
                                    String name3 = b6.name();
                                    Long valueOf2 = Long.valueOf(eax.g);
                                    dzq dzq2 = eap.d;
                                    if (dzq2 == null) {
                                        dzq2 = dzq.c;
                                    }
                                    str = String.format(locale9, "AudioRouteDisconnectStatus: %s, clientToken: %s, route: %s", new Object[]{name3, valueOf2, fbi.t(dzq2)});
                                    break;
                                }
                            case 5:
                                if (i5 == 100) {
                                    ebo = (ebo) eax.c;
                                } else {
                                    ebo = ebo.c;
                                }
                                eak eak2 = ebo.b;
                                if (eak2 == null) {
                                    eak2 = eak.c;
                                }
                                Locale locale10 = Locale.US;
                                int i7 = eak2.a;
                                if (i7 == 1) {
                                    eal b7 = eal.b(((Integer) eak2.b).intValue());
                                    if (b7 == null) {
                                        b7 = eal.UNKNOWN_OPENING_SUCCESS;
                                    }
                                    str3 = b7.name();
                                } else {
                                    if (i7 == 2) {
                                        eaj2 = eaj.b(((Integer) eak2.b).intValue());
                                        if (eaj2 == null) {
                                            eaj2 = eaj.UNKNOWN_OPENING_FAILURE;
                                        }
                                    } else {
                                        eaj2 = eaj.UNKNOWN_OPENING_FAILURE;
                                    }
                                    str3 = eaj2.name();
                                }
                                str = String.format(locale10, "HotwordStartListeningStatus: AudioSourceOpeningStatus: %s", new Object[]{str3});
                                break;
                            case 6:
                                if (i5 == 101) {
                                    ebp = (ebp) eax.c;
                                } else {
                                    ebp = ebp.d;
                                }
                                eah eah2 = ebp.b;
                                if (eah2 == null) {
                                    eah2 = eah.c;
                                }
                                Locale locale11 = Locale.US;
                                int i8 = eah2.a;
                                if (i8 == 1) {
                                    eai b8 = eai.b(((Integer) eah2.b).intValue());
                                    if (b8 == null) {
                                        b8 = eai.UNKNOWN_CLOSING_SUCCESS;
                                    }
                                    str4 = b8.name();
                                } else {
                                    if (i8 == 2) {
                                        eag2 = eag.b(((Integer) eah2.b).intValue());
                                        if (eag2 == null) {
                                            eag2 = eag.UNKNOWN_CLOSING_FAILURE;
                                        }
                                    } else {
                                        eag2 = eag.UNKNOWN_CLOSING_FAILURE;
                                    }
                                    str4 = eag2.name();
                                }
                                if (eax.b == 101) {
                                    ebp2 = (ebp) eax.c;
                                } else {
                                    ebp2 = ebp.d;
                                }
                                eam b9 = eam.b(ebp2.c);
                                if (b9 == null) {
                                    b9 = eam.UNSET;
                                }
                                str = String.format(locale11, "HotwordStoptListeningStatus: AudioSourceOpeningStatus: %s, StopListeningReason: %s", new Object[]{str4, b9.name()});
                                break;
                            case 7:
                                if (i5 == 102) {
                                    eaz = (eaz) eax.c;
                                } else {
                                    eaz = eaz.e;
                                }
                                if (eaz.b != 2) {
                                    Locale locale12 = Locale.US;
                                    if (eaz.b == 1) {
                                        dzo2 = (dzo) eaz.c;
                                    } else {
                                        dzo2 = dzo.c;
                                    }
                                    eaf b10 = eaf.b(dzo2.b);
                                    if (b10 == null) {
                                        b10 = eaf.UNKNOWN_ROUTING_STATUS;
                                    }
                                    String name4 = b10.name();
                                    dzq dzq3 = eaz.d;
                                    if (dzq3 == null) {
                                        dzq3 = dzq.c;
                                    }
                                    str = String.format(locale12, "UpdateRoutingStatus: %s, route: %s", new Object[]{name4, fbi.t(dzq3)});
                                    break;
                                } else {
                                    Locale locale13 = Locale.US;
                                    if (eaz.b == 2) {
                                        dzm2 = (dzm) eaz.c;
                                    } else {
                                        dzm2 = dzm.c;
                                    }
                                    dzx b11 = dzx.b(dzm2.b);
                                    if (b11 == null) {
                                        b11 = dzx.UNKNOWN_DISCONNECT_REASON;
                                    }
                                    String name5 = b11.name();
                                    dzq dzq4 = eaz.d;
                                    if (dzq4 == null) {
                                        dzq4 = dzq.c;
                                    }
                                    str = String.format(locale13, "AudioRouteDisconnectStatus: %s, route: %s", new Object[]{name5, fbi.t(dzq4)});
                                    break;
                                }
                            case 8:
                                str = "Empty ClientEvent Status.";
                                break;
                        }
                        Locale locale14 = Locale.US;
                        int e4 = dvx.e(eax.b);
                        switch (e4) {
                            case 1:
                                str5 = "START_LISTENING_STATUS";
                                break;
                            case 2:
                                str5 = "STOP_LISTENING_STATUS";
                                break;
                            case 3:
                                str5 = "ACQUIRE_AUDIO_FOCUS_STATUS";
                                break;
                            case 4:
                                str5 = "RELEASE_AUDIO_FOCUS_STATUS";
                                break;
                            case 5:
                                str5 = "AUDIO_REQUEST_CLIENT_ROUTE_STATUS";
                                break;
                            case 6:
                                str5 = "HOTWORD_START_LISTENING_STATUS";
                                break;
                            case 7:
                                str5 = "HOTWORD_STOP_LISTENING_STATUS";
                                break;
                            case 8:
                                str5 = "HOTWORD_CLIENT_ROUTE_STATUS";
                                break;
                            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                                str5 = "RESULT_NOT_SET";
                                break;
                        }
                        if (e4 != 0) {
                            str = String.format(locale14, "%s: %s", new Object[]{str5, str});
                        } else {
                            throw null;
                        }
                    } else {
                        throw null;
                    }
                } else if (i4 == 2) {
                    if (i3 == 4) {
                        eaw = (eaw) eao.c;
                    } else {
                        eaw = eaw.h;
                    }
                    int C = a.C(eaw.b);
                    switch (C) {
                        case 1:
                            str5 = "AUDIO_REQUEST_FIRST_BYTE_READ_OUTCOME";
                            break;
                        case 2:
                            str5 = "HOTWORD_FIRST_BYTE_READ_OUTCOME";
                            break;
                        case 3:
                            str5 = "AUDIO_BUFFER_OUTCOME";
                            break;
                        case 4:
                            str5 = "AUDIO_START_TIME_OUTCOME";
                            break;
                        case 5:
                            str5 = "BLUETOOTH_CONNECTION_OUTCOME";
                            break;
                        case 6:
                            str5 = "OUTCOME_NOT_SET";
                            break;
                    }
                    if (C != 0) {
                        StringBuilder sb = new StringBuilder(str5);
                        sb.append(":");
                        if ((eaw.a & 1) != 0) {
                            Locale locale15 = Locale.US;
                            ehg ehg = eaw.d;
                            if (ehg == null) {
                                ehg = ehg.c;
                            }
                            sb.append(String.format(locale15, " clientInfo: %s ", new Object[]{fbi.q(ehg)}));
                        }
                        if ((eaw.a & 2) != 0) {
                            sb.append(String.format(Locale.US, " sessionToken: %d ", new Object[]{Long.valueOf(eaw.e)}));
                        }
                        int i9 = eaw.b;
                        int C2 = a.C(i9);
                        int i10 = C2 - 1;
                        if (C2 != 0) {
                            if (i10 == 0) {
                                if (i9 == 1) {
                                    eaq = (eaq) eaw.c;
                                } else {
                                    eaq = eaq.c;
                                }
                                String str6 = "OUTCOME_UNKNOWN_FIRST_BYTE_READ";
                                if ((eaq.a & 1) != 0) {
                                    Locale locale16 = Locale.US;
                                    int x = a.x(eaq.b);
                                    if (!(x == 0 || x == 1)) {
                                        str6 = x != 2 ? "OUTCOME_FAILED_READING_FIRST_BYTE" : "OUTCOME_SUCCESS_FIRST_BYTE_READ";
                                    }
                                    sb.append(String.format(locale16, " %s ", new Object[]{str6}));
                                } else {
                                    sb.append(String.format(Locale.US, " %s ", new Object[]{str6}));
                                }
                            } else if (i10 == 1) {
                                if (i9 == 2) {
                                    eba = (eba) eaw.c;
                                } else {
                                    eba = eba.c;
                                }
                                String str7 = "OUTCOME_UNKNOWN_HOTWORD_FIRST_BYTE_READ";
                                if ((eba.a & 1) != 0) {
                                    Locale locale17 = Locale.US;
                                    int x2 = a.x(eba.b);
                                    if (!(x2 == 0 || x2 == 1)) {
                                        str7 = x2 != 2 ? "OUTCOME_FAILED_READING_HOTWORD_FIRST_BYTE" : "OUTCOME_SUCCESS_HOTWORD_FIRST_BYTE_READ";
                                    }
                                    sb.append(String.format(locale17, " %s ", new Object[]{str7}));
                                } else {
                                    sb.append(String.format(Locale.US, " %s ", new Object[]{str7}));
                                }
                            } else if (i10 == 2) {
                                if (i9 == 3) {
                                    ean = (ean) eaw.c;
                                } else {
                                    ean = ean.g;
                                }
                                sb.append(" [");
                                Locale locale18 = Locale.US;
                                if ((ean.a & 1) != 0) {
                                    eau b12 = eau.b(ean.b);
                                    if (b12 == null) {
                                        b12 = eau.OUTCOME_UNKNOWN_BUFFER_AUDIO;
                                    }
                                    obj = b12.name();
                                } else {
                                    obj = eau.OUTCOME_UNKNOWN_BUFFER_AUDIO;
                                }
                                sb.append(String.format(locale18, " outcome: %s ", new Object[]{obj}));
                                if ((ean.a & 2) != 0) {
                                    sb.append(String.format(Locale.US, " buffer_id: %s ", new Object[]{ean.c}));
                                }
                                if ((ean.a & 16) != 0) {
                                    sb.append(String.format(Locale.US, " offset: %s ", new Object[]{Integer.valueOf(ean.f)}));
                                }
                                if ((4 & ean.a) != 0) {
                                    sb.append(String.format(Locale.US, " bytes_available: %s ", new Object[]{Integer.valueOf(ean.d)}));
                                }
                                if ((ean.a & 8) != 0) {
                                    sb.append(String.format(Locale.US, " raw_error: %s ", new Object[]{Integer.valueOf(ean.e)}));
                                }
                                sb.append("]");
                            } else if (i10 == 3) {
                                if (i9 == 4) {
                                    ear = (ear) eaw.c;
                                } else {
                                    ear = ear.e;
                                }
                                sb.append(" [ sourceType: ");
                                eel b13 = eel.b(ear.b);
                                if (b13 == null) {
                                    b13 = eel.SOURCE_EMPTY;
                                }
                                sb.append(b13.name());
                                dyu dyu = ear.c;
                                if (dyu == null) {
                                    dyu = dyu.c;
                                }
                                if ((dyu.a & 1) != 0) {
                                    sb.append(" audioStartTimeElapsedMs: ");
                                    sb.append(Duration.ofNanos(dyu.b).toMillis());
                                }
                                gnk gnk = evk.a;
                                dyu.h(gnk);
                                if (dyu.r.n((htp) gnk.c)) {
                                    gnk gnk2 = evk.a;
                                    dyu.h(gnk2);
                                    Object k2 = dyu.r.k((htp) gnk2.c);
                                    if (k2 == null) {
                                        obj2 = gnk2.d;
                                    } else {
                                        obj2 = gnk2.n(k2);
                                    }
                                    evi evi = (evi) obj2;
                                    sb.append(" completionReason: ");
                                    evj b14 = evj.b(evi.b);
                                    if (b14 == null) {
                                        b14 = evj.REASON_UNKNOWN;
                                    }
                                    sb.append(b14.name());
                                    sb.append(" computationTimeMs: ");
                                    sb.append(Duration.ofNanos(evi.c).toMillis());
                                }
                                if ((4 & ear.a) != 0) {
                                    sb.append(" unixEpochStartTime: ");
                                    sb.append(ear.d);
                                }
                                sb.append(" ]");
                            } else if (i10 == 4) {
                                if (i9 == 5) {
                                    eat = (eat) eaw.c;
                                } else {
                                    eat = eat.f;
                                }
                                sb.append(" [ stage: ");
                                eas b15 = eas.b(eat.b);
                                if (b15 == null) {
                                    b15 = eas.UNKNOWN;
                                }
                                sb.append(b15.name());
                                sb.append(" name: ");
                                sb.append(eat.c);
                                sb.append(" class: ");
                                sb.append(eat.d);
                                sb.append(" majorClass: ");
                                sb.append(eat.e);
                                sb.append(" ]");
                            } else if (i10 == 5) {
                                sb.append(" Empty ClientEvent outcome");
                            }
                            str = sb.toString();
                        } else {
                            throw null;
                        }
                    } else {
                        throw null;
                    }
                } else if (i4 == 3) {
                    str = "Empty AudioEvent.";
                }
            } else {
                if (i3 == 2) {
                    eav = (eav) eao.c;
                } else {
                    eav = eav.n;
                }
                str = u(eav);
            }
            return String.format(Locale.US, "timestamp: %s | event: %s", new Object[]{Instant.ofEpochMilli(eao.d).atZone(ZoneId.systemDefault()).format(DateTimeFormatter.ofPattern("MM-dd HH:mm:ss.SSS")), str});
        }
        throw null;
    }

    public static String r(dyt dyt) {
        StringBuilder sb = new StringBuilder("AudioLibInputParams: [");
        if ((dyt.a & 1) != 0) {
            sb.append(" sourceType: ");
            sb.append(dyt.b);
        }
        if ((dyt.a & 2) != 0) {
            sb.append(" sampleRateHz: ");
            sb.append(dyt.c);
        }
        if ((dyt.a & 4) != 0) {
            sb.append(" channelConfig: ");
            sb.append(dyt.d);
        }
        if ((dyt.a & 8) != 0) {
            sb.append(" encodingFormat: ");
            sb.append(dyt.e);
        }
        if ((dyt.a & 32) != 0) {
            sb.append("  audioEffects: [");
            dyr dyr = dyt.g;
            if (dyr == null) {
                dyr = dyr.d;
            }
            if ((dyr.a & 1) != 0) {
                sb.append("  noiseSuppressionEnabled: ");
                dyr dyr2 = dyt.g;
                if (dyr2 == null) {
                    dyr2 = dyr.d;
                }
                sb.append(dyr2.b);
            }
            dyr dyr3 = dyt.g;
            if (dyr3 == null) {
                dyr3 = dyr.d;
            }
            if ((dyr3.a & 2) != 0) {
                sb.append("  acousticEchoCancellationEnabled: ");
                dyr dyr4 = dyt.g;
                if (dyr4 == null) {
                    dyr4 = dyr.d;
                }
                sb.append(dyr4.c);
            }
            sb.append("]");
        }
        if ((dyt.a & 16) != 0) {
            sb.append(" customAudioBufferLengthInSec: ");
            sb.append(dyt.f);
        }
        if ((dyt.a & 128) != 0) {
            sb.append(" customAudioResourceTimeoutMs: ");
            sb.append(dyt.h);
        }
        if ((dyt.a & 1024) != 0) {
            sb.append(" usePushMechanism: ");
            sb.append(dyt.k);
        }
        if ((dyt.a & 256) != 0) {
            sb.append(" timestampPollingMs: ");
            dys dys = dyt.i;
            if (dys == null) {
                dys = dys.c;
            }
            sb.append(dys.b);
        }
        if ((dyt.a & 512) != 0) {
            sb.append(" enableLoopbackAudio: ");
            sb.append(dyt.j);
        }
        sb.append("]");
        return sb.toString();
    }

    private static String u(eav eav) {
        int i2;
        dzc dzc;
        dze dze;
        ebh ebh;
        ebh ebh2;
        ebn ebn;
        String str;
        ebq ebq;
        dym dym;
        dzn dzn;
        eab eab;
        eay b2 = eay.b(eav.d);
        if (b2 == null) {
            b2 = eay.UNKNOWN_EVENT;
        }
        StringBuilder sb = new StringBuilder(b2.name());
        if ((eav.a & 64) != 0) {
            sb.append(", ");
            ehg ehg = eav.j;
            if (ehg == null) {
                ehg = ehg.c;
            }
            sb.append(fbi.q(ehg));
        }
        boolean z = false;
        if ((eav.a & 2) != 0) {
            sb.append(String.format(Locale.US, ", clientToken: %d", new Object[]{Long.valueOf(eav.e)}));
        }
        if ((eav.a & 4) != 0) {
            sb.append(String.format(Locale.US, ", sessionToken: %d", new Object[]{Long.valueOf(eav.f)}));
        }
        if ((eav.a & 128) != 0) {
            sb.append(String.format(Locale.US, ", routeSessionToken: %d", new Object[]{Long.valueOf(eav.k)}));
        }
        if ((eav.a & 16) != 0) {
            Locale locale = Locale.US;
            eeg eeg = eav.h;
            if (eeg == null) {
                eeg = eeg.c;
            }
            sb.append(String.format(locale, ", audioFocusToken: %d", new Object[]{Integer.valueOf(eeg.b)}));
        }
        if ((eav.a & 256) != 0) {
            Locale locale2 = Locale.US;
            ecg b3 = ecg.b(eav.l);
            if (b3 == null) {
                b3 = ecg.BISTO;
            }
            sb.append(String.format(locale2, ", audioAdapter: %s", new Object[]{b3.name()}));
        }
        if (eav.m.size() > 0) {
            Locale locale3 = Locale.US;
            huf<ekt> huf = eav.m;
            jnu.e(huf, "<this>");
            ArrayList arrayList = new ArrayList(jji.K(huf));
            for (ekt ekt : huf) {
                arrayList.add(ekt.b);
            }
            sb.append(String.format(locale3, ", refs: %s", new Object[]{arrayList.toString()}));
        }
        if ((eav.a & 8) != 0) {
            Locale locale4 = Locale.US;
            eam b4 = eam.b(eav.g);
            if (b4 == null) {
                b4 = eam.UNSET;
            }
            sb.append(String.format(locale4, ", StopListeningReason: %s", new Object[]{b4.name()}));
        }
        if ((eav.a & 32) != 0) {
            Locale locale5 = Locale.US;
            dzf b5 = dzf.b(eav.i);
            if (b5 == null) {
                b5 = dzf.UNKNOWN_DEACTIVATING_STATUS;
            }
            sb.append(String.format(locale5, ", ClientDeactivatingStatus: %s", new Object[]{b5.name()}));
        }
        int i3 = eav.b;
        if (i3 != 0) {
            switch (i3) {
                case 200:
                    i2 = 1;
                    break;
                case 201:
                    i2 = 2;
                    break;
                case 202:
                    i2 = 3;
                    break;
                case 203:
                    i2 = 4;
                    break;
                case 204:
                    i2 = 5;
                    break;
                case 205:
                    i2 = 6;
                    break;
                default:
                    i2 = 0;
                    break;
            }
        } else {
            i2 = 7;
        }
        if (i2 != 0) {
            int i4 = i2 - 1;
            if (i4 == 0) {
                sb.append(", ");
                if (eav.b == 200) {
                    dzc = (dzc) eav.c;
                } else {
                    dzc = dzc.d;
                }
                StringBuilder sb2 = new StringBuilder("AudioRequestActivationParams [");
                if ((dzc.a & 1) != 0) {
                    ehg ehg2 = dzc.b;
                    if (ehg2 == null) {
                        ehg2 = ehg.c;
                    }
                    sb2.append(fbi.q(ehg2));
                }
                if ((dzc.a & 2) != 0) {
                    sb2.append(" intent: ");
                    dzb b6 = dzb.b(dzc.c);
                    if (b6 == null) {
                        b6 = dzb.DEFAULT;
                    }
                    sb2.append(b6.name());
                }
                sb2.append("]");
                sb.append(sb2.toString());
            } else if (i4 == 1) {
                sb.append(", ");
                if (eav.b == 201) {
                    dze = (dze) eav.c;
                } else {
                    dze = dze.k;
                }
                StringBuilder sb3 = new StringBuilder("AudioRequestMicInputParams: [AudioInputMode: ");
                int ordinal = dzd.a(dze.b).ordinal();
                if (ordinal == 0) {
                    sb3.append("uri");
                } else if (ordinal == 1) {
                    sb3.append("handoff_data: [handoff_id: ");
                    if (dze.b == 3) {
                        ebh = (ebh) dze.c;
                    } else {
                        ebh = ebh.c;
                    }
                    sb3.append(ebh.a);
                    sb3.append(", handoff_offset: ");
                    if (dze.b == 3) {
                        ebh2 = (ebh) dze.c;
                    } else {
                        ebh2 = ebh.c;
                    }
                    sb3.append(ebh2.b);
                    sb3.append("]");
                } else if (ordinal == 2) {
                    sb3.append("file_path");
                } else if (ordinal == 3) {
                    sb3.append("enable_zero_latency_mic: ");
                    if (dze.b == 10) {
                        z = ((Boolean) dze.c).booleanValue();
                    }
                    sb3.append(z);
                } else if (ordinal == 4) {
                    sb3.append("enable_voice_dsp_hotword_mic: ");
                    if (dze.b == 17) {
                        z = ((Boolean) dze.c).booleanValue();
                    }
                    sb3.append(z);
                } else if (ordinal == 5) {
                    sb3.append("built-in mic");
                }
                if ((dze.a & 1) != 0) {
                    sb3.append(", ");
                    dyt dyt = dze.f;
                    if (dyt == null) {
                        dyt = dyt.l;
                    }
                    sb3.append(r(dyt));
                }
                if ((dze.a & 2) != 0) {
                    sb3.append(" enableEmulatedMicrophone: ");
                    sb3.append(dze.g);
                }
                if ((dze.a & 4) != 0) {
                    sb3.append(" micForTalkBack: ");
                    sb3.append(dze.h);
                }
                if ((dze.a & 8) != 0) {
                    sb3.append(" fastFailIfOpNotAllowed: ");
                    sb3.append(dze.i);
                }
                if ((dze.a & 16) != 0) {
                    sb3.append(" micOccupiedBehavior: ");
                    ebt b7 = ebt.b(dze.j);
                    if (b7 == null) {
                        b7 = ebt.BEHAVIOR_NO_FAIL;
                    }
                    sb3.append(b7.name());
                }
                sb3.append("]");
                sb.append(sb3.toString());
            } else if (i4 == 2) {
                sb.append(", ");
                if (eav.b == 202) {
                    ebn = (ebn) eav.c;
                } else {
                    ebn = ebn.h;
                }
                StringBuilder sb4 = new StringBuilder("HotwordMicInputParams: [AudioInputMode: ");
                int ordinal2 = ebm.a(ebn.b).ordinal();
                if (ordinal2 == 0) {
                    sb4.append("uri");
                } else if (ordinal2 == 1) {
                    sb4.append("media_sync_event: ");
                    if (ebn.b == 9) {
                        ebq = (ebq) ebn.c;
                    } else {
                        ebq = ebq.b;
                    }
                    sb4.append(ebq.a);
                } else if (ordinal2 == 2) {
                    sb4.append("built-in mic");
                }
                if ((ebn.a & 1) != 0) {
                    sb4.append(", ");
                    dyt dyt2 = ebn.d;
                    if (dyt2 == null) {
                        dyt2 = dyt.l;
                    }
                    sb4.append(r(dyt2));
                }
                if ((ebn.a & 2) != 0) {
                    sb4.append(" enableEmulatedMicrophone: ");
                    sb4.append(ebn.e);
                }
                if ((ebn.a & 8) != 0) {
                    sb4.append(" captureMode: ");
                    int A = a.A(ebn.g);
                    if (A == 0 || A == 1) {
                        str = "HOTWORD_CAPTURE_MODE_DEFAULT";
                    } else {
                        str = "HOTWORD_CAPTURE_MODE_CONCURRENT";
                    }
                    sb4.append(str);
                }
                if ((ebn.a & 4) != 0) {
                    sb4.append(" ");
                    ehg ehg3 = ebn.f;
                    if (ehg3 == null) {
                        ehg3 = ehg.c;
                    }
                    sb4.append(fbi.q(ehg3));
                }
                sb4.append("]");
                sb.append(sb4.toString());
            } else if (i4 == 3) {
                sb.append(", ");
                if (eav.b == 203) {
                    dym = (dym) eav.c;
                } else {
                    dym = dym.g;
                }
                StringBuilder sb5 = new StringBuilder("AudioFocusRequestParams: [ audioFocusGain: ");
                sb5.append(dym.b);
                if ((dym.a & 16) != 0) {
                    sb5.append(" usageType: ");
                    sb5.append(dym.f);
                } else {
                    sb5.append(" streamType: ");
                    sb5.append(dym.c);
                }
                if ((dym.a & 8) != 0) {
                    sb5.append(" ");
                    ehg ehg4 = dym.e;
                    if (ehg4 == null) {
                        ehg4 = ehg.c;
                    }
                    sb5.append(fbi.q(ehg4));
                }
                sb5.append("]");
                sb.append(sb5.toString());
            } else if (i4 == 4) {
                sb.append(", ");
                if (eav.b == 204) {
                    dzn = (dzn) eav.c;
                } else {
                    dzn = dzn.c;
                }
                dzq dzq = dzn.b;
                if (dzq == null) {
                    dzq = dzq.c;
                }
                sb.append("AudioRouteRequestParams: [" + fbi.p(dzq) + "]");
            } else if (i4 == 5) {
                sb.append(", ");
                if (eav.b == 205) {
                    eab = (eab) eav.c;
                } else {
                    eab = eab.d;
                }
                ehg ehg5 = eab.c;
                if (ehg5 == null) {
                    ehg5 = ehg.c;
                }
                String q = fbi.q(ehg5);
                dzq dzq2 = eab.b;
                if (dzq2 == null) {
                    dzq2 = dzq.c;
                }
                sb.append("HotwordAudioRouteRequestParams: [" + q + ", " + fbi.p(dzq2) + "]");
            }
            return sb.toString();
        }
        throw null;
    }
}
