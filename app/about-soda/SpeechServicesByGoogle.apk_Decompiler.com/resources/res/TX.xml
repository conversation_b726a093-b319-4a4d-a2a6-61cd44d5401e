<?xml version="1.0" encoding="utf-8"?>
<com.android.car.ui.uxr.DrawableStateLinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:gravity="start" android:orientation="vertical" android:paddingBottom="@dimen/car_ui_preference_content_margin_bottom" android:clipToPadding="false" android:layout_width="match_parent" android:layout_height="wrap_content" android:minHeight="?android:attr/listPreferredItemHeightSmall" android:paddingStart="?android:attr/listPreferredItemPaddingStart" android:paddingEnd="?android:attr/listPreferredItemPaddingEnd">
    <com.android.car.ui.uxr.DrawableStateLinearLayout android:layout_gravity="start" android:orientation="horizontal" android:layout_width="wrap_content" android:layout_height="wrap_content">
        <com.android.car.ui.uxr.DrawableStateImageView android:id="@android:id/icon" android:padding="@dimen/car_ui_footer_icon_padding" android:layout_width="@dimen/car_ui_footer_icon_size" android:layout_height="@dimen/car_ui_footer_icon_size" android:layout_marginTop="@dimen/car_ui_preference_content_margin_top" android:alpha="@dimen/car_ui_footer_icon_alpha" android:layout_marginEnd="@dimen/car_ui_preference_icon_margin_end"/>
        <com.android.car.ui.uxr.DrawableStateTextView android:textStyle="bold" android:textColor="@color/car_ui_text_color_secondary" android:id="@android:id/title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/car_ui_preference_content_margin_top"/>
    </com.android.car.ui.uxr.DrawableStateLinearLayout>
    <com.android.car.ui.uxr.DrawableStateTextView android:textAppearance="@style/TextAppearance.CarUi.PreferenceSummary" android:id="@android:id/summary" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/car_ui_preference_content_margin_top"/>
    <com.android.car.ui.uxr.DrawableStateTextView android:textStyle="bold" android:textColor="?android:attr/colorAccent" android:id="@+id/car_ui_link" android:layout_width="wrap_content" android:layout_height="wrap_content" android:layout_marginTop="@dimen/car_ui_preference_content_margin_top"/>
</com.android.car.ui.uxr.DrawableStateLinearLayout>
