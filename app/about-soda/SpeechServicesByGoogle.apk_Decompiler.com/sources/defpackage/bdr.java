package defpackage;

import java.util.concurrent.Callable;

/* renamed from: bdr  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bdr implements Callable {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public bdr(add add, int i) {
        this.b = i;
        this.a = add;
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v0, resolved type: boolean} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v1, resolved type: boolean} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v5, resolved type: boolean} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v24, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v6, resolved type: boolean} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v7, resolved type: boolean} */
    /* JADX WARNING: type inference failed for: r1v10, types: [bhf, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v12, types: [bhf, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v20, types: [bhf, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v41, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v69, types: [elk, java.lang.Object] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object call() {
        /*
            r13 = this;
            int r0 = r13.b
            r1 = 7
            r2 = 10
            r3 = 2
            r4 = 0
            java.lang.Boolean r5 = java.lang.Boolean.valueOf(r4)
            r6 = 0
            r7 = 1
            java.lang.Boolean r8 = java.lang.Boolean.valueOf(r7)
            switch(r0) {
                case 0: goto L_0x0327;
                case 1: goto L_0x02f4;
                case 2: goto L_0x027f;
                case 3: goto L_0x026c;
                case 4: goto L_0x024c;
                case 5: goto L_0x01e7;
                case 6: goto L_0x01c4;
                case 7: goto L_0x0189;
                case 8: goto L_0x0166;
                case 9: goto L_0x0143;
                case 10: goto L_0x013b;
                case 11: goto L_0x0101;
                case 12: goto L_0x00f6;
                case 13: goto L_0x00f0;
                case 14: goto L_0x00cf;
                case 15: goto L_0x00c6;
                case 16: goto L_0x00bb;
                case 17: goto L_0x00a5;
                case 18: goto L_0x0044;
                case 19: goto L_0x003f;
                default: goto L_0x0014;
            }
        L_0x0014:
            hca r0 = defpackage.fcu.a
            hco r0 = r0.f()
            hby r0 = (defpackage.hby) r0
            java.lang.String r2 = "VoiceLanguageHelper.java"
            java.lang.String r3 = "com/google/android/libraries/speech/transcription/languages/VoiceLanguageHelper"
            java.lang.String r4 = "initializeLanguages"
            r5 = 66
            hco r0 = r0.j(r3, r4, r5, r2)
            hby r0 = (defpackage.hby) r0
            java.lang.String r2 = "#initializeLanguages"
            r0.r(r2)
            fct r0 = defpackage.fct.b
            java.lang.Object r0 = r0.C(r1)
            hvh r0 = (defpackage.hvh) r0
            java.lang.Object r1 = r13.a
            fcu r1 = (defpackage.fcu) r1
            android.content.Context r2 = r1.b
            goto L_0x035b
        L_0x003f:
            hdf r0 = defpackage.ezo.a
            java.lang.Object r0 = r13.a
            return r0
        L_0x0044:
            java.lang.Object r0 = r13.a
            ezd r0 = (defpackage.ezd) r0
            gxq r0 = r0.b
            j$.util.stream.Stream r0 = j$.util.Collection.EL.stream(r0)
            eyh r4 = new eyh
            r5 = 8
            r4.<init>(r5)
            j$.util.stream.Stream r0 = r0.map(r4)
            eyf r4 = new eyf
            r4.<init>(r3)
            j$.util.stream.Stream r0 = r0.filter(r4)
            eyh r4 = new eyh
            r5 = 9
            r4.<init>(r5)
            j$.util.stream.Stream r0 = r0.flatMap(r4)
            eyf r4 = new eyf
            r4.<init>(r3)
            j$.util.stream.Stream r0 = r0.filter(r4)
            eyf r3 = new eyf
            r4 = 3
            r3.<init>(r4)
            j$.util.stream.Stream r0 = r0.filter(r3)
            eyh r3 = new eyh
            r3.<init>(r2)
            j$.util.stream.Stream r0 = r0.map(r3)
            eyf r2 = new eyf
            r3 = 4
            r2.<init>(r3)
            j$.util.stream.Stream r0 = r0.filter(r2)
            eyh r2 = new eyh
            r2.<init>(r1)
            j$.util.stream.Stream r0 = r0.map(r2)
            j$.util.stream.Collector r1 = defpackage.gvx.a
            java.lang.Object r0 = r0.collect(r1)
            gxq r0 = (defpackage.gxq) r0
            return r0
        L_0x00a5:
            hdf r0 = defpackage.exx.a
            hco r0 = r0.f()
            hdc r0 = (defpackage.hdc) r0
            java.lang.Object r1 = r13.a
            java.util.Locale r1 = (java.util.Locale) r1
            java.lang.String r1 = r1.toLanguageTag()
            java.lang.String r2 = "All LP removals for locale %s finished. Disk space may remain occupied before MDD has had a chance to purge."
            r0.u(r2, r1)
            return r6
        L_0x00bb:
            java.lang.String[] r0 = defpackage.ewt.a
            java.lang.Object r0 = r13.a
            android.content.Context r0 = (android.content.Context) r0
            java.io.File[] r0 = r0.getExternalCacheDirs()
            return r0
        L_0x00c6:
            java.lang.Object r0 = r13.a
            android.content.Context r0 = (android.content.Context) r0
            java.io.File[] r0 = defpackage.yd.g(r0)
            return r0
        L_0x00cf:
            java.lang.Object r0 = r13.a
            eam r1 = defpackage.eam.SEAMLESS_HANDOVER_TIMEOUT
            enb r0 = (defpackage.enb) r0
            ebk r1 = r0.e(r1)
            hme r2 = r1.a()
            ema r4 = new ema
            ehg r5 = r0.a
            emd r0 = r0.d
            r4.<init>(r0, r5, r3)
            hls r3 = defpackage.gof.g(r4)
            java.util.concurrent.Executor r0 = r0.c
            defpackage.hfc.T(r2, r3, r0)
            return r1
        L_0x00f0:
            java.lang.Object r0 = r13.a
            r0.a()
            return r8
        L_0x00f6:
            java.lang.Object r0 = r13.a
            eam r1 = defpackage.eam.TIMEOUT
            eli r0 = (defpackage.eli) r0
            dyx r0 = r0.e(r1)
            return r0
        L_0x0101:
            java.lang.Object r0 = r13.a
            edo r0 = (defpackage.edo) r0
            java.util.concurrent.atomic.AtomicBoolean r1 = r0.h
            boolean r1 = r1.getAndSet(r7)
            if (r1 == 0) goto L_0x0110
            jkd r0 = defpackage.jkd.a
            goto L_0x013a
        L_0x0110:
            hca r1 = defpackage.edo.a
            hco r1 = r1.f()
            hcr r2 = defpackage.hdg.a
            java.lang.String r3 = "ALT.ProcStartTime"
            hco r1 = r1.h(r2, r3)
            java.lang.String r2 = "AudioStartTimeProcessor.kt"
            java.lang.String r3 = "com/google/android/libraries/search/audio/audiobuffer/processing/AudioStartTimeProcessor"
            java.lang.String r4 = "close$lambda$3"
            r5 = 93
            hco r1 = r1.j(r3, r4, r5, r2)
            hby r1 = (defpackage.hby) r1
            java.lang.String r2 = r0.g
            java.lang.String r3 = "#audio# close processor(%s)"
            r1.u(r3, r2)
            evj r1 = defpackage.evj.REASON_CLOSED
            r0.d(r1)
            jkd r0 = defpackage.jkd.a
        L_0x013a:
            return r0
        L_0x013b:
            java.lang.Object r0 = r13.a
            android.content.BroadcastReceiver$PendingResult r0 = (android.content.BroadcastReceiver.PendingResult) r0
            r0.finish()
            return r6
        L_0x0143:
            hca r0 = defpackage.djs.a
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            java.lang.String r1 = "DeferrableExecutor.java"
            java.lang.String r2 = "com/google/android/libraries/performance/primes/DeferrableExecutor"
            java.lang.String r3 = "unblockAfterResume"
            r4 = 120(0x78, float:1.68E-43)
            hco r0 = r0.j(r2, r3, r4, r1)
            hby r0 = (defpackage.hby) r0
            java.lang.String r1 = "DeferrableExecutor unblocked after onResume"
            r0.r(r1)
            java.lang.Object r0 = r13.a
            dji r0 = (defpackage.dji) r0
            r0.i()
            return r6
        L_0x0166:
            hca r0 = defpackage.djs.a
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            java.lang.String r1 = "DeferrableExecutor.java"
            java.lang.String r2 = "com/google/android/libraries/performance/primes/DeferrableExecutor"
            java.lang.String r3 = "unblockAfterMaxDelay"
            r4 = 114(0x72, float:1.6E-43)
            hco r0 = r0.j(r2, r3, r4, r1)
            hby r0 = (defpackage.hby) r0
            java.lang.String r1 = "DeferrableExecutor unblocked after max task delay"
            r0.r(r1)
            java.lang.Object r0 = r13.a
            dji r0 = (defpackage.dji) r0
            r0.i()
            return r6
        L_0x0189:
            java.lang.Object r0 = r13.a
            r1 = r0
            dgl r1 = (defpackage.dgl) r1
            dgm r1 = r1.h
            monitor-enter(r1)
            r2 = r0
            dgl r2 = (defpackage.dgl) r2     // Catch:{ all -> 0x01c1 }
            dgm r2 = r2.h     // Catch:{ all -> 0x01c1 }
            java.util.Map r2 = r2.c     // Catch:{ all -> 0x01c1 }
            r3 = r0
            dgl r3 = (defpackage.dgl) r3     // Catch:{ all -> 0x01c1 }
            ded r3 = r3.a     // Catch:{ all -> 0x01c1 }
            ddc r3 = r3.o()     // Catch:{ all -> 0x01c1 }
            java.lang.Object r2 = r2.remove(r3)     // Catch:{ all -> 0x01c1 }
            dgl r2 = (defpackage.dgl) r2     // Catch:{ all -> 0x01c1 }
            if (r2 == 0) goto L_0x01ac
            r2.close()     // Catch:{ all -> 0x01c1 }
        L_0x01ac:
            monitor-exit(r1)     // Catch:{ all -> 0x01c1 }
            monitor-enter(r0)
            r1 = r0
            dgl r1 = (defpackage.dgl) r1     // Catch:{ all -> 0x01be }
            hmr r1 = r1.f     // Catch:{ all -> 0x01be }
            if (r1 == 0) goto L_0x01b8
            r1.m(r6)     // Catch:{ all -> 0x01be }
        L_0x01b8:
            monitor-exit(r0)     // Catch:{ all -> 0x01be }
            hme r0 = defpackage.hfc.K(r6)
            return r0
        L_0x01be:
            r1 = move-exception
            monitor-exit(r0)     // Catch:{ all -> 0x01be }
            throw r1
        L_0x01c1:
            r0 = move-exception
            monitor-exit(r1)     // Catch:{ all -> 0x01c1 }
            throw r0
        L_0x01c4:
            java.lang.Object r0 = r13.a
            java.util.Iterator r0 = r0.iterator()
        L_0x01ca:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L_0x01e5
            java.lang.Object r1 = r0.next()
            hme r1 = (defpackage.hme) r1
            java.lang.Object r1 = defpackage.hfc.S(r1)
            ctf r1 = (defpackage.ctf) r1
            ctf r2 = defpackage.ctf.NONE
            if (r1 == r2) goto L_0x01ca
            ctf r2 = defpackage.ctf.SUBSCRIBED
            if (r1 == r2) goto L_0x01ca
            goto L_0x01e6
        L_0x01e5:
            r5 = r8
        L_0x01e6:
            return r5
        L_0x01e7:
            java.lang.Object r0 = r13.a
            cuf r0 = (defpackage.cuf) r0
            grh r1 = r0.b
            boolean r1 = r1.f()
            if (r1 != 0) goto L_0x01fb
            java.lang.String r0 = "%s: Called schedulePeriodicTasksInternal when taskScheduler is not provided."
            java.lang.String r1 = "MobileDataDownload"
            defpackage.cyh.g(r0, r1)
            goto L_0x024b
        L_0x01fb:
            grh r0 = r0.b
            java.lang.Object r0 = r0.b()
            cxk r0 = (defpackage.cxk) r0
            ikj r1 = defpackage.ikj.a
            ikk r1 = r1.a()
            long r9 = r1.b()
            java.lang.String r8 = "MDD.CHARGING.PERIODIC.TASK"
            r11 = 3
            gqd r12 = defpackage.gqd.a
            r7 = r0
            r7.d(r8, r9, r11, r12)
            ikj r1 = defpackage.ikj.a
            ikk r1 = r1.a()
            long r9 = r1.c()
            java.lang.String r8 = "MDD.MAINTENANCE.PERIODIC.GCM.TASK"
            gqd r12 = defpackage.gqd.a
            r7.d(r8, r9, r11, r12)
            ikj r1 = defpackage.ikj.a
            ikk r1 = r1.a()
            long r9 = r1.a()
            java.lang.String r8 = "MDD.CELLULAR.CHARGING.PERIODIC.TASK"
            r11 = 1
            gqd r12 = defpackage.gqd.a
            r7.d(r8, r9, r11, r12)
            ikj r1 = defpackage.ikj.a
            ikk r1 = r1.a()
            long r9 = r1.d()
            java.lang.String r8 = "MDD.WIFI.CHARGING.PERIODIC.TASK"
            r11 = 2
            gqd r12 = defpackage.gqd.a
            r7.d(r8, r9, r11, r12)
        L_0x024b:
            return r6
        L_0x024c:
            java.lang.Object r0 = r13.a
            byw r0 = (defpackage.byw) r0
            java.lang.Object r1 = r0.a
            androidx.work.impl.WorkDatabase r1 = (androidx.work.impl.WorkDatabase) r1
            java.lang.String r2 = "next_job_scheduler_id"
            int r1 = defpackage.yh.m(r1, r2)
            if (r1 < 0) goto L_0x025e
            r4 = r1
            goto L_0x0267
        L_0x025e:
            java.lang.Object r0 = r0.a
            androidx.work.impl.WorkDatabase r0 = (androidx.work.impl.WorkDatabase) r0
            java.lang.String r1 = "next_job_scheduler_id"
            defpackage.yh.n(r0, r1, r7)
        L_0x0267:
            java.lang.Integer r0 = java.lang.Integer.valueOf(r4)
            return r0
        L_0x026c:
            java.lang.Object r0 = r13.a
            byw r0 = (defpackage.byw) r0
            java.lang.Object r0 = r0.a
            androidx.work.impl.WorkDatabase r0 = (androidx.work.impl.WorkDatabase) r0
            java.lang.String r1 = "next_alarm_manager_id"
            int r0 = defpackage.yh.m(r0, r1)
            java.lang.Integer r0 = java.lang.Integer.valueOf(r0)
            return r0
        L_0x027f:
            java.lang.Object r0 = r13.a
            eez r0 = (defpackage.eez) r0
            java.lang.Object r1 = r0.k
            java.lang.Object r2 = r0.j
            java.lang.String r2 = (java.lang.String) r2
            bbx r1 = r1.a(r2)
            bbx r2 = defpackage.bbx.ENQUEUED
            if (r1 != r2) goto L_0x02ef
            java.lang.Object r1 = r0.k
            java.lang.Object r2 = r0.j
            bbx r3 = defpackage.bbx.RUNNING
            java.lang.String r2 = (java.lang.String) r2
            r1.m(r3, r2)
            java.lang.Object r1 = r0.k
            java.lang.Object r2 = r0.j
            r3 = r1
            bhx r3 = (defpackage.bhx) r3
            aus r4 = r3.a
            r4.k()
            auv r4 = r3.f
            axc r4 = r4.d()
            java.lang.String r2 = (java.lang.String) r2
            r4.g(r7, r2)
            r2 = r1
            bhx r2 = (defpackage.bhx) r2     // Catch:{ all -> 0x02e8 }
            aus r2 = r2.a     // Catch:{ all -> 0x02e8 }
            r2.l()     // Catch:{ all -> 0x02e8 }
            r4.a()     // Catch:{ all -> 0x02df }
            r2 = r1
            bhx r2 = (defpackage.bhx) r2     // Catch:{ all -> 0x02df }
            aus r2 = r2.a     // Catch:{ all -> 0x02df }
            r2.o()     // Catch:{ all -> 0x02df }
            bhx r1 = (defpackage.bhx) r1     // Catch:{ all -> 0x02e8 }
            aus r1 = r1.a     // Catch:{ all -> 0x02e8 }
            r1.m()     // Catch:{ all -> 0x02e8 }
            auv r1 = r3.f
            r1.f(r4)
            java.lang.Object r1 = r0.k
            java.lang.Object r0 = r0.j
            java.lang.String r0 = (java.lang.String) r0
            r2 = -256(0xffffffffffffff00, float:NaN)
            r1.j(r0, r2)
            r4 = r7
            goto L_0x02ef
        L_0x02df:
            r0 = move-exception
            bhx r1 = (defpackage.bhx) r1     // Catch:{ all -> 0x02e8 }
            aus r1 = r1.a     // Catch:{ all -> 0x02e8 }
            r1.m()     // Catch:{ all -> 0x02e8 }
            throw r0     // Catch:{ all -> 0x02e8 }
        L_0x02e8:
            r0 = move-exception
            auv r1 = r3.f
            r1.f(r4)
            throw r0
        L_0x02ef:
            java.lang.Boolean r0 = java.lang.Boolean.valueOf(r4)
            return r0
        L_0x02f4:
            java.lang.Object r0 = r13.a
            add r0 = (defpackage.add) r0
            java.util.concurrent.atomic.AtomicBoolean r0 = r0.e
            r0.set(r7)
            android.os.Process.setThreadPriority(r2)     // Catch:{ all -> 0x0313 }
            java.lang.Object r0 = r13.a     // Catch:{ all -> 0x0313 }
            add r0 = (defpackage.add) r0     // Catch:{ all -> 0x0313 }
            java.lang.Object r6 = r0.a()     // Catch:{ all -> 0x0313 }
            android.os.Binder.flushPendingCommands()     // Catch:{ all -> 0x0313 }
            java.lang.Object r0 = r13.a
            add r0 = (defpackage.add) r0
            r0.d(r6)
            return r6
        L_0x0313:
            r0 = move-exception
            java.lang.Object r1 = r13.a     // Catch:{ all -> 0x031e }
            add r1 = (defpackage.add) r1     // Catch:{ all -> 0x031e }
            java.util.concurrent.atomic.AtomicBoolean r1 = r1.d     // Catch:{ all -> 0x031e }
            r1.set(r7)     // Catch:{ all -> 0x031e }
            throw r0     // Catch:{ all -> 0x031e }
        L_0x031e:
            r0 = move-exception
            java.lang.Object r1 = r13.a
            add r1 = (defpackage.add) r1
            r1.d(r6)
            throw r0
        L_0x0327:
            java.lang.Object r0 = r13.a
            eez r0 = (defpackage.eez) r0
            java.lang.Object r0 = r0.a
            bhe r0 = (defpackage.bhe) r0
            bbx r1 = r0.c
            bbx r2 = defpackage.bbx.ENQUEUED
            if (r1 == r2) goto L_0x033c
            java.lang.String r0 = defpackage.bdy.a
            defpackage.bbk.a()
        L_0x033a:
            r5 = r8
            goto L_0x035a
        L_0x033c:
            boolean r1 = r0.d()
            if (r1 != 0) goto L_0x0348
            boolean r1 = r0.c()
            if (r1 == 0) goto L_0x035a
        L_0x0348:
            long r1 = java.lang.System.currentTimeMillis()
            long r3 = r0.a()
            int r0 = (r1 > r3 ? 1 : (r1 == r3 ? 0 : -1))
            if (r0 >= 0) goto L_0x035a
            defpackage.bbk.a()
            java.lang.String r0 = defpackage.bdy.a
            goto L_0x033a
        L_0x035a:
            return r5
        L_0x035b:
            android.content.res.Resources r2 = r2.getResources()     // Catch:{ IOException -> 0x039d }
            r3 = 2131951638(0x7f130016, float:1.9539696E38)
            java.io.InputStream r6 = r2.openRawResource(r3)     // Catch:{ IOException -> 0x039d }
            boolean r2 = defpackage.fnk.g()     // Catch:{ IOException -> 0x039d }
            if (r2 == 0) goto L_0x037d
            int r2 = r6.available()     // Catch:{ IOException -> 0x039d }
            r3 = 1024(0x400, float:1.435E-42)
            if (r2 > r3) goto L_0x0375
            goto L_0x037d
        L_0x0375:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException     // Catch:{ IOException -> 0x039d }
            java.lang.String r1 = "parseFromRawRes can only parse small Protocol Buffers on the UI thread. This provides a best effort protection against dropping frames for parsing."
            r0.<init>(r1)     // Catch:{ IOException -> 0x039d }
            throw r0     // Catch:{ IOException -> 0x039d }
        L_0x037d:
            java.lang.Object r0 = r0.d(r6)     // Catch:{ IOException -> 0x039d }
            defpackage.hgz.a(r6)
            fct r0 = (defpackage.fct) r0
            r2 = 5
            java.lang.Object r2 = r0.C(r2)
            htk r2 = (defpackage.htk) r2
            r2.x(r0)
            htq r0 = r2.r()
            fct r0 = (defpackage.fct) r0
            r1.d = r0
            fct r0 = r1.d
            return r0
        L_0x039b:
            r0 = move-exception
            goto L_0x03a4
        L_0x039d:
            r0 = move-exception
            java.lang.RuntimeException r1 = new java.lang.RuntimeException     // Catch:{ all -> 0x039b }
            r1.<init>(r0)     // Catch:{ all -> 0x039b }
            throw r1     // Catch:{ all -> 0x039b }
        L_0x03a4:
            defpackage.hgz.a(r6)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bdr.call():java.lang.Object");
    }

    public /* synthetic */ bdr(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }
}
