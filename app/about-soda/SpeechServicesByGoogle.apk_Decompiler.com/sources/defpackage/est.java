package defpackage;

/* renamed from: est  reason: default package */
/* compiled from: PG */
public final class est implements jid {
    private final /* synthetic */ int a;

    public est(int i) {
        this.a = i;
    }

    public final /* synthetic */ jie a(iow iow, iov iov) {
        int i = this.a;
        if (i == 0) {
            return new esu(iow, iov);
        }
        if (i == 1) {
            return new esw(iow, iov);
        }
        if (i != 2) {
            return new hzy(iow, iov);
        }
        return new fbf(iow, iov);
    }
}
