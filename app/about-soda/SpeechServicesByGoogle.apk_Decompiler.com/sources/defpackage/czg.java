package defpackage;

import java.util.Map;

/* renamed from: czg  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class czg implements fpk {
    public final /* synthetic */ cyk a;
    private final /* synthetic */ int b;

    public /* synthetic */ czg(cyk cyk, int i) {
        this.b = i;
        this.a = cyk;
    }

    public final hva a(bzl bzl, hva hva) {
        if (this.b != 0) {
            cte cte = (cte) hva;
            htk l = cte.d.l();
            hbp k = bzl.H().entrySet().iterator();
            cyk cyk = this.a;
            while (k.hasNext()) {
                Map.Entry entry = (Map.Entry) k.next();
                try {
                    String str = (String) entry.getValue();
                    fvf.aP(str);
                    try {
                        l.B((String) entry.getKey(), (csx) cqh.C(str, (hvh) csx.w.C(7)));
                    } catch (hui e) {
                        cyh.g("SharedPreferences file groups metadata had unexpected format: %s", e);
                        cyk.d(1084);
                    }
                } catch (ClassCastException | NullPointerException e2) {
                    cyh.g("SharedPreferences file groups metadata key wasn't a string: %s", e2);
                    cyk.d(1083);
                }
            }
            return (cte) l.r();
        }
        ctn ctn = (ctn) hva;
        htk l2 = ctn.b.l();
        hbp k2 = bzl.H().entrySet().iterator();
        cyk cyk2 = this.a;
        while (k2.hasNext()) {
            Map.Entry entry2 = (Map.Entry) k2.next();
            try {
                String str2 = (String) entry2.getValue();
                fvf.aP(str2);
                try {
                    l2.D((String) entry2.getKey(), (ctl) cqh.C(str2, (hvh) ctl.h.C(7)));
                } catch (hui e3) {
                    cyh.g("SharedPreferences shared files metadata had unexpected format: %s", e3);
                    cyk2.d(1084);
                }
            } catch (ClassCastException | NullPointerException e4) {
                cyh.g("SharedPreferences shared files metadata key wasn't a string: %s", e4);
                cyk2.d(1083);
            }
        }
        return (ctn) l2.r();
    }
}
