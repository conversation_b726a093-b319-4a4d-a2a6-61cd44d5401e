package defpackage;

import com.google.android.tts.R;

/* renamed from: bjo  reason: default package */
/* compiled from: PG */
public final class bjo {
    public static final int[] a = {R.attr.carUiArrowColor, R.attr.carUiArrowGravity, R.attr.carUi<PERSON>rrowHeight, R.attr.carUi<PERSON>rrowRadius, R.attr.car<PERSON>i<PERSON>, R.attr.carUi<PERSON>ontentView, R.attr.carUiContentViewDrawable, R.attr.carUiHasArrow, R.attr.carUiOffsetX, R.attr.carUiOffsetY};
    public static final int[] b = {R.attr.carUiClickableWhileDisabled, R.attr.carUiShowChevron, R.attr.car_ui_ux_restricted};
    public static final int[] c = {16842948, R.attr.carUiSize, <PERSON><PERSON>attr.enableD<PERSON>, <PERSON><PERSON>attr.<PERSON>, <PERSON>.attr.layout<PERSON><PERSON>, R.attr.numOf<PERSON><PERSON>um<PERSON>, R.attr.reverseLayout, R.attr.rotaryScrollEnabled};
    public static final int[] d = {R.attr.activatable, R.attr.activated, R.attr.carUiIcon, R.attr.checkable, R.attr.checked, R.attr.displayBehavior, R.attr.id, R.attr.onClick, R.attr.search, R.attr.settings, R.attr.showIconAndTitle, R.attr.tinted, R.attr.title, R.attr.uxRestrictions, R.attr.visible};
    public static final int[] e = {16842994, 16843243, R.attr.carUiLayout, R.attr.widgetLayout};
    public static final int[] f = {R.attr.secondaryActionIcon};
    public static final int[] g = {R.attr.actionEnabled, R.attr.actionShown};
    public static final int[] h = {R.attr.secondaryActionStyle, R.attr.secondaryActionText};
    public static final int[] i = {R.attr.shouldRestoreFocus};
    public static final int[] j = {R.attr.bottomBoundOffset, R.attr.defaultFocus, R.attr.defaultFocusOverridesHistory, R.attr.endBoundOffset, R.attr.highlightPaddingBottom, R.attr.highlightPaddingEnd, R.attr.highlightPaddingHorizontal, R.attr.highlightPaddingStart, R.attr.highlightPaddingTop, R.attr.highlightPaddingVertical, R.attr.horizontalBoundOffset, R.attr.nudgeDown, R.attr.nudgeDownDisabled, R.attr.nudgeDownShortcut, R.attr.nudgeLeft, R.attr.nudgeLeftDisabled, R.attr.nudgeLeftShortcut, R.attr.nudgeRight, R.attr.nudgeRightDisabled, R.attr.nudgeRightShortcut, R.attr.nudgeShortcut, R.attr.nudgeShortcutDirection, R.attr.nudgeUp, R.attr.nudgeUpDisabled, R.attr.nudgeUpShortcut, R.attr.startBoundOffset, R.attr.topBoundOffset, R.attr.verticalBoundOffset, R.attr.wrapAround};
    public static final int[] k = {16842754, 16842765, 16842766, 16842994, 16843233, 16843238, 16843240, 16843241, 16843242, 16843243, 16843244, 16843245, 16843246, 16843491, 16844124, 16844129, R.attr.allowDividerAbove, R.attr.allowDividerBelow, R.attr.carUiPreferenceType, R.attr.defaultValue, R.attr.dependency, R.attr.enableCopying, R.attr.enabled, R.attr.fragment, R.attr.icon, R.attr.iconSpaceReserved, R.attr.isPreferenceVisible, R.attr.key, R.attr.layout, R.attr.order, R.attr.persistent, R.attr.selectable, R.attr.shouldDisableView, R.attr.singleLineTitle, R.attr.summary, R.attr.title, R.attr.widgetLayout};
}
