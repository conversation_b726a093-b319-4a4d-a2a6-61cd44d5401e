package defpackage;

import java.io.File;

/* renamed from: cur  reason: default package */
/* compiled from: PG */
public final class cur implements diq {
    private final po a;

    public cur(po poVar) {
        this.a = poVar;
    }

    public final void a(File file, dip dip) {
        csh csh;
        cyh.p("%s: Failed to download file %s due to %s", "DownloadCompleteHandler", file.getName(), dip.b);
        kml a2 = csi.a();
        switch (((dio) dip.b).ordinal()) {
            case 0:
                csh = csh.ANDROID_DOWNLOADER_UNKNOWN;
                break;
            case 1:
                csh = csh.ANDROID_DOWNLOADER_CANCELED;
                break;
            case 2:
                csh = csh.ANDROID_DOWNLOADER_INVALID_REQUEST;
                break;
            case 3:
                csh = csh.ANDROID_DOWNLOADER_HTTP_ERROR;
                break;
            case 4:
                csh = csh.ANDROID_DOWNLOADER_REQUEST_ERROR;
                break;
            case 5:
                csh = csh.ANDROID_DOWNLOADER_RESPONSE_OPEN_ERROR;
                break;
            case 6:
                csh = csh.ANDROID_DOWNLOADER_RESPONSE_CLOSE_ERROR;
                break;
            case 7:
                csh = csh.ANDROID_DOWNLOADER_NETWORK_IO_ERROR;
                break;
            case 8:
                csh = csh.ANDROID_DOWNLOADER_DISK_IO_ERROR;
                break;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                csh = csh.ANDROID_DOWNLOADER_FILE_SYSTEM_ERROR;
                break;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                csh = csh.ANDROID_DOWNLOADER_UNKNOWN_IO_ERROR;
                break;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                csh = csh.ANDROID_DOWNLOADER_OAUTH_ERROR;
                break;
            default:
                csh = csh.UNKNOWN_ERROR;
                break;
        }
        a2.b = csh;
        String str = "ANDROID_DOWNLOADER_" + ((dio) dip.b).name() + "; ";
        int i = dip.a;
        if (i >= 0) {
            str = str + "HttpCode: " + i + "; ";
        }
        Object obj = dip.c;
        if (obj != null) {
            str = str + "Message: " + ((String) obj) + "; ";
        }
        Object obj2 = dip.d;
        if (obj2 != null) {
            str = str + "AuthToken: " + ((String) obj2) + "; ";
        }
        Object obj3 = dip.e;
        a2.c = str;
        if (obj3 != null) {
            a2.d = obj3;
        }
        this.a.d(a2.a());
    }

    public final void b(File file) {
        cyh.d("%s: Downloaded file %s", "DownloadCompleteHandler", file.getName());
        this.a.c((Object) null);
    }
}
