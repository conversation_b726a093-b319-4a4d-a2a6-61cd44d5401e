package defpackage;

import android.app.Notification;
import android.content.Context;
import android.content.Intent;
import android.os.Build;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CancellationException;

/* renamed from: bgc  reason: default package */
/* compiled from: PG */
public final class bgc implements bey, bce {
    public static final String a = bbk.b("SystemFgDispatcher");
    public final bdm b;
    public final Object c = new Object();
    bgt d;
    final Map e;
    public final Map f;
    public final Map g;
    public bgb h;
    public final cyw i;
    public final byw j;
    private final Context k;

    public bgc(Context context) {
        this.k = context;
        bdm i2 = bdm.i(context);
        this.b = i2;
        this.i = i2.k;
        this.d = null;
        this.e = new LinkedHashMap();
        this.g = new HashMap();
        this.f = new HashMap();
        this.j = new byw(i2.j, (byte[]) null);
        i2.f.a(this);
    }

    public final void a(bgt bgt, boolean z) {
        jrz jrz;
        Map.Entry entry;
        synchronized (this.c) {
            if (((bhe) this.f.remove(bgt)) != null) {
                jrz = (jrz) this.g.remove(bgt);
            } else {
                jrz = null;
            }
            if (jrz != null) {
                jrz.r((CancellationException) null);
            }
        }
        baz baz = (baz) this.e.remove(bgt);
        if (bgt.equals(this.d)) {
            if (this.e.size() > 0) {
                Iterator it = this.e.entrySet().iterator();
                Object next = it.next();
                while (true) {
                    entry = (Map.Entry) next;
                    if (!it.hasNext()) {
                        break;
                    }
                    next = it.next();
                }
                this.d = (bgt) entry.getKey();
                if (this.h != null) {
                    baz baz2 = (baz) entry.getValue();
                    this.h.c(baz2.a, baz2.b, baz2.c);
                    this.h.a(baz2.a);
                }
            } else {
                this.d = null;
            }
        }
        bgb bgb = this.h;
        if (baz != null && bgb != null) {
            bbk.a();
            int i2 = baz.a;
            Objects.toString(bgt);
            int i3 = baz.b;
            bgb.a(baz.a);
        }
    }

    public final void b(Intent intent) {
        if (this.h != null) {
            int i2 = 0;
            int intExtra = intent.getIntExtra("KEY_NOTIFICATION_ID", 0);
            int intExtra2 = intent.getIntExtra("KEY_FOREGROUND_SERVICE_TYPE", 0);
            bgt bgt = new bgt(intent.getStringExtra("KEY_WORKSPEC_ID"), intent.getIntExtra("KEY_GENERATION", 0));
            Notification notification = (Notification) intent.getParcelableExtra("KEY_NOTIFICATION");
            bbk.a();
            if (notification != null) {
                baz baz = new baz(intExtra, notification, intExtra2);
                this.e.put(bgt, baz);
                baz baz2 = (baz) this.e.get(this.d);
                if (baz2 == null) {
                    this.d = bgt;
                } else {
                    this.h.b(intExtra, notification);
                    if (Build.VERSION.SDK_INT >= 29) {
                        for (Map.Entry value : this.e.entrySet()) {
                            i2 |= ((baz) value.getValue()).b;
                        }
                        baz = new baz(baz2.a, baz2.c, i2);
                    } else {
                        baz = baz2;
                    }
                }
                this.h.c(baz.a, baz.b, baz.c);
                return;
            }
            throw new IllegalArgumentException("Notification passed in the intent was null.");
        }
        throw new IllegalStateException("handleNotify was called on the destroyed dispatcher");
    }

    public final void c() {
        this.h = null;
        synchronized (this.c) {
            for (jrz r : this.g.values()) {
                r.r((CancellationException) null);
            }
        }
        this.b.f.b(this);
    }

    public final void e(bhe bhe, wf wfVar) {
        if (wfVar instanceof ber) {
            String str = bhe.b;
            bbk.a();
            bdm bdm = this.b;
            bgt f2 = wg.f(bhe);
            cyw cyw = bdm.k;
            bcp bcp = bdm.f;
            byw byw = new byw((Object) f2, (byte[]) null);
            jnu.e(bcp, "processor");
            cyw.a(new bik(bcp, byw, true, -512));
        }
    }
}
