package defpackage;

import android.support.v7.widget.RecyclerView;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewConfiguration;
import com.google.android.tts.R;

/* renamed from: blz  reason: default package */
/* compiled from: PG */
public final class blz implements View.OnTouchListener {
    public final View a;
    private float b = -1.0f;
    private final View c;
    private final RecyclerView d;
    private final int e;

    public blz(RecyclerView recyclerView, View view, View view2) {
        this.d = recyclerView;
        this.a = view;
        this.c = bnv.i(view2, R.id.car_ui_scrollbar_thumb);
        this.e = ViewConfiguration.get(recyclerView.getContext()).getScaledTouchSlop();
    }

    private final void a(float f) {
        int i;
        int y = ((int) this.a.getY()) + (this.c.getHeight() / 2);
        int y2 = (((int) this.a.getY()) + this.a.getHeight()) - (this.c.getHeight() / 2);
        int i2 = y2 - new int[]{y, y2}[0];
        View view = this.c;
        float y3 = view.getY() + (((float) view.getHeight()) / 2.0f);
        if (i2 == 0) {
            i = 0;
        } else {
            RecyclerView recyclerView = this.d;
            i = (int) (((f - y3) / ((float) i2)) * ((float) (recyclerView.computeVerticalScrollRange() - ((recyclerView.getHeight() - recyclerView.getPaddingTop()) - this.d.getPaddingBottom()))));
        }
        if (i != 0) {
            this.d.scrollBy(0, i);
        }
    }

    public final boolean onTouch(View view, MotionEvent motionEvent) {
        int action = motionEvent.getAction();
        if (action == 0) {
            this.b = motionEvent.getY();
            return true;
        } else if (action != 2) {
            if (Math.abs(this.b - motionEvent.getY()) < ((float) this.e)) {
                a(motionEvent.getY() + this.a.getY());
            }
            this.b = -1.0f;
            return true;
        } else {
            View view2 = this.c;
            if (motionEvent.getY() + this.a.getY() >= view2.getY() + ((float) view2.getHeight()) || motionEvent.getY() + this.a.getY() <= this.c.getY()) {
                return true;
            }
            View view3 = this.c;
            a(view3.getY() + (((float) view3.getHeight()) / 2.0f) + (motionEvent.getY() - this.b));
            this.b = motionEvent.getY();
            return true;
        }
    }
}
