package defpackage;

/* renamed from: eqq  reason: default package */
/* compiled from: PG */
public final class eqq extends htq implements hvb {
    public static final eqq g;
    private static volatile hvh h;
    public int a;
    public int b = 0;
    public Object c;
    public int d;
    public String e = "";
    public int f = -1;

    static {
        eqq eqq = new eqq();
        g = eqq;
        htq.z(eqq.class, eqq);
    }

    private eqq() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(g, "\u0004\u0005\u0001\u0001\u0001\u0005\u0005\u0000\u0000\u0000\u0001᠌\u0000\u0002<\u0000\u0003<\u0000\u0004ဈ\u0001\u0005င\u0002", new Object[]{"c", "b", "a", "d", ebb.k, eak.class, eah.class, "e", "f"});
        } else if (i2 == 3) {
            return new eqq();
        } else {
            if (i2 == 4) {
                return new htk((htq) g);
            }
            if (i2 == 5) {
                return g;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = h;
            if (hvh == null) {
                synchronized (eqq.class) {
                    hvh = h;
                    if (hvh == null) {
                        hvh = new htl(g);
                        h = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
