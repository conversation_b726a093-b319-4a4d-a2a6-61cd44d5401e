package defpackage;

/* renamed from: brz  reason: default package */
/* compiled from: PG */
final class brz implements Comparable {
    public final bsk a;
    public int b;

    public brz(bsk bsk, int i) {
        this.a = bsk;
        this.b = i;
    }

    public final /* bridge */ /* synthetic */ int compareTo(Object obj) {
        brz brz = (brz) obj;
        int i = this.b - brz.b;
        if (i != 0) {
            return i;
        }
        return this.a.p().compareTo(brz.a.p());
    }
}
