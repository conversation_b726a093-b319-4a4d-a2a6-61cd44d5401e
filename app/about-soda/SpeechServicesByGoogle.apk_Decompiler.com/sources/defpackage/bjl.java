package defpackage;

import android.os.SystemClock;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import com.android.car.ui.FocusParkingView;

/* renamed from: bjl  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bjl implements ViewTreeObserver.OnGlobalFocusChangeListener {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ bjl(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    public final void onGlobalFocusChanged(View view, View view2) {
        if (this.b != 0) {
            bjk bjk = (bjk) this.a;
            boolean hasFocus = bjk.c.hasFocus();
            if (hasFocus) {
                View focusedChild = bjk.c.getFocusedChild();
                while (focusedChild != null && !focusedChild.isFocused()) {
                    if (focusedChild instanceof ViewGroup) {
                        focusedChild = ((ViewGroup) focusedChild).getFocusedChild();
                    } else {
                        focusedChild = null;
                    }
                }
                bjk.y = focusedChild;
            } else if (bjk.d) {
                bjk.F.i(bjk.y, SystemClock.uptimeMillis());
                bjk.y = null;
            }
            if (bjk.d || !hasFocus || view == null || (view instanceof FocusParkingView)) {
                bjk.E = null;
            } else {
                bjk.E = yi.F(view);
                if (bjk.E == null) {
                    Log.w("FocusAreaHelper", "No ancestor focus area for ".concat(view.toString()));
                }
            }
            if (bjk.x && hasFocus && view != null && yi.F(view) == bjk.c) {
                bjk.F.h();
            }
            if ((bjk.f || bjk.e) && bjk.d != hasFocus) {
                bjk.c.invalidate();
            }
            bjk.d = hasFocus;
            return;
        }
        Object obj = this.a;
        if (true == (view2 instanceof FocusParkingView)) {
            view2 = null;
        }
        ((FocusParkingView) obj).a(view2);
    }
}
