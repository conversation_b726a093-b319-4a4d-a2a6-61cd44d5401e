package defpackage;

/* renamed from: cuz  reason: default package */
/* compiled from: PG */
public final class cuz extends cvb {
    private final hme a;

    public cuz(hme hme) {
        this.a = hme;
    }

    public final int b() {
        return 2;
    }

    public final hme c() {
        return this.a;
    }

    public final boolean equals(Object obj) {
        if (obj instanceof cvc) {
            cvc cvc = (cvc) obj;
            if (cvc.b() != 2 || !this.a.equals(cvc.c())) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return this.a.hashCode();
    }
}
