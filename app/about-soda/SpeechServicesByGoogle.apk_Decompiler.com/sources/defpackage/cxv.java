package defpackage;

import android.content.Context;
import android.net.Uri;
import android.os.StatFs;
import android.util.Pair;
import j$.util.Objects;
import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;

/* renamed from: cxv  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cxv implements hko {
    public final /* synthetic */ cxx a;
    public final /* synthetic */ ctg b;
    public final /* synthetic */ int c;
    public final /* synthetic */ long d;
    public final /* synthetic */ String e;
    public final /* synthetic */ Uri f;
    public final /* synthetic */ String g;
    public final /* synthetic */ int h;
    public final /* synthetic */ csz i;
    public final /* synthetic */ int j;
    public final /* synthetic */ List k;
    public final /* synthetic */ hse l;

    public /* synthetic */ cxv(cxx cxx, ctg ctg, int i2, long j2, String str, Uri uri, String str2, int i3, csz csz, int i4, List list, hse hse) {
        this.a = cxx;
        this.b = ctg;
        this.c = i2;
        this.d = j2;
        this.e = str;
        this.f = uri;
        this.g = str2;
        this.h = i3;
        this.i = csz;
        this.j = i4;
        this.k = list;
        this.l = hse;
    }

    public final hme a(Object obj) {
        long j2;
        Object obj2;
        String str;
        Object obj3;
        Object obj4;
        Object obj5;
        File file;
        int x;
        float f2;
        Void voidR = (Void) obj;
        Uri uri = this.f;
        cxx cxx = this.a;
        String str2 = this.g;
        if (!str2.startsWith("http") || !ijz.a.a().g() || str2.startsWith("https")) {
            try {
                j2 = cxx.i.c(uri);
            } catch (IOException unused) {
                j2 = 0;
            }
            try {
                Context context = cxx.a;
                long j3 = ((long) this.h) - j2;
                boolean h2 = ijz.a.a().h();
                csz csz = this.i;
                char c2 = 65535;
                if (h2) {
                    if (!cqx.m(str2, new hbi("inlinefile")) || j3 != 0) {
                        StatFs statFs = new StatFs(context.getFilesDir().getAbsolutePath());
                        long blockCount = ((long) statFs.getBlockCount()) * ((long) statFs.getBlockSize());
                        long availableBlocks = (((long) statFs.getAvailableBlocks()) * ((long) statFs.getBlockSize())) - j3;
                        float f3 = (float) blockCount;
                        double min = (double) Math.min(cqh.m() * f3, (float) ((int) ijz.a.a().b()));
                        if (csz != null) {
                            int x2 = a.x(csz.b);
                            if (x2 == 0) {
                                x2 = 1;
                            }
                            int i2 = x2 - 1;
                            if (i2 == 1) {
                                f2 = Math.min(f3 * cqh.m(), (float) ((int) ijz.a.a().d()));
                            } else if (i2 == 2) {
                                f2 = Math.min(f3 * cqh.m(), (float) ((int) ijz.a.a().c()));
                            }
                            min = (double) f2;
                        }
                        if (((double) availableBlocks) <= min) {
                            kml a2 = csi.a();
                            a2.b = csh.LOW_DISK_ERROR;
                            throw a2.a();
                        }
                    }
                }
                ctg ctg = this.b;
                if (cqh.n()) {
                    czs czs = cxx.c;
                    cyi cyi = cxx.e;
                    htk l2 = ctb.h.l();
                    if (!l2.b.B()) {
                        l2.u();
                    }
                    htq htq = l2.b;
                    ctb ctb = (ctb) htq;
                    ctg.getClass();
                    ctb.b = ctg;
                    ctb.a |= 1;
                    if (!htq.B()) {
                        l2.u();
                    }
                    long j4 = this.d;
                    htq htq2 = l2.b;
                    ctb ctb2 = (ctb) htq2;
                    ctb2.a |= 2;
                    ctb2.c = j4;
                    if (!htq2.B()) {
                        l2.u();
                    }
                    String str3 = this.e;
                    htq htq3 = l2.b;
                    ctb ctb3 = (ctb) htq3;
                    str3.getClass();
                    ctb3.a |= 4;
                    ctb3.d = str3;
                    if (!htq3.B()) {
                        l2.u();
                    }
                    int i3 = this.c;
                    ctb ctb4 = (ctb) l2.b;
                    ctb4.a |= 8;
                    ctb4.e = i3;
                    ctb ctb5 = (ctb) l2.r();
                    synchronized (czs.b) {
                        if (!czs.c.containsKey(ctb5)) {
                            HashMap hashMap = czs.c;
                            czr czr = new czr(czs.a, cyi, ctb5);
                            Objects.requireNonNull(czs.e);
                            hashMap.put(ctb5, new fon(czr, new czq(0), 10, TimeUnit.SECONDS));
                        }
                        czs.d.put(uri, (fon) czs.c.get(ctb5));
                    }
                } else {
                    cyh.m("%s: NetworkUsageMonitor is disabled", "MddFileDownloader");
                }
                if (cxx.d.f()) {
                    czp czp = (czp) cxx.d.b();
                    String str4 = ctg.b;
                    synchronized (czp.class) {
                        czp.b.put(uri, str4);
                    }
                }
                dbk dbk = new dbk((byte[]) null, (byte[]) null);
                dbk.i(-1);
                int i4 = gxq.d;
                dbk.h(hal.a);
                dbk.f(hse.c);
                if (uri != null) {
                    dbk.d = uri;
                    if (str2 != null) {
                        dbk.a = str2;
                        if (csz == null || (x = a.x(csz.c)) == 0 || x != 2) {
                            dbk.g(cuo.b);
                        } else {
                            dbk.g(cuo.c);
                        }
                        int i5 = this.j;
                        if (i5 > 0) {
                            dbk.i(i5);
                        }
                        List<cta> list = this.k;
                        gxl gxl = new gxl();
                        for (cta cta : list) {
                            gxl.h(Pair.create(cta.a, cta.b));
                        }
                        hse hse = this.l;
                        dbk.h(gxl.g());
                        dbk.f(hse);
                        cyw cyw = (cyw) cxx.b.a();
                        String str5 = dbk.a;
                        if (str5 != null) {
                            if (str5.startsWith("inlinefile")) {
                                fvf.aw(false, "InlineDownloadParams must be set when using inlinefile: scheme");
                                dbk.g(cuo.a);
                            }
                            if (dbk.c != 1 || (obj2 = dbk.d) == null || (str = dbk.a) == null || (obj3 = dbk.e) == null || (obj4 = dbk.f) == null || (obj5 = dbk.h) == null) {
                                StringBuilder sb = new StringBuilder();
                                if (dbk.d == null) {
                                    sb.append(" fileUri");
                                }
                                if (dbk.a == null) {
                                    sb.append(" urlToDownload");
                                }
                                if (dbk.e == null) {
                                    sb.append(" downloadConstraints");
                                }
                                if (dbk.c == 0) {
                                    sb.append(" trafficTag");
                                }
                                if (dbk.f == null) {
                                    sb.append(" extraHttpHeaders");
                                }
                                if (dbk.h == null) {
                                    sb.append(" customDownloaderMetadata");
                                }
                                throw new IllegalStateException("Missing required properties:".concat(sb.toString()));
                            }
                            Uri uri2 = (Uri) obj2;
                            cup cup = new cup(uri2, str, (cuo) obj3, dbk.b, (gxq) obj4, (grh) dbk.g, (hse) obj5);
                            String lastPathSegment = cup.a.getLastPathSegment();
                            fvf.aP(lastPathSegment);
                            try {
                                Object obj6 = cyw.b;
                                Uri uri3 = cup.a;
                                String scheme = uri3.getScheme();
                                int hashCode = scheme.hashCode();
                                if (hashCode != -861391249) {
                                    if (hashCode == 3143036 && scheme.equals("file")) {
                                        c2 = 1;
                                    }
                                } else if (scheme.equals("android")) {
                                    c2 = 0;
                                }
                                if (c2 == 0) {
                                    file = ftc.L(uri3, (Context) obj6);
                                } else if (c2 == 1) {
                                    file = ftc.I(uri3);
                                } else {
                                    throw new fny("Couldn't convert URI to path: ".concat(String.valueOf(String.valueOf(uri3))));
                                }
                                File parentFile = file.getParentFile();
                                fvf.aP(parentFile);
                                try {
                                    return kq.f(new cus(cyw, cup, parentFile, lastPathSegment, (djc) ((kjd) cyw.d).e(cup.a, new fok(0)), 0));
                                } catch (IOException e2) {
                                    cyh.j(e2, "%s: Unable to create mobstore ResponseWriter for file %s", "OffroadFileDownloader", cup.a);
                                    kml a3 = csi.a();
                                    a3.b = csh.UNABLE_TO_CREATE_MOBSTORE_RESPONSE_WRITER_ERROR;
                                    a3.d = e2;
                                    return hfc.J(a3.a());
                                }
                            } catch (IOException e3) {
                                cyh.h("%s: The file uri is malformed, uri = %s", "OffroadFileDownloader", cup.a);
                                kml a4 = csi.a();
                                a4.b = csh.MALFORMED_FILE_URI_ERROR;
                                a4.d = e3;
                                return hfc.J(a4.a());
                            }
                        } else {
                            throw new IllegalStateException("Property \"urlToDownload\" has not been set");
                        }
                    } else {
                        throw new NullPointerException("Null urlToDownload");
                    }
                } else {
                    throw new NullPointerException("Null fileUri");
                }
            } catch (csi e4) {
                cyh.h("%s: Not enough space to download file %s", "MddFileDownloader", str2);
                return hfc.J(e4);
            }
        } else {
            cyh.h("%s: File url = %s is not secure", "MddFileDownloader", str2);
            kml a5 = csi.a();
            a5.b = csh.INSECURE_URL_ERROR;
            return hfc.J(a5.a());
        }
    }
}
