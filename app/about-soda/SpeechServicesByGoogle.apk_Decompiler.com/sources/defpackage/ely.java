package defpackage;

/* renamed from: ely  reason: default package */
/* compiled from: PG */
public final class ely extends jmi implements jne {
    Object a;
    int b;
    final /* synthetic */ hme c;
    final /* synthetic */ hme d;
    final /* synthetic */ cyw e;
    private /* synthetic */ Object f;
    private final /* synthetic */ int g;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public ely(cyw cyw, hme hme, hme hme2, jlr jlr, int i) {
        super(2, jlr);
        this.g = i;
        this.e = cyw;
        this.c = hme;
        this.d = hme2;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        if (this.g != 0) {
            return ((ely) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
        return ((ely) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX WARNING: Removed duplicated region for block: B:27:0x0077 A[Catch:{ all -> 0x0080 }] */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0078 A[Catch:{ all -> 0x0080 }] */
    /* JADX WARNING: Removed duplicated region for block: B:58:0x00f7 A[Catch:{ all -> 0x0100 }] */
    /* JADX WARNING: Removed duplicated region for block: B:59:0x00f8 A[Catch:{ all -> 0x0100 }] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r8) {
        /*
            r7 = this;
            int r0 = r7.g
            r1 = 3
            r2 = 2
            r3 = 1
            if (r0 == 0) goto L_0x0087
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r4 = r7.b
            if (r4 == 0) goto L_0x0035
            if (r4 == r3) goto L_0x0029
            if (r4 == r2) goto L_0x001d
            java.lang.Object r0 = r7.a
            eel r0 = (defpackage.eel) r0
            java.lang.Object r1 = r7.f
            cyw r1 = (defpackage.cyw) r1
            defpackage.jji.c(r8)     // Catch:{ all -> 0x0080 }
            goto L_0x007a
        L_0x001d:
            java.lang.Object r2 = r7.a
            eel r2 = (defpackage.eel) r2
            java.lang.Object r3 = r7.f
            cyw r3 = (defpackage.cyw) r3
            defpackage.jji.c(r8)     // Catch:{ all -> 0x0080 }
            goto L_0x0065
        L_0x0029:
            java.lang.Object r3 = r7.a
            cyw r3 = (defpackage.cyw) r3
            java.lang.Object r4 = r7.f
            hme r4 = (defpackage.hme) r4
            defpackage.jji.c(r8)     // Catch:{ all -> 0x0080 }
            goto L_0x0053
        L_0x0035:
            defpackage.jji.c(r8)
            java.lang.Object r8 = r7.f
            jqs r8 = (defpackage.jqs) r8
            cyw r8 = r7.e
            hme r4 = r7.c
            hme r5 = r7.d
            r7.f = r5     // Catch:{ all -> 0x0080 }
            r7.a = r8     // Catch:{ all -> 0x0080 }
            r7.b = r3     // Catch:{ all -> 0x0080 }
            java.lang.Object r3 = defpackage.jqw.x(r4, r7)     // Catch:{ all -> 0x0080 }
            if (r3 != r0) goto L_0x004f
            goto L_0x0086
        L_0x004f:
            r4 = r5
            r6 = r3
            r3 = r8
            r8 = r6
        L_0x0053:
            eel r8 = (defpackage.eel) r8     // Catch:{ all -> 0x0080 }
            r7.f = r3     // Catch:{ all -> 0x0080 }
            r7.a = r8     // Catch:{ all -> 0x0080 }
            r7.b = r2     // Catch:{ all -> 0x0080 }
            java.lang.Object r2 = defpackage.jqw.x(r4, r7)     // Catch:{ all -> 0x0080 }
            if (r2 != r0) goto L_0x0062
            goto L_0x0086
        L_0x0062:
            r6 = r2
            r2 = r8
            r8 = r6
        L_0x0065:
            dyw r8 = (defpackage.dyw) r8     // Catch:{ all -> 0x0080 }
            hme r8 = r8.e()     // Catch:{ all -> 0x0080 }
            r7.f = r3     // Catch:{ all -> 0x0080 }
            r7.a = r2     // Catch:{ all -> 0x0080 }
            r7.b = r1     // Catch:{ all -> 0x0080 }
            java.lang.Object r8 = defpackage.jqw.x(r8, r7)     // Catch:{ all -> 0x0080 }
            if (r8 != r0) goto L_0x0078
            goto L_0x0086
        L_0x0078:
            r0 = r2
            r1 = r3
        L_0x007a:
            dyu r8 = (defpackage.dyu) r8     // Catch:{ all -> 0x0080 }
            r1.p(r0, r8)     // Catch:{ all -> 0x0080 }
            goto L_0x0084
        L_0x0080:
            r8 = move-exception
            defpackage.jji.b(r8)
        L_0x0084:
            jkd r0 = defpackage.jkd.a
        L_0x0086:
            return r0
        L_0x0087:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r4 = r7.b
            if (r4 == 0) goto L_0x00b5
            if (r4 == r3) goto L_0x00a9
            if (r4 == r2) goto L_0x009d
            java.lang.Object r0 = r7.a
            eel r0 = (defpackage.eel) r0
            java.lang.Object r1 = r7.f
            cyw r1 = (defpackage.cyw) r1
            defpackage.jji.c(r8)     // Catch:{ all -> 0x0100 }
            goto L_0x00fa
        L_0x009d:
            java.lang.Object r2 = r7.a
            eel r2 = (defpackage.eel) r2
            java.lang.Object r3 = r7.f
            cyw r3 = (defpackage.cyw) r3
            defpackage.jji.c(r8)     // Catch:{ all -> 0x0100 }
            goto L_0x00e5
        L_0x00a9:
            java.lang.Object r3 = r7.a
            cyw r3 = (defpackage.cyw) r3
            java.lang.Object r4 = r7.f
            hme r4 = (defpackage.hme) r4
            defpackage.jji.c(r8)     // Catch:{ all -> 0x0100 }
            goto L_0x00d3
        L_0x00b5:
            defpackage.jji.c(r8)
            java.lang.Object r8 = r7.f
            jqs r8 = (defpackage.jqs) r8
            cyw r8 = r7.e
            hme r4 = r7.c
            hme r5 = r7.d
            r7.f = r5     // Catch:{ all -> 0x0100 }
            r7.a = r8     // Catch:{ all -> 0x0100 }
            r7.b = r3     // Catch:{ all -> 0x0100 }
            java.lang.Object r3 = defpackage.jqw.x(r4, r7)     // Catch:{ all -> 0x0100 }
            if (r3 != r0) goto L_0x00cf
            goto L_0x0106
        L_0x00cf:
            r4 = r5
            r6 = r3
            r3 = r8
            r8 = r6
        L_0x00d3:
            eel r8 = (defpackage.eel) r8     // Catch:{ all -> 0x0100 }
            r7.f = r3     // Catch:{ all -> 0x0100 }
            r7.a = r8     // Catch:{ all -> 0x0100 }
            r7.b = r2     // Catch:{ all -> 0x0100 }
            java.lang.Object r2 = defpackage.jqw.x(r4, r7)     // Catch:{ all -> 0x0100 }
            if (r2 != r0) goto L_0x00e2
            goto L_0x0106
        L_0x00e2:
            r6 = r2
            r2 = r8
            r8 = r6
        L_0x00e5:
            ebj r8 = (defpackage.ebj) r8     // Catch:{ all -> 0x0100 }
            hme r8 = r8.e()     // Catch:{ all -> 0x0100 }
            r7.f = r3     // Catch:{ all -> 0x0100 }
            r7.a = r2     // Catch:{ all -> 0x0100 }
            r7.b = r1     // Catch:{ all -> 0x0100 }
            java.lang.Object r8 = defpackage.jqw.x(r8, r7)     // Catch:{ all -> 0x0100 }
            if (r8 != r0) goto L_0x00f8
            goto L_0x0106
        L_0x00f8:
            r0 = r2
            r1 = r3
        L_0x00fa:
            dyu r8 = (defpackage.dyu) r8     // Catch:{ all -> 0x0100 }
            r1.p(r0, r8)     // Catch:{ all -> 0x0100 }
            goto L_0x0104
        L_0x0100:
            r8 = move-exception
            defpackage.jji.b(r8)
        L_0x0104:
            jkd r0 = defpackage.jkd.a
        L_0x0106:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.ely.bk(java.lang.Object):java.lang.Object");
    }

    public final jlr c(Object obj, jlr jlr) {
        Object obj2 = obj;
        if (this.g != 0) {
            ely ely = new ely(this.e, this.c, this.d, jlr, 1, (byte[]) null);
            ely.f = obj2;
            return ely;
        }
        ely ely2 = new ely(this.e, this.c, this.d, jlr, 0);
        ely2.f = obj2;
        return ely2;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public ely(cyw cyw, hme hme, hme hme2, jlr jlr, int i, byte[] bArr) {
        super(2, jlr);
        this.g = i;
        this.e = cyw;
        this.c = hme;
        this.d = hme2;
    }
}
