package defpackage;

import android.content.Context;
import androidx.wear.ambient.AmbientModeSupport;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicReferenceArray;

/* renamed from: cxp  reason: default package */
/* compiled from: PG */
public final class cxp {
    public final Object a;
    public final Object b;
    public final Object c;
    public final Object d;
    public final Object e;

    public cxp(dtu dtu, AmbientModeSupport.AmbientController ambientController, ihn ihn, ihn ihn2, Executor executor) {
        jnu.e(ambientController, "subpackager");
        jnu.e(ihn, "runtimeProperties");
        jnu.e(ihn2, "runtimePropertiesWithFallback");
        jnu.e(executor, "executor");
        this.a = dtu;
        this.d = ambientController;
        this.c = ihn;
        this.b = ihn2;
        this.e = executor;
    }

    public static /* synthetic */ Object a(Object obj) {
        edr edr = (edr) obj;
        jnu.e(edr, "it");
        return (hme) edr.a();
    }

    /* JADX WARNING: type inference failed for: r1v0, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r2v0, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r1v4, types: [java.lang.Object, jjk] */
    public final eng b(ehg ehg, grh grh, int i, eck eck, ech ech) {
        ehg.getClass();
        ech.getClass();
        ? r1 = this.a;
        Object b2 = this.c.b();
        jqs jqs = (jqs) r1.b();
        jqs.getClass();
        emt a2 = ((emu) this.e).b();
        ((cqx) this.b.b()).getClass();
        bzj bzj = (bzj) ((iiv) this.d).a;
        bzj.getClass();
        return new eng(ehg, grh, i, eck, ech, gqd.a, (dlv) b2, jqs, a2, bzj);
    }

    public final int c() {
        return ((jpq) this.e).b - ((jpq) this.c).b;
    }

    public final jxx d(jxx jxx) {
        if (c() == 127) {
            return jxx;
        }
        if (jxx.h.a == 1) {
            ((jpq) this.a).a();
        }
        int i = ((jpq) this.e).b & 127;
        while (((AtomicReferenceArray) this.b).get(i) != null) {
            Thread.yield();
        }
        ((AtomicReferenceArray) this.b).lazySet(i, jxx);
        ((jpq) this.e).a();
        return null;
    }

    public final jxx e() {
        jxx jxx;
        while (true) {
            Object obj = this.c;
            Object obj2 = this.e;
            int i = ((jpq) obj).b;
            if (i - ((jpq) obj2).b == 0) {
                return null;
            }
            int i2 = i & 127;
            if (((jpq) this.c).b(i, i + 1) && (jxx = (jxx) ((AtomicReferenceArray) this.b).getAndSet(i2, (Object) null)) != null) {
                if (jxx.h.a == 1) {
                    ((jpq) this.a).c();
                    boolean z = jqv.a;
                }
                return jxx;
            }
        }
    }

    public final jxx f(int i, boolean z) {
        AtomicReferenceArray atomicReferenceArray;
        int i2 = i & 127;
        jxx jxx = (jxx) ((AtomicReferenceArray) this.b).get(i2);
        if (jxx == null || jxx.h.a != z) {
            return null;
        }
        Object obj = this.b;
        do {
            atomicReferenceArray = (AtomicReferenceArray) obj;
            if (atomicReferenceArray.compareAndSet(i2, jxx, (Object) null)) {
                if (z) {
                    ((jpq) this.a).c();
                }
                return jxx;
            }
        } while (atomicReferenceArray.get(i2) == jxx);
        return null;
    }

    public final hme g() {
        return ((dvr) this.d).c(false);
    }

    public cxp(fps fps, fps fps2, fps fps3, fps fps4, fps fps5) {
        this.a = fps;
        this.b = fps2;
        this.c = fps3;
        this.d = fps4;
        this.e = fps5;
    }

    public cxp(Context context, jjk jjk, jjk jjk2, jjk jjk3, bzj bzj) {
        jnu.e(jjk, "backgroundExecutor");
        jnu.e(jjk2, "synchronousFileStorage");
        jnu.e(jjk3, "phenotypeClient");
        this.a = context;
        this.b = jjk;
        this.e = jjk3;
        this.c = bzj;
        gee gee = new gee(jjk, 1);
        gee gee2 = new gee(jjk2, 0);
        gee gee3 = new gee(jjk3, 2);
        Context context2 = context;
        this.d = new dvr(context, gee, gee2, gee3);
    }

    public cxp(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5) {
        jjk.getClass();
        this.a = jjk;
        jjk2.getClass();
        this.d = jjk2;
        jjk3.getClass();
        this.e = jjk3;
        this.c = jjk4;
        jjk5.getClass();
        this.b = jjk5;
    }

    public cxp() {
        this.b = new AtomicReferenceArray(128);
        this.d = new jps((Object) null, jpt.a);
        this.e = new jpq(0, jpt.a);
        jpt jpt = jpt.a;
        this.c = new jpq(0, jpt);
        this.a = new jpq(0, jpt);
    }

    public cxp(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, byte[] bArr, byte[] bArr2) {
        jjk.getClass();
        this.c = jjk;
        jjk2.getClass();
        this.a = jjk2;
        jjk3.getClass();
        this.e = jjk3;
        jjk4.getClass();
        this.b = jjk4;
        jjk5.getClass();
        this.d = jjk5;
    }

    public cxp(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, byte[] bArr) {
        jjk.getClass();
        this.a = jjk;
        jjk2.getClass();
        this.d = jjk2;
        jjk3.getClass();
        this.e = jjk3;
        jjk4.getClass();
        this.b = jjk4;
        jjk5.getClass();
        this.c = jjk5;
    }
}
