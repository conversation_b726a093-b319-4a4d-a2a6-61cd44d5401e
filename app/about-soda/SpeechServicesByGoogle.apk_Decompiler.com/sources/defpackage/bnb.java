package defpackage;

import j$.util.function.BiConsumer$CC;
import java.util.function.BiConsumer;

/* renamed from: bnb  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bnb implements BiConsumer {
    public final /* synthetic */ bne a;

    public /* synthetic */ bnb(bne bne) {
        this.a = bne;
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r12v19, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v5, resolved type: bla} */
    /* JADX WARNING: type inference failed for: r0v29, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v31, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:47:0x0138 A[Catch:{ NameNotFoundException -> 0x0161 }] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void accept(java.lang.Object r12, java.lang.Object r13) {
        /*
            r11 = this;
            java.lang.String r12 = (java.lang.String) r12
            android.os.Bundle r13 = (android.os.Bundle) r13
            java.lang.String r0 = "automotive_wide_screen_clear_data"
            boolean r0 = r0.equals(r12)
            bne r1 = r11.a
            if (r0 == 0) goto L_0x0017
            android.widget.TextView r0 = r1.d
            if (r0 == 0) goto L_0x0017
            java.lang.String r2 = ""
            r0.setText(r2)
        L_0x0017:
            java.lang.String r0 = "automotive_wide_screen_post_load_search_results"
            boolean r0 = r0.equals(r12)
            r2 = 0
            if (r0 == 0) goto L_0x002d
            android.content.Context r0 = r1.b
            android.net.Uri r3 = com.android.car.ui.core.SearchResultsProvider.a(r0)
            android.content.ContentResolver r0 = r0.getContentResolver()
            r0.delete(r3, r2, r2)
        L_0x002d:
            java.lang.String r0 = "automotive_wide_screen_back_clicked"
            r0.equals(r12)
            if (r13 != 0) goto L_0x0036
            goto L_0x01a9
        L_0x0036:
            java.lang.String r12 = "search_result_item_id_list"
            java.lang.String r0 = r13.getString(r12)
            if (r0 == 0) goto L_0x005d
            java.lang.String r12 = r13.getString(r12)
            bmu r0 = r1.k
            java.lang.Object r0 = r0.c
            if (r0 != 0) goto L_0x0049
            goto L_0x0054
        L_0x0049:
            int r12 = java.lang.Integer.parseInt(r12)
            java.lang.Object r12 = r0.get(r12)
            r2 = r12
            bla r2 = (defpackage.bla) r2
        L_0x0054:
            if (r2 == 0) goto L_0x005d
            ekf r12 = r2.i
            if (r12 == 0) goto L_0x005d
            r12.b()
        L_0x005d:
            java.lang.String r12 = "search_result_supplemental_icon_id_list"
            java.lang.String r0 = r13.getString(r12)
            if (r0 == 0) goto L_0x0079
            java.lang.String r12 = r13.getString(r12)
            bmu r0 = r1.k
            java.lang.Object r0 = r0.c
            if (r0 == 0) goto L_0x0079
            int r12 = java.lang.Integer.parseInt(r12)
            java.lang.Object r12 = r0.get(r12)
            bla r12 = (defpackage.bla) r12
        L_0x0079:
            java.lang.String r12 = "content_area_surface_display_id"
            int r12 = r13.getInt(r12)
            java.lang.String r0 = "content_area_surface_height"
            int r0 = r13.getInt(r0)
            java.lang.String r2 = "content_area_surface_width"
            int r2 = r13.getInt(r2)
            java.lang.String r3 = "content_area_surface_host_token"
            android.os.IBinder r13 = r13.getBinder(r3)
            java.lang.String r3 = "Views in the widescreen ime aren't supported pre R"
            r4 = 30
            r5 = 0
            if (r13 == 0) goto L_0x0195
            int r6 = android.os.Build.VERSION.SDK_INT
            if (r6 < r4) goto L_0x018f
            android.content.Context r3 = r1.b
            java.lang.Class<android.hardware.display.DisplayManager> r4 = android.hardware.display.DisplayManager.class
            java.lang.Object r3 = r3.getSystemService(r4)
            android.hardware.display.DisplayManager r3 = (android.hardware.display.DisplayManager) r3
            android.view.Display r12 = r3.getDisplay(r12)
            android.content.Context r3 = r1.b
            android.view.SurfaceControlViewHost r4 = new android.view.SurfaceControlViewHost
            r4.<init>(r3, r12, r13)
            r1.e = r4
            r1.f = r0
            r1.g = r2
            android.os.Bundle r12 = new android.os.Bundle
            r12.<init>()
            bmu r13 = r1.k
            java.lang.Object r13 = r13.b
            if (r13 == 0) goto L_0x017a
            android.util.SparseArray r0 = defpackage.bnv.a
            boolean r0 = r13 instanceof android.graphics.drawable.BitmapDrawable
            r2 = 1
            if (r0 == 0) goto L_0x00d7
            r0 = r13
            android.graphics.drawable.BitmapDrawable r0 = (android.graphics.drawable.BitmapDrawable) r0
            android.graphics.Bitmap r3 = r0.getBitmap()
            if (r3 == 0) goto L_0x00d7
            android.graphics.Bitmap r13 = r0.getBitmap()
            goto L_0x010f
        L_0x00d7:
            android.graphics.drawable.Drawable r13 = (android.graphics.drawable.Drawable) r13
            int r0 = r13.getIntrinsicWidth()
            if (r0 <= 0) goto L_0x00f5
            int r0 = r13.getIntrinsicHeight()
            if (r0 > 0) goto L_0x00e6
            goto L_0x00f5
        L_0x00e6:
            int r0 = r13.getIntrinsicWidth()
            int r3 = r13.getIntrinsicHeight()
            android.graphics.Bitmap$Config r4 = android.graphics.Bitmap.Config.ARGB_8888
            android.graphics.Bitmap r0 = android.graphics.Bitmap.createBitmap(r0, r3, r4)
            goto L_0x00fb
        L_0x00f5:
            android.graphics.Bitmap$Config r0 = android.graphics.Bitmap.Config.ARGB_8888
            android.graphics.Bitmap r0 = android.graphics.Bitmap.createBitmap(r2, r2, r0)
        L_0x00fb:
            android.graphics.Canvas r3 = new android.graphics.Canvas
            r3.<init>(r0)
            int r4 = r3.getWidth()
            int r6 = r3.getHeight()
            r13.setBounds(r5, r5, r4, r6)
            r13.draw(r3)
            r13 = r0
        L_0x010f:
            android.content.Context r0 = r1.b
            android.content.res.Resources r3 = r0.getResources()
            r4 = 2131165379(0x7f0700c3, float:1.7944973E38)
            java.lang.Class<android.view.inputmethod.InputMethodManager> r6 = android.view.inputmethod.InputMethodManager.class
            java.lang.Object r6 = r0.getSystemService(r6)     // Catch:{ NameNotFoundException -> 0x0161 }
            android.view.inputmethod.InputMethodManager r6 = (android.view.inputmethod.InputMethodManager) r6     // Catch:{ NameNotFoundException -> 0x0161 }
            android.content.ContentResolver r7 = r0.getContentResolver()     // Catch:{ NameNotFoundException -> 0x0161 }
            java.lang.String r8 = "default_input_method"
            java.lang.String r7 = android.provider.Settings.Secure.getString(r7, r8)     // Catch:{ NameNotFoundException -> 0x0161 }
            java.util.List r6 = r6.getInputMethodList()     // Catch:{ NameNotFoundException -> 0x0161 }
            java.util.Iterator r6 = r6.iterator()     // Catch:{ NameNotFoundException -> 0x0161 }
        L_0x0132:
            boolean r8 = r6.hasNext()     // Catch:{ NameNotFoundException -> 0x0161 }
            if (r8 == 0) goto L_0x0169
            java.lang.Object r8 = r6.next()     // Catch:{ NameNotFoundException -> 0x0161 }
            android.view.inputmethod.InputMethodInfo r8 = (android.view.inputmethod.InputMethodInfo) r8     // Catch:{ NameNotFoundException -> 0x0161 }
            java.lang.String r9 = r8.getId()     // Catch:{ NameNotFoundException -> 0x0161 }
            boolean r9 = r9.equals(r7)     // Catch:{ NameNotFoundException -> 0x0161 }
            if (r9 == 0) goto L_0x0132
            java.lang.String r9 = r8.getPackageName()     // Catch:{ NameNotFoundException -> 0x0161 }
            android.content.Context r9 = r0.createPackageContext(r9, r5)     // Catch:{ NameNotFoundException -> 0x0161 }
            android.content.res.Resources r3 = r9.getResources()     // Catch:{ NameNotFoundException -> 0x0161 }
            java.lang.String r9 = "car_ui_primary_icon_size"
            java.lang.String r10 = "dimen"
            java.lang.String r8 = r8.getPackageName()     // Catch:{ NameNotFoundException -> 0x0161 }
            int r4 = r3.getIdentifier(r9, r10, r8)     // Catch:{ NameNotFoundException -> 0x0161 }
            goto L_0x0132
        L_0x0161:
            r0 = move-exception
            java.lang.String r5 = "car-ui-lib"
            java.lang.String r6 = "Unable to read `R.dimen.car_ui_primary_icon_size` from the IME service. Please make sure the IME service is exported via `android.view.InputMethod` intent-filter."
            android.util.Log.w(r5, r6, r0)
        L_0x0169:
            int r0 = r3.getDimensionPixelSize(r4)
            android.graphics.Bitmap r13 = android.graphics.Bitmap.createScaledBitmap(r13, r0, r0, r2)
            byte[] r13 = defpackage.bnv.q(r13)
            java.lang.String r0 = "extracted_text_icon"
            r12.putByteArray(r0, r13)
        L_0x017a:
            android.view.SurfaceControlViewHost r13 = r1.e
            android.view.SurfaceControlViewHost$SurfacePackage r13 = defpackage.sk$$ExternalSyntheticApiModelOutline1.m((android.view.SurfaceControlViewHost) r13)
            java.lang.String r0 = "content_area_surface_package"
            r12.putParcelable(r0, r13)
            android.view.inputmethod.InputMethodManager r13 = r1.c
            android.widget.TextView r0 = r1.d
            java.lang.String r1 = "automotive_wide_screen"
            r13.sendAppPrivateCommand(r0, r1, r12)
            return
        L_0x018f:
            java.lang.IllegalStateException r12 = new java.lang.IllegalStateException
            r12.<init>(r3)
            throw r12
        L_0x0195:
            if (r0 != 0) goto L_0x019a
            if (r2 == 0) goto L_0x01a9
            r0 = r5
        L_0x019a:
            int r12 = android.os.Build.VERSION.SDK_INT
            if (r12 < r4) goto L_0x01aa
            r1.f = r0
            r1.g = r2
            android.view.SurfaceControlViewHost r12 = r1.e
            if (r12 == 0) goto L_0x01a9
            r12.relayout(r2, r0)
        L_0x01a9:
            return
        L_0x01aa:
            java.lang.IllegalStateException r12 = new java.lang.IllegalStateException
            r12.<init>(r3)
            throw r12
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bnb.accept(java.lang.Object, java.lang.Object):void");
    }

    public final /* synthetic */ BiConsumer andThen(BiConsumer biConsumer) {
        return BiConsumer$CC.$default$andThen(this, biConsumer);
    }
}
