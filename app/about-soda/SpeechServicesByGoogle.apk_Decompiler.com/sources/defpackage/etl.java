package defpackage;

import android.app.AppOpsManager;
import android.os.Build;
import android.os.Process;

/* renamed from: etl  reason: default package */
/* compiled from: PG */
public final class etl implements hls {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;
    final /* synthetic */ Object c;
    public final /* synthetic */ Object d;
    private final /* synthetic */ int e;

    public etl(emt emt, int i, String str, hme hme, int i2) {
        this.e = i2;
        this.a = i;
        this.b = str;
        this.c = hme;
        this.d = emt;
    }

    /* JADX WARNING: type inference failed for: r2v10, types: [jix, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v11, types: [jix, java.lang.Object] */
    public final void a(Throwable th) {
        if (this.e != 0) {
            ((hby) ((hby) ((hby) emt.a.h()).i(th)).j("com/google/android/libraries/search/audio/microphone/accountability/RecordAudioOpChecker$1", "onFailure", 157, "RecordAudioOpChecker.java")).r("#audio# Failed getting startListening future");
            return;
        }
        ((hby) ((hby) etm.a.h().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl$3", "onFailure", 335, "AudioServiceImpl.java")).s("#audio# Failed to get HotwordStopListeningStatus. sessionToken: %d", this.a);
        htk l = eta.d.l();
        ebp i = eki.i(eag.FAILED_CLOSING_ERROR_IN_GETTING_AUDIO_SOURCE_CLOSING_STATUS, eam.CLIENT_REQUESTED);
        if (!l.b.B()) {
            l.u();
        }
        eta eta = (eta) l.b;
        i.getClass();
        eta.b = i;
        eta.a |= 1;
        ebr ebr = ebr.c;
        if (!l.b.B()) {
            l.u();
        }
        ? r2 = this.b;
        eta eta2 = (eta) l.b;
        ebr.getClass();
        eta2.c = ebr;
        eta2.a |= 2;
        r2.c((eta) l.r());
        this.b.b(th);
    }

    /* JADX WARNING: type inference failed for: r12v10, types: [hme, java.lang.Object] */
    public final /* synthetic */ void b(Object obj) {
        if (this.e != 0) {
            dyw dyw = (dyw) obj;
            ((hby) ((hby) emt.a.c()).j("com/google/android/libraries/search/audio/microphone/accountability/RecordAudioOpChecker$1", "onSuccess", 129, "RecordAudioOpChecker.java")).x("#audio# startOp, session=%d, tag=%s", this.a, this.b);
            if (Build.VERSION.SDK_INT >= 30) {
                emt emt = (emt) this.d;
                AppOpsManager appOpsManager = (AppOpsManager) emt.b.getSystemService("appops");
                if (appOpsManager == null) {
                    ((hby) ((hby) emt.a.h()).j("com/google/android/libraries/search/audio/microphone/accountability/RecordAudioOpChecker", "startOp", 80, "RecordAudioOpChecker.java")).r("#audio# unable to get access to app ops manager for startOp");
                } else {
                    Object obj2 = this.b;
                    int unused = appOpsManager.startOp("android:record_audio", Process.myUid(), emt.b.getPackageName(), (String) obj2, (String) null);
                }
            }
            hfc.T(this.c, gof.g(new cmk(this, 6)), ((emt) this.d).c);
            return;
        }
        hfc.T(hfc.K(((ekf) this.d).a), gof.g(new ema(this.b, (Object) (ebp) obj, 6, (byte[]) null)), ((etm) this.c).b);
    }

    public etl(etm etm, ekf ekf, jix jix, int i, int i2) {
        this.e = i2;
        this.d = ekf;
        this.b = jix;
        this.a = i;
        this.c = etm;
    }
}
