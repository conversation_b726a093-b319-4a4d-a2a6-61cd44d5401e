package defpackage;

import android.os.Bundle;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/* renamed from: bqr  reason: default package */
/* compiled from: PG */
public final class bqr {
    public Object a;
    public final Object b;
    public final Object c;
    public final Object d;

    public bqr(brc brc, bqy bqy, bqx bqx) {
        this.b = brc;
        this.c = bqy;
        this.d = bqx;
    }

    public final bqz a() {
        hzz.s(this.a, bc.class);
        return new bqz((brc) this.b, (bqx) this.d, (bc) this.a);
    }

    public final Bundle b(String str, Bundle bundle) {
        if (bundle != null) {
            return (Bundle) ((HashMap) this.b).put(str, bundle);
        }
        return (Bundle) ((HashMap) this.b).remove(str);
    }

    public final bc c(String str) {
        cb cbVar = (cb) ((HashMap) this.c).get(str);
        if (cbVar != null) {
            return cbVar.a;
        }
        return null;
    }

    public final bc d(String str) {
        for (cb cbVar : ((HashMap) this.c).values()) {
            if (cbVar != null) {
                bc bcVar = cbVar.a;
                if (!str.equals(bcVar.k)) {
                    bcVar = bcVar.C.x.d(str);
                }
                if (bcVar != null) {
                    return bcVar;
                }
            }
        }
        return null;
    }

    public final cb e(String str) {
        return (cb) ((HashMap) this.c).get(str);
    }

    public final List f() {
        ArrayList arrayList = new ArrayList();
        for (cb cbVar : ((HashMap) this.c).values()) {
            if (cbVar != null) {
                arrayList.add(cbVar);
            }
        }
        return arrayList;
    }

    public final List g() {
        ArrayList arrayList = new ArrayList();
        for (cb cbVar : ((HashMap) this.c).values()) {
            if (cbVar != null) {
                arrayList.add(cbVar.a);
            } else {
                arrayList.add((Object) null);
            }
        }
        return arrayList;
    }

    /* JADX WARNING: type inference failed for: r2v0, types: [java.util.Collection, java.lang.Object] */
    public final List h() {
        ArrayList arrayList;
        if (((ArrayList) this.d).isEmpty()) {
            return Collections.emptyList();
        }
        synchronized (this.d) {
            arrayList = new ArrayList(this.d);
        }
        return arrayList;
    }

    public final void i(bc bcVar) {
        if (!((ArrayList) this.d).contains(bcVar)) {
            synchronized (this.d) {
                ((ArrayList) this.d).add(bcVar);
            }
            bcVar.q = true;
            return;
        }
        Objects.toString(bcVar);
        throw new IllegalStateException("Fragment already added: ".concat(String.valueOf(bcVar)));
    }

    public final void j() {
        ((HashMap) this.c).values().removeAll(Collections.singleton((Object) null));
    }

    public final void k(cb cbVar) {
        bc bcVar = cbVar.a;
        if (!n(bcVar.k)) {
            ((HashMap) this.c).put(bcVar.k, cbVar);
            if (bcVar.K) {
                if (bcVar.f13J) {
                    ((by) this.a).a(bcVar);
                } else {
                    ((by) this.a).e(bcVar);
                }
                bcVar.K = false;
            }
            if (bw.V(2)) {
                Objects.toString(bcVar);
            }
        }
    }

    public final void l(cb cbVar) {
        bc bcVar = cbVar.a;
        if (bcVar.f13J) {
            ((by) this.a).e(bcVar);
        }
        if (((HashMap) this.c).get(bcVar.k) == cbVar) {
            if (((cb) ((HashMap) this.c).put(bcVar.k, (Object) null)) != null && bw.V(2)) {
                Objects.toString(bcVar);
            }
        }
    }

    public final void m(bc bcVar) {
        synchronized (this.d) {
            ((ArrayList) this.d).remove(bcVar);
        }
        bcVar.q = false;
    }

    public final boolean n(String str) {
        if (((HashMap) this.c).get(str) != null) {
            return true;
        }
        return false;
    }

    public bqr() {
        this.d = new ArrayList();
        this.c = new HashMap();
        this.b = new HashMap();
    }
}
