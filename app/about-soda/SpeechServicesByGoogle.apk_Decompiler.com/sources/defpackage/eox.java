package defpackage;

/* renamed from: eox  reason: default package */
/* compiled from: PG */
public final class eox implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;

    public eox(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4) {
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
    }

    /* renamed from: a */
    public final cyw b() {
        return new cyw(((emu) this.a).b(), (epv) this.b.b(), ((ems) this.c).b(), ((iim) this.d).a());
    }
}
