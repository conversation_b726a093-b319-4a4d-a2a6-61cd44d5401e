package defpackage;

import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Build;
import android.util.Log;
import androidx.wear.ambient.AmbientMode;
import androidx.wear.ambient.AmbientModeSupport;
import j$.util.Objects;
import java.io.IOException;
import java.util.Date;
import java.util.Map;
import java.util.Set;

/* renamed from: bng  reason: default package */
/* compiled from: PG */
public final class bng {
    public final boolean a;
    public final Object b;
    public final Object c;
    public final Object d;

    public bng(bnf bnf) {
        this.b = null;
        this.c = null;
        this.d = bnf.a;
        this.a = bnf.b;
    }

    /* JADX WARNING: type inference failed for: r0v2, types: [dvj, java.lang.Object] */
    public final Object a(String str, String str2) {
        try {
            return this.b.a(str2);
        } catch (IOException | IllegalArgumentException e) {
            Log.e("PhenotypeCombinedFlags", "Invalid Phenotype flag value for flag ".concat(str), e);
            return null;
        }
    }

    /* JADX WARNING: type inference failed for: r1v0, types: [java.util.Set, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v2, types: [java.lang.Object, java.util.concurrent.ConcurrentMap] */
    /* JADX WARNING: type inference failed for: r2v1, types: [java.lang.Object, java.util.concurrent.ConcurrentMap] */
    public final duu b(dte dte, String str) {
        boolean z = this.a;
        ? r1 = this.d;
        dlv dlv = duu.g;
        dut dut = new dut(dte, str, z, r1);
        gri gri = new gri(str, "");
        Object obj = (duu) dlv.a.get(gri);
        if (obj == null) {
            obj = dut.a();
            Object obj2 = (duu) dlv.a.putIfAbsent(gri, obj);
            if (obj2 == null) {
                Context context = dte.c;
                dve.c.putIfAbsent(gri, new AmbientMode.AmbientController(obj, (byte[]) null));
                if (!dve.b) {
                    synchronized (dve.a) {
                        if (!dve.b && !Objects.equals(context.getPackageName(), "com.google.android.gms")) {
                            if (Build.VERSION.SDK_INT >= 33) {
                                Intent unused = context.registerReceiver(new dve(), new IntentFilter("com.google.android.gms.phenotype.UPDATE"), 2);
                            } else {
                                context.registerReceiver(new dve(), new IntentFilter("com.google.android.gms.phenotype.UPDATE"));
                            }
                            dve.b = true;
                        }
                    }
                }
                Objects.requireNonNull(obj);
                duy.a.putIfAbsent(gri, new dtc(obj, 6));
            } else {
                obj = obj2;
            }
        }
        duu duu = (duu) obj;
        boolean z2 = duu.d;
        fvf.aA(true, "Package %s cannot be registered both with and without stickyAccountSupport", str);
        return duu;
    }

    /* JADX WARNING: type inference failed for: r10v1, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme c(Set set, long j, Map map) {
        if (this.a) {
            return hfc.K(haq.a);
        }
        return hke.f(((giw) this.b).b(), gof.b(new gji(this, map, set, j)), this.d);
    }

    public bng(String str, byte[][] bArr, boolean z, Date date) {
        this.b = str;
        this.d = bArr;
        this.a = z;
        this.c = date;
    }

    public bng(boolean z, Set set, dvj dvj, dvj dvj2) {
        this.a = z;
        this.d = set;
        this.b = dvj;
        this.c = dvj2;
    }

    public bng(giw giw, AmbientModeSupport.AmbientController ambientController, hmh hmh, grh grh) {
        this.b = giw;
        this.c = ambientController;
        this.d = hmh;
        this.a = ((Boolean) grh.d(false)).booleanValue();
    }
}
