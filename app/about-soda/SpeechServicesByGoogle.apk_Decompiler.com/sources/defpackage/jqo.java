package defpackage;

/* renamed from: jqo  reason: default package */
/* compiled from: PG */
public final class jqo {
    public static final jlv a(jlv jlv, jlv jlv2) {
        if (!e(jlv2)) {
            return jlv.plus(jlv2);
        }
        return d(jlv, jlv2, false);
    }

    public static final jlv b(jqs jqs, jlv jlv) {
        jlv jlv2;
        jlv d = d(jqs.bB(), jlv, true);
        if (jqv.a) {
            jlv2 = d.plus(new jqq(jqv.c.incrementAndGet()));
        } else {
            jlv2 = d;
        }
        if (d == jrf.a || d.get(jls.b) != null) {
            return jlv2;
        }
        return jlv2.plus(jrf.a);
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v4, resolved type: jmf} */
    /* JADX WARNING: type inference failed for: r2v1, types: [jmf] */
    /* JADX WARNING: type inference failed for: r2v3 */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static final defpackage.jsy c(defpackage.jlr r2, defpackage.jlv r3, java.lang.Object r4) {
        /*
            boolean r0 = r2 instanceof defpackage.jmf
            r1 = 0
            if (r0 != 0) goto L_0x0006
            return r1
        L_0x0006:
            jsz r0 = defpackage.jsz.a
            jlt r0 = r3.get(r0)
            if (r0 == 0) goto L_0x0026
        L_0x000e:
            boolean r0 = r2 instanceof defpackage.jrc
            if (r0 == 0) goto L_0x0013
            goto L_0x0021
        L_0x0013:
            jmf r2 = r2.by()
            if (r2 != 0) goto L_0x001a
            goto L_0x0021
        L_0x001a:
            boolean r0 = r2 instanceof defpackage.jsy
            if (r0 == 0) goto L_0x000e
            r1 = r2
            jsy r1 = (defpackage.jsy) r1
        L_0x0021:
            if (r1 == 0) goto L_0x0026
            r1.R(r3, r4)
        L_0x0026:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.jqo.c(jlr, jlv, java.lang.Object):jsy");
    }

    private static final jlv d(jlv jlv, jlv jlv2, boolean z) {
        boolean e = e(jlv);
        boolean e2 = e(jlv2);
        if (!e && !e2) {
            return jlv.plus(jlv2);
        }
        jnz jnz = new jnz();
        jnz.a = jlv2;
        jlv jlv3 = (jlv) jlv.fold(jlw.a, new jqn(jnz, z));
        if (e2) {
            jnz.a = ((jlv) jnz.a).fold(jlw.a, gfn.e);
        }
        return jlv3.plus((jlv) jnz.a);
    }

    private static final boolean e(jlv jlv) {
        return ((Boolean) jlv.fold(false, gfn.f)).booleanValue();
    }
}
