package defpackage;

import android.util.Log;
import java.util.Locale;
import java.util.Random;

/* renamed from: cyh  reason: default package */
/* compiled from: PG */
public final class cyh {
    private static final Random a = new Random();

    public static String a(String str, Object... objArr) {
        return String.format(Locale.US, str, objArr);
    }

    public static boolean b(long j) {
        int i = (j > 0 ? 1 : (j == 0 ? 0 : -1));
        if (i <= 0) {
            if (i < 0) {
                g("Bad sample interval: %d", Long.valueOf(j));
            }
            return false;
        } else if (a.nextLong() % j == 0) {
            return true;
        } else {
            return false;
        }
    }

    public static void c(String str, Object obj) {
        if (Log.isLoggable("MDD", 3)) {
            a(str, obj);
        }
    }

    public static void d(String str, Object obj, Object obj2) {
        if (Log.isLoggable("MDD", 3)) {
            a(str, obj, obj2);
        }
    }

    public static void e(String str, Object... objArr) {
        if (Log.isLoggable("MDD", 3)) {
            a(str, objArr);
        }
    }

    public static void f(String str) {
        if (Log.isLoggable("MDD", 6)) {
            Log.e("MDD", str);
        }
    }

    public static void g(String str, Object obj) {
        if (Log.isLoggable("MDD", 6)) {
            Log.e("MDD", a(str, obj));
        }
    }

    public static void h(String str, Object obj, Object obj2) {
        if (Log.isLoggable("MDD", 6)) {
            Log.e("MDD", a(str, obj, obj2));
        }
    }

    public static void i(String str, Object... objArr) {
        if (Log.isLoggable("MDD", 6)) {
            Log.e("MDD", a(str, objArr));
        }
    }

    public static void j(Throwable th, String str, Object... objArr) {
        if (Log.isLoggable("MDD", 6)) {
            q(th, a(str, objArr));
        }
    }

    public static void k(String str, Object obj, Object obj2) {
        if (Log.isLoggable("MDD", 2)) {
            a(str, obj, obj2);
        }
    }

    public static void l(String str, Object... objArr) {
        if (Log.isLoggable("MDD", 2)) {
            a(str, objArr);
        }
    }

    public static void m(String str, Object obj) {
        if (Log.isLoggable("MDD", 5)) {
            Log.w("MDD", a(str, obj));
        }
    }

    public static void n(String str, Object obj, Object obj2) {
        if (Log.isLoggable("MDD", 5)) {
            Log.w("MDD", a(str, obj, obj2));
        }
    }

    public static void o(Throwable th, String str, Object... objArr) {
        if (!Log.isLoggable("MDD", 5)) {
            return;
        }
        if (Log.isLoggable("MDD", 3)) {
            Log.w("MDD", a(str, objArr), th);
            return;
        }
        String a2 = a(str, objArr);
        String valueOf = String.valueOf(th);
        Log.w("MDD", a2 + ": " + valueOf);
    }

    public static void p(String str, Object... objArr) {
        if (Log.isLoggable("MDD", 3)) {
            a(str, objArr);
        }
    }

    public static void q(Throwable th, String str) {
        if (!Log.isLoggable("MDD", 6)) {
            return;
        }
        if (Log.isLoggable("MDD", 3)) {
            Log.e("MDD", str, th);
            return;
        }
        String valueOf = String.valueOf(th);
        Log.e("MDD", str + ": " + valueOf);
    }
}
