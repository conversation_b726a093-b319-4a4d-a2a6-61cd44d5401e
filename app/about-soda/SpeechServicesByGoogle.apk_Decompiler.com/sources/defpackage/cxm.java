package defpackage;

import android.content.Context;
import java.util.concurrent.Executor;

/* renamed from: cxm  reason: default package */
/* compiled from: PG */
public final class cxm {
    public final grh a;
    public final grh b;
    public final Object c;
    public final Object d;
    public final Object e;
    public final Object f;
    public final Object g;
    public final Object h;
    public final Object i;
    public final Object j;
    public final Object k;

    public cxm(kjd kjd, czs czs, cyk cyk, grh grh, grh grh2, grh grh3, grh grh4, grh grh5, cqh cqh, grh grh6, grh grh7) {
        this.c = kjd;
        this.d = czs;
        this.j = cyk;
        this.a = grh;
        this.e = grh2;
        this.f = grh3;
        this.g = grh4;
        this.b = grh5;
        this.k = cqh;
        this.h = grh6;
        this.i = grh7;
    }

    public final long a(String str) {
        if (((djv) this.k).a) {
            return -1;
        }
        dru dru = (dru) this.d;
        if (dru.c.c()) {
            return -1;
        }
        boolean z = dru.b;
        dry dry = dru.a;
        if (z) {
            return dry.a(str);
        }
        return -1;
    }

    /* JADX WARNING: type inference failed for: r2v1, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme b(dma dma) {
        if (((djv) this.k).a) {
            return hfc.I();
        }
        return hfc.P(new dmc(this, dma), this.h);
    }

    public final boolean c(String str) {
        if (a(str) != -1) {
            return true;
        }
        return false;
    }

    public final boolean d() {
        dru dru = (dru) this.d;
        boolean z = dru.b;
        dry dry = dru.a;
        if (!z || !dry.c()) {
            return false;
        }
        return true;
    }

    /* JADX WARNING: type inference failed for: r3v3, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r3v5, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r4v1, types: [java.lang.Object, jjk] */
    public cxm(dmb dmb, jjk jjk, djv djv, cxp cxp, jjk jjk2, grh grh, grh grh2, jjk jjk3, grh grh3, jjk jjk4, Executor executor, ihn ihn, jjk jjk5) {
        cxp cxp2 = cxp;
        this.j = dmb;
        this.k = djv;
        this.g = jjk;
        this.h = executor;
        this.e = new dab(jjk2, 2);
        Context a2 = ((iim) cxp2.a).a();
        Executor executor2 = (Executor) cxp2.d.b();
        executor2.getClass();
        Object b2 = cxp2.e.b();
        ((Boolean) cxp2.c.b()).booleanValue();
        grh grh4 = (grh) ((iiv) cxp2.b).a;
        grh4.getClass();
        this.d = new dru(a2, executor2, (drx) b2, ihn, true, grh4, jjk5);
        this.a = grh;
        this.b = grh2;
        this.f = jjk3;
        this.c = grh3;
        this.i = jjk4;
    }
}
