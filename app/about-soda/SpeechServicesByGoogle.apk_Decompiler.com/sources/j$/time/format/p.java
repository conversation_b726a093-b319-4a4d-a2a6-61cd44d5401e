package j$.time.format;

import j$.time.d;
import j$.time.temporal.TemporalUnit;
import j$.time.temporal.v;
import j$.util.Objects;
import java.util.Calendar;
import java.util.Locale;

final class p extends j {
    private char g;
    private int h;

    p(char c, int i, int i2, int i3, int i4) {
        super((j$.time.temporal.p) null, i2, i3, A.NOT_NEGATIVE, i4);
        this.g = c;
        this.h = i;
    }

    /* access modifiers changed from: package-private */
    public final j c() {
        if (this.e == -1) {
            return this;
        }
        return new p(this.g, this.h, this.b, this.c, -1);
    }

    /* access modifiers changed from: package-private */
    public final j d(int i) {
        int i2 = this.b;
        int i3 = this.c;
        return new p(this.g, this.h, i2, i3, this.e + i);
    }

    public final boolean g(u uVar, StringBuilder sb) {
        j jVar;
        j$.time.temporal.p h2;
        A a;
        Locale c = uVar.c();
        TemporalUnit temporalUnit = v.h;
        Objects.requireNonNull(c, "locale");
        Calendar instance = Calendar.getInstance(new Locale(c.getLanguage(), c.getCountry()));
        v f = v.f(d.SUNDAY.J((long) (instance.getFirstDayOfWeek() - 1)), instance.getMinimalDaysInFirstWeek());
        char c2 = this.g;
        if (c2 == 'W') {
            h2 = f.h();
        } else if (c2 == 'Y') {
            j$.time.temporal.p g2 = f.g();
            int i = this.h;
            if (i == 2) {
                jVar = new m(g2, m.h, this.e);
            } else {
                if (i < 4) {
                    a = A.NORMAL;
                } else {
                    a = A.EXCEEDS_PAD;
                }
                jVar = new j(g2, i, 19, a, this.e);
            }
            return jVar.g(uVar, sb);
        } else if (c2 == 'c' || c2 == 'e') {
            h2 = f.c();
        } else if (c2 == 'w') {
            h2 = f.i();
        } else {
            throw new IllegalStateException("unreachable");
        }
        j$.time.temporal.p pVar = h2;
        A a2 = A.NOT_NEGATIVE;
        jVar = new j(pVar, this.b, this.c, a2, this.e);
        return jVar.g(uVar, sb);
    }

    public final String toString() {
        A a;
        StringBuilder sb = new StringBuilder(30);
        sb.append("Localized(");
        int i = this.h;
        char c = this.g;
        if (c != 'Y') {
            if (c == 'W') {
                sb.append("WeekOfMonth");
            } else if (c == 'c' || c == 'e') {
                sb.append("DayOfWeek");
            } else if (c == 'w') {
                sb.append("WeekOfWeekBasedYear");
            }
            sb.append(",");
            sb.append(i);
        } else if (i == 1) {
            sb.append("WeekBasedYear");
        } else if (i == 2) {
            sb.append("ReducedValue(WeekBasedYear,2,2,2000-01-01)");
        } else {
            sb.append("WeekBasedYear,");
            sb.append(i);
            sb.append(",19,");
            if (i < 4) {
                a = A.NORMAL;
            } else {
                a = A.EXCEEDS_PAD;
            }
            sb.append(a);
        }
        sb.append(")");
        return sb.toString();
    }
}
