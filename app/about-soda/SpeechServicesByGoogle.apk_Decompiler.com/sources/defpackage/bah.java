package defpackage;

import android.animation.ObjectAnimator;
import android.animation.TimeInterpolator;
import android.graphics.Canvas;
import android.graphics.ColorFilter;
import android.graphics.Paint;
import android.graphics.RectF;
import android.graphics.drawable.Drawable;
import android.util.Property;
import android.view.animation.LinearInterpolator;

/* renamed from: bah  reason: default package */
/* compiled from: PG */
public final class bah extends Drawable {
    private static final Property b = new bag(Integer.class);
    private static final TimeInterpolator c = bab.a;
    public final ObjectAnimator a;
    private final RectF d = new RectF();
    private final Paint e;

    public bah() {
        Paint paint = new Paint();
        this.e = paint;
        paint.setAntiAlias(true);
        paint.setStyle(Paint.Style.STROKE);
        ObjectAnimator ofInt = ObjectAnimator.ofInt(this, b, new int[]{0, 10000});
        this.a = ofInt;
        ofInt.setRepeatCount(-1);
        ofInt.setRepeatMode(1);
        ofInt.setDuration(6000);
        ofInt.setInterpolator(new LinearInterpolator());
    }

    private static float a(float f, float f2, float f3) {
        if (f != f2) {
            return (f3 - f) / (f2 - f);
        }
        return 0.0f;
    }

    public final void draw(Canvas canvas) {
        float f;
        canvas.save();
        this.d.set(getBounds());
        float f2 = 0.0f;
        this.d.inset(0.0f, 0.0f);
        this.e.setStrokeWidth(0.0f);
        boolean z = false;
        this.e.setColor(0);
        int level = getLevel();
        float f3 = ((float) (level - ((level / 2000) * 2000))) / 2000.0f;
        if (f3 < 0.5f) {
            z = true;
        }
        if (z) {
            f = c.getInterpolation(a(0.0f, 0.5f, f3));
        } else {
            f = 1.0f - c.getInterpolation(a(0.5f, 1.0f, f3));
        }
        float max = Math.max(1.0f, f * 306.0f);
        RectF rectF = this.d;
        float f4 = ((float) level) * 1.0E-4f;
        canvas.rotate((((f4 + f4) * 360.0f) - 0.049804688f) + (f3 * 54.0f), rectF.centerX(), rectF.centerY());
        RectF rectF2 = this.d;
        if (!z) {
            f2 = 306.0f - max;
        }
        canvas.drawArc(rectF2, f2, max, false, this.e);
        canvas.restore();
    }

    public final int getOpacity() {
        return -1;
    }

    /* access modifiers changed from: protected */
    public final boolean onLevelChange(int i) {
        return true;
    }

    public final void setAlpha(int i) {
    }

    public final void setColorFilter(ColorFilter colorFilter) {
    }
}
