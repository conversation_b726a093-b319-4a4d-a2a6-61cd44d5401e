package defpackage;

/* renamed from: epb  reason: default package */
/* compiled from: PG */
public final class epb {
    public final int a;
    public final ehg b;

    public epb(int i, ehg ehg) {
        jnu.e(ehg, "clientInfo");
        this.a = i;
        this.b = ehg;
    }

    public final String a() {
        String s = fbi.s(this.b);
        return "client(token(" + this.a + "), " + s + ")";
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof epb)) {
            return false;
        }
        epb epb = (epb) obj;
        if (this.a == epb.a && jnu.i(this.b, epb.b)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        ehg ehg = this.b;
        if (ehg.B()) {
            i = ehg.i();
        } else {
            int i2 = ehg.memoizedHashCode;
            if (i2 == 0) {
                i2 = ehg.i();
                ehg.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (this.a * 31) + i;
    }

    public final String toString() {
        return "AudioSourceClientData(clientToken=" + this.a + ", clientInfo=" + this.b + ")";
    }
}
