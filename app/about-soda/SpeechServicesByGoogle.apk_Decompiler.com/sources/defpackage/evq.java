package defpackage;

/* renamed from: evq  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class evq implements gsb {
    public final /* synthetic */ bmu a;
    private final /* synthetic */ int b;

    public /* synthetic */ evq(bmu bmu, int i) {
        this.b = i;
        this.a = bmu;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                fqy g = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/action_upload_requests", new fqx("status", String.class));
                g.c();
                return g;
            case 1:
                fqy g2 = ((frc) this.a.a).g("/client_streamz/android_gsa/recognition_event_for_s3_with_hotword_validation_result", new fqx("top_hypothesis_contains_hotword", String.class), new fqx("recognition_event_type", String.class), new fqx("is_cloud_handover_for_soda_asr_hotword_validation_enabled", Boolean.class), new fqx("is_pixel_device", Boolean.class), new fqx("is_hotword_triggered", String.class), new fqx("is_locale_supported", String.class), new fqx("soda_event_type", String.class));
                g2.c();
                return g2;
            case 2:
                fqy g3 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/actions/actions_per_nau_request_counter", new fqx("action_count", Integer.class), new fqx("upload_source", String.class));
                g3.c();
                return g3;
            case 3:
                fqy g4 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/actions/failed_nau_counter", new fqx("http_response_code", String.class), new fqx("google_response_code", String.class), new fqx("upload_source", String.class));
                g4.c();
                return g4;
            case 4:
                fqy g5 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/surfaces/dictation/instant_voice_replies/eligible_quick_phrase_notification_count", new fqx[0]);
                g5.c();
                return g5;
            case 5:
                fqy g6 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/actions/missing_action_duration_counter", new fqx("notification_category", String.class), new fqx("action_type", String.class));
                g6.c();
                return g6;
            case 6:
                fqy g7 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/actions/suppression_counter", new fqx("notification_category", String.class), new fqx("suppression_store_type", String.class));
                g7.c();
                return g7;
            case 7:
                fqv c = ((frc) this.a.a).c("/client_streamz/android_gsa/search_notifications/actions/suppression_store_num_entries", new fqx[0]);
                c.c();
                return c;
            case 8:
                fqy g8 = ((frc) this.a.a).g("/client_streamz/android_gsa/amp/stamp_viewer_recommendations_load_failure", new fqx[0]);
                g8.c();
                return g8;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fqy g9 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/chime_registration_failure", new fqx[0]);
                g9.c();
                return g9;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fqy g10 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/chime_registration_payload_diff", new fqx("error", String.class));
                g10.c();
                return g10;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                fqy g11 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/chime_registration_success", new fqx[0]);
                g11.c();
                return g11;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                fqy g12 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/chime_registration_trigger", new fqx[0]);
                g12.c();
                return g12;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                fqy g13 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/chime_registration_worker_enqueue_failure", new fqx[0]);
                g13.c();
                return g13;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                fqy g14 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/device_notification_settings_change", new fqx("level", String.class), new fqx("name", String.class), new fqx("status", String.class));
                g14.c();
                return g14;
            case 15:
                fqy g15 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/surfaces/languagepack/download/request_count", new fqx("surface", String.class), new fqx("locale", String.class), new fqx("type", String.class), new fqx("version", Integer.class));
                g15.c();
                return g15;
            case 16:
                fqy g16 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/inbox/no_chime_rtid", new fqx("has_tiktok_user_id", Boolean.class));
                g16.c();
                return g16;
            case 17:
                fqy g17 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/legacy_now_sync_task", new fqx("reason", String.class));
                g17.c();
                return g17;
            case 18:
                fqy g18 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/location_reporting_requests", new fqx("location_availability", String.class), new fqx("status", String.class));
                g18.c();
                return g18;
            case 19:
                fqy g19 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/location_updates", new fqx("status", String.class));
                g19.c();
                return g19;
            default:
                fqy g20 = ((frc) this.a.a).g("/client_streamz/android_gsa/amp/stamp_viewer_report_abuse_failure", new fqx("cause", String.class));
                g20.c();
                return g20;
        }
    }
}
