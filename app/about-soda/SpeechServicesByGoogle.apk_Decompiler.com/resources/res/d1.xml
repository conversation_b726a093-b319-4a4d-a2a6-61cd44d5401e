<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:orientation="vertical" android:id="@+id/voice_list_container" android:layout_width="match_parent" android:layout_height="match_parent">
    <LinearLayout android:orientation="horizontal" android:id="@+id/voice_entry_summary" android:background="?android:attr/selectableItemBackground" android:focusable="false" android:clickable="false" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_marginStart="@dimen/car_ui_scrollbar_margin" android:layout_marginEnd="@dimen/car_ui_scrollbar_margin">
        <TextView android:textAppearance="?android:attr/textAppearanceListItem" android:textColor="?android:attr/textColorAlertDialogListItem" android:gravity="left|center_vertical|center_horizontal|center|start" android:id="@+id/voice_entry_status" android:paddingTop="15dp" android:layout_width="0dp" android:layout_height="wrap_content" android:singleLine="false" android:layout_weight="1"/>
        <View android:layout_gravity="right|center_vertical|center_horizontal|center|end" android:id="@+id/voice_entry_divider" android:background="@android:drawable/divider_horizontal_dark" android:layout_width="2dp" android:layout_height="match_parent" android:layout_marginTop="10dp" android:layout_marginBottom="10dp"/>
        <ImageView android:layout_gravity="right|center_vertical|center_horizontal|center|end" android:id="@+id/voice_entry_download_button" android:background="?android:attr/selectableItemBackground" android:padding="15dp" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/downloadIcon" android:tint="?android:attr/colorControlNormal" android:contentDescription="@string/voice_entry_download_desc"/>
        <ImageView android:layout_gravity="right|center_vertical|center_horizontal|center|end" android:id="@+id/voice_entry_cancel_download_button" android:background="?android:attr/selectableItemBackground" android:padding="15dp" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/cancelIcon" android:tint="?android:attr/colorControlNormal" android:contentDescription="@string/voice_entry_cancel_download_desc"/>
        <ImageView android:id="@+id/voice_entry_delete_button" android:background="?android:attr/selectableItemBackground" android:padding="15dp" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/deleteIcon" android:tint="?android:attr/colorControlNormal" android:contentDescription="@string/voice_entry_delete_desc"/>
        <ImageView android:layout_gravity="right|center_vertical|center_horizontal|center|end" android:id="@+id/voice_entry_overflow" android:background="?android:attr/selectableItemBackground" android:padding="15dp" android:focusable="true" android:visibility="gone" android:clickable="true" android:layout_width="wrap_content" android:layout_height="wrap_content" android:src="@drawable/quantum_ic_more_vert_grey600_24" android:tint="?android:attr/colorControlNormal" android:contentDescription="@string/voice_entry_overflow_desc"/>
    </LinearLayout>
    <com.android.car.ui.recyclerview.CarUiRecyclerView android:id="@+id/locales_list" android:descendantFocusability="afterDescendants" android:layout_width="match_parent" android:layout_height="match_parent"/>
</LinearLayout>
