package defpackage;

import java.util.List;
import java.util.Map;

/* renamed from: brb  reason: default package */
/* compiled from: PG */
public final class brb implements gir, gis, iio {
    private iiz a;
    private iiz b;
    private iiz c;
    private final brc d;

    public brb() {
        throw null;
    }

    public final Map a() {
        return haq.a;
    }

    public final bmu b() {
        brc brc = this.d;
        cqx cqx = (cqx) brc.a.b();
        bzl bzl = new bzl((hmi) brc.b.b());
        hau hau = hau.a;
        iiu iiu = ijc.a;
        fvf.aw(hau.isEmpty(), "Can't provide Sync SyncSystemMonitor to any account level. Only application scoped SyncSystemMonitors may be provided");
        return new bmu((Object) bzl, (Object) new dab(hau, 10), (Object) iiu, (char[]) null);
    }

    public brb(brc brc, fwm fwm) {
        this.d = brc;
        iiu c2 = iiv.c(fwm);
        this.a = c2;
        this.b = new gbh(c2, 10);
        iiu iiu = ijc.a;
        List ar = ftc.ar(0);
        List ar2 = ftc.ar(1);
        hzz.p(this.b, ar2);
        this.c = new ijc(ar, ar2);
        ijd.a(new gfa((jjk) brc.e, (jjk) brc.f, (jjk) this.c, 3, (byte[]) null));
    }
}
