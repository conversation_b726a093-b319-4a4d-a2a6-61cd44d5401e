package defpackage;

/* renamed from: ber  reason: default package */
/* compiled from: PG */
public final class ber extends wf {
    public final int a;

    public ber(int i) {
        this.a = i;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if ((obj instanceof ber) && this.a == ((ber) obj).a) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return this.a;
    }

    public final String toString() {
        return "ConstraintsNotMet(reason=" + this.a + ')';
    }
}
