package j$.time;

import j$.time.chrono.C0003b;
import j$.time.chrono.C0006e;
import j$.time.chrono.C0010i;
import j$.time.chrono.C0012k;
import j$.time.temporal.ChronoUnit;
import j$.time.temporal.TemporalUnit;
import j$.time.temporal.a;
import j$.time.temporal.k;
import j$.time.temporal.l;
import j$.time.temporal.m;
import j$.time.temporal.n;
import j$.time.temporal.p;
import j$.time.temporal.r;
import j$.time.temporal.t;
import j$.util.Objects;
import java.io.DataOutput;
import java.io.InvalidObjectException;
import java.io.ObjectInputStream;
import java.io.Serializable;

public final class i implements k, n, C0006e, Serializable {
    public static final i c = Q(g.d, k.e);
    public static final i d = Q(g.e, k.f);
    private static final long serialVersionUID = 6207766400415563566L;
    private final g a;
    private final k b;

    private i(g gVar, k kVar) {
        this.a = gVar;
        this.b = kVar;
    }

    private int I(i iVar) {
        int I = this.a.I(iVar.a);
        if (I == 0) {
            return this.b.compareTo(iVar.b);
        }
        return I;
    }

    public static i J(m mVar) {
        if (mVar instanceof i) {
            return (i) mVar;
        }
        if (mVar instanceof ZonedDateTime) {
            return ((ZonedDateTime) mVar).M();
        }
        if (mVar instanceof p) {
            return ((p) mVar).L();
        }
        try {
            return new i(g.K(mVar), k.K(mVar));
        } catch (c e) {
            String valueOf = String.valueOf(mVar);
            String name = mVar.getClass().getName();
            throw new RuntimeException("Unable to obtain LocalDateTime from TemporalAccessor: " + valueOf + " of type " + name, e);
        }
    }

    public static i P(int i) {
        return new i(g.T(i, 12, 31), k.P(0));
    }

    public static i Q(g gVar, k kVar) {
        Objects.requireNonNull(gVar, "date");
        Objects.requireNonNull(kVar, "time");
        return new i(gVar, kVar);
    }

    public static i R(long j, int i, x xVar) {
        Objects.requireNonNull(xVar, "offset");
        long j2 = (long) i;
        a.NANO_OF_SECOND.I(j2);
        long O = j + ((long) xVar.O());
        long j3 = (long) 86400;
        return new i(g.V(j$.nio.file.attribute.a.b(O, j3)), k.Q((((long) ((int) j$.nio.file.attribute.a.d(O, j3))) * 1000000000) + j2));
    }

    private i U(g gVar, long j, long j2, long j3, long j4) {
        g gVar2 = gVar;
        k kVar = this.b;
        if ((j | j2 | j3 | j4) == 0) {
            return Y(gVar2, kVar);
        }
        long j5 = j2 / 1440;
        long j6 = j / 24;
        long j7 = j6 + j5 + (j3 / 86400) + (j4 / 86400000000000L);
        long j8 = (long) 1;
        long j9 = (j2 % 1440) * 60000000000L;
        long j10 = ((j % 24) * 3600000000000L) + j9 + ((j3 % 86400) * 1000000000) + (j4 % 86400000000000L);
        long Y = kVar.Y();
        long j11 = (j10 * j8) + Y;
        long b2 = j$.nio.file.attribute.a.b(j11, 86400000000000L) + (j7 * j8);
        long d2 = j$.nio.file.attribute.a.d(j11, 86400000000000L);
        if (d2 != Y) {
            kVar = k.Q(d2);
        }
        return Y(gVar2.X(b2), kVar);
    }

    private i Y(g gVar, k kVar) {
        if (this.a == gVar && this.b == kVar) {
            return this;
        }
        return new i(gVar, kVar);
    }

    private void readObject(ObjectInputStream objectInputStream) {
        throw new InvalidObjectException("Deserialization via serialization delegate");
    }

    private Object writeReplace() {
        return new s((byte) 5, this);
    }

    public final long B(p pVar) {
        if (!(pVar instanceof a)) {
            return pVar.g(this);
        }
        if (((a) pVar).J()) {
            return this.b.B(pVar);
        }
        return this.a.B(pVar);
    }

    public final Object F(r rVar) {
        if (rVar == l.f()) {
            return this.a;
        }
        return C0010i.k(this, rVar);
    }

    public final int K() {
        return this.b.N();
    }

    public final int L() {
        return this.b.O();
    }

    public final int M() {
        return this.a.P();
    }

    public final boolean N(i iVar) {
        if (!(iVar instanceof i)) {
            int i = (this.a.C() > iVar.a.C() ? 1 : (this.a.C() == iVar.a.C() ? 0 : -1));
            if (i > 0 || (i == 0 && this.b.Y() > iVar.b.Y())) {
                return true;
            }
            return false;
        } else if (I(iVar) > 0) {
            return true;
        } else {
            return false;
        }
    }

    public final boolean O(i iVar) {
        if (!(iVar instanceof i)) {
            int i = (this.a.C() > iVar.a.C() ? 1 : (this.a.C() == iVar.a.C() ? 0 : -1));
            if (i < 0 || (i == 0 && this.b.Y() < iVar.b.Y())) {
                return true;
            }
            return false;
        } else if (I(iVar) < 0) {
            return true;
        } else {
            return false;
        }
    }

    /* renamed from: S */
    public final i f(long j, TemporalUnit temporalUnit) {
        long j2 = j;
        TemporalUnit temporalUnit2 = temporalUnit;
        if (!(temporalUnit2 instanceof ChronoUnit)) {
            return (i) temporalUnit2.m(this, j2);
        }
        int i = h.a[((ChronoUnit) temporalUnit2).ordinal()];
        k kVar = this.b;
        g gVar = this.a;
        switch (i) {
            case 1:
                return U(this.a, 0, 0, 0, j);
            case 2:
                i Y = Y(gVar.X(j2 / 86400000000L), kVar);
                return Y.U(Y.a, 0, 0, 0, (j2 % 86400000000L) * 1000);
            case 3:
                i Y2 = Y(gVar.X(j2 / 86400000), kVar);
                return Y2.U(Y2.a, 0, 0, 0, (j2 % 86400000) * 1000000);
            case 4:
                return T(j);
            case 5:
                return U(this.a, 0, j, 0, 0);
            case 6:
                return U(this.a, j, 0, 0, 0);
            case 7:
                i Y3 = Y(gVar.X(j2 / 256), kVar);
                return Y3.U(Y3.a, (j2 % 256) * 12, 0, 0, 0);
            default:
                return Y(gVar.f(j2, temporalUnit2), kVar);
        }
    }

    public final i T(long j) {
        return U(this.a, 0, 0, j, 0);
    }

    public final g V() {
        return this.a;
    }

    /* renamed from: W */
    public final i d(long j, p pVar) {
        if (!(pVar instanceof a)) {
            return (i) pVar.s(this, j);
        }
        boolean J2 = ((a) pVar).J();
        k kVar = this.b;
        g gVar = this.a;
        if (J2) {
            return Y(gVar, kVar.d(j, pVar));
        }
        return Y(gVar.d(j, pVar), kVar);
    }

    public final i X(g gVar) {
        return Y(gVar, this.b);
    }

    /* access modifiers changed from: package-private */
    public final void Z(DataOutput dataOutput) {
        this.a.f0(dataOutput);
        this.b.c0(dataOutput);
    }

    public final j$.time.chrono.n a() {
        return ((g) c()).a();
    }

    public final k b() {
        return this.b;
    }

    public final C0003b c() {
        return this.a;
    }

    public final boolean e(p pVar) {
        if (pVar instanceof a) {
            a aVar = (a) pVar;
            if (aVar.B() || aVar.J()) {
                return true;
            }
            return false;
        } else if (pVar == null || !pVar.p(this)) {
            return false;
        } else {
            return true;
        }
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof i)) {
            return false;
        }
        i iVar = (i) obj;
        if (!this.a.equals(iVar.a) || !this.b.equals(iVar.b)) {
            return false;
        }
        return true;
    }

    public final k g(k kVar) {
        return kVar.d(((g) c()).C(), a.EPOCH_DAY).d(b().Y(), a.NANO_OF_DAY);
    }

    public final int hashCode() {
        return this.a.hashCode() ^ this.b.hashCode();
    }

    /* renamed from: j */
    public final int compareTo(C0006e eVar) {
        if (eVar instanceof i) {
            return I((i) eVar);
        }
        return C0010i.c(this, eVar);
    }

    public final k m(long j, ChronoUnit chronoUnit) {
        if (j == Long.MIN_VALUE) {
            return f(Long.MAX_VALUE, chronoUnit).f(1, chronoUnit);
        }
        return f(-j, chronoUnit);
    }

    public final int p(p pVar) {
        if (!(pVar instanceof a)) {
            return l.a(this, pVar);
        }
        if (((a) pVar).J()) {
            return this.b.p(pVar);
        }
        return this.a.p(pVar);
    }

    public final k s(g gVar) {
        return Y(gVar, this.b);
    }

    public final t t(p pVar) {
        if (!(pVar instanceof a)) {
            return pVar.t(this);
        }
        if (!((a) pVar).J()) {
            return this.a.t(pVar);
        }
        k kVar = this.b;
        kVar.getClass();
        return l.d(kVar, pVar);
    }

    public final String toString() {
        String gVar = this.a.toString();
        String kVar = this.b.toString();
        return gVar + "T" + kVar;
    }

    public final C0012k w(x xVar) {
        return ZonedDateTime.J(this, xVar, (x) null);
    }
}
