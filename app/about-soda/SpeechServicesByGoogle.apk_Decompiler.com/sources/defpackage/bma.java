package defpackage;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.MotionEvent;
import android.view.View;
import com.google.android.tts.R;

/* renamed from: bma  reason: default package */
/* compiled from: PG */
public final class bma implements View.OnTouchListener {
    public final Handler a = new Handler(Looper.getMainLooper());
    public final int b;
    public final View.OnClickListener c;
    public View d;
    public boolean e;
    private final int f;
    private final Runnable g = new alr(this, 19, (byte[]) null);

    public bma(Context context, View.OnClickListener onClickListener) {
        int integer = context.getResources().getInteger(R.integer.car_ui_scrollbar_longpress_initial_delay);
        this.f = integer;
        int integer2 = context.getResources().getInteger(R.integer.car_ui_scrollbar_longpress_repeat_interval);
        this.b = integer2;
        if (integer < 0 || integer2 < 0) {
            throw new IllegalArgumentException("negative intervals are not allowed");
        }
        this.c = onClickListener;
    }

    public final void a() {
        this.a.removeCallbacks(this.g);
        this.e = false;
    }

    public final boolean onTouch(View view, MotionEvent motionEvent) {
        this.d = view;
        int action = motionEvent.getAction();
        if (action == 0) {
            this.a.removeCallbacks(this.g);
            this.a.postDelayed(this.g, (long) this.f);
            this.d.setPressed(true);
            return true;
        } else if (action != 1 && action != 3) {
            return false;
        } else {
            if (!this.e) {
                this.c.onClick(view);
            }
            this.a.removeCallbacks(this.g);
            this.d.setPressed(false);
            this.e = false;
            return true;
        }
    }
}
