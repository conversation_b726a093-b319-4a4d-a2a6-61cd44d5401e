package j$.time.format;

import j$.time.ZoneId;
import j$.time.temporal.r;

class q implements f {
    private final r a;
    private final String b;

    q(r rVar, String str) {
        this.a = rVar;
        this.b = str;
    }

    public boolean g(u uVar, StringBuilder sb) {
        ZoneId zoneId = (ZoneId) uVar.f(this.a);
        if (zoneId == null) {
            return false;
        }
        sb.append(zoneId.l());
        return true;
    }

    public final String toString() {
        return this.b;
    }
}
