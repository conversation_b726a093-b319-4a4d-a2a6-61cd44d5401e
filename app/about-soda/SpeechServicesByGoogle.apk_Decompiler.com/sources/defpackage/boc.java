package defpackage;

import android.view.View;
import android.view.ViewTreeObserver;

/* renamed from: boc  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class boc implements Runnable {
    public final /* synthetic */ boe a;
    public final /* synthetic */ ViewTreeObserver.OnGlobalLayoutListener[] b;
    public final /* synthetic */ Runnable[] c;
    public final /* synthetic */ Runnable[] d;
    public final /* synthetic */ View e;

    public /* synthetic */ boc(boe boe, ViewTreeObserver.OnGlobalLayoutListener[] onGlobalLayoutListenerArr, Runnable[] runnableArr, Runnable[] runnableArr2, View view) {
        this.a = boe;
        this.b = onGlobalLayoutListenerArr;
        this.c = runnableArr;
        this.d = runnableArr2;
        this.e = view;
    }

    public final void run() {
        boe boe = this.a;
        yi.u(boe, this.b, this.c, this.d);
        View view = this.e;
        if (yi.y(view)) {
            return;
        }
        if (!boe.isLayoutCompleted() || !view.isShown() || !yi.z(boe)) {
            yi.w(view.getRootView(), 1, (View) null, false, false);
        }
    }
}
