package j$.time.format;

import j$.util.concurrent.ConcurrentHashMap;

final class r extends q {
    private static final ConcurrentHashMap e = new ConcurrentHashMap();
    private final B c;
    private final boolean d;

    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    r(j$.time.format.B r5, boolean r6) {
        /*
            r4 = this;
            j$.time.temporal.r r0 = j$.time.temporal.l.j()
            java.lang.String r1 = java.lang.String.valueOf(r5)
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            java.lang.String r3 = "ZoneText("
            r2.<init>(r3)
            r2.append(r1)
            java.lang.String r1 = ")"
            r2.append(r1)
            java.lang.String r1 = r2.toString()
            r4.<init>(r0, r1)
            java.util.HashMap r0 = new java.util.HashMap
            r0.<init>()
            java.util.HashMap r0 = new java.util.HashMap
            r0.<init>()
            java.lang.String r0 = "textStyle"
            java.lang.Object r5 = j$.util.Objects.requireNonNull(r5, r0)
            j$.time.format.B r5 = (j$.time.format.B) r5
            r4.c = r5
            r4.d = r6
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: j$.time.format.r.<init>(j$.time.format.B, boolean):void");
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r6v0, resolved type: j$.util.concurrent.ConcurrentHashMap} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r6v1, resolved type: java.lang.String} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r6v5, resolved type: j$.util.concurrent.ConcurrentHashMap} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r6v9, resolved type: java.util.Map} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r6v10, resolved type: java.lang.String} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r6v11, resolved type: java.lang.String} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r6v12, resolved type: java.lang.String} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r6v15, resolved type: java.lang.String} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r6v16, resolved type: java.lang.String} */
    /* JADX WARNING: Code restructure failed: missing block: B:25:0x00a9, code lost:
        if (r8 == null) goto L_0x00ab;
     */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:20:0x008f  */
    /* JADX WARNING: Removed duplicated region for block: B:36:0x0102  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean g(j$.time.format.u r14, java.lang.StringBuilder r15) {
        /*
            r13 = this;
            j$.time.temporal.r r0 = j$.time.temporal.l.k()
            java.lang.Object r0 = r14.f(r0)
            j$.time.ZoneId r0 = (j$.time.ZoneId) r0
            r1 = 0
            if (r0 != 0) goto L_0x000e
            return r1
        L_0x000e:
            java.lang.String r2 = r0.l()
            boolean r3 = r0 instanceof j$.time.x
            r4 = 1
            if (r3 != 0) goto L_0x0103
            j$.time.temporal.m r3 = r14.d()
            boolean r5 = r13.d
            r6 = 0
            r7 = 2
            if (r5 != 0) goto L_0x0082
            j$.time.temporal.a r5 = j$.time.temporal.a.INSTANT_SECONDS
            boolean r5 = r3.e(r5)
            if (r5 == 0) goto L_0x0036
            j$.time.zone.e r0 = r0.I()
            j$.time.Instant r3 = j$.time.Instant.J(r3)
            boolean r0 = r0.h(r3)
            goto L_0x0083
        L_0x0036:
            j$.time.temporal.a r5 = j$.time.temporal.a.EPOCH_DAY
            boolean r8 = r3.e(r5)
            if (r8 == 0) goto L_0x0082
            j$.time.temporal.a r8 = j$.time.temporal.a.NANO_OF_DAY
            boolean r9 = r3.e(r8)
            if (r9 == 0) goto L_0x0082
            long r9 = r3.B(r5)
            j$.time.g r5 = j$.time.g.V(r9)
            long r8 = r3.B(r8)
            j$.time.k r3 = j$.time.k.Q(r8)
            j$.time.i r3 = j$.time.i.Q(r5, r3)
            j$.time.zone.e r5 = r0.I()
            j$.time.zone.b r5 = r5.f(r3)
            if (r5 != 0) goto L_0x0082
            j$.time.zone.e r5 = r0.I()
            j$.time.ZonedDateTime r0 = j$.time.ZonedDateTime.J(r3, r0, r6)
            long r8 = j$.time.chrono.C0010i.o(r0)
            j$.time.k r0 = r0.b()
            int r0 = r0.N()
            long r10 = (long) r0
            j$.time.Instant r0 = j$.time.Instant.ofEpochSecond(r8, r10)
            boolean r0 = r5.h(r0)
            goto L_0x0083
        L_0x0082:
            r0 = 2
        L_0x0083:
            java.util.Locale r14 = r14.c()
            j$.time.format.B r3 = j$.time.format.B.NARROW
            j$.time.format.B r5 = r13.c
            if (r5 != r3) goto L_0x008f
            goto L_0x0100
        L_0x008f:
            j$.util.concurrent.ConcurrentHashMap r3 = e
            java.lang.Object r8 = r3.get(r2)
            java.lang.ref.SoftReference r8 = (java.lang.ref.SoftReference) r8
            r9 = 5
            r10 = 3
            if (r8 == 0) goto L_0x00ab
            java.lang.Object r6 = r8.get()
            java.util.Map r6 = (java.util.Map) r6
            if (r6 == 0) goto L_0x00ab
            java.lang.Object r8 = r6.get(r14)
            java.lang.String[] r8 = (java.lang.String[]) r8
            if (r8 != 0) goto L_0x00e5
        L_0x00ab:
            java.util.TimeZone r8 = java.util.TimeZone.getTimeZone(r2)
            r11 = 7
            java.lang.String[] r11 = new java.lang.String[r11]
            r11[r1] = r2
            java.lang.String r12 = r8.getDisplayName(r1, r4, r14)
            r11[r4] = r12
            java.lang.String r12 = r8.getDisplayName(r1, r1, r14)
            r11[r7] = r12
            java.lang.String r7 = r8.getDisplayName(r4, r4, r14)
            r11[r10] = r7
            r7 = 4
            java.lang.String r1 = r8.getDisplayName(r4, r1, r14)
            r11[r7] = r1
            r11[r9] = r2
            r1 = 6
            r11[r1] = r2
            if (r6 != 0) goto L_0x00d9
            j$.util.concurrent.ConcurrentHashMap r6 = new j$.util.concurrent.ConcurrentHashMap
            r6.<init>()
        L_0x00d9:
            r6.put(r14, r11)
            java.lang.ref.SoftReference r14 = new java.lang.ref.SoftReference
            r14.<init>(r6)
            r3.put(r2, r14)
            r8 = r11
        L_0x00e5:
            if (r0 == 0) goto L_0x00f9
            if (r0 == r4) goto L_0x00f1
            int r14 = r5.g()
            int r14 = r14 + r9
            r6 = r8[r14]
            goto L_0x0100
        L_0x00f1:
            int r14 = r5.g()
            int r14 = r14 + r10
            r6 = r8[r14]
            goto L_0x0100
        L_0x00f9:
            int r14 = r5.g()
            int r14 = r14 + r4
            r6 = r8[r14]
        L_0x0100:
            if (r6 == 0) goto L_0x0103
            r2 = r6
        L_0x0103:
            r15.append(r2)
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: j$.time.format.r.g(j$.time.format.u, java.lang.StringBuilder):boolean");
    }
}
