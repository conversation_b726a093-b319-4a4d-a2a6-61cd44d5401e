package defpackage;

import android.app.ActivityManager;
import android.content.Context;
import android.os.Build;
import android.os.Process;
import java.lang.reflect.Method;
import java.util.Iterator;
import java.util.List;

/* renamed from: big  reason: default package */
/* compiled from: PG */
public final class big {
    static {
        bbk.b("ProcessUtils");
    }

    public static final boolean a(Context context, bam bam) {
        String str;
        T t;
        jnu.e(context, "context");
        jnu.e(bam, "configuration");
        if (Build.VERSION.SDK_INT >= 28) {
            str = a$$ExternalSyntheticApiModelOutline0.m();
            jnu.d(str, "getProcessName()");
        } else {
            str = null;
            try {
                Method declaredMethod = Class.forName("android.app.ActivityThread", false, bbz.class.getClassLoader()).getDeclaredMethod("currentProcessName", (Class[]) null);
                declaredMethod.setAccessible(true);
                Object invoke = declaredMethod.invoke((Object) null, (Object[]) null);
                jnu.b(invoke);
                if (invoke instanceof String) {
                    str = (String) invoke;
                }
            } catch (Throwable unused) {
                bbk.a();
            }
            int myPid = Process.myPid();
            Object systemService = context.getSystemService("activity");
            jnu.c(systemService, "null cannot be cast to non-null type android.app.ActivityManager");
            List<ActivityManager.RunningAppProcessInfo> runningAppProcesses = ((ActivityManager) systemService).getRunningAppProcesses();
            if (runningAppProcesses != null) {
                Iterator<T> it = runningAppProcesses.iterator();
                while (true) {
                    if (!it.hasNext()) {
                        t = null;
                        break;
                    }
                    t = it.next();
                    if (((ActivityManager.RunningAppProcessInfo) t).pid == myPid) {
                        break;
                    }
                }
                ActivityManager.RunningAppProcessInfo runningAppProcessInfo = (ActivityManager.RunningAppProcessInfo) t;
                if (runningAppProcessInfo != null) {
                    str = runningAppProcessInfo.processName;
                }
            }
        }
        String str2 = bam.i;
        if (str2 == null || str2.length() == 0) {
            return jnu.i(str, context.getApplicationInfo().processName);
        }
        return jnu.i(str, bam.i);
    }
}
