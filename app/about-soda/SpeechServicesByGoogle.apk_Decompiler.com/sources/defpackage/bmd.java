package defpackage;

import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.view.View;
import com.google.android.tts.R;
import j$.util.Objects;

/* renamed from: bmd  reason: default package */
/* compiled from: PG */
public final class bmd extends ki {
    private final Drawable a;
    private int b;

    public bmd(Drawable drawable) {
        this.a = drawable;
    }

    public final void a(Rect rect, View view, RecyclerView recyclerView, la laVar) {
        super.a(rect, view, recyclerView, laVar);
        if (recyclerView.c(view) != 0) {
            int i = ((LinearLayoutManager) Objects.requireNonNull(recyclerView.m)).k;
            this.b = i;
            if (i == 0) {
                rect.left = this.a.getIntrinsicWidth();
            } else {
                rect.top = this.a.getIntrinsicHeight();
            }
        }
    }

    public final void b(Canvas canvas, RecyclerView recyclerView) {
        int i = 0;
        if (this.b == 0) {
            int paddingTop = recyclerView.getPaddingTop() + ((int) recyclerView.getContext().getResources().getDimension(R.dimen.car_ui_recyclerview_divider_top_margin));
            int height = recyclerView.getHeight() - recyclerView.getPaddingBottom();
            int dimension = (int) recyclerView.getContext().getResources().getDimension(R.dimen.car_ui_recyclerview_divider_bottom_margin);
            kl klVar = recyclerView.m;
            int ao = klVar.ao();
            while (i < ao - 1) {
                View aA = klVar.aA(i);
                int right = aA.getRight() + ((km) aA.getLayoutParams()).rightMargin;
                int intrinsicWidth = this.a.getIntrinsicWidth() + right;
                this.a.setBounds(right, paddingTop, intrinsicWidth, height - dimension);
                this.a.draw(canvas);
                i++;
            }
            return;
        }
        int paddingLeft = recyclerView.getPaddingLeft() + ((int) recyclerView.getContext().getResources().getDimension(R.dimen.car_ui_recyclerview_divider_start_margin));
        int width = recyclerView.getWidth() - recyclerView.getPaddingRight();
        int dimension2 = (int) recyclerView.getContext().getResources().getDimension(R.dimen.car_ui_recyclerview_divider_end_margin);
        kl klVar2 = recyclerView.m;
        int ao2 = klVar2.ao();
        while (i < ao2 - 1) {
            View aA2 = klVar2.aA(i);
            int bottom = aA2.getBottom() + ((km) aA2.getLayoutParams()).bottomMargin;
            this.a.setBounds(paddingLeft, bottom, width - dimension2, this.a.getIntrinsicHeight() + bottom);
            this.a.draw(canvas);
            i++;
        }
    }
}
