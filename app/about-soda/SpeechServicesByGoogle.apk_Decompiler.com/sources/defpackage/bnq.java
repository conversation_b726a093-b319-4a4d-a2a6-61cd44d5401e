package defpackage;

import android.graphics.drawable.Drawable;
import java.util.List;

/* renamed from: bnq  reason: default package */
/* compiled from: PG */
public interface bnq {
    @Deprecated
    boolean isStateSet();

    void setLogo(Drawable drawable);

    void setMenuItems(List list);

    void setNavButtonMode(bmp bmp);

    @Deprecated
    void setNavButtonMode(bnk bnk);

    @Deprecated
    void setState(bnp bnp);

    void setSubtitle(CharSequence charSequence);

    void setTabs(List list);

    void setTitle(int i);

    void setTitle(CharSequence charSequence);
}
