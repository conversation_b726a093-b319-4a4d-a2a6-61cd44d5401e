package defpackage;

/* renamed from: evr  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class evr implements gsb {
    public final /* synthetic */ bmu a;
    private final /* synthetic */ int b;

    public /* synthetic */ evr(bmu bmu, int i) {
        this.b = i;
        this.a = bmu;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                fqy g = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/notifications_device_channel_request_status", new fqx("status", String.class));
                g.c();
                return g;
            case 1:
                fqy g2 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/notifications_device_channel_orphan_count", new fqx("channel_id", String.class), new fqx("channel_name", String.class));
                g2.c();
                return g2;
            case 2:
                fqy g3 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/notifications_device_channel_sync_counter", new fqx[0]);
                g3.c();
                return g3;
            case 3:
                fqy g4 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/notifications_dropped_on_non_primary_account", new fqx("notification_category", String.class));
                g4.c();
                return g4;
            case 4:
                fqy g5 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/notifications_dropped_on_unicorn_account", new fqx("notification_category", String.class));
                g5.c();
                return g5;
            case 5:
                fqy g6 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/surfaces/languagepack/download/trigger_count", new fqx("surface", String.class), new fqx("locale", String.class), new fqx("trigger", String.class));
                g6.c();
                return g6;
            case 6:
                fqy g7 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/notifications_dropped_on_unicorn_account_destination", new fqx("first_destination", Integer.class), new fqx("notification_category", String.class));
                g7.c();
                return g7;
            case 7:
                fqy g8 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_notifications/tng_entrytree_invalidation_counter", new fqx[0]);
                g8.c();
                return g8;
            case 8:
                fqy g9 = ((frc) this.a.a).g("/client_streamz/android_gsa/search_widget/repeating_update_scheduler_count", new fqx[0]);
                g9.c();
                return g9;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fqy g10 = ((frc) this.a.a).g("/client_streamz/android_gsa/sensitive_api_calls_contact", new fqx("calling_component_name", String.class));
                g10.c();
                return g10;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fqy g11 = ((frc) this.a.a).g("/client_streamz/android_gsa/sensitive_api_calls_contact_attribution", new fqx("attribution_constrant", String.class));
                g11.c();
                return g11;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                fqy g12 = ((frc) this.a.a).g("/client_streamz/android_gsa/appactions/app_inventory_api/request_count", new fqx("android_version", Integer.class), new fqx("method_name", String.class), new fqx("client_id", String.class));
                g12.c();
                return g12;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                fqy g13 = ((frc) this.a.a).g("/client_streamz/android_gsa/sensitive_api_calls_location", new fqx("calling_component_name", String.class));
                g13.c();
                return g13;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                fqy g14 = ((frc) this.a.a).g("/client_streamz/android_gsa/signed_out", new fqx("reason", String.class), new fqx("initializing", Boolean.class), new fqx("fetch_accounts_with_gmscore", Boolean.class));
                g14.c();
                return g14;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                fqy g15 = ((frc) this.a.a).g("/client_streamz/android_gsa/silentfeedback/crashloop_recoverer_trigger_count", new fqx[0]);
                g15.c();
                return g15;
            case 15:
                fqy g16 = ((frc) this.a.a).g("/client_streamz/android_gsa/smart_dictation/corrections_cached", new fqx[0]);
                g16.c();
                return g16;
            case 16:
                fqv c = ((frc) this.a.a).c("/client_streamz/android_gsa/assistant/surfaces/voice_search/handover/latency", new fqx("lang", String.class), new fqx("handover", String.class), new fqx("classifier_enabled", Boolean.class), new fqx("assistant_handover_enabled", Boolean.class), new fqx("from_classifier", Boolean.class), new fqx("from_safe_guard", Boolean.class));
                c.c();
                return c;
            case 17:
                fqy g17 = ((frc) this.a.a).g("/client_streamz/android_gsa/smart_dictation/corrections_expired", new fqx[0]);
                g17.c();
                return g17;
            case 18:
                fqy g18 = ((frc) this.a.a).g("/client_streamz/android_gsa/smart_dictation/corrections_migrated_from_ekho", new fqx[0]);
                g18.c();
                return g18;
            case 19:
                fqy g19 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/ambient_assistant_events_count", new fqx("app_version", String.class), new fqx("event_type", String.class));
                g19.c();
                return g19;
            default:
                fqy g20 = ((frc) this.a.a).g("/client_streamz/android_gsa/smartspace/app_suggestions", new fqx("app_version", String.class), new fqx("context_type", String.class), new fqx("num_suggestions", Integer.class), new fqx("dau", Boolean.class));
                g20.c();
                return g20;
        }
    }
}
