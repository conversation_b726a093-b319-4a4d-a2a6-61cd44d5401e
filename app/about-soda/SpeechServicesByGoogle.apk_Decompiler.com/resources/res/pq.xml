<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt">
    <Preference android:title="@string/install_voices_title" android:key="@string/install_voices_key" app:iconSpaceReserved="false"/>
    <SwitchPreference android:persistent="true" android:title="@string/auto_update_wifi_title" android:key="@string/auto_update_wifi_key" android:summary="@string/auto_update_wifi_summary" android:defaultValue="false" app:iconSpaceReserved="false"/>
    <SwitchPreference android:persistent="true" android:title="@string/loudness_title" android:key="@string/loudness_key" android:summary="@string/loudness_summary" android:defaultValue="false" app:iconSpaceReserved="false"/>
    <DropDownPreference android:persistent="true" android:entries="@array/language_detection_entries" android:title="@string/language_detection_title" android:key="@string/language_detection_options_key" android:defaultValue="script" android:entryValues="@array/language_detection_values" app:iconSpaceReserved="false"/>
    <PreferenceScreen android:title="@string/analytics_title" android:key="@string/analytics_screen_key" android:fragment="com.google.android.apps.speech.tts.googletts.settings.AnalyticsPreferenceScreen" app:iconSpaceReserved="false"/>
    <Preference android:title="@string/feedback_title" android:key="@string/feedback_key" app:iconSpaceReserved="false"/>
    <Preference android:title="@string/open_source_licenses" android:key="@string/open_source_licenses_key" app:iconSpaceReserved="false"/>
</PreferenceScreen>
