package defpackage;

import android.view.View;
import java.lang.ref.WeakReference;

/* renamed from: bjq  reason: default package */
/* compiled from: PG */
public final class bjq {
    final int a;
    final long b;
    eku c;

    public bjq(int i, long j) {
        this.a = i;
        this.b = j;
        if (i == 2 && j <= 0) {
            throw new IllegalArgumentException("Expiration time must be positive if CacheType is CACHE_TYPE_EXPIRED_AFTER_SOME_TIME");
        }
    }

    public final View a(long j) {
        eku eku = this.c;
        if (eku == null) {
            return null;
        }
        int i = this.a;
        if (i != 2) {
            if (i != 3) {
                return null;
            }
        } else if (j >= eku.a + this.b) {
            return null;
        }
        return (View) ((WeakReference) eku.b).get();
    }

    public final void b(View view, long j) {
        eku eku;
        if (this.a != 1) {
            if (view != null) {
                eku = new eku(view, j);
            } else {
                eku = null;
            }
            this.c = eku;
        }
    }
}
