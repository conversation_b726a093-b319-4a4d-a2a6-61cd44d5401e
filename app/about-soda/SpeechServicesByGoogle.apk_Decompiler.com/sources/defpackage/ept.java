package defpackage;

/* renamed from: ept  reason: default package */
/* compiled from: PG */
public enum ept implements hts {
    CLIENT_TYPE_UNKNOWN(0),
    CLIENT_TYPE_AUDIO_REQUEST(1),
    CLIENT_TYPE_HOTWORD(2),
    CLIENT_TYPE_EXTERNAL(10),
    CLIENT_TYPE_TELEPHONY_CALL(11),
    CLIENT_TYPE_PLATFORM_EXTERNAL(12);
    
    public final int g;

    private ept(int i) {
        this.g = i;
    }

    public static ept b(int i) {
        if (i == 0) {
            return CLIENT_TYPE_UNKNOWN;
        }
        if (i == 1) {
            return CLIENT_TYPE_AUDIO_REQUEST;
        }
        if (i == 2) {
            return CLIENT_TYPE_HOTWORD;
        }
        switch (i) {
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return CLIENT_TYPE_EXTERNAL;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return CLIENT_TYPE_TELEPHONY_CALL;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return CLIENT_TYPE_PLATFORM_EXTERNAL;
            default:
                return null;
        }
    }

    public final int a() {
        return this.g;
    }

    public final String toString() {
        return Integer.toString(this.g);
    }
}
