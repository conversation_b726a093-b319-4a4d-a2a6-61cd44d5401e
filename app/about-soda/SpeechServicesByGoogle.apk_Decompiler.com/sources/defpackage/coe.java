package defpackage;

import java.util.concurrent.Executor;

/* renamed from: coe  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class coe implements Runnable {
    public final /* synthetic */ Executor a;
    public final /* synthetic */ Runnable b;
    public final /* synthetic */ hmr c;

    public /* synthetic */ coe(Executor executor, Runnable runnable, hmr hmr) {
        this.a = executor;
        this.b = runnable;
        this.c = hmr;
    }

    public final void run() {
        this.a.execute(new cod(this.b, this.c));
    }
}
