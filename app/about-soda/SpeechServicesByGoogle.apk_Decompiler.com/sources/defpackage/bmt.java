package defpackage;

import android.os.ParcelFileDescriptor;
import j$.util.DesugarCollections;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

/* renamed from: bmt  reason: default package */
/* compiled from: PG */
public final class bmt {
    public Object a;
    public Object b;
    public Object c;

    public bmt() {
    }

    public final bmu a() {
        if (this.a == null || this.c == null) {
            return new bmu(this);
        }
        throw new RuntimeException("Both searchResultItems and searchResultsView can't be set at the same time");
    }

    public final void b(List list) {
        if (list == null || list.size() == 0) {
            this.c = null;
        } else {
            this.c = DesugarCollections.unmodifiableList(new ArrayList(list));
        }
    }

    public final bqd c() {
        Object obj = this.a;
        if (obj != null) {
            return new bqd((byte[]) this.c, (Locale) obj, (ParcelFileDescriptor) this.b);
        }
        throw new IllegalStateException("Missing required properties: locale");
    }

    public final void d(Locale locale) {
        if (locale != null) {
            this.a = locale;
            return;
        }
        throw new NullPointerException("Null locale");
    }

    public bmt(char[] cArr) {
        this.a = null;
    }
}
