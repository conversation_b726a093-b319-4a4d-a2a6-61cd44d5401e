package defpackage;

/* renamed from: cta  reason: default package */
/* compiled from: PG */
public final class cta extends htq implements hvb {
    public static final cta c;
    private static volatile hvh e;
    public String a = "";
    public String b = "";
    private int d;

    static {
        cta cta = new cta();
        c = cta;
        htq.z(cta.class, cta);
    }

    private cta() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(c, "\u0001\u0002\u0000\u0001\u0001\u0002\u0002\u0000\u0000\u0000\u0001ဈ\u0000\u0002ဈ\u0001", new Object[]{"d", "a", "b"});
        } else if (i2 == 3) {
            return new cta();
        } else {
            if (i2 == 4) {
                return new htk((htq) c);
            }
            if (i2 == 5) {
                return c;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = e;
            if (hvh == null) {
                synchronized (cta.class) {
                    hvh = e;
                    if (hvh == null) {
                        hvh = new htl(c);
                        e = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
