package defpackage;

/* renamed from: esy  reason: default package */
/* compiled from: PG */
public final class esy extends htq implements hvb {
    public static final esy b;
    private static volatile hvh d;
    public ebf a;
    private int c;

    static {
        esy esy = new esy();
        b = esy;
        htq.z(esy.class, esy);
    }

    private esy() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(b, "\u0004\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001ဉ\u0000", new Object[]{"c", "a"});
        } else if (i2 == 3) {
            return new esy();
        } else {
            if (i2 == 4) {
                return new htk((htq) b);
            }
            if (i2 == 5) {
                return b;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = d;
            if (hvh == null) {
                synchronized (esy.class) {
                    hvh = d;
                    if (hvh == null) {
                        hvh = new htl(b);
                        d = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
