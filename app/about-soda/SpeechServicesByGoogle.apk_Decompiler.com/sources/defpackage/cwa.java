package defpackage;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;

/* renamed from: cwa  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwa implements hko {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    private final /* synthetic */ int c;

    public /* synthetic */ cwa(Object obj, htq htq, int i) {
        this.c = i;
        this.b = obj;
        this.a = htq;
    }

    /* JADX WARNING: type inference failed for: r3v0, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v15, types: [java.lang.Object, java.util.Comparator] */
    /* JADX WARNING: type inference failed for: r9v55, types: [java.util.concurrent.Future, java.lang.Object] */
    public final hme a(Object obj) {
        switch (this.c) {
            case 0:
                return ((cwb) this.a).o((cze) this.b, (cze) obj, 1094);
            case 1:
                return ((cwb) this.a).o((cze) this.b, (cze) obj, 1097);
            case 2:
                return ((cwb) this.a).o((cze) this.b, (cze) obj, 1088);
            case 3:
                Object obj2 = this.a;
                cwb cwb = (cwb) obj2;
                return ftd.L(cwb.n(cwb.a.j(this.b)), new cwa(obj2, (Object) (cze) obj, 0), cwb.b);
            case 4:
                return ((cwb) this.a).o((cze) this.b, (cze) obj, 1087);
            case 5:
                return ((cwb) this.a).o((cze) this.b, (cze) obj, 1099);
            case 6:
                Object obj3 = this.b;
                cwb cwb2 = (cwb) obj3;
                return ftd.L(cwb2.n(cwb2.a.g((ctg) this.a)), new cwa(obj3, (Object) (cze) obj, 4), cwb2.b);
            case 7:
                return ((cwd) this.a).i((cze) this.b, (cze) obj, 1100);
            case 8:
                return ((cwd) this.a).i((cze) this.b, (cze) obj, 1102);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return ((cwd) this.a).i((cze) this.b, (cze) obj, 1101);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return ((cwd) this.a).i((cze) this.b, (cze) obj, 1104);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                Object obj4 = this.b;
                cwd cwd = (cwd) obj4;
                return ftd.L(cwd.b(cwd.b.e((ctj) this.a)), new cwa(obj4, (Object) (cze) obj, 7), cwd.c);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                Object obj5 = this.a;
                cwd cwd2 = (cwd) obj5;
                return ftd.L(cwd2.b(cwd2.b.f((gyo) this.b)), new cwa(obj5, (Object) (cze) obj, 14), cwd2.c);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                Object obj6 = this.b;
                cwd cwd3 = (cwd) obj6;
                return ftd.L(cwd3.b(cwd3.b.g((ctj) this.a)), new cwa(obj6, (Object) (cze) obj, 8), cwd3.c);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return ((cwd) this.a).i((cze) this.b, (cze) obj, 1100);
            case 15:
                List list = (List) obj;
                ArrayList arrayList = new ArrayList();
                Iterator it = list.iterator();
                while (true) {
                    Object obj7 = this.a;
                    if (it.hasNext()) {
                        arrayList.add(((cwd) obj7).a.e((ctj) it.next()));
                    } else {
                        cwd cwd4 = (cwd) obj7;
                        return cqh.U(arrayList).o(new cvs(cwd4, list, (List) arrayList, (Boolean) this.b, 2), cwd4.c);
                    }
                }
            case 16:
                Object obj8 = this.a;
                cwd cwd5 = (cwd) obj8;
                return ftd.L(cwd5.b(cwd5.b.c()), new cvp(obj8, (cze) obj, (Comparator) this.b, 16), cwd5.c);
            case 17:
                Void voidR = (Void) obj;
                if (!ikc.a.a().v()) {
                    return hma.a;
                }
                Object obj9 = this.b;
                cwm cwm = (cwm) this.a;
                cwm.l.d(1032);
                cvy cvy = cwm.c;
                return cvy.q(cvy.c.d(), gof.d(new cvn((Object) cvy, obj9, 4)));
            case 18:
                if (((cvx) obj) == cvx.DOWNLOADED) {
                    ? r9 = this.b;
                    Object obj10 = this.a;
                    csx csx = (csx) hfc.S(r9);
                    fvf.aP(csx);
                    ((cwm) obj10).l.e(1034, csx.c, csx.e, csx.r, csx.s);
                }
                return hma.a;
            case 19:
                Void voidR2 = (Void) obj;
                return ((cwm) this.a).b(((cxg) this.b).b);
            default:
                Void voidR3 = (Void) obj;
                cxg cxg = (cxg) this.b;
                csx csx2 = cxg.b;
                csw csw = csx2.b;
                if (csw == null) {
                    csw = csw.i;
                }
                htk htk = (htk) csw.C(5);
                htk.x(csw);
                if (!htk.b.B()) {
                    htk.u();
                }
                csw csw2 = (csw) htk.b;
                csw2.a |= 32;
                csw2.g = true;
                csw csw3 = (csw) htk.r();
                htk htk2 = (htk) csx2.C(5);
                htk2.x(csx2);
                if (!htk2.b.B()) {
                    htk2.u();
                }
                Object obj11 = this.a;
                csx csx3 = (csx) htk2.b;
                csw3.getClass();
                csx3.b = csw3;
                csx3.a |= 1;
                cwm cwm2 = (cwm) obj11;
                return ftd.K(cwm2.d.l(cxg.a, (csx) htk2.r()), new amv(20), cwm2.h);
        }
    }

    public /* synthetic */ cwa(Object obj, Object obj2, int i) {
        this.c = i;
        this.a = obj;
        this.b = obj2;
    }
}
