package defpackage;

import android.os.Looper;
import java.util.concurrent.ThreadFactory;

/* renamed from: cnu  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cnu implements ThreadFactory {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ cnu(int i) {
        this.b = i;
        this.a = "ExoPlayer:Loader:ProgressiveMediaPeriod";
    }

    /* JADX WARNING: type inference failed for: r3v1, types: [java.lang.Object, java.util.concurrent.ThreadFactory] */
    /* JADX WARNING: type inference failed for: r0v4, types: [java.lang.Object, java.util.concurrent.ThreadFactory] */
    public final Thread newThread(Runnable runnable) {
        int i = this.b;
        if (i == 0) {
            return this.a.newThread(new cmp(runnable, 2));
        } else if (i == 1) {
            return new Thread(runnable, (String) this.a);
        } else {
            Thread newThread = this.a.newThread(runnable);
            if (newThread.getContextClassLoader() == null) {
                newThread.setContextClassLoader(Looper.getMainLooper().getThread().getContextClassLoader());
            }
            return newThread;
        }
    }

    public /* synthetic */ cnu(ThreadFactory threadFactory, int i) {
        this.b = i;
        this.a = threadFactory;
    }
}
