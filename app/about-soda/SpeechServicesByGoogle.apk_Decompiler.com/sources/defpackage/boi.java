package defpackage;

import android.view.View;
import j$.util.DesugarArrays;
import java.util.function.Function;

/* renamed from: boi  reason: default package */
/* compiled from: PG */
public final class boi implements boj {
    public int[] a;
    private final View b;
    private int[] c;

    public boi(View view) {
        this.b = view;
    }

    public final void a(int[] iArr, int[] iArr2) {
        this.c = iArr;
        this.a = iArr2;
        this.b.refreshDrawableState();
    }

    public final int[] b(int i, Function function) {
        int[] iArr;
        int[] iArr2 = this.c;
        if (iArr2 == null) {
            iArr = (int[]) function.apply(Integer.valueOf(i));
        } else {
            iArr = (int[]) function.apply(Integer.valueOf(i + iArr2.length));
            int[] iArr3 = this.c;
            int length = iArr.length;
            do {
                length--;
                if (length < 0 || iArr[length] != 0) {
                    System.arraycopy(iArr3, 0, iArr, length + 1, iArr3.length);
                }
                length--;
                break;
            } while (iArr[length] != 0);
            System.arraycopy(iArr3, 0, iArr, length + 1, iArr3.length);
        }
        if (this.a != null) {
            return DesugarArrays.stream(iArr).filter(new boh(this)).toArray();
        }
        return iArr;
    }
}
