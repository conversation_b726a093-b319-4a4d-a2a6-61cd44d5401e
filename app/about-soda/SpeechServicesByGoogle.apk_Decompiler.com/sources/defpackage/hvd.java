package defpackage;

import androidx.preference.Preference;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import sun.misc.Unsafe;

/* renamed from: hvd  reason: default package */
/* compiled from: PG */
final class hvd implements hvp {
    public static final int[] a = new int[0];
    public static final Unsafe b = hwe.j();
    private final int[] c;
    private final Object[] d;
    private final int e;
    private final int f;
    private final hva g;
    private final boolean h;
    private final boolean i;
    private final int[] j;
    private final int k;
    private final int l;
    private final hvy m;

    public hvd(int[] iArr, Object[] objArr, int i2, int i3, hva hva, int[] iArr2, int i4, int i5, hvy hvy, hzz hzz) {
        this.c = iArr;
        this.d = objArr;
        this.e = i2;
        this.f = i3;
        this.i = hva instanceof htq;
        boolean z = false;
        if (hzz != null && (hva instanceof htn)) {
            z = true;
        }
        this.h = z;
        this.j = iArr2;
        this.k = i4;
        this.l = i5;
        this.m = hvy;
        this.g = hva;
    }

    private final Object A(Object obj, int i2, Object obj2, hvy hvy, Object obj3) {
        htu y;
        int p = p(i2);
        Object h2 = hwe.h(obj, w(v(i2)));
        if (h2 == null || (y = y(i2)) == null) {
            return obj2;
        }
        gnk aS = ftc.aS(B(i2));
        Iterator it = ((huv) h2).entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry entry = (Map.Entry) it.next();
            if (!y.a(((Integer) entry.getValue()).intValue())) {
                if (obj2 == null) {
                    obj2 = hvy.b(obj3);
                }
                int D = dku.D(aS, entry.getKey(), entry.getValue());
                hsq hsq = hsq.b;
                byte[] bArr = new byte[D];
                hsz ag = hsz.ag(bArr);
                try {
                    dku.E(ag, aS, entry.getKey(), entry.getValue());
                    hvy.c(obj2, p, hzz.B(ag, bArr));
                    it.remove();
                } catch (IOException e2) {
                    throw new RuntimeException(e2);
                }
            }
        }
        return obj2;
    }

    private final Object B(int i2) {
        int i3 = i2 / 3;
        return this.d[i3 + i3];
    }

    private final Object C(Object obj, int i2) {
        hvp z = z(i2);
        long w = w(v(i2));
        if (!N(obj, i2)) {
            return z.e();
        }
        Object object = b.getObject(obj, w);
        if (Q(object)) {
            return object;
        }
        Object e2 = z.e();
        if (object != null) {
            z.h(e2, object);
        }
        return e2;
    }

    private final Object D(Object obj, int i2, int i3) {
        hvp z = z(i3);
        if (!R(obj, i2, i3)) {
            return z.e();
        }
        Object object = b.getObject(obj, w(v(i3)));
        if (Q(object)) {
            return object;
        }
        Object e2 = z.e();
        if (object != null) {
            z.h(e2, object);
        }
        return e2;
    }

    private static void E(Object obj) {
        if (!Q(obj)) {
            throw new IllegalArgumentException("Mutating immutable message: ".concat(String.valueOf(String.valueOf(obj))));
        }
    }

    private final void F(Object obj, Object obj2, int i2) {
        if (N(obj2, i2)) {
            long w = w(v(i2));
            Unsafe unsafe = b;
            Object object = unsafe.getObject(obj2, w);
            if (object != null) {
                hvp z = z(i2);
                if (!N(obj, i2)) {
                    if (!Q(object)) {
                        unsafe.putObject(obj, w, object);
                    } else {
                        Object e2 = z.e();
                        z.h(e2, object);
                        unsafe.putObject(obj, w, e2);
                    }
                    H(obj, i2);
                    return;
                }
                Object object2 = unsafe.getObject(obj, w);
                if (!Q(object2)) {
                    Object e3 = z.e();
                    z.h(e3, object2);
                    unsafe.putObject(obj, w, e3);
                    object2 = e3;
                }
                z.h(object2, object);
                return;
            }
            int p = p(i2);
            String obj3 = obj2.toString();
            throw new IllegalStateException("Source subfield " + p + " is present but null: " + obj3);
        }
    }

    private final void G(Object obj, Object obj2, int i2) {
        int p = p(i2);
        if (R(obj2, p, i2)) {
            long w = w(v(i2));
            Unsafe unsafe = b;
            Object object = unsafe.getObject(obj2, w);
            if (object != null) {
                hvp z = z(i2);
                if (!R(obj, p, i2)) {
                    if (!Q(object)) {
                        unsafe.putObject(obj, w, object);
                    } else {
                        Object e2 = z.e();
                        z.h(e2, object);
                        unsafe.putObject(obj, w, e2);
                    }
                    I(obj, p, i2);
                    return;
                }
                Object object2 = unsafe.getObject(obj, w);
                if (!Q(object2)) {
                    Object e3 = z.e();
                    z.h(e3, object2);
                    unsafe.putObject(obj, w, e3);
                    object2 = e3;
                }
                z.h(object2, object);
                return;
            }
            int p2 = p(i2);
            String obj3 = obj2.toString();
            throw new IllegalStateException("Source subfield " + p2 + " is present but null: " + obj3);
        }
    }

    private final void H(Object obj, int i2) {
        int s = s(i2);
        long j2 = (long) (1048575 & s);
        if (j2 != 1048575) {
            hwe.s(obj, j2, (1 << (s >>> 20)) | hwe.d(obj, j2));
        }
    }

    private final void I(Object obj, int i2, int i3) {
        hwe.s(obj, (long) (s(i3) & 1048575), i2);
    }

    private final void J(Object obj, int i2, Object obj2) {
        b.putObject(obj, w(v(i2)), obj2);
        H(obj, i2);
    }

    private final void K(Object obj, int i2, int i3, Object obj2) {
        b.putObject(obj, w(v(i3)), obj2);
        I(obj, i2, i3);
    }

    private final boolean L(Object obj, Object obj2, int i2) {
        if (N(obj, i2) == N(obj2, i2)) {
            return true;
        }
        return false;
    }

    private static boolean M(int i2) {
        if ((i2 & 536870912) != 0) {
            return true;
        }
        return false;
    }

    private final boolean N(Object obj, int i2) {
        int s = s(i2);
        long j2 = (long) (1048575 & s);
        if (j2 == 1048575) {
            int v = v(i2);
            long w = w(v);
            switch (u(v)) {
                case 0:
                    if (Double.doubleToRawLongBits(hwe.b(obj, w)) != 0) {
                        return true;
                    }
                    return false;
                case 1:
                    if (Float.floatToRawIntBits(hwe.c(obj, w)) != 0) {
                        return true;
                    }
                    return false;
                case 2:
                    if (hwe.f(obj, w) != 0) {
                        return true;
                    }
                    return false;
                case 3:
                    if (hwe.f(obj, w) != 0) {
                        return true;
                    }
                    return false;
                case 4:
                    if (hwe.d(obj, w) != 0) {
                        return true;
                    }
                    return false;
                case 5:
                    if (hwe.f(obj, w) != 0) {
                        return true;
                    }
                    return false;
                case 6:
                    if (hwe.d(obj, w) != 0) {
                        return true;
                    }
                    return false;
                case 7:
                    return hwe.w(obj, w);
                case 8:
                    Object h2 = hwe.h(obj, w);
                    if (h2 instanceof String) {
                        if (!((String) h2).isEmpty()) {
                            return true;
                        }
                        return false;
                    } else if (!(h2 instanceof hsq)) {
                        throw new IllegalArgumentException();
                    } else if (!hsq.b.equals(h2)) {
                        return true;
                    } else {
                        return false;
                    }
                case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER:
                    if (hwe.h(obj, w) != null) {
                        return true;
                    }
                    return false;
                case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER:
                    if (!hsq.b.equals(hwe.h(obj, w))) {
                        return true;
                    }
                    return false;
                case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER:
                    if (hwe.d(obj, w) != 0) {
                        return true;
                    }
                    return false;
                case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER:
                    if (hwe.d(obj, w) != 0) {
                        return true;
                    }
                    return false;
                case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER:
                    if (hwe.d(obj, w) != 0) {
                        return true;
                    }
                    return false;
                case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER:
                    if (hwe.f(obj, w) != 0) {
                        return true;
                    }
                    return false;
                case 15:
                    if (hwe.d(obj, w) != 0) {
                        return true;
                    }
                    return false;
                case 16:
                    if (hwe.f(obj, w) != 0) {
                        return true;
                    }
                    return false;
                case 17:
                    if (hwe.h(obj, w) != null) {
                        return true;
                    }
                    return false;
                default:
                    throw new IllegalArgumentException();
            }
        } else {
            if ((hwe.d(obj, j2) & (1 << (s >>> 20))) != 0) {
                return true;
            }
            return false;
        }
    }

    private final boolean O(Object obj, int i2, int i3, int i4, int i5) {
        if (i3 == 1048575) {
            return N(obj, i2);
        }
        if ((i4 & i5) != 0) {
            return true;
        }
        return false;
    }

    private static boolean P(Object obj, int i2, hvp hvp) {
        return hvp.k(hwe.h(obj, w(i2)));
    }

    private static boolean Q(Object obj) {
        if (obj == null) {
            return false;
        }
        if (obj instanceof htq) {
            return ((htq) obj).B();
        }
        return true;
    }

    private final boolean R(Object obj, int i2, int i3) {
        if (hwe.d(obj, (long) (s(i3) & 1048575)) == i2) {
            return true;
        }
        return false;
    }

    private static boolean S(Object obj, long j2) {
        return ((Boolean) hwe.h(obj, j2)).booleanValue();
    }

    private final void T(Object obj, int i2, hsv hsv) {
        if (M(i2)) {
            hwe.u(obj, w(i2), hsv.v());
        } else if (this.i) {
            hwe.u(obj, w(i2), hsv.u());
        } else {
            hwe.u(obj, w(i2), hsv.o());
        }
    }

    private static final int U(byte[] bArr, int i2, int i3, hwh hwh, Class cls, hsf hsf) {
        int i4;
        boolean z;
        hwh hwh2 = hwh.DOUBLE;
        switch (hwh.ordinal()) {
            case 0:
                i4 = i2 + 8;
                hsf.c = Double.valueOf(hsg.a(bArr, i2));
                break;
            case 1:
                i4 = i2 + 4;
                hsf.c = Float.valueOf(hsg.b(bArr, i2));
                break;
            case 2:
            case 3:
                int o = hsg.o(bArr, i2, hsf);
                hsf.c = Long.valueOf(hsf.b);
                return o;
            case 4:
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER:
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER:
                int l2 = hsg.l(bArr, i2, hsf);
                hsf.c = Integer.valueOf(hsf.a);
                return l2;
            case 5:
            case 15:
                i4 = i2 + 8;
                hsf.c = Long.valueOf(hsg.s(bArr, i2));
                break;
            case 6:
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER:
                i4 = i2 + 4;
                hsf.c = Integer.valueOf(hsg.d(bArr, i2));
                break;
            case 7:
                int o2 = hsg.o(bArr, i2, hsf);
                if (hsf.b != 0) {
                    z = true;
                } else {
                    z = false;
                }
                hsf.c = Boolean.valueOf(z);
                return o2;
            case 8:
                return hsg.j(bArr, i2, hsf);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER:
                return hsg.f(hvj.a.a(cls), bArr, i2, i3, hsf);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER:
                return hsg.c(bArr, i2, hsf);
            case 16:
                int l3 = hsg.l(bArr, i2, hsf);
                hsf.c = Integer.valueOf(hsu.H(hsf.a));
                return l3;
            case 17:
                int o3 = hsg.o(bArr, i2, hsf);
                hsf.c = Long.valueOf(hsu.J(hsf.b));
                return o3;
            default:
                throw new RuntimeException("unsupported field type.");
        }
        return i4;
    }

    private static final void V(int i2, Object obj, cxi cxi) {
        if (obj instanceof String) {
            cxi.I(i2, (String) obj);
        } else {
            cxi.t(i2, (hsq) obj);
        }
    }

    static hvz d(Object obj) {
        htq htq = (htq) obj;
        hvz hvz = htq.unknownFields;
        if (hvz != hvz.a) {
            return hvz;
        }
        hvz hvz2 = new hvz();
        htq.unknownFields = hvz2;
        return hvz2;
    }

    public static Field f(Class cls, String str) {
        try {
            return cls.getDeclaredField(str);
        } catch (NoSuchFieldException unused) {
            Field[] declaredFields = cls.getDeclaredFields();
            for (Field field : declaredFields) {
                if (str.equals(field.getName())) {
                    return field;
                }
            }
            throw new RuntimeException("Field " + str + " for " + cls.getName() + " not found. Known fields are " + Arrays.toString(declaredFields));
        }
    }

    private static double n(Object obj, long j2) {
        return ((Double) hwe.h(obj, j2)).doubleValue();
    }

    private static float o(Object obj, long j2) {
        return ((Float) hwe.h(obj, j2)).floatValue();
    }

    private final int p(int i2) {
        return this.c[i2];
    }

    private static int q(Object obj, long j2) {
        return ((Integer) hwe.h(obj, j2)).intValue();
    }

    private final int r(int i2) {
        if (i2 < this.e || i2 > this.f) {
            return -1;
        }
        return t(i2, 0);
    }

    private final int s(int i2) {
        return this.c[i2 + 2];
    }

    private final int t(int i2, int i3) {
        int length = (this.c.length / 3) - 1;
        while (i3 <= length) {
            int i4 = (length + i3) >>> 1;
            int i5 = i4 * 3;
            int p = p(i5);
            if (i2 == p) {
                return i5;
            }
            if (i2 < p) {
                length = i4 - 1;
            } else {
                i3 = i4 + 1;
            }
        }
        return -1;
    }

    private static int u(int i2) {
        return (i2 >>> 20) & 255;
    }

    private final int v(int i2) {
        return this.c[i2 + 1];
    }

    private static long w(int i2) {
        return (long) (i2 & 1048575);
    }

    private static long x(Object obj, long j2) {
        return ((Long) hwe.h(obj, j2)).longValue();
    }

    private final htu y(int i2) {
        int i3 = i2 / 3;
        return (htu) this.d[i3 + i3 + 1];
    }

    private final hvp z(int i2) {
        Object[] objArr = this.d;
        int i3 = i2 / 3;
        int i4 = i3 + i3;
        hvp hvp = (hvp) objArr[i4];
        if (hvp != null) {
            return hvp;
        }
        hvp a2 = hvj.a.a((Class) objArr[i4 + 1]);
        this.d[i4] = a2;
        return a2;
    }

    /* JADX WARNING: Can't fix incorrect switch cases order */
    /* JADX WARNING: Code restructure failed: missing block: B:128:0x0310, code lost:
        r1 = r1 + r2;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:129:0x0311, code lost:
        r1 = r1 + r0;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:130:0x0312, code lost:
        r12 = r12 + r1;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:135:0x032e, code lost:
        r0 = r0 + (r1 * r2);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:149:0x03a0, code lost:
        r1 = 0;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:167:0x0403, code lost:
        r2 = 0;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:184:0x0448, code lost:
        r12 = r12 + r2;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:201:0x04e1, code lost:
        r12 = r12 + r0;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:202:0x04e2, code lost:
        r17 = r11;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:260:0x06c2, code lost:
        r12 = r12 + r0;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:261:0x06c3, code lost:
        r11 = r17 + 3;
        r0 = r14;
        r1 = r15;
        r10 = 1048575;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final int a(java.lang.Object r19) {
        /*
            r18 = this;
            r6 = r18
            r7 = r19
            sun.misc.Unsafe r8 = b
            r10 = 1048575(0xfffff, float:1.469367E-39)
            r0 = r10
            r1 = 0
            r11 = 0
            r12 = 0
        L_0x000d:
            int[] r2 = r6.c
            int r2 = r2.length
            if (r11 >= r2) goto L_0x06cc
            int r2 = r6.v(r11)
            int r3 = u(r2)
            int r13 = r6.p(r11)
            int[] r4 = r6.c
            int r5 = r11 + 2
            r4 = r4[r5]
            r5 = r4 & r10
            r14 = 17
            if (r3 > r14) goto L_0x0040
            if (r5 == r0) goto L_0x0037
            if (r5 != r10) goto L_0x0030
            r0 = 0
            goto L_0x0035
        L_0x0030:
            long r0 = (long) r5
            int r0 = r8.getInt(r7, r0)
        L_0x0035:
            r1 = r0
            r0 = r5
        L_0x0037:
            int r4 = r4 >>> 20
            r5 = 1
            int r4 = r5 << r4
            r14 = r0
            r15 = r1
            r5 = r4
            goto L_0x0043
        L_0x0040:
            r14 = r0
            r15 = r1
            r5 = 0
        L_0x0043:
            long r1 = w(r2)
            hth r0 = defpackage.hth.DOUBLE_LIST_PACKED
            int r0 = r0.Z
            if (r3 < r0) goto L_0x0051
            hth r0 = defpackage.hth.SINT64_LIST_PACKED
            int r0 = r0.Z
        L_0x0051:
            switch(r3) {
                case 0: goto L_0x06ae;
                case 1: goto L_0x0699;
                case 2: goto L_0x067f;
                case 3: goto L_0x0665;
                case 4: goto L_0x064a;
                case 5: goto L_0x0634;
                case 6: goto L_0x061e;
                case 7: goto L_0x0608;
                case 8: goto L_0x05df;
                case 9: goto L_0x05be;
                case 10: goto L_0x05a1;
                case 11: goto L_0x0586;
                case 12: goto L_0x056b;
                case 13: goto L_0x0555;
                case 14: goto L_0x053f;
                case 15: goto L_0x0524;
                case 16: goto L_0x0509;
                case 17: goto L_0x04e6;
                case 18: goto L_0x04d7;
                case 19: goto L_0x04cc;
                case 20: goto L_0x04ad;
                case 21: goto L_0x0493;
                case 22: goto L_0x0479;
                case 23: goto L_0x046e;
                case 24: goto L_0x0462;
                case 25: goto L_0x044b;
                case 26: goto L_0x03f5;
                case 27: goto L_0x03bd;
                case 28: goto L_0x0392;
                case 29: goto L_0x037a;
                case 30: goto L_0x0362;
                case 31: goto L_0x0356;
                case 32: goto L_0x034a;
                case 33: goto L_0x0332;
                case 34: goto L_0x0315;
                case 35: goto L_0x02fc;
                case 36: goto L_0x02e7;
                case 37: goto L_0x02d2;
                case 38: goto L_0x02bd;
                case 39: goto L_0x02a8;
                case 40: goto L_0x0293;
                case 41: goto L_0x027d;
                case 42: goto L_0x0265;
                case 43: goto L_0x024f;
                case 44: goto L_0x0239;
                case 45: goto L_0x0223;
                case 46: goto L_0x020d;
                case 47: goto L_0x01f7;
                case 48: goto L_0x01e1;
                case 49: goto L_0x01b8;
                case 50: goto L_0x0174;
                case 51: goto L_0x0168;
                case 52: goto L_0x015c;
                case 53: goto L_0x014c;
                case 54: goto L_0x013c;
                case 55: goto L_0x012c;
                case 56: goto L_0x0120;
                case 57: goto L_0x0114;
                case 58: goto L_0x0108;
                case 59: goto L_0x00ea;
                case 60: goto L_0x00d6;
                case 61: goto L_0x00c4;
                case 62: goto L_0x00b4;
                case 63: goto L_0x00a4;
                case 64: goto L_0x0098;
                case 65: goto L_0x008c;
                case 66: goto L_0x007c;
                case 67: goto L_0x006c;
                case 68: goto L_0x0056;
                default: goto L_0x0054;
            }
        L_0x0054:
            goto L_0x04e2
        L_0x0056:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            java.lang.Object r0 = r8.getObject(r7, r1)
            hva r0 = (defpackage.hva) r0
            hvp r1 = r6.z(r11)
            int r0 = defpackage.hsz.J(r13, r0, r1)
            goto L_0x04e1
        L_0x006c:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            long r0 = x(r7, r1)
            int r0 = defpackage.hsz.V(r13, r0)
            goto L_0x04e1
        L_0x007c:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            int r0 = q(r7, r1)
            int r0 = defpackage.hsz.T(r13, r0)
            goto L_0x04e1
        L_0x008c:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            int r0 = defpackage.hsz.ay(r13)
            goto L_0x04e1
        L_0x0098:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            int r0 = defpackage.hsz.ax(r13)
            goto L_0x04e1
        L_0x00a4:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            int r0 = q(r7, r1)
            int r0 = defpackage.hsz.I(r13, r0)
            goto L_0x04e1
        L_0x00b4:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            int r0 = q(r7, r1)
            int r0 = defpackage.hsz.aa(r13, r0)
            goto L_0x04e1
        L_0x00c4:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            java.lang.Object r0 = r8.getObject(r7, r1)
            hsq r0 = (defpackage.hsq) r0
            int r0 = defpackage.hsz.G(r13, r0)
            goto L_0x04e1
        L_0x00d6:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            java.lang.Object r0 = r8.getObject(r7, r1)
            hvp r1 = r6.z(r11)
            int r0 = defpackage.hvq.c(r13, r0, r1)
            goto L_0x04e1
        L_0x00ea:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            java.lang.Object r0 = r8.getObject(r7, r1)
            boolean r1 = r0 instanceof defpackage.hsq
            if (r1 == 0) goto L_0x0100
            hsq r0 = (defpackage.hsq) r0
            int r0 = defpackage.hsz.G(r13, r0)
            goto L_0x04e1
        L_0x0100:
            java.lang.String r0 = (java.lang.String) r0
            int r0 = defpackage.hsz.X(r13, r0)
            goto L_0x04e1
        L_0x0108:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            int r0 = defpackage.hsz.as(r13)
            goto L_0x04e1
        L_0x0114:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            int r0 = defpackage.hsz.au(r13)
            goto L_0x04e1
        L_0x0120:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            int r0 = defpackage.hsz.av(r13)
            goto L_0x04e1
        L_0x012c:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            int r0 = q(r7, r1)
            int r0 = defpackage.hsz.K(r13, r0)
            goto L_0x04e1
        L_0x013c:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            long r0 = x(r7, r1)
            int r0 = defpackage.hsz.ac(r13, r0)
            goto L_0x04e1
        L_0x014c:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            long r0 = x(r7, r1)
            int r0 = defpackage.hsz.M(r13, r0)
            goto L_0x04e1
        L_0x015c:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            int r0 = defpackage.hsz.aw(r13)
            goto L_0x04e1
        L_0x0168:
            boolean r0 = r6.R(r7, r13, r11)
            if (r0 == 0) goto L_0x04e2
            int r0 = defpackage.hsz.at(r13)
            goto L_0x04e1
        L_0x0174:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.lang.Object r1 = r6.B(r11)
            huv r0 = (defpackage.huv) r0
            dku r1 = (defpackage.dku) r1
            boolean r2 = r0.isEmpty()
            if (r2 == 0) goto L_0x0188
            goto L_0x0403
        L_0x0188:
            java.util.Set r0 = r0.entrySet()
            java.util.Iterator r0 = r0.iterator()
            r2 = 0
        L_0x0191:
            boolean r3 = r0.hasNext()
            if (r3 == 0) goto L_0x0448
            java.lang.Object r3 = r0.next()
            java.util.Map$Entry r3 = (java.util.Map.Entry) r3
            java.lang.Object r4 = r3.getKey()
            java.lang.Object r3 = r3.getValue()
            int r5 = defpackage.hsz.Z(r13)
            java.lang.Object r9 = r1.a
            gnk r9 = (defpackage.gnk) r9
            int r3 = defpackage.dku.D(r9, r4, r3)
            int r3 = defpackage.hsz.P(r3)
            int r5 = r5 + r3
            int r2 = r2 + r5
            goto L_0x0191
        L_0x01b8:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            hvp r1 = r6.z(r11)
            hvy r2 = defpackage.hvq.a
            int r2 = r0.size()
            if (r2 != 0) goto L_0x01cc
            r4 = 0
            goto L_0x01de
        L_0x01cc:
            r3 = 0
            r4 = 0
        L_0x01ce:
            if (r3 >= r2) goto L_0x01de
            java.lang.Object r5 = r0.get(r3)
            hva r5 = (defpackage.hva) r5
            int r5 = defpackage.hsz.J(r13, r5, r1)
            int r4 = r4 + r5
            int r3 = r3 + 1
            goto L_0x01ce
        L_0x01de:
            int r12 = r12 + r4
            goto L_0x04e2
        L_0x01e1:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.e(r0)
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
            goto L_0x0310
        L_0x01f7:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.d(r0)
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
            goto L_0x0310
        L_0x020d:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.b(r0)
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
            goto L_0x0310
        L_0x0223:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.a(r0)
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
            goto L_0x0310
        L_0x0239:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.k(r0)
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
            goto L_0x0310
        L_0x024f:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.f(r0)
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
            goto L_0x0310
        L_0x0265:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            hvy r1 = defpackage.hvq.a
            int r0 = r0.size()
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
            goto L_0x0310
        L_0x027d:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.a(r0)
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
            goto L_0x0310
        L_0x0293:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.b(r0)
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
            goto L_0x0310
        L_0x02a8:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.k(r0)
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
            goto L_0x0310
        L_0x02bd:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.l(r0)
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
            goto L_0x0310
        L_0x02d2:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.l(r0)
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
            goto L_0x0310
        L_0x02e7:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.a(r0)
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
            goto L_0x0310
        L_0x02fc:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.b(r0)
            if (r0 <= 0) goto L_0x04e2
            int r1 = defpackage.hsz.Z(r13)
            int r2 = defpackage.hsz.ab(r0)
        L_0x0310:
            int r1 = r1 + r2
        L_0x0311:
            int r1 = r1 + r0
        L_0x0312:
            int r12 = r12 + r1
            goto L_0x04e2
        L_0x0315:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            hvy r1 = defpackage.hvq.a
            int r1 = r0.size()
            if (r1 != 0) goto L_0x0326
        L_0x0323:
            r0 = 0
            goto L_0x04e1
        L_0x0326:
            int r0 = defpackage.hvq.e(r0)
            int r2 = defpackage.hsz.Z(r13)
        L_0x032e:
            int r1 = r1 * r2
            int r0 = r0 + r1
            goto L_0x04e1
        L_0x0332:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            hvy r1 = defpackage.hvq.a
            int r1 = r0.size()
            if (r1 != 0) goto L_0x0341
            goto L_0x0323
        L_0x0341:
            int r0 = defpackage.hvq.d(r0)
            int r2 = defpackage.hsz.Z(r13)
            goto L_0x032e
        L_0x034a:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.j(r13, r0)
            goto L_0x04e1
        L_0x0356:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.i(r13, r0)
            goto L_0x04e1
        L_0x0362:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            hvy r1 = defpackage.hvq.a
            int r1 = r0.size()
            if (r1 != 0) goto L_0x0371
            goto L_0x0323
        L_0x0371:
            int r0 = defpackage.hvq.k(r0)
            int r2 = defpackage.hsz.Z(r13)
            goto L_0x032e
        L_0x037a:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            hvy r1 = defpackage.hvq.a
            int r1 = r0.size()
            if (r1 != 0) goto L_0x0389
            goto L_0x0323
        L_0x0389:
            int r0 = defpackage.hvq.f(r0)
            int r2 = defpackage.hsz.Z(r13)
            goto L_0x032e
        L_0x0392:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            hvy r1 = defpackage.hvq.a
            int r1 = r0.size()
            if (r1 != 0) goto L_0x03a3
        L_0x03a0:
            r1 = 0
            goto L_0x0312
        L_0x03a3:
            int r2 = defpackage.hsz.Z(r13)
            int r1 = r1 * r2
            r2 = 0
        L_0x03a9:
            int r3 = r0.size()
            if (r2 >= r3) goto L_0x0312
            java.lang.Object r3 = r0.get(r2)
            hsq r3 = (defpackage.hsq) r3
            int r3 = defpackage.hsz.H(r3)
            int r1 = r1 + r3
            int r2 = r2 + 1
            goto L_0x03a9
        L_0x03bd:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            hvp r1 = r6.z(r11)
            hvy r2 = defpackage.hvq.a
            int r2 = r0.size()
            if (r2 != 0) goto L_0x03d1
            r3 = 0
            goto L_0x03f2
        L_0x03d1:
            int r3 = defpackage.hsz.Z(r13)
            int r3 = r3 * r2
            r4 = 0
        L_0x03d7:
            if (r4 >= r2) goto L_0x03f2
            java.lang.Object r5 = r0.get(r4)
            boolean r9 = r5 instanceof defpackage.huo
            if (r9 == 0) goto L_0x03e8
            huo r5 = (defpackage.huo) r5
            int r5 = defpackage.hsz.O(r5)
            goto L_0x03ee
        L_0x03e8:
            hva r5 = (defpackage.hva) r5
            int r5 = defpackage.hsz.R(r5, r1)
        L_0x03ee:
            int r3 = r3 + r5
            int r4 = r4 + 1
            goto L_0x03d7
        L_0x03f2:
            int r12 = r12 + r3
            goto L_0x04e2
        L_0x03f5:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            hvy r1 = defpackage.hvq.a
            int r1 = r0.size()
            if (r1 != 0) goto L_0x0405
        L_0x0403:
            r2 = 0
            goto L_0x0448
        L_0x0405:
            int r2 = defpackage.hsz.Z(r13)
            int r2 = r2 * r1
            boolean r3 = r0 instanceof defpackage.hup
            if (r3 == 0) goto L_0x042c
            hup r0 = (defpackage.hup) r0
            r3 = 0
        L_0x0411:
            if (r3 >= r1) goto L_0x0448
            java.lang.Object r4 = r0.c()
            boolean r5 = r4 instanceof defpackage.hsq
            if (r5 == 0) goto L_0x0422
            hsq r4 = (defpackage.hsq) r4
            int r4 = defpackage.hsz.H(r4)
            goto L_0x0428
        L_0x0422:
            java.lang.String r4 = (java.lang.String) r4
            int r4 = defpackage.hsz.Y(r4)
        L_0x0428:
            int r2 = r2 + r4
            int r3 = r3 + 1
            goto L_0x0411
        L_0x042c:
            r3 = 0
        L_0x042d:
            if (r3 >= r1) goto L_0x0448
            java.lang.Object r4 = r0.get(r3)
            boolean r5 = r4 instanceof defpackage.hsq
            if (r5 == 0) goto L_0x043e
            hsq r4 = (defpackage.hsq) r4
            int r4 = defpackage.hsz.H(r4)
            goto L_0x0444
        L_0x043e:
            java.lang.String r4 = (java.lang.String) r4
            int r4 = defpackage.hsz.Y(r4)
        L_0x0444:
            int r2 = r2 + r4
            int r3 = r3 + 1
            goto L_0x042d
        L_0x0448:
            int r12 = r12 + r2
            goto L_0x04e2
        L_0x044b:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            hvy r1 = defpackage.hvq.a
            int r0 = r0.size()
            if (r0 != 0) goto L_0x045b
            goto L_0x0323
        L_0x045b:
            int r1 = defpackage.hsz.as(r13)
            int r0 = r0 * r1
            goto L_0x04e1
        L_0x0462:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.i(r13, r0)
            goto L_0x04e1
        L_0x046e:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.j(r13, r0)
            goto L_0x04e1
        L_0x0479:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            hvy r1 = defpackage.hvq.a
            int r1 = r0.size()
            if (r1 != 0) goto L_0x0489
            goto L_0x0323
        L_0x0489:
            int r0 = defpackage.hvq.k(r0)
            int r2 = defpackage.hsz.Z(r13)
            goto L_0x032e
        L_0x0493:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            hvy r1 = defpackage.hvq.a
            int r1 = r0.size()
            if (r1 != 0) goto L_0x04a3
            goto L_0x0323
        L_0x04a3:
            int r0 = defpackage.hvq.l(r0)
            int r2 = defpackage.hsz.Z(r13)
            goto L_0x032e
        L_0x04ad:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            hvy r1 = defpackage.hvq.a
            int r1 = r0.size()
            if (r1 != 0) goto L_0x04bd
            goto L_0x03a0
        L_0x04bd:
            int r1 = defpackage.hvq.l(r0)
            int r0 = r0.size()
            int r2 = defpackage.hsz.Z(r13)
            int r0 = r0 * r2
            goto L_0x0311
        L_0x04cc:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.i(r13, r0)
            goto L_0x04e1
        L_0x04d7:
            java.lang.Object r0 = r8.getObject(r7, r1)
            java.util.List r0 = (java.util.List) r0
            int r0 = defpackage.hvq.j(r13, r0)
        L_0x04e1:
            int r12 = r12 + r0
        L_0x04e2:
            r17 = r11
            goto L_0x06c3
        L_0x04e6:
            r0 = r18
            r3 = r1
            r1 = r19
            r2 = r11
            r17 = r11
            r10 = r3
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            java.lang.Object r0 = r8.getObject(r7, r10)
            hva r0 = (defpackage.hva) r0
            r4 = r17
            hvp r1 = r6.z(r4)
            int r0 = defpackage.hsz.J(r13, r0, r1)
            goto L_0x06c2
        L_0x0509:
            r4 = r11
            r10 = r1
            r0 = r18
            r1 = r19
            r2 = r4
            r3 = r14
            r17 = r4
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            long r0 = r8.getLong(r7, r10)
            int r0 = defpackage.hsz.V(r13, r0)
            goto L_0x06c2
        L_0x0524:
            r17 = r11
            r10 = r1
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            int r0 = r8.getInt(r7, r10)
            int r0 = defpackage.hsz.T(r13, r0)
            goto L_0x06c2
        L_0x053f:
            r17 = r11
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            int r0 = defpackage.hsz.ay(r13)
            goto L_0x06c2
        L_0x0555:
            r17 = r11
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            int r0 = defpackage.hsz.ax(r13)
            goto L_0x06c2
        L_0x056b:
            r17 = r11
            r10 = r1
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            int r0 = r8.getInt(r7, r10)
            int r0 = defpackage.hsz.I(r13, r0)
            goto L_0x06c2
        L_0x0586:
            r17 = r11
            r10 = r1
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            int r0 = r8.getInt(r7, r10)
            int r0 = defpackage.hsz.aa(r13, r0)
            goto L_0x06c2
        L_0x05a1:
            r17 = r11
            r10 = r1
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            java.lang.Object r0 = r8.getObject(r7, r10)
            hsq r0 = (defpackage.hsq) r0
            int r0 = defpackage.hsz.G(r13, r0)
            goto L_0x06c2
        L_0x05be:
            r17 = r11
            r10 = r1
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            java.lang.Object r0 = r8.getObject(r7, r10)
            r4 = r17
            hvp r1 = r6.z(r4)
            int r0 = defpackage.hvq.c(r13, r0, r1)
            goto L_0x06c2
        L_0x05df:
            r4 = r11
            r10 = r1
            r0 = r18
            r1 = r19
            r2 = r4
            r3 = r14
            r17 = r4
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            java.lang.Object r0 = r8.getObject(r7, r10)
            boolean r1 = r0 instanceof defpackage.hsq
            if (r1 == 0) goto L_0x0600
            hsq r0 = (defpackage.hsq) r0
            int r0 = defpackage.hsz.G(r13, r0)
            goto L_0x06c2
        L_0x0600:
            java.lang.String r0 = (java.lang.String) r0
            int r0 = defpackage.hsz.X(r13, r0)
            goto L_0x06c2
        L_0x0608:
            r17 = r11
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            int r0 = defpackage.hsz.as(r13)
            goto L_0x06c2
        L_0x061e:
            r17 = r11
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            int r0 = defpackage.hsz.au(r13)
            goto L_0x06c2
        L_0x0634:
            r17 = r11
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            int r0 = defpackage.hsz.av(r13)
            goto L_0x06c2
        L_0x064a:
            r17 = r11
            r10 = r1
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            int r0 = r8.getInt(r7, r10)
            int r0 = defpackage.hsz.K(r13, r0)
            goto L_0x06c2
        L_0x0665:
            r17 = r11
            r10 = r1
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            long r0 = r8.getLong(r7, r10)
            int r0 = defpackage.hsz.ac(r13, r0)
            goto L_0x06c2
        L_0x067f:
            r17 = r11
            r10 = r1
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            long r0 = r8.getLong(r7, r10)
            int r0 = defpackage.hsz.M(r13, r0)
            goto L_0x06c2
        L_0x0699:
            r17 = r11
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            int r0 = defpackage.hsz.aw(r13)
            goto L_0x06c2
        L_0x06ae:
            r17 = r11
            r0 = r18
            r1 = r19
            r2 = r17
            r3 = r14
            r4 = r15
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x06c3
            int r0 = defpackage.hsz.at(r13)
        L_0x06c2:
            int r12 = r12 + r0
        L_0x06c3:
            int r11 = r17 + 3
            r0 = r14
            r1 = r15
            r10 = 1048575(0xfffff, float:1.469367E-39)
            goto L_0x000d
        L_0x06cc:
            hvz r0 = defpackage.hvy.f(r19)
            int r0 = r0.a()
            int r12 = r12 + r0
            boolean r0 = r6.h
            if (r0 == 0) goto L_0x0726
            htg r0 = defpackage.hzz.x(r19)
            hvu r1 = r0.b
            int r1 = r1.b
            r9 = 0
            r16 = 0
        L_0x06e4:
            if (r9 >= r1) goto L_0x06fd
            hvu r2 = r0.b
            java.util.Map$Entry r2 = r2.d(r9)
            hvr r2 = (defpackage.hvr) r2
            java.lang.Comparable r3 = r2.a
            htp r3 = (defpackage.htp) r3
            java.lang.Object r2 = r2.b
            int r2 = defpackage.htg.j(r3, r2)
            int r16 = r16 + r2
            int r9 = r9 + 1
            goto L_0x06e4
        L_0x06fd:
            hvu r0 = r0.b
            java.lang.Iterable r0 = r0.a()
            java.util.Iterator r0 = r0.iterator()
        L_0x0707:
            boolean r1 = r0.hasNext()
            if (r1 == 0) goto L_0x0724
            java.lang.Object r1 = r0.next()
            java.util.Map$Entry r1 = (java.util.Map.Entry) r1
            java.lang.Object r2 = r1.getKey()
            htp r2 = (defpackage.htp) r2
            java.lang.Object r1 = r1.getValue()
            int r1 = defpackage.htg.j(r2, r1)
            int r16 = r16 + r1
            goto L_0x0707
        L_0x0724:
            int r12 = r12 + r16
        L_0x0726:
            return r12
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.hvd.a(java.lang.Object):int");
    }

    /* JADX WARNING: Can't fix incorrect switch cases order */
    /* JADX WARNING: Code restructure failed: missing block: B:74:0x01c5, code lost:
        r1 = r1 + r6;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:84:0x0227, code lost:
        r1 = r1 + r2;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:85:0x0228, code lost:
        r0 = r0 + 3;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final int b(java.lang.Object r8) {
        /*
            r7 = this;
            r0 = 0
            r1 = r0
        L_0x0002:
            int[] r2 = r7.c
            int r2 = r2.length
            if (r0 >= r2) goto L_0x022c
            int r2 = r7.v(r0)
            int r3 = r7.p(r0)
            long r4 = w(r2)
            int r2 = u(r2)
            r6 = 37
            switch(r2) {
                case 0: goto L_0x0219;
                case 1: goto L_0x020e;
                case 2: goto L_0x0203;
                case 3: goto L_0x01f8;
                case 4: goto L_0x01f1;
                case 5: goto L_0x01e6;
                case 6: goto L_0x01df;
                case 7: goto L_0x01d4;
                case 8: goto L_0x01c7;
                case 9: goto L_0x01b9;
                case 10: goto L_0x01ad;
                case 11: goto L_0x01a5;
                case 12: goto L_0x019d;
                case 13: goto L_0x0195;
                case 14: goto L_0x0189;
                case 15: goto L_0x0181;
                case 16: goto L_0x0175;
                case 17: goto L_0x0168;
                case 18: goto L_0x015c;
                case 19: goto L_0x015c;
                case 20: goto L_0x015c;
                case 21: goto L_0x015c;
                case 22: goto L_0x015c;
                case 23: goto L_0x015c;
                case 24: goto L_0x015c;
                case 25: goto L_0x015c;
                case 26: goto L_0x015c;
                case 27: goto L_0x015c;
                case 28: goto L_0x015c;
                case 29: goto L_0x015c;
                case 30: goto L_0x015c;
                case 31: goto L_0x015c;
                case 32: goto L_0x015c;
                case 33: goto L_0x015c;
                case 34: goto L_0x015c;
                case 35: goto L_0x015c;
                case 36: goto L_0x015c;
                case 37: goto L_0x015c;
                case 38: goto L_0x015c;
                case 39: goto L_0x015c;
                case 40: goto L_0x015c;
                case 41: goto L_0x015c;
                case 42: goto L_0x015c;
                case 43: goto L_0x015c;
                case 44: goto L_0x015c;
                case 45: goto L_0x015c;
                case 46: goto L_0x015c;
                case 47: goto L_0x015c;
                case 48: goto L_0x015c;
                case 49: goto L_0x015c;
                case 50: goto L_0x0150;
                case 51: goto L_0x013a;
                case 52: goto L_0x0128;
                case 53: goto L_0x0116;
                case 54: goto L_0x0104;
                case 55: goto L_0x00f6;
                case 56: goto L_0x00e4;
                case 57: goto L_0x00d6;
                case 58: goto L_0x00c4;
                case 59: goto L_0x00b0;
                case 60: goto L_0x009e;
                case 61: goto L_0x008c;
                case 62: goto L_0x007e;
                case 63: goto L_0x0070;
                case 64: goto L_0x0062;
                case 65: goto L_0x0050;
                case 66: goto L_0x0042;
                case 67: goto L_0x0030;
                case 68: goto L_0x001e;
                default: goto L_0x001c;
            }
        L_0x001c:
            goto L_0x0228
        L_0x001e:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            java.lang.Object r2 = defpackage.hwe.h(r8, r4)
            int r2 = r2.hashCode()
            goto L_0x0227
        L_0x0030:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            long r2 = x(r8, r4)
            int r2 = defpackage.hug.a(r2)
            goto L_0x0227
        L_0x0042:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            int r2 = q(r8, r4)
            goto L_0x0227
        L_0x0050:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            long r2 = x(r8, r4)
            int r2 = defpackage.hug.a(r2)
            goto L_0x0227
        L_0x0062:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            int r2 = q(r8, r4)
            goto L_0x0227
        L_0x0070:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            int r2 = q(r8, r4)
            goto L_0x0227
        L_0x007e:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            int r2 = q(r8, r4)
            goto L_0x0227
        L_0x008c:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            java.lang.Object r2 = defpackage.hwe.h(r8, r4)
            int r2 = r2.hashCode()
            goto L_0x0227
        L_0x009e:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            java.lang.Object r2 = defpackage.hwe.h(r8, r4)
            int r2 = r2.hashCode()
            goto L_0x0227
        L_0x00b0:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            java.lang.Object r2 = defpackage.hwe.h(r8, r4)
            java.lang.String r2 = (java.lang.String) r2
            int r2 = r2.hashCode()
            goto L_0x0227
        L_0x00c4:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            boolean r2 = S(r8, r4)
            int r2 = defpackage.a.f(r2)
            goto L_0x0227
        L_0x00d6:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            int r2 = q(r8, r4)
            goto L_0x0227
        L_0x00e4:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            long r2 = x(r8, r4)
            int r2 = defpackage.hug.a(r2)
            goto L_0x0227
        L_0x00f6:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            int r2 = q(r8, r4)
            goto L_0x0227
        L_0x0104:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            long r2 = x(r8, r4)
            int r2 = defpackage.hug.a(r2)
            goto L_0x0227
        L_0x0116:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            long r2 = x(r8, r4)
            int r2 = defpackage.hug.a(r2)
            goto L_0x0227
        L_0x0128:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            float r2 = o(r8, r4)
            int r2 = java.lang.Float.floatToIntBits(r2)
            goto L_0x0227
        L_0x013a:
            boolean r2 = r7.R(r8, r3, r0)
            if (r2 == 0) goto L_0x0228
            int r1 = r1 * 53
            double r2 = n(r8, r4)
            long r2 = java.lang.Double.doubleToLongBits(r2)
            int r2 = defpackage.hug.a(r2)
            goto L_0x0227
        L_0x0150:
            int r1 = r1 * 53
            java.lang.Object r2 = defpackage.hwe.h(r8, r4)
            int r2 = r2.hashCode()
            goto L_0x0227
        L_0x015c:
            int r1 = r1 * 53
            java.lang.Object r2 = defpackage.hwe.h(r8, r4)
            int r2 = r2.hashCode()
            goto L_0x0227
        L_0x0168:
            int r1 = r1 * 53
            java.lang.Object r2 = defpackage.hwe.h(r8, r4)
            if (r2 == 0) goto L_0x01c5
            int r6 = r2.hashCode()
            goto L_0x01c5
        L_0x0175:
            int r1 = r1 * 53
            long r2 = defpackage.hwe.f(r8, r4)
            int r2 = defpackage.hug.a(r2)
            goto L_0x0227
        L_0x0181:
            int r1 = r1 * 53
            int r2 = defpackage.hwe.d(r8, r4)
            goto L_0x0227
        L_0x0189:
            int r1 = r1 * 53
            long r2 = defpackage.hwe.f(r8, r4)
            int r2 = defpackage.hug.a(r2)
            goto L_0x0227
        L_0x0195:
            int r1 = r1 * 53
            int r2 = defpackage.hwe.d(r8, r4)
            goto L_0x0227
        L_0x019d:
            int r1 = r1 * 53
            int r2 = defpackage.hwe.d(r8, r4)
            goto L_0x0227
        L_0x01a5:
            int r1 = r1 * 53
            int r2 = defpackage.hwe.d(r8, r4)
            goto L_0x0227
        L_0x01ad:
            int r1 = r1 * 53
            java.lang.Object r2 = defpackage.hwe.h(r8, r4)
            int r2 = r2.hashCode()
            goto L_0x0227
        L_0x01b9:
            int r1 = r1 * 53
            java.lang.Object r2 = defpackage.hwe.h(r8, r4)
            if (r2 == 0) goto L_0x01c5
            int r6 = r2.hashCode()
        L_0x01c5:
            int r1 = r1 + r6
            goto L_0x0228
        L_0x01c7:
            int r1 = r1 * 53
            java.lang.Object r2 = defpackage.hwe.h(r8, r4)
            java.lang.String r2 = (java.lang.String) r2
            int r2 = r2.hashCode()
            goto L_0x0227
        L_0x01d4:
            int r1 = r1 * 53
            boolean r2 = defpackage.hwe.w(r8, r4)
            int r2 = defpackage.a.f(r2)
            goto L_0x0227
        L_0x01df:
            int r1 = r1 * 53
            int r2 = defpackage.hwe.d(r8, r4)
            goto L_0x0227
        L_0x01e6:
            int r1 = r1 * 53
            long r2 = defpackage.hwe.f(r8, r4)
            int r2 = defpackage.hug.a(r2)
            goto L_0x0227
        L_0x01f1:
            int r1 = r1 * 53
            int r2 = defpackage.hwe.d(r8, r4)
            goto L_0x0227
        L_0x01f8:
            int r1 = r1 * 53
            long r2 = defpackage.hwe.f(r8, r4)
            int r2 = defpackage.hug.a(r2)
            goto L_0x0227
        L_0x0203:
            int r1 = r1 * 53
            long r2 = defpackage.hwe.f(r8, r4)
            int r2 = defpackage.hug.a(r2)
            goto L_0x0227
        L_0x020e:
            int r1 = r1 * 53
            float r2 = defpackage.hwe.c(r8, r4)
            int r2 = java.lang.Float.floatToIntBits(r2)
            goto L_0x0227
        L_0x0219:
            int r1 = r1 * 53
            double r2 = defpackage.hwe.b(r8, r4)
            long r2 = java.lang.Double.doubleToLongBits(r2)
            int r2 = defpackage.hug.a(r2)
        L_0x0227:
            int r1 = r1 + r2
        L_0x0228:
            int r0 = r0 + 3
            goto L_0x0002
        L_0x022c:
            int r1 = r1 * 53
            hvz r0 = defpackage.hvy.f(r8)
            int r0 = r0.hashCode()
            int r1 = r1 + r0
            boolean r0 = r7.h
            if (r0 == 0) goto L_0x0246
            int r1 = r1 * 53
            htg r8 = defpackage.hzz.x(r8)
            int r8 = r8.hashCode()
            int r1 = r1 + r8
        L_0x0246:
            return r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.hvd.b(java.lang.Object):int");
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v0, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v0, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v0, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v1, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v1, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v2, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v2, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v3, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v1, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v8, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v11, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v2, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v4, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v12, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v5, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v5, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v4, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v14, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v4, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v5, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v17, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v7, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v10, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v7, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v23, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v8, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v11, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v9, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v12, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v13, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v14, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v15, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v11, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v16, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v12, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v17, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v18, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v19, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v13, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v20, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v14, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v9, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v43, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v44, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v52, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v54, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v16, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v17, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v18, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v19, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v10, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v63, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v11, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v64, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r11v9, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v65, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v12, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v67, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v21, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v7, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v15, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v22, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v35, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v74, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v11, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v6, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v23, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v37, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v75, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v38, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v76, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v39, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v40, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v77, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v37, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v41, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v78, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v42, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v39, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v43, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v40, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v44, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v41, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v45, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v43, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v46, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v45, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v47, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v46, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v48, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v15, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v16, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v79, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v17, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v49, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v50, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v82, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v52, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v17, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v53, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v83, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v54, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v18, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v55, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v56, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v19, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v57, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v85, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v20, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v58, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v86, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v59, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v60, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v61, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v62, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v90, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v63, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v64, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v56, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v12, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v40, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v41, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v60, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v61, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v63, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v64, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v66, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r35v2, resolved type: byte} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v45, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v69, resolved type: byte} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v56, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v52, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v108, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v53, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v30, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v31, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v18, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v58, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v55, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v109, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v56, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v76, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v59, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v57, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v77, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v110, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v60, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v58, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v78, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v112, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v113, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v114, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v117, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v59, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v79, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v122, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v123, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v125, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v128, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v60, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v80, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v134, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v138, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v140, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v61, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v84, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v64, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v62, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v141, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v63, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v85, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v142, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v65, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v86, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v64, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v145, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v67, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v146, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v88, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v65, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v148, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v149, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v150, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v153, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v89, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v66, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v158, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v159, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v161, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v164, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v90, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v67, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v169, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v170, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v171, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v174, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v176, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v68, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v91, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v68, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v92, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v69, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v52, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v69, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v54, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v55, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v56, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v57, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v58, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v59, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v60, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v61, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v62, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v63, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v64, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v44, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v65, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r8v66, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v70, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v93, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v75, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v94, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v71, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v78, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v183, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r5v95, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v72, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v122, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v123, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v185, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v80, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v127, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v81, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v73, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r21v40, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v46, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v74, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v189, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v83, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v75, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v84, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v76, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v77, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v87, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v78, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v193, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v88, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v79, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v196, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v197, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v198, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v201, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v203, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v205, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v206, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v207, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v210, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v212, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v80, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r4v102, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r2v61, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r10v90, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v216, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v217, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v219, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v52, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v55, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v227, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v56, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v229, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v53, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v57, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v54, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v230, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v95, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v55, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v84, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v97, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v98, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v234, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v58, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v56, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v236, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v101, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v239, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v103, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v59, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v240, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v105, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v60, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v242, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v243, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v61, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v59, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v108, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v244, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v109, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r9v53, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v62, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v247, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v111, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v249, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v112, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v250, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v113, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v253, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v254, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v255, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v114, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v257, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v63, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v258, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v117, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v259, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v64, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v62, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v260, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v261, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v65, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v63, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v122, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v66, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v64, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v265, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v124, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r11v47, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v66, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v69, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v269, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v85, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r3v127, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v270, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r13v67, resolved type: int} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v86, resolved type: int} */
    /*  JADX ERROR: JadxOverflowException in pass: RegionMakerVisitor
        jadx.core.utils.exceptions.JadxOverflowException: Regions count limit reached
        	at jadx.core.utils.ErrorsCounter.addError(ErrorsCounter.java:47)
        	at jadx.core.utils.ErrorsCounter.methodError(ErrorsCounter.java:81)
        */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:213:0x05ca  */
    /* JADX WARNING: Removed duplicated region for block: B:615:0x0654 A[SYNTHETIC] */
    final int c(java.lang.Object r37, byte[] r38, int r39, int r40, int r41, defpackage.hsf r42) {
        /*
            r36 = this;
            r6 = r36
            r7 = r37
            r15 = r38
            r14 = r40
            r13 = r41
            r12 = r42
            E(r37)
            sun.misc.Unsafe r11 = b
            r16 = 0
            r0 = r39
            r2 = r16
            r3 = r2
            r4 = r3
            r1 = -1
            r5 = 1048575(0xfffff, float:1.469367E-39)
        L_0x001d:
            java.lang.String r8 = "Failed to parse the message."
            if (r0 >= r14) goto L_0x0edb
            int r3 = r0 + 1
            byte r0 = r15[r0]
            if (r0 >= 0) goto L_0x002e
            int r0 = defpackage.hsg.m(r0, r15, r3, r12)
            int r3 = r12.a
            goto L_0x0033
        L_0x002e:
            r35 = r3
            r3 = r0
            r0 = r35
        L_0x0033:
            int r10 = r3 >>> 3
            r9 = 3
            if (r10 <= r1) goto L_0x0048
            int r2 = r2 / r9
            int r1 = r6.e
            if (r10 < r1) goto L_0x0046
            int r1 = r6.f
            if (r10 > r1) goto L_0x0046
            int r1 = r6.t(r10, r2)
            goto L_0x004c
        L_0x0046:
            r1 = -1
            goto L_0x004c
        L_0x0048:
            int r1 = r6.r(r10)
        L_0x004c:
            r2 = r1
            r19 = 0
            r9 = -1
            if (r2 != r9) goto L_0x0065
            r18 = r4
            r27 = r5
            r21 = r8
            r17 = r9
            r6 = r10
            r33 = r11
            r28 = r16
            r5 = 1
            r10 = r7
            r7 = r13
        L_0x0062:
            r13 = r0
            goto L_0x0cc4
        L_0x0065:
            r9 = r3 & 7
            int[] r1 = r6.c
            int r23 = r2 + 1
            r24 = r3
            r3 = r1[r23]
            r23 = r8
            int r8 = u(r3)
            long r13 = w(r3)
            r25 = r10
            r10 = 17
            r26 = r3
            if (r8 > r10) goto L_0x02f6
            int r10 = r2 + 2
            r1 = r1[r10]
            int r10 = r1 >>> 20
            r22 = 1
            int r10 = r22 << r10
            r6 = 1048575(0xfffff, float:1.469367E-39)
            r1 = r1 & r6
            r17 = r2
            if (r1 == r5) goto L_0x00a7
            if (r5 == r6) goto L_0x0099
            long r2 = (long) r5
            r11.putInt(r7, r2, r4)
        L_0x0099:
            if (r1 != r6) goto L_0x009e
            r4 = r16
            goto L_0x00a4
        L_0x009e:
            long r2 = (long) r1
            int r2 = r11.getInt(r7, r2)
            r4 = r2
        L_0x00a4:
            r27 = r1
            goto L_0x00a9
        L_0x00a7:
            r27 = r5
        L_0x00a9:
            switch(r8) {
                case 0: goto L_0x02ba;
                case 1: goto L_0x029f;
                case 2: goto L_0x026f;
                case 3: goto L_0x026f;
                case 4: goto L_0x024d;
                case 5: goto L_0x0226;
                case 6: goto L_0x020f;
                case 7: goto L_0x01f0;
                case 8: goto L_0x01cd;
                case 9: goto L_0x019e;
                case 10: goto L_0x0187;
                case 11: goto L_0x024d;
                case 12: goto L_0x0147;
                case 13: goto L_0x020f;
                case 14: goto L_0x0226;
                case 15: goto L_0x0125;
                case 16: goto L_0x00f4;
                default: goto L_0x00ac;
            }
        L_0x00ac:
            r2 = r36
            r3 = r0
            r8 = r17
            r0 = 1
            r1 = 3
            r17 = r6
            r6 = r24
            if (r9 != r1) goto L_0x02dd
            int r0 = r25 << 3
            r4 = r4 | r10
            r13 = r0 | 4
            java.lang.Object r0 = r2.C(r7, r8)
            hvp r9 = r2.z(r8)
            r1 = r8
            r8 = r0
            r14 = r17
            r5 = -1
            r17 = r5
            r5 = r25
            r10 = r38
            r5 = r11
            r11 = r3
            r3 = r12
            r12 = r40
            r39 = r4
            r4 = r40
            r14 = r42
            int r8 = defpackage.hsg.p(r8, r9, r10, r11, r12, r13, r14)
            r2.J(r7, r1, r0)
            r13 = r41
            r12 = r3
            r14 = r4
            r11 = r5
            r3 = r6
            r0 = r8
            r5 = r27
            r4 = r39
            r6 = r2
            r2 = r1
            r1 = r25
            goto L_0x001d
        L_0x00f4:
            if (r9 != 0) goto L_0x0116
            r8 = r4 | r10
            int r9 = defpackage.hsg.o(r15, r0, r12)
            long r0 = r12.b
            long r4 = defpackage.hsu.J(r0)
            r0 = r11
            r1 = r37
            r10 = r17
            r6 = r24
            r2 = r13
            r0.putLong(r1, r2, r4)
            r14 = r40
            r13 = r41
            r3 = r6
            r4 = r8
            r0 = r9
            r2 = r10
            goto L_0x013f
        L_0x0116:
            r10 = r17
            r6 = r24
            r2 = r36
            r3 = r0
            r8 = r4
            r1 = r10
            r5 = r11
            r0 = 1
            r17 = -1
            goto L_0x02e2
        L_0x0125:
            r8 = r17
            r6 = r24
            if (r9 != 0) goto L_0x0182
            r4 = r4 | r10
            int r0 = defpackage.hsg.l(r15, r0, r12)
            int r1 = r12.a
            int r1 = defpackage.hsu.H(r1)
            r11.putInt(r7, r13, r1)
            r14 = r40
            r13 = r41
            r3 = r6
            r2 = r8
        L_0x013f:
            r1 = r25
            r5 = r27
        L_0x0143:
            r6 = r36
            goto L_0x001d
        L_0x0147:
            r8 = r17
            r6 = r24
            if (r9 != 0) goto L_0x017f
            int r0 = defpackage.hsg.l(r15, r0, r12)
            int r1 = r12.a
            r17 = 1048575(0xfffff, float:1.469367E-39)
            r5 = r36
            htu r2 = r5.y(r8)
            r3 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r26 & r3
            if (r3 == 0) goto L_0x0179
            if (r2 == 0) goto L_0x0179
            boolean r2 = r2.a(r1)
            if (r2 == 0) goto L_0x016b
            goto L_0x0179
        L_0x016b:
            hvz r2 = d(r37)
            long r9 = (long) r1
            java.lang.Long r1 = java.lang.Long.valueOf(r9)
            r2.e(r6, r1)
            goto L_0x0262
        L_0x0179:
            r4 = r4 | r10
            r11.putInt(r7, r13, r1)
            goto L_0x0262
        L_0x017f:
            r17 = 1048575(0xfffff, float:1.469367E-39)
        L_0x0182:
            r2 = r36
            r3 = r0
            goto L_0x02b6
        L_0x0187:
            r5 = r36
            r8 = r17
            r1 = 2
            r17 = r6
            r6 = r24
            if (r9 != r1) goto L_0x01ca
            r4 = r4 | r10
            int r0 = defpackage.hsg.c(r15, r0, r12)
            java.lang.Object r1 = r12.c
            r11.putObject(r7, r13, r1)
            goto L_0x0262
        L_0x019e:
            r5 = r36
            r8 = r17
            r1 = 2
            r17 = r6
            r6 = r24
            if (r9 != r1) goto L_0x01ca
            r9 = r4 | r10
            java.lang.Object r10 = r5.C(r7, r8)
            hvp r1 = r5.z(r8)
            r3 = r0
            r0 = r10
            r2 = r38
            r4 = r40
            r13 = r5
            r5 = r42
            int r0 = defpackage.hsg.q(r0, r1, r2, r3, r4, r5)
            r13.J(r7, r8, r10)
            r14 = r40
            r3 = r6
            r2 = r8
            r4 = r9
            goto L_0x0294
        L_0x01ca:
            r3 = r0
            goto L_0x029d
        L_0x01cd:
            r5 = r36
            r3 = r0
            r8 = r17
            r0 = 2
            r17 = r6
            r6 = r24
            if (r9 != r0) goto L_0x029d
            r4 = r4 | r10
            boolean r0 = M(r26)
            if (r0 == 0) goto L_0x01e5
            int r0 = defpackage.hsg.j(r15, r3, r12)
            goto L_0x01e9
        L_0x01e5:
            int r0 = defpackage.hsg.i(r15, r3, r12)
        L_0x01e9:
            java.lang.Object r1 = r12.c
            r11.putObject(r7, r13, r1)
            goto L_0x0262
        L_0x01f0:
            r5 = r36
            r3 = r0
            r8 = r17
            r17 = r6
            r6 = r24
            if (r9 != 0) goto L_0x029d
            r4 = r4 | r10
            int r0 = defpackage.hsg.o(r15, r3, r12)
            long r1 = r12.b
            int r1 = (r1 > r19 ? 1 : (r1 == r19 ? 0 : -1))
            if (r1 == 0) goto L_0x0208
            r1 = 1
            goto L_0x020a
        L_0x0208:
            r1 = r16
        L_0x020a:
            defpackage.hwe.m(r7, r13, r1)
            goto L_0x0262
        L_0x020f:
            r5 = r36
            r3 = r0
            r8 = r17
            r0 = 5
            r17 = r6
            r6 = r24
            if (r9 != r0) goto L_0x029d
            int r0 = r3 + 4
            r4 = r4 | r10
            int r1 = defpackage.hsg.d(r15, r3)
            r11.putInt(r7, r13, r1)
            goto L_0x0262
        L_0x0226:
            r5 = r36
            r3 = r0
            r8 = r17
            r0 = 1
            r17 = r6
            r6 = r24
            if (r9 != r0) goto L_0x024a
            int r9 = r3 + 8
            r10 = r10 | r4
            long r19 = defpackage.hsg.s(r15, r3)
            r0 = r11
            r1 = r37
            r2 = r13
            r13 = r5
            r4 = r19
            r0.putLong(r1, r2, r4)
            r14 = r40
            r3 = r6
            r2 = r8
            r0 = r9
            r4 = r10
            goto L_0x0294
        L_0x024a:
            r2 = r5
            goto L_0x02dd
        L_0x024d:
            r5 = r36
            r3 = r0
            r8 = r17
            r17 = r6
            r6 = r24
            if (r9 != 0) goto L_0x029d
            r4 = r4 | r10
            int r0 = defpackage.hsg.l(r15, r3, r12)
            int r1 = r12.a
            r11.putInt(r7, r13, r1)
        L_0x0262:
            r14 = r40
            r13 = r41
            r3 = r6
            r2 = r8
            r1 = r25
            r6 = r5
            r5 = r27
            goto L_0x001d
        L_0x026f:
            r5 = r36
            r3 = r0
            r8 = r17
            r17 = r6
            r6 = r24
            if (r9 != 0) goto L_0x029d
            r9 = r4 | r10
            int r10 = defpackage.hsg.o(r15, r3, r12)
            long r2 = r12.b
            r0 = r11
            r1 = r37
            r19 = r2
            r2 = r13
            r13 = r5
            r4 = r19
            r0.putLong(r1, r2, r4)
            r14 = r40
            r3 = r6
            r2 = r8
            r4 = r9
            r0 = r10
        L_0x0294:
            r6 = r13
            r1 = r25
            r5 = r27
        L_0x0299:
            r13 = r41
            goto L_0x001d
        L_0x029d:
            r2 = r5
            goto L_0x02b6
        L_0x029f:
            r2 = r36
            r3 = r0
            r8 = r17
            r0 = 5
            r17 = r6
            r6 = r24
            if (r9 != r0) goto L_0x02b6
            int r0 = r3 + 4
            r4 = r4 | r10
            float r1 = defpackage.hsg.b(r15, r3)
            defpackage.hwe.r(r7, r13, r1)
            goto L_0x02d0
        L_0x02b6:
            r1 = r8
            r5 = r11
            r0 = 1
            goto L_0x02df
        L_0x02ba:
            r2 = r36
            r3 = r0
            r8 = r17
            r0 = 1
            r17 = r6
            r6 = r24
            if (r9 != r0) goto L_0x02dd
            int r0 = r3 + 8
            r4 = r4 | r10
            double r9 = defpackage.hsg.a(r15, r3)
            defpackage.hwe.q(r7, r13, r9)
        L_0x02d0:
            r14 = r40
            r13 = r41
            r3 = r6
            r1 = r25
            r5 = r27
            r6 = r2
            r2 = r8
            goto L_0x001d
        L_0x02dd:
            r1 = r8
            r5 = r11
        L_0x02df:
            r17 = -1
            r8 = r4
        L_0x02e2:
            r4 = r40
            r28 = r1
            r13 = r3
            r33 = r5
            r3 = r6
            r10 = r7
            r18 = r8
            r21 = r23
            r6 = r25
            r7 = r41
            r5 = r0
            goto L_0x0cc4
        L_0x02f6:
            r3 = r0
            r10 = r2
            r18 = r4
            r27 = r5
            r2 = r6
            r5 = r11
            r6 = r24
            r11 = r25
            r17 = -1
            r4 = r40
            r0 = 27
            r24 = 10
            if (r8 != r0) goto L_0x035d
            r0 = 2
            if (r9 != r0) goto L_0x0350
            java.lang.Object r0 = r5.getObject(r7, r13)
            huf r0 = (defpackage.huf) r0
            boolean r1 = r0.c()
            if (r1 != 0) goto L_0x032d
            int r1 = r0.size()
            if (r1 != 0) goto L_0x0322
            goto L_0x0324
        L_0x0322:
            int r24 = r1 + r1
        L_0x0324:
            r1 = r24
            huf r0 = r0.d(r1)
            r5.putObject(r7, r13, r0)
        L_0x032d:
            r13 = r0
            hvp r8 = r2.z(r10)
            r9 = r6
            r0 = r10
            r10 = r38
            r1 = r11
            r11 = r3
            r3 = r12
            r12 = r40
            r14 = r42
            int r8 = defpackage.hsg.g(r8, r9, r10, r11, r12, r13, r14)
            r13 = r41
            r12 = r3
            r14 = r4
            r11 = r5
            r3 = r6
            r4 = r18
            r5 = r27
            r6 = r2
            r2 = r0
            r0 = r8
            goto L_0x001d
        L_0x0350:
            r8 = r4
            r33 = r5
            r24 = r6
            r7 = r10
            r25 = r11
            r6 = r23
            r10 = r3
            goto L_0x0a2e
        L_0x035d:
            r0 = r10
            java.lang.String r10 = "Protocol message had invalid UTF-8."
            r28 = r0
            java.lang.String r0 = ""
            java.lang.String r2 = "While parsing a protocol message, the input ended unexpectedly in the middle of a field.  This could mean either that the input has been truncated or that an embedded message misreported its own length."
            r29 = r5
            r5 = 49
            if (r8 > r5) goto L_0x0919
            r25 = r0
            r5 = r26
            long r0 = (long) r5
            sun.misc.Unsafe r5 = b
            java.lang.Object r26 = r5.getObject(r7, r13)
            r30 = r10
            r10 = r26
            huf r10 = (defpackage.huf) r10
            boolean r26 = r10.c()
            if (r26 != 0) goto L_0x0399
            int r26 = r10.size()
            if (r26 != 0) goto L_0x038a
            goto L_0x038c
        L_0x038a:
            int r24 = r26 + r26
        L_0x038c:
            r31 = r0
            r0 = r24
            huf r0 = r10.d(r0)
            r5.putObject(r7, r13, r0)
            r13 = r0
            goto L_0x039c
        L_0x0399:
            r31 = r0
            r13 = r10
        L_0x039c:
            java.lang.String r0 = "CodedInputStream encountered an embedded string or message which claimed to have negative size."
            switch(r8) {
                case 18: goto L_0x087c;
                case 19: goto L_0x0829;
                case 20: goto L_0x07cb;
                case 21: goto L_0x07cb;
                case 22: goto L_0x078a;
                case 23: goto L_0x0739;
                case 24: goto L_0x06e6;
                case 25: goto L_0x067c;
                case 26: goto L_0x0593;
                case 27: goto L_0x0557;
                case 28: goto L_0x04e0;
                case 29: goto L_0x078a;
                case 30: goto L_0x048d;
                case 31: goto L_0x06e6;
                case 32: goto L_0x0739;
                case 33: goto L_0x0426;
                case 34: goto L_0x03d3;
                case 35: goto L_0x087c;
                case 36: goto L_0x0829;
                case 37: goto L_0x07cb;
                case 38: goto L_0x07cb;
                case 39: goto L_0x078a;
                case 40: goto L_0x0739;
                case 41: goto L_0x06e6;
                case 42: goto L_0x067c;
                case 43: goto L_0x078a;
                case 44: goto L_0x048d;
                case 45: goto L_0x06e6;
                case 46: goto L_0x0739;
                case 47: goto L_0x0426;
                case 48: goto L_0x03d3;
                default: goto L_0x03a1;
            }
        L_0x03a1:
            r14 = r36
            r7 = r4
            r5 = r12
            r34 = r28
            r33 = r29
            r0 = 3
            r8 = 1
            r4 = r3
            r12 = r11
            if (r9 != r0) goto L_0x08f0
            r0 = r6 & -8
            r9 = r0 | 4
            r10 = r34
            hvp r11 = r14.z(r10)
            r0 = r11
            r1 = r38
            r2 = r4
            r3 = r40
            r28 = r10
            r10 = r4
            r4 = r9
            r25 = r12
            r12 = r5
            r5 = r42
            int r0 = defpackage.hsg.e(r0, r1, r2, r3, r4, r5)
            java.lang.Object r1 = r12.c
            r13.add(r1)
            goto L_0x08d4
        L_0x03d3:
            r1 = 2
            if (r9 != r1) goto L_0x03fb
            int r0 = defpackage.hsg.a
            hur r13 = (defpackage.hur) r13
            int r0 = defpackage.hsg.l(r15, r3, r12)
            int r1 = r12.a
            int r1 = r1 + r0
        L_0x03e1:
            if (r0 >= r1) goto L_0x03f1
            int r0 = defpackage.hsg.o(r15, r0, r12)
            long r8 = r12.b
            long r8 = defpackage.hsu.J(r8)
            r13.e(r8)
            goto L_0x03e1
        L_0x03f1:
            if (r0 != r1) goto L_0x03f5
            goto L_0x0478
        L_0x03f5:
            hui r0 = new hui
            r0.<init>((java.lang.String) r2)
            throw r0
        L_0x03fb:
            if (r9 != 0) goto L_0x0483
            int r0 = defpackage.hsg.a
            hur r13 = (defpackage.hur) r13
            int r0 = defpackage.hsg.o(r15, r3, r12)
            long r1 = r12.b
            long r1 = defpackage.hsu.J(r1)
            r13.e(r1)
        L_0x040e:
            if (r0 >= r4) goto L_0x0478
            int r1 = defpackage.hsg.l(r15, r0, r12)
            int r2 = r12.a
            if (r6 != r2) goto L_0x0478
            int r0 = defpackage.hsg.o(r15, r1, r12)
            long r1 = r12.b
            long r1 = defpackage.hsu.J(r1)
            r13.e(r1)
            goto L_0x040e
        L_0x0426:
            r0 = 2
            if (r9 != r0) goto L_0x044d
            int r0 = defpackage.hsg.a
            htr r13 = (defpackage.htr) r13
            int r0 = defpackage.hsg.l(r15, r3, r12)
            int r1 = r12.a
            int r1 = r1 + r0
        L_0x0434:
            if (r0 >= r1) goto L_0x0444
            int r0 = defpackage.hsg.l(r15, r0, r12)
            int r5 = r12.a
            int r5 = defpackage.hsu.H(r5)
            r13.g(r5)
            goto L_0x0434
        L_0x0444:
            if (r0 != r1) goto L_0x0447
            goto L_0x0478
        L_0x0447:
            hui r0 = new hui
            r0.<init>((java.lang.String) r2)
            throw r0
        L_0x044d:
            if (r9 != 0) goto L_0x0483
            int r0 = defpackage.hsg.a
            htr r13 = (defpackage.htr) r13
            int r0 = defpackage.hsg.l(r15, r3, r12)
            int r1 = r12.a
            int r1 = defpackage.hsu.H(r1)
            r13.g(r1)
        L_0x0460:
            if (r0 >= r4) goto L_0x0478
            int r1 = defpackage.hsg.l(r15, r0, r12)
            int r2 = r12.a
            if (r6 != r2) goto L_0x0478
            int r0 = defpackage.hsg.l(r15, r1, r12)
            int r1 = r12.a
            int r1 = defpackage.hsu.H(r1)
            r13.g(r1)
            goto L_0x0460
        L_0x0478:
            r14 = r36
            r10 = r3
            r7 = r4
            r25 = r11
            r33 = r29
            r8 = 1
            goto L_0x08f7
        L_0x0483:
            r14 = r36
            r10 = r3
            r7 = r4
            r25 = r11
            r33 = r29
            goto L_0x04dd
        L_0x048d:
            r0 = 2
            if (r9 != r0) goto L_0x04a0
            int r0 = defpackage.hsg.h(r15, r3, r13, r12)
            r8 = r36
            r21 = r0
            r9 = r3
            r7 = r4
            r14 = r28
            r33 = r29
            r10 = 1
            goto L_0x04bc
        L_0x04a0:
            if (r9 != 0) goto L_0x04d5
            r14 = r28
            r1 = 1
            r0 = r6
            r10 = r1
            r1 = r38
            r8 = r36
            r2 = r3
            r9 = r3
            r3 = r40
            r5 = r4
            r4 = r13
            r7 = r5
            r33 = r29
            r5 = r42
            int r0 = defpackage.hsg.n(r0, r1, r2, r3, r4, r5)
            r21 = r0
        L_0x04bc:
            htu r3 = r8.y(r14)
            r4 = 0
            hvy r5 = r8.m
            r0 = r37
            r1 = r11
            r2 = r13
            defpackage.hvq.g(r0, r1, r2, r3, r4, r5)
            r25 = r11
            r28 = r14
            r0 = r21
            r14 = r8
            r8 = r10
            r10 = r9
            goto L_0x08f7
        L_0x04d5:
            r7 = r4
            r33 = r29
            r14 = r36
            r10 = r3
            r25 = r11
        L_0x04dd:
            r8 = 1
            goto L_0x08f6
        L_0x04e0:
            r8 = r36
            r5 = r3
            r7 = r4
            r14 = r28
            r33 = r29
            r1 = 2
            r10 = 1
            if (r9 != r1) goto L_0x0550
            int r1 = defpackage.hsg.l(r15, r5, r12)
            int r3 = r12.a
            if (r3 < 0) goto L_0x054a
            int r4 = r15.length
            int r4 = r4 - r1
            if (r3 > r4) goto L_0x0544
            if (r3 != 0) goto L_0x0500
            hsq r3 = defpackage.hsq.b
            r13.add(r3)
            goto L_0x0508
        L_0x0500:
            hsq r4 = defpackage.hsq.s(r15, r1, r3)
            r13.add(r4)
        L_0x0507:
            int r1 = r1 + r3
        L_0x0508:
            if (r1 >= r7) goto L_0x053a
            int r3 = defpackage.hsg.l(r15, r1, r12)
            int r4 = r12.a
            if (r6 != r4) goto L_0x053a
            int r1 = defpackage.hsg.l(r15, r3, r12)
            int r3 = r12.a
            if (r3 < 0) goto L_0x0534
            int r4 = r15.length
            int r4 = r4 - r1
            if (r3 > r4) goto L_0x052e
            if (r3 != 0) goto L_0x0526
            hsq r3 = defpackage.hsq.b
            r13.add(r3)
            goto L_0x0508
        L_0x0526:
            hsq r4 = defpackage.hsq.s(r15, r1, r3)
            r13.add(r4)
            goto L_0x0507
        L_0x052e:
            hui r0 = new hui
            r0.<init>((java.lang.String) r2)
            throw r0
        L_0x0534:
            hui r1 = new hui
            r1.<init>((java.lang.String) r0)
            throw r1
        L_0x053a:
            r0 = r1
            r25 = r11
            r28 = r14
            r14 = r8
            r8 = r10
            r10 = r5
            goto L_0x08f7
        L_0x0544:
            hui r0 = new hui
            r0.<init>((java.lang.String) r2)
            throw r0
        L_0x054a:
            hui r1 = new hui
            r1.<init>((java.lang.String) r0)
            throw r1
        L_0x0550:
            r25 = r11
            r28 = r14
            r14 = r8
            r8 = r10
            goto L_0x0590
        L_0x0557:
            r8 = r36
            r5 = r3
            r7 = r4
            r14 = r28
            r33 = r29
            r0 = 2
            r10 = 1
            if (r9 != r0) goto L_0x0589
            hvp r0 = r8.z(r14)
            r4 = r8
            r3 = r23
            r8 = r0
            r1 = 0
            r9 = r6
            r2 = r10
            r10 = r38
            r0 = r11
            r11 = r5
            r4 = r12
            r12 = r40
            r34 = r14
            r14 = r42
            int r8 = defpackage.hsg.g(r8, r9, r10, r11, r12, r13, r14)
            r14 = r36
            r25 = r0
            r12 = r4
            r10 = r5
            r0 = r8
            r28 = r34
            r8 = r2
            goto L_0x08f7
        L_0x0589:
            r8 = r10
            r25 = r11
            r28 = r14
            r14 = r36
        L_0x0590:
            r10 = r5
            goto L_0x08f6
        L_0x0593:
            r5 = r3
            r7 = r4
            r4 = r12
            r3 = r23
            r34 = r28
            r33 = r29
            r1 = 0
            r2 = 1
            r8 = 2
            r12 = r11
            if (r9 != r8) goto L_0x066f
            r8 = 536870912(0x20000000, double:2.652494739E-315)
            long r8 = r31 & r8
            int r8 = (r8 > r19 ? 1 : (r8 == r19 ? 0 : -1))
            if (r8 != 0) goto L_0x05f7
            int r8 = defpackage.hsg.l(r15, r5, r4)
            int r9 = r4.a
            if (r9 < 0) goto L_0x05f1
            if (r9 != 0) goto L_0x05bb
            r10 = r25
            r13.add(r10)
            goto L_0x05c8
        L_0x05bb:
            r10 = r25
            java.lang.String r11 = new java.lang.String
            java.nio.charset.Charset r14 = defpackage.hug.a
            r11.<init>(r15, r8, r9, r14)
            r13.add(r11)
        L_0x05c7:
            int r8 = r8 + r9
        L_0x05c8:
            if (r8 >= r7) goto L_0x0654
            int r9 = defpackage.hsg.l(r15, r8, r4)
            int r11 = r4.a
            if (r6 != r11) goto L_0x0654
            int r8 = defpackage.hsg.l(r15, r9, r4)
            int r9 = r4.a
            if (r9 < 0) goto L_0x05eb
            if (r9 != 0) goto L_0x05e0
            r13.add(r10)
            goto L_0x05c8
        L_0x05e0:
            java.lang.String r11 = new java.lang.String
            java.nio.charset.Charset r14 = defpackage.hug.a
            r11.<init>(r15, r8, r9, r14)
            r13.add(r11)
            goto L_0x05c7
        L_0x05eb:
            hui r1 = new hui
            r1.<init>((java.lang.String) r0)
            throw r1
        L_0x05f1:
            hui r1 = new hui
            r1.<init>((java.lang.String) r0)
            throw r1
        L_0x05f7:
            r10 = r25
            int r8 = defpackage.hsg.l(r15, r5, r4)
            int r9 = r4.a
            if (r9 < 0) goto L_0x0669
            if (r9 != 0) goto L_0x0607
            r13.add(r10)
            goto L_0x061a
        L_0x0607:
            int r11 = r8 + r9
            boolean r14 = defpackage.hwg.d(r15, r8, r11)
            if (r14 == 0) goto L_0x0661
            java.lang.String r14 = new java.lang.String
            java.nio.charset.Charset r1 = defpackage.hug.a
            r14.<init>(r15, r8, r9, r1)
            r13.add(r14)
            r8 = r11
        L_0x061a:
            if (r8 >= r7) goto L_0x0654
            int r1 = defpackage.hsg.l(r15, r8, r4)
            int r9 = r4.a
            if (r6 != r9) goto L_0x0654
            int r8 = defpackage.hsg.l(r15, r1, r4)
            int r1 = r4.a
            if (r1 < 0) goto L_0x064e
            if (r1 != 0) goto L_0x0632
            r13.add(r10)
            goto L_0x061a
        L_0x0632:
            int r9 = r8 + r1
            boolean r11 = defpackage.hwg.d(r15, r8, r9)
            if (r11 == 0) goto L_0x0646
            java.lang.String r11 = new java.lang.String
            java.nio.charset.Charset r14 = defpackage.hug.a
            r11.<init>(r15, r8, r1, r14)
            r13.add(r11)
            r8 = r9
            goto L_0x061a
        L_0x0646:
            hui r0 = new hui
            r11 = r30
            r0.<init>((java.lang.String) r11)
            throw r0
        L_0x064e:
            hui r1 = new hui
            r1.<init>((java.lang.String) r0)
            throw r1
        L_0x0654:
            r14 = r36
            r23 = r3
            r10 = r5
            r0 = r8
            r25 = r12
            r28 = r34
            r8 = r2
            goto L_0x07a5
        L_0x0661:
            r11 = r30
            hui r0 = new hui
            r0.<init>((java.lang.String) r11)
            throw r0
        L_0x0669:
            hui r1 = new hui
            r1.<init>((java.lang.String) r0)
            throw r1
        L_0x066f:
            r14 = r36
            r8 = r2
        L_0x0672:
            r23 = r3
            r10 = r5
            r25 = r12
            r28 = r34
            r12 = r4
            goto L_0x08f6
        L_0x067c:
            r5 = r3
            r7 = r4
            r4 = r12
            r3 = r23
            r34 = r28
            r33 = r29
            r0 = 2
            r8 = 1
            r12 = r11
            if (r9 != r0) goto L_0x06b3
            int r0 = defpackage.hsg.a
            hsi r13 = (defpackage.hsi) r13
            int r0 = defpackage.hsg.l(r15, r5, r4)
            int r1 = r4.a
            int r1 = r1 + r0
        L_0x0695:
            if (r0 >= r1) goto L_0x06a9
            int r0 = defpackage.hsg.o(r15, r0, r4)
            long r9 = r4.b
            int r9 = (r9 > r19 ? 1 : (r9 == r19 ? 0 : -1))
            if (r9 == 0) goto L_0x06a3
            r9 = r8
            goto L_0x06a5
        L_0x06a3:
            r9 = r16
        L_0x06a5:
            r13.e(r9)
            goto L_0x0695
        L_0x06a9:
            if (r0 != r1) goto L_0x06ad
            goto L_0x0760
        L_0x06ad:
            hui r0 = new hui
            r0.<init>((java.lang.String) r2)
            throw r0
        L_0x06b3:
            if (r9 != 0) goto L_0x07c7
            int r0 = defpackage.hsg.a
            hsi r13 = (defpackage.hsi) r13
            int r0 = defpackage.hsg.o(r15, r5, r4)
            long r1 = r4.b
            int r1 = (r1 > r19 ? 1 : (r1 == r19 ? 0 : -1))
            if (r1 == 0) goto L_0x06c5
            r1 = r8
            goto L_0x06c7
        L_0x06c5:
            r1 = r16
        L_0x06c7:
            r13.e(r1)
        L_0x06ca:
            if (r0 >= r7) goto L_0x079c
            int r1 = defpackage.hsg.l(r15, r0, r4)
            int r2 = r4.a
            if (r6 != r2) goto L_0x079c
            int r0 = defpackage.hsg.o(r15, r1, r4)
            long r1 = r4.b
            int r1 = (r1 > r19 ? 1 : (r1 == r19 ? 0 : -1))
            if (r1 == 0) goto L_0x06e0
            r1 = r8
            goto L_0x06e2
        L_0x06e0:
            r1 = r16
        L_0x06e2:
            r13.e(r1)
            goto L_0x06ca
        L_0x06e6:
            r5 = r3
            r7 = r4
            r4 = r12
            r3 = r23
            r34 = r28
            r33 = r29
            r0 = 2
            r8 = 1
            r12 = r11
            if (r9 != r0) goto L_0x0715
            int r0 = defpackage.hsg.a
            htr r13 = (defpackage.htr) r13
            int r0 = defpackage.hsg.l(r15, r5, r4)
            int r1 = r4.a
            int r1 = r1 + r0
        L_0x06ff:
            if (r0 >= r1) goto L_0x070b
            int r9 = defpackage.hsg.d(r15, r0)
            r13.g(r9)
            int r0 = r0 + 4
            goto L_0x06ff
        L_0x070b:
            if (r0 != r1) goto L_0x070f
            goto L_0x079c
        L_0x070f:
            hui r0 = new hui
            r0.<init>((java.lang.String) r2)
            throw r0
        L_0x0715:
            r0 = 5
            if (r9 != r0) goto L_0x07c7
            int r0 = r5 + 4
            int r1 = defpackage.hsg.a
            htr r13 = (defpackage.htr) r13
            int r1 = defpackage.hsg.d(r15, r5)
            r13.g(r1)
        L_0x0725:
            if (r0 >= r7) goto L_0x079c
            int r1 = defpackage.hsg.l(r15, r0, r4)
            int r2 = r4.a
            if (r6 != r2) goto L_0x079c
            int r0 = defpackage.hsg.d(r15, r1)
            r13.g(r0)
            int r0 = r1 + 4
            goto L_0x0725
        L_0x0739:
            r5 = r3
            r7 = r4
            r4 = r12
            r3 = r23
            r34 = r28
            r33 = r29
            r0 = 2
            r8 = 1
            r12 = r11
            if (r9 != r0) goto L_0x0767
            int r0 = defpackage.hsg.a
            hur r13 = (defpackage.hur) r13
            int r0 = defpackage.hsg.l(r15, r5, r4)
            int r1 = r4.a
            int r1 = r1 + r0
        L_0x0752:
            if (r0 >= r1) goto L_0x075e
            long r9 = defpackage.hsg.s(r15, r0)
            r13.e(r9)
            int r0 = r0 + 8
            goto L_0x0752
        L_0x075e:
            if (r0 != r1) goto L_0x0761
        L_0x0760:
            goto L_0x079c
        L_0x0761:
            hui r0 = new hui
            r0.<init>((java.lang.String) r2)
            throw r0
        L_0x0767:
            if (r9 != r8) goto L_0x07c7
            int r0 = r5 + 8
            int r1 = defpackage.hsg.a
            hur r13 = (defpackage.hur) r13
            long r1 = defpackage.hsg.s(r15, r5)
            r13.e(r1)
        L_0x0776:
            if (r0 >= r7) goto L_0x079c
            int r1 = defpackage.hsg.l(r15, r0, r4)
            int r2 = r4.a
            if (r6 != r2) goto L_0x079c
            long r9 = defpackage.hsg.s(r15, r1)
            r13.e(r9)
            int r0 = r1 + 8
            goto L_0x0776
        L_0x078a:
            r5 = r3
            r7 = r4
            r4 = r12
            r3 = r23
            r34 = r28
            r33 = r29
            r0 = 2
            r8 = 1
            r12 = r11
            if (r9 != r0) goto L_0x07a8
            int r0 = defpackage.hsg.h(r15, r5, r13, r4)
        L_0x079c:
            r14 = r36
            r23 = r3
            r10 = r5
            r25 = r12
            r28 = r34
        L_0x07a5:
            r12 = r4
            goto L_0x08f7
        L_0x07a8:
            if (r9 != 0) goto L_0x07c7
            r0 = r6
            r10 = 0
            r1 = r38
            r2 = r5
            r11 = r3
            r3 = r40
            r14 = r36
            r9 = r4
            r4 = r13
            r13 = r5
            r5 = r42
            int r0 = defpackage.hsg.n(r0, r1, r2, r3, r4, r5)
            r23 = r11
            r25 = r12
            r10 = r13
            r28 = r34
            r12 = r9
            goto L_0x08f7
        L_0x07c7:
            r14 = r36
            goto L_0x0672
        L_0x07cb:
            r14 = r36
            r7 = r4
            r5 = r12
            r34 = r28
            r33 = r29
            r0 = 2
            r8 = 1
            r10 = 0
            r4 = r3
            r12 = r11
            r11 = r23
            if (r9 != r0) goto L_0x0804
            int r0 = defpackage.hsg.a
            hur r13 = (defpackage.hur) r13
            int r0 = defpackage.hsg.l(r15, r4, r5)
            int r1 = r5.a
            int r1 = r1 + r0
        L_0x07e7:
            if (r0 >= r1) goto L_0x07f8
            int r0 = defpackage.hsg.o(r15, r0, r5)
            r23 = r11
            long r10 = r5.b
            r13.e(r10)
            r11 = r23
            r10 = 0
            goto L_0x07e7
        L_0x07f8:
            r23 = r11
            if (r0 != r1) goto L_0x07fe
            goto L_0x08cd
        L_0x07fe:
            hui r0 = new hui
            r0.<init>((java.lang.String) r2)
            throw r0
        L_0x0804:
            r23 = r11
            if (r9 != 0) goto L_0x08f0
            int r0 = defpackage.hsg.a
            hur r13 = (defpackage.hur) r13
            int r0 = defpackage.hsg.o(r15, r4, r5)
            long r1 = r5.b
            r13.e(r1)
        L_0x0815:
            if (r0 >= r7) goto L_0x08cd
            int r1 = defpackage.hsg.l(r15, r0, r5)
            int r2 = r5.a
            if (r6 != r2) goto L_0x08cd
            int r0 = defpackage.hsg.o(r15, r1, r5)
            long r1 = r5.b
            r13.e(r1)
            goto L_0x0815
        L_0x0829:
            r14 = r36
            r7 = r4
            r5 = r12
            r34 = r28
            r33 = r29
            r0 = 2
            r8 = 1
            r4 = r3
            r12 = r11
            if (r9 != r0) goto L_0x0858
            int r0 = defpackage.hsg.a
            hti r13 = (defpackage.hti) r13
            int r0 = defpackage.hsg.l(r15, r4, r5)
            int r1 = r5.a
            int r1 = r1 + r0
        L_0x0842:
            if (r0 >= r1) goto L_0x084e
            float r3 = defpackage.hsg.b(r15, r0)
            r13.g(r3)
            int r0 = r0 + 4
            goto L_0x0842
        L_0x084e:
            if (r0 != r1) goto L_0x0852
            goto L_0x08cd
        L_0x0852:
            hui r0 = new hui
            r0.<init>((java.lang.String) r2)
            throw r0
        L_0x0858:
            r0 = 5
            if (r9 != r0) goto L_0x08f0
            int r0 = r4 + 4
            int r1 = defpackage.hsg.a
            hti r13 = (defpackage.hti) r13
            float r1 = defpackage.hsg.b(r15, r4)
            r13.g(r1)
        L_0x0868:
            if (r0 >= r7) goto L_0x08cd
            int r1 = defpackage.hsg.l(r15, r0, r5)
            int r2 = r5.a
            if (r6 != r2) goto L_0x08cd
            float r0 = defpackage.hsg.b(r15, r1)
            r13.g(r0)
            int r0 = r1 + 4
            goto L_0x0868
        L_0x087c:
            r14 = r36
            r7 = r4
            r5 = r12
            r34 = r28
            r33 = r29
            r0 = 2
            r8 = 1
            r4 = r3
            r12 = r11
            if (r9 != r0) goto L_0x08aa
            int r0 = defpackage.hsg.a
            hta r13 = (defpackage.hta) r13
            int r0 = defpackage.hsg.l(r15, r4, r5)
            int r1 = r5.a
            int r1 = r1 + r0
        L_0x0895:
            if (r0 >= r1) goto L_0x08a1
            double r9 = defpackage.hsg.a(r15, r0)
            r13.f(r9)
            int r0 = r0 + 8
            goto L_0x0895
        L_0x08a1:
            if (r0 != r1) goto L_0x08a4
            goto L_0x08cd
        L_0x08a4:
            hui r0 = new hui
            r0.<init>((java.lang.String) r2)
            throw r0
        L_0x08aa:
            if (r9 != r8) goto L_0x08f0
            int r0 = r4 + 8
            int r1 = defpackage.hsg.a
            hta r13 = (defpackage.hta) r13
            double r1 = defpackage.hsg.a(r15, r4)
            r13.f(r1)
        L_0x08b9:
            if (r0 >= r7) goto L_0x08cd
            int r1 = defpackage.hsg.l(r15, r0, r5)
            int r2 = r5.a
            if (r6 != r2) goto L_0x08cd
            double r2 = defpackage.hsg.a(r15, r1)
            r13.f(r2)
            int r0 = r1 + 8
            goto L_0x08b9
        L_0x08cd:
            r10 = r4
            r25 = r12
            r28 = r34
            r12 = r5
            goto L_0x08f7
        L_0x08d4:
            if (r0 >= r7) goto L_0x08f7
            int r2 = defpackage.hsg.l(r15, r0, r12)
            int r1 = r12.a
            if (r6 != r1) goto L_0x08f7
            r0 = r11
            r1 = r38
            r3 = r40
            r4 = r9
            r5 = r42
            int r0 = defpackage.hsg.e(r0, r1, r2, r3, r4, r5)
            java.lang.Object r1 = r12.c
            r13.add(r1)
            goto L_0x08d4
        L_0x08f0:
            r10 = r4
            r25 = r12
            r28 = r34
            r12 = r5
        L_0x08f6:
            r0 = r10
        L_0x08f7:
            if (r0 == r10) goto L_0x090c
            r13 = r41
            r3 = r6
            r6 = r14
            r4 = r18
            r1 = r25
            r5 = r27
            r2 = r28
            r11 = r33
            r14 = r7
            r7 = r37
            goto L_0x001d
        L_0x090c:
            r10 = r37
            r7 = r41
            r13 = r0
            r3 = r6
            r5 = r8
            r21 = r23
            r6 = r25
            goto L_0x0cc4
        L_0x0919:
            r4 = r36
            r22 = r0
            r25 = r11
            r0 = r23
            r5 = r26
            r33 = r29
            r11 = r10
            r10 = r3
            r3 = r28
            r7 = 50
            if (r8 != r7) goto L_0x0a3e
            r7 = 2
            if (r9 != r7) goto L_0x0a28
            sun.misc.Unsafe r1 = b
            java.lang.Object r5 = r4.B(r3)
            r7 = r37
            r8 = r40
            java.lang.Object r9 = r1.getObject(r7, r13)
            boolean r11 = defpackage.ftc.aO(r9)
            if (r11 == 0) goto L_0x094f
            java.lang.Object r11 = defpackage.ftc.aQ()
            defpackage.ftc.aP(r11, r9)
            r1.putObject(r7, r13, r11)
            r9 = r11
        L_0x094f:
            gnk r11 = defpackage.ftc.aS(r5)
            huv r9 = (defpackage.huv) r9
            int r1 = defpackage.hsg.l(r15, r10, r12)
            int r5 = r12.a
            if (r5 < 0) goto L_0x0a22
            int r13 = r8 - r1
            if (r5 > r13) goto L_0x0a22
            int r13 = r1 + r5
            java.lang.Object r2 = r11.b
            java.lang.Object r5 = r11.d
            r14 = r2
        L_0x0968:
            if (r1 >= r13) goto L_0x09f2
            int r2 = r1 + 1
            byte r1 = r15[r1]
            if (r1 >= 0) goto L_0x097b
            int r1 = defpackage.hsg.m(r1, r15, r2, r12)
            int r2 = r12.a
            r35 = r2
            r2 = r1
            r1 = r35
        L_0x097b:
            r24 = r0
            int r0 = r1 >>> 3
            r28 = r3
            r3 = r1 & 7
            r4 = 1
            if (r0 == r4) goto L_0x09b7
            r4 = 2
            if (r0 == r4) goto L_0x0993
        L_0x0989:
            r7 = r28
            r35 = r24
            r24 = r6
            r6 = r35
            goto L_0x09e4
        L_0x0993:
            java.lang.Object r0 = r11.a
            hwh r0 = (defpackage.hwh) r0
            int r4 = r0.t
            if (r3 != r4) goto L_0x0989
            java.lang.Object r1 = r11.d
            java.lang.Class r4 = r1.getClass()
            r3 = r0
            r5 = r24
            r0 = r38
            r1 = r2
            r2 = r40
            r7 = r28
            r24 = r6
            r6 = r5
            r5 = r42
            int r1 = U(r0, r1, r2, r3, r4, r5)
            java.lang.Object r5 = r12.c
            goto L_0x09e8
        L_0x09b7:
            r7 = r28
            r35 = r24
            r24 = r6
            r6 = r35
            java.lang.Object r0 = r11.c
            r4 = r0
            hwh r4 = (defpackage.hwh) r4
            int r0 = r4.t
            if (r3 != r0) goto L_0x09e4
            r14 = 0
            r0 = r38
            r1 = r2
            r2 = r40
            r3 = r4
            r4 = r14
            r14 = r5
            r5 = r42
            int r1 = U(r0, r1, r2, r3, r4, r5)
            java.lang.Object r0 = r12.c
            r4 = r36
            r3 = r7
            r5 = r14
            r7 = r37
            r14 = r0
            r0 = r6
            r6 = r24
            goto L_0x0968
        L_0x09e4:
            int r1 = defpackage.hsg.r(r1, r15, r2, r8, r12)
        L_0x09e8:
            r4 = r36
            r0 = r6
            r3 = r7
            r6 = r24
            r7 = r37
            goto L_0x0968
        L_0x09f2:
            r7 = r3
            r24 = r6
            r6 = r0
            if (r1 != r13) goto L_0x0a1c
            r9.put(r14, r5)
            if (r13 == r10) goto L_0x0a10
            r6 = r36
            r2 = r7
            r14 = r8
            r0 = r13
            r4 = r18
            r3 = r24
            r1 = r25
            r5 = r27
            r11 = r33
            r7 = r37
            goto L_0x0299
        L_0x0a10:
            r10 = r37
            r21 = r6
            r28 = r7
            r3 = r24
            r6 = r25
            r5 = 1
            goto L_0x0a3a
        L_0x0a1c:
            hui r0 = new hui
            r0.<init>((java.lang.String) r6)
            throw r0
        L_0x0a22:
            hui r0 = new hui
            r0.<init>((java.lang.String) r2)
            throw r0
        L_0x0a28:
            r8 = r40
            r7 = r3
            r24 = r6
            r6 = r0
        L_0x0a2e:
            r21 = r6
            r28 = r7
            r13 = r10
            r3 = r24
            r6 = r25
            r5 = 1
            r10 = r37
        L_0x0a3a:
            r7 = r41
            goto L_0x0cc4
        L_0x0a3e:
            r4 = r40
            r7 = r3
            r24 = r6
            r6 = r0
            int r2 = r7 + 2
            sun.misc.Unsafe r0 = b
            r1 = r1[r2]
            r3 = 1048575(0xfffff, float:1.469367E-39)
            r1 = r1 & r3
            long r1 = (long) r1
            switch(r8) {
                case 51: goto L_0x0c8d;
                case 52: goto L_0x0c6a;
                case 53: goto L_0x0c4c;
                case 54: goto L_0x0c4c;
                case 55: goto L_0x0c2e;
                case 56: goto L_0x0c0f;
                case 57: goto L_0x0bef;
                case 58: goto L_0x0bc8;
                case 59: goto L_0x0b84;
                case 60: goto L_0x0b4b;
                case 61: goto L_0x0b29;
                case 62: goto L_0x0c2e;
                case 63: goto L_0x0ae7;
                case 64: goto L_0x0bef;
                case 65: goto L_0x0c0f;
                case 66: goto L_0x0abf;
                case 67: goto L_0x0a9e;
                case 68: goto L_0x0a60;
                default: goto L_0x0a52;
            }
        L_0x0a52:
            r21 = r6
            r28 = r7
            r4 = r10
            r3 = r24
            r6 = r25
        L_0x0a5b:
            r5 = 1
            r10 = r37
            goto L_0x0cad
        L_0x0a60:
            r8 = 3
            if (r9 != r8) goto L_0x0a91
            r0 = r24 & -8
            r13 = r0 | 4
            r5 = r37
            r1 = r7
            r0 = r25
            r7 = r36
            java.lang.Object r2 = r7.D(r5, r0, r1)
            hvp r9 = r7.z(r1)
            r8 = r2
            r14 = r10
            r10 = r38
            r11 = r14
            r21 = r6
            r6 = r0
            r0 = r12
            r12 = r40
            r4 = r14
            r14 = r42
            int r8 = defpackage.hsg.p(r8, r9, r10, r11, r12, r13, r14)
            r7.K(r5, r6, r1, r2)
            r12 = r0
            r28 = r1
            r10 = r5
            r0 = r8
            goto L_0x0ae3
        L_0x0a91:
            r21 = r6
            r1 = r7
            r6 = r25
            r7 = r36
            r28 = r1
            r4 = r10
            r3 = r24
            goto L_0x0a5b
        L_0x0a9e:
            r5 = r37
            r21 = r6
            r8 = r7
            r4 = r10
            r6 = r25
            r7 = r36
            if (r9 != 0) goto L_0x0b22
            int r9 = defpackage.hsg.o(r15, r4, r12)
            long r10 = r12.b
            long r10 = defpackage.hsu.J(r10)
            java.lang.Long r10 = java.lang.Long.valueOf(r10)
            r0.putObject(r5, r13, r10)
            r0.putInt(r5, r1, r6)
            goto L_0x0adf
        L_0x0abf:
            r5 = r37
            r21 = r6
            r8 = r7
            r4 = r10
            r6 = r25
            r7 = r36
            if (r9 != 0) goto L_0x0b22
            int r9 = defpackage.hsg.l(r15, r4, r12)
            int r10 = r12.a
            int r10 = defpackage.hsu.H(r10)
            java.lang.Integer r10 = java.lang.Integer.valueOf(r10)
            r0.putObject(r5, r13, r10)
            r0.putInt(r5, r1, r6)
        L_0x0adf:
            r10 = r5
            r28 = r8
            r0 = r9
        L_0x0ae3:
            r3 = r24
            goto L_0x0c89
        L_0x0ae7:
            r5 = r37
            r21 = r6
            r8 = r7
            r4 = r10
            r6 = r25
            r7 = r36
            if (r9 != 0) goto L_0x0b22
            int r9 = defpackage.hsg.l(r15, r4, r12)
            int r10 = r12.a
            htu r11 = r7.y(r8)
            if (r11 == 0) goto L_0x0b15
            boolean r11 = r11.a(r10)
            if (r11 == 0) goto L_0x0b06
            goto L_0x0b15
        L_0x0b06:
            hvz r0 = d(r37)
            long r1 = (long) r10
            java.lang.Long r1 = java.lang.Long.valueOf(r1)
            r11 = r24
            r0.e(r11, r1)
            goto L_0x0b44
        L_0x0b15:
            r11 = r24
            java.lang.Integer r10 = java.lang.Integer.valueOf(r10)
            r0.putObject(r5, r13, r10)
            r0.putInt(r5, r1, r6)
            goto L_0x0b44
        L_0x0b22:
            r10 = r5
            r28 = r8
            r3 = r24
            goto L_0x0c8b
        L_0x0b29:
            r5 = r37
            r21 = r6
            r8 = r7
            r4 = r10
            r11 = r24
            r6 = r25
            r10 = 2
            r7 = r36
            if (r9 != r10) goto L_0x0b7e
            int r9 = defpackage.hsg.c(r15, r4, r12)
            java.lang.Object r10 = r12.c
            r0.putObject(r5, r13, r10)
            r0.putInt(r5, r1, r6)
        L_0x0b44:
            r10 = r5
            r28 = r8
            r0 = r9
            r3 = r11
            goto L_0x0c89
        L_0x0b4b:
            r5 = r37
            r21 = r6
            r8 = r7
            r4 = r10
            r11 = r24
            r6 = r25
            r10 = 2
            r7 = r36
            if (r9 != r10) goto L_0x0b7c
            java.lang.Object r9 = r7.D(r5, r6, r8)
            hvp r1 = r7.z(r8)
            r0 = r9
            r2 = r38
            r10 = r3
            r3 = r4
            r13 = r40
            r14 = r4
            r4 = r40
            r10 = r5
            r5 = r42
            int r0 = defpackage.hsg.q(r0, r1, r2, r3, r4, r5)
            r7.K(r10, r6, r8, r9)
            r28 = r8
            r3 = r11
            r4 = r14
            goto L_0x0c89
        L_0x0b7c:
            r13 = r40
        L_0x0b7e:
            r10 = r5
            r28 = r8
            r3 = r11
            goto L_0x0c8b
        L_0x0b84:
            r21 = r6
            r28 = r7
            r4 = r10
            r3 = r24
            r6 = r25
            r8 = 2
            r7 = r36
            r10 = r37
            if (r9 != r8) goto L_0x0c8b
            int r8 = defpackage.hsg.l(r15, r4, r12)
            int r9 = r12.a
            if (r9 != 0) goto L_0x0ba2
            r7 = r22
            r0.putObject(r10, r13, r7)
            goto L_0x0bc2
        L_0x0ba2:
            int r7 = r8 + r9
            r22 = 536870912(0x20000000, float:1.0842022E-19)
            r5 = r5 & r22
            if (r5 == 0) goto L_0x0bb7
            boolean r5 = defpackage.hwg.d(r15, r8, r7)
            if (r5 == 0) goto L_0x0bb1
            goto L_0x0bb7
        L_0x0bb1:
            hui r0 = new hui
            r0.<init>((java.lang.String) r11)
            throw r0
        L_0x0bb7:
            java.lang.String r5 = new java.lang.String
            java.nio.charset.Charset r11 = defpackage.hug.a
            r5.<init>(r15, r8, r9, r11)
            r0.putObject(r10, r13, r5)
            r8 = r7
        L_0x0bc2:
            r0.putInt(r10, r1, r6)
            r0 = r8
            goto L_0x0c89
        L_0x0bc8:
            r21 = r6
            r28 = r7
            r4 = r10
            r3 = r24
            r6 = r25
            r10 = r37
            if (r9 != 0) goto L_0x0c8b
            int r5 = defpackage.hsg.o(r15, r4, r12)
            long r7 = r12.b
            int r7 = (r7 > r19 ? 1 : (r7 == r19 ? 0 : -1))
            if (r7 == 0) goto L_0x0be1
            r7 = 1
            goto L_0x0be3
        L_0x0be1:
            r7 = r16
        L_0x0be3:
            java.lang.Boolean r7 = java.lang.Boolean.valueOf(r7)
            r0.putObject(r10, r13, r7)
            r0.putInt(r10, r1, r6)
            goto L_0x0c88
        L_0x0bef:
            r21 = r6
            r28 = r7
            r4 = r10
            r3 = r24
            r6 = r25
            r5 = 5
            r10 = r37
            if (r9 != r5) goto L_0x0c8b
            int r5 = r4 + 4
            int r7 = defpackage.hsg.d(r15, r4)
            java.lang.Integer r7 = java.lang.Integer.valueOf(r7)
            r0.putObject(r10, r13, r7)
            r0.putInt(r10, r1, r6)
            goto L_0x0c88
        L_0x0c0f:
            r21 = r6
            r28 = r7
            r4 = r10
            r3 = r24
            r6 = r25
            r5 = 1
            r10 = r37
            if (r9 != r5) goto L_0x0cad
            int r5 = r4 + 8
            long r7 = defpackage.hsg.s(r15, r4)
            java.lang.Long r7 = java.lang.Long.valueOf(r7)
            r0.putObject(r10, r13, r7)
            r0.putInt(r10, r1, r6)
            goto L_0x0c88
        L_0x0c2e:
            r21 = r6
            r28 = r7
            r4 = r10
            r3 = r24
            r6 = r25
            r10 = r37
            if (r9 != 0) goto L_0x0c8b
            int r5 = defpackage.hsg.l(r15, r4, r12)
            int r7 = r12.a
            java.lang.Integer r7 = java.lang.Integer.valueOf(r7)
            r0.putObject(r10, r13, r7)
            r0.putInt(r10, r1, r6)
            goto L_0x0c88
        L_0x0c4c:
            r21 = r6
            r28 = r7
            r4 = r10
            r3 = r24
            r6 = r25
            r10 = r37
            if (r9 != 0) goto L_0x0c8b
            int r5 = defpackage.hsg.o(r15, r4, r12)
            long r7 = r12.b
            java.lang.Long r7 = java.lang.Long.valueOf(r7)
            r0.putObject(r10, r13, r7)
            r0.putInt(r10, r1, r6)
            goto L_0x0c88
        L_0x0c6a:
            r21 = r6
            r28 = r7
            r4 = r10
            r3 = r24
            r6 = r25
            r5 = 5
            r10 = r37
            if (r9 != r5) goto L_0x0c8b
            int r5 = r4 + 4
            float r7 = defpackage.hsg.b(r15, r4)
            java.lang.Float r7 = java.lang.Float.valueOf(r7)
            r0.putObject(r10, r13, r7)
            r0.putInt(r10, r1, r6)
        L_0x0c88:
            r0 = r5
        L_0x0c89:
            r5 = 1
            goto L_0x0cae
        L_0x0c8b:
            r5 = 1
            goto L_0x0cad
        L_0x0c8d:
            r21 = r6
            r28 = r7
            r4 = r10
            r3 = r24
            r6 = r25
            r5 = 1
            r10 = r37
            if (r9 != r5) goto L_0x0cad
            int r7 = r4 + 8
            double r8 = defpackage.hsg.a(r15, r4)
            java.lang.Double r8 = java.lang.Double.valueOf(r8)
            r0.putObject(r10, r13, r8)
            r0.putInt(r10, r1, r6)
            r0 = r7
            goto L_0x0cae
        L_0x0cad:
            r0 = r4
        L_0x0cae:
            if (r0 == r4) goto L_0x0cc0
            r14 = r40
            r13 = r41
            r1 = r6
            r7 = r10
            r4 = r18
            r5 = r27
            r2 = r28
            r11 = r33
            goto L_0x0143
        L_0x0cc0:
            r7 = r41
            goto L_0x0062
        L_0x0cc4:
            if (r3 != r7) goto L_0x0cd7
            if (r7 == 0) goto L_0x0cd7
            r8 = r36
            r9 = r40
            r6 = r3
            r0 = r18
            r5 = r27
            r1 = 1048575(0xfffff, float:1.469367E-39)
            r4 = 0
            goto L_0x0eef
        L_0x0cd7:
            r14 = r36
            boolean r0 = r14.h
            if (r0 == 0) goto L_0x0ead
            hte r0 = r12.d
            hte r1 = defpackage.hte.a
            hvj r1 = defpackage.hvj.a
            hte r1 = defpackage.hte.a
            if (r0 == r1) goto L_0x0ead
            hva r0 = r14.g
            hvy r1 = r14.m
            hte r2 = r12.d
            int r4 = defpackage.hsg.a
            gnk r8 = r2.b(r0, r6)
            if (r8 != 0) goto L_0x0d0d
            hvz r4 = d(r37)
            r0 = r3
            r1 = r38
            r2 = r13
            r21 = r3
            r3 = r40
            r11 = r40
            r5 = r42
            int r0 = defpackage.hsg.k(r0, r1, r2, r3, r4, r5)
        L_0x0d09:
            r9 = r11
        L_0x0d0a:
            r8 = r14
            goto L_0x0ec8
        L_0x0d0d:
            r11 = r40
            r21 = r3
            r0 = r10
            htn r0 = (defpackage.htn) r0
            r0.c()
            htg r9 = r0.r
            hwh r2 = r8.m()
            hwh r3 = defpackage.hwh.ENUM
            if (r2 != r3) goto L_0x0d39
            int r13 = defpackage.hsg.l(r15, r13, r12)
            int r2 = r12.a
            hxn r3 = defpackage.hxn.b(r2)
            if (r3 != 0) goto L_0x0d34
            r4 = 0
            defpackage.hvq.h(r0, r6, r2, r4, r1)
            r9 = r11
            r0 = r13
            goto L_0x0d0a
        L_0x0d34:
            java.lang.Integer r0 = java.lang.Integer.valueOf(r2)
            goto L_0x0d72
        L_0x0d39:
            r4 = 0
            hwh r0 = r8.m()
            int r0 = r0.ordinal()
            switch(r0) {
                case 0: goto L_0x0e83;
                case 1: goto L_0x0e74;
                case 2: goto L_0x0e65;
                case 3: goto L_0x0e65;
                case 4: goto L_0x0e56;
                case 5: goto L_0x0e47;
                case 6: goto L_0x0e38;
                case 7: goto L_0x0e21;
                case 8: goto L_0x0e15;
                case 9: goto L_0x0dbc;
                case 10: goto L_0x0d79;
                case 11: goto L_0x0d6c;
                case 12: goto L_0x0e56;
                case 13: goto L_0x0d64;
                case 14: goto L_0x0e38;
                case 15: goto L_0x0e47;
                case 16: goto L_0x0d55;
                case 17: goto L_0x0d46;
                default: goto L_0x0d45;
            }
        L_0x0d45:
            goto L_0x0d73
        L_0x0d46:
            int r13 = defpackage.hsg.o(r15, r13, r12)
            long r0 = r12.b
            long r0 = defpackage.hsu.J(r0)
            java.lang.Long r0 = java.lang.Long.valueOf(r0)
            goto L_0x0d72
        L_0x0d55:
            int r13 = defpackage.hsg.l(r15, r13, r12)
            int r0 = r12.a
            int r0 = defpackage.hsu.H(r0)
            java.lang.Integer r0 = java.lang.Integer.valueOf(r0)
            goto L_0x0d72
        L_0x0d64:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            java.lang.String r1 = "Shouldn't reach here."
            r0.<init>(r1)
            throw r0
        L_0x0d6c:
            int r13 = defpackage.hsg.c(r15, r13, r12)
            java.lang.Object r0 = r12.c
        L_0x0d72:
            r4 = r0
        L_0x0d73:
            r3 = r10
            r2 = r11
            r1 = r12
            r0 = r14
            goto L_0x0e93
        L_0x0d79:
            java.lang.Object r0 = r8.b
            hvj r1 = defpackage.hvj.a
            java.lang.Class r0 = r0.getClass()
            hvp r1 = r1.a(r0)
            boolean r0 = r8.q()
            if (r0 == 0) goto L_0x0d9a
            int r0 = defpackage.hsg.f(r1, r15, r13, r11, r12)
            java.lang.Object r1 = r8.c
            java.lang.Object r2 = r12.c
            htp r1 = (defpackage.htp) r1
            r9.l(r1, r2)
            goto L_0x0d09
        L_0x0d9a:
            java.lang.Object r0 = r8.c
            htp r0 = (defpackage.htp) r0
            java.lang.Object r0 = r9.k(r0)
            if (r0 != 0) goto L_0x0daf
            java.lang.Object r0 = r1.e()
            java.lang.Object r2 = r8.c
            htp r2 = (defpackage.htp) r2
            r9.m(r2, r0)
        L_0x0daf:
            r2 = r38
            r3 = r13
            r4 = r40
            r5 = r42
            int r0 = defpackage.hsg.q(r0, r1, r2, r3, r4, r5)
            goto L_0x0d09
        L_0x0dbc:
            int r0 = r6 << 3
            r4 = r0 | 4
            java.lang.Object r0 = r8.b
            hvj r1 = defpackage.hvj.a
            java.lang.Class r0 = r0.getClass()
            hvp r0 = r1.a(r0)
            boolean r1 = r8.q()
            if (r1 == 0) goto L_0x0de8
            r1 = r38
            r2 = r13
            r3 = r40
            r5 = r42
            int r0 = defpackage.hsg.e(r0, r1, r2, r3, r4, r5)
            java.lang.Object r1 = r8.c
            java.lang.Object r2 = r12.c
            htp r1 = (defpackage.htp) r1
            r9.l(r1, r2)
            goto L_0x0d09
        L_0x0de8:
            java.lang.Object r1 = r8.c
            htp r1 = (defpackage.htp) r1
            java.lang.Object r1 = r9.k(r1)
            if (r1 != 0) goto L_0x0dfd
            java.lang.Object r1 = r0.e()
            java.lang.Object r2 = r8.c
            htp r2 = (defpackage.htp) r2
            r9.m(r2, r1)
        L_0x0dfd:
            r8 = r1
            r9 = r0
            r3 = r10
            r10 = r38
            r2 = r11
            r11 = r13
            r1 = r12
            r12 = r40
            r13 = r4
            r0 = r14
            r14 = r42
            int r4 = defpackage.hsg.p(r8, r9, r10, r11, r12, r13, r14)
            r8 = r0
            r9 = r2
            r10 = r3
            r0 = r4
            goto L_0x0ec8
        L_0x0e15:
            r3 = r10
            r2 = r11
            r1 = r12
            r0 = r14
            int r13 = defpackage.hsg.i(r15, r13, r1)
            java.lang.Object r4 = r1.c
            goto L_0x0e93
        L_0x0e21:
            r3 = r10
            r2 = r11
            r1 = r12
            r0 = r14
            int r13 = defpackage.hsg.o(r15, r13, r1)
            long r10 = r1.b
            int r4 = (r10 > r19 ? 1 : (r10 == r19 ? 0 : -1))
            if (r4 == 0) goto L_0x0e30
            goto L_0x0e32
        L_0x0e30:
            r5 = r16
        L_0x0e32:
            java.lang.Boolean r4 = java.lang.Boolean.valueOf(r5)
            goto L_0x0e93
        L_0x0e38:
            r3 = r10
            r2 = r11
            r1 = r12
            r0 = r14
            int r4 = r13 + 4
            int r5 = defpackage.hsg.d(r15, r13)
            java.lang.Integer r5 = java.lang.Integer.valueOf(r5)
            goto L_0x0e91
        L_0x0e47:
            r3 = r10
            r2 = r11
            r1 = r12
            r0 = r14
            int r4 = r13 + 8
            long r10 = defpackage.hsg.s(r15, r13)
            java.lang.Long r5 = java.lang.Long.valueOf(r10)
            goto L_0x0e91
        L_0x0e56:
            r3 = r10
            r2 = r11
            r1 = r12
            r0 = r14
            int r13 = defpackage.hsg.l(r15, r13, r1)
            int r4 = r1.a
            java.lang.Integer r4 = java.lang.Integer.valueOf(r4)
            goto L_0x0e93
        L_0x0e65:
            r3 = r10
            r2 = r11
            r1 = r12
            r0 = r14
            int r13 = defpackage.hsg.o(r15, r13, r1)
            long r4 = r1.b
            java.lang.Long r4 = java.lang.Long.valueOf(r4)
            goto L_0x0e93
        L_0x0e74:
            r3 = r10
            r2 = r11
            r1 = r12
            r0 = r14
            int r4 = r13 + 4
            float r5 = defpackage.hsg.b(r15, r13)
            java.lang.Float r5 = java.lang.Float.valueOf(r5)
            goto L_0x0e91
        L_0x0e83:
            r3 = r10
            r2 = r11
            r1 = r12
            r0 = r14
            int r4 = r13 + 8
            double r10 = defpackage.hsg.a(r15, r13)
            java.lang.Double r5 = java.lang.Double.valueOf(r10)
        L_0x0e91:
            r13 = r4
            r4 = r5
        L_0x0e93:
            boolean r5 = r8.q()
            if (r5 == 0) goto L_0x0ea1
            java.lang.Object r5 = r8.c
            htp r5 = (defpackage.htp) r5
            r9.l(r5, r4)
            goto L_0x0ea8
        L_0x0ea1:
            java.lang.Object r5 = r8.c
            htp r5 = (defpackage.htp) r5
            r9.m(r5, r4)
        L_0x0ea8:
            r8 = r0
            r9 = r2
            r10 = r3
            r0 = r13
            goto L_0x0ec8
        L_0x0ead:
            r2 = r40
            r21 = r3
            r3 = r10
            r1 = r12
            r0 = r14
            hvz r4 = d(r37)
            r8 = r0
            r0 = r21
            r1 = r38
            r9 = r2
            r2 = r13
            r10 = r3
            r3 = r40
            r5 = r42
            int r0 = defpackage.hsg.k(r0, r1, r2, r3, r4, r5)
        L_0x0ec8:
            r12 = r42
            r1 = r6
            r13 = r7
            r6 = r8
            r14 = r9
            r7 = r10
            r4 = r18
            r3 = r21
            r5 = r27
            r2 = r28
            r11 = r33
            goto L_0x001d
        L_0x0edb:
            r18 = r4
            r27 = r5
            r10 = r7
            r21 = r8
            r33 = r11
            r7 = r13
            r9 = r14
            r4 = 0
            r8 = r6
            r13 = r0
            r6 = r3
            r0 = r18
            r1 = 1048575(0xfffff, float:1.469367E-39)
        L_0x0eef:
            if (r5 == r1) goto L_0x0ef7
            long r1 = (long) r5
            r3 = r33
            r3.putInt(r10, r1, r0)
        L_0x0ef7:
            int r0 = r8.k
            r11 = r0
            r3 = r4
        L_0x0efb:
            int r0 = r8.l
            if (r11 >= r0) goto L_0x0f12
            int[] r0 = r8.j
            hvy r4 = r8.m
            r2 = r0[r11]
            r0 = r36
            r1 = r37
            r5 = r37
            java.lang.Object r3 = r0.A(r1, r2, r3, r4, r5)
            int r11 = r11 + 1
            goto L_0x0efb
        L_0x0f12:
            if (r3 == 0) goto L_0x0f19
            hvz r3 = (defpackage.hvz) r3
            defpackage.hvy.g(r10, r3)
        L_0x0f19:
            if (r7 != 0) goto L_0x0f26
            if (r13 != r9) goto L_0x0f1e
            goto L_0x0f2c
        L_0x0f1e:
            hui r0 = new hui
            r1 = r21
            r0.<init>((java.lang.String) r1)
            throw r0
        L_0x0f26:
            r1 = r21
            if (r13 > r9) goto L_0x0f2d
            if (r6 != r7) goto L_0x0f2d
        L_0x0f2c:
            return r13
        L_0x0f2d:
            hui r0 = new hui
            r0.<init>((java.lang.String) r1)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.hvd.c(java.lang.Object, byte[], int, int, int, hsf):int");
    }

    public final Object e() {
        return ((htq) this.g).n();
    }

    public final void g(Object obj) {
        if (Q(obj)) {
            if (obj instanceof htq) {
                htq htq = (htq) obj;
                htq.memoizedSerializedSize = (htq.memoizedSerializedSize & Integer.MIN_VALUE) | Preference.DEFAULT_ORDER;
                htq.memoizedHashCode = 0;
                htq.y();
            }
            int[] iArr = this.c;
            for (int i2 = 0; i2 < iArr.length; i2 += 3) {
                int v = v(i2);
                long w = w(v);
                int u = u(v);
                if (u != 9) {
                    if (u == 60 || u == 68) {
                        if (R(obj, p(i2), i2)) {
                            z(i2).g(b.getObject(obj, w));
                        }
                    } else {
                        switch (u) {
                            case 17:
                                break;
                            case 18:
                            case 19:
                            case 20:
                            case 21:
                            case 22:
                            case 23:
                            case 24:
                            case 25:
                            case 26:
                            case 27:
                            case 28:
                            case 29:
                            case 30:
                            case 31:
                            case 32:
                            case 33:
                            case 34:
                            case 35:
                            case 36:
                            case 37:
                            case 38:
                            case 39:
                            case 40:
                            case 41:
                            case 42:
                            case 43:
                            case 44:
                            case 45:
                            case 46:
                            case 47:
                            case 48:
                            case 49:
                                hzz.v(obj, w).b();
                                continue;
                            case 50:
                                Unsafe unsafe = b;
                                Object object = unsafe.getObject(obj, w);
                                if (object != null) {
                                    ((huv) object).c();
                                    unsafe.putObject(obj, w, object);
                                    break;
                                } else {
                                    continue;
                                }
                        }
                    }
                }
                if (N(obj, i2)) {
                    z(i2).g(b.getObject(obj, w));
                }
            }
            this.m.e(obj);
            if (this.h) {
                hzz.A(obj);
            }
        }
    }

    public final void h(Object obj, Object obj2) {
        E(obj);
        obj2.getClass();
        for (int i2 = 0; i2 < this.c.length; i2 += 3) {
            int v = v(i2);
            long w = w(v);
            int p = p(i2);
            switch (u(v)) {
                case 0:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.q(obj, w, hwe.b(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case 1:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.r(obj, w, hwe.c(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case 2:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.t(obj, w, hwe.f(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case 3:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.t(obj, w, hwe.f(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case 4:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.s(obj, w, hwe.d(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case 5:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.t(obj, w, hwe.f(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case 6:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.s(obj, w, hwe.d(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case 7:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.m(obj, w, hwe.w(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case 8:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.u(obj, w, hwe.h(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER:
                    F(obj, obj2, i2);
                    break;
                case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.u(obj, w, hwe.h(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.s(obj, w, hwe.d(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.s(obj, w, hwe.d(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.s(obj, w, hwe.d(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.t(obj, w, hwe.f(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case 15:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.s(obj, w, hwe.d(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case 16:
                    if (!N(obj2, i2)) {
                        break;
                    } else {
                        hwe.t(obj, w, hwe.f(obj2, w));
                        H(obj, i2);
                        break;
                    }
                case 17:
                    F(obj, obj2, i2);
                    break;
                case 18:
                case 19:
                case 20:
                case 21:
                case 22:
                case 23:
                case 24:
                case 25:
                case 26:
                case 27:
                case 28:
                case 29:
                case 30:
                case 31:
                case 32:
                case 33:
                case 34:
                case 35:
                case 36:
                case 37:
                case 38:
                case 39:
                case 40:
                case 41:
                case 42:
                case 43:
                case 44:
                case 45:
                case 46:
                case 47:
                case 48:
                case 49:
                    huf v2 = hzz.v(obj, w);
                    huf v3 = hzz.v(obj2, w);
                    int size = v2.size();
                    int size2 = v3.size();
                    if (size > 0 && size2 > 0) {
                        if (!v2.c()) {
                            v2 = v2.d(size2 + size);
                        }
                        v2.addAll(v3);
                    }
                    if (size > 0) {
                        v3 = v2;
                    }
                    hwe.u(obj, w, v3);
                    break;
                case 50:
                    hvy hvy = hvq.a;
                    hwe.u(obj, w, ftc.aP(hwe.h(obj, w), hwe.h(obj2, w)));
                    break;
                case 51:
                case 52:
                case 53:
                case 54:
                case 55:
                case 56:
                case 57:
                case 58:
                case 59:
                    if (!R(obj2, p, i2)) {
                        break;
                    } else {
                        hwe.u(obj, w, hwe.h(obj2, w));
                        I(obj, p, i2);
                        break;
                    }
                case 60:
                    G(obj, obj2, i2);
                    break;
                case 61:
                case 62:
                case 63:
                case 64:
                case 65:
                case 66:
                case 67:
                    if (!R(obj2, p, i2)) {
                        break;
                    } else {
                        hwe.u(obj, w, hwe.h(obj2, w));
                        I(obj, p, i2);
                        break;
                    }
                case 68:
                    G(obj, obj2, i2);
                    break;
            }
        }
        hvq.n(obj, obj2);
        if (this.h) {
            hvq.m(obj, obj2);
        }
    }

    public final void i(Object obj, byte[] bArr, int i2, int i3, hsf hsf) {
        c(obj, bArr, i2, i3, 0, hsf);
    }

    public final boolean j(Object obj, Object obj2) {
        boolean z;
        for (int i2 = 0; i2 < this.c.length; i2 += 3) {
            int v = v(i2);
            long w = w(v);
            switch (u(v)) {
                case 0:
                    if (L(obj, obj2, i2) && Double.doubleToLongBits(hwe.b(obj, w)) == Double.doubleToLongBits(hwe.b(obj2, w))) {
                        continue;
                    }
                case 1:
                    if (L(obj, obj2, i2) && Float.floatToIntBits(hwe.c(obj, w)) == Float.floatToIntBits(hwe.c(obj2, w))) {
                        continue;
                    }
                case 2:
                    if (L(obj, obj2, i2) && hwe.f(obj, w) == hwe.f(obj2, w)) {
                        continue;
                    }
                case 3:
                    if (L(obj, obj2, i2) && hwe.f(obj, w) == hwe.f(obj2, w)) {
                        continue;
                    }
                case 4:
                    if (L(obj, obj2, i2) && hwe.d(obj, w) == hwe.d(obj2, w)) {
                        continue;
                    }
                case 5:
                    if (L(obj, obj2, i2) && hwe.f(obj, w) == hwe.f(obj2, w)) {
                        continue;
                    }
                case 6:
                    if (L(obj, obj2, i2) && hwe.d(obj, w) == hwe.d(obj2, w)) {
                        continue;
                    }
                case 7:
                    if (L(obj, obj2, i2) && hwe.w(obj, w) == hwe.w(obj2, w)) {
                        continue;
                    }
                case 8:
                    if (L(obj, obj2, i2) && a.k(hwe.h(obj, w), hwe.h(obj2, w))) {
                        continue;
                    }
                case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER:
                    if (L(obj, obj2, i2) && a.k(hwe.h(obj, w), hwe.h(obj2, w))) {
                        continue;
                    }
                case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER:
                    if (L(obj, obj2, i2) && a.k(hwe.h(obj, w), hwe.h(obj2, w))) {
                        continue;
                    }
                case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER:
                    if (L(obj, obj2, i2) && hwe.d(obj, w) == hwe.d(obj2, w)) {
                        continue;
                    }
                case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER:
                    if (L(obj, obj2, i2) && hwe.d(obj, w) == hwe.d(obj2, w)) {
                        continue;
                    }
                case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER:
                    if (L(obj, obj2, i2) && hwe.d(obj, w) == hwe.d(obj2, w)) {
                        continue;
                    }
                case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER:
                    if (L(obj, obj2, i2) && hwe.f(obj, w) == hwe.f(obj2, w)) {
                        continue;
                    }
                case 15:
                    if (L(obj, obj2, i2) && hwe.d(obj, w) == hwe.d(obj2, w)) {
                        continue;
                    }
                case 16:
                    if (L(obj, obj2, i2) && hwe.f(obj, w) == hwe.f(obj2, w)) {
                        continue;
                    }
                case 17:
                    if (L(obj, obj2, i2) && a.k(hwe.h(obj, w), hwe.h(obj2, w))) {
                        continue;
                    }
                case 18:
                case 19:
                case 20:
                case 21:
                case 22:
                case 23:
                case 24:
                case 25:
                case 26:
                case 27:
                case 28:
                case 29:
                case 30:
                case 31:
                case 32:
                case 33:
                case 34:
                case 35:
                case 36:
                case 37:
                case 38:
                case 39:
                case 40:
                case 41:
                case 42:
                case 43:
                case 44:
                case 45:
                case 46:
                case 47:
                case 48:
                case 49:
                    z = a.k(hwe.h(obj, w), hwe.h(obj2, w));
                    break;
                case 50:
                    z = a.k(hwe.h(obj, w), hwe.h(obj2, w));
                    break;
                case 51:
                case 52:
                case 53:
                case 54:
                case 55:
                case 56:
                case 57:
                case 58:
                case 59:
                case 60:
                case 61:
                case 62:
                case 63:
                case 64:
                case 65:
                case 66:
                case 67:
                case 68:
                    long s = (long) (s(i2) & 1048575);
                    if (hwe.d(obj, s) == hwe.d(obj2, s) && a.k(hwe.h(obj, w), hwe.h(obj2, w))) {
                        continue;
                    }
            }
            if (!z) {
                return false;
            }
        }
        if (!hvy.f(obj).equals(hvy.f(obj2))) {
            return false;
        }
        if (this.h) {
            return hzz.x(obj).equals(hzz.x(obj2));
        }
        return true;
    }

    public final boolean k(Object obj) {
        int i2;
        int i3;
        Object obj2 = obj;
        int i4 = 0;
        int i5 = 0;
        int i6 = 1048575;
        while (i5 < this.k) {
            int i7 = this.j[i5];
            int p = p(i7);
            int v = v(i7);
            int i8 = this.c[i7 + 2];
            int i9 = i8 & 1048575;
            int i10 = 1 << (i8 >>> 20);
            if (i9 != i6) {
                if (i9 != 1048575) {
                    i4 = b.getInt(obj2, (long) i9);
                }
                i2 = i4;
                i3 = i9;
            } else {
                i3 = i6;
                i2 = i4;
            }
            if ((268435456 & v) != 0 && !O(obj, i7, i3, i2, i10)) {
                return false;
            }
            int u = u(v);
            if (u != 9 && u != 17) {
                if (u != 27) {
                    if (u == 60 || u == 68) {
                        if (R(obj2, p, i7) && !P(obj2, v, z(i7))) {
                            return false;
                        }
                    } else if (u != 49) {
                        if (u != 50) {
                            continue;
                        } else {
                            huv huv = (huv) hwe.h(obj2, w(v));
                            if (!huv.isEmpty() && ((hwh) ftc.aS(B(i7)).a).s == hwi.MESSAGE) {
                                hvp hvp = null;
                                for (Object next : huv.values()) {
                                    if (hvp == null) {
                                        hvp = hvj.a.a(next.getClass());
                                    }
                                    if (!hvp.k(next)) {
                                        return false;
                                    }
                                }
                                continue;
                            }
                        }
                    }
                }
                List list = (List) hwe.h(obj2, w(v));
                if (!list.isEmpty()) {
                    hvp z = z(i7);
                    for (int i11 = 0; i11 < list.size(); i11++) {
                        if (!z.k(list.get(i11))) {
                            return false;
                        }
                    }
                    continue;
                } else {
                    continue;
                }
            } else if (O(obj, i7, i3, i2, i10) && !P(obj2, v, z(i7))) {
                return false;
            }
            i5++;
            i6 = i3;
            i4 = i2;
        }
        if (!this.h || hzz.x(obj).i()) {
            return true;
        }
        return false;
    }

    /* JADX WARNING: Can't fix incorrect switch cases order */
    /* JADX WARNING: Code restructure failed: missing block: B:152:0x040c, code lost:
        throw new defpackage.hui("Unable to parse map entry.");
     */
    /* JADX WARNING: Failed to process nested try/catch */
    /* JADX WARNING: Missing exception handler attribute for start block: B:147:0x03fc */
    /* JADX WARNING: Removed duplicated region for block: B:259:0x0801 A[SYNTHETIC, Splitter:B:259:0x0801] */
    /* JADX WARNING: Removed duplicated region for block: B:268:0x0828  */
    /* JADX WARNING: Removed duplicated region for block: B:277:0x0407 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:278:0x080d A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:354:0x0012 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:361:0x0402 A[SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:370:? A[RETURN, SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:64:0x019b A[Catch:{ all -> 0x01ff }] */
    /* JADX WARNING: Removed duplicated region for block: B:65:0x01a4 A[Catch:{ all -> 0x01ff }] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void l(java.lang.Object r17, defpackage.hsv r18, defpackage.hte r19) {
        /*
            r16 = this;
            r7 = r16
            r8 = r17
            r9 = r18
            r10 = r19
            r19.getClass()
            E(r17)
            hvy r11 = r7.m
            r13 = 0
            r14 = 0
        L_0x0012:
            int r2 = r18.c()     // Catch:{ all -> 0x01ff }
            int r0 = r7.r(r2)     // Catch:{ all -> 0x01ff }
            r1 = 3
            r3 = 2147483647(0x7fffffff, float:NaN)
            r15 = 0
            if (r0 >= 0) goto L_0x0202
            if (r2 != r3) goto L_0x003c
            int r0 = r7.k
            r4 = r13
        L_0x0026:
            int r1 = r7.l
            if (r0 >= r1) goto L_0x0826
            int[] r1 = r7.j
            r3 = r1[r0]
            r1 = r16
            r2 = r17
            r5 = r11
            r6 = r17
            java.lang.Object r4 = r1.A(r2, r3, r4, r5, r6)
            int r0 = r0 + 1
            goto L_0x0026
        L_0x003c:
            boolean r0 = r7.h     // Catch:{ all -> 0x01ff }
            if (r0 != 0) goto L_0x0042
            r0 = 0
            goto L_0x0048
        L_0x0042:
            hva r0 = r7.g     // Catch:{ all -> 0x01ff }
            gnk r0 = r10.b(r0, r2)     // Catch:{ all -> 0x01ff }
        L_0x0048:
            if (r0 == 0) goto L_0x01da
            if (r14 != 0) goto L_0x0050
            htg r14 = defpackage.hzz.y(r17)     // Catch:{ all -> 0x01ff }
        L_0x0050:
            int r2 = r0.l()     // Catch:{ all -> 0x01ff }
            hwh r3 = r0.m()     // Catch:{ all -> 0x01ff }
            hwh r4 = defpackage.hwh.ENUM     // Catch:{ all -> 0x01ff }
            if (r3 != r4) goto L_0x0071
            int r1 = r18.f()     // Catch:{ all -> 0x01ff }
            hxn r3 = defpackage.hxn.b(r1)     // Catch:{ all -> 0x01ff }
            if (r3 != 0) goto L_0x006b
            java.lang.Object r13 = defpackage.hvq.h(r8, r2, r1, r13, r11)     // Catch:{ all -> 0x01ff }
            goto L_0x0012
        L_0x006b:
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x0071:
            hwh r2 = r0.m()     // Catch:{ all -> 0x01ff }
            int r2 = r2.ordinal()     // Catch:{ all -> 0x01ff }
            switch(r2) {
                case 0: goto L_0x018d;
                case 1: goto L_0x0184;
                case 2: goto L_0x017b;
                case 3: goto L_0x0172;
                case 4: goto L_0x0169;
                case 5: goto L_0x0160;
                case 6: goto L_0x0157;
                case 7: goto L_0x014e;
                case 8: goto L_0x0149;
                case 9: goto L_0x0100;
                case 10: goto L_0x00bf;
                case 11: goto L_0x00b9;
                case 12: goto L_0x00af;
                case 13: goto L_0x00a7;
                case 14: goto L_0x009d;
                case 15: goto L_0x0093;
                case 16: goto L_0x0089;
                case 17: goto L_0x007f;
                default: goto L_0x007c;
            }     // Catch:{ all -> 0x01ff }
        L_0x007c:
            r1 = 0
            goto L_0x0195
        L_0x007f:
            long r1 = r18.m()     // Catch:{ all -> 0x01ff }
            java.lang.Long r1 = java.lang.Long.valueOf(r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x0089:
            int r1 = r18.h()     // Catch:{ all -> 0x01ff }
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x0093:
            long r1 = r18.l()     // Catch:{ all -> 0x01ff }
            java.lang.Long r1 = java.lang.Long.valueOf(r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x009d:
            int r1 = r18.g()     // Catch:{ all -> 0x01ff }
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x00a7:
            java.lang.String r0 = "Shouldn't reach here."
            java.lang.IllegalStateException r1 = new java.lang.IllegalStateException     // Catch:{ all -> 0x01ff }
            r1.<init>(r0)     // Catch:{ all -> 0x01ff }
            throw r1     // Catch:{ all -> 0x01ff }
        L_0x00af:
            int r1 = r18.i()     // Catch:{ all -> 0x01ff }
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x00b9:
            hsq r1 = r18.o()     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x00bf:
            boolean r1 = r0.q()     // Catch:{ all -> 0x01ff }
            if (r1 != 0) goto L_0x00f4
            java.lang.Object r1 = r0.c     // Catch:{ all -> 0x01ff }
            htp r1 = (defpackage.htp) r1     // Catch:{ all -> 0x01ff }
            java.lang.Object r1 = r14.k(r1)     // Catch:{ all -> 0x01ff }
            boolean r2 = r1 instanceof defpackage.htq     // Catch:{ all -> 0x01ff }
            if (r2 == 0) goto L_0x00f4
            hvj r2 = defpackage.hvj.a     // Catch:{ all -> 0x01ff }
            hvp r2 = r2.b(r1)     // Catch:{ all -> 0x01ff }
            r3 = r1
            htq r3 = (defpackage.htq) r3     // Catch:{ all -> 0x01ff }
            boolean r3 = r3.B()     // Catch:{ all -> 0x01ff }
            if (r3 != 0) goto L_0x00ef
            java.lang.Object r3 = r2.e()     // Catch:{ all -> 0x01ff }
            r2.h(r3, r1)     // Catch:{ all -> 0x01ff }
            java.lang.Object r0 = r0.c     // Catch:{ all -> 0x01ff }
            htp r0 = (defpackage.htp) r0     // Catch:{ all -> 0x01ff }
            r14.m(r0, r3)     // Catch:{ all -> 0x01ff }
            r1 = r3
        L_0x00ef:
            r9.x(r1, r2, r10)     // Catch:{ all -> 0x01ff }
            goto L_0x0012
        L_0x00f4:
            java.lang.Object r1 = r0.b     // Catch:{ all -> 0x01ff }
            java.lang.Class r1 = r1.getClass()     // Catch:{ all -> 0x01ff }
            java.lang.Object r1 = r9.t(r1, r10)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x0100:
            boolean r2 = r0.q()     // Catch:{ all -> 0x01ff }
            if (r2 != 0) goto L_0x0135
            java.lang.Object r2 = r0.c     // Catch:{ all -> 0x01ff }
            htp r2 = (defpackage.htp) r2     // Catch:{ all -> 0x01ff }
            java.lang.Object r2 = r14.k(r2)     // Catch:{ all -> 0x01ff }
            boolean r3 = r2 instanceof defpackage.htq     // Catch:{ all -> 0x01ff }
            if (r3 == 0) goto L_0x0135
            hvj r1 = defpackage.hvj.a     // Catch:{ all -> 0x01ff }
            hvp r1 = r1.b(r2)     // Catch:{ all -> 0x01ff }
            r3 = r2
            htq r3 = (defpackage.htq) r3     // Catch:{ all -> 0x01ff }
            boolean r3 = r3.B()     // Catch:{ all -> 0x01ff }
            if (r3 != 0) goto L_0x0130
            java.lang.Object r3 = r1.e()     // Catch:{ all -> 0x01ff }
            r1.h(r3, r2)     // Catch:{ all -> 0x01ff }
            java.lang.Object r0 = r0.c     // Catch:{ all -> 0x01ff }
            htp r0 = (defpackage.htp) r0     // Catch:{ all -> 0x01ff }
            r14.m(r0, r3)     // Catch:{ all -> 0x01ff }
            r2 = r3
        L_0x0130:
            r9.w(r2, r1, r10)     // Catch:{ all -> 0x01ff }
            goto L_0x0012
        L_0x0135:
            java.lang.Object r2 = r0.b     // Catch:{ all -> 0x01ff }
            java.lang.Class r2 = r2.getClass()     // Catch:{ all -> 0x01ff }
            r9.N(r1)     // Catch:{ all -> 0x01ff }
            hvj r1 = defpackage.hvj.a     // Catch:{ all -> 0x01ff }
            hvp r1 = r1.a(r2)     // Catch:{ all -> 0x01ff }
            java.lang.Object r1 = r9.r(r1, r10)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x0149:
            java.lang.String r1 = r18.u()     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x014e:
            boolean r1 = r18.O()     // Catch:{ all -> 0x01ff }
            java.lang.Boolean r1 = java.lang.Boolean.valueOf(r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x0157:
            int r1 = r18.e()     // Catch:{ all -> 0x01ff }
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x0160:
            long r1 = r18.j()     // Catch:{ all -> 0x01ff }
            java.lang.Long r1 = java.lang.Long.valueOf(r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x0169:
            int r1 = r18.f()     // Catch:{ all -> 0x01ff }
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x0172:
            long r1 = r18.n()     // Catch:{ all -> 0x01ff }
            java.lang.Long r1 = java.lang.Long.valueOf(r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x017b:
            long r1 = r18.k()     // Catch:{ all -> 0x01ff }
            java.lang.Long r1 = java.lang.Long.valueOf(r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x0184:
            float r1 = r18.b()     // Catch:{ all -> 0x01ff }
            java.lang.Float r1 = java.lang.Float.valueOf(r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0195
        L_0x018d:
            double r1 = r18.a()     // Catch:{ all -> 0x01ff }
            java.lang.Double r1 = java.lang.Double.valueOf(r1)     // Catch:{ all -> 0x01ff }
        L_0x0195:
            boolean r2 = r0.q()     // Catch:{ all -> 0x01ff }
            if (r2 == 0) goto L_0x01a4
            java.lang.Object r0 = r0.c     // Catch:{ all -> 0x01ff }
            htp r0 = (defpackage.htp) r0     // Catch:{ all -> 0x01ff }
            r14.l(r0, r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0012
        L_0x01a4:
            hwh r2 = r0.m()     // Catch:{ all -> 0x01ff }
            int r2 = r2.ordinal()     // Catch:{ all -> 0x01ff }
            r3 = 9
            if (r2 == r3) goto L_0x01b5
            r3 = 10
            if (r2 == r3) goto L_0x01b5
            goto L_0x01d1
        L_0x01b5:
            java.lang.Object r2 = r0.c     // Catch:{ all -> 0x01ff }
            htp r2 = (defpackage.htp) r2     // Catch:{ all -> 0x01ff }
            java.lang.Object r2 = r14.k(r2)     // Catch:{ all -> 0x01ff }
            if (r2 == 0) goto L_0x01d1
            byte[] r3 = defpackage.hug.b     // Catch:{ all -> 0x01ff }
            hva r2 = (defpackage.hva) r2     // Catch:{ all -> 0x01ff }
            huz r2 = r2.bh()     // Catch:{ all -> 0x01ff }
            hva r1 = (defpackage.hva) r1     // Catch:{ all -> 0x01ff }
            huz r1 = r2.d(r1)     // Catch:{ all -> 0x01ff }
            hva r1 = r1.s()     // Catch:{ all -> 0x01ff }
        L_0x01d1:
            java.lang.Object r0 = r0.c     // Catch:{ all -> 0x01ff }
            htp r0 = (defpackage.htp) r0     // Catch:{ all -> 0x01ff }
            r14.m(r0, r1)     // Catch:{ all -> 0x01ff }
            goto L_0x0012
        L_0x01da:
            if (r13 != 0) goto L_0x01e0
            java.lang.Object r13 = r11.b(r8)     // Catch:{ all -> 0x01ff }
        L_0x01e0:
            boolean r0 = r11.a(r13, r9, r15)     // Catch:{ all -> 0x01ff }
            if (r0 != 0) goto L_0x0012
            int r0 = r7.k
            r4 = r13
        L_0x01e9:
            int r1 = r7.l
            if (r0 >= r1) goto L_0x0826
            int[] r1 = r7.j
            r3 = r1[r0]
            r1 = r16
            r2 = r17
            r5 = r11
            r6 = r17
            java.lang.Object r4 = r1.A(r2, r3, r4, r5, r6)
            int r0 = r0 + 1
            goto L_0x01e9
        L_0x01ff:
            r0 = move-exception
            goto L_0x082e
        L_0x0202:
            int r4 = r7.v(r0)     // Catch:{ all -> 0x01ff }
            int r5 = u(r4)     // Catch:{ huh -> 0x07fe }
            r6 = 2
            r15 = 1
            switch(r5) {
                case 0: goto L_0x07cd;
                case 1: goto L_0x07bc;
                case 2: goto L_0x07ab;
                case 3: goto L_0x079a;
                case 4: goto L_0x0789;
                case 5: goto L_0x0778;
                case 6: goto L_0x0767;
                case 7: goto L_0x0756;
                case 8: goto L_0x074d;
                case 9: goto L_0x073a;
                case 10: goto L_0x0729;
                case 11: goto L_0x0718;
                case 12: goto L_0x06f4;
                case 13: goto L_0x06e3;
                case 14: goto L_0x06d2;
                case 15: goto L_0x06c1;
                case 16: goto L_0x06b0;
                case 17: goto L_0x069d;
                case 18: goto L_0x068f;
                case 19: goto L_0x0681;
                case 20: goto L_0x0673;
                case 21: goto L_0x0665;
                case 22: goto L_0x0657;
                case 23: goto L_0x0649;
                case 24: goto L_0x063b;
                case 25: goto L_0x062d;
                case 26: goto L_0x060a;
                case 27: goto L_0x05cc;
                case 28: goto L_0x0594;
                case 29: goto L_0x0586;
                case 30: goto L_0x056c;
                case 31: goto L_0x055e;
                case 32: goto L_0x0550;
                case 33: goto L_0x0542;
                case 34: goto L_0x0534;
                case 35: goto L_0x0526;
                case 36: goto L_0x0518;
                case 37: goto L_0x050a;
                case 38: goto L_0x04fc;
                case 39: goto L_0x04ee;
                case 40: goto L_0x04e0;
                case 41: goto L_0x04d2;
                case 42: goto L_0x04c4;
                case 43: goto L_0x04b6;
                case 44: goto L_0x049c;
                case 45: goto L_0x048e;
                case 46: goto L_0x0480;
                case 47: goto L_0x0472;
                case 48: goto L_0x0464;
                case 49: goto L_0x0426;
                case 50: goto L_0x0375;
                case 51: goto L_0x0361;
                case 52: goto L_0x034d;
                case 53: goto L_0x033a;
                case 54: goto L_0x0327;
                case 55: goto L_0x0314;
                case 56: goto L_0x0301;
                case 57: goto L_0x02ee;
                case 58: goto L_0x02db;
                case 59: goto L_0x02d2;
                case 60: goto L_0x02c1;
                case 61: goto L_0x02b2;
                case 62: goto L_0x029f;
                case 63: goto L_0x0279;
                case 64: goto L_0x0266;
                case 65: goto L_0x0252;
                case 66: goto L_0x023e;
                case 67: goto L_0x022a;
                case 68: goto L_0x0218;
                default: goto L_0x020f;
            }
        L_0x020f:
            r15 = 0
            if (r13 != 0) goto L_0x07de
            java.lang.Object r13 = r11.b(r8)     // Catch:{ huh -> 0x07ff }
            goto L_0x07de
        L_0x0218:
            java.lang.Object r1 = r7.D(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            hva r1 = (defpackage.hva) r1     // Catch:{ huh -> 0x07fe }
            hvp r3 = r7.z(r0)     // Catch:{ huh -> 0x07fe }
            r9.w(r1, r3, r10)     // Catch:{ huh -> 0x07fe }
            r7.K(r8, r2, r0, r1)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x022a:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            long r5 = r18.m()     // Catch:{ huh -> 0x07fe }
            java.lang.Long r1 = java.lang.Long.valueOf(r5)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x023e:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            int r1 = r18.h()     // Catch:{ huh -> 0x07fe }
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x0252:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            long r5 = r18.l()     // Catch:{ huh -> 0x07fe }
            java.lang.Long r1 = java.lang.Long.valueOf(r5)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x0266:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            int r1 = r18.g()     // Catch:{ huh -> 0x07fe }
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x0279:
            int r1 = r18.d()     // Catch:{ huh -> 0x07fe }
            htu r3 = r7.y(r0)     // Catch:{ huh -> 0x07fe }
            if (r3 == 0) goto L_0x0290
            boolean r3 = r3.a(r1)     // Catch:{ huh -> 0x07fe }
            if (r3 == 0) goto L_0x028a
            goto L_0x0290
        L_0x028a:
            java.lang.Object r13 = defpackage.hvq.h(r8, r2, r1, r13, r11)     // Catch:{ huh -> 0x07fe }
            goto L_0x0012
        L_0x0290:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x029f:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            int r1 = r18.i()     // Catch:{ huh -> 0x07fe }
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x02b2:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            hsq r1 = r18.o()     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x02c1:
            java.lang.Object r1 = r7.D(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            hva r1 = (defpackage.hva) r1     // Catch:{ huh -> 0x07fe }
            hvp r3 = r7.z(r0)     // Catch:{ huh -> 0x07fe }
            r9.x(r1, r3, r10)     // Catch:{ huh -> 0x07fe }
            r7.K(r8, r2, r0, r1)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x02d2:
            r7.T(r8, r4, r9)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
        L_0x02d8:
            r15 = 0
            goto L_0x0012
        L_0x02db:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            boolean r1 = r18.O()     // Catch:{ huh -> 0x07fe }
            java.lang.Boolean r1 = java.lang.Boolean.valueOf(r1)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x02ee:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            int r1 = r18.e()     // Catch:{ huh -> 0x07fe }
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x0301:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            long r5 = r18.j()     // Catch:{ huh -> 0x07fe }
            java.lang.Long r1 = java.lang.Long.valueOf(r5)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x0314:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            int r1 = r18.f()     // Catch:{ huh -> 0x07fe }
            java.lang.Integer r1 = java.lang.Integer.valueOf(r1)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x0327:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            long r5 = r18.n()     // Catch:{ huh -> 0x07fe }
            java.lang.Long r1 = java.lang.Long.valueOf(r5)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x033a:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            long r5 = r18.k()     // Catch:{ huh -> 0x07fe }
            java.lang.Long r1 = java.lang.Long.valueOf(r5)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x034d:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            float r1 = r18.b()     // Catch:{ huh -> 0x07fe }
            java.lang.Float r1 = java.lang.Float.valueOf(r1)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x0361:
            long r3 = w(r4)     // Catch:{ huh -> 0x07fe }
            double r5 = r18.a()     // Catch:{ huh -> 0x07fe }
            java.lang.Double r1 = java.lang.Double.valueOf(r5)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r3, r1)     // Catch:{ huh -> 0x07fe }
            r7.I(r8, r2, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x02d8
        L_0x0375:
            java.lang.Object r1 = r7.B(r0)     // Catch:{ huh -> 0x07fe }
            int r0 = r7.v(r0)     // Catch:{ huh -> 0x07fe }
            long r4 = w(r0)     // Catch:{ huh -> 0x07fe }
            java.lang.Object r0 = defpackage.hwe.h(r8, r4)     // Catch:{ huh -> 0x07fe }
            if (r0 != 0) goto L_0x038f
            java.lang.Object r0 = defpackage.ftc.aQ()     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r4, r0)     // Catch:{ huh -> 0x07fe }
            goto L_0x03a0
        L_0x038f:
            boolean r2 = defpackage.ftc.aO(r0)     // Catch:{ huh -> 0x07fe }
            if (r2 == 0) goto L_0x03a0
            java.lang.Object r2 = defpackage.ftc.aQ()     // Catch:{ huh -> 0x07fe }
            defpackage.ftc.aP(r2, r0)     // Catch:{ huh -> 0x07fe }
            defpackage.hwe.u(r8, r4, r2)     // Catch:{ huh -> 0x07fe }
            r0 = r2
        L_0x03a0:
            huv r0 = (defpackage.huv) r0     // Catch:{ huh -> 0x07fe }
            gnk r1 = defpackage.ftc.aS(r1)     // Catch:{ huh -> 0x07fe }
            r9.N(r6)     // Catch:{ huh -> 0x07fe }
            java.lang.Object r2 = r9.c     // Catch:{ huh -> 0x07fe }
            hsu r2 = (defpackage.hsu) r2     // Catch:{ huh -> 0x07fe }
            int r2 = r2.n()     // Catch:{ huh -> 0x07fe }
            java.lang.Object r4 = r9.c     // Catch:{ huh -> 0x07fe }
            hsu r4 = (defpackage.hsu) r4     // Catch:{ huh -> 0x07fe }
            int r2 = r4.e(r2)     // Catch:{ huh -> 0x07fe }
            java.lang.Object r4 = r1.b     // Catch:{ huh -> 0x07fe }
            java.lang.Object r5 = r1.d     // Catch:{ huh -> 0x07fe }
        L_0x03bd:
            int r12 = r18.c()     // Catch:{ all -> 0x041c }
            if (r12 == r3) goto L_0x040d
            java.lang.Object r3 = r9.c     // Catch:{ all -> 0x041c }
            hsu r3 = (defpackage.hsu) r3     // Catch:{ all -> 0x041c }
            boolean r3 = r3.C()     // Catch:{ all -> 0x041c }
            if (r3 == 0) goto L_0x03ce
            goto L_0x040d
        L_0x03ce:
            java.lang.String r3 = "Unable to parse map entry."
            if (r12 == r15) goto L_0x03f1
            if (r12 == r6) goto L_0x03e2
            boolean r12 = r18.P()     // Catch:{ huh -> 0x03fb }
            if (r12 == 0) goto L_0x03dc
            r15 = 0
            goto L_0x0402
        L_0x03dc:
            hui r12 = new hui     // Catch:{ huh -> 0x03fb }
            r12.<init>((java.lang.String) r3)     // Catch:{ huh -> 0x03fb }
            throw r12     // Catch:{ huh -> 0x03fb }
        L_0x03e2:
            java.lang.Object r12 = r1.a     // Catch:{ huh -> 0x03fb }
            java.lang.Object r15 = r1.d     // Catch:{ huh -> 0x03fb }
            java.lang.Class r15 = r15.getClass()     // Catch:{ huh -> 0x03fb }
            hwh r12 = (defpackage.hwh) r12     // Catch:{ huh -> 0x03fb }
            java.lang.Object r5 = r9.q(r12, r15, r10)     // Catch:{ huh -> 0x03fb }
            goto L_0x0402
        L_0x03f1:
            java.lang.Object r12 = r1.c     // Catch:{ huh -> 0x03fb }
            hwh r12 = (defpackage.hwh) r12     // Catch:{ huh -> 0x03fb }
            r15 = 0
            java.lang.Object r4 = r9.q(r12, r15, r15)     // Catch:{ huh -> 0x03fc }
            goto L_0x0402
        L_0x03fb:
            r15 = 0
        L_0x03fc:
            boolean r12 = r18.P()     // Catch:{ all -> 0x041a }
            if (r12 == 0) goto L_0x0407
        L_0x0402:
            r3 = 2147483647(0x7fffffff, float:NaN)
            r15 = 1
            goto L_0x03bd
        L_0x0407:
            hui r0 = new hui     // Catch:{ all -> 0x041a }
            r0.<init>((java.lang.String) r3)     // Catch:{ all -> 0x041a }
            throw r0     // Catch:{ all -> 0x041a }
        L_0x040d:
            r15 = 0
            r0.put(r4, r5)     // Catch:{ all -> 0x041a }
            java.lang.Object r0 = r9.c     // Catch:{ huh -> 0x07ff }
            hsu r0 = (defpackage.hsu) r0     // Catch:{ huh -> 0x07ff }
            r0.A(r2)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x041a:
            r0 = move-exception
            goto L_0x041e
        L_0x041c:
            r0 = move-exception
            r15 = 0
        L_0x041e:
            java.lang.Object r1 = r9.c     // Catch:{ huh -> 0x07ff }
            hsu r1 = (defpackage.hsu) r1     // Catch:{ huh -> 0x07ff }
            r1.A(r2)     // Catch:{ huh -> 0x07ff }
            throw r0     // Catch:{ huh -> 0x07ff }
        L_0x0426:
            r15 = 0
            long r2 = w(r4)     // Catch:{ huh -> 0x07ff }
            hvp r0 = r7.z(r0)     // Catch:{ huh -> 0x07ff }
            java.util.List r2 = defpackage.hzz.w(r8, r2)     // Catch:{ huh -> 0x07ff }
            int r3 = r9.a     // Catch:{ huh -> 0x07ff }
            int r4 = defpackage.hwj.b(r3)     // Catch:{ huh -> 0x07ff }
            if (r4 != r1) goto L_0x045e
        L_0x043b:
            java.lang.Object r1 = r9.r(r0, r10)     // Catch:{ huh -> 0x07ff }
            r2.add(r1)     // Catch:{ huh -> 0x07ff }
            java.lang.Object r1 = r9.c     // Catch:{ huh -> 0x07ff }
            hsu r1 = (defpackage.hsu) r1     // Catch:{ huh -> 0x07ff }
            boolean r1 = r1.C()     // Catch:{ huh -> 0x07ff }
            if (r1 != 0) goto L_0x0012
            int r1 = r9.b     // Catch:{ huh -> 0x07ff }
            if (r1 != 0) goto L_0x0012
            java.lang.Object r1 = r9.c     // Catch:{ huh -> 0x07ff }
            hsu r1 = (defpackage.hsu) r1     // Catch:{ huh -> 0x07ff }
            int r1 = r1.m()     // Catch:{ huh -> 0x07ff }
            if (r1 == r3) goto L_0x043b
            r9.b = r1     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x045e:
            huh r0 = new huh     // Catch:{ huh -> 0x07ff }
            r0.<init>()     // Catch:{ huh -> 0x07ff }
            throw r0     // Catch:{ huh -> 0x07ff }
        L_0x0464:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.J(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0472:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.I(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0480:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.H(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x048e:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.G(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x049c:
            r15 = 0
            long r3 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r3 = defpackage.hzz.w(r8, r3)     // Catch:{ huh -> 0x07ff }
            r9.A(r3)     // Catch:{ huh -> 0x07ff }
            htu r4 = r7.y(r0)     // Catch:{ huh -> 0x07ff }
            r1 = r17
            r5 = r13
            r6 = r11
            java.lang.Object r13 = defpackage.hvq.g(r1, r2, r3, r4, r5, r6)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x04b6:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.L(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x04c4:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.y(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x04d2:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.B(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x04e0:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.C(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x04ee:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.E(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x04fc:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.M(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x050a:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.F(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0518:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.D(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0526:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.z(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0534:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.J(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0542:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.I(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0550:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.H(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x055e:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.G(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x056c:
            r15 = 0
            long r3 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r3 = defpackage.hzz.w(r8, r3)     // Catch:{ huh -> 0x07ff }
            r9.A(r3)     // Catch:{ huh -> 0x07ff }
            htu r4 = r7.y(r0)     // Catch:{ huh -> 0x07ff }
            r1 = r17
            r5 = r13
            r6 = r11
            java.lang.Object r13 = defpackage.hvq.g(r1, r2, r3, r4, r5, r6)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0586:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.L(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0594:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            int r1 = r9.a     // Catch:{ huh -> 0x07ff }
            int r1 = defpackage.hwj.b(r1)     // Catch:{ huh -> 0x07ff }
            if (r1 != r6) goto L_0x05c6
        L_0x05a5:
            hsq r1 = r18.o()     // Catch:{ huh -> 0x07ff }
            r0.add(r1)     // Catch:{ huh -> 0x07ff }
            java.lang.Object r1 = r9.c     // Catch:{ huh -> 0x07ff }
            hsu r1 = (defpackage.hsu) r1     // Catch:{ huh -> 0x07ff }
            boolean r1 = r1.C()     // Catch:{ huh -> 0x07ff }
            if (r1 != 0) goto L_0x0012
            java.lang.Object r1 = r9.c     // Catch:{ huh -> 0x07ff }
            hsu r1 = (defpackage.hsu) r1     // Catch:{ huh -> 0x07ff }
            int r1 = r1.m()     // Catch:{ huh -> 0x07ff }
            int r2 = r9.a     // Catch:{ huh -> 0x07ff }
            if (r1 == r2) goto L_0x05a5
            r9.b = r1     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x05c6:
            huh r0 = new huh     // Catch:{ huh -> 0x07ff }
            r0.<init>()     // Catch:{ huh -> 0x07ff }
            throw r0     // Catch:{ huh -> 0x07ff }
        L_0x05cc:
            r15 = 0
            hvp r0 = r7.z(r0)     // Catch:{ huh -> 0x07ff }
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r1 = defpackage.hzz.w(r8, r1)     // Catch:{ huh -> 0x07ff }
            int r2 = r9.a     // Catch:{ huh -> 0x07ff }
            int r3 = defpackage.hwj.b(r2)     // Catch:{ huh -> 0x07ff }
            if (r3 != r6) goto L_0x0604
        L_0x05e1:
            java.lang.Object r3 = r9.s(r0, r10)     // Catch:{ huh -> 0x07ff }
            r1.add(r3)     // Catch:{ huh -> 0x07ff }
            java.lang.Object r3 = r9.c     // Catch:{ huh -> 0x07ff }
            hsu r3 = (defpackage.hsu) r3     // Catch:{ huh -> 0x07ff }
            boolean r3 = r3.C()     // Catch:{ huh -> 0x07ff }
            if (r3 != 0) goto L_0x0012
            int r3 = r9.b     // Catch:{ huh -> 0x07ff }
            if (r3 != 0) goto L_0x0012
            java.lang.Object r3 = r9.c     // Catch:{ huh -> 0x07ff }
            hsu r3 = (defpackage.hsu) r3     // Catch:{ huh -> 0x07ff }
            int r3 = r3.m()     // Catch:{ huh -> 0x07ff }
            if (r3 == r2) goto L_0x05e1
            r9.b = r3     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0604:
            huh r0 = new huh     // Catch:{ huh -> 0x07ff }
            r0.<init>()     // Catch:{ huh -> 0x07ff }
            throw r0     // Catch:{ huh -> 0x07ff }
        L_0x060a:
            r15 = 0
            boolean r0 = M(r4)     // Catch:{ huh -> 0x07ff }
            if (r0 == 0) goto L_0x061f
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r1 = 1
            r9.K(r0, r1)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x061f:
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r1 = 0
            r9.K(r0, r1)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x062d:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.y(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x063b:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.B(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0649:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.C(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0657:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.E(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0665:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.M(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0673:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.F(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0681:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.D(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x068f:
            r15 = 0
            long r0 = w(r4)     // Catch:{ huh -> 0x07ff }
            java.util.List r0 = defpackage.hzz.w(r8, r0)     // Catch:{ huh -> 0x07ff }
            r9.z(r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x069d:
            r15 = 0
            java.lang.Object r1 = r7.C(r8, r0)     // Catch:{ huh -> 0x07ff }
            hva r1 = (defpackage.hva) r1     // Catch:{ huh -> 0x07ff }
            hvp r2 = r7.z(r0)     // Catch:{ huh -> 0x07ff }
            r9.w(r1, r2, r10)     // Catch:{ huh -> 0x07ff }
            r7.J(r8, r0, r1)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x06b0:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            long r3 = r18.m()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.t(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x06c1:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            int r3 = r18.h()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.s(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x06d2:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            long r3 = r18.l()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.t(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x06e3:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            int r3 = r18.g()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.s(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x06f4:
            r15 = 0
            int r1 = r18.d()     // Catch:{ huh -> 0x07ff }
            htu r3 = r7.y(r0)     // Catch:{ huh -> 0x07ff }
            if (r3 == 0) goto L_0x070c
            boolean r3 = r3.a(r1)     // Catch:{ huh -> 0x07ff }
            if (r3 == 0) goto L_0x0706
            goto L_0x070c
        L_0x0706:
            java.lang.Object r13 = defpackage.hvq.h(r8, r2, r1, r13, r11)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x070c:
            long r2 = w(r4)     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.s(r8, r2, r1)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0718:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            int r3 = r18.i()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.s(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0729:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            hsq r3 = r18.o()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.u(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x073a:
            r15 = 0
            java.lang.Object r1 = r7.C(r8, r0)     // Catch:{ huh -> 0x07ff }
            hva r1 = (defpackage.hva) r1     // Catch:{ huh -> 0x07ff }
            hvp r2 = r7.z(r0)     // Catch:{ huh -> 0x07ff }
            r9.x(r1, r2, r10)     // Catch:{ huh -> 0x07ff }
            r7.J(r8, r0, r1)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x074d:
            r15 = 0
            r7.T(r8, r4, r9)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0756:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            boolean r3 = r18.O()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.m(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0767:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            int r3 = r18.e()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.s(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0778:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            long r3 = r18.j()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.t(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x0789:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            int r3 = r18.f()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.s(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x079a:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            long r3 = r18.n()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.t(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x07ab:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            long r3 = r18.k()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.t(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x07bc:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            float r3 = r18.b()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.r(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x07cd:
            r15 = 0
            long r1 = w(r4)     // Catch:{ huh -> 0x07ff }
            double r3 = r18.a()     // Catch:{ huh -> 0x07ff }
            defpackage.hwe.q(r8, r1, r3)     // Catch:{ huh -> 0x07ff }
            r7.H(r8, r0)     // Catch:{ huh -> 0x07ff }
            goto L_0x0012
        L_0x07de:
            r1 = 0
            boolean r0 = r11.a(r13, r9, r1)     // Catch:{ huh -> 0x07ff }
            if (r0 != 0) goto L_0x0012
            int r0 = r7.k
            r4 = r13
        L_0x07e8:
            int r1 = r7.l
            if (r0 >= r1) goto L_0x0826
            int[] r1 = r7.j
            r3 = r1[r0]
            r1 = r16
            r2 = r17
            r5 = r11
            r6 = r17
            java.lang.Object r4 = r1.A(r2, r3, r4, r5, r6)
            int r0 = r0 + 1
            goto L_0x07e8
        L_0x07fe:
            r15 = 0
        L_0x07ff:
            if (r13 != 0) goto L_0x0806
            java.lang.Object r0 = r11.b(r8)     // Catch:{ all -> 0x01ff }
            r13 = r0
        L_0x0806:
            r1 = 0
            boolean r0 = r11.a(r13, r9, r1)     // Catch:{ all -> 0x01ff }
            if (r0 != 0) goto L_0x0012
            int r0 = r7.k
            r4 = r13
        L_0x0810:
            int r1 = r7.l
            if (r0 >= r1) goto L_0x0826
            int[] r1 = r7.j
            r3 = r1[r0]
            r1 = r16
            r2 = r17
            r5 = r11
            r6 = r17
            java.lang.Object r4 = r1.A(r2, r3, r4, r5, r6)
            int r0 = r0 + 1
            goto L_0x0810
        L_0x0826:
            if (r4 == 0) goto L_0x082d
            hvz r4 = (defpackage.hvz) r4
            defpackage.hvy.g(r8, r4)
        L_0x082d:
            return
        L_0x082e:
            int r1 = r7.k
            r9 = r1
            r4 = r13
        L_0x0832:
            int r1 = r7.l
            if (r9 >= r1) goto L_0x0848
            int[] r1 = r7.j
            r3 = r1[r9]
            r1 = r16
            r2 = r17
            r5 = r11
            r6 = r17
            java.lang.Object r4 = r1.A(r2, r3, r4, r5, r6)
            int r9 = r9 + 1
            goto L_0x0832
        L_0x0848:
            if (r4 == 0) goto L_0x084f
            hvz r4 = (defpackage.hvz) r4
            defpackage.hvy.g(r8, r4)
        L_0x084f:
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.hvd.l(java.lang.Object, hsv, hte):void");
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v5, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v5, resolved type: java.util.Map$Entry} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v139, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r14v4, resolved type: java.util.Map$Entry} */
    /* JADX WARNING: Multi-variable type inference failed */
    /* JADX WARNING: Removed duplicated region for block: B:180:0x064b  */
    /* JADX WARNING: Removed duplicated region for block: B:9:0x002f  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void m(java.lang.Object r22, defpackage.cxi r23) {
        /*
            r21 = this;
            r6 = r21
            r7 = r22
            r8 = r23
            boolean r0 = r6.h
            if (r0 == 0) goto L_0x0020
            htg r0 = defpackage.hzz.x(r22)
            boolean r1 = r0.h()
            if (r1 != 0) goto L_0x0020
            java.util.Iterator r0 = r0.d()
            java.lang.Object r1 = r0.next()
            java.util.Map$Entry r1 = (java.util.Map.Entry) r1
            r10 = r0
            goto L_0x0022
        L_0x0020:
            r1 = 0
            r10 = 0
        L_0x0022:
            int[] r11 = r6.c
            sun.misc.Unsafe r12 = b
            r13 = 1048575(0xfffff, float:1.469367E-39)
            r0 = r13
            r2 = 0
            r15 = 0
        L_0x002c:
            int r3 = r11.length
            if (r15 >= r3) goto L_0x0647
            int r3 = r6.v(r15)
            int r5 = r6.p(r15)
            int r4 = u(r3)
            r9 = 17
            if (r4 > r9) goto L_0x0066
            int[] r9 = r6.c
            int r17 = r15 + 2
            r9 = r9[r17]
            r14 = r9 & r13
            if (r14 == r0) goto L_0x0059
            if (r14 != r13) goto L_0x004f
            r18 = r1
            r2 = 0
            goto L_0x0057
        L_0x004f:
            r18 = r1
            long r0 = (long) r14
            int r0 = r12.getInt(r7, r0)
            r2 = r0
        L_0x0057:
            r0 = r14
            goto L_0x005b
        L_0x0059:
            r18 = r1
        L_0x005b:
            int r1 = r9 >>> 20
            r9 = 1
            int r1 = r9 << r1
            r9 = r0
            r19 = r1
            r14 = r18
            goto L_0x006d
        L_0x0066:
            r18 = r1
            r9 = r0
            r14 = r18
            r19 = 0
        L_0x006d:
            r18 = r2
        L_0x006f:
            if (r14 == 0) goto L_0x008e
            java.lang.Object r0 = r14.getKey()
            htp r0 = (defpackage.htp) r0
            int r0 = r0.a
            if (r0 > r5) goto L_0x008e
            defpackage.hzz.O(r8, r14)
            boolean r0 = r10.hasNext()
            if (r0 == 0) goto L_0x008c
            java.lang.Object r0 = r10.next()
            r14 = r0
            java.util.Map$Entry r14 = (java.util.Map.Entry) r14
            goto L_0x006f
        L_0x008c:
            r14 = 0
            goto L_0x006f
        L_0x008e:
            long r2 = w(r3)
            switch(r4) {
                case 0: goto L_0x061c;
                case 1: goto L_0x05fe;
                case 2: goto L_0x05e0;
                case 3: goto L_0x05c1;
                case 4: goto L_0x05a2;
                case 5: goto L_0x0583;
                case 6: goto L_0x0564;
                case 7: goto L_0x0545;
                case 8: goto L_0x0526;
                case 9: goto L_0x0503;
                case 10: goto L_0x04e2;
                case 11: goto L_0x04c3;
                case 12: goto L_0x04a4;
                case 13: goto L_0x0485;
                case 14: goto L_0x0466;
                case 15: goto L_0x0447;
                case 16: goto L_0x0428;
                case 17: goto L_0x0403;
                case 18: goto L_0x03f3;
                case 19: goto L_0x03e3;
                case 20: goto L_0x03d3;
                case 21: goto L_0x03c3;
                case 22: goto L_0x03b3;
                case 23: goto L_0x03a3;
                case 24: goto L_0x0393;
                case 25: goto L_0x0383;
                case 26: goto L_0x0374;
                case 27: goto L_0x0361;
                case 28: goto L_0x0352;
                case 29: goto L_0x0342;
                case 30: goto L_0x0332;
                case 31: goto L_0x0322;
                case 32: goto L_0x0312;
                case 33: goto L_0x0302;
                case 34: goto L_0x02f2;
                case 35: goto L_0x02e2;
                case 36: goto L_0x02d2;
                case 37: goto L_0x02c2;
                case 38: goto L_0x02b2;
                case 39: goto L_0x02a2;
                case 40: goto L_0x0292;
                case 41: goto L_0x0282;
                case 42: goto L_0x0272;
                case 43: goto L_0x0262;
                case 44: goto L_0x0252;
                case 45: goto L_0x0242;
                case 46: goto L_0x0232;
                case 47: goto L_0x0222;
                case 48: goto L_0x0212;
                case 49: goto L_0x01ff;
                case 50: goto L_0x01ab;
                case 51: goto L_0x019c;
                case 52: goto L_0x018d;
                case 53: goto L_0x017e;
                case 54: goto L_0x016f;
                case 55: goto L_0x0160;
                case 56: goto L_0x0151;
                case 57: goto L_0x0142;
                case 58: goto L_0x0133;
                case 59: goto L_0x0124;
                case 60: goto L_0x0111;
                case 61: goto L_0x0101;
                case 62: goto L_0x00f3;
                case 63: goto L_0x00e5;
                case 64: goto L_0x00d7;
                case 65: goto L_0x00c9;
                case 66: goto L_0x00bb;
                case 67: goto L_0x00ad;
                case 68: goto L_0x009b;
                default: goto L_0x0095;
            }
        L_0x0095:
            r20 = r11
            r16 = r14
            goto L_0x0639
        L_0x009b:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            java.lang.Object r0 = r12.getObject(r7, r2)
            hvp r1 = r6.z(r15)
            r8.z(r5, r0, r1)
            goto L_0x0095
        L_0x00ad:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            long r0 = x(r7, r2)
            r8.H(r5, r0)
            goto L_0x0095
        L_0x00bb:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            int r0 = q(r7, r2)
            r8.G(r5, r0)
            goto L_0x0095
        L_0x00c9:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            long r0 = x(r7, r2)
            r8.F(r5, r0)
            goto L_0x0095
        L_0x00d7:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            int r0 = q(r7, r2)
            r8.E(r5, r0)
            goto L_0x0095
        L_0x00e5:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            int r0 = q(r7, r2)
            r8.v(r5, r0)
            goto L_0x0095
        L_0x00f3:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            int r0 = q(r7, r2)
            r8.J(r5, r0)
            goto L_0x0095
        L_0x0101:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            java.lang.Object r0 = r12.getObject(r7, r2)
            hsq r0 = (defpackage.hsq) r0
            r8.t(r5, r0)
            goto L_0x0095
        L_0x0111:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            java.lang.Object r0 = r12.getObject(r7, r2)
            hvp r1 = r6.z(r15)
            r8.C(r5, r0, r1)
            goto L_0x0095
        L_0x0124:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            java.lang.Object r0 = r12.getObject(r7, r2)
            V(r5, r0, r8)
            goto L_0x0095
        L_0x0133:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            boolean r0 = S(r7, r2)
            r8.s(r5, r0)
            goto L_0x0095
        L_0x0142:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            int r0 = q(r7, r2)
            r8.w(r5, r0)
            goto L_0x0095
        L_0x0151:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            long r0 = x(r7, r2)
            r8.x(r5, r0)
            goto L_0x0095
        L_0x0160:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            int r0 = q(r7, r2)
            r8.A(r5, r0)
            goto L_0x0095
        L_0x016f:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            long r0 = x(r7, r2)
            r8.K(r5, r0)
            goto L_0x0095
        L_0x017e:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            long r0 = x(r7, r2)
            r8.B(r5, r0)
            goto L_0x0095
        L_0x018d:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            float r0 = o(r7, r2)
            r8.y(r5, r0)
            goto L_0x0095
        L_0x019c:
            boolean r0 = r6.R(r7, r5, r15)
            if (r0 == 0) goto L_0x0095
            double r0 = n(r7, r2)
            r8.u(r5, r0)
            goto L_0x0095
        L_0x01ab:
            java.lang.Object r0 = r12.getObject(r7, r2)
            if (r0 == 0) goto L_0x0095
            java.lang.Object r1 = r6.B(r15)
            gnk r1 = defpackage.ftc.aS(r1)
            huv r0 = (defpackage.huv) r0
            java.lang.Object r2 = r8.a
            java.util.Set r0 = r0.entrySet()
            java.util.Iterator r0 = r0.iterator()
        L_0x01c5:
            boolean r2 = r0.hasNext()
            if (r2 == 0) goto L_0x0095
            java.lang.Object r2 = r0.next()
            java.util.Map$Entry r2 = (java.util.Map.Entry) r2
            java.lang.Object r3 = r8.a
            hsz r3 = (defpackage.hsz) r3
            r4 = 2
            r3.A(r5, r4)
            java.lang.Object r3 = r8.a
            java.lang.Object r4 = r2.getKey()
            java.lang.Object r13 = r2.getValue()
            int r4 = defpackage.dku.D(r1, r4, r13)
            hsz r3 = (defpackage.hsz) r3
            r3.C(r4)
            java.lang.Object r3 = r8.a
            java.lang.Object r4 = r2.getKey()
            java.lang.Object r2 = r2.getValue()
            hsz r3 = (defpackage.hsz) r3
            defpackage.dku.E(r3, r1, r4, r2)
            r13 = 1048575(0xfffff, float:1.469367E-39)
            goto L_0x01c5
        L_0x01ff:
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            hvp r2 = r6.z(r15)
            defpackage.hvq.v(r0, r1, r8, r2)
            goto L_0x0095
        L_0x0212:
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            r4 = 1
            defpackage.hvq.C(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0222:
            r4 = 1
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.B(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0232:
            r4 = 1
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.A(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0242:
            r4 = 1
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.z(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0252:
            r4 = 1
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.r(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0262:
            r4 = 1
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.E(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0272:
            r4 = 1
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.o(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0282:
            r4 = 1
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.s(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0292:
            r4 = 1
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.t(r0, r1, r8, r4)
            goto L_0x0095
        L_0x02a2:
            r4 = 1
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.w(r0, r1, r8, r4)
            goto L_0x0095
        L_0x02b2:
            r4 = 1
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.F(r0, r1, r8, r4)
            goto L_0x0095
        L_0x02c2:
            r4 = 1
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.x(r0, r1, r8, r4)
            goto L_0x0095
        L_0x02d2:
            r4 = 1
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.u(r0, r1, r8, r4)
            goto L_0x0095
        L_0x02e2:
            r4 = 1
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.q(r0, r1, r8, r4)
            goto L_0x0095
        L_0x02f2:
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            r4 = 0
            defpackage.hvq.C(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0302:
            r4 = 0
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.B(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0312:
            r4 = 0
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.A(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0322:
            r4 = 0
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.z(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0332:
            r4 = 0
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.r(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0342:
            r4 = 0
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.E(r0, r1, r8, r4)
            goto L_0x0095
        L_0x0352:
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.p(r0, r1, r8)
            goto L_0x0095
        L_0x0361:
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            hvp r2 = r6.z(r15)
            defpackage.hvq.y(r0, r1, r8, r2)
            goto L_0x0095
        L_0x0374:
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.D(r0, r1, r8)
            goto L_0x0095
        L_0x0383:
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            r13 = 0
            defpackage.hvq.o(r0, r1, r8, r13)
            goto L_0x0095
        L_0x0393:
            r13 = 0
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.s(r0, r1, r8, r13)
            goto L_0x0095
        L_0x03a3:
            r13 = 0
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.t(r0, r1, r8, r13)
            goto L_0x0095
        L_0x03b3:
            r13 = 0
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.w(r0, r1, r8, r13)
            goto L_0x0095
        L_0x03c3:
            r13 = 0
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.F(r0, r1, r8, r13)
            goto L_0x0095
        L_0x03d3:
            r13 = 0
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.x(r0, r1, r8, r13)
            goto L_0x0095
        L_0x03e3:
            r13 = 0
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.u(r0, r1, r8, r13)
            goto L_0x0095
        L_0x03f3:
            r13 = 0
            int r0 = r6.p(r15)
            java.lang.Object r1 = r12.getObject(r7, r2)
            java.util.List r1 = (java.util.List) r1
            defpackage.hvq.q(r0, r1, r8, r13)
            goto L_0x0095
        L_0x0403:
            r13 = 0
            r0 = r21
            r1 = r22
            r3 = r2
            r2 = r15
            r16 = r14
            r13 = r3
            r3 = r9
            r4 = r18
            r20 = r11
            r11 = r5
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            java.lang.Object r0 = r12.getObject(r7, r13)
            hvp r1 = r6.z(r15)
            r8.z(r11, r0, r1)
            goto L_0x0639
        L_0x0428:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            long r0 = r12.getLong(r7, r13)
            r8.H(r11, r0)
            goto L_0x0639
        L_0x0447:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            int r0 = r12.getInt(r7, r13)
            r8.G(r11, r0)
            goto L_0x0639
        L_0x0466:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            long r0 = r12.getLong(r7, r13)
            r8.F(r11, r0)
            goto L_0x0639
        L_0x0485:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            int r0 = r12.getInt(r7, r13)
            r8.E(r11, r0)
            goto L_0x0639
        L_0x04a4:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            int r0 = r12.getInt(r7, r13)
            r8.v(r11, r0)
            goto L_0x0639
        L_0x04c3:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            int r0 = r12.getInt(r7, r13)
            r8.J(r11, r0)
            goto L_0x0639
        L_0x04e2:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            java.lang.Object r0 = r12.getObject(r7, r13)
            hsq r0 = (defpackage.hsq) r0
            r8.t(r11, r0)
            goto L_0x0639
        L_0x0503:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            java.lang.Object r0 = r12.getObject(r7, r13)
            hvp r1 = r6.z(r15)
            r8.C(r11, r0, r1)
            goto L_0x0639
        L_0x0526:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            java.lang.Object r0 = r12.getObject(r7, r13)
            V(r11, r0, r8)
            goto L_0x0639
        L_0x0545:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            boolean r0 = defpackage.hwe.w(r7, r13)
            r8.s(r11, r0)
            goto L_0x0639
        L_0x0564:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            int r0 = r12.getInt(r7, r13)
            r8.w(r11, r0)
            goto L_0x0639
        L_0x0583:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            long r0 = r12.getLong(r7, r13)
            r8.x(r11, r0)
            goto L_0x0639
        L_0x05a2:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            int r0 = r12.getInt(r7, r13)
            r8.A(r11, r0)
            goto L_0x0639
        L_0x05c1:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            long r0 = r12.getLong(r7, r13)
            r8.K(r11, r0)
            goto L_0x0639
        L_0x05e0:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            long r0 = r12.getLong(r7, r13)
            r8.B(r11, r0)
            goto L_0x0639
        L_0x05fe:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            float r0 = defpackage.hwe.c(r7, r13)
            r8.y(r11, r0)
            goto L_0x0639
        L_0x061c:
            r20 = r11
            r16 = r14
            r13 = r2
            r11 = r5
            r0 = r21
            r1 = r22
            r2 = r15
            r3 = r9
            r4 = r18
            r5 = r19
            boolean r0 = r0.O(r1, r2, r3, r4, r5)
            if (r0 == 0) goto L_0x0639
            double r0 = defpackage.hwe.b(r7, r13)
            r8.u(r11, r0)
        L_0x0639:
            int r15 = r15 + 3
            r0 = r9
            r1 = r16
            r2 = r18
            r11 = r20
            r13 = 1048575(0xfffff, float:1.469367E-39)
            goto L_0x002c
        L_0x0647:
            r18 = r1
        L_0x0649:
            if (r1 == 0) goto L_0x065e
            defpackage.hzz.O(r8, r1)
            boolean r0 = r10.hasNext()
            if (r0 == 0) goto L_0x065c
            java.lang.Object r0 = r10.next()
            r1 = r0
            java.util.Map$Entry r1 = (java.util.Map.Entry) r1
            goto L_0x0649
        L_0x065c:
            r1 = 0
            goto L_0x0649
        L_0x065e:
            hvz r0 = defpackage.hvy.f(r22)
            r0.f(r8)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.hvd.m(java.lang.Object, cxi):void");
    }
}
