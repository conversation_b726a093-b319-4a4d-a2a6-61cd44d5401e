package defpackage;

import java.util.concurrent.Executor;

/* renamed from: euy  reason: default package */
/* compiled from: PG */
public final class euy implements dxy {
    final /* synthetic */ eui a;
    final /* synthetic */ jnz b;
    final /* synthetic */ euz c;
    final /* synthetic */ jqh d;

    public euy(eui eui, jqh jqh, jnz jnz, euz euz) {
        this.a = eui;
        this.d = jqh;
        this.b = jnz;
        this.c = euz;
    }

    public final Executor a() {
        return this.c.b;
    }

    public final void b(dyc dyc) {
        dxz dxz;
        jnu.e(dyc, "audioData");
        if (dyc.b == 2) {
            this.a.a();
            this.d.O(jkd.a);
        } else if (this.a.b(dyc).isCancelled() && (dxz = (dxz) this.b.a) != null) {
            dxz.b.a();
        }
    }
}
