package defpackage;

import java.util.concurrent.Executor;

/* renamed from: eto  reason: default package */
/* compiled from: PG */
public final class eto {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/service/impl/StreamAudioClientResponseSender");
    public final int b;
    public final Runnable c;
    public final jix d;
    public final jqs e;
    public final jpp f = new jpp(false, jpt.a);
    public final jpp g = new jpp(false, jpt.a);
    private final hme h;
    private final Executor i;

    public eto(int i2, hme hme, Runnable runnable, jix jix, jqs jqs, Executor executor) {
        this.b = i2;
        this.h = hme;
        this.c = runnable;
        this.d = jix;
        this.e = jqs;
        this.i = executor;
        ((jiq) jix).e(new dqb(this, 16));
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x003b  */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0078  */
    /* JADX WARNING: Removed duplicated region for block: B:31:0x00cf  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0029  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object a(defpackage.jlr r10) {
        /*
            r9 = this;
            boolean r0 = r10 instanceof defpackage.etn
            if (r0 == 0) goto L_0x0013
            r0 = r10
            etn r0 = (defpackage.etn) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            etn r0 = new etn
            r0.<init>(r9, r10)
        L_0x0018:
            java.lang.Object r10 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            java.lang.String r3 = "sendDeactivateStatus"
            java.lang.String r4 = "com/google/android/libraries/search/audio/service/impl/StreamAudioClientResponseSender"
            java.lang.String r5 = "StreamAudioClientResponseSender.kt"
            java.lang.String r6 = "ALT.GrpcClientRespSndr"
            r7 = 1
            if (r2 == 0) goto L_0x003b
            if (r2 != r7) goto L_0x0033
            eto r0 = r0.d
            defpackage.jji.c(r10)     // Catch:{ all -> 0x0031 }
            goto L_0x0066
        L_0x0031:
            r10 = move-exception
            goto L_0x006c
        L_0x0033:
            java.lang.IllegalStateException r10 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r10.<init>(r0)
            throw r10
        L_0x003b:
            defpackage.jji.c(r10)
            hca r10 = a
            hco r10 = r10.c()
            hcr r2 = defpackage.hdg.a
            hco r10 = r10.h(r2, r6)
            r2 = 77
            hco r10 = r10.j(r4, r3, r2, r5)
            hby r10 = (defpackage.hby) r10
            int r2 = r9.b
            java.lang.String r8 = "#audio# sending audio client(%d) deactivate status once done"
            r10.s(r8, r2)
            hme r10 = r9.h     // Catch:{ all -> 0x006a }
            r0.d = r9     // Catch:{ all -> 0x006a }
            r0.c = r7     // Catch:{ all -> 0x006a }
            java.lang.Object r10 = defpackage.jqw.x(r10, r0)     // Catch:{ all -> 0x006a }
            if (r10 == r1) goto L_0x0069
            r0 = r9
        L_0x0066:
            dzg r10 = (defpackage.dzg) r10     // Catch:{ all -> 0x0031 }
            goto L_0x0070
        L_0x0069:
            return r1
        L_0x006a:
            r10 = move-exception
            r0 = r9
        L_0x006c:
            java.lang.Object r10 = defpackage.jji.b(r10)
        L_0x0070:
            java.lang.Throwable r1 = defpackage.jju.a(r10)
            java.lang.String r2 = "newBuilder(...)"
            if (r1 == 0) goto L_0x00af
            hca r10 = a
            hco r10 = r10.h()
            hcr r7 = defpackage.hdg.a
            hco r10 = r10.h(r7, r6)
            hby r10 = (defpackage.hby) r10
            hco r10 = r10.i(r1)
            r1 = 84
            hco r10 = r10.j(r4, r3, r1, r5)
            hby r10 = (defpackage.hby) r10
            int r1 = r0.b
            java.lang.String r3 = "#audio# unexpected fail while getting audio client(%d) deactivate status, sending failure"
            r10.s(r3, r1)
            dzg r10 = defpackage.dzg.c
            htk r10 = r10.l()
            defpackage.jnu.d(r10, r2)
            bzj r10 = defpackage.jnu.e(r10, "builder")
            dzf r1 = defpackage.dzf.FAILED_GETTING_DEACTIVATE_STATUS
            r10.F(r1)
            dzg r10 = r10.E()
        L_0x00af:
            dzg r10 = (defpackage.dzg) r10
            ete r1 = defpackage.ete.c
            htk r1 = r1.l()
            defpackage.jnu.d(r1, r2)
            dlv r1 = defpackage.jnu.e(r1, "builder")
            java.lang.String r2 = "value"
            defpackage.jnu.e(r10, r2)
            java.lang.Object r2 = r1.a
            htk r2 = (defpackage.htk) r2
            htq r3 = r2.b
            boolean r3 = r3.B()
            if (r3 != 0) goto L_0x00d2
            r2.u()
        L_0x00d2:
            htq r2 = r2.b
            ete r2 = (defpackage.ete) r2
            r10.getClass()
            r2.b = r10
            r10 = 3
            r2.a = r10
            ete r10 = r1.t()
            r0.b(r10)
            jkd r10 = defpackage.jkd.a
            return r10
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eto.a(jlr):java.lang.Object");
    }

    public final Object b(ete ete) {
        Object obj;
        if (this.f.b()) {
            return jji.b(new Throwable());
        }
        try {
            this.d.c(ete);
            obj = jkd.a;
        } catch (Throwable th) {
            obj = jji.b(th);
        }
        Throwable a2 = jju.a(obj);
        if (a2 == null) {
            return obj;
        }
        ((hby) ((hby) a.h().h(hdg.a, "ALT.GrpcClientRespSndr")).i(a2).j("com/google/android/libraries/search/audio/service/impl/StreamAudioClientResponseSender", "sendNext-IoAF18A", 120, "StreamAudioClientResponseSender.kt")).s("#audio# cannot send audio client(%d) data to the remote client, shutting down", this.b);
        if (!this.f.c()) {
            try {
                this.d.b(a2);
            } catch (Throwable th2) {
                jji.b(th2);
            }
            c();
        }
        return obj;
    }

    public final void c() {
        ((hby) a.f().h(hdg.a, "ALT.GrpcClientRespSndr").j("com/google/android/libraries/search/audio/service/impl/StreamAudioClientResponseSender", "shutdown", 94, "StreamAudioClientResponseSender.kt")).s("#audio# the client's(%d) communication channel shut down", this.b);
        this.i.execute(gof.h(new dqb(this, 15)));
    }
}
