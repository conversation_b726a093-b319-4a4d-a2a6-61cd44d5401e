package defpackage;

import android.app.Activity;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/* renamed from: bmo  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bmo implements bmk {
    public final /* synthetic */ Method a;
    public final /* synthetic */ Activity b;

    public /* synthetic */ bmo(Method method, Activity activity) {
        this.a = method;
        this.b = activity;
    }

    public final void a(bml bml) {
        try {
            this.a.invoke(this.b, new Object[]{bml});
        } catch (IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException("Couldn't call the MenuItem's listener", e);
        }
    }
}
