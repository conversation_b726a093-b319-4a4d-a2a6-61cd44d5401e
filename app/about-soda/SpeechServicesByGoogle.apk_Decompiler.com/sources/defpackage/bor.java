package defpackage;

/* renamed from: bor  reason: default package */
/* compiled from: PG */
public final class bor extends htq implements hvb {
    public static final bor h;
    private static volatile hvh j;
    public int a;
    public bou b;
    public hyc c;
    public bos d;
    public long e;
    public long f;
    public int g;
    private byte i = 2;

    static {
        bor bor = new bor();
        h = bor;
        htq.z(bor.class, bor);
    }

    private bor() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i2, Object obj) {
        int i3 = i2 - 1;
        if (i3 == 0) {
            return Byte.valueOf(this.i);
        }
        byte b2 = 1;
        if (i3 == 2) {
            return new hvl(h, "\u0001\u0003\u0000\u0001\u0001\u0004\u0003\u0000\u0000\u0003\u0001ᐉ\u0000\u0002ᐉ\u0001\u0004ᐉ\u0002", new Object[]{"a", "b", "c", "d"});
        } else if (i3 == 3) {
            return new bor();
        } else {
            if (i3 == 4) {
                return new htk((htq) h);
            }
            if (i3 == 5) {
                return h;
            }
            if (i3 != 6) {
                if (obj == null) {
                    b2 = 0;
                }
                this.i = b2;
                return null;
            }
            hvh hvh = j;
            if (hvh == null) {
                synchronized (bor.class) {
                    hvh = j;
                    if (hvh == null) {
                        hvh = new htl(h);
                        j = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
