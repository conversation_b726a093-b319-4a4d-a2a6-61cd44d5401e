package defpackage;

/* renamed from: csc  reason: default package */
/* compiled from: PG */
public final class csc extends htq implements hvb {
    public static final csc j;
    private static volatile hvh k;
    public int a;
    public String b = "";
    public String c = "";
    public int d;
    public long e;
    public csf f;
    public huf g = hvk.a;
    public long h;
    public String i = "";

    static {
        csc csc = new csc();
        j = csc;
        htq.z(csc.class, csc);
    }

    private csc() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i2, Object obj) {
        int i3 = i2 - 1;
        if (i3 == 0) {
            return (byte) 1;
        }
        if (i3 == 2) {
            return new hvl(j, "\u0001\b\u0000\u0001\u0001\u001a\b\u0000\u0001\u0000\u0001ဈ\u0000\u0002\u001b\u0003ဂ\t\u0006ဈ\u0001\nင\u0002\rဉ\u000b\u0017ဂ\u000f\u001aဈ\u0010", new Object[]{"a", "b", "g", csb.class, "e", "c", "d", "f", "h", "i"});
        } else if (i3 == 3) {
            return new csc();
        } else {
            if (i3 == 4) {
                return new htk((htq) j);
            }
            if (i3 == 5) {
                return j;
            }
            if (i3 != 6) {
                return null;
            }
            hvh hvh = k;
            if (hvh == null) {
                synchronized (csc.class) {
                    hvh = k;
                    if (hvh == null) {
                        hvh = new htl(j);
                        k = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
