package defpackage;

import java.util.List;
import java.util.concurrent.AbstractExecutorService;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/* renamed from: coo  reason: default package */
/* compiled from: PG */
public final class coo extends AbstractExecutorService {
    private final ExecutorService a;
    private final com b;

    private coo(ExecutorService executorService, com com2) {
        this.a = executorService;
        this.b = com2;
    }

    public static ExecutorService a(grh grh, ExecutorService executorService) {
        if (grh.f()) {
            return new coo(executorService, (com) grh.b());
        }
        return executorService;
    }

    public final boolean awaitTermination(long j, TimeUnit timeUnit) {
        return this.a.awaitTermination(j, timeUnit);
    }

    public final void execute(Runnable runnable) {
        col a2 = this.b.a();
        if (a2 == null) {
            this.a.execute(runnable);
        } else {
            this.a.execute(new ckm((Object) a2, (Object) runnable, 10));
        }
    }

    public final boolean isShutdown() {
        return this.a.isShutdown();
    }

    public final boolean isTerminated() {
        return this.a.isTerminated();
    }

    public final void shutdown() {
        this.a.shutdown();
    }

    public final List shutdownNow() {
        return this.a.shutdownNow();
    }

    public final String toString() {
        return this.a.toString();
    }
}
