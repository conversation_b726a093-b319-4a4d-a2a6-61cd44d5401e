package defpackage;

import java.util.concurrent.ExecutionException;

/* renamed from: bcu  reason: default package */
/* compiled from: PG */
public final class bcu implements Runnable {
    private final /* synthetic */ int a;
    private final Object b;
    private final Object c;

    public bcu(ipj ipj, Throwable th, int i) {
        this.a = i;
        this.b = ipj;
        this.c = th;
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v0, types: [jlr, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v1, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v10, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v4, types: [jlr, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v5, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v21, types: [java.lang.Object, java.lang.Runnable] */
    /* JADX WARNING: type inference failed for: r0v32, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v26, types: [jlr, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v37, types: [jlr, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v27, types: [java.util.concurrent.Future, java.lang.Object] */
    public final void run() {
        int i = this.a;
        if (i != 0) {
            if (i != 1) {
                if (i == 2) {
                    try {
                        this.c.run();
                        synchronized (((bih) this.b).b) {
                            ((bih) this.b).a();
                        }
                    } catch (Throwable th) {
                        synchronized (((bih) this.b).b) {
                            ((bih) this.b).a();
                            throw th;
                        }
                    }
                } else if (i == 3) {
                    synchronized (((bis) this.c).d) {
                        if (((bcu) ((bis) this.c).b.remove(this.b)) != null) {
                            bir bir = (bir) ((bis) this.c).c.remove(this.b);
                            if (bir != null) {
                                bir.b((bgt) this.b);
                            }
                        } else {
                            bbk.a();
                            String.format("Timer with %s is already marked as complete.", new Object[]{this.b});
                        }
                    }
                } else if (i == 4) {
                    ((ipj) this.b).j((Throwable) this.c);
                } else if (i == 5) {
                    Object obj = this.b;
                    jqb jqb = (jqb) obj;
                    jqb.w((jqp) this.c, jkd.a);
                } else if (this.b.isCancelled()) {
                    ((jqb) this.c).A((Throwable) null);
                } else {
                    try {
                        this.c.bE(a.h(this.b));
                    } catch (ExecutionException e) {
                        this.c.bE(jji.b(jqw.y(e)));
                    }
                }
            } else if (this.b.isCancelled()) {
                ((jqb) this.c).A((Throwable) null);
            } else {
                try {
                    ((jqb) this.c).bE(a.h(this.b));
                } catch (ExecutionException e2) {
                    this.c.bE(jji.b(kq.e(e2)));
                }
            }
        } else if (this.b.isCancelled()) {
            ((jqb) this.c).A((Throwable) null);
        } else {
            try {
                ((jqb) this.c).bE(bdy.b(this.b));
            } catch (ExecutionException e3) {
                this.c.bE(jji.b(bdy.c(e3)));
            }
        }
    }

    public bcu(Object obj, Object obj2, int i) {
        this.a = i;
        this.c = obj;
        this.b = obj2;
    }

    public bcu(Object obj, Runnable runnable, int i) {
        this.a = i;
        this.b = obj;
        this.c = runnable;
    }
}
