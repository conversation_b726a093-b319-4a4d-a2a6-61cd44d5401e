package defpackage;

/* renamed from: bcm  reason: default package */
/* compiled from: PG */
public final class bcm extends avu {
    public static final bcm c = new bcm();

    private bcm() {
        super(6, 7);
    }

    public final void a(awl awl) {
        awl.g("\n    CREATE TABLE IF NOT EXISTS `WorkProgress` (`work_spec_id` TEXT NOT NULL, `progress`\n    BLOB NOT NULL, PRIMARY KEY(`work_spec_id`), FOREIGN KEY(`work_spec_id`)\n    REFERENCES `WorkSpec`(`id`) ON UPDATE CASCADE ON DELETE CASCADE )\n    ");
    }
}
