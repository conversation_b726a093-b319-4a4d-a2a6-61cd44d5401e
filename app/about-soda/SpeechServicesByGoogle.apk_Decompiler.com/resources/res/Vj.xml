<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:layout_width="wrap_content" android:layout_height="match_parent" style="@style/Widget.CarUi.Toolbar.Tab.Container">
    <ImageView android:id="@+id/car_ui_toolbar_tab_item_icon" android:layout_width="@dimen/car_ui_toolbar_tab_icon_width" android:layout_height="@dimen/car_ui_toolbar_tab_icon_height" style="@style/Widget.CarUi.Toolbar.Tab.Icon"/>
    <TextView android:id="@+id/car_ui_toolbar_tab_item_text" android:layout_width="@dimen/car_ui_toolbar_tab_text_width" android:layout_height="wrap_content" style="@style/Widget.CarUi.Toolbar.Tab.Text"/>
</LinearLayout>
