package com.google.android.apps.googlespeechoffline.speech;

import android.util.Log;
import com.google.android.libraries.assistant.soda.Soda;
import com.google.android.libraries.assistant.soda.SodaConfigBuilder;
import com.google.android.libraries.assistant.soda.data.SodaDataProviderJni;
import com.google.android.libraries.assistant.soda.SodaTransportFactory;
import com.google.speech.recognizer.RecognitionResultCallback;
import com.google.speech.recognizer.ResourceManager;
import com.google.speech.recognizer.SodaCleanParser;

import java.io.Closeable;
import java.io.IOException;
import java.nio.ByteBuffer;

/**
 * 自定义SODA实现，基于完整的SODA JNI API
 *
 * 实现完整的实时语音识别流程：
 * 1. 创建共享资源
 * 2. 构造SODA实例
 * 3. 设置数据提供器和传输工厂
 * 4. 初始化引擎
 * 5. 启动采集会话
 * 6. 持续投喂音频数据
 * 7. 处理识别结果回调
 * 8. 释放资源
 */
public class CustomSoda extends Soda implements Closeable {
    private static final String TAG = "CustomSoda";

    public final ResourceManager mResourceManager = new CustomResourceManager();

    // SODA实例管理
    private long sharedResHandle = 0;
    private long sodaHandle = 0;
    private boolean isInitialized = false;
    private boolean isCaptureStarted = false;

    // 配置和组件
    private SodaConfigBuilder configBuilder;
    private SodaDataProviderJni dataProvider;
    private SodaTransportFactory transportFactory;
    private RecognitionResultCallback recognitionCallback;

    // 音频参数
    private static final int SAMPLE_RATE = 16000;
    private static final int CHANNELS = 1;
    private static final String LANGUAGE = "en-US";

    // 时间戳管理
    private long audioSequenceNumber = 0;

    static {
        System.loadLibrary("google_speech_jni");
        System.loadLibrary("inject");
    }

    /**
     * 私有构造函数，使用create方法创建实例
     */
    private CustomSoda() {
        this.configBuilder = new SodaConfigBuilder()
                .setSampleRate(SAMPLE_RATE)
                .setChannels(CHANNELS)
                .setLanguage(LANGUAGE);
    }

    /**
     * 创建CustomSoda实例
     * @param modelPath 模型路径
     * @return CustomSoda实例
     */
    public static CustomSoda create(String modelPath) {
        Log.i(TAG, "Creating CustomSoda with model path: " + modelPath);

        CustomSoda soda = new CustomSoda();
        soda.configBuilder.setModelPath(modelPath);

        try {
            soda.initializeSoda(modelPath);
            return soda;
        } catch (Exception e) {
            Log.e(TAG, "Failed to create CustomSoda", e);
            try {
                soda.close();
            } catch (IOException closeException) {
                Log.e(TAG, "Failed to close CustomSoda during error cleanup", closeException);
            }
            return null;
        }
    }

    /**
     * 设置识别结果回调
     * @param callback 回调接口
     */
    public void setRecognitionCallback(RecognitionResultCallback callback) {
        this.recognitionCallback = callback;
        Log.d(TAG, "Recognition callback set");
    }

    /**
     * 初始化SODA引擎（按照完整的JNI API流程）
     * @param modelPath 模型路径
     */
    private void initializeSoda(String modelPath) throws Exception {
        Log.i(TAG, "Initializing SODA engine with complete JNI API...");

        // 1. 创建共享资源
        Log.d(TAG, "Step 1: Creating shared resources");
        sharedResHandle = nativeCreateSharedResources(this);
        if (sharedResHandle == 0) {
            throw new RuntimeException("Failed to create shared resources");
        }
        Log.d(TAG, "Shared resources created, handle: " + sharedResHandle);

        // 2. 构造SODA实例
        Log.d(TAG, "Step 2: Constructing SODA instance");
        sodaHandle = nativeConstruct(sharedResHandle);
        if (sodaHandle == 0) {
            throw new RuntimeException("Failed to construct SODA instance");
        }
        Log.d(TAG, "SODA instance constructed, handle: " + sodaHandle);

        // 3. 设置数据提供器和传输工厂（可选但推荐）
        Log.d(TAG, "Step 3: Setting up data provider and transport factory");
        setupDataProviderAndTransport();

        // 4. 初始化引擎
        Log.d(TAG, "Step 4: Initializing engine with config");
        initializeEngine(modelPath);

        // 5. 启动采集会话
        Log.d(TAG, "Step 5: Starting capture session");
        startCaptureSession();

        isInitialized = true;
        isCaptureStarted = true;
        Log.i(TAG, "SODA engine initialized successfully with complete JNI API");
    }

    /**
     * 设置数据提供器和传输工厂
     */
    private void setupDataProviderAndTransport() {
        // 设置数据提供器
        try {
            dataProvider = new SodaDataProviderJni(this);
            nativeSetDataProvider(sodaHandle, dataProvider);
            Log.d(TAG, "Data provider set successfully");
        } catch (Exception e) {
            Log.w(TAG, "Failed to setup data provider (optional component): " + e.getMessage());
            dataProvider = null;
        }

        // 设置传输工厂
        try {
            transportFactory = new SodaTransportFactory();
            if (transportFactory.initialize()) {
                nativeSetTransportFactory(sodaHandle, transportFactory);
                Log.d(TAG, "Transport factory set successfully");
            } else {
                Log.w(TAG, "Failed to initialize transport factory");
                transportFactory = null;
            }
        } catch (Exception e) {
            Log.w(TAG, "Failed to setup transport factory (optional component): " + e.getMessage());
            transportFactory = null;
        }

        Log.d(TAG, "Data provider and transport setup completed (optional components may have failed)");
    }

    /**
     * 初始化引擎配置
     * @param modelPath 模型路径
     */
    private void initializeEngine(String modelPath) throws Exception {
        // 尝试从配置文件读取配置
        String configFilePath = modelPath + "/configs/ONDEVICE_MEDIUM_CONTINUOUS.config";
        byte[] configBytes = configBuilder.buildConfigFromFile(configFilePath);

        if (configBytes == null) {
            Log.w(TAG, "Failed to load config file, using default config");
            configBytes = configBuilder.buildRecognitionConfig();
        }

        if (configBytes == null) {
            throw new RuntimeException("Failed to build configuration");
        }

        Log.d(TAG, "Initializing engine with config size: " + configBytes.length);
        byte[] initResult = nativeInit(sodaHandle, configBytes);

        if (initResult == null) {
            throw new RuntimeException("Failed to initialize SODA engine");
        }

        Log.d(TAG, "Engine initialized successfully, result size: " + initResult.length);
    }

    /**
     * 启动采集会话
     */
    private void startCaptureSession() throws Exception {
        byte[] startParams = configBuilder.buildStartParams();
        if (startParams == null) {
            throw new RuntimeException("Failed to build start parameters");
        }

        Log.d(TAG, "Starting capture with params size: " + startParams.length);
        byte[] startResult = nativeStartCapture(sodaHandle, startParams);

        if (startResult == null) {
            throw new RuntimeException("Failed to start capture session");
        }

        isCaptureStarted = true;
        Log.d(TAG, "Capture session started successfully, result size: " + startResult.length);
    }

    /**
     * 添加音频数据（实时识别的核心方法）
     * @param audioBuffer 音频缓冲区
     * @param byteCount 字节数
     * @return 是否成功
     */
    public boolean addAudio(ByteBuffer audioBuffer, int byteCount) {
        if (!isInitialized || !isCaptureStarted) {
            Log.w(TAG, "SODA not initialized or capture not started");
            return false;
        }

        if (audioBuffer == null || byteCount <= 0) {
            Log.w(TAG, "Invalid audio data");
            return false;
        }

        try {
            // 使用带时间戳的音频添加方法
            long currentTimeUsec = System.currentTimeMillis() * 1000;
            byte[] timestampMeta = configBuilder.buildTimestampMetadata(currentTimeUsec, audioSequenceNumber++);

            if (timestampMeta != null && timestampMeta.length == 16) {
                ByteBuffer tsMetaBuffer = ByteBuffer.allocateDirect(16);
                tsMetaBuffer.put(timestampMeta);
                tsMetaBuffer.flip();

                boolean success = nativeAddTimestampedAudio(sodaHandle, audioBuffer, byteCount,
                                                          tsMetaBuffer, 16);

                if (success) {
                    Log.v(TAG, "Successfully added timestamped audio: " + byteCount + " bytes, seq: " + (audioSequenceNumber - 1));
                } else {
                    Log.w(TAG, "Failed to add timestamped audio");
                }

                return success;
            } else {
                // 回退到普通的音频添加方法
                boolean success = nativeAddAudio(sodaHandle, audioBuffer, byteCount);

                if (success) {
                    Log.v(TAG, "Successfully added audio: " + byteCount + " bytes");
                } else {
                    Log.w(TAG, "Failed to add audio");
                }

                return success;
            }

        } catch (Exception e) {
            Log.e(TAG, "Exception while adding audio", e);
            return false;
        }
    }

    /**
     * 停止采集会话
     */
    public void stopCapture() {
        if (isCaptureStarted && sodaHandle != 0) {
            Log.d(TAG, "Stopping capture session");
            nativeStopCapture(sodaHandle);
            isCaptureStarted = false;
            Log.d(TAG, "Capture session stopped");
        }
    }

    /**
     * 检查是否需要重新初始化
     * @param newConfigBytes 新的配置数据
     * @return 是否需要重新初始化
     */
    public boolean requiresReinitialization(byte[] newConfigBytes) {
        if (!isInitialized || sodaHandle == 0) {
            return true;
        }

        try {
            return nativeRequiresReinitialization(sodaHandle, newConfigBytes);
        } catch (Exception e) {
            Log.e(TAG, "Failed to check reinitialization requirement", e);
            return true;
        }
    }

    // ========== JNI回调方法 ==========
    // 这些方法会被native层直接调用

    /**
     * 处理SODA事件回调
     * @param data 事件数据
     */
    @Override
    public void handleSodaEvent(byte[] data) {
        Log.v(TAG, "handleSodaEvent called, data length: " + (data != null ? data.length : 0));

        if (data != null && data.length > 0) {
            // 使用SodaCleanParser解析识别结果
            if (recognitionCallback != null) {
                SodaCleanParser.parseSodaResponse(data, recognitionCallback);
            } else {
                // 如果没有设置回调，使用默认处理
                SodaCleanParser.parseSodaResponse(data, new DefaultRecognitionCallback());
            }
        }
    }

    /**
     * 处理开始事件
     */
    @Override
    public void handleStart() {
        Log.d(TAG, "handleStart called");
        if (recognitionCallback != null) {
            recognitionCallback.onStartEvent();
        }
    }

    /**
     * 处理停止事件
     * @param code 停止代码
     */
    @Override
    public void handleStop(int code) {
        Log.d(TAG, "handleStop called with code: " + code);
        if (recognitionCallback != null) {
            recognitionCallback.onStopEvent();
        }
    }

    /**
     * 处理关闭事件
     */
    @Override
    public void handleShutdown() {
        Log.d(TAG, "handleShutdown called");
        if (recognitionCallback != null) {
            recognitionCallback.onShutdownEvent();
        }
    }

    /**
     * 默认识别回调实现
     */
    private static class DefaultRecognitionCallback implements RecognitionResultCallback {
        @Override
        public void onFinalResult(String hypothesis, String endpointReason) {
            Log.i(TAG, "[FINAL] " + hypothesis + " (reason: " + endpointReason + ")");
        }

        @Override
        public void onPartialResult(String hypothesis) {
            Log.d(TAG, "[PARTIAL] " + hypothesis);
        }

        @Override
        public void onEndpointEvent(String endpointType) {
            Log.d(TAG, "[ENDPOINT] " + endpointType);
        }

        @Override
        public void onAudioLevelInfo(float rms, float audioLevel) {
            Log.v(TAG, "[AUDIO_LEVEL] RMS: " + rms + ", Level: " + audioLevel);
        }

        @Override
        public void onLanguageIdEvent(String language, int confidenceLevel) {
            Log.d(TAG, "[LANGID] " + language + " (confidence: " + confidenceLevel + ")");
        }

        @Override
        public void onParseError(String error, byte[] rawData) {
            Log.e(TAG, "[ERROR] " + error);
        }
    }

    // ========== 资源管理方法 ==========

    /**
     * 获取初始化状态
     * @return 是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }

    /**
     * 获取采集状态
     * @return 是否正在采集
     */
    public boolean isCaptureStarted() {
        return isCaptureStarted;
    }

    /**
     * 获取SODA句柄
     * @return SODA句柄
     */
    public long getSodaHandle() {
        return sodaHandle;
    }

    /**
     * 获取共享资源句柄
     * @return 共享资源句柄
     */
    public long getSharedResourceHandle() {
        return sharedResHandle;
    }

    /**
     * 关闭并释放所有资源
     */
    @Override
    public void close() throws IOException {
        Log.i(TAG, "Closing CustomSoda and releasing resources");

        try {
            // 1. 停止采集会话
            if (isCaptureStarted) {
                stopCapture();
            }

            // 2. 删除SODA实例
            if (sodaHandle != 0) {
                Log.d(TAG, "Deleting SODA instance, handle: " + sodaHandle);
                nativeDelete(sodaHandle);
                sodaHandle = 0;
            }

            // 3. 清理组件
            if (dataProvider != null) {
                dataProvider.cleanup();
                dataProvider = null;
            }

            if (transportFactory != null) {
                transportFactory.cleanup();
                transportFactory = null;
            }

            // 4. 释放共享资源
            if (sharedResHandle != 0) {
                Log.d(TAG, "Deleting shared resources, handle: " + sharedResHandle);

                // 尝试条件删除（如果还有其他处理器则保留）
                boolean deleted = nativeDeleteSharedResourcesIfDiarizationProcessorAbsent(sharedResHandle);

                if (!deleted) {
                    // 如果条件删除失败，尝试直接删除
                    Log.d(TAG, "Conditional delete failed, trying direct delete");
                    deleted = nativeDeleteSharedResources(this, sharedResHandle);
                }

                if (deleted) {
                    Log.d(TAG, "Shared resources deleted successfully");
                    sharedResHandle = 0;
                } else {
                    Log.w(TAG, "Failed to delete shared resources");
                }
            }

            // 5. 重置状态
            isInitialized = false;
            isCaptureStarted = false;
            recognitionCallback = null;
            audioSequenceNumber = 0;

            Log.i(TAG, "CustomSoda closed successfully");

        } catch (Exception e) {
            Log.e(TAG, "Error during CustomSoda cleanup", e);
            throw new IOException("Failed to close CustomSoda", e);
        }
    }

    /**
     * 确保资源被正确释放
     */
    @Override
    protected void finalize() throws Throwable {
        try {
            if (isInitialized) {
                Log.w(TAG, "CustomSoda was not properly closed, cleaning up in finalize()");
                close();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error in finalize()", e);
        } finally {
            super.finalize();
        }
    }
}