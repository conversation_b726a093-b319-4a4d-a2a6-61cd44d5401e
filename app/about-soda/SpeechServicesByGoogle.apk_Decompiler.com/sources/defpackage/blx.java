package defpackage;

import com.android.car.ui.recyclerview.DefaultScrollBar;

/* renamed from: blx  reason: default package */
/* compiled from: PG */
public final class blx extends ke {
    final /* synthetic */ DefaultScrollBar a;

    public blx(DefaultScrollBar defaultScrollBar) {
        this.a = defaultScrollBar;
    }

    public final void onChanged() {
        this.a.clearCachedHeights();
        this.a.updatePaginationButtons();
    }

    public final void onItemRangeChanged(int i, int i2) {
        this.a.clearCachedHeights();
        this.a.updatePaginationButtons();
    }

    public final void onItemRangeInserted(int i, int i2) {
        this.a.clearCachedHeights();
        this.a.updatePaginationButtons();
    }

    public final void onItemRangeMoved(int i, int i2, int i3) {
        this.a.clearCachedHeights();
        this.a.updatePaginationButtons();
    }

    public final void onItemRangeRemoved(int i, int i2) {
        this.a.clearCachedHeights();
        this.a.updatePaginationButtons();
    }

    public final void onItemRangeChanged(int i, int i2, Object obj) {
        this.a.clearCachedHeights();
        this.a.updatePaginationButtons();
    }
}
