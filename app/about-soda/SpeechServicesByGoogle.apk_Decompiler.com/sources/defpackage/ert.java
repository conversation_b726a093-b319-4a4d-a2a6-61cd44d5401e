package defpackage;

/* renamed from: ert  reason: default package */
/* compiled from: PG */
final class ert extends jme {
    /* synthetic */ Object a;
    final /* synthetic */ eru b;
    int c;
    eru d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public ert(eru eru, jlr jlr) {
        super(jlr);
        this.b = eru;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.c |= Integer.MIN_VALUE;
        return this.b.h((dzx) null, this);
    }
}
