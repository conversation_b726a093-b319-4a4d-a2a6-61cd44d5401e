package defpackage;

import android.content.Context;
import android.text.TextWatcher;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;
import android.widget.ImageView;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.android.car.ui.toolbar.CarUiEditText;
import com.google.android.tts.R;
import j$.util.Objects;
import java.util.Collections;
import java.util.Set;

/* renamed from: bna  reason: default package */
/* compiled from: PG */
public final class bna extends ConstraintLayout {
    public final InputMethodManager h;
    public final bne i;
    public final ImageView j;
    public final EditText k;
    public CharSequence l;
    public boolean m;
    public final View n;
    public final int o;
    public final int p;
    public final int q;
    public Set r = Collections.emptySet();
    public Set s = Collections.emptySet();
    public Set t = Collections.emptySet();
    public Set u = Collections.emptySet();
    public boolean v;
    private final bny w = new bmz(this);
    private final TextWatcher x;
    private boolean y;

    public bna(Context context) {
        super(context, (AttributeSet) null, 0);
        bmy bmy = new bmy(this);
        this.x = bmy;
        this.v = false;
        this.y = false;
        this.h = (InputMethodManager) getContext().getSystemService(InputMethodManager.class);
        LayoutInflater.from(context).inflate(R.layout.car_ui_toolbar_search_view, this, true);
        EditText editText = (EditText) bnv.i(this, R.id.car_ui_toolbar_search_bar);
        this.k = editText;
        this.j = (ImageView) bnv.i(this, R.id.car_ui_toolbar_search_icon);
        View i2 = bnv.i(this, R.id.car_ui_toolbar_search_close);
        this.n = i2;
        i2.setOnClickListener(new gp(this, 17, (byte[]) null));
        i2.setVisibility(8);
        this.o = editText.getPaddingStart();
        int dimensionPixelSize = context.getResources().getDimensionPixelSize(R.dimen.car_ui_toolbar_search_search_icon_container_width);
        this.p = dimensionPixelSize;
        int dimensionPixelSize2 = context.getResources().getDimensionPixelSize(R.dimen.car_ui_toolbar_search_close_icon_container_width);
        this.q = dimensionPixelSize2;
        editText.setSaveEnabled(false);
        editText.setPaddingRelative(dimensionPixelSize, 0, dimensionPixelSize2, 0);
        editText.setOnClickListener(new gp(this, 18, (byte[]) null));
        editText.setOnFocusChangeListener(new bmw(this, 0));
        editText.addTextChangedListener(bmy);
        editText.setOnEditorActionListener(new bmx(this));
        bne bne = new bne(context);
        this.i = bne;
        Objects.requireNonNull(bne);
        new bme(bne, 2).accept(editText);
        bnb bnb = new bnb(bne);
        if (editText instanceof CarUiEditText) {
            ((CarUiEditText) editText).a = bnb;
        }
    }

    public final void d() {
        this.k.clearFocus();
        for (Runnable run : this.s) {
            run.run();
        }
        for (bnm a : this.u) {
            a.a();
        }
    }

    /* access modifiers changed from: protected */
    public final void onAttachedToWindow() {
        super.onAttachedToWindow();
        bnz.b(getContext()).c(this.w);
    }

    /* access modifiers changed from: protected */
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        bnz.b(getContext()).e(this.w);
    }

    public final void onVisibilityChanged(View view, int i2) {
        int i3;
        super.onVisibilityChanged(view, i2);
        boolean isShown = isShown();
        if (isShown && !this.y) {
            int length = this.k.getText().length();
            View view2 = this.n;
            if (length > 0) {
                i3 = 0;
            } else {
                i3 = 8;
            }
            view2.setVisibility(i3);
            this.k.requestFocus();
            this.h.showSoftInput(this.k, 0);
        } else if (!isShown && this.y) {
            this.h.hideSoftInputFromWindow(this.k.getWindowToken(), 0);
        }
        this.y = isShown;
    }
}
