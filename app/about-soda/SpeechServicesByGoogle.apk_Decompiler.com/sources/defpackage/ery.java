package defpackage;

/* renamed from: ery  reason: default package */
/* compiled from: PG */
public final class ery implements erq {
    private static final dzq b;
    public po a;
    private final hme c = kq.f(new bwl(this, 5));
    private final grh d;

    static {
        htk l = dzq.c.l();
        dzv dzv = dzv.a;
        if (!l.b.B()) {
            l.u();
        }
        dzq dzq = (dzq) l.b;
        dzv.getClass();
        dzq.b = dzv;
        dzq.a = 2;
        b = (dzq) l.r();
    }

    public ery(grh grh) {
        this.d = grh;
    }

    public final ejn a() {
        return new ejn(this.c, b, this.d, gqd.a);
    }

    public final synchronized hme b() {
        if (this.c.isDone()) {
            return eki.k(eaf.FAILED_ROUTING_DUE_TO_DISCONNECT_ALREADY_CALLED);
        }
        return eki.k(eaf.UPDATED);
    }

    public final synchronized hme c(dzx dzx) {
        if (this.c.isDone()) {
            return this.c;
        }
        htk l = dzm.c.l();
        if (!l.b.B()) {
            l.u();
        }
        dzm dzm = (dzm) l.b;
        dzm.b = dzx.H;
        dzm.a |= 1;
        this.a.c((dzm) l.r());
        return this.c;
    }

    public final /* synthetic */ ekt d() {
        return null;
    }

    public final /* synthetic */ erq e() {
        return this;
    }
}
