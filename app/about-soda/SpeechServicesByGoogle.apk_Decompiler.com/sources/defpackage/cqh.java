package defpackage;

import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.res.AssetManager;
import android.net.Uri;
import android.os.Process;
import android.util.Base64;
import android.util.JsonWriter;
import android.util.Log;
import com.google.android.tts.R;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.BufferOverflowException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledExecutorService;
import java.util.zip.CRC32;

/* renamed from: cqh  reason: default package */
/* compiled from: PG */
public final class cqh {
    public cqh() {
    }

    public static int A(int i) {
        if (i == 0) {
            return 1;
        }
        switch (i) {
            case 950:
                return 951;
            case 951:
                return 952;
            case 952:
                return 953;
            default:
                switch (i) {
                    case 1001:
                        return 1002;
                    case 1002:
                        return 1003;
                    case 1003:
                        return 1004;
                    case 1004:
                        return 1005;
                    case 1005:
                        return 1006;
                    default:
                        return 0;
                }
        }
    }

    public static SharedPreferences B(Context context, String str, grh grh) {
        return context.getSharedPreferences(E(str, grh), 0);
    }

    /* JADX WARNING: type inference failed for: r1v4, types: [hva, java.lang.Object] */
    public static hva C(String str, hvh hvh) {
        try {
            byte[] decode = Base64.decode(str, 3);
            hte hte = hte.a;
            hvj hvj = hvj.a;
            return hvh.g(decode, hte.a);
        } catch (IllegalArgumentException e) {
            throw new hui(new IOException(e), (byte[]) null);
        }
    }

    public static hva D(SharedPreferences sharedPreferences, String str, hvh hvh) {
        String string = sharedPreferences.getString(str, (String) null);
        if (string == null) {
            return null;
        }
        try {
            return C(string, hvh);
        } catch (hui unused) {
            return null;
        }
    }

    public static String E(String str, grh grh) {
        if (grh == null || !grh.f()) {
            return str;
        }
        return str.concat((String) grh.b());
    }

    public static String F(hva hva) {
        return Base64.encodeToString(hva.g(), 3);
    }

    public static void G(SharedPreferences.Editor editor, String str) {
        editor.remove(str);
    }

    public static void H(SharedPreferences.Editor editor, String str, hva hva) {
        editor.putString(str, F(hva));
    }

    public static boolean I(SharedPreferences sharedPreferences, String str) {
        return sharedPreferences.edit().remove(str).commit();
    }

    public static boolean J(SharedPreferences sharedPreferences, String str, hva hva) {
        SharedPreferences.Editor edit = sharedPreferences.edit();
        H(edit, str, hva);
        return edit.commit();
    }

    public static ctj K(String str, Context context, cuk cuk) {
        htk htk;
        List g = gry.c("|").g(str);
        int ordinal = u(context, cuk).ordinal();
        if (ordinal != 1) {
            if (ordinal != 2) {
                if (g.size() == 4) {
                    htk = ctj.g.l();
                    String str2 = (String) g.get(0);
                    if (!htk.b.B()) {
                        htk.u();
                    }
                    ctj ctj = (ctj) htk.b;
                    str2.getClass();
                    ctj.a |= 1;
                    ctj.b = str2;
                    int parseInt = Integer.parseInt((String) g.get(1));
                    if (!htk.b.B()) {
                        htk.u();
                    }
                    ctj ctj2 = (ctj) htk.b;
                    ctj2.a |= 2;
                    ctj2.c = parseInt;
                    String str3 = (String) g.get(2);
                    if (!htk.b.B()) {
                        htk.u();
                    }
                    ctj ctj3 = (ctj) htk.b;
                    str3.getClass();
                    ctj3.a |= 4;
                    ctj3.d = str3;
                    int x = a.x(Integer.parseInt((String) g.get(3)));
                    if (!htk.b.B()) {
                        htk.u();
                    }
                    ctj ctj4 = (ctj) htk.b;
                    int i = x - 1;
                    if (x != 0) {
                        ctj4.e = i;
                        ctj4.a |= 8;
                    } else {
                        throw null;
                    }
                } else {
                    throw new czh("Bad-format serializedFileKey = ".concat(String.valueOf(str)));
                }
            } else if (g.size() == 2) {
                htk = ctj.g.l();
                String str4 = (String) g.get(0);
                if (!htk.b.B()) {
                    htk.u();
                }
                ctj ctj5 = (ctj) htk.b;
                str4.getClass();
                ctj5.a |= 4;
                ctj5.d = str4;
                int x2 = a.x(Integer.parseInt((String) g.get(1)));
                if (!htk.b.B()) {
                    htk.u();
                }
                ctj ctj6 = (ctj) htk.b;
                int i2 = x2 - 1;
                if (x2 != 0) {
                    ctj6.e = i2;
                    ctj6.a |= 8;
                } else {
                    throw null;
                }
            } else {
                throw new czh("Bad-format serializedFileKey = s".concat(String.valueOf(str)));
            }
        } else if (g.size() == 5) {
            htk l = ctj.g.l();
            String str5 = (String) g.get(0);
            if (!l.b.B()) {
                l.u();
            }
            ctj ctj7 = (ctj) l.b;
            str5.getClass();
            ctj7.a |= 1;
            ctj7.b = str5;
            int parseInt2 = Integer.parseInt((String) g.get(1));
            if (!l.b.B()) {
                l.u();
            }
            ctj ctj8 = (ctj) l.b;
            ctj8.a |= 2;
            ctj8.c = parseInt2;
            String str6 = (String) g.get(2);
            if (!l.b.B()) {
                l.u();
            }
            ctj ctj9 = (ctj) l.b;
            str6.getClass();
            ctj9.a |= 4;
            ctj9.d = str6;
            int x3 = a.x(Integer.parseInt((String) g.get(3)));
            if (!l.b.B()) {
                l.u();
            }
            ctj ctj10 = (ctj) l.b;
            int i3 = x3 - 1;
            if (x3 != 0) {
                ctj10.e = i3;
                ctj10.a |= 8;
                if (g.get(4) != null && !((String) g.get(4)).isEmpty()) {
                    try {
                        ihf ihf = (ihf) C((String) g.get(4), (hvh) ihf.b.C(7));
                        if (!l.b.B()) {
                            l.u();
                        }
                        ctj ctj11 = (ctj) l.b;
                        ihf.getClass();
                        ctj11.f = ihf;
                        ctj11.a |= 16;
                    } catch (hui e) {
                        throw new czh("Failed to deserialize key:".concat(String.valueOf(str)), e);
                    }
                }
                htk = l;
            } else {
                throw null;
            }
        } else {
            throw new czh("Bad-format serializedFileKey = ".concat(String.valueOf(str)));
        }
        return (ctj) htk.r();
    }

    public static String L(ctj ctj, Context context, cuk cuk) {
        int ordinal = u(context, cuk).ordinal();
        if (ordinal == 0) {
            return M(ctj);
        }
        if (ordinal == 1) {
            return O(ctj);
        }
        if (ordinal != 2) {
            return M(ctj);
        }
        return N(ctj);
    }

    public static String M(ctj ctj) {
        StringBuilder sb = new StringBuilder(ctj.b);
        sb.append("|");
        sb.append(ctj.c);
        sb.append("|");
        sb.append(ctj.d);
        sb.append("|");
        int x = a.x(ctj.e);
        if (x == 0) {
            x = 1;
        }
        sb.append(x - 1);
        return sb.toString();
    }

    public static String N(ctj ctj) {
        StringBuilder sb = new StringBuilder(ctj.d);
        sb.append("|");
        int x = a.x(ctj.e);
        if (x == 0) {
            x = 1;
        }
        sb.append(x - 1);
        return sb.toString();
    }

    public static String O(ctj ctj) {
        String str;
        StringBuilder sb = new StringBuilder(ctj.b);
        sb.append("|");
        sb.append(ctj.c);
        sb.append("|");
        sb.append(ctj.d);
        sb.append("|");
        int x = a.x(ctj.e);
        if (x == 0) {
            x = 1;
        }
        sb.append(x - 1);
        sb.append("|");
        if ((ctj.a & 16) != 0) {
            ihf ihf = ctj.f;
            if (ihf == null) {
                ihf = ihf.b;
            }
            str = F(ihf);
        } else {
            str = "";
        }
        sb.append(str);
        return sb.toString();
    }

    public static ByteBuffer P(Iterable iterable) {
        String str;
        Iterator it = iterable.iterator();
        long j = 0;
        int i = 0;
        int i2 = 0;
        while (it.hasNext()) {
            j += (long) (((hva) it.next()).k() + 12);
            i2++;
        }
        if (i2 == 0) {
            return ByteBuffer.allocate(0);
        }
        try {
            ByteBuffer allocate = ByteBuffer.allocate((int) j);
            byte[] array = allocate.array();
            Iterator it2 = iterable.iterator();
            while (it2.hasNext()) {
                hva hva = (hva) it2.next();
                int k = hva.k();
                try {
                    allocate.putInt(k);
                    int i3 = i + 4;
                    try {
                        hva.aZ(new hsx(array, i3, k));
                    } catch (IOException e) {
                        Log.e("ProtoLiteUtil", "Exception while writing to buffer.", e);
                    }
                    try {
                        allocate.put(array, i3, k);
                        int i4 = i3 + k;
                        CRC32 crc32 = new CRC32();
                        crc32.update(array, i4 - k, k);
                        allocate.putLong(crc32.getValue());
                        i = i4 + 8;
                    } catch (BufferOverflowException e2) {
                        aq(e2);
                        return null;
                    }
                } catch (BufferOverflowException e3) {
                    aq(e3);
                    return null;
                }
            }
            allocate.rewind();
            return allocate;
        } catch (IllegalArgumentException e4) {
            if (j > 1073741824) {
                str = String.format(Locale.US, "%.2fGB", new Object[]{Double.valueOf(((double) j) / 1.073741824E9d)});
            } else if (j > 1048576) {
                str = String.format(Locale.US, "%.2fMB", new Object[]{Double.valueOf(((double) j) / 1048576.0d)});
            } else if (j > 1024) {
                str = String.format(Locale.US, "%.2fKB", new Object[]{Double.valueOf(((double) j) / 1024.0d)});
            } else {
                str = String.format(Locale.US, "%d Bytes", new Object[]{Long.valueOf(j)});
            }
            Log.e("ProtoLiteUtil", String.format("Too big to serialize, %s", new Object[]{str}), e4);
            return null;
        }
    }

    /* JADX WARNING: Removed duplicated region for block: B:28:0x00b2 A[LOOP:0: B:1:0x0018->B:28:0x00b2, LOOP_END] */
    /* JADX WARNING: Removed duplicated region for block: B:38:0x00b1 A[SYNTHETIC] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static java.util.List Q(java.nio.ByteBuffer r15, java.lang.Class r16, defpackage.hvh r17) {
        /*
            r1 = r15
            java.lang.String r2 = "ProtoLiteUtil"
            java.lang.String r3 = r16.toString()
            int r4 = r15.limit()
            java.util.ArrayList r5 = new java.util.ArrayList
            int r0 = r15.limit()
            int r0 = r0 / 1000
            r6 = 1
            int r0 = r0 + r6
            r5.<init>(r0)
        L_0x0018:
            int r0 = r15.position()
            if (r0 >= r4) goto L_0x00e9
            r7 = 0
            r8 = 0
            int r9 = r15.getInt()     // Catch:{ BufferUnderflowException -> 0x00d9 }
            r0 = 2
            if (r9 >= 0) goto L_0x003b
            java.lang.Integer r1 = java.lang.Integer.valueOf(r9)
            java.lang.Object[] r0 = new java.lang.Object[r0]
            r0[r8] = r1
            r0[r6] = r3
            java.lang.String r1 = "Invalid message size: %d. May have given the wrong message type: %s"
            java.lang.String r0 = java.lang.String.format(r1, r0)
            android.util.Log.e(r2, r0)
            return r7
        L_0x003b:
            int r10 = r15.position()
            int r10 = r10 + r9
            int r10 = r10 + 8
            if (r4 >= r10) goto L_0x005c
            java.lang.Integer r1 = java.lang.Integer.valueOf(r9)
            java.lang.Integer r3 = java.lang.Integer.valueOf(r4)
            java.lang.Object[] r0 = new java.lang.Object[r0]
            r0[r8] = r1
            r0[r6] = r3
            java.lang.String r1 = "Invalid message size: %d (buffer end is %d)"
            java.lang.String r0 = java.lang.String.format(r1, r0)
            android.util.Log.e(r2, r0)
            return r5
        L_0x005c:
            int r10 = r15.position()
            int r10 = r10 + r9
            long r10 = r15.getLong(r10)
            byte[] r12 = r15.array()
            int r13 = r15.arrayOffset()
            int r14 = r15.position()
            int r13 = r13 + r14
            java.util.zip.CRC32 r14 = new java.util.zip.CRC32
            r14.<init>()
            r14.update(r12, r13, r9)
            long r12 = r14.getValue()
            int r14 = (r12 > r10 ? 1 : (r12 == r10 ? 0 : -1))
            if (r14 != 0) goto L_0x00c1
            byte[] r0 = r15.array()
            int r8 = r15.arrayOffset()
            int r10 = r15.position()
            int r8 = r8 + r10
            hte r10 = defpackage.hte.a     // Catch:{ hui -> 0x009e }
            hvj r10 = defpackage.hvj.a     // Catch:{ hui -> 0x009e }
            hte r10 = defpackage.hte.a     // Catch:{ hui -> 0x009e }
            r14 = r17
            java.lang.Object r0 = r14.h(r0, r8, r9, r10)     // Catch:{ hui -> 0x009c }
            goto L_0x00af
        L_0x009c:
            r0 = move-exception
            goto L_0x00a1
        L_0x009e:
            r0 = move-exception
            r14 = r17
        L_0x00a1:
            java.lang.String r8 = r16.toString()
            java.lang.String r10 = "Cannot deserialize message of type "
            java.lang.String r8 = r10.concat(r8)
            android.util.Log.e(r2, r8, r0)
            r0 = r7
        L_0x00af:
            if (r0 != 0) goto L_0x00b2
            return r7
        L_0x00b2:
            r5.add(r0)
            int r0 = r15.position()
            int r0 = r0 + r9
            int r0 = r0 + 8
            r15.position(r0)
            goto L_0x0018
        L_0x00c1:
            java.lang.Long r1 = java.lang.Long.valueOf(r10)
            java.lang.Long r3 = java.lang.Long.valueOf(r12)
            java.lang.Object[] r0 = new java.lang.Object[r0]
            r0[r8] = r1
            r0[r6] = r3
            java.lang.String r1 = "Corrupt protobuf data, expected CRC: %d computed CRC: %d"
            java.lang.String r0 = java.lang.String.format(r1, r0)
            android.util.Log.e(r2, r0)
            return r5
        L_0x00d9:
            r0 = move-exception
            r1 = r0
            java.lang.Object[] r0 = new java.lang.Object[r6]
            r0[r8] = r3
            java.lang.String r3 = "Buffer underflow. May have given the wrong message type: %s"
            java.lang.String r0 = java.lang.String.format(r3, r0)
            android.util.Log.e(r2, r0, r1)
            return r7
        L_0x00e9:
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cqh.Q(java.nio.ByteBuffer, java.lang.Class, hvh):java.util.List");
    }

    public static csz R(csf csf) {
        byte[] g = csf.g();
        hte hte = hte.a;
        hvj hvj = hvj.a;
        htq o = htq.o(csz.f, g, 0, g.length, hte.a);
        htq.D(o);
        return (csz) o;
    }

    public static fpd S(Context context, hmh hmh, cyk cyk, cxi cxi, grh grh) {
        fpj d = fpl.d(context, hmh);
        d.c = E("gms_icing_mdd_groups", grh);
        d.b();
        d.d = new aio(cxi, 6);
        d.c(new czg(cyk, 1));
        return d.a();
    }

    public static fpd T(Context context, hmh hmh, cyk cyk, cxi cxi, grh grh) {
        fpj d = fpl.d(context, hmh);
        d.c = E("gms_icing_mdd_shared_files", grh);
        d.b();
        d.d = new aio(cxi, 6);
        d.c(new czg(cyk, 0));
        return d.a();
    }

    public static bzj U(Iterable iterable) {
        return new bzj((Object) hfc.Y(iterable));
    }

    @SafeVarargs
    public static bzj V(hme... hmeArr) {
        return new bzj((Object) hfc.Z(hmeArr));
    }

    public static bzj W(Iterable iterable) {
        return new bzj((Object) hfc.aa(iterable));
    }

    @SafeVarargs
    public static bzj X(hme... hmeArr) {
        return new bzj((Object) hfc.ab(hmeArr));
    }

    public static dld Y(grh grh, jjk jjk) {
        return (dld) ((jjk) grh.d(jjk)).b();
    }

    public static /* synthetic */ String Z(int i) {
        if (i == 1) {
            return "MALFORMED";
        }
        if (i == 2) {
            return "UNKNOWN_OPTION";
        }
        if (i != 3) {
            return "INVALID_PAYLOAD";
        }
        return "INVALID_ENCODING";
    }

    public static ddi aa(int i) {
        boolean z;
        boolean z2;
        boolean z3;
        boolean z4 = true;
        if ((i & 8) > 0) {
            z = true;
        } else {
            z = false;
        }
        ddh e = ddi.e();
        e.e(z);
        if ((i & 16) > 0) {
            z2 = true;
        } else {
            z2 = false;
        }
        e.c(z2);
        if ((i & 32) > 0) {
            z3 = true;
        } else {
            z3 = false;
        }
        e.d(z3);
        if ((i & 64) <= 0) {
            z4 = false;
        }
        e.b(z4);
        return e.a();
    }

    public static boolean ab(int i) {
        if ((i & 3) == 0) {
            return true;
        }
        return false;
    }

    public static final int ac(ddi ddi, int i) {
        dcg dcg = (dcg) ddi;
        if (dcg.a) {
            i |= 8;
        }
        if (dcg.b) {
            i |= 16;
        }
        if (dcg.c) {
            i |= 32;
        }
        if (dcg.d) {
            return i | 64;
        }
        return i;
    }

    public static long ad(ded ded) {
        if (ded.k() == null) {
            return ded.d();
        }
        return ded.c();
    }

    public static final dgm ae(ScheduledExecutorService scheduledExecutorService, List list) {
        if (scheduledExecutorService == null) {
            throw new IllegalStateException("Control executor must be set.");
        } else if (!list.isEmpty()) {
            return new dgm(scheduledExecutorService, list);
        } else {
            throw new IllegalStateException("At least one download protocol must be added.");
        }
    }

    /* JADX WARNING: Can't fix incorrect switch cases order */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static final defpackage.dfz af(java.lang.String r4) {
        /*
            int r0 = r4.hashCode()
            r1 = 3
            r2 = 2
            r3 = 1
            switch(r0) {
                case -1140680715: goto L_0x0029;
                case -903629273: goto L_0x001f;
                case 107902: goto L_0x0015;
                case 94921523: goto L_0x000b;
                default: goto L_0x000a;
            }
        L_0x000a:
            goto L_0x0033
        L_0x000b:
            java.lang.String r0 = "crc32"
            boolean r4 = r4.equals(r0)
            if (r4 == 0) goto L_0x0033
            r4 = r3
            goto L_0x0034
        L_0x0015:
            java.lang.String r0 = "md5"
            boolean r4 = r4.equals(r0)
            if (r4 == 0) goto L_0x0033
            r4 = r2
            goto L_0x0034
        L_0x001f:
            java.lang.String r0 = "sha256"
            boolean r4 = r4.equals(r0)
            if (r4 == 0) goto L_0x0033
            r4 = r1
            goto L_0x0034
        L_0x0029:
            java.lang.String r0 = "adler32"
            boolean r4 = r4.equals(r0)
            if (r4 == 0) goto L_0x0033
            r4 = 0
            goto L_0x0034
        L_0x0033:
            r4 = -1
        L_0x0034:
            if (r4 == 0) goto L_0x0047
            if (r4 == r3) goto L_0x0044
            if (r4 == r2) goto L_0x0041
            if (r4 == r1) goto L_0x003e
            r4 = 0
            return r4
        L_0x003e:
            dfz r4 = defpackage.dgf.a
            return r4
        L_0x0041:
            dfz r4 = defpackage.dgd.a
            return r4
        L_0x0044:
            dfz r4 = defpackage.dgb.a
            return r4
        L_0x0047:
            dfz r4 = defpackage.dga.a
            return r4
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cqh.af(java.lang.String):dfz");
    }

    public static long ag(Collection collection) {
        Iterator it = collection.iterator();
        long j = 0;
        while (it.hasNext()) {
            dfi dfi = (dfi) it.next();
            dep c = dfi.c();
            int b = c.b();
            if (b == 1 || b == 2) {
                j += Math.max(dfi.b(), c.e());
            }
        }
        return j;
    }

    public static long ah(Collection collection) {
        Iterator it = collection.iterator();
        long j = 0;
        while (it.hasNext()) {
            dfi dfi = (dfi) it.next();
            if (dfi.b.exists()) {
                j += dfi.b();
            }
        }
        return j;
    }

    public static String ai(Object obj) {
        String str;
        if (obj == null) {
            return "-";
        }
        if (obj instanceof dcu) {
            str = ((dcu) obj).c();
        } else {
            str = obj.getClass().getSimpleName();
        }
        int hashCode = obj.hashCode();
        return str + ":" + hashCode;
    }

    static int aj(String str, int i) {
        int length = str.length();
        if (i >= length) {
            return -1;
        }
        int indexOf = str.indexOf(10, i);
        if (indexOf == -1) {
            return length - 1;
        }
        return indexOf;
    }

    public static dco ak(String str, int i) {
        int i2;
        int aj = aj(str, i);
        dco dco = null;
        while (aj > 0) {
            if (aj >= str.length()) {
                i2 = str.length() - 1;
            } else {
                i2 = aj;
            }
            int i3 = i;
            while (i3 <= i2 && Character.isWhitespace(str.charAt(i3))) {
                i3++;
            }
            if (i3 > i && i3 <= i2 - 2 && str.charAt(i3) == 'a' && str.charAt(i3 + 1) == 't' && Character.isWhitespace(str.charAt(i3 + 2))) {
                if (dco == null) {
                    dco = new dco();
                    dco.a = i;
                    dco.c = str;
                }
                dco.b = aj;
            } else if (dco != null) {
                break;
            }
            i = aj + 1;
            aj = aj(str, i);
        }
        return dco;
    }

    public static String al(Throwable th, int i) {
        String str = th.getClass().getName() + ": " + th.getMessage();
        Throwable cause = th.getCause();
        if (cause == null) {
            return str;
        }
        if (i >= 5) {
            return str.concat("\n(...)");
        }
        return str + "\nCaused by: " + al(cause, i + 1);
    }

    public static Throwable am(Throwable th) {
        Throwable cause = th.getCause();
        if (cause != null) {
            Class<?> cls = th.getClass();
            if (cls.equals(ExecutionException.class) || cls.equals(hhm.class)) {
                return am(cause);
            }
        }
        return th;
    }

    public static void an(AssetManager assetManager, String str, File file) {
        hha hha = new hha();
        try {
            InputStream open = assetManager.open(str);
            hha.c(open);
            BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(new FileOutputStream(file));
            hha.c(bufferedOutputStream);
            hgy.a(open, bufferedOutputStream);
            hha.close();
        } catch (Throwable th) {
            hha.close();
            throw th;
        }
    }

    public static final void ao(JsonWriter jsonWriter, ddb ddb) {
        for (String str : ddb.b()) {
            Object a = ddb.a(str);
            if (a != null) {
                jsonWriter.name(str).value(a.toString());
            }
        }
    }

    private static void aq(BufferOverflowException bufferOverflowException) {
        Log.e("ProtoLiteUtil", "Buffer underflow. A message may have an invalid serialized form or has been concurrently modified.", bufferOverflowException);
    }

    private final Object ar(hrp hrp, int i) {
        if (i < 5) {
            int r = hrp.r();
            int i2 = r - 1;
            if (i2 == 0) {
                hrp.l();
                ArrayList arrayList = new ArrayList();
                while (hrp.p()) {
                    arrayList.add(ar(hrp, i + 1));
                }
                hrp.n();
                return arrayList;
            } else if (i2 == 5) {
                return hrp.j();
            } else {
                if (i2 == 6) {
                    return Integer.valueOf(hrp.b());
                }
                if (i2 == 7) {
                    return Boolean.valueOf(hrp.q());
                }
                throw new dbc("Unsupported extra type found: " + hzz.C(r) + ": " + hrp.f());
            }
        } else {
            throw new dbc("Array has a depth greater than max of 5: ".concat(hrp.f()));
        }
    }

    public static boolean b(Context context) {
        if ("com.google.android.gms".equals(context.getPackageName())) {
            return true;
        }
        return false;
    }

    public static sk c(Context context) {
        sk d = d(context);
        d.f(context.getResources().getString(R.string.mdd_foreground_service_notification_title));
        d.i(17301629);
        return d;
    }

    public static sk d(Context context) {
        sk skVar = new sk(context, "download-notification-channel-id");
        skVar.n = "service";
        skVar.d(8, true);
        return skVar;
    }

    public static String e(Context context) {
        return context.getResources().getString(R.string.mdd_notification_download_failed);
    }

    public static void f(Context context, Class cls, String str) {
        Intent intent = new Intent(context, cls);
        intent.putExtra("key", str);
        ComponentName unused = context.startForegroundService(intent);
    }

    public static void g(Context context, Class cls, String str) {
        Intent intent = new Intent(context, cls);
        intent.putExtra("stop-service", true);
        intent.putExtra("key", str);
        ComponentName unused = context.startForegroundService(intent);
    }

    public static final boolean h() {
        return ikc.a.a().i();
    }

    public static final boolean i() {
        return ikc.a.a().j();
    }

    public static final boolean j() {
        return ikc.a.a().k();
    }

    public static final boolean k() {
        return ikc.a.a().l();
    }

    public static final int l() {
        return (int) ikc.a.a().a();
    }

    public static final float m() {
        return (float) ijz.a.a().a();
    }

    public static final boolean n() {
        return ikc.a.a().q();
    }

    public static final int o() {
        return (int) ikg.a.a().d();
    }

    public static final int p() {
        return (int) ikc.a.a().b();
    }

    public static final int q() {
        return (int) ikg.a.a().f();
    }

    public static Uri r(Uri uri) {
        String uri2 = uri.toString();
        return Uri.parse(uri2.substring(0, uri2.lastIndexOf("_")));
    }

    public static String s(String str, String str2) {
        return a.as(str2, str, "_");
    }

    public static ctj t(csv csv, int i) {
        htk l = ctj.g.l();
        String str = csv.c;
        if (!l.b.B()) {
            l.u();
        }
        htq htq = l.b;
        ctj ctj = (ctj) htq;
        str.getClass();
        ctj.a |= 1;
        ctj.b = str;
        int i2 = csv.d;
        if (!htq.B()) {
            l.u();
        }
        ctj ctj2 = (ctj) l.b;
        ctj2.a |= 2;
        ctj2.c = i2;
        String k = cqx.k(csv);
        if (!l.b.B()) {
            l.u();
        }
        htq htq2 = l.b;
        ctj ctj3 = (ctj) htq2;
        k.getClass();
        ctj3.a |= 4;
        ctj3.d = k;
        if (!htq2.B()) {
            l.u();
        }
        ctj ctj4 = (ctj) l.b;
        ctj4.e = i - 1;
        ctj4.a |= 8;
        if ((csv.a & 32) != 0) {
            ihf ihf = csv.g;
            if (ihf == null) {
                ihf = ihf.b;
            }
            if (!l.b.B()) {
                l.u();
            }
            ctj ctj5 = (ctj) l.b;
            ihf.getClass();
            ctj5.f = ihf;
            ctj5.a |= 16;
        }
        return (ctj) l.r();
    }

    public static cwf u(Context context, cuk cuk) {
        try {
            return cwf.a(context.getSharedPreferences("gms_icing_mdd_migrations", 0).getInt("mdd_file_key_version", cwf.NEW_FILE_KEY.d));
        } catch (IllegalArgumentException unused) {
            cuk.a();
            context.getSharedPreferences("gms_icing_mdd_migrations", 0).edit().clear().commit();
            return cwf.USE_CHECKSUM_ONLY;
        }
    }

    public static boolean v(Context context) {
        return context.getSharedPreferences("gms_icing_mdd_migrations", 0).getBoolean("migrated_to_new_file_key", false);
    }

    public static boolean w(Context context, cwf cwf) {
        cyh.d("%s: Setting FileKeyVersion to %s", "Migrations", cwf.name());
        return context.getSharedPreferences("gms_icing_mdd_migrations", 0).edit().putInt("mdd_file_key_version", cwf.d).commit();
    }

    public static void x(Context context) {
        cyh.d("%s: Setting migration to new file key to %s", "Migrations", true);
        context.getSharedPreferences("gms_icing_mdd_migrations", 0).edit().putBoolean("migrated_to_new_file_key", true).commit();
    }

    public static /* synthetic */ String y(int i) {
        if (i == 1) {
            return "PENDING_GROUP";
        }
        if (i != 2) {
            return "DOWNLOADED_GROUP";
        }
        return "IN_PROGRESS_FUTURE";
    }

    public static hme z(hme hme, Callable callable, Executor executor) {
        fvf.aP(executor);
        hmr hmr = new hmr();
        hme.c(new dak(hmr, callable, hme, executor), executor);
        return hmr;
    }

    public final cqg a() {
        cqg cqg = new cqg(cqj.b, Process.myTid(), true, 0, false, 104);
        cqj.c = cqg;
        return cqg;
    }

    public final Object ap(hrp hrp) {
        return ar(hrp, 0);
    }

    public cqh(byte[] bArr, byte[] bArr2) {
        fvf.aB(true, "%s expected to have the %s least significant bits equal to 0", 0, 14);
        fvf.aB(true, "%s expected to be at least %s", 16383, 16383);
    }
}
