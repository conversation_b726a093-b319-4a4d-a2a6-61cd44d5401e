package defpackage;

/* renamed from: eoi  reason: default package */
/* compiled from: PG */
final class eoi extends jnv implements jmp {
    final /* synthetic */ int a;
    final /* synthetic */ eam b;
    final /* synthetic */ eoa c;
    final /* synthetic */ eoj d;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eoi(int i, eam eam, eoa eoa, eoj eoj) {
        super(0);
        this.a = i;
        this.b = eam;
        this.c = eoa;
        this.d = eoj;
    }

    /* JADX WARNING: type inference failed for: r6v0, types: [elp, java.lang.Object] */
    public final /* bridge */ /* synthetic */ Object a() {
        ((hby) eoj.a.f().h(hdg.a, "ALT.AudioSessionsRegy").j("com/google/android/libraries/search/audio/microphone/registry/AudioSessionsRegistry$stopAudioSession$logSessionAbsence$1", "invoke", 261, "AudioSessionsRegistry.kt")).G("#audio# skipping audio session(%d) stop(%s) for %s, inactive", Integer.valueOf(this.a), this.b.name(), this.c.a());
        eoa eoa = this.c;
        jnu.e(eoa, "client");
        eam eam = this.b;
        jnu.e(eam, "reason");
        ehg ehg = eoa.b.b;
        if (ehg == null) {
            ehg = ehg.c;
        }
        eoj eoj = this.d;
        int i = this.a;
        jnu.d(ehg, "getClientInfo(...)");
        long j = (long) i;
        cyw cyw = eoj.b;
        cyw.a.t(ehg, j);
        htk l = eav.n.l();
        jnu.d(l, "newBuilder(...)");
        byw D = jnu.e(l, "builder");
        D.o(eay.AUDIO_REQUEST_STOP_LISTENING);
        D.q((long) eoa.a);
        D.s(j);
        D.t(eam);
        ehg ehg2 = eoa.b.b;
        if (ehg2 == null) {
            ehg2 = ehg.c;
        }
        Object obj = cyw.b;
        D.p(ehg2);
        ((eug) obj).d(D.l());
        return jkd.a;
    }
}
