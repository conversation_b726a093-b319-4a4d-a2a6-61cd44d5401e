<?xml version="1.0" encoding="utf-8"?>
<com.android.car.ui.shortcutspopup.CarUiArrowContainerView xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:focusable="false" android:clickable="true" android:layout_width="match_parent" android:layout_height="wrap_content" app:carUiArrowColor="@color/car_ui_shortcuts_arrow_color" app:carUiArrowHeight="21dp" app:carUiArrowRadius="4dp" app:carUiArrowWidth="25dp" app:carUiContentView="@+id/car_ui_shortcuts_contentView" app:carUiContentViewDrawable="@drawable/car_ui_shortcuts_rounded_selector" app:carUiOffsetX="26dp" app:carUiOffsetY="4dp">
    <androidx.constraintlayout.widget.ConstraintLayout android:layout_gravity="center" android:id="@+id/car_ui_shortcuts_contentView" android:paddingTop="24dp" android:paddingBottom="24dp" android:focusable="true" android:layout_width="match_parent" android:layout_height="wrap_content" android:paddingStart="24dp" android:paddingEnd="32dp">
        <ImageView android:id="@+id/car_ui_content_left_drawable" android:layout_width="30dp" android:layout_height="30dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0" app:tint="@color/car_ui_shortcuts_item_text_color"/>
        <TextView android:textColor="@color/car_ui_shortcuts_item_text_color" android:id="@+id/car_ui_content_name" android:layout_width="0dp" android:layout_height="wrap_content" android:layout_marginStart="24dp" app:layout_constraintBottom_toBottomOf="@+id/car_ui_content_left_drawable" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toEndOf="@+id/car_ui_content_left_drawable" app:layout_constraintTop_toTopOf="@+id/car_ui_content_left_drawable"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.android.car.ui.shortcutspopup.CarUiArrowContainerView>
