package defpackage;

/* renamed from: cxo  reason: default package */
/* compiled from: PG */
public final class cxo implements iiu {
    private final /* synthetic */ int a;
    private final Object b;

    public cxo(Object obj, int i) {
        this.a = i;
        this.b = obj;
    }

    /* JADX WARNING: type inference failed for: r0v7, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v53, types: [java.lang.Object, jjk] */
    public final /* synthetic */ Object b() {
        switch (this.a) {
            case 0:
                Object obj = ((cxm) this.b).c;
                hzz.u(obj);
                return obj;
            case 1:
                Object obj2 = ((cxm) this.b).g;
                hzz.u(obj2);
                return obj2;
            case 2:
                cqx cqx = (cqx) this.b.b();
                return new cqx();
            case 3:
                cxm cxm = (cxm) this.b;
                if (((grh) cxm.e).f()) {
                    return ((grh) cxm.e).b();
                }
                return new cxl();
            case 4:
                Object obj3 = ((cxp) this.b).a;
                hzz.u(obj3);
                return obj3;
            case 5:
                Object obj4 = ((cxp) this.b).b;
                hzz.u(obj4);
                return obj4;
            case 6:
                Object obj5 = ((cxp) this.b).c;
                hzz.u(obj5);
                return obj5;
            case 7:
                Object obj6 = ((cxp) this.b).d;
                hzz.u(obj6);
                return obj6;
            case 8:
                Object obj7 = ((cxp) this.b).e;
                hzz.u(obj7);
                return obj7;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return ((cyw) this.b).a;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return ((cyw) this.b).b;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return ((cyw) this.b).c;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return ((cyw) this.b).d;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return new bzl(((iim) this.b).a(), (byte[]) null);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return new bzj(((iim) this.b).a());
            case 15:
                return new czs(((iim) this.b).a());
            case 16:
                return new gjk((cto) this.b.b(), 1);
            case 17:
                dng dng = (dng) cqh.Y((grh) ((iiv) this.b).a, new dje(2));
                hzz.u(dng);
                return dng;
            case 18:
                dmk dmk = (dmk) cqh.Y((grh) ((iiv) this.b).a, new dje(1));
                hzz.u(dmk);
                return dmk;
            case 19:
                dmo dmo = (dmo) cqh.Y(((bqs) this.b).a(), new dje(9));
                hzz.u(dmo);
                return dmo;
            default:
                dnr dnr = (dnr) cqh.Y((grh) ((iiv) this.b).a, new dje(0));
                hzz.u(dnr);
                return dnr;
        }
    }
}
