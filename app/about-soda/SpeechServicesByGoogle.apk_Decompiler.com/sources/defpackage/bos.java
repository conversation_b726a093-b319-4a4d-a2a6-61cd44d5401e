package defpackage;

/* renamed from: bos  reason: default package */
/* compiled from: PG */
public final class bos extends htn implements hto {
    public static final bos a;
    private static volatile hvh c;
    private byte b = 2;

    static {
        bos bos = new bos();
        a = bos;
        htq.z(bos.class, bos);
    }

    private bos() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        byte b2;
        int i2 = i - 1;
        if (i2 == 0) {
            return Byte.valueOf(this.b);
        }
        if (i2 == 2) {
            return new hvl(a, "\u0001\u0000", (Object[]) null);
        }
        if (i2 == 3) {
            return new bos();
        }
        if (i2 == 4) {
            return new htm(a);
        }
        if (i2 == 5) {
            return a;
        }
        if (i2 != 6) {
            if (obj == null) {
                b2 = 0;
            } else {
                b2 = 1;
            }
            this.b = b2;
            return null;
        }
        hvh hvh = c;
        if (hvh == null) {
            synchronized (bos.class) {
                hvh = c;
                if (hvh == null) {
                    hvh = new htl(a);
                    c = hvh;
                }
            }
        }
        return hvh;
    }
}
