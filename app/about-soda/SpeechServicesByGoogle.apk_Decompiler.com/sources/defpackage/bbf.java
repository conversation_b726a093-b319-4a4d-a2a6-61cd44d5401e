package defpackage;

/* renamed from: bbf  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bbf implements pq {
    public final Object a(po poVar) {
        poVar.d(new IllegalStateException("Expedited WorkRequests require a ListenableWorker to provide an implementation for`getForegroundInfoAsync()`"));
        return "default failing getForegroundInfoAsync";
    }
}
