<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" android:background="@drawable/car_ui_list_header_background" android:layout_width="match_parent" android:layout_height="@dimen/car_ui_list_item_header_height">
    <androidx.constraintlayout.widget.Guideline android:orientation="vertical" android:id="@+id/car_ui_list_item_start_guideline" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintGuide_begin="@dimen/car_ui_list_item_header_start_inset"/>
    <TextView android:textAppearance="@style/TextAppearance.CarUi.ListItem.Header" android:id="@+id/car_ui_list_item_title" android:layout_width="0dp" android:layout_height="wrap_content" android:textAlignment="viewStart" android:layout_marginStart="@dimen/car_ui_header_list_item_text_start_margin" app:layout_constraintBottom_toTopOf="@+id/car_ui_list_item_body" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="@+id/car_ui_list_item_start_guideline" app:layout_constraintTop_toTopOf="0" app:layout_constraintVertical_chainStyle="2"/>
    <TextView android:textAppearance="@style/TextAppearance.CarUi.ListItem.Body" android:id="@+id/car_ui_list_item_body" android:layout_width="0dp" android:layout_height="wrap_content" android:textAlignment="viewStart" android:layout_marginStart="@dimen/car_ui_list_item_text_no_icon_start_margin" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="@+id/car_ui_list_item_start_guideline" app:layout_constraintTop_toBottomOf="@+id/car_ui_list_item_title"/>
</androidx.constraintlayout.widget.ConstraintLayout>
