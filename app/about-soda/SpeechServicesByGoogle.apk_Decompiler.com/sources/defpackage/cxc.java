package defpackage;

import android.content.Context;
import java.util.concurrent.Executor;

/* renamed from: cxc  reason: default package */
/* compiled from: PG */
public final class cxc implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;
    private final jjk e;

    public cxc(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5) {
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
        this.e = jjk5;
    }

    /* renamed from: a */
    public final cxb b() {
        Context a2 = ((iim) this.a).a();
        cqx cqx = (cqx) this.b.b();
        return new cxb(a2, (cuk) this.c.b(), (grh) this.d.b(), (Executor) this.e.b());
    }
}
