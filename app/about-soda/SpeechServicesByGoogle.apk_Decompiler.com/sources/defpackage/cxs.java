package defpackage;

import android.net.Uri;
import java.util.concurrent.Executor;

/* renamed from: cxs  reason: default package */
/* compiled from: PG */
public final class cxs implements cxw {
    private final cxa a;
    private final csv b;
    private final String c;
    private final ctg d;
    private final int e;
    private final long f;
    private final String g;
    private final Executor h;
    private final cyk i;
    private final int j;
    private final cqh k;
    private final kjd l;

    public cxs(cxa cxa, kjd kjd, csv csv, int i2, cyk cyk, ctg ctg, int i3, long j2, String str, cqh cqh, Executor executor) {
        this.a = cxa;
        this.l = kjd;
        this.b = csv;
        this.j = i2;
        this.c = cqx.k(csv);
        this.i = cyk;
        this.d = ctg;
        this.e = i3;
        this.f = j2;
        this.g = str;
        this.k = cqh;
        this.h = executor;
    }

    public static hme c(ctf ctf, csv csv, int i2, cxa cxa, Executor executor) {
        ctj t = cqh.t(csv, i2);
        return czw.e(e(cxa, t, executor)).g(new cwt(ctf, cxa, t, 5), executor).g(new cwi(t, 19), executor);
    }

    static hme d(cxa cxa, csv csv, int i2, kjd kjd, Uri uri, String str, cyk cyk, Executor executor) {
        return czw.e(e(cxa, cqh.t(csv, i2), executor)).g(new cvd((Object) str, (Object) cyk, (Object) kjd, (Object) uri, 16, (char[]) null), executor);
    }

    private static hme e(cxa cxa, ctj ctj, Executor executor) {
        return ftd.L(cxa.e(ctj), new cxr(ctj, 0), executor);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:100:0x0222, code lost:
        if (r0.B() != false) goto L_0x0227;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:101:0x0224, code lost:
        r15.u();
     */
    /* JADX WARNING: Code restructure failed: missing block: B:102:0x0227, code lost:
        r0 = r15.b;
        r1 = (defpackage.hig) r0;
        r1.a |= 2;
        r1.c = r8;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:103:0x0238, code lost:
        if (r0.B() != false) goto L_0x023d;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:104:0x023a, code lost:
        r15.u();
     */
    /* JADX WARNING: Code restructure failed: missing block: B:105:0x023d, code lost:
        r0 = r15.b;
        r1 = (defpackage.hig) r0;
        r1.a |= 64;
        r1.h = r9;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:106:0x024e, code lost:
        if (r0.B() != false) goto L_0x0253;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:107:0x0250, code lost:
        r15.u();
     */
    /* JADX WARNING: Code restructure failed: missing block: B:108:0x0253, code lost:
        r0 = r15.b;
        r1 = (defpackage.hig) r0;
        r11.getClass();
        r1.a |= 128;
        r1.i = r11;
        r1 = r5.c;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:109:0x0269, code lost:
        if (r0.B() != false) goto L_0x026e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:110:0x026b, code lost:
        r15.u();
     */
    /* JADX WARNING: Code restructure failed: missing block: B:111:0x026e, code lost:
        r0 = (defpackage.hig) r15.b;
        r1.getClass();
        r0.a |= 4;
        r0.d = r1;
        r7.g((defpackage.hig) r15.r(), 3, r13, r17, r12.b, 0);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:131:0x02c4, code lost:
        if (r0 != 2) goto L_0x02c6;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:86:0x01e9, code lost:
        r13 = r4.c(r0);
        r17 = r4.c(r2);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:87:0x01f3, code lost:
        if (r13 <= r17) goto L_0x0296;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:88:0x01f5, code lost:
        r15 = defpackage.hig.k.l();
        r6 = r5.b;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:89:0x01fd, code lost:
        r19 = r0;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:92:0x0205, code lost:
        if (r15.b.B() != false) goto L_0x020a;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:94:?, code lost:
        r15.u();
     */
    /* JADX WARNING: Code restructure failed: missing block: B:96:?, code lost:
        r0 = r15.b;
        r1 = (defpackage.hig) r0;
        r6.getClass();
     */
    /* JADX WARNING: Code restructure failed: missing block: B:97:0x0212, code lost:
        r21 = "DownloaderCallbackImpl";
     */
    /* JADX WARNING: Code restructure failed: missing block: B:99:?, code lost:
        r1.a |= 1;
        r1.b = r6;
     */
    /* JADX WARNING: No exception handlers in catch block: Catch:{  } */
    /* JADX WARNING: Removed duplicated region for block: B:129:0x02c2 A[Catch:{ IllegalArgumentException -> 0x0329, IOException -> 0x015f, IOException -> 0x010c, csi -> 0x0354 }] */
    /* JADX WARNING: Removed duplicated region for block: B:130:0x02c3 A[Catch:{ IllegalArgumentException -> 0x0329, IOException -> 0x015f, IOException -> 0x010c, csi -> 0x0354 }] */
    /* JADX WARNING: Removed duplicated region for block: B:134:0x02d2 A[Catch:{ IllegalArgumentException -> 0x0329, IOException -> 0x015f, IOException -> 0x010c, csi -> 0x0354 }] */
    /* JADX WARNING: Removed duplicated region for block: B:135:0x02d4 A[Catch:{ IllegalArgumentException -> 0x0329, IOException -> 0x015f, IOException -> 0x010c, csi -> 0x0354 }] */
    /* JADX WARNING: Removed duplicated region for block: B:152:0x02fe A[SYNTHETIC, Splitter:B:152:0x02fe] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme a(android.net.Uri r23) {
        /*
            r22 = this;
            r1 = r22
            r6 = r23
            java.lang.String r0 = r1.c
            java.lang.String r2 = "%s: Successfully downloaded file %s"
            java.lang.String r3 = "DownloaderCallbackImpl"
            defpackage.cyh.d(r2, r3, r0)
            csv r0 = r1.b
            int r2 = r0.a
            r2 = r2 & 32
            if (r2 == 0) goto L_0x0018
            java.lang.String r2 = r0.h
            goto L_0x001a
        L_0x0018:
            java.lang.String r2 = r0.f
        L_0x001a:
            kjd r4 = r1.l     // Catch:{ csi -> 0x0354 }
            defpackage.cxt.c(r4, r0, r6, r2)     // Catch:{ csi -> 0x0354 }
            csv r0 = r1.b     // Catch:{ csi -> 0x0354 }
            int r0 = r0.a     // Catch:{ csi -> 0x0354 }
            r0 = r0 & 32
            if (r0 == 0) goto L_0x0345
            android.net.Uri r0 = defpackage.cqh.r(r23)     // Catch:{ csi -> 0x0354 }
            csv r2 = r1.b     // Catch:{ csi -> 0x0354 }
            boolean r2 = defpackage.cqx.l(r2)     // Catch:{ csi -> 0x0354 }
            if (r2 == 0) goto L_0x012a
            cyk r11 = r1.i     // Catch:{ csi -> 0x0354 }
            kjd r2 = r1.l     // Catch:{ csi -> 0x0354 }
            ctg r8 = r1.d     // Catch:{ csi -> 0x0354 }
            int r9 = r1.e     // Catch:{ csi -> 0x0354 }
            long r12 = r1.f     // Catch:{ csi -> 0x0354 }
            java.lang.String r14 = r1.g     // Catch:{ csi -> 0x0354 }
            csv r15 = r1.b     // Catch:{ csi -> 0x0354 }
            java.lang.String r15 = r15.b     // Catch:{ csi -> 0x0354 }
            cxy r5 = new cxy     // Catch:{ IOException -> 0x010c }
            r5.<init>(r0)     // Catch:{ IOException -> 0x010c }
            r2.e(r6, r5)     // Catch:{ IOException -> 0x010c }
            hig r5 = defpackage.hig.k     // Catch:{ IOException -> 0x00fc }
            htk r5 = r5.l()     // Catch:{ IOException -> 0x00fc }
            java.lang.String r4 = r8.b     // Catch:{ IOException -> 0x00fc }
            htq r7 = r5.b     // Catch:{ IOException -> 0x00fc }
            boolean r7 = r7.B()     // Catch:{ IOException -> 0x00fc }
            if (r7 != 0) goto L_0x005e
            r5.u()     // Catch:{ IOException -> 0x00fc }
        L_0x005e:
            htq r7 = r5.b     // Catch:{ IOException -> 0x00fc }
            r10 = r7
            hig r10 = (defpackage.hig) r10     // Catch:{ IOException -> 0x00fc }
            r4.getClass()     // Catch:{ IOException -> 0x00fc }
            r17 = r15
            int r15 = r10.a     // Catch:{ IOException -> 0x00fc }
            r18 = 1
            r15 = r15 | 1
            r10.a = r15     // Catch:{ IOException -> 0x00fc }
            r10.b = r4     // Catch:{ IOException -> 0x00fc }
            boolean r4 = r7.B()     // Catch:{ IOException -> 0x00fc }
            if (r4 != 0) goto L_0x007b
            r5.u()     // Catch:{ IOException -> 0x00fc }
        L_0x007b:
            htq r4 = r5.b     // Catch:{ IOException -> 0x00fc }
            r7 = r4
            hig r7 = (defpackage.hig) r7     // Catch:{ IOException -> 0x00fc }
            int r10 = r7.a     // Catch:{ IOException -> 0x00fc }
            r15 = 2
            r10 = r10 | r15
            r7.a = r10     // Catch:{ IOException -> 0x00fc }
            r7.c = r9     // Catch:{ IOException -> 0x00fc }
            boolean r4 = r4.B()     // Catch:{ IOException -> 0x00fc }
            if (r4 != 0) goto L_0x0091
            r5.u()     // Catch:{ IOException -> 0x00fc }
        L_0x0091:
            htq r4 = r5.b     // Catch:{ IOException -> 0x00fc }
            r7 = r4
            hig r7 = (defpackage.hig) r7     // Catch:{ IOException -> 0x00fc }
            int r9 = r7.a     // Catch:{ IOException -> 0x00fc }
            r9 = r9 | 64
            r7.a = r9     // Catch:{ IOException -> 0x00fc }
            r7.h = r12     // Catch:{ IOException -> 0x00fc }
            boolean r4 = r4.B()     // Catch:{ IOException -> 0x00fc }
            if (r4 != 0) goto L_0x00a7
            r5.u()     // Catch:{ IOException -> 0x00fc }
        L_0x00a7:
            htq r4 = r5.b     // Catch:{ IOException -> 0x00fc }
            r7 = r4
            hig r7 = (defpackage.hig) r7     // Catch:{ IOException -> 0x00fc }
            r14.getClass()     // Catch:{ IOException -> 0x00fc }
            int r9 = r7.a     // Catch:{ IOException -> 0x00fc }
            r9 = r9 | 128(0x80, float:1.794E-43)
            r7.a = r9     // Catch:{ IOException -> 0x00fc }
            r7.i = r14     // Catch:{ IOException -> 0x00fc }
            java.lang.String r7 = r8.c     // Catch:{ IOException -> 0x00fc }
            boolean r4 = r4.B()     // Catch:{ IOException -> 0x00fc }
            if (r4 != 0) goto L_0x00c2
            r5.u()     // Catch:{ IOException -> 0x00fc }
        L_0x00c2:
            htq r4 = r5.b     // Catch:{ IOException -> 0x00fc }
            hig r4 = (defpackage.hig) r4     // Catch:{ IOException -> 0x00fc }
            r7.getClass()     // Catch:{ IOException -> 0x00fc }
            int r8 = r4.a     // Catch:{ IOException -> 0x00fc }
            r9 = 4
            r8 = r8 | r9
            r4.a = r8     // Catch:{ IOException -> 0x00fc }
            r4.d = r7     // Catch:{ IOException -> 0x00fc }
            htq r4 = r5.r()     // Catch:{ IOException -> 0x00fc }
            r12 = r4
            hig r12 = (defpackage.hig) r12     // Catch:{ IOException -> 0x00fc }
            fok r4 = new fok     // Catch:{ IOException -> 0x00fc }
            r4.<init>(r9)     // Catch:{ IOException -> 0x00fc }
            java.lang.Object r0 = r2.e(r0, r4)     // Catch:{ IOException -> 0x00fc }
            java.lang.Long r0 = (java.lang.Long) r0     // Catch:{ IOException -> 0x00fc }
            long r14 = r0.longValue()     // Catch:{ IOException -> 0x00fc }
            long r4 = r2.c(r6)     // Catch:{ IOException -> 0x00fc }
            r19 = 0
            r13 = 5
            r0 = r17
            r16 = r4
            r18 = r0
            r11.g(r12, r13, r14, r16, r18, r19)     // Catch:{ IOException -> 0x00fc }
            r2.h(r6)     // Catch:{ IOException -> 0x00fc }
            goto L_0x0345
        L_0x00fc:
            java.lang.String r0 = "%s: Failed to get file size or delete zip file %s."
            r2 = 2
            java.lang.Object[] r2 = new java.lang.Object[r2]     // Catch:{ csi -> 0x0354 }
            r4 = 0
            r2[r4] = r3     // Catch:{ csi -> 0x0354 }
            r3 = 1
            r2[r3] = r6     // Catch:{ csi -> 0x0354 }
            defpackage.cyh.p(r0, r2)     // Catch:{ csi -> 0x0354 }
            goto L_0x0345
        L_0x010c:
            r0 = move-exception
            java.lang.String r2 = "%s: Failed to apply zip download transform for file %s."
            r4 = 2
            java.lang.Object[] r4 = new java.lang.Object[r4]     // Catch:{ csi -> 0x0354 }
            r5 = 0
            r4[r5] = r3     // Catch:{ csi -> 0x0354 }
            r3 = 1
            r4[r3] = r6     // Catch:{ csi -> 0x0354 }
            defpackage.cyh.j(r0, r2, r4)     // Catch:{ csi -> 0x0354 }
            kml r2 = defpackage.csi.a()     // Catch:{ csi -> 0x0354 }
            csh r3 = defpackage.csh.DOWNLOAD_TRANSFORM_IO_ERROR     // Catch:{ csi -> 0x0354 }
            r2.b = r3     // Catch:{ csi -> 0x0354 }
            r2.d = r0     // Catch:{ csi -> 0x0354 }
            csi r0 = r2.a()     // Catch:{ csi -> 0x0354 }
            throw r0     // Catch:{ csi -> 0x0354 }
        L_0x012a:
            csv r2 = r1.b     // Catch:{ csi -> 0x0354 }
            int r4 = r2.a     // Catch:{ csi -> 0x0354 }
            r4 = r4 & 32
            if (r4 == 0) goto L_0x017d
            ihf r2 = r2.g     // Catch:{ csi -> 0x0354 }
            if (r2 != 0) goto L_0x0138
            ihf r2 = defpackage.ihf.b     // Catch:{ csi -> 0x0354 }
        L_0x0138:
            huf r2 = r2.a     // Catch:{ csi -> 0x0354 }
            java.util.Iterator r2 = r2.iterator()     // Catch:{ csi -> 0x0354 }
        L_0x013e:
            boolean r4 = r2.hasNext()     // Catch:{ csi -> 0x0354 }
            if (r4 == 0) goto L_0x017d
            java.lang.Object r4 = r2.next()     // Catch:{ csi -> 0x0354 }
            ihe r4 = (defpackage.ihe) r4     // Catch:{ csi -> 0x0354 }
            int r4 = r4.a     // Catch:{ csi -> 0x0354 }
            r5 = 6
            if (r4 != r5) goto L_0x013e
            kjd r2 = r1.l     // Catch:{ csi -> 0x0354 }
            fok r4 = new fok     // Catch:{ IOException -> 0x015f }
            r5 = 3
            r4.<init>(r5)     // Catch:{ IOException -> 0x015f }
            r2.e(r6, r4)     // Catch:{ IOException -> 0x015f }
            r2.i(r6, r0)     // Catch:{ IOException -> 0x015f }
            goto L_0x0345
        L_0x015f:
            r0 = move-exception
            java.lang.String r2 = "%s: Failed to apply defrag download transform for file %s."
            r4 = 2
            java.lang.Object[] r4 = new java.lang.Object[r4]     // Catch:{ csi -> 0x0354 }
            r5 = 0
            r4[r5] = r3     // Catch:{ csi -> 0x0354 }
            r3 = 1
            r4[r3] = r6     // Catch:{ csi -> 0x0354 }
            defpackage.cyh.j(r0, r2, r4)     // Catch:{ csi -> 0x0354 }
            kml r2 = defpackage.csi.a()     // Catch:{ csi -> 0x0354 }
            csh r3 = defpackage.csh.DOWNLOAD_TRANSFORM_IO_ERROR     // Catch:{ csi -> 0x0354 }
            r2.b = r3     // Catch:{ csi -> 0x0354 }
            r2.d = r0     // Catch:{ csi -> 0x0354 }
            csi r0 = r2.a()     // Catch:{ csi -> 0x0354 }
            throw r0     // Catch:{ csi -> 0x0354 }
        L_0x017d:
            android.net.Uri$Builder r2 = r23.buildUpon()     // Catch:{ IllegalArgumentException -> 0x0329 }
            csv r4 = r1.b     // Catch:{ IllegalArgumentException -> 0x0329 }
            ihf r4 = r4.g     // Catch:{ IllegalArgumentException -> 0x0329 }
            if (r4 != 0) goto L_0x0189
            ihf r4 = defpackage.ihf.b     // Catch:{ IllegalArgumentException -> 0x0329 }
        L_0x0189:
            java.lang.String r4 = defpackage.fpa.a(r4)     // Catch:{ IllegalArgumentException -> 0x0329 }
            android.net.Uri$Builder r2 = r2.encodedFragment(r4)     // Catch:{ IllegalArgumentException -> 0x0329 }
            android.net.Uri r2 = r2.build()     // Catch:{ IllegalArgumentException -> 0x0329 }
            cyk r7 = r1.i     // Catch:{ csi -> 0x0354 }
            kjd r4 = r1.l     // Catch:{ csi -> 0x0354 }
            ctg r5 = r1.d     // Catch:{ csi -> 0x0354 }
            int r8 = r1.e     // Catch:{ csi -> 0x0354 }
            long r9 = r1.f     // Catch:{ csi -> 0x0354 }
            java.lang.String r11 = r1.g     // Catch:{ csi -> 0x0354 }
            csv r12 = r1.b     // Catch:{ csi -> 0x0354 }
            foq r13 = new foq     // Catch:{ IOException -> 0x030a }
            r13.<init>()     // Catch:{ IOException -> 0x030a }
            java.lang.Object r13 = r4.e(r2, r13)     // Catch:{ IOException -> 0x030a }
            java.io.InputStream r13 = (java.io.InputStream) r13     // Catch:{ IOException -> 0x030a }
            fou r14 = new fou     // Catch:{ all -> 0x02f9 }
            r14.<init>()     // Catch:{ all -> 0x02f9 }
            java.lang.Object r14 = r4.e(r0, r14)     // Catch:{ all -> 0x02f9 }
            java.io.OutputStream r14 = (java.io.OutputStream) r14     // Catch:{ all -> 0x02f9 }
            defpackage.hgy.a(r13, r14)     // Catch:{ all -> 0x02e8 }
            if (r14 == 0) goto L_0x01c1
            r14.close()     // Catch:{ all -> 0x02f9 }
        L_0x01c1:
            if (r13 == 0) goto L_0x01c6
            r13.close()     // Catch:{ IOException -> 0x030a }
        L_0x01c6:
            int r13 = r12.a     // Catch:{  }
            r13 = r13 & 32
            if (r13 == 0) goto L_0x0296
            ihf r13 = r12.g     // Catch:{  }
            if (r13 != 0) goto L_0x01d2
            ihf r13 = defpackage.ihf.b     // Catch:{ IOException -> 0x02a5 }
        L_0x01d2:
            huf r13 = r13.a     // Catch:{  }
            java.util.Iterator r13 = r13.iterator()     // Catch:{  }
        L_0x01d8:
            boolean r14 = r13.hasNext()     // Catch:{  }
            if (r14 == 0) goto L_0x0296
            java.lang.Object r14 = r13.next()     // Catch:{  }
            ihe r14 = (defpackage.ihe) r14     // Catch:{  }
            int r14 = r14.a     // Catch:{  }
            r15 = 1
            if (r14 != r15) goto L_0x0290
            long r13 = r4.c(r0)     // Catch:{  }
            long r17 = r4.c(r2)     // Catch:{  }
            int r15 = (r13 > r17 ? 1 : (r13 == r17 ? 0 : -1))
            if (r15 <= 0) goto L_0x0296
            hig r15 = defpackage.hig.k     // Catch:{  }
            htk r15 = r15.l()     // Catch:{  }
            java.lang.String r6 = r5.b     // Catch:{  }
            r19 = r0
            htq r0 = r15.b     // Catch:{  }
            boolean r0 = r0.B()     // Catch:{  }
            if (r0 != 0) goto L_0x020a
            r15.u()     // Catch:{ IOException -> 0x02a7 }
        L_0x020a:
            htq r0 = r15.b     // Catch:{  }
            r1 = r0
            hig r1 = (defpackage.hig) r1     // Catch:{  }
            r6.getClass()     // Catch:{  }
            r21 = r3
            int r3 = r1.a     // Catch:{ IOException -> 0x02a9 }
            r20 = 1
            r3 = r3 | 1
            r1.a = r3     // Catch:{ IOException -> 0x02a9 }
            r1.b = r6     // Catch:{ IOException -> 0x02a9 }
            boolean r0 = r0.B()     // Catch:{ IOException -> 0x02a9 }
            if (r0 != 0) goto L_0x0227
            r15.u()     // Catch:{ IOException -> 0x02a9 }
        L_0x0227:
            htq r0 = r15.b     // Catch:{ IOException -> 0x02a9 }
            r1 = r0
            hig r1 = (defpackage.hig) r1     // Catch:{ IOException -> 0x02a9 }
            int r3 = r1.a     // Catch:{ IOException -> 0x02a9 }
            r6 = 2
            r3 = r3 | r6
            r1.a = r3     // Catch:{ IOException -> 0x02a9 }
            r1.c = r8     // Catch:{ IOException -> 0x02a9 }
            boolean r0 = r0.B()     // Catch:{ IOException -> 0x02a9 }
            if (r0 != 0) goto L_0x023d
            r15.u()     // Catch:{ IOException -> 0x02a9 }
        L_0x023d:
            htq r0 = r15.b     // Catch:{ IOException -> 0x02a9 }
            r1 = r0
            hig r1 = (defpackage.hig) r1     // Catch:{ IOException -> 0x02a9 }
            int r3 = r1.a     // Catch:{ IOException -> 0x02a9 }
            r3 = r3 | 64
            r1.a = r3     // Catch:{ IOException -> 0x02a9 }
            r1.h = r9     // Catch:{ IOException -> 0x02a9 }
            boolean r0 = r0.B()     // Catch:{ IOException -> 0x02a9 }
            if (r0 != 0) goto L_0x0253
            r15.u()     // Catch:{ IOException -> 0x02a9 }
        L_0x0253:
            htq r0 = r15.b     // Catch:{ IOException -> 0x02a9 }
            r1 = r0
            hig r1 = (defpackage.hig) r1     // Catch:{ IOException -> 0x02a9 }
            r11.getClass()     // Catch:{ IOException -> 0x02a9 }
            int r3 = r1.a     // Catch:{ IOException -> 0x02a9 }
            r3 = r3 | 128(0x80, float:1.794E-43)
            r1.a = r3     // Catch:{ IOException -> 0x02a9 }
            r1.i = r11     // Catch:{ IOException -> 0x02a9 }
            java.lang.String r1 = r5.c     // Catch:{ IOException -> 0x02a9 }
            boolean r0 = r0.B()     // Catch:{ IOException -> 0x02a9 }
            if (r0 != 0) goto L_0x026e
            r15.u()     // Catch:{ IOException -> 0x02a9 }
        L_0x026e:
            htq r0 = r15.b     // Catch:{ IOException -> 0x02a9 }
            hig r0 = (defpackage.hig) r0     // Catch:{ IOException -> 0x02a9 }
            r1.getClass()     // Catch:{ IOException -> 0x02a9 }
            int r3 = r0.a     // Catch:{ IOException -> 0x02a9 }
            r6 = 4
            r3 = r3 | r6
            r0.a = r3     // Catch:{ IOException -> 0x02a9 }
            r0.d = r1     // Catch:{ IOException -> 0x02a9 }
            htq r0 = r15.r()     // Catch:{ IOException -> 0x02a9 }
            r8 = r0
            hig r8 = (defpackage.hig) r8     // Catch:{ IOException -> 0x02a9 }
            java.lang.String r0 = r12.b     // Catch:{ IOException -> 0x02a9 }
            r9 = 3
            r15 = 0
            r10 = r13
            r12 = r17
            r14 = r0
            r7.g(r8, r9, r10, r12, r14, r15)     // Catch:{ IOException -> 0x02a9 }
            goto L_0x029a
        L_0x0290:
            r1 = r22
            r6 = r23
            goto L_0x01d8
        L_0x0296:
            r19 = r0
            r21 = r3
        L_0x029a:
            r4.h(r2)     // Catch:{ IOException -> 0x02a9 }
        L_0x029d:
            r1 = r22
            goto L_0x02b8
        L_0x02a0:
            r0 = move-exception
            r1 = r22
            goto L_0x0355
        L_0x02a5:
            r19 = r0
        L_0x02a7:
            r21 = r3
        L_0x02a9:
            java.lang.String r0 = "%s: Failed to get file size or delete compress file %s."
            r1 = 2
            java.lang.Object[] r3 = new java.lang.Object[r1]     // Catch:{ csi -> 0x02a0 }
            r1 = 0
            r3[r1] = r21     // Catch:{ csi -> 0x02a0 }
            r1 = 1
            r3[r1] = r2     // Catch:{ csi -> 0x02a0 }
            defpackage.cyh.p(r0, r3)     // Catch:{ csi -> 0x02a0 }
            goto L_0x029d
        L_0x02b8:
            csv r0 = r1.b     // Catch:{ csi -> 0x0354 }
            int r0 = r0.e     // Catch:{ csi -> 0x0354 }
            int r0 = defpackage.a.A(r0)     // Catch:{ csi -> 0x0354 }
            if (r0 != 0) goto L_0x02c3
            goto L_0x02c6
        L_0x02c3:
            r2 = 2
            if (r0 == r2) goto L_0x0345
        L_0x02c6:
            kjd r0 = r1.l     // Catch:{ csi -> 0x0354 }
            java.lang.String r2 = r1.c     // Catch:{ csi -> 0x0354 }
            r3 = r19
            boolean r0 = defpackage.cxt.d(r0, r3, r2)     // Catch:{ csi -> 0x0354 }
            if (r0 == 0) goto L_0x02d4
            goto L_0x0345
        L_0x02d4:
            java.lang.String r0 = "%s: Final file checksum verification failed. %s."
            r4 = r21
            defpackage.cyh.h(r0, r4, r3)     // Catch:{ csi -> 0x0354 }
            kml r0 = defpackage.csi.a()     // Catch:{ csi -> 0x0354 }
            csh r2 = defpackage.csh.FINAL_FILE_CHECKSUM_MISMATCH_ERROR     // Catch:{ csi -> 0x0354 }
            r0.b = r2     // Catch:{ csi -> 0x0354 }
            csi r0 = r0.a()     // Catch:{ csi -> 0x0354 }
            throw r0     // Catch:{ csi -> 0x0354 }
        L_0x02e8:
            r0 = move-exception
            r4 = r3
            r3 = r0
            if (r14 == 0) goto L_0x02f6
            r14.close()     // Catch:{ all -> 0x02f1 }
            goto L_0x02f6
        L_0x02f1:
            r0 = move-exception
            r5 = r0
            r3.addSuppressed(r5)     // Catch:{ all -> 0x02f7 }
        L_0x02f6:
            throw r3     // Catch:{ all -> 0x02f7 }
        L_0x02f7:
            r0 = move-exception
            goto L_0x02fb
        L_0x02f9:
            r0 = move-exception
            r4 = r3
        L_0x02fb:
            r3 = r0
            if (r13 == 0) goto L_0x0307
            r13.close()     // Catch:{ all -> 0x0302 }
            goto L_0x0307
        L_0x0302:
            r0 = move-exception
            r5 = r0
            r3.addSuppressed(r5)     // Catch:{ IOException -> 0x0308 }
        L_0x0307:
            throw r3     // Catch:{ IOException -> 0x0308 }
        L_0x0308:
            r0 = move-exception
            goto L_0x030c
        L_0x030a:
            r0 = move-exception
            r4 = r3
        L_0x030c:
            java.lang.String r3 = "%s: Failed to apply download transform for file %s."
            r5 = 2
            java.lang.Object[] r5 = new java.lang.Object[r5]     // Catch:{ csi -> 0x0354 }
            r6 = 0
            r5[r6] = r4     // Catch:{ csi -> 0x0354 }
            r4 = 1
            r5[r4] = r2     // Catch:{ csi -> 0x0354 }
            defpackage.cyh.j(r0, r3, r5)     // Catch:{ csi -> 0x0354 }
            kml r2 = defpackage.csi.a()     // Catch:{ csi -> 0x0354 }
            csh r3 = defpackage.csh.DOWNLOAD_TRANSFORM_IO_ERROR     // Catch:{ csi -> 0x0354 }
            r2.b = r3     // Catch:{ csi -> 0x0354 }
            r2.d = r0     // Catch:{ csi -> 0x0354 }
            csi r0 = r2.a()     // Catch:{ csi -> 0x0354 }
            throw r0     // Catch:{ csi -> 0x0354 }
        L_0x0329:
            r0 = move-exception
            r4 = r3
            java.lang.String r2 = "%s: Exception while trying to serialize download transform"
            r3 = 1
            java.lang.Object[] r5 = new java.lang.Object[r3]     // Catch:{ csi -> 0x0354 }
            r3 = 0
            r5[r3] = r4     // Catch:{ csi -> 0x0354 }
            defpackage.cyh.j(r0, r2, r5)     // Catch:{ csi -> 0x0354 }
            kml r2 = defpackage.csi.a()     // Catch:{ csi -> 0x0354 }
            csh r3 = defpackage.csh.UNABLE_TO_SERIALIZE_DOWNLOAD_TRANSFORM_ERROR     // Catch:{ csi -> 0x0354 }
            r2.b = r3     // Catch:{ csi -> 0x0354 }
            r2.d = r0     // Catch:{ csi -> 0x0354 }
            csi r0 = r2.a()     // Catch:{ csi -> 0x0354 }
            throw r0     // Catch:{ csi -> 0x0354 }
        L_0x0345:
            csv r0 = r1.b
            int r2 = r1.j
            cxa r3 = r1.a
            java.util.concurrent.Executor r4 = r1.h
            ctf r5 = defpackage.ctf.DOWNLOAD_COMPLETE
            hme r0 = c(r5, r0, r2, r3, r4)
            return r0
        L_0x0354:
            r0 = move-exception
        L_0x0355:
            csh r2 = r0.a
            csh r3 = defpackage.csh.DOWNLOADED_FILE_CHECKSUM_MISMATCH_ERROR
            boolean r2 = r2.equals(r3)
            if (r2 == 0) goto L_0x0393
            cxa r2 = r1.a
            csv r3 = r1.b
            int r4 = r1.j
            kjd r5 = r1.l
            java.lang.String r7 = r1.c
            cyk r8 = r1.i
            java.util.concurrent.Executor r9 = r1.h
            r6 = r23
            hme r2 = d(r2, r3, r4, r5, r6, r7, r8, r9)
            czw r2 = defpackage.czw.e(r2)
            cwi r3 = new cwi
            r4 = 20
            r3.<init>(r0, r4)
            java.util.concurrent.Executor r4 = r1.h
            java.lang.Class<java.io.IOException> r5 = java.io.IOException.class
            czw r2 = r2.d(r5, r3, r4)
            cxr r3 = new cxr
            r4 = 1
            r3.<init>(r0, r4)
            java.util.concurrent.Executor r0 = r1.h
            czw r0 = r2.g(r3, r0)
            return r0
        L_0x0393:
            hme r0 = defpackage.hfc.J(r0)
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cxs.a(android.net.Uri):hme");
    }

    public final hme b(csi csi) {
        cyh.d("%s: Failed to download file %s", "DownloaderCallbackImpl", this.c);
        if (csi.a.equals(csh.DOWNLOADED_FILE_CHECKSUM_MISMATCH_ERROR)) {
            return c(ctf.CORRUPTED, this.b, this.j, this.a, this.h);
        }
        return c(ctf.DOWNLOAD_FAILED, this.b, this.j, this.a, this.h);
    }
}
