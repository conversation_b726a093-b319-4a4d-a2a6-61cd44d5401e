package defpackage;

import java.io.Serializable;
import java.util.concurrent.TimeUnit;

/* renamed from: cpf  reason: default package */
/* compiled from: PG */
final class cpf implements Serializable, cpp {
    private static final TimeUnit a = TimeUnit.MINUTES;
    private static final long serialVersionUID = 0;

    public final int a() {
        return 1;
    }

    public final Class annotationType() {
        return cpp.class;
    }

    public final int b() {
        return 1000;
    }

    public final int c() {
        return 1;
    }

    public final int d() {
        return 1;
    }

    public final int e() {
        return 1000;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof cpp) {
            cpp cpp = (cpp) obj;
            cpp.d();
            cpp.e();
            cpp.a();
            cpp.b();
            cpp.c();
            cpp.f();
            if (a.equals(cpp.g())) {
                return true;
            }
        }
        return false;
    }

    public final long f() {
        return 1;
    }

    public final TimeUnit g() {
        return a;
    }

    public final int hashCode() {
        return (a.hashCode() ^ -810573619) - 1944263094;
    }

    public final String toString() {
        return "@com.google.android.libraries.concurrent.monitoring.ThreadMonitoringConfiguration()";
    }
}
