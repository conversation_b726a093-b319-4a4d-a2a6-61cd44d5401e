package defpackage;

import android.net.Uri;

/* renamed from: cup  reason: default package */
/* compiled from: PG */
public final class cup {
    public final Uri a;
    public final String b;
    public final cuo c;
    public final int d;
    public final gxq e;
    private final grh f;
    private final hse g;

    public cup() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof cup) {
            cup cup = (cup) obj;
            if (!this.a.equals(cup.a) || !this.b.equals(cup.b) || !this.c.equals(cup.c) || this.d != cup.d || !fvf.D(this.e, cup.e) || !this.f.equals(cup.f) || !this.g.equals(cup.g)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        int hashCode = ((((((((this.a.hashCode() ^ 1000003) * 1000003) ^ this.b.hashCode()) * 1000003) ^ this.c.hashCode()) * 1000003) ^ this.d) * 1000003) ^ this.e.hashCode();
        hse hse = this.g;
        if (hse.B()) {
            i = hse.i();
        } else {
            int i2 = hse.memoizedHashCode;
            if (i2 == 0) {
                i2 = hse.i();
                hse.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (((hashCode * 1000003) ^ 2040732332) * 1000003) ^ i;
    }

    public final String toString() {
        hse hse = this.g;
        grh grh = this.f;
        gxq gxq = this.e;
        cuo cuo = this.c;
        String valueOf = String.valueOf(this.a);
        String valueOf2 = String.valueOf(cuo);
        String valueOf3 = String.valueOf(gxq);
        String valueOf4 = String.valueOf(grh);
        String valueOf5 = String.valueOf(hse);
        return "DownloadRequest{fileUri=" + valueOf + ", urlToDownload=" + this.b + ", downloadConstraints=" + valueOf2 + ", trafficTag=" + this.d + ", extraHttpHeaders=" + valueOf3 + ", inlineDownloadParamsOptional=" + valueOf4 + ", customDownloaderMetadata=" + valueOf5 + "}";
    }

    public cup(Uri uri, String str, cuo cuo, int i, gxq gxq, grh grh, hse hse) {
        this.a = uri;
        this.b = str;
        this.c = cuo;
        this.d = i;
        this.e = gxq;
        this.f = grh;
        this.g = hse;
    }
}
