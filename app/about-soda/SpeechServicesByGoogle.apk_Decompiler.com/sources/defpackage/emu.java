package defpackage;

import java.util.concurrent.Executor;

/* renamed from: emu  reason: default package */
/* compiled from: PG */
public final class emu implements iiu {
    private final jjk a;
    private final jjk b;

    public emu(jjk jjk, jjk jjk2) {
        this.a = jjk;
        this.b = jjk2;
    }

    /* renamed from: a */
    public final emt b() {
        return new emt(((iim) this.a).a(), (Executor) this.b.b());
    }
}
