package defpackage;

/* renamed from: csr  reason: default package */
/* compiled from: PG */
public final class csr {
    public final boolean a;
    public final boolean b;
    public final boolean c;
    private final grh d;
    private final grh e;
    private final grh f;
    private final grh g;

    public csr() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof csr) {
            csr csr = (csr) obj;
            if (this.a != csr.a || !this.d.equals(csr.d) || !this.e.equals(csr.e) || !this.f.equals(csr.f) || !this.g.equals(csr.g) || this.b != csr.b || this.c != csr.c) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        int i2;
        int i3 = 1231;
        if (true != this.a) {
            i = 1237;
        } else {
            i = 1231;
        }
        int i4 = ((i ^ 1000003) * 1000003) ^ 1237;
        if (true != this.b) {
            i2 = 1237;
        } else {
            i2 = 1231;
        }
        int i5 = ((((((((((i4 * 1000003) ^ 2040732332) * 1000003) ^ 2040732332) * 1000003) ^ 2040732332) * 1000003) ^ 2040732332) * 1000003) ^ i2) * 1000003;
        if (true != this.c) {
            i3 = 1237;
        }
        return i5 ^ i3;
    }

    public final String toString() {
        grh grh = this.g;
        grh grh2 = this.f;
        grh grh3 = this.e;
        String valueOf = String.valueOf(this.d);
        String valueOf2 = String.valueOf(grh3);
        String valueOf3 = String.valueOf(grh2);
        String valueOf4 = String.valueOf(grh);
        return "GetFileGroupsByFilterRequest{includeAllGroups=" + this.a + ", groupWithNoAccountOnly=false, groupNameOptional=" + valueOf + ", groupNamePrefixOptional=" + valueOf2 + ", accountOptional=" + valueOf3 + ", sourceOptional=" + valueOf4 + ", preserveZipDirectories=" + this.b + ", verifyIsolatedStructure=" + this.c + "}";
    }

    public csr(boolean z, grh grh, grh grh2, grh grh3, grh grh4, boolean z2, boolean z3) {
        this.a = z;
        this.d = grh;
        this.e = grh2;
        this.f = grh3;
        this.g = grh4;
        this.b = z2;
        this.c = z3;
    }
}
