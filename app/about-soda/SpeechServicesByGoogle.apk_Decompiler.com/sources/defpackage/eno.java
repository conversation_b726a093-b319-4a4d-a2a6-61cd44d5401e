package defpackage;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/* renamed from: eno  reason: default package */
/* compiled from: PG */
public final class eno implements enr {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/microphone/impl/audiosourcedataaccessor/AudioSourceDataAccessorImpl");
    public final ehg b;
    public final eeh c;
    public final efo d;
    public final dyt e;
    public final grh f;
    public jju g;
    public final String h;
    public final jqh i = new jqh();
    public final jqh j;
    public final dlv k;
    private final jqs l;
    private final jjo m;
    private final jjo n;
    private final grh o;
    private final grh p;
    private final hme q;
    private final eej r;
    private final eoz s;

    public eno(ehg ehg, eeh eeh, efo efo, dyt dyt, grh grh, dlv dlv, jqs jqs, bzj bzj) {
        this.b = ehg;
        this.c = eeh;
        this.d = efo;
        this.e = dyt;
        this.f = grh;
        this.k = dlv;
        this.l = jqs;
        this.s = don.k(jqs);
        jqh jqh = new jqh();
        this.j = jqh;
        this.h = fbi.o(eeh);
        jjw jjw = new jjw(new mq(this, 19));
        this.m = jjw;
        this.n = new jjw(new bes(this, bzj, 8, (byte[]) null));
        this.o = m(edr.class);
        this.p = m(edn.class);
        this.q = jqw.w(jqh);
        grh grh2 = (grh) jjw.a();
        eej b2 = eeh.b();
        jnu.d(b2, "getAudioSourceMetadata(...)");
        this.r = b2;
    }

    private final grh m(Class cls) {
        Object obj;
        efo efo = this.d;
        List<grh> o2 = jji.o(efo.d, efo.c);
        jnu.e(o2, "<this>");
        ArrayList arrayList = new ArrayList(jji.K(o2));
        for (grh e2 : o2) {
            arrayList.add(dom.q(e2.e(), cls));
        }
        Iterator it = arrayList.iterator();
        while (true) {
            if (!it.hasNext()) {
                obj = null;
                break;
            }
            obj = it.next();
            if (((grh) obj).f()) {
                break;
            }
        }
        grh grh = (grh) obj;
        if (grh == null) {
            return gqd.a;
        }
        return grh;
    }

    public final int a() {
        l(eaj.FAILED_TO_OPEN_AUDIO_SOURCE);
        efo efo = this.d;
        ecn ecn = (ecn) efo.d.e();
        if (ecn != null) {
            return ecn.b();
        }
        ecu ecu = (ecu) efo.c.e();
        if (ecu != null) {
            return ecu.b();
        }
        throw new IllegalStateException("Source was expected to be opened by this time");
    }

    public final dyt b() {
        return this.e;
    }

    public final dyw c(eal eal, enk enk) {
        dxx dxx;
        grh grh;
        efo efo = this.d;
        ecn ecn = (ecn) efo.d.e();
        if (ecn != null) {
            dxx = doe.h(ecn, enk.a());
        } else {
            dxx = null;
        }
        grh grh2 = efo.c;
        grh g2 = grh.g(dxx);
        grh g3 = doe.g((ecu) grh2.e(), enk.a());
        grh g4 = grh.g((Object) null);
        if (eal == eal.OPENED_SHARED) {
            grh = dom.n(this.o).a(new ejr(wl.s, 6));
        } else {
            grh = this.o.a(new ejr(wl.t, 7));
        }
        jnu.e(eal, "success");
        htk l2 = eak.c.l();
        jnu.d(l2, "newBuilder(...)");
        dlv e2 = jnu.e(l2, "builder");
        e2.g(eal);
        return ejw.c(g2, g3, g4, e2.e(), grh, (hme) dom.o(this.p, enk.a(), this.e).a());
    }

    public final eej d() {
        return this.r;
    }

    public final /* synthetic */ ekt e() {
        return null;
    }

    public final eqw g() {
        return (eqw) this.n.a();
    }

    public final hme h(eam eam) {
        jnu.e(eam, "stopListeningReason");
        return this.s.o(new ene(this, eam, (jlr) null, 2));
    }

    public final hme i(eal eal, enk enk) {
        jnu.e(eal, "success");
        return this.s.o(new enm(this, enk, eal, (jlr) null, 0));
    }

    public final hme j() {
        return this.q;
    }

    public final hme k(enk enk) {
        return this.s.o(new enn(this, enk, (jlr) null, 0));
    }

    public final void l(eaj eaj) {
        Object obj;
        try {
            obj = (eak) this.i.A();
        } catch (Throwable th) {
            obj = jji.b(th);
        }
        if (true == (obj instanceof jjt)) {
            obj = null;
        }
        eak eak = (eak) obj;
        if (eak == null || eak.a != 1) {
            throw new elc(ejw.h(eaj));
        }
    }

    public final /* synthetic */ enr f() {
        return this;
    }
}
