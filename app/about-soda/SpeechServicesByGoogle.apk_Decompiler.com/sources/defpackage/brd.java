package defpackage;

import java.util.Map;

/* renamed from: brd  reason: default package */
/* compiled from: PG */
public final class brd implements iht, iio {
    private iiz a;
    private iiz b;
    private iiz c;
    private iiz d;
    private final brc e;

    public brd() {
        throw null;
    }

    public final Map a() {
        return haq.a;
    }

    public final Map b() {
        return new iiw(gxv.m(this.b, this.c, this.d));
    }

    public brd(brc brc, abv abv) {
        this.e = brc;
        iiu a2 = iiv.a(abv);
        this.a = a2;
        this.b = new foh(a2, 5);
        iiz iiz = brc.c;
        iiz iiz2 = brc.i;
        this.c = new ecb(a2, iiz, iiz2, 18);
        this.d = new ecb((jjk) brc.a, (jjk) brc.n, (jjk) iiz2, 20, (int[][][]) null);
    }
}
