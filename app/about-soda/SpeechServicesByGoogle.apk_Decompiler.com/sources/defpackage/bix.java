package defpackage;

/* renamed from: bix  reason: default package */
/* compiled from: PG */
public final class bix extends jmi implements jne {
    Object a;
    Object b;
    int c;
    final /* synthetic */ bbj d;
    final /* synthetic */ bhe e;
    final /* synthetic */ byw f;
    private /* synthetic */ Object g;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bix(bbj bbj, byw byw, bhe bhe, jlr jlr) {
        super(2, jlr);
        this.d = bbj;
        this.f = byw;
        this.e = bhe;
    }

    public final /* bridge */ /* synthetic */ Object b(Object obj, Object obj2) {
        return ((bix) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v7, resolved type: hme} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v3, resolved type: jrz} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v11, resolved type: hme} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v9, resolved type: jrz} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v10, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v12, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v12, resolved type: jrz} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v13, resolved type: hme} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v14, resolved type: hme} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v13, resolved type: jrz} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v14, resolved type: jrz} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v16, resolved type: jrz} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v17, resolved type: jrz} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v18, resolved type: jrz} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v16, resolved type: hme} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v17, resolved type: hme} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r1v18, resolved type: hme} */
    /* JADX WARNING: type inference failed for: r0v1, types: [jrz] */
    /* JADX WARNING: type inference failed for: r0v11 */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r14) {
        /*
            r13 = this;
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r1 = r13.c
            r2 = -256(0xffffffffffffff00, float:NaN)
            if (r1 == 0) goto L_0x0018
            java.lang.Object r0 = r13.b
            java.lang.Object r1 = r13.a
            java.lang.Object r3 = r13.g
            java.util.concurrent.atomic.AtomicInteger r3 = (java.util.concurrent.atomic.AtomicInteger) r3
            defpackage.jji.c(r14)     // Catch:{ CancellationException -> 0x0016, all -> 0x0014 }
            goto L_0x0051
        L_0x0014:
            r14 = move-exception
            goto L_0x005c
        L_0x0016:
            r14 = move-exception
            goto L_0x0073
        L_0x0018:
            defpackage.jji.c(r14)
            java.lang.Object r14 = r13.g
            jqs r14 = (defpackage.jqs) r14
            java.util.concurrent.atomic.AtomicInteger r1 = new java.util.concurrent.atomic.AtomicInteger
            r1.<init>(r2)
            bbj r3 = r13.d
            byw r4 = r13.f
            bhe r5 = r13.e
            hme r10 = r3.b()
            biw r11 = new biw
            r8 = 0
            r9 = 0
            r3 = r11
            r6 = r1
            r7 = r10
            r3.<init>((defpackage.byw) r4, (defpackage.bhe) r5, (java.util.concurrent.atomic.AtomicInteger) r6, (defpackage.hme) r7, (defpackage.jlr) r8, (int) r9)
            r3 = 3
            r4 = 0
            jrz r14 = defpackage.job.S(r14, r4, r4, r11, r3)
            r13.g = r1     // Catch:{ CancellationException -> 0x006d, all -> 0x0058 }
            r13.a = r10     // Catch:{ CancellationException -> 0x006d, all -> 0x0058 }
            r13.b = r14     // Catch:{ CancellationException -> 0x006d, all -> 0x0058 }
            r3 = 1
            r13.c = r3     // Catch:{ CancellationException -> 0x006d, all -> 0x0058 }
            java.lang.Object r3 = defpackage.kq.d(r10, r13)     // Catch:{ CancellationException -> 0x006d, all -> 0x0058 }
            if (r3 == r0) goto L_0x0057
            r0 = r14
            r14 = r3
            r3 = r1
            r1 = r10
        L_0x0051:
            androidx.wear.ambient.AmbientLifecycleObserverKt r14 = (androidx.wear.ambient.AmbientLifecycleObserverKt) r14     // Catch:{ CancellationException -> 0x0016, all -> 0x0014 }
            r0.r((java.util.concurrent.CancellationException) null)
            return r14
        L_0x0057:
            return r0
        L_0x0058:
            r0 = move-exception
            r12 = r0
            r0 = r14
            r14 = r12
        L_0x005c:
            java.lang.String r1 = defpackage.bje.a     // Catch:{ all -> 0x006b }
            bbj r1 = r13.d     // Catch:{ all -> 0x006b }
            defpackage.bbk.a()     // Catch:{ all -> 0x006b }
            java.lang.Class r1 = r1.getClass()     // Catch:{ all -> 0x006b }
            java.util.Objects.toString(r1)     // Catch:{ all -> 0x006b }
            throw r14     // Catch:{ all -> 0x006b }
        L_0x006b:
            r14 = move-exception
            goto L_0x0098
        L_0x006d:
            r0 = move-exception
            r3 = r1
            r1 = r10
            r12 = r0
            r0 = r14
            r14 = r12
        L_0x0073:
            java.lang.String r4 = defpackage.bje.a     // Catch:{ all -> 0x006b }
            bbj r4 = r13.d     // Catch:{ all -> 0x006b }
            defpackage.bbk.a()     // Catch:{ all -> 0x006b }
            java.lang.Class r4 = r4.getClass()     // Catch:{ all -> 0x006b }
            java.util.Objects.toString(r4)     // Catch:{ all -> 0x006b }
            int r4 = r3.get()     // Catch:{ all -> 0x006b }
            boolean r1 = r1.isCancelled()     // Catch:{ all -> 0x006b }
            if (r1 == 0) goto L_0x0097
            if (r4 == r2) goto L_0x0097
            biu r14 = new biu     // Catch:{ all -> 0x006b }
            int r1 = r3.get()     // Catch:{ all -> 0x006b }
            r14.<init>(r1)     // Catch:{ all -> 0x006b }
            throw r14     // Catch:{ all -> 0x006b }
        L_0x0097:
            throw r14     // Catch:{ all -> 0x006b }
        L_0x0098:
            r0.r((java.util.concurrent.CancellationException) null)
            throw r14
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bix.bk(java.lang.Object):java.lang.Object");
    }

    public final jlr c(Object obj, jlr jlr) {
        bix bix = new bix(this.d, this.f, this.e, jlr);
        bix.g = obj;
        return bix;
    }
}
