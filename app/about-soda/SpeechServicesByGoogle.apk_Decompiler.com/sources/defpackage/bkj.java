package defpackage;

import android.app.Dialog;
import android.content.DialogInterface;

/* renamed from: bkj  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bkj implements DialogInterface.OnShowListener {
    public final /* synthetic */ Dialog a;

    public /* synthetic */ bkj(Dialog dialog) {
        this.a = dialog;
    }

    public final void onShow(DialogInterface dialogInterface) {
        this.a.getWindow().setSoftInputMode(5);
    }
}
