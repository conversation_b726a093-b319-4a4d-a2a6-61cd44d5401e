package defpackage;

import android.content.Context;
import android.content.SharedPreferences;
import com.google.android.apps.speech.tts.googletts.dispatch.LanguageRegistry;
import j$.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;

/* renamed from: brx  reason: default package */
/* compiled from: PG */
public final class brx implements iiu {
    private final jjk a;
    private final /* synthetic */ int b;
    private final Object c;

    public brx(Object obj, jjk jjk, int i) {
        this.b = i;
        this.c = obj;
        this.a = jjk;
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v4, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r1v8, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v17, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v23, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r1v24, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v34, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v45, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v63, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r0v78, types: [java.lang.Object, jjk] */
    public final /* synthetic */ Object b() {
        switch (this.b) {
            case 0:
                return new LanguageRegistry(((bto) this.a).b(), (bwm) this.c.b());
            case 1:
                ahr ahr = new ahr((kjx) this.c.b(), (ExecutorService) this.a.b());
                ahr.c = "Google-Speech-Actions";
                ahr.a = ahi.a;
                ahr.b = new bro();
                return ahr.a();
            case 2:
                kjd a2 = ((gay) this.c).b();
                jjk jjk = this.a;
                Objects.requireNonNull(jjk);
                return new gav(new dab(jjk, 1), a2);
            case 3:
                bxc bxc = (bxc) this.c.b();
                return new bvj((Context) this.a.b());
            case 4:
                return new bvl((Context) this.c.b(), (bvj) this.a.b());
            case 5:
                return new bwn((Context) this.a.b(), ((bwo) this.c).b());
            case 6:
                bxc bxc2 = (bxc) this.a.b();
                if (!((Context) this.c.b()).getPackageManager().hasSystemFeature("android.hardware.telephony")) {
                    bxc2.f(true);
                }
                hzz.u(bxc2);
                return bxc2;
            case 7:
                return new byl(((iim) this.c).a(), ((gbe) this.a).b());
            case 8:
                return new coj(((cns) this.a).a(), this.c);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                cqx cqx = (cqx) this.c.b();
                return new coy(((coc) this.a).b());
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                ((iim) this.a).a();
                grh grh = ((cxm) this.c).b;
                hzz.u(grh);
                return grh;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                cqx cqx2 = (cqx) this.c.b();
                return new czp((hmh) this.a.b());
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                ((iim) this.c).a();
                cqh cqh = (cqh) this.a.b();
                return new czt();
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                gif a3 = gig.a();
                a3.a = "TaskPeriods";
                a3.d(czy.b);
                return ((gih) this.c).b().h(a3.a(), (kjd) this.a.b());
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                kjd a4 = ((gay) this.c).b();
                jjk jjk2 = this.a;
                Objects.requireNonNull(jjk2);
                return new gav(new dab(jjk2, 0), a4);
            case 15:
                return new cxk((fps) this.c.b(), (gnk) this.a.b());
            case 16:
                SharedPreferences sharedPreferences = (SharedPreferences) ((gsb) ((drj) this.a).a().d(new aio(((iim) this.c).a(), 9))).a();
                hzz.u(sharedPreferences);
                return sharedPreferences;
            case 17:
                int i = djl.b;
                if (!fnk.g()) {
                    dmb dmb = (dmb) this.c.b();
                    if (!dmb.b()) {
                        ((hby) ((hby) djs.a.h()).j("com/google/android/libraries/performance/primes/CrashOnBadPrimesConfiguration", "observedBackgroundInitialization", 29, "CrashOnBadPrimesConfiguration.java")).u("Primes init triggered from background in package: %s", dmb.a);
                        if (!dmb.a()) {
                            throw new IllegalStateException(String.format("Primes init triggered from background in package: %s", new Object[]{dmb.a}));
                        }
                    }
                }
                return new djl(((djo) this.a).b());
            case 18:
                djt djt = (djt) this.a.b();
                hmi hmi = djt.a;
                bzj a5 = ((con) this.c).b();
                if (hmi == null) {
                    int i2 = djt.c;
                    ScheduledThreadPoolExecutor scheduledThreadPoolExecutor = new ScheduledThreadPoolExecutor(i2, new djr(djt.b), new djq());
                    scheduledThreadPoolExecutor.setMaximumPoolSize(i2);
                    hmi = a5.a(hfc.F(scheduledThreadPoolExecutor));
                }
                hzz.u(hmi);
                return hmi;
            case 19:
                return new dkd((cxj) this.a.b(), (grh) ((iiv) this.c).a);
            default:
                return new dku(((iim) this.c).a(), (dku) this.a.b());
        }
    }

    public brx(jjk jjk, jjk jjk2, int i) {
        this.b = i;
        this.a = jjk;
        this.c = jjk2;
    }
}
