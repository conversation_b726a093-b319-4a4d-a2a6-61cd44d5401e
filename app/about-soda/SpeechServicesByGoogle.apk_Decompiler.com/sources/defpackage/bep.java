package defpackage;

import android.app.job.JobInfo;
import android.app.job.JobScheduler;
import android.content.ComponentName;
import android.content.Context;
import android.net.NetworkRequest;
import android.os.Build;
import android.os.PersistableBundle;
import androidx.wear.ambient.AmbientModeSupport;
import androidx.work.impl.WorkDatabase;
import androidx.work.impl.background.systemjob.SystemJobService;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import java.util.Objects;

/* renamed from: bep  reason: default package */
/* compiled from: PG */
public final class bep implements bcr {
    public static final /* synthetic */ int a = 0;
    private static final String b = bbk.b("SystemJobScheduler");
    private final Context c;
    private final JobScheduler d;
    private final beo e;
    private final WorkDatabase f;
    private final bam g;

    public bep(Context context, WorkDatabase workDatabase, bam bam) {
        JobScheduler a2 = ben.a(context);
        AmbientModeSupport.AmbientCallback ambientCallback = bam.q;
        beo beo = new beo(context, bam.n);
        this.c = context;
        this.d = a2;
        this.e = beo;
        this.f = workDatabase;
        this.g = bam;
    }

    public static bgt a(JobInfo jobInfo) {
        PersistableBundle extras = jobInfo.getExtras();
        if (extras == null) {
            return null;
        }
        try {
            if (!extras.containsKey("EXTRA_WORK_SPEC_ID")) {
                return null;
            }
            return new bgt(extras.getString("EXTRA_WORK_SPEC_ID"), extras.getInt("EXTRA_WORK_SPEC_GENERATION", 0));
        } catch (NullPointerException unused) {
            return null;
        }
    }

    public static List e(Context context, JobScheduler jobScheduler) {
        List<JobInfo> b2 = ben.b(jobScheduler);
        if (b2 == null) {
            return null;
        }
        ArrayList arrayList = new ArrayList(b2.size());
        ComponentName componentName = new ComponentName(context, SystemJobService.class);
        for (JobInfo jobInfo : b2) {
            if (componentName.equals(jobInfo.getService())) {
                arrayList.add(jobInfo);
            }
        }
        return arrayList;
    }

    public static void f(JobScheduler jobScheduler, int i) {
        try {
            jobScheduler.cancel(i);
        } catch (Throwable th) {
            bbk.a().d(b, String.format(Locale.getDefault(), "Exception while trying to cancel job (%d)", new Object[]{Integer.valueOf(i)}), th);
        }
    }

    public final void b(String str) {
        ArrayList<Integer> arrayList;
        List<JobInfo> e2 = e(this.c, this.d);
        if (e2 == null) {
            arrayList = null;
        } else {
            ArrayList arrayList2 = new ArrayList(2);
            for (JobInfo jobInfo : e2) {
                bgt a2 = a(jobInfo);
                if (a2 != null && str.equals(a2.a)) {
                    arrayList2.add(Integer.valueOf(jobInfo.getId()));
                }
            }
            arrayList = arrayList2;
        }
        if (arrayList != null && !arrayList.isEmpty()) {
            for (Integer intValue : arrayList) {
                f(this.d, intValue.intValue());
            }
            bgo x = this.f.x();
            bgs bgs = (bgs) x;
            bgs.a.k();
            axc d2 = bgs.c.d();
            d2.g(1, str);
            try {
                ((bgs) x).a.l();
                d2.a();
                ((bgs) x).a.o();
                ((bgs) x).a.m();
                bgs.c.f(d2);
            } catch (Throwable th) {
                bgs.c.f(d2);
                throw th;
            }
        }
    }

    public final void c(bhe... bheArr) {
        WorkDatabase workDatabase;
        int i;
        byw byw = new byw(this.f, (byte[]) null);
        int length = bheArr.length;
        int i2 = 0;
        while (i2 < length) {
            bhe bhe = bheArr[i2];
            this.f.l();
            try {
                bhe b2 = this.f.A().b(bhe.b);
                if (b2 == null) {
                    bbk a2 = bbk.a();
                    String str = b;
                    a2.f(str, "Skipping scheduling " + bhe.b + " because it's no longer in the DB");
                    this.f.o();
                    workDatabase = this.f;
                } else if (b2.c != bbx.ENQUEUED) {
                    bbk a3 = bbk.a();
                    String str2 = b;
                    a3.f(str2, "Skipping scheduling " + bhe.b + " because it is no longer enqueued");
                    this.f.o();
                    workDatabase = this.f;
                } else {
                    bgt f2 = wg.f(bhe);
                    bgn f3 = wf.f(this.f.x(), f2);
                    if (f3 != null) {
                        i = f3.c;
                    } else {
                        int i3 = this.g.k;
                        Object e2 = ((aus) byw.a).e(new bdr((Object) byw, 4));
                        jnu.d(e2, "workDatabase.runInTransa…d\n            }\n        )");
                        i = ((Number) e2).intValue();
                    }
                    if (f3 == null) {
                        this.f.x().a(wf.e(f2, i));
                    }
                    g(bhe, i);
                    this.f.o();
                    workDatabase = this.f;
                }
                workDatabase.m();
                i2++;
            } catch (Throwable th) {
                this.f.m();
                throw th;
            }
        }
    }

    public final boolean d() {
        return true;
    }

    public final void g(bhe bhe, int i) {
        int i2;
        int i3;
        String str;
        String str2;
        int i4;
        int i5;
        bhe bhe2 = bhe;
        baq baq = bhe2.k;
        PersistableBundle persistableBundle = new PersistableBundle();
        persistableBundle.putString("EXTRA_WORK_SPEC_ID", bhe2.b);
        persistableBundle.putInt("EXTRA_WORK_SPEC_GENERATION", bhe2.u);
        persistableBundle.putBoolean("EXTRA_IS_PERIODIC", bhe.d());
        beo beo = this.e;
        JobInfo.Builder extras = new JobInfo.Builder(i, beo.a).setRequiresCharging(baq.d).setRequiresDeviceIdle(baq.e).setExtras(persistableBundle);
        NetworkRequest a2 = baq.a();
        int i6 = 0;
        if (Build.VERSION.SDK_INT < 28 || a2 == null) {
            bbl bbl = baq.b;
            if (Build.VERSION.SDK_INT < 30 || bbl != bbl.TEMPORARILY_UNMETERED) {
                int ordinal = bbl.ordinal();
                if (ordinal != 0) {
                    if (ordinal != 1) {
                        i5 = 2;
                        if (ordinal != 2) {
                            i5 = 3;
                            if (ordinal != 3) {
                                i5 = 4;
                                if (ordinal != 4) {
                                    bbk.a();
                                    Objects.toString(bbl);
                                }
                            }
                        }
                    }
                    i5 = 1;
                } else {
                    i5 = 0;
                }
                extras.setRequiredNetworkType(i5);
            } else {
                JobInfo.Builder unused = extras.setRequiredNetwork(new NetworkRequest.Builder().addCapability(25).build());
            }
        } else {
            jnu.e(extras, "builder");
            JobInfo.Builder unused2 = extras.setRequiredNetwork(a2);
        }
        if (!baq.e) {
            if (bhe2.m == bak.LINEAR) {
                i4 = 0;
            } else {
                i4 = 1;
            }
            extras.setBackoffCriteria(bhe2.n, i4);
        }
        long max = Math.max(bhe.a() - System.currentTimeMillis(), 0);
        if (Build.VERSION.SDK_INT <= 28) {
            extras.setMinimumLatency(max);
        } else if (max > 0) {
            extras.setMinimumLatency(max);
        } else if (!bhe2.r && beo.b) {
            JobInfo.Builder unused3 = extras.setImportantWhileForeground(true);
        }
        if (baq.b()) {
            for (bap bap : baq.j) {
                JobInfo.Builder unused4 = extras.addTriggerContentUri(new JobInfo.TriggerContentUri(bap.a, bap.b ? 1 : 0));
            }
            JobInfo.Builder unused5 = extras.setTriggerContentUpdateDelay(baq.h);
            JobInfo.Builder unused6 = extras.setTriggerContentMaxDelay(baq.i);
        }
        extras.setPersisted(false);
        JobInfo.Builder unused7 = extras.setRequiresBatteryNotLow(baq.f);
        JobInfo.Builder unused8 = extras.setRequiresStorageNotLow(baq.g);
        int i7 = bhe2.l;
        if (Build.VERSION.SDK_INT >= 31 && bhe2.r && i7 <= 0 && max <= 0) {
            JobInfo.Builder unused9 = extras.setExpedited(true);
        }
        if (Build.VERSION.SDK_INT >= 35 && (str2 = bhe2.y) != null) {
            JobInfo.Builder unused10 = extras.setTraceTag(str2);
        }
        JobInfo build = extras.build();
        bbk.a();
        String str3 = bhe2.b;
        try {
            if (this.d.schedule(build) == 0) {
                bbk.a().f(b, "Unable to schedule work ID " + bhe2.b);
                if (bhe2.r && bhe2.s == bbt.RUN_AS_NON_EXPEDITED_WORK_REQUEST) {
                    bhe2.r = false;
                    String.format("Scheduling a non-expedited job (work ID %s)", new Object[]{bhe2.b});
                    bbk.a();
                    g(bhe, i);
                }
            }
        } catch (IllegalStateException e2) {
            Context context = this.c;
            WorkDatabase workDatabase = this.f;
            bam bam = this.g;
            jnu.e(context, "context");
            jnu.e(workDatabase, "workDatabase");
            jnu.e(bam, "configuration");
            int size = workDatabase.A().d().size();
            String str4 = "<faulty JobScheduler failed to getPendingJobs>";
            if (Build.VERSION.SDK_INT >= 34) {
                JobScheduler a3 = ben.a(context);
                List b2 = ben.b(a3);
                if (b2 != null) {
                    List e3 = e(context, a3);
                    if (e3 != null) {
                        i3 = b2.size() - e3.size();
                    } else {
                        i3 = 0;
                    }
                    String str5 = null;
                    if (i3 == 0) {
                        str = null;
                    } else {
                        str = i3 + " of which are not owned by WorkManager";
                    }
                    Object systemService = context.getSystemService("jobscheduler");
                    jnu.c(systemService, "null cannot be cast to non-null type android.app.job.JobScheduler");
                    List e4 = e(context, (JobScheduler) systemService);
                    if (e4 != null) {
                        i6 = e4.size();
                    }
                    if (i6 != 0) {
                        str5 = i6 + " from WorkManager in the default namespace";
                    }
                    str4 = jji.M(jji.Z(new String[]{b2.size() + " jobs in \"androidx.work.systemjobscheduler\" namespace", str, str5}), ",\n", (CharSequence) null, (CharSequence) null, (jna) null, 62);
                }
            } else {
                List e5 = e(context, ben.a(context));
                if (e5 != null) {
                    str4 = e5.size() + " jobs from WorkManager";
                }
            }
            if (Build.VERSION.SDK_INT >= 31) {
                i2 = 150;
            } else {
                i2 = 100;
            }
            String str6 = "JobScheduler " + i2 + " job limit exceeded.\nIn JobScheduler there are " + str4 + ".\nThere are " + size + " jobs tracked by WorkManager's database;\nthe Configuration limit is " + bam.m + '.';
            bbk.a().c(b, str6);
            IllegalStateException illegalStateException = new IllegalStateException(str6, e2);
            um umVar = this.g.g;
            if (umVar != null) {
                umVar.a(illegalStateException);
                return;
            }
            throw illegalStateException;
        } catch (Throwable th) {
            bbk a4 = bbk.a();
            String str7 = b;
            Objects.toString(bhe);
            a4.d(str7, "Unable to schedule ".concat(String.valueOf(bhe)), th);
        }
    }
}
