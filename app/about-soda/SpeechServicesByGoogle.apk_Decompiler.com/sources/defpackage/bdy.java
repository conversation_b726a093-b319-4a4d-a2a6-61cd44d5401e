package defpackage;

import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/* renamed from: bdy  reason: default package */
/* compiled from: PG */
public final class bdy {
    public static final String a = bbk.b("WorkerWrapper");

    public static final Object a(hme hme, bbj bbj, jlr jlr) {
        try {
            if (hme.isDone()) {
                return b(hme);
            }
            jqb jqb = new jqb(jji.av(jlr), 1);
            jqb.s();
            hme.c(new bcu((Object) hme, (Runnable) jqb, 0), bay.a);
            jqb.t(new exv(bbj, hme, 1));
            Object a2 = jqb.a();
            if (a2 == jlx.COROUTINE_SUSPENDED) {
                jji.as(jlr);
            }
            return a2;
        } catch (ExecutionException e) {
            throw c(e);
        }
    }

    public static final Object b(Future future) {
        Object obj;
        boolean z = false;
        while (true) {
            try {
                obj = future.get();
                break;
            } catch (InterruptedException unused) {
                z = true;
            } catch (Throwable th) {
                if (z) {
                    Thread.currentThread().interrupt();
                }
                throw th;
            }
        }
        if (z) {
            Thread.currentThread().interrupt();
        }
        return obj;
    }

    public static final Throwable c(ExecutionException executionException) {
        Throwable cause = executionException.getCause();
        jnu.b(cause);
        return cause;
    }
}
