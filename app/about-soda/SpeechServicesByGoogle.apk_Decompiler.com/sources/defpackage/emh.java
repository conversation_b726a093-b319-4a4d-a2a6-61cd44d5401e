package defpackage;

/* renamed from: emh  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class emh implements gsb {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ emh(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                fqy g = ((emi) this.a).a.g("/client_streamz/audio_library_android/audio_format_characteristics", new fqx("channel_config", String.class), new fqx("sample_rate", String.class), new fqx("encoding", String.class), new fqx("client_name", String.class));
                g.c();
                return g;
            case 1:
                fqv c = ((emi) this.a).a.c("/client_streamz/audio_library_android/audio_routing_latency", new fqx("audio_route_type", String.class));
                c.c();
                return c;
            case 2:
                fqy g2 = ((emi) this.a).a.g("/client_streamz/audio_library_android/audio_listening_session_closing_status", new fqx("status", String.class), new fqx("reason", String.class), new fqx("device_ram_gb", String.class), new fqx("client_name", String.class));
                g2.c();
                return g2;
            case 3:
                fqy g3 = ((emi) this.a).a.g("/client_streamz/audio_library_android/audio_listening_session_opening_status", new fqx("status", String.class), new fqx("device_ram_gb", String.class), new fqx("client_name", String.class), new fqx("successful", Boolean.class));
                g3.c();
                return g3;
            case 4:
                fqy g4 = ((emi) this.a).a.g("/client_streamz/audio_library_android/audio_routing_disconnect_status", new fqx("audio_route_type", String.class), new fqx("status", String.class));
                g4.c();
                return g4;
            case 5:
                fqy g5 = ((emi) this.a).a.g("/client_streamz/audio_library_android/audio_routing_status", new fqx("audio_route_type", String.class), new fqx("status", String.class));
                g5.c();
                return g5;
            case 6:
                fqv c2 = ((emi) this.a).a.c("/client_streamz/audio_library_android/audio_source_start_latency", new fqx("client", String.class), new fqx("route", String.class), new fqx("device_ram_gb", String.class));
                c2.c();
                return c2;
            case 7:
                fqv c3 = ((emi) this.a).a.c("/client_streamz/audio_library_android/audio_start_recording_correction_duration", new fqx("negative", Boolean.class), new fqx("client_type", String.class), new fqx("client", String.class), new fqx("source_type", String.class));
                c3.c();
                return c3;
            case 8:
                fqv c4 = ((emi) this.a).a.c("/client_streamz/audio_library_android/audio_start_time_computation_duration", new fqx("source_type", String.class), new fqx("completion_reason", String.class), new fqx("found_start_time", Boolean.class), new fqx("device_ram_gb", String.class));
                c4.c();
                return c4;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fqy g6 = ((emi) this.a).a.g("/client_streamz/audio_library_android/client_info_undefined", new fqx("client_type", String.class));
                g6.c();
                return g6;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fqy g7 = ((emi) this.a).a.g("/client_streamz/audio_library_android/hotword_listening_session_timeout", new fqx("hotword_client_name", String.class));
                g7.c();
                return g7;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                fqy g8 = ((emi) this.a).a.g("/client_streamz/audio_library_android/hotword_session_closing_status", new fqx("status", String.class), new fqx("reason", String.class), new fqx("device_ram_gb", String.class), new fqx("client_name", String.class));
                g8.c();
                return g8;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                fqy g9 = ((emi) this.a).a.g("/client_streamz/audio_library_android/hotword_session_opening_status", new fqx("status", String.class), new fqx("device_ram_gb", String.class), new fqx("successful", Boolean.class), new fqx("client_name", String.class));
                g9.c();
                return g9;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                fqy g10 = ((frc) ((bmu) this.a).a).g("/client_streamz/android_gsa/aap/apa_triggering_unexpected_disconnect_call_counter", new fqx("type", String.class));
                g10.c();
                return g10;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                fqy g11 = ((frc) ((bmu) this.a).a).g("/client_streamz/android_gsa/pcp/sync_while_charging", new fqx("client_type", String.class), new fqx("data_type", String.class));
                g11.c();
                return g11;
            case 15:
                fqy g12 = ((frc) ((bmu) this.a).a).g("/client_streamz/android_gsa/pcp/ttl_triggering_time_range", new fqx("counter_name", String.class));
                g12.c();
                return g12;
            case 16:
                fqv c5 = ((frc) ((bmu) this.a).a).c("/client_streamz/android_gsa/pixelsettings/get_suggestions_latency", new fqx("language", String.class), new fqx("status", String.class));
                c5.c();
                return c5;
            case 17:
                fqy g13 = ((frc) ((bmu) this.a).a).g("/client_streamz/android_gsa/podcasts/download_bytes", new fqx("app_version", String.class));
                g13.c();
                return g13;
            case 18:
                fqy g14 = ((frc) ((bmu) this.a).a).g("/client_streamz/android_gsa/podcasts/entry_point_count", new fqx("app_version", String.class), new fqx("tng", Boolean.class), new fqx("entry_point_type", String.class));
                g14.c();
                return g14;
            case 19:
                fqy g15 = ((frc) ((bmu) this.a).a).g("/client_streamz/android_gsa/podcasts/errors", new fqx("app_version", String.class), new fqx("error_name", String.class));
                g15.c();
                return g15;
            default:
                fqy g16 = ((frc) ((bmu) this.a).a).g("/client_streamz/android_gsa/podcasts/events", new fqx("app_version", String.class), new fqx("event_name", String.class));
                g16.c();
                return g16;
        }
    }
}
