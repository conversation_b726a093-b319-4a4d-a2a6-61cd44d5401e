package defpackage;

import android.content.Context;
import android.os.PowerManager;

/* renamed from: bil  reason: default package */
/* compiled from: PG */
public final class bil {
    public static final String a = bbk.b("WakeLocks");

    public static final PowerManager.WakeLock a(Context context, String str) {
        jnu.e(context, "context");
        Object systemService = context.getApplicationContext().getSystemService("power");
        jnu.c(systemService, "null cannot be cast to non-null type android.os.PowerManager");
        String concat = "WorkManager: ".concat(str);
        PowerManager.WakeLock newWakeLock = ((PowerManager) systemService).newWakeLock(1, concat);
        synchronized (bim.a) {
            String str2 = (String) bim.b.put(newWakeLock, concat);
        }
        jnu.d(newWakeLock, "wakeLock");
        return newWakeLock;
    }
}
