package defpackage;

/* renamed from: ekt  reason: default package */
/* compiled from: PG */
public final class ekt extends htq implements hvb {
    public static final ekt c;
    private static volatile hvh d;
    public int a;
    public String b = "";

    static {
        ekt ekt = new ekt();
        c = ekt;
        htq.z(ekt.class, ekt);
    }

    private ekt() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(c, "\u0004\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001ဈ\u0000", new Object[]{"a", "b"});
        } else if (i2 == 3) {
            return new ekt();
        } else {
            if (i2 == 4) {
                return new htk((htq) c);
            }
            if (i2 == 5) {
                return c;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = d;
            if (hvh == null) {
                synchronized (ekt.class) {
                    hvh = d;
                    if (hvh == null) {
                        hvh = new htl(c);
                        d = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
