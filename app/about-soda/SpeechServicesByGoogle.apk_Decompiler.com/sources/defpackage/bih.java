package defpackage;

import java.util.ArrayDeque;
import java.util.concurrent.Executor;

/* renamed from: bih  reason: default package */
/* compiled from: PG */
public final class bih implements Executor {
    public final ArrayDeque a = new ArrayDeque();
    public final Object b = new Object();
    private final Executor c;
    private Runnable d;
    private final /* synthetic */ int e;

    public bih(Executor executor, int i, byte[] bArr) {
        this.e = i;
        this.c = executor;
    }

    public final void a() {
        if (this.e != 0) {
            synchronized (this.b) {
                Object poll = this.a.poll();
                Runnable runnable = (Runnable) poll;
                this.d = runnable;
                if (poll != null) {
                    this.c.execute(runnable);
                }
            }
            return;
        }
        Runnable runnable2 = (Runnable) this.a.poll();
        this.d = runnable2;
        if (runnable2 != null) {
            this.c.execute(runnable2);
        }
    }

    public final void execute(Runnable runnable) {
        if (this.e != 0) {
            jnu.e(runnable, "command");
            synchronized (this.b) {
                this.a.offer(new aku((Object) runnable, (Object) this, 4, (char[]) null));
                if (this.d == null) {
                    a();
                }
            }
            return;
        }
        synchronized (this.b) {
            this.a.add(new bcu((Object) this, runnable, 2));
            if (this.d == null) {
                a();
            }
        }
    }

    public bih(Executor executor, int i) {
        this.e = i;
        this.c = executor;
    }
}
