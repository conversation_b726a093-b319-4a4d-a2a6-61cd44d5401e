package defpackage;

import androidx.wear.ambient.AmbientLifecycleObserverKt;
import androidx.wear.ambient.AmbientModeSupport;
import androidx.wear.ambient.WearableControllerProvider;
import java.util.concurrent.Executor;

/* renamed from: bam  reason: default package */
/* compiled from: PG */
public final class bam {
    public final Executor a;
    public final jlv b;
    public final Executor c;
    public final bcb d;
    public final bbv e;
    public final um f;
    public final um g;
    public final um h;
    public final String i;
    public final int j;
    public final int k;
    public final int l;
    public final int m;
    public final boolean n;
    public final WearableControllerProvider o;
    public final AmbientLifecycleObserverKt p;
    public final AmbientModeSupport.AmbientCallback q;

    /* JADX WARNING: Code restructure failed: missing block: B:13:0x0029, code lost:
        r3 = r3.e();
     */
    /* JADX WARNING: Removed duplicated region for block: B:19:0x0039  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public bam(defpackage.bal r5) {
        /*
            r4 = this;
            r4.<init>()
            jlv r0 = r5.b
            java.util.concurrent.Executor r1 = r5.a
            r2 = 0
            if (r1 != 0) goto L_0x003e
            if (r0 == 0) goto L_0x0036
            gon r1 = defpackage.jls.b
            jlt r1 = r0.get(r1)
            jls r1 = (defpackage.jls) r1
            boolean r3 = r1 instanceof defpackage.jqp
            if (r3 == 0) goto L_0x001b
            jqp r1 = (defpackage.jqp) r1
            goto L_0x001c
        L_0x001b:
            r1 = r2
        L_0x001c:
            if (r1 == 0) goto L_0x0036
            boolean r3 = r1 instanceof defpackage.jrr
            if (r3 == 0) goto L_0x0026
            r3 = r1
            jrr r3 = (defpackage.jrr) r3
            goto L_0x0027
        L_0x0026:
            r3 = r2
        L_0x0027:
            if (r3 == 0) goto L_0x002f
            java.util.concurrent.Executor r3 = r3.e()
            if (r3 != 0) goto L_0x0034
        L_0x002f:
            jre r3 = new jre
            r3.<init>(r1)
        L_0x0034:
            r1 = r3
            goto L_0x0037
        L_0x0036:
            r1 = r2
        L_0x0037:
            if (r1 != 0) goto L_0x003e
            r1 = 0
            java.util.concurrent.Executor r1 = androidx.wear.ambient.AmbientMode.AmbientCallback.a(r1)
        L_0x003e:
            r4.a = r1
            if (r0 != 0) goto L_0x004d
            java.util.concurrent.Executor r0 = r5.a
            if (r0 == 0) goto L_0x004b
            jqp r0 = defpackage.jnu.I(r1)
            goto L_0x004d
        L_0x004b:
            jqp r0 = defpackage.jrf.a
        L_0x004d:
            r4.b = r0
            java.util.concurrent.Executor r0 = r5.d
            r1 = 1
            if (r0 != 0) goto L_0x0058
            java.util.concurrent.Executor r0 = androidx.wear.ambient.AmbientMode.AmbientCallback.a(r1)
        L_0x0058:
            r4.c = r0
            androidx.wear.ambient.AmbientModeSupport$AmbientCallback r0 = new androidx.wear.ambient.AmbientModeSupport$AmbientCallback
            r0.<init>(r2, r2)
            r4.q = r0
            bcb r0 = r5.c
            if (r0 != 0) goto L_0x0067
            bav r0 = defpackage.bav.a
        L_0x0067:
            r4.d = r0
            bbm r0 = defpackage.bbm.b
            r4.o = r0
            bbv r0 = r5.e
            if (r0 != 0) goto L_0x0076
            gas r0 = new gas
            r0.<init>(r1)
        L_0x0076:
            r4.e = r0
            int r0 = r5.j
            r4.j = r0
            r0 = 2147483647(0x7fffffff, float:NaN)
            r4.k = r0
            int r0 = r5.k
            r4.m = r0
            um r0 = r5.f
            r4.f = r0
            um r0 = r5.g
            r4.g = r0
            um r0 = r5.h
            r4.h = r0
            java.lang.String r0 = r5.i
            r4.i = r0
            r0 = 8
            r4.l = r0
            boolean r5 = r5.l
            r4.n = r5
            androidx.wear.ambient.AmbientLifecycleObserverKt r5 = new androidx.wear.ambient.AmbientLifecycleObserverKt
            r5.<init>()
            r4.p = r5
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bam.<init>(bal):void");
    }
}
