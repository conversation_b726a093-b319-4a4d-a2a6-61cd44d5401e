package j$.time.temporal;

import j$.time.chrono.C0010i;
import j$.time.chrono.u;

/* 'enum' modifier removed */
final class b extends f {
    b() {
        super("DAY_OF_QUARTER", 0);
    }

    public final long g(m mVar) {
        int i;
        if (p(mVar)) {
            int p = mVar.p(a.DAY_OF_YEAR);
            int p2 = mVar.p(a.MONTH_OF_YEAR);
            long B = mVar.B(a.YEAR);
            int[] F = f.a;
            int i2 = (p2 - 1) / 3;
            if (u.d.D(B)) {
                i = 4;
            } else {
                i = 0;
            }
            return (long) (p - F[i2 + i]);
        }
        throw new RuntimeException("Unsupported field: DayOfQuarter");
    }

    public final t m() {
        return t.k(1, 90, 92);
    }

    public final boolean p(m mVar) {
        if (mVar.e(a.DAY_OF_YEAR) && mVar.e(a.MONTH_OF_YEAR) && mVar.e(a.YEAR)) {
            p pVar = h.a;
            if (C0010i.p(mVar).equals(u.d)) {
                return true;
            }
        }
        return false;
    }

    public final k s(k kVar, long j) {
        long g = g(kVar);
        m().b(j, this);
        a aVar = a.DAY_OF_YEAR;
        return kVar.d((j - g) + kVar.B(aVar), aVar);
    }

    public final t t(m mVar) {
        if (p(mVar)) {
            long B = mVar.B(f.QUARTER_OF_YEAR);
            if (B == 1) {
                if (u.d.D(mVar.B(a.YEAR))) {
                    return t.j(1, 91);
                }
                return t.j(1, 90);
            } else if (B == 2) {
                return t.j(1, 91);
            } else {
                if (B == 3 || B == 4) {
                    return t.j(1, 92);
                }
                return m();
            }
        } else {
            throw new RuntimeException("Unsupported field: DayOfQuarter");
        }
    }

    public final String toString() {
        return "DayOfQuarter";
    }
}
