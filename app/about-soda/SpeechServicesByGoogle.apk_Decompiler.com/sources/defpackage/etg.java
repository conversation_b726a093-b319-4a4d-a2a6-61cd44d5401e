package defpackage;

/* renamed from: etg  reason: default package */
/* compiled from: PG */
public final class etg extends htq implements hvb {
    public static final etg c;
    private static volatile hvh d;
    public int a = 0;
    public Object b;

    static {
        etg etg = new etg();
        c = etg;
        htq.z(etg.class, etg);
    }

    private etg() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(c, "\u0004\u0003\u0001\u0000\u0001\u0003\u0003\u0000\u0000\u0000\u0001<\u0000\u0002<\u0000\u0003<\u0000", new Object[]{"b", "a", htc.class, ebw.class, dzm.class});
        } else if (i2 == 3) {
            return new etg();
        } else {
            if (i2 == 4) {
                return new htk((htq) c);
            }
            if (i2 == 5) {
                return c;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = d;
            if (hvh == null) {
                synchronized (etg.class) {
                    hvh = d;
                    if (hvh == null) {
                        hvh = new htl(c);
                        d = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
