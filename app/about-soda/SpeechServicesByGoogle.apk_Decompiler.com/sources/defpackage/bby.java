package defpackage;

import androidx.wear.ambient.SharedLibraryVersion;
import java.util.Set;
import java.util.UUID;

/* renamed from: bby  reason: default package */
/* compiled from: PG */
public final class bby {
    public final UUID a;
    public final bbx b;
    public final Set c;
    public final baq d;
    private final bat e;
    private final bat f;
    private final int g;
    private final int h;
    private final long i;
    private final bbw j;
    private final long k;
    private final int l;

    public bby(UUID uuid, bbx bbx, Set set, bat bat, bat bat2, int i2, int i3, baq baq, long j2, bbw bbw, long j3, int i4) {
        jnu.e(uuid, "id");
        jnu.e(bbx, "state");
        jnu.e(bat, "outputData");
        jnu.e(bat2, "progress");
        this.a = uuid;
        this.b = bbx;
        this.c = set;
        this.e = bat;
        this.f = bat2;
        this.g = i2;
        this.h = i3;
        this.d = baq;
        this.i = j2;
        this.j = bbw;
        this.k = j3;
        this.l = i4;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || !jnu.i(getClass(), obj.getClass())) {
            return false;
        }
        bby bby = (bby) obj;
        if (this.g == bby.g && this.h == bby.h && jnu.i(this.a, bby.a) && this.b == bby.b && jnu.i(this.e, bby.e) && jnu.i(this.d, bby.d) && this.i == bby.i && jnu.i(this.j, bby.j) && this.k == bby.k && this.l == bby.l && jnu.i(this.c, bby.c)) {
            return jnu.i(this.f, bby.f);
        }
        return false;
    }

    public final int hashCode() {
        int i2;
        int hashCode = (((((((((((((this.a.hashCode() * 31) + this.b.hashCode()) * 31) + this.e.hashCode()) * 31) + this.c.hashCode()) * 31) + this.f.hashCode()) * 31) + this.g) * 31) + this.h) * 31) + this.d.hashCode();
        bbw bbw = this.j;
        if (bbw != null) {
            i2 = bbw.hashCode();
        } else {
            i2 = 0;
        }
        return (((((((hashCode * 31) + SharedLibraryVersion.b(this.i)) * 31) + i2) * 31) + SharedLibraryVersion.b(this.k)) * 31) + this.l;
    }

    public final String toString() {
        return "WorkInfo{id='" + this.a + "', state=" + this.b + ", outputData=" + this.e + ", tags=" + this.c + ", progress=" + this.f + ", runAttemptCount=" + this.g + ", generation=" + this.h + ", constraints=" + this.d + ", initialDelayMillis=" + this.i + ", periodicityInfo=" + this.j + ", nextScheduleTimeMillis=" + this.k + "}, stopReason=" + this.l;
    }
}
