package defpackage;

import android.view.View;
import android.view.ViewGroup;
import com.android.car.ui.recyclerview.DefaultScrollBar;

/* renamed from: bmw  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bmw implements View.OnFocusChangeListener {
    public final /* synthetic */ ViewGroup a;
    private final /* synthetic */ int b;

    public /* synthetic */ bmw(ViewGroup viewGroup, int i) {
        this.b = i;
        this.a = viewGroup;
    }

    public final void onFocusChange(View view, boolean z) {
        if (this.b != 0) {
            DefaultScrollBar defaultScrollBar = ((blp) this.a).c;
            if (defaultScrollBar != null) {
                defaultScrollBar.setHighlightThumb(z);
            }
        } else if (!z) {
            ViewGroup viewGroup = this.a;
            ((bna) viewGroup).h.hideSoftInputFromWindow(view.getWindowToken(), 0);
        }
    }
}
