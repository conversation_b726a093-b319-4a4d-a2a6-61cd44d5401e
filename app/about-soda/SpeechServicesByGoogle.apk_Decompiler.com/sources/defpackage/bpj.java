package defpackage;

import android.os.Parcel;
import android.os.ParcelFileDescriptor;
import android.os.Parcelable;

/* renamed from: bpj  reason: default package */
/* compiled from: PG */
public final class bpj extends cgf implements Parcelable {
    public static final Parcelable.Creator CREATOR = new aqu(18);
    public final byte[] a;
    public final String b;
    public final int c;
    public final ParcelFileDescriptor d;

    public bpj(byte[] bArr, String str, int i, ParcelFileDescriptor parcelFileDescriptor) {
        this.a = bArr;
        this.b = str;
        this.c = i;
        this.d = parcelFileDescriptor;
    }

    public final void writeToParcel(Parcel parcel, int i) {
        byte[] bArr = this.a;
        int i2 = cgr.i(parcel);
        cgr.r(parcel, 1, bArr);
        cgr.y(parcel, 2, this.b);
        cgr.o(parcel, 3, this.c);
        cgr.x(parcel, 4, this.d, i);
        cgr.k(parcel, i2);
    }
}
