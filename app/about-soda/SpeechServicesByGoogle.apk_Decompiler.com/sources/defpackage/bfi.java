package defpackage;

/* renamed from: bfi  reason: default package */
/* compiled from: PG */
public final class bfi extends bfd {
    static {
        bbk.b("NetworkMeteredCtrlr");
    }

    public bfi(bft bft) {
        super(bft);
    }

    public final boolean b(bhe bhe) {
        jnu.e(bhe, "workSpec");
        if (bhe.k.b == bbl.METERED) {
            return true;
        }
        return false;
    }

    public final int d() {
        return 7;
    }

    public final /* bridge */ /* synthetic */ boolean e(Object obj) {
        bex bex = (bex) obj;
        jnu.e(bex, "value");
        if (!bex.a || !bex.c) {
            return true;
        }
        return false;
    }
}
