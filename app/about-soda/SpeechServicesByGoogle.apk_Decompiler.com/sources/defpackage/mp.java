package defpackage;

import android.content.Intent;
import android.graphics.Typeface;
import android.os.Parcel;
import android.text.Layout;
import android.view.View;
import android.widget.ScrollView;
import android.widget.TextView;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.tts.R;
import j$.util.Objects;
import java.util.Iterator;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.logging.Level;

/* renamed from: mp  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class mp implements Runnable {
    public final /* synthetic */ int a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    private final /* synthetic */ int d;

    public mp(TextView textView, Typeface typeface, int i, int i2) {
        this.d = i2;
        this.b = textView;
        this.c = typeface;
        this.a = i;
    }

    /* JADX WARNING: type inference failed for: r1v3, types: [java.lang.Object, nl] */
    /* JADX WARNING: type inference failed for: r1v8, types: [java.lang.Object, java.io.Serializable] */
    /* JADX WARNING: type inference failed for: r2v8, types: [java.lang.Object, afs] */
    public final void run() {
        Object obj = null;
        switch (this.d) {
            case 0:
                nq nqVar = (nq) this.c;
                String str = (String) nqVar.a.get(Integer.valueOf(this.a));
                if (str != null) {
                    bvj bvj = (bvj) nqVar.e.get(str);
                    if (bvj != null) {
                        obj = bvj.b;
                    }
                    Object obj2 = ((byw) this.b).a;
                    if (obj != null) {
                        ? r1 = bvj.b;
                        jnu.c(r1, "null cannot be cast to non-null type androidx.activity.result.ActivityResultCallback<O of androidx.activity.result.ActivityResultRegistry.dispatchResult>");
                        if (nqVar.d.remove(str)) {
                            r1.a(obj2);
                            return;
                        }
                        return;
                    }
                    nqVar.g.remove(str);
                    nqVar.f.put(str, obj2);
                    return;
                }
                return;
            case 1:
                ic.f((TextView) this.b, (Typeface) this.c, this.a);
                return;
            case 2:
                Intent putExtra = new Intent().setAction("androidx.activity.result.contract.action.INTENT_SENDER_REQUEST").putExtra("androidx.activity.result.contract.extra.SEND_INTENT_EXCEPTION", this.c);
                ((nq) this.b).e(this.a, 0, putExtra);
                return;
            case 3:
                Iterator it = ((CopyOnWriteArraySet) this.b).iterator();
                while (it.hasNext()) {
                    afu afu = (afu) it.next();
                    if (!afu.d) {
                        int i = this.a;
                        if (i != -1) {
                            afu.b.b(i);
                        }
                        ? r2 = this.c;
                        afu.c = true;
                        r2.a(afu.a);
                    }
                }
                return;
            case 4:
                Object obj3 = this.c;
                dzx dzx = dzx.DISCONNECT_REASON_REMOTE_COMMUNICATION_CHANNEL_SHUTDOWN;
                ((etm) this.b).c.K(this.a, ((ejk) obj3).b, dzx);
                return;
            case 5:
                ((etm) this.b).c.x(this.a, ((ejm) this.c).b, eam.REMOTE_COMMUNICATION_CHANNEL_SHUTDOWN);
                return;
            case 6:
                Layout layout = ((TextView) ((dp) this.b).findViewById(R.id.license_activity_textview)).getLayout();
                if (layout != null) {
                    ((ScrollView) this.c).scrollTo(0, layout.getLineTop(layout.getLineForOffset(this.a)));
                    return;
                }
                return;
            case 7:
                Object obj4 = this.b;
                fhz fhz = (fhz) obj4;
                fhz.d.a(this.a);
                fhz.c.d.remove(fhz.d);
                fhz.c.f.ifPresent(new eyx(obj4, this.c, 12));
                return;
            case 8:
                ((BottomSheetBehavior) this.b).N((View) this.c, this.a, false);
                return;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER:
                hlw hlw = (hlw) this.b;
                hme[] hmeArr = hlw.d;
                int i2 = this.a;
                hme hme = (hme) Objects.requireNonNull(hmeArr[i2]);
                hlw.d[i2] = null;
                int i3 = hlw.e;
                while (true) {
                    Object obj5 = this.c;
                    int i4 = ((hal) obj5).c;
                    if (i3 < i4) {
                        int i5 = i3 + 1;
                        if (!((hka) ((gxq) obj5).get(i3)).o(hme)) {
                            i3 = i5;
                        } else {
                            hlw.a();
                            hlw.e = i5;
                            return;
                        }
                    } else {
                        hlw.e = i4;
                        return;
                    }
                }
            default:
                Object obj6 = this.c;
                try {
                    if (!((ive) this.b).c(this.a, (Parcel) obj6)) {
                        ive.a.logp(Level.FINEST, "io.grpc.binder.internal.OneWayBinderProxy$InProcessImpl", "transact", "A oneway transaction was not understood - ignoring");
                        return;
                    }
                    return;
                } catch (Exception e) {
                    ive.a.logp(Level.FINEST, "io.grpc.binder.internal.OneWayBinderProxy$InProcessImpl", "transact", "A oneway transaction threw - ignoring", e);
                    return;
                }
        }
    }

    public mp(BottomSheetBehavior bottomSheetBehavior, View view, int i, int i2) {
        this.d = i2;
        this.c = view;
        this.a = i;
        this.b = bottomSheetBehavior;
    }

    public /* synthetic */ mp(hlw hlw, gxq gxq, int i, int i2) {
        this.d = i2;
        this.b = hlw;
        this.c = gxq;
        this.a = i;
    }

    public /* synthetic */ mp(Object obj, int i, Object obj2, int i2) {
        this.d = i2;
        this.b = obj;
        this.a = i;
        this.c = obj2;
    }

    public /* synthetic */ mp(nq nqVar, int i, byw byw, int i2) {
        this.d = i2;
        this.c = nqVar;
        this.a = i;
        this.b = byw;
    }
}
