package defpackage;

/* renamed from: eki  reason: default package */
/* compiled from: PG */
public final class eki {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/core/common/ResultUtils");

    public static dzi a(eag eag, eam eam) {
        return b(d(eag), eam);
    }

    public static dzi b(eah eah, eam eam) {
        htk l = dzi.d.l();
        if (!l.b.B()) {
            l.u();
        }
        htq htq = l.b;
        dzi dzi = (dzi) htq;
        eah.getClass();
        dzi.b = eah;
        dzi.a |= 1;
        if (!htq.B()) {
            l.u();
        }
        dzi dzi2 = (dzi) l.b;
        dzi2.c = eam.s;
        dzi2.a |= 2;
        return (dzi) l.r();
    }

    public static dzo c(eaf eaf) {
        htk l = dzo.c.l();
        if (!l.b.B()) {
            l.u();
        }
        dzo dzo = (dzo) l.b;
        dzo.b = eaf.z;
        dzo.a |= 1;
        return (dzo) l.r();
    }

    public static eah d(eag eag) {
        htk l = eah.c.l();
        if (!l.b.B()) {
            l.u();
        }
        eah eah = (eah) l.b;
        eah.b = Integer.valueOf(eag.L);
        eah.a = 2;
        return (eah) l.r();
    }

    public static eah e(eai eai) {
        htk l = eah.c.l();
        if (!l.b.B()) {
            l.u();
        }
        eah eah = (eah) l.b;
        eah.b = Integer.valueOf(eai.d);
        eah.a = 1;
        return (eah) l.r();
    }

    public static eak f(eaj eaj) {
        htk l = eak.c.l();
        if (!l.b.B()) {
            l.u();
        }
        eak eak = (eak) l.b;
        eak.b = Integer.valueOf(eaj.ag);
        eak.a = 2;
        return (eak) l.r();
    }

    public static eam g(dzx dzx) {
        int ordinal = dzx.ordinal();
        if (ordinal == 32) {
            return eam.REMOTE_COMMUNICATION_CHANNEL_SHUTDOWN;
        }
        switch (ordinal) {
            case 1:
            case 8:
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
            case 15:
            case 21:
            case 22:
            case 23:
            case 27:
            case 28:
            case 29:
                return eam.FAILED_TO_CONNECT_TO_AUDIO_ROUTE;
            case 2:
            case 6:
            case 16:
            case 18:
            case 19:
            case 20:
            case 25:
                return eam.AUDIO_ROUTE_LOST;
            case 3:
                return eam.AUDIO_ROUTE_DISCONNECT_CLIENT_REQUESTED;
            case 4:
            case 24:
                return eam.MISSING_AUDIO_ROUTE_IMPLEMENTATION;
            case 5:
                return eam.CLIENT_DEACTIVATED;
            case 7:
                return eam.AUDIO_ADAPTER_NOT_REGISTERED;
            case 17:
                return eam.UNEXPECTED_AUDIO_SOURCE_OPENED;
            case 26:
                return eam.NEW_CLIENT_ACTIVATING;
            default:
                return eam.UNKNOWN_AUDIO_ROUTE_DISCONNECT_STATUS;
        }
    }

    public static ebk h(eag eag, eam eam) {
        return new ekb(d(eag), eam);
    }

    public static ebp i(eag eag, eam eam) {
        return j(d(eag), eam);
    }

    public static ebp j(eah eah, eam eam) {
        htk l = ebp.d.l();
        if (!l.b.B()) {
            l.u();
        }
        htq htq = l.b;
        ebp ebp = (ebp) htq;
        eah.getClass();
        ebp.b = eah;
        ebp.a |= 1;
        if (!htq.B()) {
            l.u();
        }
        ebp ebp2 = (ebp) l.b;
        ebp2.c = eam.s;
        ebp2.a |= 2;
        return (ebp) l.r();
    }

    public static hme k(eaf eaf) {
        return hfc.K(c(eaf));
    }

    public static ekf l(eag eag, ebr ebr, eam eam) {
        return m(d(eag), ebr, eam);
    }

    public static ekf m(eah eah, ebr ebr, eam eam) {
        htk l = ebp.d.l();
        if (!l.b.B()) {
            l.u();
        }
        htq htq = l.b;
        ebp ebp = (ebp) htq;
        eah.getClass();
        ebp.b = eah;
        ebp.a |= 1;
        if (!htq.B()) {
            l.u();
        }
        ebp ebp2 = (ebp) l.b;
        ebp2.c = eam.s;
        ebp2.a |= 2;
        return new ekf((Object) l, (Object) ebr);
    }

    public static dyk n(int i) {
        htk l = dyk.c.l();
        if (!l.b.B()) {
            l.u();
        }
        dyk dyk = (dyk) l.b;
        dyk.b = i - 1;
        dyk.a |= 1;
        return (dyk) l.r();
    }

    public static dyn o(int i) {
        htk l = dyn.d.l();
        if (!l.b.B()) {
            l.u();
        }
        dyn dyn = (dyn) l.b;
        dyn.b = i - 1;
        dyn.a |= 1;
        return (dyn) l.r();
    }

    public static dyn p(int i, dyl dyl) {
        htk l = dyn.d.l();
        if (!l.b.B()) {
            l.u();
        }
        htq htq = l.b;
        dyn dyn = (dyn) htq;
        dyn.b = i - 1;
        dyn.a |= 1;
        if (!htq.B()) {
            l.u();
        }
        dyn dyn2 = (dyn) l.b;
        dyn2.c = dyl.e;
        dyn2.a |= 2;
        return (dyn) l.r();
    }
}
