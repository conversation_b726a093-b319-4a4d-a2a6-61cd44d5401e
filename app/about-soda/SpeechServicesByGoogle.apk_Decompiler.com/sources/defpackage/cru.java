package defpackage;

/* renamed from: cru  reason: default package */
/* compiled from: PG */
public final class cru extends ftc {
    public final Object a = new Object();
    public crt b;
    public boolean c = false;
    public final dpp d;

    public cru(dpp dpp) {
        super((short[]) null);
        this.d = dpp;
        if (!ftd.p("application/grpc")) {
            dpp.k = "application/grpc";
        }
    }

    public final ipb bb() {
        synchronized (this.a) {
            if (this.b != null) {
                ipb ipb = new ipb();
                return ipb;
            }
            crt crt = new crt(this.d);
            this.b = crt;
            return crt;
        }
    }
}
