package defpackage;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

/* renamed from: coh  reason: default package */
/* compiled from: PG */
final class coh extends hlq implements hmg {
    private final long a;

    public coh(hme hme, long j) {
        super(hme);
        this.a = j;
    }

    public final /* bridge */ /* synthetic */ int compareTo(Object obj) {
        Delayed delayed = (Delayed) obj;
        if (delayed == this) {
            return 0;
        }
        return Long.compare(getDelay(TimeUnit.NANOSECONDS), delayed.getDelay(TimeUnit.NANOSECONDS));
    }

    public final long getDelay(TimeUnit timeUnit) {
        return timeUnit.convert(this.a - System.nanoTime(), TimeUnit.NANOSECONDS);
    }
}
