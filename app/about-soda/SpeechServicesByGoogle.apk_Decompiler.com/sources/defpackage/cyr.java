package defpackage;

import android.content.Context;
import android.net.Uri;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.atomic.AtomicLong;

/* renamed from: cyr  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cyr implements Callable {
    public final /* synthetic */ Map a;
    public final /* synthetic */ Map b;
    public final /* synthetic */ AtomicLong c;
    public final /* synthetic */ int d;
    public final /* synthetic */ eix e;

    public /* synthetic */ cyr(eix eix, Map map, Map map2, AtomicLong atomicLong, int i) {
        this.e = eix;
        this.a = map;
        this.b = map2;
        this.c = atomicLong;
        this.d = i;
    }

    /* JADX WARNING: type inference failed for: r3v6, types: [cuk, java.lang.Object] */
    public final Object call() {
        htk l = hiu.j.l();
        Map map = this.a;
        eix eix = this.e;
        for (String str : map.keySet()) {
            cys cys = (cys) map.get(str);
            List g = gry.c("|").g(str);
            htk l2 = hig.k.l();
            String str2 = (String) g.get(0);
            if (!l2.b.B()) {
                l2.u();
            }
            hig hig = (hig) l2.b;
            str2.getClass();
            hig.a |= 1;
            hig.b = str2;
            String str3 = (String) g.get(1);
            if (!l2.b.B()) {
                l2.u();
            }
            htq htq = l2.b;
            hig hig2 = (hig) htq;
            str3.getClass();
            hig2.a = 4 | hig2.a;
            hig2.d = str3;
            int i = cys.e;
            if (!htq.B()) {
                l2.u();
            }
            htq htq2 = l2.b;
            hig hig3 = (hig) htq2;
            hig3.a |= 8;
            hig3.e = i;
            int i2 = cys.f;
            if (!htq2.B()) {
                l2.u();
            }
            Map map2 = this.b;
            hig hig4 = (hig) l2.b;
            hig4.a |= 16;
            hig4.f = i2;
            csx csx = (csx) map2.get(str);
            if (csx == null) {
                if (!l2.b.B()) {
                    l2.u();
                }
                hig hig5 = (hig) l2.b;
                hig5.a |= 2;
                hig5.c = -1;
            } else {
                int i3 = csx.e;
                if (!l2.b.B()) {
                    l2.u();
                }
                htq htq3 = l2.b;
                hig hig6 = (hig) htq3;
                hig6.a |= 2;
                hig6.c = i3;
                long j = csx.r;
                if (!htq3.B()) {
                    l2.u();
                }
                htq htq4 = l2.b;
                hig hig7 = (hig) htq4;
                hig7.a |= 64;
                hig7.h = j;
                String str4 = csx.s;
                if (!htq4.B()) {
                    l2.u();
                }
                hig hig8 = (hig) l2.b;
                str4.getClass();
                hig8.a |= 128;
                hig8.i = str4;
            }
            hig hig9 = (hig) l2.r();
            if (!l.b.B()) {
                l.u();
            }
            hiu hiu = (hiu) l.b;
            hig9.getClass();
            huf huf = hiu.b;
            if (!huf.c()) {
                hiu.b = htq.s(huf);
            }
            hiu.b.add(hig9);
            long j2 = cys.a;
            if (!l.b.B()) {
                l.u();
            }
            hiu hiu2 = (hiu) l.b;
            htz htz = hiu2.c;
            if (!htz.c()) {
                hiu2.c = htq.r(htz);
            }
            hiu2.c.e(j2);
            long j3 = cys.b;
            if (!l.b.B()) {
                l.u();
            }
            hiu hiu3 = (hiu) l.b;
            htz htz2 = hiu3.d;
            if (!htz2.c()) {
                hiu3.d = htq.r(htz2);
            }
            hiu3.d.e(j3);
            long j4 = cys.c;
            if (!l.b.B()) {
                l.u();
            }
            hiu hiu4 = (hiu) l.b;
            htz htz3 = hiu4.e;
            if (!htz3.c()) {
                hiu4.e = htq.r(htz3);
            }
            hiu4.e.e(j4);
            long j5 = cys.d;
            if (!l.b.B()) {
                l.u();
            }
            hiu hiu5 = (hiu) l.b;
            htz htz4 = hiu5.f;
            if (!htz4.c()) {
                hiu5.f = htq.r(htz4);
            }
            hiu5.f.e(j5);
        }
        long j6 = this.c.get();
        if (!l.b.B()) {
            l.u();
        }
        hiu hiu6 = (hiu) l.b;
        hiu6.a |= 1;
        hiu6.g = j6;
        long j7 = 0;
        try {
            Uri r = cqx.r((Context) eix.d, (grh) eix.a);
            if (((kjd) eix.b).j(r)) {
                j7 = ((Long) ((kjd) eix.b).e(r, new fok(4))).longValue();
            }
        } catch (IOException e2) {
            cyh.j(e2, "%s: Failed to call Mobstore to compute MDD Directory bytes used!", "StorageLogger");
            eix.f.a();
        }
        if (!l.b.B()) {
            l.u();
        }
        htq htq5 = l.b;
        hiu hiu7 = (hiu) htq5;
        hiu7.a |= 2;
        hiu7.h = j7;
        if (!htq5.B()) {
            l.u();
        }
        int i4 = this.d;
        hiu hiu8 = (hiu) l.b;
        hiu8.a |= 4;
        hiu8.i = i4;
        return (hiu) l.r();
    }
}
