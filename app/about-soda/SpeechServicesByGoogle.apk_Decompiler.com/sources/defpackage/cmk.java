package defpackage;

import android.content.BroadcastReceiver;
import android.database.sqlite.SQLiteDatabase;
import android.net.Uri;
import java.io.File;

/* renamed from: cmk  reason: default package */
/* compiled from: PG */
public final class cmk implements hls {
    final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public cmk(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r0v10, types: [java.lang.Object, csl] */
    public final void a(Throwable th) {
        switch (this.b) {
            case 0:
                ((hby) ((hby) cml.a.f()).j("com/google/android/libraries/assistant/soda/SodaAudioPusher$1", "onFailure", 127, "SodaAudioPusher.java")).u("Failed with: %s", th.getMessage());
                ((cml) this.a).f.n(th);
                ((cml) this.a).a();
                return;
            case 1:
                synchronized (((bpy) this.a).f) {
                    ((bpy) this.a).g = null;
                }
                return;
            case 2:
                this.a.b(th);
                return;
            case 3:
                ctg ctg = ((czr) this.a).a.b;
                if (ctg == null) {
                    ctg = ctg.g;
                }
                cyh.j(th, "%s: Unable to increment LoggingStateStore network usage for %s", "NetworkUsageMonitor", ctg.b);
                return;
            case 4:
                ((hby) ((hby) ((hby) eec.a.h().h(hdg.a, "ALT.AFCSession")).i(th)).j("com/google/android/libraries/search/audio/audiofocus/impl/AudioFocusSessionImpl$1", "onFailure", 'x', "AudioFocusSessionImpl.java")).r("#audio# Failed to get ReleaseAudioFocusStatus future.");
                ((eec) this.a).g.c(eki.o(6));
                return;
            case 5:
                return;
            case 6:
                ((hby) ((hby) ((hby) emt.a.h()).i(th)).j("com/google/android/libraries/search/audio/microphone/accountability/RecordAudioOpChecker$1$1", "onFailure", 147, "RecordAudioOpChecker.java")).r("#audio# Failed getting StopListeningStatus future");
                etl etl = (etl) this.a;
                ((emt) etl.d).b((String) etl.b);
                return;
            case 7:
                ((hby) ((hby) ((hby) ens.a.g().h(hdg.a, "ALT.HWHandoverHandler")).i(th)).j("com/google/android/libraries/search/audio/microphone/impl/audiosourcedataaccessor/HotwordHandoverHandler$1", "onFailure", 'c', "HotwordHandoverHandler.java")).r("#audio# Failure in StopListeningStatus future.");
                ((ens) this.a).d.c(eki.i(eag.FAILED_CLOSING_ERROR_IN_GETTING_AUDIO_SOURCE_CLOSING_STATUS, eam.FAILED_START_LISTENING));
                return;
            case 8:
                ((eyc) this.a).d.a(dwv.ai.c(3));
                ((hdc) ((hdc) ((hdc) eyc.a.g()).i(th)).j("com/google/android/libraries/speech/modeldownload/languagepacks/backgroundtask/LanguagePackMaintenance$1", "onFailure", 80, "LanguagePackMaintenance.java")).r("Error when background downloading LanguagePacks");
                return;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                ((hby) ((hby) ((hby) eyy.a.h().h(hdg.a, "LanguagePackCBTrigger")).i(th)).j("com/google/android/libraries/speech/modeldownload/languagepacks/signals/DownloaderCallbacksTrigger$1", "onFailure", 124, "DownloaderCallbacksTrigger.java")).u("%s callback failed.", this.a);
                return;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                exo exo = (exo) this.a;
                ((hdc) ((hdc) ((hdc) ezl.a.g()).i(th)).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackPopulator$2", "onFailure", 216, "ZipfileLanguagePackPopulator.java")).C("Failed to add file group for '%s' from location: %s", ezl.c(exo), ((exn) exo.f.b()).a);
                return;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                ((hby) ((hby) ((hby) fds.a.h()).i(th)).j("com/google/android/libraries/speech/transcription/recognition/NetworkSpeechRecognizer$1", "onFailure", 190, "NetworkSpeechRecognizer.java")).r("Failed uploading data to S3");
                fvf.aP(((fds) this.a).f);
                if (fds.b(th, fck.class) != null) {
                    ((fds) this.a).f.b(th);
                    return;
                } else if (fds.b(th, elb.class) != null) {
                    ((fds) this.a).f.b(new fcc());
                    return;
                } else {
                    ((fds) this.a).f.b(new fcd());
                    return;
                }
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                ((hby) ((hby) ((hby) ffa.a.h()).i(th)).j("com/google/android/libraries/speech/transcription/recognition/audio/impl/AudioLibraryAudioController$1", "onFailure", 281, "AudioLibraryAudioController.java")).r("Could not get Audio Recording URI");
                if (!(th instanceof ekz)) {
                    Object obj = this.a;
                    ((ffa) obj).c.l(Uri.EMPTY);
                    return;
                }
                return;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                ((hby) ((hby) ((hby) ffa.a.g()).i(th)).j("com/google/android/libraries/speech/transcription/recognition/audio/impl/AudioLibraryAudioController$2", "onFailure", 317, "AudioLibraryAudioController.java")).r("Microphone didn't close!");
                return;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
            case 15:
                return;
            default:
                ((BroadcastReceiver.PendingResult) this.a).finish();
                return;
        }
    }

    /* JADX WARNING: type inference failed for: r0v3, types: [java.lang.Object, csl] */
    public final /* synthetic */ void b(Object obj) {
        eai eai;
        switch (this.b) {
            case 0:
                Void voidR = (Void) obj;
                ((cml) this.a).f.m((Object) null);
                ((cml) this.a).a();
                return;
            case 1:
                bpx bpx = (bpx) obj;
                return;
            case 2:
                crw crw = (crw) obj;
                try {
                    this.a.a(crw);
                    return;
                } catch (Exception e) {
                    cyh.o(e, "%s: Listener onComplete failed for group %s", "MobileDataDownload", crw.b);
                    return;
                }
            case 3:
                Void voidR2 = (Void) obj;
                ctg ctg = ((czr) this.a).a.b;
                if (ctg == null) {
                    ctg = ctg.g;
                }
                cyh.d("%s: Successfully incremented LoggingStateStore network usage for %s", "NetworkUsageMonitor", ctg.b);
                return;
            case 4:
                ((eec) this.a).g.c((dyn) obj);
                return;
            case 5:
                eah eah = (eah) obj;
                if (eah.a == 1) {
                    Object obj2 = this.a;
                    htk l = ebu.e.l();
                    if (eah.a == 1) {
                        eai = eai.b(((Integer) eah.b).intValue());
                        if (eai == null) {
                            eai = eai.UNKNOWN_CLOSING_SUCCESS;
                        }
                    } else {
                        eai = eai.UNKNOWN_CLOSING_SUCCESS;
                    }
                    if (!l.b.B()) {
                        l.u();
                    }
                    ebu ebu = (ebu) l.b;
                    ebu.c = Integer.valueOf(eai.d);
                    ebu.b = 3;
                    Object obj3 = this.a;
                    if (!l.b.B()) {
                        l.u();
                    }
                    int i = ((ell) obj3).c;
                    ebu ebu2 = (ebu) l.b;
                    elm elm = ((ell) obj2).b;
                    ebu2.d = i - 1;
                    ebu2.a |= 1;
                    ebu ebu3 = (ebu) l.r();
                    elm.b();
                    return;
                }
                return;
            case 6:
                dzi dzi = (dzi) obj;
                etl etl = (etl) this.a;
                ((hby) ((hby) emt.a.c()).j("com/google/android/libraries/search/audio/microphone/accountability/RecordAudioOpChecker$1$1", "onSuccess", 139, "RecordAudioOpChecker.java")).x("#audio# finishOp, session=%d, tag=%s", etl.a, etl.b);
                etl etl2 = (etl) this.a;
                ((emt) etl2.d).b((String) etl2.b);
                return;
            case 7:
                dzi dzi2 = (dzi) obj;
                po poVar = ((ens) this.a).d;
                eah eah2 = dzi2.b;
                if (eah2 == null) {
                    eah2 = eah.c;
                }
                eam b2 = eam.b(dzi2.c);
                if (b2 == null) {
                    b2 = eam.UNSET;
                }
                poVar.c(eki.j(eah2, b2));
                return;
            case 8:
                Void voidR3 = (Void) obj;
                ((hdc) ((hdc) eyc.a.f()).j("com/google/android/libraries/speech/modeldownload/languagepacks/backgroundtask/LanguagePackMaintenance$1", "onSuccess", 73, "LanguagePackMaintenance.java")).r("Background LanguagePack updates finished successfully");
                ((eyc) this.a).d.a(dwv.ai.c(1));
                return;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                Void voidR4 = (Void) obj;
                ((hby) ((hby) eyy.a.b().h(hdg.a, "LanguagePackCBTrigger")).j("com/google/android/libraries/speech/modeldownload/languagepacks/signals/DownloaderCallbacksTrigger$1", "onSuccess", 119, "DownloaderCallbacksTrigger.java")).u("%s callback done.", this.a);
                return;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                if (((Boolean) obj).booleanValue()) {
                    exo exo = (exo) this.a;
                    ((hdc) ((hdc) ezl.a.f()).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackPopulator$2", "onSuccess", 204, "ZipfileLanguagePackPopulator.java")).C("Updated file group for group '%s' from location: %s", ezl.c(exo), ((exn) exo.f.b()).a);
                    return;
                }
                exo exo2 = (exo) this.a;
                ((hdc) ((hdc) ezl.a.g()).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackPopulator$2", "onSuccess", 208, "ZipfileLanguagePackPopulator.java")).C("Failed to add file group for '%s' from location: %s", ezl.c(exo2), ((exn) exo2.f.b()).a);
                return;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                Void voidR5 = (Void) obj;
                ((hby) ((hby) fds.a.c()).j("com/google/android/libraries/speech/transcription/recognition/NetworkSpeechRecognizer$1", "onSuccess", 185, "NetworkSpeechRecognizer.java")).r("Finished uploading data to S3");
                return;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                ((ffa) this.a).c.l((Uri) obj);
                return;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                ((ffa) this.a).c.n();
                hby hby = (hby) ((hby) ffa.a.f()).j("com/google/android/libraries/speech/transcription/recognition/audio/impl/AudioLibraryAudioController$2", "onSuccess", 312, "AudioLibraryAudioController.java");
                eah eah3 = ((dzi) obj).b;
                if (eah3 == null) {
                    eah3 = eah.c;
                }
                hby.u("Mic closing result: %s", eah3);
                return;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                new File(((fqj) this.a).b.getDatabasePath((String) obj).getPath().concat(".bak")).delete();
                return;
            case 15:
                if (!new File(((SQLiteDatabase) obj).getPath()).exists()) {
                    synchronized (((fqj) this.a).h) {
                        ((fqj) this.a).d();
                    }
                    return;
                }
                return;
            default:
                ghj ghj = (ghj) obj;
                ((BroadcastReceiver.PendingResult) this.a).setResult(ghj.a, ghj.b, ghj.c);
                ((BroadcastReceiver.PendingResult) this.a).finish();
                return;
        }
    }
}
