package defpackage;

/* renamed from: evc  reason: default package */
/* compiled from: PG */
public final class evc {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/utils/AudioParamsOptimizationUtils");

    public static int a(int i) {
        try {
            return b(i);
        } catch (RuntimeException unused) {
            ((hby) ((hby) a.h().h(hdg.a, "ALT.OptimizationUtils")).j("com/google/android/libraries/search/audio/utils/AudioParamsOptimizationUtils", "getBytesPerSample", 110, "AudioParamsOptimizationUtils.java")).s("#audio# invalid encoding(%d), fallback to PCM_16BIT", i);
            return 2;
        }
    }

    public static int b(int i) {
        if (i == 2) {
            return 2;
        }
        if (i == 3) {
            return 1;
        }
        if (i == 4 || i == 22) {
            return 4;
        }
        throw new IllegalArgumentException();
    }

    public static int c(dyt dyt) {
        return a(dyt.e) * Integer.bitCount(dyt.d) * dyt.c;
    }

    public static int d(dyt dyt) {
        int a2 = a(dyt.e);
        return Long.valueOf(((long) Math.floor(((((double) c(dyt)) * 2.0E7d) / 1.0E9d) / ((double) a2))) * ((long) a2)).intValue();
    }
}
