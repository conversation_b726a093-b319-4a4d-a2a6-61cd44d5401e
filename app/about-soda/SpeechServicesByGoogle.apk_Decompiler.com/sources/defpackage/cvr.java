package defpackage;

/* renamed from: cvr  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvr implements hko {
    public final /* synthetic */ cvy a;
    public final /* synthetic */ csx b;
    public final /* synthetic */ csv c;
    public final /* synthetic */ ctj d;
    public final /* synthetic */ int e;

    public /* synthetic */ cvr(cvy cvy, int i, csx csx, csv csv, ctj ctj) {
        this.a = cvy;
        this.e = i;
        this.b = csx;
        this.c = csv;
        this.d = ctj;
    }

    public final hme a(Object obj) {
        if (((Boolean) obj).booleanValue() || this.e == 4) {
            return hma.a;
        }
        ctj ctj = this.d;
        csv csv = this.c;
        csx csx = this.b;
        return this.a.r(csx, csv, ctj, csx.k);
    }
}
