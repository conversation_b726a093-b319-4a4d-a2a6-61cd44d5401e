package defpackage;

import androidx.wear.ambient.SharedLibraryVersion;

/* renamed from: bho  reason: default package */
/* compiled from: PG */
final class bho extends aub {
    public bho(aus aus) {
        super(aus);
    }

    /* access modifiers changed from: protected */
    public final String a() {
        return "INSERT OR IGNORE INTO `WorkSpec` (`id`,`state`,`worker_class_name`,`input_merger_class_name`,`input`,`output`,`initial_delay`,`interval_duration`,`flex_duration`,`run_attempt_count`,`backoff_policy`,`backoff_delay_duration`,`last_enqueue_time`,`minimum_retention_duration`,`schedule_requested_at`,`run_in_foreground`,`out_of_quota_policy`,`period_count`,`generation`,`next_schedule_time_override`,`next_schedule_time_override_generation`,`stop_reason`,`trace_tag`,`required_network_type`,`required_network_request`,`requires_charging`,`requires_device_idle`,`requires_battery_not_low`,`requires_storage_not_low`,`trigger_content_update_delay`,`trigger_max_content_delay`,`content_uri_triggers`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
    }

    /* access modifiers changed from: protected */
    public final /* synthetic */ void c(axc axc, Object obj) {
        bhe bhe = (bhe) obj;
        axc.g(1, bhe.b);
        axc.e(2, (long) xm.m(bhe.c));
        axc.g(3, bhe.d);
        axc.g(4, bhe.e);
        axc.c(5, SharedLibraryVersion.a(bhe.f));
        axc.c(6, SharedLibraryVersion.a(bhe.g));
        axc.e(7, bhe.h);
        axc.e(8, bhe.i);
        axc.e(9, bhe.j);
        axc.e(10, (long) bhe.l);
        axc.e(11, (long) xm.j(bhe.m));
        axc.e(12, bhe.n);
        axc.e(13, bhe.o);
        axc.e(14, bhe.p);
        axc.e(15, bhe.q);
        axc.e(16, bhe.r ? 1 : 0);
        axc.e(17, (long) xm.l(bhe.s));
        axc.e(18, (long) bhe.t);
        axc.e(19, (long) bhe.u);
        axc.e(20, bhe.v);
        axc.e(21, (long) bhe.w);
        axc.e(22, (long) bhe.x);
        String str = bhe.y;
        if (str == null) {
            axc.f(23);
        } else {
            axc.g(23, str);
        }
        baq baq = bhe.k;
        axc.e(24, (long) xm.k(baq.b));
        axc.c(25, xm.t(baq.c));
        axc.e(26, baq.d ? 1 : 0);
        axc.e(27, baq.e ? 1 : 0);
        axc.e(28, baq.f ? 1 : 0);
        axc.e(29, baq.g ? 1 : 0);
        axc.e(30, baq.h);
        axc.e(31, baq.i);
        axc.c(32, xm.u(baq.j));
    }
}
