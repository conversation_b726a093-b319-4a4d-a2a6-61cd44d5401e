package defpackage;

/* renamed from: bpq  reason: default package */
/* compiled from: PG */
public final class bpq {
    public final String a;
    public final int b;
    public final int c;
    private final String d;
    private final int e;
    private final int f;

    public bpq() {
        throw null;
    }

    public final boy a() {
        return new boy(this.a, this.d, this.b, this.c, this.e, this.f);
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof bpq) {
            bpq bpq = (bpq) obj;
            if (this.a.equals(bpq.a) && this.d.equals(bpq.d) && this.b == bpq.b && this.c == bpq.c && this.e == bpq.e && this.f == bpq.f) {
                return true;
            }
            return false;
        }
        return false;
    }

    public final int hashCode() {
        return ((((((((((this.a.hashCode() ^ 1000003) * 1000003) ^ this.d.hashCode()) * 1000003) ^ this.b) * 1000003) ^ this.c) * 1000003) ^ this.e) * 1000003) ^ this.f;
    }

    public final String toString() {
        return "AiFeature{name=" + this.a + ", modelName=" + this.d + ", type=" + this.b + ", variant=" + this.c + ", id=" + this.e + ", version=" + this.f + "}";
    }

    public bpq(String str, String str2, int i, int i2, int i3, int i4) {
        if (str != null) {
            this.a = str;
            if (str2 != null) {
                this.d = str2;
                this.b = i;
                this.c = i2;
                this.e = i3;
                this.f = i4;
                return;
            }
            throw new NullPointerException("Null modelName");
        }
        throw new NullPointerException("Null name");
    }
}
