syntax = "proto2";

package greco;

import "speech/common/proto/recognition_context.proto";
import "speech/greco3/core/params.proto";
import "speech/greco3/decoder_endpointer/decoder_endpointer_params.proto";
import "speech/greco3/frontend/endpointer_event_params.proto";
import "speech/magicmic/streams/intended_query_request_info.proto";
import "speech/service/proto/alternates.proto";

option objc_class_prefix = "GRC";
option optimize_for = CODE_SIZE;
option java_outer_classname = "RecognizerSessionParamsProto";
option java_package = "com.google.speech.recognizer.api";

message RecognizerSessionParams {
    optional AudioType type = 1 [default = LINEAR16];
    enum AudioType {
        LINEAR16 = 0;
        FLOAT32 = 1;
        FLAC = 2;
        AMR = 3;
        MULAW = 4;
        SPEEX_NB = 5;
        SPEEX_WB = 6;
        OGG_VORBIS = 7;
        ADTS_AAC = 8;
        AMR_WB = 9;
        OGG_OPUS = 10;
        WEBM_OPUS = 11;
        MKV = 12;
        MP3 = 13;
        OPUS = 14;
    }
    
    optional float sample_rate = 2 [default = 8000];
    optional bool mask_offensive_words = 3 [default = true];
    optional bool enable_alternates = 4 [default = false];
    optional speech.common.AlternateParams alternate_params = 5;
    optional int32 num_nbest = 6;
    optional bool enable_partial_results = 7 [default = true];
    optional int32 reset_interval_ms = 8;
    optional float hotword_decision_threshold = 9;
    optional int32 channel_count = 10 [default = 1];
    optional bool enable_frame_logging = 11 [default = false];
    optional string force_transcript = 12;
    optional bool enable_partial_nbest = 13;
    optional bool enable_combined_nbest = 50 [default = true];
    optional bool enable_partial_confidence_scores = 21;
    optional bool enable_result_prefetch = 17;
    optional bool enable_decoder_events = 29;
    optional bool enable_speaker_training = 14 [default = false];
    optional string speaker_id = 15;
    optional string speaker_profiles_filename = 16;
    optional AudioType preamble_type = 18;
    optional float preamble_sample_rate = 19;
    optional int32 preamble_channel_count = 20 [default = 1];
    optional string myhotword_model_filepath = 22;
    optional float vt_warp_factor = 24 [default = 1];
    optional string surrounding_text_prefix = 25;
    optional string surrounding_text_suffix = 26;
    optional bool enable_debugging = 34;
    optional bool enable_tracing = 27;
    optional string tracing_inspector = 28;
    optional DecoderEndpointerParams decoder_endpointer_params = 30;
    optional EndpointerEventParams endpointer_event_params = 31;
    optional speech.common.RecognitionContext recognition_context = 32;
    optional bool enable_predicted_recognition_events = 33;
    optional float predicted_recognition_probability_threshold = 35 [default = 1];
    repeated string preamble_word = 36;
    optional bool decode_and_drop_preamble_audio = 37;
    optional bytes preamble_audio_bytes = 48;
    optional int32 hotword_start_time_ms = 38;
    optional bool enable_intended_query_result = 39;
    optional bool apply_preamble_based_dynamic_scale = 40;
    optional bool is_open_mic_query = 41;
    optional bool enable_automatic_punctuation = 42;
    optional bool enable_second_pass_rescoring = 43 [default = true];
    optional bool output_partial_alignments = 44;
    optional IntendedQueryRequestInfo intended_query_request_info = 47;
    optional string previous_transcript = 49;
    
    extend Params {
        optional RecognizerSessionParams id = 21619945;
    }
}
