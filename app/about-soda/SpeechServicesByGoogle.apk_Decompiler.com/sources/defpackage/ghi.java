package defpackage;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import j$.util.Objects;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/* renamed from: ghi  reason: default package */
/* compiled from: PG */
public class ghi extends BroadcastReceiver implements ghf {
    public static final hca a = hca.m("com/google/apps/tiktok/receiver/IntentFilterAcledReceiver");
    private final Class b;
    private long c;
    private hmi d;

    protected ghi(Class cls) {
        this.b = cls;
    }

    protected static hme f(Context context, Class cls) {
        try {
            return hfc.K(ftd.k(context, cls));
        } catch (IllegalStateException e) {
            return hfc.J(new IllegalStateException("Missing entry point. If you're in a test with explicit entry points specified in your @TestRoot, check that you're not missing the one for this class.", e));
        }
    }

    public static final void g(hme hme) {
        if (!hme.isCancelled()) {
            fnk.e(gof.h(new ghg(hme, 3)));
        }
    }

    private final hme h(Context context, Intent intent, gmn gmn) {
        gmj Q = ftd.Q("getEntryPoint");
        try {
            hme f = f(context, this.b);
            Q.a(f);
            Q.close();
            ghj ghj = new ghj(getResultCode(), getResultData(), getResultExtras(false));
            gmj R = ftd.R("handleBroadcast", gmn);
            try {
                hme g = hke.g(f, gof.d(new cwt((Object) this, (Object) intent, (Object) ghj, 15)), hld.a);
                R.a(g);
                R.close();
                if (g.isDone()) {
                    try {
                        ghj ghj2 = (ghj) a.h(g);
                        setResult(ghj2.a, ghj2.b, ghj2.c);
                    } catch (ExecutionException e) {
                        return hfc.J(e);
                    }
                } else {
                    hfc.T(g, gof.g(new cmk(goAsync(), 16)), hld.a);
                }
                return g;
            } catch (Throwable th) {
                th.addSuppressed(th);
            }
            throw th;
            throw th;
        } catch (Throwable th2) {
            th.addSuppressed(th2);
        }
    }

    private final hme i(Context context, Intent intent, gmn gmn) {
        gmj Q = ftd.Q("getEntryPoint");
        try {
            hme f = f(context, this.b);
            Q.a(f);
            Q.close();
            getResultCode();
            gmj R = ftd.R("handleBroadcast", gmn);
            try {
                hme g = hke.g(f, gof.d(new ezm(this, intent, 5)), hld.a);
                R.a(g);
                R.close();
                if (!g.isDone()) {
                    BroadcastReceiver.PendingResult goAsync = goAsync();
                    Objects.requireNonNull(goAsync);
                    g.c(gof.h(new ghg(goAsync, 4)), hld.a);
                }
                return g;
            } catch (Throwable th) {
                th.addSuppressed(th);
            }
            throw th;
            throw th;
        } catch (Throwable th2) {
            th.addSuppressed(th2);
        }
    }

    public ghk a(Object obj) {
        return null;
    }

    public final /* synthetic */ long b() {
        return -1;
    }

    public final /* synthetic */ long ba() {
        return -1;
    }

    /* access modifiers changed from: protected */
    public boolean c() {
        return false;
    }

    public fbb d(Object obj) {
        return null;
    }

    public final hme e(hme hme, Object obj, Intent intent) {
        long j;
        if (obj instanceof ghf) {
            ghf ghf = (ghf) obj;
            if ((intent.getFlags() & 268435456) != 0) {
                j = ghf.b();
            } else {
                j = ghf.ba();
            }
        } else {
            j = -1;
        }
        if (j <= 0) {
            return hme;
        }
        return ftd.G(hfc.R(hme, j - (cqx.H().toMillis() - this.c), TimeUnit.MILLISECONDS, this.d), TimeoutException.class, new gax(3), hld.a);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:59:0x0181, code lost:
        r0 = r11.getPackageManager();
        r6 = defpackage.ewv.a;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:60:0x0187, code lost:
        monitor-enter(r6);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:63:0x018a, code lost:
        if (defpackage.ewv.b != null) goto L_0x019e;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:64:0x018c, code lost:
        r7 = new android.content.Intent();
        r7.setPackage(r11.getPackageName());
        defpackage.ewv.b = r0.queryBroadcastReceivers(r7, 0);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:65:0x019e, code lost:
        r0 = defpackage.ewv.b;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:66:0x01a0, code lost:
        monitor-exit(r6);
     */
    /* JADX WARNING: Code restructure failed: missing block: B:68:?, code lost:
        r0 = r0.iterator();
     */
    /* JADX WARNING: Code restructure failed: missing block: B:70:0x01a9, code lost:
        if (r0.hasNext() == false) goto L_0x01c5;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:72:0x01b9, code lost:
        if (r3.equals(((android.content.pm.ResolveInfo) r0.next()).activityInfo.name) != false) goto L_0x01bc;
     */
    /* JADX WARNING: Code restructure failed: missing block: B:75:0x01c1, code lost:
        throw new defpackage.ewu(r12);
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void onReceive(android.content.Context r11, android.content.Intent r12) {
        /*
            r10 = this;
            java.lang.String r0 = "ResolveInfo did not match receiver name: "
            android.content.Context r1 = r11.getApplicationContext()
            java.lang.Class r1 = r1.getClass()
            java.lang.Class<android.app.Application> r2 = android.app.Application.class
            if (r1 != r2) goto L_0x004c
            android.content.pm.ApplicationInfo r1 = r11.getApplicationInfo()
            java.lang.String r1 = r1.className
            if (r1 == 0) goto L_0x004c
            java.lang.Class<android.app.Application> r2 = android.app.Application.class
            java.lang.String r2 = r2.getName()
            boolean r2 = r2.equals(r1)
            if (r2 == 0) goto L_0x0023
            goto L_0x004c
        L_0x0023:
            java.lang.Class r11 = r10.getClass()
            java.lang.String r11 = r11.getName()
            java.lang.StringBuilder r12 = new java.lang.StringBuilder
            java.lang.String r0 = "While attempting to launch "
            r12.<init>(r0)
            r12.append(r11)
            java.lang.String r11 = " the Application class "
            r12.append(r11)
            r12.append(r1)
            java.lang.String r11 = " was not created. This is known to happen with backup/restore operations. To avoid breaking an in-progress restore, this broadcast will be dropped and not processed."
            r12.append(r11)
            java.lang.String r11 = r12.toString()
            java.lang.String r12 = "BroadcastError"
            android.util.Log.e(r12, r11)
            return
        L_0x004c:
            java.lang.Class<ghh> r1 = defpackage.ghh.class
            java.lang.Object r1 = defpackage.ftd.k(r11, r1)
            ghh r1 = (defpackage.ghh) r1
            r1.H()
            j$.time.Duration r2 = defpackage.cqx.H()
            long r2 = r2.toMillis()
            r10.c = r2
            hmi r2 = r1.v()
            r10.d = r2
            r1.J()
            gmn r2 = defpackage.gmm.a
            r1.I()
            java.lang.String r1 = "foo"
            r12.hasExtra(r1)     // Catch:{ all -> 0x02dd }
            android.content.pm.PackageManager r1 = r11.getPackageManager()
            java.lang.Class r3 = r10.getClass()
            android.content.Intent r4 = new android.content.Intent
            r4.<init>(r11, r3)
            r3 = 786432(0xc0000, float:1.102026E-39)
            java.util.List r1 = r1.queryBroadcastReceivers(r4, r3)
            boolean r3 = r1.isEmpty()
            r4 = 0
            r5 = 0
            if (r3 == 0) goto L_0x0091
            r1 = r5
            goto L_0x0097
        L_0x0091:
            java.lang.Object r1 = r1.get(r4)
            android.content.pm.ResolveInfo r1 = (android.content.pm.ResolveInfo) r1
        L_0x0097:
            java.lang.Class r3 = r10.getClass()     // Catch:{ ewu -> 0x02ba }
            java.lang.String r3 = r3.getName()     // Catch:{ ewu -> 0x02ba }
            if (r1 == 0) goto L_0x00d2
            android.content.pm.ActivityInfo r6 = r1.activityInfo     // Catch:{ ewu -> 0x02ba }
            java.lang.String r6 = r6.name     // Catch:{ ewu -> 0x02ba }
            boolean r6 = r3.equals(r6)     // Catch:{ ewu -> 0x02ba }
            if (r6 == 0) goto L_0x00ac
            goto L_0x00d2
        L_0x00ac:
            java.lang.IllegalArgumentException r11 = new java.lang.IllegalArgumentException     // Catch:{ ewu -> 0x02ba }
            java.lang.Class r2 = r10.getClass()     // Catch:{ ewu -> 0x02ba }
            java.lang.String r2 = java.lang.String.valueOf(r2)     // Catch:{ ewu -> 0x02ba }
            android.content.pm.ActivityInfo r1 = r1.activityInfo     // Catch:{ ewu -> 0x02ba }
            java.lang.String r1 = r1.name     // Catch:{ ewu -> 0x02ba }
            java.lang.StringBuilder r3 = new java.lang.StringBuilder     // Catch:{ ewu -> 0x02ba }
            r3.<init>(r0)     // Catch:{ ewu -> 0x02ba }
            r3.append(r2)     // Catch:{ ewu -> 0x02ba }
            java.lang.String r0 = " != "
            r3.append(r0)     // Catch:{ ewu -> 0x02ba }
            r3.append(r1)     // Catch:{ ewu -> 0x02ba }
            java.lang.String r0 = r3.toString()     // Catch:{ ewu -> 0x02ba }
            r11.<init>(r0)     // Catch:{ ewu -> 0x02ba }
            throw r11     // Catch:{ ewu -> 0x02ba }
        L_0x00d2:
            if (r1 == 0) goto L_0x01c5
            android.content.pm.ActivityInfo r0 = r1.activityInfo     // Catch:{ ewu -> 0x02ba }
            boolean r0 = r0.exported     // Catch:{ ewu -> 0x02ba }
            if (r0 != 0) goto L_0x00dc
            goto L_0x01c5
        L_0x00dc:
            android.content.pm.PackageManager r0 = r11.getPackageManager()     // Catch:{ ewu -> 0x02ba }
            android.content.Intent r6 = r12.setComponent(r5)     // Catch:{ ewu -> 0x02ba }
            android.content.Intent r6 = r6.cloneFilter()     // Catch:{ ewu -> 0x02ba }
            r6.setSelector(r5)     // Catch:{ ewu -> 0x02ba }
            java.lang.String r7 = r11.getPackageName()     // Catch:{ ewu -> 0x02ba }
            r6.setPackage(r7)     // Catch:{ ewu -> 0x02ba }
            int r7 = android.os.Build.VERSION.SDK_INT     // Catch:{ ewu -> 0x02ba }
            r8 = 29
            if (r7 < r8) goto L_0x00fc
            r7 = 269221952(0x100c0040, float:2.7610324E-29)
            goto L_0x00ff
        L_0x00fc:
            r7 = 786496(0xc0040, float:1.102116E-39)
        L_0x00ff:
            java.util.List r0 = r0.queryBroadcastReceivers(r6, r7)     // Catch:{ ewu -> 0x02ba }
            java.util.Iterator r0 = r0.iterator()     // Catch:{ ewu -> 0x02ba }
        L_0x0107:
            boolean r6 = r0.hasNext()     // Catch:{ ewu -> 0x02ba }
            if (r6 == 0) goto L_0x0181
            java.lang.Object r6 = r0.next()     // Catch:{ ewu -> 0x02ba }
            android.content.pm.ResolveInfo r6 = (android.content.pm.ResolveInfo) r6     // Catch:{ ewu -> 0x02ba }
            android.content.pm.ActivityInfo r7 = r6.activityInfo     // Catch:{ ewu -> 0x02ba }
            java.lang.String r7 = r7.name     // Catch:{ ewu -> 0x02ba }
            boolean r7 = r3.equals(r7)     // Catch:{ ewu -> 0x02ba }
            if (r7 == 0) goto L_0x0107
            android.content.IntentFilter r0 = r6.filter     // Catch:{ ewu -> 0x02ba }
            defpackage.fvf.aP(r0)     // Catch:{ ewu -> 0x02ba }
            java.lang.String r3 = r12.getAction()     // Catch:{ ewu -> 0x02ba }
            boolean r3 = r0.matchAction(r3)     // Catch:{ ewu -> 0x02ba }
            if (r3 == 0) goto L_0x017b
            java.util.Set r3 = r12.getCategories()     // Catch:{ ewu -> 0x02ba }
            java.lang.String r3 = r0.matchCategories(r3)     // Catch:{ ewu -> 0x02ba }
            if (r3 != 0) goto L_0x0175
            java.lang.String r3 = r12.getType()     // Catch:{ ewu -> 0x02ba }
            java.lang.String r6 = r12.getScheme()     // Catch:{ ewu -> 0x02ba }
            android.net.Uri r7 = r12.getData()     // Catch:{ ewu -> 0x02ba }
            int r6 = r0.matchData(r3, r6, r7)     // Catch:{ ewu -> 0x02ba }
            r7 = -2
            if (r6 == r7) goto L_0x016f
            r8 = -1
            if (r6 != r8) goto L_0x01c5
            if (r3 != 0) goto L_0x0169
            java.lang.String r3 = r12.resolveType(r11)     // Catch:{ ewu -> 0x02ba }
            java.lang.String r6 = r12.getScheme()     // Catch:{ ewu -> 0x02ba }
            android.net.Uri r9 = r12.getData()     // Catch:{ ewu -> 0x02ba }
            int r0 = r0.matchData(r3, r6, r9)     // Catch:{ ewu -> 0x02ba }
            if (r0 == r8) goto L_0x0163
            if (r0 == r7) goto L_0x0163
            goto L_0x01c5
        L_0x0163:
            ewu r11 = new ewu     // Catch:{ ewu -> 0x02ba }
            r11.<init>(r12)     // Catch:{ ewu -> 0x02ba }
            throw r11     // Catch:{ ewu -> 0x02ba }
        L_0x0169:
            ewu r11 = new ewu     // Catch:{ ewu -> 0x02ba }
            r11.<init>(r12)     // Catch:{ ewu -> 0x02ba }
            throw r11     // Catch:{ ewu -> 0x02ba }
        L_0x016f:
            ewu r11 = new ewu     // Catch:{ ewu -> 0x02ba }
            r11.<init>(r12)     // Catch:{ ewu -> 0x02ba }
            throw r11     // Catch:{ ewu -> 0x02ba }
        L_0x0175:
            ewu r11 = new ewu     // Catch:{ ewu -> 0x02ba }
            r11.<init>(r12)     // Catch:{ ewu -> 0x02ba }
            throw r11     // Catch:{ ewu -> 0x02ba }
        L_0x017b:
            ewu r11 = new ewu     // Catch:{ ewu -> 0x02ba }
            r11.<init>(r12)     // Catch:{ ewu -> 0x02ba }
            throw r11     // Catch:{ ewu -> 0x02ba }
        L_0x0181:
            android.content.pm.PackageManager r0 = r11.getPackageManager()     // Catch:{ ewu -> 0x02ba }
            java.lang.Object r6 = defpackage.ewv.a     // Catch:{ ewu -> 0x02ba }
            monitor-enter(r6)     // Catch:{ ewu -> 0x02ba }
            java.util.List r7 = defpackage.ewv.b     // Catch:{ all -> 0x01c2 }
            if (r7 != 0) goto L_0x019e
            android.content.Intent r7 = new android.content.Intent     // Catch:{ all -> 0x01c2 }
            r7.<init>()     // Catch:{ all -> 0x01c2 }
            java.lang.String r8 = r11.getPackageName()     // Catch:{ all -> 0x01c2 }
            r7.setPackage(r8)     // Catch:{ all -> 0x01c2 }
            java.util.List r0 = r0.queryBroadcastReceivers(r7, r4)     // Catch:{ all -> 0x01c2 }
            defpackage.ewv.b = r0     // Catch:{ all -> 0x01c2 }
        L_0x019e:
            java.util.List r0 = defpackage.ewv.b     // Catch:{ all -> 0x01c2 }
            monitor-exit(r6)     // Catch:{ all -> 0x01c2 }
            java.util.Iterator r0 = r0.iterator()     // Catch:{ ewu -> 0x02ba }
        L_0x01a5:
            boolean r6 = r0.hasNext()     // Catch:{ ewu -> 0x02ba }
            if (r6 == 0) goto L_0x01c5
            java.lang.Object r6 = r0.next()     // Catch:{ ewu -> 0x02ba }
            android.content.pm.ResolveInfo r6 = (android.content.pm.ResolveInfo) r6     // Catch:{ ewu -> 0x02ba }
            android.content.pm.ActivityInfo r6 = r6.activityInfo     // Catch:{ ewu -> 0x02ba }
            java.lang.String r6 = r6.name     // Catch:{ ewu -> 0x02ba }
            boolean r6 = r3.equals(r6)     // Catch:{ ewu -> 0x02ba }
            if (r6 != 0) goto L_0x01bc
            goto L_0x01a5
        L_0x01bc:
            ewu r11 = new ewu     // Catch:{ ewu -> 0x02ba }
            r11.<init>(r12)     // Catch:{ ewu -> 0x02ba }
            throw r11     // Catch:{ ewu -> 0x02ba }
        L_0x01c2:
            r11 = move-exception
            monitor-exit(r6)     // Catch:{ all -> 0x01c2 }
            throw r11     // Catch:{ ewu -> 0x02ba }
        L_0x01c5:
            java.lang.String r0 = r12.getAction()
            if (r1 != 0) goto L_0x01ce
            java.lang.String r1 = "anonymous"
            goto L_0x01d6
        L_0x01ce:
            java.lang.Class r1 = r10.getClass()
            java.lang.String r1 = r1.getName()
        L_0x01d6:
            if (r0 == 0) goto L_0x01ef
            java.lang.StringBuilder r3 = new java.lang.StringBuilder
            java.lang.String r6 = "Broadcast to "
            r3.<init>(r6)
            r3.append(r1)
            java.lang.String r1 = " "
            r3.append(r1)
            r3.append(r0)
            java.lang.String r0 = r3.toString()
            goto L_0x01f9
        L_0x01ef:
            java.lang.String r0 = java.lang.String.valueOf(r1)
            java.lang.String r1 = "Broadcast to "
            java.lang.String r0 = r1.concat(r0)
        L_0x01f9:
            boolean r1 = defpackage.fnk.g()
            if (r1 == 0) goto L_0x0209
            boolean r1 = defpackage.gll.q()
            if (r1 != 0) goto L_0x0209
            gna r5 = defpackage.gll.e()
        L_0x0209:
            if (r5 == 0) goto L_0x026c
            defpackage.gll.u(r5)     // Catch:{ all -> 0x025d }
            gkn r1 = new gkn     // Catch:{ all -> 0x025d }
            r3 = 1
            r1.<init>(r3)     // Catch:{ all -> 0x025d }
            gml r3 = defpackage.gmn.b()     // Catch:{ all -> 0x025d }
            fvf r6 = defpackage.gmw.c     // Catch:{ all -> 0x025d }
            r3.a(r6, r1)     // Catch:{ all -> 0x025d }
            gmn r3 = (defpackage.gmn) r3     // Catch:{ all -> 0x025d }
            gmn r1 = r3.f()     // Catch:{ all -> 0x025d }
            goq r3 = defpackage.goq.a     // Catch:{ all -> 0x025d }
            gmj r0 = defpackage.ftd.T(r0, r3, r1)     // Catch:{ all -> 0x025d }
            boolean r1 = r10.isOrderedBroadcast()     // Catch:{ all -> 0x0253 }
            if (r1 == 0) goto L_0x023a
            boolean r1 = r10.c()     // Catch:{ all -> 0x0253 }
            if (r1 == 0) goto L_0x023a
            hme r11 = r10.h(r11, r12, r2)     // Catch:{ all -> 0x0253 }
            goto L_0x023e
        L_0x023a:
            hme r11 = r10.i(r11, r12, r2)     // Catch:{ all -> 0x0253 }
        L_0x023e:
            ghg r12 = new ghg     // Catch:{ all -> 0x0253 }
            r12.<init>(r11, r4)     // Catch:{ all -> 0x0253 }
            java.lang.Runnable r12 = defpackage.gof.h(r12)     // Catch:{ all -> 0x0253 }
            hld r1 = defpackage.hld.a     // Catch:{ all -> 0x0253 }
            r11.c(r12, r1)     // Catch:{ all -> 0x0253 }
            r0.close()     // Catch:{ all -> 0x025d }
            r5.close()
            return
        L_0x0253:
            r11 = move-exception
            r0.close()     // Catch:{ all -> 0x0258 }
            goto L_0x025c
        L_0x0258:
            r12 = move-exception
            r11.addSuppressed(r12)     // Catch:{ all -> 0x025d }
        L_0x025c:
            throw r11     // Catch:{ all -> 0x025d }
        L_0x025d:
            r11 = move-exception
            defpackage.gsg.b(r11)     // Catch:{ all -> 0x0267 }
            java.lang.RuntimeException r12 = new java.lang.RuntimeException     // Catch:{ all -> 0x0267 }
            r12.<init>(r11)     // Catch:{ all -> 0x0267 }
            throw r12     // Catch:{ all -> 0x0267 }
        L_0x0267:
            r11 = move-exception
            r5.close()
            throw r11
        L_0x026c:
            java.lang.Class<gno> r1 = defpackage.gno.class
            java.lang.Object r1 = defpackage.ftd.k(r11, r1)     // Catch:{ all -> 0x02b0 }
            gno r1 = (defpackage.gno) r1     // Catch:{ all -> 0x02b0 }
            gnk r1 = r1.b()     // Catch:{ all -> 0x02b0 }
            goq r3 = defpackage.goq.a     // Catch:{ all -> 0x02b0 }
            gmd r0 = r1.b(r0, r3)     // Catch:{ all -> 0x02b0 }
            boolean r1 = r10.isOrderedBroadcast()     // Catch:{ all -> 0x02a6 }
            if (r1 == 0) goto L_0x028f
            boolean r1 = r10.c()     // Catch:{ all -> 0x02a6 }
            if (r1 == 0) goto L_0x028f
            hme r11 = r10.h(r11, r12, r2)     // Catch:{ all -> 0x02a6 }
            goto L_0x0293
        L_0x028f:
            hme r11 = r10.i(r11, r12, r2)     // Catch:{ all -> 0x02a6 }
        L_0x0293:
            ghg r12 = new ghg     // Catch:{ all -> 0x02a6 }
            r1 = 2
            r12.<init>(r11, r1)     // Catch:{ all -> 0x02a6 }
            java.lang.Runnable r12 = defpackage.gof.h(r12)     // Catch:{ all -> 0x02a6 }
            hld r1 = defpackage.hld.a     // Catch:{ all -> 0x02a6 }
            r11.c(r12, r1)     // Catch:{ all -> 0x02a6 }
            r0.close()     // Catch:{ all -> 0x02b0 }
            return
        L_0x02a6:
            r11 = move-exception
            r0.close()     // Catch:{ all -> 0x02ab }
            goto L_0x02af
        L_0x02ab:
            r12 = move-exception
            r11.addSuppressed(r12)     // Catch:{ all -> 0x02b0 }
        L_0x02af:
            throw r11     // Catch:{ all -> 0x02b0 }
        L_0x02b0:
            r11 = move-exception
            defpackage.gsg.b(r11)
            java.lang.RuntimeException r12 = new java.lang.RuntimeException
            r12.<init>(r11)
            throw r12
        L_0x02ba:
            r11 = move-exception
            hca r0 = a
            hco r0 = r0.g()
            hby r0 = (defpackage.hby) r0
            hco r11 = r0.i(r11)
            hby r11 = (defpackage.hby) r11
            java.lang.String r0 = "IntentFilterAcledReceiver.java"
            java.lang.String r1 = "com/google/apps/tiktok/receiver/IntentFilterAcledReceiver"
            java.lang.String r2 = "onReceive"
            r3 = 134(0x86, float:1.88E-43)
            hco r11 = r11.j(r1, r2, r3, r0)
            hby r11 = (defpackage.hby) r11
            java.lang.String r0 = "Got unexpected intent: %s"
            r11.u(r0, r12)
            return
        L_0x02dd:
            r11 = move-exception
            r6 = r11
            hca r11 = a
            hco r0 = r11.g()
            java.lang.String r1 = "Got invalid intent"
            java.lang.String r5 = "IntentFilterAcledReceiver.java"
            java.lang.String r2 = "com/google/apps/tiktok/receiver/IntentFilterAcledReceiver"
            java.lang.String r3 = "onReceive"
            r4 = 118(0x76, float:1.65E-43)
            ((defpackage.hby) ((defpackage.hby) ((defpackage.hby) r0).i(r6)).j(r2, r3, r4, r5)).r(r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.ghi.onReceive(android.content.Context, android.content.Intent):void");
    }
}
