package defpackage;

/* renamed from: ekk  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ekk implements ecp {
    public final /* synthetic */ ecp a;
    public final /* synthetic */ ekp b;

    public /* synthetic */ ekk(ecp ecp, ekp ekp) {
        this.a = ecp;
        this.b = ekp;
    }

    public final /* synthetic */ int a(byte[] bArr, int i, int i2, int i3) {
        return doe.k(this, bArr, i, i2, i3);
    }

    public final int c(byte[] bArr, int i, int i2, int i3, int i4) {
        jnu.e(bArr, "dest");
        return this.a.c(bArr, i, i2, i3, jnu.p(this.b.e.b, i4));
    }
}
