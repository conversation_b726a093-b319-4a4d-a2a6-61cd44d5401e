apply plugin: 'com.android.application'
// apply plugin: 'com.google.protobuf'  // 暂时禁用，使用手动解析

android {
    compileSdkVersion 29
    buildToolsVersion "29.0.2"
    defaultConfig {
        applicationId "com.google.android.apps.googlespeechoffline"
        minSdkVersion 25
        targetSdkVersion 29
        versionCode 1
        versionName "1.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        ndk {
            abiFilters "arm64-v8a" //"""armeabi-v7a", "arm64-v8a"
        }

    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }

    externalNativeBuild {
        cmake {
            version "3.6.0"
            path 'src/main/cpp/CMakeLists.txt'
        }
    }
    ndkVersion = '20.0.5594570'
    namespace 'com.google.android.apps.googlespeechoffline'
}

// 暂时注释protobuf配置，使用手动解析
/*
protobuf {
    protoc {
        artifact = 'com.google.protobuf:protoc:3.21.12'
    }
    generateProtoTasks {
        all().each { task ->
            task.builtins {
                java {}
            }
        }
    }
}
*/

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.0.2'
    implementation 'androidx.constraintlayout:constraintlayout:1.1.3'
    implementation 'com.google.code.gson:gson:2.8.2'

    // Protobuf Lite runtime
    implementation 'com.google.protobuf:protobuf-javalite:3.25.1'

    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test.ext:junit:1.1.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.1.1'
}

