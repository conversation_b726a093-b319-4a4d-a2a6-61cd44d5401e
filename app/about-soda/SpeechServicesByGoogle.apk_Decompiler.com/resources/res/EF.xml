<?xml version="1.0" encoding="utf-8"?>
<phenotype-registrations xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt">
    <phenotype-registration>
        <configuration-package>com.google.android.apps.search.transcription.device
        </configuration-package>
        <log-sources>
            <log-source>AGSA_APA
            </log-source>
            <log-source>AGSA_ASSISTANT
            </log-source>
            <log-source>AGSA_ASSISTANT_AUTO
            </log-source>
            <log-source>AGSA_ASSISTANT_INTERACTOR
            </log-source>
            <log-source>AGSA_ASSISTANT_TITAN_TNG
            </log-source>
            <log-source>AGSA_BISTO
            </log-source>
            <log-source>AGSA_FACEVIEWER
            </log-source>
            <log-source>AGSA_FEDORA
            </log-source>
            <log-source>AGSA_GOOGLE_APP
            </log-source>
            <log-source>AGSA_GOOGLE_APP_COUNTERS
            </log-source>
            <log-source>AGSA_HOTWORD_LIBRARY
            </log-source>
            <log-source>AGSA_INFRASTRUCTURE
            </log-source>
            <log-source>AGSA_INFRASTRUCTURE_COUNTERS
            </log-source>
            <log-source>AGSA_INTERPRETER_MODE
            </log-source>
            <log-source>AGSA_IN_APP_UPDATE_ANDROID
            </log-source>
            <log-source>AGSA_KAHANI
            </log-source>
            <log-source>AGSA_LEGACY
            </log-source>
            <log-source>AGSA_LENS
            </log-source>
            <log-source>AGSA_LENS_COUNTERS
            </log-source>
            <log-source>AGSA_LINGO_CAMERA
            </log-source>
            <log-source>AGSA_MDD_ANDROID
            </log-source>
            <log-source>AGSA_MORRIS
            </log-source>
            <log-source>AGSA_NIU
            </log-source>
            <log-source>AGSA_OMNI
            </log-source>
            <log-source>AGSA_PODCASTS
            </log-source>
            <log-source>AGSA_PROACTIVE_ASSISTANT
            </log-source>
            <log-source>AGSA_PRONUNCIATION_LEARNING
            </log-source>
            <log-source>AGSA_QUICK_PHRASES
            </log-source>
            <log-source>AGSA_READ
            </log-source>
            <log-source>AGSA_RESTRICTED
            </log-source>
            <log-source>AGSA_SCENEVIEWER
            </log-source>
            <log-source>AGSA_SEARCH_VIDEO_ANDROID
            </log-source>
            <log-source>AGSA_SEARCH_XR
            </log-source>
            <log-source>AGSA_SOUND_SEARCH
            </log-source>
            <log-source>AGSA_TNG_FINANCE_WIDGET
            </log-source>
            <log-source>AGSA_TNG_SPORTS_WIDGET
            </log-source>
            <log-source>AGSA_TRANSCRIPTION
            </log-source>
            <log-source>AGSA_WEATHER
            </log-source>
            <log-source>AGSA_WEBGLIDE
            </log-source>
            <log-source>AGSA_XBLEND
            </log-source>
            <log-source>ANDROID_GSA
            </log-source>
            <log-source>ANDROID_GSA_ANDROID_PRIMES
            </log-source>
            <log-source>ANDROID_GSA_COUNTERS
            </log-source>
            <log-source>ANDROID_GSA_HIGH_PRIORITY_EVENTS
            </log-source>
            <log-source>AUDIO_LIBRARY_ANDROID
            </log-source>
            <log-source>CLIENT_LOGGING_PROD
            </log-source>
            <log-source>CRONET_ANDROID_GSA
            </log-source>
            <log-source>FEDASS_COUNTERS
            </log-source>
            <log-source>FEDASS_LOGS
            </log-source>
            <log-source>ONEGOOGLE_MOBILE
            </log-source>
            <log-source>PHENOTYPE
            </log-source>
            <log-source>ROBIN_ANDROID
            </log-source>
            <log-source>ROBIN_ANDROID_PSEUDO
            </log-source>
            <log-source>SILK_NATIVE
            </log-source>
            <log-source>SODA_CLEARCUT
            </log-source>
            <log-source>STREAMZ_ANDROID_ASSISTANT
            </log-source>
            <log-source>STREAMZ_ANDROID_GSA
            </log-source>
            <log-source>STREAMZ_GELLER_LIBRARY
            </log-source>
            <log-source>STREAMZ_HUBMODE_GSA
            </log-source>
            <log-source>STREAMZ_LENS_ANDROID
            </log-source>
            <log-source>STREAMZ_MOBILE_ASSISTANT
            </log-source>
            <log-source>STREAMZ_OPA_PROACTIVE
            </log-source>
            <log-source>STREAMZ_SODA
            </log-source>
        </log-sources>
        <auto-subpackage>true
        </auto-subpackage>
        <params>gOowA4rqMAYIDxAgGAc=
        </params>
    </phenotype-registration>
</phenotype-registrations>
