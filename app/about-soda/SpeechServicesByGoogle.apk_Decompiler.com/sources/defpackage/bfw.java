package defpackage;

import android.content.Context;
import android.net.ConnectivityManager;

/* renamed from: bfw  reason: default package */
/* compiled from: PG */
public final class bfw extends bft {
    public final ConnectivityManager e;
    private final bfv f = new bfv(this);

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bfw(Context context, cyw cyw) {
        super(context, cyw);
        jnu.e(context, "context");
        Object systemService = this.a.getSystemService("connectivity");
        jnu.c(systemService, "null cannot be cast to non-null type android.net.ConnectivityManager");
        this.e = (ConnectivityManager) systemService;
    }

    public final /* bridge */ /* synthetic */ Object b() {
        return bfx.a(this.e);
    }

    public final void d() {
        try {
            bbk.a();
            String str = bfx.a;
            ConnectivityManager connectivityManager = this.e;
            bfv bfv = this.f;
            jnu.e(connectivityManager, "<this>");
            jnu.e(bfv, "networkCallback");
            connectivityManager.registerDefaultNetworkCallback(bfv);
        } catch (IllegalArgumentException e2) {
            bbk.a().d(bfx.a, "Received exception while registering network callback", e2);
        } catch (SecurityException e3) {
            bbk.a().d(bfx.a, "Received exception while registering network callback", e3);
        }
    }

    public final void e() {
        try {
            bbk.a();
            String str = bfx.a;
            ConnectivityManager connectivityManager = this.e;
            bfv bfv = this.f;
            jnu.e(connectivityManager, "<this>");
            jnu.e(bfv, "networkCallback");
            connectivityManager.unregisterNetworkCallback(bfv);
        } catch (IllegalArgumentException e2) {
            bbk.a().d(bfx.a, "Received exception while unregistering network callback", e2);
        } catch (SecurityException e3) {
            bbk.a().d(bfx.a, "Received exception while unregistering network callback", e3);
        }
    }
}
