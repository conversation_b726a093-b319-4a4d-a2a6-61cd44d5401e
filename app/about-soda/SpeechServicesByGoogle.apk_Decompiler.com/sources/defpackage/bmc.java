package defpackage;

import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.support.v7.widget.RecyclerView;
import android.view.View;
import com.google.android.tts.R;
import j$.util.Objects;

/* renamed from: bmc  reason: default package */
/* compiled from: PG */
public final class bmc extends ki {
    public int a;
    private final Drawable b;
    private final Drawable c;

    public bmc(Drawable drawable, Drawable drawable2, int i) {
        this.b = drawable;
        this.c = drawable2;
        this.a = i;
    }

    public final void a(Rect rect, View view, RecyclerView recyclerView, la laVar) {
        Drawable drawable = this.b;
        rect.set(0, 0, drawable.getIntrinsicWidth(), drawable.getIntrinsicHeight());
    }

    public final void h(Canvas canvas, RecyclerView recyclerView) {
        int i;
        int i2;
        kl klVar = (kl) Objects.requireNonNull(recyclerView.m);
        double ceil = Math.ceil(((double) klVar.ao()) / ((double) this.a));
        int i3 = 1;
        while (true) {
            double d = (double) i3;
            if (d > ceil) {
                break;
            }
            if (i3 != 1) {
                int i4 = i3 - 1;
                if (d == ceil) {
                    i2 = this.a * i4;
                } else {
                    i2 = this.a * i3;
                }
                View aA = klVar.aA(this.a * i4);
                View aA2 = klVar.aA(i2 - 1);
                int top = aA.getTop();
                this.c.setBounds(aA.getLeft() + ((int) recyclerView.getContext().getResources().getDimension(R.dimen.car_ui_recyclerview_divider_start_margin)), top - this.c.getIntrinsicHeight(), aA2.getRight() - ((int) recyclerView.getContext().getResources().getDimension(R.dimen.car_ui_recyclerview_divider_end_margin)), top);
                this.c.draw(canvas);
            }
            i3++;
        }
        kl klVar2 = (kl) Objects.requireNonNull(recyclerView.m);
        int ao = klVar2.ao();
        int i5 = this.a;
        int i6 = ao / i5;
        int i7 = ao % i5;
        int min = Math.min(ao, i5);
        for (int i8 = 1; i8 < min; i8++) {
            if (i8 < i7) {
                i = this.a * i6;
            } else {
                i = (i6 - 1) * this.a;
            }
            View aA3 = klVar2.aA(i8);
            View aA4 = klVar2.aA(i + i8);
            int top2 = aA3.getTop() + ((int) recyclerView.getContext().getResources().getDimension(R.dimen.car_ui_recyclerview_divider_top_margin));
            int left = aA3.getLeft();
            this.b.setBounds(left - this.b.getIntrinsicWidth(), top2, left, aA4.getBottom() - ((int) recyclerView.getContext().getResources().getDimension(R.dimen.car_ui_recyclerview_divider_bottom_margin)));
            this.b.draw(canvas);
        }
    }
}
