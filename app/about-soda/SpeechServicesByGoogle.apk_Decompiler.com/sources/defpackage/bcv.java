package defpackage;

/* renamed from: bcv  reason: default package */
/* compiled from: PG */
public final class bcv extends jmi implements jng {
    int a;
    public /* synthetic */ Object b;
    public /* synthetic */ long c;

    public bcv(jlr jlr) {
        super(4, jlr);
    }

    public final Object bk(Object obj) {
        jlx jlx = jlx.COROUTINE_SUSPENDED;
        int i = this.a;
        jji.c(obj);
        if (i == 0) {
            Object obj2 = this.b;
            long j = this.c;
            bbk.a().d(bcx.a, "Cannot check for unfinished work", (Throwable) obj2);
            long min = Math.min(j * 30000, bcx.b);
            this.a = 1;
            if (jnu.M(min, this) == jlx) {
                return jlx;
            }
        }
        return true;
    }
}
