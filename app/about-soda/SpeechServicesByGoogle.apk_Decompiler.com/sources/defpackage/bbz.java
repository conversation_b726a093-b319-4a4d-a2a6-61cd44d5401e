package defpackage;

import android.content.Context;
import java.util.List;
import java.util.UUID;

/* renamed from: bbz  reason: default package */
/* compiled from: PG */
public abstract class bbz {
    public static bbz d(Context context) {
        jnu.e(context, "context");
        return bdm.i(context);
    }

    public abstract bbr a(String str);

    public abstract bbr b(UUID uuid);

    public abstract bbr c(List list);

    public abstract bbr e(String str, int i, List list);

    public final bbr f(bmu bmu) {
        return c(jji.m(bmu));
    }

    public abstract bbr g(String str, int i, bmu bmu);

    public abstract hme h(byw byw);
}
