package defpackage;

/* renamed from: cty  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cty implements cud {
    private final /* synthetic */ int a;

    public /* synthetic */ cty(int i) {
        this.a = i;
    }

    public final int a(Object obj) {
        if (this.a != 0) {
            crw crw = (crw) obj;
            return 3;
        } else if (((<PERSON><PERSON>an) obj).booleanValue()) {
            return 3;
        } else {
            return 4;
        }
    }
}
