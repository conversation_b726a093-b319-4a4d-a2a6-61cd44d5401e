package defpackage;

import android.car.drivingstate.CarUxRestrictions;
import android.widget.EditText;
import com.google.android.tts.R;

/* renamed from: bmz  reason: default package */
/* compiled from: PG */
final class bmz implements bny {
    final /* synthetic */ bna a;

    public bmz(bna bna) {
        this.a = bna;
    }

    public final void a(CarUxRestrictions carUxRestrictions) {
        if ((carUxRestrictions.getActiveRestrictions() & 8) != 0) {
            bna bna = this.a;
            bna.m = true;
            EditText editText = bna.k;
            if (editText != null) {
                editText.setHint(editText.getContext().getString(R.string.car_ui_restricted_while_driving));
                bna.k.setEnabled(false);
                return;
            }
            return;
        }
        bna bna2 = this.a;
        bna2.m = false;
        EditText editText2 = bna2.k;
        if (editText2 != null) {
            editText2.setHint(bna2.l);
            bna2.k.setEnabled(true);
        }
    }
}
