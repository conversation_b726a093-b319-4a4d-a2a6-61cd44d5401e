package com.google.android.libraries.assistant.soda;

import java.nio.ByteBuffer;
import java.util.logging.Logger;
import android.util.Log;

import com.google.android.apps.googlespeechoffline.speech.CustomSoda;
import com.google.speech.recognizer.ResourceManager;

/**
 * Base class for SODA (Speech On-Device API) library
 *
 * This class provides access to real-time speech recognition functionality
 * through JNI calls to the native libgoogle_speech_sbg_jni.so library.
 *
 * Implements the complete SODA JNI API as documented in SODA_JNI_API_Documentation.md
 */
public class Soda {

    private static final Logger logger = Logger.getLogger(Soda.class.getName());
    static public String TAG = "Soda";

    // Native object pointer
    protected long nativePtr;

    // === 资源生命周期管理 ===
    public static native long nativeCreateSharedResources(Object self);
    public native long nativeConstruct(long sharedResHandle);
    public native void nativeDelete(long sodaHandle);
    public static native boolean nativeDeleteSharedResources(Object self, long sharedResHandle);
    public static native boolean nativeDeleteSharedResourcesIfDiarizationProcessorAbsent(long sharedResHandle);
    public static native void nativeDeleteDiarizationProcessor(long sharedResHandle);

    // === 初始化与配置 ===
    public native byte[] nativeInit(long sodaHandle, byte[] configBytes);
    public native boolean nativeRequiresReinitialization(long sodaHandle, byte[] configBytes);

    // === 数据提供与传输 ===
    public native void nativeSetDataProvider(long sodaHandle, SodaDataProviderJni dataProvider);
    public native void nativeSetTransportFactory(long sodaHandle, SodaTransportFactory transport);

    // === 采集控制 ===
    public native byte[] nativeStartCapture(long sodaHandle, byte[] startParamsBytes);
    public native void nativeStopCapture(long sodaHandle);

    // === 音频输入 ===
    public native boolean nativeAddAudio(long sodaHandle, ByteBuffer audio, long byteCount);
    public native boolean nativeAddTimestampedAudio(long sodaHandle, ByteBuffer audio, long byteCount,
                                                   ByteBuffer tsMeta, long tsMetaByteCount);

    // === 数据回调 ===
    public static native void nativeHandleDataResponse(long reqId, long token, byte[] payload);

    // === 兼容性方法（保持向后兼容） ===
    private native long nativeConstruct();
    private native void nativeInit();
    private native long nativeConstruct(Object param1, Object param2, Object param3);
    private static native boolean nativeAddAudio(long nativePtr, ByteBuffer audioBuffer, int length);

    public Soda() {

    }

    /**
     * 兼容性方法：添加音频数据（旧版本接口）
     */
    public boolean addAudio(ByteBuffer audioBuffer, int length) {
        if (this.nativePtr != 0) {
            return nativeAddAudio(this.nativePtr, audioBuffer, length);
        }
        return false;
    }

    /**
     * 兼容性方法：初始化（旧版本接口）
     */
    public final void init(String model, Soda soda) {
        this.nativePtr = nativeConstruct(this, model, soda);
        this.nativeInit();
    }

    // JNI 会直接回调 CustomSoda 的方法
    public void handleSodaEvent(byte[] data) {
        System.out.println("## handleSodaEvent");
        // 处理音频事件
    }

    public void handleStart() {
        System.out.println("## handleStart");
    }

    public void handleStop(int code) {
        System.out.println("## handleStop " + code);
    }

    public void handleShutdown() {
        System.out.println("## handleShutdown");
    }

    public long getNativeObj() {
        return this.nativePtr;
    }
}
