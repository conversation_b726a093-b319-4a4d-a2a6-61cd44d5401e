package defpackage;

/* renamed from: csy  reason: default package */
/* compiled from: PG */
public final class csy extends htq implements hvb {
    public static final csy g;
    private static volatile hvh h;
    public int a;
    public String b = "";
    public int c;
    public String d = "";
    public int e;
    public csu f;

    static {
        csy csy = new csy();
        g = csy;
        htq.z(csy.class, csy);
    }

    private csy() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(g, "\u0001\u0005\u0000\u0001\u0001\u0006\u0005\u0000\u0000\u0000\u0001ဈ\u0000\u0002င\u0001\u0003ဈ\u0002\u0005᠌\u0003\u0006ဉ\u0004", new Object[]{"a", "b", "c", "d", "e", bqk.q, "f"});
        } else if (i2 == 3) {
            return new csy();
        } else {
            if (i2 == 4) {
                return new htk((htq) g);
            }
            if (i2 == 5) {
                return g;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = h;
            if (hvh == null) {
                synchronized (csy.class) {
                    hvh = h;
                    if (hvh == null) {
                        hvh = new htl(g);
                        h = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
