package defpackage;

import android.content.Context;

/* renamed from: cre  reason: default package */
/* compiled from: PG */
public final class cre {
    public final cqu a;
    public final cqy b;

    protected cre(Context context, cqy cqy) {
        Context context2;
        fvf.aP(context);
        Context applicationContext = context.getApplicationContext();
        crf crf = new crf();
        cqt cqt = new cqt((byte[]) null);
        cqt.a();
        if (applicationContext != null) {
            cqt.a = applicationContext;
            cqt.c = grh.h(crf);
            cqt.a();
            if (cqt.e != 1 || (context2 = cqt.a) == null) {
                StringBuilder sb = new StringBuilder();
                if (cqt.a == null) {
                    sb.append(" context");
                }
                if (cqt.e == 0) {
                    sb.append(" googlerOverridesCheckbox");
                }
                throw new IllegalStateException("Missing required properties:".concat(sb.toString()));
            }
            this.a = new cqu(context2, cqt.b, cqt.c, cqt.d);
            this.b = cqy;
            return;
        }
        throw new NullPointerException("Null context");
    }

    public static cre a(Context context, cqs cqs) {
        return new cre(context, new cqy(cqs));
    }

    public final String toString() {
        return "CollectionBasisLogVerifier{collectionBasisContext=" + this.a + ", basis=" + this.b + "}";
    }
}
