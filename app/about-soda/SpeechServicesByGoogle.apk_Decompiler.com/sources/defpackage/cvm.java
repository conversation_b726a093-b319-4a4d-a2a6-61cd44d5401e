package defpackage;

/* renamed from: cvm  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvm implements hko {
    public final /* synthetic */ cvy a;
    public final /* synthetic */ csx b;
    public final /* synthetic */ csv c;
    public final /* synthetic */ ctl d;
    public final /* synthetic */ ctj e;
    public final /* synthetic */ String f;
    public final /* synthetic */ long g;
    public final /* synthetic */ int h;

    public /* synthetic */ cvm(cvy cvy, csx csx, csv csv, ctl ctl, ctj ctj, String str, long j, int i) {
        this.a = cvy;
        this.b = csx;
        this.c = csv;
        this.d = ctl;
        this.e = ctj;
        this.f = str;
        this.g = j;
        this.h = i;
    }

    public final hme a(Object obj) {
        Void voidR = (Void) obj;
        cvy cvy = this.a;
        int i = this.h;
        csx csx = this.b;
        csv csv = this.c;
        ctl ctl = this.d;
        ctj ctj = this.e;
        String str = this.f;
        long j = this.g;
        hme y = cvy.y(csx, csv, ctl, ctj, str, j, i);
        cvl cvl = r2;
        cvl cvl2 = new cvl(cvy, i, csx, csv, ctj, j);
        return cvy.q(y, cvl);
    }
}
