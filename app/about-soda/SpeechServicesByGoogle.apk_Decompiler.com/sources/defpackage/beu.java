package defpackage;

import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import java.util.concurrent.CancellationException;

/* renamed from: beu  reason: default package */
/* compiled from: PG */
public final class beu extends ConnectivityManager.NetworkCallback {
    final /* synthetic */ jrz a;
    final /* synthetic */ jty b;

    public beu(jrz jrz, jty jty) {
        this.a = jrz;
        this.b = jty;
    }

    public final void onCapabilitiesChanged(Network network, NetworkCapabilities networkCapabilities) {
        jnu.e(network, "network");
        jnu.e(networkCapabilities, "networkCapabilities");
        this.a.r((CancellationException) null);
        bbk.a();
        long j = bfc.a;
        this.b.g(beq.a);
    }

    public final void onLost(Network network) {
        jnu.e(network, "network");
        this.a.r((CancellationException) null);
        bbk.a();
        long j = bfc.a;
        this.b.g(new ber(7));
    }
}
