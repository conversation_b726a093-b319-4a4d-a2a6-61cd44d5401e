package defpackage;

/* renamed from: evj  reason: default package */
/* compiled from: PG */
public enum evj implements hts {
    REASON_UNKNOWN(0),
    REASON_REALTIME_GAP(1),
    REASON_INITIAL_REALTIME_GAP(2),
    REASON_MAX_AUDIO_RECEIVED(3),
    REASON_MAX_COMPUTATION_DURATION(4),
    REASON_CLOSED(5);
    
    public final int g;

    private evj(int i) {
        this.g = i;
    }

    public static evj b(int i) {
        if (i == 0) {
            return REASON_UNKNOWN;
        }
        if (i == 1) {
            return REASON_REALTIME_GAP;
        }
        if (i == 2) {
            return REASON_INITIAL_REALTIME_GAP;
        }
        if (i == 3) {
            return REASON_MAX_AUDIO_RECEIVED;
        }
        if (i == 4) {
            return REASON_MAX_COMPUTATION_DURATION;
        }
        if (i != 5) {
            return null;
        }
        return REASON_CLOSED;
    }

    public final int a() {
        return this.g;
    }

    public final String toString() {
        return Integer.toString(this.g);
    }
}
