package defpackage;

import java.util.function.Predicate;

/* renamed from: emb  reason: default package */
/* compiled from: PG */
public final class emb implements hls {
    final /* synthetic */ Object a;
    final /* synthetic */ Object b;
    final /* synthetic */ Object c;
    final /* synthetic */ Object d;
    private final /* synthetic */ int e;

    public emb(cuf cuf, csk csk, String str, cuv cuv, int i) {
        this.e = i;
        this.c = csk;
        this.a = str;
        this.d = cuv;
        this.b = cuf;
    }

    public final void a(Throwable th) {
        int i = this.e;
        if (i == 0) {
            ((hdc) ((hdc) ((hdc) emd.a.h()).i(th)).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl$1", "onFailure", 165, "MonitoringLoggerImpl.java")).r("#audio# AudioRequestListeningSession start listening failed");
            ((emi) ((emd) this.d).b.b()).d(((eaj) this.c).name(), (String) ((emd) this.d).d.a(), ehf.a(((ehg) this.b).a).name(), false);
        } else if (i == 1) {
            if (((csk) this.c).g.f()) {
                ((csl) ((csk) this.c).g.b()).b(th);
                if (((cuf) this.b).e.f()) {
                    ((czp) ((cuf) this.b).e.b()).i((String) this.a);
                }
            }
            ((cuf) this.b).j.h(((cuv) this.d).a);
        } else if (i == 2) {
            ((hdc) ((hdc) ((hdc) emd.a.h()).i(th)).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl$2", "onFailure", 212, "MonitoringLoggerImpl.java")).r("#audio# AudioRequestListeningSession stop listening failed");
            ((emi) ((emd) this.d).b.b()).c(((eag) this.c).name(), eam.UNSET.name(), (String) ((emd) this.d).d.a(), ehf.a(((ehg) this.b).a).name());
        } else if (i == 3) {
            ((hdc) ((hdc) ((hdc) emd.a.h()).i(th)).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl$3", "onFailure", 306, "MonitoringLoggerImpl.java")).r("#audio# HotwordListeningSession start listening failed");
            ((emi) ((emd) this.d).b.b()).f(((eaj) this.c).name(), (String) ((emd) this.d).d.a(), false, ehf.a(((ehg) this.b).a).name());
        } else if (i != 4) {
            ((isw) this.b).a(itg.j.d(th).e("Authorization future failed"), new irw());
        } else {
            ((hdc) ((hdc) ((hdc) emd.a.h()).i(th)).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl$4", "onFailure", 353, "MonitoringLoggerImpl.java")).r("#audio# HotwordListeningSession stop listening failed");
            ((emi) ((emd) this.d).b.b()).e(((eag) this.c).name(), eam.UNSET.name(), (String) ((emd) this.d).d.a(), ehf.a(((ehg) this.b).a).name());
        }
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [java.util.function.Predicate, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v3, types: [java.util.function.Predicate, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v39, types: [java.util.function.Predicate, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v19, types: [java.util.function.Predicate, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v26, types: [java.lang.Object, isx] */
    public final /* synthetic */ void b(Object obj) {
        String str;
        eal eal;
        String str2;
        eai eai;
        String str3;
        eal eal2;
        String str4;
        eai eai2;
        int i = this.e;
        boolean z = false;
        if (i == 0) {
            eak eak = ((dzh) ((dyw) obj).f()).b;
            if (eak == null) {
                eak = eak.c;
            }
            if (this.a.test(eak)) {
                int i2 = eak.a;
                if (i2 == 2) {
                    eaj b2 = eaj.b(((Integer) eak.b).intValue());
                    if (b2 == null) {
                        b2 = eaj.UNKNOWN_OPENING_FAILURE;
                    }
                    str = b2.name();
                } else {
                    if (i2 == 1) {
                        eal = eal.b(((Integer) eak.b).intValue());
                        if (eal == null) {
                            eal = eal.UNKNOWN_OPENING_SUCCESS;
                        }
                    } else {
                        eal = eal.UNKNOWN_OPENING_SUCCESS;
                    }
                    str = eal.name();
                }
                ((hdc) ((hdc) emd.a.f()).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl$1", "onSuccess", 152, "MonitoringLoggerImpl.java")).u("#audio# AudioRequestListeningSession start listening status: %s", str);
                emi emi = (emi) ((emd) this.d).b.b();
                String str5 = (String) ((emd) this.d).d.a();
                String name = ehf.a(((ehg) this.b).a).name();
                if (eak.a == 1) {
                    z = true;
                }
                emi.d(str, str5, name, z);
            }
        } else if (i == 1) {
            crw crw = (crw) obj;
        } else if (i == 2) {
            dzi dzi = (dzi) obj;
            eah eah = dzi.b;
            if (eah == null) {
                eah = eah.c;
            }
            if (this.a.test(eah)) {
                int i3 = eah.a;
                if (i3 == 2) {
                    eag b3 = eag.b(((Integer) eah.b).intValue());
                    if (b3 == null) {
                        b3 = eag.UNKNOWN_CLOSING_FAILURE;
                    }
                    str2 = b3.name();
                } else {
                    if (i3 == 1) {
                        eai = eai.b(((Integer) eah.b).intValue());
                        if (eai == null) {
                            eai = eai.UNKNOWN_CLOSING_SUCCESS;
                        }
                    } else {
                        eai = eai.UNKNOWN_CLOSING_SUCCESS;
                    }
                    str2 = eai.name();
                }
                ((hdc) ((hdc) emd.a.f()).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl$2", "onSuccess", 199, "MonitoringLoggerImpl.java")).u("#audio# AudioRequestListeningSession stop listening status: %s", str2);
                emi emi2 = (emi) ((emd) this.d).b.b();
                eam b4 = eam.b(dzi.c);
                if (b4 == null) {
                    b4 = eam.UNSET;
                }
                emi2.c(str2, b4.name(), (String) ((emd) this.d).d.a(), ehf.a(((ehg) this.b).a).name());
            }
        } else if (i == 3) {
            eak eak2 = ((ebo) ((ebj) obj).f()).b;
            if (eak2 == null) {
                eak2 = eak.c;
            }
            if (this.a.test(eak2)) {
                int i4 = eak2.a;
                if (i4 == 2) {
                    eaj b5 = eaj.b(((Integer) eak2.b).intValue());
                    if (b5 == null) {
                        b5 = eaj.UNKNOWN_OPENING_FAILURE;
                    }
                    str3 = b5.name();
                } else {
                    if (i4 == 1) {
                        eal2 = eal.b(((Integer) eak2.b).intValue());
                        if (eal2 == null) {
                            eal2 = eal.UNKNOWN_OPENING_SUCCESS;
                        }
                    } else {
                        eal2 = eal.UNKNOWN_OPENING_SUCCESS;
                    }
                    str3 = eal2.name();
                }
                if (eak2.a == 1) {
                    z = true;
                }
                ((hdc) ((hdc) emd.a.f()).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl$3", "onSuccess", 296, "MonitoringLoggerImpl.java")).u("#audio# HotwordListeningSession start listening status: %s", str3);
                ((emi) ((emd) this.d).b.b()).f(str3, (String) ((emd) this.d).d.a(), z, ehf.a(((ehg) this.b).a).name());
            }
        } else if (i != 4) {
            itg itg = (itg) obj;
            if (!itg.g()) {
                ((isw) this.b).a(itg, new irw());
                return;
            }
            Object obj2 = this.a;
            Object obj3 = this.b;
            try {
                ivn ivn = (ivn) obj2;
                ivn.a.set(this.d.a((isw) obj3, (irw) this.c));
                ivn.f();
            } catch (RuntimeException e2) {
                ((isw) obj3).a(itg.j.d(e2).e("Failed to start server call after authorization check"), new irw());
            }
        } else {
            ebp ebp = (ebp) obj;
            eah eah2 = ebp.b;
            if (eah2 == null) {
                eah2 = eah.c;
            }
            if (this.a.test(eah2)) {
                int i5 = eah2.a;
                if (i5 == 2) {
                    eag b6 = eag.b(((Integer) eah2.b).intValue());
                    if (b6 == null) {
                        b6 = eag.UNKNOWN_CLOSING_FAILURE;
                    }
                    str4 = b6.name();
                } else {
                    if (i5 == 1) {
                        eai2 = eai.b(((Integer) eah2.b).intValue());
                        if (eai2 == null) {
                            eai2 = eai.UNKNOWN_CLOSING_SUCCESS;
                        }
                    } else {
                        eai2 = eai.UNKNOWN_CLOSING_SUCCESS;
                    }
                    str4 = eai2.name();
                }
                ((hdc) ((hdc) emd.a.f()).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl$4", "onSuccess", 340, "MonitoringLoggerImpl.java")).u("#audio# HotwordListeningSession stop listening status: %s", str4);
                emi emi3 = (emi) ((emd) this.d).b.b();
                eam b7 = eam.b(ebp.c);
                if (b7 == null) {
                    b7 = eam.UNSET;
                }
                emi3.e(str4, b7.name(), (String) ((emd) this.d).d.a(), ehf.a(((ehg) this.b).a).name());
            }
        }
    }

    public emb(emd emd, Predicate predicate, ehg ehg, Enum enumR, int i) {
        this.e = i;
        this.a = predicate;
        this.b = ehg;
        this.c = enumR;
        this.d = emd;
    }

    public emb(isw isw, ivn ivn, irw irw, isx isx, int i) {
        this.e = i;
        this.b = isw;
        this.a = ivn;
        this.c = irw;
        this.d = isx;
    }
}
