<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="abc_decor_view_status_guard">#ff000000</color>
    <color name="abc_decor_view_status_guard_light">#ffffffff</color>
    <color name="abc_search_url_text_normal">#7fa87f</color>
    <color name="abc_search_url_text_pressed">@android:color/black</color>
    <color name="abc_search_url_text_selected">@android:color/black</color>
    <color name="accent_material_dark">@color/material_deep_teal_200</color>
    <color name="accent_material_light">@color/material_deep_teal_500</color>
    <color name="agsa_color_background">@color/agsa_color_light_background</color>
    <color name="agsa_color_dark_background">#171717</color>
    <color name="agsa_color_dark_error">#f46e73</color>
    <color name="agsa_color_dark_hairline">#424548</color>
    <color name="agsa_color_dark_on_primary">@color/google_grey900</color>
    <color name="agsa_color_dark_on_surface">@color/google_grey300</color>
    <color name="agsa_color_dark_on_surface_variant">#969ba1</color>
    <color name="agsa_color_dark_primary">@color/google_blue300</color>
    <color name="agsa_color_dark_primary_variant">@color/google_blue400</color>
    <color name="agsa_color_error">@color/agsa_color_light_error</color>
    <color name="agsa_color_hairline">@color/agsa_color_light_hairline</color>
    <color name="agsa_color_light_background">@color/google_white</color>
    <color name="agsa_color_light_error">@color/google_red600</color>
    <color name="agsa_color_light_hairline">@color/google_grey300</color>
    <color name="agsa_color_light_on_primary">@color/google_white</color>
    <color name="agsa_color_light_on_surface">@color/google_grey800</color>
    <color name="agsa_color_light_on_surface_variant">@color/google_grey700</color>
    <color name="agsa_color_light_primary">@color/google_blue600</color>
    <color name="agsa_color_light_primary_variant">@color/google_blue500</color>
    <color name="agsa_color_on_primary">@color/agsa_color_light_on_primary</color>
    <color name="agsa_color_on_surface">@color/agsa_color_light_on_surface</color>
    <color name="agsa_color_on_surface_variant">@color/agsa_color_light_on_surface_variant</color>
    <color name="agsa_color_primary">@color/agsa_color_light_primary</color>
    <color name="agsa_color_primary_variant">@color/agsa_color_light_primary_variant</color>
    <color name="background_floating_material_dark">@color/material_grey_800</color>
    <color name="background_floating_material_light">@android:color/white</color>
    <color name="background_material_dark">@color/material_grey_850</color>
    <color name="background_material_light">@color/material_grey_50</color>
    <color name="bright_foreground_disabled_material_dark">#80ffffff</color>
    <color name="bright_foreground_disabled_material_light">#80000000</color>
    <color name="bright_foreground_material_dark">@android:color/white</color>
    <color name="bright_foreground_material_light">@android:color/black</color>
    <color name="button_material_dark">#ff5a595b</color>
    <color name="button_material_light">#ffd6d7d7</color>
    <color name="car_ui_activity_background_color">#000000</color>
    <color name="car_ui_ime_wide_screen_description_color">#fff</color>
    <color name="car_ui_ime_wide_screen_description_title_color">#fff</color>
    <color name="car_ui_ime_wide_screen_divider_color">#2e3134</color>
    <color name="car_ui_ime_wide_screen_error_text_color">#000</color>
    <color name="car_ui_ime_wide_screen_search_item_sub_title_color">#fff</color>
    <color name="car_ui_ime_wide_screen_search_item_title_color">#fff</color>
    <color name="car_ui_preference_icon_color">@color/car_ui_text_color_primary</color>
    <color name="car_ui_preference_two_action_divider_color">#75ffffff</color>
    <color name="car_ui_recyclerview_divider_color">@android:color/transparent</color>
    <color name="car_ui_ripple_color">#27ffffff</color>
    <color name="car_ui_rotary_focus_fill_color">#3d94cbff</color>
    <color name="car_ui_rotary_focus_fill_secondary_color">#3d0059b3</color>
    <color name="car_ui_rotary_focus_pressed_fill_color">#8a94cbff</color>
    <color name="car_ui_rotary_focus_pressed_fill_secondary_color">#8a0041be</color>
    <color name="car_ui_rotary_focus_pressed_stroke_color">#94cbff</color>
    <color name="car_ui_rotary_focus_pressed_stroke_secondary_color">#0041be</color>
    <color name="car_ui_rotary_focus_stroke_color">#94cbff</color>
    <color name="car_ui_rotary_focus_stroke_secondary_color">#0059b3</color>
    <color name="car_ui_scrollbar_arrow">#ffffff</color>
    <color name="car_ui_toolbar_nav_icon_color">@color/car_ui_text_color_primary</color>
    <color name="car_ui_toolbar_search_hint_text_color">@color/car_ui_text_color_hint</color>
    <color name="car_ui_toolbar_tab_selected_color">@color/car_ui_text_color_primary</color>
    <color name="car_ui_toolbar_tab_unselected_color">@color/car_ui_text_color_secondary</color>
    <color name="design_dark_default_color_background">#121212</color>
    <color name="design_dark_default_color_error">#cf6679</color>
    <color name="design_dark_default_color_on_background">#ffffff</color>
    <color name="design_dark_default_color_on_error">#000000</color>
    <color name="design_dark_default_color_on_primary">#000000</color>
    <color name="design_dark_default_color_on_secondary">#000000</color>
    <color name="design_dark_default_color_on_surface">#ffffff</color>
    <color name="design_dark_default_color_primary">#ba86fc</color>
    <color name="design_dark_default_color_primary_dark">#000000</color>
    <color name="design_dark_default_color_primary_variant">#3700b3</color>
    <color name="design_dark_default_color_secondary">#03dac6</color>
    <color name="design_dark_default_color_secondary_variant">#03dac6</color>
    <color name="design_dark_default_color_surface">#121212</color>
    <color name="design_default_color_background">#ffffff</color>
    <color name="design_default_color_error">#b00020</color>
    <color name="design_default_color_on_background">#000000</color>
    <color name="design_default_color_on_error">#ffffff</color>
    <color name="design_default_color_on_primary">#ffffff</color>
    <color name="design_default_color_on_secondary">#000000</color>
    <color name="design_default_color_on_surface">#000000</color>
    <color name="design_default_color_primary">#6200ee</color>
    <color name="design_default_color_primary_dark">#3700b3</color>
    <color name="design_default_color_primary_variant">#3700b3</color>
    <color name="design_default_color_secondary">#03dac6</color>
    <color name="design_default_color_secondary_variant">#018786</color>
    <color name="design_default_color_surface">#ffffff</color>
    <color name="design_fab_stroke_end_inner_color">#0a000000</color>
    <color name="design_fab_stroke_end_outer_color">#0f000000</color>
    <color name="design_fab_stroke_top_inner_color">#1affffff</color>
    <color name="design_fab_stroke_top_outer_color">#2effffff</color>
    <color name="design_snackbar_background_color">#323232</color>
    <color name="error_color_material_dark">#ff7043</color>
    <color name="error_color_material_light">#ff5722</color>
    <color name="foreground_material_dark">@android:color/white</color>
    <color name="foreground_material_light">@android:color/black</color>
    <color name="gm_ref_palette_blue100">#ffd2e3fc</color>
    <color name="gm_ref_palette_blue200">#ffaecbfa</color>
    <color name="gm_ref_palette_blue300">#ff8ab4f8</color>
    <color name="gm_ref_palette_blue400">#ff669df6</color>
    <color name="gm_ref_palette_blue50">#ffe8f0fe</color>
    <color name="gm_ref_palette_blue500">#ff4285f4</color>
    <color name="gm_ref_palette_blue600">#ff1a73e8</color>
    <color name="gm_ref_palette_blue700">#ff1967d2</color>
    <color name="gm_ref_palette_blue800">#ff185abc</color>
    <color name="gm_ref_palette_blue900">#ff174ea6</color>
    <color name="gm_ref_palette_green100">#ffceead6</color>
    <color name="gm_ref_palette_green500">#ff34a853</color>
    <color name="gm_ref_palette_green900">#ff0d652d</color>
    <color name="gm_ref_palette_grey100">#fff1f3f4</color>
    <color name="gm_ref_palette_grey200">#ffe8eaed</color>
    <color name="gm_ref_palette_grey300">#ffdadce0</color>
    <color name="gm_ref_palette_grey400">#ffbdc1c6</color>
    <color name="gm_ref_palette_grey50">#fff8f9fa</color>
    <color name="gm_ref_palette_grey500">#ff9aa0a6</color>
    <color name="gm_ref_palette_grey600">#ff80868b</color>
    <color name="gm_ref_palette_grey700">#ff5f6368</color>
    <color name="gm_ref_palette_grey800">#ff3c4043</color>
    <color name="gm_ref_palette_grey900">#ff202124</color>
    <color name="gm_ref_palette_red200">#fff6aea9</color>
    <color name="gm_ref_palette_red300">#fff28b82</color>
    <color name="gm_ref_palette_red600">#ffd93025</color>
    <color name="gm_ref_palette_red700">#ffc5221f</color>
    <color name="gm_ref_palette_red900">#ffa50e0e</color>
    <color name="gm_ref_palette_white">#ffffffff</color>
    <color name="gm_sys_color_dark_background">@color/gm_ref_palette_grey900</color>
    <color name="gm_sys_color_dark_error">@color/gm_ref_palette_red300</color>
    <color name="gm_sys_color_dark_error_state_content">@color/gm_ref_palette_red200</color>
    <color name="gm_sys_color_dark_hairline">@color/gm_ref_palette_grey700</color>
    <color name="gm_sys_color_dark_inverse_on_surface">@color/gm_ref_palette_grey800</color>
    <color name="gm_sys_color_dark_inverse_primary">@color/gm_ref_palette_blue600</color>
    <color name="gm_sys_color_dark_inverse_primary_state_content">@color/gm_ref_palette_blue900</color>
    <color name="gm_sys_color_dark_inverse_surface">@color/gm_ref_palette_white</color>
    <color name="gm_sys_color_dark_on_background">@color/gm_ref_palette_grey200</color>
    <color name="gm_sys_color_dark_on_error">@color/gm_ref_palette_grey900</color>
    <color name="gm_sys_color_dark_on_primary">@color/gm_ref_palette_grey900</color>
    <color name="gm_sys_color_dark_on_primary_state_layer">@color/gm_ref_palette_white</color>
    <color name="gm_sys_color_dark_on_secondary">@color/gm_ref_palette_blue100</color>
    <color name="gm_sys_color_dark_on_secondary_state_content">@color/gm_ref_palette_white</color>
    <color name="gm_sys_color_dark_on_surface">@color/gm_ref_palette_grey200</color>
    <color name="gm_sys_color_dark_on_surface_state_content">@color/gm_ref_palette_grey50</color>
    <color name="gm_sys_color_dark_on_surface_variant">@color/gm_ref_palette_grey500</color>
    <color name="gm_sys_color_dark_primary">@color/gm_ref_palette_blue300</color>
    <color name="gm_sys_color_dark_primary_state_content">@color/gm_ref_palette_blue200</color>
    <color name="gm_sys_color_dark_primary_variant">@color/gm_ref_palette_blue400</color>
    <color name="gm_sys_color_dark_secondary">#ff394557</color>
    <color name="gm_sys_color_dark_secondary_variant">#ff212b3c</color>
    <color name="gm_sys_color_dark_surface">@color/gm_ref_palette_grey900</color>
    <color name="gm_sys_color_dark_textfield_error">@color/gm_ref_palette_red200</color>
    <color name="gm_sys_color_dark_textfield_hairline">@color/gm_ref_palette_grey400</color>
    <color name="gm_sys_color_dark_textfield_on_surface_variant">@color/gm_ref_palette_grey400</color>
    <color name="gm_sys_color_light_background">@color/gm_ref_palette_white</color>
    <color name="gm_sys_color_light_error">@color/gm_ref_palette_red600</color>
    <color name="gm_sys_color_light_error_state_content">@color/gm_ref_palette_red900</color>
    <color name="gm_sys_color_light_hairline">@color/gm_ref_palette_grey300</color>
    <color name="gm_sys_color_light_inverse_on_surface">@color/gm_ref_palette_grey200</color>
    <color name="gm_sys_color_light_inverse_primary">@color/gm_ref_palette_blue300</color>
    <color name="gm_sys_color_light_inverse_primary_state_content">@color/gm_ref_palette_blue200</color>
    <color name="gm_sys_color_light_inverse_surface">@color/gm_ref_palette_grey900</color>
    <color name="gm_sys_color_light_on_background">@color/gm_ref_palette_grey900</color>
    <color name="gm_sys_color_light_on_error">@color/gm_ref_palette_white</color>
    <color name="gm_sys_color_light_on_primary">@color/gm_ref_palette_white</color>
    <color name="gm_sys_color_light_on_primary_state_layer">@color/gm_ref_palette_grey900</color>
    <color name="gm_sys_color_light_on_secondary">@color/gm_ref_palette_blue700</color>
    <color name="gm_sys_color_light_on_secondary_state_content">@color/gm_ref_palette_blue900</color>
    <color name="gm_sys_color_light_on_surface">@color/gm_ref_palette_grey800</color>
    <color name="gm_sys_color_light_on_surface_state_content">@color/gm_ref_palette_grey900</color>
    <color name="gm_sys_color_light_on_surface_variant">@color/gm_ref_palette_grey700</color>
    <color name="gm_sys_color_light_primary">@color/gm_ref_palette_blue600</color>
    <color name="gm_sys_color_light_primary_state_content">@color/gm_ref_palette_blue900</color>
    <color name="gm_sys_color_light_primary_variant">@color/gm_ref_palette_blue500</color>
    <color name="gm_sys_color_light_secondary">@color/gm_ref_palette_blue50</color>
    <color name="gm_sys_color_light_secondary_variant">@color/gm_ref_palette_blue100</color>
    <color name="gm_sys_color_light_surface">@color/gm_ref_palette_white</color>
    <color name="gm_sys_color_light_textfield_error">@color/gm_ref_palette_red700</color>
    <color name="gm_sys_color_light_textfield_hairline">@color/gm_ref_palette_grey600</color>
    <color name="gm_sys_color_light_textfield_on_surface_variant">@color/gm_ref_palette_grey700</color>
    <color name="google_blue100">@color/gm_ref_palette_blue100</color>
    <color name="google_blue200">@color/gm_ref_palette_blue200</color>
    <color name="google_blue300">@color/gm_ref_palette_blue300</color>
    <color name="google_blue400">@color/gm_ref_palette_blue400</color>
    <color name="google_blue500">@color/gm_ref_palette_blue500</color>
    <color name="google_blue600">@color/gm_ref_palette_blue600</color>
    <color name="google_blue700">@color/gm_ref_palette_blue700</color>
    <color name="google_blue800">@color/gm_ref_palette_blue800</color>
    <color name="google_blue900">@color/gm_ref_palette_blue900</color>
    <color name="google_dark_color_on_secondary_state_content">@color/gm_sys_color_dark_on_secondary_state_content</color>
    <color name="google_dark_color_on_surface_state_content">@color/gm_sys_color_dark_on_surface_state_content</color>
    <color name="google_dark_color_textfield_hairline">@color/gm_sys_color_dark_textfield_hairline</color>
    <color name="google_dark_color_textfield_on_surface_variant">@color/gm_sys_color_dark_textfield_on_surface_variant</color>
    <color name="google_dark_default_color_background">@color/gm_sys_color_dark_background</color>
    <color name="google_dark_default_color_error">@color/gm_sys_color_dark_error</color>
    <color name="google_dark_default_color_error_state_content">@color/gm_sys_color_dark_error_state_content</color>
    <color name="google_dark_default_color_hairline">@color/gm_sys_color_dark_hairline</color>
    <color name="google_dark_default_color_inverse_on_surface">@color/gm_sys_color_dark_inverse_on_surface</color>
    <color name="google_dark_default_color_inverse_primary_google">@color/gm_sys_color_dark_inverse_primary</color>
    <color name="google_dark_default_color_inverse_primary_state_content_google">@color/gm_sys_color_dark_inverse_primary_state_content</color>
    <color name="google_dark_default_color_inverse_surface">@color/gm_sys_color_dark_inverse_surface</color>
    <color name="google_dark_default_color_on_background">@color/gm_sys_color_dark_on_background</color>
    <color name="google_dark_default_color_on_error">@color/gm_sys_color_dark_on_error</color>
    <color name="google_dark_default_color_on_primary">@color/gm_sys_color_dark_on_background</color>
    <color name="google_dark_default_color_on_primary_container">@color/google_blue900</color>
    <color name="google_dark_default_color_on_primary_container_state_content">@color/google_blue800</color>
    <color name="google_dark_default_color_on_primary_container_state_layer">@color/google_blue800</color>
    <color name="google_dark_default_color_on_primary_google">@color/gm_sys_color_dark_on_primary</color>
    <color name="google_dark_default_color_on_primary_state_content">@color/google_blue800</color>
    <color name="google_dark_default_color_on_primary_state_layer">@color/google_blue800</color>
    <color name="google_dark_default_color_on_primary_state_layer_google">@color/gm_sys_color_dark_on_primary_state_layer</color>
    <color name="google_dark_default_color_on_secondary">@color/gm_sys_color_dark_on_secondary</color>
    <color name="google_dark_default_color_on_secondary_container">@color/google_blue900</color>
    <color name="google_dark_default_color_on_secondary_container_state_content">@color/google_blue900</color>
    <color name="google_dark_default_color_on_secondary_container_state_layer">@color/google_blue900</color>
    <color name="google_dark_default_color_on_surface">@color/gm_sys_color_dark_on_surface</color>
    <color name="google_dark_default_color_on_surface_variant">@color/gm_sys_color_dark_on_surface_variant</color>
    <color name="google_dark_default_color_on_surface_variant_state_content">@color/google_grey400</color>
    <color name="google_dark_default_color_on_surface_variant_state_layer">@color/google_grey400</color>
    <color name="google_dark_default_color_on_tertiary_container">@color/google_green900</color>
    <color name="google_dark_default_color_on_tertiary_container_state_content">@color/google_green900</color>
    <color name="google_dark_default_color_on_tertiary_container_state_layer">@color/google_green900</color>
    <color name="google_dark_default_color_outline">@color/google_grey600</color>
    <color name="google_dark_default_color_primary">@color/gm_sys_color_dark_background</color>
    <color name="google_dark_default_color_primary_container">@color/google_blue100</color>
    <color name="google_dark_default_color_primary_dark">@color/gm_sys_color_dark_background</color>
    <color name="google_dark_default_color_primary_google">@color/gm_sys_color_dark_primary</color>
    <color name="google_dark_default_color_primary_state_content">@color/google_blue200</color>
    <color name="google_dark_default_color_primary_state_content_google">@color/gm_sys_color_dark_primary_state_content</color>
    <color name="google_dark_default_color_primary_variant">@color/gm_sys_color_dark_background</color>
    <color name="google_dark_default_color_primary_variant_google">@color/gm_sys_color_dark_primary_variant</color>
    <color name="google_dark_default_color_secondary">@color/gm_sys_color_dark_secondary</color>
    <color name="google_dark_default_color_secondary_container">@color/google_blue300</color>
    <color name="google_dark_default_color_secondary_state_content">@color/google_blue200</color>
    <color name="google_dark_default_color_secondary_state_layer">@color/google_blue300</color>
    <color name="google_dark_default_color_secondary_variant">@color/gm_sys_color_dark_secondary_variant</color>
    <color name="google_dark_default_color_surface">@color/gm_sys_color_dark_surface</color>
    <color name="google_dark_default_color_surface_variant">@color/google_grey800</color>
    <color name="google_dark_default_color_tertiary_container">@color/google_green100</color>
    <color name="google_dark_default_color_textfield_error">@color/gm_sys_color_dark_textfield_error</color>
    <color name="google_dark_default_color_timepicker_surface">@color/google_grey800</color>
    <color name="google_default_color_background">@color/gm_sys_color_light_background</color>
    <color name="google_default_color_error">@color/gm_sys_color_light_error</color>
    <color name="google_default_color_error_state_content">@color/gm_sys_color_light_error_state_content</color>
    <color name="google_default_color_hairline">@color/gm_sys_color_light_hairline</color>
    <color name="google_default_color_inverse_on_surface">@color/gm_sys_color_light_inverse_on_surface</color>
    <color name="google_default_color_inverse_primary_google">@color/gm_sys_color_light_inverse_primary</color>
    <color name="google_default_color_inverse_primary_state_content_google">@color/gm_sys_color_light_inverse_primary_state_content</color>
    <color name="google_default_color_inverse_surface">@color/gm_sys_color_light_inverse_surface</color>
    <color name="google_default_color_on_background">@color/gm_sys_color_light_on_background</color>
    <color name="google_default_color_on_error">@color/gm_sys_color_light_on_error</color>
    <color name="google_default_color_on_primary">@color/gm_sys_color_light_on_background</color>
    <color name="google_default_color_on_primary_container">@color/google_blue900</color>
    <color name="google_default_color_on_primary_container_state_content">@color/google_blue800</color>
    <color name="google_default_color_on_primary_container_state_layer">@color/google_blue800</color>
    <color name="google_default_color_on_primary_google">@color/gm_sys_color_light_on_primary</color>
    <color name="google_default_color_on_primary_state_content">@color/google_white</color>
    <color name="google_default_color_on_primary_state_layer">@color/google_blue900</color>
    <color name="google_default_color_on_primary_state_layer_google">@color/gm_sys_color_light_on_primary_state_layer</color>
    <color name="google_default_color_on_secondary">@color/gm_sys_color_light_on_secondary</color>
    <color name="google_default_color_on_secondary_container">@color/google_blue900</color>
    <color name="google_default_color_on_secondary_container_state_content">@color/google_blue900</color>
    <color name="google_default_color_on_secondary_container_state_layer">@color/google_blue900</color>
    <color name="google_default_color_on_secondary_state_content">@color/gm_sys_color_light_on_secondary_state_content</color>
    <color name="google_default_color_on_surface">@color/gm_sys_color_light_on_surface</color>
    <color name="google_default_color_on_surface_state_content">@color/gm_sys_color_light_on_surface_state_content</color>
    <color name="google_default_color_on_surface_variant">@color/gm_sys_color_light_on_surface_variant</color>
    <color name="google_default_color_on_surface_variant_state_content">@color/google_grey800</color>
    <color name="google_default_color_on_surface_variant_state_layer">@color/google_grey800</color>
    <color name="google_default_color_on_tertiary_container">@color/google_green900</color>
    <color name="google_default_color_on_tertiary_container_state_content">@color/google_green900</color>
    <color name="google_default_color_on_tertiary_container_state_layer">@color/google_green900</color>
    <color name="google_default_color_outline">@color/google_grey700</color>
    <color name="google_default_color_primary">@color/gm_sys_color_light_background</color>
    <color name="google_default_color_primary_container">@color/google_blue100</color>
    <color name="google_default_color_primary_dark">@color/gm_sys_color_light_background</color>
    <color name="google_default_color_primary_google">@color/gm_sys_color_light_primary</color>
    <color name="google_default_color_primary_state_content">@color/google_blue700</color>
    <color name="google_default_color_primary_state_content_google">@color/gm_sys_color_light_primary_state_content</color>
    <color name="google_default_color_primary_variant">@color/gm_sys_color_light_background</color>
    <color name="google_default_color_primary_variant_google">@color/gm_sys_color_light_primary_variant</color>
    <color name="google_default_color_secondary">@color/gm_sys_color_light_secondary</color>
    <color name="google_default_color_secondary_container">@color/google_blue200</color>
    <color name="google_default_color_secondary_state_content">@color/google_blue800</color>
    <color name="google_default_color_secondary_state_layer">@color/google_blue700</color>
    <color name="google_default_color_secondary_variant">@color/gm_sys_color_light_secondary_variant</color>
    <color name="google_default_color_surface">@color/gm_sys_color_light_surface</color>
    <color name="google_default_color_surface_variant">@color/google_grey300</color>
    <color name="google_default_color_tertiary_container">@color/google_green100</color>
    <color name="google_default_color_textfield_error">@color/gm_sys_color_light_textfield_error</color>
    <color name="google_default_color_textfield_hairline">@color/gm_sys_color_light_textfield_hairline</color>
    <color name="google_default_color_textfield_on_surface_variant">@color/gm_sys_color_light_textfield_on_surface_variant</color>
    <color name="google_default_color_timepicker_surface">@color/google_grey100</color>
    <color name="google_green100">@color/gm_ref_palette_green100</color>
    <color name="google_green500">@color/gm_ref_palette_green500</color>
    <color name="google_green900">@color/gm_ref_palette_green900</color>
    <color name="google_grey100">@color/gm_ref_palette_grey100</color>
    <color name="google_grey300">@color/gm_ref_palette_grey300</color>
    <color name="google_grey400">@color/gm_ref_palette_grey400</color>
    <color name="google_grey50">@color/gm_ref_palette_grey50</color>
    <color name="google_grey500">@color/gm_ref_palette_grey500</color>
    <color name="google_grey600">@color/gm_ref_palette_grey600</color>
    <color name="google_grey700">@color/gm_ref_palette_grey700</color>
    <color name="google_grey800">@color/gm_ref_palette_grey800</color>
    <color name="google_grey900">@color/gm_ref_palette_grey900</color>
    <color name="google_red600">@color/gm_ref_palette_red600</color>
    <color name="google_scrim">#99000000</color>
    <color name="google_transparent">#00000000</color>
    <color name="google_white">@color/gm_ref_palette_white</color>
    <color name="highlighted_text_material_dark">#6680cbc4</color>
    <color name="highlighted_text_material_light">#66009688</color>
    <color name="material_deep_teal_200">#ff80cbc4</color>
    <color name="material_deep_teal_500">#ff008577</color>
    <color name="material_grey_100">#fff5f5f5</color>
    <color name="material_grey_50">#fffafafa</color>
    <color name="material_grey_600">#ff757575</color>
    <color name="material_grey_800">#ff424242</color>
    <color name="material_grey_850">#ff303030</color>
    <color name="material_grey_900">#ff212121</color>
    <color name="mtrl_scrim_color">#52000000</color>
    <color name="mtrl_textinput_default_box_stroke_color">#6b000000</color>
    <color name="mtrl_textinput_disabled_color">#1f000000</color>
    <color name="mtrl_textinput_focused_box_stroke_color">#00000000</color>
    <color name="mtrl_textinput_hovered_box_stroke_color">#de000000</color>
    <color name="primary_blue">@color/quantum_googblue500</color>
    <color name="primary_dark_material_dark">@android:color/black</color>
    <color name="primary_dark_material_light">@color/material_grey_600</color>
    <color name="primary_material_dark">@color/material_grey_900</color>
    <color name="primary_material_light">@color/material_grey_100</color>
    <color name="primary_text_default_material_dark">#ffffffff</color>
    <color name="primary_text_default_material_light">#de000000</color>
    <color name="primary_text_disabled_material_dark">#4dffffff</color>
    <color name="primary_text_disabled_material_light">#39000000</color>
    <color name="quantum_googblue500">#4285f4</color>
    <color name="ripple_material_dark">#33ffffff</color>
    <color name="ripple_material_light">#1f000000</color>
    <color name="secondary_text_default_material_dark">#b3ffffff</color>
    <color name="secondary_text_default_material_light">#8a000000</color>
    <color name="secondary_text_disabled_material_dark">#36ffffff</color>
    <color name="secondary_text_disabled_material_light">#24000000</color>
    <color name="switch_thumb_disabled_material_dark">#ff616161</color>
    <color name="switch_thumb_disabled_material_light">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_dark">#ffbdbdbd</color>
    <color name="switch_thumb_normal_material_light">#fff1f1f1</color>
    <color name="tooltip_background_dark">#e6616161</color>
    <color name="tooltip_background_light">#e6ffffff</color>
    <color name="transcription_intent_api_google_logo_tint">@color/google_grey700</color>
    <color name="transcription_voice_ime_color_bg">@color/google_grey50</color>
</resources>
