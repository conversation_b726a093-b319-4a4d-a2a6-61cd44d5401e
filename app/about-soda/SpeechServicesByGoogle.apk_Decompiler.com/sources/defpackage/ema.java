package defpackage;

import androidx.wear.ambient.AmbientMode;
import j$.util.Optional;
import j$.util.concurrent.atomic.DesugarAtomicLong;
import java.io.IOException;
import java.util.Locale;

/* renamed from: ema  reason: default package */
/* compiled from: PG */
public final class ema implements hls {
    final /* synthetic */ Object a;
    final /* synthetic */ Object b;
    private final /* synthetic */ int c;

    public ema(Object obj, Object obj2, int i) {
        this.c = i;
        this.a = obj2;
        this.b = obj;
    }

    /* JADX WARNING: type inference failed for: r2v12, types: [jix, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v39, types: [jix, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r11v28, types: [java.lang.Object, cto] */
    public final void a(Throwable th) {
        switch (this.c) {
            case 0:
                ((emi) ((emd) this.b).b.b()).b("FAILED_GETTING_STATUS_FUTURE", dyl.REASON_UNKNOWN.name(), ehf.a(((ehg) this.a).a).name());
                return;
            case 1:
                cyh.p("%s: Unable to create symlink structure, cleaning up symlinks...", "FileGroupManager");
                try {
                    Object obj = this.b;
                    cqx.K(((cvy) obj).a, ((cvy) obj).f, (csx) this.a, ((cvy) obj).m);
                    return;
                } catch (IOException unused) {
                    cyh.p("%s: Unable to clean up symlink structure after failure", "FileGroupManager");
                    return;
                }
            case 2:
                ((hdc) ((hdc) ((hdc) emd.a.h()).i(th)).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl$5", "onFailure", 403, "MonitoringLoggerImpl.java")).r("#audio# seamless handover timeout failed");
                return;
            case 3:
                ((hdc) ((hdc) ((hdc) emd.a.h()).i(th)).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl$6", "onFailure", 446, "MonitoringLoggerImpl.java")).u("#audio# logging audio route(%s) status failed", fbi.p((dzq) this.a));
                return;
            case 4:
                ((hdc) ((hdc) ((hdc) emd.a.h()).i(th)).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl$7", "onFailure", 465, "MonitoringLoggerImpl.java")).u("#audio# route(%s) disconnect status failed", fbi.p((dzq) this.a));
                return;
            case 5:
                ((emi) ((emd) this.b).b.b()).a("FAILED_ACQUIRING_ERROR_GETTING_STATUS_FUTURE", ehf.a(((ehg) this.a).a).name());
                return;
            case 6:
                ((hby) ((hby) etm.a.h().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl$4", "onFailure", 402, "AudioServiceImpl.java")).r("#audio# Failed to get StopListeningStatus.");
                htk l = eta.d.l();
                if (!l.b.B()) {
                    l.u();
                }
                Object obj2 = this.a;
                eta eta = (eta) l.b;
                obj2.getClass();
                eta.b = (ebp) obj2;
                eta.a |= 1;
                ebr ebr = ebr.c;
                if (!l.b.B()) {
                    l.u();
                }
                ? r2 = this.b;
                eta eta2 = (eta) l.b;
                ebr.getClass();
                eta2.c = ebr;
                eta2.a |= 2;
                r2.c((eta) l.r());
                this.b.b(th);
                return;
            case 7:
                ((ezl) this.b).d(this.a);
                return;
            case 8:
                ((hby) ((hby) ((hby) ffe.a.h()).i(th)).j("com/google/android/libraries/speech/transcription/recognition/dataservice/TranscriptionDataService$3", "onFailure", 443, "TranscriptionDataService.java")).C("Future [%s] FAILED for request-id %s", this.b, ((ffe) this.a).e);
                return;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                ((hby) ((hby) ((hby) fhf.a.g().h(hdg.a, "ModelManagerWrapper")).i(th)).j("com/google/android/libraries/speech/transcription/recognition/grpc/impl/ModelManagerWrapper$1", "onFailure", 116, "ModelManagerWrapper.java")).u("Can't get supported LP for %s", ((iai) this.b).name());
                ((AmbientMode.AmbientController) this.a).e();
                return;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                ((hby) ((hby) ((hby) fhf.a.g().h(hdg.a, "ModelManagerWrapper")).i(th)).j("com/google/android/libraries/speech/transcription/recognition/grpc/impl/ModelManagerWrapper$3", "onFailure", 217, "ModelManagerWrapper.java")).r("Can't download USM encoder LP.");
                return;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                ((hby) ((hby) ((hby) fjr.b.g()).i(th)).j("com/google/android/libraries/speech/transcription/voiceime/VoiceInputMethodDataService$4", "onFailure", 280, "VoiceInputMethodDataService.java")).r("Failed to load ProtoDataStore");
                return;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                ((hpk) this.a).c = true;
                ((ftc) this.b).a(itg.c(th), new irw());
                return;
            default:
                ((iuh) this.b).e(th);
                return;
        }
    }

    /* JADX WARNING: type inference failed for: r1v12, types: [jix, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r9v51, types: [jix, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r9v53, types: [java.lang.Object, cto] */
    /* JADX WARNING: type inference failed for: r1v19, types: [jix, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r9v61, types: [jix, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v80, types: [java.lang.Object, android.os.IBinder] */
    public final /* synthetic */ void b(Object obj) {
        int i = 1;
        switch (this.c) {
            case 0:
                dyn dyn = (dyn) obj;
                emi emi = (emi) ((emd) this.b).b.b();
                int c2 = dnk.c(dyn.b);
                if (c2 != 0) {
                    i = c2;
                }
                dyl b2 = dyl.b(dyn.c);
                if (b2 == null) {
                    b2 = dyl.REASON_UNKNOWN;
                }
                emi.b(dnk.b(i), b2.name(), ehf.a(((ehg) this.a).a).name());
                return;
            case 1:
                Void voidR = (Void) obj;
                return;
            case 2:
                ebp ebp = (ebp) obj;
                eah eah = ebp.b;
                if (eah == null) {
                    eah = eah.c;
                }
                if (eah.a == 1) {
                    eam b3 = eam.b(ebp.c);
                    if (b3 == null) {
                        b3 = eam.UNSET;
                    }
                    if (b3 == eam.SEAMLESS_HANDOVER_TIMEOUT) {
                        ((hdc) ((hdc) emd.a.f()).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl$5", "onSuccess", 393, "MonitoringLoggerImpl.java")).r("#audio# seamless handover timeout success");
                        ((fqy) ((emi) ((emd) this.b).b.b()).p.a()).b((String) ((emd) this.b).d.a(), ehf.a(((ehg) this.a).a).name());
                        return;
                    }
                    return;
                }
                return;
            case 3:
                emi emi2 = (emi) ((emd) this.b).b.b();
                Object t = fbi.t((dzq) this.a);
                eaf b4 = eaf.b(((dzo) obj).b);
                if (b4 == null) {
                    b4 = eaf.UNKNOWN_ROUTING_STATUS;
                }
                ((fqy) emi2.g.a()).b(t, b4.name());
                return;
            case 4:
                emi emi3 = (emi) ((emd) this.b).b.b();
                Object t2 = fbi.t((dzq) this.a);
                dzx b5 = dzx.b(((dzm) obj).b);
                if (b5 == null) {
                    b5 = dzx.UNKNOWN_DISCONNECT_REASON;
                }
                ((fqy) emi3.e.a()).b(t2, b5.name());
                return;
            case 5:
                emi emi4 = (emi) ((emd) this.b).b.b();
                int e = dnk.e(((dyk) obj).b);
                if (e != 0) {
                    i = e;
                }
                emi4.a(dnk.d(i), ehf.a(((ehg) this.a).a).name());
                return;
            case 6:
                ebr ebr = (ebr) obj;
                htk l = eta.d.l();
                if (!l.b.B()) {
                    l.u();
                }
                Object obj2 = this.a;
                htq htq = l.b;
                eta eta = (eta) htq;
                obj2.getClass();
                eta.b = (ebp) obj2;
                eta.a |= 1;
                if (!htq.B()) {
                    l.u();
                }
                ? r1 = this.b;
                eta eta2 = (eta) l.b;
                ebr.getClass();
                eta2.c = ebr;
                eta2.a |= 2;
                r1.c((eta) l.r());
                this.b.a();
                return;
            case 7:
                Void voidR2 = (Void) obj;
                ((ezl) this.b).d(this.a);
                return;
            case 8:
                Void voidR3 = (Void) obj;
                ((hby) ((hby) ffe.a.f()).j("com/google/android/libraries/speech/transcription/recognition/dataservice/TranscriptionDataService$3", "onSuccess", 438, "TranscriptionDataService.java")).C("Future [%s] SUCCESS for request-id %s", this.b, ((ffe) this.a).e);
                return;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fgb fgb = (fgb) obj;
                ((hby) ((hby) fhf.a.c().h(hdg.a, "ModelManagerWrapper")).j("com/google/android/libraries/speech/transcription/recognition/grpc/impl/ModelManagerWrapper$1", "onSuccess", 105, "ModelManagerWrapper.java")).H("Get supported LPs for %s: [%d]-[%d]-[%d]", ((iai) this.b).name(), Integer.valueOf(fgb.a.size()), Integer.valueOf(fgb.c.size()), Integer.valueOf(fgb.b.size()));
                AmbientMode.AmbientController ambientController = (AmbientMode.AmbientController) this.a;
                ambientController.a.c(fgb);
                ambientController.a.a();
                return;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                if (((Optional) obj).isPresent()) {
                    ((hby) ((hby) fhf.a.f().h(hdg.a, "ModelManagerWrapper")).j("com/google/android/libraries/speech/transcription/recognition/grpc/impl/ModelManagerWrapper$3", "onSuccess", 202, "ModelManagerWrapper.java")).r("USM encoder LP available.");
                    return;
                }
                ((hby) ((hby) fhf.a.f().h(hdg.a, "ModelManagerWrapper")).j("com/google/android/libraries/speech/transcription/recognition/grpc/impl/ModelManagerWrapper$3", "onSuccess", 204, "ModelManagerWrapper.java")).r("USM encoder LP not available, trigger download.");
                Object obj3 = this.b;
                Object obj4 = this.a;
                Locale forLanguageTag = Locale.forLanguageTag("ag-AG");
                Optional empty = Optional.empty();
                iai b6 = iai.b(((fgc) obj4).c);
                if (b6 == null) {
                    b6 = iai.UNKNOWN;
                }
                ((fhf) obj3).b.d("", forLanguageTag, true, empty, b6);
                return;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                DesugarAtomicLong.getAndUpdate(((fjr) this.a).e, new fjq((fke) obj));
                ((fjr) this.a).c((String) this.b, new fhd(5));
                return;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                ioy ioy = (ioy) obj;
                try {
                    Object obj5 = this.a;
                    ((hpk) obj5).b = ioy;
                    for (Runnable run : ((hpk) obj5).a) {
                        run.run();
                    }
                    return;
                } catch (Throwable th) {
                    a(th);
                    return;
                }
            default:
                ((iuh) this.b).f(this.a, (itg) obj);
                return;
        }
    }

    public ema(Object obj, Object obj2, int i, byte[] bArr) {
        this.c = i;
        this.b = obj;
        this.a = obj2;
    }

    public ema(Object obj, Object obj2, int i, char[] cArr) {
        this.c = i;
        this.b = obj2;
        this.a = obj;
    }
}
