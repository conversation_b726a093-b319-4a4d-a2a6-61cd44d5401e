package defpackage;

/* renamed from: cvh  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvh implements hko {
    public final /* synthetic */ cvy a;
    public final /* synthetic */ ctj b;
    public final /* synthetic */ String c;
    public final /* synthetic */ long d;
    public final /* synthetic */ csv e;
    public final /* synthetic */ csx f;
    public final /* synthetic */ int g;

    public /* synthetic */ cvh(cvy cvy, ctj ctj, String str, long j, csv csv, csx csx, int i) {
        this.a = cvy;
        this.b = ctj;
        this.c = str;
        this.d = j;
        this.e = csv;
        this.f = csx;
        this.g = i;
    }

    /* JADX WARNING: type inference failed for: r1v4, types: [java.lang.Object, cxa] */
    public final hme a(Object obj) {
        Void voidR = (Void) obj;
        htk l = ctl.h.l();
        ctf ctf = ctf.DOWNLOAD_COMPLETE;
        if (!l.b.B()) {
            l.u();
        }
        String str = this.c;
        htq htq = l.b;
        ctl ctl = (ctl) htq;
        ctl.c = ctf.h;
        ctl.a |= 2;
        if (!htq.B()) {
            l.u();
        }
        String valueOf = String.valueOf(str);
        htq htq2 = l.b;
        ctl ctl2 = (ctl) htq2;
        ctl2.a |= 1;
        ctl2.b = "android_shared_".concat(valueOf);
        if (!htq2.B()) {
            l.u();
        }
        htq htq3 = l.b;
        ctl ctl3 = (ctl) htq3;
        ctl3.a |= 4;
        ctl3.d = true;
        if (!htq3.B()) {
            l.u();
        }
        long j = this.d;
        htq htq4 = l.b;
        ctl ctl4 = (ctl) htq4;
        ctl4.a |= 8;
        ctl4.e = j;
        if (!htq4.B()) {
            l.u();
        }
        cvy cvy = this.a;
        int i = this.g;
        csx csx = this.f;
        csv csv = this.e;
        ctj ctj = this.b;
        ctl ctl5 = (ctl) l.b;
        str.getClass();
        ctl5.a |= 16;
        ctl5.f = str;
        return cvy.q(cvy.j.j.h(ctj, (ctl) l.r()), new cvt(cvy, csv, csx, i, j));
    }
}
