package defpackage;

import j$.util.Objects;

/* renamed from: cpa  reason: default package */
/* compiled from: PG */
final class cpa extends ThreadLocal {
    /* access modifiers changed from: protected */
    public final /* bridge */ /* synthetic */ Object initialValue() {
        boolean z;
        Thread currentThread = Thread.currentThread();
        synchronized (cpe.a) {
            Boolean bool = (Boolean) cpe.a.remove(currentThread);
            z = true;
            if (bool != null) {
                boolean booleanValue = bool.booleanValue();
                if (!booleanValue) {
                }
                return Boolean.valueOf(z);
            }
        }
        ThreadLocal threadLocal = cpb.a;
        if (!"Filter".equals(currentThread.getName())) {
            String name = currentThread.getName();
            if (!Objects.equals(name, "Instr: com.google.android.apps.common.testing.testrunner.Google3InstrumentationTestRunner") && (name == null || !name.startsWith("SimpleServer ConnectionThread "))) {
                z = false;
            }
        }
        return Boolean.valueOf(z);
    }
}
