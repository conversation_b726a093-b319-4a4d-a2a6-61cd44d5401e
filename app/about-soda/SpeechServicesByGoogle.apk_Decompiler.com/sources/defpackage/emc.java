package defpackage;

import com.google.android.apps.speech.tts.googletts.local.voicepack.ui.MultipleVoicesActivity;
import j$.util.Optional;
import j$.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/* renamed from: emc  reason: default package */
/* compiled from: PG */
public final class emc implements hls {
    public final /* synthetic */ Object a;
    final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    private final /* synthetic */ int d;

    public emc(eug eug, eeg eeg, ehg ehg, int i) {
        this.d = i;
        this.b = eeg;
        this.c = ehg;
        this.a = eug;
    }

    public final void a(Throwable th) {
        switch (this.d) {
            case 0:
                return;
            case 1:
                ((hby) ((hby) ((hby) btf.a.g()).i(th)).j("com/google/android/apps/speech/tts/googletts/local/voicepack/VoiceDataDownloader$3", "onFailure", 469, "VoiceDataDownloader.java")).r("Delete failed");
                return;
            case 2:
                if (((grh) this.a).f()) {
                    ((hby) ((hby) eug.a.h().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$10", "onFailure", 632, "AudioEventsHolderImpl.java")).C("#audio# cannot retrieve route(%s) disconnect status for audio client(token(%d))", fbi.p((dzq) this.b), ((grh) this.a).b());
                } else {
                    ((hby) ((hby) eug.a.h().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$10", "onFailure", 636, "AudioEventsHolderImpl.java")).u("#audio# cannot retrieve route(%s) disconnect status for hotword client", fbi.p((dzq) this.b));
                }
                Object obj = this.c;
                htk l = dzm.c.l();
                dzx dzx = dzx.FAILED_GETTING_DISCONNECT_REASON;
                if (!l.b.B()) {
                    l.u();
                }
                dzm dzm = (dzm) l.b;
                dzm.b = dzx.H;
                dzm.a = 1 | dzm.a;
                ((eug) obj).f(eug.a((dzm) l.r(), (grh) this.a, (dzq) this.b));
                return;
            case 3:
                ((hby) ((hby) eug.a.h().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$5", "onFailure", 406, "AudioEventsHolderImpl.java")).s("#audio# cannot retrieve audio focus session(token(%d)) release status", ((eeg) this.b).b);
                htk l2 = eax.h.l();
                dyn o = eki.o(6);
                if (!l2.b.B()) {
                    l2.u();
                }
                htq htq = l2.b;
                eax eax = (eax) htq;
                o.getClass();
                eax.c = o;
                eax.b = 6;
                Object obj2 = this.b;
                if (!htq.B()) {
                    l2.u();
                }
                htq htq2 = l2.b;
                eax eax2 = (eax) htq2;
                obj2.getClass();
                eax2.e = (eeg) obj2;
                eax2.a |= 2;
                Object obj3 = this.c;
                if (!htq2.B()) {
                    l2.u();
                }
                Object obj4 = this.a;
                eax eax3 = (eax) l2.b;
                obj3.getClass();
                eax3.f = (ehg) obj3;
                eax3.a |= 4;
                ((eug) obj4).f((eax) l2.r());
                return;
            case 4:
                ((hby) ((hby) eug.a.h().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$6", "onFailure", 443, "AudioEventsHolderImpl.java")).s("#audio# cannot retrieve audio focus session(token(%d)) acquire status", ((eeg) this.b).b);
                htk l3 = eax.h.l();
                dyk n = eki.n(6);
                if (!l3.b.B()) {
                    l3.u();
                }
                htq htq3 = l3.b;
                eax eax4 = (eax) htq3;
                n.getClass();
                eax4.c = n;
                eax4.b = 5;
                Object obj5 = this.b;
                if (!htq3.B()) {
                    l3.u();
                }
                htq htq4 = l3.b;
                eax eax5 = (eax) htq4;
                obj5.getClass();
                eax5.e = (eeg) obj5;
                eax5.a |= 2;
                Object obj6 = this.c;
                if (!htq4.B()) {
                    l3.u();
                }
                Object obj7 = this.a;
                eax eax6 = (eax) l3.b;
                obj6.getClass();
                eax6.f = (ehg) obj6;
                eax6.a |= 4;
                ((eug) obj7).f((eax) l3.r());
                return;
            case 5:
                if (((grh) this.a).f()) {
                    ((hby) ((hby) eug.a.h().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$9", "onFailure", 594, "AudioEventsHolderImpl.java")).C("#audio# cannot retrieve route(%s) update status for audio client(token(%d))", fbi.p((dzq) this.b), ((grh) this.a).b());
                } else {
                    ((hby) ((hby) eug.a.h().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$9", "onFailure", 598, "AudioEventsHolderImpl.java")).u("#audio# cannot retrieve route(%s) update status for hotword client", fbi.p((dzq) this.b));
                }
                ((eug) this.c).f(eug.b(eki.c(eaf.FAILED_GETTING_ROUTING_STATUS_FUTURE), (grh) this.a, (dzq) this.b));
                return;
            case 6:
                ((hby) ((hby) fag.a.f().h(hdg.a, "ModelManagerImpl")).j("com/google/android/libraries/speech/modelmanager/languagepack/ModelManagerImpl$1", "onFailure", 223, "ModelManagerImpl.java")).r("Error pack read.");
                ((fag) this.b).e.ifPresent(new bme(this, 18));
                fag.e((Optional) this.c);
                return;
            case 7:
                ((hby) ((hby) faj.a.b().h(hdg.a, "ScheduleDownloadHelper")).j("com/google/android/libraries/speech/modelmanager/languagepack/ScheduleDownloadHelper$1", "onFailure", 125, "ScheduleDownloadHelper.java")).u("#onFailure: %s", th.getMessage());
                ((faj) this.b).b.ifPresent(new fai(this, 1));
                faj.b((Optional) this.c, 3);
                return;
            default:
                ((ConcurrentHashMap) ((iul) this.c).b).remove(this.a, this.b);
                return;
        }
    }

    public final /* synthetic */ void b(Object obj) {
        int i = 1;
        switch (this.d) {
            case 0:
                dzo dzo = (dzo) obj;
                gsa gsa = (gsa) this.a;
                gsa.e();
                long a2 = gsa.a(TimeUnit.MILLISECONDS);
                ((fqv) ((emi) ((emd) this.c).b.b()).f.a()).b((double) a2, fbi.t((dzq) this.b));
                ((hdc) ((hdc) emd.a.f()).j("com/google/android/libraries/search/audio/logging/impl/MonitoringLoggerImpl$8", "onSuccess", 491, "MonitoringLoggerImpl.java")).B("#audio# audio routing(%s) took %d(ms)", fbi.p((dzq) this.b), a2);
                return;
            case 1:
                Void voidR = (Void) obj;
                ((hby) ((hby) btf.a.f()).j("com/google/android/apps/speech/tts/googletts/local/voicepack/VoiceDataDownloader$3", "onSuccess", 458, "VoiceDataDownloader.java")).u("Delete voice %s succeeded", this.a);
                synchronized (((btf) this.c).l) {
                    ((btf) this.c).l.remove(this.a);
                }
                buv buv = (buv) this.b;
                ((btj) buv.a).d();
                Object obj2 = buv.b;
                buv buv2 = (buv) obj2;
                ((hby) ((hby) MultipleVoicesActivity.k.f()).j("com/google/android/apps/speech/tts/googletts/local/voicepack/ui/MultipleVoicesActivity$2", "onPackDeleteSuccess", 364, "MultipleVoicesActivity.java")).u("User uninstalled %s", buv2.a);
                ((MultipleVoicesActivity) buv2.b).runOnUiThread(new bpu(obj2, 10));
                return;
            case 2:
                dzm dzm = (dzm) obj;
                if (((grh) this.a).f()) {
                    hby hby = (hby) ((hby) eug.a.f().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$10", "onSuccess", 618, "AudioEventsHolderImpl.java");
                    Object obj3 = this.b;
                    Object obj4 = this.a;
                    String p = fbi.p((dzq) obj3);
                    Object b2 = ((grh) obj4).b();
                    dzx b3 = dzx.b(dzm.b);
                    if (b3 == null) {
                        b3 = dzx.UNKNOWN_DISCONNECT_REASON;
                    }
                    hby.G("#audio# route(%s) for audio request client(token(%d)) disconnected(reason(%s))", p, b2, b3.name());
                } else {
                    hby hby2 = (hby) ((hby) eug.a.f().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$10", "onSuccess", 622, "AudioEventsHolderImpl.java");
                    String p2 = fbi.p((dzq) this.b);
                    dzx b4 = dzx.b(dzm.b);
                    if (b4 == null) {
                        b4 = dzx.UNKNOWN_DISCONNECT_REASON;
                    }
                    hby2.C("#audio# route(%s) for hotword client disconnected(reason(%s))", p2, b4.name());
                }
                ((eug) this.c).f(eug.a(dzm, (grh) this.a, (dzq) this.b));
                return;
            case 3:
                dyn dyn = (dyn) obj;
                hby hby3 = (hby) ((hby) eug.a.f().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$5", "onSuccess", 393, "AudioEventsHolderImpl.java");
                int i2 = ((eeg) this.b).b;
                int c2 = dnk.c(dyn.b);
                if (c2 != 0) {
                    i = c2;
                }
                hby3.x("#audio# audio focus session(token(%d)) release status(%s)", i2, dnk.b(i));
                Object obj5 = this.a;
                htk l = eax.h.l();
                if (!l.b.B()) {
                    l.u();
                }
                htq htq = l.b;
                eax eax = (eax) htq;
                dyn.getClass();
                eax.c = dyn;
                eax.b = 6;
                Object obj6 = this.b;
                if (!htq.B()) {
                    l.u();
                }
                htq htq2 = l.b;
                eax eax2 = (eax) htq2;
                obj6.getClass();
                eax2.e = (eeg) obj6;
                eax2.a |= 2;
                Object obj7 = this.c;
                if (!htq2.B()) {
                    l.u();
                }
                eax eax3 = (eax) l.b;
                obj7.getClass();
                eax3.f = (ehg) obj7;
                eax3.a |= 4;
                ((eug) obj5).f((eax) l.r());
                return;
            case 4:
                dyk dyk = (dyk) obj;
                hby hby4 = (hby) ((hby) eug.a.f().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$6", "onSuccess", 430, "AudioEventsHolderImpl.java");
                int i3 = ((eeg) this.b).b;
                int e = dnk.e(dyk.b);
                if (e != 0) {
                    i = e;
                }
                hby4.x("#audio# audio focus session(token(%d)) acquire status(%s)", i3, dnk.d(i));
                Object obj8 = this.a;
                htk l2 = eax.h.l();
                if (!l2.b.B()) {
                    l2.u();
                }
                htq htq3 = l2.b;
                eax eax4 = (eax) htq3;
                dyk.getClass();
                eax4.c = dyk;
                eax4.b = 5;
                Object obj9 = this.b;
                if (!htq3.B()) {
                    l2.u();
                }
                htq htq4 = l2.b;
                eax eax5 = (eax) htq4;
                obj9.getClass();
                eax5.e = (eeg) obj9;
                eax5.a |= 2;
                Object obj10 = this.c;
                if (!htq4.B()) {
                    l2.u();
                }
                eax eax6 = (eax) l2.b;
                obj10.getClass();
                eax6.f = (ehg) obj10;
                eax6.a |= 4;
                ((eug) obj8).f((eax) l2.r());
                return;
            case 5:
                dzo dzo2 = (dzo) obj;
                if (((grh) this.a).f()) {
                    hby hby5 = (hby) ((hby) eug.a.f().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$9", "onSuccess", 578, "AudioEventsHolderImpl.java");
                    Object obj11 = this.b;
                    Object obj12 = this.a;
                    String p3 = fbi.p((dzq) obj11);
                    Object b5 = ((grh) obj12).b();
                    eaf b6 = eaf.b(dzo2.b);
                    if (b6 == null) {
                        b6 = eaf.UNKNOWN_ROUTING_STATUS;
                    }
                    hby5.G("#audio# route(%s) for audio request client(token(%d)) updated with status(%s)", p3, b5, b6.name());
                } else {
                    hby hby6 = (hby) ((hby) eug.a.f().h(hdg.a, "ALT.AudioEventsHolder")).j("com/google/android/libraries/search/audio/state/events/impl/AudioEventsHolderImpl$9", "onSuccess", 584, "AudioEventsHolderImpl.java");
                    String p4 = fbi.p((dzq) this.b);
                    eaf b7 = eaf.b(dzo2.b);
                    if (b7 == null) {
                        b7 = eaf.UNKNOWN_ROUTING_STATUS;
                    }
                    hby6.C("#audio# route(%s) for hotword client updated with status(%s)", p4, b7.name());
                }
                ((eug) this.c).f(eug.b(dzo2, (grh) this.a, (dzq) this.b));
                return;
            case 6:
                Void voidR2 = (Void) obj;
                ((hby) ((hby) fag.a.f().h(hdg.a, "ModelManagerImpl")).j("com/google/android/libraries/speech/modelmanager/languagepack/ModelManagerImpl$1", "onSuccess", 218, "ModelManagerImpl.java")).r("Success pack read.");
                return;
            case 7:
                bql b8 = bql.b(((bqm) obj).b);
                if (b8 == null) {
                    b8 = bql.ENQUEUE_STATUS_FAIL;
                }
                if (b8 == bql.ENQUEUE_STATUS_SUCCESS) {
                    ((faj) this.b).b.ifPresent(new fai(this, 0));
                    return;
                }
                ((faj) this.b).b.ifPresent(new fai(this, 2));
                int ordinal = b8.ordinal();
                if (ordinal == 0) {
                    faj.b((Optional) this.c, 3);
                    return;
                } else if (ordinal == 1) {
                    ((Optional) this.c).ifPresent(new ezx(8));
                    return;
                } else if (ordinal == 3) {
                    faj.b((Optional) this.c, 13);
                    return;
                } else if (ordinal == 4) {
                    ((Optional) this.c).ifPresent(new ezx(7));
                    return;
                } else {
                    return;
                }
            default:
                itg itg = (itg) obj;
                return;
        }
    }

    public emc(Object obj, Object obj2, Object obj3, int i) {
        this.d = i;
        this.a = obj2;
        this.b = obj3;
        this.c = obj;
    }

    public emc(Object obj, String str, Optional optional, int i) {
        this.d = i;
        this.a = str;
        this.c = optional;
        this.b = obj;
    }
}
