package defpackage;

/* renamed from: ekr  reason: default package */
/* compiled from: PG */
public final class ekr {
    public final boolean a;
    public final int b;

    public ekr(boolean z, int i) {
        this.a = z;
        this.b = i;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof ekr)) {
            return false;
        }
        ekr ekr = (ekr) obj;
        if (this.a == ekr.a && this.b == ekr.b) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (a.f(this.a) * 31) + this.b;
    }

    public final String toString() {
        return "RefOpResult(existing=" + this.a + ", count=" + this.b + ")";
    }
}
