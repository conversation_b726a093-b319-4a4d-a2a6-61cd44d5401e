package defpackage;

/* renamed from: csk  reason: default package */
/* compiled from: PG */
public final class csk {
    public final String a;
    public final grh b;
    public final grh c;
    public final grh d;
    public final grh e;
    public final grh f;
    public final grh g;
    public final int h;
    public final boolean i;
    public final int j;
    private final grh k;

    public csk() {
        throw null;
    }

    public static csj a() {
        csj csj = new csj((byte[]) null);
        csj.c(0);
        csj.g = 2;
        byte b2 = csj.f;
        csj.e = true;
        csj.f = (byte) (b2 | 6);
        return csj;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof csk) {
            csk csk = (csk) obj;
            if (this.a.equals(csk.a) && this.b.equals(csk.b) && this.c.equals(csk.c) && this.d.equals(csk.d) && this.e.equals(csk.e) && this.k.equals(csk.k) && this.f.equals(csk.f) && this.g.equals(csk.g) && this.h == csk.h) {
                int i2 = this.j;
                int i3 = csk.j;
                if (i2 == 0) {
                    throw null;
                } else if (i2 == i3 && this.i == csk.i) {
                    return true;
                } else {
                    return false;
                }
            }
        }
        return false;
    }

    public final int hashCode() {
        int i2;
        int hashCode = ((((((((((((((this.a.hashCode() ^ 1000003) * 1000003) ^ 2040732332) * 1000003) ^ 2040732332) * 1000003) ^ this.d.hashCode()) * 1000003) ^ this.e.hashCode()) * 1000003) ^ 2040732332) * 1000003) ^ this.f.hashCode()) * 1000003) ^ this.g.hashCode();
        int i3 = this.j;
        a.I(i3);
        if (true != this.i) {
            i2 = 1237;
        } else {
            i2 = 1231;
        }
        return (((((((hashCode * 1000003) ^ this.h) * 1000003) ^ i3) * 1000003) ^ 1237) * 1000003) ^ i2;
    }

    public final String toString() {
        String str;
        int i2 = this.j;
        grh grh = this.g;
        grh grh2 = this.f;
        grh grh3 = this.k;
        grh grh4 = this.e;
        grh grh5 = this.d;
        grh grh6 = this.c;
        String valueOf = String.valueOf(this.b);
        String valueOf2 = String.valueOf(grh6);
        String valueOf3 = String.valueOf(grh5);
        String valueOf4 = String.valueOf(grh4);
        String valueOf5 = String.valueOf(grh3);
        String valueOf6 = String.valueOf(grh2);
        String valueOf7 = String.valueOf(grh);
        if (i2 == 1) {
            str = "NONE";
        } else if (i2 != 2) {
            str = "null";
        } else {
            str = "ALL";
        }
        int i3 = this.h;
        String str2 = this.a;
        boolean z = this.i;
        return "DownloadFileGroupRequest{groupName=" + str2 + ", accountOptional=" + valueOf + ", variantIdOptional=" + valueOf2 + ", contentTitleOptional=" + valueOf3 + ", contentTextOptional=" + valueOf4 + ", contentIntentOptional=" + valueOf5 + ", downloadConditionsOptional=" + valueOf6 + ", listenerOptional=" + valueOf7 + ", groupSizeBytes=" + i3 + ", showNotifications=" + str + ", preserveZipDirectories=false, verifyIsolatedStructure=" + z + "}";
    }

    public csk(String str, grh grh, grh grh2, grh grh3, grh grh4, grh grh5, grh grh6, grh grh7, int i2, int i3, boolean z) {
        this.a = str;
        this.b = grh;
        this.c = grh2;
        this.d = grh3;
        this.e = grh4;
        this.k = grh5;
        this.f = grh6;
        this.g = grh7;
        this.h = i2;
        this.j = i3;
        this.i = z;
    }
}
