package defpackage;

import java.util.concurrent.TimeUnit;

/* renamed from: eun  reason: default package */
/* compiled from: PG */
public final class eun implements eui {
    public final ebd a;
    public final long b;
    public hsq c = hsq.b;
    public final jpp d = new jpp(false, jpt.a);
    final /* synthetic */ euo e;

    public eun(euo euo, ebd ebd, long j) {
        jnu.e(ebd, "audioMetadata");
        this.e = euo;
        this.a = ebd;
        this.b = j;
    }

    public final hme a() {
        if (this.d.b()) {
            return hfc.I();
        }
        euo euo = this.e;
        return euo.f.o(new egk(this, euo, (jlr) null, 11));
    }

    public final /* synthetic */ hme b(dyc dyc) {
        return esx.p(this, dyc);
    }

    public final hme c(hsq hsq) {
        jnu.e(hsq, "bytes");
        if (d()) {
            return hfc.I();
        }
        euo euo = this.e;
        return euo.f.o(new egk(this, hsq, (jlr) null, 12));
    }

    public final boolean d() {
        if (!this.d.b()) {
            return false;
        }
        ((hby) ((hby) euo.a.h().h(hdg.a, "ALT.SingleRecordStore")).g(1, TimeUnit.SECONDS).j("com/google/android/libraries/search/audio/store/memory/SingleAudioRecordStore$Writer", "maybeSkipWriteIfClosed", 79, "SingleAudioRecordStore.kt")).t("#audio# write called after close, skipping... AudioRecordId: %d", this.b);
        return true;
    }
}
