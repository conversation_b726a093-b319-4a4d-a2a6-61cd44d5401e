package defpackage;

import android.os.Debug;
import j$.util.concurrent.ThreadLocalRandom;
import java.util.ArrayList;
import java.util.Collections;

/* renamed from: cpk  reason: default package */
/* compiled from: PG */
public final class cpk {
    public static final /* synthetic */ int d = 0;
    private static final gsb e = fvf.as(new bps(7));
    public final grh a;
    public final boolean b;
    public final hmi c;

    public cpk(grh grh, grh grh2, hmi hmi) {
        this.a = grh;
        this.b = ((Boolean) grh2.d(false)).booleanValue();
        this.c = hmi;
    }

    public static void a(cpl cpl, ArrayList arrayList, RuntimeException runtimeException) {
        cou cou;
        if (!Debug.isDebuggerConnected()) {
            if (arrayList.size() > 20) {
                for (int i = 0; i < 20; i++) {
                    Collections.swap(arrayList, i, ThreadLocalRandom.current().nextInt(arrayList.size() - i) + i);
                }
            }
            for (Thread thread : arrayList.subList(0, Math.min(arrayList.size(), 20))) {
                RuntimeException b2 = gol.b(thread);
                if (b2.getStackTrace().length > 0) {
                    cou = new cou(thread, b2);
                } else {
                    cou = new cou(thread);
                }
                runtimeException.addSuppressed(cou);
            }
            int ordinal = cpl.ordinal();
            if (ordinal == 0) {
                ((hby) ((hby) ((hby) ((hca) e.a()).g()).i(runtimeException)).j("com/google/android/libraries/concurrent/monitoring/ThreadMonitoring", "reportUnhealthyThreadPool", 410, "ThreadMonitoring.java")).q();
            } else if (ordinal == 1) {
                fnk.e(new cmp(runtimeException, 7));
            }
        }
    }

    static /* bridge */ /* synthetic */ boolean b() {
        if (ThreadLocalRandom.current().nextInt(1000) <= 0) {
            return true;
        }
        return false;
    }
}
