package defpackage;

/* renamed from: bex  reason: default package */
/* compiled from: PG */
public final class bex {
    public final boolean a;
    public final boolean b;
    public final boolean c;
    public final boolean d;

    public bex(boolean z, boolean z2, boolean z3, boolean z4) {
        this.a = z;
        this.b = z2;
        this.c = z3;
        this.d = z4;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof bex)) {
            return false;
        }
        bex bex = (bex) obj;
        if (this.a == bex.a && this.b == bex.b && this.c == bex.c && this.d == bex.d) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int f = a.f(this.a);
        boolean z = this.d;
        return (((((f * 31) + a.f(this.b)) * 31) + a.f(this.c)) * 31) + a.f(z);
    }

    public final String toString() {
        return "NetworkState(isConnected=" + this.a + ", isValidated=" + this.b + ", isMetered=" + this.c + ", isNotRoaming=" + this.d + ')';
    }
}
