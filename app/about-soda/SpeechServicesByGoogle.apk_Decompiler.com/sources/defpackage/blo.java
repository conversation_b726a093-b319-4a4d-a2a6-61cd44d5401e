package defpackage;

import android.car.drivingstate.CarUxRestrictions;

/* renamed from: blo  reason: default package */
/* compiled from: PG */
final class blo implements bny {
    final /* synthetic */ blp a;

    public blo(blp blp) {
        this.a = blp;
    }

    public final void a(CarUxRestrictions carUxRestrictions) {
        int i;
        kc g = this.a.a.g();
        if (g instanceof bli) {
            if ((carUxRestrictions.getActiveRestrictions() & 32) != 0) {
                i = carUxRestrictions.getMaxCumulativeContentItems();
            } else {
                i = -1;
            }
            int itemCount = g.getItemCount();
            ((bli) g).a = i;
            int itemCount2 = g.getItemCount();
            if (itemCount2 == itemCount) {
                return;
            }
            if (itemCount2 < itemCount) {
                g.notifyItemRangeRemoved(itemCount2, itemCount - itemCount2);
            } else {
                g.notifyItemRangeInserted(itemCount, itemCount2 - itemCount);
            }
        }
    }
}
