package defpackage;

/* renamed from: ewc  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ewc implements gsb {
    public final /* synthetic */ bmu a;
    private final /* synthetic */ int b;

    public /* synthetic */ ewc(bmu bmu, int i) {
        this.b = i;
        this.a = bmu;
    }

    public final Object a() {
        switch (this.b) {
            case 0:
                fqv c = ((frc) this.a.a).c("/client_streamz/android_gsa/tng_ma/conv_blocker_calculation_latency", new fqx("app_version", String.class));
                c.c();
                return c;
            case 1:
                fqy g = ((frc) this.a.a).g("/client_streamz/android_gsa/tng_ma/app_embedding_outcome", new fqx("outcome", String.class), new fqx("intent", String.class));
                g.c();
                return g;
            case 2:
                fqy g2 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/context_debug/impression_count", new fqx("url", String.class), new fqx("trigger_source", Integer.class));
                g2.c();
                return g2;
            case 3:
                fqv c2 = ((frc) this.a.a).c("/client_streamz/android_gsa/tng_ma/fetch_sbcp_session_token_latency", new fqx("app_version", String.class));
                c2.c();
                return c2;
            case 4:
                fqy g3 = ((frc) this.a.a).g("/client_streamz/android_gsa/tng_ma/growth_data/l28_count", new fqx("app_version", String.class), new fqx("trigger_type", String.class));
                g3.c();
                return g3;
            case 5:
                fqy g4 = ((frc) this.a.a).g("/client_streamz/android_gsa/tng_ma/initial_data_download_finished", new fqx("app_version", String.class), new fqx("download_finished_status", String.class), new fqx("download_finished_error_code", String.class));
                g4.c();
                return g4;
            case 6:
                fqv c3 = ((frc) this.a.a).c("/client_streamz/android_gsa/tng_ma/initial_data_download_latency", new fqx("app_version", String.class), new fqx("download_finished_status", String.class));
                c3.c();
                return c3;
            case 7:
                fqy g5 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/verticals/memory/query_rewrites_corpus_update/started_count", new fqx[0]);
                g5.c();
                return g5;
            case 8:
                fqy g6 = ((frc) this.a.a).g("/client_streamz/android_gsa/tng_ma/initial_data_download_started", new fqx("app_version", String.class), new fqx("download_start_type", String.class));
                g6.c();
                return g6;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                fqy g7 = ((frc) this.a.a).g("/client_streamz/android_gsa/tng_ma/input_translate_chip_prioritize_timeout", new fqx("app_version", String.class), new fqx("is_timed_out", Boolean.class));
                g7.c();
                return g7;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                fqy g8 = ((frc) this.a.a).g("/client_streamz/android_gsa/tng_ma/installed_app_cache_for_package", new fqx("application_build", String.class), new fqx("package_name", String.class), new fqx("action", String.class));
                g8.c();
                return g8;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                fqy g9 = ((frc) this.a.a).g("/client_streamz/android_gsa/tng_ma/installed_app_change_listener_interactor", new fqx("application_build", String.class), new fqx("action", String.class));
                g9.c();
                return g9;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                fqy g10 = ((frc) this.a.a).g("/client_streamz/android_gsa/tng_ma/installed_app_recache_reason", new fqx("application_build", String.class), new fqx("reason", String.class));
                g10.c();
                return g10;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                fqv c4 = ((frc) this.a.a).c("/client_streamz/android_gsa/tng_ma/installed_apps", new fqx("application_build", String.class));
                c4.c();
                return c4;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                fqy g11 = ((frc) this.a.a).g("/client_streamz/android_gsa/assistant/context_launch/compatibility_count", new fqx("method", String.class), new fqx("expected", Boolean.class), new fqx("received", Boolean.class));
                g11.c();
                return g11;
            case 15:
                fqv c5 = ((frc) this.a.a).c("/client_streamz/android_gsa/tng_ma/invocation_data/invocation_data_store_size", new fqx("app_version", String.class));
                c5.c();
                return c5;
            case 16:
                fqy g12 = ((frc) this.a.a).g("/client_streamz/android_gsa/tng_ma/invocation_data/invocation_event_count", new fqx("app_version", String.class), new fqx("event_type", String.class), new fqx("trigger_type", String.class));
                g12.c();
                return g12;
            case 17:
                fqy g13 = ((frc) this.a.a).g("/client_streamz/android_gsa/tng_ma/invocation_handover_finished", new fqx("app_version", String.class), new fqx("status", String.class), new fqx("timeout_duration_in_seconds", Integer.class));
                g13.c();
                return g13;
            case 18:
                fqv c6 = ((frc) this.a.a).c("/client_streamz/android_gsa/assistant/verticals/memory/reconciliation/affected_entities", new fqx("trigger", String.class));
                c6.c();
                return c6;
            case 19:
                fqv c7 = ((frc) this.a.a).c("/client_streamz/android_gsa/tng_ma/invocation_handover_latency", new fqx("app_version", String.class), new fqx("status", String.class), new fqx("timeout_duration_in_seconds", Integer.class));
                c7.c();
                return c7;
            default:
                fqv c8 = ((frc) this.a.a).c("/client_streamz/android_gsa/tng_ma/show_temporary_model_download_prompt_latency", new fqx("app_version", String.class));
                c8.c();
                return c8;
        }
    }
}
