package defpackage;

/* renamed from: epc  reason: default package */
/* compiled from: PG */
public final class epc {
    public final enr a;
    public final epb b;
    public final epg c;
    private final epp d;

    public epc(enr enr, epb epb, epp epp, epg epg) {
        this.a = enr;
        this.b = epb;
        this.d = epp;
        this.c = epg;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof epc)) {
            return false;
        }
        epc epc = (epc) obj;
        if (jnu.i(this.a, epc.a) && jnu.i(this.b, epc.b) && jnu.i(this.d, epc.d) && jnu.i(this.c, epc.c)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (((((this.a.hashCode() * 31) + this.b.hashCode()) * 31) + this.d.hashCode()) * 31) + this.c.hashCode();
    }

    public final String toString() {
        return "AudioSourceData(sourceAccessor=" + this.a + ", client=" + this.b + ", route=" + this.d + ", session=" + this.c + ")";
    }
}
