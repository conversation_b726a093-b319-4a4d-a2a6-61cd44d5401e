package defpackage;

/* renamed from: eoq  reason: default package */
/* compiled from: PG */
public final class eoq extends jnv implements jna {
    final /* synthetic */ int a;
    final /* synthetic */ Object b;
    private final /* synthetic */ int c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eoq(int i, dyt dyt, int i2) {
        super(1);
        this.c = i2;
        this.a = i;
        this.b = dyt;
    }

    public final /* synthetic */ Object a(Object obj) {
        int i = this.c;
        if (i == 0) {
            eam eam = (eam) obj;
            jnu.e(eam, "it");
            ((eor) this.b).a(this.a, eam, true);
            return jkd.a;
        } else if (i != 1) {
            eam eam2 = (eam) obj;
            jnu.e(eam2, "it");
            ((eor) this.b).a(this.a, eam2, true);
            return jkd.a;
        } else {
            dyu dyu = (dyu) obj;
            jnu.e(dyu, "input");
            if ((dyu.a & 1) == 0) {
                return dyu;
            }
            int i2 = this.a;
            Object obj2 = this.b;
            htk htk = (htk) dyu.C(5);
            htk.x(dyu);
            jnu.d(htk, "toBuilder(...)");
            dlv d = jnu.e((htm) htk, "builder");
            d.d(dyu.b + ejq.c(i2, (dyt) obj2));
            return d.c();
        }
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eoq(eor eor, int i, int i2) {
        super(1);
        this.c = i2;
        this.b = eor;
        this.a = i;
    }
}
