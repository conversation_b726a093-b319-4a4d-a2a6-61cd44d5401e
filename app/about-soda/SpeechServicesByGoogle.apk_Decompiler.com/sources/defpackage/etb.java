package defpackage;

/* renamed from: etb  reason: default package */
/* compiled from: PG */
public final class etb extends htq implements hvb {
    public static final etb e;
    private static volatile hvh f;
    public int a;
    public int b = 0;
    public Object c;
    public dze d;

    static {
        etb etb = new etb();
        e = etb;
        htq.z(etb.class, etb);
    }

    private etb() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(e, "\u0004\u0003\u0001\u0001\u0001\u0004\u0003\u0000\u0000\u0000\u0001ဉ\u0000\u0002<\u0000\u0004<\u0000", new Object[]{"c", "b", "a", "d", ebf.class, dzl.class});
        } else if (i2 == 3) {
            return new etb();
        } else {
            if (i2 == 4) {
                return new htk((htq) e);
            }
            if (i2 == 5) {
                return e;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = f;
            if (hvh == null) {
                synchronized (etb.class) {
                    hvh = f;
                    if (hvh == null) {
                        hvh = new htl(e);
                        f = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
