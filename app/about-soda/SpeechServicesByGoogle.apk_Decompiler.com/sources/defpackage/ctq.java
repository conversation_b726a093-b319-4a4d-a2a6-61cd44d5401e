package defpackage;

/* renamed from: ctq  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ctq implements hko {
    public final /* synthetic */ cuf a;
    public final /* synthetic */ boolean b;
    private final /* synthetic */ int c;

    public /* synthetic */ ctq(cuf cuf, boolean z, int i) {
        this.c = i;
        this.a = cuf;
        this.b = z;
    }

    public final hme a(Object obj) {
        if (this.c != 0) {
            Void voidR = (Void) obj;
            cuf cuf = this.a;
            return cuf.c.c(this.b, cuf.g);
        }
        Void voidR2 = (Void) obj;
        cuf cuf2 = this.a;
        return cuf2.c.c(this.b, cuf2.g);
    }
}
