package defpackage;

import android.content.Context;
import android.content.res.TypedArray;
import android.os.Build;
import android.os.Parcelable;
import android.support.v7.widget.GridLayoutManager;
import android.support.v7.widget.LinearLayoutManager;
import android.support.v7.widget.RecyclerView;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import com.android.car.ui.recyclerview.CarUiRecyclerView;
import com.android.car.ui.recyclerview.CarUiRecyclerViewContainer;
import com.android.car.ui.recyclerview.DefaultScrollBar;
import com.google.android.tts.R;
import j$.util.Objects;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

/* renamed from: blp  reason: default package */
/* compiled from: PG */
public final class blp extends FrameLayout implements CarUiRecyclerView, boe, bkt {
    public static final /* synthetic */ int d = 0;
    private static final Class[] e;
    public final RecyclerView a;
    public final Set b = new HashSet();
    public DefaultScrollBar c;
    private kc f;
    private final bny g = new blo(this);
    private final bnz h;
    private final boolean i;
    private String j;
    private final int k;
    private final int l;
    private final bmc m;
    private final ki n;
    private int o;
    private final boolean p;
    private final boolean q;
    private bld r;

    static {
        Class cls = Integer.TYPE;
        e = new Class[]{Context.class, AttributeSet.class, cls, cls};
    }

    public blp(Context context, AttributeSet attributeSet) {
        super(context, attributeSet, 0);
        new ArrayList();
        this.h = bnz.b(context);
        TypedArray obtainStyledAttributes = context.obtainStyledAttributes(attributeSet, bjo.c, 0, 0);
        boolean z = context.getResources().getBoolean(R.bool.car_ui_scrollbar_enable);
        this.i = z;
        int i2 = obtainStyledAttributes.getInt(1, 2);
        int i3 = R.layout.car_ui_recycler_view_no_scrollbar;
        if (z) {
            if (i2 == 0) {
                i3 = R.layout.car_ui_recycler_view_small;
            } else if (i2 == 1) {
                i3 = R.layout.car_ui_recycler_view_medium;
            } else if (i2 == 2) {
                i3 = R.layout.car_ui_recycler_view;
            }
        }
        View inflate = LayoutInflater.from(context).inflate(i3, this, true);
        ViewGroup viewGroup = (ViewGroup) bnv.i(inflate, R.id.car_ui_recycler_view);
        if (viewGroup instanceof CarUiRecyclerViewContainer) {
            this.a = (RecyclerView) viewGroup.getChildAt(0);
        } else {
            this.a = (RecyclerView) viewGroup;
        }
        if (Build.VERSION.SDK_INT <= 32 && Build.VERSION.SDK_INT >= 29) {
            this.a.setVerticalScrollBarEnabled(isVerticalScrollBarEnabled());
            setVerticalScrollBarEnabled(false);
            this.a.setHorizontalScrollBarEnabled(isHorizontalScrollBarEnabled());
            setHorizontalScrollBarEnabled(false);
            this.a.setVerticalScrollbarThumbDrawable(getVerticalScrollbarThumbDrawable());
            this.a.setHorizontalScrollbarThumbDrawable(getHorizontalScrollbarThumbDrawable());
            this.a.setVerticalScrollbarTrackDrawable(getVerticalScrollbarTrackDrawable());
            this.a.setHorizontalScrollbarTrackDrawable(getHorizontalScrollbarTrackDrawable());
            this.a.setVerticalScrollbarPosition(getVerticalScrollbarPosition());
            this.a.setScrollBarSize(getScrollBarSize());
            this.a.setScrollBarStyle(getScrollBarStyle());
            if (isVerticalFadingEdgeEnabled()) {
                this.a.setVerticalFadingEdgeEnabled(true);
                this.a.setFadingEdgeLength(getVerticalFadingEdgeLength());
                setVerticalFadingEdgeEnabled(false);
                setFadingEdgeLength(0);
            }
            if (isHorizontalFadingEdgeEnabled()) {
                this.a.setHorizontalFadingEdgeEnabled(true);
                this.a.setFadingEdgeLength(getHorizontalFadingEdgeLength());
                setHorizontalFadingEdgeEnabled(false);
                setFadingEdgeLength(0);
            }
        }
        d(this.a, obtainStyledAttributes.getBoolean(7, false), obtainStyledAttributes.getInt(0, 1));
        this.k = context.getResources().getDimensionPixelSize(R.dimen.car_ui_scrollbar_padding_top);
        this.l = context.getResources().getDimensionPixelSize(R.dimen.car_ui_scrollbar_padding_bottom);
        int i4 = obtainStyledAttributes.getInt(4, 0);
        this.o = obtainStyledAttributes.getInt(5, 2);
        this.q = obtainStyledAttributes.getBoolean(2, false);
        this.n = new bmd(context.getDrawable(R.drawable.car_ui_recyclerview_divider));
        this.m = new bmc(context.getDrawable(R.drawable.car_ui_divider), context.getDrawable(R.drawable.car_ui_divider), this.o);
        this.p = true;
        this.a.setClipToPadding(false);
        String string = obtainStyledAttributes.getString(3);
        if (!TextUtils.isEmpty(string)) {
            f(context, string, attributeSet);
        } else if (i4 == 1) {
            getContext();
            b(new GridLayoutManager(this.o));
        } else {
            getContext();
            b(new LinearLayoutManager());
        }
        obtainStyledAttributes.recycle();
        if (z) {
            this.a.setVerticalScrollBarEnabled(false);
            this.a.setHorizontalScrollBarEnabled(false);
            this.j = context.getResources().getString(R.string.car_ui_scrollbar_component);
            c(context, bnv.i(inflate, R.id.car_ui_scroll_bar));
        }
    }

    private final void c(Context context, View view) {
        Class cls;
        try {
            if (!TextUtils.isEmpty(this.j)) {
                cls = getContext().getClassLoader().loadClass(this.j);
            } else {
                cls = DefaultScrollBar.class;
            }
            try {
                Constructor declaredConstructor = cls.getDeclaredConstructor((Class[]) null);
                declaredConstructor.setAccessible(true);
                DefaultScrollBar defaultScrollBar = (DefaultScrollBar) declaredConstructor.newInstance((Object[]) null);
                this.c = defaultScrollBar;
                defaultScrollBar.initialize(context, this.a, view);
                e(this.k + getPaddingTop(), this.l + getPaddingBottom());
            } catch (ReflectiveOperationException e2) {
                throw new IllegalArgumentException("Error creating scroll bar component: ".concat(String.valueOf(this.j)), e2);
            }
        } catch (ReflectiveOperationException e3) {
            throw new IllegalArgumentException("Error loading scroll bar component: ".concat(String.valueOf(this.j)), e3);
        }
    }

    private final void d(ViewGroup viewGroup, boolean z, int i2) {
        bll bll;
        String str;
        if (z) {
            if (i2 != 1) {
                str = "com.android.car.ui.utils.HORIZONTALLY_SCROLLABLE";
            } else {
                str = "com.android.car.ui.utils.VERTICALLY_SCROLLABLE";
            }
            viewGroup.setContentDescription(str);
        }
        bmw bmw = null;
        if (z) {
            bll = new bll(viewGroup);
        } else {
            bll = null;
        }
        viewGroup.setOnGenericMotionListener(bll);
        viewGroup.setFocusable(z);
        viewGroup.setDescendantFocusability(131072);
        viewGroup.setDefaultFocusHighlightEnabled(false);
        if (z) {
            bmw = new bmw(this, 1);
        }
        viewGroup.setOnFocusChangeListener(bmw);
        if (!z) {
            viewGroup.setContentDescription("com.android.car.ui.utils.ROTARY_CONTAINER");
        }
    }

    private final void e(int i2, int i3) {
        DefaultScrollBar defaultScrollBar;
        if (this.i && (defaultScrollBar = this.c) != null) {
            defaultScrollBar.setPadding(i2, i3);
        }
    }

    private final void f(Context context, String str, AttributeSet attributeSet) {
        ClassLoader classLoader;
        Object[] objArr;
        Constructor<? extends U> constructor;
        if (str != null) {
            String trim = str.trim();
            if (!trim.isEmpty()) {
                if (trim.charAt(0) == '.') {
                    trim = String.valueOf(context.getPackageName()).concat(String.valueOf(trim));
                } else if (!trim.contains(".")) {
                    trim = RecyclerView.class.getPackage().getName() + "." + trim;
                }
                try {
                    if (isInEditMode()) {
                        classLoader = getClass().getClassLoader();
                    } else {
                        classLoader = context.getClassLoader();
                    }
                    Class<? extends U> asSubclass = Class.forName(trim, false, classLoader).asSubclass(kl.class);
                    try {
                        constructor = asSubclass.getConstructor(e);
                        objArr = new Object[]{context, attributeSet, 0, 0};
                    } catch (NoSuchMethodException e2) {
                        objArr = null;
                        constructor = asSubclass.getConstructor((Class[]) null);
                    }
                    constructor.setAccessible(true);
                    b((kl) constructor.newInstance(objArr));
                } catch (NoSuchMethodException e3) {
                    e3.initCause(e2);
                    throw new IllegalStateException(attributeSet.getPositionDescription() + ": Error creating LayoutManager " + trim, e3);
                } catch (ClassNotFoundException e4) {
                    throw new IllegalStateException(attributeSet.getPositionDescription() + ": Unable to find LayoutManager " + trim, e4);
                } catch (InvocationTargetException e5) {
                    throw new IllegalStateException(attributeSet.getPositionDescription() + ": Could not instantiate the LayoutManager: " + trim, e5);
                } catch (InstantiationException e6) {
                    throw new IllegalStateException(attributeSet.getPositionDescription() + ": Could not instantiate the LayoutManager: " + trim, e6);
                } catch (IllegalAccessException e7) {
                    throw new IllegalStateException(attributeSet.getPositionDescription() + ": Cannot access non-public constructor " + trim, e7);
                } catch (ClassCastException e8) {
                    throw new IllegalStateException(attributeSet.getPositionDescription() + ": Class is not a LayoutManager " + trim, e8);
                }
            }
        }
    }

    public final int a() {
        return ((LinearLayoutManager) Objects.requireNonNull(this.a.m)).K();
    }

    public final void addItemDecoration(ki kiVar) {
        this.a.t(kiVar);
    }

    public final void addOnChildAttachStateChangeListener(kn knVar) {
        RecyclerView recyclerView = this.a;
        if (recyclerView.x == null) {
            recyclerView.x = new ArrayList();
        }
        recyclerView.x.add(knVar);
    }

    public final void addOnLayoutCompleteListener(Runnable runnable) {
        this.b.add(runnable);
    }

    public final void b(kl klVar) {
        if (klVar instanceof GridLayoutManager) {
            setLayoutStyle(blb.d(klVar));
        } else {
            setLayoutStyle(ble.d(klVar));
        }
    }

    public final boolean canScrollHorizontally(int i2) {
        return this.a.canScrollHorizontally(i2);
    }

    public final boolean canScrollVertically(int i2) {
        return this.a.canScrollVertically(i2);
    }

    public final ld findViewHolderForAdapterPosition(int i2) {
        return this.a.h(i2);
    }

    public final kc getAdapter() {
        return this.a.g();
    }

    public final int getChildLayoutPosition(View view) {
        return this.a.d(view);
    }

    public final int getPaddingBottom() {
        return this.a.getPaddingBottom();
    }

    public final int getPaddingTop() {
        return this.a.getPaddingTop();
    }

    public final RecyclerView getRecyclerView() {
        return this.a;
    }

    public final void invalidateItemDecorations() {
        this.a.M();
    }

    public final boolean isLayoutCompleted() {
        kc adapter = getAdapter();
        if (adapter == null || adapter.getItemCount() <= 0 || this.a.ap()) {
            return false;
        }
        return true;
    }

    /* access modifiers changed from: protected */
    public final void onAttachedToWindow() {
        super.onAttachedToWindow();
        this.h.c(this.g);
    }

    /* access modifiers changed from: protected */
    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        this.h.e(this.g);
    }

    public final boolean post(Runnable runnable) {
        return this.a.post(runnable);
    }

    public final void removeOnLayoutCompleteListener(Runnable runnable) {
        if (runnable != null) {
            this.b.remove(runnable);
        }
    }

    public final void requestLayout() {
        Parcelable parcelable;
        super.requestLayout();
        if (this.p) {
            kl klVar = this.a.m;
            if (klVar != null) {
                parcelable = klVar.N();
            } else {
                parcelable = null;
            }
            this.a.requestLayout();
            kl klVar2 = this.a.m;
            if (!(klVar2 == null || parcelable == null)) {
                klVar2.U(parcelable);
            }
        }
        DefaultScrollBar defaultScrollBar = this.c;
        if (defaultScrollBar != null) {
            defaultScrollBar.requestLayout();
        }
    }

    public final void scrollBy(int i2, int i3) {
        this.a.scrollBy(i2, i3);
    }

    public final void scrollToPosition(int i2) {
        this.a.Z(i2);
    }

    public final void setAdapter(kc kcVar) {
        DefaultScrollBar defaultScrollBar = this.c;
        if (defaultScrollBar != null) {
            defaultScrollBar.adapterChanged(kcVar);
        }
        kc kcVar2 = this.f;
        if (kcVar2 instanceof blj) {
            ((blj) kcVar2).b();
        }
        this.f = kcVar;
        this.a.ab(kcVar);
        if (kcVar instanceof blj) {
            ((blj) kcVar).a();
        }
    }

    public final void setContentDescription(CharSequence charSequence) {
        boolean z = false;
        int i2 = 1;
        if (charSequence != null && ("com.android.car.ui.utils.HORIZONTALLY_SCROLLABLE".contentEquals(charSequence) || "com.android.car.ui.utils.VERTICALLY_SCROLLABLE".contentEquals(charSequence))) {
            z = true;
        }
        bld bld = this.r;
        if (bld != null) {
            i2 = bld.b();
        }
        d(this.a, z, i2);
        if (!z) {
            super.setContentDescription(charSequence);
        }
    }

    public final void setLayoutStyle(bld bld) {
        blm blm;
        this.r = bld;
        if (bld == null) {
            this.a.ac((kl) null);
            return;
        }
        if (bld.a() == 0) {
            getContext();
            blm = new blm(this, bld.b(), bld.f());
        } else {
            getContext();
            bln bln = new bln(this, bld.c(), bld.b(), bld.f());
            blm = bln;
            if (bld instanceof blb) {
                bln.g = ((blb) bld).c;
                blm = bln;
            }
        }
        if (this.p) {
            this.a.W((ki) Objects.requireNonNull(this.m));
            this.a.W((ki) Objects.requireNonNull(this.n));
            if (blm instanceof GridLayoutManager) {
                if (this.q) {
                    this.a.t((ki) Objects.requireNonNull(this.m));
                }
                int i2 = blm.b;
                this.o = i2;
                bmc bmc = this.m;
                if (bmc != null) {
                    bmc.a = i2;
                }
            } else if (this.q) {
                this.a.t((ki) Objects.requireNonNull(this.n));
            }
        }
        this.a.ac(blm);
    }

    public final void setPadding(int i2, int i3, int i4, int i5) {
        RecyclerView recyclerView = this.a;
        int a2 = a();
        recyclerView.setPadding(0, i3, 0, i5);
        if (this.i) {
            e(this.k + i3, this.l + i5);
            scrollToPosition(a2);
        }
        super.setPadding(i2, 0, i4, 0);
    }

    public final void setPaddingRelative(int i2, int i3, int i4, int i5) {
        RecyclerView recyclerView = this.a;
        int a2 = a();
        recyclerView.setPaddingRelative(0, i3, 0, i5);
        if (this.i) {
            e(this.k + i3, this.l + i5);
            scrollToPosition(a2);
        }
        super.setPaddingRelative(i2, 0, i4, 0);
    }

    public final View getView() {
        return this;
    }
}
