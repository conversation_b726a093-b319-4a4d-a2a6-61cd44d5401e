package defpackage;

import android.content.Context;
import android.support.v7.widget.RecyclerView;
import android.view.View;
import com.android.car.ui.recyclerview.CarUiRecyclerView;

/* renamed from: bmb  reason: default package */
/* compiled from: PG */
public final class bmb extends RecyclerView {
    private final CarUiRecyclerView ac;

    public bmb(Context context, CarUiRecyclerView carUiRecyclerView) {
        super(context);
        this.ac = carUiRecyclerView;
    }

    public final void M() {
        this.ac.invalidateItemDecorations();
    }

    public final void Z(int i) {
        this.ac.scrollToPosition(i);
    }

    public final void ab(kc kcVar) {
        this.ac.setAdapter(kcVar);
    }

    public final void focusableViewAvailable(View view) {
        this.ac.focusableViewAvailable(view);
    }

    public final kc g() {
        return this.ac.getAdapter();
    }

    public final void t(ki kiVar) {
        this.ac.addItemDecoration(kiVar);
    }
}
