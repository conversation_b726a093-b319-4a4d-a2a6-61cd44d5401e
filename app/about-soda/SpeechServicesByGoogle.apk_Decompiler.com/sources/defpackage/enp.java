package defpackage;

/* renamed from: enp  reason: default package */
/* compiled from: PG */
public final class enp implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;
    private final jjk e;
    private final jjk f;

    public enp(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6) {
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
        this.e = jjk5;
        this.f = jjk6;
    }

    /* renamed from: a */
    public final fnn b() {
        return new fnn(((efd) this.a).b(), ((efp) this.b).b(), ((edt) this.c).b(), ((edw) this.d).b(), ((edp) this.e).b(), ((enq) this.f).b());
    }
}
