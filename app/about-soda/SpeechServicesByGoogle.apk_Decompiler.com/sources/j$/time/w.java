package j$.time;

import j$.time.chrono.C0010i;
import j$.time.chrono.u;
import j$.time.format.A;
import j$.time.format.s;
import j$.time.temporal.ChronoUnit;
import j$.time.temporal.TemporalUnit;
import j$.time.temporal.a;
import j$.time.temporal.k;
import j$.time.temporal.l;
import j$.time.temporal.n;
import j$.time.temporal.p;
import j$.time.temporal.r;
import j$.time.temporal.t;
import java.io.DataOutput;
import java.io.InvalidObjectException;
import java.io.ObjectInput;
import java.io.ObjectInputStream;
import java.io.Serializable;

public final class w implements k, n, Comparable, Serializable {
    private static final long serialVersionUID = 4183400860270640070L;
    private final int a;
    private final int b;

    static {
        s sVar = new s();
        sVar.p(a.YEAR, 4, 10, A.EXCEEDS_PAD);
        sVar.e('-');
        sVar.o(a.MONTH_OF_YEAR, 2);
        sVar.x();
    }

    private w(int i, int i2) {
        this.a = i;
        this.b = i2;
    }

    private long I() {
        return ((((long) this.a) * 12) + ((long) this.b)) - 1;
    }

    static w M(ObjectInput objectInput) {
        int readInt = objectInput.readInt();
        byte readByte = objectInput.readByte();
        a.YEAR.I((long) readInt);
        a.MONTH_OF_YEAR.I((long) readByte);
        return new w(readInt, readByte);
    }

    private w N(int i, int i2) {
        if (this.a == i && this.b == i2) {
            return this;
        }
        return new w(i, i2);
    }

    private void readObject(ObjectInputStream objectInputStream) {
        throw new InvalidObjectException("Deserialization via serialization delegate");
    }

    private Object writeReplace() {
        return new s((byte) 12, this);
    }

    public final long B(p pVar) {
        if (!(pVar instanceof a)) {
            return pVar.g(this);
        }
        int i = v.a[((a) pVar).ordinal()];
        int i2 = 1;
        if (i == 1) {
            return (long) this.b;
        }
        if (i == 2) {
            return I();
        }
        int i3 = this.a;
        if (i == 3) {
            if (i3 < 1) {
                i3 = 1 - i3;
            }
            return (long) i3;
        } else if (i == 4) {
            return (long) i3;
        } else {
            if (i == 5) {
                if (i3 < 1) {
                    i2 = 0;
                }
                return (long) i2;
            }
            throw new RuntimeException("Unsupported field: ".concat(String.valueOf(pVar)));
        }
    }

    public final Object F(r rVar) {
        if (rVar == l.e()) {
            return u.d;
        }
        if (rVar == l.i()) {
            return ChronoUnit.MONTHS;
        }
        return l.c(this, rVar);
    }

    /* renamed from: J */
    public final w f(long j, TemporalUnit temporalUnit) {
        if (!(temporalUnit instanceof ChronoUnit)) {
            return (w) temporalUnit.m(this, j);
        }
        switch (v.b[((ChronoUnit) temporalUnit).ordinal()]) {
            case 1:
                return K(j);
            case 2:
                return L(j);
            case 3:
                return L(j$.nio.file.attribute.a.f(j, (long) 10));
            case 4:
                return L(j$.nio.file.attribute.a.f(j, (long) 100));
            case 5:
                return L(j$.nio.file.attribute.a.f(j, (long) 1000));
            case 6:
                a aVar = a.ERA;
                return d(j$.nio.file.attribute.a.e(B(aVar), j), aVar);
            default:
                throw new RuntimeException("Unsupported unit: ".concat(String.valueOf(temporalUnit)));
        }
    }

    public final w K(long j) {
        if (j == 0) {
            return this;
        }
        long j2 = (((long) this.a) * 12) + ((long) (this.b - 1)) + j;
        long j3 = (long) 12;
        return N(a.YEAR.F(j$.nio.file.attribute.a.b(j2, j3)), ((int) j$.nio.file.attribute.a.d(j2, j3)) + 1);
    }

    public final w L(long j) {
        if (j == 0) {
            return this;
        }
        return N(a.YEAR.F(((long) this.a) + j), this.b);
    }

    /* renamed from: O */
    public final w d(long j, p pVar) {
        if (!(pVar instanceof a)) {
            return (w) pVar.s(this, j);
        }
        a aVar = (a) pVar;
        aVar.I(j);
        int i = v.a[aVar.ordinal()];
        int i2 = this.a;
        if (i == 1) {
            int i3 = (int) j;
            a.MONTH_OF_YEAR.I((long) i3);
            return N(i2, i3);
        } else if (i == 2) {
            return K(j - I());
        } else {
            int i4 = this.b;
            if (i == 3) {
                if (i2 < 1) {
                    j = 1 - j;
                }
                int i5 = (int) j;
                a.YEAR.I((long) i5);
                return N(i5, i4);
            } else if (i == 4) {
                int i6 = (int) j;
                a.YEAR.I((long) i6);
                return N(i6, i4);
            } else if (i != 5) {
                throw new RuntimeException("Unsupported field: ".concat(String.valueOf(pVar)));
            } else if (B(a.ERA) == j) {
                return this;
            } else {
                int i7 = 1 - i2;
                a.YEAR.I((long) i7);
                return N(i7, i4);
            }
        }
    }

    /* access modifiers changed from: package-private */
    public final void P(DataOutput dataOutput) {
        dataOutput.writeInt(this.a);
        dataOutput.writeByte(this.b);
    }

    public final int compareTo(Object obj) {
        w wVar = (w) obj;
        int i = this.a - wVar.a;
        if (i == 0) {
            return this.b - wVar.b;
        }
        return i;
    }

    public final boolean e(p pVar) {
        if (pVar instanceof a) {
            if (pVar == a.YEAR || pVar == a.MONTH_OF_YEAR || pVar == a.PROLEPTIC_MONTH || pVar == a.YEAR_OF_ERA || pVar == a.ERA) {
                return true;
            }
            return false;
        } else if (pVar == null || !pVar.p(this)) {
            return false;
        } else {
            return true;
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:5:0x0009, code lost:
        r5 = (j$.time.w) r5;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean equals(java.lang.Object r5) {
        /*
            r4 = this;
            r0 = 1
            if (r4 != r5) goto L_0x0004
            return r0
        L_0x0004:
            boolean r1 = r5 instanceof j$.time.w
            r2 = 0
            if (r1 == 0) goto L_0x001a
            j$.time.w r5 = (j$.time.w) r5
            int r1 = r5.a
            int r3 = r4.a
            if (r3 != r1) goto L_0x0018
            int r1 = r4.b
            int r5 = r5.b
            if (r1 != r5) goto L_0x0018
            goto L_0x0019
        L_0x0018:
            r0 = 0
        L_0x0019:
            return r0
        L_0x001a:
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: j$.time.w.equals(java.lang.Object):boolean");
    }

    public final k g(k kVar) {
        if (C0010i.p(kVar).equals(u.d)) {
            return kVar.d(I(), a.PROLEPTIC_MONTH);
        }
        throw new RuntimeException("Adjustment only supported on ISO date-time");
    }

    public final int hashCode() {
        return (this.b << 27) ^ this.a;
    }

    public final k m(long j, ChronoUnit chronoUnit) {
        if (j == Long.MIN_VALUE) {
            return f(Long.MAX_VALUE, chronoUnit).f(1, chronoUnit);
        }
        return f(-j, chronoUnit);
    }

    public final int p(p pVar) {
        return t(pVar).a(B(pVar), pVar);
    }

    public final k s(g gVar) {
        return (w) C0010i.a(gVar, this);
    }

    public final t t(p pVar) {
        long j;
        if (pVar != a.YEAR_OF_ERA) {
            return l.d(this, pVar);
        }
        if (this.a <= 0) {
            j = 1000000000;
        } else {
            j = 999999999;
        }
        return t.j(1, j);
    }

    public final String toString() {
        String str;
        int i = this.a;
        int abs = Math.abs(i);
        StringBuilder sb = new StringBuilder(9);
        if (abs >= 1000) {
            sb.append(i);
        } else if (i < 0) {
            sb.append(i - 10000);
            sb.deleteCharAt(1);
        } else {
            sb.append(i + 10000);
            sb.deleteCharAt(0);
        }
        int i2 = this.b;
        if (i2 < 10) {
            str = "-0";
        } else {
            str = "-";
        }
        sb.append(str);
        sb.append(i2);
        return sb.toString();
    }
}
