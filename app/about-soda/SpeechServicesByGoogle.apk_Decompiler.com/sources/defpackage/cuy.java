package defpackage;

/* renamed from: cuy  reason: default package */
/* compiled from: PG */
public final class cuy extends cvb {
    private final crw a;

    public cuy(crw crw) {
        this.a = crw;
    }

    public final crw a() {
        return this.a;
    }

    public final int b() {
        return 3;
    }

    public final boolean equals(Object obj) {
        if (obj instanceof cvc) {
            cvc cvc = (cvc) obj;
            if (cvc.b() != 3 || !this.a.equals(cvc.a())) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        crw crw = this.a;
        if (crw.B()) {
            return crw.i();
        }
        int i = crw.memoizedHashCode;
        if (i == 0) {
            i = crw.i();
            crw.memoizedHashCode = i;
        }
        return i;
    }
}
