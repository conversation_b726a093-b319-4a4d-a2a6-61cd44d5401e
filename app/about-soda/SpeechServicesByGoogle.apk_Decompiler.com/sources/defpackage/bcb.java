package defpackage;

import android.content.Context;
import androidx.work.WorkerParameters;

/* renamed from: bcb  reason: default package */
/* compiled from: PG */
public abstract class bcb {
    public abstract bbj a(Context context, String str, WorkerParameters workerParameters);

    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r7v6, resolved type: java.lang.Object} */
    /* JADX DEBUG: Multi-variable search result rejected for TypeSearchVarInfo{r0v10, resolved type: bbj} */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.bbj b(android.content.Context r7, java.lang.String r8, androidx.work.WorkerParameters r9) {
        /*
            r6 = this;
            java.lang.String r0 = "appContext"
            defpackage.jnu.e(r7, r0)
            java.lang.String r0 = "workerClassName"
            defpackage.jnu.e(r8, r0)
            java.lang.String r0 = "workerParameters"
            defpackage.jnu.e(r9, r0)
            bbj r0 = r6.a(r7, r8, r9)
            if (r0 != 0) goto L_0x0072
            java.lang.Class r0 = java.lang.Class.forName(r8)     // Catch:{ all -> 0x005d }
            java.lang.Class<bbj> r1 = defpackage.bbj.class
            java.lang.Class r0 = r0.asSubclass(r1)     // Catch:{ all -> 0x005d }
            java.lang.String r1 = "{\n                Class.…class.java)\n            }"
            defpackage.jnu.d(r0, r1)     // Catch:{ all -> 0x005d }
            r1 = 2
            java.lang.Class[] r2 = new java.lang.Class[r1]     // Catch:{ all -> 0x0048 }
            java.lang.Class<android.content.Context> r3 = android.content.Context.class
            r4 = 0
            r2[r4] = r3     // Catch:{ all -> 0x0048 }
            java.lang.Class<androidx.work.WorkerParameters> r3 = androidx.work.WorkerParameters.class
            r5 = 1
            r2[r5] = r3     // Catch:{ all -> 0x0048 }
            java.lang.reflect.Constructor r0 = r0.getDeclaredConstructor(r2)     // Catch:{ all -> 0x0048 }
            java.lang.Object[] r1 = new java.lang.Object[r1]     // Catch:{ all -> 0x0048 }
            r1[r4] = r7     // Catch:{ all -> 0x0048 }
            r1[r5] = r9     // Catch:{ all -> 0x0048 }
            java.lang.Object r7 = r0.newInstance(r1)     // Catch:{ all -> 0x0048 }
            java.lang.String r9 = "{\n                val co…Parameters)\n            }"
            defpackage.jnu.d(r7, r9)     // Catch:{ all -> 0x0048 }
            r0 = r7
            bbj r0 = (defpackage.bbj) r0     // Catch:{ all -> 0x0048 }
            goto L_0x0072
        L_0x0048:
            r7 = move-exception
            java.lang.String r8 = java.lang.String.valueOf(r8)
            bbk r9 = defpackage.bbk.a()
            java.lang.String r0 = defpackage.bcc.a
            java.lang.String r1 = "Could not instantiate "
            java.lang.String r8 = r1.concat(r8)
            r9.d(r0, r8, r7)
            throw r7
        L_0x005d:
            r7 = move-exception
            java.lang.String r8 = java.lang.String.valueOf(r8)
            bbk r9 = defpackage.bbk.a()
            java.lang.String r0 = defpackage.bcc.a
            java.lang.String r1 = "Invalid class: "
            java.lang.String r8 = r1.concat(r8)
            r9.d(r0, r8, r7)
            throw r7
        L_0x0072:
            boolean r7 = r0.d
            if (r7 != 0) goto L_0x0077
            return r0
        L_0x0077:
            java.lang.StringBuilder r7 = new java.lang.StringBuilder
            java.lang.String r9 = "WorkerFactory ("
            r7.<init>(r9)
            java.lang.Class r9 = r6.getClass()
            java.lang.String r9 = r9.getName()
            r7.append(r9)
            java.lang.String r9 = ") returned an instance of a ListenableWorker ("
            r7.append(r9)
            r7.append(r8)
            java.lang.String r8 = ") which has already been invoked. createWorker() must always return a new instance of a ListenableWorker."
            r7.append(r8)
            java.lang.String r7 = r7.toString()
            java.lang.IllegalStateException r8 = new java.lang.IllegalStateException
            r8.<init>(r7)
            throw r8
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bcb.b(android.content.Context, java.lang.String, androidx.work.WorkerParameters):bbj");
    }
}
