package defpackage;

import androidx.work.impl.workers.ConstraintTrackingWorker;

/* renamed from: biv  reason: default package */
/* compiled from: PG */
public final class biv extends jme {
    public /* synthetic */ Object a;
    final /* synthetic */ ConstraintTrackingWorker b;
    public int c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public biv(ConstraintTrackingWorker constraintTrackingWorker, jlr jlr) {
        super(jlr);
        this.b = constraintTrackingWorker;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.c |= Integer.MIN_VALUE;
        return this.b.j((bbj) null, (byw) null, (bhe) null, this);
    }
}
