package defpackage;

import android.content.Context;
import android.content.IntentFilter;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.RemoteException;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;

/* renamed from: bpv  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bpv implements pq {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    private final /* synthetic */ int c;

    public /* synthetic */ bpv(Object obj, Object obj2, int i) {
        this.c = i;
        this.a = obj;
        this.b = obj2;
    }

    /* JADX WARNING: type inference failed for: r0v4, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v5, types: [java.lang.Object, java.lang.Runnable] */
    public final Object a(po poVar) {
        bpb bpb;
        int i = this.c;
        if (i == 0) {
            bpd bpd = new bpd(poVar, 0);
            try {
                Object obj = ((bpx) this.b).a;
                Parcel a2 = ((bov) obj).a();
                int i2 = box.a;
                a2.writeStrongBinder(bpd);
                Parcel b2 = ((bov) obj).b(4, a2);
                IBinder readStrongBinder = b2.readStrongBinder();
                if (readStrongBinder == null) {
                    bpb = null;
                } else {
                    IInterface queryLocalInterface = readStrongBinder.queryLocalInterface("com.google.android.apps.aicore.aidl.ICancellationCallback");
                    if (queryLocalInterface instanceof bpb) {
                        bpb = (bpb) queryLocalInterface;
                    } else {
                        bpb = new bpb(readStrongBinder);
                    }
                }
                b2.recycle();
                poVar.a(new bpu(bpb, 1), ((bpy) this.a).d);
                return "prepareInferenceEngineFuture";
            } catch (RemoteException e) {
                poVar.d(new bpp(3, 6, "Failed to prepare inference engine", e));
                return null;
            } catch (RuntimeException e2) {
                poVar.d(new bpp(3, 0, "Failed to prepare inference engine", e2));
                return null;
            }
        } else if (i != 1) {
            Object obj2 = this.b;
            AtomicBoolean atomicBoolean = new AtomicBoolean();
            ? r0 = this.a;
            cri cri = new cri(atomicBoolean, r0, poVar);
            Context context = (Context) obj2;
            context.registerReceiver(cri, new IntentFilter("android.intent.action.USER_UNLOCKED"));
            if (!crj.e(context) || !atomicBoolean.compareAndSet(false, true)) {
                poVar.a(new ai((Object) atomicBoolean, obj2, (Object) cri, 14, (byte[]) null), hld.a);
                return "DirectBootUtils.runWhenUnlocked";
            }
            context.unregisterReceiver(cri);
            r0.run();
            poVar.c((Object) null);
            return "DirectBootUtils.runWhenUnlocked";
        } else {
            ? r02 = this.b;
            jnu.e(r02, "$this_future");
            AtomicBoolean atomicBoolean2 = new AtomicBoolean(false);
            poVar.a(new alr(atomicBoolean2, 13), bay.a);
            r02.execute(new ai((Object) atomicBoolean2, (Object) poVar, this.a, 6, (short[]) null));
            return jkd.a;
        }
    }

    public /* synthetic */ bpv(Executor executor, jmp jmp, int i) {
        this.c = i;
        this.b = executor;
        this.a = jmp;
    }
}
