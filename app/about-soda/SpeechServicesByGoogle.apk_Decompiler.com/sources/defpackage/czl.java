package defpackage;

import android.content.Context;

/* renamed from: czl  reason: default package */
/* compiled from: PG */
public final class czl implements czd {
    final /* synthetic */ grh a;
    final /* synthetic */ Context b;
    private final /* synthetic */ int c;

    public czl(grh grh, Context context, int i) {
        this.c = i;
        this.a = grh;
        this.b = context;
    }

    public final void a(String str, int i) {
        if (this.c != 0) {
            if (i == 1 && this.a.f()) {
                cqh.f(this.b, (Class) this.a.b(), str);
            }
        } else if (i == 1) {
            grh grh = this.a;
            if (grh.f()) {
                cqh.f(this.b, (Class) grh.b(), str);
            }
        }
    }

    public final void b(String str, int i) {
        if (this.c != 0) {
            if (i == 0 && this.a.f()) {
                cqh.g(this.b, (Class) this.a.b(), str);
            }
        } else if (i == 0) {
            grh grh = this.a;
            if (grh.f()) {
                cqh.g(this.b, (Class) grh.b(), str);
            }
        }
    }
}
