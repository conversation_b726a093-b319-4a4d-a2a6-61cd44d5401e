package defpackage;

import java.util.Collections;
import java.util.Set;
import java.util.WeakHashMap;
import java.util.concurrent.ThreadFactory;

/* renamed from: cpv  reason: default package */
/* compiled from: PG */
public final class cpv implements ThreadFactory {
    public final Object a = new Object();
    public final Set b = Collections.newSetFromMap(new WeakHashMap());
    public final Set c = new ov();
    private final int d;

    public cpv(int i) {
        this.d = i;
    }

    public final Thread newThread(Runnable runnable) {
        cpu cpu;
        synchronized (this.a) {
            cpu = new cpu(this.d, runnable);
            this.b.add(cpu);
            cpu.a = new ckm(this, cpu, 14, (byte[]) null);
            cpu.b = new ckm(this, cpu, 15, (byte[]) null);
        }
        return cpu;
    }
}
