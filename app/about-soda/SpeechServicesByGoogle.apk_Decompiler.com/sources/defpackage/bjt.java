package defpackage;

import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Insets;
import android.graphics.drawable.ColorDrawable;
import android.os.Build;
import android.os.Bundle;
import android.util.DisplayMetrics;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowInsets;
import android.view.WindowManager;
import com.google.android.tts.R;

/* renamed from: bjt  reason: default package */
/* compiled from: PG */
public class bjt extends Dialog implements abd, awi, ng {
    private static final int DIALOG_MIN_PADDING = 32;
    private static final int DIALOG_START_MARGIN_THRESHOLD = 64;
    private static final int IME_OVERLAP_DP = 32;
    private static final double VISIBLE_SCREEN_PERCENTAGE = 0.9d;
    private WindowManager.LayoutParams mBaseLayoutParams;
    /* access modifiers changed from: private */
    public View mContent;
    /* access modifiers changed from: private */
    public final Context mContext;
    private final abf mLifecycleRegistry = new abf(this);
    private final nf mOnBackPressedDispatcher = new nf(new alr(this, 17));
    private final awh mSavedStateRegistryController = new awh((awi) this);
    private int mSceneType;

    public bjt(Context context) {
        super(context);
        int i;
        this.mContext = context;
        requestWindowFeature(1);
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams layoutParams = new WindowManager.LayoutParams();
            this.mBaseLayoutParams = layoutParams;
            layoutParams.copyFrom(window.getAttributes());
            if (Build.VERSION.SDK_INT >= 30) {
                if (this.mBaseLayoutParams.layoutInDisplayCutoutMode != 3) {
                    i = 135;
                } else {
                    i = 7;
                }
                this.mBaseLayoutParams.setFitInsetsTypes(i);
            }
            updateAttributes();
        }
    }

    private void configureImeInsetFit() {
        Window window = getWindow();
        if (window != null) {
            if (Build.VERSION.SDK_INT < 30) {
                window.setSoftInputMode(16);
            } else {
                iih.g(window.getDecorView().getRootView(), new bjs(this, window));
            }
        }
    }

    private void copySystemUiVisibility() {
        if (getWindow() != null) {
            Activity e = bnv.e(this.mContext);
            getWindow().getDecorView().setSystemUiVisibility(e.getWindow().getDecorView().getSystemUiVisibility());
            getWindow().getDecorView().setOnSystemUiVisibilityChangeListener(new bjr(this, e));
        }
    }

    /* access modifiers changed from: private */
    public void copyWindowInsets() {
        WindowInsets rootWindowInsets;
        Window window = getWindow();
        if (window != null) {
            byw byw = new byw(window, getWindow().getDecorView());
            Activity e = bnv.e(this.mContext);
            int c = ((we) new byw(e.getWindow(), e.getWindow().getDecorView()).a).c();
            if (c != 0) {
                ((we) byw.a).g(c);
            }
            if (Build.VERSION.SDK_INT >= 30 && (rootWindowInsets = e.getWindow().getDecorView().getRootWindowInsets()) != null) {
                if (!sk$$ExternalSyntheticApiModelOutline1.m(rootWindowInsets, sk$$ExternalSyntheticApiModelOutline1.m$1())) {
                    byw.ae(1);
                }
                if (!sk$$ExternalSyntheticApiModelOutline1.m(rootWindowInsets, WindowInsets.Type.navigationBars())) {
                    byw.ae(2);
                }
            }
        }
    }

    private float getHorizontalInset(DisplayMetrics displayMetrics) {
        if (Build.VERSION.SDK_INT < 30) {
            return (float) (((double) displayMetrics.widthPixels) * 0.09999999999999998d);
        }
        Insets m = sk$$ExternalSyntheticApiModelOutline1.m(((WindowManager) bnv.f(this.mContext).getSystemService(WindowManager.class)).getCurrentWindowMetrics().getWindowInsets(), 7);
        return (float) (m.left + m.right);
    }

    private float getVerticalInset(DisplayMetrics displayMetrics) {
        if (Build.VERSION.SDK_INT < 30) {
            return (float) (((double) displayMetrics.heightPixels) * 0.09999999999999998d);
        }
        Insets m = sk$$ExternalSyntheticApiModelOutline1.m(((WindowManager) bnv.f(this.mContext).getSystemService(WindowManager.class)).getCurrentWindowMetrics().getWindowInsets(), 7);
        return (float) (m.top + m.bottom);
    }

    private void initViewTreeOwners() {
        Window window = getWindow();
        if (window != null) {
            we.i(window.getDecorView(), this);
            wb.l(window.getDecorView(), this);
            kq.c(window.getDecorView(), this);
        }
    }

    /* access modifiers changed from: private */
    public void updateAttributes() {
        Window window = getWindow();
        if (window != null) {
            window.setAttributes(getDialogWindowLayoutParam(this.mBaseLayoutParams));
        }
    }

    public void addContentView(View view, ViewGroup.LayoutParams layoutParams) {
        initViewTreeOwners();
        super.addContentView(view, layoutParams);
    }

    public WindowManager.LayoutParams getDialogWindowLayoutParam(WindowManager.LayoutParams layoutParams) {
        Context context = this.mContext;
        DisplayMetrics g = bnv.g(context);
        int dimensionPixelSize = context.getResources().getDimensionPixelSize(R.dimen.car_ui_app_styled_dialog_width_max);
        int dimensionPixelSize2 = this.mContext.getResources().getDimensionPixelSize(R.dimen.car_ui_app_styled_dialog_height_max);
        int i = g.widthPixels;
        int i2 = g.heightPixels;
        int horizontalInset = (int) getHorizontalInset(g);
        int verticalInset = (int) getVerticalInset(g);
        int dimensionPixelSize3 = this.mContext.getResources().getDimensionPixelSize(R.dimen.car_ui_app_styled_dialog_width);
        int dimensionPixelSize4 = this.mContext.getResources().getDimensionPixelSize(R.dimen.car_ui_app_styled_dialog_height);
        if (dimensionPixelSize3 == 0) {
            dimensionPixelSize3 = Math.min(i, dimensionPixelSize);
        }
        if (dimensionPixelSize4 == 0) {
            dimensionPixelSize4 = Math.min(i2, dimensionPixelSize2);
        }
        layoutParams.dimAmount = bnv.b(this.mContext.getResources(), R.dimen.car_ui_app_styled_dialog_dim_amount);
        int i3 = this.mSceneType;
        if (i3 == 1) {
            layoutParams.windowAnimations = **********;
        } else if (i3 == 2) {
            layoutParams.windowAnimations = **********;
        } else if (i3 != 3) {
            layoutParams.windowAnimations = **********;
        } else {
            layoutParams.windowAnimations = **********;
        }
        int dimensionPixelSize5 = this.mContext.getResources().getDimensionPixelSize(R.dimen.car_ui_app_styled_dialog_position_x);
        int dimensionPixelSize6 = this.mContext.getResources().getDimensionPixelSize(R.dimen.car_ui_app_styled_dialog_position_y);
        if (dimensionPixelSize5 == 0) {
            dimensionPixelSize5 = 0;
            if (dimensionPixelSize6 == 0) {
                int i4 = i - horizontalInset;
                layoutParams.x = 0;
                layoutParams.y = 0;
                int a = (int) bnv.a(this.mContext.getResources(), 32);
                int i5 = a + a;
                if (horizontalInset + dimensionPixelSize3 >= i - i5) {
                    dimensionPixelSize3 = i4 - i5;
                }
                if (dimensionPixelSize4 + verticalInset >= i2 - i5) {
                    dimensionPixelSize4 = (i2 - verticalInset) - i5;
                }
                layoutParams.width = dimensionPixelSize3;
                layoutParams.height = dimensionPixelSize4;
                int a2 = (int) bnv.a(this.mContext.getResources(), DIALOG_START_MARGIN_THRESHOLD);
                int i6 = i4 - dimensionPixelSize3;
                if (this.mContext.getResources().getConfiguration().orientation != 2 || i6 / 2 < a2) {
                    layoutParams.gravity = 17;
                } else {
                    layoutParams.gravity = 8388659;
                    layoutParams.x = a2;
                    layoutParams.y = ((i2 - verticalInset) - dimensionPixelSize4) / 2;
                }
                return layoutParams;
            }
        }
        layoutParams.gravity = 8388659;
        layoutParams.x = dimensionPixelSize5;
        layoutParams.y = dimensionPixelSize6;
        return layoutParams;
    }

    public aba getLifecycle() {
        return this.mLifecycleRegistry;
    }

    public nf getOnBackPressedDispatcher() {
        return this.mOnBackPressedDispatcher;
    }

    public awg getSavedStateRegistry() {
        return (awg) this.mSavedStateRegistryController.c;
    }

    public WindowManager.LayoutParams getWindowLayoutParams() {
        if (getWindow() == null) {
            return null;
        }
        return getWindow().getAttributes();
    }

    /* renamed from: lambda$copySystemUiVisibility$1$com-android-car-ui-appstyledview-AppStyledDialog  reason: not valid java name */
    public /* synthetic */ void m5lambda$copySystemUiVisibility$1$comandroidcaruiappstyledviewAppStyledDialog(Activity activity, int i) {
        getWindow().getDecorView().setSystemUiVisibility(activity.getWindow().getDecorView().getSystemUiVisibility());
    }

    /* renamed from: lambda$new$0$com-android-car-ui-appstyledview-AppStyledDialog  reason: not valid java name */
    public /* synthetic */ void m6lambda$new$0$comandroidcaruiappstyledviewAppStyledDialog() {
        super.onBackPressed();
    }

    public void onAttachedToWindow() {
        super.onAttachedToWindow();
        copyWindowInsets();
        copySystemUiVisibility();
        updateAttributes();
    }

    /* access modifiers changed from: protected */
    public void onCreate(Bundle bundle) {
        super.onCreate(bundle);
        this.mSavedStateRegistryController.b(bundle);
        Window window = getWindow();
        if (window != null) {
            window.setBackgroundDrawable(new ColorDrawable(0));
            updateAttributes();
            configureImeInsetFit();
            this.mLifecycleRegistry.e(aaz.CREATED);
        }
    }

    public Bundle onSaveInstanceState() {
        awh awh = this.mSavedStateRegistryController;
        Bundle onSaveInstanceState = super.onSaveInstanceState();
        awh.c(onSaveInstanceState);
        return onSaveInstanceState;
    }

    /* access modifiers changed from: protected */
    public void onStart() {
        this.mLifecycleRegistry.e(aaz.STARTED);
        this.mLifecycleRegistry.e(aaz.RESUMED);
        super.onStart();
    }

    /* access modifiers changed from: protected */
    public void onStop() {
        this.mLifecycleRegistry.e(aaz.DESTROYED);
        super.onStop();
    }

    public void setContentView(int i) {
        initViewTreeOwners();
        super.setContentView(i);
    }

    public void setSceneType(int i) {
        this.mSceneType = i;
    }

    public void show() {
        if (!isShowing()) {
            super.show();
            View currentFocus = getCurrentFocus();
            if (currentFocus != null) {
                currentFocus.clearFocus();
            }
        }
    }

    public void setContentView(View view) {
        initViewTreeOwners();
        this.mContent = view;
        super.setContentView(view);
    }

    public void setContentView(View view, ViewGroup.LayoutParams layoutParams) {
        initViewTreeOwners();
        super.setContentView(view, layoutParams);
    }
}
