package defpackage;

/* renamed from: bgg  reason: default package */
/* compiled from: PG */
public final class bgg {
    public final String a;
    public final Long b;

    public bgg(String str, Long l) {
        this.a = str;
        this.b = l;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof bgg)) {
            return false;
        }
        bgg bgg = (bgg) obj;
        if (jnu.i(this.a, bgg.a) && jnu.i(this.b, bgg.b)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (this.a.hashCode() * 31) + this.b.hashCode();
    }

    public final String toString() {
        return "Preference(key=" + this.a + ", value=" + this.b + ')';
    }
}
