package defpackage;

import android.os.Binder;
import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;

/* renamed from: bow  reason: default package */
/* compiled from: PG */
public class bow extends Binder implements IInterface {
    protected bow(String str) {
        attachInterface(this, str);
    }

    public final boolean onTransact(int i, Parcel parcel, Parcel parcel2, int i2) {
        if (i <= 16777215) {
            parcel.enforceInterface(getInterfaceDescriptor());
        } else if (super.onTransact(i, parcel, parcel2, i2)) {
            return true;
        }
        return y(i, parcel, parcel2);
    }

    /* access modifiers changed from: protected */
    public boolean y(int i, Parcel parcel, Parcel parcel2) {
        return false;
    }

    public final IBinder asBinder() {
        return this;
    }
}
