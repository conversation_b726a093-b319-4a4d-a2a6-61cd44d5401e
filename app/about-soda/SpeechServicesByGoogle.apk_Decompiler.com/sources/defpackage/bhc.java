package defpackage;

/* renamed from: bhc  reason: default package */
/* compiled from: PG */
public final class bhc {
    public final String a;
    public final bbx b;

    public bhc(String str, bbx bbx) {
        jnu.e(str, "id");
        jnu.e(bbx, "state");
        this.a = str;
        this.b = bbx;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof bhc)) {
            return false;
        }
        bhc bhc = (bhc) obj;
        if (jnu.i(this.a, bhc.a) && this.b == bhc.b) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (this.a.hashCode() * 31) + this.b.hashCode();
    }

    public final String toString() {
        return "IdAndState(id=" + this.a + ", state=" + this.b + ')';
    }
}
