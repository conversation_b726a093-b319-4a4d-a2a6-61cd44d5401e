package com.google.speech.recognizer;

/**
 * 语音识别结果回调接口
 * 模仿Python版本的resultHandler逻辑
 */
public interface RecognitionResultCallback {
    
    /**
     * 处理最终识别结果
     * @param hypothesis 识别的文本结果
     * @param endpointReason 结束原因
     */
    void onFinalResult(String hypothesis, String endpointReason);
    
    /**
     * 处理部分识别结果（实时结果）
     * @param hypothesis 当前识别的文本
     */
    void onPartialResult(String hypothesis);
    
    /**
     * 处理端点事件
     * @param endpointType 端点类型（开始说话、结束说话等）
     */
    void onEndpointEvent(String endpointType);
    
    /**
     * 处理音频级别信息
     * @param rms RMS值
     * @param audioLevel 音频级别
     */
    void onAudioLevelInfo(float rms, float audioLevel);
    
    /**
     * 处理语言识别事件
     * @param language 识别的语言
     * @param confidenceLevel 置信度
     */
    void onLanguageIdEvent(String language, int confidenceLevel);
    
    /**
     * 处理解析错误
     * @param error 错误信息
     * @param rawData 原始数据（用于调试）
     */
    void onParseError(String error, byte[] rawData);

    /**
     * 处理预取结果
     * @param hypothesis 预取的文本结果
     */
    default void onPrefetchResult(String hypothesis) {
        // 默认实现，可选择性重写
    }

    /**
     * 处理时间信息
     * @param startTimeUsec 开始时间（微秒）
     * @param elapsedTimeUsec 经过时间（微秒）
     */
    default void onTimingInfo(long startTimeUsec, long elapsedTimeUsec) {
        // 默认实现，可选择性重写
    }

    /**
     * 处理对齐信息
     * @param alignmentInfo 对齐信息字符串
     */
    default void onAlignmentInfo(String alignmentInfo) {
        // 默认实现，可选择性重写
    }

    /**
     * 处理开始事件
     */
    default void onStartEvent() {
        // 默认实现，可选择性重写
    }

    /**
     * 处理停止事件
     */
    default void onStopEvent() {
        // 默认实现，可选择性重写
    }

    /**
     * 处理关闭事件
     */
    default void onShutdownEvent() {
        // 默认实现，可选择性重写
    }
}
