package defpackage;

/* renamed from: eqa  reason: default package */
/* compiled from: PG */
public final class eqa extends htq implements hvb {
    public static final eqa b;
    private static volatile hvh c;
    public huf a = hvk.a;

    static {
        eqa eqa = new eqa();
        b = eqa;
        htq.z(eqa.class, eqa);
    }

    private eqa() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(b, "\u0004\u0001\u0000\u0000\u0001\u0001\u0001\u0000\u0001\u0000\u0001\u001b", new Object[]{"a", epr.class});
        } else if (i2 == 3) {
            return new eqa();
        } else {
            if (i2 == 4) {
                return new htk((htq) b);
            }
            if (i2 == 5) {
                return b;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = c;
            if (hvh == null) {
                synchronized (eqa.class) {
                    hvh = c;
                    if (hvh == null) {
                        hvh = new htl(b);
                        c = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
