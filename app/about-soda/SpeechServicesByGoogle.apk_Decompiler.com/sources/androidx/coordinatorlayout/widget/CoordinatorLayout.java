package androidx.coordinatorlayout.widget;

import android.content.Context;
import android.content.res.Resources;
import android.content.res.TypedArray;
import android.graphics.Canvas;
import android.graphics.Rect;
import android.graphics.drawable.Drawable;
import android.os.Parcelable;
import android.os.SystemClock;
import android.util.AttributeSet;
import android.util.Log;
import android.util.SparseArray;
import android.view.Gravity;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import com.google.android.tts.R;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/* compiled from: PG */
public class CoordinatorLayout extends ViewGroup implements vi, vj {
    public static final String a;
    public static final Class[] b = {Context.class, AttributeSet.class};
    public static final ThreadLocal c = new ThreadLocal();
    static final Comparator d = new py(2);
    private static final uo w = new up(12);
    public xn e;
    public boolean f;
    public ViewGroup.OnHierarchyChangeListener g;
    public final ayn h;
    private final List i;
    private final List j;
    private final int[] k;
    private final int[] l;
    private final int[] m;
    private boolean n;
    private boolean o;
    private int[] p;
    private View q;
    private View r;
    private sa s;
    private boolean t;
    private Drawable u;
    private vk v;
    private final bvh x;

    static {
        String str;
        Package packageR = CoordinatorLayout.class.getPackage();
        if (packageR != null) {
            str = packageR.getName();
        } else {
            str = null;
        }
        a = str;
    }

    public CoordinatorLayout(Context context) {
        this(context, (AttributeSet) null);
    }

    private final boolean A(int i2) {
        View view = this;
        while (true) {
            if (view != null) {
                if (view.isFocused()) {
                    break;
                } else if (view instanceof ViewGroup) {
                    view = ((ViewGroup) view).getFocusedChild();
                } else {
                    view = null;
                }
            } else {
                view = null;
                break;
            }
        }
        t(this, view, 2, 1);
        int[] iArr = this.m;
        iArr[0] = 0;
        iArr[1] = 0;
        f(view, 0, 0, 0, i2, 1, iArr);
        h(view, 1);
        if (this.m[1] > 0) {
            return true;
        }
        return false;
    }

    private final boolean B(rw rwVar, View view, MotionEvent motionEvent, int i2) {
        if (i2 != 0) {
            return rwVar.g(this, view, motionEvent);
        }
        return rwVar.d(this, view, motionEvent);
    }

    /* JADX WARNING: Code restructure failed: missing block: B:20:0x005b, code lost:
        r6 = B(r9, r7, r12, r13);
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    private final boolean C(android.view.MotionEvent r12, int r13) {
        /*
            r11 = this;
            int r0 = r12.getActionMasked()
            java.util.List r1 = r11.j
            r1.clear()
            boolean r2 = r11.isChildrenDrawingOrderEnabled()
            int r3 = r11.getChildCount()
            int r4 = r3 + -1
        L_0x0013:
            if (r4 < 0) goto L_0x0027
            if (r2 == 0) goto L_0x001c
            int r5 = r11.getChildDrawingOrder(r3, r4)
            goto L_0x001d
        L_0x001c:
            r5 = r4
        L_0x001d:
            android.view.View r5 = r11.getChildAt(r5)
            r1.add(r5)
            int r4 = r4 + -1
            goto L_0x0013
        L_0x0027:
            java.util.Comparator r2 = d
            if (r2 == 0) goto L_0x002e
            java.util.Collections.sort(r1, r2)
        L_0x002e:
            int r2 = r1.size()
            r3 = 0
            r4 = 0
            r5 = r3
            r6 = r5
        L_0x0036:
            if (r5 >= r2) goto L_0x0093
            java.lang.Object r7 = r1.get(r5)
            android.view.View r7 = (android.view.View) r7
            android.view.ViewGroup$LayoutParams r8 = r7.getLayoutParams()
            rz r8 = (defpackage.rz) r8
            rw r9 = r8.a
            if (r6 != 0) goto L_0x0049
            goto L_0x0057
        L_0x0049:
            if (r0 == 0) goto L_0x0057
            if (r9 == 0) goto L_0x0090
            if (r4 != 0) goto L_0x0053
            android.view.MotionEvent r4 = E(r12)
        L_0x0053:
            r11.B(r9, r7, r4, r13)
            goto L_0x0090
        L_0x0057:
            if (r6 != 0) goto L_0x0088
            if (r9 == 0) goto L_0x0088
            boolean r6 = r11.B(r9, r7, r12, r13)
            if (r6 == 0) goto L_0x0088
            r11.q = r7
            r7 = 3
            if (r0 == r7) goto L_0x0088
            r7 = 1
            if (r0 == r7) goto L_0x0088
            r7 = r3
        L_0x006a:
            if (r7 >= r5) goto L_0x0088
            java.lang.Object r9 = r1.get(r7)
            android.view.View r9 = (android.view.View) r9
            android.view.ViewGroup$LayoutParams r10 = r9.getLayoutParams()
            rz r10 = (defpackage.rz) r10
            rw r10 = r10.a
            if (r10 == 0) goto L_0x0085
            if (r4 != 0) goto L_0x0082
            android.view.MotionEvent r4 = E(r12)
        L_0x0082:
            r11.B(r10, r9, r4, r13)
        L_0x0085:
            int r7 = r7 + 1
            goto L_0x006a
        L_0x0088:
            rw r7 = r8.a
            if (r7 != 0) goto L_0x008e
            r8.m = r3
        L_0x008e:
            boolean r7 = r8.m
        L_0x0090:
            int r5 = r5 + 1
            goto L_0x0036
        L_0x0093:
            r1.clear()
            if (r4 == 0) goto L_0x009b
            r4.recycle()
        L_0x009b:
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.coordinatorlayout.widget.CoordinatorLayout.C(android.view.MotionEvent, int):boolean");
    }

    private static final void D(int i2, Rect rect, Rect rect2, rz rzVar, int i3, int i4) {
        int i5;
        int i6;
        int i7 = rzVar.c;
        if (i7 == 0) {
            i7 = 17;
        }
        int absoluteGravity = Gravity.getAbsoluteGravity(i7, i2);
        int i8 = absoluteGravity & 7;
        int i9 = absoluteGravity & 112;
        int absoluteGravity2 = Gravity.getAbsoluteGravity(s(rzVar.d), i2);
        int i10 = absoluteGravity2 & 7;
        int i11 = absoluteGravity2 & 112;
        if (i10 == 1) {
            i5 = rect.left + (rect.width() / 2);
        } else if (i10 != 5) {
            i5 = rect.left;
        } else {
            i5 = rect.right;
        }
        if (i11 == 16) {
            i6 = rect.top + (rect.height() / 2);
        } else if (i11 != 80) {
            i6 = rect.top;
        } else {
            i6 = rect.bottom;
        }
        if (i8 == 1) {
            i5 -= i3 / 2;
        } else if (i8 != 5) {
            i5 -= i3;
        }
        if (i9 == 16) {
            i6 -= i4 / 2;
        } else if (i9 != 80) {
            i6 -= i4;
        }
        rect2.set(i5, i6, i3 + i5, i4 + i6);
    }

    private static final MotionEvent E(MotionEvent motionEvent) {
        MotionEvent obtain = MotionEvent.obtain(motionEvent);
        obtain.setAction(3);
        return obtain;
    }

    private static final void F(View view, int i2) {
        rz rzVar = (rz) view.getLayoutParams();
        int i3 = rzVar.i;
        if (i3 != i2) {
            int[] iArr = wj.a;
            view.offsetLeftAndRight(i2 - i3);
            rzVar.i = i2;
        }
    }

    private static final void G(View view, int i2) {
        rz rzVar = (rz) view.getLayoutParams();
        int i3 = rzVar.j;
        if (i3 != i2) {
            int[] iArr = wj.a;
            view.offsetTopAndBottom(i2 - i3);
            rzVar.j = i2;
        }
    }

    static final rz l(View view) {
        rz rzVar = (rz) view.getLayoutParams();
        if (!rzVar.b) {
            if (view instanceof rv) {
                rw a2 = ((rv) view).a();
                if (a2 == null) {
                    Log.e("CoordinatorLayout", "Attached behavior class is null");
                }
                rzVar.b(a2);
                rzVar.b = true;
            } else {
                rx rxVar = null;
                for (Class cls = view.getClass(); cls != null; cls = cls.getSuperclass()) {
                    rxVar = (rx) cls.getAnnotation(rx.class);
                    if (rxVar != null) {
                        break;
                    }
                }
                if (rxVar != null) {
                    try {
                        rzVar.b((rw) rxVar.a().getDeclaredConstructor((Class[]) null).newInstance((Object[]) null));
                    } catch (Exception e2) {
                        Log.e("CoordinatorLayout", "Default behavior class " + rxVar.a().getName() + " could not be instantiated. Did you forget a default constructor?", e2);
                    }
                }
                rzVar.b = true;
            }
        }
        return rzVar;
    }

    private final int n() {
        return p() - getHeight();
    }

    private final int o() {
        return -p();
    }

    private final int p() {
        int i2 = 0;
        for (int i3 = 0; i3 < getChildCount(); i3++) {
            View childAt = getChildAt(i3);
            rz rzVar = (rz) childAt.getLayoutParams();
            i2 += childAt.getHeight() + rzVar.topMargin + rzVar.bottomMargin;
        }
        return i2;
    }

    private final int q(int i2) {
        int[] iArr = this.p;
        if (iArr == null) {
            Log.e("CoordinatorLayout", "No keylines defined for " + this + " - attempted index lookup " + i2);
            return 0;
        } else if (i2 >= 0 && i2 < iArr.length) {
            return iArr[i2];
        } else {
            Log.e("CoordinatorLayout", "Keyline index " + i2 + " out of range for " + this);
            return 0;
        }
    }

    private final int r() {
        return (int) (((float) getHeight()) * 0.1f);
    }

    private static int s(int i2) {
        if ((i2 & 7) == 0) {
            i2 |= 8388611;
        }
        if ((i2 & 112) == 0) {
            return i2 | 48;
        }
        return i2;
    }

    private static int u(int i2) {
        if (i2 == 0) {
            return 8388661;
        }
        return i2;
    }

    private static Rect v() {
        Rect rect = (Rect) w.a();
        if (rect == null) {
            return new Rect();
        }
        return rect;
    }

    private final void w(rz rzVar, Rect rect, int i2, int i3) {
        int width = getWidth();
        int height = getHeight();
        int max = Math.max(getPaddingLeft() + rzVar.leftMargin, Math.min(rect.left, ((width - getPaddingRight()) - i2) - rzVar.rightMargin));
        int max2 = Math.max(getPaddingTop() + rzVar.topMargin, Math.min(rect.top, ((height - getPaddingBottom()) - i3) - rzVar.bottomMargin));
        rect.set(max, max2, i2 + max, i3 + max2);
    }

    private static void x(Rect rect) {
        rect.setEmpty();
        w.b(rect);
    }

    private final void y() {
        View view = this.q;
        if (view != null) {
            rw rwVar = ((rz) view.getLayoutParams()).a;
            if (rwVar != null) {
                long uptimeMillis = SystemClock.uptimeMillis();
                MotionEvent obtain = MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, 0.0f, 0.0f, 0);
                rwVar.g(this, this.q, obtain);
                obtain.recycle();
            }
            this.q = null;
        }
        int childCount = getChildCount();
        for (int i2 = 0; i2 < childCount; i2++) {
            ((rz) getChildAt(i2).getLayoutParams()).m = false;
        }
        this.n = false;
    }

    private final void z() {
        int[] iArr = wj.a;
        if (getFitsSystemWindows()) {
            if (this.v == null) {
                this.v = new bon((ViewGroup) this, 1);
            }
            wa.k(this, this.v);
            setSystemUiVisibility(1280);
            return;
        }
        wa.k(this, (vk) null);
    }

    public final List a(View view) {
        ayn ayn = this.h;
        int i2 = ((pa) ayn.b).f;
        ArrayList arrayList = null;
        for (int i3 = 0; i3 < i2; i3++) {
            ArrayList arrayList2 = (ArrayList) ((pa) ayn.b).g(i3);
            if (arrayList2 != null && arrayList2.contains(view)) {
                if (arrayList == null) {
                    arrayList = new ArrayList();
                }
                arrayList.add(((pa) ayn.b).d(i3));
            }
        }
        if (arrayList == null) {
            return Collections.emptyList();
        }
        return arrayList;
    }

    public final void b(View view) {
        ArrayList b2 = this.h.b(view);
        if (b2 != null && !b2.isEmpty()) {
            for (int i2 = 0; i2 < b2.size(); i2++) {
                View view2 = (View) b2.get(i2);
                rw rwVar = ((rz) view2.getLayoutParams()).a;
                if (rwVar != null) {
                    rwVar.i(this, view2, view);
                }
            }
        }
    }

    /* access modifiers changed from: package-private */
    public final void c(View view, boolean z, Rect rect) {
        if (view.isLayoutRequested() || view.getVisibility() == 8) {
            rect.setEmpty();
        } else if (z) {
            sc.a(this, view, rect);
        } else {
            rect.set(view.getLeft(), view.getTop(), view.getRight(), view.getBottom());
        }
    }

    /* access modifiers changed from: protected */
    public final boolean checkLayoutParams(ViewGroup.LayoutParams layoutParams) {
        if (!(layoutParams instanceof rz) || !super.checkLayoutParams(layoutParams)) {
            return false;
        }
        return true;
    }

    public final void d(View view, int i2, int i3, int[] iArr, int i4) {
        rw rwVar;
        int i5;
        int i6;
        int childCount = getChildCount();
        boolean z = false;
        int i7 = 0;
        int i8 = 0;
        for (int i9 = 0; i9 < childCount; i9++) {
            View childAt = getChildAt(i9);
            if (childAt.getVisibility() != 8) {
                rz rzVar = (rz) childAt.getLayoutParams();
                if (rzVar.d(i4) && (rwVar = rzVar.a) != null) {
                    int[] iArr2 = this.k;
                    iArr2[0] = 0;
                    iArr2[1] = 0;
                    rwVar.m(this, childAt, view, i3, iArr2, i4);
                    if (i2 > 0) {
                        i5 = Math.max(i7, this.k[0]);
                    } else {
                        i5 = Math.min(i7, this.k[0]);
                    }
                    i7 = i5;
                    if (i3 > 0) {
                        i6 = Math.max(i8, this.k[1]);
                    } else {
                        i6 = Math.min(i8, this.k[1]);
                    }
                    i8 = i6;
                    z = true;
                }
            } else {
                int i10 = i4;
            }
        }
        iArr[0] = i7;
        iArr[1] = i8;
        if (z) {
            i(1);
        }
    }

    public final boolean dispatchKeyEvent(KeyEvent keyEvent) {
        boolean dispatchKeyEvent = super.dispatchKeyEvent(keyEvent);
        if (dispatchKeyEvent || keyEvent.getAction() != 0) {
            return dispatchKeyEvent;
        }
        int keyCode = keyEvent.getKeyCode();
        if (keyCode != 19) {
            if (keyCode != 20) {
                if (keyCode != 62) {
                    if (keyCode == 92) {
                        return A(-getHeight());
                    }
                    if (keyCode == 93) {
                        return A(getHeight());
                    }
                    if (keyCode == 122) {
                        return A(o());
                    }
                    if (keyCode != 123) {
                        return dispatchKeyEvent;
                    }
                    return A(n());
                } else if (keyEvent.isShiftPressed()) {
                    return A(o());
                } else {
                    return A(n());
                }
            } else if (keyEvent.isAltPressed()) {
                return A(getHeight());
            } else {
                return A(r());
            }
        } else if (keyEvent.isAltPressed()) {
            return A(-getHeight());
        } else {
            return A(-r());
        }
    }

    /* access modifiers changed from: protected */
    public final boolean drawChild(Canvas canvas, View view, long j2) {
        rw rwVar = ((rz) view.getLayoutParams()).a;
        return super.drawChild(canvas, view, j2);
    }

    /* access modifiers changed from: protected */
    public final void drawableStateChanged() {
        super.drawableStateChanged();
        int[] drawableState = getDrawableState();
        Drawable drawable = this.u;
        if (drawable != null && drawable.isStateful() && drawable.setState(drawableState)) {
            invalidate();
        }
    }

    public final void e(View view, int i2, int i3, int i4, int i5, int i6) {
        f(view, i2, i3, i4, i5, 0, this.l);
    }

    public final void f(View view, int i2, int i3, int i4, int i5, int i6, int[] iArr) {
        rw rwVar;
        int i7;
        int i8;
        int childCount = getChildCount();
        boolean z = false;
        int i9 = 0;
        int i10 = 0;
        for (int i11 = 0; i11 < childCount; i11++) {
            View childAt = getChildAt(i11);
            if (childAt.getVisibility() != 8) {
                rz rzVar = (rz) childAt.getLayoutParams();
                if (rzVar.d(i6) && (rwVar = rzVar.a) != null) {
                    int[] iArr2 = this.k;
                    iArr2[0] = 0;
                    iArr2[1] = 0;
                    rwVar.n(this, childAt, i3, i4, i5, iArr2);
                    if (i4 > 0) {
                        i7 = Math.max(i9, this.k[0]);
                    } else {
                        i7 = Math.min(i9, this.k[0]);
                    }
                    i9 = i7;
                    if (i5 > 0) {
                        i8 = Math.max(i10, this.k[1]);
                    } else {
                        i8 = Math.min(i10, this.k[1]);
                    }
                    i10 = i8;
                    z = true;
                }
            } else {
                int i12 = i6;
            }
        }
        iArr[0] = iArr[0] + i9;
        iArr[1] = iArr[1] + i10;
        if (z) {
            i(1);
        }
    }

    public final void g(View view, View view2, int i2, int i3) {
        this.x.b(i2, i3);
        this.r = view2;
        int childCount = getChildCount();
        for (int i4 = 0; i4 < childCount; i4++) {
            rz rzVar = (rz) getChildAt(i4).getLayoutParams();
            if (rzVar.d(i3)) {
                rw rwVar = rzVar.a;
            }
        }
    }

    /* access modifiers changed from: protected */
    public final /* synthetic */ ViewGroup.LayoutParams generateDefaultLayoutParams() {
        return new rz();
    }

    public final /* bridge */ /* synthetic */ ViewGroup.LayoutParams generateLayoutParams(AttributeSet attributeSet) {
        return new rz(getContext(), attributeSet);
    }

    public final int getNestedScrollAxes() {
        return this.x.a();
    }

    /* access modifiers changed from: protected */
    public final int getSuggestedMinimumHeight() {
        return Math.max(super.getSuggestedMinimumHeight(), getPaddingTop() + getPaddingBottom());
    }

    /* access modifiers changed from: protected */
    public final int getSuggestedMinimumWidth() {
        return Math.max(super.getSuggestedMinimumWidth(), getPaddingLeft() + getPaddingRight());
    }

    public final void h(View view, int i2) {
        this.x.c(i2);
        int childCount = getChildCount();
        for (int i3 = 0; i3 < childCount; i3++) {
            View childAt = getChildAt(i3);
            rz rzVar = (rz) childAt.getLayoutParams();
            if (rzVar.d(i2)) {
                rw rwVar = rzVar.a;
                if (rwVar != null) {
                    rwVar.c(this, childAt, view, i2);
                }
                rzVar.c(i2, false);
                rzVar.a();
            }
        }
        this.r = null;
    }

    /* JADX WARNING: Removed duplicated region for block: B:102:0x0280  */
    /* JADX WARNING: Removed duplicated region for block: B:105:0x0286  */
    /* JADX WARNING: Removed duplicated region for block: B:98:0x025e  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void i(int r25) {
        /*
            r24 = this;
            r0 = r24
            r1 = r25
            int r8 = r24.getLayoutDirection()
            java.util.List r2 = r0.i
            int r9 = r2.size()
            android.graphics.Rect r10 = v()
            android.graphics.Rect r11 = v()
            android.graphics.Rect r12 = v()
            r14 = 0
        L_0x001b:
            if (r14 >= r9) goto L_0x02c2
            java.util.List r2 = r0.i
            java.lang.Object r2 = r2.get(r14)
            r15 = r2
            android.view.View r15 = (android.view.View) r15
            android.view.ViewGroup$LayoutParams r2 = r15.getLayoutParams()
            r7 = r2
            rz r7 = (defpackage.rz) r7
            if (r1 != 0) goto L_0x003c
            int r2 = r15.getVisibility()
            r3 = 8
            if (r2 != r3) goto L_0x003c
            r4 = r1
            r6 = r9
            r7 = r12
            goto L_0x02bb
        L_0x003c:
            r6 = 0
        L_0x003d:
            if (r6 >= r14) goto L_0x00e9
            java.util.List r2 = r0.i
            java.lang.Object r2 = r2.get(r6)
            android.view.View r2 = (android.view.View) r2
            android.view.View r3 = r7.l
            if (r3 != r2) goto L_0x00d5
            android.view.ViewGroup$LayoutParams r2 = r15.getLayoutParams()
            r4 = r2
            rz r4 = (defpackage.rz) r4
            android.view.View r2 = r4.k
            if (r2 == 0) goto L_0x00d5
            android.graphics.Rect r3 = v()
            android.graphics.Rect r2 = v()
            android.graphics.Rect r13 = v()
            android.view.View r5 = r4.k
            defpackage.sc.a(r0, r5, r3)
            r5 = 0
            r0.c(r15, r5, r2)
            int r5 = r15.getMeasuredWidth()
            r18 = r9
            int r9 = r15.getMeasuredHeight()
            r19 = r12
            r12 = r2
            r2 = r8
            r20 = r3
            r21 = r4
            r4 = r13
            r17 = r5
            r1 = 1
            r5 = r21
            r22 = r6
            r6 = r17
            r23 = r7
            r7 = r9
            D(r2, r3, r4, r5, r6, r7)
            int r2 = r13.left
            int r3 = r12.left
            if (r2 != r3) goto L_0x00a0
            int r2 = r13.top
            int r3 = r12.top
            if (r2 == r3) goto L_0x009a
            goto L_0x00a0
        L_0x009a:
            r1 = r17
            r2 = r21
            r5 = 0
            goto L_0x00a5
        L_0x00a0:
            r5 = r1
            r1 = r17
            r2 = r21
        L_0x00a5:
            r0.w(r2, r13, r1, r9)
            int r1 = r13.left
            int r3 = r12.left
            int r1 = r1 - r3
            int r3 = r13.top
            int r4 = r12.top
            int r3 = r3 - r4
            if (r1 == 0) goto L_0x00b9
            int[] r4 = defpackage.wj.a
            r15.offsetLeftAndRight(r1)
        L_0x00b9:
            if (r3 == 0) goto L_0x00c0
            int[] r1 = defpackage.wj.a
            r15.offsetTopAndBottom(r3)
        L_0x00c0:
            if (r5 == 0) goto L_0x00cb
            rw r1 = r2.a
            if (r1 == 0) goto L_0x00cb
            android.view.View r2 = r2.k
            r1.i(r0, r15, r2)
        L_0x00cb:
            x(r20)
            x(r12)
            x(r13)
            goto L_0x00dd
        L_0x00d5:
            r22 = r6
            r23 = r7
            r18 = r9
            r19 = r12
        L_0x00dd:
            int r6 = r22 + 1
            r1 = r25
            r9 = r18
            r12 = r19
            r7 = r23
            goto L_0x003d
        L_0x00e9:
            r23 = r7
            r18 = r9
            r19 = r12
            r1 = 1
            r0.c(r15, r1, r11)
            r2 = r23
            int r3 = r2.g
            r4 = 5
            r5 = 3
            r6 = 80
            r7 = 48
            if (r3 == 0) goto L_0x014d
            boolean r3 = r11.isEmpty()
            if (r3 != 0) goto L_0x014d
            int r3 = r2.g
            int r3 = android.view.Gravity.getAbsoluteGravity(r3, r8)
            r9 = r3 & 112(0x70, float:1.57E-43)
            if (r9 == r7) goto L_0x0122
            if (r9 == r6) goto L_0x0112
            goto L_0x012c
        L_0x0112:
            int r9 = r10.bottom
            int r12 = r24.getHeight()
            int r13 = r11.top
            int r12 = r12 - r13
            int r9 = java.lang.Math.max(r9, r12)
            r10.bottom = r9
            goto L_0x012c
        L_0x0122:
            int r9 = r10.top
            int r12 = r11.bottom
            int r9 = java.lang.Math.max(r9, r12)
            r10.top = r9
        L_0x012c:
            r3 = r3 & 7
            if (r3 == r5) goto L_0x0143
            if (r3 == r4) goto L_0x0133
            goto L_0x014d
        L_0x0133:
            int r3 = r10.right
            int r9 = r24.getWidth()
            int r12 = r11.left
            int r9 = r9 - r12
            int r3 = java.lang.Math.max(r3, r9)
            r10.right = r3
            goto L_0x014d
        L_0x0143:
            int r3 = r10.left
            int r9 = r11.right
            int r3 = java.lang.Math.max(r3, r9)
            r10.left = r3
        L_0x014d:
            int r2 = r2.h
            if (r2 == 0) goto L_0x0255
            int r2 = r15.getVisibility()
            if (r2 != 0) goto L_0x0255
            boolean r2 = r15.isLaidOut()
            if (r2 != 0) goto L_0x015f
            goto L_0x0255
        L_0x015f:
            int r2 = r15.getWidth()
            if (r2 <= 0) goto L_0x0255
            int r2 = r15.getHeight()
            if (r2 <= 0) goto L_0x0255
            android.view.ViewGroup$LayoutParams r2 = r15.getLayoutParams()
            rz r2 = (defpackage.rz) r2
            rw r3 = r2.a
            android.graphics.Rect r9 = v()
            android.graphics.Rect r12 = v()
            int r13 = r15.getLeft()
            int r1 = r15.getTop()
            int r4 = r15.getRight()
            int r5 = r15.getBottom()
            r12.set(r13, r1, r4, r5)
            if (r3 == 0) goto L_0x01c1
            boolean r1 = r3.r(r15, r9)
            if (r1 == 0) goto L_0x01c1
            boolean r1 = r12.contains(r9)
            if (r1 == 0) goto L_0x019d
            goto L_0x01c4
        L_0x019d:
            java.lang.IllegalArgumentException r1 = new java.lang.IllegalArgumentException
            java.lang.StringBuilder r2 = new java.lang.StringBuilder
            java.lang.String r3 = "Rect should be within the child's bounds. Rect:"
            r2.<init>(r3)
            java.lang.String r3 = r9.toShortString()
            r2.append(r3)
            java.lang.String r3 = " | Bounds:"
            r2.append(r3)
            java.lang.String r3 = r12.toShortString()
            r2.append(r3)
            java.lang.String r2 = r2.toString()
            r1.<init>(r2)
            throw r1
        L_0x01c1:
            r9.set(r12)
        L_0x01c4:
            x(r12)
            boolean r1 = r9.isEmpty()
            if (r1 == 0) goto L_0x01d2
            x(r9)
            goto L_0x0255
        L_0x01d2:
            int r1 = r2.h
            int r1 = android.view.Gravity.getAbsoluteGravity(r1, r8)
            r3 = r1 & 48
            if (r3 != r7) goto L_0x01f0
            int r3 = r9.top
            int r4 = r2.topMargin
            int r3 = r3 - r4
            int r4 = r2.j
            int r3 = r3 - r4
            int r4 = r10.top
            if (r3 >= r4) goto L_0x01f0
            int r4 = r10.top
            int r4 = r4 - r3
            G(r15, r4)
            r5 = 1
            goto L_0x01f1
        L_0x01f0:
            r5 = 0
        L_0x01f1:
            r3 = r1 & 80
            if (r3 != r6) goto L_0x020d
            int r3 = r24.getHeight()
            int r4 = r9.bottom
            int r3 = r3 - r4
            int r4 = r2.bottomMargin
            int r3 = r3 - r4
            int r4 = r2.j
            int r3 = r3 + r4
            int r4 = r10.bottom
            if (r3 >= r4) goto L_0x020d
            int r4 = r10.bottom
            int r3 = r3 - r4
            G(r15, r3)
            goto L_0x0213
        L_0x020d:
            if (r5 != 0) goto L_0x0213
            r3 = 0
            G(r15, r3)
        L_0x0213:
            r3 = r1 & 3
            r4 = 3
            if (r3 != r4) goto L_0x022c
            int r3 = r9.left
            int r4 = r2.leftMargin
            int r3 = r3 - r4
            int r4 = r2.i
            int r3 = r3 - r4
            int r4 = r10.left
            if (r3 >= r4) goto L_0x022c
            int r4 = r10.left
            int r4 = r4 - r3
            F(r15, r4)
            r5 = 1
            goto L_0x022d
        L_0x022c:
            r5 = 0
        L_0x022d:
            r1 = r1 & 5
            r3 = 5
            if (r1 != r3) goto L_0x024b
            int r1 = r24.getWidth()
            int r3 = r9.right
            int r1 = r1 - r3
            int r3 = r2.rightMargin
            int r1 = r1 - r3
            int r2 = r2.i
            int r1 = r1 + r2
            int r2 = r10.right
            if (r1 >= r2) goto L_0x024b
            int r2 = r10.right
            int r1 = r1 - r2
            F(r15, r1)
            r1 = 0
            goto L_0x0251
        L_0x024b:
            r1 = 0
            if (r5 != 0) goto L_0x0251
            F(r15, r1)
        L_0x0251:
            x(r9)
            goto L_0x0256
        L_0x0255:
            r1 = 0
        L_0x0256:
            int r2 = r14 + 1
            r3 = 2
            r4 = r25
            r5 = 1
            if (r4 == r3) goto L_0x0280
            android.view.ViewGroup$LayoutParams r6 = r15.getLayoutParams()
            rz r6 = (defpackage.rz) r6
            android.graphics.Rect r6 = r6.p
            r7 = r19
            r7.set(r6)
            boolean r6 = r7.equals(r11)
            if (r6 != 0) goto L_0x027d
            android.view.ViewGroup$LayoutParams r6 = r15.getLayoutParams()
            rz r6 = (defpackage.rz) r6
            android.graphics.Rect r6 = r6.p
            r6.set(r11)
            goto L_0x0282
        L_0x027d:
            r6 = r18
            goto L_0x02bb
        L_0x0280:
            r7 = r19
        L_0x0282:
            r6 = r18
        L_0x0284:
            if (r2 >= r6) goto L_0x02bb
            java.util.List r9 = r0.i
            java.lang.Object r9 = r9.get(r2)
            android.view.View r9 = (android.view.View) r9
            android.view.ViewGroup$LayoutParams r12 = r9.getLayoutParams()
            rz r12 = (defpackage.rz) r12
            rw r13 = r12.a
            if (r13 == 0) goto L_0x02b7
            boolean r16 = r13.h(r15)
            if (r16 == 0) goto L_0x02b7
            if (r4 != 0) goto L_0x02a8
            boolean r1 = r12.o
            if (r1 == 0) goto L_0x02a8
            r12.a()
            goto L_0x02b7
        L_0x02a8:
            if (r4 == r3) goto L_0x02af
            r13.i(r0, r9, r15)
            r1 = 0
            goto L_0x02b3
        L_0x02af:
            r13.j(r0, r15)
            r1 = r5
        L_0x02b3:
            if (r4 != r5) goto L_0x02b7
            r12.o = r1
        L_0x02b7:
            int r2 = r2 + 1
            r1 = 0
            goto L_0x0284
        L_0x02bb:
            int r14 = r14 + 1
            r1 = r4
            r9 = r6
            r12 = r7
            goto L_0x001b
        L_0x02c2:
            r7 = r12
            x(r10)
            x(r11)
            x(r7)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.coordinatorlayout.widget.CoordinatorLayout.i(int):void");
    }

    public final void j(View view, int i2) {
        int i3;
        rz rzVar = (rz) view.getLayoutParams();
        View view2 = rzVar.k;
        if (view2 == null && rzVar.f != -1) {
            throw new IllegalStateException("An anchor may not be changed after CoordinatorLayout measurement begins before layout is complete.");
        } else if (view2 != null) {
            Rect v2 = v();
            Rect v3 = v();
            try {
                sc.a(this, view2, v2);
                rz rzVar2 = (rz) view.getLayoutParams();
                int measuredWidth = view.getMeasuredWidth();
                int measuredHeight = view.getMeasuredHeight();
                D(i2, v2, v3, rzVar2, measuredWidth, measuredHeight);
                w(rzVar2, v3, measuredWidth, measuredHeight);
                view.layout(v3.left, v3.top, v3.right, v3.bottom);
            } finally {
                x(v2);
                x(v3);
            }
        } else {
            int i4 = rzVar.e;
            if (i4 >= 0) {
                rz rzVar3 = (rz) view.getLayoutParams();
                int absoluteGravity = Gravity.getAbsoluteGravity(u(rzVar3.c), i2);
                int i5 = absoluteGravity & 7;
                int i6 = absoluteGravity & 112;
                int width = getWidth();
                int height = getHeight();
                int measuredWidth2 = view.getMeasuredWidth();
                int measuredHeight2 = view.getMeasuredHeight();
                if (i2 == 1) {
                    i4 = width - i4;
                }
                int q2 = q(i4) - measuredWidth2;
                if (i5 == 1) {
                    q2 += measuredWidth2 / 2;
                } else if (i5 == 5) {
                    q2 += measuredWidth2;
                }
                if (i6 == 16) {
                    i3 = measuredHeight2 / 2;
                } else if (i6 != 80) {
                    i3 = 0;
                } else {
                    i3 = measuredHeight2;
                }
                int max = Math.max(getPaddingLeft() + rzVar3.leftMargin, Math.min(q2, ((width - getPaddingRight()) - measuredWidth2) - rzVar3.rightMargin));
                int max2 = Math.max(getPaddingTop() + rzVar3.topMargin, Math.min(i3, ((height - getPaddingBottom()) - measuredHeight2) - rzVar3.bottomMargin));
                view.layout(max, max2, measuredWidth2 + max, measuredHeight2 + max2);
                return;
            }
            rz rzVar4 = (rz) view.getLayoutParams();
            Rect v4 = v();
            v4.set(getPaddingLeft() + rzVar4.leftMargin, getPaddingTop() + rzVar4.topMargin, (getWidth() - getPaddingRight()) - rzVar4.rightMargin, (getHeight() - getPaddingBottom()) - rzVar4.bottomMargin);
            if (this.e != null) {
                int[] iArr = wj.a;
                if (getFitsSystemWindows() && !view.getFitsSystemWindows()) {
                    v4.left += this.e.b();
                    v4.top += this.e.d();
                    v4.right -= this.e.c();
                    v4.bottom -= this.e.a();
                }
            }
            Rect v5 = v();
            Gravity.apply(s(rzVar4.c), view.getMeasuredWidth(), view.getMeasuredHeight(), v4, v5, i2);
            view.layout(v5.left, v5.top, v5.right, v5.bottom);
            x(v4);
            x(v5);
        }
    }

    public final boolean k(View view, int i2, int i3) {
        Rect v2 = v();
        sc.a(this, view, v2);
        try {
            return v2.contains(i2, i3);
        } finally {
            x(v2);
        }
    }

    public final void m(View view, int i2, int i3, int i4) {
        measureChildWithMargins(view, i2, i3, i4, 0);
    }

    public final void onAttachedToWindow() {
        super.onAttachedToWindow();
        y();
        if (this.t) {
            if (this.s == null) {
                this.s = new sa(this);
            }
            getViewTreeObserver().addOnPreDrawListener(this.s);
        }
        if (this.e == null) {
            int[] iArr = wj.a;
            if (getFitsSystemWindows()) {
                vy.d(this);
            }
        }
        this.o = true;
    }

    public final void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        y();
        if (this.t && this.s != null) {
            getViewTreeObserver().removeOnPreDrawListener(this.s);
        }
        View view = this.r;
        if (view != null) {
            onStopNestedScroll(view);
        }
        this.o = false;
    }

    public final void onDraw(Canvas canvas) {
        int i2;
        super.onDraw(canvas);
        if (this.f && this.u != null) {
            xn xnVar = this.e;
            if (xnVar != null) {
                i2 = xnVar.d();
            } else {
                i2 = 0;
            }
            if (i2 > 0) {
                this.u.setBounds(0, 0, getWidth(), i2);
                this.u.draw(canvas);
            }
        }
    }

    public final boolean onInterceptTouchEvent(MotionEvent motionEvent) {
        int actionMasked = motionEvent.getActionMasked();
        if (actionMasked == 0) {
            y();
            actionMasked = 0;
        }
        boolean C = C(motionEvent, 0);
        if (actionMasked == 1 || actionMasked == 3) {
            this.q = null;
            y();
        }
        return C;
    }

    /* access modifiers changed from: protected */
    public final void onLayout(boolean z, int i2, int i3, int i4, int i5) {
        rw rwVar;
        int layoutDirection = getLayoutDirection();
        int size = this.i.size();
        for (int i6 = 0; i6 < size; i6++) {
            View view = (View) this.i.get(i6);
            if (view.getVisibility() != 8 && ((rwVar = ((rz) view.getLayoutParams()).a) == null || !rwVar.e(this, view, layoutDirection))) {
                j(view, layoutDirection);
            }
        }
    }

    /* JADX WARNING: type inference failed for: r1v6, types: [java.util.Collection, java.lang.Object] */
    /* access modifiers changed from: protected */
    /* JADX WARNING: Removed duplicated region for block: B:128:0x0286  */
    /* JADX WARNING: Removed duplicated region for block: B:153:0x02eb  */
    /* JADX WARNING: Removed duplicated region for block: B:159:0x0327  */
    /* JADX WARNING: Removed duplicated region for block: B:163:0x0356  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void onMeasure(int r31, int r32) {
        /*
            r30 = this;
            r6 = r30
            java.util.List r0 = r6.i
            r0.clear()
            ayn r0 = r6.h
            java.lang.Object r1 = r0.b
            pa r1 = (defpackage.pa) r1
            int r1 = r1.f
            r7 = 0
            r2 = r7
        L_0x0011:
            if (r2 >= r1) goto L_0x002c
            java.lang.Object r3 = r0.b
            pa r3 = (defpackage.pa) r3
            java.lang.Object r3 = r3.g(r2)
            java.util.ArrayList r3 = (java.util.ArrayList) r3
            if (r3 == 0) goto L_0x0029
            r3.clear()
            java.lang.Object r4 = r0.c
            uo r4 = (defpackage.uo) r4
            r4.b(r3)
        L_0x0029:
            int r2 = r2 + 1
            goto L_0x0011
        L_0x002c:
            java.lang.Object r0 = r0.b
            pa r0 = (defpackage.pa) r0
            r0.clear()
            int r0 = r30.getChildCount()
            r1 = r7
        L_0x0038:
            if (r1 >= r0) goto L_0x0193
            android.view.View r2 = r6.getChildAt(r1)
            rz r3 = l(r2)
            int r4 = r3.f
            r5 = -1
            r8 = 0
            if (r4 != r5) goto L_0x004e
            r3.l = r8
            r3.k = r8
            goto L_0x00d5
        L_0x004e:
            android.view.View r4 = r3.k
            if (r4 == 0) goto L_0x007c
            int r4 = r4.getId()
            int r5 = r3.f
            if (r4 == r5) goto L_0x005b
            goto L_0x007c
        L_0x005b:
            android.view.View r4 = r3.k
            android.view.ViewParent r5 = r4.getParent()
        L_0x0061:
            if (r5 == r6) goto L_0x0079
            if (r5 == 0) goto L_0x0074
            if (r5 != r2) goto L_0x0068
            goto L_0x0074
        L_0x0068:
            boolean r9 = r5 instanceof android.view.View
            if (r9 == 0) goto L_0x006f
            r4 = r5
            android.view.View r4 = (android.view.View) r4
        L_0x006f:
            android.view.ViewParent r5 = r5.getParent()
            goto L_0x0061
        L_0x0074:
            r3.l = r8
            r3.k = r8
            goto L_0x007c
        L_0x0079:
            r3.l = r4
            goto L_0x00d3
        L_0x007c:
            int r4 = r3.f
            android.view.View r4 = r6.findViewById(r4)
            r3.k = r4
            android.view.View r4 = r3.k
            if (r4 == 0) goto L_0x00c9
            if (r4 != r6) goto L_0x009d
            boolean r4 = r30.isInEditMode()
            if (r4 == 0) goto L_0x0095
            r3.l = r8
            r3.k = r8
            goto L_0x00d3
        L_0x0095:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            java.lang.String r1 = "View can not be anchored to the the parent CoordinatorLayout"
            r0.<init>(r1)
            throw r0
        L_0x009d:
            android.view.ViewParent r5 = r4.getParent()
        L_0x00a1:
            if (r5 == r6) goto L_0x00c6
            if (r5 == 0) goto L_0x00c6
            if (r5 != r2) goto L_0x00ba
            boolean r4 = r30.isInEditMode()
            if (r4 == 0) goto L_0x00b2
            r3.l = r8
            r3.k = r8
            goto L_0x00d3
        L_0x00b2:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            java.lang.String r1 = "Anchor must not be a descendant of the anchored view"
            r0.<init>(r1)
            throw r0
        L_0x00ba:
            boolean r9 = r5 instanceof android.view.View
            if (r9 == 0) goto L_0x00c1
            r4 = r5
            android.view.View r4 = (android.view.View) r4
        L_0x00c1:
            android.view.ViewParent r5 = r5.getParent()
            goto L_0x00a1
        L_0x00c6:
            r3.l = r4
            goto L_0x00d3
        L_0x00c9:
            boolean r4 = r30.isInEditMode()
            if (r4 == 0) goto L_0x016d
            r3.l = r8
            r3.k = r8
        L_0x00d3:
            android.view.View r4 = r3.k
        L_0x00d5:
            ayn r4 = r6.h
            r4.c(r2)
            r4 = r7
        L_0x00db:
            if (r4 >= r0) goto L_0x0169
            if (r4 != r1) goto L_0x00e1
            goto L_0x015d
        L_0x00e1:
            android.view.View r5 = r6.getChildAt(r4)
            android.view.View r8 = r3.l
            if (r5 == r8) goto L_0x010f
            int r8 = r30.getLayoutDirection()
            android.view.ViewGroup$LayoutParams r9 = r5.getLayoutParams()
            rz r9 = (defpackage.rz) r9
            int r9 = r9.g
            int r9 = android.view.Gravity.getAbsoluteGravity(r9, r8)
            if (r9 == 0) goto L_0x0105
            int r10 = r3.h
            int r8 = android.view.Gravity.getAbsoluteGravity(r10, r8)
            r8 = r8 & r9
            if (r8 != r9) goto L_0x0105
            goto L_0x010f
        L_0x0105:
            rw r8 = r3.a
            if (r8 == 0) goto L_0x015d
            boolean r8 = r8.h(r5)
            if (r8 == 0) goto L_0x015d
        L_0x010f:
            ayn r8 = r6.h
            java.lang.Object r8 = r8.b
            pa r8 = (defpackage.pa) r8
            boolean r8 = r8.containsKey(r5)
            if (r8 != 0) goto L_0x0120
            ayn r8 = r6.h
            r8.c(r5)
        L_0x0120:
            ayn r8 = r6.h
            java.lang.Object r9 = r8.b
            pa r9 = (defpackage.pa) r9
            boolean r9 = r9.containsKey(r5)
            if (r9 == 0) goto L_0x0161
            java.lang.Object r9 = r8.b
            pa r9 = (defpackage.pa) r9
            boolean r9 = r9.containsKey(r2)
            if (r9 == 0) goto L_0x0161
            java.lang.Object r9 = r8.b
            pa r9 = (defpackage.pa) r9
            java.lang.Object r9 = r9.get(r5)
            java.util.ArrayList r9 = (java.util.ArrayList) r9
            if (r9 != 0) goto L_0x015a
            java.lang.Object r9 = r8.c
            uo r9 = (defpackage.uo) r9
            java.lang.Object r9 = r9.a()
            java.util.ArrayList r9 = (java.util.ArrayList) r9
            if (r9 != 0) goto L_0x0153
            java.util.ArrayList r9 = new java.util.ArrayList
            r9.<init>()
        L_0x0153:
            java.lang.Object r8 = r8.b
            pa r8 = (defpackage.pa) r8
            r8.put(r5, r9)
        L_0x015a:
            r9.add(r2)
        L_0x015d:
            int r4 = r4 + 1
            goto L_0x00db
        L_0x0161:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.String r1 = "All nodes must be present in the graph before being added as an edge"
            r0.<init>(r1)
            throw r0
        L_0x0169:
            int r1 = r1 + 1
            goto L_0x0038
        L_0x016d:
            java.lang.IllegalStateException r0 = new java.lang.IllegalStateException
            java.lang.StringBuilder r1 = new java.lang.StringBuilder
            java.lang.String r4 = "Could not find CoordinatorLayout descendant view with id "
            r1.<init>(r4)
            android.content.res.Resources r4 = r30.getResources()
            int r3 = r3.f
            java.lang.String r3 = r4.getResourceName(r3)
            r1.append(r3)
            java.lang.String r3 = " to anchor view "
            r1.append(r3)
            r1.append(r2)
            java.lang.String r1 = r1.toString()
            r0.<init>(r1)
            throw r0
        L_0x0193:
            java.util.List r0 = r6.i
            ayn r1 = r6.h
            java.lang.Object r2 = r1.d
            java.util.ArrayList r2 = (java.util.ArrayList) r2
            r2.clear()
            java.lang.Object r2 = r1.a
            java.util.HashSet r2 = (java.util.HashSet) r2
            r2.clear()
            java.lang.Object r2 = r1.b
            pa r2 = (defpackage.pa) r2
            int r2 = r2.f
            r3 = r7
        L_0x01ac:
            if (r3 >= r2) goto L_0x01c4
            java.lang.Object r4 = r1.b
            pa r4 = (defpackage.pa) r4
            java.lang.Object r4 = r4.d(r3)
            java.lang.Object r5 = r1.d
            java.lang.Object r8 = r1.a
            java.util.HashSet r8 = (java.util.HashSet) r8
            java.util.ArrayList r5 = (java.util.ArrayList) r5
            r1.d(r4, r5, r8)
            int r3 = r3 + 1
            goto L_0x01ac
        L_0x01c4:
            java.lang.Object r1 = r1.d
            r0.addAll(r1)
            java.util.List r0 = r6.i
            java.util.Collections.reverse(r0)
            int r0 = r30.getChildCount()
            r1 = r7
        L_0x01d3:
            r8 = 1
            if (r1 >= r0) goto L_0x01ff
            android.view.View r2 = r6.getChildAt(r1)
            ayn r3 = r6.h
            java.lang.Object r4 = r3.b
            pa r4 = (defpackage.pa) r4
            int r4 = r4.f
            r5 = r7
        L_0x01e3:
            if (r5 >= r4) goto L_0x01fc
            java.lang.Object r9 = r3.b
            pa r9 = (defpackage.pa) r9
            java.lang.Object r9 = r9.g(r5)
            java.util.ArrayList r9 = (java.util.ArrayList) r9
            if (r9 == 0) goto L_0x01f9
            boolean r9 = r9.contains(r2)
            if (r9 == 0) goto L_0x01f9
            r0 = r8
            goto L_0x0200
        L_0x01f9:
            int r5 = r5 + 1
            goto L_0x01e3
        L_0x01fc:
            int r1 = r1 + 1
            goto L_0x01d3
        L_0x01ff:
            r0 = r7
        L_0x0200:
            boolean r1 = r6.t
            if (r0 == r1) goto L_0x0234
            if (r0 == 0) goto L_0x0221
            boolean r0 = r6.o
            if (r0 == 0) goto L_0x021e
            sa r0 = r6.s
            if (r0 != 0) goto L_0x0215
            sa r0 = new sa
            r0.<init>(r6)
            r6.s = r0
        L_0x0215:
            android.view.ViewTreeObserver r0 = r30.getViewTreeObserver()
            sa r1 = r6.s
            r0.addOnPreDrawListener(r1)
        L_0x021e:
            r6.t = r8
            goto L_0x0234
        L_0x0221:
            boolean r0 = r6.o
            if (r0 == 0) goto L_0x0232
            sa r0 = r6.s
            if (r0 == 0) goto L_0x0232
            android.view.ViewTreeObserver r0 = r30.getViewTreeObserver()
            sa r1 = r6.s
            r0.removeOnPreDrawListener(r1)
        L_0x0232:
            r6.t = r7
        L_0x0234:
            int r9 = r30.getPaddingLeft()
            int r0 = r30.getPaddingTop()
            int r10 = r30.getPaddingRight()
            int r1 = r30.getPaddingBottom()
            int r11 = r30.getLayoutDirection()
            if (r11 != r8) goto L_0x024c
            r12 = r8
            goto L_0x024d
        L_0x024c:
            r12 = r7
        L_0x024d:
            int r13 = android.view.View.MeasureSpec.getMode(r31)
            int r14 = android.view.View.MeasureSpec.getSize(r31)
            int r15 = android.view.View.MeasureSpec.getMode(r32)
            int r16 = android.view.View.MeasureSpec.getSize(r32)
            int r17 = r9 + r10
            int r18 = r0 + r1
            int r0 = r30.getSuggestedMinimumWidth()
            int r1 = r30.getSuggestedMinimumHeight()
            xn r2 = r6.e
            if (r2 == 0) goto L_0x0278
            int[] r2 = defpackage.wj.a
            boolean r2 = r30.getFitsSystemWindows()
            if (r2 == 0) goto L_0x0278
            r19 = r8
            goto L_0x027a
        L_0x0278:
            r19 = r7
        L_0x027a:
            java.util.List r2 = r6.i
            int r5 = r2.size()
            r4 = r0
            r3 = r1
            r1 = r7
            r2 = r1
        L_0x0284:
            if (r2 >= r5) goto L_0x03bc
            java.util.List r0 = r6.i
            java.lang.Object r0 = r0.get(r2)
            android.view.View r0 = (android.view.View) r0
            int r8 = r0.getVisibility()
            r7 = 8
            if (r8 == r7) goto L_0x039e
            android.view.ViewGroup$LayoutParams r7 = r0.getLayoutParams()
            rz r7 = (defpackage.rz) r7
            int r8 = r7.e
            if (r8 < 0) goto L_0x02e3
            if (r13 == 0) goto L_0x02e3
            int r8 = r6.q(r8)
            r21 = r1
            int r1 = r7.c
            int r1 = u(r1)
            int r1 = android.view.Gravity.getAbsoluteGravity(r1, r11)
            r1 = r1 & 7
            r22 = r2
            r2 = 3
            if (r1 != r2) goto L_0x02bf
            if (r12 == 0) goto L_0x02c6
            r1 = r2
            r23 = 1
            goto L_0x02c1
        L_0x02bf:
            r23 = r12
        L_0x02c1:
            r2 = 5
            if (r1 != r2) goto L_0x02d3
            if (r23 == 0) goto L_0x02d0
        L_0x02c6:
            int r1 = r14 - r10
            int r1 = r1 - r8
            r2 = 0
            int r1 = java.lang.Math.max(r2, r1)
        L_0x02ce:
            r8 = r1
            goto L_0x02e9
        L_0x02d0:
            r1 = r2
            r23 = 0
        L_0x02d3:
            if (r1 != r2) goto L_0x02d7
            if (r23 == 0) goto L_0x02dc
        L_0x02d7:
            r2 = 3
            if (r1 != r2) goto L_0x02e7
            if (r23 == 0) goto L_0x02e7
        L_0x02dc:
            int r8 = r8 - r9
            r2 = 0
            int r1 = java.lang.Math.max(r2, r8)
            goto L_0x02ce
        L_0x02e3:
            r21 = r1
            r22 = r2
        L_0x02e7:
            r2 = 0
            r8 = r2
        L_0x02e9:
            if (r19 == 0) goto L_0x031d
            int[] r1 = defpackage.wj.a
            boolean r1 = r0.getFitsSystemWindows()
            if (r1 != 0) goto L_0x031d
            xn r1 = r6.e
            int r1 = r1.b()
            xn r2 = r6.e
            int r2 = r2.c()
            int r1 = r1 + r2
            xn r2 = r6.e
            int r2 = r2.d()
            r23 = r0
            xn r0 = r6.e
            int r0 = r0.a()
            int r2 = r2 + r0
            int r0 = r14 - r1
            int r0 = android.view.View.MeasureSpec.makeMeasureSpec(r0, r13)
            int r1 = r16 - r2
            int r1 = android.view.View.MeasureSpec.makeMeasureSpec(r1, r15)
            r2 = r0
            goto L_0x0323
        L_0x031d:
            r23 = r0
            r2 = r31
            r1 = r32
        L_0x0323:
            rw r0 = r7.a
            if (r0 == 0) goto L_0x0356
            r24 = r23
            r23 = r9
            r9 = r21
            r21 = r1
            r1 = r30
            r20 = r22
            r25 = 0
            r22 = r2
            r2 = r24
            r26 = r10
            r10 = r3
            r3 = r22
            r27 = r11
            r11 = r4
            r4 = r8
            r28 = r5
            r5 = r21
            boolean r0 = r0.k(r1, r2, r3, r4, r5)
            if (r0 != 0) goto L_0x0353
            r2 = r21
            r1 = r22
            r0 = r24
            goto L_0x036f
        L_0x0353:
            r0 = r24
            goto L_0x0372
        L_0x0356:
            r28 = r5
            r26 = r10
            r27 = r11
            r20 = r22
            r24 = r23
            r25 = 0
            r10 = r3
            r11 = r4
            r23 = r9
            r9 = r21
            r0 = r24
            r29 = r2
            r2 = r1
            r1 = r29
        L_0x036f:
            r6.m(r0, r1, r8, r2)
        L_0x0372:
            int r1 = r0.getMeasuredWidth()
            int r1 = r17 + r1
            int r2 = r7.leftMargin
            int r1 = r1 + r2
            int r2 = r7.rightMargin
            int r1 = r1 + r2
            int r1 = java.lang.Math.max(r11, r1)
            int r2 = r0.getMeasuredHeight()
            int r2 = r18 + r2
            int r3 = r7.topMargin
            int r2 = r2 + r3
            int r3 = r7.bottomMargin
            int r2 = r2 + r3
            int r2 = java.lang.Math.max(r10, r2)
            int r0 = r0.getMeasuredState()
            int r0 = android.view.View.combineMeasuredStates(r9, r0)
            r4 = r1
            r3 = r2
            r1 = r0
            goto L_0x03ad
        L_0x039e:
            r20 = r2
            r28 = r5
            r23 = r9
            r26 = r10
            r27 = r11
            r25 = 0
            r9 = r1
            r10 = r3
            r11 = r4
        L_0x03ad:
            int r2 = r20 + 1
            r9 = r23
            r7 = r25
            r10 = r26
            r11 = r27
            r5 = r28
            r8 = 1
            goto L_0x0284
        L_0x03bc:
            r9 = r1
            r10 = r3
            r11 = r4
            r0 = -16777216(0xffffffffff000000, float:-1.7014118E38)
            r0 = r0 & r9
            r1 = r31
            int r0 = android.view.View.resolveSizeAndState(r11, r1, r0)
            int r1 = r9 << 16
            r2 = r32
            int r1 = android.view.View.resolveSizeAndState(r10, r2, r1)
            r6.setMeasuredDimension(r0, r1)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: androidx.coordinatorlayout.widget.CoordinatorLayout.onMeasure(int, int):void");
    }

    public final boolean onNestedFling(View view, float f2, float f3, boolean z) {
        int childCount = getChildCount();
        for (int i2 = 0; i2 < childCount; i2++) {
            View childAt = getChildAt(i2);
            if (childAt.getVisibility() != 8) {
                rz rzVar = (rz) childAt.getLayoutParams();
                if (rzVar.n) {
                    rw rwVar = rzVar.a;
                }
            }
        }
        return false;
    }

    public final boolean onNestedPreFling(View view, float f2, float f3) {
        rw rwVar;
        int childCount = getChildCount();
        boolean z = false;
        for (int i2 = 0; i2 < childCount; i2++) {
            View childAt = getChildAt(i2);
            if (childAt.getVisibility() != 8) {
                rz rzVar = (rz) childAt.getLayoutParams();
                if (rzVar.n && (rwVar = rzVar.a) != null) {
                    z |= rwVar.l(view);
                }
            }
        }
        return z;
    }

    public final void onNestedPreScroll(View view, int i2, int i3, int[] iArr) {
        d(view, i2, i3, iArr, 0);
    }

    public final void onNestedScroll(View view, int i2, int i3, int i4, int i5) {
        e(view, i2, i3, i4, i5, 0);
    }

    public final void onNestedScrollAccepted(View view, View view2, int i2) {
        g(view, view2, i2, 0);
    }

    /* access modifiers changed from: protected */
    public final void onRestoreInstanceState(Parcelable parcelable) {
        Parcelable parcelable2;
        if (!(parcelable instanceof sb)) {
            super.onRestoreInstanceState(parcelable);
            return;
        }
        sb sbVar = (sb) parcelable;
        super.onRestoreInstanceState(sbVar.d);
        SparseArray sparseArray = sbVar.a;
        int childCount = getChildCount();
        for (int i2 = 0; i2 < childCount; i2++) {
            View childAt = getChildAt(i2);
            int id = childAt.getId();
            rw rwVar = l(childAt).a;
            if (!(id == -1 || rwVar == null || (parcelable2 = (Parcelable) sparseArray.get(id)) == null)) {
                rwVar.o(childAt, parcelable2);
            }
        }
    }

    /* access modifiers changed from: protected */
    public final Parcelable onSaveInstanceState() {
        Parcelable p2;
        sb sbVar = new sb(super.onSaveInstanceState());
        SparseArray sparseArray = new SparseArray();
        int childCount = getChildCount();
        for (int i2 = 0; i2 < childCount; i2++) {
            View childAt = getChildAt(i2);
            int id = childAt.getId();
            rw rwVar = ((rz) childAt.getLayoutParams()).a;
            if (!(id == -1 || rwVar == null || (p2 = rwVar.p(childAt)) == null)) {
                sparseArray.append(id, p2);
            }
        }
        sbVar.a = sparseArray;
        return sbVar;
    }

    public final boolean onStartNestedScroll(View view, View view2, int i2) {
        return t(view, view2, i2, 0);
    }

    public final void onStopNestedScroll(View view) {
        h(view, 0);
    }

    public final boolean onTouchEvent(MotionEvent motionEvent) {
        boolean z;
        int actionMasked = motionEvent.getActionMasked();
        View view = this.q;
        boolean z2 = false;
        if (view != null) {
            rw rwVar = ((rz) view.getLayoutParams()).a;
            z = rwVar != null ? rwVar.g(this, this.q, motionEvent) : false;
        } else {
            z = C(motionEvent, 1);
            if (actionMasked != 0 && z) {
                z2 = true;
            }
        }
        if (this.q == null || actionMasked == 3) {
            z |= super.onTouchEvent(motionEvent);
        } else if (z2) {
            MotionEvent E = E(motionEvent);
            super.onTouchEvent(E);
            E.recycle();
        }
        if (actionMasked == 1 || actionMasked == 3) {
            this.q = null;
            y();
        }
        return z;
    }

    public final boolean requestChildRectangleOnScreen(View view, Rect rect, boolean z) {
        rw rwVar = ((rz) view.getLayoutParams()).a;
        if (rwVar == null || !rwVar.f(this, view, rect, z)) {
            return super.requestChildRectangleOnScreen(view, rect, z);
        }
        return true;
    }

    public final void requestDisallowInterceptTouchEvent(boolean z) {
        super.requestDisallowInterceptTouchEvent(z);
        if (z && !this.n) {
            if (this.q == null) {
                int childCount = getChildCount();
                MotionEvent motionEvent = null;
                for (int i2 = 0; i2 < childCount; i2++) {
                    View childAt = getChildAt(i2);
                    rw rwVar = ((rz) childAt.getLayoutParams()).a;
                    if (rwVar != null) {
                        if (motionEvent == null) {
                            long uptimeMillis = SystemClock.uptimeMillis();
                            motionEvent = MotionEvent.obtain(uptimeMillis, uptimeMillis, 3, 0.0f, 0.0f, 0);
                        }
                        rwVar.d(this, childAt, motionEvent);
                    }
                }
                if (motionEvent != null) {
                    motionEvent.recycle();
                }
            }
            y();
            this.n = true;
        }
    }

    public final void setFitsSystemWindows(boolean z) {
        super.setFitsSystemWindows(z);
        z();
    }

    public final void setOnHierarchyChangeListener(ViewGroup.OnHierarchyChangeListener onHierarchyChangeListener) {
        this.g = onHierarchyChangeListener;
    }

    public final void setVisibility(int i2) {
        boolean z;
        super.setVisibility(i2);
        Drawable drawable = this.u;
        if (drawable != null) {
            if (i2 == 0) {
                z = true;
            } else {
                z = false;
            }
            if (drawable.isVisible() != z) {
                this.u.setVisible(z, false);
            }
        }
    }

    public final boolean t(View view, View view2, int i2, int i3) {
        int childCount = getChildCount();
        boolean z = false;
        for (int i4 = 0; i4 < childCount; i4++) {
            View childAt = getChildAt(i4);
            if (childAt.getVisibility() != 8) {
                rz rzVar = (rz) childAt.getLayoutParams();
                rw rwVar = rzVar.a;
                if (rwVar != null) {
                    boolean q2 = rwVar.q(this, childAt, view, i2, i3);
                    z |= q2;
                    rzVar.c(i3, q2);
                } else {
                    rzVar.c(i3, false);
                }
            }
        }
        return z;
    }

    /* access modifiers changed from: protected */
    public final boolean verifyDrawable(Drawable drawable) {
        if (super.verifyDrawable(drawable) || drawable == this.u) {
            return true;
        }
        return false;
    }

    public CoordinatorLayout(Context context, AttributeSet attributeSet) {
        this(context, attributeSet, R.attr.coordinatorLayoutStyle);
    }

    /* access modifiers changed from: protected */
    public final /* bridge */ /* synthetic */ ViewGroup.LayoutParams generateLayoutParams(ViewGroup.LayoutParams layoutParams) {
        if (layoutParams instanceof rz) {
            return new rz((rz) layoutParams);
        }
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            return new rz((ViewGroup.MarginLayoutParams) layoutParams);
        }
        return new rz(layoutParams);
    }

    public CoordinatorLayout(Context context, AttributeSet attributeSet, int i2) {
        super(context, attributeSet, i2);
        TypedArray typedArray;
        this.i = new ArrayList();
        this.h = new ayn((byte[]) null);
        this.j = new ArrayList();
        this.k = new int[2];
        this.l = new int[2];
        this.m = new int[2];
        this.x = new bvh();
        if (i2 == 0) {
            typedArray = context.obtainStyledAttributes(attributeSet, ru.a, 0, 2132084792);
        } else {
            typedArray = context.obtainStyledAttributes(attributeSet, ru.a, i2, 0);
        }
        if (i2 == 0) {
            wj.l(this, context, ru.a, attributeSet, typedArray, 0, 2132084792);
        } else {
            wj.l(this, context, ru.a, attributeSet, typedArray, i2, 0);
        }
        int resourceId = typedArray.getResourceId(0, 0);
        if (resourceId != 0) {
            Resources resources = context.getResources();
            this.p = resources.getIntArray(resourceId);
            float f2 = resources.getDisplayMetrics().density;
            int length = this.p.length;
            for (int i3 = 0; i3 < length; i3++) {
                int[] iArr = this.p;
                iArr[i3] = (int) (((float) iArr[i3]) * f2);
            }
        }
        this.u = typedArray.getDrawable(1);
        typedArray.recycle();
        z();
        super.setOnHierarchyChangeListener(new ry(this));
        if (getImportantForAccessibility() == 0) {
            setImportantForAccessibility(1);
        }
    }
}
