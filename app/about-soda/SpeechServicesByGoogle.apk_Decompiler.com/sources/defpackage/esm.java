package defpackage;

import java.util.concurrent.Executor;

/* renamed from: esm  reason: default package */
/* compiled from: PG */
public final class esm implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;
    private final jjk e;
    private final jjk f;
    private final jjk g;
    private final jjk h;
    private final jjk i;
    private final jjk j;
    private final jjk k;
    private final jjk l;
    private final jjk m;
    private final /* synthetic */ int n;

    public esm(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, jjk jjk9, jjk jjk10, jjk jjk11, jjk jjk12, jjk jjk13, int i2) {
        this.n = i2;
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
        this.e = jjk5;
        this.f = jjk6;
        this.g = jjk7;
        this.h = jjk8;
        this.i = jjk9;
        this.j = jjk10;
        this.k = jjk11;
        this.l = jjk12;
        this.m = jjk13;
    }

    public final /* synthetic */ Object b() {
        if (this.n != 0) {
            jjk jjk = this.k;
            jjk jjk2 = this.f;
            jjk jjk3 = this.i;
            jjk jjk4 = this.e;
            ihn c2 = iit.c(this.d);
            Object b2 = jjk4.b();
            jjk jjk5 = this.a;
            jjk jjk6 = this.c;
            ihn c3 = iit.c(this.g);
            dou a2 = ((dov) jjk6).b();
            don a3 = ((doo) jjk5).b();
            doc doc = (doc) b2;
            jjk jjk7 = this.l;
            jjk jjk8 = this.b;
            jjk jjk9 = this.h;
            return new doi(((dme) this.m).b(), ((iim) jjk2).a(), (dku) jjk.b(), c2, doc, this.j, jjk9, (Executor) jjk3.b(), c3, a2, jjk8, jjk7, a3);
        }
        jjk jjk10 = this.c;
        jjk jjk11 = this.i;
        jjk jjk12 = this.h;
        jjk jjk13 = this.g;
        jjk jjk14 = this.f;
        jjk jjk15 = this.e;
        byw a4 = ((erx) this.d).b();
        dsy a5 = ((erv) jjk15).b();
        cyw a6 = ((egx) jjk14).b();
        eoz a7 = ((epa) jjk13).b();
        dsy a8 = ((epo) jjk12).b();
        jjk jjk16 = this.m;
        return new esl((dvx) this.a.b(), ((eme) this.b).b(), (eoz) jjk10.b(), a4, a5, a6, a7, a8, (doe) jjk11.b(), (Executor) this.j.b(), (hll) this.k.b(), ((esf) this.l).b(), ((ijj) jjk16).a().booleanValue());
    }

    public esm(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, jjk jjk5, jjk jjk6, jjk jjk7, jjk jjk8, jjk jjk9, jjk jjk10, jjk jjk11, jjk jjk12, jjk jjk13, int i2, byte[] bArr) {
        this.n = i2;
        this.m = jjk;
        this.f = jjk2;
        this.k = jjk3;
        this.d = jjk4;
        this.e = jjk5;
        this.j = jjk6;
        this.h = jjk7;
        this.i = jjk8;
        this.g = jjk9;
        this.c = jjk10;
        this.b = jjk11;
        this.l = jjk12;
        this.a = jjk13;
    }
}
