package defpackage;

import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.RadioButton;
import android.widget.Switch;
import com.google.android.tts.R;

/* renamed from: blh  reason: default package */
/* compiled from: PG */
public final class blh extends ld {
    public static final /* synthetic */ int q = 0;
    final View a;
    final View b;
    final View c;
    final bok d;
    final bok e;
    final ImageView f;
    final ImageView g;
    final ImageView h;
    final ViewGroup i;
    final ViewGroup j;
    final View k;
    final Switch l;
    final CheckBox m;
    final RadioButton n;
    final ImageView o;
    final boolean p;

    public blh(View view, boolean z) {
        super(view);
        if (z) {
            bnv.i(view, R.id.car_ui_list_item_touch_interceptor).setVisibility(0);
        } else {
            bnv.i(view, R.id.car_ui_list_item_reduced_touch_interceptor).setVisibility(0);
            bnv.i(view, R.id.car_ui_list_item_action_container_touch_interceptor).setVisibility(0);
        }
        this.a = bnv.i(view, R.id.car_ui_list_item_touch_interceptor);
        this.b = bnv.i(view, R.id.car_ui_list_item_reduced_touch_interceptor);
        this.c = bnv.i(view, R.id.car_ui_list_item_action_container_touch_interceptor);
        this.d = (bok) bnv.i(view, R.id.car_ui_list_item_title);
        this.e = (bok) bnv.i(view, R.id.car_ui_list_item_body);
        this.f = (ImageView) bnv.i(view, R.id.car_ui_list_item_icon);
        this.g = (ImageView) bnv.i(view, R.id.car_ui_list_item_content_icon);
        this.h = (ImageView) bnv.i(view, R.id.car_ui_list_item_avatar_icon);
        this.i = (ViewGroup) bnv.i(view, R.id.car_ui_list_item_icon_container);
        this.j = (ViewGroup) bnv.i(view, R.id.car_ui_list_item_action_container);
        this.k = bnv.i(view, R.id.car_ui_list_item_action_divider);
        this.l = (Switch) bnv.i(view, R.id.car_ui_list_item_switch_widget);
        this.m = (CheckBox) bnv.i(view, R.id.car_ui_list_item_checkbox_widget);
        this.n = (RadioButton) bnv.i(view, R.id.car_ui_list_item_radio_button_widget);
        this.o = (ImageView) bnv.i(view, R.id.car_ui_list_item_supplemental_icon);
        this.p = z;
    }

    /* access modifiers changed from: package-private */
    public final void a(View view, boolean z) {
        view.setEnabled(z);
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i2 = 0; i2 < viewGroup.getChildCount(); i2++) {
                a(viewGroup.getChildAt(i2), z);
            }
        }
    }

    /* access modifiers changed from: package-private */
    public final void b(bla bla, CompoundButton compoundButton, ekf ekf) {
        compoundButton.setVisibility(0);
        compoundButton.setOnCheckedChangeListener((CompoundButton.OnCheckedChangeListener) null);
        compoundButton.setChecked(bla.e);
        compoundButton.setOnCheckedChangeListener(new atx(bla, 3));
        this.a.setVisibility(0);
        this.a.setOnClickListener(new bul(compoundButton, ekf, 1));
        this.a.setClickable(true);
        this.b.setVisibility(8);
        this.c.setVisibility(8);
        this.j.setVisibility(0);
        this.j.setClickable(false);
    }
}
