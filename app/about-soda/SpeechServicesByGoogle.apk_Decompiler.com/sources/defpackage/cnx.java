package defpackage;

import java.util.concurrent.Callable;
import java.util.concurrent.RunnableScheduledFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;

/* renamed from: cnx  reason: default package */
/* compiled from: PG */
public final class cnx extends ScheduledThreadPoolExecutor {
    public cnx(ThreadFactory threadFactory) {
        super(1, threadFactory);
    }

    /* access modifiers changed from: protected */
    public final RunnableScheduledFuture decorateTask(Runnable runnable, RunnableScheduledFuture runnableScheduledFuture) {
        return cos.b(runnableScheduledFuture);
    }

    /* access modifiers changed from: protected */
    public final RunnableScheduledFuture decorateTask(Callable callable, RunnableScheduledFuture runnableScheduledFuture) {
        return cos.b(runnableScheduledFuture);
    }
}
