<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:layout_width="match_parent" android:layout_height="match_parent">
    <com.android.car.ui.FocusParkingView android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintTop_toTopOf="0"/>
    <FrameLayout android:id="@+id/car_ui_base_layout_content_container" android:layout_width="match_parent" android:layout_height="match_parent" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintTop_toTopOf="0"/>
    <FrameLayout android:id="@+id/car_ui_toolbar" android:tag="car_ui_top_inset" android:layout_width="match_parent" android:layout_height="wrap_content" app:layout_constraintTop_toTopOf="0">
        <include layout="@layout/car_ui_toolbar"/>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
