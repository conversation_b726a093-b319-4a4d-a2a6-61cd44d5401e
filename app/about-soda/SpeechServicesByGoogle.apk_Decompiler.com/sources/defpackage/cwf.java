package defpackage;

/* renamed from: cwf  reason: default package */
/* compiled from: PG */
public enum cwf {
    NEW_FILE_KEY(0),
    ADD_DOWNLOAD_TRANSFORM(1),
    USE_CHECKSUM_ONLY(2);
    
    public final int d;

    private cwf(int i) {
        this.d = i;
    }

    public static cwf a(int i) {
        if (i == 0) {
            return NEW_FILE_KEY;
        }
        if (i == 1) {
            return ADD_DOWNLOAD_TRANSFORM;
        }
        if (i == 2) {
            return USE_CHECKSUM_ONLY;
        }
        throw new IllegalArgumentException(a.ak(i, "Unknown MDD FileKey version:"));
    }
}
