<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:gravity="center" android:id="@+id/car_ui_scroll_bar" android:layout_width="@dimen/car_ui_scrollbar_container_width" android:layout_height="match_parent">
    <ImageView android:id="@+id/car_ui_scrollbar_page_up" android:background="@drawable/car_ui_recyclerview_button_ripple_background" android:focusable="false" android:layout_width="@dimen/car_ui_scrollbar_button_size" android:layout_height="@dimen/car_ui_scrollbar_button_size" android:layout_marginTop="15dp" android:src="@drawable/car_ui_recyclerview_ic_up" android:scaleType="centerInside" android:hapticFeedbackEnabled="false" android:contentDescription="@string/car_ui_scrollbar_page_up_button" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintRight_toRightOf="0" app:layout_constraintTop_toTopOf="0"/>
    <View android:layout_gravity="center_horizontal" android:id="@+id/car_ui_scrollbar_thumb" android:background="@drawable/car_ui_recyclerview_scrollbar_thumb" android:layout_width="@dimen/car_ui_scrollbar_thumb_width" android:layout_height="0dp" app:layout_constraintBottom_toBottomOf="@+id/car_ui_scrollbar_track" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintRight_toRightOf="0" app:layout_constraintTop_toTopOf="@+id/car_ui_scrollbar_track"/>
    <View android:id="@+id/car_ui_scrollbar_track" android:layout_width="0dp" android:layout_height="0dp" android:layout_marginTop="@dimen/car_ui_scrollbar_separator_margin" android:layout_marginBottom="@dimen/car_ui_scrollbar_separator_margin" app:layout_constraintBottom_toTopOf="@+id/car_ui_scrollbar_page_down" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toBottomOf="@+id/car_ui_scrollbar_page_up"/>
    <ImageView android:id="@+id/car_ui_scrollbar_page_down" android:background="@drawable/car_ui_recyclerview_button_ripple_background" android:focusable="false" android:layout_width="@dimen/car_ui_scrollbar_button_size" android:layout_height="@dimen/car_ui_scrollbar_button_size" android:layout_marginBottom="15dp" android:src="@drawable/car_ui_recyclerview_ic_down" android:scaleType="centerInside" android:hapticFeedbackEnabled="false" android:contentDescription="@string/car_ui_scrollbar_page_down_button" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintLeft_toLeftOf="0" app:layout_constraintRight_toRightOf="0"/>
</androidx.constraintlayout.widget.ConstraintLayout>
