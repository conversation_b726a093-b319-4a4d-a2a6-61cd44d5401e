package defpackage;

/* renamed from: bfd  reason: default package */
/* compiled from: PG */
public abstract class bfd implements bfg {
    public final bft a;

    public bfd(bft bft) {
        this.a = bft;
    }

    public final juo a(baq baq) {
        jnu.e(baq, "constraints");
        return new jug(new ava(this, (jlr) null, 3));
    }

    public final boolean c(bhe bhe) {
        jnu.e(bhe, "workSpec");
        if (!b(bhe) || !e(this.a.b())) {
            return false;
        }
        return true;
    }

    public abstract int d();

    public boolean e(Object obj) {
        throw null;
    }
}
