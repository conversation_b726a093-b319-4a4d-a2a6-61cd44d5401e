package defpackage;

import java.util.concurrent.Executor;

/* renamed from: eot  reason: default package */
/* compiled from: PG */
public final class eot extends jmi implements jne {
    int a;
    final /* synthetic */ Object b;
    final /* synthetic */ Object c;
    private /* synthetic */ Object d;
    private final /* synthetic */ int e;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eot(dya dya, Executor executor, jlr jlr, int i) {
        super(2, jlr);
        this.e = i;
        this.c = dya;
        this.b = executor;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        int i = this.e;
        if (i == 0) {
            return ((eot) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        } else if (i != 1) {
            return ((eot) c((jty) obj, (jlr) obj2)).bk(jkd.a);
        } else {
            return ((eot) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
    }

    /* JADX WARNING: type inference failed for: r6v5, types: [java.lang.Object, jna] */
    /* JADX WARNING: type inference failed for: r6v16, types: [java.lang.Object, eou] */
    /* JADX WARNING: type inference failed for: r0v6, types: [java.lang.Object, jna] */
    /* JADX WARNING: type inference failed for: r6v33, types: [java.lang.Object, eov] */
    /* JADX WARNING: type inference failed for: r2v7, types: [java.lang.Object, dya] */
    /* JADX WARNING: type inference failed for: r3v1, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: Code restructure failed: missing block: B:12:0x0046, code lost:
        if (defpackage.job.aa(r1, r6, r5) == r0) goto L_0x004c;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r6) {
        /*
            r5 = this;
            int r0 = r5.e
            r1 = 1
            if (r0 == 0) goto L_0x00ac
            if (r0 == r1) goto L_0x004d
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r5.a
            if (r2 == 0) goto L_0x001b
            if (r2 == r1) goto L_0x0013
            defpackage.jji.c(r6)
            goto L_0x0049
        L_0x0013:
            java.lang.Object r1 = r5.d
            jty r1 = (defpackage.jty) r1
            defpackage.jji.c(r6)
            goto L_0x003a
        L_0x001b:
            defpackage.jji.c(r6)
            java.lang.Object r6 = r5.d
            jty r6 = (defpackage.jty) r6
            java.lang.Object r2 = r5.c
            java.lang.Object r3 = r5.b
            evb r4 = new evb
            r4.<init>(r6, r3)
            hme r2 = r2.a(r4)
            r5.d = r6
            r5.a = r1
            java.lang.Object r1 = defpackage.jqw.x(r2, r5)
            if (r1 == r0) goto L_0x004c
            r1 = r6
        L_0x003a:
            auj r6 = defpackage.auj.e
            r2 = 0
            r5.d = r2
            r2 = 2
            r5.a = r2
            java.lang.Object r6 = defpackage.job.aa(r1, r6, r5)
            if (r6 != r0) goto L_0x0049
            goto L_0x004c
        L_0x0049:
            jkd r6 = defpackage.jkd.a
            return r6
        L_0x004c:
            return r0
        L_0x004d:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r5.a
            if (r2 == 0) goto L_0x0059
            defpackage.jji.c(r6)     // Catch:{ all -> 0x0057 }
            goto L_0x0078
        L_0x0057:
            r6 = move-exception
            goto L_0x007b
        L_0x0059:
            defpackage.jji.c(r6)
            java.lang.Object r6 = r5.d
            jqs r6 = (defpackage.jqs) r6
            java.lang.Object r6 = r5.c
            hme r6 = r6.f()     // Catch:{ all -> 0x0057 }
            hme r6 = defpackage.hfc.L(r6)     // Catch:{ all -> 0x0057 }
            java.lang.String r2 = "nonCancellationPropagating(...)"
            defpackage.jnu.d(r6, r2)     // Catch:{ all -> 0x0057 }
            r5.a = r1     // Catch:{ all -> 0x0057 }
            java.lang.Object r6 = defpackage.jqw.x(r6, r5)     // Catch:{ all -> 0x0057 }
            if (r6 != r0) goto L_0x0078
            goto L_0x00ab
        L_0x0078:
            dzm r6 = (defpackage.dzm) r6     // Catch:{ all -> 0x0057 }
            goto L_0x007f
        L_0x007b:
            java.lang.Object r6 = defpackage.jji.b(r6)
        L_0x007f:
            java.lang.Object r0 = r5.b
            java.lang.Throwable r1 = defpackage.jju.a(r6)
            if (r1 != 0) goto L_0x00a0
            dzm r6 = (defpackage.dzm) r6
            int r6 = r6.b
            dzx r6 = defpackage.dzx.b(r6)
            if (r6 != 0) goto L_0x0093
            dzx r6 = defpackage.dzx.UNKNOWN_DISCONNECT_REASON
        L_0x0093:
            eam r6 = defpackage.eki.g(r6)
            java.lang.String r1 = "convertDisconnectStatusToStopListeningReason(...)"
            defpackage.jnu.d(r6, r1)
            r0.a(r6)
            goto L_0x00a9
        L_0x00a0:
            boolean r6 = r1 instanceof java.util.concurrent.CancellationException
            if (r6 != 0) goto L_0x00a9
            eam r6 = defpackage.eam.ERROR_IN_GETTING_AUDIO_ROUTE_DISCONNECT_STATUS
            r0.a(r6)
        L_0x00a9:
            jkd r0 = defpackage.jkd.a
        L_0x00ab:
            return r0
        L_0x00ac:
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r5.a
            r3 = 0
            if (r2 == 0) goto L_0x00b9
            defpackage.jji.c(r6)     // Catch:{ all -> 0x00b7 }
            goto L_0x00cb
        L_0x00b7:
            r6 = move-exception
            goto L_0x00d9
        L_0x00b9:
            defpackage.jji.c(r6)
            java.lang.Object r6 = r5.d
            jqs r6 = (defpackage.jqs) r6
            java.lang.Object r6 = r5.c
            r5.a = r1     // Catch:{ all -> 0x00b7 }
            java.lang.Object r6 = r6.g(r5)     // Catch:{ all -> 0x00b7 }
            if (r6 != r0) goto L_0x00cb
            goto L_0x00f7
        L_0x00cb:
            eak r6 = (defpackage.eak) r6     // Catch:{ all -> 0x00b7 }
            int r6 = r6.a     // Catch:{ all -> 0x00b7 }
            if (r6 != r1) goto L_0x00d3
            r6 = r1
            goto L_0x00d4
        L_0x00d3:
            r6 = r3
        L_0x00d4:
            java.lang.Boolean r6 = java.lang.Boolean.valueOf(r6)     // Catch:{ all -> 0x00b7 }
            goto L_0x00dd
        L_0x00d9:
            java.lang.Object r6 = defpackage.jji.b(r6)
        L_0x00dd:
            java.lang.Boolean r0 = java.lang.Boolean.valueOf(r3)
            boolean r2 = r6 instanceof defpackage.jjt
            if (r1 != r2) goto L_0x00e6
            r6 = r0
        L_0x00e6:
            java.lang.Boolean r6 = (java.lang.Boolean) r6
            boolean r6 = r6.booleanValue()
            if (r6 != 0) goto L_0x00f5
            java.lang.Object r6 = r5.b
            eam r0 = defpackage.eam.FAILED_START_LISTENING
            r6.a(r0)
        L_0x00f5:
            jkd r0 = defpackage.jkd.a
        L_0x00f7:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eot.bk(java.lang.Object):java.lang.Object");
    }

    /* JADX WARNING: type inference failed for: r1v0, types: [java.lang.Object, jna] */
    /* JADX WARNING: type inference failed for: r2v0, types: [java.lang.Object, eou] */
    /* JADX WARNING: type inference failed for: r2v1, types: [java.lang.Object, eov] */
    /* JADX WARNING: type inference failed for: r3v1, types: [java.lang.Object, jna] */
    /* JADX WARNING: type inference failed for: r1v2, types: [java.lang.Object, dya] */
    /* JADX WARNING: type inference failed for: r2v2, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final jlr c(Object obj, jlr jlr) {
        int i = this.e;
        if (i == 0) {
            eot eot = new eot((jna) this.b, (eou) this.c, jlr, 0);
            eot.d = obj;
            return eot;
        } else if (i != 1) {
            eot eot2 = new eot((dya) this.c, (Executor) this.b, jlr, 2);
            eot2.d = obj;
            return eot2;
        } else {
            eot eot3 = new eot((eov) this.c, (jna) this.b, jlr, 1);
            eot3.d = obj;
            return eot3;
        }
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eot(eov eov, jna jna, jlr jlr, int i) {
        super(2, jlr);
        this.e = i;
        this.c = eov;
        this.b = jna;
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public eot(jna jna, eou eou, jlr jlr, int i) {
        super(2, jlr);
        this.e = i;
        this.b = jna;
        this.c = eou;
    }
}
