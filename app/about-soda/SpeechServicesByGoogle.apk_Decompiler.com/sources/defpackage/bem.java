package defpackage;

import android.content.Context;
import android.content.Intent;
import android.os.Looper;
import android.os.PowerManager;
import android.text.TextUtils;
import androidx.wear.ambient.AmbientModeSupport;
import androidx.work.impl.background.systemalarm.SystemAlarmService;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/* renamed from: bem  reason: default package */
/* compiled from: PG */
public final class bem implements bce {
    public static final String a = bbk.b("SystemAlarmDispatcher");
    final Context b;
    public final bis c;
    public final bcp d;
    public final bdm e;
    final bee f;
    final List g = new ArrayList();
    Intent h = null;
    public bek i;
    final cyw j;
    public final bxq k;
    private final bxq l;

    public bem(Context context) {
        Context applicationContext = context.getApplicationContext();
        this.b = applicationContext;
        bxq bxq = new bxq();
        this.l = bxq;
        bdm i2 = bdm.i(context);
        this.e = i2;
        AmbientModeSupport.AmbientCallback ambientCallback = i2.c.q;
        this.f = new bee(applicationContext, bxq);
        this.c = new bis(i2.c.e);
        bcp bcp = i2.f;
        this.d = bcp;
        cyw cyw = i2.k;
        this.j = cyw;
        this.k = new bxq(bcp, cyw);
        bcp.a(this);
    }

    public static final void e() {
        if (Looper.getMainLooper().getThread() != Thread.currentThread()) {
            throw new IllegalStateException("Needs to be invoked on the main thread.");
        }
    }

    /* JADX WARNING: type inference failed for: r5v3, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final void a(bgt bgt, boolean z) {
        Intent intent = new Intent(this.b, SystemAlarmService.class);
        intent.setAction("ACTION_EXECUTION_COMPLETED");
        intent.putExtra("KEY_NEEDS_RESCHEDULE", z);
        bee.f(intent, bgt);
        this.j.c.execute(new bej(this, intent, 0));
    }

    public final void b() {
        bbk.a();
        this.d.b(this);
        this.i = null;
    }

    public final void c() {
        e();
        PowerManager.WakeLock a2 = bil.a(this.b, "ProcessCommand");
        try {
            a2.acquire();
            this.e.k.a(new bei(this));
        } finally {
            a2.release();
        }
    }

    public final void d(Intent intent, int i2) {
        bbk.a();
        Objects.toString(intent);
        e();
        String action = intent.getAction();
        if (TextUtils.isEmpty(action)) {
            bbk.a().f(a, "Unknown command. Ignoring");
            return;
        }
        if ("ACTION_CONSTRAINTS_CHANGED".equals(action)) {
            e();
            synchronized (this.g) {
                for (Intent action2 : this.g) {
                    if ("ACTION_CONSTRAINTS_CHANGED".equals(action2.getAction())) {
                        return;
                    }
                }
            }
        }
        intent.putExtra("KEY_START_ID", i2);
        synchronized (this.g) {
            boolean isEmpty = this.g.isEmpty();
            this.g.add(intent);
            if (isEmpty) {
                c();
            }
        }
    }
}
