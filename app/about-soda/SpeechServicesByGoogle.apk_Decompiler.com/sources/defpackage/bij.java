package defpackage;

import androidx.work.impl.WorkDatabase;

/* renamed from: bij  reason: default package */
/* compiled from: PG */
public final class bij extends jnv implements jmp {
    final /* synthetic */ jna a;
    final /* synthetic */ WorkDatabase b;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bij(jna jna, WorkDatabase workDatabase) {
        super(0);
        this.a = jna;
        this.b = workDatabase;
    }

    public final Object a() {
        return this.a.a(this.b);
    }
}
