package defpackage;

/* renamed from: bfa  reason: default package */
/* compiled from: PG */
final class bfa implements jup {
    final /* synthetic */ bey a;
    final /* synthetic */ bhe b;

    public bfa(bey bey, bhe bhe) {
        this.a = bey;
        this.b = bhe;
    }

    public final /* bridge */ /* synthetic */ Object bK(Object obj, jlr jlr) {
        this.a.e(this.b, (wf) obj);
        return jkd.a;
    }
}
