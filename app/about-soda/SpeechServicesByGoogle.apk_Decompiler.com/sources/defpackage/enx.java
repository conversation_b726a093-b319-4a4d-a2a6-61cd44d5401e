package defpackage;

/* renamed from: enx  reason: default package */
/* compiled from: PG */
final class enx extends jme {
    /* synthetic */ Object a;
    final /* synthetic */ eny b;
    int c;
    eny d;
    jqh e;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public enx(eny eny, jlr jlr) {
        super(jlr);
        this.b = eny;
    }

    public final Object bk(Object obj) {
        this.a = obj;
        this.c |= Integer.MIN_VALUE;
        return this.b.c(this);
    }
}
