package defpackage;

/* renamed from: eid  reason: default package */
/* compiled from: PG */
public final class eid extends htq implements hvb {
    public static final eid a;
    private static volatile hvh b;

    static {
        eid eid = new eid();
        a = eid;
        htq.z(eid.class, eid);
    }

    private eid() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(a, "\u0001\u0000", (Object[]) null);
        }
        if (i2 == 3) {
            return new eid();
        }
        if (i2 == 4) {
            return new htk((htq) a);
        }
        if (i2 == 5) {
            return a;
        }
        if (i2 != 6) {
            return null;
        }
        hvh hvh = b;
        if (hvh == null) {
            synchronized (eid.class) {
                hvh = b;
                if (hvh == null) {
                    hvh = new htl(a);
                    b = hvh;
                }
            }
        }
        return hvh;
    }
}
