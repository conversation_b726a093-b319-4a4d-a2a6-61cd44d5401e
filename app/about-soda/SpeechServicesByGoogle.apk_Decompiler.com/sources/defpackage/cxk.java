package defpackage;

import android.content.Context;
import android.media.AudioRecord;
import androidx.wear.ambient.AmbientModeSupport;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;

/* renamed from: cxk  reason: default package */
/* compiled from: PG */
public final class cxk {
    public final Object a;
    public final Object b;

    public cxk(cyk cyk, cyi cyi) {
        this.a = cyk;
        this.b = cyi;
    }

    /* JADX WARNING: type inference failed for: r3v0, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final void a(dai dai) {
        synchronized (this.b) {
            Iterator it = ((CopyOnWriteArrayList) this.b).iterator();
            while (it.hasNext()) {
                this.a.execute(new ckm((Object) dai, it.next(), 18));
            }
        }
    }

    public final boolean b() {
        return ((CopyOnWriteArrayList) this.b).isEmpty();
    }

    public final void c(Object obj) {
        ((CopyOnWriteArrayList) this.b).add(obj);
    }

    public final void d(String str, long j, int i, grh grh) {
        String str2 = str;
        long j2 = j;
        int i2 = i;
        dad dad = new dad(this, str2, j2, i2);
        ftd.M(((fps) this.b).b(dad, hld.a), new dae(this, str2, j2, i2, 0), hld.a);
    }

    public final void e(String str, long j, int i, boolean z) {
        bbl bbl;
        gai a2 = gam.a(czz.class);
        bao bao = new bao();
        bao.b = true;
        bao.a = true;
        int i2 = i - 1;
        int i3 = 2;
        if (i2 == 0) {
            bbl = bbl.CONNECTED;
        } else if (i2 != 2) {
            bbl = bbl.UNMETERED;
        } else {
            bbl = bbl.NOT_REQUIRED;
        }
        bao.b(bbl);
        a2.b(bao.a());
        if (true == z) {
            i3 = 3;
        }
        a2.e(new gal(str, i3));
        a2.c(new gaj(new gak(j, TimeUnit.SECONDS), gqd.a));
        LinkedHashMap linkedHashMap = new LinkedHashMap();
        AmbientModeSupport.AmbientCallback.d("MDD_TASK_TAG_KEY", str, linkedHashMap);
        a2.f = AmbientModeSupport.AmbientCallback.a(linkedHashMap);
        ftd.M(((gnk) this.a).z(a2.a()), new daf(str, j), hld.a);
    }

    /* JADX WARNING: type inference failed for: r0v0, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r1v0, types: [java.lang.Object, jjk] */
    public final efj f(AudioRecord audioRecord, eeq eeq, grh grh, eel eel) {
        audioRecord.getClass();
        eeq.getClass();
        eel.getClass();
        ? r0 = this.a;
        return new efj(audioRecord, eeq, grh, eel, (dku) this.b.b(), ((Boolean) r0.b()).booleanValue());
    }

    /* JADX WARNING: type inference failed for: r2v0, types: [java.lang.Object, jjk] */
    public final eff g(String str) {
        str.getClass();
        Context a2 = ((iim) this.a).a();
        ((doe) this.b.b()).getClass();
        return new eff(str, a2);
    }

    /* JADX WARNING: type inference failed for: r1v0, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r2v0, types: [java.lang.Object, jjk] */
    /* JADX WARNING: type inference failed for: r1v1, types: [java.lang.Object, ihn] */
    public final eeu h(dyt dyt) {
        dyt.getClass();
        ? r1 = this.b;
        Object b2 = this.a.b();
        ? b3 = r1.b();
        b3.getClass();
        return new eeu(dyt, (cxk) b2, b3);
    }

    public cxk(fps fps, gnk gnk) {
        this.b = fps;
        this.a = gnk;
    }

    public cxk(Executor executor, Executor executor2) {
        this.a = executor;
        this.b = executor2;
    }

    public cxk(Executor executor) {
        this.b = new CopyOnWriteArrayList();
        this.a = executor;
    }

    public cxk(jjk jjk, jjk jjk2, char[] cArr) {
        jjk.getClass();
        this.b = jjk;
        jjk2.getClass();
        this.a = jjk2;
    }

    public cxk(jjk jjk, jjk jjk2, jjk jjk3) {
        jjk.getClass();
        jjk2.getClass();
        this.a = jjk2;
        this.b = jjk3;
    }

    public cxk(jjk jjk, jjk jjk2, byte[] bArr) {
        jjk.getClass();
        this.a = jjk;
        jjk2.getClass();
        this.b = jjk2;
    }

    public cxk(jjk jjk, jjk jjk2) {
        jjk.getClass();
        this.b = jjk;
        jjk2.getClass();
        this.a = jjk2;
    }
}
