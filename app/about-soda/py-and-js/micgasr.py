#!/usr/bin/env python3
import sys
import ctypes
import time
import pyaudio

from soda_api_pb2 import ExtendedSodaConfigMsg, SodaResponse, SodaRecognitionResult

CHANNEL_COUNT = 1
SAMPLE_RATE = 16000
CHUNK_SIZE = 2048  # 2 chunks per frame, a frame is a single s16

CALLBACK = ctypes.CFUNCTYPE(None, ctypes.POINTER(ctypes.c_byte), ctypes.c_int, ctypes.c_void_p)


class SodaConfig(ctypes.Structure):
    _fields_ = [('soda_config', ctypes.c_char_p),
                ('soda_config_size', ctypes.c_int),
                ('callback', CALLBACK),
                ('callback_handle', ctypes.c_void_p)]


class SodaClient():
    def __init__(self, callback=None):
        self.sodalib = ctypes.CDLL('./libsoda_ok.so')
        if callback is None:
            callback = CALLBACK(self.resultHandler)
        else:
            callback = CALLBACK(callback)
        cfg_proto = ExtendedSodaConfigMsg()
        cfg_proto.channel_count = CHANNEL_COUNT
        cfg_proto.sample_rate = SAMPLE_RATE
        cfg_proto.api_key = 'ce04d119-129f-404e-b4fe-6b913fffb6cb'
        cfg_proto.language_pack_directory = './asr_en-US_v3056/'
        cfg_serialized = cfg_proto.SerializeToString()
        self.config = SodaConfig(cfg_serialized, len(cfg_serialized), callback, None)
        self.sodalib.CreateExtendedSodaAsync.restype = ctypes.c_void_p

    def start(self):
        self.handle = ctypes.c_void_p(self.sodalib.CreateExtendedSodaAsync(self.config))
        self.sodalib.ExtendedSodaStart(self.handle)

        # 初始化 PyAudio
        p = pyaudio.PyAudio()

        # 打开麦克风流
        stream = p.open(format=pyaudio.paInt16,
                        channels=CHANNEL_COUNT,
                        rate=SAMPLE_RATE,
                        input=True,
                        frames_per_buffer=CHUNK_SIZE)

        print("开始录音... 按 Ctrl+C 停止")
        try:
            while True:
                # 从麦克风读取音频数据
                audio = stream.read(CHUNK_SIZE)
                self.sodalib.ExtendedAddAudio(self.handle, audio, len(audio))
                time.sleep(0.1)  # 模拟流延迟，可根据需要调整
        except KeyboardInterrupt:
            print("停止录音")
        finally:
            # 关闭流和 PyAudio
            stream.stop_stream()
            stream.close()
            p.terminate()

    def delete(self):
        self.sodalib.DeleteExtendedSodaAsync(self.handle)

    def resultHandler(self, response, rlen, instance):
        res = SodaResponse()
        res.ParseFromString(ctypes.string_at(response, rlen))
        if res.soda_type == SodaResponse.SodaMessageType.RECOGNITION:
            if res.recognition_result.result_type == SodaRecognitionResult.ResultType.FINAL:
                print(f'## {res.recognition_result.hypothesis[0]}')
            elif res.recognition_result.result_type == SodaRecognitionResult.ResultType.PARTIAL:
                print(f'** {res.recognition_result.hypothesis[0]}', end='\r')


if __name__ == '__main__':
    client = SodaClient()
    try:
        client.start()
    except KeyboardInterrupt:
        client.delete()