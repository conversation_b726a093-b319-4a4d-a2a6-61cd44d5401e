package defpackage;

import android.content.Context;

/* renamed from: bcq  reason: default package */
/* compiled from: PG */
public final class bcq extends avu {
    private final Context c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bcq(Context context, int i, int i2) {
        super(i, i2);
        jnu.e(context, "mContext");
        this.c = context;
    }

    public final void a(awl awl) {
        if (this.b >= 10) {
            awl.l(new Object[]{"reschedule_needed", 1});
            return;
        }
        this.c.getSharedPreferences("androidx.work.util.preferences", 0).edit().putBoolean("reschedule_needed", true).apply();
    }
}
