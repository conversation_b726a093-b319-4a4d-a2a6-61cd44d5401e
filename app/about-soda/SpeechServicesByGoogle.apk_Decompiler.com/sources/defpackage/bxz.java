package defpackage;

import android.app.Activity;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.MenuItem;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.Animation;
import com.google.android.apps.speech.tts.googletts.settings.asr.NondownloadedLanguageDialogPreference;
import com.google.android.tts.R;
import java.util.Locale;

/* renamed from: bxz  reason: default package */
/* compiled from: PG */
public final class bxz extends bxw implements iip, gfb, ggp, gmy {
    private bya ag;
    private Context ah;
    private final abf ai = new abf(this);
    private boolean aj;
    private final jfc al = new jfc((bc) this);

    @Deprecated
    public bxz() {
        fnk.c();
    }

    public final void C(boolean z) {
        L();
    }

    /* access modifiers changed from: protected */
    public final /* synthetic */ iig H() {
        return new ggt(this);
    }

    @Deprecated
    public final Context I() {
        if (this.ah == null) {
            this.ah = new ggq((bc) this, super.getContext());
        }
        return this.ah;
    }

    public final goi K() {
        return (goi) this.al.c;
    }

    public final bya L() {
        bya bya = this.ag;
        if (bya == null) {
            throw new IllegalStateException("peer() called before initialized.");
        } else if (!this.aj) {
            return bya;
        } else {
            throw new IllegalStateException("peer() called after destroyed.");
        }
    }

    public final Locale M() {
        return fvf.s(this);
    }

    public final void N(goi goi, boolean z) {
        this.al.c(goi, z);
    }

    /* access modifiers changed from: protected */
    public final void aT(dm dmVar) {
        bya L = L();
        ((hdc) ((hdc) bya.a.b()).j("com/google/android/apps/speech/tts/googletts/settings/asr/NondownloadedLanguageDialogFragmentPeer", "onPrepareDialogBuilder", 85, "NondownloadedLanguageDialogFragmentPeer.java")).r("#onPrepareDialogBuilder");
        NondownloadedLanguageDialogPreference nondownloadedLanguageDialogPreference = (NondownloadedLanguageDialogPreference) L.b.G();
        byn byn = nondownloadedLanguageDialogPreference.a;
        if (bya.a(nondownloadedLanguageDialogPreference)) {
            L.e.a(dwv.aq);
            gnf gnf = new gnf(L.g, "nondownloadedDialogTrace", L.b);
            String string = L.d.getString(R.string.pending_title, new Object[]{byn.d});
            Context context = L.d;
            dm dmVar2 = dmVar;
            adi.j(dmVar2, gnf, string, context.getString(R.string.pending_language_pack_details, new Object[]{byn.d, adi.h(context, byn.a)}), L.d.getString(R.string.stop_pending_download_prompt), L.d.getString(R.string.cancel_prompt));
            return;
        }
        L.e.a(dwv.ao);
        gnf gnf2 = new gnf(L.g, "nondownloadedDialogTrace", L.b);
        String string2 = L.d.getString(R.string.download_title, new Object[]{byn.d});
        Context context2 = L.d;
        dm dmVar3 = dmVar;
        adi.j(dmVar3, gnf2, string2, context2.getString(R.string.download_language_pack_details, new Object[]{adi.h(context2, byn.a)}), L.d.getString(R.string.download_prompt), L.d.getString(R.string.cancel_prompt));
    }

    public final Context getContext() {
        if (super.getContext() == null) {
            return null;
        }
        return I();
    }

    public final aba getLifecycle() {
        return this.ai;
    }

    public final void onActivityCreated(Bundle bundle) {
        this.al.j();
        try {
            super.onActivityCreated(bundle);
            gll.k();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onActivityResult(int i, int i2, Intent intent) {
        gnd d = this.al.d();
        try {
            super.onActivityResult(i, i2, intent);
            d.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onAttach(Activity activity) {
        this.al.j();
        try {
            super.onAttach(activity);
            gll.k();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onCancel(DialogInterface dialogInterface) {
        gnd e = this.al.e();
        try {
            L().e.a(dwv.ar.c(2));
            e.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onClick(DialogInterface dialogInterface, int i) {
        this.ak = i;
        bya L = L();
        ((hdc) ((hdc) bya.a.f()).j("com/google/android/apps/speech/tts/googletts/settings/asr/NondownloadedLanguageDialogFragmentPeer", "onClick", 130, "NondownloadedLanguageDialogFragmentPeer.java")).r("#onClick");
        if (i != -1) {
            L.e.a(dwv.ar.c(2));
            return;
        }
        NondownloadedLanguageDialogPreference nondownloadedLanguageDialogPreference = (NondownloadedLanguageDialogPreference) L.b.G();
        if (bya.a(nondownloadedLanguageDialogPreference)) {
            ((hdc) ((hdc) bya.a.f()).j("com/google/android/apps/speech/tts/googletts/settings/asr/NondownloadedLanguageDialogFragmentPeer", "onClick", 140, "NondownloadedLanguageDialogFragmentPeer.java")).r("#onClick starting LP uninstall");
            L.c.b(L.i, nondownloadedLanguageDialogPreference.a.a.a);
        } else {
            ((hdc) ((hdc) bya.a.f()).j("com/google/android/apps/speech/tts/googletts/settings/asr/NondownloadedLanguageDialogFragmentPeer", "onClick", 145, "NondownloadedLanguageDialogFragmentPeer.java")).r("#onClick starting LP download");
            byu byu = L.c;
            bys bys = L.h;
            exo exo = nondownloadedLanguageDialogPreference.a.a;
            byu.a(bys, exo.a, exo.b);
        }
        L.e.a(dwv.ar.c(1));
    }

    public final void onCreate(Bundle bundle) {
        this.al.j();
        try {
            super.onCreate(bundle);
            bya L = L();
            ((hdc) ((hdc) bya.a.b()).j("com/google/android/apps/speech/tts/googletts/settings/asr/NondownloadedLanguageDialogFragmentPeer", "onCreate", 78, "NondownloadedLanguageDialogFragmentPeer.java")).r("#onCreate");
            L.f.b(L.h);
            L.f.b(L.i);
            gll.k();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final Animation onCreateAnimation(int i, boolean z, int i2) {
        this.al.f(i, i2);
        gll.k();
        return null;
    }

    public final View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        this.al.j();
        try {
            View onCreateView = super.onCreateView(layoutInflater, viewGroup, bundle);
            gll.k();
            return onCreateView;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onDestroy() {
        gnd k = jfc.k(this.al);
        try {
            super.onDestroy();
            k.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onDestroyView() {
        gnd k = jfc.k(this.al);
        try {
            super.onDestroyView();
            k.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onDetach() {
        gnd a = this.al.a();
        try {
            super.onDetach();
            this.aj = true;
            a.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onDismiss(DialogInterface dialogInterface) {
        gnd g = this.al.g();
        try {
            super.onDismiss(dialogInterface);
            g.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final LayoutInflater onGetLayoutInflater(Bundle bundle) {
        this.al.j();
        try {
            LayoutInflater onGetLayoutInflater = super.onGetLayoutInflater(bundle);
            LayoutInflater cloneInContext = onGetLayoutInflater.cloneInContext(new ggq((bc) this, onGetLayoutInflater));
            gll.k();
            return cloneInContext;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final boolean onOptionsItemSelected(MenuItem menuItem) {
        this.al.h().close();
        return false;
    }

    public final void onPause() {
        this.al.j();
        try {
            super.onPause();
            gll.k();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onResume() {
        gnd k = jfc.k(this.al);
        try {
            super.onResume();
            k.close();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onSaveInstanceState(Bundle bundle) {
        this.al.j();
        try {
            super.onSaveInstanceState(bundle);
            gll.k();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onStart() {
        this.al.j();
        try {
            super.onStart();
            ftd.C(this);
            if (this.b) {
                ftd.B(this);
            }
            gll.k();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onStop() {
        this.al.j();
        try {
            super.onStop();
            gll.k();
            return;
        } catch (Throwable th) {
            th.addSuppressed(th);
        }
        throw th;
    }

    public final void onViewCreated(View view, Bundle bundle) {
        this.al.j();
        gll.k();
    }

    public final void setEnterTransition(Object obj) {
        jfc jfc = this.al;
        if (jfc != null) {
            jfc.b(true);
        }
        super.setEnterTransition(obj);
    }

    public final void setExitTransition(Object obj) {
        jfc jfc = this.al;
        if (jfc != null) {
            jfc.b(true);
        }
        super.setExitTransition(obj);
    }

    public final void setReenterTransition(Object obj) {
        jfc jfc = this.al;
        if (jfc != null) {
            jfc.b(true);
        }
        super.setReenterTransition(obj);
    }

    public final void setRetainInstance(boolean z) {
        a.j(z);
    }

    public final void setReturnTransition(Object obj) {
        jfc jfc = this.al;
        if (jfc != null) {
            jfc.b(true);
        }
        super.setReturnTransition(obj);
    }

    public final void setSharedElementEnterTransition(Object obj) {
        jfc jfc = this.al;
        if (jfc != null) {
            jfc.b(true);
        }
        super.setSharedElementEnterTransition(obj);
    }

    public final void setSharedElementReturnTransition(Object obj) {
        jfc jfc = this.al;
        if (jfc != null) {
            jfc.b(true);
        }
        super.setSharedElementReturnTransition(obj);
    }

    public final void startActivity(Intent intent) {
        if (ftd.m(intent, getContext().getApplicationContext())) {
            gof.j(intent);
        }
        super.startActivity(intent);
    }

    public final void startActivity(Intent intent, Bundle bundle) {
        if (ftd.m(intent, getContext().getApplicationContext())) {
            gof.j(intent);
        }
        super.startActivity(intent, bundle);
    }

    public final void onAttach(Context context) {
        this.al.j();
        try {
            if (!this.aj) {
                super.onAttach(context);
                if (this.ag == null) {
                    Object bo = bo();
                    bc bcVar = ((bqz) bo).a;
                    if (bcVar instanceof bxz) {
                        bxz bxz = (bxz) bcVar;
                        hzz.u(bxz);
                        this.ag = new bya(bxz, ((bqz) bo).b(), ((bqz) bo).f.a(), (dwj) ((bqz) bo).e.j.b(), (fzv) ((bqz) bo).c.b(), (gnk) ((bqz) bo).e.g.b());
                        this.Z.b(new gga(this.al, this.ai));
                    } else {
                        throw new IllegalStateException(a.ay(bcVar, bya.class, "Attempt to inject a Fragment wrapper of type "));
                    }
                }
                bc bcVar2 = this.D;
                if (bcVar2 instanceof gmy) {
                    jfc jfc = this.al;
                    if (jfc.c == null) {
                        jfc.c(((gmy) bcVar2).K(), true);
                    }
                }
                gll.k();
                return;
            }
            throw new IllegalStateException("A Fragment cannot be attached more than once. Instead, create a new Fragment instance.");
        } catch (ClassCastException e) {
            throw new IllegalStateException("Missing entry point. If you're in a test with explicit entry points specified in your @TestRoot, check that you're not missing the one for this class.", e);
        } catch (Throwable th) {
            try {
                gll.k();
            } catch (Throwable th2) {
                th.addSuppressed(th2);
            }
            throw th;
        }
    }
}
