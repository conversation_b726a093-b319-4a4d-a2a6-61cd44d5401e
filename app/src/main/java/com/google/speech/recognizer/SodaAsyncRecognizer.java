package com.google.speech.recognizer;

import android.util.Log;
import com.google.android.apps.googlespeechoffline.speech.CustomRecognizer;
import com.google.android.apps.googlespeechoffline.speech.CustomSoda;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * SODA异步识别器 - 使用现有的CustomRecognizer实现异步功能
 * 收集音频数据，然后一次性进行识别
 */
public class SodaAsyncRecognizer {
    private static final String TAG = "SodaAsyncRecognizer";

    private CustomRecognizer recognizer;
    private RecognitionResultCallback callback;
    private ByteArrayOutputStream audioBuffer;
    private ExecutorService executorService;
    private boolean isStarted = false;
    private String modelPath;
    private byte[] configData;
    
    /**
     * 创建异步SODA识别器
     * @param config 配置数据
     * @param callback 结果回调
     */
    public SodaAsyncRecognizer(byte[] config, RecognitionResultCallback callback) {
        this.callback = callback;
        this.configData = config;
        this.executorService = Executors.newSingleThreadExecutor();
        this.audioBuffer = new ByteArrayOutputStream();

        Log.d(TAG, "Created SODA async recognizer with config size: " + config.length);
    }

    /**
     * 设置模型路径
     * @param modelPath 模型路径
     */
    public void setModelPath(String modelPath) {
        this.modelPath = modelPath;
        Log.d(TAG, "Set model path: " + modelPath);
    }
    
    /**
     * 开始识别 - 准备接收音频数据
     */
    public void start() {
        if (isStarted) {
            Log.w(TAG, "Recognition already started");
            return;
        }

        isStarted = true;
        Log.d(TAG, "SODA async recognizer ready to receive audio data");
    }

    /**
     * 完成音频数据添加并开始识别
     */
    public void finishAndRecognize() {
        if (!isStarted) {
            Log.w(TAG, "Recognition not started");
            return;
        }

        Log.d(TAG, "Starting recognition with " + audioBuffer.size() + " bytes of audio data");

        // 在后台线程中运行识别
        executorService.submit(() -> {
            try {
                // 使用设置的模型路径，如果没有设置则使用默认路径
                String useModelPath = modelPath != null ? modelPath : "/storage/emulated/0/Android/asr/model/asr_en-US_v3056";

                Log.d(TAG, "Creating recognizer with model path: " + useModelPath);
                Log.d(TAG, "Using config data size: " + configData.length);

                // 将收集的音频数据转换为输入流
                byte[] audioData = audioBuffer.toByteArray();
                ByteArrayInputStream audioInputStream = new ByteArrayInputStream(audioData);

                // 创建识别器
                recognizer = CustomRecognizer.create(audioInputStream, configData, useModelPath);
                recognizer.setRecognitionCallback(callback);

                Log.d(TAG, "Starting recognition process...");

                // 运行识别
                byte[] params = new byte[] { 0x15, 0x00, 0x00, 0x7A, 0x46, 0x18, 0x01 };
                byte[] result = recognizer.run(params);

                Log.d(TAG, "Recognition completed, result size: " + (result != null ? result.length : 0));

            } catch (Exception e) {
                Log.e(TAG, "Recognition failed", e);
                if (callback != null) {
                    callback.onParseError("Recognition failed: " + e.getMessage(), null);
                }
            }
        });
    }
    
    /**
     * 添加音频数据 - 恢复原始功能
     * @param audioData 音频数据
     */
    public void addAudio(byte[] audioData) {
        if (!isStarted || audioData == null || audioData.length == 0) {
            return;
        }

        try {
            audioBuffer.write(audioData);
            //Log.v(TAG, "Added audio data: " + audioData.length + " bytes, total: " + audioBuffer.size());
        } catch (IOException e) {
            Log.e(TAG, "Failed to add audio data", e);
            if (callback != null) {
                callback.onParseError("Failed to add audio: " + e.getMessage(), audioData);
            }
        }
    }

    /**
     * 获取当前音频缓冲区数据（用于实时识别）
     */
    public byte[] getCurrentAudioBuffer() {
        if (audioBuffer == null) {
            return null;
        }
        return audioBuffer.toByteArray();
    }

    /**
     * 获取模型路径
     */
    public String getModelPath() {
        return modelPath;
    }

    /**
     * 删除识别器
     */
    public void delete() {
        if (!isStarted) {
            return;
        }

        isStarted = false;
        Log.d(TAG, "Deleting SODA async recognizer");

        try {
            if (audioBuffer != null) {
                audioBuffer.close();
            }
        } catch (IOException e) {
            Log.e(TAG, "Error closing audio buffer", e);
        }

        if (recognizer != null) {
            recognizer.close();
            recognizer = null;
        }

        if (executorService != null) {
            executorService.shutdown();
        }
    }
    
    @Override
    protected void finalize() throws Throwable {
        delete();
        super.finalize();
    }
}
