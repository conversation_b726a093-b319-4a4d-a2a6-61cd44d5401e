package defpackage;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/* renamed from: cvi  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvi implements hko {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ cvi(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r2v16, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final hme a(Object obj) {
        grh grh;
        int A;
        hme hme;
        long j;
        switch (this.b) {
            case 0:
                csx csx = (csx) obj;
                if (csx == null) {
                    grh = grh.h(hix.GROUP_NOT_FOUND);
                } else {
                    grh = cvy.a((csx) this.a, csx);
                }
                return hfc.K(grh);
            case 1:
                csx csx2 = (csx) obj;
                if (csx2 == null || (A = cqh.A(csx2.q)) == 0 || A == 1) {
                    return hfc.K(true);
                }
                return ((dah) ((gsb) ((cvy) this.a).g.b()).a()).b();
            case 2:
                if (((Boolean) obj).booleanValue()) {
                    return hma.a;
                }
                ((cvy) this.a).i.d(1036);
                return hfc.J(new IOException("Failed to commit new group metadata to disk."));
            case 3:
                grh grh2 = (grh) obj;
                if (!grh2.f()) {
                    return hma.a;
                }
                Object obj2 = this.a;
                cvy cvy = (cvy) obj2;
                return cvy.q(cvy.c.a((csx) grh2.b()), new cvi(obj2, 6));
            case 4:
                if (!((Boolean) obj).booleanValue()) {
                    ((cvy) this.a).i.d(1036);
                }
                return hma.a;
            case 5:
                ArrayList arrayList = new ArrayList();
                Iterator it = ((List) obj).iterator();
                while (true) {
                    Object obj3 = this.a;
                    if (!it.hasNext()) {
                        return cqh.U(arrayList).n(new ctw(5), ((cvy) obj3).e);
                    }
                    ctg ctg = (ctg) it.next();
                    cvy cvy2 = (cvy) obj3;
                    if (!cvy2.t(ctg.c)) {
                        arrayList.add(cvy2.q(cvy2.c.g(ctg), new bpt(obj3, ctg, 18, (byte[]) null)));
                    }
                }
            case 6:
                if (!((Boolean) obj).booleanValue()) {
                    ((cvy) this.a).i.d(1036);
                }
                return hma.a;
            case 7:
                Void voidR = (Void) obj;
                throw ((Throwable) this.a);
            case 8:
                cxg cxg = (cxg) obj;
                ctg ctg2 = cxg.a;
                csx csx3 = cxg.b;
                if (!ctg2.e || !cqx.o(csx3)) {
                    return hma.a;
                }
                Object obj4 = this.a;
                if (!cqh.j() || !cqx.o(csx3)) {
                    hme = hfc.K(true);
                } else {
                    cvy cvy3 = (cvy) obj4;
                    hme = czw.e(cvy3.k(csx3)).f(new btb(obj4, csx3, 4, (byte[]) null), cvy3.e);
                }
                return ((cvy) obj4).q(hme, new cvn(obj4, (htq) csx3, 5));
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                ArrayList arrayList2 = new ArrayList();
                Iterator it2 = ((List) obj).iterator();
                while (true) {
                    Object obj5 = this.a;
                    if (!it2.hasNext()) {
                        return cqh.U(arrayList2).n(new ctw(3), ((cvy) obj5).e);
                    }
                    ctg ctg3 = (ctg) it2.next();
                    if (!ctg3.e) {
                        cvy cvy4 = (cvy) obj5;
                        arrayList2.add(cvy4.q(cvy4.c.g(ctg3), new cvi(obj5, 1)));
                    }
                }
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                cxg cxg2 = (cxg) obj;
                csx csx4 = cxg2.b;
                for (csv csv : csx4.n) {
                    int x = a.x(csx4.i);
                    if (x == 0) {
                        x = 1;
                    }
                    Object obj6 = this.a;
                    ctj t = cqh.t(csv, x);
                    cvy cvy5 = (cvy) obj6;
                    dbw dbw = cvy5.j;
                    ftd.G(czw.e(dbw.n(t)).g(new cwt(dbw, (htq) t, (htq) csv, 1), dbw.l), cwz.class, new bsx(obj6, (Object) csx4, (Object) cxg2, 11), cvy5.e);
                }
                return hma.a;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                csx csx5 = (csx) obj;
                Object obj7 = this.a;
                if (csx5 == null || !cvy.s((csx) obj7, csx5)) {
                    j = cqx.c();
                } else {
                    csw csw = csx5.b;
                    if (csw == null) {
                        csw = csw.i;
                    }
                    j = csw.c;
                }
                csw csw2 = ((csx) obj7).b;
                if (csw2 == null) {
                    csw2 = csw.i;
                }
                htk htk = (htk) csw2.C(5);
                htk.x(csw2);
                if (!htk.b.B()) {
                    htk.u();
                }
                csw csw3 = (csw) htk.b;
                csw3.a = 2 | csw3.a;
                csw3.c = j;
                csw csw4 = (csw) htk.r();
                htq htq = (htq) obj7;
                htk htk2 = (htk) htq.C(5);
                htk2.x(htq);
                if (!htk2.b.B()) {
                    htk2.u();
                }
                csx csx6 = (csx) htk2.b;
                csw4.getClass();
                csx6.b = csw4;
                csx6.a |= 1;
                return hfc.K((csx) htk2.r());
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                cyh.o((csi) obj, "%s: Unable to correct isolated structure, returning null instead of group %s", "FileGroupManager", ((csx) this.a).c);
                return hma.a;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                Object obj8 = this.a;
                cwb cwb = (cwb) obj8;
                return ftd.L(cwb.n(cwb.a.e()), new cvn(obj8, (Object) (cze) obj, 19), cwb.b);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                Object obj9 = this.a;
                cwb cwb2 = (cwb) obj9;
                return ftd.L(cwb2.n(cwb2.a.b()), new cwa(obj9, (Object) (cze) obj, 5), cwb2.b);
            case 15:
                Object obj10 = this.a;
                cwb cwb3 = (cwb) obj10;
                return ftd.L(cwb3.n(cwb3.a.k()), new cvn(obj10, (Object) (cze) obj, 13), cwb3.b);
            case 16:
                Object obj11 = this.a;
                cwd cwd = (cwd) obj11;
                return ftd.L(cwd.a.c(), new cwa(obj11, (Object) (Boolean) obj, 15), cwd.c);
            case 17:
                Object obj12 = this.a;
                cwd cwd2 = (cwd) obj12;
                return ftd.L(cwd2.b(cwd2.b.a()), new cwa(obj12, (Object) (cze) obj, 10), cwd2.c);
            case 18:
                Void voidR2 = (Void) obj;
                return ((cwm) this.a).d.b();
            case 19:
                Void voidR3 = (Void) obj;
                Object obj13 = this.a;
                cwm cwm = (cwm) obj13;
                return ftd.L(cwm.e.a(), new cvi(obj13, 18), cwm.h);
            default:
                if (((Boolean) obj).booleanValue()) {
                    return hma.a;
                }
                Object obj14 = this.a;
                cyh.m("%s Clearing MDD since FileManager failed or needs migration.", "MDDManager");
                return ((cwm) obj14).a();
        }
    }
}
