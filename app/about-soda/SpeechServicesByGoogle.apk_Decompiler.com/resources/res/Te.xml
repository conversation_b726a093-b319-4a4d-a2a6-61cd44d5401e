<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:orientation="vertical" android:layout_width="wrap_content" android:layout_height="wrap_content">
    <ImageView android:layout_width="@dimen/analytics_info_image_size" android:layout_height="@dimen/analytics_info_image_size" android:layout_marginTop="@dimen/analytics_info_image_margin" android:src="@drawable/infoIcon" android:contentDescription="@string/analytics_info_icon_description" android:layout_marginStart="@dimen/analytics_info_image_margin"/>
    <TextView android:textAppearance="?android:attr/textAppearanceSmall" android:layout_gravity="center_horizontal" android:id="@+id/analytics_screen_explanation" android:layout_width="wrap_content" android:layout_height="wrap_content" android:text="@string/analytics_screen_explanation" android:layout_marginStart="@dimen/analytics_summary_margin_left" android:layout_marginEnd="@dimen/analytics_summary_margin_right" android:layout_marginVertical="@dimen/analytics_summary_margin_vertical"/>
</LinearLayout>
