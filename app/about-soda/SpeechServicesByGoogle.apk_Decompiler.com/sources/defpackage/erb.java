package defpackage;

/* renamed from: erb  reason: default package */
/* compiled from: PG */
public final class erb extends htq implements hvb {
    public static final erb f;
    private static volatile hvh h;
    public int a;
    public int b = 0;
    public Object c;
    public era d;
    public huf e = hvk.a;
    private byte g = 2;

    static {
        erb erb = new erb();
        f = erb;
        htq.z(erb.class, erb);
    }

    private erb() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return Byte.valueOf(this.g);
        }
        byte b2 = 1;
        if (i2 == 2) {
            return new hvl(f, "\u0004\u0004\u0001\u0001\u0001\u0004\u0004\u0000\u0001\u0002\u0001ᐉ\u0000\u0002Л\u0003<\u0000\u0004࠿\u0000", new Object[]{"c", "b", "a", "d", "e", era.class, ecc.class, ebb.m});
        } else if (i2 == 3) {
            return new erb();
        } else {
            if (i2 == 4) {
                return new htk((htq) f);
            }
            if (i2 == 5) {
                return f;
            }
            if (i2 != 6) {
                if (obj == null) {
                    b2 = 0;
                }
                this.g = b2;
                return null;
            }
            hvh hvh = h;
            if (hvh == null) {
                synchronized (erb.class) {
                    hvh = h;
                    if (hvh == null) {
                        hvh = new htl(f);
                        h = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
