package defpackage;

/* renamed from: esq  reason: default package */
/* compiled from: PG */
public final class esq implements eso {
    private final ebi a;
    private final int b;
    private final ejn c;
    private final eab d;
    private final boolean e = true;

    public esq(ebi ebi, int i, ejn ejn, eab eab) {
        jnu.e(ebi, "session");
        jnu.e(ejn, "routeData");
        jnu.e(eab, "params");
        this.a = ebi;
        this.b = i;
        this.c = ejn;
        this.d = eab;
    }

    public static /* synthetic */ esq g(esq esq, int i, eab eab, int i2) {
        ebi ebi;
        ejn ejn = null;
        if ((i2 & 1) != 0) {
            ebi = esq.a;
        } else {
            ebi = null;
        }
        if ((i2 & 2) != 0) {
            i = esq.b;
        }
        if ((i2 & 4) != 0) {
            ejn = esq.c;
        }
        if ((i2 & 8) != 0) {
            eab = esq.d;
        }
        jnu.e(ebi, "session");
        jnu.e(ejn, "routeData");
        jnu.e(eab, "params");
        return new esq(ebi, i, ejn, eab);
    }

    public final int a() {
        return this.b;
    }

    public final eab b() {
        return this.d;
    }

    public final ebi c() {
        return this.a;
    }

    public final ejn d() {
        return this.c;
    }

    public final /* synthetic */ eow e() {
        return fbi.w(this);
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof esq)) {
            return false;
        }
        esq esq = (esq) obj;
        if (!jnu.i(this.a, esq.a) || this.b != esq.b || !jnu.i(this.c, esq.c) || !jnu.i(this.d, esq.d)) {
            return false;
        }
        boolean z = esq.e;
        return true;
    }

    public final boolean f() {
        return true;
    }

    public final int hashCode() {
        int i;
        int hashCode = (((this.a.hashCode() * 31) + this.b) * 31) + this.c.hashCode();
        eab eab = this.d;
        if (eab.B()) {
            i = eab.i();
        } else {
            int i2 = eab.memoizedHashCode;
            if (i2 == 0) {
                i2 = eab.i();
                eab.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (((hashCode * 31) + i) * 31) + a.f(true);
    }

    public final String toString() {
        return "HotwordRouteSessionDataSimple(session=" + this.a + ", routeToken=" + this.b + ", routeData=" + this.c + ", params=" + this.d + ", isInactive=true)";
    }
}
