package defpackage;

/* renamed from: bro  reason: default package */
/* compiled from: PG */
public final class bro implements ahn {
    private int a;

    public final void a(agv agv, boolean z, int i) {
        int i2 = this.a + i;
        this.a = i2;
        if (i2 > 2000000) {
            throw new IllegalStateException("Downloaded file exceeded size limit");
        }
    }

    public final void c() {
        this.a = 0;
    }

    public final void b(agv agv, boolean z) {
    }

    public final void d(agv agv, boolean z) {
    }
}
