package defpackage;

import j$.time.Duration;

/* renamed from: elp  reason: default package */
/* compiled from: PG */
public interface elp {
    void a(String str, int i);

    void b(String str, int i);

    void c(String str, int i);

    void d(String str);

    void e(String str, int i);

    void f(String str, Integer num);

    void g(String str, int i);

    void h(ehg ehg, int i, dym dym);

    void i(ehg ehg, int i, dyl dyl);

    void j(ehg ehg);

    void k(ehg ehg, ebl ebl, long j, ejn ejn);

    void l(ehg ehg, dyy dyy, long j, dze dze, ejn ejn);

    void m(ehg ehg, Integer num);

    void n(ehg ehg, Integer num, dzq dzq, hme hme);

    void o(ehg ehg, Integer num);

    void p(ehg ehg, hme hme, hme hme2, Integer num, dzq dzq);

    void q(ehg ehg, Integer num, dzq dzq, eaf eaf);

    void r(ehg ehg, Integer num);

    void s(ehg ehg, long j, Duration duration);

    void t(ehg ehg, long j);

    void u(elq elq, eak eak);

    void v(ehg ehg, int i, eec eec);
}
