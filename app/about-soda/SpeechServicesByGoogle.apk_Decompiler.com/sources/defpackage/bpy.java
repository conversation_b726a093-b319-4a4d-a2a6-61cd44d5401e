package defpackage;

import java.util.concurrent.Executor;

/* renamed from: bpy  reason: default package */
/* compiled from: PG */
public abstract class bpy {
    public static final String a = "bpy";
    public final bpm b;
    protected final bpq c;
    public final hmh d;
    public final Executor e;
    public final Object f = new Object();
    public hme g;
    private hme h;
    private final abq i;

    protected bpy(bpm bpm, bpq bpq, abq abq) {
        this.b = bpm;
        this.c = bpq;
        this.i = abq;
        bqa bqa = (bqa) bpm;
        this.d = bqa.d;
        this.e = bqa.f;
    }

    public final hme a() {
        hme hme;
        synchronized (this.f) {
            if (this.h == this.b.a() && (hme = this.g) != null) {
                return hme;
            }
            abq abq = this.i;
            bpm bpm = this.b;
            hme g2 = hke.g(hke.g(hly.q(hke.g(hly.q(hke.f(((bqa) bpm).b(), new brg(this.c, 1), ((bqa) bpm).d)), new bpt(this, abq, 1), this.d)), new bpr(this, 1), this.d), new bpr(this, 0), this.d);
            this.g = g2;
            this.h = this.b.a();
            hfc.T(g2, new cmk(this, 1), hld.a);
            return g2;
        }
    }

    /* access modifiers changed from: protected */
    public abstract Object b(boz boz);
}
