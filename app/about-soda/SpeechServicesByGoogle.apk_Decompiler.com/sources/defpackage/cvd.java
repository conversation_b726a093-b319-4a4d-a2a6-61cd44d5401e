package defpackage;

import android.content.Context;
import android.database.sqlite.SQLiteException;
import android.net.Uri;
import android.os.Build;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Iterator;

/* renamed from: cvd  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvd implements hko {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    public final /* synthetic */ Object d;
    private final /* synthetic */ int e;

    public /* synthetic */ cvd(cvy cvy, csx csx, csv csv, ctj ctj, int i) {
        this.e = i;
        this.d = cvy;
        this.a = csx;
        this.c = csv;
        this.b = ctj;
    }

    /* JADX WARNING: type inference failed for: r7v1, types: [java.lang.Object, cxa] */
    /* JADX WARNING: type inference failed for: r5v2, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v4, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v3, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v10, types: [java.util.concurrent.Future, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r8v6, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v14, types: [java.lang.Object, hko] */
    /* JADX WARNING: type inference failed for: r0v59, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v45, types: [java.lang.Object, hko] */
    /* JADX WARNING: type inference failed for: r0v171, types: [java.util.concurrent.Future, java.lang.Object] */
    public final hme a(Object obj) {
        hme hme;
        int i;
        Uri uri = null;
        int i2 = 1;
        switch (this.e) {
            case 0:
                ctl ctl = (ctl) obj;
                Object obj2 = this.a;
                if (ctl != null && ctl.d) {
                    this.b.add(cqx.t((Context) ((dmd) obj2).h, ctl.f));
                }
                Object obj3 = this.d;
                Object obj4 = this.c;
                dmd dmd = (dmd) obj2;
                Object obj5 = dmd.b;
                dbw dbw = (dbw) obj5;
                return ftd.K(ftd.L(dbw.j.e((ctj) obj4), new cwq(obj5, obj4, 6), dbw.l), new cve(obj2, obj3, obj4, 0), dmd.c);
            case 1:
                Void voidR = (Void) obj;
                crw crw = (crw) hfc.S(this.b);
                Object obj6 = this.c;
                if (((csk) obj6).g.f()) {
                    try {
                        ((csl) ((csk) obj6).g.b()).a(crw);
                    } catch (Exception e2) {
                        cyh.o(e2, "%s: Listener onComplete failed for group %s", "MobileDataDownload", crw.b);
                    }
                    cuf cuf = (cuf) this.a;
                    if (cuf.e.f()) {
                        ((czp) cuf.e.b()).i((String) this.d);
                    }
                }
                return hfc.K(crw);
            case 2:
                Object obj7 = this.c;
                csx csx = (csx) obj;
                if (obj7 == null && (obj7 = csx.l) == null) {
                    obj7 = csz.f;
                }
                Object obj8 = obj7;
                ArrayList arrayList = new ArrayList();
                Iterator it = csx.n.iterator();
                while (true) {
                    Object obj9 = this.a;
                    Object obj10 = this.b;
                    if (it.hasNext()) {
                        csv csv = (csv) it.next();
                        if (!cqx.p(csv)) {
                            int x = a.x(csx.i);
                            if (x == 0) {
                                x = i2;
                            }
                            ctj t = cqh.t(csv, x);
                            if (Build.VERSION.SDK_INT >= 30) {
                                cvy cvy = (cvy) obj10;
                                hme l = cvy.l(csx, csv, t);
                                cvj cvj = r10;
                                cvj cvj2 = new cvj(cvy, csv, csx, l, t, 0);
                                hme = cvy.q(czw.e(l).g(new bsx(cvy, (htq) csv, (htq) csx, 15), cvy.e).g(cvj, cvy.e).d(cza.class, new bsx(cvy, (htq) csv, (htq) csx, 16), cvy.e), new dbp(cvy, (ctg) obj9, csv, t, (csz) obj8, csx, 1));
                            } else {
                                ctj ctj = t;
                                try {
                                    dbw dbw2 = ((cvy) obj10).j;
                                    int i3 = csx.o;
                                    huf huf = csx.p;
                                    hse hse = csx.h;
                                    if (hse == null) {
                                        hse = hse.c;
                                    }
                                    hme = dbw2.o((ctg) obj9, csv, ctj, (csz) obj8, i3, huf, hse);
                                } catch (RuntimeException e3) {
                                    kml a2 = csi.a();
                                    a2.b = csh.UNKNOWN_ERROR;
                                    a2.d = e3;
                                    hme = hfc.J(a2.a());
                                }
                            }
                            arrayList.add(hme);
                            i2 = 1;
                        }
                    } else {
                        return cqh.U(arrayList).o(new cvs(obj10, obj9, this.d, (Object) arrayList, 1), ((cvy) obj10).e);
                    }
                }
            case 3:
                Void voidR2 = (Void) obj;
                Object obj11 = this.b;
                Object obj12 = this.c;
                Object obj13 = this.a;
                cvy cvy2 = (cvy) this.d;
                csx csx2 = (csx) obj13;
                csv csv2 = (csv) obj12;
                ctj ctj2 = (ctj) obj11;
                return czw.e(cvy2.l(csx2, csv2, ctj2)).g(new cvd(cvy2, csx2, csv2, ctj2, 10), hld.a);
            case 4:
                cxh cxh = (cxh) obj;
                csx csx3 = cxh.a;
                if (csx3 == null) {
                    csx3 = cxh.b;
                }
                csx csx4 = csx3;
                ? r8 = this.d;
                Object obj14 = this.c;
                if (csx4 != null) {
                    ? r2 = this.b;
                    cvy cvy3 = (cvy) this.a;
                    ctg ctg = (ctg) obj14;
                    return cvy3.q(cvy3.B(ctg, csx4, r2, new cxi((Object) cvy3.i, (char[]) null)), new cvd(cvy3, (Object) r8, csx4, ctg, 5));
                }
                cvy.v(r8, (ctg) obj14);
                return hfc.J(new AssertionError("impossible error"));
            case 5:
                cvx cvx = (cvx) obj;
                Object obj15 = this.a;
                if (cvx != cvx.DOWNLOADED) {
                    cvy.v(this.d, (ctg) obj15);
                }
                Object obj16 = this.b;
                htk l2 = hig.k.l();
                ctg ctg2 = (ctg) obj15;
                String str = ctg2.b;
                if (!l2.b.B()) {
                    l2.u();
                }
                htq htq = l2.b;
                hig hig = (hig) htq;
                str.getClass();
                hig.a = 1 | hig.a;
                hig.b = str;
                String str2 = ctg2.c;
                if (!htq.B()) {
                    l2.u();
                }
                Object obj17 = this.c;
                htq htq2 = l2.b;
                hig hig2 = (hig) htq2;
                str2.getClass();
                hig2.a |= 4;
                hig2.d = str2;
                csx csx5 = (csx) obj17;
                int i4 = csx5.e;
                if (!htq2.B()) {
                    l2.u();
                }
                htq htq3 = l2.b;
                hig hig3 = (hig) htq3;
                hig3.a |= 2;
                hig3.c = i4;
                long j = csx5.r;
                if (!htq3.B()) {
                    l2.u();
                }
                htq htq4 = l2.b;
                hig hig4 = (hig) htq4;
                hig4.a |= 64;
                hig4.h = j;
                String str3 = csx5.s;
                if (!htq4.B()) {
                    l2.u();
                }
                cyk cyk = ((cvy) obj16).i;
                hig hig5 = (hig) l2.b;
                str3.getClass();
                hig5.a |= 128;
                hig5.i = str3;
                cyk.j(3, (hig) l2.r(), 2);
                return hfc.K(obj17);
            case 6:
                if (((Boolean) obj).booleanValue()) {
                    return hma.a;
                }
                Object obj18 = this.a;
                Object obj19 = this.c;
                Object obj20 = this.d;
                Object obj21 = this.b;
                ((cxi) obj20).d((csx) obj19);
                hfc.K(true);
                cvy cvy4 = (cvy) obj21;
                return cvy4.q(cvy4.c.i((ctg) obj18), new cvn(obj21, (htq) obj18, 3));
            case 7:
                Void voidR3 = (Void) obj;
                csx csx6 = (csx) this.a;
                return ((cvy) this.d).n((ctg) this.b, (csi) this.c, csx6.r, csx6.s);
            case 8:
                Void voidR4 = (Void) obj;
                csx csx7 = (csx) this.a;
                return ((cvy) this.d).n((ctg) this.b, (csi) this.c, csx7.r, csx7.s);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                cyh.h("%s: Shared file not found, newFileKey = %s", "FileGroupManager", this.b);
                cvy cvy5 = (cvy) this.d;
                cvy5.b.a();
                cvy.A(cvy5.i, (csx) this.a, (csv) this.c, 26);
                return hfc.J((cwz) obj);
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                ctl ctl2 = (ctl) obj;
                ctf b2 = ctf.b(ctl2.c);
                if (b2 == null) {
                    b2 = ctf.NONE;
                }
                if (b2 != ctf.DOWNLOAD_COMPLETE) {
                    return hma.a;
                }
                Object obj22 = this.b;
                Object obj23 = this.c;
                Object obj24 = this.a;
                cvy cvy6 = (cvy) this.d;
                csx csx8 = (csx) obj24;
                csv csv3 = (csv) obj23;
                ctj ctj3 = (ctj) obj22;
                return czw.e(cvy6.f(ctl2, csv3, csx8)).g(new cvj(cvy6, csv3, csx8, ctl2, ctj3, 2), cvy6.e).d(cza.class, new cvd(cvy6, csx8, csv3, ctj3, 13), cvy6.e);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                csx csx9 = (csx) obj;
                Object obj25 = this.d;
                hme hme2 = hma.a;
                if (csx9 != null) {
                    cvy cvy7 = (cvy) obj25;
                    hme2 = cvy7.q(cvy7.c.i((ctg) this.a), new cvp(obj25, (ctg) this.b, csx9, 11));
                }
                return ((cvy) obj25).q(hme2, new cvn(obj25, (htq) this.c, 8));
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                Void voidR5 = (Void) obj;
                Object obj26 = this.d;
                cvy cvy8 = (cvy) obj26;
                return cvy8.q(cvy8.c.i((ctg) this.a), new cvp(obj26, (ctg) this.b, (csx) this.c, 8));
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                int i5 = ((cza) obj).a;
                cvy cvy9 = (cvy) this.d;
                cyk cyk2 = cvy9.i;
                Object obj27 = this.c;
                csx csx10 = (csx) this.a;
                csv csv4 = (csv) obj27;
                cvy.A(cyk2, csx10, csv4, i5);
                cyh.e("%s: File couldn't be shared after download %s, filegroup %s", "FileGroupManager", csv4.b, csx10.c);
                return cvy9.r(csx10, csv4, (ctj) this.b, csx10.k);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                Void voidR6 = (Void) obj;
                return ((cwm) this.a).c.e((ctg) this.c, (csz) ((grh) this.b).e(), this.d);
            case 15:
                ctl ctl3 = (ctl) obj;
                Object obj28 = this.a;
                Object obj29 = this.c;
                Object obj30 = this.d;
                if (ctl3 != null) {
                    ctf b3 = ctf.b(ctl3.c);
                    if (b3 == null) {
                        b3 = ctf.NONE;
                    }
                    if (b3 == ctf.DOWNLOAD_COMPLETE) {
                        cxq cxq = (cxq) obj30;
                        uri = cqx.u(cxq.a, cxq.n, ctl3.b, ((ctj) this.b).d, cxq.c, cxq.k, false);
                    }
                }
                if (uri == null) {
                    kml a3 = csi.a();
                    a3.b = csh.DELTA_DOWNLOAD_BASE_FILE_NOT_FOUND_ERROR;
                    return hfc.J(a3.a());
                }
                try {
                    if (((cxq) obj30).o.j((Uri) obj29)) {
                        ((cxq) obj30).o.h((Uri) obj29);
                    }
                    ((cxq) obj30).e.a();
                    ((cxq) obj30).o.h((Uri) obj28);
                    htk l3 = hig.k.l();
                    cxq cxq2 = (cxq) obj30;
                    String str4 = cxq2.g.b;
                    if (!l3.b.B()) {
                        l3.u();
                    }
                    htq htq5 = l3.b;
                    hig hig6 = (hig) htq5;
                    str4.getClass();
                    hig6.a |= 1;
                    hig6.b = str4;
                    int i6 = cxq2.h;
                    if (!htq5.B()) {
                        l3.u();
                    }
                    htq htq6 = l3.b;
                    hig hig7 = (hig) htq6;
                    hig7.a |= 2;
                    hig7.c = i6;
                    String str5 = cxq2.g.c;
                    if (!htq6.B()) {
                        l3.u();
                    }
                    htq htq7 = l3.b;
                    hig hig8 = (hig) htq7;
                    str5.getClass();
                    hig8.a |= 4;
                    hig8.d = str5;
                    long j2 = cxq2.i;
                    if (!htq7.B()) {
                        l3.u();
                    }
                    htq htq8 = l3.b;
                    hig hig9 = (hig) htq8;
                    hig9.a |= 64;
                    hig9.h = j2;
                    String str6 = cxq2.j;
                    if (!htq8.B()) {
                        l3.u();
                    }
                    hig hig10 = (hig) l3.b;
                    str6.getClass();
                    hig10.a |= 128;
                    hig10.i = str6;
                    hig hig11 = (hig) l3.r();
                    cyk cyk3 = cxq2.m;
                    csv csv5 = cxq2.d;
                    long j3 = (long) csv5.d;
                    long j4 = (long) cxq2.f.c;
                    String str7 = csv5.b;
                    int i7 = 0;
                    while (true) {
                        if (i7 < cxq2.d.k.size()) {
                            int i8 = i7 + 1;
                            if (ftd.v(((csy) cxq2.d.k.get(i7)).d, cxq2.f.d)) {
                                i = i8;
                            } else {
                                i7 = i8;
                            }
                        } else {
                            i = 0;
                        }
                    }
                    cyk3.g(hig11, 4, j3, j4, str7, i);
                    return hma.a;
                } catch (IOException e4) {
                    cxq cxq3 = (cxq) obj30;
                    cyh.j(e4, "%s: Failed to decode delta file with url = %s failed. checksum = %s ", "DeltaFileDownloaderCallbackImpl", cxq3.f.b, cxq3.d.f);
                    cxq3.c.a();
                    kml a4 = csi.a();
                    a4.b = csh.DELTA_DOWNLOAD_DECODE_IO_ERROR;
                    a4.d = e4;
                    return hfc.J(a4.a());
                }
            case 16:
                int i9 = ((ctl) obj).g;
                int e5 = (int) ijz.a.a().e();
                Object obj31 = this.c;
                Object obj32 = this.b;
                Object obj33 = this.d;
                if (i9 >= e5) {
                    Object obj34 = this.a;
                    cyh.d("%s: Checksum mismatch detected but the has already reached retry limit! Skipping removal for file %s", "DownloaderCallbackImpl", obj33);
                    ((cyk) obj34).d(1115);
                } else {
                    cyh.c("%s: Removing file and marking as corrupted due to checksum mismatch", "DownloaderCallbackImpl");
                    try {
                        ((kjd) obj32).h((Uri) obj31);
                    } catch (IOException e6) {
                        cyh.j(e6, "%s: Failed to remove corrupted file %s", "DownloaderCallbackImpl", obj33);
                        return hfc.J(e6);
                    }
                }
                return hma.a;
            case 17:
                Void voidR7 = (Void) obj;
                Object obj35 = this.a;
                Object obj36 = this.c;
                ded ded = (ded) this.b;
                ((kli) this.d).c(ded, (ddc) obj36, (ddc) obj35);
                return kli.e(ded);
            case 18:
                kli kli = (kli) this.d;
                deq deq = ((dfl) kli.c).d;
                Object obj37 = this.c;
                Throwable th = (Throwable) obj;
                try {
                    ((dfb) deq).b.getWritableDatabase().execSQL("UPDATE file_metadata SET validation_count=validation_count+1 WHERE namespace=? AND name=?", new String[]{((dcd) obj37).a, ((dcd) obj37).b});
                    Object obj38 = this.b;
                    ((cxk) kli.f).a(new dgp(kli, (ddc) this.a, (ded) obj38, (ddc) obj37, th, 0));
                    return hfc.J(th);
                } catch (SQLiteException e7) {
                    dem dem = ((dfb) deq).b;
                    IOException iOException = new IOException("SqliteFileMetadataTable#incrementValidationFailureCount, SQL update failed, name: ".concat(obj37.toString()), e7);
                    dem.a(iOException);
                    throw iOException;
                }
            case 19:
                Object obj39 = this.a;
                String str8 = ((dcd) obj39).b;
                Throwable th2 = (Throwable) obj;
                dcq.a();
                Object obj40 = this.d;
                kli kli2 = (kli) obj40;
                kli kli3 = kli2;
                ((cxk) kli2.f).a(new dgp(kli3, (ddc) obj39, (ded) this.b, (ddc) this.c, th2, 1));
                return hfc.J(th2);
            default:
                Void voidR8 = (Void) obj;
                dvo dvo = (dvo) hfc.S(this.b);
                if (dvo.b.isEmpty()) {
                    return hma.a;
                }
                Object obj41 = this.d;
                Object obj42 = this.a;
                dte dte = (dte) this.c;
                duw duw = (duw) obj42;
                return hke.g(hly.q(hke.f(hly.q(duy.b(dte).a()), new cyg(duw.a, 16), dte.d())), new ezn((String) obj41, duw, dte, dvo, 1), dte.d());
        }
    }

    public /* synthetic */ cvd(cvy cvy, csz csz, ctg ctg, hko hko, int i) {
        this.e = i;
        this.b = cvy;
        this.c = csz;
        this.a = ctg;
        this.d = hko;
    }

    public /* synthetic */ cvd(cvy cvy, ctj ctj, csx csx, csv csv, int i) {
        this.e = i;
        this.d = cvy;
        this.b = ctj;
        this.a = csx;
        this.c = csv;
    }

    public /* synthetic */ cvd(cvy cvy, Object obj, csx csx, ctg ctg, int i) {
        this.e = i;
        this.b = cvy;
        this.d = obj;
        this.c = csx;
        this.a = ctg;
    }

    public /* synthetic */ cvd(dte dte, hme hme, duw duw, String str, int i) {
        this.e = i;
        this.c = dte;
        this.b = hme;
        this.a = duw;
        this.d = str;
    }

    public /* synthetic */ cvd(Object obj, ctg ctg, Object obj2, Object obj3, int i) {
        this.e = i;
        this.a = obj;
        this.c = ctg;
        this.b = obj2;
        this.d = obj3;
    }

    public /* synthetic */ cvd(Object obj, Object obj2, Object obj3, Object obj4, int i) {
        this.e = i;
        this.a = obj;
        this.b = obj2;
        this.c = obj3;
        this.d = obj4;
    }

    public /* synthetic */ cvd(Object obj, Object obj2, Object obj3, Object obj4, int i, byte[] bArr) {
        this.e = i;
        this.d = obj;
        this.b = obj2;
        this.c = obj3;
        this.a = obj4;
    }

    public /* synthetic */ cvd(Object obj, Object obj2, Object obj3, Object obj4, int i, char[] cArr) {
        this.e = i;
        this.d = obj;
        this.a = obj2;
        this.b = obj3;
        this.c = obj4;
    }

    public /* synthetic */ cvd(kli kli, ddc ddc, ddc ddc2, ded ded, int i) {
        this.e = i;
        this.d = kli;
        this.c = ddc;
        this.a = ddc2;
        this.b = ded;
    }
}
