package defpackage;

import com.google.android.apps.speech.tts.googletts.audio.AndroidDecoder;
import java.nio.ByteBuffer;

/* renamed from: brh  reason: default package */
/* compiled from: PG */
public final class brh implements brj {
    final /* synthetic */ AndroidDecoder a;

    public brh(AndroidDecoder androidDecoder) {
        this.a = androidDecoder;
    }

    public final void a() {
        AndroidDecoder androidDecoder = this.a;
        androidDecoder.jniDecoderCompleted(androidDecoder.c);
    }

    public final void b(iff iff) {
        AndroidDecoder androidDecoder = this.a;
        androidDecoder.jniDecoderStopped(androidDecoder.c, iff.g);
        this.a.directAudioBuffer.clear();
    }

    public final boolean c(ByteBuffer byteBuffer) {
        int limit = byteBuffer.limit();
        int position = byteBuffer.position();
        while (position < limit) {
            byteBuffer.position(position).limit(Math.min(limit, this.a.b + position));
            this.a.directAudioBuffer.put(byteBuffer).flip();
            AndroidDecoder androidDecoder = this.a;
            int jniDecoderAudioAvailable = androidDecoder.jniDecoderAudioAvailable(androidDecoder.c, androidDecoder.directAudioBuffer.limit());
            if (jniDecoderAudioAvailable < 0) {
                ((hby) ((hby) AndroidDecoder.a.g()).j("com/google/android/apps/speech/tts/googletts/audio/AndroidDecoder$AndroidDecoderCallback", "audioAvailable", 178, "AndroidDecoder.java")).r("Native code failed to process audio buffer");
                return false;
            }
            this.a.directAudioBuffer.position(jniDecoderAudioAvailable);
            if (this.a.directAudioBuffer.hasRemaining()) {
                ((hby) ((hby) AndroidDecoder.a.g()).j("com/google/android/apps/speech/tts/googletts/audio/AndroidDecoder$AndroidDecoderCallback", "audioAvailable", 185, "AndroidDecoder.java")).r("Audio buffer has audio remaining from last call");
                return false;
            }
            this.a.directAudioBuffer.clear();
            position += this.a.b;
        }
        return true;
    }

    public final void d() {
    }
}
