package defpackage;

/* renamed from: bqh  reason: default package */
/* compiled from: PG */
public final class bqh extends htq implements hvb {
    public static final bqh c;
    private static volatile hvh e;
    public int a;
    public int b;
    private int d;

    static {
        bqh bqh = new bqh();
        c = bqh;
        htq.z(bqh.class, bqh);
    }

    private bqh() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(c, "\u0001\u0002\u0000\u0001\u0001\u0002\u0002\u0000\u0000\u0000\u0001ဋ\u0000\u0002ဋ\u0001", new Object[]{"d", "a", "b"});
        } else if (i2 == 3) {
            return new bqh();
        } else {
            if (i2 == 4) {
                return new htk((htq) c);
            }
            if (i2 == 5) {
                return c;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = e;
            if (hvh == null) {
                synchronized (bqh.class) {
                    hvh = e;
                    if (hvh == null) {
                        hvh = new htl(c);
                        e = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
