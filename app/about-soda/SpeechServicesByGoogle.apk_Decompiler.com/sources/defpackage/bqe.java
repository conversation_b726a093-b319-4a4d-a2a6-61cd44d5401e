package defpackage;

/* renamed from: bqe  reason: default package */
/* compiled from: PG */
public final class bqe {
    public final gxq a;

    public bqe() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof bqe) {
            return fvf.D(this.a, ((bqe) obj).a);
        }
        return false;
    }

    public final int hashCode() {
        return this.a.hashCode() ^ 1000003;
    }

    public final String toString() {
        String obj = this.a.toString();
        return "RosieRobotResult{results=" + obj + "}";
    }

    public bqe(gxq gxq) {
        if (gxq != null) {
            this.a = gxq;
            return;
        }
        throw new NullPointerException("Null results");
    }
}
