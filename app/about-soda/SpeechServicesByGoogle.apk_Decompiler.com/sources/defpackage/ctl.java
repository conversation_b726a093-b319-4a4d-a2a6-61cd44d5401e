package defpackage;

/* renamed from: ctl  reason: default package */
/* compiled from: PG */
public final class ctl extends htq implements hvb {
    public static final ctl h;
    private static volatile hvh i;
    public int a;
    public String b = "";
    public int c;
    public boolean d;
    public long e;
    public String f = "";
    public int g;

    static {
        ctl ctl = new ctl();
        h = ctl;
        htq.z(ctl.class, ctl);
    }

    private ctl() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i2, Object obj) {
        int i3 = i2 - 1;
        if (i3 == 0) {
            return (byte) 1;
        }
        if (i3 == 2) {
            return new hvl(h, "\u0001\u0006\u0000\u0001\u0004\u000b\u0006\u0000\u0000\u0000\u0004ဈ\u0000\u0005᠌\u0001\bဇ\u0002\tဂ\u0003\nဈ\u0004\u000bင\u0005", new Object[]{"a", "b", "c", bqk.u, "d", "e", "f", "g"});
        } else if (i3 == 3) {
            return new ctl();
        } else {
            if (i3 == 4) {
                return new htk((htq) h);
            }
            if (i3 == 5) {
                return h;
            }
            if (i3 != 6) {
                return null;
            }
            hvh hvh = i;
            if (hvh == null) {
                synchronized (ctl.class) {
                    hvh = i;
                    if (hvh == null) {
                        hvh = new htl(h);
                        i = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
