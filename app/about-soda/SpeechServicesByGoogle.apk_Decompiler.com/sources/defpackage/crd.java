package defpackage;

/* renamed from: crd  reason: default package */
/* compiled from: PG */
public final class crd {
    public static boolean a = false;
    public static final Object b = new Object();
    private static final String[] c = {"COLLECTION_BASIS_VERIFIER"};

    /* JADX WARNING: type inference failed for: r14v11, types: [java.util.concurrent.ExecutorService] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public static void a(defpackage.cqu r14, defpackage.iih r15) {
        /*
            cjt r0 = new cjt
            android.content.Context r1 = r14.a
            r0.<init>(r1)
            android.content.Context r1 = r14.a
            java.lang.String r1 = r1.getPackageName()
            java.lang.String r1 = java.lang.String.valueOf(r1)
            android.content.Context r2 = r14.a
            java.lang.Object r3 = r15.a
            r4 = 0
            if (r3 != 0) goto L_0x0034
            android.content.pm.PackageManager r3 = r2.getPackageManager()     // Catch:{ NameNotFoundException -> 0x002d }
            java.lang.String r2 = r2.getPackageName()     // Catch:{ NameNotFoundException -> 0x002d }
            android.content.pm.PackageInfo r2 = r3.getPackageInfo(r2, r4)     // Catch:{ NameNotFoundException -> 0x002d }
            int r2 = r2.versionCode     // Catch:{ NameNotFoundException -> 0x002d }
            java.lang.Integer r2 = java.lang.Integer.valueOf(r2)     // Catch:{ NameNotFoundException -> 0x002d }
            r15.a = r2     // Catch:{ NameNotFoundException -> 0x002d }
            goto L_0x0034
        L_0x002d:
            r2 = -1
            java.lang.Integer r2 = java.lang.Integer.valueOf(r2)
            r15.a = r2
        L_0x0034:
            java.lang.String r2 = "com.google.android.libraries.consentverifier#"
            java.lang.String r1 = r2.concat(r1)
            java.lang.Object r15 = r15.a
            java.lang.Integer r15 = (java.lang.Integer) r15
            int r15 = r15.intValue()
            java.lang.String[] r2 = c
            r3 = 0
            cks r15 = r0.b(r1, r15, r2, r3)
            android.content.Context r14 = r14.a
            boolean r14 = defpackage.cqh.b(r14)
            r2 = 1
            if (r14 == 0) goto L_0x0072
            cgr r14 = defpackage.cic.a
            java.util.concurrent.ThreadFactory r12 = java.util.concurrent.Executors.defaultThreadFactory()
            java.util.concurrent.ThreadPoolExecutor r14 = new java.util.concurrent.ThreadPoolExecutor
            java.util.concurrent.TimeUnit r10 = java.util.concurrent.TimeUnit.SECONDS
            java.util.concurrent.LinkedBlockingQueue r11 = new java.util.concurrent.LinkedBlockingQueue
            r11.<init>()
            r7 = 10
            r8 = 60
            r5 = r14
            r6 = r7
            r5.<init>(r6, r7, r8, r10, r11, r12)
            r14.allowCoreThreadTimeOut(r2)
            java.util.concurrent.ExecutorService r14 = java.util.concurrent.Executors.unconfigurableExecutorService(r14)
            goto L_0x0096
        L_0x0072:
            java.util.concurrent.LinkedBlockingQueue r11 = new java.util.concurrent.LinkedBlockingQueue
            r14 = 10
            r11.<init>(r14)
            java.util.concurrent.ThreadPoolExecutor r14 = new java.util.concurrent.ThreadPoolExecutor
            iqe r5 = new iqe
            r5.<init>(r3)
            java.lang.String r3 = "ConsentVerifierLibraryThread-%d"
            r5.h(r3)
            java.util.concurrent.ThreadFactory r12 = defpackage.iqe.i(r5)
            java.util.concurrent.RejectedExecutionHandler r13 = defpackage.crg.a
            r8 = 10
            java.util.concurrent.TimeUnit r10 = java.util.concurrent.TimeUnit.SECONDS
            r6 = 0
            r7 = 10
            r5 = r14
            r5.<init>(r6, r7, r8, r10, r11, r12, r13)
        L_0x0096:
            crb r3 = new crb     // Catch:{ RejectedExecutionException -> 0x00a7 }
            r3.<init>(r0, r1, r14)     // Catch:{ RejectedExecutionException -> 0x00a7 }
            r15.f(r14, r3)     // Catch:{ RejectedExecutionException -> 0x00a7 }
            crc r0 = new crc     // Catch:{ RejectedExecutionException -> 0x00a7 }
            r0.<init>(r1, r4)     // Catch:{ RejectedExecutionException -> 0x00a7 }
            r15.e(r14, r0)     // Catch:{ RejectedExecutionException -> 0x00a7 }
            return
        L_0x00a7:
            r14 = move-exception
            r15 = 2
            java.lang.Object[] r15 = new java.lang.Object[r15]
            r15[r4] = r1
            r15[r2] = r14
            java.lang.String r14 = "Execution failure when updating phenotypeflags for %s. %s"
            java.lang.String r14 = java.lang.String.format(r14, r15)
            java.lang.String r15 = "CBVerifier"
            android.util.Log.w(r15, r14)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.crd.a(cqu, iih):void");
    }
}
