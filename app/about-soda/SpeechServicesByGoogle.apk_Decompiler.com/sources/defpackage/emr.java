package defpackage;

/* renamed from: emr  reason: default package */
/* compiled from: PG */
public final class emr {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/microphone/AudioSessionToMicStateUpdater");
    public final eqr b;
    private final jqs c;

    public emr(eqr eqr, jqs jqs) {
        jnu.e(jqs, "lightweightScope");
        this.b = eqr;
        this.c = jqs;
    }

    /* JADX WARNING: Removed duplicated region for block: B:14:0x0031  */
    /* JADX WARNING: Removed duplicated region for block: B:23:0x004e  */
    /* JADX WARNING: Removed duplicated region for block: B:24:0x0051  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object a(defpackage.eej r5, defpackage.jlr r6) {
        /*
            r4 = this;
            boolean r0 = r6 instanceof defpackage.eml
            if (r0 == 0) goto L_0x0013
            r0 = r6
            eml r0 = (defpackage.eml) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            eml r0 = new eml
            r0.<init>(r4, r6)
        L_0x0018:
            java.lang.Object r6 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            r3 = 1
            if (r2 == 0) goto L_0x0031
            if (r2 != r3) goto L_0x0029
            defpackage.jji.c(r6)     // Catch:{ all -> 0x0027 }
            goto L_0x0041
        L_0x0027:
            r5 = move-exception
            goto L_0x0044
        L_0x0029:
            java.lang.IllegalStateException r5 = new java.lang.IllegalStateException
            java.lang.String r6 = "call to 'resume' before 'invoke' with coroutine"
            r5.<init>(r6)
            throw r5
        L_0x0031:
            defpackage.jji.c(r6)
            hme r5 = r5.a()     // Catch:{ all -> 0x0027 }
            r0.c = r3     // Catch:{ all -> 0x0027 }
            java.lang.Object r6 = defpackage.jqw.x(r5, r0)     // Catch:{ all -> 0x0027 }
            if (r6 != r1) goto L_0x0041
            return r1
        L_0x0041:
            grh r6 = (defpackage.grh) r6     // Catch:{ all -> 0x0027 }
            goto L_0x0048
        L_0x0044:
            java.lang.Object r6 = defpackage.jji.b(r5)
        L_0x0048:
            java.lang.Throwable r5 = defpackage.jju.a(r6)
            if (r5 != 0) goto L_0x0051
            grh r6 = (defpackage.grh) r6
            goto L_0x0074
        L_0x0051:
            hca r5 = a
            hco r5 = r5.h()
            hcr r6 = defpackage.hdg.a
            java.lang.String r0 = "ALT.SessionMicUpdater"
            hco r5 = r5.h(r6, r0)
            java.lang.String r6 = "audioRecordIdAwait"
            r0 = 215(0xd7, float:3.01E-43)
            java.lang.String r1 = "com/google/android/libraries/search/audio/microphone/AudioSessionToMicStateUpdater"
            java.lang.String r2 = "AudioSessionToMicStateUpdater.kt"
            hco r5 = r5.j(r1, r6, r0, r2)
            hby r5 = (defpackage.hby) r5
            java.lang.String r6 = "#audio# unable to retrieve audio record ID"
            r5.r(r6)
            gqd r6 = defpackage.gqd.a
        L_0x0074:
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.emr.a(eej, jlr):java.lang.Object");
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x0037  */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x006b  */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x0079  */
    /* JADX WARNING: Removed duplicated region for block: B:35:0x00cc  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object b(defpackage.dyy r6, defpackage.grh r7, java.lang.String r8, defpackage.jlr r9) {
        /*
            r5 = this;
            boolean r0 = r9 instanceof defpackage.emn
            if (r0 == 0) goto L_0x0013
            r0 = r9
            emn r0 = (defpackage.emn) r0
            int r1 = r0.d
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.d = r1
            goto L_0x0018
        L_0x0013:
            emn r0 = new emn
            r0.<init>(r5, r9)
        L_0x0018:
            java.lang.Object r9 = r0.b
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.d
            r3 = 1
            if (r2 == 0) goto L_0x0037
            if (r2 != r3) goto L_0x002f
            java.lang.String r8 = r0.f
            java.lang.Object r7 = r0.a
            emr r6 = r0.e
            defpackage.jji.c(r9)     // Catch:{ all -> 0x002d }
            goto L_0x0059
        L_0x002d:
            r9 = move-exception
            goto L_0x005f
        L_0x002f:
            java.lang.IllegalStateException r6 = new java.lang.IllegalStateException
            java.lang.String r7 = "call to 'resume' before 'invoke' with coroutine"
            r6.<init>(r7)
            throw r6
        L_0x0037:
            defpackage.jji.c(r9)
            java.lang.Object r6 = r6.d()     // Catch:{ all -> 0x005c }
            dyx r6 = (defpackage.dyx) r6     // Catch:{ all -> 0x005c }
            hme r6 = r6.a()     // Catch:{ all -> 0x005c }
            java.lang.String r9 = "getStopListeningStatus(...)"
            defpackage.jnu.d(r6, r9)     // Catch:{ all -> 0x005c }
            r0.e = r5     // Catch:{ all -> 0x005c }
            r0.a = r7     // Catch:{ all -> 0x005c }
            r0.f = r8     // Catch:{ all -> 0x005c }
            r0.d = r3     // Catch:{ all -> 0x005c }
            java.lang.Object r9 = defpackage.jqw.x(r6, r0)     // Catch:{ all -> 0x005c }
            if (r9 != r1) goto L_0x0058
            return r1
        L_0x0058:
            r6 = r5
        L_0x0059:
            dzi r9 = (defpackage.dzi) r9     // Catch:{ all -> 0x002d }
            goto L_0x0063
        L_0x005c:
            r6 = move-exception
            r9 = r6
            r6 = r5
        L_0x005f:
            java.lang.Object r9 = defpackage.jji.b(r9)
        L_0x0063:
            java.lang.Throwable r0 = defpackage.jju.a(r9)
            java.lang.String r1 = "newBuilder(...)"
            if (r0 != 0) goto L_0x0079
            dzi r9 = (defpackage.dzi) r9
            eah r9 = r9.b
            if (r9 != 0) goto L_0x0073
            eah r9 = defpackage.eah.c
        L_0x0073:
            java.lang.String r0 = "getAudioSourceClosingStatus(...)"
            defpackage.jnu.d(r9, r0)
            goto L_0x00b0
        L_0x0079:
            hca r9 = a
            hco r9 = r9.h()
            hcr r0 = defpackage.hdg.a
            java.lang.String r2 = "ALT.SessionMicUpdater"
            hco r9 = r9.h(r0, r2)
            java.lang.String r0 = "reportAudioSourceClose"
            r2 = 140(0x8c, float:1.96E-43)
            java.lang.String r3 = "com/google/android/libraries/search/audio/microphone/AudioSessionToMicStateUpdater"
            java.lang.String r4 = "AudioSessionToMicStateUpdater.kt"
            hco r9 = r9.j(r3, r0, r2, r4)
            hby r9 = (defpackage.hby) r9
            java.lang.String r0 = "#audio# stop status failed, continue update(%s)"
            r9.u(r0, r8)
            eah r9 = defpackage.eah.c
            htk r9 = r9.l()
            defpackage.jnu.d(r9, r1)
            dlv r9 = defpackage.jnu.e(r9, "builder")
            eag r0 = defpackage.eag.UNKNOWN_CLOSING_FAILURE
            r9.i(r0)
            eah r9 = r9.h()
        L_0x00b0:
            eqr r6 = r6.b
            eqq r0 = defpackage.eqq.g
            htk r0 = r0.l()
            defpackage.jnu.d(r0, r1)
            bzl r0 = defpackage.jnu.e(r0, "builder")
            ept r1 = defpackage.ept.CLIENT_TYPE_AUDIO_REQUEST
            r0.Q(r1)
            grh r7 = (defpackage.grh) r7
            boolean r1 = r7.f()
            if (r1 == 0) goto L_0x00d9
            java.lang.Object r7 = r7.b()
            java.lang.Number r7 = (java.lang.Number) r7
            int r7 = r7.intValue()
            r0.P(r7)
        L_0x00d9:
            r0.T(r8)
            r0.R(r9)
            eqq r7 = r0.O()
            r6.e(r7)
            jkd r6 = defpackage.jkd.a
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.emr.b(dyy, grh, java.lang.String, jlr):java.lang.Object");
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x0037  */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0064  */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x0072  */
    /* JADX WARNING: Removed duplicated region for block: B:35:0x00c5  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object c(defpackage.ebl r6, defpackage.grh r7, java.lang.String r8, defpackage.jlr r9) {
        /*
            r5 = this;
            boolean r0 = r9 instanceof defpackage.emo
            if (r0 == 0) goto L_0x0013
            r0 = r9
            emo r0 = (defpackage.emo) r0
            int r1 = r0.d
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.d = r1
            goto L_0x0018
        L_0x0013:
            emo r0 = new emo
            r0.<init>(r5, r9)
        L_0x0018:
            java.lang.Object r9 = r0.b
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.d
            r3 = 1
            if (r2 == 0) goto L_0x0037
            if (r2 != r3) goto L_0x002f
            java.lang.String r8 = r0.f
            java.lang.Object r7 = r0.a
            emr r6 = r0.e
            defpackage.jji.c(r9)     // Catch:{ all -> 0x002d }
            goto L_0x0052
        L_0x002d:
            r9 = move-exception
            goto L_0x0058
        L_0x002f:
            java.lang.IllegalStateException r6 = new java.lang.IllegalStateException
            java.lang.String r7 = "call to 'resume' before 'invoke' with coroutine"
            r6.<init>(r7)
            throw r6
        L_0x0037:
            defpackage.jji.c(r9)
            ebk r6 = r6.a()     // Catch:{ all -> 0x0055 }
            hme r6 = r6.a()     // Catch:{ all -> 0x0055 }
            r0.e = r5     // Catch:{ all -> 0x0055 }
            r0.a = r7     // Catch:{ all -> 0x0055 }
            r0.f = r8     // Catch:{ all -> 0x0055 }
            r0.d = r3     // Catch:{ all -> 0x0055 }
            java.lang.Object r9 = defpackage.jqw.x(r6, r0)     // Catch:{ all -> 0x0055 }
            if (r9 != r1) goto L_0x0051
            return r1
        L_0x0051:
            r6 = r5
        L_0x0052:
            ebp r9 = (defpackage.ebp) r9     // Catch:{ all -> 0x002d }
            goto L_0x005c
        L_0x0055:
            r6 = move-exception
            r9 = r6
            r6 = r5
        L_0x0058:
            java.lang.Object r9 = defpackage.jji.b(r9)
        L_0x005c:
            java.lang.Throwable r0 = defpackage.jju.a(r9)
            java.lang.String r1 = "newBuilder(...)"
            if (r0 != 0) goto L_0x0072
            ebp r9 = (defpackage.ebp) r9
            eah r9 = r9.b
            if (r9 != 0) goto L_0x006c
            eah r9 = defpackage.eah.c
        L_0x006c:
            java.lang.String r0 = "getAudioSourceClosingStatus(...)"
            defpackage.jnu.d(r9, r0)
            goto L_0x00a9
        L_0x0072:
            hca r9 = a
            hco r9 = r9.h()
            hcr r0 = defpackage.hdg.a
            java.lang.String r2 = "ALT.SessionMicUpdater"
            hco r9 = r9.h(r0, r2)
            java.lang.String r0 = "reportAudioSourceClose"
            r2 = 196(0xc4, float:2.75E-43)
            java.lang.String r3 = "com/google/android/libraries/search/audio/microphone/AudioSessionToMicStateUpdater"
            java.lang.String r4 = "AudioSessionToMicStateUpdater.kt"
            hco r9 = r9.j(r3, r0, r2, r4)
            hby r9 = (defpackage.hby) r9
            java.lang.String r0 = "#audio# hotword stop status failed, continue update(%s)"
            r9.u(r0, r8)
            eah r9 = defpackage.eah.c
            htk r9 = r9.l()
            defpackage.jnu.d(r9, r1)
            dlv r9 = defpackage.jnu.e(r9, "builder")
            eag r0 = defpackage.eag.UNKNOWN_CLOSING_FAILURE
            r9.i(r0)
            eah r9 = r9.h()
        L_0x00a9:
            eqr r6 = r6.b
            eqq r0 = defpackage.eqq.g
            htk r0 = r0.l()
            defpackage.jnu.d(r0, r1)
            bzl r0 = defpackage.jnu.e(r0, "builder")
            ept r1 = defpackage.ept.CLIENT_TYPE_HOTWORD
            r0.Q(r1)
            grh r7 = (defpackage.grh) r7
            boolean r1 = r7.f()
            if (r1 == 0) goto L_0x00d2
            java.lang.Object r7 = r7.b()
            java.lang.Number r7 = (java.lang.Number) r7
            int r7 = r7.intValue()
            r0.P(r7)
        L_0x00d2:
            r0.T(r8)
            r0.R(r9)
            eqq r7 = r0.O()
            r6.e(r7)
            jkd r6 = defpackage.jkd.a
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.emr.c(ebl, grh, java.lang.String, jlr):java.lang.Object");
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x0037  */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0065  */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x0079  */
    /* JADX WARNING: Removed duplicated region for block: B:35:0x00cc  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object d(defpackage.dyy r7, defpackage.grh r8, java.lang.String r9, defpackage.jlr r10) {
        /*
            r6 = this;
            boolean r0 = r10 instanceof defpackage.emp
            if (r0 == 0) goto L_0x0013
            r0 = r10
            emp r0 = (defpackage.emp) r0
            int r1 = r0.d
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.d = r1
            goto L_0x0018
        L_0x0013:
            emp r0 = new emp
            r0.<init>(r6, r10)
        L_0x0018:
            java.lang.Object r10 = r0.b
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.d
            r3 = 1
            if (r2 == 0) goto L_0x0037
            if (r2 != r3) goto L_0x002f
            java.lang.String r9 = r0.f
            java.lang.Object r8 = r0.a
            emr r7 = r0.e
            defpackage.jji.c(r10)     // Catch:{ all -> 0x002d }
            goto L_0x0053
        L_0x002d:
            r10 = move-exception
            goto L_0x0059
        L_0x002f:
            java.lang.IllegalStateException r7 = new java.lang.IllegalStateException
            java.lang.String r8 = "call to 'resume' before 'invoke' with coroutine"
            r7.<init>(r8)
            throw r7
        L_0x0037:
            defpackage.jji.c(r10)
            hme r7 = r7.c()     // Catch:{ all -> 0x0056 }
            java.lang.String r10 = "getStartListeningResult(...)"
            defpackage.jnu.d(r7, r10)     // Catch:{ all -> 0x0056 }
            r0.e = r6     // Catch:{ all -> 0x0056 }
            r0.a = r8     // Catch:{ all -> 0x0056 }
            r0.f = r9     // Catch:{ all -> 0x0056 }
            r0.d = r3     // Catch:{ all -> 0x0056 }
            java.lang.Object r10 = defpackage.jqw.x(r7, r0)     // Catch:{ all -> 0x0056 }
            if (r10 != r1) goto L_0x0052
            return r1
        L_0x0052:
            r7 = r6
        L_0x0053:
            dyw r10 = (defpackage.dyw) r10     // Catch:{ all -> 0x002d }
            goto L_0x005d
        L_0x0056:
            r7 = move-exception
            r10 = r7
            r7 = r6
        L_0x0059:
            java.lang.Object r10 = defpackage.jji.b(r10)
        L_0x005d:
            java.lang.Throwable r0 = defpackage.jju.a(r10)
            java.lang.String r1 = "newBuilder(...)"
            if (r0 != 0) goto L_0x0079
            dyw r10 = (defpackage.dyw) r10
            java.lang.Object r10 = r10.f()
            dzh r10 = (defpackage.dzh) r10
            eak r10 = r10.b
            if (r10 != 0) goto L_0x0073
            eak r10 = defpackage.eak.c
        L_0x0073:
            java.lang.String r0 = "getAudioSourceOpeningStatus(...)"
            defpackage.jnu.d(r10, r0)
            goto L_0x00b0
        L_0x0079:
            hca r10 = a
            hco r10 = r10.h()
            hcr r0 = defpackage.hdg.a
            java.lang.String r2 = "ALT.SessionMicUpdater"
            hco r10 = r10.h(r0, r2)
            java.lang.String r0 = "reportAudioSourceOpen"
            r2 = 114(0x72, float:1.6E-43)
            java.lang.String r4 = "com/google/android/libraries/search/audio/microphone/AudioSessionToMicStateUpdater"
            java.lang.String r5 = "AudioSessionToMicStateUpdater.kt"
            hco r10 = r10.j(r4, r0, r2, r5)
            hby r10 = (defpackage.hby) r10
            java.lang.String r0 = "#audio# start status failed, continue update(%s)"
            r10.u(r0, r9)
            eak r10 = defpackage.eak.c
            htk r10 = r10.l()
            defpackage.jnu.d(r10, r1)
            dlv r10 = defpackage.jnu.e(r10, "builder")
            eaj r0 = defpackage.eaj.UNKNOWN_OPENING_FAILURE
            r10.f(r0)
            eak r10 = r10.e()
        L_0x00b0:
            eqr r7 = r7.b
            eqq r0 = defpackage.eqq.g
            htk r0 = r0.l()
            defpackage.jnu.d(r0, r1)
            bzl r0 = defpackage.jnu.e(r0, "builder")
            ept r1 = defpackage.ept.CLIENT_TYPE_AUDIO_REQUEST
            r0.Q(r1)
            grh r8 = (defpackage.grh) r8
            boolean r1 = r8.f()
            if (r1 == 0) goto L_0x00d9
            java.lang.Object r8 = r8.b()
            java.lang.Number r8 = (java.lang.Number) r8
            int r8 = r8.intValue()
            r0.P(r8)
        L_0x00d9:
            r0.T(r9)
            r0.S(r10)
            eqq r8 = r0.O()
            r7.e(r8)
            java.lang.Boolean r7 = java.lang.Boolean.valueOf(r3)
            return r7
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.emr.d(dyy, grh, java.lang.String, jlr):java.lang.Object");
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x0037  */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0060  */
    /* JADX WARNING: Removed duplicated region for block: B:32:0x0074  */
    /* JADX WARNING: Removed duplicated region for block: B:35:0x00c7  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object e(defpackage.ebl r7, defpackage.grh r8, java.lang.String r9, defpackage.jlr r10) {
        /*
            r6 = this;
            boolean r0 = r10 instanceof defpackage.emq
            if (r0 == 0) goto L_0x0013
            r0 = r10
            emq r0 = (defpackage.emq) r0
            int r1 = r0.d
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.d = r1
            goto L_0x0018
        L_0x0013:
            emq r0 = new emq
            r0.<init>(r6, r10)
        L_0x0018:
            java.lang.Object r10 = r0.b
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.d
            r3 = 1
            if (r2 == 0) goto L_0x0037
            if (r2 != r3) goto L_0x002f
            java.lang.String r9 = r0.f
            java.lang.Object r8 = r0.a
            emr r7 = r0.e
            defpackage.jji.c(r10)     // Catch:{ all -> 0x002d }
            goto L_0x004e
        L_0x002d:
            r10 = move-exception
            goto L_0x0054
        L_0x002f:
            java.lang.IllegalStateException r7 = new java.lang.IllegalStateException
            java.lang.String r8 = "call to 'resume' before 'invoke' with coroutine"
            r7.<init>(r8)
            throw r7
        L_0x0037:
            defpackage.jji.c(r10)
            hme r7 = r7.c()     // Catch:{ all -> 0x0051 }
            r0.e = r6     // Catch:{ all -> 0x0051 }
            r0.a = r8     // Catch:{ all -> 0x0051 }
            r0.f = r9     // Catch:{ all -> 0x0051 }
            r0.d = r3     // Catch:{ all -> 0x0051 }
            java.lang.Object r10 = defpackage.jqw.x(r7, r0)     // Catch:{ all -> 0x0051 }
            if (r10 != r1) goto L_0x004d
            return r1
        L_0x004d:
            r7 = r6
        L_0x004e:
            ebj r10 = (defpackage.ebj) r10     // Catch:{ all -> 0x002d }
            goto L_0x0058
        L_0x0051:
            r7 = move-exception
            r10 = r7
            r7 = r6
        L_0x0054:
            java.lang.Object r10 = defpackage.jji.b(r10)
        L_0x0058:
            java.lang.Throwable r0 = defpackage.jju.a(r10)
            java.lang.String r1 = "newBuilder(...)"
            if (r0 != 0) goto L_0x0074
            ebj r10 = (defpackage.ebj) r10
            java.lang.Object r10 = r10.f()
            ebo r10 = (defpackage.ebo) r10
            eak r10 = r10.b
            if (r10 != 0) goto L_0x006e
            eak r10 = defpackage.eak.c
        L_0x006e:
            java.lang.String r0 = "getAudioSourceOpeningStatus(...)"
            defpackage.jnu.d(r10, r0)
            goto L_0x00ab
        L_0x0074:
            hca r10 = a
            hco r10 = r10.h()
            hcr r0 = defpackage.hdg.a
            java.lang.String r2 = "ALT.SessionMicUpdater"
            hco r10 = r10.h(r0, r2)
            java.lang.String r0 = "reportAudioSourceOpen"
            r2 = 168(0xa8, float:2.35E-43)
            java.lang.String r4 = "com/google/android/libraries/search/audio/microphone/AudioSessionToMicStateUpdater"
            java.lang.String r5 = "AudioSessionToMicStateUpdater.kt"
            hco r10 = r10.j(r4, r0, r2, r5)
            hby r10 = (defpackage.hby) r10
            java.lang.String r0 = "#audio# hotword start status failed, continue update(%s)"
            r10.u(r0, r9)
            eak r10 = defpackage.eak.c
            htk r10 = r10.l()
            defpackage.jnu.d(r10, r1)
            dlv r10 = defpackage.jnu.e(r10, "builder")
            eaj r0 = defpackage.eaj.UNKNOWN_OPENING_FAILURE
            r10.f(r0)
            eak r10 = r10.e()
        L_0x00ab:
            eqr r7 = r7.b
            eqq r0 = defpackage.eqq.g
            htk r0 = r0.l()
            defpackage.jnu.d(r0, r1)
            bzl r0 = defpackage.jnu.e(r0, "builder")
            ept r1 = defpackage.ept.CLIENT_TYPE_HOTWORD
            r0.Q(r1)
            grh r8 = (defpackage.grh) r8
            boolean r1 = r8.f()
            if (r1 == 0) goto L_0x00d4
            java.lang.Object r8 = r8.b()
            java.lang.Number r8 = (java.lang.Number) r8
            int r8 = r8.intValue()
            r0.P(r8)
        L_0x00d4:
            r0.T(r9)
            r0.S(r10)
            eqq r8 = r0.O()
            r7.e(r8)
            java.lang.Boolean r7 = java.lang.Boolean.valueOf(r3)
            return r7
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.emr.e(ebl, grh, java.lang.String, jlr):java.lang.Object");
    }

    public final String f(int i) {
        if (i != -1) {
            return a.ak(i, "arc_active:");
        }
        long a2 = this.b.a();
        return "arc_inactive:" + a2;
    }

    public final String g(int i) {
        if (i != -1) {
            return a.ak(i, "hwc_active:");
        }
        long a2 = this.b.a();
        return "hwc_inactive:" + a2;
    }

    public final void h(dyy dyy, int i, eej eej) {
        jnu.e(dyy, "session");
        jnu.e(eej, "sourceMetadata");
        job.S(this.c, (jlv) null, (jqt) null, new emm(this, i, eej, dyy, (jlr) null, 1), 3);
    }

    public final void i(ebl ebl, int i, eej eej) {
        jnu.e(ebl, "session");
        jnu.e(eej, "sourceMetadata");
        job.S(this.c, (jlv) null, (jqt) null, new emm(this, i, eej, ebl, (jlr) null, 0), 3);
    }
}
