package defpackage;

/* renamed from: cyj  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cyj implements hko {
    public final /* synthetic */ int a;
    public final /* synthetic */ int b;
    public final /* synthetic */ Object c;
    public final /* synthetic */ Object d;
    private final /* synthetic */ int e;

    public /* synthetic */ cyj(cvy cvy, csx csx, int i, int i2, int i3) {
        this.e = i3;
        this.d = cvy;
        this.c = csx;
        this.b = i;
        this.a = i2;
    }

    /* JADX WARNING: type inference failed for: r8v2, types: [java.lang.Object, hkn] */
    public final hme a(Object obj) {
        if (this.e != 0) {
            boolean booleanValue = ((Boolean) obj).booleanValue();
            Object obj2 = this.c;
            if (!booleanValue) {
                cyh.h("%s: Subscribing to file failed for group: %s", "FileGroupManager", ((csx) obj2).c);
                return hfc.K(false);
            }
            return ((cvy) this.d).o((csx) obj2, this.b + 1, this.a);
        }
        grh grh = (grh) obj;
        if (!grh.f()) {
            return hma.a;
        }
        int i = this.a;
        int i2 = this.b;
        return czw.e(this.d.a()).f(new fdu((cyk) this.c, i2, i, grh, 1), hld.a);
    }

    public /* synthetic */ cyj(cyk cyk, hkn hkn, int i, int i2, int i3) {
        this.e = i3;
        this.c = cyk;
        this.d = hkn;
        this.b = i;
        this.a = i2;
    }
}
