package defpackage;

/* renamed from: epq  reason: default package */
/* compiled from: PG */
public enum epq implements hts {
    REQUEST_UNKNOWN(0),
    REQUEST_OPEN_PENDING(1);
    
    public final int c;

    private epq(int i) {
        this.c = i;
    }

    public static epq b(int i) {
        if (i == 0) {
            return REQUEST_UNKNOWN;
        }
        if (i != 1) {
            return null;
        }
        return REQUEST_OPEN_PENDING;
    }

    public final int a() {
        return this.c;
    }

    public final String toString() {
        return Integer.toString(this.c);
    }
}
