package defpackage;

/* renamed from: cpg  reason: default package */
/* compiled from: PG */
public final class cpg implements Runnable {
    public final Runnable a;
    public final /* synthetic */ cph b;

    public cpg(cph cph, Runnable runnable) {
        this.b = cph;
        this.a = runnable;
    }

    public final void run() {
        ckm ckm = new ckm((Object) this, (Object) Thread.currentThread(), 12);
        cph cph = this.b;
        cph.c.f();
        hmg b2 = cph.e.b(ckm, 1, this.b.c.g());
        try {
            this.a.run();
        } finally {
            b2.cancel(false);
        }
    }

    public final String toString() {
        return this.a.toString();
    }
}
