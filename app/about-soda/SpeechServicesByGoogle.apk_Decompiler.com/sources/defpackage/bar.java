package defpackage;

/* renamed from: bar  reason: default package */
/* compiled from: PG */
public final class bar extends jqp {
    public static final bar a = new bar();
    private static final jqp d = jrf.a;

    private bar() {
    }

    public final void a(jlv jlv, Runnable runnable) {
        jnu.e(jlv, "context");
        jnu.e(runnable, "block");
        d.a(jlv, runnable);
    }

    public final boolean b(jlv jlv) {
        jnu.e(jlv, "context");
        return true;
    }
}
