<?xml version="1.0" encoding="utf-8"?>
<inset xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:insetLeft="@dimen/appcompat_dialog_background_inset" android:insetRight="@dimen/appcompat_dialog_background_inset" android:insetTop="@dimen/appcompat_dialog_background_inset" android:insetBottom="@dimen/appcompat_dialog_background_inset">
    <shape android:shape="rectangle">
        <corners android:radius="@dimen/google_shape_corner_size_medium_component"/>
        <solid android:color="?attr/colorSurface"/>
    </shape>
</inset>
