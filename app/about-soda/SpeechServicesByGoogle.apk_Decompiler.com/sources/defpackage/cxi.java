package defpackage;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Locale;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Matcher;

/* renamed from: cxi  reason: default package */
/* compiled from: PG */
public final class cxi {
    public final Object a;

    public cxi(Object obj, char[] cArr) {
        this.a = obj;
    }

    public static final boolean c(long j, long j2) {
        if (j % j2 == 0) {
            return true;
        }
        return false;
    }

    public final void A(int i, int i2) {
        ((hsz) this.a).s(i, i2);
    }

    public final void B(int i, long j) {
        ((hsz) this.a).D(i, j);
    }

    public final void C(int i, Object obj, hvp hvp) {
        ((hsz) this.a).u(i, (hva) obj, hvp);
    }

    public final void D(int i, Object obj) {
        if (obj instanceof hsq) {
            ((hsz) this.a).x(i, (hsq) obj);
            return;
        }
        ((hsz) this.a).w(i, (hva) obj);
    }

    public final void E(int i, int i2) {
        ((hsz) this.a).o(i, i2);
    }

    public final void F(int i, long j) {
        ((hsz) this.a).q(i, j);
    }

    public final void G(int i, int i2) {
        ((hsz) this.a).ao(i, i2);
    }

    public final void H(int i, long j) {
        ((hsz) this.a).aq(i, j);
    }

    public final void I(int i, String str) {
        ((hsz) this.a).y(i, str);
    }

    public final void J(int i, int i2) {
        ((hsz) this.a).B(i, i2);
    }

    public final void K(int i, long j) {
        ((hsz) this.a).D(i, j);
    }

    /* JADX WARNING: type inference failed for: r2v0, types: [java.lang.Object, java.lang.AutoCloseable] */
    /* JADX WARNING: Unknown variable types count: 1 */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void L(java.lang.Object r2, java.util.concurrent.Executor r3) {
        /*
            r1 = this;
            defpackage.fvf.aP(r3)
            if (r2 == 0) goto L_0x000c
            java.lang.Object r0 = r1.a
            hku r0 = (defpackage.hku) r0
            r0.a(r2, r3)
        L_0x000c:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.cxi.L(java.lang.Object, java.util.concurrent.Executor):void");
    }

    /* JADX WARNING: type inference failed for: r0v2, types: [java.util.List, java.lang.Object] */
    public final void M(haj haj) {
        fvf.aA(!haj.b(), "range must not be empty, but was %s", haj);
        this.a.add(haj);
    }

    /* JADX WARNING: type inference failed for: r0v0, types: [gsb, java.lang.Object] */
    public final css a() {
        return (css) this.a.a();
    }

    /* JADX WARNING: type inference failed for: r6v6, types: [cyi, java.lang.Object] */
    public final hme b(long j, grh grh) {
        int i = (j > 0 ? 1 : (j == 0 ? 0 : -1));
        if (i == 0) {
            return hfc.K(gqd.a);
        }
        if (i < 0) {
            cyh.g("Bad sample interval (negative number): %d", Long.valueOf(j));
            return hfc.K(gqd.a);
        } else if (ikc.a.a().m() && grh.f()) {
            return czw.e(grh.b().c()).f(new fjl(j, 1), hld.a);
        } else {
            if (i == 0 || !c(((Random) this.a).nextLong(), j)) {
                return hfc.K(gqd.a);
            }
            htk l = hiw.f.l();
            if (!l.b.B()) {
                l.u();
            }
            hiw hiw = (hiw) l.b;
            hiw.a |= 1;
            hiw.b = false;
            return hfc.K(grh.h((hiw) l.r()));
        }
    }

    public final void d(csx csx) {
        e(1008, csx);
    }

    public final void e(int i, csx csx) {
        ((cyk) this.a).e(i, csx.c, csx.e, csx.r, csx.s);
    }

    public final /* synthetic */ dze f() {
        htq o = ((htk) this.a).r();
        jnu.d(o, "build(...)");
        return (dze) o;
    }

    public final /* synthetic */ dzc g() {
        htq o = ((htk) this.a).r();
        jnu.d(o, "build(...)");
        return (dzc) o;
    }

    public final /* synthetic */ dyt h() {
        htq o = ((htk) this.a).r();
        jnu.d(o, "build(...)");
        return (dyt) o;
    }

    public final void i(boolean z) {
        htk htk = (htk) this.a;
        if (!htk.b.B()) {
            htk.u();
        }
        dyt dyt = (dyt) htk.b;
        dyt dyt2 = dyt.l;
        dyt.a |= 1024;
        dyt.k = z;
    }

    public final /* synthetic */ dyb j() {
        htq o = ((htk) this.a).r();
        jnu.d(o, "build(...)");
        return (dyb) o;
    }

    public final void k(hsq hsq) {
        jnu.e(hsq, "value");
        htk htk = (htk) this.a;
        if (!htk.b.B()) {
            htk.u();
        }
        dyb dyb = (dyb) htk.b;
        dyb dyb2 = dyb.c;
        hsq.getClass();
        dyb.a |= 1;
        dyb.b = hsq;
    }

    public final /* synthetic */ ekt l() {
        htq o = ((htk) this.a).r();
        jnu.d(o, "build(...)");
        return (ekt) o;
    }

    public final void m(String str) {
        htk htk = (htk) this.a;
        if (!htk.b.B()) {
            htk.u();
        }
        ekt ekt = (ekt) htk.b;
        ekt ekt2 = ekt.c;
        ekt.a |= 1;
        ekt.b = str;
    }

    /* JADX WARNING: type inference failed for: r1v0, types: [java.lang.Object, jjk] */
    public final ekp n(ebg ebg, jmp jmp, hme hme) {
        ebg.getClass();
        hme.getClass();
        jqs jqs = (jqs) this.a.b();
        jqs.getClass();
        return new ekp(ebg, jmp, hme, jqs);
    }

    public final /* synthetic */ ehg o() {
        htq o = ((htk) this.a).r();
        jnu.d(o, "build(...)");
        return (ehg) o;
    }

    public final boolean p(BluetoothDevice bluetoothDevice) {
        jnu.e(bluetoothDevice, "device");
        huf<String> huf = ((hxc) this.a).a;
        jnu.d(huf, "getElementList(...)");
        if ((huf instanceof Collection) && huf.isEmpty()) {
            return false;
        }
        for (String str : huf) {
            String d = efs.d(bluetoothDevice);
            Locale locale = Locale.getDefault();
            jnu.d(locale, "getDefault(...)");
            String lowerCase = d.toLowerCase(locale);
            jnu.d(lowerCase, "toLowerCase(...)");
            jnu.b(str);
            Locale locale2 = Locale.getDefault();
            jnu.d(locale2, "getDefault(...)");
            String lowerCase2 = str.toLowerCase(locale2);
            jnu.d(lowerCase2, "toLowerCase(...)");
            if (job.s(lowerCase, lowerCase2)) {
                return true;
            }
        }
        return false;
    }

    /* JADX WARNING: type inference failed for: r1v8, types: [java.lang.Object, java.util.Queue] */
    public final void q(int i, eep eep) {
        jnu.e(eep, "type");
        htk l = eeo.e.l();
        jnu.d(l, "newBuilder(...)");
        jnu.e(l, "builder");
        if (!l.b.B()) {
            l.u();
        }
        eeo eeo = (eeo) l.b;
        eeo.a |= 1;
        eeo.b = i;
        jnu.e(eep, "value");
        if (!l.b.B()) {
            l.u();
        }
        eeo eeo2 = (eeo) l.b;
        eeo2.c = eep.f;
        eeo2.a |= 2;
        long epochMilli = cqx.G().toEpochMilli();
        if (!l.b.B()) {
            l.u();
        }
        ? r1 = this.a;
        eeo eeo3 = (eeo) l.b;
        eeo3.a |= 4;
        eeo3.d = epochMilli;
        htq o = l.r();
        jnu.d(o, "build(...)");
        r1.add((eeo) o);
    }

    public final /* synthetic */ htc r() {
        htq o = ((htk) this.a).r();
        jnu.d(o, "build(...)");
        return (htc) o;
    }

    public final void s(int i, boolean z) {
        ((hsz) this.a).l(i, z);
    }

    public final void t(int i, hsq hsq) {
        ((hsz) this.a).m(i, hsq);
    }

    public final void u(int i, double d) {
        ((hsz) this.a).aj(i, d);
    }

    public final void v(int i, int i2) {
        ((hsz) this.a).s(i, i2);
    }

    public final void w(int i, int i2) {
        ((hsz) this.a).o(i, i2);
    }

    public final void x(int i, long j) {
        ((hsz) this.a).q(i, j);
    }

    public final void y(int i, float f) {
        ((hsz) this.a).al(i, f);
    }

    public final void z(int i, Object obj, hvp hvp) {
        hsz hsz = (hsz) this.a;
        hsz.A(i, 3);
        hvp.m((hva) obj, hsz.f);
        hsz.A(i, 4);
    }

    public cxi(Object obj, short[] sArr) {
        this.a = obj;
    }

    public cxi() {
        this.a = fvf.as(new bps(8));
    }

    public cxi(hsz hsz) {
        byte[] bArr = hug.b;
        this.a = hsz;
        hsz hsz2 = hsz;
        hsz.f = this;
    }

    public cxi(jjk jjk, byte[] bArr) {
        jjk.getClass();
        this.a = jjk;
    }

    public cxi(Context context) {
        this.a = context.getApplicationContext();
    }

    public cxi(byte[] bArr) {
        this.a = new AtomicInteger();
    }

    public cxi(cqx cqx) {
        jnu.e(cqx, "clock");
        this.a = new hbm(new gwt(100));
    }

    public cxi(jjk jjk) {
        jjk.getClass();
        this.a = jjk;
    }

    public cxi(Matcher matcher) {
        fvf.aP(matcher);
        this.a = matcher;
    }

    public cxi(char[] cArr) {
        this.a = new ArrayList();
    }

    public cxi(itg itg, irw irw) {
        fvf.aP(itg);
        this.a = itg;
        fvf.aP(irw);
    }

    public cxi(Object obj, byte[] bArr) {
        a.w(obj, "Request message cannot be null");
        this.a = obj;
    }

    public cxi(irw irw) {
        fvf.aP(irw);
        this.a = irw;
    }

    public cxi(Object obj) {
        a.w(obj, "Response message cannot be null");
        this.a = obj;
    }
}
