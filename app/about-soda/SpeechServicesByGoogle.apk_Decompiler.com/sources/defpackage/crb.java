package defpackage;

import java.util.concurrent.Executor;

/* renamed from: crb  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class crb implements ckq {
    public final /* synthetic */ cjt a;
    public final /* synthetic */ String b;
    public final /* synthetic */ Executor c;

    public /* synthetic */ crb(cjt cjt, String str, Executor executor) {
        this.a = cjt;
        this.b = str;
        this.c = executor;
    }

    public final void c(Object obj) {
        Void voidR = (Void) obj;
        boolean z = crd.a;
        cjt cjt = this.a;
        String str = this.b;
        cjt.k(str).e(this.c, new crc(str, 1));
    }
}
