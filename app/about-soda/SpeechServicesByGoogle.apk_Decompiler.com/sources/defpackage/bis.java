package defpackage;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/* renamed from: bis  reason: default package */
/* compiled from: PG */
public final class bis {
    public final bbv a;
    public final Map b = new HashMap();
    public final Map c = new HashMap();
    public final Object d = new Object();

    static {
        bbk.b("WorkTimer");
    }

    public bis(bbv bbv) {
        this.a = bbv;
    }

    public final void a(bgt bgt) {
        synchronized (this.d) {
            if (((bcu) this.b.remove(bgt)) != null) {
                bbk.a();
                Objects.toString(bgt);
                this.c.remove(bgt);
            }
        }
    }
}
