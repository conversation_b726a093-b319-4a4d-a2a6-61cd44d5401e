package defpackage;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.database.Cursor;
import android.net.Uri;
import android.os.Bundle;
import android.util.Log;
import android.util.Pair;
import android.view.View;
import android.view.ViewGroup;
import androidx.work.impl.WorkDatabase;
import androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy;
import androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy;
import androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy;
import androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy;
import androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver;
import java.io.File;
import java.util.ArrayList;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicBoolean;

/* renamed from: ai  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class ai implements Runnable {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    private final /* synthetic */ int d;

    public ai(Intent intent, Context context, BroadcastReceiver.PendingResult pendingResult, int i) {
        this.d = i;
        this.b = intent;
        this.a = context;
        this.c = pendingResult;
    }

    /* JADX INFO: finally extract failed */
    /* JADX WARNING: type inference failed for: r1v18, types: [ano, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v13, types: [java.lang.Object, jmp] */
    /* JADX WARNING: type inference failed for: r2v16, types: [java.lang.Object, jmp] */
    /* JADX WARNING: type inference failed for: r1v23, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v19, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v81, types: [cmm, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v98, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v48, types: [java.util.concurrent.Future, java.lang.Object] */
    public final void run() {
        bzs bzs;
        int i;
        int i2;
        Bundle bundle = null;
        boolean z = true;
        switch (this.d) {
            case 0:
                bc bcVar = ((cy) this.a).c;
                bc bcVar2 = ((cy) this.b).c;
                am amVar = (am) this.c;
                ce.a(bcVar, bcVar2, amVar.f, amVar.e, false);
                return;
            case 1:
                Object obj = this.b;
                jnu.e(obj, "$container");
                ((ViewGroup) obj).endViewTransition((View) this.a);
                Object obj2 = this.c;
                ((ad) obj2).a.a.f((cu) obj2);
                return;
            case 2:
                gxq g = ((gxl) this.b).g();
                akn akn = ((ajn) this.c).j;
                aet aet = akn.e;
                yi.k(aet);
                gxq o = gxq.o(g);
                akm akm = akn.b;
                akm.b = o;
                if (!g.isEmpty()) {
                    Object obj3 = this.a;
                    akm.d = (ank) g.get(0);
                    yi.k(obj3);
                    akm.e = (ank) obj3;
                }
                if (akm.c == null) {
                    akm.c = akm.b(aet, akm.b, akm.d, akm.a);
                }
                akm.c(aet.g());
                return;
            case 3:
                Pair pair = (Pair) this.c;
                ((ajp) this.a).a.j.B(((Integer) pair.first).intValue(), (ank) pair.second, (hpy) this.b);
                return;
            case 4:
                this.b.B(0, (ank) ((dvc) this.a).c, (hpy) this.c);
                return;
            case 5:
                boolean z2 = ((AtomicBoolean) this.a).get();
                Object obj4 = this.c;
                ? r2 = this.b;
                if (!z2) {
                    try {
                        ((po) obj4).c(r2.a());
                        return;
                    } catch (Throwable th) {
                        ((po) obj4).d(th);
                        return;
                    }
                } else {
                    return;
                }
            case 6:
                boolean z3 = ((AtomicBoolean) this.a).get();
                Object obj5 = this.c;
                ? r22 = this.b;
                if (!z3) {
                    try {
                        ((po) obj5).c(r22.a());
                        return;
                    } catch (Throwable th2) {
                        ((po) obj5).d(th2);
                        return;
                    }
                } else {
                    return;
                }
            case 7:
                Object obj6 = this.a;
                ? r1 = this.b;
                Object obj7 = this.c;
                try {
                    z = ((Boolean) r1.get()).booleanValue();
                } catch (InterruptedException | ExecutionException unused) {
                }
                synchronized (((bcp) obj7).j) {
                    bgt h = ((eez) obj6).h();
                    String str = h.a;
                    if (((bcp) obj7).e(str) == obj6) {
                        ((bcp) obj7).d(str);
                    }
                    bbk.a();
                    obj7.getClass().getSimpleName();
                    for (bce a2 : ((bcp) obj7).i) {
                        a2.a(h, z);
                    }
                }
                return;
            case 8:
                ((bcp) ((bxq) this.b).b).g((byw) this.a);
                return;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                try {
                    boolean booleanExtra = ((Intent) this.b).getBooleanExtra("KEY_BATTERY_NOT_LOW_PROXY_ENABLED", false);
                    boolean booleanExtra2 = ((Intent) this.b).getBooleanExtra("KEY_BATTERY_CHARGING_PROXY_ENABLED", false);
                    boolean booleanExtra3 = ((Intent) this.b).getBooleanExtra("KEY_STORAGE_NOT_LOW_PROXY_ENABLED", false);
                    boolean booleanExtra4 = ((Intent) this.b).getBooleanExtra("KEY_NETWORK_STATE_PROXY_ENABLED", false);
                    bbk.a();
                    int i3 = ConstraintProxyUpdateReceiver.a;
                    bif.a((Context) this.a, ConstraintProxy$BatteryNotLowProxy.class, booleanExtra);
                    bif.a((Context) this.a, ConstraintProxy$BatteryChargingProxy.class, booleanExtra2);
                    bif.a((Context) this.a, ConstraintProxy$StorageNotLowProxy.class, booleanExtra3);
                    bif.a((Context) this.a, ConstraintProxy$NetworkStateProxy.class, booleanExtra4);
                    return;
                } finally {
                    ((BroadcastReceiver.PendingResult) this.c).finish();
                }
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                Object obj8 = this.c;
                jnu.e(obj8, "$workDatabase");
                Object obj9 = this.b;
                jnu.e(obj9, "$name");
                bhf A = ((WorkDatabase) obj8).A();
                auu a3 = auu.a("SELECT id FROM workspec WHERE state NOT IN (2, 3, 5) AND id IN (SELECT work_spec_id FROM workname WHERE name=?)", 1);
                a3.g(1, (String) obj9);
                bhx bhx = (bhx) A;
                bhx.a.k();
                Cursor f = vy.f(bhx.a, a3, false);
                try {
                    ArrayList<String> arrayList = new ArrayList<>(f.getCount());
                    while (f.moveToNext()) {
                        arrayList.add(f.getString(0));
                    }
                    f.close();
                    a3.j();
                    for (String g2 : arrayList) {
                        xm.g((bdm) this.a, g2);
                    }
                    return;
                } catch (Throwable th3) {
                    f.close();
                    a3.j();
                    throw th3;
                }
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                dvc dvc = (dvc) this.a;
                hbq v = ((gxq) dvc.c).listIterator();
                while (v.hasNext()) {
                    ? r3 = this.b;
                    brs brs = (brs) v.next();
                    if (r3 == 0 || r3.isEmpty()) {
                        bzs = null;
                    } else {
                        bzs = ((bxq) r3.get(brs.a)).d(brs.b);
                    }
                    double d2 = brs.e * ((double) dvc.a);
                    if (bzs != null) {
                        i = bzs.a(brs.c);
                    } else {
                        i = brs.c;
                    }
                    if (bzs != null) {
                        i2 = bzs.a(brs.d);
                    } else {
                        i2 = brs.d;
                    }
                    ((bsf) this.c).rangeStart((int) d2, i, i2);
                }
                return;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                cea cea = (cea) this.a;
                if (cea.a > 0) {
                    Object obj10 = this.c;
                    Bundle bundle2 = cea.b;
                    if (bundle2 != null) {
                        bundle = bundle2.getBundle((String) this.b);
                    }
                    ((cdx) obj10).d(bundle);
                }
                if (((cea) this.a).a >= 2) {
                    ((cdx) this.c).i();
                }
                if (((cea) this.a).a >= 3) {
                    ((cdp) this.c).k();
                }
                if (((cea) this.a).a >= 4) {
                    ((cdx) this.c).j();
                    return;
                }
                return;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                ((cmg) this.c).f((String) this.b, this.a);
                return;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                int i4 = crj.a;
                if (((AtomicBoolean) this.b).compareAndSet(false, true)) {
                    ((Context) this.a).unregisterReceiver((BroadcastReceiver) this.c);
                    return;
                }
                return;
            case 15:
                ((diz) ((cyw) this.c).c).d((File) this.a, (String) this.b);
                return;
            case 16:
                ((cxx) this.a).b((String) this.b, (Uri) this.c);
                return;
            case 17:
                ((dgm) this.a).d.a(new dao(this.c, this.b, 6));
                return;
            case 18:
                ((dhp) this.a).a((ddk) this.c, this.b);
                return;
            case 19:
                dru.b(new dpz(this.c, this.a, 2, (byte[]) null), this.b);
                return;
            default:
                Object obj11 = this.c;
                Object obj12 = this.b;
                try {
                    hfc.S(this.a);
                    Log.i("PhenotypeBackgroundRecv", a.an((String) obj12, "Successfully updated snapshot for "));
                } catch (ExecutionException e) {
                    Log.w("PhenotypeBackgroundRecv", a.an((String) obj12, "Failed to update local snapshot for "), e);
                } catch (Throwable th4) {
                    ((BroadcastReceiver.PendingResult) obj11).finish();
                    throw th4;
                }
                ((BroadcastReceiver.PendingResult) obj11).finish();
                return;
        }
    }

    public ai(cea cea, cdx cdx, int i) {
        this.d = i;
        this.c = cdx;
        this.b = "ConnectionlessLifecycleHelper";
        this.a = cea;
    }

    public /* synthetic */ ai(Object obj, Object obj2, Object obj3, int i) {
        this.d = i;
        this.a = obj;
        this.b = obj2;
        this.c = obj3;
    }

    public /* synthetic */ ai(Object obj, Object obj2, Object obj3, int i, byte[] bArr) {
        this.d = i;
        this.b = obj;
        this.a = obj2;
        this.c = obj3;
    }

    public /* synthetic */ ai(Object obj, Object obj2, Object obj3, int i, char[] cArr) {
        this.d = i;
        this.c = obj;
        this.b = obj2;
        this.a = obj3;
    }

    public /* synthetic */ ai(Object obj, Object obj2, Object obj3, int i, int[] iArr) {
        this.d = i;
        this.c = obj;
        this.a = obj2;
        this.b = obj3;
    }

    public /* synthetic */ ai(Object obj, Object obj2, Object obj3, int i, short[] sArr) {
        this.d = i;
        this.a = obj;
        this.c = obj2;
        this.b = obj3;
    }
}
