package defpackage;

import android.os.Parcel;
import android.os.Parcelable;
import j$.util.Objects;

/* renamed from: boy  reason: default package */
/* compiled from: PG */
public final class boy extends cgf {
    public static final Parcelable.Creator CREATOR = new aqu(16);
    public final String a;
    public final String b;
    public final int c;
    public final int d;
    public final int e;
    public final int f;

    public boy(String str, String str2, int i, int i2, int i3, int i4) {
        this.a = str;
        this.b = str2;
        this.c = i;
        this.d = i2;
        this.e = i3;
        this.f = i4;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof boy)) {
            return false;
        }
        boy boy = (boy) obj;
        if (this.c == boy.c && this.d == boy.d && Objects.equals(this.a, boy.a) && this.e == boy.e && this.f == boy.f) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return Objects.hash(this.a, Integer.valueOf(this.c), Integer.valueOf(this.d), Integer.valueOf(this.e), Integer.valueOf(this.f));
    }

    public final String toString() {
        grg r = ftd.r(this);
        r.b("name", this.a);
        r.b("modelName", this.b);
        r.e("type", this.c);
        r.e("variant", this.d);
        r.e("id", this.e);
        r.e("version", this.f);
        return r.toString();
    }

    public final void writeToParcel(Parcel parcel, int i) {
        String str = this.a;
        int i2 = cgr.i(parcel);
        cgr.y(parcel, 1, str);
        cgr.y(parcel, 2, this.b);
        cgr.o(parcel, 3, this.c);
        cgr.o(parcel, 4, this.d);
        cgr.o(parcel, 5, this.e);
        cgr.o(parcel, 6, this.f);
        cgr.k(parcel, i2);
    }
}
