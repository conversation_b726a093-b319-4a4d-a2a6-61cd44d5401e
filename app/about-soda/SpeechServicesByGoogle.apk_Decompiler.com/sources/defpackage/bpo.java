package defpackage;

import android.content.Context;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;

/* renamed from: bpo  reason: default package */
/* compiled from: PG */
public final class bpo {
    public final Context a;
    public final ExecutorService b;
    public final Executor c;

    public bpo() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof bpo) {
            bpo bpo = (bpo) obj;
            if (!this.a.equals(bpo.a) || !this.b.equals(bpo.b) || !this.c.equals(bpo.c)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return ((((((((this.a.hashCode() ^ 1000003) * 1000003) ^ this.b.hashCode()) * 1000003) ^ this.c.hashCode()) * 1000003) ^ 1237) * 1000003) ^ 1237;
    }

    public final String toString() {
        Executor executor = this.c;
        ExecutorService executorService = this.b;
        String valueOf = String.valueOf(this.a);
        String valueOf2 = String.valueOf(executorService);
        String valueOf3 = String.valueOf(executor);
        return "AiCoreClientOptions{context=" + valueOf + ", workerExecutor=" + valueOf2 + ", callbackExecutor=" + valueOf3 + ", bindImportantEnabled=false, bindWaivePriorityEnabled=false}";
    }

    public bpo(Context context, ExecutorService executorService, Executor executor) {
        this.a = context;
        this.b = executorService;
        this.c = executor;
    }
}
