package com.google.android.libraries.assistant.soda;

import java.nio.ByteBuffer;
import java.util.logging.Logger;
import android.util.Log;

import com.google.android.apps.googlespeechoffline.speech.CustomSoda;
import com.google.speech.recognizer.ResourceManager;

/**
 * Base class for SODA (Speech On-Device API) library
 * 
 * This class provides access to real-time speech recognition functionality
 * through JNI calls to the native libsoda.so library.
 * 
 * Similar to AbstractRecognizer pattern, this serves as a base for custom implementations.
 */
public class Soda {
    
    private static final Logger logger = Logger.getLogger(Soda.class.getName());
    static public String TAG = "Soda";
    
    // Native object pointer
    protected long nativePtr;
    private native long nativeConstruct();
    private native void nativeInit();
    private native long nativeConstruct(Object param1, Object param2, Object param3);
    private static native boolean nativeAddAudio(long nativePtr, ByteBuffer audioBuffer, int length);

    public Soda() {

    }


    public boolean addAudio(ByteBuffer audioBuffer, int length) {
        return nativeAddAudio(this.nativePtr, audioBuffer, length);
    }

    public final void init(String model, Soda soda) {
        this.nativePtr = nativeConstruct(this, model, soda);
        this.nativeInit();

    }

    // JNI 会直接回调 CustomSoda 的方法
    public void handleSodaEvent(byte[] data) {
        System.out.println("## handleSodaEvent");
        // 处理音频事件
    }

    public void handleStart() {
        System.out.println("## handleStart");
    }

    public void handleStop(int code) {
        System.out.println("## handleStop " + code);
    }

    public void handleShutdown() {
        System.out.println("## handleShutdown");
    }

    public long getNativeObj() {
        return this.nativePtr;
    }
}
