package j$.time;

abstract /* synthetic */ class t {
    static final /* synthetic */ int[] a;
    static final /* synthetic */ int[] b;

    /* JADX WARNING: Can't wrap try/catch for region: R(17:0|(2:1|2)|3|(2:5|6)|7|9|10|11|12|13|14|15|17|18|19|20|(3:21|22|24)) */
    /* JADX WARNING: Can't wrap try/catch for region: R(18:0|(2:1|2)|3|5|6|7|9|10|11|12|13|14|15|17|18|19|20|(3:21|22|24)) */
    /* JADX WARNING: Can't wrap try/catch for region: R(21:0|1|2|3|5|6|7|9|10|11|12|13|14|15|17|18|19|20|21|22|24) */
    /* JADX WARNING: Failed to process nested try/catch */
    /* JADX WARNING: Missing exception handler attribute for start block: B:11:0x0028 */
    /* JADX WARNING: Missing exception handler attribute for start block: B:13:0x0033 */
    /* JADX WARNING: Missing exception handler attribute for start block: B:19:0x004f */
    /* JADX WARNING: Missing exception handler attribute for start block: B:21:0x0059 */
    static {
        /*
            j$.time.temporal.ChronoUnit[] r0 = j$.time.temporal.ChronoUnit.values()
            int r0 = r0.length
            int[] r0 = new int[r0]
            b = r0
            r1 = 1
            j$.time.temporal.ChronoUnit r2 = j$.time.temporal.ChronoUnit.YEARS     // Catch:{ NoSuchFieldError -> 0x0012 }
            int r2 = r2.ordinal()     // Catch:{ NoSuchFieldError -> 0x0012 }
            r0[r2] = r1     // Catch:{ NoSuchFieldError -> 0x0012 }
        L_0x0012:
            r0 = 2
            int[] r2 = b     // Catch:{ NoSuchFieldError -> 0x001d }
            j$.time.temporal.ChronoUnit r3 = j$.time.temporal.ChronoUnit.DECADES     // Catch:{ NoSuchFieldError -> 0x001d }
            int r3 = r3.ordinal()     // Catch:{ NoSuchFieldError -> 0x001d }
            r2[r3] = r0     // Catch:{ NoSuchFieldError -> 0x001d }
        L_0x001d:
            r2 = 3
            int[] r3 = b     // Catch:{ NoSuchFieldError -> 0x0028 }
            j$.time.temporal.ChronoUnit r4 = j$.time.temporal.ChronoUnit.CENTURIES     // Catch:{ NoSuchFieldError -> 0x0028 }
            int r4 = r4.ordinal()     // Catch:{ NoSuchFieldError -> 0x0028 }
            r3[r4] = r2     // Catch:{ NoSuchFieldError -> 0x0028 }
        L_0x0028:
            int[] r3 = b     // Catch:{ NoSuchFieldError -> 0x0033 }
            j$.time.temporal.ChronoUnit r4 = j$.time.temporal.ChronoUnit.MILLENNIA     // Catch:{ NoSuchFieldError -> 0x0033 }
            int r4 = r4.ordinal()     // Catch:{ NoSuchFieldError -> 0x0033 }
            r5 = 4
            r3[r4] = r5     // Catch:{ NoSuchFieldError -> 0x0033 }
        L_0x0033:
            int[] r3 = b     // Catch:{ NoSuchFieldError -> 0x003e }
            j$.time.temporal.ChronoUnit r4 = j$.time.temporal.ChronoUnit.ERAS     // Catch:{ NoSuchFieldError -> 0x003e }
            int r4 = r4.ordinal()     // Catch:{ NoSuchFieldError -> 0x003e }
            r5 = 5
            r3[r4] = r5     // Catch:{ NoSuchFieldError -> 0x003e }
        L_0x003e:
            j$.time.temporal.a[] r3 = j$.time.temporal.a.values()
            int r3 = r3.length
            int[] r3 = new int[r3]
            a = r3
            j$.time.temporal.a r4 = j$.time.temporal.a.YEAR_OF_ERA     // Catch:{ NoSuchFieldError -> 0x004f }
            int r4 = r4.ordinal()     // Catch:{ NoSuchFieldError -> 0x004f }
            r3[r4] = r1     // Catch:{ NoSuchFieldError -> 0x004f }
        L_0x004f:
            int[] r1 = a     // Catch:{ NoSuchFieldError -> 0x0059 }
            j$.time.temporal.a r3 = j$.time.temporal.a.YEAR     // Catch:{ NoSuchFieldError -> 0x0059 }
            int r3 = r3.ordinal()     // Catch:{ NoSuchFieldError -> 0x0059 }
            r1[r3] = r0     // Catch:{ NoSuchFieldError -> 0x0059 }
        L_0x0059:
            int[] r0 = a     // Catch:{ NoSuchFieldError -> 0x0063 }
            j$.time.temporal.a r1 = j$.time.temporal.a.ERA     // Catch:{ NoSuchFieldError -> 0x0063 }
            int r1 = r1.ordinal()     // Catch:{ NoSuchFieldError -> 0x0063 }
            r0[r1] = r2     // Catch:{ NoSuchFieldError -> 0x0063 }
        L_0x0063:
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: j$.time.t.<clinit>():void");
    }
}
