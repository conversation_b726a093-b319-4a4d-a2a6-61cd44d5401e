package defpackage;

/* renamed from: emq  reason: default package */
/* compiled from: PG */
final class emq extends jme {
    Object a;
    /* synthetic */ Object b;
    final /* synthetic */ emr c;
    int d;
    emr e;
    String f;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public emq(emr emr, jlr jlr) {
        super(jlr);
        this.c = emr;
    }

    public final Object bk(Object obj) {
        this.b = obj;
        this.d |= Integer.MIN_VALUE;
        return this.c.e((ebl) null, (grh) null, (String) null, this);
    }
}
