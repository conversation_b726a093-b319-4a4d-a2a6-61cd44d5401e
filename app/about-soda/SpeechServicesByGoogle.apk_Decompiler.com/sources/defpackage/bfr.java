package defpackage;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;

/* renamed from: bfr  reason: default package */
/* compiled from: PG */
public abstract class bfr extends bft {
    private final BroadcastReceiver e = new bfq(this);

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bfr(Context context, cyw cyw) {
        super(context, cyw);
        jnu.e(context, "context");
    }

    public abstract IntentFilter a();

    public abstract void c(Intent intent);

    public final void d() {
        bbk.a();
        int i = bfs.a;
        getClass().getSimpleName();
        this.a.registerReceiver(this.e, a());
    }

    public final void e() {
        bbk.a();
        int i = bfs.a;
        getClass().getSimpleName();
        this.a.unregisterReceiver(this.e);
    }
}
