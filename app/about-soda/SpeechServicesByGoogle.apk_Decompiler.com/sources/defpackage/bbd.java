package defpackage;

import android.os.IBinder;
import android.os.IInterface;
import android.os.Parcel;
import android.os.ParcelFileDescriptor;
import android.os.RemoteException;
import android.util.Log;
import java.util.concurrent.atomic.AtomicBoolean;

/* renamed from: bbd  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bbd implements pq {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    public final /* synthetic */ Object c;
    private final /* synthetic */ int d;

    public /* synthetic */ bbd(bqa bqa, bpl bpl, bpq bpq, int i) {
        this.d = i;
        this.b = bqa;
        this.a = bpl;
        this.c = bpq;
    }

    /* JADX WARNING: type inference failed for: r0v1, types: [jlv, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v2, types: [jne, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v3, types: [java.util.concurrent.Executor, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v24, types: [hme, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v27, types: [java.util.List, java.lang.Object] */
    public final Object a(po poVar) {
        bpj bpj;
        bpb bpb;
        int i = this.d;
        if (i == 0) {
            ? r0 = this.a;
            jnu.e(r0, "$context");
            Object obj = this.b;
            jnu.e(obj, "$start");
            poVar.a(new alr((jrz) r0.get(jrz.c), 12), bay.a);
            return job.S(jqw.e(r0), (jlv) null, (jqt) obj, new bbe((jne) this.c, poVar, (jlr) null, 0), 1);
        } else if (i == 1) {
            ? r02 = this.c;
            jnu.e(r02, "$this_executeAsync");
            AtomicBoolean atomicBoolean = new AtomicBoolean(false);
            poVar.a(new alr(atomicBoolean, 11), bay.a);
            r02.execute(new ai((Object) atomicBoolean, (Object) poVar, this.b, 5, (short[]) null));
            return this.a;
        } else if (i == 2) {
            Object obj2 = this.a;
            Object obj3 = this.b;
            try {
                Object obj4 = ((bpx) obj2).a;
                byw byw = new byw((Object) poVar);
                ParcelFileDescriptor parcelFileDescriptor = ((bqd) obj3).c;
                if (parcelFileDescriptor != null) {
                    bpj = new bpj((byte[]) null, ((bqd) obj3).b.toLanguageTag(), 1, parcelFileDescriptor);
                } else {
                    bpj = new bpj(((bqd) obj3).a, ((bqd) obj3).b.toLanguageTag(), 1, (ParcelFileDescriptor) null);
                }
                bpd bpd = new bpd(byw, 2, (char[]) null);
                Parcel a2 = ((bov) obj4).a();
                box.c(a2, bpj);
                a2.writeStrongBinder(bpd);
                Parcel b2 = ((bov) obj4).b(2, a2);
                IBinder readStrongBinder = b2.readStrongBinder();
                if (readStrongBinder == null) {
                    bpb = null;
                } else {
                    IInterface queryLocalInterface = readStrongBinder.queryLocalInterface("com.google.android.apps.aicore.aidl.ICancellationCallback");
                    if (queryLocalInterface instanceof bpb) {
                        bpb = (bpb) queryLocalInterface;
                    } else {
                        bpb = new bpb(readStrongBinder);
                    }
                }
                b2.recycle();
                poVar.a(new bpu(bpb, 0), ((bpy) this.c).d);
                return "runInferenceFuture";
            } catch (RemoteException e) {
                poVar.d(new bpp(2, 6, "Failed to run inference", e));
                return null;
            } catch (RuntimeException e2) {
                poVar.d(new bpp(2, 0, "Failed to run inference", e2));
                return null;
            }
        } else if (i != 3) {
            ? r1 = this.b;
            poVar.a(new bpu(r1, 2), hld.a);
            aku aku = new aku((Object) poVar, this.c, 14);
            Object obj5 = this.a;
            awh awh = (awh) obj5;
            synchronized (awh.c) {
                if (((awh) obj5).a) {
                    aku.run();
                } else {
                    ((awh) obj5).b.add(aku);
                }
            }
            hfc.T(r1, new bqb(awh, poVar, aku), hld.a);
            return "FailureSignal.propagate";
        } else {
            Object obj6 = this.a;
            Object obj7 = this.c;
            try {
                boz boz = ((bpl) obj6).a;
                Parcel b3 = boz.b(11, boz.a());
                int readInt = b3.readInt();
                b3.recycle();
                Object obj8 = this.b;
                if (readInt > 0) {
                    boz boz2 = ((bpl) obj6).a;
                    boy a3 = ((bpq) obj7).a();
                    bpc bpc = new bpc((bqa) obj8, poVar, 0);
                    Parcel a4 = boz2.a();
                    box.c(a4, a3);
                    a4.writeStrongBinder(bpc);
                    Parcel b4 = boz2.b(12, a4);
                    b4.readInt();
                    b4.recycle();
                    return "requestDownloadableFeatureFuture";
                }
                bpc bpc2 = new bpc((bqa) obj8, poVar, 1, (byte[]) null);
                boz boz3 = ((bpl) obj6).a;
                boy a5 = ((bpq) obj7).a();
                Parcel a6 = boz3.a();
                box.c(a6, a5);
                a6.writeStrongBinder(bpc2);
                Parcel b5 = boz3.b(7, a6);
                b5.readInt();
                b5.recycle();
                return "requestDownloadableFeatureFuture";
            } catch (RemoteException e3) {
                bpq bpq = (bpq) obj7;
                Log.e(bqa.a, "AiCore service failed to download feature ".concat(bpq.a), e3);
                poVar.d(new bpp(1, 6, "AICore service failed to download feature ".concat(bpq.a), e3));
                return "requestDownloadableFeatureFuture";
            }
        }
    }

    public /* synthetic */ bbd(Object obj, Object obj2, Object obj3, int i) {
        this.d = i;
        this.a = obj;
        this.b = obj2;
        this.c = obj3;
    }

    public /* synthetic */ bbd(Object obj, Object obj2, Object obj3, int i, byte[] bArr) {
        this.d = i;
        this.c = obj;
        this.a = obj2;
        this.b = obj3;
    }
}
