package defpackage;

/* renamed from: bqs  reason: default package */
/* compiled from: PG */
public final class bqs implements iiz {
    private final iiz a;
    private final /* synthetic */ int b;

    public bqs(iiz iiz, int i, byte[] bArr) {
        this.b = i;
        hzz.t(iiz);
        this.a = iiz;
    }

    public final grh a() {
        if (this.b != 0) {
            return grh.h(this.a.b());
        }
        return grh.h(this.a);
    }

    public final /* synthetic */ Object b() {
        if (this.b != 0) {
            return a();
        }
        return a();
    }

    public bqs(iiz iiz, int i) {
        this.b = i;
        hzz.t(iiz);
        this.a = iiz;
    }
}
