<?xml version="1.0" encoding="utf-8"?>
<resources>
    <attr name="CarUiToolbarStyle" format="reference">
    </attr>
    <attr name="actionBarDivider" format="reference">
    </attr>
    <attr name="actionBarItemBackground" format="reference">
    </attr>
    <attr name="actionBarPopupTheme" format="reference">
    </attr>
    <attr name="actionBarSize" format="dimension">
        <enum name="wrap_content" value="0" />
    </attr>
    <attr name="actionBarSplitStyle" format="reference">
    </attr>
    <attr name="actionBarStyle" format="reference">
    </attr>
    <attr name="actionBarTabBarStyle" format="reference">
    </attr>
    <attr name="actionBarTabStyle" format="reference">
    </attr>
    <attr name="actionBarTabTextStyle" format="reference">
    </attr>
    <attr name="actionBarTheme" format="reference">
    </attr>
    <attr name="actionBarWidgetTheme" format="reference">
    </attr>
    <attr name="actionButtonStyle" format="reference">
    </attr>
    <attr name="actionDropDownStyle" format="reference">
    </attr>
    <attr name="actionEnabled" format="boolean">
    </attr>
    <attr name="actionLayout" format="reference">
    </attr>
    <attr name="actionMenuTextAppearance" format="reference">
    </attr>
    <attr name="actionMenuTextColor" format="reference|color">
    </attr>
    <attr name="actionModeBackground" format="reference">
    </attr>
    <attr name="actionModeCloseButtonStyle" format="reference">
    </attr>
    <attr name="actionModeCloseContentDescription" format="string">
    </attr>
    <attr name="actionModeCloseDrawable" format="reference">
    </attr>
    <attr name="actionModeCopyDrawable" format="reference">
    </attr>
    <attr name="actionModeCutDrawable" format="reference">
    </attr>
    <attr name="actionModeFindDrawable" format="reference">
    </attr>
    <attr name="actionModePasteDrawable" format="reference">
    </attr>
    <attr name="actionModePopupWindowStyle" format="reference">
    </attr>
    <attr name="actionModeSelectAllDrawable" format="reference">
    </attr>
    <attr name="actionModeShareDrawable" format="reference">
    </attr>
    <attr name="actionModeSplitBackground" format="reference">
    </attr>
    <attr name="actionModeStyle" format="reference">
    </attr>
    <attr name="actionModeTheme" format="reference">
    </attr>
    <attr name="actionModeWebSearchDrawable" format="reference">
    </attr>
    <attr name="actionOverflowButtonStyle" format="reference">
    </attr>
    <attr name="actionOverflowMenuStyle" format="reference">
    </attr>
    <attr name="actionProviderClass" format="string">
    </attr>
    <attr name="actionShown" format="boolean">
    </attr>
    <attr name="actionTextColorAlpha" format="float">
    </attr>
    <attr name="actionViewClass" format="string">
    </attr>
    <attr name="activatable" format="boolean">
    </attr>
    <attr name="activated" format="boolean">
    </attr>
    <attr name="activeIndicatorLabelPadding" format="dimension">
    </attr>
    <attr name="activityChooserViewStyle" format="reference">
    </attr>
    <attr name="addElevationShadow" format="boolean">
    </attr>
    <attr name="adjustable" format="boolean">
    </attr>
    <attr name="alertDialogButtonGroupStyle" format="reference">
    </attr>
    <attr name="alertDialogCenterButtons" format="boolean">
    </attr>
    <attr name="alertDialogStyle" format="reference">
    </attr>
    <attr name="alertDialogTheme" format="reference">
    </attr>
    <attr name="allowDividerAbove" format="boolean">
    </attr>
    <attr name="allowDividerAfterLastItem" format="boolean">
    </attr>
    <attr name="allowDividerBelow" format="boolean">
    </attr>
    <attr name="allowStacking" format="boolean">
    </attr>
    <attr name="alpha" format="float">
    </attr>
    <attr name="alphabeticModifiers">
        <flag name="ALT" value="2" />
        <flag name="CTRL" value="1000" />
        <flag name="FUNCTION" value="8" />
        <flag name="META" value="10000" />
        <flag name="SHIFT" value="1" />
        <flag name="SYM" value="4" />
    </attr>
    <attr name="animateCircleAngleTo">
        <enum name="antiClockwise" value="3" />
        <enum name="bestChoice" value="0" />
        <enum name="clockwise" value="2" />
        <enum name="closest" value="1" />
        <enum name="constraint" value="4" />
    </attr>
    <attr name="animateRelativeTo" format="reference">
    </attr>
    <attr name="animationMode">
        <enum name="fade" value="1" />
        <enum name="slide" value="0" />
    </attr>
    <attr name="appBarLayoutStyle" format="reference">
    </attr>
    <attr name="arrowHeadLength" format="dimension">
    </attr>
    <attr name="arrowShaftLength" format="dimension">
    </attr>
    <attr name="attributeName" format="string">
    </attr>
    <attr name="autoAdjustToWithinGrandparentBounds" format="boolean">
    </attr>
    <attr name="autoCompleteTextViewStyle" format="reference">
    </attr>
    <attr name="autoSizeMaxTextSize" format="dimension">
    </attr>
    <attr name="autoSizeMinTextSize" format="dimension">
    </attr>
    <attr name="autoSizePresetSizes" format="reference">
    </attr>
    <attr name="autoSizeStepGranularity" format="dimension">
    </attr>
    <attr name="autoSizeTextType">
        <enum name="none" value="0" />
        <enum name="uniform" value="1" />
    </attr>
    <attr name="background" format="reference">
    </attr>
    <attr name="backgroundColor" format="color">
    </attr>
    <attr name="backgroundInsetBottom" format="dimension">
    </attr>
    <attr name="backgroundInsetEnd" format="dimension">
    </attr>
    <attr name="backgroundInsetStart" format="dimension">
    </attr>
    <attr name="backgroundInsetTop" format="dimension">
    </attr>
    <attr name="backgroundOverlayColorAlpha" format="float">
    </attr>
    <attr name="backgroundSplit" format="reference|color">
    </attr>
    <attr name="backgroundStacked" format="reference|color">
    </attr>
    <attr name="backgroundTint" format="color">
    </attr>
    <attr name="backgroundTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="background_border_cap">
        <enum name="butt" value="0" />
        <enum name="round" value="1" />
        <enum name="square" value="2" />
    </attr>
    <attr name="background_border_color" format="color">
    </attr>
    <attr name="background_border_width" format="dimension">
    </attr>
    <attr name="background_color" format="color">
    </attr>
    <attr name="background_radius" format="dimension">
    </attr>
    <attr name="background_radius_percent" format="fraction">
    </attr>
    <attr name="background_radius_pressed" format="dimension">
    </attr>
    <attr name="background_radius_pressed_percent" format="fraction">
    </attr>
    <attr name="background_shadow_width" format="dimension">
    </attr>
    <attr name="badgeGravity">
        <enum name="BOTTOM_END" value="8388693" />
        <enum name="BOTTOM_START" value="8388691" />
        <enum name="TOP_END" value="8388661" />
        <enum name="TOP_START" value="8388659" />
    </attr>
    <attr name="badgeHeight" format="dimension">
    </attr>
    <attr name="badgeShapeAppearance" format="reference">
    </attr>
    <attr name="badgeStyle" format="reference">
    </attr>
    <attr name="badgeTextAppearance" format="reference">
    </attr>
    <attr name="badgeWidePadding" format="dimension">
    </attr>
    <attr name="badgeWidth" format="dimension">
    </attr>
    <attr name="badgeWithTextHeight" format="dimension">
    </attr>
    <attr name="badgeWithTextShapeAppearance" format="reference">
    </attr>
    <attr name="badgeWithTextWidth" format="dimension">
    </attr>
    <attr name="barLength" format="dimension">
    </attr>
    <attr name="barrierAllowsGoneWidgets" format="boolean">
    </attr>
    <attr name="barrierDirection">
        <enum name="bottom" value="3" />
        <enum name="end" value="6" />
        <enum name="left" value="0" />
        <enum name="right" value="1" />
        <enum name="start" value="5" />
        <enum name="top" value="2" />
    </attr>
    <attr name="barrierMargin" format="dimension">
    </attr>
    <attr name="behavior_autoHide" format="boolean">
    </attr>
    <attr name="behavior_autoShrink" format="boolean">
    </attr>
    <attr name="behavior_draggable" format="boolean">
    </attr>
    <attr name="behavior_draggableOnNestedScroll" format="boolean">
    </attr>
    <attr name="behavior_expandedOffset" format="reference|dimension">
    </attr>
    <attr name="behavior_fitToContents" format="boolean">
    </attr>
    <attr name="behavior_halfExpandedRatio" format="reference|float">
    </attr>
    <attr name="behavior_hideable" format="boolean">
    </attr>
    <attr name="behavior_overlapTop" format="dimension">
    </attr>
    <attr name="behavior_peekHeight" format="dimension">
        <enum name="auto" value="-1" />
    </attr>
    <attr name="behavior_saveFlags">
        <flag name="all" value="-1" />
        <flag name="fitToContents" value="2" />
        <flag name="hideable" value="4" />
        <flag name="none" value="0" />
        <flag name="peekHeight" value="1" />
        <flag name="skipCollapsed" value="8" />
    </attr>
    <attr name="behavior_significantVelocityThreshold" format="dimension">
    </attr>
    <attr name="behavior_skipCollapsed" format="boolean">
    </attr>
    <attr name="bezelWidth" format="fraction">
    </attr>
    <attr name="borderWidth" format="dimension">
    </attr>
    <attr name="borderlessButtonStyle" format="reference">
    </attr>
    <attr name="bottomAppBarStyle" format="reference">
    </attr>
    <attr name="bottomBoundOffset" format="dimension">
    </attr>
    <attr name="bottomNavigationStyle" format="reference">
    </attr>
    <attr name="bottomSheetDialogTheme" format="reference">
    </attr>
    <attr name="bottomSheetStyle" format="reference">
    </attr>
    <attr name="boxBackgroundColor" format="color">
    </attr>
    <attr name="boxBackgroundMode">
        <enum name="filled" value="1" />
        <enum name="none" value="0" />
        <enum name="outline" value="2" />
    </attr>
    <attr name="boxCollapsedPaddingTop" format="dimension">
    </attr>
    <attr name="boxStrokeColor" format="color">
    </attr>
    <attr name="boxStrokeErrorColor" format="color">
    </attr>
    <attr name="boxStrokeWidth" format="dimension">
    </attr>
    <attr name="boxStrokeWidthFocused" format="dimension">
    </attr>
    <attr name="boxedEdges">
        <flag name="all" value="f" />
        <flag name="bottom" value="8" />
        <flag name="left" value="1" />
        <flag name="none" value="0" />
        <flag name="right" value="4" />
        <flag name="top" value="2" />
    </attr>
    <attr name="buttonBarButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarNegativeButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarNeutralButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarPositiveButtonStyle" format="reference">
    </attr>
    <attr name="buttonBarStyle" format="reference">
    </attr>
    <attr name="buttonCompat" format="reference">
    </attr>
    <attr name="buttonGravity">
        <flag name="bottom" value="50" />
        <flag name="center_vertical" value="10" />
        <flag name="top" value="30" />
    </attr>
    <attr name="buttonIcon" format="reference">
    </attr>
    <attr name="buttonIconDimen" format="dimension">
    </attr>
    <attr name="buttonIconTint" format="reference|color">
    </attr>
    <attr name="buttonIconTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="buttonPanelSideLayout" format="reference">
    </attr>
    <attr name="buttonStyle" format="reference">
    </attr>
    <attr name="buttonStyleSmall" format="reference">
    </attr>
    <attr name="buttonTint" format="color">
    </attr>
    <attr name="buttonTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="carUiActivity" format="boolean">
    </attr>
    <attr name="carUiArrowColor" format="reference|color">
    </attr>
    <attr name="carUiArrowGravity">
        <flag name="bottom" value="4" />
        <flag name="left" value="1" />
        <flag name="right" value="3" />
        <flag name="top" value="2" />
    </attr>
    <attr name="carUiArrowHeight" format="dimension">
    </attr>
    <attr name="carUiArrowRadius" format="dimension">
    </attr>
    <attr name="carUiArrowWidth" format="dimension">
    </attr>
    <attr name="carUiBaseLayout" format="boolean">
    </attr>
    <attr name="carUiClickableWhileDisabled" format="boolean">
    </attr>
    <attr name="carUiContentView" format="reference">
    </attr>
    <attr name="carUiContentViewDrawable" format="reference">
    </attr>
    <attr name="carUiHasArrow" format="boolean">
    </attr>
    <attr name="carUiIcon" format="reference">
    </attr>
    <attr name="carUiLayout" format="reference">
    </attr>
    <attr name="carUiOffsetX" format="dimension">
    </attr>
    <attr name="carUiOffsetY" format="dimension">
    </attr>
    <attr name="carUiPreferenceStyle" format="reference">
    </attr>
    <attr name="carUiPreferenceType">
        <enum name="category" value="11" />
        <enum name="dropdown" value="1" />
        <enum name="edittext" value="8" />
        <enum name="footer" value="10" />
        <enum name="preference" value="0" />
        <enum name="primarySwitch" value="2" />
        <enum name="seekbardialog" value="9" />
        <enum name="twoaction" value="3" />
        <enum name="twoactionicon" value="6" />
        <enum name="twoactionswitch" value="7" />
        <enum name="twoactiontext" value="4" />
        <enum name="twoactiontextborderless" value="5" />
    </attr>
    <attr name="name" value="carUiRecyclerViewStyle">0</attr>
    <attr name="carUiShowChevron" format="boolean">
    </attr>
    <attr name="carUiSize">
        <enum name="large" value="2" />
        <enum name="medium" value="1" />
        <enum name="small" value="0" />
    </attr>
    <attr name="carUiToolbar" format="boolean">
    </attr>
    <attr name="car_ui_ux_restricted" format="boolean">
    </attr>
    <attr name="cardBackgroundColor" format="color">
    </attr>
    <attr name="cardCornerRadius" format="dimension">
    </attr>
    <attr name="cardElevation" format="dimension">
    </attr>
    <attr name="cardForegroundColor" format="color">
    </attr>
    <attr name="cardMaxElevation" format="dimension">
    </attr>
    <attr name="cardPreventCornerOverlap" format="boolean">
    </attr>
    <attr name="cardUseCompatPadding" format="boolean">
    </attr>
    <attr name="centerIfNoTextEnabled" format="boolean">
    </attr>
    <attr name="chainUseRtl" format="boolean">
    </attr>
    <attr name="checkBoxPreferenceStyle" format="reference">
    </attr>
    <attr name="checkMarkCompat" format="reference">
    </attr>
    <attr name="checkMarkTint" format="color">
    </attr>
    <attr name="checkMarkTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="checkable" format="boolean">
    </attr>
    <attr name="checkboxStyle" format="reference">
    </attr>
    <attr name="checked" format="boolean">
    </attr>
    <attr name="checkedIcon" format="reference">
    </attr>
    <attr name="checkedIconMargin" format="dimension">
    </attr>
    <attr name="checkedIconSize" format="dimension">
    </attr>
    <attr name="checkedIconTint" format="color">
    </attr>
    <attr name="checkedIconVisible" format="boolean">
    </attr>
    <attr name="checkedState">
        <enum name="checked" value="1" />
        <enum name="indeterminate" value="2" />
        <enum name="unchecked" value="0" />
    </attr>
    <attr name="checkedTextViewStyle" format="reference">
    </attr>
    <attr name="chipBackgroundColor" format="color">
    </attr>
    <attr name="chipEndPadding" format="dimension">
    </attr>
    <attr name="chipGroupStyle" format="reference">
    </attr>
    <attr name="chipIcon" format="reference">
    </attr>
    <attr name="chipIconSize" format="dimension">
    </attr>
    <attr name="chipIconVisible" format="boolean">
    </attr>
    <attr name="chipMinHeight" format="dimension">
    </attr>
    <attr name="chipMinTouchTargetSize" format="dimension">
    </attr>
    <attr name="chipSpacingHorizontal" format="dimension">
    </attr>
    <attr name="chipSpacingVertical" format="dimension">
    </attr>
    <attr name="chipStandaloneStyle" format="reference">
    </attr>
    <attr name="chipStartPadding" format="dimension">
    </attr>
    <attr name="chipStrokeColor" format="color">
    </attr>
    <attr name="chipStrokeWidth" format="dimension">
    </attr>
    <attr name="chipStyle" format="reference">
    </attr>
    <attr name="chipSurfaceColor" format="color">
    </attr>
    <attr name="circularProgressIndicatorStyle" format="reference">
    </attr>
    <attr name="circularScrollingGestureEnabled" format="boolean">
    </attr>
    <attr name="circularflow_angles" format="string">
    </attr>
    <attr name="circularflow_defaultAngle" format="float">
    </attr>
    <attr name="circularflow_defaultRadius" format="dimension">
    </attr>
    <attr name="circularflow_radiusInDP" format="string">
    </attr>
    <attr name="circularflow_viewCenter" format="reference">
    </attr>
    <attr name="clip_dimen">
        <enum name="height" value="1" />
        <enum name="none" value="0" />
        <enum name="width" value="2" />
    </attr>
    <attr name="clockFaceBackgroundColor" format="color">
    </attr>
    <attr name="clockHandColor" format="color">
    </attr>
    <attr name="clockIcon" format="reference">
    </attr>
    <attr name="clockNumberTextColor" format="color">
    </attr>
    <attr name="closeIcon" format="reference">
    </attr>
    <attr name="closeIconEndPadding" format="dimension">
    </attr>
    <attr name="closeIconSize" format="dimension">
    </attr>
    <attr name="closeIconStartPadding" format="dimension">
    </attr>
    <attr name="closeIconTint" format="color">
    </attr>
    <attr name="closeIconVisible" format="boolean">
    </attr>
    <attr name="closeItemLayout" format="reference">
    </attr>
    <attr name="collapseContentDescription" format="string">
    </attr>
    <attr name="collapseIcon" format="reference">
    </attr>
    <attr name="collapsedSize" format="dimension">
    </attr>
    <attr name="collapsedTitleGravity">
        <flag name="bottom" value="50" />
        <flag name="center" value="11" />
        <flag name="center_horizontal" value="1" />
        <flag name="center_vertical" value="10" />
        <flag name="end" value="800005" />
        <flag name="fill_vertical" value="70" />
        <flag name="left" value="3" />
        <flag name="right" value="5" />
        <flag name="start" value="800003" />
        <flag name="top" value="30" />
    </attr>
    <attr name="collapsedTitleTextAppearance" format="reference">
    </attr>
    <attr name="collapsedTitleTextColor" format="reference|color">
    </attr>
    <attr name="collapsingToolbarLayoutLargeSize" format="reference">
    </attr>
    <attr name="collapsingToolbarLayoutLargeStyle" format="reference">
    </attr>
    <attr name="collapsingToolbarLayoutMediumSize" format="reference">
    </attr>
    <attr name="collapsingToolbarLayoutMediumStyle" format="reference">
    </attr>
    <attr name="collapsingToolbarLayoutStyle" format="reference">
    </attr>
    <attr name="color" format="color">
    </attr>
    <attr name="colorAccent" format="color">
    </attr>
    <attr name="colorBackgroundFloating" format="color">
    </attr>
    <attr name="colorButtonNormal" format="color">
    </attr>
    <attr name="colorControlActivated" format="color">
    </attr>
    <attr name="colorControlHighlight" format="color">
    </attr>
    <attr name="colorControlNormal" format="color">
    </attr>
    <attr name="colorError" format="reference|color">
    </attr>
    <attr name="colorErrorStateContent" format="color">
    </attr>
    <attr name="colorHairline" format="color">
    </attr>
    <attr name="colorOnBackground" format="reference|string|integer|boolean|color|float|dimension|fraction">
    </attr>
    <attr name="colorOnError" format="color">
    </attr>
    <attr name="colorOnPrimary" format="color">
    </attr>
    <attr name="colorOnPrimaryContainer" format="color">
    </attr>
    <attr name="colorOnPrimaryContainerStateContent" format="color">
    </attr>
    <attr name="colorOnPrimaryContainerStateLayer" format="color">
    </attr>
    <attr name="colorOnPrimaryGoogle" format="color">
    </attr>
    <attr name="colorOnPrimaryStateContent" format="color">
    </attr>
    <attr name="colorOnPrimaryStateLayer" format="color">
    </attr>
    <attr name="colorOnPrimaryStateLayerGoogle" format="color">
    </attr>
    <attr name="colorOnPrimarySurface" format="color">
    </attr>
    <attr name="colorOnSecondary" format="color">
    </attr>
    <attr name="colorOnSecondaryContainer" format="color">
    </attr>
    <attr name="colorOnSecondaryContainerStateContent" format="color">
    </attr>
    <attr name="colorOnSecondaryContainerStateLayer" format="color">
    </attr>
    <attr name="colorOnSecondaryStateContent" format="color">
    </attr>
    <attr name="colorOnSecondaryStateLayer" format="color">
    </attr>
    <attr name="colorOnSurface" format="color">
    </attr>
    <attr name="colorOnSurfaceInverse" format="color">
    </attr>
    <attr name="colorOnSurfaceStateContent" format="color">
    </attr>
    <attr name="colorOnSurfaceStateLayer" format="color">
    </attr>
    <attr name="colorOnSurfaceVariant" format="color">
    </attr>
    <attr name="colorOnSurfaceVariantStateContent" format="color">
    </attr>
    <attr name="colorOnSurfaceVariantStateLayer" format="color">
    </attr>
    <attr name="colorOnTertiaryContainer" format="color">
    </attr>
    <attr name="colorOnTertiaryContainerStateContent" format="color">
    </attr>
    <attr name="colorOnTertiaryContainerStateLayer" format="color">
    </attr>
    <attr name="colorOutline" format="color">
    </attr>
    <attr name="colorPrimary" format="color">
    </attr>
    <attr name="colorPrimaryContainer" format="color">
    </attr>
    <attr name="colorPrimaryDark" format="color">
    </attr>
    <attr name="colorPrimaryGoogle" format="color">
    </attr>
    <attr name="colorPrimaryGoogleInverse" format="color">
    </attr>
    <attr name="colorPrimaryInverse" format="color">
    </attr>
    <attr name="colorPrimaryStateContent" format="color">
    </attr>
    <attr name="colorPrimaryStateContentGoogle" format="color">
    </attr>
    <attr name="colorPrimaryStateContentGoogleInverse" format="color">
    </attr>
    <attr name="colorPrimaryStateContentInverse" format="color">
    </attr>
    <attr name="colorPrimaryStateLayer" format="color">
    </attr>
    <attr name="colorPrimaryStateLayerGoogleInverse" format="color">
    </attr>
    <attr name="colorPrimaryStateLayerInverse" format="color">
    </attr>
    <attr name="colorPrimarySurface" format="color">
    </attr>
    <attr name="colorPrimaryVariant" format="color">
    </attr>
    <attr name="colorPrimaryVariantGoogle" format="color">
    </attr>
    <attr name="colorSecondary" format="color">
    </attr>
    <attr name="colorSecondaryContainer" format="color">
    </attr>
    <attr name="colorSecondaryStateContent" format="color">
    </attr>
    <attr name="colorSecondaryStateLayer" format="color">
    </attr>
    <attr name="colorSecondaryVariant" format="color">
    </attr>
    <attr name="colorSurface" format="color">
    </attr>
    <attr name="colorSurfaceContainer" format="color">
    </attr>
    <attr name="colorSurfaceInverse" format="color">
    </attr>
    <attr name="colorSurfaceVariant" format="color">
    </attr>
    <attr name="colorSwitchThumbNormal" format="color">
    </attr>
    <attr name="colorTertiaryContainer" format="color">
    </attr>
    <attr name="colorTextFieldError" format="color">
    </attr>
    <attr name="colorTextFieldHairline" format="color">
    </attr>
    <attr name="colorTextFieldOnSurfaceVariant" format="color">
    </attr>
    <attr name="colorTextFieldPrimary" format="color">
    </attr>
    <attr name="colorTextFieldSurface" format="color">
    </attr>
    <attr name="colorTimePickerSurface" format="color">
    </attr>
    <attr name="commitIcon" format="reference">
    </attr>
    <attr name="compatShadowEnabled" format="boolean">
    </attr>
    <attr name="constraintRotate">
        <enum name="left" value="2" />
        <enum name="none" value="0" />
        <enum name="right" value="1" />
        <enum name="x_left" value="4" />
        <enum name="x_right" value="3" />
    </attr>
    <attr name="constraintSet" format="reference">
    </attr>
    <attr name="constraint_referenced_ids" format="string">
    </attr>
    <attr name="constraint_referenced_tags" format="string">
    </attr>
    <attr name="constraints" format="reference">
    </attr>
    <attr name="contentDescription" format="string">
    </attr>
    <attr name="contentInsetEnd" format="dimension">
    </attr>
    <attr name="contentInsetEndWithActions" format="dimension">
    </attr>
    <attr name="contentInsetLeft" format="dimension">
    </attr>
    <attr name="contentInsetRight" format="dimension">
    </attr>
    <attr name="contentInsetStart" format="dimension">
    </attr>
    <attr name="contentInsetStartWithNavigation" format="dimension">
    </attr>
    <attr name="contentMarginTop" format="dimension">
    </attr>
    <attr name="contentScrim" format="color">
    </attr>
    <attr name="controlBackground" format="reference">
    </attr>
    <attr name="coordinatorLayoutStyle" format="reference">
    </attr>
    <attr name="cornerFamily">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerFamilyBottomLeft">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerFamilyBottomRight">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerFamilyTopLeft">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerFamilyTopRight">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="cornerRadius" format="dimension">
    </attr>
    <attr name="cornerSize" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeBottomLeft" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeBottomRight" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeTopLeft" format="dimension|fraction">
    </attr>
    <attr name="cornerSizeTopRight" format="dimension|fraction">
    </attr>
    <attr name="counterOverflowTextAppearance" format="reference">
    </attr>
    <attr name="counterOverflowTextColor" format="reference">
    </attr>
    <attr name="counterTextAppearance" format="reference">
    </attr>
    <attr name="counterTextColor" format="reference">
    </attr>
    <attr name="cursorColor" format="color">
    </attr>
    <attr name="cursorErrorColor" format="color">
    </attr>
    <attr name="customBoolean" format="boolean">
    </attr>
    <attr name="customColorDrawableValue" format="color">
    </attr>
    <attr name="customColorValue" format="color">
    </attr>
    <attr name="customDimension" format="dimension">
    </attr>
    <attr name="customFloatValue" format="float">
    </attr>
    <attr name="customIntegerValue" format="integer">
    </attr>
    <attr name="customNavigationLayout" format="reference">
    </attr>
    <attr name="customPixelDimension" format="dimension">
    </attr>
    <attr name="customReference" format="reference">
    </attr>
    <attr name="customStringValue" format="string">
    </attr>
    <attr name="dayInvalidStyle" format="reference">
    </attr>
    <attr name="daySelectedStyle" format="reference">
    </attr>
    <attr name="dayStyle" format="reference">
    </attr>
    <attr name="dayTodayStyle" format="reference">
    </attr>
    <attr name="defaultFocus" format="reference">
    </attr>
    <attr name="defaultFocusOverridesHistory" format="boolean">
    </attr>
    <attr name="defaultMarginsEnabled" format="boolean">
    </attr>
    <attr name="defaultQueryHint" format="string">
    </attr>
    <attr name="defaultScrollFlagsEnabled" format="boolean">
    </attr>
    <attr name="defaultValue" format="reference|string|integer|boolean|float">
    </attr>
    <attr name="dependency" format="string">
    </attr>
    <attr name="deriveConstraintsFrom" format="reference">
    </attr>
    <attr name="dialogCornerRadius" format="dimension">
    </attr>
    <attr name="dialogIcon" format="reference">
    </attr>
    <attr name="dialogLayout" format="reference">
    </attr>
    <attr name="dialogMessage" format="string">
    </attr>
    <attr name="dialogPreferenceStyle" format="reference">
    </attr>
    <attr name="dialogPreferredPadding" format="dimension">
    </attr>
    <attr name="dialogTheme" format="reference">
    </attr>
    <attr name="dialogTitle" format="string">
    </attr>
    <attr name="disableDependentsState" format="boolean">
    </attr>
    <attr name="displayBehavior">
        <enum name="always" value="0" />
        <enum name="never" value="1" />
    </attr>
    <attr name="displayOptions">
        <flag name="disableHome" value="20" />
        <flag name="homeAsUp" value="4" />
        <flag name="none" value="0" />
        <flag name="showCustom" value="10" />
        <flag name="showHome" value="2" />
        <flag name="showTitle" value="8" />
        <flag name="useLogo" value="1" />
    </attr>
    <attr name="divider" format="reference">
    </attr>
    <attr name="dividerHorizontal" format="reference">
    </attr>
    <attr name="dividerPadding" format="dimension">
    </attr>
    <attr name="dividerVertical" format="reference">
    </attr>
    <attr name="drawPath">
        <enum name="asConfigured" value="4" />
        <enum name="deltaRelative" value="3" />
        <enum name="none" value="0" />
        <enum name="path" value="1" />
        <enum name="pathRelative" value="2" />
        <enum name="rectangles" value="5" />
    </attr>
    <attr name="drawableBottomCompat" format="reference">
    </attr>
    <attr name="drawableEndCompat" format="reference">
    </attr>
    <attr name="drawableLeftCompat" format="reference">
    </attr>
    <attr name="drawableRightCompat" format="reference">
    </attr>
    <attr name="drawableSize" format="dimension">
    </attr>
    <attr name="drawableStartCompat" format="reference">
    </attr>
    <attr name="drawableTint" format="color">
    </attr>
    <attr name="drawableTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="drawableTopCompat" format="reference">
    </attr>
    <attr name="drawerArrowStyle" format="reference">
    </attr>
    <attr name="drawerLayoutCornerSize" format="dimension">
    </attr>
    <attr name="dropDownBackgroundTint" format="color">
    </attr>
    <attr name="dropDownListViewStyle" format="reference">
    </attr>
    <attr name="dropdownListPreferredItemHeight" format="dimension">
    </attr>
    <attr name="dropdownPreferenceStyle" format="reference">
    </attr>
    <attr name="editTextBackground" format="reference">
    </attr>
    <attr name="editTextColor" format="reference|color">
    </attr>
    <attr name="editTextPreferenceStyle" format="reference">
    </attr>
    <attr name="editTextStyle" format="reference">
    </attr>
    <attr name="elevation" format="dimension">
    </attr>
    <attr name="elevationOverlayAccentColor" format="color">
    </attr>
    <attr name="elevationOverlayColor" format="color">
    </attr>
    <attr name="elevationOverlayEnabled" format="boolean">
    </attr>
    <attr name="emojiCompatEnabled" format="boolean">
    </attr>
    <attr name="enableCopying" format="boolean">
    </attr>
    <attr name="enableDivider" format="boolean">
    </attr>
    <attr name="enableEdgeToEdge" format="boolean">
    </attr>
    <attr name="enabled" format="boolean">
    </attr>
    <attr name="endBoundOffset" format="dimension">
    </attr>
    <attr name="endIconMode">
        <enum name="clear_text" value="2" />
        <enum name="custom" value="-1" />
        <enum name="dropdown_menu" value="3" />
        <enum name="none" value="0" />
        <enum name="password_toggle" value="1" />
    </attr>
    <attr name="endIconTint" format="color">
    </attr>
    <attr name="enforceMaterialTheme" format="boolean">
    </attr>
    <attr name="enforceTextAppearance" format="boolean">
    </attr>
    <attr name="ensureMinTouchTargetSize" format="boolean">
    </attr>
    <attr name="entries" format="reference">
    </attr>
    <attr name="entryValues" format="reference">
    </attr>
    <attr name="errorAccessibilityLabel" format="string">
    </attr>
    <attr name="errorIconDrawable" format="reference">
    </attr>
    <attr name="errorIconTint" format="reference">
    </attr>
    <attr name="errorShown" format="boolean">
    </attr>
    <attr name="errorTextAppearance" format="reference">
    </attr>
    <attr name="errorTextColor" format="color">
    </attr>
    <attr name="expanded" format="boolean">
    </attr>
    <attr name="expandedTitleGravity">
        <flag name="bottom" value="50" />
        <flag name="center" value="11" />
        <flag name="center_horizontal" value="1" />
        <flag name="center_vertical" value="10" />
        <flag name="end" value="800005" />
        <flag name="fill_vertical" value="70" />
        <flag name="left" value="3" />
        <flag name="right" value="5" />
        <flag name="start" value="800003" />
        <flag name="top" value="30" />
    </attr>
    <attr name="expandedTitleMargin" format="dimension">
    </attr>
    <attr name="expandedTitleMarginBottom" format="dimension">
    </attr>
    <attr name="expandedTitleMarginEnd" format="dimension">
    </attr>
    <attr name="expandedTitleMarginStart" format="dimension">
    </attr>
    <attr name="expandedTitleMarginTop" format="dimension">
    </attr>
    <attr name="expandedTitleTextAppearance" format="reference">
    </attr>
    <attr name="expandedTitleTextColor" format="reference|color">
    </attr>
    <attr name="extendStrategy">
        <enum name="auto" value="0" />
        <enum name="match_parent" value="2" />
        <enum name="wrap_content" value="1" />
    </attr>
    <attr name="extendedFloatingActionButtonBrandedStyle" format="reference">
    </attr>
    <attr name="extendedFloatingActionButtonPrimaryStyle" format="reference">
    </attr>
    <attr name="extendedFloatingActionButtonStyle" format="reference">
    </attr>
    <attr name="extraMultilineHeightEnabled" format="boolean">
    </attr>
    <attr name="fabAlignmentMode">
        <enum name="center" value="0" />
        <enum name="end" value="1" />
    </attr>
    <attr name="fabAnchorMode">
        <enum name="cradle" value="1" />
        <enum name="embed" value="0" />
    </attr>
    <attr name="fabAnimationMode">
        <enum name="scale" value="0" />
        <enum name="slide" value="1" />
    </attr>
    <attr name="fabCradleMargin" format="dimension">
    </attr>
    <attr name="fabCradleRoundedCornerRadius" format="dimension">
    </attr>
    <attr name="fabCradleVerticalOffset" format="dimension">
    </attr>
    <attr name="fabCustomSize" format="dimension">
    </attr>
    <attr name="fabSize">
        <enum name="auto" value="-1" />
        <enum name="mini" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="fastScrollEnabled" format="boolean">
    </attr>
    <attr name="fastScrollHorizontalThumbDrawable" format="reference">
    </attr>
    <attr name="fastScrollHorizontalTrackDrawable" format="reference">
    </attr>
    <attr name="fastScrollVerticalThumbDrawable" format="reference">
    </attr>
    <attr name="fastScrollVerticalTrackDrawable" format="reference">
    </attr>
    <attr name="firstBaselineToTopHeight" format="dimension">
    </attr>
    <attr name="floatingActionButtonBrandedStyle" format="reference">
    </attr>
    <attr name="floatingActionButtonPrimaryStyle" format="reference">
    </attr>
    <attr name="floatingActionButtonSecondaryStyle" format="reference">
    </attr>
    <attr name="floatingActionButtonStyle" format="reference">
    </attr>
    <attr name="floatingActionButtonSurfaceStyle" format="reference">
    </attr>
    <attr name="flow_firstHorizontalBias" format="float">
    </attr>
    <attr name="flow_firstHorizontalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_firstVerticalBias" format="float">
    </attr>
    <attr name="flow_firstVerticalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_horizontalAlign">
        <enum name="center" value="2" />
        <enum name="end" value="1" />
        <enum name="start" value="0" />
    </attr>
    <attr name="flow_horizontalBias" format="float">
    </attr>
    <attr name="flow_horizontalGap" format="dimension">
    </attr>
    <attr name="flow_horizontalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_lastHorizontalBias" format="float">
    </attr>
    <attr name="flow_lastHorizontalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_lastVerticalBias" format="float">
    </attr>
    <attr name="flow_lastVerticalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_maxElementsWrap" format="integer">
    </attr>
    <attr name="flow_verticalAlign">
        <enum name="baseline" value="3" />
        <enum name="bottom" value="1" />
        <enum name="center" value="2" />
        <enum name="top" value="0" />
    </attr>
    <attr name="flow_verticalBias" format="float">
    </attr>
    <attr name="flow_verticalGap" format="dimension">
    </attr>
    <attr name="flow_verticalStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="flow_wrapMode">
        <enum name="aligned" value="2" />
        <enum name="chain" value="1" />
        <enum name="chain2" value="3" />
        <enum name="none" value="0" />
    </attr>
    <attr name="font" format="reference">
    </attr>
    <attr name="fontFamily" format="string">
    </attr>
    <attr name="fontProviderAuthority" format="string">
    </attr>
    <attr name="fontProviderCerts" format="reference">
    </attr>
    <attr name="fontProviderFallbackQuery" format="string">
    </attr>
    <attr name="fontProviderFetchStrategy">
        <enum name="async" value="1" />
        <enum name="blocking" value="0" />
    </attr>
    <attr name="fontProviderFetchTimeout" format="integer">
        <enum name="forever" value="-1" />
    </attr>
    <attr name="fontProviderPackage" format="string">
    </attr>
    <attr name="fontProviderQuery" format="string">
    </attr>
    <attr name="fontProviderSystemFontFamily" format="string">
    </attr>
    <attr name="fontStyle">
        <enum name="italic" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="fontVariationSettings" format="string">
    </attr>
    <attr name="fontWeight" format="integer">
    </attr>
    <attr name="footerPreferenceStyle" format="reference">
    </attr>
    <attr name="forceApplySystemWindowInsetTop" format="boolean">
    </attr>
    <attr name="foregroundInsidePadding" format="boolean">
    </attr>
    <attr name="fragment" format="string">
    </attr>
    <attr name="gapBetweenBars" format="dimension">
    </attr>
    <attr name="gestureInsetBottomIgnored" format="boolean">
    </attr>
    <attr name="goIcon" format="reference">
    </attr>
    <attr name="guidelineUseRtl" format="boolean">
    </attr>
    <attr name="haloColor" format="color">
    </attr>
    <attr name="haloRadius" format="dimension">
    </attr>
    <attr name="headerMarginBottom" format="dimension">
    </attr>
    <attr name="height" format="dimension">
    </attr>
    <attr name="helperTextTextAppearance" format="reference">
    </attr>
    <attr name="helperTextTextColor" format="color">
    </attr>
    <attr name="hideMotionSpec" format="reference">
    </attr>
    <attr name="hideNavigationIcon" format="boolean">
    </attr>
    <attr name="hideOnContentScroll" format="boolean">
    </attr>
    <attr name="highlightPaddingBottom" format="dimension">
    </attr>
    <attr name="highlightPaddingEnd" format="dimension">
    </attr>
    <attr name="highlightPaddingHorizontal" format="dimension">
    </attr>
    <attr name="highlightPaddingStart" format="dimension">
    </attr>
    <attr name="highlightPaddingTop" format="dimension">
    </attr>
    <attr name="highlightPaddingVertical" format="dimension">
    </attr>
    <attr name="hintTextAppearance" format="reference">
    </attr>
    <attr name="hintTextColor" format="color">
    </attr>
    <attr name="homeAsUpIndicator" format="reference">
    </attr>
    <attr name="homeLayout" format="reference">
    </attr>
    <attr name="horizontalBoundOffset" format="dimension">
    </attr>
    <attr name="hoveredFocusedTranslationZ" format="dimension">
    </attr>
    <attr name="icon" format="reference">
    </attr>
    <attr name="iconEndPadding" format="dimension">
    </attr>
    <attr name="iconGravity">
        <flag name="end" value="3" />
        <flag name="start" value="1" />
        <flag name="textEnd" value="4" />
        <flag name="textStart" value="2" />
        <flag name="textTop" value="20" />
        <flag name="top" value="10" />
    </attr>
    <attr name="iconPadding" format="dimension">
    </attr>
    <attr name="iconSize" format="dimension">
    </attr>
    <attr name="iconSpaceReserved" format="boolean">
    </attr>
    <attr name="iconStartPadding" format="dimension">
    </attr>
    <attr name="iconTint" format="color">
    </attr>
    <attr name="iconTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="id" format="reference">
    </attr>
    <attr name="imageButtonStyle" format="reference">
    </attr>
    <attr name="img_circle_percentage" format="dimension">
    </attr>
    <attr name="img_horizontal_offset_percentage" format="dimension">
    </attr>
    <attr name="img_padding" format="dimension">
    </attr>
    <attr name="img_tint" format="color">
    </attr>
    <attr name="indeterminateAnimationType">
        <enum name="contiguous" value="0" />
        <enum name="disjoint" value="1" />
    </attr>
    <attr name="indeterminateProgressStyle" format="reference">
    </attr>
    <attr name="indeterminateTrackVisible" format="boolean">
    </attr>
    <attr name="indicatorColor" format="reference|color">
    </attr>
    <attr name="indicatorDirectionCircular">
        <enum name="clockwise" value="0" />
        <enum name="counterclockwise" value="1" />
    </attr>
    <attr name="indicatorDirectionLinear">
        <enum name="endToStart" value="3" />
        <enum name="leftToRight" value="0" />
        <enum name="rightToLeft" value="1" />
        <enum name="startToEnd" value="2" />
    </attr>
    <attr name="indicatorInset" format="dimension">
    </attr>
    <attr name="indicatorSize" format="dimension">
    </attr>
    <attr name="initialExpandedChildrenCount" format="integer">
    </attr>
    <attr name="insetForeground" format="reference|color">
    </attr>
    <attr name="isLightTheme" format="boolean">
    </attr>
    <attr name="isMaterial3Theme" format="boolean">
    </attr>
    <attr name="isMaterialTheme" format="boolean">
    </attr>
    <attr name="isPreferenceVisible" format="boolean">
    </attr>
    <attr name="itemActiveIndicatorStyle" format="reference">
    </attr>
    <attr name="itemBackground" format="reference">
    </attr>
    <attr name="itemFillColor" format="color">
    </attr>
    <attr name="itemHorizontalPadding" format="dimension">
    </attr>
    <attr name="itemHorizontalTranslationEnabled" format="boolean">
    </attr>
    <attr name="itemIconGravity">
        <enum name="start" value="1" />
        <enum name="top" value="0" />
    </attr>
    <attr name="itemIconPadding" format="dimension">
    </attr>
    <attr name="itemIconSize" format="dimension">
    </attr>
    <attr name="itemIconTint" format="color">
    </attr>
    <attr name="itemMinHeight" format="dimension">
    </attr>
    <attr name="itemPadding" format="dimension">
    </attr>
    <attr name="itemPaddingBottom" format="dimension">
    </attr>
    <attr name="itemPaddingTop" format="dimension">
    </attr>
    <attr name="itemRippleColor" format="color">
    </attr>
    <attr name="itemShapeAppearance" format="reference">
    </attr>
    <attr name="itemShapeAppearanceOverlay" format="reference">
    </attr>
    <attr name="itemShapeFillColor" format="color">
    </attr>
    <attr name="itemShapeInsetBottom" format="dimension">
    </attr>
    <attr name="itemShapeInsetEnd" format="dimension">
    </attr>
    <attr name="itemShapeInsetStart" format="dimension">
    </attr>
    <attr name="itemShapeInsetTop" format="dimension">
    </attr>
    <attr name="itemStrokeColor" format="color">
    </attr>
    <attr name="itemStrokeWidth" format="dimension">
    </attr>
    <attr name="itemTextAppearance" format="reference">
    </attr>
    <attr name="itemTextAppearanceActive" format="reference">
    </attr>
    <attr name="itemTextAppearanceInactive" format="reference">
    </attr>
    <attr name="itemTextColor" format="color">
    </attr>
    <attr name="key" format="string">
    </attr>
    <attr name="keyboardIcon" format="reference">
    </attr>
    <attr name="keylines" format="reference">
    </attr>
    <attr name="lStar" format="float">
    </attr>
    <attr name="labelStyle" format="reference">
    </attr>
    <attr name="labelVisibilityMode">
        <enum name="auto" value="-1" />
        <enum name="labeled" value="1" />
        <enum name="selected" value="0" />
        <enum name="unlabeled" value="2" />
    </attr>
    <attr name="lastBaselineToBottomHeight" format="dimension">
    </attr>
    <attr name="layout" format="reference">
    </attr>
    <attr name="layoutDescription" format="reference">
    </attr>
    <attr name="layoutManager" format="string">
    </attr>
    <attr name="layoutStyle">
        <enum name="grid" value="1" />
        <enum name="linear" value="0" />
    </attr>
    <attr name="layout_anchor" format="reference">
    </attr>
    <attr name="layout_anchorGravity">
        <flag name="bottom" value="50" />
        <flag name="center" value="11" />
        <flag name="center_horizontal" value="1" />
        <flag name="center_vertical" value="10" />
        <flag name="clip_horizontal" value="8" />
        <flag name="clip_vertical" value="80" />
        <flag name="end" value="800005" />
        <flag name="fill" value="77" />
        <flag name="fill_horizontal" value="7" />
        <flag name="fill_vertical" value="70" />
        <flag name="left" value="3" />
        <flag name="right" value="5" />
        <flag name="start" value="800003" />
        <flag name="top" value="30" />
    </attr>
    <attr name="layout_behavior" format="string">
    </attr>
    <attr name="layout_boxedEdges">
        <flag name="all" value="f" />
        <flag name="bottom" value="8" />
        <flag name="left" value="1" />
        <flag name="none" value="0" />
        <flag name="right" value="4" />
        <flag name="top" value="2" />
    </attr>
    <attr name="layout_collapseMode">
        <enum name="none" value="0" />
        <enum name="parallax" value="2" />
        <enum name="pin" value="1" />
    </attr>
    <attr name="layout_collapseParallaxMultiplier" format="float">
    </attr>
    <attr name="layout_constrainedHeight" format="boolean">
    </attr>
    <attr name="layout_constrainedWidth" format="boolean">
    </attr>
    <attr name="layout_constraintBaseline_creator" format="integer">
    </attr>
    <attr name="layout_constraintBaseline_toBaselineOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBaseline_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBaseline_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_creator" format="integer">
    </attr>
    <attr name="layout_constraintBottom_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintBottom_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintCircle" format="reference">
    </attr>
    <attr name="layout_constraintCircleAngle" format="float">
    </attr>
    <attr name="layout_constraintCircleRadius" format="dimension">
    </attr>
    <attr name="layout_constraintDimensionRatio" format="string">
    </attr>
    <attr name="layout_constraintEnd_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintEnd_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintGuide_begin" format="dimension">
    </attr>
    <attr name="layout_constraintGuide_end" format="dimension">
    </attr>
    <attr name="layout_constraintGuide_percent" format="float">
    </attr>
    <attr name="layout_constraintHeight" format="string|dimension">
        <enum name="match_constraint" value="-3" />
        <enum name="match_parent" value="-1" />
        <enum name="wrap_content" value="-2" />
        <enum name="wrap_content_constrained" value="-4" />
    </attr>
    <attr name="layout_constraintHeight_default">
        <enum name="percent" value="2" />
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
    </attr>
    <attr name="layout_constraintHeight_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintHeight_percent" format="float">
    </attr>
    <attr name="layout_constraintHorizontal_bias" format="float">
    </attr>
    <attr name="layout_constraintHorizontal_chainStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="layout_constraintHorizontal_weight" format="float">
    </attr>
    <attr name="layout_constraintLeft_creator" format="integer">
    </attr>
    <attr name="layout_constraintLeft_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintLeft_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_creator" format="integer">
    </attr>
    <attr name="layout_constraintRight_toLeftOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintRight_toRightOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toEndOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintStart_toStartOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTag" format="string">
    </attr>
    <attr name="layout_constraintTop_creator" format="integer">
    </attr>
    <attr name="layout_constraintTop_toBottomOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintTop_toTopOf" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="layout_constraintVertical_bias" format="float">
    </attr>
    <attr name="layout_constraintVertical_chainStyle">
        <enum name="packed" value="2" />
        <enum name="spread" value="0" />
        <enum name="spread_inside" value="1" />
    </attr>
    <attr name="layout_constraintVertical_weight" format="float">
    </attr>
    <attr name="layout_constraintWidth" format="string|dimension">
        <enum name="match_constraint" value="-3" />
        <enum name="match_parent" value="-1" />
        <enum name="wrap_content" value="-2" />
        <enum name="wrap_content_constrained" value="-4" />
    </attr>
    <attr name="layout_constraintWidth_default">
        <enum name="percent" value="2" />
        <enum name="spread" value="0" />
        <enum name="wrap" value="1" />
    </attr>
    <attr name="layout_constraintWidth_max" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_min" format="dimension">
        <enum name="wrap" value="-2" />
    </attr>
    <attr name="layout_constraintWidth_percent" format="float">
    </attr>
    <attr name="layout_dodgeInsetEdges">
        <flag name="all" value="77" />
        <flag name="bottom" value="50" />
        <flag name="end" value="800005" />
        <flag name="left" value="3" />
        <flag name="none" value="0" />
        <flag name="right" value="5" />
        <flag name="start" value="800003" />
        <flag name="top" value="30" />
    </attr>
    <attr name="layout_editor_absoluteX" format="dimension">
    </attr>
    <attr name="layout_editor_absoluteY" format="dimension">
    </attr>
    <attr name="layout_goneMarginBaseline" format="dimension">
    </attr>
    <attr name="layout_goneMarginBottom" format="dimension">
    </attr>
    <attr name="layout_goneMarginEnd" format="dimension">
    </attr>
    <attr name="layout_goneMarginLeft" format="dimension">
    </attr>
    <attr name="layout_goneMarginRight" format="dimension">
    </attr>
    <attr name="layout_goneMarginStart" format="dimension">
    </attr>
    <attr name="layout_goneMarginTop" format="dimension">
    </attr>
    <attr name="layout_insetEdge">
        <enum name="bottom" value="50" />
        <enum name="end" value="800005" />
        <enum name="left" value="3" />
        <enum name="none" value="0" />
        <enum name="right" value="5" />
        <enum name="start" value="800003" />
        <enum name="top" value="30" />
    </attr>
    <attr name="layout_keyline" format="integer">
    </attr>
    <attr name="layout_marginBaseline" format="dimension">
    </attr>
    <attr name="layout_optimizationLevel">
        <flag name="barrier" value="2" />
        <flag name="cache_measures" value="256" />
        <flag name="chains" value="4" />
        <flag name="dependency_ordering" value="512" />
        <flag name="dimensions" value="8" />
        <flag name="direct" value="1" />
        <flag name="graph" value="64" />
        <flag name="graph_wrap" value="128" />
        <flag name="grouping" value="1024" />
        <flag name="groups" value="32" />
        <flag name="legacy" value="0" />
        <flag name="none" value="0" />
        <flag name="ratio" value="16" />
        <flag name="standard" value="257" />
    </attr>
    <attr name="layout_scrollEffect">
        <enum name="compress" value="1" />
        <enum name="none" value="0" />
    </attr>
    <attr name="layout_scrollFlags">
        <flag name="enterAlways" value="4" />
        <flag name="enterAlwaysCollapsed" value="8" />
        <flag name="exitUntilCollapsed" value="2" />
        <flag name="noScroll" value="0" />
        <flag name="scroll" value="1" />
        <flag name="snap" value="10" />
        <flag name="snapMargins" value="20" />
    </attr>
    <attr name="layout_scrollInterpolator" format="reference">
    </attr>
    <attr name="layout_wrapBehaviorInParent">
        <enum name="horizontal_only" value="1" />
        <enum name="included" value="0" />
        <enum name="skipped" value="3" />
        <enum name="vertical_only" value="2" />
    </attr>
    <attr name="liftOnScroll" format="boolean">
    </attr>
    <attr name="liftOnScrollColor" format="color">
    </attr>
    <attr name="liftOnScrollTargetViewId" format="reference">
    </attr>
    <attr name="lineHeight" format="dimension">
    </attr>
    <attr name="linearProgressIndicatorStyle" format="reference">
    </attr>
    <attr name="listChoiceBackgroundIndicator" format="reference">
    </attr>
    <attr name="listChoiceIndicatorMultipleAnimated" format="reference">
    </attr>
    <attr name="listChoiceIndicatorSingleAnimated" format="reference">
    </attr>
    <attr name="listDividerAlertDialog" format="reference">
    </attr>
    <attr name="listItemLayout" format="reference">
    </attr>
    <attr name="listLayout" format="reference">
    </attr>
    <attr name="listMenuViewStyle" format="reference">
    </attr>
    <attr name="listPopupWindowStyle" format="reference">
    </attr>
    <attr name="listPreferredItemHeight" format="dimension">
    </attr>
    <attr name="listPreferredItemHeightLarge" format="dimension">
    </attr>
    <attr name="listPreferredItemHeightSmall" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingEnd" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingLeft" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingRight" format="dimension">
    </attr>
    <attr name="listPreferredItemPaddingStart" format="dimension">
    </attr>
    <attr name="lockupSizingMode">
        <enum name="auto" value="0" />
        <enum name="custom" value="-1" />
        <enum name="normal" value="1" />
        <enum name="small" value="2" />
    </attr>
    <attr name="logo" format="reference">
    </attr>
    <attr name="logoAdjustViewBounds" format="boolean">
    </attr>
    <attr name="logoColor">
        <enum name="dark" value="1" />
        <enum name="grey" value="3" />
        <enum name="light" value="2" />
        <enum name="standard" value="0" />
    </attr>
    <attr name="logoDescription" format="string">
    </attr>
    <attr name="logoScaleType">
        <enum name="center" value="5" />
        <enum name="centerCrop" value="6" />
        <enum name="centerInside" value="7" />
        <enum name="fitCenter" value="3" />
        <enum name="fitEnd" value="4" />
        <enum name="fitStart" value="2" />
        <enum name="fitXY" value="1" />
        <enum name="matrix" value="0" />
    </attr>
    <attr name="marginLeftSystemWindowInsets" format="boolean">
    </attr>
    <attr name="marginRightSystemWindowInsets" format="boolean">
    </attr>
    <attr name="marginTopSystemWindowInsets" format="boolean">
    </attr>
    <attr name="materialAlertDialogBodyTextStyle" format="reference">
    </attr>
    <attr name="materialAlertDialogButtonSpacerVisibility" format="integer">
    </attr>
    <attr name="materialAlertDialogTheme" format="reference">
    </attr>
    <attr name="materialAlertDialogTitleIconStyle" format="reference">
    </attr>
    <attr name="materialAlertDialogTitlePanelStyle" format="reference">
    </attr>
    <attr name="materialAlertDialogTitleTextStyle" format="reference">
    </attr>
    <attr name="materialButtonOutlinedStyle" format="reference">
    </attr>
    <attr name="materialButtonStyle" format="reference">
    </attr>
    <attr name="materialButtonToggleGroupStyle" format="reference">
    </attr>
    <attr name="materialCalendarDay" format="reference">
    </attr>
    <attr name="materialCalendarDayOfWeekLabel" format="reference">
    </attr>
    <attr name="materialCalendarFullscreenTheme" format="reference">
    </attr>
    <attr name="materialCalendarHeaderCancelButton" format="reference">
    </attr>
    <attr name="materialCalendarHeaderConfirmButton" format="reference">
    </attr>
    <attr name="materialCalendarHeaderDivider" format="reference">
    </attr>
    <attr name="materialCalendarHeaderLayout" format="reference">
    </attr>
    <attr name="materialCalendarHeaderSelection" format="reference">
    </attr>
    <attr name="materialCalendarHeaderTitle" format="reference">
    </attr>
    <attr name="materialCalendarHeaderToggleButton" format="reference">
    </attr>
    <attr name="materialCalendarMonth" format="reference">
    </attr>
    <attr name="materialCalendarMonthNavigationButton" format="reference">
    </attr>
    <attr name="materialCalendarStyle" format="reference">
    </attr>
    <attr name="materialCalendarTheme" format="reference">
    </attr>
    <attr name="materialCalendarYearNavigationButton" format="reference">
    </attr>
    <attr name="materialCardViewElevatedStyle" format="reference">
    </attr>
    <attr name="materialCardViewOutlinedStyle" format="reference">
    </attr>
    <attr name="materialCardViewStyle" format="reference">
    </attr>
    <attr name="materialClockStyle" format="reference">
    </attr>
    <attr name="materialDisplayDividerStyle" format="reference">
    </attr>
    <attr name="materialSearchBarStyle" format="reference">
    </attr>
    <attr name="materialSearchViewPrefixStyle" format="reference">
    </attr>
    <attr name="materialSearchViewStyle" format="reference">
    </attr>
    <attr name="materialSearchViewToolbarHeight" format="dimension">
    </attr>
    <attr name="materialSearchViewToolbarStyle" format="reference">
    </attr>
    <attr name="materialThemeOverlay" format="reference">
    </attr>
    <attr name="materialTimePickerStyle" format="reference">
    </attr>
    <attr name="materialTimePickerTheme" format="reference">
    </attr>
    <attr name="materialTimePickerTitleStyle" format="reference">
    </attr>
    <attr name="maxActionInlineWidth" format="dimension">
    </attr>
    <attr name="maxButtonHeight" format="dimension">
    </attr>
    <attr name="maxHeight" format="dimension">
    </attr>
    <attr name="maxImageSize" format="dimension">
    </attr>
    <attr name="maxLines" format="integer">
    </attr>
    <attr name="maxNumber" format="integer">
    </attr>
    <attr name="maxWidth" format="dimension">
    </attr>
    <attr name="measureBottomPaddingFromLabelBaseline" format="boolean">
    </attr>
    <attr name="measureWithLargestChild" format="boolean">
    </attr>
    <attr name="menu" format="reference">
    </attr>
    <attr name="menuAlignmentMode">
        <enum name="auto" value="0" />
        <enum name="start" value="1" />
    </attr>
    <attr name="menuGravity">
        <enum name="bottom" value="81" />
        <enum name="center" value="17" />
        <enum name="top" value="49" />
    </attr>
    <attr name="menuItems" format="reference">
    </attr>
    <attr name="methodName" format="string">
    </attr>
    <attr name="min" format="integer">
    </attr>
    <attr name="minHeight" format="dimension">
    </attr>
    <attr name="minSeparation" format="dimension">
    </attr>
    <attr name="minTouchTargetSize" format="dimension">
    </attr>
    <attr name="minWidth" format="dimension">
    </attr>
    <attr name="motionDurationLong1" format="integer">
    </attr>
    <attr name="motionDurationLong2" format="integer">
    </attr>
    <attr name="motionDurationMedium1" format="integer">
    </attr>
    <attr name="motionDurationMedium2" format="integer">
    </attr>
    <attr name="motionDurationMedium4" format="integer">
    </attr>
    <attr name="motionDurationShort1" format="integer">
    </attr>
    <attr name="motionDurationShort2" format="integer">
    </attr>
    <attr name="motionDurationShort3" format="integer">
    </attr>
    <attr name="motionEasingAccelerated" format="string">
    </attr>
    <attr name="motionEasingDecelerated" format="string">
    </attr>
    <attr name="motionEasingEmphasized" format="string">
    </attr>
    <attr name="motionEasingEmphasizedAccelerateInterpolator" format="reference">
    </attr>
    <attr name="motionEasingEmphasizedDecelerateInterpolator" format="reference">
    </attr>
    <attr name="motionEasingEmphasizedInterpolator" format="reference">
    </attr>
    <attr name="motionEasingLinear" format="string">
    </attr>
    <attr name="motionEasingLinearInterpolator" format="reference">
    </attr>
    <attr name="motionEasingStandard" format="string">
    </attr>
    <attr name="motionEasingStandardInterpolator" format="reference">
    </attr>
    <attr name="motionPath" format="string">
        <enum name="arc" value="1" />
        <enum name="linear" value="0" />
    </attr>
    <attr name="motionPathRotate" format="float">
    </attr>
    <attr name="motionProgress" format="float">
    </attr>
    <attr name="motionStagger" format="float">
    </attr>
    <attr name="motionTarget" format="reference|string">
    </attr>
    <attr name="multiChoiceItemLayout" format="reference">
    </attr>
    <attr name="navigationContentDescription" format="string">
    </attr>
    <attr name="navigationIcon" format="reference">
    </attr>
    <attr name="navigationIconTint" format="color">
    </attr>
    <attr name="navigationMode">
        <enum name="listMode" value="1" />
        <enum name="normal" value="0" />
        <enum name="tabMode" value="2" />
    </attr>
    <attr name="navigationRailStyle" format="reference">
    </attr>
    <attr name="navigationViewStyle" format="reference">
    </attr>
    <attr name="negativeButtonText" format="string">
    </attr>
    <attr name="nestedScrollViewStyle" format="reference">
    </attr>
    <attr name="nudgeDown" format="reference">
    </attr>
    <attr name="nudgeDownDisabled" format="boolean">
    </attr>
    <attr name="nudgeDownShortcut" format="reference">
    </attr>
    <attr name="nudgeLeft" format="reference">
    </attr>
    <attr name="nudgeLeftDisabled" format="boolean">
    </attr>
    <attr name="nudgeLeftShortcut" format="reference">
    </attr>
    <attr name="nudgeRight" format="reference">
    </attr>
    <attr name="nudgeRightDisabled" format="boolean">
    </attr>
    <attr name="nudgeRightShortcut" format="reference">
    </attr>
    <attr name="nudgeShortcut" format="reference">
    </attr>
    <attr name="nudgeShortcutDirection">
        <flag name="down" value="82" />
        <flag name="left" value="11" />
        <flag name="right" value="42" />
        <flag name="up" value="21" />
    </attr>
    <attr name="nudgeUp" format="reference">
    </attr>
    <attr name="nudgeUpDisabled" format="boolean">
    </attr>
    <attr name="nudgeUpShortcut" format="reference">
    </attr>
    <attr name="numOfColumns" format="integer">
    </attr>
    <attr name="numericModifiers">
        <flag name="ALT" value="2" />
        <flag name="CTRL" value="1000" />
        <flag name="FUNCTION" value="8" />
        <flag name="META" value="10000" />
        <flag name="SHIFT" value="1" />
        <flag name="SYM" value="4" />
    </attr>
    <attr name="offsetAlignmentMode">
        <enum name="edge" value="0" />
        <enum name="legacy" value="1" />
    </attr>
    <attr name="onClick" format="string">
    </attr>
    <attr name="openSearchBarStyle" format="reference">
    </attr>
    <attr name="openSearchViewPrefixStyle" format="reference">
    </attr>
    <attr name="openSearchViewStyle" format="reference">
    </attr>
    <attr name="openSearchViewToolbarHeight" format="reference">
    </attr>
    <attr name="openSearchViewToolbarStyle" format="reference">
    </attr>
    <attr name="order" format="integer">
    </attr>
    <attr name="orderingFromXml" format="boolean">
    </attr>
    <attr name="overlapAnchor" format="boolean">
    </attr>
    <attr name="paddingBottomNoButtons" format="dimension">
    </attr>
    <attr name="paddingBottomSystemWindowInsets" format="boolean">
    </attr>
    <attr name="paddingEnd" format="dimension">
    </attr>
    <attr name="paddingLeftSystemWindowInsets" format="boolean">
    </attr>
    <attr name="paddingRightSystemWindowInsets" format="boolean">
    </attr>
    <attr name="paddingStart" format="dimension">
    </attr>
    <attr name="paddingTopNoTitle" format="dimension">
    </attr>
    <attr name="paddingTopSystemWindowInsets" format="boolean">
    </attr>
    <attr name="panelBackground" format="reference">
    </attr>
    <attr name="panelMenuListTheme" format="reference">
    </attr>
    <attr name="panelMenuListWidth" format="dimension">
    </attr>
    <attr name="passwordToggleContentDescription" format="string">
    </attr>
    <attr name="passwordToggleDrawable" format="reference">
    </attr>
    <attr name="passwordToggleTint" format="color">
    </attr>
    <attr name="pathMotionArc">
        <enum name="flip" value="3" />
        <enum name="none" value="0" />
        <enum name="startHorizontal" value="2" />
        <enum name="startVertical" value="1" />
    </attr>
    <attr name="persistent" format="boolean">
    </attr>
    <attr name="pivotAnchor" format="reference">
        <enum name="parent" value="0" />
    </attr>
    <attr name="placeholderTextAppearance" format="reference">
    </attr>
    <attr name="placeholderTextColor" format="color">
    </attr>
    <attr name="polarRelativeTo" format="reference">
    </attr>
    <attr name="popupMenuBackground" format="reference">
    </attr>
    <attr name="popupMenuStyle" format="reference">
    </attr>
    <attr name="popupTheme" format="reference">
    </attr>
    <attr name="popupWindowStyle" format="reference">
    </attr>
    <attr name="positiveButtonText" format="string">
    </attr>
    <attr name="preferenceCategoryStyle" format="reference">
    </attr>
    <attr name="preferenceCategoryTitleTextAppearance" format="reference">
    </attr>
    <attr name="preferenceCategoryTitleTextColor" format="reference|color">
    </attr>
    <attr name="preferenceFragmentCompatStyle" format="reference">
    </attr>
    <attr name="preferenceFragmentListStyle" format="reference">
    </attr>
    <attr name="preferenceFragmentStyle" format="reference">
    </attr>
    <attr name="preferenceScreenStyle" format="reference">
    </attr>
    <attr name="preferenceStyle" format="reference">
    </attr>
    <attr name="preferenceTheme" format="reference">
    </attr>
    <attr name="prefixTextAppearance" format="reference">
    </attr>
    <attr name="prefixTextColor" format="color">
    </attr>
    <attr name="preserveIconSpacing" format="boolean">
    </attr>
    <attr name="pressedTranslationZ" format="dimension">
    </attr>
    <attr name="productLockupViewStyle" format="reference">
    </attr>
    <attr name="productNameTextColor" format="color">
    </attr>
    <attr name="progressBarPadding" format="dimension">
    </attr>
    <attr name="progressBarStyle" format="reference">
    </attr>
    <attr name="quantizeMotionInterpolator" format="reference|string">
        <enum name="bounce" value="4" />
        <enum name="easeIn" value="1" />
        <enum name="easeInOut" value="0" />
        <enum name="easeOut" value="2" />
        <enum name="linear" value="3" />
        <enum name="overshoot" value="5" />
    </attr>
    <attr name="quantizeMotionPhase" format="float">
    </attr>
    <attr name="quantizeMotionSteps" format="integer">
    </attr>
    <attr name="queryBackground" format="reference">
    </attr>
    <attr name="radioButtonStyle" format="reference">
    </attr>
    <attr name="rangeFillColor" format="color">
    </attr>
    <attr name="ratingBarStyle" format="reference">
    </attr>
    <attr name="ratingBarStyleIndicator" format="reference">
    </attr>
    <attr name="ratingBarStyleSmall" format="reference">
    </attr>
    <attr name="recyclerViewStyle" format="reference">
    </attr>
    <attr name="region_heightLessThan" format="dimension">
    </attr>
    <attr name="region_heightMoreThan" format="dimension">
    </attr>
    <attr name="region_widthLessThan" format="dimension">
    </attr>
    <attr name="region_widthMoreThan" format="dimension">
    </attr>
    <attr name="reverseLayout" format="boolean">
    </attr>
    <attr name="rippleColor" format="color">
    </attr>
    <attr name="rotaryScrollEnabled" format="boolean">
    </attr>
    <attr name="scrimAnimationDuration" format="integer">
    </attr>
    <attr name="scrimBackground" format="reference|color">
    </attr>
    <attr name="scrimVisibleHeightTrigger" format="dimension">
    </attr>
    <attr name="scrollDegreesPerScreen" format="float">
    </attr>
    <attr name="search" format="boolean">
    </attr>
    <attr name="searchHint" format="string">
    </attr>
    <attr name="searchHintIcon" format="reference">
    </attr>
    <attr name="searchIcon" format="reference">
    </attr>
    <attr name="searchViewStyle" format="reference">
    </attr>
    <attr name="secondaryActionIcon" format="reference">
    </attr>
    <attr name="secondaryActionStyle">
        <enum name="bordered" value="0" />
        <enum name="borderless" value="1" />
    </attr>
    <attr name="secondaryActionText" format="string">
    </attr>
    <attr name="seekBarIncrement" format="integer">
    </attr>
    <attr name="seekBarPreferenceStyle" format="reference">
    </attr>
    <attr name="seekBarStyle" format="reference">
    </attr>
    <attr name="selectable" format="boolean">
    </attr>
    <attr name="selectableItemBackground" format="reference">
    </attr>
    <attr name="selectableItemBackgroundBorderless" format="reference">
    </attr>
    <attr name="settings" format="boolean">
    </attr>
    <attr name="shapeAppearance" format="reference">
    </attr>
    <attr name="shapeAppearanceLargeComponent" format="reference">
    </attr>
    <attr name="shapeAppearanceMediumComponent" format="reference">
    </attr>
    <attr name="shapeAppearanceOverlay" format="reference">
    </attr>
    <attr name="shapeAppearanceSmallComponent" format="reference">
    </attr>
    <attr name="shapeCornerFamily">
        <enum name="cut" value="1" />
        <enum name="rounded" value="0" />
    </attr>
    <attr name="shouldDisableView" format="boolean">
    </attr>
    <attr name="shouldRemoveExpandedCorners" format="boolean">
    </attr>
    <attr name="shouldRestoreFocus" format="boolean">
    </attr>
    <attr name="showAsAction">
        <flag name="always" value="2" />
        <flag name="collapseActionView" value="8" />
        <flag name="ifRoom" value="1" />
        <flag name="never" value="0" />
        <flag name="withText" value="4" />
    </attr>
    <attr name="showBackground" format="boolean">
    </attr>
    <attr name="showDividers">
        <flag name="beginning" value="1" />
        <flag name="end" value="4" />
        <flag name="middle" value="2" />
        <flag name="none" value="0" />
    </attr>
    <attr name="showIconAndTitle" format="boolean">
    </attr>
    <attr name="showMenuItemsWhileSearching" format="boolean">
    </attr>
    <attr name="showMotionSpec" format="reference">
    </attr>
    <attr name="showSeekBarValue" format="boolean">
    </attr>
    <attr name="showTabsInSubpage" format="boolean">
    </attr>
    <attr name="showText" format="boolean">
    </attr>
    <attr name="showTitle" format="boolean">
    </attr>
    <attr name="simpleItemLayout" format="reference">
    </attr>
    <attr name="simpleItemSelectedColor" format="color">
    </attr>
    <attr name="simpleItemSelectedRippleColor" format="color">
    </attr>
    <attr name="simpleItems" format="reference">
    </attr>
    <attr name="singleChoiceItemLayout" format="reference">
    </attr>
    <attr name="singleLine" format="boolean">
    </attr>
    <attr name="singleLineTitle" format="boolean">
    </attr>
    <attr name="singleSelection" format="boolean">
    </attr>
    <attr name="sliderStyle" format="reference">
    </attr>
    <attr name="snackbarButtonStyle" format="reference">
    </attr>
    <attr name="snackbarStyle" format="reference">
    </attr>
    <attr name="snackbarTextViewStyle" format="reference">
    </attr>
    <attr name="spanCount" format="integer">
    </attr>
    <attr name="spinBars" format="boolean">
    </attr>
    <attr name="spinnerDropDownItemStyle" format="reference">
    </attr>
    <attr name="spinnerStyle" format="reference">
    </attr>
    <attr name="splitTrack" format="boolean">
    </attr>
    <attr name="srcCompat" format="reference">
    </attr>
    <attr name="stackFromEnd" format="boolean">
    </attr>
    <attr name="startBoundOffset" format="dimension">
    </attr>
    <attr name="startIconTint" format="color">
    </attr>
    <attr name="stateLabels" format="string">
    </attr>
    <attr name="state_collapsed" format="boolean">
    </attr>
    <attr name="state_collapsible" format="boolean">
    </attr>
    <attr name="state_dragged" format="boolean">
    </attr>
    <attr name="state_error" format="boolean">
    </attr>
    <attr name="state_indeterminate" format="boolean">
    </attr>
    <attr name="state_liftable" format="boolean">
    </attr>
    <attr name="state_lifted" format="boolean">
    </attr>
    <attr name="state_ux_restricted" format="boolean">
    </attr>
    <attr name="statusBarBackground" format="reference|color">
    </attr>
    <attr name="statusBarForeground" format="color">
    </attr>
    <attr name="statusBarScrim" format="color">
    </attr>
    <attr name="strokeColor" format="color">
    </attr>
    <attr name="strokeWidth" format="dimension">
    </attr>
    <attr name="subMenuArrow" format="reference">
    </attr>
    <attr name="subheaderInsetEnd" format="dimension">
    </attr>
    <attr name="subheaderInsetStart" format="dimension">
    </attr>
    <attr name="submitBackground" format="reference">
    </attr>
    <attr name="subtitle" format="string">
    </attr>
    <attr name="subtitleCentered" format="boolean">
    </attr>
    <attr name="subtitleTextAppearance" format="reference">
    </attr>
    <attr name="subtitleTextColor" format="color">
    </attr>
    <attr name="subtitleTextStyle" format="reference">
    </attr>
    <attr name="suffixTextAppearance" format="reference">
    </attr>
    <attr name="suffixTextColor" format="color">
    </attr>
    <attr name="suggestionRowLayout" format="reference">
    </attr>
    <attr name="summary" format="string">
    </attr>
    <attr name="summaryOff" format="string">
    </attr>
    <attr name="summaryOn" format="string">
    </attr>
    <attr name="switchMinWidth" format="dimension">
    </attr>
    <attr name="switchPadding" format="dimension">
    </attr>
    <attr name="switchPreferenceCompatStyle" format="reference">
    </attr>
    <attr name="switchPreferenceStyle" format="reference">
    </attr>
    <attr name="switchStyle" format="reference">
    </attr>
    <attr name="switchTextAppearance" format="reference">
    </attr>
    <attr name="switchTextOff" format="string">
    </attr>
    <attr name="switchTextOn" format="string">
    </attr>
    <attr name="tabGravity">
        <enum name="center" value="1" />
        <enum name="fill" value="0" />
        <enum name="start" value="2" />
    </attr>
    <attr name="tabIconTint" format="color">
    </attr>
    <attr name="tabIndicator" format="reference">
    </attr>
    <attr name="tabIndicatorAnimationDuration" format="integer">
    </attr>
    <attr name="tabIndicatorAnimationMode">
        <enum name="elastic" value="1" />
        <enum name="fade" value="2" />
        <enum name="linear" value="0" />
    </attr>
    <attr name="tabIndicatorColor" format="color">
    </attr>
    <attr name="tabIndicatorFullWidth" format="boolean">
    </attr>
    <attr name="tabIndicatorGravity">
        <enum name="bottom" value="0" />
        <enum name="center" value="1" />
        <enum name="stretch" value="3" />
        <enum name="top" value="2" />
    </attr>
    <attr name="tabMaxWidth" format="dimension">
    </attr>
    <attr name="tabMode">
        <enum name="auto" value="2" />
        <enum name="fixed" value="1" />
        <enum name="scrollable" value="0" />
    </attr>
    <attr name="tabPaddingEnd" format="dimension">
    </attr>
    <attr name="tabPaddingStart" format="dimension">
    </attr>
    <attr name="tabRippleColor" format="color">
    </attr>
    <attr name="tabSecondaryStyle" format="reference">
    </attr>
    <attr name="tabStyle" format="reference">
    </attr>
    <attr name="tabTextAppearance" format="reference">
    </attr>
    <attr name="tabTextColor" format="color">
    </attr>
    <attr name="tabUnboundedRipple" format="boolean">
    </attr>
    <attr name="textAllCaps" format="reference|boolean">
    </attr>
    <attr name="textAppearanceBody1" format="reference">
    </attr>
    <attr name="textAppearanceBody2" format="reference">
    </attr>
    <attr name="textAppearanceButton" format="reference">
    </attr>
    <attr name="textAppearanceCaption" format="reference">
    </attr>
    <attr name="textAppearanceDisplay1" format="reference">
    </attr>
    <attr name="textAppearanceDisplay2" format="reference">
    </attr>
    <attr name="textAppearanceDisplay3" format="reference">
    </attr>
    <attr name="textAppearanceHeadline1" format="reference">
    </attr>
    <attr name="textAppearanceHeadline2" format="reference">
    </attr>
    <attr name="textAppearanceHeadline3" format="reference">
    </attr>
    <attr name="textAppearanceHeadline4" format="reference">
    </attr>
    <attr name="textAppearanceHeadline5" format="reference">
    </attr>
    <attr name="textAppearanceHeadline6" format="reference">
    </attr>
    <attr name="textAppearanceHeadlineSmall" format="reference">
    </attr>
    <attr name="textAppearanceLargePopupMenu" format="reference">
    </attr>
    <attr name="textAppearanceLineHeightEnabled" format="boolean">
    </attr>
    <attr name="textAppearanceListItem" format="reference">
    </attr>
    <attr name="textAppearanceListItemSecondary" format="reference">
    </attr>
    <attr name="textAppearanceListItemSmall" format="reference">
    </attr>
    <attr name="textAppearanceOverline" format="reference">
    </attr>
    <attr name="textAppearancePopupMenuHeader" format="reference">
    </attr>
    <attr name="textAppearanceSearchResultSubtitle" format="reference">
    </attr>
    <attr name="textAppearanceSearchResultTitle" format="reference">
    </attr>
    <attr name="textAppearanceSmallPopupMenu" format="reference">
    </attr>
    <attr name="textAppearanceSubhead1" format="reference">
    </attr>
    <attr name="textAppearanceSubhead2" format="reference">
    </attr>
    <attr name="textAppearanceSubtitle1" format="reference">
    </attr>
    <attr name="textAppearanceSubtitle2" format="reference">
    </attr>
    <attr name="textAppearanceTitleLarge" format="reference">
    </attr>
    <attr name="textColorAlertDialogListItem" format="reference|color">
    </attr>
    <attr name="textColorSearchUrl" format="reference|color">
    </attr>
    <attr name="textEndPadding" format="dimension">
    </attr>
    <attr name="textInputFilledDenseStyle" format="reference">
    </attr>
    <attr name="textInputFilledExposedDropdownMenuStyle" format="reference">
    </attr>
    <attr name="textInputFilledStyle" format="reference">
    </attr>
    <attr name="textInputLayoutFocusedRectEnabled" format="boolean">
    </attr>
    <attr name="textInputOutlinedDenseStyle" format="reference">
    </attr>
    <attr name="textInputOutlinedExposedDropdownMenuStyle" format="reference">
    </attr>
    <attr name="textInputOutlinedStyle" format="reference">
    </attr>
    <attr name="textInputStyle" format="reference">
    </attr>
    <attr name="textLocale" format="string">
    </attr>
    <attr name="textStartPadding" format="dimension">
    </attr>
    <attr name="theme" format="reference">
    </attr>
    <attr name="themeFontFamily" format="reference|string">
    </attr>
    <attr name="themeFontFamilyContent" format="reference|string">
    </attr>
    <attr name="themeFontFamilyContentMedium" format="reference|string">
    </attr>
    <attr name="themeFontFamilyDisplay" format="reference|string">
    </attr>
    <attr name="themeFontFamilyMedium" format="reference|string">
    </attr>
    <attr name="thickness" format="dimension">
    </attr>
    <attr name="thumbColor" format="color">
    </attr>
    <attr name="thumbElevation" format="dimension">
    </attr>
    <attr name="thumbRadius" format="dimension">
    </attr>
    <attr name="thumbTextPadding" format="dimension">
    </attr>
    <attr name="thumbTint" format="color">
    </attr>
    <attr name="thumbTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tickColorActive" format="color">
    </attr>
    <attr name="tickColorInactive" format="color">
    </attr>
    <attr name="tickMark" format="reference">
    </attr>
    <attr name="tickMarkTint" format="color">
    </attr>
    <attr name="tickMarkTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tickRadiusActive" format="dimension">
    </attr>
    <attr name="tickRadiusInactive" format="dimension">
    </attr>
    <attr name="tint" format="color">
    </attr>
    <attr name="tintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="tinted" format="boolean">
    </attr>
    <attr name="title" format="string">
    </attr>
    <attr name="titleCentered" format="boolean">
    </attr>
    <attr name="titleCollapseMode">
        <enum name="fade" value="1" />
        <enum name="scale" value="0" />
    </attr>
    <attr name="titleEnabled" format="boolean">
    </attr>
    <attr name="titleMargin" format="dimension">
    </attr>
    <attr name="titleMarginBottom" format="dimension">
    </attr>
    <attr name="titleMarginEnd" format="dimension">
    </attr>
    <attr name="titleMarginStart" format="dimension">
    </attr>
    <attr name="titleMarginTop" format="dimension">
    </attr>
    <attr name="titleMargins" format="dimension">
    </attr>
    <attr name="titlePositionInterpolator" format="reference">
    </attr>
    <attr name="titleTextAppearance" format="reference">
    </attr>
    <attr name="titleTextColor" format="color">
    </attr>
    <attr name="titleTextEllipsize">
        <enum name="end" value="2" />
        <enum name="marquee" value="3" />
        <enum name="middle" value="1" />
        <enum name="start" value="0" />
    </attr>
    <attr name="titleTextStyle" format="reference">
    </attr>
    <attr name="toggleCheckedStateOnClick" format="boolean">
    </attr>
    <attr name="toolbarId" format="reference">
    </attr>
    <attr name="toolbarNavigationButtonStyle" format="reference">
    </attr>
    <attr name="toolbarStyle" format="reference">
    </attr>
    <attr name="toolbarSurfaceStyle" format="reference">
    </attr>
    <attr name="tooltipForegroundColor" format="reference|color">
    </attr>
    <attr name="tooltipFrameBackground" format="reference">
    </attr>
    <attr name="tooltipText" format="string">
    </attr>
    <attr name="topBoundOffset" format="dimension">
    </attr>
    <attr name="track" format="reference">
    </attr>
    <attr name="trackColor" format="color">
    </attr>
    <attr name="trackColorActive" format="color">
    </attr>
    <attr name="trackColorInactive" format="color">
    </attr>
    <attr name="trackHeight" format="dimension">
    </attr>
    <attr name="trackThickness" format="dimension">
    </attr>
    <attr name="trackTint" format="color">
    </attr>
    <attr name="trackTintMode">
        <enum name="add" value="16" />
        <enum name="multiply" value="14" />
        <enum name="screen" value="15" />
        <enum name="src_atop" value="9" />
        <enum name="src_in" value="5" />
        <enum name="src_over" value="3" />
    </attr>
    <attr name="transcription_levelBaseDimen" format="reference|dimension">
    </attr>
    <attr name="transcription_levelColors" format="reference|color">
    </attr>
    <attr name="transformPivotTarget" format="reference">
    </attr>
    <attr name="transitionEasing" format="string">
        <enum name="accelerate" value="1" />
        <enum name="decelerate" value="2" />
        <enum name="linear" value="3" />
        <enum name="standard" value="0" />
    </attr>
    <attr name="transitionPathRotate" format="float">
    </attr>
    <attr name="ttcIndex" format="integer">
    </attr>
    <attr name="updatesContinuously" format="boolean">
    </attr>
    <attr name="useCompatPadding" format="boolean">
    </attr>
    <attr name="useMaterialThemeColors" format="boolean">
    </attr>
    <attr name="useSimpleSummaryProvider" format="boolean">
    </attr>
    <attr name="uxRestrictions">
        <flag name="UX_RESTRICTIONS_FULLY_RESTRICTED" value="511" />
        <flag name="UX_RESTRICTIONS_LIMIT_CONTENT" value="32" />
        <flag name="UX_RESTRICTIONS_LIMIT_STRING_LENGTH" value="4" />
        <flag name="UX_RESTRICTIONS_NO_DIALPAD" value="1" />
        <flag name="UX_RESTRICTIONS_NO_FILTERING" value="2" />
        <flag name="UX_RESTRICTIONS_NO_KEYBOARD" value="8" />
        <flag name="UX_RESTRICTIONS_NO_SETUP" value="64" />
        <flag name="UX_RESTRICTIONS_NO_TEXT_MESSAGE" value="128" />
        <flag name="UX_RESTRICTIONS_NO_VIDEO" value="16" />
        <flag name="UX_RESTRICTIONS_NO_VOICE_TRANSCRIPTION" value="256" />
    </attr>
    <attr name="verticalBoundOffset" format="dimension">
    </attr>
    <attr name="viewInflaterClass" format="string">
    </attr>
    <attr name="visibilityMode">
        <enum name="ignore" value="1" />
        <enum name="normal" value="0" />
    </attr>
    <attr name="visible" format="boolean">
    </attr>
    <attr name="voiceIcon" format="reference">
    </attr>
    <attr name="widgetLayout" format="reference">
    </attr>
    <attr name="windowActionBar" format="boolean">
    </attr>
    <attr name="windowActionBarOverlay" format="boolean">
    </attr>
    <attr name="windowActionModeOverlay" format="boolean">
    </attr>
    <attr name="windowFixedHeightMajor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedHeightMinor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedWidthMajor" format="dimension|fraction">
    </attr>
    <attr name="windowFixedWidthMinor" format="dimension|fraction">
    </attr>
    <attr name="windowMinWidthMajor" format="dimension|fraction">
    </attr>
    <attr name="windowMinWidthMinor" format="dimension|fraction">
    </attr>
    <attr name="windowNoTitle" format="boolean">
    </attr>
    <attr name="wrapAround" format="boolean">
    </attr>
    <attr name="wsPageIndicatorDotColor" format="color">
    </attr>
    <attr name="wsPageIndicatorDotColorSelected" format="color">
    </attr>
    <attr name="wsPageIndicatorDotFadeInDuration" format="integer">
    </attr>
    <attr name="wsPageIndicatorDotFadeOutDelay" format="integer">
    </attr>
    <attr name="wsPageIndicatorDotFadeOutDuration" format="integer">
    </attr>
    <attr name="wsPageIndicatorDotFadeWhenIdle" format="boolean">
    </attr>
    <attr name="wsPageIndicatorDotRadius" format="dimension">
    </attr>
    <attr name="wsPageIndicatorDotRadiusSelected" format="dimension">
    </attr>
    <attr name="wsPageIndicatorDotShadowColor" format="color">
    </attr>
    <attr name="wsPageIndicatorDotShadowDx" format="dimension">
    </attr>
    <attr name="wsPageIndicatorDotShadowDy" format="dimension">
    </attr>
    <attr name="wsPageIndicatorDotShadowRadius" format="dimension">
    </attr>
    <attr name="wsPageIndicatorDotSpacing" format="dimension">
    </attr>
    <attr name="yearSelectedStyle" format="reference">
    </attr>
    <attr name="yearStyle" format="reference">
    </attr>
    <attr name="yearTodayStyle" format="reference">
    </attr>
</resources>
