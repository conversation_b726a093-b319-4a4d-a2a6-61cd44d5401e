package defpackage;

/* renamed from: epf  reason: default package */
/* compiled from: PG */
public final class epf {
    public final int a;
    public final epg b;
    public final dyt c;

    public epf(int i, epg epg, dyt dyt) {
        jnu.e(dyt, "sourceParams");
        this.a = i;
        this.b = epg;
        this.c = dyt;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof epf)) {
            return false;
        }
        epf epf = (epf) obj;
        if (this.a == epf.a && jnu.i(this.b, epf.b) && jnu.i(this.c, epf.c)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        int hashCode = (this.a * 31) + this.b.hashCode();
        dyt dyt = this.c;
        if (dyt.B()) {
            i = dyt.i();
        } else {
            int i2 = dyt.memoizedHashCode;
            if (i2 == 0) {
                i2 = dyt.i();
                dyt.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (hashCode * 31) + i;
    }

    public final String toString() {
        return "PreStart(routeToken=" + this.a + ", session=" + this.b + ", sourceParams=" + this.c + ")";
    }
}
