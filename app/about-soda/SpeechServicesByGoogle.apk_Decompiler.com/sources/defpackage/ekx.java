package defpackage;

import android.content.Context;
import com.google.android.libraries.speech.modelmanager.languagepack.ui.grpc.DownloadActivityStarterService;
import j$.util.Optional;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/* renamed from: ekx  reason: default package */
/* compiled from: PG */
public final class ekx implements iiu {
    private final jjk a;
    private final /* synthetic */ int b;

    public ekx(jjk jjk, int i) {
        this.b = i;
        this.a = jjk;
    }

    public final /* synthetic */ Object b() {
        switch (this.b) {
            case 0:
                return new eoz((cqx) this.a.b());
            case 1:
                return new dku(this.a, (byte[]) null, (byte[]) null);
            case 2:
                return new dku(this.a);
            case 3:
                return new dlv(this.a, (byte[]) null);
            case 4:
                jqs jqs = (jqs) this.a.b();
                jnu.e(jqs, "scope");
                return don.k(jqs);
            case 5:
                iow iow = (iow) this.a.b();
                jnu.e(iow, "channel");
                return (esu) esu.c(new est(0), iow);
            case 6:
                iow iow2 = (iow) this.a.b();
                jnu.e(iow2, "channel");
                return (esw) esw.b(new est(1), iow2);
            case 7:
                return new fbi((cqx) this.a.b());
            case 8:
                Boolean a2 = ((ijj) this.a).a();
                a2.booleanValue();
                return a2;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                return new bmu((ScheduledExecutorService) this.a.b(), (frd) new ewm());
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return new byw((Object) (dwj) this.a.b(), (byte[]) null);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                return new bzj((Object) (dwj) this.a.b(), (byte[]) null);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                Context a3 = ((iim) this.a).a();
                ipx ipx = new ipx(ito.c(a3, DownloadActivityStarterService.class), a3, new ftc((byte[]) null));
                ipx.f(5, TimeUnit.SECONDS);
                return ipx.a();
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return (fbf) fbf.c(new est(2), (iow) this.a.b());
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return new ixj(((fbu) this.a).b());
            case 15:
                return new feb((dwj) this.a.b());
            case 16:
                return new ffv(((fhc) this.a).b());
            case 17:
                fek fek = new fek();
                bmu bmu = (bmu) ((bzj) ((iiv) this.a).a).a;
                return new cnl(((iim) bmu.c).a(), (Optional) ((iiv) bmu.b).a, (Optional) ((iiv) bmu.a).a, fek);
            case 18:
                hau hau = hau.a;
                hzz.u(hau);
                return new fij((fia) this.a.b(), hau);
            case 19:
                return new fik(((iim) this.a).a());
            default:
                return new fje(((iim) this.a).a());
        }
    }
}
