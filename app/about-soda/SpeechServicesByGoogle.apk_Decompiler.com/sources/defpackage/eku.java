package defpackage;

import android.media.AudioTimestamp;
import android.view.View;
import com.android.car.ui.FocusArea;
import java.lang.ref.WeakReference;

/* renamed from: eku  reason: default package */
/* compiled from: PG */
public final class eku {
    public final long a;
    public final Object b;

    public eku(long j, AudioTimestamp audioTimestamp) {
        this.a = j;
        this.b = audioTimestamp;
    }

    public static eku a(long j, String str) {
        dct.f(str);
        dds.o("compressedSize", j);
        return new eku(j, str);
    }

    public static eku b(ded ded) {
        return a(ded.c(), ded.k());
    }

    public static eku c() {
        return a(0, (String) null);
    }

    private eku(long j, String str) {
        this.a = j;
        this.b = str;
    }

    public eku(FocusArea focusArea, long j) {
        this.b = new WeakReference(focusArea);
        this.a = j;
    }

    public eku(View view, long j) {
        this.b = new WeakReference(view);
        this.a = j;
    }
}
