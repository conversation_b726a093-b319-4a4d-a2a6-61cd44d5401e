package defpackage;

import androidx.wear.ambient.WearableControllerProvider;

/* renamed from: bbw  reason: default package */
/* compiled from: PG */
public final class bbw {
    private final long a;
    private final long b;

    public bbw(long j, long j2) {
        this.a = j;
        this.b = j2;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj != null && jnu.i(getClass(), obj.getClass())) {
            bbw bbw = (bbw) obj;
            if (bbw.a == this.a && bbw.b == this.b) {
                return true;
            }
            return false;
        }
        return false;
    }

    public final int hashCode() {
        return (WearableControllerProvider.b(this.a) * 31) + WearableControllerProvider.b(this.b);
    }

    public final String toString() {
        return "PeriodicityInfo{repeatIntervalMillis=" + this.a + ", flexIntervalMillis=" + this.b + '}';
    }
}
