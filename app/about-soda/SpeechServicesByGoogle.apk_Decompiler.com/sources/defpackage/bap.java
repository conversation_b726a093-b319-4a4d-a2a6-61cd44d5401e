package defpackage;

import android.net.Uri;

/* renamed from: bap  reason: default package */
/* compiled from: PG */
public final class bap {
    public final Uri a;
    public final boolean b;

    public bap(Uri uri, boolean z) {
        jnu.e(uri, "uri");
        this.a = uri;
        this.b = z;
    }

    public final boolean equals(Object obj) {
        Class<?> cls;
        if (this == obj) {
            return true;
        }
        Class<?> cls2 = getClass();
        if (obj != null) {
            cls = obj.getClass();
        } else {
            cls = null;
        }
        if (!jnu.i(cls2, cls)) {
            return false;
        }
        jnu.c(obj, "null cannot be cast to non-null type androidx.work.Constraints.ContentUriTrigger");
        bap bap = (bap) obj;
        if (jnu.i(this.a, bap.a) && this.b == bap.b) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (this.a.hashCode() * 31) + a.f(this.b);
    }
}
