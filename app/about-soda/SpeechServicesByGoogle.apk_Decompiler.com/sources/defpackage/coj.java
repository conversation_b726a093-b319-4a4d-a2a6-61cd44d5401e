package defpackage;

import java.util.List;
import java.util.concurrent.ExecutorService;

/* renamed from: coj  reason: default package */
/* compiled from: PG */
public final class coj extends cop {
    final /* synthetic */ hmi a;
    final /* synthetic */ jjk b;

    public coj(hmi hmi, jjk jjk) {
        this.a = hmi;
        this.b = jjk;
    }

    public final /* synthetic */ Object a() {
        return this.a;
    }

    public final void execute(Runnable runnable) {
        this.a.execute(new cok(runnable, this.b));
    }

    public final /* synthetic */ hmh f() {
        return this.a;
    }

    public final /* synthetic */ ExecutorService g() {
        return this.a;
    }

    public final hmi h() {
        return this.a;
    }

    public final void shutdown() {
        throw new UnsupportedOperationException();
    }

    public final List shutdownNow() {
        throw new UnsupportedOperationException();
    }

    public final String toString() {
        String cop = super.toString();
        return "ExceptionHandling[" + cop + "]";
    }
}
