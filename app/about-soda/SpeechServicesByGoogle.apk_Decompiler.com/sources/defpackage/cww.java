package defpackage;

/* renamed from: cww  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cww implements hko {
    public final /* synthetic */ long a;
    public final /* synthetic */ ctj b;
    public final /* synthetic */ dbw c;

    public /* synthetic */ cww(dbw dbw, long j, ctj ctj) {
        this.c = dbw;
        this.a = j;
        this.b = ctj;
    }

    /* JADX WARNING: type inference failed for: r1v1, types: [java.lang.Object, cxa] */
    public final hme a(Object obj) {
        ctl ctl = (ctl) obj;
        long j = ctl.e;
        long j2 = this.a;
        if (j2 <= j) {
            return hfc.K(true);
        }
        htk htk = (htk) ctl.C(5);
        htk.x(ctl);
        if (!htk.b.B()) {
            htk.u();
        }
        ctj ctj = this.b;
        dbw dbw = this.c;
        ctl ctl2 = (ctl) htk.b;
        ctl2.a |= 8;
        ctl2.e = j2;
        return dbw.j.h(ctj, (ctl) htk.r());
    }
}
