package defpackage;

/* renamed from: csh  reason: default package */
/* compiled from: PG */
public enum csh {
    UNSPECIFIED(0),
    SUCCESS(1),
    UNKNOWN_ERROR(2),
    ANDROID_DOWNLOADER_UNKNOWN(100),
    ANDROID_DOWNLOADER_CANCELED(101),
    ANDROID_DOWNLOADER_INVALID_REQUEST(102),
    ANDROID_DOWNLOADER_HTTP_ERROR(103),
    ANDROID_DOWNLOADER_REQUEST_ERROR(104),
    ANDROID_DOWNLOADER_RESPONSE_OPEN_ERROR(105),
    ANDROID_DOWNLOADER_RESPONSE_CLOSE_ERROR(106),
    ANDROID_DOWNLOADER_NETWORK_IO_ERROR(107),
    ANDROID_DOWNLOADER_DISK_IO_ERROR(108),
    ANDROID_DOWNLOADER_FILE_SYSTEM_ERROR(109),
    ANDROID_DOWNLOADER_UNKNOWN_IO_ERROR(110),
    ANDROID_DOWNLOADER_OAUTH_ERROR(111),
    ANDROID_DOWNLOADER2_ERROR(200),
    GROUP_NOT_FOUND_ERROR(300),
    DOWNLOAD_MONITOR_NOT_PROVIDED_ERROR(301),
    INSECURE_URL_ERROR(302),
    LOW_DISK_ERROR(303),
    UNABLE_TO_CREATE_FILE_URI_ERROR(304),
    SHARED_FILE_NOT_FOUND_ERROR(305),
    MALFORMED_FILE_URI_ERROR(306),
    UNABLE_TO_CREATE_MOBSTORE_RESPONSE_WRITER_ERROR(307),
    UNABLE_TO_VALIDATE_DOWNLOAD_FILE_ERROR(308),
    DOWNLOADED_FILE_NOT_FOUND_ERROR(309),
    DOWNLOADED_FILE_CHECKSUM_MISMATCH_ERROR(310),
    CUSTOM_FILEGROUP_VALIDATION_FAILED(330),
    UNABLE_TO_SERIALIZE_DOWNLOAD_TRANSFORM_ERROR(311),
    DOWNLOAD_TRANSFORM_IO_ERROR(312),
    FINAL_FILE_CHECKSUM_MISMATCH_ERROR(313),
    DELTA_DOWNLOAD_BASE_FILE_NOT_FOUND_ERROR(314),
    DELTA_DOWNLOAD_DECODE_IO_ERROR(315),
    UNABLE_TO_UPDATE_FILE_STATE_ERROR(316),
    UNABLE_TO_UPDATE_GROUP_METADATA_ERROR(317),
    UNABLE_TO_UPDATE_FILE_MAX_EXPIRATION_DATE(318),
    UNABLE_SHARE_FILE_BEFORE_DOWNLOAD_ERROR(319),
    UNABLE_SHARE_FILE_AFTER_DOWNLOAD_ERROR(320),
    UNABLE_TO_REMOVE_SYMLINK_STRUCTURE(321),
    UNABLE_TO_CREATE_SYMLINK_STRUCTURE(322),
    UNABLE_TO_RESERVE_FILE_ENTRY(323),
    INVALID_INLINE_FILE_URL_SCHEME(324),
    INLINE_FILE_IO_ERROR(327),
    MISSING_INLINE_DOWNLOAD_PARAMS(328),
    MISSING_INLINE_FILE_SOURCE(329),
    MALFORMED_DOWNLOAD_URL(325),
    UNSUPPORTED_DOWNLOAD_URL_SCHEME(326),
    MANIFEST_FILE_GROUP_POPULATOR_INVALID_FLAG_ERROR(400),
    MANIFEST_FILE_GROUP_POPULATOR_CONTENT_CHANGED_DURING_DOWNLOAD_ERROR(401),
    MANIFEST_FILE_GROUP_POPULATOR_PARSE_MANIFEST_FILE_ERROR(402),
    MANIFEST_FILE_GROUP_POPULATOR_DELETE_MANIFEST_FILE_ERROR(403),
    MANIFEST_FILE_GROUP_POPULATOR_METADATA_IO_ERROR(404),
    EMBEDDED_ASSET_MANIFEST_POPULATOR_PARSE_MANIFEST_FILE_ERROR(500),
    EMBEDDED_ASSET_MANIFEST_POPULATOR_REWRITE_DOWNLOAD_URLS_ERROR(501),
    EMBEDDED_ASSET_MANIFEST_POPULATOR_FETCH_ACCOUNTS_ERROR(502),
    EMBEDDED_ASSET_MANIFEST_POPULATOR_OVERRIDER_FAILURE_ERROR(503),
    EMBEDDED_ASSET_MANIFEST_POPULATOR_FAIL_TO_ADD_GROUP_ERROR(504),
    EMBEDDED_ASSET_MANIFEST_POPULATOR_FAIL_TO_IMPORT_GROUP_ERROR(505),
    EMBEDDED_ASSET_MANIFEST_POPULATOR_MANIFEST_CONFIG_HELPER_ERROR(506),
    HADES_FAIL_TO_DOWNLOAD_MANIFEST(4000),
    GDD_INVALID_ACCOUNT(2000),
    GDD_INVALID_AUTH_TOKEN(2001),
    GDD_FAIL_IN_SYNC_RUNNER(2002),
    GDD_INVALID_ELEMENT_COMBINATION_RECEIVED(2003),
    GDD_INVALID_INLINE_PAYLOAD_ELEMENT_DATA(2004),
    GDD_INVALID_CURRENT_ACTIVE_ELEMENT_DATA(2005),
    GDD_INVALID_NEXT_PENDING_ELEMENT_DATA(2006),
    GDD_CURRENT_ACTIVE_GROUP_HAS_NO_INLINE_FILE(2007),
    GDD_FAIL_TO_ADD_NEXT_PENDING_GROUP(2008),
    GDD_MISSING_ACCOUNT_FOR_PRIVATE_SYNC(2009),
    GDD_FAIL_IN_SYNC_RUNNER_PUBLIC(2010),
    GDD_FAIL_IN_SYNC_RUNNER_PRIVATE(2011),
    GDD_PUBLIC_SYNC_SUCCESS(2012),
    GDD_PRIVATE_SYNC_SUCCESS(2013),
    GDD_FAIL_TO_RETRIEVE_ZWIEBACK_TOKEN(2014),
    PCDD_GENERIC_FAILURE(4100),
    PCDD_FAIL_IN_OAK_CLIENT(4101),
    PCDD_FAIL_IN_OAK_REQUEST(4102),
    PCDD_RESULT_ILLEGAL_ARGUMENT(4103),
    PCDD_RESULT_INVALID_DATA(4104),
    PCDD_RESULT_NOT_FOUND(4105),
    PCDD_FAIL_TO_ADD_GROUP(4106);
    
    public final int aE;

    private csh(int i) {
        this.aE = i;
    }
}
