package defpackage;

/* renamed from: bff  reason: default package */
/* compiled from: PG */
public final class bff extends bfd {
    public bff(bfo bfo) {
        super(bfo);
    }

    public final boolean b(bhe bhe) {
        jnu.e(bhe, "workSpec");
        return bhe.k.f;
    }

    public final int d() {
        return 5;
    }

    public final /* bridge */ /* synthetic */ boolean e(Object obj) {
        if (!((Boolean) obj).booleanValue()) {
            return true;
        }
        return false;
    }
}
