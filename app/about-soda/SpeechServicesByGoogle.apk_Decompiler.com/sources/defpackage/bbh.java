package defpackage;

import androidx.wear.ambient.AmbientLifecycleObserverKt;

/* renamed from: bbh  reason: default package */
/* compiled from: PG */
public final class bbh extends AmbientLifecycleObserverKt {
    public bbh() {
        super((byte[]) null);
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || getClass() != obj.getClass()) {
            return false;
        }
        return true;
    }

    public final int hashCode() {
        return 97320;
    }

    public final String toString() {
        return "Retry";
    }
}
