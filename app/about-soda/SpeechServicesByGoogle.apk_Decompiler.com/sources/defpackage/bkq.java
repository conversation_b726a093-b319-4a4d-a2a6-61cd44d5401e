package defpackage;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.preference.Preference;
import com.android.car.ui.FocusArea;
import com.android.car.ui.baselayout.Insets;
import com.android.car.ui.preference.CarUiMultiSelectListPreference;
import com.android.car.ui.recyclerview.CarUiRecyclerView;
import com.google.android.tts.R;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Set;

/* renamed from: bkq  reason: default package */
/* compiled from: PG */
public final class bkq extends bc implements bjw {
    public Set a;
    public boolean b;
    private CarUiMultiSelectListPreference c;

    public final void a(Insets insets) {
        View requireView = requireView();
        bnv.i(requireView, R.id.list).setPadding(0, insets.getTop(), 0, insets.getBottom());
        requireView.setPadding(insets.getLeft(), 0, insets.getRight(), 0);
        FocusArea focusArea = (FocusArea) requireView.findViewById(R.id.car_ui_focus_area);
        if (focusArea != null) {
            focusArea.c(0, insets.getTop(), 0, insets.getBottom());
            focusArea.b(0, insets.getTop(), 0, insets.getBottom());
        }
    }

    public final void c() {
        if (this.c.callChangeListener(this.a)) {
            this.c.setValues(this.a);
        }
    }

    public final View onCreateView(LayoutInflater layoutInflater, ViewGroup viewGroup, Bundle bundle) {
        return layoutInflater.inflate(R.layout.car_ui_list_preference, viewGroup, false);
    }

    public final void onStart() {
        Insets I;
        super.onStart();
        if ((getTargetFragment() instanceof bku) && (I = ((bku) getTargetFragment()).I()) != null) {
            a(I);
        }
    }

    public final void onStop() {
        super.onStop();
        if (!this.b) {
            c();
        }
    }

    public final void onViewCreated(View view, Bundle bundle) {
        CarUiRecyclerView carUiRecyclerView = (CarUiRecyclerView) bnv.i(view, R.id.list);
        this.b = getResources().getBoolean(R.bool.car_ui_preference_list_instant_change_callback);
        Bundle bundle2 = this.l;
        if (bundle2 != null) {
            String string = bundle2.getString("key");
            asg asg = (asg) getTargetFragment();
            if (string == null) {
                throw new IllegalStateException("MultiSelectListPreference key not found in Fragment arguments");
            } else if (asg != null) {
                Preference aW = asg.aW(string);
                if (aW instanceof CarUiMultiSelectListPreference) {
                    this.c = (CarUiMultiSelectListPreference) aW;
                    carUiRecyclerView.setClipToPadding(false);
                    this.a = new HashSet(this.c.getValues());
                    CharSequence[] entries = this.c.getEntries();
                    CharSequence[] entryValues = this.c.getEntryValues();
                    if (entries == null || entryValues == null) {
                        throw new IllegalStateException("MultiSelectListPreference requires an entries array and an entryValues array.");
                    }
                    if (entries.length == entryValues.length) {
                        ArrayList arrayList = new ArrayList();
                        boolean[] selectedItems = this.c.getSelectedItems();
                        for (int i = 0; i < entries.length; i++) {
                            String charSequence = entries[i].toString();
                            String charSequence2 = entryValues[i].toString();
                            bla bla = new bla(bkx.CHECK_BOX);
                            bla.b(charSequence);
                            bla.a(selectedItems[i]);
                            bla.h = new bkp(this, charSequence2);
                            arrayList.add(bla);
                        }
                        carUiRecyclerView.setAdapter(new bli(arrayList));
                        return;
                    }
                    throw new IllegalStateException("MultiSelectListPreference entries array length does not match entryValues array length.");
                }
                throw new IllegalStateException("Cannot use MultiSelectListPreferenceFragment with a preference that is not of type CarUiMultiSelectListPreference");
            } else {
                throw new IllegalStateException("Target fragment must be registered before displaying MultiSelectListPreference screen.");
            }
        } else {
            throw new IllegalStateException("Preference arguments cannot be null");
        }
    }
}
