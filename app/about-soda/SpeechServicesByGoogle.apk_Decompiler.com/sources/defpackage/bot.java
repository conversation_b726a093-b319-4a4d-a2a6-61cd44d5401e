package defpackage;

/* renamed from: bot  reason: default package */
/* compiled from: PG */
public final class bot extends htq implements hvb {
    public static final bot f;
    private static volatile hvh g;
    public int a;
    public int b;
    public int c;
    public int d;
    public htw e = htr.a;

    static {
        bot bot = new bot();
        f = bot;
        htq.z(bot.class, bot);
    }

    private bot() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(f, "\u0001\u0004\u0000\u0001\u0001\u0004\u0004\u0000\u0001\u0000\u0001င\u0000\u0002င\u0001\u0003᠌\u0002\u0004'", new Object[]{"a", "b", "c", "d", hna.a, "e"});
        } else if (i2 == 3) {
            return new bot();
        } else {
            if (i2 == 4) {
                return new htk((htq) f);
            }
            if (i2 == 5) {
                return f;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = g;
            if (hvh == null) {
                synchronized (bot.class) {
                    hvh = g;
                    if (hvh == null) {
                        hvh = new htl(f);
                        g = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
