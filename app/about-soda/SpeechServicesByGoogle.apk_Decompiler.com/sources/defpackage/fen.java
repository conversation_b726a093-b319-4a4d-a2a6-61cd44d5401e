package defpackage;

/* renamed from: fen  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class fen implements hko {
    public final /* synthetic */ fer a;
    public final /* synthetic */ iai b;

    public /* synthetic */ fen(fer fer, iai iai) {
        this.a = fer;
        this.b = iai;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:34:0x0103, code lost:
        if (true != r6.i) goto L_0x0105;
     */
    /* JADX WARNING: Removed duplicated region for block: B:172:0x057f  */
    /* JADX WARNING: Removed duplicated region for block: B:175:0x05a2  */
    /* JADX WARNING: Removed duplicated region for block: B:176:0x05a8  */
    /* JADX WARNING: Removed duplicated region for block: B:200:0x069f  */
    /* JADX WARNING: Removed duplicated region for block: B:206:0x06b5  */
    /* JADX WARNING: Removed duplicated region for block: B:37:0x010d  */
    /* JADX WARNING: Removed duplicated region for block: B:40:0x0126  */
    /* JADX WARNING: Removed duplicated region for block: B:43:0x0148  */
    /* JADX WARNING: Removed duplicated region for block: B:44:0x0153  */
    /* JADX WARNING: Removed duplicated region for block: B:47:0x0189  */
    /* JADX WARNING: Removed duplicated region for block: B:48:0x018c  */
    /* JADX WARNING: Removed duplicated region for block: B:51:0x0199  */
    /* JADX WARNING: Removed duplicated region for block: B:68:0x024c  */
    /* JADX WARNING: Removed duplicated region for block: B:73:0x0260  */
    /* JADX WARNING: Removed duplicated region for block: B:79:0x02b6  */
    /* JADX WARNING: Removed duplicated region for block: B:82:0x02d2  */
    /* JADX WARNING: Removed duplicated region for block: B:83:0x02d4  */
    /* JADX WARNING: Removed duplicated region for block: B:86:0x0326  */
    /* JADX WARNING: Removed duplicated region for block: B:88:0x032d  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.hme a(java.lang.Object r19) {
        /*
            r18 = this;
            r1 = r18
            r0 = r19
            gxv r0 = (defpackage.gxv) r0
            fer r2 = r1.a
            fey r3 = r2.k
            java.lang.String r3 = r3.b
            java.lang.Object r3 = r0.get(r3)
            fhp r3 = (defpackage.fhp) r3
            defpackage.fvf.aP(r3)
            java.lang.String r4 = r3.c
            hca r5 = defpackage.fer.a
            hco r5 = r5.f()
            hby r5 = (defpackage.hby) r5
            r6 = 466(0x1d2, float:6.53E-43)
            java.lang.String r7 = "com/google/android/libraries/speech/transcription/recognition/SodaSpeechRecognizer"
            java.lang.String r8 = "initializeSodaDetectionHandler"
            java.lang.String r9 = "SodaSpeechRecognizer.java"
            hco r5 = r5.j(r7, r8, r6, r9)
            hby r5 = (defpackage.hby) r5
            java.lang.String r6 = "Initialize Soda with language pack directory"
            r5.r(r6)
            iam r5 = defpackage.iam.i
            htk r5 = r5.l()
            fey r6 = r2.k
            few r10 = r6.f
            few r11 = defpackage.few.GRPC_SERVICE_API
            boolean r10 = r10.equals(r11)
            if (r10 == 0) goto L_0x006e
            j$.util.Optional r10 = r6.E
            boolean r10 = r10.isEmpty()
            if (r10 == 0) goto L_0x0063
            hca r10 = defpackage.fer.a
            hco r10 = r10.g()
            hby r10 = (defpackage.hby) r10
            java.lang.String r11 = "getHostAppId"
            r12 = 562(0x232, float:7.88E-43)
            hco r10 = r10.j(r7, r11, r12, r9)
            hby r10 = (defpackage.hby) r10
            java.lang.String r11 = "Google ASR Service did not send a HostAppId."
            r10.r(r11)
        L_0x0063:
            j$.util.Optional r6 = r6.E
            iar r10 = defpackage.iar.UNKNOWN_HOST_APP_ID
            java.lang.Object r6 = r6.orElse(r10)
            iar r6 = (defpackage.iar) r6
            goto L_0x0070
        L_0x006e:
            iar r6 = defpackage.iar.RECOGNITION_SERVICE_PUBLIC_API
        L_0x0070:
            htq r10 = r5.b
            boolean r10 = r10.B()
            if (r10 != 0) goto L_0x007b
            r5.u()
        L_0x007b:
            htq r10 = r5.b
            iam r10 = (defpackage.iam) r10
            int r6 = r6.D
            r10.b = r6
            int r6 = r10.a
            r11 = 1
            r6 = r6 | r11
            r10.a = r6
            fey r6 = r2.k
            few r6 = r6.f
            int r6 = r6.ordinal()
            r12 = 3
            r13 = 4
            r14 = 2
            if (r6 == r11) goto L_0x00bb
            if (r6 == r14) goto L_0x00b9
            if (r6 == r12) goto L_0x00b7
            if (r6 == r13) goto L_0x00b5
            hca r6 = defpackage.fer.a
            hco r6 = r6.g()
            hby r6 = (defpackage.hby) r6
            java.lang.String r15 = "getIntegrationFlavor"
            r10 = 582(0x246, float:8.16E-43)
            hco r6 = r6.j(r7, r15, r10, r9)
            hby r6 = (defpackage.hby) r6
            java.lang.String r10 = "Entrypoint unidentified"
            r6.r(r10)
            r6 = r11
            goto L_0x00bc
        L_0x00b5:
            r6 = 6
            goto L_0x00bc
        L_0x00b7:
            r6 = r13
            goto L_0x00bc
        L_0x00b9:
            r6 = r12
            goto L_0x00bc
        L_0x00bb:
            r6 = 5
        L_0x00bc:
            htq r10 = r5.b
            boolean r10 = r10.B()
            if (r10 != 0) goto L_0x00c7
            r5.u()
        L_0x00c7:
            htq r10 = r5.b
            iam r10 = (defpackage.iam) r10
            int r6 = r6 + -1
            r10.c = r6
            int r6 = r10.a
            r6 = r6 | r14
            r10.a = r6
            fey r6 = r2.k
            int r10 = r6.M
            if (r10 == 0) goto L_0x06bb
            int r10 = r10 + -1
            if (r10 == 0) goto L_0x0101
            if (r10 == r11) goto L_0x00ff
            if (r10 == r14) goto L_0x0105
            if (r10 == r12) goto L_0x00fd
            hca r6 = defpackage.fer.a
            hco r6 = r6.g()
            hby r6 = (defpackage.hby) r6
            java.lang.String r10 = "getIntegrationPreference"
            r12 = 601(0x259, float:8.42E-43)
            hco r6 = r6.j(r7, r10, r12, r9)
            hby r6 = (defpackage.hby) r6
            java.lang.String r10 = "RecognizerMode unidentified"
            r6.r(r10)
            r12 = r11
            goto L_0x0105
        L_0x00fd:
            r12 = r14
            goto L_0x0105
        L_0x00ff:
            r12 = r13
            goto L_0x0105
        L_0x0101:
            boolean r6 = r6.i
            if (r11 == r6) goto L_0x00ff
        L_0x0105:
            htq r6 = r5.b
            boolean r6 = r6.B()
            if (r6 != 0) goto L_0x0110
            r5.u()
        L_0x0110:
            htq r6 = r5.b
            r10 = r6
            iam r10 = (defpackage.iam) r10
            int r12 = r12 + -1
            r10.d = r12
            int r12 = r10.a
            r12 = r12 | r13
            r10.a = r12
            fey r10 = r2.k
            boolean r6 = r6.B()
            if (r6 != 0) goto L_0x0129
            r5.u()
        L_0x0129:
            java.lang.String r6 = r10.e
            htq r10 = r5.b
            iam r10 = (defpackage.iam) r10
            r6.getClass()
            int r12 = r10.a
            r12 = r12 | 8
            r10.a = r12
            r10.e = r6
            htq r5 = r5.r()
            iam r5 = (defpackage.iam) r5
            grh r6 = r2.o
            boolean r10 = r6.f()
            if (r10 == 0) goto L_0x0153
            java.lang.Object r6 = r6.b()
            feh r6 = (defpackage.feh) r6
            iaj r6 = r6.a()
            goto L_0x0154
        L_0x0153:
            r6 = 0
        L_0x0154:
            int r10 = r3.b
            r2.u = r10
            hca r10 = defpackage.fer.a
            hco r10 = r10.c()
            hby r10 = (defpackage.hby) r10
            r12 = 479(0x1df, float:6.71E-43)
            hco r10 = r10.j(r7, r8, r12, r9)
            hby r10 = (defpackage.hby) r10
            int r12 = r2.u
            java.lang.String r15 = "Language pack directory: %s version: %d"
            r10.A(r15, r4, r12)
            grh r10 = r2.q
            dun r12 = new dun
            r15 = 20
            r12.<init>(r15)
            grh r10 = r10.a(r12)
            fhq r12 = defpackage.fhq.c
            java.lang.Object r10 = r10.d(r12)
            fhq r10 = (defpackage.fhq) r10
            int r12 = r10.a
            r12 = r12 & r14
            if (r12 == 0) goto L_0x018c
            java.lang.String r12 = r10.b
            goto L_0x018e
        L_0x018c:
            java.lang.String r12 = ""
        L_0x018e:
            fev r15 = r2.l
            r15.e(r3, r10)
            boolean r3 = r2.h
            ibf r10 = defpackage.ibf.e
            if (r3 == 0) goto L_0x024c
            android.content.Context r3 = r2.t
            java.io.File r10 = new java.io.File
            java.io.File r3 = r3.getFilesDir()
            java.lang.String r15 = "transcription"
            r10.<init>(r3, r15)
            boolean r3 = r10.exists()
            if (r3 != 0) goto L_0x01cf
            boolean r3 = r10.mkdirs()
            if (r3 != 0) goto L_0x01cf
            boolean r3 = r10.exists()
            if (r3 != 0) goto L_0x01cf
            hca r3 = defpackage.fer.a
            hco r3 = r3.g()
            hby r3 = (defpackage.hby) r3
            java.lang.String r15 = "createAndGetTranscriptionDir"
            r13 = 1044(0x414, float:1.463E-42)
            hco r3 = r3.j(r7, r15, r13, r9)
            hby r3 = (defpackage.hby) r3
            java.lang.String r13 = "Transcription directory not created"
            r3.r(r13)
        L_0x01cf:
            java.lang.String r3 = r10.getPath()
            ibf r10 = defpackage.ibf.e
            htk r10 = r10.l()
            boolean r13 = r2.h
            htq r15 = r10.b
            boolean r15 = r15.B()
            if (r15 != 0) goto L_0x01e6
            r10.u()
        L_0x01e6:
            htq r15 = r10.b
            r14 = r15
            ibf r14 = (defpackage.ibf) r14
            r17 = r12
            int r12 = r14.a
            r12 = r12 | r11
            r14.a = r12
            r14.b = r13
            boolean r12 = r15.B()
            if (r12 != 0) goto L_0x01fd
            r10.u()
        L_0x01fd:
            htq r12 = r10.b
            r13 = r12
            ibf r13 = (defpackage.ibf) r13
            r3.getClass()
            int r14 = r13.a
            r15 = 2
            r14 = r14 | r15
            r13.a = r14
            r13.c = r3
            fey r13 = r2.k
            boolean r12 = r12.B()
            if (r12 != 0) goto L_0x0218
            r10.u()
        L_0x0218:
            java.lang.String r12 = r13.e
            htq r13 = r10.b
            ibf r13 = (defpackage.ibf) r13
            int r14 = r13.a
            r15 = 4
            r14 = r14 | r15
            r13.a = r14
            java.lang.String r14 = "speech_service_"
            java.lang.String r12 = java.lang.String.valueOf(r12)
            java.lang.String r12 = r14.concat(r12)
            r13.d = r12
            htq r10 = r10.r()
            ibf r10 = (defpackage.ibf) r10
            hca r12 = defpackage.fer.a
            hco r12 = r12.f()
            hby r12 = (defpackage.hby) r12
            r13 = 504(0x1f8, float:7.06E-43)
            hco r12 = r12.j(r7, r8, r13, r9)
            hby r12 = (defpackage.hby) r12
            java.lang.String r13 = "soda_audio_dump_to_disk_enable audio path: %s"
            r12.u(r13, r3)
            goto L_0x024e
        L_0x024c:
            r17 = r12
        L_0x024e:
            fcm r3 = r2.y
            boolean r3 = r3.k
            java.lang.String r12 = "ag-AG"
            if (r3 == 0) goto L_0x02b6
            fey r3 = r2.k
            j$.util.Optional r3 = r3.B
            boolean r3 = r3.isPresent()
            if (r3 == 0) goto L_0x02b6
            fey r3 = r2.k
            j$.util.Optional r3 = r3.B
            java.lang.Object r3 = r3.get()
            java.lang.Object r13 = r0.get(r12)
            fhp r13 = (defpackage.fhp) r13
            if (r13 == 0) goto L_0x02b7
            hca r14 = defpackage.fer.a
            hco r14 = r14.b()
            hby r14 = (defpackage.hby) r14
            r15 = 513(0x201, float:7.19E-43)
            hco r7 = r14.j(r7, r8, r15, r9)
            hby r7 = (defpackage.hby) r7
            java.lang.String r8 = "USM encoder LP path: %s"
            java.lang.String r9 = r13.c
            r7.u(r8, r9)
            htq r3 = (defpackage.htq) r3
            r7 = 5
            java.lang.Object r7 = r3.C(r7)
            htk r7 = (defpackage.htk) r7
            r7.x(r3)
            java.lang.String r3 = r13.c
            htq r8 = r7.b
            boolean r8 = r8.B()
            if (r8 != 0) goto L_0x02a0
            r7.u()
        L_0x02a0:
            htq r8 = r7.b
            ibe r8 = (defpackage.ibe) r8
            r3.getClass()
            int r9 = r8.a
            r13 = 2
            r9 = r9 | r13
            r8.a = r9
            r8.c = r3
            htq r3 = r7.r()
            ibe r3 = (defpackage.ibe) r3
            goto L_0x02b7
        L_0x02b6:
            r3 = 0
        L_0x02b7:
            cmq r7 = defpackage.cmr.a()
            r7.i(r4)
            fey r4 = r2.k
            boolean r4 = r4.q
            r7.g(r4)
            fey r4 = r2.k
            java.lang.String r8 = r4.w
            java.lang.String r9 = "smart_dictation"
            boolean r8 = r8.equals(r9)
            r13 = 0
            if (r8 == 0) goto L_0x02d4
            r4 = r13
            goto L_0x02d6
        L_0x02d4:
            boolean r4 = r4.o
        L_0x02d6:
            iai r8 = r1.b
            r7.c(r4)
            fey r4 = r2.k
            j$.util.Optional r4 = r4.r
            r14 = 0
            java.lang.Object r4 = r4.orElse(r14)
            ibq r4 = (defpackage.ibq) r4
            r7.b = r4
            fey r4 = r2.k
            boolean r4 = r4.s
            r7.e(r4)
            r7.a(r8)
            fey r4 = r2.k
            java.lang.String r4 = r4.b
            r7.e = r4
            grh r4 = r2.p
            feq r8 = new feq
            r8.<init>(r2, r4)
            r7.f = r8
            r7.g = r5
            fey r4 = r2.k
            j$.util.Optional r4 = r4.v
            r5 = 0
            java.lang.Object r4 = r4.orElse(r5)
            ibi r4 = (defpackage.ibi) r4
            r7.h = r4
            fey r4 = r2.k
            j$.util.Optional r4 = r4.z
            java.lang.Object r4 = r4.orElse(r5)
            ibl r4 = (defpackage.ibl) r4
            r7.i = r4
            fey r4 = r2.k
            j$.util.Optional r4 = r4.A
            boolean r4 = r4.isEmpty()
            if (r4 == 0) goto L_0x032d
            j$.util.Optional r0 = j$.util.Optional.empty()
        L_0x032a:
            r4 = 0
            goto L_0x0560
        L_0x032d:
            fey r4 = r2.k
            j$.util.Optional r4 = r4.A
            java.lang.Object r4 = r4.get()
            ibp r5 = defpackage.ibp.k
            htk r5 = r5.l()
            fdp r4 = (defpackage.fdp) r4
            int r8 = r4.b
            int r8 = defpackage.a.x(r8)
            if (r8 != 0) goto L_0x0346
            r8 = r11
        L_0x0346:
            htq r14 = r5.b
            boolean r14 = r14.B()
            if (r14 != 0) goto L_0x0351
            r5.u()
        L_0x0351:
            htq r14 = r5.b
            r15 = r14
            ibp r15 = (defpackage.ibp) r15
            int r8 = r8 + -1
            r15.e = r8
            int r8 = r15.a
            r16 = 4
            r8 = r8 | 4
            r15.a = r8
            boolean r8 = r14.B()
            if (r8 != 0) goto L_0x036b
            r5.u()
        L_0x036b:
            htq r8 = r5.b
            r14 = r8
            ibp r14 = (defpackage.ibp) r14
            int r15 = r14.a
            r15 = r15 | r11
            r14.a = r15
            r14.c = r11
            int r14 = r4.f
            int r14 = defpackage.a.A(r14)
            if (r14 != 0) goto L_0x0380
            r14 = r11
        L_0x0380:
            boolean r8 = r8.B()
            if (r8 != 0) goto L_0x0389
            r5.u()
        L_0x0389:
            htq r8 = r5.b
            r15 = r8
            ibp r15 = (defpackage.ibp) r15
            int r14 = r14 + -1
            r15.h = r14
            int r14 = r15.a
            r14 = r14 | 32
            r15.a = r14
            int r14 = r4.d
            if (r14 <= 0) goto L_0x03b1
            boolean r8 = r8.B()
            if (r8 != 0) goto L_0x03a5
            r5.u()
        L_0x03a5:
            htq r8 = r5.b
            ibp r8 = (defpackage.ibp) r8
            int r15 = r8.a
            r15 = r15 | 8
            r8.a = r15
            r8.f = r14
        L_0x03b1:
            int r8 = r4.e
            if (r8 <= 0) goto L_0x03cc
            htq r14 = r5.b
            boolean r14 = r14.B()
            if (r14 != 0) goto L_0x03c0
            r5.u()
        L_0x03c0:
            htq r14 = r5.b
            ibp r14 = (defpackage.ibp) r14
            int r15 = r14.a
            r15 = r15 | 16
            r14.a = r15
            r14.g = r8
        L_0x03cc:
            boolean r4 = r4.g
            htq r8 = r5.b
            boolean r8 = r8.B()
            if (r8 != 0) goto L_0x03d9
            r5.u()
        L_0x03d9:
            htq r8 = r5.b
            r14 = r8
            ibp r14 = (defpackage.ibp) r14
            int r15 = r14.a
            r15 = r15 | 128(0x80, float:1.794E-43)
            r14.a = r15
            r14.j = r4
            int r4 = r14.h
            int r4 = defpackage.a.A(r4)
            if (r4 != 0) goto L_0x03ef
            goto L_0x03f1
        L_0x03ef:
            if (r4 != r11) goto L_0x040a
        L_0x03f1:
            int r4 = r14.f
            if (r4 != r11) goto L_0x040a
            boolean r4 = r8.B()
            if (r4 != 0) goto L_0x03fe
            r5.u()
        L_0x03fe:
            htq r4 = r5.b
            ibp r4 = (defpackage.ibp) r4
            int r8 = r4.a
            r14 = 2
            r8 = r8 | r14
            r4.a = r8
            r4.d = r13
        L_0x040a:
            fey r4 = r2.k
            java.lang.String r4 = r4.w
            boolean r4 = r4.equals(r9)
            if (r4 == 0) goto L_0x0483
            htq r4 = r5.b
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x041f
            r5.u()
        L_0x041f:
            htq r4 = r5.b
            r8 = r4
            ibp r8 = (defpackage.ibp) r8
            r8.h = r11
            int r9 = r8.a
            r9 = r9 | 32
            r8.a = r9
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x0435
            r5.u()
        L_0x0435:
            htq r4 = r5.b
            r8 = r4
            ibp r8 = (defpackage.ibp) r8
            int r9 = r8.a
            r9 = r9 | 8
            r8.a = r9
            r8.f = r13
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x044b
            r5.u()
        L_0x044b:
            htq r4 = r5.b
            r8 = r4
            ibp r8 = (defpackage.ibp) r8
            int r9 = r8.a
            r9 = r9 | 16
            r8.a = r9
            r8.g = r13
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x0461
            r5.u()
        L_0x0461:
            htq r4 = r5.b
            r8 = r4
            ibp r8 = (defpackage.ibp) r8
            int r9 = r8.a
            r9 = r9 | 64
            r8.a = r9
            r8.i = r13
            boolean r4 = r4.B()
            if (r4 != 0) goto L_0x0477
            r5.u()
        L_0x0477:
            htq r4 = r5.b
            ibp r4 = (defpackage.ibp) r4
            int r8 = r4.a
            r8 = r8 | 128(0x80, float:1.794E-43)
            r4.a = r8
            r4.j = r13
        L_0x0483:
            fey r4 = r2.k
            j$.util.Optional r4 = r4.z
            java.lang.Object r4 = r4.get()
            ibl r4 = (defpackage.ibl) r4
            huf r4 = r4.a
            fey r8 = r2.k
            j$.util.Optional r8 = r8.A
            java.lang.Object r8 = r8.get()
            fdp r8 = (defpackage.fdp) r8
            huf r8 = r8.c
            boolean r9 = r4.isEmpty()
            if (r9 == 0) goto L_0x04e5
            boolean r9 = r8.isEmpty()
            if (r9 == 0) goto L_0x04e5
            gyo r0 = r0.entrySet()
            hbp r0 = r0.iterator()
        L_0x04af:
            boolean r4 = r0.hasNext()
            if (r4 == 0) goto L_0x04d9
            java.lang.Object r4 = r0.next()
            java.util.Map$Entry r4 = (java.util.Map.Entry) r4
            java.lang.Object r8 = r4.getKey()
            java.lang.String r8 = (java.lang.String) r8
            boolean r8 = r8.equals(r12)
            if (r8 != 0) goto L_0x04af
            java.lang.Object r8 = r4.getKey()
            java.lang.String r8 = (java.lang.String) r8
            java.lang.Object r4 = r4.getValue()
            fhp r4 = (defpackage.fhp) r4
            java.lang.String r4 = r4.c
            r5.S(r8, r4)
            goto L_0x04af
        L_0x04d9:
            htq r0 = r5.r()
            ibp r0 = (defpackage.ibp) r0
            j$.util.Optional r0 = j$.util.Optional.of(r0)
            goto L_0x032a
        L_0x04e5:
            boolean r9 = r4.isEmpty()
            if (r9 == 0) goto L_0x04f1
            java.util.HashSet r4 = new java.util.HashSet
            r4.<init>(r8)
            goto L_0x0525
        L_0x04f1:
            boolean r9 = r8.isEmpty()
            if (r9 == 0) goto L_0x04fe
            java.util.HashSet r8 = new java.util.HashSet
            r8.<init>(r4)
            r4 = r8
            goto L_0x0525
        L_0x04fe:
            java.util.HashSet r9 = new java.util.HashSet
            r9.<init>(r4)
            j$.util.stream.Stream r4 = j$.util.Collection.EL.stream(r8)
            j$.util.Objects.requireNonNull(r9)
            eyn r8 = new eyn
            r12 = 12
            r8.<init>(r9, r12)
            j$.util.stream.Stream r4 = r4.filter(r8)
            fcx r8 = new fcx
            r9 = 2
            r8.<init>(r9)
            j$.util.stream.Collector r8 = j$.util.stream.Collectors.toCollection(r8)
            java.lang.Object r4 = r4.collect(r8)
            java.util.Set r4 = (java.util.Set) r4
        L_0x0525:
            fey r8 = r2.k
            java.lang.String r8 = r8.b
            r4.add(r8)
            java.util.Iterator r4 = r4.iterator()
        L_0x0530:
            boolean r8 = r4.hasNext()
            if (r8 == 0) goto L_0x0554
            java.lang.Object r8 = r4.next()
            java.lang.String r8 = (java.lang.String) r8
            boolean r9 = r0.containsKey(r8)
            if (r9 == 0) goto L_0x0530
            java.lang.Object r9 = r0.get(r8)
            fhp r9 = (defpackage.fhp) r9
            java.lang.Object r9 = j$.util.Objects.requireNonNull(r9)
            fhp r9 = (defpackage.fhp) r9
            java.lang.String r9 = r9.c
            r5.S(r8, r9)
            goto L_0x0530
        L_0x0554:
            htq r0 = r5.r()
            ibp r0 = (defpackage.ibp) r0
            j$.util.Optional r0 = j$.util.Optional.of(r0)
            goto L_0x032a
        L_0x0560:
            java.lang.Object r0 = r0.orElse(r4)
            ibp r0 = (defpackage.ibp) r0
            r7.k = r0
            r7.l = r6
            fey r0 = r2.k
            java.lang.String r0 = r0.w
            r7.f(r0)
            r12 = r17
            r7.h(r12)
            r7.j = r10
            fcm r0 = r2.y
            int r0 = r0.m
            if (r0 <= r11) goto L_0x057f
            goto L_0x0580
        L_0x057f:
            r11 = r13
        L_0x0580:
            r7.b(r11)
            fey r0 = r2.k
            boolean r0 = r0.L
            r7.d(r0)
            ibe r3 = (defpackage.ibe) r3
            r7.m = r3
            grh r0 = r2.r
            grm r0 = (defpackage.grm) r0
            java.lang.Object r0 = r0.a
            awh r0 = (defpackage.awh) r0
            r7.p = r0
            fnn r0 = r2.A
            cms r3 = r7.j(r0)
            boolean r0 = r3.e
            if (r0 == 0) goto L_0x05a8
            idk r0 = r3.b()
            goto L_0x069b
        L_0x05a8:
            boolean r0 = r3.g()
            java.lang.String r4 = "blockingReconnect"
            java.lang.String r5 = "com/google/android/libraries/assistant/soda/SodaDetectionHandler"
            java.lang.String r6 = "SodaDetectionHandler.java"
            if (r0 == 0) goto L_0x060a
            hca r0 = defpackage.cms.a     // Catch:{ IllegalStateException -> 0x05d5 }
            hco r0 = r0.c()     // Catch:{ IllegalStateException -> 0x05d5 }
            hby r0 = (defpackage.hby) r0     // Catch:{ IllegalStateException -> 0x05d5 }
            r7 = 322(0x142, float:4.51E-43)
            hco r0 = r0.j(r5, r4, r7, r6)     // Catch:{ IllegalStateException -> 0x05d5 }
            hby r0 = (defpackage.hby) r0     // Catch:{ IllegalStateException -> 0x05d5 }
            java.lang.String r7 = "Trying to stop SODA if it is running"
            r0.r(r7)     // Catch:{ IllegalStateException -> 0x05d5 }
            grh r0 = r3.f     // Catch:{ IllegalStateException -> 0x05d5 }
            grm r0 = (defpackage.grm) r0     // Catch:{ IllegalStateException -> 0x05d5 }
            java.lang.Object r0 = r0.a     // Catch:{ IllegalStateException -> 0x05d5 }
            cmw r0 = (defpackage.cmw) r0     // Catch:{ IllegalStateException -> 0x05d5 }
            r0.m()     // Catch:{ IllegalStateException -> 0x05d5 }
            goto L_0x060a
        L_0x05d5:
            r0 = move-exception
            r13 = r0
            hca r0 = defpackage.cms.a
            hco r7 = r0.g()
            java.lang.String r10 = "blockingReconnect"
            r11 = 325(0x145, float:4.55E-43)
            java.lang.String r8 = "SODA failed to stop capturing; simply deleting SODA"
            java.lang.String r9 = "com/google/android/libraries/assistant/soda/SodaDetectionHandler"
            java.lang.String r12 = "SodaDetectionHandler.java"
            ((defpackage.hby) ((defpackage.hby) ((defpackage.hby) r7).i(r13)).j(r9, r10, r11, r12)).r(r8)
            hca r0 = defpackage.cms.a
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            r7 = 326(0x146, float:4.57E-43)
            hco r0 = r0.j(r5, r4, r7, r6)
            hby r0 = (defpackage.hby) r0
            java.lang.String r7 = "Deleting SODA due to failure in stopping capture."
            r0.r(r7)
            grh r0 = r3.f
            grm r0 = (defpackage.grm) r0
            java.lang.Object r0 = r0.a
            cmw r0 = (defpackage.cmw) r0
            r0.j()
        L_0x060a:
            boolean r0 = r3.g()
            if (r0 == 0) goto L_0x064a
            grh r0 = r3.f
            grm r0 = (defpackage.grm) r0
            java.lang.Object r0 = r0.a
            cmw r0 = (defpackage.cmw) r0
            ibs r7 = r3.a()
            boolean r0 = r0.r(r7)
            if (r0 != 0) goto L_0x064a
            cmm r0 = r3.c
            if (r0 == 0) goto L_0x0632
            grh r4 = r3.f
            grm r4 = (defpackage.grm) r4
            java.lang.Object r4 = r4.a
            cmw r4 = (defpackage.cmw) r4
            r4.l(r0)
            goto L_0x0647
        L_0x0632:
            hca r0 = defpackage.cms.a
            hco r0 = r0.h()
            hby r0 = (defpackage.hby) r0
            r7 = 337(0x151, float:4.72E-43)
            hco r0 = r0.j(r5, r4, r7, r6)
            hby r0 = (defpackage.hby) r0
            java.lang.String r4 = "No soda callback provided. Events will not be reported back"
            r0.r(r4)
        L_0x0647:
            idk r0 = defpackage.idk.NO_ERROR
            goto L_0x069b
        L_0x064a:
            hca r0 = defpackage.cms.a
            hco r0 = r0.f()
            hby r0 = (defpackage.hby) r0
            r4 = 296(0x128, float:4.15E-43)
            java.lang.String r7 = "blockingDisconnect"
            hco r0 = r0.j(r5, r7, r4, r6)
            hby r0 = (defpackage.hby) r0
            java.lang.String r4 = "#blockingDisconnect"
            r0.r(r4)
            boolean r0 = r3.e
            if (r0 == 0) goto L_0x0671
            cmg r0 = r3.g
            java.lang.String r4 = r3.c()
            cmm r5 = r3.c
            r0.f(r4, r5)
            goto L_0x0697
        L_0x0671:
            boolean r0 = r3.g()
            if (r0 == 0) goto L_0x0697
            hca r0 = defpackage.cms.a
            hco r0 = r0.c()
            hby r0 = (defpackage.hby) r0
            r4 = 303(0x12f, float:4.25E-43)
            hco r0 = r0.j(r5, r7, r4, r6)
            hby r0 = (defpackage.hby) r0
            java.lang.String r4 = "Deleting SODA"
            r0.r(r4)
            grh r0 = r3.f
            grm r0 = (defpackage.grm) r0
            java.lang.Object r0 = r0.a
            cmw r0 = (defpackage.cmw) r0
            r0.j()
        L_0x0697:
            idk r0 = r3.b()
        L_0x069b:
            idk r4 = defpackage.idk.NO_ERROR
            if (r0 != r4) goto L_0x06b5
            htb r0 = r2.g
            long r4 = r0.a
            r6 = -1
            int r2 = (r4 > r6 ? 1 : (r4 == r6 ? 0 : -1))
            if (r2 != 0) goto L_0x06ad
            r3.d()
            goto L_0x06b0
        L_0x06ad:
            r3.e(r0)
        L_0x06b0:
            hme r0 = defpackage.hfc.K(r3)
            return r0
        L_0x06b5:
            fch r2 = new fch
            r2.<init>(r0)
            throw r2
        L_0x06bb:
            r0 = 0
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.fen.a(java.lang.Object):hme");
    }
}
