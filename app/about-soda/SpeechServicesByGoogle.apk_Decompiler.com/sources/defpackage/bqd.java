package defpackage;

import android.os.ParcelFileDescriptor;
import java.util.Arrays;
import java.util.Locale;

/* renamed from: bqd  reason: default package */
/* compiled from: PG */
public final class bqd {
    public final byte[] a;
    public final Locale b;
    public final ParcelFileDescriptor c;

    public bqd() {
        throw null;
    }

    public final boolean equals(Object obj) {
        byte[] bArr;
        if (obj == this) {
            return true;
        }
        if (obj instanceof bqd) {
            bqd bqd = (bqd) obj;
            byte[] bArr2 = this.a;
            if (bqd instanceof bqd) {
                bArr = bqd.a;
            } else {
                bArr = bqd.a;
            }
            if (Arrays.equals(bArr2, bArr) && this.b.equals(bqd.b)) {
                ParcelFileDescriptor parcelFileDescriptor = this.c;
                ParcelFileDescriptor parcelFileDescriptor2 = bqd.c;
                if (parcelFileDescriptor != null ? parcelFileDescriptor.equals(parcelFileDescriptor2) : parcelFileDescriptor2 == null) {
                    return true;
                }
            }
        }
        return false;
    }

    public final int hashCode() {
        int i;
        int hashCode = ((Arrays.hashCode(this.a) ^ 1000003) * 1000003) ^ this.b.hashCode();
        ParcelFileDescriptor parcelFileDescriptor = this.c;
        if (parcelFileDescriptor == null) {
            i = 0;
        } else {
            i = parcelFileDescriptor.hashCode();
        }
        return (hashCode * 1000003) ^ i;
    }

    public final String toString() {
        ParcelFileDescriptor parcelFileDescriptor = this.c;
        Locale locale = this.b;
        String arrays = Arrays.toString(this.a);
        String valueOf = String.valueOf(locale);
        String valueOf2 = String.valueOf(parcelFileDescriptor);
        return "RosieRobotRequest{audio=" + arrays + ", locale=" + valueOf + ", audioFileDescriptor=" + valueOf2 + "}";
    }

    public bqd(byte[] bArr, Locale locale, ParcelFileDescriptor parcelFileDescriptor) {
        this.a = bArr;
        this.b = locale;
        this.c = parcelFileDescriptor;
    }
}
