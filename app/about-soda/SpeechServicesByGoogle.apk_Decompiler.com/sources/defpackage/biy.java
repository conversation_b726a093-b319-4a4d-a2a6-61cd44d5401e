package defpackage;

import androidx.work.impl.workers.ConstraintTrackingWorker;

/* renamed from: biy  reason: default package */
/* compiled from: PG */
public final class biy extends jme {
    public Object a;
    public /* synthetic */ Object b;
    final /* synthetic */ ConstraintTrackingWorker c;
    public int d;
    public ConstraintTrackingWorker e;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public biy(ConstraintTrackingWorker constraintTrackingWorker, jlr jlr) {
        super(jlr);
        this.c = constraintTrackingWorker;
    }

    public final Object bk(Object obj) {
        this.b = obj;
        this.d |= Integer.MIN_VALUE;
        return this.c.i(this);
    }
}
