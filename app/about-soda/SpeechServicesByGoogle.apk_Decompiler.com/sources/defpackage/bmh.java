package defpackage;

import android.content.Context;
import android.graphics.drawable.Drawable;
import java.lang.ref.WeakReference;

/* renamed from: bmh  reason: default package */
/* compiled from: PG */
public final class bmh {
    public final WeakReference a;
    public String b;
    public String c;
    public Drawable d;
    public Drawable e;
    public int f = -1;
    public CharSequence g;
    public Drawable h;
    public bmk i;
    public bmi j = bmi.ALWAYS;
    public boolean k = true;
    public boolean l = false;
    public boolean m = false;
    public boolean n = false;
    public boolean o = true;
    public boolean p = false;
    public boolean q = false;
    public boolean r = false;
    public boolean s = false;
    public int t = 0;

    public bmh(Context context) {
        this.a = new WeakReference(context);
    }

    public final bml a() {
        boolean z = this.p;
        if (z && (this.l || this.h == null)) {
            throw new IllegalStateException("Only simple icons can be activatable");
        } else if (!this.m || (!this.l && !z)) {
            boolean z2 = this.r;
            if (z2 && this.s) {
                throw new IllegalStateException("Can't have both a search and settings MenuItem");
            } else if (z && this.j == bmi.NEVER) {
                throw new IllegalStateException("Activatable MenuItems not supported as Overflow");
            } else if (z2 && (!this.b.contentEquals(this.g) || !this.d.equals(this.h) || this.m || this.p || !this.k || this.l || this.j != bmi.ALWAYS)) {
                throw new IllegalStateException("Invalid search MenuItem");
            } else if (!this.s || (this.c.contentEquals(this.g) && this.e.equals(this.h) && !this.m && !this.p && this.k && !this.l && this.j == bmi.ALWAYS)) {
                return new bml(this);
            } else {
                throw new IllegalStateException("Invalid settings MenuItem");
            }
        } else {
            throw new IllegalStateException("Unsupported options for a checkable MenuItem");
        }
    }
}
