package defpackage;

/* renamed from: etj  reason: default package */
/* compiled from: PG */
final class etj implements hls {
    final /* synthetic */ jix a;
    final /* synthetic */ int b;
    final /* synthetic */ int c;

    public etj(jix jix, int i, int i2) {
        this.a = jix;
        this.b = i;
        this.c = i2;
    }

    public final void a(Throwable th) {
        ((hby) ((hby) etm.a.h().h(hdg.a, "ALT.AudioService")).j("com/google/android/libraries/search/audio/service/impl/AudioServiceImpl$1", "onFailure", 224, "AudioServiceImpl.java")).v("#audio# Failed to get StopListeningStatus. clientToken: %d, sessionToken: %d", this.b, this.c);
        this.a.c(eki.a(eag.FAILED_CLOSING_ERROR_IN_GETTING_AUDIO_SOURCE_CLOSING_STATUS, eam.CLIENT_REQUESTED));
        this.a.b(th);
    }

    public final /* bridge */ /* synthetic */ void b(Object obj) {
        this.a.c((dzi) obj);
        this.a.a();
    }
}
