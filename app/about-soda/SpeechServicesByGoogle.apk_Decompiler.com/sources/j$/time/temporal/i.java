package j$.time.temporal;

import j$.nio.file.attribute.a;

enum i implements p {
    JULIAN_DAY("JulianDay", r4, r5, 2440588),
    MODIFIED_JULIAN_DAY("ModifiedJulianDay", r4, r5, 40587),
    RATA_DIE("Rata<PERSON>ie", r4, r5, 719163);
    
    private static final long serialVersionUID = -7501623920830201812L;
    private final transient String a;
    private final transient t b;
    private final transient long c;

    private i(String str, ChronoUnit chronoUnit, ChronoUnit chronoUnit2, long j) {
        this.a = str;
        this.b = t.j(-365243219162L + j, 365241780471L + j);
        this.c = j;
    }

    public final boolean B() {
        return true;
    }

    public final long g(m mVar) {
        return mVar.B(a.EPOCH_DAY) + this.c;
    }

    public final t m() {
        return this.b;
    }

    public final boolean p(m mVar) {
        return mVar.e(a.EPOCH_DAY);
    }

    public final k s(k kVar, long j) {
        if (this.b.i(j)) {
            return kVar.d(a.g(j, this.c), a.EPOCH_DAY);
        }
        throw new RuntimeException("Invalid value: " + this.a + " " + j);
    }

    public final t t(m mVar) {
        if (mVar.e(a.EPOCH_DAY)) {
            return this.b;
        }
        throw new RuntimeException("Unsupported field: ".concat(String.valueOf(this)));
    }

    public final String toString() {
        return this.a;
    }
}
