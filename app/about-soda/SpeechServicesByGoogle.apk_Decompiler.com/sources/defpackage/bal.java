package defpackage;

import java.util.concurrent.Executor;

/* renamed from: bal  reason: default package */
/* compiled from: PG */
public final class bal {
    public Executor a;
    public jlv b;
    public bcb c;
    public Executor d;
    public bbv e;
    public um f;
    public um g;
    public um h;
    public String i;
    public int j = 4;
    public int k = 20;
    public boolean l = true;
}
