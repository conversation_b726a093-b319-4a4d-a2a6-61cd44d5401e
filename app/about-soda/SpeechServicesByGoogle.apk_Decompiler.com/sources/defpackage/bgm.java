package defpackage;

import android.database.Cursor;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Set;

/* renamed from: bgm  reason: default package */
/* compiled from: PG */
public final class bgm implements bgk {
    public final aus a;

    public bgm(aus aus) {
        this.a = aus;
    }

    public final void a(HashMap hashMap) {
        Set<String> keySet = hashMap.keySet();
        if (!keySet.isEmpty()) {
            if (hashMap.size() <= 999) {
                StringBuilder sb = new StringBuilder();
                sb.append("SELECT `progress`,`work_spec_id` FROM `WorkProgress` WHERE `work_spec_id` IN (");
                int size = keySet.size();
                wb.r(sb, size);
                sb.append(")");
                auu a2 = auu.a(sb.toString(), size);
                int i = 1;
                for (String g : keySet) {
                    a2.g(i, g);
                    i++;
                }
                Cursor f = vy.f(this.a, a2, false);
                try {
                    int g2 = vy.g(f, "work_spec_id");
                    if (g2 != -1) {
                        while (f.moveToNext()) {
                            ArrayList arrayList = (ArrayList) hashMap.get(f.getString(g2));
                            if (arrayList != null) {
                                arrayList.add(bat.a(f.getBlob(0)));
                            }
                        }
                    }
                } finally {
                    f.close();
                }
            } else {
                wa.w(hashMap, new bgl(this, 0));
            }
        }
    }

    public final void b(HashMap hashMap) {
        Set<String> keySet = hashMap.keySet();
        if (!keySet.isEmpty()) {
            if (hashMap.size() <= 999) {
                StringBuilder sb = new StringBuilder();
                sb.append("SELECT `tag`,`work_spec_id` FROM `WorkTag` WHERE `work_spec_id` IN (");
                int size = keySet.size();
                wb.r(sb, size);
                sb.append(")");
                auu a2 = auu.a(sb.toString(), size);
                int i = 1;
                for (String g : keySet) {
                    a2.g(i, g);
                    i++;
                }
                Cursor f = vy.f(this.a, a2, false);
                try {
                    int g2 = vy.g(f, "work_spec_id");
                    if (g2 != -1) {
                        while (f.moveToNext()) {
                            ArrayList arrayList = (ArrayList) hashMap.get(f.getString(g2));
                            if (arrayList != null) {
                                arrayList.add(f.getString(0));
                            }
                        }
                    }
                } finally {
                    f.close();
                }
            } else {
                wa.w(hashMap, new bgl(this, 1));
            }
        }
    }
}
