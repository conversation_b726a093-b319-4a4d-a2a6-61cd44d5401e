<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt">
    <item android:state_enabled="true" android:state_pressed="true">
        <set>
            <objectAnimator android:interpolator="@interpolator/mtrl_fast_out_slow_in" android:duration="@integer/mtrl_card_anim_duration_ms" android:valueTo="@dimen/google_card_elevated_dragged_pressed_z" android:valueType="floatType" android:propertyName="translationZ" android:startDelay="@integer/mtrl_card_anim_delay_ms"/>
        </set>
    </item>
    <item android:state_enabled="true" app:state_dragged="true">
        <set>
            <objectAnimator android:interpolator="@anim/mtrl_card_lowers_interpolator" android:duration="0" android:valueTo="@dimen/google_card_elevated_dragged_pressed_z" android:valueType="floatType" android:propertyName="translationZ"/>
        </set>
    </item>
    <item>
        <set>
            <objectAnimator android:interpolator="@anim/mtrl_card_lowers_interpolator" android:duration="@integer/mtrl_card_anim_duration_ms" android:valueTo="0dp" android:valueType="floatType" android:propertyName="translationZ"/>
        </set>
    </item>
</selector>
