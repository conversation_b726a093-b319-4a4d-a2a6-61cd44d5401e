package defpackage;

import android.content.Context;
import android.content.res.Resources;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.SparseArray;
import android.view.SurfaceControlViewHost;
import android.view.View;
import android.view.ViewGroup;
import android.view.inputmethod.InputMethodManager;
import android.widget.TextView;
import com.android.car.ui.recyclerview.CarUiRecyclerView;
import com.google.android.tts.R;

/* renamed from: bne  reason: default package */
/* compiled from: PG */
public final class bne {
    public final Handler a = new Handler(Looper.getMainLooper());
    public final Context b;
    public final InputMethodManager c;
    public TextView d;
    public SurfaceControlViewHost e;
    public int f;
    public int g;
    public boolean h = false;
    public ViewGroup i;
    public ViewGroup.LayoutParams j;
    public bmu k = new bmt().a();

    public bne(Context context) {
        this.b = context;
        this.c = (InputMethodManager) context.getSystemService(InputMethodManager.class);
    }

    public final View a() {
        Object obj = this.k.a;
        if (obj instanceof CarUiRecyclerView) {
            return ((CarUiRecyclerView) obj).getView();
        }
        return (View) obj;
    }

    public final bms b() {
        String str;
        boolean z;
        boolean z2;
        Context context = this.b;
        Resources resources = context.getResources();
        SparseArray sparseArray = bnv.a;
        String string = resources.getString(R.string.car_ui_ime_wide_screen_system_property_name);
        if (!string.startsWith("ro.")) {
            str = bnv.k(string);
        } else {
            synchronized (bnv.a) {
                if (bnv.a.indexOfKey(R.string.car_ui_ime_wide_screen_system_property_name) >= 0) {
                    str = (String) bnv.a.get(R.string.car_ui_ime_wide_screen_system_property_name);
                } else {
                    str = bnv.k(string);
                    bnv.a.put(R.string.car_ui_ime_wide_screen_system_property_name, str);
                }
            }
        }
        boolean z3 = true;
        if (TextUtils.isEmpty(str) || !Boolean.parseBoolean(str) || Build.VERSION.SDK_INT < 30) {
            z = false;
        } else {
            z = true;
        }
        boolean z4 = context.getResources().getBoolean(R.bool.car_ui_ime_wide_screen_allow_app_hide_content_area);
        String[] stringArray = context.getResources().getStringArray(R.array.car_ui_ime_wide_screen_allowed_package_list);
        int length = stringArray.length;
        int i2 = 0;
        while (true) {
            if (i2 >= length) {
                z2 = false;
                break;
            } else if (stringArray[i2].equals(context.getPackageName())) {
                z2 = true;
                break;
            } else {
                i2++;
            }
        }
        if (!z || (!z2 && !z4)) {
            z3 = false;
        }
        bmr bmr = new bmr();
        bmr.a = z3;
        bmr.b = z;
        return new bms(bmr);
    }

    public final void c(bmu bmu) {
        if (bmu == null) {
            bmu = new bmt().a();
        }
        this.k = bmu;
    }

    public final void d(TextView textView) {
        TextView textView2;
        if (this.d == null) {
            this.d = textView;
            bnc bnc = new bnc(this);
            if (Build.VERSION.SDK_INT >= 30 && (textView2 = this.d) != null) {
                if (textView2.isAttachedToWindow()) {
                    this.d.getRootView().setOnApplyWindowInsetsListener(bnc);
                } else {
                    this.d.addOnAttachStateChangeListener(new bnd(this, (View.OnApplyWindowInsetsListener) bnc, 0));
                }
            }
        } else {
            throw new IllegalStateException("TextView already set");
        }
    }
}
