package defpackage;

/* renamed from: era  reason: default package */
/* compiled from: PG */
public final class era extends htn implements hto {
    public static final era c;
    private static volatile hvh e;
    public int a;
    public eqz b;
    private byte d = 2;

    static {
        era era = new era();
        c = era;
        htq.z(era.class, era);
    }

    private era() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return Byte.valueOf(this.d);
        }
        byte b2 = 1;
        if (i2 == 2) {
            return new hvl(c, "\u0004\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0001\u0001ᐉ\u0000", new Object[]{"a", "b"});
        } else if (i2 == 3) {
            return new era();
        } else {
            if (i2 == 4) {
                return new htm(c);
            }
            if (i2 == 5) {
                return c;
            }
            if (i2 != 6) {
                if (obj == null) {
                    b2 = 0;
                }
                this.d = b2;
                return null;
            }
            hvh hvh = e;
            if (hvh == null) {
                synchronized (era.class) {
                    hvh = e;
                    if (hvh == null) {
                        hvh = new htl(c);
                        e = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
