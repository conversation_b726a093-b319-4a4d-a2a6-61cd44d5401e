package defpackage;

/* renamed from: erj  reason: default package */
/* compiled from: PG */
public final class erj extends htq implements hvb {
    public static final erj c;
    private static volatile hvh d;
    public int a;
    public ebh b;

    static {
        erj erj = new erj();
        c = erj;
        htq.z(erj.class, erj);
    }

    private erj() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(c, "\u0004\u0001\u0000\u0001\u0001\u0001\u0001\u0000\u0000\u0000\u0001ဉ\u0000", new Object[]{"a", "b"});
        } else if (i2 == 3) {
            return new erj();
        } else {
            if (i2 == 4) {
                return new htk((htq) c);
            }
            if (i2 == 5) {
                return c;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = d;
            if (hvh == null) {
                synchronized (erj.class) {
                    hvh = d;
                    if (hvh == null) {
                        hvh = new htl(c);
                        d = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
