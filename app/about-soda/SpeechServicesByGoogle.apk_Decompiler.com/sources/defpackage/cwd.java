package defpackage;

import android.net.Uri;
import java.io.IOException;
import java.util.concurrent.Executor;

/* renamed from: cwd  reason: default package */
/* compiled from: PG */
public final class cwd implements cxa {
    public final cxd a;
    public final cws b;
    public final Executor c;
    public final cyk d;
    private final cws e;
    private final Uri f;
    private final Uri g;
    private final cxi h;
    private final kjd i;

    public cwd(cyk cyk, cxd cxd, cws cws, cws cws2, Uri uri, Uri uri2, cxi cxi, kjd kjd, Executor executor) {
        this.d = cyk;
        this.a = cxd;
        this.b = cws;
        this.e = cws2;
        this.f = uri;
        this.g = uri2;
        this.h = cxi;
        this.i = kjd;
        this.c = executor;
    }

    private static hme j() {
        return hfc.J(new IllegalStateException("Migration flag had unexpected state"));
    }

    private final void k(Uri uri) {
        if (this.i.j(uri)) {
            this.i.h(uri);
        }
    }

    public final hme a() {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.a.a();
        }
        if (ordinal == 2) {
            return ftd.L(b(this.a.a()), new cvi(this, 17), this.c);
        }
        if (ordinal != 3) {
            return j();
        }
        return this.e.a();
    }

    public final hme b(hme hme) {
        return ftd.F(ftd.K(hme, new amv(17), this.c), Exception.class, new amv(18), this.c);
    }

    public final hme c() {
        aom aom = new aom(18);
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.a.c();
        }
        if (ordinal == 2) {
            return ftd.L(b(this.a.c()), new cwa((Object) this, (Object) aom, 16), this.c);
        }
        if (ordinal != 3) {
            return j();
        }
        return this.e.c();
    }

    public final hme d() {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            try {
                k(this.f);
                k(this.g);
                return this.a.d();
            } catch (IOException e2) {
                return hfc.J(e2);
            } catch (Throwable th) {
                k(this.g);
                throw th;
            }
        } else if (ordinal == 2) {
            try {
                k(this.g);
                return ftd.L(this.a.d(), new cvi(this, 16), this.c);
            } catch (IOException e3) {
                return hfc.J(e3);
            }
        } else if (ordinal != 3) {
            return j();
        } else {
            try {
                k(this.f);
                return this.e.d();
            } catch (IOException e4) {
                return hfc.J(e4);
            }
        }
    }

    public final hme e(ctj ctj) {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.a.e(ctj);
        }
        if (ordinal == 2) {
            return ftd.L(b(this.a.e(ctj)), new cwa((Object) this, (htq) ctj, 11), this.c);
        }
        if (ordinal != 3) {
            return j();
        }
        return this.e.e(ctj);
    }

    public final hme f(gyo gyo) {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.a.f(gyo);
        }
        if (ordinal == 2) {
            return ftd.L(b(this.a.f(gyo)), new cwa((Object) this, (Object) gyo, 12), this.c);
        }
        if (ordinal != 3) {
            return j();
        }
        return this.e.f(gyo);
    }

    public final hme g(ctj ctj) {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.a.g(ctj);
        }
        if (ordinal == 2) {
            return ftd.L(b(this.a.g(ctj)), new cwa((Object) this, (htq) ctj, 13), this.c);
        }
        if (ordinal != 3) {
            return j();
        }
        return this.e.g(ctj);
    }

    public final hme h(ctj ctj, ctl ctl) {
        int ordinal = this.h.a().ordinal();
        if (ordinal == 1) {
            return this.a.h(ctj, ctl);
        }
        if (ordinal == 2) {
            return ftd.L(b(this.a.h(ctj, ctl)), new cvp((Object) this, (htq) ctj, (htq) ctl, 17), this.c);
        }
        if (ordinal != 3) {
            return j();
        }
        return this.e.h(ctj, ctl);
    }

    public final hme i(cze cze, cze cze2, int i2) {
        int q = cqh.q();
        if (cyh.b((long) q)) {
            if (cze.equals(cze2)) {
                this.d.c(1106, q);
            } else {
                this.d.c(i2, q);
            }
        }
        if (cze.a) {
            return hfc.K(cze.a());
        }
        return hfc.J((Throwable) cze.b());
    }
}
