package defpackage;

import java.util.HashMap;

/* renamed from: bgl  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bgl implements jna {
    public final /* synthetic */ bgm a;
    private final /* synthetic */ int b;

    public /* synthetic */ bgl(bgm bgm, int i) {
        this.b = i;
        this.a = bgm;
    }

    public final Object a(Object obj) {
        if (this.b != 0) {
            this.a.b((HashMap) obj);
            return jkd.a;
        }
        this.a.a((HashMap) obj);
        return jkd.a;
    }
}
