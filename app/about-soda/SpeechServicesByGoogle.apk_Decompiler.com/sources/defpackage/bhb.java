package defpackage;

/* renamed from: bhb  reason: default package */
/* compiled from: PG */
public final class bhb implements bgx {
    public final aus a;
    public final auv b;
    private final auv c;

    public bhb(aus aus) {
        this.a = aus;
        new bgy(aus);
        this.c = new bgz(aus);
        this.b = new bha(aus);
    }

    public final void a(String str) {
        this.a.k();
        axc d = this.c.d();
        d.g(1, str);
        try {
            this.a.l();
            d.a();
            this.a.o();
            this.a.m();
            this.c.f(d);
        } catch (Throwable th) {
            this.c.f(d);
            throw th;
        }
    }
}
