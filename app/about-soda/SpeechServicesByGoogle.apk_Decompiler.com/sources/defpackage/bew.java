package defpackage;

import android.net.ConnectivityManager;

/* renamed from: bew  reason: default package */
/* compiled from: PG */
public final class bew implements bfg {
    public final ConnectivityManager a;
    public final long b;

    public bew(ConnectivityManager connectivityManager, long j) {
        jnu.e(connectivityManager, "connManager");
        this.a = connectivityManager;
        this.b = j;
    }

    public final juo a(baq baq) {
        jnu.e(baq, "constraints");
        return new jug(new bev(baq, this, (jlr) null));
    }

    public final boolean b(bhe bhe) {
        jnu.e(bhe, "workSpec");
        if (bhe.k.a() != null) {
            return true;
        }
        return false;
    }

    public final boolean c(bhe bhe) {
        jnu.e(bhe, "workSpec");
        if (!b(bhe)) {
            return false;
        }
        throw new IllegalStateException("isCurrentlyConstrained() must never be called onNetworkRequestConstraintController. isCurrentlyConstrained() is called only on older platforms where NetworkRequest isn't supported");
    }
}
