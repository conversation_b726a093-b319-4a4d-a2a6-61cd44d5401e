package defpackage;

import android.content.Context;

/* renamed from: cqu  reason: default package */
/* compiled from: PG */
public final class cqu {
    public final Context a;
    public final grh b;
    private final grh c;
    private final grh d;

    public cqu() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof cqu) {
            cqu cqu = (cqu) obj;
            if (!this.a.equals(cqu.a) || !this.c.equals(cqu.c) || !this.d.equals(cqu.d) || !this.b.equals(cqu.b)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return ((((((((this.a.hashCode() ^ 1000003) * 1000003) ^ **********) * 1000003) ^ this.d.hashCode()) * 1000003) ^ 1237) * 1000003) ^ **********;
    }

    public final String toString() {
        grh grh = this.b;
        grh grh2 = this.d;
        grh grh3 = this.c;
        String valueOf = String.valueOf(this.a);
        String valueOf2 = String.valueOf(grh3);
        String valueOf3 = String.valueOf(grh2);
        String valueOf4 = String.valueOf(grh);
        return "CollectionBasisContext{context=" + valueOf + ", accountNames=" + valueOf2 + ", stacktrace=" + valueOf3 + ", googlerOverridesCheckbox=false, executor=" + valueOf4 + "}";
    }

    public cqu(Context context, grh grh, grh grh2, grh grh3) {
        this.a = context;
        this.c = grh;
        this.d = grh2;
        this.b = grh3;
    }
}
