package com.google.speech.recognizer;

import android.util.Log;
import com.google.protobuf.CodedInputStream;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.WireFormat;

/**
 * 干净的SODA解析器 - 使用protobuf-javalite库
 * 
 * 这个解析器专注于解决乱码问题：
 * 1. 使用Google官方的protobuf库
 * 2. 严格按照protobuf规范解析
 * 3. 不进行额外的字符串处理
 * 4. 类似JavaScript版本的简单直接方式
 */
public class SodaCleanParser {
    private static final String TAG = "SodaCleanParser";

    // 用于去重的最近结果缓存
    private static String lastFinalResult = "";
    private static long lastResultTime = 0;
    
    /**
     * 解析SODA响应数据
     * @param responseData 原始响应数据
     * @param callback 结果回调
     */
    public static void parseSodaResponse(byte[] responseData, RecognitionResultCallback callback) {
        if (responseData == null || responseData.length == 0) {
            Log.w(TAG, "Response data is null or empty");
            return;
        }
        
        try {
            Log.v(TAG, "Parsing response data, length: " + responseData.length);

            // 基于Android实际数据结构的解析策略
            String hypothesis = parseAndroidSodaData(responseData);
            if (hypothesis != null && !hypothesis.trim().isEmpty()) {
                // 去重逻辑：避免重复输出相同或相似的句子
                if (shouldOutputResult(hypothesis)) {
                    Log.i(TAG, "✅ New sentence: " + hypothesis);
                    callback.onFinalResult(hypothesis, "ENDPOINT_END_OF_SPEECH");
                    updateLastResult(hypothesis);
                } else {
                    Log.v(TAG, "Skipping duplicate/similar result: " + hypothesis);
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Unexpected error during clean parsing", e);
            callback.onParseError("Clean parse error: " + e.getMessage(), responseData);
        }
    }


    
    /**
     * 验证protobuf数据是否有效
     */
    public static boolean isValidSodaResponse(byte[] data) {
        if (data == null || data.length == 0) {
            return false;
        }
        
        try {
            CodedInputStream input = CodedInputStream.newInstance(data);
            
            // 尝试读取第一个字段
            if (!input.isAtEnd()) {
                int tag = input.readTag();
                int fieldNumber = WireFormat.getTagFieldNumber(tag);
                
                // 第一个字段应该是soda_type (field 1)
                return fieldNumber == 1;
            }
            
            return false;
        } catch (Exception e) {
            return false;
        }
    }
    




    /**
     * 基于真正的Android SODA proto定义解析
     * 参考从libsoda.so提取的recognizer.proto
     */
    private static String parseAndroidSodaData(byte[] data) {
        try {
            // 尝试解析为RecognitionEvent
            return parseRecognitionEvent(data);
        } catch (Exception e) {
            Log.w(TAG, "RecognitionEvent parsing failed: " + e.getMessage());

            // 如果直接解析失败，可能是其他类型的事件，跳过
            if (data.length < 1000) {
                Log.v(TAG, "Skipping small data packet: " + data.length + " bytes");
                return null;
            }

            // 对于大数据包，尝试字节扫描作为备用
            return scanForRecognitionText(data);
        }
    }

    /**
     * 解析RecognitionEvent - 基于真正的proto定义
     */
    private static String parseRecognitionEvent(byte[] data) throws Exception {
        CodedInputStream input = CodedInputStream.newInstance(data);

        int eventType = -1;
        String hypothesis = null;

        while (!input.isAtEnd()) {
            int tag = input.readTag();
            int fieldNumber = WireFormat.getTagFieldNumber(tag);
            int wireType = WireFormat.getTagWireType(tag);

            Log.v(TAG, "RecognitionEvent field " + fieldNumber + ", wire type " + wireType);

            switch (fieldNumber) {
                case 1: // event_type
                    if (wireType == WireFormat.WIRETYPE_VARINT) {
                        eventType = input.readInt32();
                        Log.d(TAG, "Event type: " + eventType + " (" + getEventTypeName(eventType) + ")");
                    } else {
                        input.skipField(tag);
                    }
                    break;

                case 3: // result (RecognitionResult)
                    if (wireType == WireFormat.WIRETYPE_LENGTH_DELIMITED) {
                        int length = input.readRawVarint32();
                        byte[] resultBytes = input.readRawBytes(length);
                        Log.d(TAG, "Found RecognitionResult, length: " + length);
                        hypothesis = parseRecognitionResult(resultBytes);
                    } else {
                        input.skipField(tag);
                    }
                    break;

                case 4: // partial_result (PartialResult)
                    if (wireType == WireFormat.WIRETYPE_LENGTH_DELIMITED) {
                        int length = input.readRawVarint32();
                        byte[] partialBytes = input.readRawBytes(length);
                        Log.d(TAG, "Found PartialResult, length: " + length);
                        String partialText = parsePartialResult(partialBytes);
                        if (partialText != null) {
                            hypothesis = partialText;
                        }
                    } else {
                        input.skipField(tag);
                    }
                    break;

                case 7: // prefetch_result (RecognitionResult)
                    if (wireType == WireFormat.WIRETYPE_LENGTH_DELIMITED) {
                        int length = input.readRawVarint32();
                        byte[] prefetchBytes = input.readRawBytes(length);
                        Log.d(TAG, "Found PrefetchResult, length: " + length);
                        String prefetchText = parseRecognitionResult(prefetchBytes);
                        if (prefetchText != null) {
                            hypothesis = prefetchText;
                        }
                    } else {
                        input.skipField(tag);
                    }
                    break;

                default:
                    input.skipField(tag);
                    break;
            }
        }

        if (hypothesis != null) {
            Log.i(TAG, "✅ Found hypothesis from " + getEventTypeName(eventType) + ": " + hypothesis);
        }

        return hypothesis;
    }

    /**
     * 解析RecognitionResult
     */
    private static String parseRecognitionResult(byte[] data) throws Exception {
        CodedInputStream input = CodedInputStream.newInstance(data);

        while (!input.isAtEnd()) {
            int tag = input.readTag();
            int fieldNumber = WireFormat.getTagFieldNumber(tag);
            int wireType = WireFormat.getTagWireType(tag);

            if (fieldNumber == 3 && wireType == WireFormat.WIRETYPE_LENGTH_DELIMITED) {
                // hypothesis field (repeated Hypothesis)
                int length = input.readRawVarint32();
                byte[] hypothesisBytes = input.readRawBytes(length);
                Log.d(TAG, "Found Hypothesis, length: " + length);

                String text = parseHypothesis(hypothesisBytes);
                if (text != null) {
                    return text;
                }
            } else {
                input.skipField(tag);
            }
        }

        return null;
    }

    /**
     * 解析PartialResult
     */
    private static String parsePartialResult(byte[] data) throws Exception {
        CodedInputStream input = CodedInputStream.newInstance(data);

        while (!input.isAtEnd()) {
            int tag = input.readTag();
            int fieldNumber = WireFormat.getTagFieldNumber(tag);
            int wireType = WireFormat.getTagWireType(tag);

            if (fieldNumber == 1 && wireType == WireFormat.WIRETYPE_LENGTH_DELIMITED) {
                // part field (repeated PartialPart)
                int length = input.readRawVarint32();
                byte[] partBytes = input.readRawBytes(length);
                Log.d(TAG, "Found PartialPart, length: " + length);

                String text = parsePartialPart(partBytes);
                if (text != null) {
                    return text;
                }
            } else {
                input.skipField(tag);
            }
        }

        return null;
    }

    /**
     * 解析Hypothesis
     */
    private static String parseHypothesis(byte[] data) throws Exception {
        CodedInputStream input = CodedInputStream.newInstance(data);

        while (!input.isAtEnd()) {
            int tag = input.readTag();
            int fieldNumber = WireFormat.getTagFieldNumber(tag);
            int wireType = WireFormat.getTagWireType(tag);

            if (fieldNumber == 1 && wireType == WireFormat.WIRETYPE_LENGTH_DELIMITED) {
                // text field
                String text = input.readStringRequireUtf8();
                Log.i(TAG, "✅ Found hypothesis text: " + text);
                return text;
            } else {
                input.skipField(tag);
            }
        }

        return null;
    }

    /**
     * 解析PartialPart
     */
    private static String parsePartialPart(byte[] data) throws Exception {
        CodedInputStream input = CodedInputStream.newInstance(data);

        while (!input.isAtEnd()) {
            int tag = input.readTag();
            int fieldNumber = WireFormat.getTagFieldNumber(tag);
            int wireType = WireFormat.getTagWireType(tag);

            if (fieldNumber == 1 && wireType == WireFormat.WIRETYPE_LENGTH_DELIMITED) {
                // text field
                String text = input.readStringRequireUtf8();
                Log.i(TAG, "✅ Found partial text: " + text);
                return text;
            } else {
                input.skipField(tag);
            }
        }

        return null;
    }

    /**
     * 判断是否应该输出这个结果（去重逻辑）
     */
    private static boolean shouldOutputResult(String hypothesis) {
        if (hypothesis == null || hypothesis.trim().isEmpty()) {
            return false;
        }

        long currentTime = System.currentTimeMillis();

        // 如果与上一个结果完全相同，跳过
        if (hypothesis.equals(lastFinalResult)) {
            return false;
        }

        // 如果当前结果是上一个结果的前缀（部分结果），跳过
        if (lastFinalResult.startsWith(hypothesis) && currentTime - lastResultTime < 2000) {
            return false;
        }

        // 如果上一个结果是当前结果的前缀，且时间很近，说明这是完整版本
        if (hypothesis.startsWith(lastFinalResult) && currentTime - lastResultTime < 2000) {
            return true; // 输出更完整的版本
        }

        // 如果时间间隔太短（小于500ms），可能是重复结果
        if (currentTime - lastResultTime < 500 && isSimilar(hypothesis, lastFinalResult)) {
            return false;
        }

        return true;
    }

    /**
     * 更新最后的结果
     */
    private static void updateLastResult(String hypothesis) {
        lastFinalResult = hypothesis;
        lastResultTime = System.currentTimeMillis();
    }

    /**
     * 判断两个字符串是否相似
     */
    private static boolean isSimilar(String s1, String s2) {
        if (s1 == null || s2 == null) {
            return false;
        }

        // 简单的相似度判断：如果一个是另一个的子串，认为相似
        return s1.contains(s2) || s2.contains(s1);
    }

    /**
     * 获取事件类型名称
     */
    private static String getEventTypeName(int eventType) {
        switch (eventType) {
            case 0: return "RECOGNITION_RESULT";
            case 1: return "RECOGNITION_COMPLETED";
            case 2: return "RECOGNITION_PRELIMINARY_RESULT";
            case 3: return "RECOGNITION_SUBMIT_RESULT";
            default: return "UNKNOWN_EVENT_TYPE_" + eventType;
        }
    }

    /**
     * 扫描字节数据查找识别文本
     * 专门针对Android SODA数据优化
     */
    private static String scanForRecognitionText(byte[] data) {
        java.util.List<String> candidates = new java.util.ArrayList<>();

        // 使用滑动窗口查找连续的文本段
        for (int start = 0; start < data.length - 20; start++) {
            StringBuilder segment = new StringBuilder();
            int validChars = 0;

            // 从当前位置开始扫描
            for (int i = start; i < Math.min(start + 500, data.length); i++) {
                byte b = data[i];

                if (b >= 32 && b <= 126) { // ASCII可打印字符
                    segment.append((char) b);
                    if (Character.isLetter((char) b)) {
                        validChars++;
                    }
                } else if (b == 0 || b < 32) {
                    // 遇到控制字符，结束当前段
                    break;
                } else {
                    // 可能是UTF-8字符，继续
                    segment.append((char) (b & 0xFF));
                }
            }

            // 检查这个段是否像识别结果
            String text = segment.toString().trim();
            if (text.length() > 30 && validChars > text.length() * 0.7) {
                // 检查是否包含常见的英文单词模式
                if (containsCommonWords(text)) {
                    candidates.add(text);
                    Log.d(TAG, "Found candidate text at offset " + start + ": " + text.substring(0, Math.min(50, text.length())) + "...");
                }
            }
        }

        // 选择最佳候选
        if (!candidates.isEmpty()) {
            String best = selectBestCandidate(candidates);
            Log.i(TAG, "Selected best recognition text: " + best);
            return best;
        }

        return null;
    }

    /**
     * 检查文本是否包含常见单词
     */
    private static boolean containsCommonWords(String text) {
        String lower = text.toLowerCase();
        String[] commonWords = {"the", "and", "of", "in", "to", "a", "is", "was", "that", "for", "with", "as", "on", "are", "have", "had"};

        int wordCount = 0;
        for (String word : commonWords) {
            if (lower.contains(" " + word + " ") || lower.startsWith(word + " ") || lower.endsWith(" " + word)) {
                wordCount++;
            }
        }

        return wordCount >= 2; // 至少包含2个常见单词
    }

    /**
     * 从候选文本中选择最佳的
     */
    private static String selectBestCandidate(java.util.List<String> candidates) {
        String best = null;
        int bestScore = 0;

        for (String candidate : candidates) {
            int score = calculateAndroidTextScore(candidate);
            if (score > bestScore) {
                bestScore = score;
                best = candidate;
            }
        }

        return best;
    }

    /**
     * 计算Android文本质量分数
     */
    private static int calculateAndroidTextScore(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        int score = 0;

        // 长度分数
        score += Math.min(text.length() / 10, 30);

        // 单词数量分数
        String[] words = text.split("\\s+");
        score += Math.min(words.length * 3, 40);

        // 常见单词奖励
        if (containsCommonWords(text)) {
            score += 20;
        }

        // 惩罚特殊字符
        int specialChars = 0;
        for (char c : text.toCharArray()) {
            if (c < 32 || c > 126) {
                specialChars++;
            }
        }
        score -= specialChars * 2;

        // 奖励完整句子
        if (text.contains(".") || text.length() > 100) {
            score += 15;
        }

        return Math.max(0, score);
    }

    /**
     * 灵活的protobuf解析 - 从任何字符串字段中查找hypothesis（备用方法）
     */
    private static String parseProtobufFlexible(byte[] data) {
        try {
            CodedInputStream input = CodedInputStream.newInstance(data);
            java.util.List<String> allStrings = new java.util.ArrayList<>();
            int sodaType = -1;

            // 解析SodaResponse
            while (!input.isAtEnd()) {
                try {
                    int tag = input.readTag();
                    int fieldNumber = WireFormat.getTagFieldNumber(tag);
                    int wireType = WireFormat.getTagWireType(tag);

                    Log.v(TAG, "SodaResponse field " + fieldNumber + ", wire type " + wireType);

                    // 检查wire type是否有效
                    if (wireType < 0 || wireType > 5) {
                        Log.w(TAG, "Invalid wire type " + wireType + " for field " + fieldNumber + ", stopping");
                        break;
                    }

                    if (fieldNumber == 1 && wireType == WireFormat.WIRETYPE_VARINT) {
                        // soda_type字段
                        sodaType = input.readInt32();
                        Log.d(TAG, "SODA Type: " + sodaType + " (" + getSodaTypeName(sodaType) + ")");

                    } else if (fieldNumber == 2 && wireType == WireFormat.WIRETYPE_LENGTH_DELIMITED) {
                        // recognition_result字段
                        int length = input.readRawVarint32();
                        byte[] messageBytes = input.readRawBytes(length);
                        Log.d(TAG, "Found recognition_result, length: " + length);

                        // 解析recognition_result内部
                        String hypothesis = parseRecognitionResultFlexible(messageBytes);
                        if (hypothesis != null) {
                            return hypothesis;
                        }

                    } else if (wireType == WireFormat.WIRETYPE_LENGTH_DELIMITED) {
                        // 尝试读取任何字符串字段
                        try {
                            String str = input.readStringRequireUtf8();
                            if (str != null && str.length() > 10) {
                                allStrings.add(str);
                                Log.d(TAG, "Found string in field " + fieldNumber + ": " + str.substring(0, Math.min(50, str.length())));
                            }
                        } catch (Exception e) {
                            input.skipField(tag);
                        }
                    } else {
                        input.skipField(tag);
                    }
                } catch (Exception e) {
                    Log.w(TAG, "Error parsing SodaResponse field: " + e.getMessage());
                    break;
                }
            }

            // 根据SODA类型判断是否应该包含识别结果
            if (sodaType == 1) { // RECOGNITION
                Log.d(TAG, "This is a RECOGNITION message, but no hypothesis found");
            } else if (sodaType == 3) { // AUDIO_LEVEL
                Log.v(TAG, "This is an AUDIO_LEVEL message, skipping");
                return null;
            } else if (sodaType == 2) { // ENDPOINT
                Log.v(TAG, "This is an ENDPOINT message, skipping");
                return null;
            } else if (sodaType == 4) { // LANGID
                Log.v(TAG, "This is a LANGID message, skipping");
                return null;
            } else if (sodaType == 0) { // UNKNOWN
                Log.v(TAG, "This is an UNKNOWN type message, skipping");
                return null; // 跳过UNKNOWN类型的消息
            }

            // 如果没有从recognition_result找到，返回最长的字符串
            if (!allStrings.isEmpty()) {
                String longest = allStrings.get(0);
                for (String s : allStrings) {
                    if (s.length() > longest.length()) {
                        longest = s;
                    }
                }
                Log.d(TAG, "Using longest string as hypothesis: " + longest);
                return longest;
            }

        } catch (Exception e) {
            Log.w(TAG, "Flexible protobuf parsing failed: " + e.getMessage());
        }

        return null;
    }

    /**
     * 获取SODA类型名称
     */
    private static String getSodaTypeName(int type) {
        switch (type) {
            case 0: return "UNKNOWN";
            case 1: return "RECOGNITION";
            case 2: return "ENDPOINT";
            case 3: return "AUDIO_LEVEL";
            case 4: return "LANGID";
            default: return "TYPE_" + type;
        }
    }

    /**
     * 灵活解析RecognitionResult - 从任何字符串字段查找hypothesis
     */
    private static String parseRecognitionResultFlexible(byte[] data) {
        try {
            CodedInputStream input = CodedInputStream.newInstance(data);
            java.util.List<String> candidates = new java.util.ArrayList<>();

            while (!input.isAtEnd()) {
                try {
                    int tag = input.readTag();
                    int fieldNumber = WireFormat.getTagFieldNumber(tag);
                    int wireType = WireFormat.getTagWireType(tag);

                    Log.v(TAG, "RecognitionResult field " + fieldNumber + ", wire type " + wireType);

                    // 检查字段编号是否合理（避免解析损坏的数据）
                    if (fieldNumber > 1000) {
                        Log.w(TAG, "Suspicious field number " + fieldNumber + ", stopping parsing");
                        break;
                    }

                    if (wireType == WireFormat.WIRETYPE_LENGTH_DELIMITED) {
                        try {
                            String str = input.readStringRequireUtf8();
                            if (str != null && str.length() > 5) {
                                candidates.add(str);
                                Log.d(TAG, "Found string in RecognitionResult field " + fieldNumber + ": " + str.substring(0, Math.min(50, str.length())));
                            }
                        } catch (Exception e) {
                            // 如果不是字符串，跳过这个字段
                            Log.v(TAG, "Field " + fieldNumber + " is not a valid string, skipping");
                            input.skipField(tag);
                        }
                    } else if (wireType >= 0 && wireType <= 5) {
                        // 只处理有效的wire type
                        input.skipField(tag);
                    } else {
                        // 无效的wire type，可能数据损坏
                        Log.w(TAG, "Invalid wire type " + wireType + " for field " + fieldNumber + ", stopping");
                        break;
                    }
                } catch (Exception e) {
                    Log.w(TAG, "Error parsing field: " + e.getMessage() + ", trying to continue");
                    // 尝试跳过一些字节并继续
                    try {
                        if (input.getBytesUntilLimit() > 0) {
                            input.readRawByte();
                        } else {
                            break;
                        }
                    } catch (Exception e2) {
                        Log.w(TAG, "Cannot recover from parsing error, stopping");
                        break;
                    }
                }
            }

            // 返回最长的候选字符串
            if (!candidates.isEmpty()) {
                String best = candidates.get(0);
                for (String candidate : candidates) {
                    if (candidate.length() > best.length()) {
                        best = candidate;
                    }
                }
                Log.i(TAG, "Selected best hypothesis: " + best);
                return best;
            }

        } catch (Exception e) {
            Log.w(TAG, "RecognitionResult flexible parsing failed: " + e.getMessage());
        }

        return null;
    }

    /**
     * 简单文本提取方法 - 类似Python版本，不区分语言（备用方法）
     */
    private static String extractTextSimple(byte[] data) {
        if (data == null || data.length == 0) {
            return null;
        }

        Log.d(TAG, "Simple text extraction from " + data.length + " bytes");

        // 查找所有连续的文本段
        java.util.List<String> candidates = new java.util.ArrayList<>();
        StringBuilder current = new StringBuilder();

        for (int i = 0; i < data.length; i++) {
            byte b = data[i];

            // 接受所有可打印字符，包括中文字符的UTF-8编码
            if (b >= 32 && b <= 126) { // ASCII可打印字符
                current.append((char) b);
            } else if ((b & 0x80) != 0) { // 可能是UTF-8多字节字符的一部分
                // 尝试解析UTF-8字符
                try {
                    // 简单处理：如果是UTF-8字符，添加到当前段
                    current.append((char) (b & 0xFF));
                } catch (Exception e) {
                    // 如果不是有效字符，结束当前段
                    if (current.length() > 10) {
                        String candidate = current.toString().trim();
                        if (!candidate.isEmpty()) {
                            candidates.add(candidate);
                        }
                    }
                    current.setLength(0);
                }
            } else {
                // 遇到控制字符，结束当前段
                if (current.length() > 10) {
                    String candidate = current.toString().trim();
                    if (!candidate.isEmpty()) {
                        candidates.add(candidate);
                    }
                }
                current.setLength(0);
            }
        }

        // 处理最后一段
        if (current.length() > 10) {
            String candidate = current.toString().trim();
            if (!candidate.isEmpty()) {
                candidates.add(candidate);
            }
        }

        // 找到最长的候选文本
        String longest = null;
        int maxLength = 0;

        for (String candidate : candidates) {
            Log.d(TAG, "Text candidate: " + candidate.substring(0, Math.min(50, candidate.length())) + "...");
            if (candidate.length() > maxLength) {
                maxLength = candidate.length();
                longest = candidate;
            }
        }

        if (longest != null && longest.length() > 15) {
            Log.i(TAG, "Selected longest text: " + longest);
            return longest;
        }

        return null;
    }

    /**
     * 智能文本提取方法 - 查找最长的连续有意义文本（备用方法）
     */
    private static String extractTextFromBytesImproved(byte[] data) {
        if (data == null || data.length == 0) {
            return null;
        }

        Log.d(TAG, "Extracting text from " + data.length + " bytes using smart method");

        // 查找所有可能的文本段
        java.util.List<String> textSegments = new java.util.ArrayList<>();
        StringBuilder currentSegment = new StringBuilder();

        for (int i = 0; i < data.length; i++) {
            byte b = data[i];

            // 只处理可打印的ASCII字符
            if (b >= 32 && b <= 126) {
                currentSegment.append((char) b);
            } else {
                // 遇到非可打印字符时，处理当前段
                if (currentSegment.length() > 20) { // 只考虑较长的段
                    String segment = currentSegment.toString().trim();
                    if (isLikelyRecognitionText(segment)) {
                        textSegments.add(segment);
                        Log.d(TAG, "Found text segment: " + segment.substring(0, Math.min(50, segment.length())) + "...");
                    }
                }
                currentSegment.setLength(0);
            }
        }

        // 处理最后一个段
        if (currentSegment.length() > 20) {
            String segment = currentSegment.toString().trim();
            if (isLikelyRecognitionText(segment)) {
                textSegments.add(segment);
                Log.d(TAG, "Found final text segment: " + segment.substring(0, Math.min(50, segment.length())) + "...");
            }
        }

        // 找到最长且最像语音识别结果的文本段
        String bestText = null;
        int bestScore = 0;

        for (String segment : textSegments) {
            int score = calculateTextQualityScore(segment);
            Log.d(TAG, "Text segment score: " + score + " for: " + segment.substring(0, Math.min(30, segment.length())) + "...");

            if (score > bestScore) {
                bestScore = score;
                bestText = segment;
            }
        }

        if (bestText != null && bestScore > 50) { // 只返回高质量的文本
            Log.i(TAG, "✅ Selected best text (score: " + bestScore + "): " + bestText);
            return bestText;
        } else {
            Log.w(TAG, "No high-quality text found (best score: " + bestScore + ")");
            return null;
        }
    }

    /**
     * 检查文本是否像语音识别结果（支持中英文）
     */
    private static boolean isLikelyRecognitionText(String text) {
        if (text == null || text.length() < 10) { // 中文文本可能更短
            return false;
        }

        // 过滤掉明显不是语音识别的文本
        String lower = text.toLowerCase();

        // 排除包含特殊符号或格式的文本
        if (text.contains("%") || text.contains("\\x") || text.contains("??") ||
            lower.contains("config") || lower.contains("proto") || lower.contains("soda") ||
            lower.contains("http") || lower.contains("file://") ||
            text.startsWith("<") || text.endsWith(">")) {
            return false;
        }

        // 检查字符类型分布
        int chineseChars = 0;
        int englishChars = 0;
        int totalChars = 0;

        for (char c : text.toCharArray()) {
            if (!Character.isWhitespace(c) && !Character.isDigit(c)) {
                totalChars++;
                if (isChinese(c)) {
                    chineseChars++;
                } else if (Character.isLetter(c)) {
                    englishChars++;
                }
            }
        }

        if (totalChars == 0) {
            return false;
        }

        // 计算中文和英文字符的比例
        int chineseRatio = (chineseChars * 100) / totalChars;
        int englishRatio = (englishChars * 100) / totalChars;

        // 如果主要是中文（>50%）或主要是英文（>50%），则认为是有效文本
        if (chineseRatio > 50 || englishRatio > 50) {
            Log.d(TAG, "Text analysis - Chinese: " + chineseRatio + "%, English: " + englishRatio + "%, Total chars: " + totalChars);
            return true;
        }

        return false;
    }

    /**
     * 判断字符是否为中文字符
     */
    private static boolean isChinese(char c) {
        // 中文字符的Unicode范围
        return (c >= 0x4E00 && c <= 0x9FFF) ||  // CJK统一汉字
               (c >= 0x3400 && c <= 0x4DBF) ||  // CJK扩展A
               (c >= 0x20000 && c <= 0x2A6DF) || // CJK扩展B
               (c >= 0x2A700 && c <= 0x2B73F) || // CJK扩展C
               (c >= 0x2B740 && c <= 0x2B81F) || // CJK扩展D
               (c >= 0x3000 && c <= 0x303F) ||  // CJK符号和标点
               (c >= 0xFF00 && c <= 0xFFEF);    // 全角ASCII、全角标点
    }

    /**
     * 计算文本质量分数（支持中英文）
     */
    private static int calculateTextQualityScore(String text) {
        if (text == null || text.isEmpty()) {
            return 0;
        }

        int score = 0;

        // 长度分数（更长的文本通常更好）
        score += Math.min(text.length() / 5, 50); // 中文文本通常更短，调整系数

        // 字符类型分析
        int chineseChars = 0;
        int englishChars = 0;
        int totalChars = 0;
        int specialChars = 0;

        for (char c : text.toCharArray()) {
            if (!Character.isWhitespace(c)) {
                totalChars++;
                if (isChinese(c)) {
                    chineseChars++;
                } else if (Character.isLetter(c)) {
                    englishChars++;
                } else if (c == '%' || c == '\\' || c == '?' || c < 32 || c > 126) {
                    specialChars++;
                }
            }
        }

        if (totalChars == 0) {
            return 0;
        }

        // 计算各种字符的比例
        int chineseRatio = (chineseChars * 100) / totalChars;
        int englishRatio = (englishChars * 100) / totalChars;
        int specialRatio = (specialChars * 100) / totalChars;

        // 中文文本分数
        if (chineseRatio > 50) {
            score += 40; // 中文文本基础分
            score += Math.min(chineseChars * 2, 30); // 中文字符数量分
            Log.d(TAG, "Chinese text detected - ratio: " + chineseRatio + "%, chars: " + chineseChars);
        }

        // 英文文本分数
        if (englishRatio > 50) {
            score += 30; // 英文文本基础分
            String[] words = text.split("\\s+");
            score += Math.min(words.length * 2, 30); // 英文单词数量分
            Log.d(TAG, "English text detected - ratio: " + englishRatio + "%, words: " + words.length);
        }

        // 惩罚特殊字符过多的文本
        if (specialRatio > 20) {
            score -= specialRatio; // 特殊字符比例越高，扣分越多
            Log.d(TAG, "High special char ratio: " + specialRatio + "%, penalty applied");
        }

        // 奖励看起来像自然语言的文本
        if (text.contains("的") || text.contains("是") || text.contains("在") || text.contains("了") ||
            text.contains(" the ") || text.contains(" and ") || text.contains(" of ") || text.contains(" in ")) {
            score += 15;
            Log.d(TAG, "Natural language patterns detected, bonus applied");
        }

        Log.d(TAG, "Text quality score: " + score + " (Chinese: " + chineseRatio + "%, English: " + englishRatio + "%, Special: " + specialRatio + "%)");
        return Math.max(0, score);
    }

    /**
     * 检查是否是有效的英文单词
     */
    private static boolean isValidEnglishWord(String word) {
        if (word == null || word.length() < 2) {
            return false;
        }

        // 过滤掉明显的非单词
        String lowerWord = word.toLowerCase();

        // 排除一些常见的非单词字符串
        if (lowerWord.startsWith("http") || lowerWord.contains("://") ||
            lowerWord.startsWith("config") || lowerWord.startsWith("model") ||
            lowerWord.startsWith("soda") || lowerWord.startsWith("proto") ||
            word.startsWith("<") || word.endsWith(">") ||
            word.contains("??") || word.contains("\\x") ||
            !word.matches(".*[a-zA-Z].*")) { // 必须包含至少一个字母
            return false;
        }

        // 检查是否主要由字母组成
        int letterCount = 0;
        int digitCount = 0;
        int otherCount = 0;

        for (char c : word.toCharArray()) {
            if (Character.isLetter(c)) {
                letterCount++;
            } else if (Character.isDigit(c)) {
                digitCount++;
            } else {
                otherCount++;
            }
        }

        // 至少70%的字符应该是字母，允许一些数字和标点
        int totalCount = letterCount + digitCount + otherCount;
        return totalCount > 0 && (letterCount * 100 / totalCount) >= 70;
    }


}
