package defpackage;

import android.content.Context;
import android.net.Uri;
import androidx.wear.ambient.AmbientMode;
import j$.util.Objects;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/* renamed from: brm  reason: default package */
/* compiled from: PG */
public final class brm implements aky {
    public static final hca a = hca.m("com/google/android/apps/speech/tts/googletts/audio/ExoPlayerDecoder");
    public static final Uri b = new Uri.Builder().scheme("bytes").authority("audio").build();
    public final air c;
    public final fzt d;
    public final ihn e;
    public final agm f;
    public int g;
    private final ScheduledExecutorService h;
    private final afk i;
    private brj j;
    private long k;
    private int l;
    private aep m = aep.a;

    public brm(Context context, fzt fzt, ScheduledExecutorService scheduledExecutorService, ihn ihn) {
        this.d = fzt;
        this.h = scheduledExecutorService;
        this.e = ihn;
        this.i = new afk();
        this.j = new brl();
        new ahd();
        this.f = new agm(context);
        aip aip = new aip(context, new bvo(this, context, 1));
        aip.b(fzt.getLooper());
        yi.h(true);
        aik.b(1, 0, "bufferForPlaybackMs", "0");
        aik.b(1, 0, "bufferForPlaybackAfterRebufferMs", "0");
        aik.b(10000, 1, "minBufferMs", "bufferForPlaybackMs");
        aik.b(10000, 1, "minBufferMs", "bufferForPlaybackAfterRebufferMs");
        aik.b(10000, 10000, "maxBufferMs", "minBufferMs");
        yi.h(true);
        aik aik = new aik(new aph(), 10000, 10000, 1, 1);
        yi.h(!aip.l);
        aip.f = new aio(aik, 1);
        this.c = aip.a();
        fzt.post(new bpu(this, 5));
        this.g = 1;
    }

    public final hme a(Uri uri, agt agt, brj brj, long j2) {
        int i2 = this.g;
        if (i2 == 0) {
            throw null;
        } else if (i2 != 1) {
            return hfc.J(new IllegalStateException("Decoder not ready to be called again yet"));
        } else {
            afk afk = this.i;
            afk.b = 1.0f;
            afk.c = 1.0f;
            afk.d = afg.a;
            afg afg = afg.a;
            afk.e = afg;
            afk.f = afg;
            afk.g = afg;
            afk.j = afk.a;
            afk.k = afk.j.asShortBuffer();
            afk.l = afk.a;
            afk.h = false;
            afk.i = null;
            afk.m = 0;
            afk.n = 0;
            this.j = brj;
            this.k = 0;
            this.g = 2;
            this.d.post(new aku((Object) this, (Object) adh.d(aeg.a(uri), new bvm(agt, 1), new AmbientMode.AmbientController(new bvn(1), (byte[]) null), new adh()), 16, (char[]) null));
            return hke.f(hjl.f(hly.q(kq.f(new bwl(this, 1))).r(j2, TimeUnit.MILLISECONDS, this.h), TimeoutException.class, new brg(this, 2), hld.a), new btb(this, brj, 1, (byte[]) null), hld.a);
        }
    }

    public final void b(iff iff, String str) {
        int i2 = this.g;
        if (i2 == 0) {
            throw null;
        } else if (i2 == 4 || i2 == 1) {
            hby hby = (hby) ((hby) a.f()).j("com/google/android/apps/speech/tts/googletts/audio/ExoPlayerDecoder", "stopDecoder", 354, "ExoPlayerDecoder.java");
            int i3 = this.g;
            String e2 = abq.e(i3);
            if (i3 != 0) {
                hby.u("Decoder is already stopped or reset, state=%s", e2);
                return;
            }
            throw null;
        } else {
            if (iff == iff.COMPOSITION_STATUS_SUCCESS || iff == iff.COMPOSITION_STATUS_END_OF_STREAM) {
                ((hby) ((hby) a.c()).j("com/google/android/apps/speech/tts/googletts/audio/ExoPlayerDecoder", "stopDecoder", 359, "ExoPlayerDecoder.java")).u("Decoder stopped with success: %s", str);
            } else {
                ((hby) ((hby) a.h()).j("com/google/android/apps/speech/tts/googletts/audio/ExoPlayerDecoder", "stopDecoder", 361, "ExoPlayerDecoder.java")).u("Decoder stopped with failure: %s", str);
            }
            int i4 = this.g;
            if (i4 != 0) {
                if (i4 != 3) {
                    ((hby) ((hby) a.f()).j("com/google/android/apps/speech/tts/googletts/audio/ExoPlayerDecoder", "stopDecoder", 366, "ExoPlayerDecoder.java")).r("Forcing start callback");
                    this.j.d();
                }
                this.g = 4;
                this.j.b(iff);
                fzt fzt = this.d;
                air air = this.c;
                Objects.requireNonNull(air);
                fzt.post(new bpu(air, 3));
                return;
            }
            throw null;
        }
    }

    public final int q(adv adv) {
        if (!"audio/raw".equals(adv.n) || adv.D != 2) {
            return 0;
        }
        return 2;
    }

    public final aep r() {
        return this.m;
    }

    public final void s() {
        int i2 = this.g;
        if (i2 == 0) {
            throw null;
        } else if (i2 == 4) {
            ((hby) ((hby) a.h()).j("com/google/android/apps/speech/tts/googletts/audio/ExoPlayerDecoder", "playToEndOfStream", 600, "ExoPlayerDecoder.java")).r("Already trying to stop, ignoring playToEndOfStream");
        } else {
            b(iff.COMPOSITION_STATUS_END_OF_STREAM, "end_of_stream");
        }
    }

    public final void t(aep aep) {
        this.m = aep;
    }

    public final boolean u() {
        boolean z;
        int i2 = this.g;
        if (i2 != 4) {
            z = false;
        } else {
            z = true;
        }
        if (i2 != 0) {
            return z;
        }
        throw null;
    }

    public final boolean v(adv adv) {
        if (q(adv) != 0) {
            return true;
        }
        return false;
    }

    public final void w(adv adv) {
        int i2 = this.g;
        if (i2 == 0) {
            throw null;
        } else if (i2 != 2) {
            hby hby = (hby) ((hby) a.h()).j("com/google/android/apps/speech/tts/googletts/audio/ExoPlayerDecoder", "configure", 508, "ExoPlayerDecoder.java");
            int i3 = this.g;
            String e2 = abq.e(i3);
            if (i3 != 0) {
                hby.u("Unexpected configure() call, state=%s", e2);
                b(iff.COMPOSITION_STATUS_ERROR, "unexpected_configure_call");
                return;
            }
            throw null;
        } else if (adv.B != 1) {
            ((hby) ((hby) a.h()).j("com/google/android/apps/speech/tts/googletts/audio/ExoPlayerDecoder", "configure", 514, "ExoPlayerDecoder.java")).r("Decoding multichannel audio not supported");
            b(iff.COMPOSITION_STATUS_INVALID_ARGUMENT_ERROR, "multichannel_audio_not_supported");
        } else {
            this.l = adv.C;
            afg afg = new afg(adv.C, adv.B, adv.D);
            afk afk = this.i;
            try {
                if (afg.d == 2) {
                    afk.d = afg;
                    afk.e = new afg(24000, afg.c, 2);
                    afk.h = true;
                    afk afk2 = this.i;
                    if (afk2.a()) {
                        afk2.f = afk2.d;
                        afk2.g = afk2.e;
                        if (afk2.h) {
                            afg afg2 = afk2.f;
                            afk2.i = new afj(afg2.b, afg2.c, afk2.b, afk2.c, afk2.g.b);
                        } else {
                            afj afj = afk2.i;
                            if (afj != null) {
                                afj.k = 0;
                                afj.m = 0;
                                afj.o = 0;
                                afj.p = 0;
                                afj.q = 0;
                                afj.r = 0;
                                afj.s = 0;
                                afj.t = 0;
                                afj.u = 0;
                                afj.v = 0;
                            }
                        }
                    }
                    afk2.l = afk.a;
                    afk2.m = 0;
                    afk2.n = 0;
                    this.j.d();
                    this.g = 3;
                    return;
                }
                throw new afh(afg);
            } catch (afh e3) {
                ((hby) ((hby) ((hby) a.g()).i(e3)).j("com/google/android/apps/speech/tts/googletts/audio/ExoPlayerDecoder", "configure", 529, "ExoPlayerDecoder.java")).r("Failed to configure SonicAudioProcessor with output format");
                b(iff.COMPOSITION_STATUS_ERROR, "sonic_configure_failed");
            }
        }
    }

    public final long x() {
        return this.k;
    }

    /* JADX WARNING: Code restructure failed: missing block: B:58:0x0178, code lost:
        if ((r3 + r3) <= (r2.t * 3)) goto L_0x017c;
     */
    /* JADX WARNING: Removed duplicated region for block: B:103:0x02e9  */
    /* JADX WARNING: Removed duplicated region for block: B:131:0x02e7 A[EDGE_INSN: B:131:0x02e7->B:102:0x02e7 ?: BREAK  , SYNTHETIC] */
    /* JADX WARNING: Removed duplicated region for block: B:87:0x0281 A[LOOP:3: B:87:0x0281->B:93:0x02c5, LOOP_START] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final boolean y(java.nio.ByteBuffer r31) {
        /*
            r30 = this;
            r0 = r30
            hca r1 = a
            hco r2 = r1.c()
            hby r2 = (defpackage.hby) r2
            r3 = 549(0x225, float:7.7E-43)
            java.lang.String r4 = "com/google/android/apps/speech/tts/googletts/audio/ExoPlayerDecoder"
            java.lang.String r5 = "handleBuffer"
            java.lang.String r6 = "ExoPlayerDecoder.java"
            hco r2 = r2.j(r4, r5, r3, r6)
            hby r2 = (defpackage.hby) r2
            java.lang.String r3 = "Decoder got new buffer of %d bytes"
            int r7 = r31.remaining()
            r2.s(r3, r7)
            int r2 = r0.g
            if (r2 == 0) goto L_0x03ec
            r3 = 4
            r7 = 0
            if (r2 == r3) goto L_0x03d7
            r3 = 1
            if (r2 != r3) goto L_0x002e
            goto L_0x03d7
        L_0x002e:
            r8 = 3
            if (r2 == r8) goto L_0x004c
            hco r1 = r1.h()
            hby r1 = (defpackage.hby) r1
            r2 = 560(0x230, float:7.85E-43)
            hco r1 = r1.j(r4, r5, r2, r6)
            hby r1 = (defpackage.hby) r1
            java.lang.String r2 = "Sink configure failed, sink cannot handle buffers, stopping"
            r1.r(r2)
            iff r1 = defpackage.iff.COMPOSITION_STATUS_ERROR
            java.lang.String r2 = "buffer_after_start_failed"
            r0.b(r1, r2)
            return r7
        L_0x004c:
            long r9 = r0.k
            int r2 = r31.remaining()
            r11 = 1000000(0xf4240, float:1.401298E-39)
            int r2 = r2 * r11
            int r11 = r0.l
            int r11 = r11 + r11
            int r2 = r2 / r11
            long r11 = (long) r2
            long r9 = r9 + r11
            r0.k = r9
            r11 = 10000000(0x989680, double:4.9406565E-317)
            int r2 = (r9 > r11 ? 1 : (r9 == r11 ? 0 : -1))
            if (r2 <= 0) goto L_0x006d
            iff r1 = defpackage.iff.COMPOSITION_STATUS_INVALID_ARGUMENT_ERROR
            java.lang.String r2 = "max_audio_duration_exceeded"
            r0.b(r1, r2)
            return r7
        L_0x006d:
            afk r2 = r0.i
            boolean r2 = r2.a()
            if (r2 == 0) goto L_0x0391
            afk r1 = r0.i
            boolean r2 = r31.hasRemaining()
            if (r2 != 0) goto L_0x0083
            r25 = r4
            r26 = r5
            goto L_0x030b
        L_0x0083:
            afj r2 = r1.i
            defpackage.yi.k(r2)
            java.nio.ShortBuffer r9 = r31.asShortBuffer()
            int r10 = r31.remaining()
            long r11 = r1.m
            long r13 = (long) r10
            long r11 = r11 + r13
            r1.m = r11
            int r1 = r9.remaining()
            int r11 = r2.b
            int r1 = r1 / r11
            int r11 = r11 * r1
            short[] r12 = r2.j
            int r13 = r2.k
            short[] r12 = r2.e(r12, r13, r1)
            r2.j = r12
            short[] r12 = r2.j
            int r13 = r2.k
            int r14 = r2.b
            int r13 = r13 * r14
            int r11 = r11 + r11
            int r11 = r11 / 2
            r9.get(r12, r13, r11)
            int r9 = r2.k
            int r9 = r9 + r1
            r2.k = r9
            int r11 = r2.m
            float r1 = r2.c
            float r12 = r2.d
            float r12 = r1 / r12
            double r13 = (double) r12
            r15 = 4607182463836013682(0x3ff0000a7c5ac472, double:1.00001)
            int r1 = (r13 > r15 ? 1 : (r13 == r15 ? 0 : -1))
            if (r1 > 0) goto L_0x00de
            r16 = 4607182328728024861(0x3fefffeb074a771d, double:0.99999)
            int r1 = (r13 > r16 ? 1 : (r13 == r16 ? 0 : -1))
            if (r1 >= 0) goto L_0x00d6
            goto L_0x00de
        L_0x00d6:
            short[] r1 = r2.j
            r2.b(r1, r7, r9)
            r2.k = r7
            goto L_0x00e2
        L_0x00de:
            int r1 = r2.h
            if (r9 >= r1) goto L_0x00e8
        L_0x00e2:
            r25 = r4
            r26 = r5
            goto L_0x0239
        L_0x00e8:
            r1 = r7
        L_0x00e9:
            int r15 = r2.r
            if (r15 <= 0) goto L_0x0108
            int r8 = r2.h
            int r8 = java.lang.Math.min(r8, r15)
            short[] r15 = r2.j
            r2.b(r15, r1, r8)
            int r15 = r2.r
            int r15 = r15 - r8
            r2.r = r15
            int r1 = r1 + r8
            r25 = r4
            r26 = r5
            r29 = r12
            r27 = r13
            goto L_0x0225
        L_0x0108:
            short[] r8 = r2.j
            int r15 = r2.a
            r7 = 4000(0xfa0, float:5.605E-42)
            if (r15 <= r7) goto L_0x0113
            int r15 = r15 / 4000
            goto L_0x0114
        L_0x0113:
            r15 = r3
        L_0x0114:
            int r7 = r2.b
            if (r7 != r3) goto L_0x0127
            if (r15 != r3) goto L_0x0127
            int r7 = r2.f
            int r15 = r2.g
            int r7 = r2.a(r8, r1, r7, r15)
            r25 = r4
            r26 = r5
            goto L_0x0162
        L_0x0127:
            r2.c(r8, r1, r15)
            short[] r7 = r2.i
            int r3 = r2.f
            r25 = r4
            int r4 = r2.g
            int r4 = r4 / r15
            int r3 = r3 / r15
            r26 = r5
            r5 = 0
            int r7 = r2.a(r7, r5, r3, r4)
            r3 = 1
            if (r15 == r3) goto L_0x0162
            int r7 = r7 * r15
            int r15 = r15 * 4
            int r3 = r2.f
            int r4 = r7 - r15
            if (r4 < r3) goto L_0x0148
            r3 = r4
        L_0x0148:
            int r7 = r7 + r15
            int r4 = r2.g
            if (r7 <= r4) goto L_0x014e
            r7 = r4
        L_0x014e:
            int r4 = r2.b
            r5 = 1
            if (r4 != r5) goto L_0x0158
            int r7 = r2.a(r8, r1, r3, r7)
            goto L_0x0162
        L_0x0158:
            r2.c(r8, r1, r5)
            short[] r4 = r2.i
            r5 = 0
            int r7 = r2.a(r4, r5, r3, r7)
        L_0x0162:
            int r3 = r2.u
            int r4 = r2.v
            if (r3 == 0) goto L_0x017b
            int r5 = r2.s
            if (r5 != 0) goto L_0x016d
            goto L_0x017b
        L_0x016d:
            int r8 = r3 * 3
            if (r4 <= r8) goto L_0x0172
            goto L_0x017b
        L_0x0172:
            int r4 = r3 + r3
            int r8 = r2.t
            r15 = 3
            int r8 = r8 * r15
            if (r4 > r8) goto L_0x017d
            goto L_0x017c
        L_0x017b:
            r15 = 3
        L_0x017c:
            r5 = r7
        L_0x017d:
            int r4 = r1 + r5
            r2.t = r3
            r2.s = r7
            r7 = 4607182418800017408(0x3ff0000000000000, double:1.0)
            int r3 = (r13 > r7 ? 1 : (r13 == r7 ? 0 : -1))
            float r7 = (float) r5
            r8 = -1082130432(0xffffffffbf800000, float:-1.0)
            if (r3 <= 0) goto L_0x01d0
            short[] r3 = r2.j
            float r8 = r8 + r12
            r16 = 1073741824(0x40000000, float:2.0)
            int r17 = (r12 > r16 ? 1 : (r12 == r16 ? 0 : -1))
            if (r17 < 0) goto L_0x0198
            float r7 = r7 / r8
            int r7 = (int) r7
            goto L_0x01a1
        L_0x0198:
            float r16 = r16 - r12
            float r7 = r7 * r16
            float r7 = r7 / r8
            int r7 = (int) r7
            r2.r = r7
            r7 = r5
        L_0x01a1:
            short[] r8 = r2.l
            int r15 = r2.m
            short[] r8 = r2.e(r8, r15, r7)
            r2.l = r8
            int r8 = r2.b
            short[] r15 = r2.l
            r27 = r13
            int r13 = r2.m
            r16 = r7
            r17 = r8
            r18 = r15
            r19 = r13
            r20 = r3
            r21 = r1
            r22 = r3
            r23 = r4
            defpackage.afj.d(r16, r17, r18, r19, r20, r21, r22, r23)
            int r3 = r2.m
            int r3 = r3 + r7
            r2.m = r3
            int r5 = r5 + r7
            int r1 = r1 + r5
            r29 = r12
            goto L_0x0225
        L_0x01d0:
            r27 = r13
            short[] r3 = r2.j
            r13 = 1065353216(0x3f800000, float:1.0)
            float r15 = r13 - r12
            r13 = 1056964608(0x3f000000, float:0.5)
            int r13 = (r12 > r13 ? 1 : (r12 == r13 ? 0 : -1))
            if (r13 >= 0) goto L_0x01e2
            float r7 = r7 * r12
            float r7 = r7 / r15
            int r7 = (int) r7
            goto L_0x01eb
        L_0x01e2:
            float r13 = r12 + r12
            float r13 = r13 + r8
            float r7 = r7 * r13
            float r7 = r7 / r15
            int r7 = (int) r7
            r2.r = r7
            r7 = r5
        L_0x01eb:
            short[] r8 = r2.l
            int r13 = r2.m
            int r14 = r5 + r7
            short[] r8 = r2.e(r8, r13, r14)
            r2.l = r8
            int r8 = r2.b
            int r13 = r1 * r8
            short[] r15 = r2.l
            r29 = r12
            int r12 = r2.m
            int r12 = r12 * r8
            int r8 = r8 * r5
            java.lang.System.arraycopy(r3, r13, r15, r12, r8)
            int r8 = r2.b
            short[] r12 = r2.l
            int r13 = r2.m
            int r19 = r13 + r5
            r16 = r7
            r17 = r8
            r18 = r12
            r20 = r3
            r21 = r4
            r22 = r3
            r23 = r1
            defpackage.afj.d(r16, r17, r18, r19, r20, r21, r22, r23)
            int r3 = r2.m
            int r3 = r3 + r14
            r2.m = r3
            int r1 = r1 + r7
        L_0x0225:
            int r3 = r2.h
            int r3 = r3 + r1
            if (r3 <= r9) goto L_0x0382
            int r3 = r2.k
            int r3 = r3 - r1
            short[] r4 = r2.j
            int r5 = r2.b
            int r1 = r1 * r5
            int r5 = r5 * r3
            r7 = 0
            java.lang.System.arraycopy(r4, r1, r4, r7, r5)
            r2.k = r3
        L_0x0239:
            float r1 = r2.d
            float r3 = r2.e
            float r3 = r3 * r1
            r4 = 1065353216(0x3f800000, float:1.0)
            int r1 = (r3 > r4 ? 1 : (r3 == r4 ? 0 : -1))
            if (r1 == 0) goto L_0x0301
            int r1 = r2.m
            if (r1 != r11) goto L_0x024a
            goto L_0x0301
        L_0x024a:
            int r1 = r2.a
            float r4 = (float) r1
            float r4 = r4 / r3
            int r3 = (int) r4
        L_0x024f:
            r4 = 16384(0x4000, float:2.2959E-41)
            if (r3 > r4) goto L_0x02fb
            if (r1 <= r4) goto L_0x0257
            goto L_0x02fb
        L_0x0257:
            int r4 = r2.m
            int r4 = r4 - r11
            short[] r5 = r2.n
            int r7 = r2.o
            short[] r5 = r2.e(r5, r7, r4)
            r2.n = r5
            short[] r5 = r2.l
            int r7 = r2.b
            int r8 = r11 * r7
            short[] r9 = r2.n
            int r12 = r2.o
            int r12 = r12 * r7
            int r7 = r7 * r4
            java.lang.System.arraycopy(r5, r8, r9, r12, r7)
            r2.m = r11
            int r5 = r2.o
            int r5 = r5 + r4
            r2.o = r5
            r4 = 0
        L_0x027b:
            int r5 = r2.o
            int r7 = r5 + -1
            if (r4 >= r7) goto L_0x02e7
        L_0x0281:
            int r5 = r2.p
            r7 = 1
            int r5 = r5 + r7
            int r8 = r5 * r3
            int r9 = r2.q
            int r11 = r9 * r1
            if (r8 <= r11) goto L_0x02d1
            short[] r5 = r2.l
            int r8 = r2.m
            short[] r5 = r2.e(r5, r8, r7)
            r2.l = r5
            r5 = 0
        L_0x0298:
            int r7 = r2.b
            if (r5 >= r7) goto L_0x02c5
            short[] r8 = r2.l
            int r9 = r2.m
            int r9 = r9 * r7
            short[] r11 = r2.n
            int r12 = r4 * r7
            int r12 = r12 + r5
            short r13 = r11[r12]
            int r12 = r12 + r7
            short r7 = r11[r12]
            int r11 = r2.q
            int r11 = r11 * r1
            int r12 = r2.p
            int r14 = r12 * r3
            r15 = 1
            int r12 = r12 + r15
            int r12 = r12 * r3
            int r11 = r12 - r11
            int r13 = r13 * r11
            int r12 = r12 - r14
            int r11 = r12 - r11
            int r11 = r11 * r7
            int r13 = r13 + r11
            int r13 = r13 / r12
            short r7 = (short) r13
            int r9 = r9 + r5
            r8[r9] = r7
            int r5 = r5 + 1
            goto L_0x0298
        L_0x02c5:
            int r5 = r2.q
            r7 = 1
            int r5 = r5 + r7
            r2.q = r5
            int r5 = r2.m
            int r5 = r5 + r7
            r2.m = r5
            goto L_0x0281
        L_0x02d1:
            r2.p = r5
            if (r5 != r1) goto L_0x02e4
            r5 = 0
            r2.p = r5
            if (r9 != r3) goto L_0x02dd
            r24 = 1
            goto L_0x02df
        L_0x02dd:
            r24 = r5
        L_0x02df:
            defpackage.yi.h(r24)
            r2.q = r5
        L_0x02e4:
            int r4 = r4 + 1
            goto L_0x027b
        L_0x02e7:
            if (r7 == 0) goto L_0x0301
            short[] r1 = r2.n
            int r3 = r2.b
            int r5 = r5 - r7
            int r4 = r7 * r3
            int r5 = r5 * r3
            r3 = 0
            java.lang.System.arraycopy(r1, r4, r1, r3, r5)
            int r1 = r2.o
            int r1 = r1 - r7
            r2.o = r1
            goto L_0x0301
        L_0x02fb:
            int r3 = r3 / 2
            int r1 = r1 / 2
            goto L_0x024f
        L_0x0301:
            int r1 = r31.position()
            int r1 = r1 + r10
            r3 = r31
            r3.position(r1)
        L_0x030b:
            afk r1 = r0.i
            afj r2 = r1.i
            if (r2 == 0) goto L_0x0377
            int r3 = r2.m
            int r4 = r2.b
            int r3 = r3 * r4
            int r3 = r3 + r3
            if (r3 <= 0) goto L_0x0377
            java.nio.ByteBuffer r4 = r1.j
            int r4 = r4.capacity()
            if (r4 >= r3) goto L_0x0338
            java.nio.ByteBuffer r4 = java.nio.ByteBuffer.allocateDirect(r3)
            java.nio.ByteOrder r5 = java.nio.ByteOrder.nativeOrder()
            java.nio.ByteBuffer r4 = r4.order(r5)
            r1.j = r4
            java.nio.ByteBuffer r4 = r1.j
            java.nio.ShortBuffer r4 = r4.asShortBuffer()
            r1.k = r4
            goto L_0x0342
        L_0x0338:
            java.nio.ByteBuffer r4 = r1.j
            r4.clear()
            java.nio.ShortBuffer r4 = r1.k
            r4.clear()
        L_0x0342:
            java.nio.ShortBuffer r4 = r1.k
            int r5 = r4.remaining()
            int r7 = r2.b
            int r5 = r5 / r7
            int r7 = r2.m
            int r5 = java.lang.Math.min(r5, r7)
            short[] r7 = r2.l
            int r8 = r2.b
            int r8 = r8 * r5
            r9 = 0
            r4.put(r7, r9, r8)
            int r4 = r2.m
            int r4 = r4 - r5
            r2.m = r4
            short[] r7 = r2.l
            int r2 = r2.b
            int r5 = r5 * r2
            int r4 = r4 * r2
            java.lang.System.arraycopy(r7, r5, r7, r9, r4)
            long r4 = r1.n
            long r7 = (long) r3
            long r4 = r4 + r7
            r1.n = r4
            java.nio.ByteBuffer r2 = r1.j
            r2.limit(r3)
            java.nio.ByteBuffer r2 = r1.j
            r1.l = r2
        L_0x0377:
            java.nio.ByteBuffer r2 = r1.l
            java.nio.ByteBuffer r3 = defpackage.afk.a
            r1.l = r3
            r4 = r25
            r5 = r26
            goto L_0x03ab
        L_0x0382:
            r3 = r31
            r4 = r25
            r5 = r26
            r13 = r27
            r12 = r29
            r3 = 1
            r7 = 0
            r8 = 3
            goto L_0x00e9
        L_0x0391:
            r3 = r31
            r25 = r4
            r26 = r5
            hco r1 = r1.c()
            hby r1 = (defpackage.hby) r1
            r2 = 583(0x247, float:8.17E-43)
            hco r1 = r1.j(r4, r5, r2, r6)
            hby r1 = (defpackage.hby) r1
            java.lang.String r2 = "Decoder will not resample"
            r1.r(r2)
            r2 = r3
        L_0x03ab:
            brj r1 = r0.j
            java.nio.ByteBuffer r2 = r2.asReadOnlyBuffer()
            boolean r1 = r1.c(r2)
            if (r1 != 0) goto L_0x03d5
            hca r1 = a
            hco r1 = r1.h()
            hby r1 = (defpackage.hby) r1
            r2 = 588(0x24c, float:8.24E-43)
            hco r1 = r1.j(r4, r5, r2, r6)
            hby r1 = (defpackage.hby) r1
            java.lang.String r2 = "Decoder caller failed to process audio, stopping"
            r1.r(r2)
            iff r1 = defpackage.iff.COMPOSITION_STATUS_ERROR
            java.lang.String r2 = "audio_available_callback_failed"
            r0.b(r1, r2)
            r2 = 0
            return r2
        L_0x03d5:
            r1 = 1
            return r1
        L_0x03d7:
            r2 = r7
            hco r1 = r1.h()
            hby r1 = (defpackage.hby) r1
            r3 = 553(0x229, float:7.75E-43)
            hco r1 = r1.j(r4, r5, r3, r6)
            hby r1 = (defpackage.hby) r1
            java.lang.String r3 = "Stopped or reset sink ignoring new buffer"
            r1.r(r3)
            return r2
        L_0x03ec:
            r1 = 0
            throw r1
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.brm.y(java.nio.ByteBuffer):boolean");
    }

    public final /* synthetic */ void z() {
    }
}
