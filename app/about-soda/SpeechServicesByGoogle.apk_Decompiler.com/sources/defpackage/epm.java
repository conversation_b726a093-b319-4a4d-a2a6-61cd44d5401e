package defpackage;

/* renamed from: epm  reason: default package */
/* compiled from: PG */
final class epm {
    public final enr a;
    public final hme b;

    public epm(enr enr, hme hme) {
        this.a = enr;
        this.b = hme;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof epm)) {
            return false;
        }
        epm epm = (epm) obj;
        if (jnu.i(this.a, epm.a) && jnu.i(this.b, epm.b)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return (this.a.hashCode() * 31) + this.b.hashCode();
    }

    public final String toString() {
        return "HotwordSourceStartedData(sourceAccessor=" + this.a + ", startListeningResult=" + this.b + ")";
    }
}
