package defpackage;

/* renamed from: bez  reason: default package */
/* compiled from: PG */
public final class bez extends jmi implements jnf {
    int a;
    public /* synthetic */ Object b;
    public /* synthetic */ Object c;

    public bez(jlr jlr) {
        super(3, jlr);
    }

    /* JADX WARNING: type inference failed for: r7v2, types: [jup, java.lang.Object] */
    public final Object bk(Object obj) {
        wf wfVar;
        Object obj2 = jlx.COROUTINE_SUSPENDED;
        int i = this.a;
        jji.c(obj);
        if (i == 0) {
            ? r7 = this.b;
            wf[] wfVarArr = (wf[]) this.c;
            int length = wfVarArr.length;
            int i2 = 0;
            while (true) {
                if (i2 >= length) {
                    wfVar = null;
                    break;
                }
                wfVar = wfVarArr[i2];
                if (!jnu.i(wfVar, beq.a)) {
                    break;
                }
                i2++;
            }
            if (wfVar == null) {
                wfVar = beq.a;
            }
            this.a = 1;
            if (r7.bK(wfVar, this) == obj2) {
                return obj2;
            }
        }
        return jkd.a;
    }
}
