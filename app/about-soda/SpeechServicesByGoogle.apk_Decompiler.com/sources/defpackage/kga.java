package defpackage;

import android.util.Log;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.logging.Handler;
import java.util.logging.Level;
import java.util.logging.LogRecord;

/* renamed from: kga  reason: default package */
/* compiled from: PG */
public final class kga extends Handler {
    public static final kga a = new kga();

    private kga() {
    }

    public final void publish(LogRecord logRecord) {
        int i;
        int min;
        jnu.e(logRecord, "record");
        CopyOnWriteArraySet copyOnWriteArraySet = kfz.a;
        String loggerName = logRecord.getLoggerName();
        jnu.d(loggerName, "getLoggerName(...)");
        if (logRecord.getLevel().intValue() > Level.INFO.intValue()) {
            i = 5;
        } else if (logRecord.getLevel().intValue() == Level.INFO.intValue()) {
            i = 4;
        } else {
            i = 3;
        }
        String message = logRecord.getMessage();
        jnu.d(message, "getMessage(...)");
        Throwable thrown = logRecord.getThrown();
        jnu.e(loggerName, "loggerName");
        jnu.e(message, "message");
        String str = (String) kfz.b.get(loggerName);
        if (str == null) {
            str = job.q(loggerName, 23);
        }
        if (Log.isLoggable(str, i)) {
            if (thrown != null) {
                message = message + "\n" + Log.getStackTraceString(thrown);
            }
            int length = message.length();
            int i2 = 0;
            while (i2 < length) {
                int y = job.y(message, 10, i2, 4);
                if (y == -1) {
                    y = length;
                }
                while (true) {
                    min = Math.min(y, i2 + 4000);
                    String substring = message.substring(i2, min);
                    jnu.d(substring, "substring(...)");
                    Log.println(i, str, substring);
                    if (min >= y) {
                        break;
                    }
                    i2 = min;
                }
                i2 = min + 1;
            }
        }
    }

    public final void close() {
    }

    public final void flush() {
    }
}
