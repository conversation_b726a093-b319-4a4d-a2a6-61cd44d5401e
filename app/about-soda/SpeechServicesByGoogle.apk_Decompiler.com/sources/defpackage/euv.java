package defpackage;

/* renamed from: euv  reason: default package */
/* compiled from: PG */
public final class euv {
    public static final eut a = new eut();
    public final boolean b;
    public final byw c;

    public euv(byw byw, boolean z) {
        jnu.e(byw, "audioFileWriterFactory");
        this.c = byw;
        this.b = z;
    }

    public static final String a(String str) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < str.length(); i++) {
            char charAt = str.charAt(i);
            if (charAt != '@') {
                sb.append(charAt);
            }
        }
        return "@".concat(sb.toString());
    }
}
