package defpackage;

/* renamed from: esd  reason: default package */
/* compiled from: PG */
final class esd extends jmi implements jne {
    Object a;
    int b;
    final /* synthetic */ ese c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public esd(ese ese, jlr jlr) {
        super(2, jlr);
        this.c = ese;
    }

    public final /* bridge */ /* synthetic */ Object b(Object obj, Object obj2) {
        return ((esd) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX WARNING: Removed duplicated region for block: B:23:0x00a7  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bk(java.lang.Object r6) {
        /*
            r5 = this;
            jlx r0 = defpackage.jlx.COROUTINE_SUSPENDED
            int r1 = r5.b
            r2 = 1
            if (r1 == 0) goto L_0x0014
            if (r1 == r2) goto L_0x0010
            java.lang.Object r0 = r5.a
            defpackage.jji.c(r6)
            goto L_0x00b7
        L_0x0010:
            defpackage.jji.c(r6)
            goto L_0x0063
        L_0x0014:
            defpackage.jji.c(r6)
            ese r6 = r5.c
            jqh r6 = r6.g
            boolean r6 = r6.bI()
            java.lang.String r1 = "newBuilder(...)"
            if (r6 == 0) goto L_0x003a
            dzo r6 = defpackage.dzo.c
            htk r6 = r6.l()
            defpackage.jnu.d(r6, r1)
            dku r6 = defpackage.jnu.e(r6, "builder")
            eaf r0 = defpackage.eaf.FAILED_ROUTING_DUE_TO_DISCONNECT_ALREADY_CALLED
            r6.i(r0)
            dzo r6 = r6.h()
            return r6
        L_0x003a:
            ese r6 = r5.c
            eks r3 = r6.h
            ekt r4 = r6.b
            ekt r6 = r6.a
            ekr r6 = r3.a(r4, r6)
            boolean r3 = r6.a
            if (r3 != 0) goto L_0x0087
            int r6 = r6.b
            if (r6 <= r2) goto L_0x004f
            goto L_0x0087
        L_0x004f:
            ese r6 = r5.c
            boolean r3 = r6.e
            if (r3 == 0) goto L_0x0067
            erq r6 = r6.c
            hme r6 = r6.b()
            r5.b = r2
            java.lang.Object r6 = defpackage.jqw.x(r6, r5)
            if (r6 == r0) goto L_0x0066
        L_0x0063:
            dzo r6 = (defpackage.dzo) r6
            goto L_0x009d
        L_0x0066:
            return r0
        L_0x0067:
            eks r2 = r6.h
            ekt r3 = r6.b
            ekt r6 = r6.a
            r2.b(r3, r6)
            dzo r6 = defpackage.dzo.c
            htk r6 = r6.l()
            defpackage.jnu.d(r6, r1)
            dku r6 = defpackage.jnu.e(r6, "builder")
            eaf r1 = defpackage.eaf.FAILED_ROUTING_DUE_TO_AUDIO_ROUTE_LOST
            r6.i(r1)
            dzo r6 = r6.h()
            goto L_0x009d
        L_0x0087:
            dzo r6 = defpackage.dzo.c
            htk r6 = r6.l()
            defpackage.jnu.d(r6, r1)
            dku r6 = defpackage.jnu.e(r6, "builder")
            eaf r1 = defpackage.eaf.UPDATED
            r6.i(r1)
            dzo r6 = r6.h()
        L_0x009d:
            ese r1 = r5.c
            hme r1 = r1.f
            boolean r1 = r1.isDone()
            if (r1 == 0) goto L_0x00b6
            ese r1 = r5.c
            r5.a = r6
            r2 = 2
            r5.b = r2
            java.lang.Object r1 = r1.f(r5)
            if (r1 == r0) goto L_0x00b5
            goto L_0x00b6
        L_0x00b5:
            return r0
        L_0x00b6:
            r0 = r6
        L_0x00b7:
            return r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.esd.bk(java.lang.Object):java.lang.Object");
    }

    public final jlr c(Object obj, jlr jlr) {
        return new esd(this.c, jlr);
    }
}
