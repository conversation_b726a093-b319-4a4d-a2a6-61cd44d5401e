package defpackage;

import androidx.preference.Preference;
import java.util.ArrayList;
import java.util.List;

/* renamed from: ekp  reason: default package */
/* compiled from: PG */
public final class ekp implements ebg {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/core/common/StopAwareStartListeningResult");
    public final ebg b;
    public final String c = fbi.o(this);
    public final List d = new ArrayList();
    public final jpq e = new jpq(Preference.DEFAULT_ORDER, jpt.a);
    public final jpp f = new jpp(false, jpt.a);
    private final jmp g;
    private final hme h;
    private final jqs i;
    private final jjo j;
    private final jjo k;
    private final grh l;
    private final grh m;
    private final hme n;
    private final Object o;
    private final jqh p;
    private final jqh q;

    public ekp(ebg ebg, jmp jmp, hme hme, jqs jqs) {
        this.b = ebg;
        this.g = jmp;
        this.h = hme;
        this.i = jqs;
        jqh jqh = new jqh();
        this.p = jqh;
        jqh jqh2 = new jqh();
        this.q = jqh2;
        job.S(jqs, (jlv) null, (jqt) null, new ekj(this, (jlr) null, 0), 3);
        this.j = new jjw(new mq(this, 13));
        this.k = new jjw(new mq(this, 14));
        this.l = ebg.d();
        this.m = grh.g(jqw.w(jqh2));
        this.n = jqw.w(jqh);
        this.o = ebg.f();
    }

    public final grh a() {
        return (grh) this.j.a();
    }

    public final grh b() {
        return (grh) this.k.a();
    }

    public final grh c() {
        return this.m;
    }

    public final grh d() {
        return this.l;
    }

    public final hme e() {
        return this.n;
    }

    public final Object f() {
        return this.o;
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x0035  */
    /* JADX WARNING: Removed duplicated region for block: B:28:0x0061  */
    /* JADX WARNING: Removed duplicated region for block: B:31:0x007c  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object g(defpackage.jlr r6) {
        /*
            r5 = this;
            boolean r0 = r6 instanceof defpackage.ekm
            if (r0 == 0) goto L_0x0013
            r0 = r6
            ekm r0 = (defpackage.ekm) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            ekm r0 = new ekm
            r0.<init>(r5, r6)
        L_0x0018:
            java.lang.Object r6 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            r3 = 1
            if (r2 == 0) goto L_0x0035
            if (r2 != r3) goto L_0x002d
            jqh r1 = r0.e
            ekp r0 = r0.d
            defpackage.jji.c(r6)     // Catch:{ all -> 0x002b }
            goto L_0x004f
        L_0x002b:
            r6 = move-exception
            goto L_0x0057
        L_0x002d:
            java.lang.IllegalStateException r6 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r6.<init>(r0)
            throw r6
        L_0x0035:
            defpackage.jji.c(r6)
            jqh r6 = r5.p
            ebg r2 = r5.b     // Catch:{ all -> 0x0053 }
            hme r2 = r2.e()     // Catch:{ all -> 0x0053 }
            r0.d = r5     // Catch:{ all -> 0x0053 }
            r0.e = r6     // Catch:{ all -> 0x0053 }
            r0.c = r3     // Catch:{ all -> 0x0053 }
            java.lang.Object r0 = defpackage.jqw.x(r2, r0)     // Catch:{ all -> 0x0053 }
            if (r0 == r1) goto L_0x0052
            r1 = r6
            r6 = r0
            r0 = r5
        L_0x004f:
            dyu r6 = (defpackage.dyu) r6     // Catch:{ all -> 0x002b }
            goto L_0x005b
        L_0x0052:
            return r1
        L_0x0053:
            r0 = move-exception
            r1 = r6
            r6 = r0
            r0 = r5
        L_0x0057:
            java.lang.Object r6 = defpackage.jji.b(r6)
        L_0x005b:
            java.lang.Throwable r2 = defpackage.jju.a(r6)
            if (r2 == 0) goto L_0x0076
            dyu r6 = defpackage.dyu.c
            htk r6 = r6.l()
            htm r6 = (defpackage.htm) r6
            java.lang.String r2 = "newBuilder(...)"
            defpackage.jnu.d(r6, r2)
            dlv r6 = defpackage.jnu.e(r6, "builder")
            dyu r6 = r6.c()
        L_0x0076:
            boolean r6 = r1.O(r6)
            if (r6 == 0) goto L_0x009f
            hca r6 = a
            hco r6 = r6.f()
            hcr r1 = defpackage.hdg.a
            java.lang.String r2 = "ALT.StopAwareStartRslt"
            hco r6 = r6.h(r1, r2)
            java.lang.String r1 = "awaitAudioStartTime"
            r2 = 161(0xa1, float:2.26E-43)
            java.lang.String r3 = "com/google/android/libraries/search/audio/core/common/StopAwareStartListeningResult"
            java.lang.String r4 = "StopAwareStartListeningResult.kt"
            hco r6 = r6.j(r3, r1, r2, r4)
            hby r6 = (defpackage.hby) r6
            java.lang.String r0 = r0.c
            java.lang.String r1 = "#audio# result(%s) synced audio-start-time"
            r6.u(r1, r0)
        L_0x009f:
            jkd r6 = defpackage.jkd.a
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.ekp.g(jlr):java.lang.Object");
    }

    /* JADX WARNING: Removed duplicated region for block: B:15:0x0037  */
    /* JADX WARNING: Removed duplicated region for block: B:31:0x008e  */
    /* JADX WARNING: Removed duplicated region for block: B:34:0x00a5  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0023  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object h(defpackage.jlr r6) {
        /*
            r5 = this;
            boolean r0 = r6 instanceof defpackage.ekn
            if (r0 == 0) goto L_0x0013
            r0 = r6
            ekn r0 = (defpackage.ekn) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            ekn r0 = new ekn
            r0.<init>(r5, r6)
        L_0x0018:
            java.lang.Object r6 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            java.lang.String r3 = "newBuilder(...)"
            r4 = 1
            if (r2 == 0) goto L_0x0037
            if (r2 != r4) goto L_0x002f
            jqh r1 = r0.e
            ekp r0 = r0.d
            defpackage.jji.c(r6)     // Catch:{ all -> 0x002d }
            goto L_0x007c
        L_0x002d:
            r6 = move-exception
            goto L_0x0084
        L_0x002f:
            java.lang.IllegalStateException r6 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r6.<init>(r0)
            throw r6
        L_0x0037:
            defpackage.jji.c(r6)
            ebg r6 = r5.b
            grh r6 = r6.c()
            boolean r6 = r6.f()
            if (r6 != 0) goto L_0x005f
            jqh r6 = r5.q
            dyv r0 = defpackage.dyv.c
            htk r0 = r0.l()
            defpackage.jnu.d(r0, r3)
            bzj r0 = defpackage.jnu.e(r0, "builder")
            dyv r0 = r0.A()
            boolean r6 = r6.O(r0)
            r0 = r5
            goto L_0x00a3
        L_0x005f:
            jqh r6 = r5.q
            ebg r2 = r5.b     // Catch:{ all -> 0x0080 }
            grh r2 = r2.c()     // Catch:{ all -> 0x0080 }
            java.lang.Object r2 = r2.b()     // Catch:{ all -> 0x0080 }
            hme r2 = (defpackage.hme) r2     // Catch:{ all -> 0x0080 }
            r0.d = r5     // Catch:{ all -> 0x0080 }
            r0.e = r6     // Catch:{ all -> 0x0080 }
            r0.c = r4     // Catch:{ all -> 0x0080 }
            java.lang.Object r0 = defpackage.jqw.x(r2, r0)     // Catch:{ all -> 0x0080 }
            if (r0 == r1) goto L_0x007f
            r1 = r6
            r6 = r0
            r0 = r5
        L_0x007c:
            dyv r6 = (defpackage.dyv) r6     // Catch:{ all -> 0x002d }
            goto L_0x0088
        L_0x007f:
            return r1
        L_0x0080:
            r0 = move-exception
            r1 = r6
            r6 = r0
            r0 = r5
        L_0x0084:
            java.lang.Object r6 = defpackage.jji.b(r6)
        L_0x0088:
            java.lang.Throwable r2 = defpackage.jju.a(r6)
            if (r2 == 0) goto L_0x009f
            dyv r6 = defpackage.dyv.c
            htk r6 = r6.l()
            defpackage.jnu.d(r6, r3)
            bzj r6 = defpackage.jnu.e(r6, "builder")
            dyv r6 = r6.A()
        L_0x009f:
            boolean r6 = r1.O(r6)
        L_0x00a3:
            if (r6 == 0) goto L_0x00c8
            hca r6 = a
            hco r6 = r6.f()
            hcr r1 = defpackage.hdg.a
            java.lang.String r2 = "ALT.StopAwareStartRslt"
            hco r6 = r6.h(r1, r2)
            java.lang.String r1 = "awaitFirstByte"
            r2 = 151(0x97, float:2.12E-43)
            java.lang.String r3 = "com/google/android/libraries/search/audio/core/common/StopAwareStartListeningResult"
            java.lang.String r4 = "StopAwareStartListeningResult.kt"
            hco r6 = r6.j(r3, r1, r2, r4)
            hby r6 = (defpackage.hby) r6
            java.lang.String r0 = r0.c
            java.lang.String r1 = "#audio# result(%s) synced first-byte-read"
            r6.u(r1, r0)
        L_0x00c8:
            jkd r6 = defpackage.jkd.a
            return r6
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.ekp.h(jlr):java.lang.Object");
    }

    /*  JADX ERROR: IndexOutOfBoundsException in pass: RegionMakerVisitor
        java.lang.IndexOutOfBoundsException: Index: 0, Size: 0
        	at java.util.ArrayList.rangeCheck(ArrayList.java:659)
        	at java.util.ArrayList.get(ArrayList.java:435)
        	at jadx.core.dex.nodes.InsnNode.getArg(InsnNode.java:101)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:611)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverseMonitorExits(RegionMaker.java:619)
        	at jadx.core.dex.visitors.regions.RegionMaker.processMonitorEnter(RegionMaker.java:561)
        	at jadx.core.dex.visitors.regions.RegionMaker.traverse(RegionMaker.java:133)
        	at jadx.core.dex.visitors.regions.RegionMaker.makeRegion(RegionMaker.java:86)
        	at jadx.core.dex.visitors.regions.RegionMakerVisitor.visit(RegionMakerVisitor.java:49)
        */
    /* JADX WARNING: Removed duplicated region for block: B:15:0x0033  */
    /* JADX WARNING: Removed duplicated region for block: B:26:0x0051  */
    /* JADX WARNING: Removed duplicated region for block: B:29:0x007a  */
    /* JADX WARNING: Removed duplicated region for block: B:36:0x00ba  */
    /* JADX WARNING: Removed duplicated region for block: B:39:0x00e3  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    public final java.lang.Object i(defpackage.jlr r7) {
        /*
            r6 = this;
            boolean r0 = r7 instanceof defpackage.eko
            if (r0 == 0) goto L_0x0013
            r0 = r7
            eko r0 = (defpackage.eko) r0
            int r1 = r0.c
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.c = r1
            goto L_0x0018
        L_0x0013:
            eko r0 = new eko
            r0.<init>(r6, r7)
        L_0x0018:
            java.lang.Object r7 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.c
            r3 = 1
            if (r2 == 0) goto L_0x0033
            if (r2 != r3) goto L_0x002b
            ekp r0 = r0.d
            defpackage.jji.c(r7)     // Catch:{ all -> 0x0029 }
            goto L_0x004b
        L_0x0029:
            r7 = move-exception
            goto L_0x0047
        L_0x002b:
            java.lang.IllegalStateException r7 = new java.lang.IllegalStateException
            java.lang.String r0 = "call to 'resume' before 'invoke' with coroutine"
            r7.<init>(r0)
            throw r7
        L_0x0033:
            defpackage.jji.c(r7)
            hme r7 = r6.h     // Catch:{ all -> 0x0045 }
            r0.d = r6     // Catch:{ all -> 0x0045 }
            r0.c = r3     // Catch:{ all -> 0x0045 }
            java.lang.Object r7 = defpackage.jqw.x(r7, r0)     // Catch:{ all -> 0x0045 }
            if (r7 != r1) goto L_0x0043
            return r1
        L_0x0043:
            r0 = r6
            goto L_0x004b
        L_0x0045:
            r7 = move-exception
            r0 = r6
        L_0x0047:
            java.lang.Object r7 = defpackage.jji.b(r7)
        L_0x004b:
            java.lang.Throwable r1 = defpackage.jju.a(r7)
            if (r1 == 0) goto L_0x0074
            hca r1 = a
            hco r1 = r1.h()
            hcr r2 = defpackage.hdg.a
            java.lang.String r3 = "ALT.StopAwareStartRslt"
            hco r1 = r1.h(r2, r3)
            java.lang.String r2 = "StopAwareStartListeningResult.kt"
            java.lang.String r3 = "com/google/android/libraries/search/audio/core/common/StopAwareStartListeningResult"
            java.lang.String r4 = "awaitStopped"
            r5 = 167(0xa7, float:2.34E-43)
            hco r1 = r1.j(r3, r4, r5, r2)
            hby r1 = (defpackage.hby) r1
            java.lang.String r2 = r0.c
            java.lang.String r3 = "#audio# result(%s) stop signal failed"
            r1.u(r3, r2)
        L_0x0074:
            boolean r7 = defpackage.jju.b(r7)
            if (r7 == 0) goto L_0x009d
            hca r7 = a
            hco r7 = r7.f()
            hcr r1 = defpackage.hdg.a
            java.lang.String r2 = "ALT.StopAwareStartRslt"
            hco r7 = r7.h(r1, r2)
            java.lang.String r1 = "StopAwareStartListeningResult.kt"
            java.lang.String r2 = "com/google/android/libraries/search/audio/core/common/StopAwareStartListeningResult"
            java.lang.String r3 = "awaitStopped"
            r4 = 168(0xa8, float:2.35E-43)
            hco r7 = r7.j(r2, r3, r4, r1)
            hby r7 = (defpackage.hby) r7
            java.lang.String r1 = r0.c
            java.lang.String r2 = "#audio# result(%s) stop signal completed"
            r7.u(r2, r1)
        L_0x009d:
            jmp r7 = r0.g     // Catch:{ all -> 0x00af }
            java.lang.Object r7 = r7.a()     // Catch:{ all -> 0x00af }
            java.lang.Number r7 = (java.lang.Number) r7     // Catch:{ all -> 0x00af }
            int r7 = r7.intValue()     // Catch:{ all -> 0x00af }
            java.lang.Integer r1 = new java.lang.Integer     // Catch:{ all -> 0x00af }
            r1.<init>(r7)     // Catch:{ all -> 0x00af }
            goto L_0x00b4
        L_0x00af:
            r7 = move-exception
            java.lang.Object r1 = defpackage.jji.b(r7)
        L_0x00b4:
            java.lang.Throwable r7 = defpackage.jju.a(r1)
            if (r7 == 0) goto L_0x00dd
            hca r7 = a
            hco r7 = r7.h()
            hcr r2 = defpackage.hdg.a
            java.lang.String r3 = "ALT.StopAwareStartRslt"
            hco r7 = r7.h(r2, r3)
            java.lang.String r2 = "StopAwareStartListeningResult.kt"
            java.lang.String r3 = "com/google/android/libraries/search/audio/core/common/StopAwareStartListeningResult"
            java.lang.String r4 = "awaitStopped"
            r5 = 170(0xaa, float:2.38E-43)
            hco r7 = r7.j(r3, r4, r5, r2)
            hby r7 = (defpackage.hby) r7
            java.lang.String r2 = r0.c
            java.lang.String r3 = "#audio# no result(%s) processed bytes"
            r7.u(r3, r2)
        L_0x00dd:
            boolean r7 = defpackage.jju.b(r1)
            if (r7 == 0) goto L_0x0110
            java.lang.Number r1 = (java.lang.Number) r1
            int r7 = r1.intValue()
            jpq r1 = r0.e
            r1.b = r7
            hca r1 = a
            hco r1 = r1.f()
            hcr r2 = defpackage.hdg.a
            java.lang.String r3 = "ALT.StopAwareStartRslt"
            hco r1 = r1.h(r2, r3)
            java.lang.String r2 = "StopAwareStartListeningResult.kt"
            java.lang.String r3 = "com/google/android/libraries/search/audio/core/common/StopAwareStartListeningResult"
            java.lang.String r4 = "awaitStopped"
            r5 = 173(0xad, float:2.42E-43)
            hco r1 = r1.j(r3, r4, r5, r2)
            hby r1 = (defpackage.hby) r1
            java.lang.String r2 = r0.c
            java.lang.String r3 = "#audio# result(%s) stopped, processed %d bytes"
            r1.A(r3, r2, r7)
        L_0x0110:
            jqh r7 = r0.q
            dyv r1 = defpackage.dyv.c
            htk r1 = r1.l()
            java.lang.String r2 = "newBuilder(...)"
            defpackage.jnu.d(r1, r2)
            bzj r1 = defpackage.jnu.e(r1, "builder")
            dyv r1 = r1.A()
            r7.O(r1)
            jqh r7 = r0.p
            dyu r1 = defpackage.dyu.c
            htk r1 = r1.l()
            htm r1 = (defpackage.htm) r1
            java.lang.String r2 = "newBuilder(...)"
            defpackage.jnu.d(r1, r2)
            dlv r1 = defpackage.jnu.e(r1, "builder")
            dyu r1 = r1.c()
            r7.O(r1)
            java.util.List r7 = r0.d
            monitor-enter(r7)
            hca r1 = a     // Catch:{ all -> 0x0192 }
            hco r1 = r1.f()     // Catch:{ all -> 0x0192 }
            hcr r2 = defpackage.hdg.a     // Catch:{ all -> 0x0192 }
            java.lang.String r3 = "ALT.StopAwareStartRslt"
            hco r1 = r1.h(r2, r3)     // Catch:{ all -> 0x0192 }
            java.lang.String r2 = "com/google/android/libraries/search/audio/core/common/StopAwareStartListeningResult"
            java.lang.String r3 = "completeUnregistrations"
            java.lang.String r4 = "StopAwareStartListeningResult.kt"
            r5 = 184(0xb8, float:2.58E-43)
            hco r1 = r1.j(r2, r3, r5, r4)     // Catch:{ all -> 0x0192 }
            hby r1 = (defpackage.hby) r1     // Catch:{ all -> 0x0192 }
            java.lang.String r2 = "#audio# result(%s) removing %d receivers"
            java.lang.String r3 = r0.c     // Catch:{ all -> 0x0192 }
            java.util.List r4 = r0.d     // Catch:{ all -> 0x0192 }
            int r4 = r4.size()     // Catch:{ all -> 0x0192 }
            r1.A(r2, r3, r4)     // Catch:{ all -> 0x0192 }
            java.util.List r1 = r0.d     // Catch:{ all -> 0x0192 }
            java.util.Iterator r1 = r1.iterator()     // Catch:{ all -> 0x0192 }
        L_0x0174:
            boolean r2 = r1.hasNext()     // Catch:{ all -> 0x0192 }
            if (r2 == 0) goto L_0x0184
            java.lang.Object r2 = r1.next()     // Catch:{ all -> 0x0192 }
            jmp r2 = (defpackage.jmp) r2     // Catch:{ all -> 0x0192 }
            r2.a()     // Catch:{ all -> 0x0192 }
            goto L_0x0174
        L_0x0184:
            java.util.List r1 = r0.d     // Catch:{ all -> 0x0192 }
            r1.clear()     // Catch:{ all -> 0x0192 }
            jpp r0 = r0.f     // Catch:{ all -> 0x0192 }
            r0.d()     // Catch:{ all -> 0x0192 }
            monitor-exit(r7)
            jkd r7 = defpackage.jkd.a
            return r7
        L_0x0192:
            r0 = move-exception
            monitor-exit(r7)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.ekp.i(jlr):java.lang.Object");
    }
}
