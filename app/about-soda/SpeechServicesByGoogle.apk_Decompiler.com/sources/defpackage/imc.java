package defpackage;

/* renamed from: imc  reason: default package */
/* compiled from: PG */
public final class imc implements iiu {
    private final jjk a;
    private final /* synthetic */ int b;

    public imc(jjk jjk, int i) {
        this.b = i;
        this.a = jjk;
    }

    public final /* synthetic */ Object b() {
        switch (this.b) {
            case 0:
                gcx p = ftc.A(((<PERSON>olean) ((grh) ((iiv) this.a).a).d(false)).booleanValue());
                hzz.u(p);
                return p;
            case 1:
                gcx q = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(true)).booleanValue());
                hzz.u(q);
                return q;
            case 2:
                gcx p2 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(false)).booleanValue());
                hzz.u(p2);
                return p2;
            case 3:
                gcx p3 = ftc.A(((<PERSON>olean) ((grh) ((iiv) this.a).a).d(false)).booleanValue());
                hzz.u(p3);
                return p3;
            case 4:
                gcx q2 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(true)).booleanValue());
                hzz.u(q2);
                return q2;
            case 5:
                gcx p4 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(false)).booleanValue());
                hzz.u(p4);
                return p4;
            case 6:
                gcx p5 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(false)).booleanValue());
                hzz.u(p5);
                return p5;
            case 7:
                gcx p6 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(false)).booleanValue());
                hzz.u(p6);
                return p6;
            case 8:
                gcx q3 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(true)).booleanValue());
                hzz.u(q3);
                return q3;
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                gcx p7 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(false)).booleanValue());
                hzz.u(p7);
                return p7;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                gcx p8 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(false)).booleanValue());
                hzz.u(p8);
                return p8;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                gcx q4 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(true)).booleanValue());
                hzz.u(q4);
                return q4;
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                gcx q5 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(true)).booleanValue());
                hzz.u(q5);
                return q5;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                gcx q6 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(true)).booleanValue());
                hzz.u(q6);
                return q6;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                gcx p9 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(false)).booleanValue());
                hzz.u(p9);
                return p9;
            case 15:
                gcx q7 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(true)).booleanValue());
                hzz.u(q7);
                return q7;
            case 16:
                gcx p10 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(false)).booleanValue());
                hzz.u(p10);
                return p10;
            case 17:
                gcx p11 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(false)).booleanValue());
                hzz.u(p11);
                return p11;
            case 18:
                gcx p12 = ftc.A(((Boolean) ((grh) ((iiv) this.a).a).d(false)).booleanValue());
                hzz.u(p12);
                return p12;
            case 19:
                return ftc.D(new bdl((grh) ((iiv) this.a).a, 19), htb.c);
            default:
                return ftc.D(new imb((grh) ((iiv) this.a).a, 0), hxe.a);
        }
    }
}
