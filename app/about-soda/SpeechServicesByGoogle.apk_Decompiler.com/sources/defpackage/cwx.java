package defpackage;

import android.content.Context;
import java.util.List;

/* renamed from: cwx  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwx implements hko {
    public final /* synthetic */ ctj a;
    public final /* synthetic */ csy b;
    public final /* synthetic */ List c;
    public final /* synthetic */ int d;
    public final /* synthetic */ int e;
    public final /* synthetic */ dbw f;

    public /* synthetic */ cwx(dbw dbw, ctj ctj, csy csy, List list, int i, int i2) {
        this.f = dbw;
        this.a = ctj;
        this.b = csy;
        this.c = list;
        this.d = i;
        this.e = i2;
    }

    /* JADX WARNING: type inference failed for: r7v0, types: [cuk, java.lang.Object] */
    public final hme a(Object obj) {
        dbw dbw = this.f;
        ctl ctl = (ctl) obj;
        if (ctl != null) {
            ctf b2 = ctf.b(ctl.c);
            if (b2 == null) {
                b2 = ctf.NONE;
            }
            if (b2 == ctf.DOWNLOAD_COMPLETE) {
                ctj ctj = this.a;
                Context context = dbw.a;
                int x = a.x(ctj.e);
                if (x == 0) {
                    x = 1;
                }
                if (cqx.u(context, x, ctl.b, ctj.d, dbw.d, (grh) dbw.g, false) != null) {
                    return hfc.K(this.b);
                }
            }
        }
        return dbw.q(this.c, this.d + 1, this.e);
    }
}
