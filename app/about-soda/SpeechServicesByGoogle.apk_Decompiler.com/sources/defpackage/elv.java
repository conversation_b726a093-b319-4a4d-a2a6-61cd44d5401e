package defpackage;

/* renamed from: elv  reason: default package */
/* compiled from: PG */
final class elv extends jmi implements jne {
    int a;
    final /* synthetic */ elx b;
    final /* synthetic */ ehg c;
    final /* synthetic */ long d;
    final /* synthetic */ Object e;
    private /* synthetic */ Object f;
    private final /* synthetic */ int g;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public elv(elx elx, ehg ehg, long j, dyy dyy, jlr jlr, int i) {
        super(2, jlr);
        this.g = i;
        this.b = elx;
        this.c = ehg;
        this.d = j;
        this.e = dyy;
    }

    public final /* synthetic */ Object b(Object obj, Object obj2) {
        if (this.g != 0) {
            return ((elv) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
        }
        return ((elv) c((jqs) obj, (jlr) obj2)).bk(jkd.a);
    }

    /* JADX WARNING: type inference failed for: r10v14, types: [java.lang.Object, ebl] */
    /* JADX WARNING: type inference failed for: r10v33, types: [dyy, java.lang.Object] */
    public final Object bk(Object obj) {
        Object obj2;
        Object obj3;
        if (this.g != 0) {
            Object obj4 = jlx.COROUTINE_SUSPENDED;
            if (this.a != 0) {
                try {
                    jji.c(obj);
                } catch (Throwable th) {
                    obj3 = jji.b(th);
                }
            } else {
                jji.c(obj);
                jqs jqs = (jqs) this.f;
                hme a2 = ((dyx) this.e.d()).a();
                jnu.d(a2, "getStopListeningStatus(...)");
                this.a = 1;
                obj = jqw.x(a2, this);
                if (obj == obj4) {
                    return obj4;
                }
            }
            obj3 = (dzi) obj;
            Object a3 = eki.a(eag.UNKNOWN_CLOSING_FAILURE, eam.UNSET);
            if (true == (obj3 instanceof jjt)) {
                obj3 = a3;
            }
            elx elx = this.b;
            ehg ehg = this.c;
            long j = this.d;
            dzi dzi = (dzi) obj3;
            jnu.b(dzi);
            jnu.e(ehg, "clientInfo");
            jnu.e(dzi, "status");
            dxl dxl = dwt.i;
            jnu.d(dxl, "AUDIO_STOP_LISTENING_DONE");
            eah eah = dzi.b;
            if (eah == null) {
                eah = eah.c;
            }
            jnu.d(eah, "getAudioSourceClosingStatus(...)");
            dxh D = elx.D(dxl, eah);
            elx.F(D, j);
            gnk gnk = elx.a;
            jyh jyh = jyh.AUDIO_REQUEST;
            eam b2 = eam.b(dzi.c);
            if (b2 == null) {
                b2 = eam.UNSET;
            }
            jnu.d(b2, "getStopListeningReason(...)");
            D.g(gnk, elx.A(ehg, jyh, b2, (Integer) null));
            elx.x(D, ehg);
            return jkd.a;
        }
        Object obj5 = jlx.COROUTINE_SUSPENDED;
        if (this.a != 0) {
            try {
                jji.c(obj);
            } catch (Throwable th2) {
                obj2 = jji.b(th2);
            }
        } else {
            jji.c(obj);
            jqs jqs2 = (jqs) this.f;
            hme a4 = this.e.a().a();
            this.a = 1;
            obj = jqw.x(a4, this);
            if (obj == obj5) {
                return obj5;
            }
        }
        obj2 = (ebp) obj;
        Object i = eki.i(eag.UNKNOWN_CLOSING_FAILURE, eam.UNSET);
        if (true == (obj2 instanceof jjt)) {
            obj2 = i;
        }
        elx elx2 = this.b;
        ehg ehg2 = this.c;
        long j2 = this.d;
        ebp ebp = (ebp) obj2;
        jnu.b(ebp);
        jnu.e(ehg2, "clientInfo");
        jnu.e(ebp, "status");
        dxl dxl2 = dwt.i;
        jnu.d(dxl2, "AUDIO_STOP_LISTENING_DONE");
        eah eah2 = ebp.b;
        if (eah2 == null) {
            eah2 = eah.c;
        }
        jnu.d(eah2, "getAudioSourceClosingStatus(...)");
        dxh D2 = elx.D(dxl2, eah2);
        elx.F(D2, j2);
        gnk gnk2 = elx.a;
        jyh jyh2 = jyh.HOTWORD;
        eam b3 = eam.b(ebp.c);
        if (b3 == null) {
            b3 = eam.UNSET;
        }
        jnu.d(b3, "getStopListeningReason(...)");
        D2.g(gnk2, elx.A(ehg2, jyh2, b3, Integer.valueOf((int) j2)));
        elx2.x(D2, ehg2);
        return jkd.a;
    }

    /* JADX WARNING: type inference failed for: r16v0, types: [ebl] */
    /* JADX WARNING: type inference failed for: r8v0, types: [dyy, java.lang.Object] */
    /* JADX WARNING: Multi-variable type inference failed */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final defpackage.jlr c(java.lang.Object r20, defpackage.jlr r21) {
        /*
            r19 = this;
            r0 = r19
            r1 = r20
            int r2 = r0.g
            if (r2 == 0) goto L_0x001c
            elv r2 = new elv
            elx r4 = r0.b
            ehg r5 = r0.c
            long r6 = r0.d
            java.lang.Object r8 = r0.e
            r10 = 1
            r3 = r2
            r9 = r21
            r3.<init>((defpackage.elx) r4, (defpackage.ehg) r5, (long) r6, (defpackage.dyy) r8, (defpackage.jlr) r9, (int) r10)
            r2.f = r1
            return r2
        L_0x001c:
            elv r2 = new elv
            elx r12 = r0.b
            ehg r13 = r0.c
            long r14 = r0.d
            java.lang.Object r3 = r0.e
            r18 = 0
            r11 = r2
            r16 = r3
            r17 = r21
            r11.<init>((defpackage.elx) r12, (defpackage.ehg) r13, (long) r14, (defpackage.ebl) r16, (defpackage.jlr) r17, (int) r18)
            r2.f = r1
            return r2
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.elv.c(java.lang.Object, jlr):jlr");
    }

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public elv(elx elx, ehg ehg, long j, ebl ebl, jlr jlr, int i) {
        super(2, jlr);
        this.g = i;
        this.b = elx;
        this.c = ehg;
        this.d = j;
        this.e = ebl;
    }
}
