package defpackage;

import android.os.SystemClock;
import java.net.InetSocketAddress;
import java.net.SocketAddress;

/* renamed from: crt  reason: default package */
/* compiled from: PG */
final class crt extends ipb {
    public final Object a = new Object();
    public final dpp b;
    public boolean c;
    public int d;
    public int e;

    public crt(dpp dpp) {
        this.b = dpp;
    }

    public final void b() {
        synchronized (this.a) {
            if (!this.c) {
                dpp dpp = this.b;
                dpp.b = SystemClock.elapsedRealtime() - dpp.a;
            }
        }
    }

    public final void c(long j) {
        synchronized (this.a) {
            this.d += (int) j;
        }
    }

    public final void d(long j) {
        synchronized (this.a) {
            this.e += (int) j;
        }
    }

    public final void e(ior ior) {
        synchronized (this.a) {
            dpp dpp = this.b;
            SocketAddress socketAddress = (SocketAddress) ior.a(iqb.a);
            if (!(socketAddress instanceof InetSocketAddress)) {
                if (socketAddress instanceof iwm) {
                    dpp.v = 2;
                } else if (socketAddress instanceof ito) {
                    dpp.v = 3;
                }
            }
        }
    }
}
