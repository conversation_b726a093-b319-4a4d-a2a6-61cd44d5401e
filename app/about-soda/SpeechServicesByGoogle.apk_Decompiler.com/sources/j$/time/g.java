package j$.time;

import j$.time.chrono.C0003b;
import j$.time.chrono.C0006e;
import j$.time.chrono.C0010i;
import j$.time.chrono.u;
import j$.time.temporal.ChronoUnit;
import j$.time.temporal.TemporalUnit;
import j$.time.temporal.a;
import j$.time.temporal.k;
import j$.time.temporal.l;
import j$.time.temporal.m;
import j$.time.temporal.n;
import j$.time.temporal.p;
import j$.time.temporal.r;
import j$.time.temporal.t;
import j$.util.Objects;
import java.io.DataOutput;
import java.io.InvalidObjectException;
import java.io.ObjectInputStream;
import java.io.Serializable;

public final class g implements k, n, C0003b, Serializable {
    public static final g d = T(-999999999, 1, 1);
    public static final g e = T(999999999, 12, 31);
    private static final long serialVersionUID = 2942565459149668126L;
    private final int a;
    private final short b;
    private final short c;

    static {
        T(1970, 1, 1);
    }

    private g(int i, int i2, int i3) {
        this.a = i;
        this.b = (short) i2;
        this.c = (short) i3;
    }

    private static g J(int i, int i2, int i3) {
        int i4 = 28;
        if (i3 > 28) {
            if (i2 != 2) {
                if (i2 == 4 || i2 == 6 || i2 == 9 || i2 == 11) {
                    i4 = 30;
                } else {
                    i4 = 31;
                }
            } else if (u.d.D((long) i)) {
                i4 = 29;
            }
            if (i3 > i4) {
                if (i3 == 29) {
                    throw new RuntimeException("Invalid date 'February 29' as '" + i + "' is not a leap year");
                }
                String name = l.L(i2).name();
                throw new RuntimeException("Invalid date '" + name + " " + i3 + "'");
            }
        }
        return new g(i, i2, i3);
    }

    public static g K(m mVar) {
        Objects.requireNonNull(mVar, "temporal");
        g gVar = (g) mVar.F(l.f());
        if (gVar != null) {
            return gVar;
        }
        String valueOf = String.valueOf(mVar);
        String name = mVar.getClass().getName();
        throw new RuntimeException("Unable to obtain LocalDate from TemporalAccessor: " + valueOf + " of type " + name);
    }

    private int L(p pVar) {
        int i;
        int i2 = f.a[((a) pVar).ordinal()];
        short s = this.c;
        int i3 = this.a;
        switch (i2) {
            case 1:
                return s;
            case 2:
                return N();
            case 3:
                i = (s - 1) / 7;
                break;
            case 4:
                if (i3 >= 1) {
                    return i3;
                }
                return 1 - i3;
            case 5:
                return M().getValue();
            case 6:
                i = (s - 1) % 7;
                break;
            case 7:
                return ((N() - 1) % 7) + 1;
            case 8:
                throw new RuntimeException("Invalid field 'EpochDay' for get() method, use getLong() instead");
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER:
                return ((N() - 1) / 7) + 1;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER:
                return this.b;
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER:
                throw new RuntimeException("Invalid field 'ProlepticMonth' for get() method, use getLong() instead");
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER:
                return i3;
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER:
                if (i3 >= 1) {
                    return 1;
                }
                return 0;
            default:
                throw new RuntimeException("Unsupported field: ".concat(String.valueOf(pVar)));
        }
        return i + 1;
    }

    public static g T(int i, int i2, int i3) {
        a.YEAR.I((long) i);
        a.MONTH_OF_YEAR.I((long) i2);
        a.DAY_OF_MONTH.I((long) i3);
        return J(i, i2, i3);
    }

    public static g U(int i, l lVar, int i2) {
        a.YEAR.I((long) i);
        Objects.requireNonNull(lVar, "month");
        a.DAY_OF_MONTH.I((long) i2);
        return J(i, lVar.getValue(), i2);
    }

    public static g V(long j) {
        long j2;
        long j3 = j;
        a.EPOCH_DAY.I(j3);
        long j4 = 719468 + j3;
        if (j4 < 0) {
            long j5 = ((j3 + 719469) / 146097) - 1;
            j2 = j5 * 400;
            j4 += (-j5) * 146097;
        } else {
            j2 = 0;
        }
        long j6 = ((j4 * 400) + 591) / 146097;
        long j7 = j4 - ((j6 / 400) + (((j6 / 4) + (j6 * 365)) - (j6 / 100)));
        if (j7 < 0) {
            j6--;
            j7 = j4 - ((j6 / 400) + (((j6 / 4) + (365 * j6)) - (j6 / 100)));
        }
        int i = (int) j7;
        int i2 = ((i * 5) + 2) / 153;
        return new g(a.YEAR.F(j6 + j2 + ((long) (i2 / 10))), ((i2 + 2) % 12) + 1, (i - (((i2 * 306) + 5) / 10)) + 1);
    }

    private static g a0(int i, int i2, int i3) {
        int i4;
        if (i2 == 2) {
            if (u.d.D((long) i)) {
                i4 = 29;
            } else {
                i4 = 28;
            }
            i3 = Math.min(i3, i4);
        } else if (i2 == 4 || i2 == 6 || i2 == 9 || i2 == 11) {
            i3 = Math.min(i3, 30);
        }
        return new g(i, i2, i3);
    }

    private void readObject(ObjectInputStream objectInputStream) {
        throw new InvalidObjectException("Deserialization via serialization delegate");
    }

    private Object writeReplace() {
        return new s((byte) 3, this);
    }

    /* renamed from: A */
    public final int compareTo(C0003b bVar) {
        if (bVar instanceof g) {
            return I((g) bVar);
        }
        return C0010i.b(this, bVar);
    }

    public final long B(p pVar) {
        if (!(pVar instanceof a)) {
            return pVar.g(this);
        }
        if (pVar == a.EPOCH_DAY) {
            return C();
        }
        if (pVar == a.PROLEPTIC_MONTH) {
            return ((((long) this.a) * 12) + ((long) this.b)) - 1;
        }
        return (long) L(pVar);
    }

    public final long C() {
        long j;
        long j2 = (long) this.a;
        long j3 = (long) this.b;
        long j4 = 365 * j2;
        if (j2 >= 0) {
            j = ((j2 + 399) / 400) + (((3 + j2) / 4) - ((99 + j2) / 100)) + j4;
        } else {
            j = j4 - ((j2 / -400) + ((j2 / -4) - (j2 / -100)));
        }
        long j5 = (((367 * j3) - 362) / 12) + j + ((long) (this.c - 1));
        if (j3 > 2) {
            long j6 = j5 - 1;
            if (!o()) {
                j5 -= 2;
            } else {
                j5 = j6;
            }
        }
        return j5 - 719528;
    }

    public final C0006e E(k kVar) {
        return i.Q(this, kVar);
    }

    public final Object F(r rVar) {
        if (rVar == l.f()) {
            return this;
        }
        return C0010i.j(this, rVar);
    }

    /* access modifiers changed from: package-private */
    public final int I(g gVar) {
        int i = this.a - gVar.a;
        if (i != 0) {
            return i;
        }
        int i2 = this.b - gVar.b;
        if (i2 == 0) {
            return this.c - gVar.c;
        }
        return i2;
    }

    public final d M() {
        return d.I(((int) j$.nio.file.attribute.a.d(C() + 3, (long) 7)) + 1);
    }

    public final int N() {
        return (l.L(this.b).I(o()) + this.c) - 1;
    }

    public final int O() {
        return this.b;
    }

    public final int P() {
        return this.a;
    }

    public final boolean Q(g gVar) {
        if (gVar instanceof g) {
            if (I(gVar) < 0) {
                return true;
            }
            return false;
        } else if (C() < gVar.C()) {
            return true;
        } else {
            return false;
        }
    }

    public final int R() {
        short s = this.b;
        if (s != 2) {
            if (s == 4 || s == 6 || s == 9 || s == 11) {
                return 30;
            }
            return 31;
        } else if (o()) {
            return 29;
        } else {
            return 28;
        }
    }

    /* renamed from: S */
    public final g v(long j, ChronoUnit chronoUnit) {
        if (j == Long.MIN_VALUE) {
            return f(Long.MAX_VALUE, chronoUnit).f(1, chronoUnit);
        }
        return f(-j, chronoUnit);
    }

    /* renamed from: W */
    public final g f(long j, TemporalUnit temporalUnit) {
        if (!(temporalUnit instanceof ChronoUnit)) {
            return (g) temporalUnit.m(this, j);
        }
        switch (f.b[((ChronoUnit) temporalUnit).ordinal()]) {
            case 1:
                return X(j);
            case 2:
                return X(j$.nio.file.attribute.a.f(j, (long) 7));
            case 3:
                return Y(j);
            case 4:
                return Z(j);
            case 5:
                return Z(j$.nio.file.attribute.a.f(j, (long) 10));
            case 6:
                return Z(j$.nio.file.attribute.a.f(j, (long) 100));
            case 7:
                return Z(j$.nio.file.attribute.a.f(j, (long) 1000));
            case 8:
                a aVar = a.ERA;
                return d(j$.nio.file.attribute.a.e(B(aVar), j), aVar);
            default:
                throw new RuntimeException("Unsupported unit: ".concat(String.valueOf(temporalUnit)));
        }
    }

    public final g X(long j) {
        if (j == 0) {
            return this;
        }
        long j2 = ((long) this.c) + j;
        if (j2 > 0) {
            short s = this.b;
            int i = this.a;
            if (j2 <= 28) {
                return new g(i, s, (int) j2);
            }
            if (j2 <= 59) {
                long R = (long) R();
                if (j2 <= R) {
                    return new g(i, s, (int) j2);
                }
                if (s < 12) {
                    return new g(i, s + 1, (int) (j2 - R));
                }
                int i2 = i + 1;
                a.YEAR.I((long) i2);
                return new g(i2, 1, (int) (j2 - R));
            }
        }
        return V(j$.nio.file.attribute.a.e(C(), j));
    }

    public final g Y(long j) {
        if (j == 0) {
            return this;
        }
        long j2 = (((long) this.a) * 12) + ((long) (this.b - 1)) + j;
        long j3 = (long) 12;
        return a0(a.YEAR.F(j$.nio.file.attribute.a.b(j2, j3)), ((int) j$.nio.file.attribute.a.d(j2, j3)) + 1, this.c);
    }

    public final g Z(long j) {
        if (j == 0) {
            return this;
        }
        return a0(a.YEAR.F(((long) this.a) + j), this.b, this.c);
    }

    public final j$.time.chrono.n a() {
        return u.d;
    }

    /* renamed from: b0 */
    public final g d(long j, p pVar) {
        if (!(pVar instanceof a)) {
            return (g) pVar.s(this, j);
        }
        a aVar = (a) pVar;
        aVar.I(j);
        int i = f.a[aVar.ordinal()];
        short s = this.c;
        short s2 = this.b;
        int i2 = this.a;
        switch (i) {
            case 1:
                int i3 = (int) j;
                if (s == i3) {
                    return this;
                }
                return T(i2, s2, i3);
            case 2:
                return d0((int) j);
            case 3:
                return X(j$.nio.file.attribute.a.f(j - B(a.ALIGNED_WEEK_OF_MONTH), (long) 7));
            case 4:
                if (i2 < 1) {
                    j = 1 - j;
                }
                return e0((int) j);
            case 5:
                return X(j - ((long) M().getValue()));
            case 6:
                return X(j - B(a.ALIGNED_DAY_OF_WEEK_IN_MONTH));
            case 7:
                return X(j - B(a.ALIGNED_DAY_OF_WEEK_IN_YEAR));
            case 8:
                return V(j);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER:
                return X(j$.nio.file.attribute.a.f(j - B(a.ALIGNED_WEEK_OF_YEAR), (long) 7));
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER:
                int i4 = (int) j;
                if (s2 == i4) {
                    return this;
                }
                a.MONTH_OF_YEAR.I((long) i4);
                return a0(i2, i4, s);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER:
                return Y(j - (((((long) i2) * 12) + ((long) s2)) - 1));
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER:
                return e0((int) j);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER:
                if (B(a.ERA) == j) {
                    return this;
                }
                return e0(1 - i2);
            default:
                throw new RuntimeException("Unsupported field: ".concat(String.valueOf(pVar)));
        }
    }

    /* renamed from: c0 */
    public final g s(n nVar) {
        if (nVar instanceof g) {
            return (g) nVar;
        }
        return (g) nVar.g(this);
    }

    public final g d0(int i) {
        if (N() == i) {
            return this;
        }
        a aVar = a.YEAR;
        int i2 = this.a;
        long j = (long) i2;
        aVar.I(j);
        a.DAY_OF_YEAR.I((long) i);
        boolean D = u.d.D(j);
        if (i != 366 || D) {
            l L = l.L(((i - 1) / 31) + 1);
            if (i > (L.J(D) + L.I(D)) - 1) {
                L = L.M();
            }
            return new g(i2, L.getValue(), (i - L.I(D)) + 1);
        }
        throw new RuntimeException("Invalid date 'DayOfYear 366' as '" + i2 + "' is not a leap year");
    }

    public final boolean e(p pVar) {
        return C0010i.h(this, pVar);
    }

    public final g e0(int i) {
        if (this.a == i) {
            return this;
        }
        a.YEAR.I((long) i);
        return a0(i, this.b, this.c);
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof g) || I((g) obj) != 0) {
            return false;
        }
        return true;
    }

    /* access modifiers changed from: package-private */
    public final void f0(DataOutput dataOutput) {
        dataOutput.writeInt(this.a);
        dataOutput.writeByte(this.b);
        dataOutput.writeByte(this.c);
    }

    public final k g(k kVar) {
        return C0010i.a(this, kVar);
    }

    public final int hashCode() {
        int i = this.a;
        return (((i << 11) + (this.b << 6)) + this.c) ^ (i & -2048);
    }

    public final boolean o() {
        return u.d.D((long) this.a);
    }

    public final int p(p pVar) {
        if (pVar instanceof a) {
            return L(pVar);
        }
        return l.a(this, pVar);
    }

    public final t t(p pVar) {
        long j;
        long j2;
        if (!(pVar instanceof a)) {
            return pVar.t(this);
        }
        a aVar = (a) pVar;
        if (aVar.B()) {
            int i = f.a[aVar.ordinal()];
            if (i == 1) {
                return t.j(1, (long) R());
            }
            if (i == 2) {
                return t.j(1, (long) x());
            }
            if (i == 3) {
                if (l.L(this.b) != l.FEBRUARY || o()) {
                    j = 5;
                } else {
                    j = 4;
                }
                return t.j(1, j);
            } else if (i != 4) {
                return ((a) pVar).m();
            } else {
                if (this.a <= 0) {
                    j2 = 1000000000;
                } else {
                    j2 = 999999999;
                }
                return t.j(1, j2);
            }
        } else {
            throw new RuntimeException("Unsupported field: ".concat(String.valueOf(pVar)));
        }
    }

    public final String toString() {
        String str;
        int i = this.a;
        int abs = Math.abs(i);
        StringBuilder sb = new StringBuilder(10);
        if (abs >= 1000) {
            if (i > 9999) {
                sb.append('+');
            }
            sb.append(i);
        } else if (i < 0) {
            sb.append(i - 10000);
            sb.deleteCharAt(1);
        } else {
            sb.append(i + 10000);
            sb.deleteCharAt(0);
        }
        String str2 = "-";
        short s = this.b;
        if (s < 10) {
            str = "-0";
        } else {
            str = str2;
        }
        sb.append(str);
        sb.append(s);
        short s2 = this.c;
        if (s2 < 10) {
            str2 = "-0";
        }
        sb.append(str2);
        sb.append(s2);
        return sb.toString();
    }

    public final int x() {
        if (o()) {
            return 366;
        }
        return 365;
    }
}
