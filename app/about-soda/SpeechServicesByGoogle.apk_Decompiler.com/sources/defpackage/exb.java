package defpackage;

import android.media.MediaCodec;
import android.media.MediaCrypto;
import android.media.MediaFormat;
import android.view.Surface;
import j$.io.DesugarInputStream;
import j$.io.InputStreamRetargetInterface;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.ByteBuffer;

/* renamed from: exb  reason: default package */
/* compiled from: PG */
public final class exb extends InputStream implements InputStreamRetargetInterface {
    private static final hca a = hca.m("com/google/android/libraries/speech/encoding/AudioEncoderInputStream");
    private final InputStream b;
    private final int c = 2048;
    private final int d;
    private final int e;
    private final String f;
    private final int g;
    private final ByteBuffer h;
    private final ByteBuffer i;
    private MediaCodec j;
    private ByteBuffer[] k;
    private ByteBuffer[] l;
    private int m = -1;
    private boolean n;
    private boolean o;
    private boolean p = false;

    /* JADX WARNING: Missing exception handler attribute for start block: B:64:0x017e */
    /* JADX WARNING: Removed duplicated region for block: B:52:0x0160  */
    /* JADX WARNING: Removed duplicated region for block: B:56:0x016d  */
    /* JADX WARNING: Removed duplicated region for block: B:67:0x0184 A[Catch:{ IOException | IllegalArgumentException -> 0x0188 }] */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public exb(java.io.InputStream r17, java.lang.String r18, int r19, int r20, int r21) {
        /*
            r16 = this;
            r1 = r16
            r0 = r18
            r2 = r19
            r3 = r21
            r16.<init>()
            r4 = -1
            r1.m = r4
            r5 = 0
            r1.p = r5
            r6 = 2048(0x800, float:2.87E-42)
            r1.c = r6
            r7 = r17
            r1.b = r7
            r1.d = r2
            r1.e = r3
            r1.f = r0
            if (r0 == 0) goto L_0x019d
            int r7 = r18.hashCode()
            r8 = 3
            r9 = 2
            r10 = 1
            switch(r7) {
                case -1606874997: goto L_0x004a;
                case -53558318: goto L_0x0040;
                case 1504619009: goto L_0x0036;
                case 1504891608: goto L_0x002c;
                default: goto L_0x002b;
            }
        L_0x002b:
            goto L_0x0053
        L_0x002c:
            java.lang.String r7 = "audio/opus"
            boolean r7 = r0.equals(r7)
            if (r7 == 0) goto L_0x0053
            r4 = r8
            goto L_0x0053
        L_0x0036:
            java.lang.String r7 = "audio/flac"
            boolean r7 = r0.equals(r7)
            if (r7 == 0) goto L_0x0053
            r4 = r9
            goto L_0x0053
        L_0x0040:
            java.lang.String r7 = "audio/mp4a-latm"
            boolean r7 = r0.equals(r7)
            if (r7 == 0) goto L_0x0053
            r4 = r5
            goto L_0x0053
        L_0x004a:
            java.lang.String r7 = "audio/amr-wb"
            boolean r7 = r0.equals(r7)
            if (r7 == 0) goto L_0x0053
            r4 = r10
        L_0x0053:
            r7 = 5
            r11 = 7
            if (r4 == 0) goto L_0x0129
            if (r4 == r10) goto L_0x0108
            if (r4 == r9) goto L_0x0078
            if (r4 != r8) goto L_0x0070
            r1.g = r8
            r4 = 21
            java.nio.ByteBuffer r4 = java.nio.ByteBuffer.allocate(r4)
            java.nio.ByteOrder r5 = java.nio.ByteOrder.LITTLE_ENDIAN
            java.nio.ByteBuffer r4 = r4.order(r5)
            r1.i = r4
            r4 = r6
            goto L_0x0136
        L_0x0070:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.String r2 = "Unsupported audio codec"
            r0.<init>(r2)
            throw r0
        L_0x0078:
            r1.g = r9
            r4 = 42
            byte[] r12 = new byte[r4]
            java.nio.ByteBuffer r12 = java.nio.ByteBuffer.wrap(r12)
            r1.i = r12
            int r13 = r12.remaining()
            if (r13 < r4) goto L_0x008c
            r4 = r10
            goto L_0x008d
        L_0x008c:
            r4 = r5
        L_0x008d:
            defpackage.fvf.aF(r4)
            hpw r4 = new hpw
            r4.<init>()
            r13 = 1716281667(0x664c6143, double:8.479558103E-315)
            r15 = 32
            r4.b(r13, r15)
            r13 = 1
            r4.b(r13, r10)
            r13 = 0
            r4.b(r13, r11)
            r5 = 34
            r11 = 24
            r4.b(r5, r11)
            r5 = 16
            r15 = 16
            r4.b(r5, r15)
            r5 = 65535(0xffff, double:3.23786E-319)
            r4.b(r5, r15)
            r4.b(r13, r11)
            r4.b(r13, r11)
            long r5 = (long) r2
            r11 = 20
            r4.b(r5, r11)
            int r5 = r3 + -1
            long r5 = (long) r5
            r4.b(r5, r8)
            r5 = 15
            r4.b(r5, r7)
            r5 = 36
            r4.b(r13, r5)
            r5 = 64
            r4.b(r13, r5)
            r4.b(r13, r5)
            int r5 = r4.b
            if (r5 != 0) goto L_0x00e5
            r5 = r10
            goto L_0x00e6
        L_0x00e5:
            r5 = 0
        L_0x00e6:
            java.lang.String r6 = "Cannot convert to byte array if not byte aligned."
            defpackage.fvf.aG(r5, r6)
            java.lang.Object r5 = r4.a     // Catch:{ IOException -> 0x0101 }
            java.io.ByteArrayOutputStream r5 = (java.io.ByteArrayOutputStream) r5     // Catch:{ IOException -> 0x0101 }
            r5.flush()     // Catch:{ IOException -> 0x0101 }
            java.lang.Object r4 = r4.a
            java.io.ByteArrayOutputStream r4 = (java.io.ByteArrayOutputStream) r4
            byte[] r4 = r4.toByteArray()
            r12.put(r4)
            r12.flip()
            goto L_0x0134
        L_0x0101:
            r0 = move-exception
            java.lang.RuntimeException r2 = new java.lang.RuntimeException
            r2.<init>(r0)
            throw r2
        L_0x0108:
            r4 = 16000(0x3e80, float:2.2421E-41)
            if (r2 != r4) goto L_0x010e
            r4 = r10
            goto L_0x010f
        L_0x010e:
            r4 = 0
        L_0x010f:
            defpackage.fvf.aF(r4)
            if (r3 != r10) goto L_0x0116
            r5 = r10
            goto L_0x0117
        L_0x0116:
            r5 = 0
        L_0x0117:
            defpackage.fvf.aF(r5)
            r1.g = r10
            java.lang.String r4 = "#!AMR-WB\n"
            byte[] r4 = r4.getBytes()
            java.nio.ByteBuffer r4 = java.nio.ByteBuffer.wrap(r4)
            r1.i = r4
            goto L_0x0134
        L_0x0129:
            r4 = r5
            r1.g = r4
            byte[] r4 = new byte[r11]
            java.nio.ByteBuffer r4 = java.nio.ByteBuffer.wrap(r4)
            r1.i = r4
        L_0x0134:
            r4 = 2048(0x800, float:2.87E-42)
        L_0x0136:
            byte[] r5 = new byte[r4]
            java.nio.ByteBuffer r5 = java.nio.ByteBuffer.wrap(r5)
            r1.h = r5
            r5.position(r4)
            android.media.MediaFormat r4 = new android.media.MediaFormat
            r4.<init>()
            java.lang.String r5 = "mime"
            r4.setString(r5, r0)
            java.lang.String r5 = "sample-rate"
            r4.setInteger(r5, r2)
            java.lang.String r2 = "bitrate"
            r5 = r20
            r4.setInteger(r2, r5)
            java.lang.String r2 = "channel-count"
            r4.setInteger(r2, r3)
            int r2 = r1.g
            if (r2 != r9) goto L_0x0165
            java.lang.String r2 = "flac-compression-level"
            r4.setInteger(r2, r7)
        L_0x0165:
            int r2 = r1.g     // Catch:{ IOException -> 0x018a, IllegalArgumentException -> 0x0188 }
            boolean r2 = e(r2)     // Catch:{ IOException -> 0x018a, IllegalArgumentException -> 0x0188 }
            if (r2 == 0) goto L_0x0184
            java.lang.String r0 = "OMX.google.aac.encoder"
            android.media.MediaCodec r0 = android.media.MediaCodec.createByCodecName(r0)     // Catch:{ IOException -> 0x0177 }
            r1.c(r0, r4)     // Catch:{ IOException -> 0x0177 }
            return
        L_0x0177:
            r0 = move-exception
            exh r2 = new exh     // Catch:{ Exception -> 0x017e }
            r2.<init>(r0)     // Catch:{ Exception -> 0x017e }
            throw r2     // Catch:{ Exception -> 0x017e }
        L_0x017e:
            java.lang.String r0 = r1.f     // Catch:{ IOException -> 0x018a, IllegalArgumentException -> 0x0188 }
            r1.d(r0, r4)     // Catch:{ IOException -> 0x018a, IllegalArgumentException -> 0x0188 }
            return
        L_0x0184:
            r1.d(r0, r4)     // Catch:{ IOException -> 0x018a, IllegalArgumentException -> 0x0188 }
            return
        L_0x0188:
            r0 = move-exception
            goto L_0x018b
        L_0x018a:
            r0 = move-exception
        L_0x018b:
            java.lang.RuntimeException r2 = new java.lang.RuntimeException
            java.lang.String r3 = r1.f
            java.lang.String r3 = java.lang.String.valueOf(r3)
            java.lang.String r4 = "Failed to create codec mimeType: "
            java.lang.String r3 = r4.concat(r3)
            r2.<init>(r3, r0)
            throw r2
        L_0x019d:
            java.lang.IllegalArgumentException r0 = new java.lang.IllegalArgumentException
            java.lang.String r2 = "Null mimetype provided"
            r0.<init>(r2)
            throw r0
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.exb.<init>(java.io.InputStream, java.lang.String, int, int, int):void");
    }

    private final void a(boolean z) {
        boolean z2;
        boolean z3;
        int i2;
        boolean z4;
        boolean z5;
        MediaCodec mediaCodec = this.j;
        if (mediaCodec != null) {
            if (z) {
                b(mediaCodec);
            }
            MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();
            int dequeueOutputBuffer = this.j.dequeueOutputBuffer(bufferInfo, 10000);
            if (dequeueOutputBuffer == -2) {
                MediaFormat outputFormat = this.j.getOutputFormat();
                if (this.d == outputFormat.getInteger("sample-rate")) {
                    z4 = true;
                } else {
                    z4 = false;
                }
                fvf.aF(z4);
                if (this.e == outputFormat.getInteger("channel-count")) {
                    z5 = true;
                } else {
                    z5 = false;
                }
                fvf.aF(z5);
                fvf.aF(this.f.equals(outputFormat.getString("mime")));
                dequeueOutputBuffer = this.j.dequeueOutputBuffer(bufferInfo, 10000);
            }
            if (dequeueOutputBuffer != -2) {
                z2 = true;
            } else {
                z2 = false;
            }
            fvf.aF(z2);
            int i3 = 4;
            if ((bufferInfo.flags & 4) != 0) {
                ((hby) ((hby) a.c()).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "fillOutputBuffer", 260, "AudioEncoderInputStream.java")).s("End of Output Stream : %d", bufferInfo.size);
                this.o = true;
            }
            if (dequeueOutputBuffer != -1) {
                if (dequeueOutputBuffer == -3) {
                    this.l = this.j.getOutputBuffers();
                } else if (dequeueOutputBuffer == -1) {
                } else {
                    if ((bufferInfo.flags & 2) != 0) {
                        ((hby) ((hby) a.c()).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "fillOutputBuffer", 289, "AudioEncoderInputStream.java")).r("Ignoring codec config packet");
                        this.m = dequeueOutputBuffer;
                        MediaCodec mediaCodec2 = this.j;
                        if (mediaCodec2 != null) {
                            b(mediaCodec2);
                            return;
                        }
                        return;
                    }
                    int i4 = bufferInfo.offset;
                    int i5 = bufferInfo.size;
                    hca hca = a;
                    ((hby) ((hby) hca.c()).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "onOutputBufferReady", 335, "AudioEncoderInputStream.java")).r("#onOutputBufferReady");
                    this.m = dequeueOutputBuffer;
                    int i6 = this.g;
                    if (e(i6)) {
                        this.i.clear();
                        int i7 = this.d;
                        int i8 = this.e;
                        ByteBuffer byteBuffer = this.i;
                        if (byteBuffer.remaining() >= 7) {
                            z3 = true;
                        } else {
                            z3 = false;
                        }
                        fvf.aF(z3);
                        switch (i7) {
                            case 7350:
                                i2 = 12;
                                break;
                            case 8000:
                                i2 = 11;
                                break;
                            case 11025:
                                i2 = 10;
                                break;
                            case 12000:
                                i2 = 9;
                                break;
                            case 16000:
                                i2 = 8;
                                break;
                            case 22050:
                                i2 = 7;
                                break;
                            case 24000:
                                i2 = 6;
                                break;
                            case 32000:
                                i2 = 5;
                                break;
                            case 44100:
                                i2 = 4;
                                break;
                            case 48000:
                                i2 = 3;
                                break;
                            case 64000:
                                i2 = 2;
                                break;
                            case 88200:
                                i2 = 1;
                                break;
                            case 96000:
                                i2 = 0;
                                break;
                            default:
                                throw new IllegalArgumentException(a.ak(i7, "Invalid sample rate: "));
                        }
                        long g2 = fbi.g(fbi.g(fbi.g(fbi.g(fbi.g(fbi.g(fbi.g(0, 12, 4095), 1, 0), 2, 0), 1, 1), 2, 0), 4, i2), 1, 0);
                        switch (i8) {
                            case 1:
                                i3 = 1;
                                break;
                            case 2:
                                i3 = 2;
                                break;
                            case 3:
                                i3 = 3;
                                break;
                            case 4:
                                break;
                            case 5:
                                i3 = 5;
                                break;
                            case 6:
                                i3 = 6;
                                break;
                            case 8:
                                i3 = 7;
                                break;
                            default:
                                throw new IllegalArgumentException(a.ak(i8, "Invalid channel count: "));
                        }
                        long g3 = fbi.g(fbi.g(fbi.g(fbi.g(fbi.g(fbi.g(fbi.g(fbi.g(g2, 3, i3), 1, 0), 1, 0), 1, 0), 1, 0), 13, i5 + 7), 11, 2047), 2, 0);
                        byteBuffer.put((byte) ((int) ((g3 >>> 48) & 255)));
                        byteBuffer.put((byte) ((int) ((g3 >>> 40) & 255)));
                        byteBuffer.put((byte) ((int) ((g3 >>> 32) & 255)));
                        byteBuffer.put((byte) ((int) ((g3 >>> 24) & 255)));
                        byteBuffer.put((byte) ((int) ((g3 >>> 16) & 255)));
                        byteBuffer.put((byte) ((int) ((g3 >>> 8) & 255)));
                        byteBuffer.put((byte) ((int) g3));
                        this.i.flip();
                    } else if (i6 == 3) {
                        this.i.clear();
                        if (!this.p) {
                            this.p = true;
                            int i9 = this.d;
                            int i10 = this.e;
                            ByteBuffer byteBuffer2 = this.i;
                            byteBuffer2.put("OpusHead".getBytes());
                            byteBuffer2.put((byte) 1);
                            byteBuffer2.put((byte) (i10 & 255));
                            byteBuffer2.putShort(80);
                            byteBuffer2.putInt((int) (((long) i9) & 4294967295L));
                            byteBuffer2.putShort(0);
                            byteBuffer2.put((byte) 0);
                        }
                        ((hby) ((hby) hca.c()).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "onOutputBufferReady", 349, "AudioEncoderInputStream.java")).s("#onOutputBufferReady Opus frame of size %d", i5);
                        ByteBuffer byteBuffer3 = this.i;
                        if (i5 > 223) {
                            byteBuffer3.put((byte) ((i5 & 31) + 223));
                            byteBuffer3.put((byte) ((i5 >> 5) - 7));
                        } else {
                            byteBuffer3.put((byte) i5);
                        }
                        this.i.flip();
                    }
                    ByteBuffer byteBuffer4 = this.l[dequeueOutputBuffer];
                    byteBuffer4.clear();
                    byteBuffer4.position(i4);
                    byteBuffer4.limit(i4 + i5);
                }
            }
        } else {
            throw new exh();
        }
    }

    private final void b(MediaCodec mediaCodec) {
        int i2 = this.m;
        if (i2 >= 0) {
            mediaCodec.releaseOutputBuffer(i2, false);
            this.m = -1;
        }
    }

    private final synchronized void c(MediaCodec mediaCodec, MediaFormat mediaFormat) {
        hca hca = a;
        ((hby) ((hby) hca.c()).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "startAndConfigureCodec", 179, "AudioEncoderInputStream.java")).r("#startAndConfigureCodec started");
        try {
            this.j = mediaCodec;
            mediaCodec.configure(mediaFormat, (Surface) null, (MediaCrypto) null, 1);
            this.j.start();
            this.k = this.j.getInputBuffers();
            this.l = this.j.getOutputBuffers();
            ((hby) ((hby) hca.c()).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "startAndConfigureCodec", 186, "AudioEncoderInputStream.java")).r("#startAndConfigureCodec ended");
        } catch (Exception e2) {
            throw new IllegalArgumentException("Could not create codec", e2);
        }
    }

    private final void d(String str, MediaFormat mediaFormat) {
        try {
            c(MediaCodec.createEncoderByType(str), mediaFormat);
        } catch (IOException e2) {
            throw new exh(e2);
        }
    }

    private static boolean e(int i2) {
        if (i2 == 0) {
            return true;
        }
        return false;
    }

    public final synchronized void close() {
        hgz.a(this.b);
        MediaCodec mediaCodec = this.j;
        if (mediaCodec != null) {
            try {
                mediaCodec.stop();
                this.j.release();
            } catch (IllegalStateException e2) {
                ((hby) ((hby) ((hby) a.h()).i(e2)).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "close", 203, "AudioEncoderInputStream.java")).r("MediaCodec has already been stopped or released.");
            }
            this.j = null;
            return;
        }
        ((hby) ((hby) a.h()).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "close", 196, "AudioEncoderInputStream.java")).r("close() called when codec is already closed");
    }

    public final synchronized void finalize() {
        if (this.j != null) {
            close();
            ((hby) ((hby) a.g()).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "finalize", 433, "AudioEncoderInputStream.java")).r("No one closed");
        }
    }

    public final int read() {
        throw new UnsupportedOperationException("Single byte read not supported");
    }

    public final /* synthetic */ long transferTo(OutputStream outputStream) {
        return DesugarInputStream.transferTo(this, outputStream);
    }

    public final int read(byte[] bArr) {
        return read(bArr, 0, bArr.length);
    }

    public final int read(byte[] bArr, int i2, int i3) {
        int i4;
        int i5 = 0;
        boolean z = this.n && !this.h.hasRemaining();
        while (!z && !this.n && ((r4 = this.m) == -1 || !this.l[r4].hasRemaining())) {
            MediaCodec mediaCodec = this.j;
            if (mediaCodec != null) {
                b(mediaCodec);
                synchronized (this) {
                    int dequeueInputBuffer = mediaCodec.dequeueInputBuffer(10000);
                    if (dequeueInputBuffer != -1) {
                        ((hby) ((hby) a.c()).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "onInputBufferReady", 301, "AudioEncoderInputStream.java")).s("#onInputBufferReady %d", dequeueInputBuffer);
                        ByteBuffer byteBuffer = this.k[dequeueInputBuffer];
                        byteBuffer.clear();
                        byteBuffer.position(0);
                        fvf.aF(byteBuffer.hasRemaining());
                        while (true) {
                            if (byteBuffer.position() >= this.c || !byteBuffer.hasRemaining() || this.n) {
                                break;
                            } else if (this.h.hasRemaining()) {
                                int ad = hfc.ad(this.h.remaining(), byteBuffer.remaining(), this.c - byteBuffer.position());
                                byteBuffer.put(this.h.array(), this.h.position(), ad);
                                ByteBuffer byteBuffer2 = this.h;
                                byteBuffer2.position(byteBuffer2.position() + ad);
                            } else {
                                int read = this.b.read(this.h.array());
                                if (read == -1) {
                                    break;
                                }
                                this.h.position(0);
                                this.h.limit(read);
                            }
                        }
                        if (byteBuffer.position() > 0) {
                            mediaCodec.queueInputBuffer(dequeueInputBuffer, 0, byteBuffer.position(), 0, 0);
                        } else {
                            this.n = true;
                            mediaCodec.queueInputBuffer(dequeueInputBuffer, 0, 0, 0, 4);
                        }
                    }
                }
                a(false);
            } else {
                throw new exh();
            }
        }
        if (this.n && ((i4 = this.m) == -1 || !this.l[i4].hasRemaining())) {
            if (this.o) {
                ((hby) ((hby) a.c()).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "read", 393, "AudioEncoderInputStream.java")).r("Returning -1");
                return -1;
            }
            hca hca = a;
            ((hby) ((hby) hca.c()).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "read", 397, "AudioEncoderInputStream.java")).r("Calling fillOutputBuffer explicitly");
            a(true);
            int i6 = this.m;
            if (i6 == -1 || !this.l[i6].hasRemaining()) {
                ((hby) ((hby) hca.c()).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "read", 404, "AudioEncoderInputStream.java")).r("Returning 0");
                return 0;
            }
        }
        if (this.i.hasRemaining()) {
            i5 = Math.min(i3, this.i.remaining());
            this.i.get(bArr, i2, i5);
            i2 += i5;
            i3 -= i5;
        }
        ByteBuffer byteBuffer3 = this.l[this.m];
        fvf.aF(byteBuffer3.hasRemaining());
        int min = Math.min(i3, byteBuffer3.remaining());
        byteBuffer3.get(bArr, i2, min);
        int i7 = min + i5;
        ((hby) ((hby) a.c()).j("com/google/android/libraries/speech/encoding/AudioEncoderInputStream", "read", 424, "AudioEncoderInputStream.java")).s("Returning %d", i7);
        return i7;
    }
}
