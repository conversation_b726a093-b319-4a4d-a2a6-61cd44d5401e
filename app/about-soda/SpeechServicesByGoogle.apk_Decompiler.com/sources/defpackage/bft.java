package defpackage;

import android.content.Context;
import java.util.LinkedHashSet;

/* renamed from: bft  reason: default package */
/* compiled from: PG */
public abstract class bft {
    public final Context a;
    public final Object b = new Object();
    public final LinkedHashSet c = new LinkedHashSet();
    public Object d;
    private final cyw e;

    protected bft(Context context, cyw cyw) {
        jnu.e(context, "context");
        this.e = cyw;
        Context applicationContext = context.getApplicationContext();
        jnu.d(applicationContext, "context.applicationContext");
        this.a = applicationContext;
    }

    public abstract Object b();

    public abstract void d();

    public abstract void e();

    /* JADX WARNING: type inference failed for: r1v2, types: [java.util.concurrent.Executor, java.lang.Object] */
    public final void f(Object obj) {
        synchronized (this.b) {
            Object obj2 = this.d;
            if (obj2 == null || !jnu.i(obj2, obj)) {
                this.d = obj;
                this.e.c.execute(new aku((Object) jji.B(this.c), (Object) this, 8, (char[]) null));
            }
        }
    }
}
