package defpackage;

import j$.time.Duration;

/* renamed from: euo  reason: default package */
/* compiled from: PG */
public final class euo {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/store/memory/SingleAudioRecordStore");
    public static final Duration b;
    public final hmi c;
    public final jps d = new jps((Object) null, jpt.a);
    public final eoz e;
    public final eoz f;
    private final jqs g;

    static {
        Duration ofMinutes = Duration.ofMinutes(5);
        jnu.d(ofMinutes, "ofMinutes(...)");
        b = ofMinutes;
    }

    public euo(eoz eoz, jqs jqs, hmi hmi) {
        jnu.e(eoz, "tokenGenerator");
        jnu.e(jqs, "lightweightScope");
        jnu.e(hmi, "audioTimeoutExecutorService");
        this.e = eoz;
        this.g = jqs;
        this.c = hmi;
        this.f = don.k(jqs);
    }
}
