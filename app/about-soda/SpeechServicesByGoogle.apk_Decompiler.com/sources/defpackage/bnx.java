package defpackage;

import android.car.Car;
import android.car.drivingstate.CarUxRestrictions;
import android.util.Log;

/* renamed from: bnx  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bnx implements Car.CarServiceLifecycleListener {
    public final /* synthetic */ bnz a;

    public /* synthetic */ bnx(bnz bnz) {
        this.a = bnz;
    }

    public final void onLifecycleChanged(Car car, boolean z) {
        bnz bnz = this.a;
        if (z) {
            bnz.d(car, bnz.c);
            return;
        }
        Log.w("CarUxRestrictionsUtil", "Car service disconnected, assuming fully restricted uxr");
        bnz.c.onUxRestrictionsChanged((CarUxRestrictions) null);
    }
}
