package defpackage;

import android.os.Bundle;

/* renamed from: fxb  reason: default package */
/* compiled from: PG */
public final class fxb extends acg {
    public int a = -1;
    public fxf b;
    public int c;
    public boolean d;
    public final boolean e;

    public fxb(abv abv) {
        jnu.e(abv, "savedStateHandle");
        fxf fxf = fxf.i;
        jnu.d(fxf, "getDefaultInstance(...)");
        this.b = fxf;
        Bundle bundle = (Bundle) abv.a("tiktok_activity_account_state_saved_instance_state");
        if (bundle != null) {
            this.e = true;
            this.a = bundle.getInt("state_account_id", -1);
            try {
                this.b = (fxf) ftc.aA(bundle, "state_account_info", fxf.i, hte.a());
                this.c = bundle.getInt("state_account_state", 0);
                this.d = bundle.getBoolean("tiktok_accounts_disabled");
            } catch (hui e2) {
                throw new RuntimeException("Failed to get AccountInfo from Bundle.", e2);
            }
        } else {
            this.e = false;
        }
        abv.b("tiktok_activity_account_state_saved_instance_state", new bm((Object) this, 5));
    }
}
