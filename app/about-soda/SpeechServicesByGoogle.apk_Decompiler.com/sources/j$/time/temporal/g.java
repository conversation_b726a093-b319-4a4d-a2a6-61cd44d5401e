package j$.time.temporal;

import j$.nio.file.attribute.a;
import j$.time.Duration;

enum g implements TemporalUnit {
    WEEK_BASED_YEARS("WeekBasedYears", Duration.ofSeconds(31556952)),
    QUARTER_YEARS("QuarterYears", Duration.ofSeconds(7889238));
    
    private final String a;
    private final Duration b;

    private g(String str, Duration duration) {
        this.a = str;
        this.b = duration;
    }

    public final Duration g() {
        return this.b;
    }

    public final k m(k kVar, long j) {
        int ordinal = ordinal();
        if (ordinal == 0) {
            p pVar = h.c;
            return kVar.d(a.e((long) kVar.p(pVar), j), pVar);
        } else if (ordinal == 1) {
            return kVar.f(j / 4, ChronoUnit.YEARS).f((j % 4) * 3, ChronoUnit.MONTHS);
        } else {
            throw new IllegalStateException("Unreachable");
        }
    }

    public final String toString() {
        return this.a;
    }
}
