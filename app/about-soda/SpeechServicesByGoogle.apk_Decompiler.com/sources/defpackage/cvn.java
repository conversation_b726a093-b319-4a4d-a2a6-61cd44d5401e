package defpackage;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Iterator;
import java.util.List;

/* renamed from: cvn  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cvn implements hko {
    public final /* synthetic */ Object a;
    public final /* synthetic */ Object b;
    private final /* synthetic */ int c;

    public /* synthetic */ cvn(Object obj, htq htq, int i) {
        this.c = i;
        this.a = obj;
        this.b = htq;
    }

    /* JADX WARNING: type inference failed for: r3v19, types: [java.lang.Object, java.util.Comparator] */
    /* JADX WARNING: type inference failed for: r3v20, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r3v22, types: [java.lang.Object, java.util.Comparator] */
    public final hme a(Object obj) {
        switch (this.c) {
            case 0:
                Object obj2 = this.a;
                cvy cvy = (cvy) obj2;
                return cvy.p(cvy.c.i((ctg) this.b), new btb(obj2, (grh) obj, 5));
            case 1:
                Void voidR = (Void) obj;
                csx csx = (csx) this.b;
                if (cqx.o(csx)) {
                    return ((cvy) this.a).d(csx);
                }
                return hma.a;
            case 2:
                String str = ((csx) this.b).c;
                cvy cvy2 = (cvy) this.a;
                return cvy2.p(cvy2.h.c(str), new brg((cvx) obj, 14));
            case 3:
                if (!((Boolean) obj).booleanValue()) {
                    Object obj3 = this.b;
                    Object obj4 = this.a;
                    ctg ctg = (ctg) obj3;
                    cyh.i("%s: Failed to remove pending version for group: '%s'; account: '%s'", "FileGroupManager", ctg.b, ctg.d);
                    ((cvy) obj4).i.d(1036);
                    return hfc.J(new IOException("Failed to remove pending group: ".concat(String.valueOf(ctg.b))));
                }
                kml a2 = csi.a();
                csh csh = csh.CUSTOM_FILEGROUP_VALIDATION_FAILED;
                a2.b = csh;
                a2.c = csh.name();
                return hfc.J(a2.a());
            case 4:
                ArrayList arrayList = new ArrayList();
                Iterator it = ((List) obj).iterator();
                while (true) {
                    Object obj5 = this.b;
                    if (!it.hasNext()) {
                        return cqh.U(arrayList).n(new ctw(8), ((cvy) obj5).e);
                    }
                    ctg ctg2 = (ctg) it.next();
                    if (!ctg2.e) {
                        cvy cvy3 = (cvy) obj5;
                        arrayList.add(cvy3.q(cvy3.g(ctg2, false), new cvp(obj5, ctg2, this.a, 7)));
                    }
                }
            case 5:
                if (((Boolean) obj).booleanValue()) {
                    return hma.a;
                }
                Object obj6 = this.b;
                cvy cvy4 = (cvy) this.a;
                return czw.e(cvy4.d((csx) obj6)).d(csi.class, new cvi(obj6, 12), cvy4.e);
            case 6:
                csx csx2 = (csx) obj;
                if (csx2 == null) {
                    return hma.a;
                }
                Object obj7 = this.b;
                Object obj8 = this.a;
                ctg ctg3 = (ctg) obj7;
                cyh.e("%s: Deleting file group %s for removed account %s", "FileGroupManager", ctg3.b, ctg3.c);
                cvy cvy5 = (cvy) obj8;
                cvy.z(1050, cvy5.i, csx2);
                return cvy5.q(cvy5.c.i(ctg3), new cvn(obj8, (htq) csx2, 7));
            case 7:
                if (!((Boolean) obj).booleanValue()) {
                    cvy.z(1036, ((cvy) this.a).i, (csx) this.b);
                }
                return hma.a;
            case 8:
                Void voidR2 = (Void) obj;
                Object obj9 = this.b;
                if (obj9 == null) {
                    return hma.a;
                }
                Object obj10 = this.a;
                cvy cvy6 = (cvy) obj10;
                return cvy6.q(cvy6.p(cvy6.c.c(), new brg(new gym(), 16)), new cvn(obj10, (htq) obj9, 9));
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                gyo gyo = (gyo) obj;
                ArrayList arrayList2 = new ArrayList();
                csx csx3 = (csx) this.b;
                Iterator it2 = csx3.n.iterator();
                while (true) {
                    Object obj11 = this.a;
                    if (!it2.hasNext()) {
                        return cqh.U(arrayList2).n(new ctw(9), ((cvy) obj11).e);
                    }
                    csv csv = (csv) it2.next();
                    if (!cqx.p(csv)) {
                        int x = a.x(csx3.i);
                        if (x == 0) {
                            x = 1;
                        }
                        ctj t = cqh.t(csv, x);
                        if (!gyo.contains(t)) {
                            arrayList2.add(((cvy) obj11).j.i(t));
                        }
                    }
                }
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                Object obj12 = this.a;
                cwb cwb = (cwb) obj12;
                return ftd.L(cwb.n(cwb.a.a((csx) this.b)), new cvn(obj12, (Object) (cze) obj, 17), cwb.b);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                Object obj13 = this.b;
                cwb cwb2 = (cwb) obj13;
                return ftd.L(cwb2.n(cwb2.a.c()), new cvp(obj13, (cze) obj, (Comparator) this.a, 15), cwb2.b);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                Object obj14 = this.b;
                cwb cwb3 = (cwb) obj14;
                return ftd.L(cwb3.n(cwb3.a.m(this.a)), new cwa(obj14, (Object) (cze) obj, 1), cwb3.b);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                return ((cwb) this.b).o((cze) this.a, (cze) obj, 1098);
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return ((cwb) this.b).o((cze) this.a, (cze) obj, 1089);
            case 15:
                Object obj15 = this.b;
                cwb cwb4 = (cwb) obj15;
                return ftd.L(cwb4.n(cwb4.a.d()), new cvp(obj15, (cze) obj, (Comparator) this.a, 13), cwb4.b);
            case 16:
                Object obj16 = this.a;
                cwb cwb5 = (cwb) obj16;
                return ftd.L(cwb5.n(cwb5.a.i((ctg) this.b)), new cvn(obj16, (Object) (cze) obj, 14), cwb5.b);
            case 17:
                return ((cwb) this.b).o((cze) this.a, (cze) obj, 1096);
            case 18:
                Object obj17 = this.a;
                cwb cwb6 = (cwb) obj17;
                return ftd.L(cwb6.n(cwb6.a.h((ctg) this.b)), new cvn(obj17, (Object) (cze) obj, 20), cwb6.b);
            case 19:
                return ((cwb) this.b).o((cze) this.a, (cze) obj, 1095);
            default:
                return ((cwb) this.b).o((cze) this.a, (cze) obj, 1090);
        }
    }

    public /* synthetic */ cvn(Object obj, Object obj2, int i) {
        this.c = i;
        this.b = obj;
        this.a = obj2;
    }
}
