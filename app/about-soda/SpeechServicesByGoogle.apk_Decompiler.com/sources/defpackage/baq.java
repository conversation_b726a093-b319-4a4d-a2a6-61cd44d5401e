package defpackage;

import android.net.NetworkRequest;
import java.util.Set;

/* renamed from: baq  reason: default package */
/* compiled from: PG */
public final class baq {
    public static final baq a = new baq(bbl.NOT_REQUIRED);
    public final bbl b;
    public final bie c;
    public final boolean d;
    public final boolean e;
    public final boolean f;
    public final boolean g;
    public final long h;
    public final long i;
    public final Set j;

    public baq(bie bie, bbl bbl, boolean z, boolean z2, boolean z3, boolean z4, long j2, long j3, Set set) {
        jnu.e(bie, "requiredNetworkRequestCompat");
        jnu.e(bbl, "requiredNetworkType");
        jnu.e(set, "contentUriTriggers");
        this.c = bie;
        this.b = bbl;
        this.d = z;
        this.e = z2;
        this.f = z3;
        this.g = z4;
        this.h = j2;
        this.i = j3;
        this.j = set;
    }

    public final NetworkRequest a() {
        return (NetworkRequest) this.c.b;
    }

    public final boolean b() {
        if (!this.j.isEmpty()) {
            return true;
        }
        return false;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (obj == null || !jnu.i(getClass(), obj.getClass())) {
            return false;
        }
        baq baq = (baq) obj;
        if (this.d == baq.d && this.e == baq.e && this.f == baq.f && this.g == baq.g && this.h == baq.h && this.i == baq.i && jnu.i(a(), baq.a()) && this.b == baq.b) {
            return jnu.i(this.j, baq.j);
        }
        return false;
    }

    public final int hashCode() {
        int i2;
        long j2 = this.i;
        Set set = this.j;
        long j3 = this.h;
        int hashCode = (((((((((((((this.b.hashCode() * 31) + (this.d ? 1 : 0)) * 31) + (this.e ? 1 : 0)) * 31) + (this.f ? 1 : 0)) * 31) + (this.g ? 1 : 0)) * 31) + ((int) (j3 ^ (j3 >>> 32)))) * 31) + ((int) (j2 ^ (j2 >>> 32)))) * 31) + set.hashCode();
        NetworkRequest a2 = a();
        if (a2 != null) {
            i2 = a2.hashCode();
        } else {
            i2 = 0;
        }
        return (hashCode * 31) + i2;
    }

    public final String toString() {
        return "Constraints{requiredNetworkType=" + this.b + ", requiresCharging=" + this.d + ", requiresDeviceIdle=" + this.e + ", requiresBatteryNotLow=" + this.f + ", requiresStorageNotLow=" + this.g + ", contentTriggerUpdateDelayMillis=" + this.h + ", contentTriggerMaxDelayMillis=" + this.i + ", contentUriTriggers=" + this.j + ", }";
    }

    public baq(baq baq) {
        jnu.e(baq, "other");
        this.d = baq.d;
        this.e = baq.e;
        this.c = baq.c;
        this.b = baq.b;
        this.f = baq.f;
        this.g = baq.g;
        this.j = baq.j;
        this.h = baq.h;
        this.i = baq.i;
    }

    public baq(bbl bbl) {
        jnu.e(bbl, "requiredNetworkType");
        jnu.e(bbl, "requiredNetworkType");
        jks jks = jks.a;
        jnu.e(bbl, "requiredNetworkType");
        this.c = new bie((byte[]) null);
        this.b = bbl;
        this.d = false;
        this.e = false;
        this.f = false;
        this.g = false;
        this.h = -1;
        this.i = -1;
        this.j = jks;
    }
}
