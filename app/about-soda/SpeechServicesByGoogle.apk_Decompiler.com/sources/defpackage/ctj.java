package defpackage;

/* renamed from: ctj  reason: default package */
/* compiled from: PG */
public final class ctj extends htq implements hvb {
    public static final ctj g;
    private static volatile hvh h;
    public int a;
    public String b = "";
    public int c;
    public String d = "";
    public int e;
    public ihf f;

    static {
        ctj ctj = new ctj();
        g = ctj;
        htq.z(ctj.class, ctj);
    }

    private ctj() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(g, "\u0001\u0005\u0000\u0001\u0001\u0005\u0005\u0000\u0000\u0000\u0001ဈ\u0000\u0002င\u0001\u0003ဈ\u0002\u0004᠌\u0003\u0005ဉ\u0004", new Object[]{"a", "b", "c", "d", "e", bqk.p, "f"});
        } else if (i2 == 3) {
            return new ctj();
        } else {
            if (i2 == 4) {
                return new htk((htq) g);
            }
            if (i2 == 5) {
                return g;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = h;
            if (hvh == null) {
                synchronized (ctj.class) {
                    hvh = h;
                    if (hvh == null) {
                        hvh = new htl(g);
                        h = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
