package com.google.android.apps.googlespeechoffline;

import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;

import android.content.Intent;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.os.Environment;

import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;
import android.widget.Toast;
import android.Manifest;
import android.net.Uri;
import android.media.AudioRecord;
import android.media.MediaRecorder;
import android.media.AudioFormat;

import java.nio.ByteBuffer;

import com.google.speech.recognizer.RecognitionResultCallback;
import com.google.speech.recognizer.SodaAsyncRecognizer;
import com.google.android.apps.googlespeechoffline.speech.CustomSoda;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.IOException;
import java.io.FileInputStream;

public class MainActivity extends AppCompatActivity {
    private static final int PERMISSION_REQUEST_CODE = 100;
    private static final int AUDIO_PERMISSION_REQUEST_CODE = 101;

    private Button asyncTestButton;
    private Button micRecognitionButton;
    private TextView debugTextView;
    private TextView statusTextView;
    private StringBuilder recognitionResults = new StringBuilder();
    private SodaAsyncRecognizer sodaAsyncRecognizer;

    // 音频录制相关
    private AudioRecord audioRecord;
    private boolean isRecording = false;
    private Thread recordingThread;

    // 音频参数
    private static final int SAMPLE_RATE = 16000;
    private static final int CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO;
    private static final int AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT;
    private int bufferSize;

    // 实时语音识别相关
    private CustomSoda realTimeRecognizer;
    private RecognitionResultCallback realTimeCallback;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        if (!checkPermission())
        {
            requestPermission();
        }

//        checkAllFilesPermission();

        debugTextView = (TextView) findViewById(R.id.debugTextView);
        statusTextView = (TextView) findViewById(R.id.statusTextView);

        asyncTestButton = (Button) findViewById(R.id.asyncTestButton);
        micRecognitionButton = (Button) findViewById(R.id.micRecognitionButton);

        // 异步API测试按钮点击事件
        asyncTestButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                debugTextView.setText("");
                recognitionResults.setLength(0);
                statusTextView.setText("正在运行异步API测试...");

                Thread asyncTestThread = new Thread() {
                    @Override
                    public void run() {
                        runOnUiThread(() -> {
                            debugTextView.setText("开始异步API测试...\n");
                            statusTextView.setText("测试中...");
                        });

                        // 只运行异步API测试
                        testSodaAsyncAPI();

                        runOnUiThread(() -> {
                            statusTextView.setText("异步API测试完成");
                        });
                    }
                };

                asyncTestThread.start();
            }
        });

        // 麦克风语音识别按钮点击事件
        micRecognitionButton.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (!isRecording) {
                    // 开始录制
                    debugTextView.setText("");
                    recognitionResults.setLength(0);
                    statusTextView.setText("开始麦克风识别...");

                    startRecording();
                    micRecognitionButton.setText("停止麦克风识别");
                    micRecognitionButton.setBackgroundColor(0xFFFF5722); // 红色
                } else {
                    // 停止录制
                    stopRecording();
                    statusTextView.setText("麦克风识别已停止");
                    micRecognitionButton.setText("开始麦克风识别");
                    micRecognitionButton.setBackgroundColor(0xFF4CAF50); // 绿色
                }
            }
        });
    }



    private boolean checkPermission() {
        int result = ContextCompat.checkSelfPermission(MainActivity.this, android.Manifest.permission.READ_EXTERNAL_STORAGE);
        if (result == PackageManager.PERMISSION_GRANTED) {
            return true;
        } else {
            return false;
        }
    }
    private void requestPermission() {
        if (ActivityCompat.shouldShowRequestPermissionRationale(MainActivity.this, android.Manifest.permission.READ_EXTERNAL_STORAGE)) {
            Toast.makeText(MainActivity.this, "Write External Storage permission allows us to read files. Please allow this permission in App Settings.", Toast.LENGTH_LONG).show();
        } else {
            ActivityCompat.requestPermissions(MainActivity.this, new String[]{android.Manifest.permission.READ_EXTERNAL_STORAGE}, PERMISSION_REQUEST_CODE);
        }
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String permissions[], int[] grantResults) {
        switch (requestCode) {
            case PERMISSION_REQUEST_CODE:
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Log.e("value", "Permission Granted, Now you can use local drive .");
                } else {
                    Log.e("value", "Permission Denied, You cannot use local drive .");
                }
                break;
            case AUDIO_PERMISSION_REQUEST_CODE:
                if (grantResults.length > 0 && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    Log.i("MainActivity", "Audio permission granted");
                    Toast.makeText(this, "麦克风权限已授予", Toast.LENGTH_SHORT).show();
                } else {
                    Log.e("MainActivity", "Audio permission denied");
                    Toast.makeText(this, "需要麦克风权限才能进行语音识别", Toast.LENGTH_LONG).show();
                }
                break;
        }
    }

    /**
     * 检查麦克风权限
     */
    private boolean checkAudioPermission() {
        int result = ContextCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO);
        return result == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * 请求麦克风权限
     */
    private void requestAudioPermission() {
        if (ActivityCompat.shouldShowRequestPermissionRationale(this, Manifest.permission.RECORD_AUDIO)) {
            Toast.makeText(this, "需要麦克风权限进行语音识别", Toast.LENGTH_LONG).show();
        }
        ActivityCompat.requestPermissions(this, new String[]{Manifest.permission.RECORD_AUDIO}, AUDIO_PERMISSION_REQUEST_CODE);
    }

    /**
     * 初始化音频录制
     */
    private void initAudioRecord() {
        bufferSize = AudioRecord.getMinBufferSize(SAMPLE_RATE, CHANNEL_CONFIG, AUDIO_FORMAT);

        if (ActivityCompat.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO) != PackageManager.PERMISSION_GRANTED) {
            Log.e("MainActivity", "No audio permission");
            return;
        }

        audioRecord = new AudioRecord(
            MediaRecorder.AudioSource.MIC,
            SAMPLE_RATE,
            CHANNEL_CONFIG,
            AUDIO_FORMAT,
            bufferSize
        );

        if (audioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
            Log.e("MainActivity", "AudioRecord initialization failed");
            return;
        }

        Log.i("MainActivity", "AudioRecord initialized successfully. Buffer size: " + bufferSize);
    }

    /**
     * 开始录制音频
     */
    private void startRecording() {
        if (!checkAudioPermission()) {
            requestAudioPermission();
            return;
        }

        if (audioRecord == null) {
            initAudioRecord();
        }

        if (audioRecord == null || audioRecord.getState() != AudioRecord.STATE_INITIALIZED) {
            Toast.makeText(this, "音频录制初始化失败", Toast.LENGTH_SHORT).show();
            return;
        }

        // 初始化实时识别器
        initRealTimeRecognizer();
        if (realTimeRecognizer == null) {
            Toast.makeText(this, "实时识别器初始化失败", Toast.LENGTH_SHORT).show();
            return;
        }

        isRecording = true;
        audioRecord.startRecording();

        recordingThread = new Thread(new Runnable() {
            @Override
            public void run() {
                byte[] audioBuffer = new byte[bufferSize];

                while (isRecording) {
                    int bytesRead = audioRecord.read(audioBuffer, 0, bufferSize);

                    if (bytesRead > 0 && realTimeRecognizer != null) {
                        // 将音频数据传递给 CustomSoda的 addAudio
                        ByteBuffer byteBuffer = ByteBuffer.allocateDirect(bytesRead);
                        byteBuffer.put(audioBuffer, 0, bytesRead);
                        byteBuffer.flip();

                        boolean success = realTimeRecognizer.addAudio(byteBuffer, bytesRead);

                        if (success) {
                            Log.v("MainActivity", "Successfully added " + bytesRead + " bytes of audio data to CustomSoda");
                        } else {
                            Log.w("MainActivity", "Failed to add audio data to CustomSoda");
                        }
                    }
                }
            }
        });

        recordingThread.start();
        Log.i("MainActivity", "Audio recording started");
    }

    /**
     * 停止录制音频
     */
    private void stopRecording() {
        isRecording = false;

        if (audioRecord != null) {
            audioRecord.stop();
            audioRecord.release();
            audioRecord = null;
        }

        if (recordingThread != null) {
            try {
                recordingThread.join();
            } catch (InterruptedException e) {
                Log.e("MainActivity", "Error stopping recording thread", e);
            }
            recordingThread = null;
        }

        // 清理识别器资源 - CustomSoda实现了Closeable接口
        if (realTimeRecognizer != null) {
            try {
                Log.d("MainActivity", "Closing CustomSoda recognizer");
                realTimeRecognizer.close();
                Log.d("MainActivity", "CustomSoda recognizer closed successfully");
            } catch (IOException e) {
                Log.e("MainActivity", "Error closing CustomSoda recognizer", e);
            } finally {
                realTimeRecognizer = null;
            }
        }

        Log.i("MainActivity", "Audio recording stopped and recognizer cleaned up");
    }

    /**
     * 初始化实时语音识别器
     */
    private void initRealTimeRecognizer() {
        try {
            File sdcard = Environment.getExternalStorageDirectory();
            String modelPath = sdcard.getAbsolutePath() + "/Android/asr/model/asr_en-US_v3056";

            // 检查模型路径是否存在
            File modelDir = new File(modelPath);
            if (!modelDir.exists()) {
                String error = "模型目录不存在: " + modelPath;
                Log.e("MainActivity", error);
                Toast.makeText(this, error, Toast.LENGTH_LONG).show();
                return;
            }

            Log.d("MainActivity", "Using model path: " + modelPath);

            // 创建回调
            realTimeCallback = new RecognitionResultCallback() {
                @Override
                public void onFinalResult(String hypothesis, String endpointReason) {
                    Log.i("REALTIME_SODA", "[FINAL] " + hypothesis + " (reason: " + endpointReason + ")");
                    recognitionResults.append("[实时_最终] ").append(hypothesis).append("\n");
                    runOnUiThread(() -> debugTextView.setText(recognitionResults.toString()));
                }

                @Override
                public void onPartialResult(String hypothesis) {
                    Log.d("REALTIME_SODA", "[PARTIAL] " + hypothesis);
                    runOnUiThread(() -> {
                        String currentResults = recognitionResults.toString();
                        debugTextView.setText(currentResults + "[实时_部分] " + hypothesis);
                    });
                }

                @Override
                public void onEndpointEvent(String endpointType) {
                    Log.d("REALTIME_SODA", "[ENDPOINT] " + endpointType);
                    runOnUiThread(() -> {
                        recognitionResults.append("[端点] ").append(endpointType).append("\n");
                        debugTextView.setText(recognitionResults.toString());
                    });
                }

                @Override
                public void onAudioLevelInfo(float rms, float audioLevel) {
                    Log.v("REALTIME_SODA", "[AUDIO_LEVEL] RMS: " + rms + ", Level: " + audioLevel);
                    // 可以在这里更新音频级别显示
                }

                @Override
                public void onLanguageIdEvent(String language, int confidenceLevel) {
                    Log.d("REALTIME_SODA", "[LANGID] " + language + " (confidence: " + confidenceLevel + ")");
                    runOnUiThread(() -> {
                        recognitionResults.append("[语言] ").append(language).append(" (置信度: ").append(confidenceLevel).append(")\n");
                        debugTextView.setText(recognitionResults.toString());
                    });
                }

                @Override
                public void onParseError(String error, byte[] rawData) {
                    Log.e("REALTIME_SODA", "[ERROR] " + error);
                    runOnUiThread(() -> {
                        recognitionResults.append("[错误] ").append(error).append("\n");
                        debugTextView.setText(recognitionResults.toString());
                    });
                }

                @Override
                public void onStartEvent() {
                    Log.d("REALTIME_SODA", "[START] Recognition started");
                    runOnUiThread(() -> {
                        recognitionResults.append("[开始] 识别已启动\n");
                        debugTextView.setText(recognitionResults.toString());
                    });
                }

                @Override
                public void onStopEvent() {
                    Log.d("REALTIME_SODA", "[STOP] Recognition stopped");
                    runOnUiThread(() -> {
                        recognitionResults.append("[停止] 识别已停止\n");
                        debugTextView.setText(recognitionResults.toString());
                    });
                }

                @Override
                public void onShutdownEvent() {
                    Log.d("REALTIME_SODA", "[SHUTDOWN] Recognition shutdown");
                    runOnUiThread(() -> {
                        recognitionResults.append("[关闭] 识别已关闭\n");
                        debugTextView.setText(recognitionResults.toString());
                    });
                }
            };

            // 创建识别器 - 使用新的CustomSoda实现
            Log.i("MainActivity", "Creating CustomSoda with model path: " + modelPath);
            realTimeRecognizer = CustomSoda.create(modelPath);

            if (realTimeRecognizer == null) {
                throw new RuntimeException("Failed to create CustomSoda instance");
            }

            // 设置识别回调
            realTimeRecognizer.setRecognitionCallback(realTimeCallback);

            Log.i("MainActivity", "Real-time recognizer initialized successfully");
            Log.i("MainActivity", "SODA initialized: " + realTimeRecognizer.isInitialized());
            Log.i("MainActivity", "SODA capture started: " + realTimeRecognizer.isCaptureStarted());

        } catch (Exception e) {
            Log.e("MainActivity", "Failed to initialize real-time recognizer", e);
            Toast.makeText(this, "初始化实时识别器失败: " + e.getMessage(), Toast.LENGTH_LONG).show();

            // 清理失败的识别器
            if (realTimeRecognizer != null) {
                try {
                    realTimeRecognizer.close();
                } catch (IOException closeException) {
                    Log.e("MainActivity", "Failed to close recognizer during error cleanup", closeException);
                }
                realTimeRecognizer = null;
            }
        }
    }


    public void checkAllFilesPermission() {
                try {
                    Intent intent = new Intent("android.settings.MANAGE_APP_ALL_FILES_ACCESS_PERMISSION");
                    intent.setData(Uri.parse("package:" + getPackageName()));
                    startActivity(intent);
                } catch (Exception e) {
                    Toast.makeText(this, "无法跳转到权限设置页面，请手动允许所有文件访问权限", Toast.LENGTH_LONG).show();
                    Intent intent = new Intent("android.settings.MANAGE_APP_ALL_FILES_ACCESS_PERMISSION");
                    startActivity(intent);
                }
    }

    /**
     * 测试SODA异步API - 使用真实音频文件
     */
    private void testSodaAsyncAPI() {
        Log.i("MainActivity", "测试SODA异步API...");

        try {
            File sdcard = Environment.getExternalStorageDirectory();

            // 读取配置文件
//            File configFile = new File(sdcard.getAbsolutePath() + "/Android/asr/model/asr_en-US_v3056/configs/ONDEVICE_MEDIUM_SHORT.config");
            File configFile = new File(sdcard.getAbsolutePath() + "/Android/asr/model/asr_en-US_v3056/configs/ONDEVICE_MEDIUM_CONTINUOUS.config");
//            File configFile = new File(sdcard.getAbsolutePath() + "/Android/asr/model/zh-CN/configs/ONDEVICE_MEDIUM_CONTINUOUS.config");
            if (!configFile.exists()) {
                String error = "配置文件不存在: " + configFile.getAbsolutePath();
                Log.e("MainActivity", error);
                recognitionResults.append("[ERROR] ").append(error).append("\n");
                runOnUiThread(() -> debugTextView.setText(recognitionResults.toString()));
                return;
            }

            byte[] dictationConfig = new byte[(int)configFile.length()];
            BufferedInputStream buf = new BufferedInputStream(new FileInputStream(configFile));
            buf.read(dictationConfig, 0, dictationConfig.length);
            buf.close();

            // 创建回调
            RecognitionResultCallback asyncCallback = new RecognitionResultCallback() {
                @Override
                public void onFinalResult(String hypothesis, String endpointReason) {
                    Log.i("SODA_ASYNC", "[FINAL] " + hypothesis + " (reason: " + endpointReason + ")");
                    recognitionResults.append("[ASYNC_FINAL] ").append(hypothesis).append("\n");
                    runOnUiThread(() -> debugTextView.setText(recognitionResults.toString()));
                }

                @Override
                public void onPartialResult(String hypothesis) {
                    Log.d("SODA_ASYNC", "[PARTIAL] " + hypothesis);
                    runOnUiThread(() -> {
                        String currentResults = recognitionResults.toString();
                        debugTextView.setText(currentResults + "[ASYNC_PARTIAL] " + hypothesis);
                    });
                }

                @Override
                public void onEndpointEvent(String endpointType) {
                    Log.d("SODA_ASYNC", "[ENDPOINT] " + endpointType);
                    recognitionResults.append("[ASYNC_ENDPOINT] ").append(endpointType).append("\n");
                    runOnUiThread(() -> debugTextView.setText(recognitionResults.toString()));
                }

                @Override
                public void onAudioLevelInfo(float rms, float audioLevel) {
                    Log.v("SODA_ASYNC", "[AUDIO] RMS: " + rms + ", Level: " + audioLevel);
                }

                @Override
                public void onLanguageIdEvent(String language, int confidenceLevel) {
                    Log.d("SODA_ASYNC", "[LANGID] " + language + " (confidence: " + confidenceLevel + ")");
                    recognitionResults.append("[ASYNC_LANGID] ").append(language)
                            .append(" (confidence: ").append(confidenceLevel).append(")\n");
                    runOnUiThread(() -> debugTextView.setText(recognitionResults.toString()));
                }

                @Override
                public void onParseError(String error, byte[] rawData) {
                    Log.e("SODA_ASYNC", "[ERROR] " + error);
                    recognitionResults.append("[ASYNC_ERROR] ").append(error).append("\n");
                    runOnUiThread(() -> debugTextView.setText(recognitionResults.toString()));
                }
            };

            // 使用真实的配置文件创建SODA配置
            Log.d("MainActivity", "Using real config file, size: " + dictationConfig.length);

            // 创建异步识别器，传入配置和模型路径
            sodaAsyncRecognizer = new SodaAsyncRecognizer(dictationConfig, asyncCallback);

            // 设置模型路径
            sodaAsyncRecognizer.setModelPath(sdcard.getAbsolutePath() + "/Android/asr/model/asr_en-US_v3056");
//            sodaAsyncRecognizer.setModelPath(sdcard.getAbsolutePath() + "/Android/asr/model/zh-CN");

            // 开始识别
            sodaAsyncRecognizer.start();

            // 读取真实音频文件
//            File audioFile = new File(sdcard.getAbsolutePath() + "/Android/asr/en_all.wav");
//            File audioFile = new File(sdcard.getAbsolutePath() + "/Android/asr/audio.wav");
            File audioFile = new File(sdcard.getAbsolutePath() + "/Android/asr/en_cut.wav");
//            File audioFile = new File(sdcard.getAbsolutePath() + "/Android/asr/zh-cn.wav");
            if (!audioFile.exists()) {
                String error = "音频文件不存在: " + audioFile.getAbsolutePath();
                Log.e("MainActivity", error);
                recognitionResults.append("[ERROR] ").append(error).append("\n");
                runOnUiThread(() -> debugTextView.setText(recognitionResults.toString()));
                return;
            }

            // 分块读取音频文件并发送给识别器
            FileInputStream audioStream = new FileInputStream(audioFile);
            byte[] buffer = new byte[1024]; // 1KB chunks
            int bytesRead;
            int totalBytes = 0;

            Log.i("MainActivity", "开始发送音频数据到异步识别器...");
            recognitionResults.append("[INFO] 开始发送音频数据...\n");
            runOnUiThread(() -> debugTextView.setText(recognitionResults.toString()));

            while ((bytesRead = audioStream.read(buffer)) != -1) {
                byte[] audioChunk = new byte[bytesRead];
                System.arraycopy(buffer, 0, audioChunk, 0, bytesRead);
                sodaAsyncRecognizer.addAudio(audioChunk);
                totalBytes += bytesRead;
            }

            audioStream.close();

            Log.i("MainActivity", "音频数据发送完成，总计: " + totalBytes + " 字节");
            recognitionResults.append("[INFO] 音频数据发送完成，总计: ").append(totalBytes).append(" 字节\n");
            recognitionResults.append("[INFO] 开始识别...\n");
            runOnUiThread(() -> debugTextView.setText(recognitionResults.toString()));

            // 开始识别
            sodaAsyncRecognizer.finishAndRecognize();

            Log.i("MainActivity", "SODA异步API测试完成");

        } catch (Exception e) {
            Log.e("MainActivity", "SODA异步API测试失败", e);
            recognitionResults.append("[ERROR] SODA异步API测试失败: ").append(e.getMessage()).append("\n");
            runOnUiThread(() -> debugTextView.setText(recognitionResults.toString()));
        }
    }



    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 清理异步识别器
        if (sodaAsyncRecognizer != null) {
            sodaAsyncRecognizer.delete();
            sodaAsyncRecognizer = null;
        }
        
        // 清理实时识别器
        if (realTimeRecognizer != null) {
            realTimeRecognizer = null;
        }
    }
}
