package defpackage;

import android.database.Cursor;
import androidx.wear.ambient.AmbientLifecycleObserverKt;
import androidx.work.impl.WorkDatabase;
import java.util.Objects;
import java.util.concurrent.Callable;

/* renamed from: bdv  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bdv implements Callable {
    public final /* synthetic */ eez a;
    public final /* synthetic */ we b;

    public /* synthetic */ bdv(we weVar, eez eez) {
        this.b = weVar;
        this.a = eez;
    }

    /* JADX WARNING: type inference failed for: r2v3, types: [bhf, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v5, types: [bhf, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v6, types: [bhf, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v4, types: [bhf, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v7, types: [bhf, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v24, types: [bhf, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r2v27, types: [bhf, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v19, types: [java.lang.Object, bgd] */
    /* JADX WARNING: type inference failed for: r7v1, types: [bhf, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r7v9, types: [bhf, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r7v10, types: [bhf, java.lang.Object] */
    public final Object call() {
        boolean z;
        eez eez = this.a;
        we weVar = this.b;
        boolean z2 = true;
        if (weVar instanceof bdt) {
            AmbientLifecycleObserverKt ambientLifecycleObserverKt = ((bdt) weVar).a;
            ? r2 = eez.k;
            Object obj = eez.j;
            Object obj2 = eez.b;
            String str = (String) obj;
            bbx a2 = r2.a(str);
            ((WorkDatabase) obj2).z().a(str);
            if (a2 != null) {
                if (a2 == bbx.RUNNING) {
                    if (ambientLifecycleObserverKt instanceof bbi) {
                        bbk.a().e(bdy.a, "Worker result SUCCESS for ".concat((String) eez.h));
                        if (((bhe) eez.a).d()) {
                            eez.k();
                        } else {
                            eez.k.m(bbx.SUCCEEDED, (String) eez.j);
                            jnu.c(ambientLifecycleObserverKt, "null cannot be cast to non-null type androidx.work.ListenableWorker.Result.Success");
                            bat bat = ((bbi) ambientLifecycleObserverKt).a;
                            jnu.d(bat, "success.outputData");
                            eez.k.i((String) eez.j, bat);
                            ? r1 = eez.g;
                            Object obj3 = eez.j;
                            long currentTimeMillis = System.currentTimeMillis();
                            for (String str2 : r1.a((String) obj3)) {
                                if (eez.k.a(str2) == bbx.BLOCKED) {
                                    Object obj4 = eez.g;
                                    auu a3 = auu.a("SELECT COUNT(*)=0 FROM dependency WHERE work_spec_id=? AND prerequisite_id IN (SELECT id FROM workspec WHERE state!=2)", 1);
                                    a3.g(1, str2);
                                    bgf bgf = (bgf) obj4;
                                    bgf.a.k();
                                    Cursor f = vy.f(bgf.a, a3, false);
                                    try {
                                        if (!f.moveToFirst() || f.getInt(0) == 0) {
                                            z = false;
                                        } else {
                                            z = true;
                                        }
                                        if (z) {
                                            bbk.a().e(bdy.a, "Setting status to enqueued for ".concat(String.valueOf(str2)));
                                            eez.k.m(bbx.ENQUEUED, str2);
                                            eez.k.h(str2, currentTimeMillis);
                                        }
                                    } finally {
                                        f.close();
                                        a3.j();
                                    }
                                }
                            }
                        }
                    } else if (ambientLifecycleObserverKt instanceof bbh) {
                        bbk.a().e(bdy.a, "Worker result RETRY for ".concat((String) eez.h));
                        eez.j(-256);
                        return Boolean.valueOf(z2);
                    } else {
                        bbk.a().e(bdy.a, "Worker result FAILURE for ".concat((String) eez.h));
                        if (((bhe) eez.a).d()) {
                            eez.k();
                        } else {
                            if (ambientLifecycleObserverKt == null) {
                                ambientLifecycleObserverKt = new bbg();
                            }
                            eez.l(ambientLifecycleObserverKt);
                        }
                    }
                } else if (!a2.a()) {
                    eez.j(-512);
                    return Boolean.valueOf(z2);
                }
            }
        } else if (weVar instanceof bds) {
            eez.l(((bds) weVar).a);
        } else if (weVar instanceof bdu) {
            int i = ((bdu) weVar).a;
            bbx a4 = eez.k.a((String) eez.j);
            if (a4 == null || a4.a()) {
                String str3 = bdy.a;
                bbk.a();
                String str4 = (String) eez.j;
                Objects.toString(a4);
            } else {
                String str5 = bdy.a;
                bbk.a();
                String str6 = (String) eez.j;
                Objects.toString(a4);
                eez.k.m(bbx.ENQUEUED, (String) eez.j);
                eez.k.j((String) eez.j, i);
                eez.k.l((String) eez.j, -1);
                return Boolean.valueOf(z2);
            }
        } else {
            throw new jjq();
        }
        z2 = false;
        return Boolean.valueOf(z2);
    }
}
