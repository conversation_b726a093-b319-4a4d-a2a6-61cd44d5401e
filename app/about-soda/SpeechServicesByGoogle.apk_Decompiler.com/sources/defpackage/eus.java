package defpackage;

import android.content.Context;
import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.util.ArrayList;
import java.util.List;

/* renamed from: eus  reason: default package */
/* compiled from: PG */
public final class eus implements AutoCloseable {
    public static final hca a = hca.m("com/google/android/libraries/search/audio/store/persistent/AudioFileWriter");
    public static final jpl b = new jpl("[\\\\/:*?\"<>|]");
    public final Context c;
    public final boolean d;
    public final eup e;
    public int f;
    public final int g;
    public final jpp h = new jpp(false, jpt.a);
    public RandomAccessFile i;
    public File j;
    public RandomAccessFile k;
    public File l;
    private final jqs m;
    private final int n;
    private final jjo o = new jjw(new eoe(this, 7));
    private final jjo p = new jjw(new eoe(this, 8));
    private final jjo q = new jjw(new eoe(this, 9));
    private final jjo r = new jjw(new eoe(this, 11));
    private final jjo s = new jjw(new eoe(this, 10));
    private final eoz t;

    public eus(Context context, jqs jqs, cqx cqx, boolean z, long j2, long j3, fbi fbi, eup eup) {
        jnu.e(jqs, "blockingScope");
        jnu.e(cqx, "clock");
        jnu.e(fbi, "audioTimeConverter");
        this.c = context;
        this.m = jqs;
        this.d = z;
        this.e = eup;
        eoz k2 = don.k(jqs);
        this.t = k2;
        this.g = jnu.o(3840, (int) j3) * 1024;
        this.n = jnu.o(10, (int) j2);
        k2.o(new egq(this, (jlr) null, 3));
    }

    public static final File h(String str) {
        File file = new File(str);
        File parentFile = file.getParentFile();
        if (parentFile != null) {
            parentFile.mkdirs();
        }
        if (file.exists() && !file.delete()) {
            throw new IOException();
        } else if (file.createNewFile()) {
            return file;
        } else {
            throw new IOException();
        }
    }

    private final String i() {
        return (String) this.q.a();
    }

    private final String j() {
        return (String) this.s.a();
    }

    public final hme a() {
        if (this.h.b()) {
            return hfc.I();
        }
        return this.t.o(new egq(this, (jlr) null, 4, (byte[]) null));
    }

    public final hme b(hsq hsq, Integer num, Long l2) {
        if (this.h.b()) {
            return hfc.I();
        }
        return this.t.o(new egr(this, hsq, num, l2, (jlr) null, 5));
    }

    public final String c() {
        return (String) this.o.a();
    }

    public final void close() {
        a();
    }

    public final String d() {
        return (String) this.p.a();
    }

    public final String e() {
        return (String) this.r.a();
    }

    public final void f() {
        if (!this.h.c()) {
            RandomAccessFile randomAccessFile = this.i;
            File file = null;
            if (randomAccessFile != null) {
                esx.o(randomAccessFile, euq.a);
                hca hca = a;
                ((hby) hca.f().h(hdg.a, "ALT.AudioFileWriter").j("com/google/android/libraries/search/audio/store/persistent/AudioFileWriter", "closeFile", 235, "AudioFileWriter.kt")).A("#audio# finalized audio(%s); wrote %d", d(), this.f);
                File file2 = this.j;
                if (file2 == null) {
                    jnu.h("file");
                    file2 = null;
                }
                if (file2.renameTo(new File(i()))) {
                    ((hby) hca.f().h(hdg.a, "ALT.AudioFileWriter").j("com/google/android/libraries/search/audio/store/persistent/AudioFileWriter", "closeFile", 237, "AudioFileWriter.kt")).u("#audio# final audio location: %s", i());
                }
            }
            RandomAccessFile randomAccessFile2 = this.k;
            if (randomAccessFile2 != null) {
                esx.o(randomAccessFile2, euq.c);
                File file3 = this.l;
                if (file3 == null) {
                    jnu.h("timestampFile");
                    file3 = null;
                }
                if (file3.length() == 0) {
                    ((hby) a.f().h(hdg.a, "ALT.AudioFileWriter").j("com/google/android/libraries/search/audio/store/persistent/AudioFileWriter", "closeTimestampsFile", 248, "AudioFileWriter.kt")).u("#audio# no audio timestamps at: %s, removing...", e());
                    File file4 = this.l;
                    if (file4 == null) {
                        jnu.h("timestampFile");
                    } else {
                        file = file4;
                    }
                    file.delete();
                    return;
                }
                hca hca2 = a;
                ((hby) hca2.f().h(hdg.a, "ALT.AudioFileWriter").j("com/google/android/libraries/search/audio/store/persistent/AudioFileWriter", "closeTimestampsFile", 252, "AudioFileWriter.kt")).u("#audio# finalized audio timestamps(%s)", e());
                File file5 = this.l;
                if (file5 == null) {
                    jnu.h("timestampFile");
                } else {
                    file = file5;
                }
                if (file.renameTo(new File(j()))) {
                    ((hby) hca2.f().h(hdg.a, "ALT.AudioFileWriter").j("com/google/android/libraries/search/audio/store/persistent/AudioFileWriter", "closeTimestampsFile", 254, "AudioFileWriter.kt")).u("#audio# final audio timestamps location: %s", j());
                }
            }
        }
    }

    public final void g(File file, String str) {
        File[] listFiles;
        if (file != null && file.exists() && file.isDirectory() && (listFiles = file.listFiles()) != null) {
            ArrayList arrayList = new ArrayList();
            for (File file2 : listFiles) {
                if (file2.isFile()) {
                    jnu.b(file2);
                    jnu.e(file2, "<this>");
                    String name = file2.getName();
                    jnu.d(name, "getName(...)");
                    if (jnu.i(job.L(name, ""), str)) {
                        arrayList.add(file2);
                    }
                }
            }
            List z = jji.z(arrayList, new dfj(6));
            if (z != null && z.size() > this.n) {
                int size = z.size() - this.n;
                ((hby) a.f().h(hdg.a, "ALT.AudioFileWriter").j("com/google/android/libraries/search/audio/store/persistent/AudioFileWriter", "maintainMaxFiles", 286, "AudioFileWriter.kt")).u("#audio# removing oldest recordings files(*.%s) to free space...", str);
                for (File delete : jji.A(z, size)) {
                    delete.delete();
                }
            }
        }
    }
}
