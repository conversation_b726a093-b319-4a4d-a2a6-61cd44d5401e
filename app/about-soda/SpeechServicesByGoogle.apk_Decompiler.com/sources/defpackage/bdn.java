package defpackage;

import android.content.Context;
import android.content.SharedPreferences;

/* renamed from: bdn  reason: default package */
/* compiled from: PG */
public final class bdn extends avu {
    private final Context c;

    /* JADX INFO: super call moved to the top of the method (can break code semantics) */
    public bdn(Context context) {
        super(9, 10);
        jnu.e(context, "context");
        this.c = context;
    }

    public final void a(awl awl) {
        awl.g("CREATE TABLE IF NOT EXISTS `Preference` (`key` TEXT NOT NULL, `long_value` INTEGER, PRIMARY KEY(`key`))");
        SharedPreferences sharedPreferences = this.c.getSharedPreferences("androidx.work.util.preferences", 0);
        if (sharedPreferences.contains("reschedule_needed") || sharedPreferences.contains("last_cancel_all_time_ms")) {
            long j = 0;
            long j2 = sharedPreferences.getLong("last_cancel_all_time_ms", 0);
            if (true == sharedPreferences.getBoolean("reschedule_needed", false)) {
                j = 1;
            }
            awl.d();
            try {
                awl.l(new Object[]{"last_cancel_all_time_ms", Long.valueOf(j2)});
                awl.l(new Object[]{"reschedule_needed", Long.valueOf(j)});
                sharedPreferences.edit().clear().apply();
                awl.h();
            } finally {
                awl.f();
            }
        }
        Context context = this.c;
        jnu.e(context, "context");
        SharedPreferences sharedPreferences2 = context.getSharedPreferences("androidx.work.util.id", 0);
        if (sharedPreferences2.contains("next_job_scheduler_id") || sharedPreferences2.contains("next_job_scheduler_id")) {
            int i = sharedPreferences2.getInt("next_job_scheduler_id", 0);
            int i2 = sharedPreferences2.getInt("next_alarm_manager_id", 0);
            awl.d();
            try {
                awl.l(new Object[]{"next_job_scheduler_id", Integer.valueOf(i)});
                awl.l(new Object[]{"next_alarm_manager_id", Integer.valueOf(i2)});
                sharedPreferences2.edit().clear().apply();
                awl.h();
            } finally {
                awl.f();
            }
        }
    }
}
