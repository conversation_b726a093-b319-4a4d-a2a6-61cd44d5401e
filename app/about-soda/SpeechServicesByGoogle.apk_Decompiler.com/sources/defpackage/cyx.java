package defpackage;

import android.content.Context;
import android.text.TextUtils;
import java.io.IOException;
import java.io.OutputStream;

/* renamed from: cyx  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cyx implements hkn {
    public final /* synthetic */ Context a;
    public final /* synthetic */ String b;
    public final /* synthetic */ long c;
    public final /* synthetic */ csv d;
    public final /* synthetic */ csx e;
    public final /* synthetic */ kjd f;

    public /* synthetic */ cyx(Context context, String str, long j, kjd kjd, csv csv, csx csx) {
        this.a = context;
        this.b = str;
        this.c = j;
        this.f = kjd;
        this.d = csv;
        this.e = csx;
    }

    public final hme a() {
        String str = "";
        Context context = this.a;
        String str2 = this.b;
        long j = this.c;
        kjd kjd = this.f;
        csv csv = this.d;
        csx csx = this.e;
        int i = 0;
        try {
            gry gry = fnt.a;
            OutputStream outputStream = (OutputStream) kjd.e(ftc.K(String.valueOf(str2).concat(".lease"), context.getPackageName(), j), new fou());
            if (outputStream != null) {
                outputStream.close();
            }
        } catch (foa e2) {
            if (!TextUtils.isEmpty(e2.getMessage())) {
                str = e2.getMessage();
            }
            cyh.l("%s: Failed to share file %s, file group %s. UnsupportedFileStorageOperation was thrown with message \"%s\"", "AndroidSharingUtil", csv.b, csx.c, str);
            str = "UnsupportedFileStorageOperation was thrown: ".concat(String.valueOf(str));
            i = 24;
        } catch (fny unused) {
            cyh.i("%s: Malformed lease uri file %s, file group %s", "AndroidSharingUtil", csv.b, csx.c);
            str = String.format("Malformed lease Uri for file %s, group %s", new Object[]{csv.b, csx.c});
            i = 18;
        } catch (fnx unused2) {
            cyh.i("%s: Failed to share after download for file %s, file group %s due to LimitExceededException", "AndroidSharingUtil", csv.b, csx.c);
            str = String.format("System limit exceeded for file %s, group %s", new Object[]{csv.b, csx.c});
            i = 25;
        } catch (IOException unused3) {
            cyh.i("%s: Failed to acquire lease for file %s, file group %s", "AndroidSharingUtil", csv.b, csx.c);
            str = String.format("Error while acquiring lease for file %s, group %s", new Object[]{csv.b, csx.c});
            i = 20;
        }
        if (i == 0) {
            return hma.a;
        }
        throw new cza(i, str);
    }
}
