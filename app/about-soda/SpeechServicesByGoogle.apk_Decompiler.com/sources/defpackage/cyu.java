package defpackage;

import android.content.Context;
import java.util.concurrent.Executor;

/* renamed from: cyu  reason: default package */
/* compiled from: PG */
public final class cyu implements iiu {
    private final jjk a;
    private final jjk b;
    private final jjk c;
    private final jjk d;
    private final /* synthetic */ int e;

    public cyu(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, int i) {
        this.e = i;
        this.a = jjk;
        this.b = jjk2;
        this.c = jjk3;
        this.d = jjk4;
    }

    public static gig c(Context context, hmh hmh, cyk cyk, cxi cxi) {
        gif a2 = gig.a();
        a2.a = "DestSharedFiles.pb";
        a2.d(ctn.b);
        a2.b(cqh.T(context, hmh, cyk, cxi, gqd.a));
        return a2.a();
    }

    public static gig d(Context context, hmh hmh, cyk cyk, cxi cxi) {
        gif a2 = gig.a();
        a2.a = "DestFileGroups.pb";
        a2.d(cte.d);
        a2.b(cqh.S(context, hmh, cyk, cxi, gqd.a));
        return a2.a();
    }

    public static gig e(Context context, hmh hmh, cyk cyk, cxi cxi) {
        gif a2 = gig.a();
        a2.a = "DiagFileGroups.pb";
        a2.d(cte.d);
        a2.b(cqh.S(context, hmh, cyk, cxi, gqd.a));
        return a2.a();
    }

    public static gig f(Context context, hmh hmh, cyk cyk, cxi cxi) {
        gif a2 = gig.a();
        a2.a = "DiagSharedFiles.pb";
        a2.d(ctn.b);
        a2.b(cqh.T(context, hmh, cyk, cxi, gqd.a));
        return a2.a();
    }

    public final gig a() {
        int i = this.e;
        if (i == 0) {
            return c(((iim) this.a).a(), (hmh) this.b.b(), (cyk) this.c.b(), ((cwp) this.d).b());
        } else if (i == 1) {
            return d(((iim) this.a).a(), (hmh) this.b.b(), (cyk) this.c.b(), ((cwp) this.d).b());
        } else if (i == 2) {
            return e(((iim) this.a).a(), (hmh) this.b.b(), (cyk) this.c.b(), ((cwp) this.d).b());
        } else if (i != 3) {
            jjk jjk = this.d;
            jjk jjk2 = this.b;
            jjk jjk3 = this.a;
            kjd a2 = ((fyk) this.c).b();
            fya a3 = ((fyb) jjk3).b();
            hmh a4 = ((fxx) jjk2).b();
            gif a5 = gig.a();
            a5.a = "AccountData";
            a5.d(fyo.d);
            a5.b(new fyg((alx) a2.c, a2));
            a5.c(a3);
            a5.c = new fyl((Object) (cqd) jjk.b(), (Executor) a4, 1);
            return a5.a();
        } else {
            return f(((iim) this.a).a(), (hmh) this.b.b(), (cyk) this.c.b(), ((cwp) this.d).b());
        }
    }

    public final /* synthetic */ Object b() {
        int i = this.e;
        if (i == 0) {
            return a();
        }
        if (i == 1) {
            return a();
        }
        if (i == 2) {
            return a();
        }
        if (i != 3) {
            return a();
        }
        return a();
    }

    public cyu(jjk jjk, jjk jjk2, jjk jjk3, jjk jjk4, int i, int[] iArr) {
        this.e = i;
        this.c = jjk;
        this.a = jjk2;
        this.b = jjk3;
        this.d = jjk4;
    }
}
