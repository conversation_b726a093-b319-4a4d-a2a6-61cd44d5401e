package defpackage;

import android.view.View;
import com.android.car.ui.toolbar.TabLayout;

/* renamed from: bnh  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class bnh implements View.OnClickListener {
    public final /* synthetic */ TabLayout a;
    public final /* synthetic */ int b;

    public /* synthetic */ bnh(TabLayout tabLayout, int i) {
        this.a = tabLayout;
        this.b = i;
    }

    /* JADX WARNING: type inference failed for: r0v5, types: [java.util.function.Consumer, java.lang.Object] */
    public final void onClick(View view) {
        TabLayout tabLayout = this.a;
        int size = tabLayout.b.size();
        int i = this.b;
        if (i < size) {
            int i2 = tabLayout.c;
            if (i != i2) {
                tabLayout.c = i;
                tabLayout.a(i2);
                tabLayout.a(i);
                bng bng = (bng) tabLayout.b.get(i);
                ? r0 = bng.d;
                if (r0 != 0) {
                    r0.accept(bng);
                    return;
                }
                return;
            }
            return;
        }
        throw new IllegalArgumentException(a.ak(i, "Tab position is invalid: "));
    }
}
