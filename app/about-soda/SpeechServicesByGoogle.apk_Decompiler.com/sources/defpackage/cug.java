package defpackage;

import android.util.Log;
import j$.util.Collection;
import java.util.List;

/* renamed from: cug  reason: default package */
/* compiled from: PG */
public final class cug implements hls {
    private final /* synthetic */ int a;

    public cug(int i) {
        this.a = i;
    }

    public final void a(Throwable th) {
        int i = this.a;
        if (i == 0) {
            cyh.n("%s: Failed to commitToFlagSnapshot: %s", "MobileDataDownloadTikTokBuilder", th);
        } else if (i == 1) {
            ((hby) ((hby) btf.a.h()).j("com/google/android/apps/speech/tts/googletts/local/voicepack/VoiceDataDownloader$1", "onFailure", 228, "VoiceDataDownloader.java")).r("Initialization failed. Will retry on subsequent voice syncs.");
        } else if (i != 2) {
            if (i == 3) {
                ((hdc) ((hdc) ((hdc) ezl.a.g()).i(th)).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackPopulator$3", "onFailure", 353, "ZipfileLanguagePackPopulator.java")).r("Failed to remove LanguagePack file groups");
            } else if (i == 4) {
                ((hdc) ((hdc) ((hdc) ezo.a.h()).i(th)).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackSourceImpl$1", "onFailure", 182, "ZipfileLanguagePackSourceImpl.java")).r("Pending LP download pings failed.");
            } else if (i != 5) {
                ((hby) ((hby) ((hby) ffa.a.g()).i(th)).j("com/google/android/libraries/speech/transcription/recognition/audio/impl/AudioLibraryAudioController$4", "onFailure", 378, "AudioLibraryAudioController.java")).r("Can't acquire audio focus!");
            } else {
                ((hby) ((hby) ((hby) ffa.a.g()).i(th)).j("com/google/android/libraries/speech/transcription/recognition/audio/impl/AudioLibraryAudioController$3", "onFailure", 336, "AudioLibraryAudioController.java")).r("Can't release audio focus!");
            }
        } else if (Log.isLoggable("ClearcutMetricXmitter", 4)) {
            Log.i("ClearcutMetricXmitter", "Transmission has failed: ".concat(String.valueOf(String.valueOf(th))));
        }
    }

    public final /* synthetic */ void b(Object obj) {
        int i = this.a;
        if (i != 0) {
            int i2 = 1;
            if (i == 1) {
                dbl dbl = (dbl) obj;
                ((hby) ((hby) btf.a.f()).j("com/google/android/apps/speech/tts/googletts/local/voicepack/VoiceDataDownloader$1", "onSuccess", 223, "VoiceDataDownloader.java")).r("Successfully initialized Superpacks.");
            } else if (i == 2) {
                Void voidR = (Void) obj;
            } else if (i == 3) {
                List list = (List) obj;
                if (!list.isEmpty()) {
                    if (Collection.EL.stream(list).allMatch(new eyf(10))) {
                        ((hdc) ((hdc) ezl.a.f()).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackPopulator$3", "onSuccess", 342, "ZipfileLanguagePackPopulator.java")).s("Successfully removed %d LanguagePack file groups", list.size());
                    } else {
                        ((hdc) ((hdc) ezl.a.g()).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackPopulator$3", "onSuccess", 345, "ZipfileLanguagePackPopulator.java")).t("Failed to completely remove LanguagePack file groups. %d remain.", Collection.EL.stream(list).filter(new eyf(11)).count());
                    }
                }
            } else if (i == 4) {
                ((hdc) ((hdc) ezo.a.b()).j("com/google/android/libraries/speech/modeldownload/languagepacks/zipfile/ZipfileLanguagePackSourceImpl$1", "onSuccess", 175, "ZipfileLanguagePackSourceImpl.java")).u("Pinged LP downloads for packs: [%s]", exo.a((gxq) obj));
            } else if (i != 5) {
                hby hby = (hby) ((hby) ffa.a.f()).j("com/google/android/libraries/speech/transcription/recognition/audio/impl/AudioLibraryAudioController$4", "onSuccess", 372, "AudioLibraryAudioController.java");
                int e = dnk.e(((dyk) obj).b);
                if (e != 0) {
                    i2 = e;
                }
                hby.u("Audio focus acquiring status: %s", dnk.d(i2));
            } else {
                hby hby2 = (hby) ((hby) ffa.a.f()).j("com/google/android/libraries/speech/transcription/recognition/audio/impl/AudioLibraryAudioController$3", "onSuccess", 330, "AudioLibraryAudioController.java");
                int c = dnk.c(((dyn) obj).b);
                if (c != 0) {
                    i2 = c;
                }
                hby2.u("Audio focus releasing status: %s", dnk.b(i2));
            }
        } else {
            Void voidR2 = (Void) obj;
            cyh.c("%s: Succeeded commitToFlagSnapshot.", "MobileDataDownloadTikTokBuilder");
        }
    }
}
