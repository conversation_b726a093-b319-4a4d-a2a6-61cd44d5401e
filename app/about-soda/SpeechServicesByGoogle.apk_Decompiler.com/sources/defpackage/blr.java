package defpackage;

import android.content.Context;
import android.support.v7.widget.RecyclerView;
import android.view.View;
import androidx.preference.Preference;
import j$.util.Objects;

/* renamed from: blr  reason: default package */
/* compiled from: PG */
public final class blr extends ko {
    public final Context d;
    public RecyclerView e;
    private ka f;
    private ka g;

    public blr(Context context) {
        this.d = context;
    }

    public static float h(View view, ka kaVar) {
        int j = kaVar.j();
        int f2 = kaVar.f();
        int d2 = kaVar.d(view);
        int a = kaVar.a(view);
        if (d2 >= j && a <= f2) {
            return 1.0f;
        }
        if (a <= j || d2 >= f2) {
            return 0.0f;
        }
        if (d2 <= j && a >= f2) {
            return (((float) f2) - ((float) j)) / ((float) kaVar.b(view));
        } else if (d2 < j) {
            return (((float) a) - ((float) j)) / ((float) kaVar.b(view));
        } else {
            return (((float) f2) - ((float) d2)) / ((float) kaVar.b(view));
        }
    }

    private static int k(int i, int i2, int i3) {
        return Math.max(i2, Math.min(i3, i));
    }

    private static int l(View view, ka kaVar) {
        return kaVar.d(view) - kaVar.j();
    }

    private final ka m(kl klVar) {
        ka kaVar = this.g;
        if (kaVar == null || kaVar.a != klVar) {
            this.g = new jy(klVar);
        }
        return this.g;
    }

    private final ka n(kl klVar) {
        ka kaVar = this.f;
        if (kaVar == null || kaVar.a != klVar) {
            this.f = new jz(klVar);
        }
        return this.f;
    }

    private static boolean o(View view, ka kaVar) {
        if (kaVar.b(view) <= kaVar.k()) {
            return true;
        }
        return false;
    }

    public final int[] b(int i, int i2) {
        kl klVar;
        int i3;
        int[] b = super.b(i, i2);
        RecyclerView recyclerView = this.e;
        if (!(recyclerView == null || (klVar = recyclerView.m) == null || klVar.ao() == 0)) {
            if (j(klVar)) {
                i3 = 0;
            } else {
                i3 = klVar.ao() - 1;
            }
            View view = (View) Objects.requireNonNull(klVar.aA(i3));
            float h = h(view, i(klVar));
            int i4 = klVar.E;
            if (h > 0.0f) {
                i4 -= kl.bg(view);
            }
            int i5 = -i4;
            b[0] = k(b[0], i5, i4);
            b[1] = k(b[1], i5, i4);
        }
        return b;
    }

    public final View d(kl klVar) {
        float f2;
        int ao = klVar.ao();
        if (ao == 0) {
            return null;
        }
        ka i = i(klVar);
        boolean z = true;
        if (ao == 1) {
            View aA = klVar.aA(0);
            if (o(aA, i)) {
                return aA;
            }
            return null;
        }
        RecyclerView recyclerView = this.e;
        if (recyclerView == null) {
            return null;
        }
        View childAt = recyclerView.getChildAt(0);
        if (childAt.getHeight() > this.e.getHeight() && i.d(childAt) < 0 && ((float) i.a(childAt)) > ((float) this.e.getHeight()) * 0.3f) {
            return null;
        }
        View view = (View) Objects.requireNonNull(klVar.aA(ao - 1));
        if (kl.bk(view) != klVar.aq() - 1) {
            z = false;
        }
        float f3 = 0.0f;
        if (z) {
            f2 = h(view, i);
        } else {
            f2 = 0.0f;
        }
        int i2 = Preference.DEFAULT_ORDER;
        View view2 = null;
        for (int i3 = 0; i3 < ao; i3++) {
            View aA2 = klVar.aA(i3);
            int d2 = i.d(aA2);
            if (Math.abs(d2) < i2) {
                float h = h(aA2, i);
                if (h > 0.5f && h > f3) {
                    view2 = aA2;
                    i2 = d2;
                    f3 = h;
                }
            }
        }
        if (view2 != null && (!z || f2 <= f3)) {
            view = view2;
        }
        if (o(view, i)) {
            return view;
        }
        return null;
    }

    public final int[] e(kl klVar, View view) {
        int[] iArr = new int[2];
        if (this.e.isInTouchMode()) {
            if (klVar.Y()) {
                iArr[0] = l(view, m(klVar));
            }
            if (klVar.Z()) {
                iArr[1] = l(view, n(klVar));
            }
        }
        return iArr;
    }

    public final ka i(kl klVar) {
        if (klVar.Z()) {
            return n(klVar);
        }
        return m(klVar);
    }

    public final boolean j(kl klVar) {
        if (klVar == null || klVar.ao() == 0) {
            return true;
        }
        int ao = klVar.ao();
        ka i = i(klVar);
        View view = (View) Objects.requireNonNull(klVar.aA(ao - 1));
        if (kl.bk(view) != klVar.aq() - 1 || kl.be(view) > i.f()) {
            return false;
        }
        return true;
    }
}
