package defpackage;

import android.net.Uri;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;

/* renamed from: cwn  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwn implements gqx {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ cwn(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r1v3, types: [java.lang.Object, java.lang.Iterable] */
    /* JADX WARNING: type inference failed for: r12v19, types: [java.util.List, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r1v10, types: [java.util.List, java.lang.Object] */
    public final Object apply(Object obj) {
        ctl ctl;
        ctl ctl2;
        switch (this.b) {
            case 0:
                cte cte = (cte) obj;
                htk htk = (htk) cte.C(5);
                htk.x(cte);
                if (!htk.b.B()) {
                    htk.u();
                }
                cte cte2 = (cte) htk.b;
                cte cte3 = cte.d;
                huf huf = cte2.c;
                if (!huf.c()) {
                    cte2.c = htq.s(huf);
                }
                hrz.g(this.a, cte2.c);
                return (cte) htk.r();
            case 1:
                return fvf.C((List) obj, new brg(this.a, 19));
            case 2:
                Void voidR = (Void) obj;
                return this.a;
            case 3:
                return (cth) Collections.unmodifiableMap(((cte) obj).b).get(this.a);
            case 4:
                cte cte4 = (cte) obj;
                htk htk2 = (htk) cte4.C(5);
                htk2.x(cte4);
                for (ctg ctg : this.a) {
                    cyh.e("%s: Removing group %s %s", "ProtoDataStoreFileGroupsMetadata", ctg.b, ctg.c);
                    htk2.C(cqx.f(ctg));
                }
                return (cte) htk2.r();
            case 5:
                Void voidR2 = (Void) obj;
                return this.a;
            case 6:
                return (csx) Collections.unmodifiableMap(((cte) obj).a).get(this.a);
            case 7:
                cte cte5 = (cte) obj;
                htk htk3 = (htk) cte5.C(5);
                htk3.x(cte5);
                htk3.C((String) this.a);
                return (cte) htk3.r();
            case 8:
                cte cte6 = (cte) obj;
                htk htk4 = (htk) cte6.C(5);
                htk4.x(cte6);
                ? r1 = this.a;
                for (Map.Entry entry : Collections.unmodifiableMap(cte6.a).entrySet()) {
                    String str = (String) entry.getKey();
                    try {
                        r1.add(new cxg(cqx.d(str), (csx) entry.getValue()));
                    } catch (czf e) {
                        htk4.C(str);
                        cyh.q(e, "Failed to deserialized file group key: ".concat(String.valueOf(str)));
                    }
                }
                return (cte) htk4.r();
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                cyh.f("Failed to commit migration metadata to disk");
                new Exception("Migration to DownloadTransform failed.", (IOException) obj);
                ((cws) this.a).b.a();
                return false;
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return (ctl) ((gxv) obj).get(this.a);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                ctn ctn = (ctn) obj;
                htk htk5 = (htk) ctn.C(5);
                htk5.x(ctn);
                htk5.E((String) this.a);
                return (ctn) htk5.r();
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                ctn ctn2 = (ctn) obj;
                cyh.c("%s: Starting migration to dedup on checksum onlu", "ProtoDataStoreSharedFilesMetadata");
                htk htk6 = (htk) ctn2.C(5);
                htk6.x(ctn2);
                Object obj2 = this.a;
                for (String str2 : Collections.unmodifiableMap(ctn2.a).keySet()) {
                    try {
                        ctj K = cqh.K(str2, ((cws) obj2).a, ((cws) obj2).b);
                        str2.getClass();
                        huv huv = ctn2.a;
                        if (huv.containsKey(str2)) {
                            ctl = (ctl) huv.get(str2);
                        } else {
                            ctl = null;
                        }
                        htk6.E(str2);
                        if (ctl == null) {
                            cyh.g("%s: Unable to read sharedFile from ProtoDataStore.", "ProtoDataStoreSharedFilesMetadata");
                        } else {
                            htk6.D(cqh.N(K), ctl);
                        }
                    } catch (czh unused) {
                        cyh.h("%s Failed to deserialize file key %s, remove and continue.", "ProtoDataStoreSharedFilesMetadata", str2);
                        ((cws) obj2).b.a();
                        htk6.E(str2);
                    }
                }
                return (ctn) htk6.r();
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                cyh.f("Failed to commit migration metadata to disk");
                new Exception("Migration to ChecksumOnly failed.", (IOException) obj);
                ((cws) this.a).b.a();
                return false;
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                Void voidR3 = (Void) obj;
                return (List) ((AtomicReference) this.a).get();
            case 15:
                ctn ctn3 = (ctn) obj;
                cyh.c("%s: Starting migration to add download transform", "ProtoDataStoreSharedFilesMetadata");
                htk htk7 = (htk) ctn3.C(5);
                htk7.x(ctn3);
                Object obj3 = this.a;
                for (String str3 : Collections.unmodifiableMap(ctn3.a).keySet()) {
                    try {
                        ctj K2 = cqh.K(str3, ((cws) obj3).a, ((cws) obj3).b);
                        str3.getClass();
                        huv huv2 = ctn3.a;
                        if (huv2.containsKey(str3)) {
                            ctl2 = (ctl) huv2.get(str3);
                        } else {
                            ctl2 = null;
                        }
                        htk7.E(str3);
                        if (ctl2 == null) {
                            cyh.g("%s: Unable to read sharedFile from ProtoDataStore.", "ProtoDataStoreSharedFilesMetadata");
                        } else {
                            htk7.D(cqh.O(K2), ctl2);
                        }
                    } catch (czh unused2) {
                        cyh.h("%s Failed to deserialize file key %s, remove and continue.", "ProtoDataStoreSharedFilesMetadata", str3);
                        ((cws) obj3).b.a();
                        htk7.E(str3);
                    }
                }
                return (ctn) htk7.r();
            case 16:
                return (Uri) ((gxv) obj).get(this.a);
            case 17:
                return (ctl) ((gxv) obj).get(this.a);
            case 18:
                csx csx = (csx) obj;
                cyc i = cyd.i(csx);
                String str4 = csx.c;
                if (!cqh.h()) {
                    return hma.a;
                }
                Object obj4 = this.a;
                cyd cyd = (cyd) obj4;
                return ftd.L(ftd.K(cyd.d(str4), new cwr(3), cyd.b), new cwq(obj4, i, 12), cyd.b);
            case 19:
                return fvf.P((List) obj, new cyb(this.a, 0));
            default:
                return fvf.P((List) obj, new cyb(this.a, 2));
        }
    }
}
