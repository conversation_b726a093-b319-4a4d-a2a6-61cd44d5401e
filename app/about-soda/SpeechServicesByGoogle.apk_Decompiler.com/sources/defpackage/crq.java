package defpackage;

import j$.util.DesugarCollections;
import java.util.ArrayList;
import java.util.Collection;

/* renamed from: crq  reason: default package */
/* compiled from: PG */
public final class crq implements ioz {
    private static final crp a = new crp();
    private final hpg b;

    public crq(hpg hpg) {
        this.b = hpg;
    }

    public final ioy a(isa isa, iov iov, iow iow) {
        ArrayList arrayList;
        crp crp = a;
        Collection collection = (Collection) iov.g(iwb.c);
        if (collection == null) {
            arrayList = new ArrayList();
        } else {
            arrayList = new ArrayList(collection);
        }
        hpg hpg = this.b;
        arrayList.add(crp);
        return hpg.a(isa, iov.f(iwb.c, DesugarCollections.unmodifiableList(arrayList)), iow);
    }
}
