package defpackage;

/* renamed from: cso  reason: default package */
/* compiled from: PG */
public final class cso {
    public String a;
    public grh b;
    public grh c;
    public boolean d;
    public boolean e;
    public byte f;

    public cso() {
        throw null;
    }

    public final void a(boolean z) {
        this.d = z;
        this.f = (byte) (this.f | 1);
    }

    public cso(byte[] bArr) {
        gqd gqd = gqd.a;
        this.b = gqd;
        this.c = gqd;
    }
}
