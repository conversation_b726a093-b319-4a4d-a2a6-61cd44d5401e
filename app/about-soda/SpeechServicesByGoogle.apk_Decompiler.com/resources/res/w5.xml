<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:tag="carUiListItem" android:layout_width="match_parent" android:layout_height="wrap_content" android:minHeight="@dimen/car_ui_list_item_height">
    <com.android.car.ui.SecureView android:id="@+id/car_ui_list_item_touch_interceptor" android:background="@drawable/car_ui_list_item_background" android:layout_width="0dp" android:layout_height="0dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="0" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
    <com.android.car.ui.SecureView android:id="@+id/car_ui_list_item_reduced_touch_interceptor" android:background="@drawable/car_ui_list_item_background" android:visibility="gone" android:layout_width="0dp" android:layout_height="0dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toStartOf="@+id/car_ui_list_item_action_container" app:layout_constraintStart_toStartOf="0" app:layout_constraintTop_toTopOf="0"/>
    <androidx.constraintlayout.widget.Guideline android:orientation="vertical" android:id="@+id/car_ui_list_item_start_guideline" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintGuide_begin="@dimen/car_ui_list_item_start_inset"/>
    <FrameLayout android:id="@+id/car_ui_list_item_icon_container" android:layout_width="@dimen/car_ui_list_item_icon_container_width" android:layout_height="0dp" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintStart_toStartOf="@+id/car_ui_list_item_start_guideline" app:layout_constraintTop_toTopOf="0">
        <ImageView android:layout_gravity="center" android:id="@+id/car_ui_list_item_icon" android:visibility="gone" android:layout_width="@dimen/car_ui_list_item_icon_size" android:layout_height="@dimen/car_ui_list_item_icon_size" android:scaleType="fitCenter"/>
        <ImageView android:layout_gravity="center" android:id="@+id/car_ui_list_item_content_icon" android:visibility="gone" android:layout_width="@dimen/car_ui_list_item_content_icon_width" android:layout_height="@dimen/car_ui_list_item_content_icon_height" android:scaleType="fitCenter"/>
        <ImageView android:layout_gravity="center" android:id="@+id/car_ui_list_item_avatar_icon" android:background="@drawable/car_ui_list_item_avatar_icon_outline" android:visibility="gone" android:layout_width="@dimen/car_ui_list_item_avatar_icon_width" android:layout_height="@dimen/car_ui_list_item_avatar_icon_height" android:scaleType="fitCenter"/>
    </FrameLayout>
    <CarUiTextView android:textAppearance="@style/TextAppearance.CarUi.ListItem" android:id="@+id/car_ui_list_item_title" android:layout_width="0dp" android:layout_height="wrap_content" android:singleLine="@bool/car_ui_list_item_single_line_title" android:textAlignment="viewStart" android:layout_marginStart="@dimen/car_ui_list_item_text_start_margin" app:layout_constraintBottom_toTopOf="@+id/car_ui_list_item_body" app:layout_constraintEnd_toStartOf="@+id/car_ui_list_item_action_container" app:layout_constraintStart_toEndOf="@+id/car_ui_list_item_icon_container" app:layout_constraintTop_toTopOf="0" app:layout_constraintVertical_chainStyle="2" app:layout_goneMarginStart="@dimen/car_ui_list_item_text_no_icon_start_margin"/>
    <CarUiTextView android:textAppearance="@style/TextAppearance.CarUi.ListItem.Body" android:id="@+id/car_ui_list_item_body" android:layout_width="0dp" android:layout_height="wrap_content" android:textAlignment="viewStart" android:layout_marginStart="@dimen/car_ui_list_item_text_start_margin" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toStartOf="@+id/car_ui_list_item_action_container" app:layout_constraintStart_toEndOf="@+id/car_ui_list_item_icon_container" app:layout_constraintTop_toBottomOf="@+id/car_ui_list_item_title" app:layout_goneMarginStart="@dimen/car_ui_list_item_text_no_icon_start_margin"/>
    <com.android.car.ui.SecureView android:id="@+id/car_ui_list_item_action_container_touch_interceptor" android:background="@drawable/car_ui_list_item_background" android:visibility="gone" android:layout_width="0dp" android:layout_height="0dp" app:layout_constraintBottom_toBottomOf="@+id/car_ui_list_item_action_container" app:layout_constraintEnd_toEndOf="@+id/car_ui_list_item_action_container" app:layout_constraintStart_toStartOf="@+id/car_ui_list_item_action_container" app:layout_constraintTop_toTopOf="@+id/car_ui_list_item_action_container"/>
    <FrameLayout android:id="@+id/car_ui_list_item_action_container" android:layout_width="wrap_content" android:layout_height="0dp" android:minWidth="@dimen/car_ui_list_item_icon_container_width" app:layout_constraintBottom_toBottomOf="0" app:layout_constraintEnd_toEndOf="@+id/car_ui_list_item_end_guideline" app:layout_constraintTop_toTopOf="0">
        <View android:layout_gravity="left|center_vertical|center_horizontal|center|start" android:id="@+id/car_ui_list_item_action_divider" android:background="@drawable/car_ui_list_item_divider" android:layout_width="@dimen/car_ui_list_item_action_divider_width" android:layout_height="@dimen/car_ui_list_item_action_divider_height"/>
        <Switch android:layout_gravity="center" android:id="@+id/car_ui_list_item_switch_widget" android:focusable="false" android:clickable="false" android:layout_width="wrap_content" android:layout_height="wrap_content"/>
        <CheckBox android:layout_gravity="center" android:id="@+id/car_ui_list_item_checkbox_widget" android:focusable="false" android:clickable="false" android:layout_width="wrap_content" android:layout_height="wrap_content"/>
        <RadioButton android:layout_gravity="center" android:id="@+id/car_ui_list_item_radio_button_widget" android:focusable="false" android:clickable="false" android:layout_width="wrap_content" android:layout_height="wrap_content"/>
        <ImageView android:layout_gravity="center" android:id="@+id/car_ui_list_item_supplemental_icon" android:layout_width="@dimen/car_ui_list_item_supplemental_icon_size" android:layout_height="@dimen/car_ui_list_item_supplemental_icon_size" android:scaleType="fitCenter"/>
    </FrameLayout>
    <androidx.constraintlayout.widget.Guideline android:orientation="vertical" android:id="@+id/car_ui_list_item_end_guideline" android:layout_width="wrap_content" android:layout_height="wrap_content" app:layout_constraintGuide_end="@dimen/car_ui_list_item_end_inset"/>
</androidx.constraintlayout.widget.ConstraintLayout>
