package j$.time.temporal;

import j$.nio.file.attribute.a;
import j$.time.chrono.C0010i;
import j$.time.chrono.u;
import j$.time.g;

/* 'enum' modifier removed */
final class d extends f {
    d() {
        super("WEEK_OF_WEEK_BASED_YEAR", 2);
    }

    public final long g(m mVar) {
        if (p(mVar)) {
            return (long) f.I(g.K(mVar));
        }
        throw new RuntimeException("Unsupported field: WeekOfWeekBasedYear");
    }

    public final t m() {
        return t.k(1, 52, 53);
    }

    public final boolean p(m mVar) {
        if (mVar.e(a.EPOCH_DAY)) {
            p pVar = h.a;
            if (C0010i.p(mVar).equals(u.d)) {
                return true;
            }
        }
        return false;
    }

    public final k s(k kVar, long j) {
        m().b(j, this);
        return kVar.f(a.g(j, g(kVar)), ChronoUnit.WEEKS);
    }

    public final t t(m mVar) {
        if (p(mVar)) {
            return f.L(g.K(mVar));
        }
        throw new RuntimeException("Unsupported field: WeekOfWeekBasedYear");
    }

    public final String toString() {
        return "WeekOfWeekBasedYear";
    }
}
