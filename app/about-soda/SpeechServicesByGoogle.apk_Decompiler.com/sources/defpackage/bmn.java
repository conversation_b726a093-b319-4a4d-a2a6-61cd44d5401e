package defpackage;

import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;
import com.google.android.tts.R;

/* renamed from: bmn  reason: default package */
/* compiled from: PG */
public final class bmn implements bmj {
    private static final int[] k = {R.attr.state_ux_restricted};
    public boolean a;
    public final bml b;
    public final ViewGroup c;
    public View d;
    public View e;
    public ImageView f;
    public Switch g;
    public TextView h;
    public TextView i;
    public boolean j;
    private final int l;

    public bmn(bml bml, ViewGroup viewGroup) {
        this.b = bml;
        this.c = viewGroup;
        bml.b(this);
        this.l = viewGroup.getContext().getResources().getDimensionPixelSize(R.dimen.car_ui_toolbar_menu_item_icon_size);
    }

    private final void b(View view) {
        int[] iArr;
        view.setEnabled(this.b.l);
        if (this.b.d()) {
            iArr = k;
        } else {
            iArr = null;
        }
        if (view instanceof ImageView) {
            ((ImageView) view).setImageState(iArr, true);
        } else if (view instanceof boj) {
            ((boj) view).a(iArr, (int[]) null);
        }
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i2 = 0; i2 < viewGroup.getChildCount(); i2++) {
                b(viewGroup.getChildAt(i2));
            }
        }
    }

    /* JADX WARNING: Code restructure failed: missing block: B:15:0x0036, code lost:
        if (r0 == false) goto L_0x003a;
     */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final void a() {
        /*
            r8 = this;
            android.view.View r0 = r8.d
            if (r0 != 0) goto L_0x0006
            goto L_0x00f8
        L_0x0006:
            bml r1 = r8.b
            int r1 = r1.f
            r0.setId(r1)
            bml r0 = r8.b
            android.graphics.drawable.Drawable r1 = r0.i
            r2 = 1
            r3 = 0
            if (r1 == 0) goto L_0x0017
            r1 = r2
            goto L_0x0018
        L_0x0017:
            r1 = r3
        L_0x0018:
            java.lang.CharSequence r0 = r0.h
            boolean r0 = android.text.TextUtils.isEmpty(r0)
            r4 = r0 ^ 1
            bml r5 = r8.b
            boolean r6 = r5.n
            r7 = 8
            if (r6 == 0) goto L_0x0104
            boolean r6 = r5.c
            if (r6 == 0) goto L_0x0030
            boolean r6 = r8.a
            if (r6 != 0) goto L_0x0104
        L_0x0030:
            boolean r6 = r5.a
            if (r6 != 0) goto L_0x0039
            if (r1 != 0) goto L_0x0039
            if (r0 != 0) goto L_0x0104
            goto L_0x003a
        L_0x0039:
            r2 = r4
        L_0x003a:
            android.view.View r0 = r8.d
            r0.setVisibility(r3)
            android.view.View r0 = r8.d
            bml r4 = r8.b
            java.lang.CharSequence r4 = r4.h
            r0.setContentDescription(r4)
            r0 = 0
            if (r6 == 0) goto L_0x0057
            android.widget.Switch r2 = r8.g
            bml r4 = r8.b
            boolean r4 = r4.m
            r2.setChecked(r4)
            android.widget.Switch r2 = r8.g
            goto L_0x0096
        L_0x0057:
            if (r2 == 0) goto L_0x007d
            if (r1 == 0) goto L_0x007d
            boolean r2 = r5.d
            if (r2 == 0) goto L_0x007d
            bml r2 = r8.b
            int r4 = r8.l
            android.graphics.drawable.Drawable r2 = r2.i
            r2.setBounds(r3, r3, r4, r4)
            android.widget.TextView r2 = r8.i
            bml r4 = r8.b
            android.graphics.drawable.Drawable r4 = r4.i
            r2.setCompoundDrawables(r4, r0, r0, r0)
            android.widget.TextView r2 = r8.i
            bml r4 = r8.b
            java.lang.CharSequence r4 = r4.h
            r2.setText(r4)
            android.widget.TextView r2 = r8.i
            goto L_0x0096
        L_0x007d:
            if (r1 == 0) goto L_0x008b
            android.widget.ImageView r2 = r8.f
            bml r4 = r8.b
            android.graphics.drawable.Drawable r4 = r4.i
            r2.setImageDrawable(r4)
            android.view.View r2 = r8.e
            goto L_0x0096
        L_0x008b:
            android.widget.TextView r2 = r8.h
            bml r4 = r8.b
            java.lang.CharSequence r4 = r4.h
            r2.setText(r4)
            android.widget.TextView r2 = r8.h
        L_0x0096:
            android.view.View r4 = r8.e
            if (r2 != r4) goto L_0x009c
            r5 = r3
            goto L_0x009d
        L_0x009c:
            r5 = r7
        L_0x009d:
            r4.setVisibility(r5)
            android.widget.TextView r4 = r8.h
            if (r2 != r4) goto L_0x00a6
            r5 = r3
            goto L_0x00a7
        L_0x00a6:
            r5 = r7
        L_0x00a7:
            r4.setVisibility(r5)
            android.widget.TextView r4 = r8.i
            if (r2 != r4) goto L_0x00b0
            r5 = r3
            goto L_0x00b1
        L_0x00b0:
            r5 = r7
        L_0x00b1:
            r4.setVisibility(r5)
            android.widget.Switch r4 = r8.g
            if (r2 != r4) goto L_0x00b9
            r7 = r3
        L_0x00b9:
            r4.setVisibility(r7)
            boolean r4 = r8.j
            if (r4 != 0) goto L_0x00c2
            android.view.View r2 = r8.d
        L_0x00c2:
            bml r4 = r8.b
            boolean r5 = r4.e
            if (r5 != 0) goto L_0x00cf
            if (r1 == 0) goto L_0x00cf
            android.graphics.drawable.Drawable r1 = r4.i
            r1.setTintList(r0)
        L_0x00cf:
            android.view.View r1 = r8.d
            r8.b(r1)
            android.view.View r1 = r8.d
            bml r4 = r8.b
            boolean r4 = r4.o
            r1.setActivated(r4)
            bml r1 = r8.b
            bmk r4 = r1.j
            if (r4 != 0) goto L_0x00f9
            boolean r4 = r1.a
            if (r4 != 0) goto L_0x00f9
            boolean r1 = r1.b
            if (r1 == 0) goto L_0x00ec
            goto L_0x00f9
        L_0x00ec:
            android.view.View r1 = r8.d
            if (r2 != r1) goto L_0x00f8
            r1.setOnClickListener(r0)
            android.view.View r0 = r8.d
            r0.setClickable(r3)
        L_0x00f8:
            return
        L_0x00f9:
            gp r1 = new gp
            r3 = 16
            r1.<init>(r8, r3, r0)
            r2.setOnClickListener(r1)
            return
        L_0x0104:
            android.view.View r0 = r8.d
            r0.setVisibility(r7)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bmn.a():void");
    }

    public final void onMenuItemChanged(bml bml) {
        a();
    }
}
