<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:orientation="vertical" android:background="@android:color/transparent" android:layout_width="match_parent" android:layout_height="match_parent">
    <ListView android:scrollbarAlwaysDrawVerticalTrack="true" android:scrollbarStyle="outsideOverlay" android:id="@+id/license_list" android:paddingLeft="16dp" android:paddingTop="0dp" android:paddingRight="16dp" android:paddingBottom="0dp" android:clipToPadding="false" android:layout_width="match_parent" android:layout_height="0dp" android:drawSelectorOnTop="false" android:cacheColorHint="@android:color/transparent" android:layout_weight="1"/>
</LinearLayout>
