package defpackage;

import android.util.Log;
import j$.util.Collection;
import j$.util.Objects;
import j$.util.stream.Stream;
import java.util.Map;

/* renamed from: cxr  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cxr implements hko {
    public final /* synthetic */ Object a;
    private final /* synthetic */ int b;

    public /* synthetic */ cxr(Object obj, int i) {
        this.b = i;
        this.a = obj;
    }

    /* JADX WARNING: type inference failed for: r0v17, types: [java.util.Map, java.lang.Object] */
    /* JADX WARNING: type inference failed for: r0v41, types: [java.lang.Object, jna] */
    /* JADX WARNING: type inference failed for: r0v42, types: [java.lang.Object, jna] */
    /* JADX WARNING: type inference failed for: r0v43, types: [java.lang.Object, jna] */
    /* JADX WARNING: type inference failed for: r0v44, types: [java.lang.Object, jna] */
    public final hme a(Object obj) {
        switch (this.b) {
            case 0:
                ctl ctl = (ctl) obj;
                if (ctl != null) {
                    return hfc.K(ctl);
                }
                cyh.h("%s: Shared file not found, newFileKey = %s", "DownloaderCallbackImpl", this.a);
                kml a2 = csi.a();
                a2.b = csh.SHARED_FILE_NOT_FOUND_ERROR;
                return hfc.J(a2.a());
            case 1:
                Void voidR = (Void) obj;
                return hfc.J((Throwable) this.a);
            case 2:
                Void voidR2 = (Void) obj;
                throw ((Throwable) this.a);
            case 3:
                return ((cyd) this.a).f((Map) obj);
            case 4:
                cti cti = (cti) obj;
                ctk ctk = cti.e;
                if (ctk == null) {
                    ctk = ctk.d;
                }
                if ((ctk.a & 1) != 0) {
                    ctk ctk2 = cti.e;
                    if (ctk2 == null) {
                        ctk2 = ctk.d;
                    }
                    return hfc.K(ctk2);
                }
                Object obj2 = this.a;
                cyn cyn = (cyn) obj2;
                return czw.e(cyn.c.b(new cyg(obj2, 2), cyn.a)).g(new cxr(obj2, 5), cyn.a).f(new cwr(7), cyn.a);
            case 5:
                Void voidR3 = (Void) obj;
                return ((cyn) this.a).c.a();
            case 6:
                grh grh = (grh) obj;
                if (grh.f()) {
                    cyh.k("%s: CancelForegroundDownload future found for key = %s, cancelling...", "DownloaderImp", this.a);
                    ((hme) grh.b()).cancel(false);
                }
                return hma.a;
            case 7:
                Boolean bool = (Boolean) obj;
                return hfc.K(this.a);
            case 8:
                ? r0 = this.a;
                r0.putAll((Map) obj);
                return hfc.K(r0);
            case klf.HTTP_CACHE_MAX_SIZE_FIELD_NUMBER /*9*/:
                ((hdc) ((hdc) ((hdc) dcs.a.g()).i((Throwable) obj)).j("com/google/android/libraries/micore/superpacks/scheduling/ScheduledDownloadTask", "download", 123, "ScheduledDownloadTask.java")).u("Download %s failed to stop", ((det) this.a).d().e());
                return hfc.K(new ddy((String) null));
            case klf.EXPERIMENTAL_OPTIONS_FIELD_NUMBER /*10*/:
                return ((gef) this.a).b((dvo) obj);
            case klf.MOCK_CERT_VERIFIER_FIELD_NUMBER /*11*/:
                dtv dtv = (dtv) obj;
                int i = dtv.a;
                if (!(i == 29501 || i == 29537 || i == 29538 || i == 29539 || i == 29540 || i == 29541 || i == 29542 || i == 29543)) {
                    if (i == 29544) {
                        i = 29544;
                    }
                    return hfc.J(dtv);
                }
                duu duu = (duu) this.a;
                boolean c = duu.f.c();
                String str = "Failed to commit due to stale snapshot for " + duu.b + ". Experiments may be delayed til next app start. Error code: " + i;
                if (!c) {
                    str = str.concat(". Triggering flag update.");
                }
                Log.w("MobStoreFlagStore", str);
                if (!c) {
                    duu.b();
                }
                return hfc.J(dtv);
            case klf.ENABLE_NETWORK_QUALITY_ESTIMATOR_FIELD_NUMBER /*12*/:
                return ((gef) this.a).b((dvo) obj);
            case klf.BYPASS_PUBLIC_KEY_PINNING_FOR_LOCAL_TRUST_ANCHORS_FIELD_NUMBER /*13*/:
                Void voidR4 = (Void) obj;
                return hfc.L((hme) ((dvr) this.a).f.a());
            case klf.NETWORK_THREAD_PRIORITY_FIELD_NUMBER /*14*/:
                return hfc.K(eki.b((eah) obj, (eam) this.a));
            case 15:
                return hfc.K(eki.j((eah) obj, (eam) this.a));
            case 16:
                return a.n(this.a, obj);
            case 17:
                return a.n(this.a, obj);
            case 18:
                return a.n(this.a, obj);
            case 19:
                return a.n(this.a, obj);
            default:
                gxq gxq = (gxq) obj;
                ((hdc) ((hdc) eyc.a.f()).j("com/google/android/libraries/speech/modeldownload/languagepacks/backgroundtask/LanguagePackMaintenance", "purgeStalePacks", 150, "LanguagePackMaintenance.java")).x("Background deleting %d stale packs: %s", gxq.size(), exo.a(gxq));
                Stream stream = Collection.EL.stream(gxq);
                exq exq = ((eyc) this.a).b;
                Objects.requireNonNull(exq);
                return hfc.H((Iterable) stream.map(new bof(exq, 10)).collect(gvx.a));
        }
    }
}
