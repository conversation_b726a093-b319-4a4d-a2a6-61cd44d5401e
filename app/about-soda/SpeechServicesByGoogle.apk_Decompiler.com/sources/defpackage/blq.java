package defpackage;

import android.content.Context;
import android.view.View;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.Interpolator;
import com.google.android.tts.R;

/* renamed from: blq  reason: default package */
/* compiled from: PG */
public final class blq extends kz {
    final float m;
    final float n;
    final float o;
    final Interpolator p;

    public blq(Context context) {
        super(context);
        float b = bnv.b(context.getResources(), R.dimen.car_ui_scrollbar_milliseconds_per_inch);
        this.m = b;
        this.n = bnv.b(context.getResources(), R.dimen.car_ui_scrollbar_deceleration_times_divisor);
        this.p = new DecelerateInterpolator(bnv.b(context.getResources(), R.dimen.car_ui_scrollbar_decelerate_interpolator_factor));
        this.o = b / ((float) context.getResources().getDisplayMetrics().densityDpi);
    }

    /* access modifiers changed from: protected */
    public final int f(int i) {
        return (int) Math.ceil((double) (((float) g(i)) / this.n));
    }

    /* access modifiers changed from: protected */
    public final int g(int i) {
        return (int) Math.ceil((double) (((float) Math.abs(i)) * this.o));
    }

    /* access modifiers changed from: protected */
    public final int h() {
        return -1;
    }

    /* access modifiers changed from: protected */
    public final void j(View view, kx kxVar) {
        int f;
        int e = e(view, -1);
        if (e != 0 && (f = f(e)) > 0) {
            kxVar.b(0, -e, f, this.p);
        }
    }
}
