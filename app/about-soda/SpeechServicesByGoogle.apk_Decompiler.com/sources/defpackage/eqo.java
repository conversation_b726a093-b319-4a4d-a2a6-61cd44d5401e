package defpackage;

/* renamed from: eqo  reason: default package */
/* compiled from: PG */
public final class eqo implements iiu {
    private final jjk a;
    private final jjk b;

    public eqo(jjk jjk, jjk jjk2) {
        this.a = jjk;
        this.b = jjk2;
    }

    /* renamed from: a */
    public final eqm b() {
        grh grh = (grh) ((iiv) this.a).a;
        eqm eqm = (eqm) this.b.b();
        jnu.e(grh, "micUseOwnerCheckerOverride");
        jnu.e(eqm, "defaultMicUseOwnerChecker");
        return (eqm) grh.d(eqm);
    }
}
