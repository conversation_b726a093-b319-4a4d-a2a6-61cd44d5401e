package defpackage;

/* renamed from: eoa  reason: default package */
/* compiled from: PG */
public final class eoa {
    public final int a;
    public final dzc b;
    public final boolean c;

    public eoa() {
        this((byte[]) null);
    }

    public final String a() {
        ehg ehg = this.b.b;
        if (ehg == null) {
            ehg = ehg.c;
        }
        int i = this.a;
        jnu.d(ehg, "getClientInfo(...)");
        String s = fbi.s(ehg);
        return "client(token(" + i + "), " + s + ")";
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof eoa)) {
            return false;
        }
        eoa eoa = (eoa) obj;
        if (this.a == eoa.a && jnu.i(this.b, eoa.b) && this.c == eoa.c) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        dzc dzc = this.b;
        if (dzc.B()) {
            i = dzc.i();
        } else {
            int i2 = dzc.memoizedHashCode;
            if (i2 == 0) {
                i2 = dzc.i();
                dzc.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (((this.a * 31) + i) * 31) + a.f(this.c);
    }

    public final String toString() {
        return "AudioSessionClientData(clientToken=" + this.a + ", params=" + this.b + ", isInactive=" + this.c + ")";
    }

    public eoa(int i, dzc dzc, boolean z) {
        jnu.e(dzc, "params");
        this.a = i;
        this.b = dzc;
        this.c = z;
    }

    /* JADX WARNING: Illegal instructions before constructor call */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public /* synthetic */ eoa(byte[] r3) {
        /*
            r2 = this;
            dzc r3 = defpackage.dzc.d
            htk r3 = r3.l()
            htm r3 = (defpackage.htm) r3
            java.lang.String r0 = "newBuilder(...)"
            defpackage.jnu.d(r3, r0)
            cxi r3 = defpackage.jnu.e(r3, "builder")
            dzc r3 = r3.g()
            r0 = 1
            r1 = -1
            r2.<init>(r1, r3, r0)
            return
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.eoa.<init>(byte[]):void");
    }
}
