package j$.time;

import j$.time.chrono.C0010i;
import j$.time.temporal.ChronoUnit;
import j$.time.temporal.TemporalUnit;
import j$.time.temporal.a;
import j$.time.temporal.k;
import j$.time.temporal.l;
import j$.time.temporal.n;
import j$.time.temporal.p;
import j$.time.temporal.r;
import j$.time.temporal.t;
import j$.util.Objects;
import java.io.InvalidObjectException;
import java.io.ObjectInput;
import java.io.ObjectInputStream;
import java.io.ObjectOutput;
import java.io.Serializable;

public final class q implements k, n, Comparable, Serializable {
    private static final long serialVersionUID = 7264499704384272492L;
    private final k a;
    private final x b;

    static {
        k kVar = k.e;
        x xVar = x.h;
        kVar.getClass();
        I(kVar, xVar);
        k kVar2 = k.f;
        x xVar2 = x.g;
        kVar2.getClass();
        I(kVar2, xVar2);
    }

    private q(k kVar, x xVar) {
        this.a = (k) Objects.requireNonNull(kVar, "time");
        this.b = (x) Objects.requireNonNull(xVar, "offset");
    }

    public static q I(k kVar, x xVar) {
        return new q(kVar, xVar);
    }

    static q K(ObjectInput objectInput) {
        return new q(k.X(objectInput), x.T(objectInput));
    }

    private q L(k kVar, x xVar) {
        if (this.a != kVar || !this.b.equals(xVar)) {
            return new q(kVar, xVar);
        }
        return this;
    }

    private void readObject(ObjectInputStream objectInputStream) {
        throw new InvalidObjectException("Deserialization via serialization delegate");
    }

    private Object writeReplace() {
        return new s((byte) 9, this);
    }

    public final long B(p pVar) {
        if (!(pVar instanceof a)) {
            return pVar.g(this);
        }
        if (pVar == a.OFFSET_SECONDS) {
            return (long) this.b.O();
        }
        return this.a.B(pVar);
    }

    public final Object F(r rVar) {
        boolean z;
        if (rVar == l.h() || rVar == l.j()) {
            return this.b;
        }
        boolean z2 = false;
        if (rVar == l.k()) {
            z = true;
        } else {
            z = false;
        }
        if (rVar == l.e()) {
            z2 = true;
        }
        if ((z || z2) || rVar == l.f()) {
            return null;
        }
        if (rVar == l.g()) {
            return this.a;
        }
        if (rVar == l.i()) {
            return ChronoUnit.NANOS;
        }
        return rVar.a(this);
    }

    /* renamed from: J */
    public final q f(long j, TemporalUnit temporalUnit) {
        if (temporalUnit instanceof ChronoUnit) {
            return L(this.a.f(j, temporalUnit), this.b);
        }
        return (q) temporalUnit.m(this, j);
    }

    public final int compareTo(Object obj) {
        q qVar = (q) obj;
        x xVar = qVar.b;
        x xVar2 = this.b;
        boolean equals = xVar2.equals(xVar);
        k kVar = qVar.a;
        k kVar2 = this.a;
        if (equals) {
            return kVar2.compareTo(kVar);
        }
        int compare = Long.compare(kVar2.Y() - (((long) xVar2.O()) * 1000000000), kVar.Y() - (((long) qVar.b.O()) * 1000000000));
        if (compare == 0) {
            return kVar2.compareTo(kVar);
        }
        return compare;
    }

    public final k d(long j, p pVar) {
        if (!(pVar instanceof a)) {
            return (q) pVar.s(this, j);
        }
        a aVar = a.OFFSET_SECONDS;
        k kVar = this.a;
        if (pVar == aVar) {
            return L(kVar, x.R(((a) pVar).F(j)));
        }
        return L(kVar.d(j, pVar), this.b);
    }

    public final boolean e(p pVar) {
        if (pVar instanceof a) {
            if (((a) pVar).J() || pVar == a.OFFSET_SECONDS) {
                return true;
            }
            return false;
        } else if (pVar == null || !pVar.p(this)) {
            return false;
        } else {
            return true;
        }
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof q)) {
            return false;
        }
        q qVar = (q) obj;
        if (!this.a.equals(qVar.a) || !this.b.equals(qVar.b)) {
            return false;
        }
        return true;
    }

    public final k g(k kVar) {
        return kVar.d(this.a.Y(), a.NANO_OF_DAY).d((long) this.b.O(), a.OFFSET_SECONDS);
    }

    public final int hashCode() {
        return this.a.hashCode() ^ this.b.hashCode();
    }

    public final k m(long j, ChronoUnit chronoUnit) {
        if (j == Long.MIN_VALUE) {
            return f(Long.MAX_VALUE, chronoUnit).f(1, chronoUnit);
        }
        return f(-j, chronoUnit);
    }

    public final int p(p pVar) {
        return l.a(this, pVar);
    }

    public final k s(g gVar) {
        return (q) C0010i.a(gVar, this);
    }

    public final t t(p pVar) {
        if (!(pVar instanceof a)) {
            return pVar.t(this);
        }
        if (pVar == a.OFFSET_SECONDS) {
            return ((a) pVar).m();
        }
        k kVar = this.a;
        kVar.getClass();
        return l.d(kVar, pVar);
    }

    public final String toString() {
        String kVar = this.a.toString();
        String xVar = this.b.toString();
        return kVar + xVar;
    }

    /* access modifiers changed from: package-private */
    public final void writeExternal(ObjectOutput objectOutput) {
        this.a.c0(objectOutput);
        this.b.U(objectOutput);
    }
}
