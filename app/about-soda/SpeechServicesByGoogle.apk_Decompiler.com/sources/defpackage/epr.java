package defpackage;

/* renamed from: epr  reason: default package */
/* compiled from: PG */
public final class epr extends htq implements hvb {
    public static final epr f;
    private static volatile hvh g;
    public int a;
    public int b = 0;
    public Object c;
    public int d;
    public int e = -1;

    static {
        epr epr = new epr();
        f = epr;
        htq.z(epr.class, epr);
    }

    private epr() {
    }

    /* access modifiers changed from: protected */
    public final Object a(int i, Object obj) {
        int i2 = i - 1;
        if (i2 == 0) {
            return (byte) 1;
        }
        if (i2 == 2) {
            return new hvl(f, "\u0004\u0004\u0001\u0001\u0001\u0004\u0004\u0000\u0000\u0000\u0001᠌\u0000\u0002<\u0000\u0003࠿\u0000\u0004င\u0001", new Object[]{"c", "b", "a", "d", ebb.k, eak.class, ebb.j, "e"});
        } else if (i2 == 3) {
            return new epr();
        } else {
            if (i2 == 4) {
                return new htk((htq) f);
            }
            if (i2 == 5) {
                return f;
            }
            if (i2 != 6) {
                return null;
            }
            hvh hvh = g;
            if (hvh == null) {
                synchronized (epr.class) {
                    hvh = g;
                    if (hvh == null) {
                        hvh = new htl(f);
                        g = hvh;
                    }
                }
            }
            return hvh;
        }
    }
}
