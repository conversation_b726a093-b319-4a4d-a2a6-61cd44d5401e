package defpackage;

/* renamed from: bqg  reason: default package */
/* compiled from: PG */
public final class bqg {
    public final bpm a;
    public final bpq b;
    public final abq c;

    public bqg() {
        throw null;
    }

    public final boolean equals(Object obj) {
        if (obj == this) {
            return true;
        }
        if (obj instanceof bqg) {
            bqg bqg = (bqg) obj;
            if (!this.a.equals(bqg.a) || !this.c.equals(bqg.c) || !this.b.equals(bqg.b)) {
                return false;
            }
            return true;
        }
        return false;
    }

    public final int hashCode() {
        return ((((this.a.hashCode() ^ 1000003) * 1000003) ^ this.c.hashCode()) * 1000003) ^ this.b.hashCode();
    }

    public final String toString() {
        bpq bpq = this.b;
        abq abq = this.c;
        String valueOf = String.valueOf(this.a);
        String valueOf2 = String.valueOf(abq);
        String valueOf3 = String.valueOf(bpq);
        return "RosieRobotServiceOptions{aiCoreClient=" + valueOf + ", downloadCallback=" + valueOf2 + ", feature=" + valueOf3 + "}";
    }

    public bqg(bpm bpm, abq abq, bpq bpq) {
        this.a = bpm;
        this.c = abq;
        this.b = bpq;
    }
}
