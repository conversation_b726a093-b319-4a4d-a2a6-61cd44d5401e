package defpackage;

/* renamed from: cwg  reason: default package */
/* compiled from: PG */
public final /* synthetic */ class cwg implements gqx {
    public final /* synthetic */ cwm a;
    public final /* synthetic */ boolean b;
    public final /* synthetic */ boolean c;
    public final /* synthetic */ gxv d;

    public /* synthetic */ cwg(cwm cwm, boolean z, boolean z2, gxv gxv) {
        this.a = cwm;
        this.b = z;
        this.c = z2;
        this.d = gxv;
    }

    public final Object apply(Object obj) {
        gxv gxv = (gxv) obj;
        if (!this.b) {
            return gxv;
        }
        gxv gxv2 = this.d;
        if (!this.c) {
            return gxv2;
        }
        return this.a.c.c(gxv2, gxv);
    }
}
