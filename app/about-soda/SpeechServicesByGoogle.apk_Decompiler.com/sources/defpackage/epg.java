package defpackage;

/* renamed from: epg  reason: default package */
/* compiled from: PG */
public final class epg {
    public final int a;
    public final dze b;

    public epg(int i, dze dze) {
        jnu.e(dze, "params");
        this.a = i;
        this.b = dze;
    }

    public final boolean equals(Object obj) {
        if (this == obj) {
            return true;
        }
        if (!(obj instanceof epg)) {
            return false;
        }
        epg epg = (epg) obj;
        if (this.a == epg.a && jnu.i(this.b, epg.b)) {
            return true;
        }
        return false;
    }

    public final int hashCode() {
        int i;
        dze dze = this.b;
        if (dze.B()) {
            i = dze.i();
        } else {
            int i2 = dze.memoizedHashCode;
            if (i2 == 0) {
                i2 = dze.i();
                dze.memoizedHashCode = i2;
            }
            i = i2;
        }
        return (this.a * 31) + i;
    }

    public final String toString() {
        return "AudioSourceSessionData(sessionToken=" + this.a + ", params=" + this.b + ")";
    }
}
