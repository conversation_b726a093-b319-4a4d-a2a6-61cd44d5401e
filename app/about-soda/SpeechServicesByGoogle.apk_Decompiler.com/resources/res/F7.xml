<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android" xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:aapt="http://schemas.android.com/aapt" android:gravity="center_vertical" android:background="@android:color/transparent" android:layout_width="match_parent" android:layout_height="wrap_content" android:minHeight="?android:attr/listPreferredItemHeightSmall">
    <com.android.car.ui.uxr.DrawableStateLinearLayout android:gravity="left|center_vertical|center_horizontal|center|start" android:id="@+id/car_ui_preference_container_without_widget" android:background="?android:attr/selectableItemBackground" android:paddingTop="@dimen/car_ui_preference_content_margin_top" android:paddingBottom="@dimen/car_ui_preference_content_margin_bottom" android:clipToPadding="false" android:layout_width="0dp" android:layout_height="match_parent" android:layout_weight="1" android:paddingStart="?android:attr/listPreferredItemPaddingStart" android:paddingEnd="?android:attr/listPreferredItemPaddingEnd">
        <androidx.preference.internal.PreferenceImageView android:id="@android:id/icon" android:layout_width="@dimen/car_ui_preference_icon_size" android:layout_height="@dimen/car_ui_preference_icon_size" android:layout_marginEnd="@dimen/car_ui_preference_icon_margin_end"/>
        <LinearLayout android:orientation="vertical" android:layout_width="match_parent" android:layout_height="wrap_content" android:layout_centerVertical="true">
            <com.android.car.ui.uxr.DrawableStateTextView android:textAppearance="@style/TextAppearance.CarUi.PreferenceTitle" android:ellipsize="end" android:id="@android:id/title" android:layout_width="wrap_content" android:layout_height="wrap_content" android:singleLine="true"/>
            <com.android.car.ui.uxr.DrawableStateTextView android:textAppearance="@style/TextAppearance.CarUi.PreferenceSummary" android:id="@android:id/summary" android:layout_width="wrap_content" android:layout_height="wrap_content"/>
        </LinearLayout>
    </com.android.car.ui.uxr.DrawableStateLinearLayout>
    <LinearLayout android:id="@+id/action_widget_container" android:layout_width="wrap_content" android:layout_height="match_parent">
        <View android:layout_width="@dimen/car_ui_divider_width" android:layout_height="match_parent" android:layout_marginTop="@dimen/car_ui_preference_content_margin_top" android:layout_marginBottom="@dimen/car_ui_preference_content_margin_bottom" style="@style/Preference.CarUi.Divider"/>
        <com.android.car.ui.uxr.DrawableStateFrameLayout android:id="@android:id/widget_frame" android:background="?android:attr/selectableItemBackground" android:layout_width="wrap_content" android:layout_height="match_parent" android:minWidth="?android:attr/listPreferredItemHeightSmall" android:paddingStart="?android:attr/listPreferredItemPaddingStart" android:paddingEnd="?android:attr/listPreferredItemPaddingEnd"/>
    </LinearLayout>
</LinearLayout>
