package defpackage;

/* renamed from: bja  reason: default package */
/* compiled from: PG */
public final class bja implements jup {
    final /* synthetic */ jup a;

    public bja(jup jup) {
        this.a = jup;
    }

    /* JADX WARNING: Removed duplicated region for block: B:12:0x002f  */
    /* JADX WARNING: Removed duplicated region for block: B:8:0x0021  */
    /* Code decompiled incorrectly, please refer to instructions dump. */
    public final java.lang.Object bK(java.lang.Object r5, defpackage.jlr r6) {
        /*
            r4 = this;
            boolean r0 = r6 instanceof defpackage.biz
            if (r0 == 0) goto L_0x0013
            r0 = r6
            biz r0 = (defpackage.biz) r0
            int r1 = r0.b
            r2 = -2147483648(0xffffffff80000000, float:-0.0)
            r3 = r1 & r2
            if (r3 == 0) goto L_0x0013
            int r1 = r1 - r2
            r0.b = r1
            goto L_0x0018
        L_0x0013:
            biz r0 = new biz
            r0.<init>(r4, r6)
        L_0x0018:
            java.lang.Object r6 = r0.a
            jlx r1 = defpackage.jlx.COROUTINE_SUSPENDED
            int r2 = r0.b
            r3 = 1
            if (r2 == 0) goto L_0x002f
            if (r2 != r3) goto L_0x0027
            defpackage.jji.c(r6)
            goto L_0x0041
        L_0x0027:
            java.lang.IllegalStateException r5 = new java.lang.IllegalStateException
            java.lang.String r6 = "call to 'resume' before 'invoke' with coroutine"
            r5.<init>(r6)
            throw r5
        L_0x002f:
            defpackage.jji.c(r6)
            jup r6 = r4.a
            boolean r2 = r5 instanceof defpackage.ber
            if (r2 == 0) goto L_0x0041
            r0.b = r3
            java.lang.Object r5 = r6.bK(r5, r0)
            if (r5 != r1) goto L_0x0041
            return r1
        L_0x0041:
            jkd r5 = defpackage.jkd.a
            return r5
        */
        throw new UnsupportedOperationException("Method not decompiled: defpackage.bja.bK(java.lang.Object, jlr):java.lang.Object");
    }
}
